import 'package:face_liveness_detector/face_liveness_detector.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class FaceLivenessDetectorWidget extends StatefulWidget {
  const FaceLivenessDetectorWidget({
    super.key,
    required this.sessionId,
    required this.region,
    required this.accessKeyId,
    this.secretAccessKey,
    this.sessionToken,
    this.onComplete,
    this.onError,
  });
  final String sessionId;
  final String region;
  final String? accessKeyId;
  final String? secretAccessKey;
  final String? sessionToken;
  final Future<bool?> Function()? onComplete;
  final ValueChanged<String>? onError;

  @override
  State<FaceLivenessDetectorWidget> createState() => _FaceLivenessDetectorWidgetState();
}

class _FaceLivenessDetectorWidgetState extends State<FaceLivenessDetectorWidget>
    with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  bool _isClosing = false;
  late AnimationController _animationController;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _startLoading();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _startLoading() async {
    await Future.delayed(const Duration(milliseconds: 1000));
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
      _animationController.forward();
    }
  }

  Future<void> _handleComplete() async {
    // Show loading overlay to cover black screen during native view closing
    setState(() {
      _isClosing = true;
    });

    // Give native view time to start closing
    await Future.delayed(const Duration(milliseconds: 100));

    try {
      final result = await widget.onComplete?.call();
      if (mounted) {
        final navigator = Navigator.of(context);
        // Add small delay to ensure smooth transition
        await Future.delayed(const Duration(milliseconds: 200));
        if (mounted) {
          navigator.pop(result);
        }
      }
    } catch (e) {
      // API call failed, hide loading overlay and let parent handle the error
      if (mounted) {
        setState(() {
          _isClosing = false;
        });
        // Don't auto-close, let parent decide when to close after handling error
      }
    }
  }

  Future<void> _handleError(String error) async {
    // Show loading overlay to cover black screen during native view closing
    setState(() {
      _isClosing = true;
    });

    // Give native view time to start closing
    await Future.delayed(const Duration(milliseconds: 100));

    widget.onError?.call(error);

    if (mounted) {
      final navigator = Navigator.of(context);
      // Add small delay to ensure smooth transition
      await Future.delayed(const Duration(milliseconds: 200));
      if (mounted) {
        navigator.pop(false);
      }
    }
  }

  // Public method to close the widget (can be called from parent)
  void closeWidget([bool? result]) {
    if (mounted) {
      Navigator.of(context).pop(result ?? false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Loading view
          if (_isLoading)
            Container(
              width: double.infinity,
              height: double.infinity,
              color: const Color(0xFF2E2D36),
              child: ring,
            ),

          // FaceLivenessDetector view with fade animation
          AnimatedBuilder(
            animation: _opacityAnimation,
            builder: (context, child) {
              return Opacity(
                opacity: _isLoading ? 0.0 : _opacityAnimation.value,
                child: Container(
                  color: const Color(0xFF2E2D36),
                  width: double.infinity,
                  height: double.infinity,
                  child: FaceLivenessDetector(
                    sessionId: widget.sessionId,
                    region: widget.region,
                    accessKeyId: widget.accessKeyId,
                    secretAccessKey: widget.secretAccessKey,
                    sessionToken: widget.sessionToken,
                    onComplete: () {
                      _handleComplete();
                    },
                    onError: (String error) {
                      _handleError(error);
                    },
                  ),
                ),
              );
            },
          ),

          // Closing overlay to cover black screen during native view closing
          if (_isClosing)
            Container(
              width: double.infinity,
              height: double.infinity,
              color: const Color(0xFF2E2D36),
              child: ring,
            ),
        ],
      ),
    );
  }

  Widget get ring => const SizedBox(
        width: 30.0,
        height: 30.0,
        child: Center(
          child: CupertinoActivityIndicator(
            color: Colors.white,
          ),
        ),
      );
}
