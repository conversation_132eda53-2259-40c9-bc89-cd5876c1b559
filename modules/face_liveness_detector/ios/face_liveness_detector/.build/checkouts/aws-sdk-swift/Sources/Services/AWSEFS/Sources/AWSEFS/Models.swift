//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.<PERSON>y<PERSON>eader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
@_spi(SmithyReadWrite) import struct SmithyReadWrite.ReadingClosureBox
@_spi(SmithyTimestamps) import struct SmithyTimestamps.TimestampFormatter


public struct CreateTagsOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteAccessPointOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteFileSystemOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteFileSystemPolicyOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteMountTargetOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteReplicationConfigurationOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteTagsOutput: Swift.Sendable {

    public init() { }
}

public struct ModifyMountTargetSecurityGroupsOutput: Swift.Sendable {

    public init() { }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

/// Returned if the access point that you are trying to create already exists, with the creation token you provided in the request.
public struct AccessPointAlreadyExists: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var accessPointId: Swift.String? = nil
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessPointAlreadyExists" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        accessPointId: Swift.String? = nil,
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.accessPointId = accessPointId
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

extension EFSClientTypes {

    public enum LifeCycleState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case available
        case creating
        case deleted
        case deleting
        case error
        case updating
        case sdkUnknown(Swift.String)

        public static var allCases: [LifeCycleState] {
            return [
                .available,
                .creating,
                .deleted,
                .deleting,
                .error,
                .updating
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .available: return "available"
            case .creating: return "creating"
            case .deleted: return "deleted"
            case .deleting: return "deleting"
            case .error: return "error"
            case .updating: return "updating"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EFSClientTypes {

    /// The full POSIX identity, including the user ID, group ID, and any secondary group IDs, on the access point that is used for all file system operations performed by NFS clients using the access point.
    public struct PosixUser: Swift.Sendable {
        /// The POSIX group ID used for all file system operations using this access point.
        /// This member is required.
        public var gid: Swift.Int?
        /// Secondary POSIX group IDs used for all file system operations using this access point.
        public var secondaryGids: [Swift.Int]?
        /// The POSIX user ID used for all file system operations using this access point.
        /// This member is required.
        public var uid: Swift.Int?

        public init(
            gid: Swift.Int? = nil,
            secondaryGids: [Swift.Int]? = nil,
            uid: Swift.Int? = nil
        )
        {
            self.gid = gid
            self.secondaryGids = secondaryGids
            self.uid = uid
        }
    }
}

extension EFSClientTypes {

    /// Required if the RootDirectory > Path specified does not exist. Specifies the POSIX IDs and permissions to apply to the access point's RootDirectory > Path. If the access point root directory does not exist, EFS creates it with these settings when a client connects to the access point. When specifying CreationInfo, you must include values for all properties. Amazon EFS creates a root directory only if you have provided the CreationInfo: OwnUid, OwnGID, and permissions for the directory. If you do not provide this information, Amazon EFS does not create the root directory. If the root directory does not exist, attempts to mount using the access point will fail. If you do not provide CreationInfo and the specified RootDirectory does not exist, attempts to mount the file system using the access point will fail.
    public struct CreationInfo: Swift.Sendable {
        /// Specifies the POSIX group ID to apply to the RootDirectory. Accepts values from 0 to 2^32 (4294967295).
        /// This member is required.
        public var ownerGid: Swift.Int?
        /// Specifies the POSIX user ID to apply to the RootDirectory. Accepts values from 0 to 2^32 (4294967295).
        /// This member is required.
        public var ownerUid: Swift.Int?
        /// Specifies the POSIX permissions to apply to the RootDirectory, in the format of an octal number representing the file's mode bits.
        /// This member is required.
        public var permissions: Swift.String?

        public init(
            ownerGid: Swift.Int? = nil,
            ownerUid: Swift.Int? = nil,
            permissions: Swift.String? = nil
        )
        {
            self.ownerGid = ownerGid
            self.ownerUid = ownerUid
            self.permissions = permissions
        }
    }
}

extension EFSClientTypes {

    /// Specifies the directory on the Amazon EFS file system that the access point provides access to. The access point exposes the specified file system path as the root directory of your file system to applications using the access point. NFS clients using the access point can only access data in the access point's RootDirectory and its subdirectories.
    public struct RootDirectory: Swift.Sendable {
        /// (Optional) Specifies the POSIX IDs and permissions to apply to the access point's RootDirectory. If the RootDirectory > Path specified does not exist, EFS creates the root directory using the CreationInfo settings when a client connects to an access point. When specifying the CreationInfo, you must provide values for all properties. If you do not provide CreationInfo and the specified RootDirectory > Path does not exist, attempts to mount the file system using the access point will fail.
        public var creationInfo: EFSClientTypes.CreationInfo?
        /// Specifies the path on the EFS file system to expose as the root directory to NFS clients using the access point to access the EFS file system. A path can have up to four subdirectories. If the specified path does not exist, you are required to provide the CreationInfo.
        public var path: Swift.String?

        public init(
            creationInfo: EFSClientTypes.CreationInfo? = nil,
            path: Swift.String? = nil
        )
        {
            self.creationInfo = creationInfo
            self.path = path
        }
    }
}

extension EFSClientTypes {

    /// A tag is a key-value pair. Allowed characters are letters, white space, and numbers that can be represented in UTF-8, and the following characters: + - = . _ : /.
    public struct Tag: Swift.Sendable {
        /// The tag key (String). The key can't start with aws:.
        /// This member is required.
        public var key: Swift.String?
        /// The value of the tag key.
        /// This member is required.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

extension EFSClientTypes {

    /// Provides a description of an EFS file system access point.
    public struct AccessPointDescription: Swift.Sendable {
        /// The unique Amazon Resource Name (ARN) associated with the access point.
        public var accessPointArn: Swift.String?
        /// The ID of the access point, assigned by Amazon EFS.
        public var accessPointId: Swift.String?
        /// The opaque string specified in the request to ensure idempotent creation.
        public var clientToken: Swift.String?
        /// The ID of the EFS file system that the access point applies to.
        public var fileSystemId: Swift.String?
        /// Identifies the lifecycle phase of the access point.
        public var lifeCycleState: EFSClientTypes.LifeCycleState?
        /// The name of the access point. This is the value of the Name tag.
        public var name: Swift.String?
        /// Identifies the Amazon Web Services account that owns the access point resource.
        public var ownerId: Swift.String?
        /// The full POSIX identity, including the user ID, group ID, and secondary group IDs on the access point that is used for all file operations by NFS clients using the access point.
        public var posixUser: EFSClientTypes.PosixUser?
        /// The directory on the EFS file system that the access point exposes as the root directory to NFS clients using the access point.
        public var rootDirectory: EFSClientTypes.RootDirectory?
        /// The tags associated with the access point, presented as an array of Tag objects.
        public var tags: [EFSClientTypes.Tag]?

        public init(
            accessPointArn: Swift.String? = nil,
            accessPointId: Swift.String? = nil,
            clientToken: Swift.String? = nil,
            fileSystemId: Swift.String? = nil,
            lifeCycleState: EFSClientTypes.LifeCycleState? = nil,
            name: Swift.String? = nil,
            ownerId: Swift.String? = nil,
            posixUser: EFSClientTypes.PosixUser? = nil,
            rootDirectory: EFSClientTypes.RootDirectory? = nil,
            tags: [EFSClientTypes.Tag]? = nil
        )
        {
            self.accessPointArn = accessPointArn
            self.accessPointId = accessPointId
            self.clientToken = clientToken
            self.fileSystemId = fileSystemId
            self.lifeCycleState = lifeCycleState
            self.name = name
            self.ownerId = ownerId
            self.posixUser = posixUser
            self.rootDirectory = rootDirectory
            self.tags = tags
        }
    }
}

/// Returned if the Amazon Web Services account has already created the maximum number of access points allowed per file system. For more informaton, see [https://docs.aws.amazon.com/efs/latest/ug/limits.html#limits-efs-resources-per-account-per-region](https://docs.aws.amazon.com/efs/latest/ug/limits.html#limits-efs-resources-per-account-per-region).
public struct AccessPointLimitExceeded: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessPointLimitExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if the specified AccessPointId value doesn't exist in the requester's Amazon Web Services account.
public struct AccessPointNotFound: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessPointNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if the Availability Zone that was specified for a mount target is different from the Availability Zone that was specified for One Zone storage. For more information, see [Regional and One Zone storage redundancy](https://docs.aws.amazon.com/efs/latest/ug/availability-durability.html).
public struct AvailabilityZonesMismatch: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AvailabilityZonesMismatch" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

extension EFSClientTypes {

    public enum Status: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case disabling
        case enabled
        case enabling
        case sdkUnknown(Swift.String)

        public static var allCases: [Status] {
            return [
                .disabled,
                .disabling,
                .enabled,
                .enabling
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "DISABLED"
            case .disabling: return "DISABLING"
            case .enabled: return "ENABLED"
            case .enabling: return "ENABLING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EFSClientTypes {

    /// The backup policy for the file system used to create automatic daily backups. If status has a value of ENABLED, the file system is being automatically backed up. For more information, see [Automatic backups](https://docs.aws.amazon.com/efs/latest/ug/awsbackup.html#automatic-backups).
    public struct BackupPolicy: Swift.Sendable {
        /// Describes the status of the file system's backup policy.
        ///
        /// * ENABLED – EFS is automatically backing up the file system.
        ///
        /// * ENABLING – EFS is turning on automatic backups for the file system.
        ///
        /// * DISABLED – Automatic back ups are turned off for the file system.
        ///
        /// * DISABLING – EFS is turning off automatic backups for the file system.
        /// This member is required.
        public var status: EFSClientTypes.Status?

        public init(
            status: EFSClientTypes.Status? = nil
        )
        {
            self.status = status
        }
    }
}

/// Returned if the request is malformed or contains an error such as an invalid parameter value or a missing required parameter.
public struct BadRequest: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "BadRequest" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if the source file system in a replication is encrypted but the destination file system is unencrypted.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if the specified FileSystemId value doesn't exist in the requester's Amazon Web Services account.
public struct FileSystemNotFound: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "FileSystemNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if the file system's lifecycle state is not "available".
public struct IncorrectFileSystemLifeCycleState: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "IncorrectFileSystemLifeCycleState" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if an error occurred on the server side.
public struct InternalServerError: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerError" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned when the CreateAccessPoint API action is called too quickly and the number of Access Points on the file system is nearing the [limit of 120](https://docs.aws.amazon.com/efs/latest/ug/limits.html#limits-efs-resources-per-account-per-region).
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

public struct CreateAccessPointInput: Swift.Sendable {
    /// A string of up to 64 ASCII characters that Amazon EFS uses to ensure idempotent creation.
    /// This member is required.
    public var clientToken: Swift.String?
    /// The ID of the EFS file system that the access point provides access to.
    /// This member is required.
    public var fileSystemId: Swift.String?
    /// The operating system user and group applied to all file system requests made using the access point.
    public var posixUser: EFSClientTypes.PosixUser?
    /// Specifies the directory on the EFS file system that the access point exposes as the root directory of your file system to NFS clients using the access point. The clients using the access point can only access the root directory and below. If the RootDirectory > Path specified does not exist, Amazon EFS creates it and applies the CreationInfo settings when a client connects to an access point. When specifying a RootDirectory, you must provide the Path, and the CreationInfo. Amazon EFS creates a root directory only if you have provided the CreationInfo: OwnUid, OwnGID, and permissions for the directory. If you do not provide this information, Amazon EFS does not create the root directory. If the root directory does not exist, attempts to mount using the access point will fail.
    public var rootDirectory: EFSClientTypes.RootDirectory?
    /// Creates tags associated with the access point. Each tag is a key-value pair, each key must be unique. For more information, see [Tagging Amazon Web Services resources](https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html) in the Amazon Web Services General Reference Guide.
    public var tags: [EFSClientTypes.Tag]?

    public init(
        clientToken: Swift.String? = nil,
        fileSystemId: Swift.String? = nil,
        posixUser: EFSClientTypes.PosixUser? = nil,
        rootDirectory: EFSClientTypes.RootDirectory? = nil,
        tags: [EFSClientTypes.Tag]? = nil
    )
    {
        self.clientToken = clientToken
        self.fileSystemId = fileSystemId
        self.posixUser = posixUser
        self.rootDirectory = rootDirectory
        self.tags = tags
    }
}

/// Provides a description of an EFS file system access point.
public struct CreateAccessPointOutput: Swift.Sendable {
    /// The unique Amazon Resource Name (ARN) associated with the access point.
    public var accessPointArn: Swift.String?
    /// The ID of the access point, assigned by Amazon EFS.
    public var accessPointId: Swift.String?
    /// The opaque string specified in the request to ensure idempotent creation.
    public var clientToken: Swift.String?
    /// The ID of the EFS file system that the access point applies to.
    public var fileSystemId: Swift.String?
    /// Identifies the lifecycle phase of the access point.
    public var lifeCycleState: EFSClientTypes.LifeCycleState?
    /// The name of the access point. This is the value of the Name tag.
    public var name: Swift.String?
    /// Identifies the Amazon Web Services account that owns the access point resource.
    public var ownerId: Swift.String?
    /// The full POSIX identity, including the user ID, group ID, and secondary group IDs on the access point that is used for all file operations by NFS clients using the access point.
    public var posixUser: EFSClientTypes.PosixUser?
    /// The directory on the EFS file system that the access point exposes as the root directory to NFS clients using the access point.
    public var rootDirectory: EFSClientTypes.RootDirectory?
    /// The tags associated with the access point, presented as an array of Tag objects.
    public var tags: [EFSClientTypes.Tag]?

    public init(
        accessPointArn: Swift.String? = nil,
        accessPointId: Swift.String? = nil,
        clientToken: Swift.String? = nil,
        fileSystemId: Swift.String? = nil,
        lifeCycleState: EFSClientTypes.LifeCycleState? = nil,
        name: Swift.String? = nil,
        ownerId: Swift.String? = nil,
        posixUser: EFSClientTypes.PosixUser? = nil,
        rootDirectory: EFSClientTypes.RootDirectory? = nil,
        tags: [EFSClientTypes.Tag]? = nil
    )
    {
        self.accessPointArn = accessPointArn
        self.accessPointId = accessPointId
        self.clientToken = clientToken
        self.fileSystemId = fileSystemId
        self.lifeCycleState = lifeCycleState
        self.name = name
        self.ownerId = ownerId
        self.posixUser = posixUser
        self.rootDirectory = rootDirectory
        self.tags = tags
    }
}

/// Returned if the file system you are trying to create already exists, with the creation token you provided.
public struct FileSystemAlreadyExists: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// This member is required.
        public internal(set) var fileSystemId: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "FileSystemAlreadyExists" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        fileSystemId: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.fileSystemId = fileSystemId
        self.properties.message = message
    }
}

/// Returned if the Amazon Web Services account has already created the maximum number of file systems allowed per account.
public struct FileSystemLimitExceeded: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "FileSystemLimitExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if there's not enough capacity to provision additional throughput. This value might be returned when you try to create a file system in provisioned throughput mode, when you attempt to increase the provisioned throughput of an existing file system, or when you attempt to change an existing file system from Bursting Throughput to Provisioned Throughput mode. Try again later.
public struct InsufficientThroughputCapacity: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InsufficientThroughputCapacity" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if the throughput mode or amount of provisioned throughput can't be changed because the throughput limit of 1024 MiB/s has been reached.
public struct ThroughputLimitExceeded: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThroughputLimitExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if the requested Amazon EFS functionality is not available in the specified Availability Zone.
public struct UnsupportedAvailabilityZone: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UnsupportedAvailabilityZone" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

extension EFSClientTypes {

    public enum PerformanceMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case generalPurpose
        case maxIo
        case sdkUnknown(Swift.String)

        public static var allCases: [PerformanceMode] {
            return [
                .generalPurpose,
                .maxIo
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .generalPurpose: return "generalPurpose"
            case .maxIo: return "maxIO"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EFSClientTypes {

    public enum ThroughputMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case bursting
        case elastic
        case provisioned
        case sdkUnknown(Swift.String)

        public static var allCases: [ThroughputMode] {
            return [
                .bursting,
                .elastic,
                .provisioned
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .bursting: return "bursting"
            case .elastic: return "elastic"
            case .provisioned: return "provisioned"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct CreateFileSystemInput: Swift.Sendable {
    /// For One Zone file systems, specify the Amazon Web Services Availability Zone in which to create the file system. Use the format us-east-1a to specify the Availability Zone. For more information about One Zone file systems, see [EFS file system types](https://docs.aws.amazon.com/efs/latest/ug/availability-durability.html#file-system-type) in the Amazon EFS User Guide. One Zone file systems are not available in all Availability Zones in Amazon Web Services Regions where Amazon EFS is available.
    public var availabilityZoneName: Swift.String?
    /// Specifies whether automatic backups are enabled on the file system that you are creating. Set the value to true to enable automatic backups. If you are creating a One Zone file system, automatic backups are enabled by default. For more information, see [Automatic backups](https://docs.aws.amazon.com/efs/latest/ug/awsbackup.html#automatic-backups) in the Amazon EFS User Guide. Default is false. However, if you specify an AvailabilityZoneName, the default is true. Backup is not available in all Amazon Web Services Regions where Amazon EFS is available.
    public var backup: Swift.Bool?
    /// A string of up to 64 ASCII characters. Amazon EFS uses this to ensure idempotent creation.
    /// This member is required.
    public var creationToken: Swift.String?
    /// A Boolean value that, if true, creates an encrypted file system. When creating an encrypted file system, you have the option of specifying an existing Key Management Service key (KMS key). If you don't specify a KMS key, then the default KMS key for Amazon EFS, /aws/elasticfilesystem, is used to protect the encrypted file system.
    public var encrypted: Swift.Bool?
    /// The ID of the KMS key that you want to use to protect the encrypted file system. This parameter is required only if you want to use a non-default KMS key. If this parameter is not specified, the default KMS key for Amazon EFS is used. You can specify a KMS key ID using the following formats:
    ///
    /// * Key ID - A unique identifier of the key, for example 1234abcd-12ab-34cd-56ef-1234567890ab.
    ///
    /// * ARN - An Amazon Resource Name (ARN) for the key, for example arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab.
    ///
    /// * Key alias - A previously created display name for a key, for example alias/projectKey1.
    ///
    /// * Key alias ARN - An ARN for a key alias, for example arn:aws:kms:us-west-2:************:alias/projectKey1.
    ///
    ///
    /// If you use KmsKeyId, you must set the [CreateFileSystemRequest$Encrypted] parameter to true. EFS accepts only symmetric KMS keys. You cannot use asymmetric KMS keys with Amazon EFS file systems.
    public var kmsKeyId: Swift.String?
    /// The performance mode of the file system. We recommend generalPurpose performance mode for all file systems. File systems using the maxIO performance mode can scale to higher levels of aggregate throughput and operations per second with a tradeoff of slightly higher latencies for most file operations. The performance mode can't be changed after the file system has been created. The maxIO mode is not supported on One Zone file systems. Due to the higher per-operation latencies with Max I/O, we recommend using General Purpose performance mode for all file systems. Default is generalPurpose.
    public var performanceMode: EFSClientTypes.PerformanceMode?
    /// The throughput, measured in mebibytes per second (MiBps), that you want to provision for a file system that you're creating. Required if ThroughputMode is set to provisioned. Valid values are 1-3414 MiBps, with the upper limit depending on Region. To increase this limit, contact Amazon Web Services Support. For more information, see [Amazon EFS quotas that you can increase](https://docs.aws.amazon.com/efs/latest/ug/limits.html#soft-limits) in the Amazon EFS User Guide.
    public var provisionedThroughputInMibps: Swift.Double?
    /// Use to create one or more tags associated with the file system. Each tag is a user-defined key-value pair. Name your file system on creation by including a "Key":"Name","Value":"{value}" key-value pair. Each key must be unique. For more information, see [Tagging Amazon Web Services resources](https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html) in the Amazon Web Services General Reference Guide.
    public var tags: [EFSClientTypes.Tag]?
    /// Specifies the throughput mode for the file system. The mode can be bursting, provisioned, or elastic. If you set ThroughputMode to provisioned, you must also set a value for ProvisionedThroughputInMibps. After you create the file system, you can decrease your file system's Provisioned throughput or change between the throughput modes, with certain time restrictions. For more information, see [Specifying throughput with provisioned mode](https://docs.aws.amazon.com/efs/latest/ug/performance.html#provisioned-throughput) in the Amazon EFS User Guide. Default is bursting.
    public var throughputMode: EFSClientTypes.ThroughputMode?

    public init(
        availabilityZoneName: Swift.String? = nil,
        backup: Swift.Bool? = nil,
        creationToken: Swift.String? = nil,
        encrypted: Swift.Bool? = nil,
        kmsKeyId: Swift.String? = nil,
        performanceMode: EFSClientTypes.PerformanceMode? = nil,
        provisionedThroughputInMibps: Swift.Double? = nil,
        tags: [EFSClientTypes.Tag]? = nil,
        throughputMode: EFSClientTypes.ThroughputMode? = nil
    )
    {
        self.availabilityZoneName = availabilityZoneName
        self.backup = backup
        self.creationToken = creationToken
        self.encrypted = encrypted
        self.kmsKeyId = kmsKeyId
        self.performanceMode = performanceMode
        self.provisionedThroughputInMibps = provisionedThroughputInMibps
        self.tags = tags
        self.throughputMode = throughputMode
    }
}

extension EFSClientTypes {

    public enum ReplicationOverwriteProtection: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case enabled
        case replicating
        case sdkUnknown(Swift.String)

        public static var allCases: [ReplicationOverwriteProtection] {
            return [
                .disabled,
                .enabled,
                .replicating
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "DISABLED"
            case .enabled: return "ENABLED"
            case .replicating: return "REPLICATING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EFSClientTypes {

    /// Describes the protection on a file system.
    public struct FileSystemProtectionDescription: Swift.Sendable {
        /// The status of the file system's replication overwrite protection.
        ///
        /// * ENABLED – The file system cannot be used as the destination file system in a replication configuration. The file system is writeable. Replication overwrite protection is ENABLED by default.
        ///
        /// * DISABLED – The file system can be used as the destination file system in a replication configuration. The file system is read-only and can only be modified by EFS replication.
        ///
        /// * REPLICATING – The file system is being used as the destination file system in a replication configuration. The file system is read-only and is only modified only by EFS replication.
        ///
        ///
        /// If the replication configuration is deleted, the file system's replication overwrite protection is re-enabled, the file system becomes writeable.
        public var replicationOverwriteProtection: EFSClientTypes.ReplicationOverwriteProtection?

        public init(
            replicationOverwriteProtection: EFSClientTypes.ReplicationOverwriteProtection? = nil
        )
        {
            self.replicationOverwriteProtection = replicationOverwriteProtection
        }
    }
}

extension EFSClientTypes {

    /// The latest known metered size (in bytes) of data stored in the file system, in its Value field, and the time at which that size was determined in its Timestamp field. The value doesn't represent the size of a consistent snapshot of the file system, but it is eventually consistent when there are no writes to the file system. That is, the value represents the actual size only if the file system is not modified for a period longer than a couple of hours. Otherwise, the value is not necessarily the exact size the file system was at any instant in time.
    public struct FileSystemSize: Swift.Sendable {
        /// The time at which the size of data, returned in the Value field, was determined. The value is the integer number of seconds since 1970-01-01T00:00:00Z.
        public var timestamp: Foundation.Date?
        /// The latest known metered size (in bytes) of data stored in the file system.
        /// This member is required.
        public var value: Swift.Int
        /// The latest known metered size (in bytes) of data stored in the Archive storage class.
        public var valueInArchive: Swift.Int?
        /// The latest known metered size (in bytes) of data stored in the Infrequent Access storage class.
        public var valueInIA: Swift.Int?
        /// The latest known metered size (in bytes) of data stored in the Standard storage class.
        public var valueInStandard: Swift.Int?

        public init(
            timestamp: Foundation.Date? = nil,
            value: Swift.Int = 0,
            valueInArchive: Swift.Int? = nil,
            valueInIA: Swift.Int? = nil,
            valueInStandard: Swift.Int? = nil
        )
        {
            self.timestamp = timestamp
            self.value = value
            self.valueInArchive = valueInArchive
            self.valueInIA = valueInIA
            self.valueInStandard = valueInStandard
        }
    }
}

/// A description of the file system.
public struct CreateFileSystemOutput: Swift.Sendable {
    /// The unique and consistent identifier of the Availability Zone in which the file system is located, and is valid only for One Zone file systems. For example, use1-az1 is an Availability Zone ID for the us-east-1 Amazon Web Services Region, and it has the same location in every Amazon Web Services account.
    public var availabilityZoneId: Swift.String?
    /// Describes the Amazon Web Services Availability Zone in which the file system is located, and is valid only for One Zone file systems. For more information, see [Using EFS storage classes](https://docs.aws.amazon.com/efs/latest/ug/storage-classes.html) in the Amazon EFS User Guide.
    public var availabilityZoneName: Swift.String?
    /// The time that the file system was created, in seconds (since 1970-01-01T00:00:00Z).
    /// This member is required.
    public var creationTime: Foundation.Date?
    /// The opaque string specified in the request.
    /// This member is required.
    public var creationToken: Swift.String?
    /// A Boolean value that, if true, indicates that the file system is encrypted.
    public var encrypted: Swift.Bool?
    /// The Amazon Resource Name (ARN) for the EFS file system, in the format arn:aws:elasticfilesystem:region:account-id:file-system/file-system-id . Example with sample data: arn:aws:elasticfilesystem:us-west-2:****************:file-system/fs-********
    public var fileSystemArn: Swift.String?
    /// The ID of the file system, assigned by Amazon EFS.
    /// This member is required.
    public var fileSystemId: Swift.String?
    /// Describes the protection on the file system.
    public var fileSystemProtection: EFSClientTypes.FileSystemProtectionDescription?
    /// The ID of an KMS key used to protect the encrypted file system.
    public var kmsKeyId: Swift.String?
    /// The lifecycle phase of the file system.
    /// This member is required.
    public var lifeCycleState: EFSClientTypes.LifeCycleState?
    /// You can add tags to a file system, including a Name tag. For more information, see [CreateFileSystem]. If the file system has a Name tag, Amazon EFS returns the value in this field.
    public var name: Swift.String?
    /// The current number of mount targets that the file system has. For more information, see [CreateMountTarget].
    /// This member is required.
    public var numberOfMountTargets: Swift.Int
    /// The Amazon Web Services account that created the file system.
    /// This member is required.
    public var ownerId: Swift.String?
    /// The performance mode of the file system.
    /// This member is required.
    public var performanceMode: EFSClientTypes.PerformanceMode?
    /// The amount of provisioned throughput, measured in MiBps, for the file system. Valid for file systems using ThroughputMode set to provisioned.
    public var provisionedThroughputInMibps: Swift.Double?
    /// The latest known metered size (in bytes) of data stored in the file system, in its Value field, and the time at which that size was determined in its Timestamp field. The Timestamp value is the integer number of seconds since 1970-01-01T00:00:00Z. The SizeInBytes value doesn't represent the size of a consistent snapshot of the file system, but it is eventually consistent when there are no writes to the file system. That is, SizeInBytes represents actual size only if the file system is not modified for a period longer than a couple of hours. Otherwise, the value is not the exact size that the file system was at any point in time.
    /// This member is required.
    public var sizeInBytes: EFSClientTypes.FileSystemSize?
    /// The tags associated with the file system, presented as an array of Tag objects.
    /// This member is required.
    public var tags: [EFSClientTypes.Tag]?
    /// Displays the file system's throughput mode. For more information, see [Throughput modes](https://docs.aws.amazon.com/efs/latest/ug/performance.html#throughput-modes) in the Amazon EFS User Guide.
    public var throughputMode: EFSClientTypes.ThroughputMode?

    public init(
        availabilityZoneId: Swift.String? = nil,
        availabilityZoneName: Swift.String? = nil,
        creationTime: Foundation.Date? = nil,
        creationToken: Swift.String? = nil,
        encrypted: Swift.Bool? = nil,
        fileSystemArn: Swift.String? = nil,
        fileSystemId: Swift.String? = nil,
        fileSystemProtection: EFSClientTypes.FileSystemProtectionDescription? = nil,
        kmsKeyId: Swift.String? = nil,
        lifeCycleState: EFSClientTypes.LifeCycleState? = nil,
        name: Swift.String? = nil,
        numberOfMountTargets: Swift.Int = 0,
        ownerId: Swift.String? = nil,
        performanceMode: EFSClientTypes.PerformanceMode? = nil,
        provisionedThroughputInMibps: Swift.Double? = nil,
        sizeInBytes: EFSClientTypes.FileSystemSize? = nil,
        tags: [EFSClientTypes.Tag]? = nil,
        throughputMode: EFSClientTypes.ThroughputMode? = nil
    )
    {
        self.availabilityZoneId = availabilityZoneId
        self.availabilityZoneName = availabilityZoneName
        self.creationTime = creationTime
        self.creationToken = creationToken
        self.encrypted = encrypted
        self.fileSystemArn = fileSystemArn
        self.fileSystemId = fileSystemId
        self.fileSystemProtection = fileSystemProtection
        self.kmsKeyId = kmsKeyId
        self.lifeCycleState = lifeCycleState
        self.name = name
        self.numberOfMountTargets = numberOfMountTargets
        self.ownerId = ownerId
        self.performanceMode = performanceMode
        self.provisionedThroughputInMibps = provisionedThroughputInMibps
        self.sizeInBytes = sizeInBytes
        self.tags = tags
        self.throughputMode = throughputMode
    }
}

/// Returned if the request specified an IpAddress that is already in use in the subnet.
public struct IpAddressInUse: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "IpAddressInUse" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if the mount target would violate one of the specified restrictions based on the file system's existing mount targets.
public struct MountTargetConflict: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "MountTargetConflict" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// The calling account has reached the limit for elastic network interfaces for the specific Amazon Web Services Region. Either delete some network interfaces or request that the account quota be raised. For more information, see [Amazon VPC Quotas](https://docs.aws.amazon.com/AmazonVPC/latest/UserGuide/VPC_Appendix_Limits.html) in the Amazon VPC User Guide (see the Network interfaces per Region entry in the Network interfaces table).
public struct NetworkInterfaceLimitExceeded: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "NetworkInterfaceLimitExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if IpAddress was not specified in the request and there are no free IP addresses in the subnet.
public struct NoFreeAddressesInSubnet: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "NoFreeAddressesInSubnet" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if the size of SecurityGroups specified in the request is greater than five.
public struct SecurityGroupLimitExceeded: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SecurityGroupLimitExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if one of the specified security groups doesn't exist in the subnet's virtual private cloud (VPC).
public struct SecurityGroupNotFound: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SecurityGroupNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if there is no subnet with ID SubnetId provided in the request.
public struct SubnetNotFound: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SubnetNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

///
public struct CreateMountTargetInput: Swift.Sendable {
    /// The ID of the file system for which to create the mount target.
    /// This member is required.
    public var fileSystemId: Swift.String?
    /// Valid IPv4 address within the address range of the specified subnet.
    public var ipAddress: Swift.String?
    /// Up to five VPC security group IDs, of the form sg-xxxxxxxx. These must be for the same VPC as subnet specified.
    public var securityGroups: [Swift.String]?
    /// The ID of the subnet to add the mount target in. For One Zone file systems, use the subnet that is associated with the file system's Availability Zone.
    /// This member is required.
    public var subnetId: Swift.String?

    public init(
        fileSystemId: Swift.String? = nil,
        ipAddress: Swift.String? = nil,
        securityGroups: [Swift.String]? = nil,
        subnetId: Swift.String? = nil
    )
    {
        self.fileSystemId = fileSystemId
        self.ipAddress = ipAddress
        self.securityGroups = securityGroups
        self.subnetId = subnetId
    }
}

/// Provides a description of a mount target.
public struct CreateMountTargetOutput: Swift.Sendable {
    /// The unique and consistent identifier of the Availability Zone that the mount target resides in. For example, use1-az1 is an AZ ID for the us-east-1 Region and it has the same location in every Amazon Web Services account.
    public var availabilityZoneId: Swift.String?
    /// The name of the Availability Zone in which the mount target is located. Availability Zones are independently mapped to names for each Amazon Web Services account. For example, the Availability Zone us-east-1a for your Amazon Web Services account might not be the same location as us-east-1a for another Amazon Web Services account.
    public var availabilityZoneName: Swift.String?
    /// The ID of the file system for which the mount target is intended.
    /// This member is required.
    public var fileSystemId: Swift.String?
    /// Address at which the file system can be mounted by using the mount target.
    public var ipAddress: Swift.String?
    /// Lifecycle state of the mount target.
    /// This member is required.
    public var lifeCycleState: EFSClientTypes.LifeCycleState?
    /// System-assigned mount target ID.
    /// This member is required.
    public var mountTargetId: Swift.String?
    /// The ID of the network interface that Amazon EFS created when it created the mount target.
    public var networkInterfaceId: Swift.String?
    /// Amazon Web Services account ID that owns the resource.
    public var ownerId: Swift.String?
    /// The ID of the mount target's subnet.
    /// This member is required.
    public var subnetId: Swift.String?
    /// The virtual private cloud (VPC) ID that the mount target is configured in.
    public var vpcId: Swift.String?

    public init(
        availabilityZoneId: Swift.String? = nil,
        availabilityZoneName: Swift.String? = nil,
        fileSystemId: Swift.String? = nil,
        ipAddress: Swift.String? = nil,
        lifeCycleState: EFSClientTypes.LifeCycleState? = nil,
        mountTargetId: Swift.String? = nil,
        networkInterfaceId: Swift.String? = nil,
        ownerId: Swift.String? = nil,
        subnetId: Swift.String? = nil,
        vpcId: Swift.String? = nil
    )
    {
        self.availabilityZoneId = availabilityZoneId
        self.availabilityZoneName = availabilityZoneName
        self.fileSystemId = fileSystemId
        self.ipAddress = ipAddress
        self.lifeCycleState = lifeCycleState
        self.mountTargetId = mountTargetId
        self.networkInterfaceId = networkInterfaceId
        self.ownerId = ownerId
        self.subnetId = subnetId
        self.vpcId = vpcId
    }
}

/// Returned if the specified file system does not have a replication configuration.
public struct ReplicationNotFound: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// ReplicationNotFound
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ReplicationNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if the Backup service is not available in the Amazon Web Services Region in which the request was made.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

extension EFSClientTypes {

    /// Describes the new or existing destination file system for the replication configuration.
    ///
    /// * If you want to replicate to a new file system, do not specify the File System ID for the destination file system. Amazon EFS creates a new, empty file system. For One Zone storage, specify the Availability Zone to create the file system in. To use an Key Management Service key other than the default KMS key, then specify it. For more information, see [Configuring replication to new Amazon EFS file system](https://docs.aws.amazon.com/efs/latest/ug/create-replication.html) in the Amazon EFS User Guide. After the file system is created, you cannot change the KMS key or the performance mode.
    ///
    /// * If you want to replicate to an existing file system that's in the same account as the source file system, then you need to provide the ID or Amazon Resource Name (ARN) of the file system to which to replicate. The file system's replication overwrite protection must be disabled. For more information, see [Replicating to an existing file system](https://docs.aws.amazon.com/efs/latest/ug/efs-replication#replicate-existing-destination) in the Amazon EFS User Guide.
    ///
    /// * If you are replicating the file system to a file system that's in a different account than the source file system (cross-account replication), you need to provide the ARN for the file system and the IAM role that allows Amazon EFS to perform replication on the destination account. The file system's replication overwrite protection must be disabled. For more information, see [Replicating across Amazon Web Services accounts](https://docs.aws.amazon.com/efs/latest/ug/cross-account-replication.html) in the Amazon EFS User Guide.
    public struct DestinationToCreate: Swift.Sendable {
        /// To create a file system that uses One Zone storage, specify the name of the Availability Zone in which to create the destination file system.
        public var availabilityZoneName: Swift.String?
        /// The ID or ARN of the file system to use for the destination. For cross-account replication, this must be an ARN. The file system's replication overwrite replication must be disabled. If no ID or ARN is specified, then a new file system is created.
        public var fileSystemId: Swift.String?
        /// Specify the Key Management Service (KMS) key that you want to use to encrypt the destination file system. If you do not specify a KMS key, Amazon EFS uses your default KMS key for Amazon EFS, /aws/elasticfilesystem. This ID can be in one of the following formats:
        ///
        /// * Key ID - The unique identifier of the key, for example 1234abcd-12ab-34cd-56ef-1234567890ab.
        ///
        /// * ARN - The ARN for the key, for example arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab.
        ///
        /// * Key alias - A previously created display name for a key, for example alias/projectKey1.
        ///
        /// * Key alias ARN - The ARN for a key alias, for example arn:aws:kms:us-west-2:************:alias/projectKey1.
        public var kmsKeyId: Swift.String?
        /// To create a file system that uses Regional storage, specify the Amazon Web Services Region in which to create the destination file system. The Region must be enabled for the Amazon Web Services account that owns the source file system. For more information, see [Managing Amazon Web Services Regions](https://docs.aws.amazon.com/general/latest/gr/rande-manage.html#rande-manage-enable) in the Amazon Web Services General Reference Reference Guide.
        public var region: Swift.String?
        /// Amazon Resource Name (ARN) of the IAM role in the source account that allows Amazon EFS to perform replication on its behalf. This is optional for same-account replication and required for cross-account replication.
        public var roleArn: Swift.String?

        public init(
            availabilityZoneName: Swift.String? = nil,
            fileSystemId: Swift.String? = nil,
            kmsKeyId: Swift.String? = nil,
            region: Swift.String? = nil,
            roleArn: Swift.String? = nil
        )
        {
            self.availabilityZoneName = availabilityZoneName
            self.fileSystemId = fileSystemId
            self.kmsKeyId = kmsKeyId
            self.region = region
            self.roleArn = roleArn
        }
    }
}

public struct CreateReplicationConfigurationInput: Swift.Sendable {
    /// An array of destination configuration objects. Only one destination configuration object is supported.
    /// This member is required.
    public var destinations: [EFSClientTypes.DestinationToCreate]?
    /// Specifies the Amazon EFS file system that you want to replicate. This file system cannot already be a source or destination file system in another replication configuration.
    /// This member is required.
    public var sourceFileSystemId: Swift.String?

    public init(
        destinations: [EFSClientTypes.DestinationToCreate]? = nil,
        sourceFileSystemId: Swift.String? = nil
    )
    {
        self.destinations = destinations
        self.sourceFileSystemId = sourceFileSystemId
    }
}

extension EFSClientTypes {

    public enum ReplicationStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case deleting
        case enabled
        case enabling
        case error
        case paused
        case pausing
        case sdkUnknown(Swift.String)

        public static var allCases: [ReplicationStatus] {
            return [
                .deleting,
                .enabled,
                .enabling,
                .error,
                .paused,
                .pausing
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .deleting: return "DELETING"
            case .enabled: return "ENABLED"
            case .enabling: return "ENABLING"
            case .error: return "ERROR"
            case .paused: return "PAUSED"
            case .pausing: return "PAUSING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EFSClientTypes {

    /// Describes the destination file system in the replication configuration.
    public struct Destination: Swift.Sendable {
        /// The ID of the destination Amazon EFS file system.
        /// This member is required.
        public var fileSystemId: Swift.String?
        /// The time when the most recent sync was successfully completed on the destination file system. Any changes to data on the source file system that occurred before this time have been successfully replicated to the destination file system. Any changes that occurred after this time might not be fully replicated.
        public var lastReplicatedTimestamp: Foundation.Date?
        /// ID of the Amazon Web Services account in which the destination file system resides.
        public var ownerId: Swift.String?
        /// The Amazon Web Services Region in which the destination file system is located.
        /// This member is required.
        public var region: Swift.String?
        /// Amazon Resource Name (ARN) of the IAM role in the source account that allows Amazon EFS to perform replication on its behalf. This is optional for same-account replication and required for cross-account replication.
        public var roleArn: Swift.String?
        /// Describes the status of the replication configuration. For more information about replication status, see [Viewing replication details](https://docs.aws.amazon.com/efs/latest/ug/awsbackup.html#restoring-backup-efsmonitoring-replication-status.html) in the Amazon EFS User Guide.
        /// This member is required.
        public var status: EFSClientTypes.ReplicationStatus?
        /// Message that provides details about the PAUSED or ERRROR state of the replication destination configuration. For more information about replication status messages, see [Viewing replication details](https://docs.aws.amazon.com/efs/latest/ug/awsbackup.html#restoring-backup-efsmonitoring-replication-status.html) in the Amazon EFS User Guide.
        public var statusMessage: Swift.String?

        public init(
            fileSystemId: Swift.String? = nil,
            lastReplicatedTimestamp: Foundation.Date? = nil,
            ownerId: Swift.String? = nil,
            region: Swift.String? = nil,
            roleArn: Swift.String? = nil,
            status: EFSClientTypes.ReplicationStatus? = nil,
            statusMessage: Swift.String? = nil
        )
        {
            self.fileSystemId = fileSystemId
            self.lastReplicatedTimestamp = lastReplicatedTimestamp
            self.ownerId = ownerId
            self.region = region
            self.roleArn = roleArn
            self.status = status
            self.statusMessage = statusMessage
        }
    }
}

/// Describes the replication configuration for a specific file system.
public struct CreateReplicationConfigurationOutput: Swift.Sendable {
    /// Describes when the replication configuration was created.
    /// This member is required.
    public var creationTime: Foundation.Date?
    /// An array of destination objects. Only one destination object is supported.
    /// This member is required.
    public var destinations: [EFSClientTypes.Destination]?
    /// The Amazon Resource Name (ARN) of the original source EFS file system in the replication configuration.
    /// This member is required.
    public var originalSourceFileSystemArn: Swift.String?
    /// The Amazon Resource Name (ARN) of the current source file system in the replication configuration.
    /// This member is required.
    public var sourceFileSystemArn: Swift.String?
    /// The ID of the source Amazon EFS file system that is being replicated.
    /// This member is required.
    public var sourceFileSystemId: Swift.String?
    /// ID of the Amazon Web Services account in which the source file system resides.
    public var sourceFileSystemOwnerId: Swift.String?
    /// The Amazon Web Services Region in which the source EFS file system is located.
    /// This member is required.
    public var sourceFileSystemRegion: Swift.String?

    public init(
        creationTime: Foundation.Date? = nil,
        destinations: [EFSClientTypes.Destination]? = nil,
        originalSourceFileSystemArn: Swift.String? = nil,
        sourceFileSystemArn: Swift.String? = nil,
        sourceFileSystemId: Swift.String? = nil,
        sourceFileSystemOwnerId: Swift.String? = nil,
        sourceFileSystemRegion: Swift.String? = nil
    )
    {
        self.creationTime = creationTime
        self.destinations = destinations
        self.originalSourceFileSystemArn = originalSourceFileSystemArn
        self.sourceFileSystemArn = sourceFileSystemArn
        self.sourceFileSystemId = sourceFileSystemId
        self.sourceFileSystemOwnerId = sourceFileSystemOwnerId
        self.sourceFileSystemRegion = sourceFileSystemRegion
    }
}

///
public struct CreateTagsInput: Swift.Sendable {
    /// The ID of the file system whose tags you want to modify (String). This operation modifies the tags only, not the file system.
    /// This member is required.
    public var fileSystemId: Swift.String?
    /// An array of Tag objects to add. Each Tag object is a key-value pair.
    /// This member is required.
    public var tags: [EFSClientTypes.Tag]?

    public init(
        fileSystemId: Swift.String? = nil,
        tags: [EFSClientTypes.Tag]? = nil
    )
    {
        self.fileSystemId = fileSystemId
        self.tags = tags
    }
}

public struct DeleteAccessPointInput: Swift.Sendable {
    /// The ID of the access point that you want to delete.
    /// This member is required.
    public var accessPointId: Swift.String?

    public init(
        accessPointId: Swift.String? = nil
    )
    {
        self.accessPointId = accessPointId
    }
}

/// Returned if a file system has mount targets.
public struct FileSystemInUse: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "FileSystemInUse" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

///
public struct DeleteFileSystemInput: Swift.Sendable {
    /// The ID of the file system you want to delete.
    /// This member is required.
    public var fileSystemId: Swift.String?

    public init(
        fileSystemId: Swift.String? = nil
    )
    {
        self.fileSystemId = fileSystemId
    }
}

public struct DeleteFileSystemPolicyInput: Swift.Sendable {
    /// Specifies the EFS file system for which to delete the FileSystemPolicy.
    /// This member is required.
    public var fileSystemId: Swift.String?

    public init(
        fileSystemId: Swift.String? = nil
    )
    {
        self.fileSystemId = fileSystemId
    }
}

/// The service timed out trying to fulfill the request, and the client should try the call again.
public struct DependencyTimeout: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DependencyTimeout" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// Returned if there is no mount target with the specified ID found in the caller's Amazon Web Services account.
public struct MountTargetNotFound: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "MountTargetNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

///
public struct DeleteMountTargetInput: Swift.Sendable {
    /// The ID of the mount target to delete (String).
    /// This member is required.
    public var mountTargetId: Swift.String?

    public init(
        mountTargetId: Swift.String? = nil
    )
    {
        self.mountTargetId = mountTargetId
    }
}

extension EFSClientTypes {

    public enum DeletionMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case allConfigurations
        case localConfigurationOnly
        case sdkUnknown(Swift.String)

        public static var allCases: [DeletionMode] {
            return [
                .allConfigurations,
                .localConfigurationOnly
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .allConfigurations: return "ALL_CONFIGURATIONS"
            case .localConfigurationOnly: return "LOCAL_CONFIGURATION_ONLY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DeleteReplicationConfigurationInput: Swift.Sendable {
    /// When replicating across Amazon Web Services accounts or across Amazon Web Services Regions, Amazon EFS deletes the replication configuration from both the source and destination account or Region (ALL_CONFIGURATIONS) by default. If there's a configuration or permissions issue that prevents Amazon EFS from deleting the replication configuration from both sides, you can use the LOCAL_CONFIGURATION_ONLY mode to delete the replication configuration from only the local side (the account or Region from which the delete is performed). Only use the LOCAL_CONFIGURATION_ONLY mode in the case that Amazon EFS is unable to delete the replication configuration in both the source and destination account or Region. Deleting the local configuration leaves the configuration in the other account or Region unrecoverable. Additionally, do not use this mode for same-account, same-region replication as doing so results in a BadRequest exception error.
    public var deletionMode: EFSClientTypes.DeletionMode?
    /// The ID of the source file system in the replication configuration.
    /// This member is required.
    public var sourceFileSystemId: Swift.String?

    public init(
        deletionMode: EFSClientTypes.DeletionMode? = nil,
        sourceFileSystemId: Swift.String? = nil
    )
    {
        self.deletionMode = deletionMode
        self.sourceFileSystemId = sourceFileSystemId
    }
}

///
public struct DeleteTagsInput: Swift.Sendable {
    /// The ID of the file system whose tags you want to delete (String).
    /// This member is required.
    public var fileSystemId: Swift.String?
    /// A list of tag keys to delete.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        fileSystemId: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.fileSystemId = fileSystemId
        self.tagKeys = tagKeys
    }
}

public struct DescribeAccessPointsInput: Swift.Sendable {
    /// (Optional) Specifies an EFS access point to describe in the response; mutually exclusive with FileSystemId.
    public var accessPointId: Swift.String?
    /// (Optional) If you provide a FileSystemId, EFS returns all access points for that file system; mutually exclusive with AccessPointId.
    public var fileSystemId: Swift.String?
    /// (Optional) When retrieving all access points for a file system, you can optionally specify the MaxItems parameter to limit the number of objects returned in a response. The default value is 100.
    public var maxResults: Swift.Int?
    /// NextToken is present if the response is paginated. You can use NextMarker in the subsequent request to fetch the next page of access point descriptions.
    public var nextToken: Swift.String?

    public init(
        accessPointId: Swift.String? = nil,
        fileSystemId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.accessPointId = accessPointId
        self.fileSystemId = fileSystemId
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct DescribeAccessPointsOutput: Swift.Sendable {
    /// An array of access point descriptions.
    public var accessPoints: [EFSClientTypes.AccessPointDescription]?
    /// Present if there are more access points than returned in the response. You can use the NextMarker in the subsequent request to fetch the additional descriptions.
    public var nextToken: Swift.String?

    public init(
        accessPoints: [EFSClientTypes.AccessPointDescription]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.accessPoints = accessPoints
        self.nextToken = nextToken
    }
}

public struct DescribeAccountPreferencesInput: Swift.Sendable {
    /// (Optional) When retrieving account preferences, you can optionally specify the MaxItems parameter to limit the number of objects returned in a response. The default value is 100.
    public var maxResults: Swift.Int?
    /// (Optional) You can use NextToken in a subsequent request to fetch the next page of Amazon Web Services account preferences if the response payload was paginated.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension EFSClientTypes {

    /// A preference indicating a choice to use 63bit/32bit IDs for all applicable resources.
    public enum ResourceIdType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case longid
        case shortid
        case sdkUnknown(Swift.String)

        public static var allCases: [ResourceIdType] {
            return [
                .longid,
                .shortid
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .longid: return "LONG_ID"
            case .shortid: return "SHORT_ID"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EFSClientTypes {

    /// An EFS resource, for example a file system or a mount target.
    public enum Resource: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case filesystem
        case mounttarget
        case sdkUnknown(Swift.String)

        public static var allCases: [Resource] {
            return [
                .filesystem,
                .mounttarget
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .filesystem: return "FILE_SYSTEM"
            case .mounttarget: return "MOUNT_TARGET"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EFSClientTypes {

    /// Describes the resource type and its ID preference for the user's Amazon Web Services account, in the current Amazon Web Services Region.
    public struct ResourceIdPreference: Swift.Sendable {
        /// Identifies the EFS resource ID preference, either LONG_ID (17 characters) or SHORT_ID (8 characters).
        public var resourceIdType: EFSClientTypes.ResourceIdType?
        /// Identifies the Amazon EFS resources to which the ID preference setting applies, FILE_SYSTEM and MOUNT_TARGET.
        public var resources: [EFSClientTypes.Resource]?

        public init(
            resourceIdType: EFSClientTypes.ResourceIdType? = nil,
            resources: [EFSClientTypes.Resource]? = nil
        )
        {
            self.resourceIdType = resourceIdType
            self.resources = resources
        }
    }
}

public struct DescribeAccountPreferencesOutput: Swift.Sendable {
    /// Present if there are more records than returned in the response. You can use the NextToken in the subsequent request to fetch the additional descriptions.
    public var nextToken: Swift.String?
    /// Describes the resource ID preference setting for the Amazon Web Services account associated with the user making the request, in the current Amazon Web Services Region.
    public var resourceIdPreference: EFSClientTypes.ResourceIdPreference?

    public init(
        nextToken: Swift.String? = nil,
        resourceIdPreference: EFSClientTypes.ResourceIdPreference? = nil
    )
    {
        self.nextToken = nextToken
        self.resourceIdPreference = resourceIdPreference
    }
}

/// Returned if the default file system policy is in effect for the EFS file system specified.
public struct PolicyNotFound: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "PolicyNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

public struct DescribeBackupPolicyInput: Swift.Sendable {
    /// Specifies which EFS file system for which to retrieve the BackupPolicy.
    /// This member is required.
    public var fileSystemId: Swift.String?

    public init(
        fileSystemId: Swift.String? = nil
    )
    {
        self.fileSystemId = fileSystemId
    }
}

public struct DescribeBackupPolicyOutput: Swift.Sendable {
    /// Describes the file system's backup policy, indicating whether automatic backups are turned on or off.
    public var backupPolicy: EFSClientTypes.BackupPolicy?

    public init(
        backupPolicy: EFSClientTypes.BackupPolicy? = nil
    )
    {
        self.backupPolicy = backupPolicy
    }
}

public struct DescribeFileSystemPolicyInput: Swift.Sendable {
    /// Specifies which EFS file system to retrieve the FileSystemPolicy for.
    /// This member is required.
    public var fileSystemId: Swift.String?

    public init(
        fileSystemId: Swift.String? = nil
    )
    {
        self.fileSystemId = fileSystemId
    }
}

public struct DescribeFileSystemPolicyOutput: Swift.Sendable {
    /// Specifies the EFS file system to which the FileSystemPolicy applies.
    public var fileSystemId: Swift.String?
    /// The JSON formatted FileSystemPolicy for the EFS file system.
    public var policy: Swift.String?

    public init(
        fileSystemId: Swift.String? = nil,
        policy: Swift.String? = nil
    )
    {
        self.fileSystemId = fileSystemId
        self.policy = policy
    }
}

///
public struct DescribeFileSystemsInput: Swift.Sendable {
    /// (Optional) Restricts the list to the file system with this creation token (String). You specify a creation token when you create an Amazon EFS file system.
    public var creationToken: Swift.String?
    /// (Optional) ID of the file system whose description you want to retrieve (String).
    public var fileSystemId: Swift.String?
    /// (Optional) Opaque pagination token returned from a previous DescribeFileSystems operation (String). If present, specifies to continue the list from where the returning call had left off.
    public var marker: Swift.String?
    /// (Optional) Specifies the maximum number of file systems to return in the response (integer). This number is automatically set to 100. The response is paginated at 100 per page if you have more than 100 file systems.
    public var maxItems: Swift.Int?

    public init(
        creationToken: Swift.String? = nil,
        fileSystemId: Swift.String? = nil,
        marker: Swift.String? = nil,
        maxItems: Swift.Int? = nil
    )
    {
        self.creationToken = creationToken
        self.fileSystemId = fileSystemId
        self.marker = marker
        self.maxItems = maxItems
    }
}

extension EFSClientTypes {

    /// A description of the file system.
    public struct FileSystemDescription: Swift.Sendable {
        /// The unique and consistent identifier of the Availability Zone in which the file system is located, and is valid only for One Zone file systems. For example, use1-az1 is an Availability Zone ID for the us-east-1 Amazon Web Services Region, and it has the same location in every Amazon Web Services account.
        public var availabilityZoneId: Swift.String?
        /// Describes the Amazon Web Services Availability Zone in which the file system is located, and is valid only for One Zone file systems. For more information, see [Using EFS storage classes](https://docs.aws.amazon.com/efs/latest/ug/storage-classes.html) in the Amazon EFS User Guide.
        public var availabilityZoneName: Swift.String?
        /// The time that the file system was created, in seconds (since 1970-01-01T00:00:00Z).
        /// This member is required.
        public var creationTime: Foundation.Date?
        /// The opaque string specified in the request.
        /// This member is required.
        public var creationToken: Swift.String?
        /// A Boolean value that, if true, indicates that the file system is encrypted.
        public var encrypted: Swift.Bool?
        /// The Amazon Resource Name (ARN) for the EFS file system, in the format arn:aws:elasticfilesystem:region:account-id:file-system/file-system-id . Example with sample data: arn:aws:elasticfilesystem:us-west-2:****************:file-system/fs-********
        public var fileSystemArn: Swift.String?
        /// The ID of the file system, assigned by Amazon EFS.
        /// This member is required.
        public var fileSystemId: Swift.String?
        /// Describes the protection on the file system.
        public var fileSystemProtection: EFSClientTypes.FileSystemProtectionDescription?
        /// The ID of an KMS key used to protect the encrypted file system.
        public var kmsKeyId: Swift.String?
        /// The lifecycle phase of the file system.
        /// This member is required.
        public var lifeCycleState: EFSClientTypes.LifeCycleState?
        /// You can add tags to a file system, including a Name tag. For more information, see [CreateFileSystem]. If the file system has a Name tag, Amazon EFS returns the value in this field.
        public var name: Swift.String?
        /// The current number of mount targets that the file system has. For more information, see [CreateMountTarget].
        /// This member is required.
        public var numberOfMountTargets: Swift.Int
        /// The Amazon Web Services account that created the file system.
        /// This member is required.
        public var ownerId: Swift.String?
        /// The performance mode of the file system.
        /// This member is required.
        public var performanceMode: EFSClientTypes.PerformanceMode?
        /// The amount of provisioned throughput, measured in MiBps, for the file system. Valid for file systems using ThroughputMode set to provisioned.
        public var provisionedThroughputInMibps: Swift.Double?
        /// The latest known metered size (in bytes) of data stored in the file system, in its Value field, and the time at which that size was determined in its Timestamp field. The Timestamp value is the integer number of seconds since 1970-01-01T00:00:00Z. The SizeInBytes value doesn't represent the size of a consistent snapshot of the file system, but it is eventually consistent when there are no writes to the file system. That is, SizeInBytes represents actual size only if the file system is not modified for a period longer than a couple of hours. Otherwise, the value is not the exact size that the file system was at any point in time.
        /// This member is required.
        public var sizeInBytes: EFSClientTypes.FileSystemSize?
        /// The tags associated with the file system, presented as an array of Tag objects.
        /// This member is required.
        public var tags: [EFSClientTypes.Tag]?
        /// Displays the file system's throughput mode. For more information, see [Throughput modes](https://docs.aws.amazon.com/efs/latest/ug/performance.html#throughput-modes) in the Amazon EFS User Guide.
        public var throughputMode: EFSClientTypes.ThroughputMode?

        public init(
            availabilityZoneId: Swift.String? = nil,
            availabilityZoneName: Swift.String? = nil,
            creationTime: Foundation.Date? = nil,
            creationToken: Swift.String? = nil,
            encrypted: Swift.Bool? = nil,
            fileSystemArn: Swift.String? = nil,
            fileSystemId: Swift.String? = nil,
            fileSystemProtection: EFSClientTypes.FileSystemProtectionDescription? = nil,
            kmsKeyId: Swift.String? = nil,
            lifeCycleState: EFSClientTypes.LifeCycleState? = nil,
            name: Swift.String? = nil,
            numberOfMountTargets: Swift.Int = 0,
            ownerId: Swift.String? = nil,
            performanceMode: EFSClientTypes.PerformanceMode? = nil,
            provisionedThroughputInMibps: Swift.Double? = nil,
            sizeInBytes: EFSClientTypes.FileSystemSize? = nil,
            tags: [EFSClientTypes.Tag]? = nil,
            throughputMode: EFSClientTypes.ThroughputMode? = nil
        )
        {
            self.availabilityZoneId = availabilityZoneId
            self.availabilityZoneName = availabilityZoneName
            self.creationTime = creationTime
            self.creationToken = creationToken
            self.encrypted = encrypted
            self.fileSystemArn = fileSystemArn
            self.fileSystemId = fileSystemId
            self.fileSystemProtection = fileSystemProtection
            self.kmsKeyId = kmsKeyId
            self.lifeCycleState = lifeCycleState
            self.name = name
            self.numberOfMountTargets = numberOfMountTargets
            self.ownerId = ownerId
            self.performanceMode = performanceMode
            self.provisionedThroughputInMibps = provisionedThroughputInMibps
            self.sizeInBytes = sizeInBytes
            self.tags = tags
            self.throughputMode = throughputMode
        }
    }
}

public struct DescribeFileSystemsOutput: Swift.Sendable {
    /// An array of file system descriptions.
    public var fileSystems: [EFSClientTypes.FileSystemDescription]?
    /// Present if provided by caller in the request (String).
    public var marker: Swift.String?
    /// Present if there are more file systems than returned in the response (String). You can use the NextMarker in the subsequent request to fetch the descriptions.
    public var nextMarker: Swift.String?

    public init(
        fileSystems: [EFSClientTypes.FileSystemDescription]? = nil,
        marker: Swift.String? = nil,
        nextMarker: Swift.String? = nil
    )
    {
        self.fileSystems = fileSystems
        self.marker = marker
        self.nextMarker = nextMarker
    }
}

public struct DescribeLifecycleConfigurationInput: Swift.Sendable {
    /// The ID of the file system whose LifecycleConfiguration object you want to retrieve (String).
    /// This member is required.
    public var fileSystemId: Swift.String?

    public init(
        fileSystemId: Swift.String? = nil
    )
    {
        self.fileSystemId = fileSystemId
    }
}

extension EFSClientTypes {

    public enum TransitionToArchiveRules: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case after14Days
        case after180Days
        case after1Day
        case after270Days
        case after30Days
        case after365Days
        case after60Days
        case after7Days
        case after90Days
        case sdkUnknown(Swift.String)

        public static var allCases: [TransitionToArchiveRules] {
            return [
                .after14Days,
                .after180Days,
                .after1Day,
                .after270Days,
                .after30Days,
                .after365Days,
                .after60Days,
                .after7Days,
                .after90Days
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .after14Days: return "AFTER_14_DAYS"
            case .after180Days: return "AFTER_180_DAYS"
            case .after1Day: return "AFTER_1_DAY"
            case .after270Days: return "AFTER_270_DAYS"
            case .after30Days: return "AFTER_30_DAYS"
            case .after365Days: return "AFTER_365_DAYS"
            case .after60Days: return "AFTER_60_DAYS"
            case .after7Days: return "AFTER_7_DAYS"
            case .after90Days: return "AFTER_90_DAYS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EFSClientTypes {

    public enum TransitionToIARules: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case after14Days
        case after180Days
        case after1Day
        case after270Days
        case after30Days
        case after365Days
        case after60Days
        case after7Days
        case after90Days
        case sdkUnknown(Swift.String)

        public static var allCases: [TransitionToIARules] {
            return [
                .after14Days,
                .after180Days,
                .after1Day,
                .after270Days,
                .after30Days,
                .after365Days,
                .after60Days,
                .after7Days,
                .after90Days
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .after14Days: return "AFTER_14_DAYS"
            case .after180Days: return "AFTER_180_DAYS"
            case .after1Day: return "AFTER_1_DAY"
            case .after270Days: return "AFTER_270_DAYS"
            case .after30Days: return "AFTER_30_DAYS"
            case .after365Days: return "AFTER_365_DAYS"
            case .after60Days: return "AFTER_60_DAYS"
            case .after7Days: return "AFTER_7_DAYS"
            case .after90Days: return "AFTER_90_DAYS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EFSClientTypes {

    public enum TransitionToPrimaryStorageClassRules: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case after1Access
        case sdkUnknown(Swift.String)

        public static var allCases: [TransitionToPrimaryStorageClassRules] {
            return [
                .after1Access
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .after1Access: return "AFTER_1_ACCESS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EFSClientTypes {

    /// Describes a policy used by lifecycle management that specifies when to transition files into and out of storage classes. For more information, see [Managing file system storage](https://docs.aws.amazon.com/efs/latest/ug/lifecycle-management-efs.html). When using the put-lifecycle-configuration CLI command or the PutLifecycleConfiguration API action, Amazon EFS requires that each LifecyclePolicy object have only a single transition. This means that in a request body, LifecyclePolicies must be structured as an array of LifecyclePolicy objects, one object for each transition. For more information, see the request examples in [PutLifecycleConfiguration].
    public struct LifecyclePolicy: Swift.Sendable {
        /// The number of days after files were last accessed in primary storage (the Standard storage class) at which to move them to Archive storage. Metadata operations such as listing the contents of a directory don't count as file access events.
        public var transitionToArchive: EFSClientTypes.TransitionToArchiveRules?
        /// The number of days after files were last accessed in primary storage (the Standard storage class) at which to move them to Infrequent Access (IA) storage. Metadata operations such as listing the contents of a directory don't count as file access events.
        public var transitionToIA: EFSClientTypes.TransitionToIARules?
        /// Whether to move files back to primary (Standard) storage after they are accessed in IA or Archive storage. Metadata operations such as listing the contents of a directory don't count as file access events.
        public var transitionToPrimaryStorageClass: EFSClientTypes.TransitionToPrimaryStorageClassRules?

        public init(
            transitionToArchive: EFSClientTypes.TransitionToArchiveRules? = nil,
            transitionToIA: EFSClientTypes.TransitionToIARules? = nil,
            transitionToPrimaryStorageClass: EFSClientTypes.TransitionToPrimaryStorageClassRules? = nil
        )
        {
            self.transitionToArchive = transitionToArchive
            self.transitionToIA = transitionToIA
            self.transitionToPrimaryStorageClass = transitionToPrimaryStorageClass
        }
    }
}

public struct DescribeLifecycleConfigurationOutput: Swift.Sendable {
    /// An array of lifecycle management policies. EFS supports a maximum of one policy per file system.
    public var lifecyclePolicies: [EFSClientTypes.LifecyclePolicy]?

    public init(
        lifecyclePolicies: [EFSClientTypes.LifecyclePolicy]? = nil
    )
    {
        self.lifecyclePolicies = lifecyclePolicies
    }
}

///
public struct DescribeMountTargetsInput: Swift.Sendable {
    /// (Optional) The ID of the access point whose mount targets that you want to list. It must be included in your request if a FileSystemId or MountTargetId is not included in your request. Accepts either an access point ID or ARN as input.
    public var accessPointId: Swift.String?
    /// (Optional) ID of the file system whose mount targets you want to list (String). It must be included in your request if an AccessPointId or MountTargetId is not included. Accepts either a file system ID or ARN as input.
    public var fileSystemId: Swift.String?
    /// (Optional) Opaque pagination token returned from a previous DescribeMountTargets operation (String). If present, it specifies to continue the list from where the previous returning call left off.
    public var marker: Swift.String?
    /// (Optional) Maximum number of mount targets to return in the response. Currently, this number is automatically set to 10, and other values are ignored. The response is paginated at 100 per page if you have more than 100 mount targets.
    public var maxItems: Swift.Int?
    /// (Optional) ID of the mount target that you want to have described (String). It must be included in your request if FileSystemId is not included. Accepts either a mount target ID or ARN as input.
    public var mountTargetId: Swift.String?

    public init(
        accessPointId: Swift.String? = nil,
        fileSystemId: Swift.String? = nil,
        marker: Swift.String? = nil,
        maxItems: Swift.Int? = nil,
        mountTargetId: Swift.String? = nil
    )
    {
        self.accessPointId = accessPointId
        self.fileSystemId = fileSystemId
        self.marker = marker
        self.maxItems = maxItems
        self.mountTargetId = mountTargetId
    }
}

extension EFSClientTypes {

    /// Provides a description of a mount target.
    public struct MountTargetDescription: Swift.Sendable {
        /// The unique and consistent identifier of the Availability Zone that the mount target resides in. For example, use1-az1 is an AZ ID for the us-east-1 Region and it has the same location in every Amazon Web Services account.
        public var availabilityZoneId: Swift.String?
        /// The name of the Availability Zone in which the mount target is located. Availability Zones are independently mapped to names for each Amazon Web Services account. For example, the Availability Zone us-east-1a for your Amazon Web Services account might not be the same location as us-east-1a for another Amazon Web Services account.
        public var availabilityZoneName: Swift.String?
        /// The ID of the file system for which the mount target is intended.
        /// This member is required.
        public var fileSystemId: Swift.String?
        /// Address at which the file system can be mounted by using the mount target.
        public var ipAddress: Swift.String?
        /// Lifecycle state of the mount target.
        /// This member is required.
        public var lifeCycleState: EFSClientTypes.LifeCycleState?
        /// System-assigned mount target ID.
        /// This member is required.
        public var mountTargetId: Swift.String?
        /// The ID of the network interface that Amazon EFS created when it created the mount target.
        public var networkInterfaceId: Swift.String?
        /// Amazon Web Services account ID that owns the resource.
        public var ownerId: Swift.String?
        /// The ID of the mount target's subnet.
        /// This member is required.
        public var subnetId: Swift.String?
        /// The virtual private cloud (VPC) ID that the mount target is configured in.
        public var vpcId: Swift.String?

        public init(
            availabilityZoneId: Swift.String? = nil,
            availabilityZoneName: Swift.String? = nil,
            fileSystemId: Swift.String? = nil,
            ipAddress: Swift.String? = nil,
            lifeCycleState: EFSClientTypes.LifeCycleState? = nil,
            mountTargetId: Swift.String? = nil,
            networkInterfaceId: Swift.String? = nil,
            ownerId: Swift.String? = nil,
            subnetId: Swift.String? = nil,
            vpcId: Swift.String? = nil
        )
        {
            self.availabilityZoneId = availabilityZoneId
            self.availabilityZoneName = availabilityZoneName
            self.fileSystemId = fileSystemId
            self.ipAddress = ipAddress
            self.lifeCycleState = lifeCycleState
            self.mountTargetId = mountTargetId
            self.networkInterfaceId = networkInterfaceId
            self.ownerId = ownerId
            self.subnetId = subnetId
            self.vpcId = vpcId
        }
    }
}

///
public struct DescribeMountTargetsOutput: Swift.Sendable {
    /// If the request included the Marker, the response returns that value in this field.
    public var marker: Swift.String?
    /// Returns the file system's mount targets as an array of MountTargetDescription objects.
    public var mountTargets: [EFSClientTypes.MountTargetDescription]?
    /// If a value is present, there are more mount targets to return. In a subsequent request, you can provide Marker in your request with this value to retrieve the next set of mount targets.
    public var nextMarker: Swift.String?

    public init(
        marker: Swift.String? = nil,
        mountTargets: [EFSClientTypes.MountTargetDescription]? = nil,
        nextMarker: Swift.String? = nil
    )
    {
        self.marker = marker
        self.mountTargets = mountTargets
        self.nextMarker = nextMarker
    }
}

/// Returned if the mount target is not in the correct state for the operation.
public struct IncorrectMountTargetState: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "IncorrectMountTargetState" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

///
public struct DescribeMountTargetSecurityGroupsInput: Swift.Sendable {
    /// The ID of the mount target whose security groups you want to retrieve.
    /// This member is required.
    public var mountTargetId: Swift.String?

    public init(
        mountTargetId: Swift.String? = nil
    )
    {
        self.mountTargetId = mountTargetId
    }
}

public struct DescribeMountTargetSecurityGroupsOutput: Swift.Sendable {
    /// An array of security groups.
    /// This member is required.
    public var securityGroups: [Swift.String]?

    public init(
        securityGroups: [Swift.String]? = nil
    )
    {
        self.securityGroups = securityGroups
    }
}

public struct DescribeReplicationConfigurationsInput: Swift.Sendable {
    /// You can retrieve the replication configuration for a specific file system by providing its file system ID. For cross-account,cross-region replication, an account can only describe the replication configuration for a file system in its own Region.
    public var fileSystemId: Swift.String?
    /// (Optional) To limit the number of objects returned in a response, you can specify the MaxItems parameter. The default value is 100.
    public var maxResults: Swift.Int?
    /// NextToken is present if the response is paginated. You can use NextToken in a subsequent request to fetch the next page of output.
    public var nextToken: Swift.String?

    public init(
        fileSystemId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.fileSystemId = fileSystemId
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension EFSClientTypes {

    /// Describes the replication configuration for a specific file system.
    public struct ReplicationConfigurationDescription: Swift.Sendable {
        /// Describes when the replication configuration was created.
        /// This member is required.
        public var creationTime: Foundation.Date?
        /// An array of destination objects. Only one destination object is supported.
        /// This member is required.
        public var destinations: [EFSClientTypes.Destination]?
        /// The Amazon Resource Name (ARN) of the original source EFS file system in the replication configuration.
        /// This member is required.
        public var originalSourceFileSystemArn: Swift.String?
        /// The Amazon Resource Name (ARN) of the current source file system in the replication configuration.
        /// This member is required.
        public var sourceFileSystemArn: Swift.String?
        /// The ID of the source Amazon EFS file system that is being replicated.
        /// This member is required.
        public var sourceFileSystemId: Swift.String?
        /// ID of the Amazon Web Services account in which the source file system resides.
        public var sourceFileSystemOwnerId: Swift.String?
        /// The Amazon Web Services Region in which the source EFS file system is located.
        /// This member is required.
        public var sourceFileSystemRegion: Swift.String?

        public init(
            creationTime: Foundation.Date? = nil,
            destinations: [EFSClientTypes.Destination]? = nil,
            originalSourceFileSystemArn: Swift.String? = nil,
            sourceFileSystemArn: Swift.String? = nil,
            sourceFileSystemId: Swift.String? = nil,
            sourceFileSystemOwnerId: Swift.String? = nil,
            sourceFileSystemRegion: Swift.String? = nil
        )
        {
            self.creationTime = creationTime
            self.destinations = destinations
            self.originalSourceFileSystemArn = originalSourceFileSystemArn
            self.sourceFileSystemArn = sourceFileSystemArn
            self.sourceFileSystemId = sourceFileSystemId
            self.sourceFileSystemOwnerId = sourceFileSystemOwnerId
            self.sourceFileSystemRegion = sourceFileSystemRegion
        }
    }
}

public struct DescribeReplicationConfigurationsOutput: Swift.Sendable {
    /// You can use the NextToken from the previous response in a subsequent request to fetch the additional descriptions.
    public var nextToken: Swift.String?
    /// The collection of replication configurations that is returned.
    public var replications: [EFSClientTypes.ReplicationConfigurationDescription]?

    public init(
        nextToken: Swift.String? = nil,
        replications: [EFSClientTypes.ReplicationConfigurationDescription]? = nil
    )
    {
        self.nextToken = nextToken
        self.replications = replications
    }
}

///
public struct DescribeTagsInput: Swift.Sendable {
    /// The ID of the file system whose tag set you want to retrieve.
    /// This member is required.
    public var fileSystemId: Swift.String?
    /// (Optional) An opaque pagination token returned from a previous DescribeTags operation (String). If present, it specifies to continue the list from where the previous call left off.
    public var marker: Swift.String?
    /// (Optional) The maximum number of file system tags to return in the response. Currently, this number is automatically set to 100, and other values are ignored. The response is paginated at 100 per page if you have more than 100 tags.
    public var maxItems: Swift.Int?

    public init(
        fileSystemId: Swift.String? = nil,
        marker: Swift.String? = nil,
        maxItems: Swift.Int? = nil
    )
    {
        self.fileSystemId = fileSystemId
        self.marker = marker
        self.maxItems = maxItems
    }
}

///
public struct DescribeTagsOutput: Swift.Sendable {
    /// If the request included a Marker, the response returns that value in this field.
    public var marker: Swift.String?
    /// If a value is present, there are more tags to return. In a subsequent request, you can provide the value of NextMarker as the value of the Marker parameter in your next request to retrieve the next set of tags.
    public var nextMarker: Swift.String?
    /// Returns tags associated with the file system as an array of Tag objects.
    /// This member is required.
    public var tags: [EFSClientTypes.Tag]?

    public init(
        marker: Swift.String? = nil,
        nextMarker: Swift.String? = nil,
        tags: [EFSClientTypes.Tag]? = nil
    )
    {
        self.marker = marker
        self.nextMarker = nextMarker
        self.tags = tags
    }
}

/// Returned if the FileSystemPolicy is malformed or contains an error such as a parameter value that is not valid or a missing required parameter. Returned in the case of a policy lockout safety check error.
public struct InvalidPolicyException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidPolicyException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// (Optional) Specifies the maximum number of tag objects to return in the response. The default value is 100.
    public var maxResults: Swift.Int?
    /// (Optional) You can use NextToken in a subsequent request to fetch the next page of access point descriptions if the response payload was paginated.
    public var nextToken: Swift.String?
    /// Specifies the EFS resource you want to retrieve tags for. You can retrieve tags for EFS file systems and access points using this API endpoint.
    /// This member is required.
    public var resourceId: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        resourceId: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.resourceId = resourceId
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// NextToken is present if the response payload is paginated. You can use NextToken in a subsequent request to fetch the next page of access point descriptions.
    public var nextToken: Swift.String?
    /// An array of the tags for the specified EFS resource.
    public var tags: [EFSClientTypes.Tag]?

    public init(
        nextToken: Swift.String? = nil,
        tags: [EFSClientTypes.Tag]? = nil
    )
    {
        self.nextToken = nextToken
        self.tags = tags
    }
}

///
public struct ModifyMountTargetSecurityGroupsInput: Swift.Sendable {
    /// The ID of the mount target whose security groups you want to modify.
    /// This member is required.
    public var mountTargetId: Swift.String?
    /// An array of up to five VPC security group IDs.
    public var securityGroups: [Swift.String]?

    public init(
        mountTargetId: Swift.String? = nil,
        securityGroups: [Swift.String]? = nil
    )
    {
        self.mountTargetId = mountTargetId
        self.securityGroups = securityGroups
    }
}

public struct PutAccountPreferencesInput: Swift.Sendable {
    /// Specifies the EFS resource ID preference to set for the user's Amazon Web Services account, in the current Amazon Web Services Region, either LONG_ID (17 characters), or SHORT_ID (8 characters). Starting in October, 2021, you will receive an error when setting the account preference to SHORT_ID. Contact Amazon Web Services support if you receive an error and must use short IDs for file system and mount target resources.
    /// This member is required.
    public var resourceIdType: EFSClientTypes.ResourceIdType?

    public init(
        resourceIdType: EFSClientTypes.ResourceIdType? = nil
    )
    {
        self.resourceIdType = resourceIdType
    }
}

public struct PutAccountPreferencesOutput: Swift.Sendable {
    /// Describes the resource type and its ID preference for the user's Amazon Web Services account, in the current Amazon Web Services Region.
    public var resourceIdPreference: EFSClientTypes.ResourceIdPreference?

    public init(
        resourceIdPreference: EFSClientTypes.ResourceIdPreference? = nil
    )
    {
        self.resourceIdPreference = resourceIdPreference
    }
}

public struct PutBackupPolicyInput: Swift.Sendable {
    /// The backup policy included in the PutBackupPolicy request.
    /// This member is required.
    public var backupPolicy: EFSClientTypes.BackupPolicy?
    /// Specifies which EFS file system to update the backup policy for.
    /// This member is required.
    public var fileSystemId: Swift.String?

    public init(
        backupPolicy: EFSClientTypes.BackupPolicy? = nil,
        fileSystemId: Swift.String? = nil
    )
    {
        self.backupPolicy = backupPolicy
        self.fileSystemId = fileSystemId
    }
}

public struct PutBackupPolicyOutput: Swift.Sendable {
    /// Describes the file system's backup policy, indicating whether automatic backups are turned on or off.
    public var backupPolicy: EFSClientTypes.BackupPolicy?

    public init(
        backupPolicy: EFSClientTypes.BackupPolicy? = nil
    )
    {
        self.backupPolicy = backupPolicy
    }
}

public struct PutFileSystemPolicyInput: Swift.Sendable {
    /// (Optional) A boolean that specifies whether or not to bypass the FileSystemPolicy lockout safety check. The lockout safety check determines whether the policy in the request will lock out, or prevent, the IAM principal that is making the request from making future PutFileSystemPolicy requests on this file system. Set BypassPolicyLockoutSafetyCheck to True only when you intend to prevent the IAM principal that is making the request from making subsequent PutFileSystemPolicy requests on this file system. The default value is False.
    public var bypassPolicyLockoutSafetyCheck: Swift.Bool?
    /// The ID of the EFS file system that you want to create or update the FileSystemPolicy for.
    /// This member is required.
    public var fileSystemId: Swift.String?
    /// The FileSystemPolicy that you're creating. Accepts a JSON formatted policy definition. EFS file system policies have a 20,000 character limit. To find out more about the elements that make up a file system policy, see [Resource-based policies within Amazon EFS](https://docs.aws.amazon.com/efs/latest/ug/security_iam_service-with-iam.html#security_iam_service-with-iam-resource-based-policies).
    /// This member is required.
    public var policy: Swift.String?

    public init(
        bypassPolicyLockoutSafetyCheck: Swift.Bool? = false,
        fileSystemId: Swift.String? = nil,
        policy: Swift.String? = nil
    )
    {
        self.bypassPolicyLockoutSafetyCheck = bypassPolicyLockoutSafetyCheck
        self.fileSystemId = fileSystemId
        self.policy = policy
    }
}

public struct PutFileSystemPolicyOutput: Swift.Sendable {
    /// Specifies the EFS file system to which the FileSystemPolicy applies.
    public var fileSystemId: Swift.String?
    /// The JSON formatted FileSystemPolicy for the EFS file system.
    public var policy: Swift.String?

    public init(
        fileSystemId: Swift.String? = nil,
        policy: Swift.String? = nil
    )
    {
        self.fileSystemId = fileSystemId
        self.policy = policy
    }
}

public struct PutLifecycleConfigurationInput: Swift.Sendable {
    /// The ID of the file system for which you are creating the LifecycleConfiguration object (String).
    /// This member is required.
    public var fileSystemId: Swift.String?
    /// An array of LifecyclePolicy objects that define the file system's LifecycleConfiguration object. A LifecycleConfiguration object informs lifecycle management of the following:
    ///
    /// * TransitionToIA – When to move files in the file system from primary storage (Standard storage class) into the Infrequent Access (IA) storage.
    ///
    /// * TransitionToArchive – When to move files in the file system from their current storage class (either IA or Standard storage) into the Archive storage. File systems cannot transition into Archive storage before transitioning into IA storage. Therefore, TransitionToArchive must either not be set or must be later than TransitionToIA. The Archive storage class is available only for file systems that use the Elastic throughput mode and the General Purpose performance mode.
    ///
    /// * TransitionToPrimaryStorageClass – Whether to move files in the file system back to primary storage (Standard storage class) after they are accessed in IA or Archive storage.
    ///
    ///
    /// When using the put-lifecycle-configuration CLI command or the PutLifecycleConfiguration API action, Amazon EFS requires that each LifecyclePolicy object have only a single transition. This means that in a request body, LifecyclePolicies must be structured as an array of LifecyclePolicy objects, one object for each storage transition. See the example requests in the following section for more information.
    /// This member is required.
    public var lifecyclePolicies: [EFSClientTypes.LifecyclePolicy]?

    public init(
        fileSystemId: Swift.String? = nil,
        lifecyclePolicies: [EFSClientTypes.LifecyclePolicy]? = nil
    )
    {
        self.fileSystemId = fileSystemId
        self.lifecyclePolicies = lifecyclePolicies
    }
}

public struct PutLifecycleConfigurationOutput: Swift.Sendable {
    /// An array of lifecycle management policies. EFS supports a maximum of one policy per file system.
    public var lifecyclePolicies: [EFSClientTypes.LifecyclePolicy]?

    public init(
        lifecyclePolicies: [EFSClientTypes.LifecyclePolicy]? = nil
    )
    {
        self.lifecyclePolicies = lifecyclePolicies
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The ID specifying the EFS resource that you want to create a tag for.
    /// This member is required.
    public var resourceId: Swift.String?
    /// An array of Tag objects to add. Each Tag object is a key-value pair.
    /// This member is required.
    public var tags: [EFSClientTypes.Tag]?

    public init(
        resourceId: Swift.String? = nil,
        tags: [EFSClientTypes.Tag]? = nil
    )
    {
        self.resourceId = resourceId
        self.tags = tags
    }
}

public struct UntagResourceInput: Swift.Sendable {
    /// Specifies the EFS resource that you want to remove tags from.
    /// This member is required.
    public var resourceId: Swift.String?
    /// The keys of the key-value tag pairs that you want to remove from the specified EFS resource.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceId: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceId = resourceId
        self.tagKeys = tagKeys
    }
}

/// Returned if you don’t wait at least 24 hours before either changing the throughput mode, or decreasing the Provisioned Throughput value.
public struct TooManyRequests: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        /// This member is required.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TooManyRequests" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

public struct UpdateFileSystemInput: Swift.Sendable {
    /// The ID of the file system that you want to update.
    /// This member is required.
    public var fileSystemId: Swift.String?
    /// (Optional) The throughput, measured in mebibytes per second (MiBps), that you want to provision for a file system that you're creating. Required if ThroughputMode is set to provisioned. Valid values are 1-3414 MiBps, with the upper limit depending on Region. To increase this limit, contact Amazon Web Services Support. For more information, see [Amazon EFS quotas that you can increase](https://docs.aws.amazon.com/efs/latest/ug/limits.html#soft-limits) in the Amazon EFS User Guide.
    public var provisionedThroughputInMibps: Swift.Double?
    /// (Optional) Updates the file system's throughput mode. If you're not updating your throughput mode, you don't need to provide this value in your request. If you are changing the ThroughputMode to provisioned, you must also set a value for ProvisionedThroughputInMibps.
    public var throughputMode: EFSClientTypes.ThroughputMode?

    public init(
        fileSystemId: Swift.String? = nil,
        provisionedThroughputInMibps: Swift.Double? = nil,
        throughputMode: EFSClientTypes.ThroughputMode? = nil
    )
    {
        self.fileSystemId = fileSystemId
        self.provisionedThroughputInMibps = provisionedThroughputInMibps
        self.throughputMode = throughputMode
    }
}

/// A description of the file system.
public struct UpdateFileSystemOutput: Swift.Sendable {
    /// The unique and consistent identifier of the Availability Zone in which the file system is located, and is valid only for One Zone file systems. For example, use1-az1 is an Availability Zone ID for the us-east-1 Amazon Web Services Region, and it has the same location in every Amazon Web Services account.
    public var availabilityZoneId: Swift.String?
    /// Describes the Amazon Web Services Availability Zone in which the file system is located, and is valid only for One Zone file systems. For more information, see [Using EFS storage classes](https://docs.aws.amazon.com/efs/latest/ug/storage-classes.html) in the Amazon EFS User Guide.
    public var availabilityZoneName: Swift.String?
    /// The time that the file system was created, in seconds (since 1970-01-01T00:00:00Z).
    /// This member is required.
    public var creationTime: Foundation.Date?
    /// The opaque string specified in the request.
    /// This member is required.
    public var creationToken: Swift.String?
    /// A Boolean value that, if true, indicates that the file system is encrypted.
    public var encrypted: Swift.Bool?
    /// The Amazon Resource Name (ARN) for the EFS file system, in the format arn:aws:elasticfilesystem:region:account-id:file-system/file-system-id . Example with sample data: arn:aws:elasticfilesystem:us-west-2:****************:file-system/fs-********
    public var fileSystemArn: Swift.String?
    /// The ID of the file system, assigned by Amazon EFS.
    /// This member is required.
    public var fileSystemId: Swift.String?
    /// Describes the protection on the file system.
    public var fileSystemProtection: EFSClientTypes.FileSystemProtectionDescription?
    /// The ID of an KMS key used to protect the encrypted file system.
    public var kmsKeyId: Swift.String?
    /// The lifecycle phase of the file system.
    /// This member is required.
    public var lifeCycleState: EFSClientTypes.LifeCycleState?
    /// You can add tags to a file system, including a Name tag. For more information, see [CreateFileSystem]. If the file system has a Name tag, Amazon EFS returns the value in this field.
    public var name: Swift.String?
    /// The current number of mount targets that the file system has. For more information, see [CreateMountTarget].
    /// This member is required.
    public var numberOfMountTargets: Swift.Int
    /// The Amazon Web Services account that created the file system.
    /// This member is required.
    public var ownerId: Swift.String?
    /// The performance mode of the file system.
    /// This member is required.
    public var performanceMode: EFSClientTypes.PerformanceMode?
    /// The amount of provisioned throughput, measured in MiBps, for the file system. Valid for file systems using ThroughputMode set to provisioned.
    public var provisionedThroughputInMibps: Swift.Double?
    /// The latest known metered size (in bytes) of data stored in the file system, in its Value field, and the time at which that size was determined in its Timestamp field. The Timestamp value is the integer number of seconds since 1970-01-01T00:00:00Z. The SizeInBytes value doesn't represent the size of a consistent snapshot of the file system, but it is eventually consistent when there are no writes to the file system. That is, SizeInBytes represents actual size only if the file system is not modified for a period longer than a couple of hours. Otherwise, the value is not the exact size that the file system was at any point in time.
    /// This member is required.
    public var sizeInBytes: EFSClientTypes.FileSystemSize?
    /// The tags associated with the file system, presented as an array of Tag objects.
    /// This member is required.
    public var tags: [EFSClientTypes.Tag]?
    /// Displays the file system's throughput mode. For more information, see [Throughput modes](https://docs.aws.amazon.com/efs/latest/ug/performance.html#throughput-modes) in the Amazon EFS User Guide.
    public var throughputMode: EFSClientTypes.ThroughputMode?

    public init(
        availabilityZoneId: Swift.String? = nil,
        availabilityZoneName: Swift.String? = nil,
        creationTime: Foundation.Date? = nil,
        creationToken: Swift.String? = nil,
        encrypted: Swift.Bool? = nil,
        fileSystemArn: Swift.String? = nil,
        fileSystemId: Swift.String? = nil,
        fileSystemProtection: EFSClientTypes.FileSystemProtectionDescription? = nil,
        kmsKeyId: Swift.String? = nil,
        lifeCycleState: EFSClientTypes.LifeCycleState? = nil,
        name: Swift.String? = nil,
        numberOfMountTargets: Swift.Int = 0,
        ownerId: Swift.String? = nil,
        performanceMode: EFSClientTypes.PerformanceMode? = nil,
        provisionedThroughputInMibps: Swift.Double? = nil,
        sizeInBytes: EFSClientTypes.FileSystemSize? = nil,
        tags: [EFSClientTypes.Tag]? = nil,
        throughputMode: EFSClientTypes.ThroughputMode? = nil
    )
    {
        self.availabilityZoneId = availabilityZoneId
        self.availabilityZoneName = availabilityZoneName
        self.creationTime = creationTime
        self.creationToken = creationToken
        self.encrypted = encrypted
        self.fileSystemArn = fileSystemArn
        self.fileSystemId = fileSystemId
        self.fileSystemProtection = fileSystemProtection
        self.kmsKeyId = kmsKeyId
        self.lifeCycleState = lifeCycleState
        self.name = name
        self.numberOfMountTargets = numberOfMountTargets
        self.ownerId = ownerId
        self.performanceMode = performanceMode
        self.provisionedThroughputInMibps = provisionedThroughputInMibps
        self.sizeInBytes = sizeInBytes
        self.tags = tags
        self.throughputMode = throughputMode
    }
}

/// Returned if the file system is already included in a replication configuration.>
public struct ReplicationAlreadyExists: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code is a string that uniquely identifies an error condition. It is meant to be read and understood by programs that detect and handle errors by type.
        public internal(set) var errorCode: Swift.String? = nil
        /// The error message contains a generic description of the error condition in English. It is intended for a human audience. Simple programs display the message directly to the end user if they encounter an error condition they don't know how or don't care to handle. Sophisticated programs with more exhaustive error handling and proper internationalization are more likely to ignore the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ReplicationAlreadyExists" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

public struct UpdateFileSystemProtectionInput: Swift.Sendable {
    /// The ID of the file system to update.
    /// This member is required.
    public var fileSystemId: Swift.String?
    /// The status of the file system's replication overwrite protection.
    ///
    /// * ENABLED – The file system cannot be used as the destination file system in a replication configuration. The file system is writeable. Replication overwrite protection is ENABLED by default.
    ///
    /// * DISABLED – The file system can be used as the destination file system in a replication configuration. The file system is read-only and can only be modified by EFS replication.
    ///
    /// * REPLICATING – The file system is being used as the destination file system in a replication configuration. The file system is read-only and is only modified only by EFS replication.
    ///
    ///
    /// If the replication configuration is deleted, the file system's replication overwrite protection is re-enabled and the file system becomes writeable.
    public var replicationOverwriteProtection: EFSClientTypes.ReplicationOverwriteProtection?

    public init(
        fileSystemId: Swift.String? = nil,
        replicationOverwriteProtection: EFSClientTypes.ReplicationOverwriteProtection? = nil
    )
    {
        self.fileSystemId = fileSystemId
        self.replicationOverwriteProtection = replicationOverwriteProtection
    }
}

/// Describes the protection on a file system.
public struct UpdateFileSystemProtectionOutput: Swift.Sendable {
    /// The status of the file system's replication overwrite protection.
    ///
    /// * ENABLED – The file system cannot be used as the destination file system in a replication configuration. The file system is writeable. Replication overwrite protection is ENABLED by default.
    ///
    /// * DISABLED – The file system can be used as the destination file system in a replication configuration. The file system is read-only and can only be modified by EFS replication.
    ///
    /// * REPLICATING – The file system is being used as the destination file system in a replication configuration. The file system is read-only and is only modified only by EFS replication.
    ///
    ///
    /// If the replication configuration is deleted, the file system's replication overwrite protection is re-enabled, the file system becomes writeable.
    public var replicationOverwriteProtection: EFSClientTypes.ReplicationOverwriteProtection?

    public init(
        replicationOverwriteProtection: EFSClientTypes.ReplicationOverwriteProtection? = nil
    )
    {
        self.replicationOverwriteProtection = replicationOverwriteProtection
    }
}

extension CreateAccessPointInput {

    static func urlPathProvider(_ value: CreateAccessPointInput) -> Swift.String? {
        return "/2015-02-01/access-points"
    }
}

extension CreateFileSystemInput {

    static func urlPathProvider(_ value: CreateFileSystemInput) -> Swift.String? {
        return "/2015-02-01/file-systems"
    }
}

extension CreateMountTargetInput {

    static func urlPathProvider(_ value: CreateMountTargetInput) -> Swift.String? {
        return "/2015-02-01/mount-targets"
    }
}

extension CreateReplicationConfigurationInput {

    static func urlPathProvider(_ value: CreateReplicationConfigurationInput) -> Swift.String? {
        guard let sourceFileSystemId = value.sourceFileSystemId else {
            return nil
        }
        return "/2015-02-01/file-systems/\(sourceFileSystemId.urlPercentEncoding())/replication-configuration"
    }
}

extension CreateTagsInput {

    static func urlPathProvider(_ value: CreateTagsInput) -> Swift.String? {
        guard let fileSystemId = value.fileSystemId else {
            return nil
        }
        return "/2015-02-01/create-tags/\(fileSystemId.urlPercentEncoding())"
    }
}

extension DeleteAccessPointInput {

    static func urlPathProvider(_ value: DeleteAccessPointInput) -> Swift.String? {
        guard let accessPointId = value.accessPointId else {
            return nil
        }
        return "/2015-02-01/access-points/\(accessPointId.urlPercentEncoding())"
    }
}

extension DeleteFileSystemInput {

    static func urlPathProvider(_ value: DeleteFileSystemInput) -> Swift.String? {
        guard let fileSystemId = value.fileSystemId else {
            return nil
        }
        return "/2015-02-01/file-systems/\(fileSystemId.urlPercentEncoding())"
    }
}

extension DeleteFileSystemPolicyInput {

    static func urlPathProvider(_ value: DeleteFileSystemPolicyInput) -> Swift.String? {
        guard let fileSystemId = value.fileSystemId else {
            return nil
        }
        return "/2015-02-01/file-systems/\(fileSystemId.urlPercentEncoding())/policy"
    }
}

extension DeleteMountTargetInput {

    static func urlPathProvider(_ value: DeleteMountTargetInput) -> Swift.String? {
        guard let mountTargetId = value.mountTargetId else {
            return nil
        }
        return "/2015-02-01/mount-targets/\(mountTargetId.urlPercentEncoding())"
    }
}

extension DeleteReplicationConfigurationInput {

    static func urlPathProvider(_ value: DeleteReplicationConfigurationInput) -> Swift.String? {
        guard let sourceFileSystemId = value.sourceFileSystemId else {
            return nil
        }
        return "/2015-02-01/file-systems/\(sourceFileSystemId.urlPercentEncoding())/replication-configuration"
    }
}

extension DeleteReplicationConfigurationInput {

    static func queryItemProvider(_ value: DeleteReplicationConfigurationInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let deletionMode = value.deletionMode {
            let deletionModeQueryItem = Smithy.URIQueryItem(name: "deletionMode".urlPercentEncoding(), value: Swift.String(deletionMode.rawValue).urlPercentEncoding())
            items.append(deletionModeQueryItem)
        }
        return items
    }
}

extension DeleteTagsInput {

    static func urlPathProvider(_ value: DeleteTagsInput) -> Swift.String? {
        guard let fileSystemId = value.fileSystemId else {
            return nil
        }
        return "/2015-02-01/delete-tags/\(fileSystemId.urlPercentEncoding())"
    }
}

extension DescribeAccessPointsInput {

    static func urlPathProvider(_ value: DescribeAccessPointsInput) -> Swift.String? {
        return "/2015-02-01/access-points"
    }
}

extension DescribeAccessPointsInput {

    static func queryItemProvider(_ value: DescribeAccessPointsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "NextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "MaxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let accessPointId = value.accessPointId {
            let accessPointIdQueryItem = Smithy.URIQueryItem(name: "AccessPointId".urlPercentEncoding(), value: Swift.String(accessPointId).urlPercentEncoding())
            items.append(accessPointIdQueryItem)
        }
        if let fileSystemId = value.fileSystemId {
            let fileSystemIdQueryItem = Smithy.URIQueryItem(name: "FileSystemId".urlPercentEncoding(), value: Swift.String(fileSystemId).urlPercentEncoding())
            items.append(fileSystemIdQueryItem)
        }
        return items
    }
}

extension DescribeAccountPreferencesInput {

    static func urlPathProvider(_ value: DescribeAccountPreferencesInput) -> Swift.String? {
        return "/2015-02-01/account-preferences"
    }
}

extension DescribeBackupPolicyInput {

    static func urlPathProvider(_ value: DescribeBackupPolicyInput) -> Swift.String? {
        guard let fileSystemId = value.fileSystemId else {
            return nil
        }
        return "/2015-02-01/file-systems/\(fileSystemId.urlPercentEncoding())/backup-policy"
    }
}

extension DescribeFileSystemPolicyInput {

    static func urlPathProvider(_ value: DescribeFileSystemPolicyInput) -> Swift.String? {
        guard let fileSystemId = value.fileSystemId else {
            return nil
        }
        return "/2015-02-01/file-systems/\(fileSystemId.urlPercentEncoding())/policy"
    }
}

extension DescribeFileSystemsInput {

    static func urlPathProvider(_ value: DescribeFileSystemsInput) -> Swift.String? {
        return "/2015-02-01/file-systems"
    }
}

extension DescribeFileSystemsInput {

    static func queryItemProvider(_ value: DescribeFileSystemsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let creationToken = value.creationToken {
            let creationTokenQueryItem = Smithy.URIQueryItem(name: "CreationToken".urlPercentEncoding(), value: Swift.String(creationToken).urlPercentEncoding())
            items.append(creationTokenQueryItem)
        }
        if let fileSystemId = value.fileSystemId {
            let fileSystemIdQueryItem = Smithy.URIQueryItem(name: "FileSystemId".urlPercentEncoding(), value: Swift.String(fileSystemId).urlPercentEncoding())
            items.append(fileSystemIdQueryItem)
        }
        if let maxItems = value.maxItems {
            let maxItemsQueryItem = Smithy.URIQueryItem(name: "MaxItems".urlPercentEncoding(), value: Swift.String(maxItems).urlPercentEncoding())
            items.append(maxItemsQueryItem)
        }
        if let marker = value.marker {
            let markerQueryItem = Smithy.URIQueryItem(name: "Marker".urlPercentEncoding(), value: Swift.String(marker).urlPercentEncoding())
            items.append(markerQueryItem)
        }
        return items
    }
}

extension DescribeLifecycleConfigurationInput {

    static func urlPathProvider(_ value: DescribeLifecycleConfigurationInput) -> Swift.String? {
        guard let fileSystemId = value.fileSystemId else {
            return nil
        }
        return "/2015-02-01/file-systems/\(fileSystemId.urlPercentEncoding())/lifecycle-configuration"
    }
}

extension DescribeMountTargetsInput {

    static func urlPathProvider(_ value: DescribeMountTargetsInput) -> Swift.String? {
        return "/2015-02-01/mount-targets"
    }
}

extension DescribeMountTargetsInput {

    static func queryItemProvider(_ value: DescribeMountTargetsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let accessPointId = value.accessPointId {
            let accessPointIdQueryItem = Smithy.URIQueryItem(name: "AccessPointId".urlPercentEncoding(), value: Swift.String(accessPointId).urlPercentEncoding())
            items.append(accessPointIdQueryItem)
        }
        if let fileSystemId = value.fileSystemId {
            let fileSystemIdQueryItem = Smithy.URIQueryItem(name: "FileSystemId".urlPercentEncoding(), value: Swift.String(fileSystemId).urlPercentEncoding())
            items.append(fileSystemIdQueryItem)
        }
        if let maxItems = value.maxItems {
            let maxItemsQueryItem = Smithy.URIQueryItem(name: "MaxItems".urlPercentEncoding(), value: Swift.String(maxItems).urlPercentEncoding())
            items.append(maxItemsQueryItem)
        }
        if let marker = value.marker {
            let markerQueryItem = Smithy.URIQueryItem(name: "Marker".urlPercentEncoding(), value: Swift.String(marker).urlPercentEncoding())
            items.append(markerQueryItem)
        }
        if let mountTargetId = value.mountTargetId {
            let mountTargetIdQueryItem = Smithy.URIQueryItem(name: "MountTargetId".urlPercentEncoding(), value: Swift.String(mountTargetId).urlPercentEncoding())
            items.append(mountTargetIdQueryItem)
        }
        return items
    }
}

extension DescribeMountTargetSecurityGroupsInput {

    static func urlPathProvider(_ value: DescribeMountTargetSecurityGroupsInput) -> Swift.String? {
        guard let mountTargetId = value.mountTargetId else {
            return nil
        }
        return "/2015-02-01/mount-targets/\(mountTargetId.urlPercentEncoding())/security-groups"
    }
}

extension DescribeReplicationConfigurationsInput {

    static func urlPathProvider(_ value: DescribeReplicationConfigurationsInput) -> Swift.String? {
        return "/2015-02-01/file-systems/replication-configurations"
    }
}

extension DescribeReplicationConfigurationsInput {

    static func queryItemProvider(_ value: DescribeReplicationConfigurationsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "NextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "MaxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let fileSystemId = value.fileSystemId {
            let fileSystemIdQueryItem = Smithy.URIQueryItem(name: "FileSystemId".urlPercentEncoding(), value: Swift.String(fileSystemId).urlPercentEncoding())
            items.append(fileSystemIdQueryItem)
        }
        return items
    }
}

extension DescribeTagsInput {

    static func urlPathProvider(_ value: DescribeTagsInput) -> Swift.String? {
        guard let fileSystemId = value.fileSystemId else {
            return nil
        }
        return "/2015-02-01/tags/\(fileSystemId.urlPercentEncoding())"
    }
}

extension DescribeTagsInput {

    static func queryItemProvider(_ value: DescribeTagsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let maxItems = value.maxItems {
            let maxItemsQueryItem = Smithy.URIQueryItem(name: "MaxItems".urlPercentEncoding(), value: Swift.String(maxItems).urlPercentEncoding())
            items.append(maxItemsQueryItem)
        }
        if let marker = value.marker {
            let markerQueryItem = Smithy.URIQueryItem(name: "Marker".urlPercentEncoding(), value: Swift.String(marker).urlPercentEncoding())
            items.append(markerQueryItem)
        }
        return items
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let resourceId = value.resourceId else {
            return nil
        }
        return "/2015-02-01/resource-tags/\(resourceId.urlPercentEncoding())"
    }
}

extension ListTagsForResourceInput {

    static func queryItemProvider(_ value: ListTagsForResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "NextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "MaxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ModifyMountTargetSecurityGroupsInput {

    static func urlPathProvider(_ value: ModifyMountTargetSecurityGroupsInput) -> Swift.String? {
        guard let mountTargetId = value.mountTargetId else {
            return nil
        }
        return "/2015-02-01/mount-targets/\(mountTargetId.urlPercentEncoding())/security-groups"
    }
}

extension PutAccountPreferencesInput {

    static func urlPathProvider(_ value: PutAccountPreferencesInput) -> Swift.String? {
        return "/2015-02-01/account-preferences"
    }
}

extension PutBackupPolicyInput {

    static func urlPathProvider(_ value: PutBackupPolicyInput) -> Swift.String? {
        guard let fileSystemId = value.fileSystemId else {
            return nil
        }
        return "/2015-02-01/file-systems/\(fileSystemId.urlPercentEncoding())/backup-policy"
    }
}

extension PutFileSystemPolicyInput {

    static func urlPathProvider(_ value: PutFileSystemPolicyInput) -> Swift.String? {
        guard let fileSystemId = value.fileSystemId else {
            return nil
        }
        return "/2015-02-01/file-systems/\(fileSystemId.urlPercentEncoding())/policy"
    }
}

extension PutLifecycleConfigurationInput {

    static func urlPathProvider(_ value: PutLifecycleConfigurationInput) -> Swift.String? {
        guard let fileSystemId = value.fileSystemId else {
            return nil
        }
        return "/2015-02-01/file-systems/\(fileSystemId.urlPercentEncoding())/lifecycle-configuration"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let resourceId = value.resourceId else {
            return nil
        }
        return "/2015-02-01/resource-tags/\(resourceId.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let resourceId = value.resourceId else {
            return nil
        }
        return "/2015-02-01/resource-tags/\(resourceId.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension UpdateFileSystemInput {

    static func urlPathProvider(_ value: UpdateFileSystemInput) -> Swift.String? {
        guard let fileSystemId = value.fileSystemId else {
            return nil
        }
        return "/2015-02-01/file-systems/\(fileSystemId.urlPercentEncoding())"
    }
}

extension UpdateFileSystemProtectionInput {

    static func urlPathProvider(_ value: UpdateFileSystemProtectionInput) -> Swift.String? {
        guard let fileSystemId = value.fileSystemId else {
            return nil
        }
        return "/2015-02-01/file-systems/\(fileSystemId.urlPercentEncoding())/protection"
    }
}

extension CreateAccessPointInput {

    static func write(value: CreateAccessPointInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["FileSystemId"].write(value.fileSystemId)
        try writer["PosixUser"].write(value.posixUser, with: EFSClientTypes.PosixUser.write(value:to:))
        try writer["RootDirectory"].write(value.rootDirectory, with: EFSClientTypes.RootDirectory.write(value:to:))
        try writer["Tags"].writeList(value.tags, memberWritingClosure: EFSClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateFileSystemInput {

    static func write(value: CreateFileSystemInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AvailabilityZoneName"].write(value.availabilityZoneName)
        try writer["Backup"].write(value.backup)
        try writer["CreationToken"].write(value.creationToken)
        try writer["Encrypted"].write(value.encrypted)
        try writer["KmsKeyId"].write(value.kmsKeyId)
        try writer["PerformanceMode"].write(value.performanceMode)
        try writer["ProvisionedThroughputInMibps"].write(value.provisionedThroughputInMibps)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: EFSClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ThroughputMode"].write(value.throughputMode)
    }
}

extension CreateMountTargetInput {

    static func write(value: CreateMountTargetInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["FileSystemId"].write(value.fileSystemId)
        try writer["IpAddress"].write(value.ipAddress)
        try writer["SecurityGroups"].writeList(value.securityGroups, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["SubnetId"].write(value.subnetId)
    }
}

extension CreateReplicationConfigurationInput {

    static func write(value: CreateReplicationConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Destinations"].writeList(value.destinations, memberWritingClosure: EFSClientTypes.DestinationToCreate.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateTagsInput {

    static func write(value: CreateTagsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Tags"].writeList(value.tags, memberWritingClosure: EFSClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DeleteTagsInput {

    static func write(value: DeleteTagsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["TagKeys"].writeList(value.tagKeys, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DescribeAccountPreferencesInput {

    static func write(value: DescribeAccountPreferencesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ModifyMountTargetSecurityGroupsInput {

    static func write(value: ModifyMountTargetSecurityGroupsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["SecurityGroups"].writeList(value.securityGroups, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension PutAccountPreferencesInput {

    static func write(value: PutAccountPreferencesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceIdType"].write(value.resourceIdType)
    }
}

extension PutBackupPolicyInput {

    static func write(value: PutBackupPolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["BackupPolicy"].write(value.backupPolicy, with: EFSClientTypes.BackupPolicy.write(value:to:))
    }
}

extension PutFileSystemPolicyInput {

    static func write(value: PutFileSystemPolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["BypassPolicyLockoutSafetyCheck"].write(value.bypassPolicyLockoutSafetyCheck)
        try writer["Policy"].write(value.policy)
    }
}

extension PutLifecycleConfigurationInput {

    static func write(value: PutLifecycleConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LifecyclePolicies"].writeList(value.lifecyclePolicies, memberWritingClosure: EFSClientTypes.LifecyclePolicy.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Tags"].writeList(value.tags, memberWritingClosure: EFSClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdateFileSystemInput {

    static func write(value: UpdateFileSystemInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ProvisionedThroughputInMibps"].write(value.provisionedThroughputInMibps)
        try writer["ThroughputMode"].write(value.throughputMode)
    }
}

extension UpdateFileSystemProtectionInput {

    static func write(value: UpdateFileSystemProtectionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ReplicationOverwriteProtection"].write(value.replicationOverwriteProtection)
    }
}

extension CreateAccessPointOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateAccessPointOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateAccessPointOutput()
        value.accessPointArn = try reader["AccessPointArn"].readIfPresent()
        value.accessPointId = try reader["AccessPointId"].readIfPresent()
        value.clientToken = try reader["ClientToken"].readIfPresent()
        value.fileSystemId = try reader["FileSystemId"].readIfPresent()
        value.lifeCycleState = try reader["LifeCycleState"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.ownerId = try reader["OwnerId"].readIfPresent()
        value.posixUser = try reader["PosixUser"].readIfPresent(with: EFSClientTypes.PosixUser.read(from:))
        value.rootDirectory = try reader["RootDirectory"].readIfPresent(with: EFSClientTypes.RootDirectory.read(from:))
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: EFSClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension CreateFileSystemOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateFileSystemOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateFileSystemOutput()
        value.availabilityZoneId = try reader["AvailabilityZoneId"].readIfPresent()
        value.availabilityZoneName = try reader["AvailabilityZoneName"].readIfPresent()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.creationToken = try reader["CreationToken"].readIfPresent() ?? ""
        value.encrypted = try reader["Encrypted"].readIfPresent()
        value.fileSystemArn = try reader["FileSystemArn"].readIfPresent()
        value.fileSystemId = try reader["FileSystemId"].readIfPresent() ?? ""
        value.fileSystemProtection = try reader["FileSystemProtection"].readIfPresent(with: EFSClientTypes.FileSystemProtectionDescription.read(from:))
        value.kmsKeyId = try reader["KmsKeyId"].readIfPresent()
        value.lifeCycleState = try reader["LifeCycleState"].readIfPresent() ?? .sdkUnknown("")
        value.name = try reader["Name"].readIfPresent()
        value.numberOfMountTargets = try reader["NumberOfMountTargets"].readIfPresent() ?? 0
        value.ownerId = try reader["OwnerId"].readIfPresent() ?? ""
        value.performanceMode = try reader["PerformanceMode"].readIfPresent() ?? .sdkUnknown("")
        value.provisionedThroughputInMibps = try reader["ProvisionedThroughputInMibps"].readIfPresent()
        value.sizeInBytes = try reader["SizeInBytes"].readIfPresent(with: EFSClientTypes.FileSystemSize.read(from:))
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: EFSClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.throughputMode = try reader["ThroughputMode"].readIfPresent()
        return value
    }
}

extension CreateMountTargetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateMountTargetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateMountTargetOutput()
        value.availabilityZoneId = try reader["AvailabilityZoneId"].readIfPresent()
        value.availabilityZoneName = try reader["AvailabilityZoneName"].readIfPresent()
        value.fileSystemId = try reader["FileSystemId"].readIfPresent() ?? ""
        value.ipAddress = try reader["IpAddress"].readIfPresent()
        value.lifeCycleState = try reader["LifeCycleState"].readIfPresent() ?? .sdkUnknown("")
        value.mountTargetId = try reader["MountTargetId"].readIfPresent() ?? ""
        value.networkInterfaceId = try reader["NetworkInterfaceId"].readIfPresent()
        value.ownerId = try reader["OwnerId"].readIfPresent()
        value.subnetId = try reader["SubnetId"].readIfPresent() ?? ""
        value.vpcId = try reader["VpcId"].readIfPresent()
        return value
    }
}

extension CreateReplicationConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateReplicationConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateReplicationConfigurationOutput()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.destinations = try reader["Destinations"].readListIfPresent(memberReadingClosure: EFSClientTypes.Destination.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.originalSourceFileSystemArn = try reader["OriginalSourceFileSystemArn"].readIfPresent() ?? ""
        value.sourceFileSystemArn = try reader["SourceFileSystemArn"].readIfPresent() ?? ""
        value.sourceFileSystemId = try reader["SourceFileSystemId"].readIfPresent() ?? ""
        value.sourceFileSystemOwnerId = try reader["SourceFileSystemOwnerId"].readIfPresent()
        value.sourceFileSystemRegion = try reader["SourceFileSystemRegion"].readIfPresent() ?? ""
        return value
    }
}

extension CreateTagsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateTagsOutput {
        return CreateTagsOutput()
    }
}

extension DeleteAccessPointOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteAccessPointOutput {
        return DeleteAccessPointOutput()
    }
}

extension DeleteFileSystemOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteFileSystemOutput {
        return DeleteFileSystemOutput()
    }
}

extension DeleteFileSystemPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteFileSystemPolicyOutput {
        return DeleteFileSystemPolicyOutput()
    }
}

extension DeleteMountTargetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteMountTargetOutput {
        return DeleteMountTargetOutput()
    }
}

extension DeleteReplicationConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteReplicationConfigurationOutput {
        return DeleteReplicationConfigurationOutput()
    }
}

extension DeleteTagsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteTagsOutput {
        return DeleteTagsOutput()
    }
}

extension DescribeAccessPointsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeAccessPointsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeAccessPointsOutput()
        value.accessPoints = try reader["AccessPoints"].readListIfPresent(memberReadingClosure: EFSClientTypes.AccessPointDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension DescribeAccountPreferencesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeAccountPreferencesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeAccountPreferencesOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.resourceIdPreference = try reader["ResourceIdPreference"].readIfPresent(with: EFSClientTypes.ResourceIdPreference.read(from:))
        return value
    }
}

extension DescribeBackupPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeBackupPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeBackupPolicyOutput()
        value.backupPolicy = try reader["BackupPolicy"].readIfPresent(with: EFSClientTypes.BackupPolicy.read(from:))
        return value
    }
}

extension DescribeFileSystemPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeFileSystemPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeFileSystemPolicyOutput()
        value.fileSystemId = try reader["FileSystemId"].readIfPresent()
        value.policy = try reader["Policy"].readIfPresent()
        return value
    }
}

extension DescribeFileSystemsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeFileSystemsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeFileSystemsOutput()
        value.fileSystems = try reader["FileSystems"].readListIfPresent(memberReadingClosure: EFSClientTypes.FileSystemDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.marker = try reader["Marker"].readIfPresent()
        value.nextMarker = try reader["NextMarker"].readIfPresent()
        return value
    }
}

extension DescribeLifecycleConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeLifecycleConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeLifecycleConfigurationOutput()
        value.lifecyclePolicies = try reader["LifecyclePolicies"].readListIfPresent(memberReadingClosure: EFSClientTypes.LifecyclePolicy.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeMountTargetsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeMountTargetsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeMountTargetsOutput()
        value.marker = try reader["Marker"].readIfPresent()
        value.mountTargets = try reader["MountTargets"].readListIfPresent(memberReadingClosure: EFSClientTypes.MountTargetDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextMarker = try reader["NextMarker"].readIfPresent()
        return value
    }
}

extension DescribeMountTargetSecurityGroupsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeMountTargetSecurityGroupsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeMountTargetSecurityGroupsOutput()
        value.securityGroups = try reader["SecurityGroups"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DescribeReplicationConfigurationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeReplicationConfigurationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeReplicationConfigurationsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.replications = try reader["Replications"].readListIfPresent(memberReadingClosure: EFSClientTypes.ReplicationConfigurationDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeTagsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeTagsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeTagsOutput()
        value.marker = try reader["Marker"].readIfPresent()
        value.nextMarker = try reader["NextMarker"].readIfPresent()
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: EFSClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: EFSClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ModifyMountTargetSecurityGroupsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ModifyMountTargetSecurityGroupsOutput {
        return ModifyMountTargetSecurityGroupsOutput()
    }
}

extension PutAccountPreferencesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutAccountPreferencesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutAccountPreferencesOutput()
        value.resourceIdPreference = try reader["ResourceIdPreference"].readIfPresent(with: EFSClientTypes.ResourceIdPreference.read(from:))
        return value
    }
}

extension PutBackupPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutBackupPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutBackupPolicyOutput()
        value.backupPolicy = try reader["BackupPolicy"].readIfPresent(with: EFSClientTypes.BackupPolicy.read(from:))
        return value
    }
}

extension PutFileSystemPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutFileSystemPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutFileSystemPolicyOutput()
        value.fileSystemId = try reader["FileSystemId"].readIfPresent()
        value.policy = try reader["Policy"].readIfPresent()
        return value
    }
}

extension PutLifecycleConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutLifecycleConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutLifecycleConfigurationOutput()
        value.lifecyclePolicies = try reader["LifecyclePolicies"].readListIfPresent(memberReadingClosure: EFSClientTypes.LifecyclePolicy.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateFileSystemOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateFileSystemOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateFileSystemOutput()
        value.availabilityZoneId = try reader["AvailabilityZoneId"].readIfPresent()
        value.availabilityZoneName = try reader["AvailabilityZoneName"].readIfPresent()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.creationToken = try reader["CreationToken"].readIfPresent() ?? ""
        value.encrypted = try reader["Encrypted"].readIfPresent()
        value.fileSystemArn = try reader["FileSystemArn"].readIfPresent()
        value.fileSystemId = try reader["FileSystemId"].readIfPresent() ?? ""
        value.fileSystemProtection = try reader["FileSystemProtection"].readIfPresent(with: EFSClientTypes.FileSystemProtectionDescription.read(from:))
        value.kmsKeyId = try reader["KmsKeyId"].readIfPresent()
        value.lifeCycleState = try reader["LifeCycleState"].readIfPresent() ?? .sdkUnknown("")
        value.name = try reader["Name"].readIfPresent()
        value.numberOfMountTargets = try reader["NumberOfMountTargets"].readIfPresent() ?? 0
        value.ownerId = try reader["OwnerId"].readIfPresent() ?? ""
        value.performanceMode = try reader["PerformanceMode"].readIfPresent() ?? .sdkUnknown("")
        value.provisionedThroughputInMibps = try reader["ProvisionedThroughputInMibps"].readIfPresent()
        value.sizeInBytes = try reader["SizeInBytes"].readIfPresent(with: EFSClientTypes.FileSystemSize.read(from:))
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: EFSClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.throughputMode = try reader["ThroughputMode"].readIfPresent()
        return value
    }
}

extension UpdateFileSystemProtectionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateFileSystemProtectionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateFileSystemProtectionOutput()
        value.replicationOverwriteProtection = try reader["ReplicationOverwriteProtection"].readIfPresent()
        return value
    }
}

enum CreateAccessPointOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessPointAlreadyExists": return try AccessPointAlreadyExists.makeError(baseError: baseError)
            case "AccessPointLimitExceeded": return try AccessPointLimitExceeded.makeError(baseError: baseError)
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "IncorrectFileSystemLifeCycleState": return try IncorrectFileSystemLifeCycleState.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateFileSystemOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemAlreadyExists": return try FileSystemAlreadyExists.makeError(baseError: baseError)
            case "FileSystemLimitExceeded": return try FileSystemLimitExceeded.makeError(baseError: baseError)
            case "InsufficientThroughputCapacity": return try InsufficientThroughputCapacity.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "ThroughputLimitExceeded": return try ThroughputLimitExceeded.makeError(baseError: baseError)
            case "UnsupportedAvailabilityZone": return try UnsupportedAvailabilityZone.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateMountTargetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AvailabilityZonesMismatch": return try AvailabilityZonesMismatch.makeError(baseError: baseError)
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "IncorrectFileSystemLifeCycleState": return try IncorrectFileSystemLifeCycleState.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "IpAddressInUse": return try IpAddressInUse.makeError(baseError: baseError)
            case "MountTargetConflict": return try MountTargetConflict.makeError(baseError: baseError)
            case "NetworkInterfaceLimitExceeded": return try NetworkInterfaceLimitExceeded.makeError(baseError: baseError)
            case "NoFreeAddressesInSubnet": return try NoFreeAddressesInSubnet.makeError(baseError: baseError)
            case "SecurityGroupLimitExceeded": return try SecurityGroupLimitExceeded.makeError(baseError: baseError)
            case "SecurityGroupNotFound": return try SecurityGroupNotFound.makeError(baseError: baseError)
            case "SubnetNotFound": return try SubnetNotFound.makeError(baseError: baseError)
            case "UnsupportedAvailabilityZone": return try UnsupportedAvailabilityZone.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateReplicationConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "FileSystemLimitExceeded": return try FileSystemLimitExceeded.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "IncorrectFileSystemLifeCycleState": return try IncorrectFileSystemLifeCycleState.makeError(baseError: baseError)
            case "InsufficientThroughputCapacity": return try InsufficientThroughputCapacity.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "ReplicationNotFound": return try ReplicationNotFound.makeError(baseError: baseError)
            case "ThroughputLimitExceeded": return try ThroughputLimitExceeded.makeError(baseError: baseError)
            case "UnsupportedAvailabilityZone": return try UnsupportedAvailabilityZone.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateTagsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteAccessPointOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessPointNotFound": return try AccessPointNotFound.makeError(baseError: baseError)
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteFileSystemOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemInUse": return try FileSystemInUse.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteFileSystemPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "IncorrectFileSystemLifeCycleState": return try IncorrectFileSystemLifeCycleState.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteMountTargetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "DependencyTimeout": return try DependencyTimeout.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "MountTargetNotFound": return try MountTargetNotFound.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteReplicationConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "ReplicationNotFound": return try ReplicationNotFound.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteTagsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeAccessPointsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessPointNotFound": return try AccessPointNotFound.makeError(baseError: baseError)
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeAccountPreferencesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeBackupPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "PolicyNotFound": return try PolicyNotFound.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeFileSystemPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "PolicyNotFound": return try PolicyNotFound.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeFileSystemsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeLifecycleConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeMountTargetsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessPointNotFound": return try AccessPointNotFound.makeError(baseError: baseError)
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "MountTargetNotFound": return try MountTargetNotFound.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeMountTargetSecurityGroupsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "IncorrectMountTargetState": return try IncorrectMountTargetState.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "MountTargetNotFound": return try MountTargetNotFound.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeReplicationConfigurationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "ReplicationNotFound": return try ReplicationNotFound.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeTagsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessPointNotFound": return try AccessPointNotFound.makeError(baseError: baseError)
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ModifyMountTargetSecurityGroupsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "IncorrectMountTargetState": return try IncorrectMountTargetState.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "MountTargetNotFound": return try MountTargetNotFound.makeError(baseError: baseError)
            case "SecurityGroupLimitExceeded": return try SecurityGroupLimitExceeded.makeError(baseError: baseError)
            case "SecurityGroupNotFound": return try SecurityGroupNotFound.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutAccountPreferencesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutBackupPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "IncorrectFileSystemLifeCycleState": return try IncorrectFileSystemLifeCycleState.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutFileSystemPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "IncorrectFileSystemLifeCycleState": return try IncorrectFileSystemLifeCycleState.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "InvalidPolicyException": return try InvalidPolicyException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutLifecycleConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "IncorrectFileSystemLifeCycleState": return try IncorrectFileSystemLifeCycleState.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessPointNotFound": return try AccessPointNotFound.makeError(baseError: baseError)
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessPointNotFound": return try AccessPointNotFound.makeError(baseError: baseError)
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateFileSystemOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "IncorrectFileSystemLifeCycleState": return try IncorrectFileSystemLifeCycleState.makeError(baseError: baseError)
            case "InsufficientThroughputCapacity": return try InsufficientThroughputCapacity.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "ThroughputLimitExceeded": return try ThroughputLimitExceeded.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequests.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateFileSystemProtectionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequest": return try BadRequest.makeError(baseError: baseError)
            case "FileSystemNotFound": return try FileSystemNotFound.makeError(baseError: baseError)
            case "IncorrectFileSystemLifeCycleState": return try IncorrectFileSystemLifeCycleState.makeError(baseError: baseError)
            case "InsufficientThroughputCapacity": return try InsufficientThroughputCapacity.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "ReplicationAlreadyExists": return try ReplicationAlreadyExists.makeError(baseError: baseError)
            case "ThroughputLimitExceeded": return try ThroughputLimitExceeded.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequests.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension FileSystemNotFound {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> FileSystemNotFound {
        let reader = baseError.errorBodyReader
        var value = FileSystemNotFound()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension BadRequest {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> BadRequest {
        let reader = baseError.errorBodyReader
        var value = BadRequest()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessPointAlreadyExists {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessPointAlreadyExists {
        let reader = baseError.errorBodyReader
        var value = AccessPointAlreadyExists()
        value.properties.accessPointId = try reader["AccessPointId"].readIfPresent() ?? ""
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension IncorrectFileSystemLifeCycleState {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> IncorrectFileSystemLifeCycleState {
        let reader = baseError.errorBodyReader
        var value = IncorrectFileSystemLifeCycleState()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerError {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerError {
        let reader = baseError.errorBodyReader
        var value = InternalServerError()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        var value = ThrottlingException()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessPointLimitExceeded {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessPointLimitExceeded {
        let reader = baseError.errorBodyReader
        var value = AccessPointLimitExceeded()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InsufficientThroughputCapacity {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InsufficientThroughputCapacity {
        let reader = baseError.errorBodyReader
        var value = InsufficientThroughputCapacity()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension FileSystemAlreadyExists {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> FileSystemAlreadyExists {
        let reader = baseError.errorBodyReader
        var value = FileSystemAlreadyExists()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.fileSystemId = try reader["FileSystemId"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension FileSystemLimitExceeded {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> FileSystemLimitExceeded {
        let reader = baseError.errorBodyReader
        var value = FileSystemLimitExceeded()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThroughputLimitExceeded {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThroughputLimitExceeded {
        let reader = baseError.errorBodyReader
        var value = ThroughputLimitExceeded()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UnsupportedAvailabilityZone {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> UnsupportedAvailabilityZone {
        let reader = baseError.errorBodyReader
        var value = UnsupportedAvailabilityZone()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AvailabilityZonesMismatch {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AvailabilityZonesMismatch {
        let reader = baseError.errorBodyReader
        var value = AvailabilityZonesMismatch()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension NoFreeAddressesInSubnet {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> NoFreeAddressesInSubnet {
        let reader = baseError.errorBodyReader
        var value = NoFreeAddressesInSubnet()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension IpAddressInUse {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> IpAddressInUse {
        let reader = baseError.errorBodyReader
        var value = IpAddressInUse()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SecurityGroupLimitExceeded {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> SecurityGroupLimitExceeded {
        let reader = baseError.errorBodyReader
        var value = SecurityGroupLimitExceeded()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension MountTargetConflict {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> MountTargetConflict {
        let reader = baseError.errorBodyReader
        var value = MountTargetConflict()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension NetworkInterfaceLimitExceeded {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> NetworkInterfaceLimitExceeded {
        let reader = baseError.errorBodyReader
        var value = NetworkInterfaceLimitExceeded()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SecurityGroupNotFound {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> SecurityGroupNotFound {
        let reader = baseError.errorBodyReader
        var value = SecurityGroupNotFound()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SubnetNotFound {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> SubnetNotFound {
        let reader = baseError.errorBodyReader
        var value = SubnetNotFound()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ReplicationNotFound {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ReplicationNotFound {
        let reader = baseError.errorBodyReader
        var value = ReplicationNotFound()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessPointNotFound {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessPointNotFound {
        let reader = baseError.errorBodyReader
        var value = AccessPointNotFound()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension FileSystemInUse {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> FileSystemInUse {
        let reader = baseError.errorBodyReader
        var value = FileSystemInUse()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension MountTargetNotFound {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> MountTargetNotFound {
        let reader = baseError.errorBodyReader
        var value = MountTargetNotFound()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DependencyTimeout {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> DependencyTimeout {
        let reader = baseError.errorBodyReader
        var value = DependencyTimeout()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension PolicyNotFound {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> PolicyNotFound {
        let reader = baseError.errorBodyReader
        var value = PolicyNotFound()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension IncorrectMountTargetState {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> IncorrectMountTargetState {
        let reader = baseError.errorBodyReader
        var value = IncorrectMountTargetState()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidPolicyException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InvalidPolicyException {
        let reader = baseError.errorBodyReader
        var value = InvalidPolicyException()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TooManyRequests {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> TooManyRequests {
        let reader = baseError.errorBodyReader
        var value = TooManyRequests()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent() ?? ""
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ReplicationAlreadyExists {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ReplicationAlreadyExists {
        let reader = baseError.errorBodyReader
        var value = ReplicationAlreadyExists()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension EFSClientTypes.Tag {

    static func write(value: EFSClientTypes.Tag?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Key"].write(value.key)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EFSClientTypes.Tag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EFSClientTypes.Tag()
        value.key = try reader["Key"].readIfPresent() ?? ""
        value.value = try reader["Value"].readIfPresent() ?? ""
        return value
    }
}

extension EFSClientTypes.PosixUser {

    static func write(value: EFSClientTypes.PosixUser?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Gid"].write(value.gid)
        try writer["SecondaryGids"].writeList(value.secondaryGids, memberWritingClosure: SmithyReadWrite.WritingClosures.writeInt(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Uid"].write(value.uid)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EFSClientTypes.PosixUser {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EFSClientTypes.PosixUser()
        value.uid = try reader["Uid"].readIfPresent() ?? 0
        value.gid = try reader["Gid"].readIfPresent() ?? 0
        value.secondaryGids = try reader["SecondaryGids"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readInt(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension EFSClientTypes.RootDirectory {

    static func write(value: EFSClientTypes.RootDirectory?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CreationInfo"].write(value.creationInfo, with: EFSClientTypes.CreationInfo.write(value:to:))
        try writer["Path"].write(value.path)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EFSClientTypes.RootDirectory {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EFSClientTypes.RootDirectory()
        value.path = try reader["Path"].readIfPresent()
        value.creationInfo = try reader["CreationInfo"].readIfPresent(with: EFSClientTypes.CreationInfo.read(from:))
        return value
    }
}

extension EFSClientTypes.CreationInfo {

    static func write(value: EFSClientTypes.CreationInfo?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["OwnerGid"].write(value.ownerGid)
        try writer["OwnerUid"].write(value.ownerUid)
        try writer["Permissions"].write(value.permissions)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EFSClientTypes.CreationInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EFSClientTypes.CreationInfo()
        value.ownerUid = try reader["OwnerUid"].readIfPresent() ?? 0
        value.ownerGid = try reader["OwnerGid"].readIfPresent() ?? 0
        value.permissions = try reader["Permissions"].readIfPresent() ?? ""
        return value
    }
}

extension EFSClientTypes.FileSystemSize {

    static func read(from reader: SmithyJSON.Reader) throws -> EFSClientTypes.FileSystemSize {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EFSClientTypes.FileSystemSize()
        value.value = try reader["Value"].readIfPresent() ?? 0
        value.timestamp = try reader["Timestamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.valueInIA = try reader["ValueInIA"].readIfPresent()
        value.valueInStandard = try reader["ValueInStandard"].readIfPresent()
        value.valueInArchive = try reader["ValueInArchive"].readIfPresent()
        return value
    }
}

extension EFSClientTypes.FileSystemProtectionDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> EFSClientTypes.FileSystemProtectionDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EFSClientTypes.FileSystemProtectionDescription()
        value.replicationOverwriteProtection = try reader["ReplicationOverwriteProtection"].readIfPresent()
        return value
    }
}

extension EFSClientTypes.Destination {

    static func read(from reader: SmithyJSON.Reader) throws -> EFSClientTypes.Destination {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EFSClientTypes.Destination()
        value.status = try reader["Status"].readIfPresent() ?? .sdkUnknown("")
        value.fileSystemId = try reader["FileSystemId"].readIfPresent() ?? ""
        value.region = try reader["Region"].readIfPresent() ?? ""
        value.lastReplicatedTimestamp = try reader["LastReplicatedTimestamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.ownerId = try reader["OwnerId"].readIfPresent()
        value.statusMessage = try reader["StatusMessage"].readIfPresent()
        value.roleArn = try reader["RoleArn"].readIfPresent()
        return value
    }
}

extension EFSClientTypes.AccessPointDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> EFSClientTypes.AccessPointDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EFSClientTypes.AccessPointDescription()
        value.clientToken = try reader["ClientToken"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: EFSClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.accessPointId = try reader["AccessPointId"].readIfPresent()
        value.accessPointArn = try reader["AccessPointArn"].readIfPresent()
        value.fileSystemId = try reader["FileSystemId"].readIfPresent()
        value.posixUser = try reader["PosixUser"].readIfPresent(with: EFSClientTypes.PosixUser.read(from:))
        value.rootDirectory = try reader["RootDirectory"].readIfPresent(with: EFSClientTypes.RootDirectory.read(from:))
        value.ownerId = try reader["OwnerId"].readIfPresent()
        value.lifeCycleState = try reader["LifeCycleState"].readIfPresent()
        return value
    }
}

extension EFSClientTypes.ResourceIdPreference {

    static func read(from reader: SmithyJSON.Reader) throws -> EFSClientTypes.ResourceIdPreference {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EFSClientTypes.ResourceIdPreference()
        value.resourceIdType = try reader["ResourceIdType"].readIfPresent()
        value.resources = try reader["Resources"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<EFSClientTypes.Resource>().read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension EFSClientTypes.BackupPolicy {

    static func write(value: EFSClientTypes.BackupPolicy?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Status"].write(value.status)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EFSClientTypes.BackupPolicy {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EFSClientTypes.BackupPolicy()
        value.status = try reader["Status"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension EFSClientTypes.FileSystemDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> EFSClientTypes.FileSystemDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EFSClientTypes.FileSystemDescription()
        value.ownerId = try reader["OwnerId"].readIfPresent() ?? ""
        value.creationToken = try reader["CreationToken"].readIfPresent() ?? ""
        value.fileSystemId = try reader["FileSystemId"].readIfPresent() ?? ""
        value.fileSystemArn = try reader["FileSystemArn"].readIfPresent()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.lifeCycleState = try reader["LifeCycleState"].readIfPresent() ?? .sdkUnknown("")
        value.name = try reader["Name"].readIfPresent()
        value.numberOfMountTargets = try reader["NumberOfMountTargets"].readIfPresent() ?? 0
        value.sizeInBytes = try reader["SizeInBytes"].readIfPresent(with: EFSClientTypes.FileSystemSize.read(from:))
        value.performanceMode = try reader["PerformanceMode"].readIfPresent() ?? .sdkUnknown("")
        value.encrypted = try reader["Encrypted"].readIfPresent()
        value.kmsKeyId = try reader["KmsKeyId"].readIfPresent()
        value.throughputMode = try reader["ThroughputMode"].readIfPresent()
        value.provisionedThroughputInMibps = try reader["ProvisionedThroughputInMibps"].readIfPresent()
        value.availabilityZoneName = try reader["AvailabilityZoneName"].readIfPresent()
        value.availabilityZoneId = try reader["AvailabilityZoneId"].readIfPresent()
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: EFSClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.fileSystemProtection = try reader["FileSystemProtection"].readIfPresent(with: EFSClientTypes.FileSystemProtectionDescription.read(from:))
        return value
    }
}

extension EFSClientTypes.LifecyclePolicy {

    static func write(value: EFSClientTypes.LifecyclePolicy?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["TransitionToArchive"].write(value.transitionToArchive)
        try writer["TransitionToIA"].write(value.transitionToIA)
        try writer["TransitionToPrimaryStorageClass"].write(value.transitionToPrimaryStorageClass)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EFSClientTypes.LifecyclePolicy {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EFSClientTypes.LifecyclePolicy()
        value.transitionToIA = try reader["TransitionToIA"].readIfPresent()
        value.transitionToPrimaryStorageClass = try reader["TransitionToPrimaryStorageClass"].readIfPresent()
        value.transitionToArchive = try reader["TransitionToArchive"].readIfPresent()
        return value
    }
}

extension EFSClientTypes.MountTargetDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> EFSClientTypes.MountTargetDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EFSClientTypes.MountTargetDescription()
        value.ownerId = try reader["OwnerId"].readIfPresent()
        value.mountTargetId = try reader["MountTargetId"].readIfPresent() ?? ""
        value.fileSystemId = try reader["FileSystemId"].readIfPresent() ?? ""
        value.subnetId = try reader["SubnetId"].readIfPresent() ?? ""
        value.lifeCycleState = try reader["LifeCycleState"].readIfPresent() ?? .sdkUnknown("")
        value.ipAddress = try reader["IpAddress"].readIfPresent()
        value.networkInterfaceId = try reader["NetworkInterfaceId"].readIfPresent()
        value.availabilityZoneId = try reader["AvailabilityZoneId"].readIfPresent()
        value.availabilityZoneName = try reader["AvailabilityZoneName"].readIfPresent()
        value.vpcId = try reader["VpcId"].readIfPresent()
        return value
    }
}

extension EFSClientTypes.ReplicationConfigurationDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> EFSClientTypes.ReplicationConfigurationDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EFSClientTypes.ReplicationConfigurationDescription()
        value.sourceFileSystemId = try reader["SourceFileSystemId"].readIfPresent() ?? ""
        value.sourceFileSystemRegion = try reader["SourceFileSystemRegion"].readIfPresent() ?? ""
        value.sourceFileSystemArn = try reader["SourceFileSystemArn"].readIfPresent() ?? ""
        value.originalSourceFileSystemArn = try reader["OriginalSourceFileSystemArn"].readIfPresent() ?? ""
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.destinations = try reader["Destinations"].readListIfPresent(memberReadingClosure: EFSClientTypes.Destination.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.sourceFileSystemOwnerId = try reader["SourceFileSystemOwnerId"].readIfPresent()
        return value
    }
}

extension EFSClientTypes.DestinationToCreate {

    static func write(value: EFSClientTypes.DestinationToCreate?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AvailabilityZoneName"].write(value.availabilityZoneName)
        try writer["FileSystemId"].write(value.fileSystemId)
        try writer["KmsKeyId"].write(value.kmsKeyId)
        try writer["Region"].write(value.region)
        try writer["RoleArn"].write(value.roleArn)
    }
}

public enum EFSClientTypes {}
