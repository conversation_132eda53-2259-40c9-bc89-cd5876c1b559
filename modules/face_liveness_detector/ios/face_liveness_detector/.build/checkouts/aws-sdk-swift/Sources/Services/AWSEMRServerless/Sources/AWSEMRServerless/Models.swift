//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
@_spi(SmithyReadWrite) import func SmithyReadWrite.listReadingClosure
@_spi(SmithyReadWrite) import func SmithyReadWrite.listWritingClosure
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
@_spi(SmithyTimestamps) import struct SmithyTimestamps.TimestampFormatter

extension EMRServerlessClientTypes {

    public enum Architecture: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case arm64
        case x8664
        case sdkUnknown(Swift.String)

        public static var allCases: [Architecture] {
            return [
                .arm64,
                .x8664
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .arm64: return "ARM64"
            case .x8664: return "X86_64"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EMRServerlessClientTypes {

    /// The configuration for an application to automatically start on job submission.
    public struct AutoStartConfig: Swift.Sendable {
        /// Enables the application to automatically start on job submission. Defaults to true.
        public var enabled: Swift.Bool?

        public init(
            enabled: Swift.Bool? = nil
        )
        {
            self.enabled = enabled
        }
    }
}

extension EMRServerlessClientTypes {

    /// The configuration for an application to automatically stop after a certain amount of time being idle.
    public struct AutoStopConfig: Swift.Sendable {
        /// Enables the application to automatically stop after a certain amount of time being idle. Defaults to true.
        public var enabled: Swift.Bool?
        /// The amount of idle time in minutes after which your application will automatically stop. Defaults to 15 minutes.
        public var idleTimeoutMinutes: Swift.Int?

        public init(
            enabled: Swift.Bool? = nil,
            idleTimeoutMinutes: Swift.Int? = nil
        )
        {
            self.enabled = enabled
            self.idleTimeoutMinutes = idleTimeoutMinutes
        }
    }
}

extension EMRServerlessClientTypes {

    /// The applied image configuration.
    public struct ImageConfiguration: Swift.Sendable {
        /// The image URI.
        /// This member is required.
        public var imageUri: Swift.String?
        /// The SHA256 digest of the image URI. This indicates which specific image the application is configured for. The image digest doesn't exist until an application has started.
        public var resolvedImageDigest: Swift.String?

        public init(
            imageUri: Swift.String? = nil,
            resolvedImageDigest: Swift.String? = nil
        )
        {
            self.imageUri = imageUri
            self.resolvedImageDigest = resolvedImageDigest
        }
    }
}

extension EMRServerlessClientTypes {

    /// The cumulative configuration requirements for every worker instance of the worker type.
    public struct WorkerResourceConfig: Swift.Sendable {
        /// The CPU requirements for every worker instance of the worker type.
        /// This member is required.
        public var cpu: Swift.String?
        /// The disk requirements for every worker instance of the worker type.
        public var disk: Swift.String?
        /// The disk type for every worker instance of the work type. Shuffle optimized disks have higher performance characteristics and are better for shuffle heavy workloads. Default is STANDARD.
        public var diskType: Swift.String?
        /// The memory requirements for every worker instance of the worker type.
        /// This member is required.
        public var memory: Swift.String?

        public init(
            cpu: Swift.String? = nil,
            disk: Swift.String? = nil,
            diskType: Swift.String? = nil,
            memory: Swift.String? = nil
        )
        {
            self.cpu = cpu
            self.disk = disk
            self.diskType = diskType
            self.memory = memory
        }
    }
}

extension EMRServerlessClientTypes {

    /// The initial capacity configuration per worker.
    public struct InitialCapacityConfig: Swift.Sendable {
        /// The resource configuration of the initial capacity configuration.
        public var workerConfiguration: EMRServerlessClientTypes.WorkerResourceConfig?
        /// The number of workers in the initial capacity configuration.
        /// This member is required.
        public var workerCount: Swift.Int

        public init(
            workerConfiguration: EMRServerlessClientTypes.WorkerResourceConfig? = nil,
            workerCount: Swift.Int = 0
        )
        {
            self.workerConfiguration = workerConfiguration
            self.workerCount = workerCount
        }
    }
}

extension EMRServerlessClientTypes {

    /// The configuration to use to enable the different types of interactive use cases in an application.
    public struct InteractiveConfiguration: Swift.Sendable {
        /// Enables an Apache Livy endpoint that you can connect to and run interactive jobs.
        public var livyEndpointEnabled: Swift.Bool?
        /// Enables you to connect an application to Amazon EMR Studio to run interactive workloads in a notebook.
        public var studioEnabled: Swift.Bool?

        public init(
            livyEndpointEnabled: Swift.Bool? = nil,
            studioEnabled: Swift.Bool? = nil
        )
        {
            self.livyEndpointEnabled = livyEndpointEnabled
            self.studioEnabled = studioEnabled
        }
    }
}

extension EMRServerlessClientTypes {

    /// The maximum allowed cumulative resources for an application. No new resources will be created once the limit is hit.
    public struct MaximumAllowedResources: Swift.Sendable {
        /// The maximum allowed CPU for an application.
        /// This member is required.
        public var cpu: Swift.String?
        /// The maximum allowed disk for an application.
        public var disk: Swift.String?
        /// The maximum allowed resources for an application.
        /// This member is required.
        public var memory: Swift.String?

        public init(
            cpu: Swift.String? = nil,
            disk: Swift.String? = nil,
            memory: Swift.String? = nil
        )
        {
            self.cpu = cpu
            self.disk = disk
            self.memory = memory
        }
    }
}

extension EMRServerlessClientTypes {

    /// The Amazon CloudWatch configuration for monitoring logs. You can configure your jobs to send log information to CloudWatch.
    public struct CloudWatchLoggingConfiguration: Swift.Sendable {
        /// Enables CloudWatch logging.
        /// This member is required.
        public var enabled: Swift.Bool?
        /// The Key Management Service (KMS) key ARN to encrypt the logs that you store in CloudWatch Logs.
        public var encryptionKeyArn: Swift.String?
        /// The name of the log group in Amazon CloudWatch Logs where you want to publish your logs.
        public var logGroupName: Swift.String?
        /// Prefix for the CloudWatch log stream name.
        public var logStreamNamePrefix: Swift.String?
        /// The types of logs that you want to publish to CloudWatch. If you don't specify any log types, driver STDOUT and STDERR logs will be published to CloudWatch Logs by default. For more information including the supported worker types for Hive and Spark, see [Logging for EMR Serverless with CloudWatch](https://docs.aws.amazon.com/emr/latest/EMR-Serverless-UserGuide/logging.html#jobs-log-storage-cw).
        ///
        /// * Key Valid Values: SPARK_DRIVER, SPARK_EXECUTOR, HIVE_DRIVER, TEZ_TASK
        ///
        /// * Array Members Valid Values: STDOUT, STDERR, HIVE_LOG, TEZ_AM, SYSTEM_LOGS
        public var logTypes: [Swift.String: [Swift.String]]?

        public init(
            enabled: Swift.Bool? = nil,
            encryptionKeyArn: Swift.String? = nil,
            logGroupName: Swift.String? = nil,
            logStreamNamePrefix: Swift.String? = nil,
            logTypes: [Swift.String: [Swift.String]]? = nil
        )
        {
            self.enabled = enabled
            self.encryptionKeyArn = encryptionKeyArn
            self.logGroupName = logGroupName
            self.logStreamNamePrefix = logStreamNamePrefix
            self.logTypes = logTypes
        }
    }
}

extension EMRServerlessClientTypes {

    /// The managed log persistence configuration for a job run.
    public struct ManagedPersistenceMonitoringConfiguration: Swift.Sendable {
        /// Enables managed logging and defaults to true. If set to false, managed logging will be turned off.
        public var enabled: Swift.Bool?
        /// The KMS key ARN to encrypt the logs stored in managed log persistence.
        public var encryptionKeyArn: Swift.String?

        public init(
            enabled: Swift.Bool? = nil,
            encryptionKeyArn: Swift.String? = nil
        )
        {
            self.enabled = enabled
            self.encryptionKeyArn = encryptionKeyArn
        }
    }
}

extension EMRServerlessClientTypes {

    /// The monitoring configuration object you can configure to send metrics to Amazon Managed Service for Prometheus for a job run.
    public struct PrometheusMonitoringConfiguration: Swift.Sendable {
        /// The remote write URL in the Amazon Managed Service for Prometheus workspace to send metrics to.
        public var remoteWriteUrl: Swift.String?

        public init(
            remoteWriteUrl: Swift.String? = nil
        )
        {
            self.remoteWriteUrl = remoteWriteUrl
        }
    }
}

extension EMRServerlessClientTypes {

    /// The Amazon S3 configuration for monitoring log publishing. You can configure your jobs to send log information to Amazon S3.
    public struct S3MonitoringConfiguration: Swift.Sendable {
        /// The KMS key ARN to encrypt the logs published to the given Amazon S3 destination.
        public var encryptionKeyArn: Swift.String?
        /// The Amazon S3 destination URI for log publishing.
        public var logUri: Swift.String?

        public init(
            encryptionKeyArn: Swift.String? = nil,
            logUri: Swift.String? = nil
        )
        {
            self.encryptionKeyArn = encryptionKeyArn
            self.logUri = logUri
        }
    }
}

extension EMRServerlessClientTypes {

    /// The configuration setting for monitoring.
    public struct MonitoringConfiguration: Swift.Sendable {
        /// The Amazon CloudWatch configuration for monitoring logs. You can configure your jobs to send log information to CloudWatch.
        public var cloudWatchLoggingConfiguration: EMRServerlessClientTypes.CloudWatchLoggingConfiguration?
        /// The managed log persistence configuration for a job run.
        public var managedPersistenceMonitoringConfiguration: EMRServerlessClientTypes.ManagedPersistenceMonitoringConfiguration?
        /// The monitoring configuration object you can configure to send metrics to Amazon Managed Service for Prometheus for a job run.
        public var prometheusMonitoringConfiguration: EMRServerlessClientTypes.PrometheusMonitoringConfiguration?
        /// The Amazon S3 configuration for monitoring log publishing.
        public var s3MonitoringConfiguration: EMRServerlessClientTypes.S3MonitoringConfiguration?

        public init(
            cloudWatchLoggingConfiguration: EMRServerlessClientTypes.CloudWatchLoggingConfiguration? = nil,
            managedPersistenceMonitoringConfiguration: EMRServerlessClientTypes.ManagedPersistenceMonitoringConfiguration? = nil,
            prometheusMonitoringConfiguration: EMRServerlessClientTypes.PrometheusMonitoringConfiguration? = nil,
            s3MonitoringConfiguration: EMRServerlessClientTypes.S3MonitoringConfiguration? = nil
        )
        {
            self.cloudWatchLoggingConfiguration = cloudWatchLoggingConfiguration
            self.managedPersistenceMonitoringConfiguration = managedPersistenceMonitoringConfiguration
            self.prometheusMonitoringConfiguration = prometheusMonitoringConfiguration
            self.s3MonitoringConfiguration = s3MonitoringConfiguration
        }
    }
}

extension EMRServerlessClientTypes {

    /// The network configuration for customer VPC connectivity.
    public struct NetworkConfiguration: Swift.Sendable {
        /// The array of security group Ids for customer VPC connectivity.
        public var securityGroupIds: [Swift.String]?
        /// The array of subnet Ids for customer VPC connectivity.
        public var subnetIds: [Swift.String]?

        public init(
            securityGroupIds: [Swift.String]? = nil,
            subnetIds: [Swift.String]? = nil
        )
        {
            self.securityGroupIds = securityGroupIds
            self.subnetIds = subnetIds
        }
    }
}

extension EMRServerlessClientTypes {

    /// The scheduler configuration for batch and streaming jobs running on this application. Supported with release labels emr-7.0.0 and above.
    public struct SchedulerConfiguration: Swift.Sendable {
        /// The maximum concurrent job runs on this application. If scheduler configuration is enabled on your application, the default value is 15. The valid range is 1 to 1000.
        public var maxConcurrentRuns: Swift.Int?
        /// The maximum duration in minutes for the job in QUEUED state. If scheduler configuration is enabled on your application, the default value is 360 minutes (6 hours). The valid range is from 15 to 720.
        public var queueTimeoutMinutes: Swift.Int?

        public init(
            maxConcurrentRuns: Swift.Int? = nil,
            queueTimeoutMinutes: Swift.Int? = nil
        )
        {
            self.maxConcurrentRuns = maxConcurrentRuns
            self.queueTimeoutMinutes = queueTimeoutMinutes
        }
    }
}

extension EMRServerlessClientTypes {

    public enum ApplicationState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case created
        case creating
        case started
        case starting
        case stopped
        case stopping
        case terminated
        case sdkUnknown(Swift.String)

        public static var allCases: [ApplicationState] {
            return [
                .created,
                .creating,
                .started,
                .starting,
                .stopped,
                .stopping,
                .terminated
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .created: return "CREATED"
            case .creating: return "CREATING"
            case .started: return "STARTED"
            case .starting: return "STARTING"
            case .stopped: return "STOPPED"
            case .stopping: return "STOPPING"
            case .terminated: return "TERMINATED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EMRServerlessClientTypes {

    /// The specifications for a worker type.
    public struct WorkerTypeSpecification: Swift.Sendable {
        /// The image configuration for a worker type.
        public var imageConfiguration: EMRServerlessClientTypes.ImageConfiguration?

        public init(
            imageConfiguration: EMRServerlessClientTypes.ImageConfiguration? = nil
        )
        {
            self.imageConfiguration = imageConfiguration
        }
    }
}

extension EMRServerlessClientTypes {

    /// The summary of attributes associated with an application.
    public struct ApplicationSummary: Swift.Sendable {
        /// The CPU architecture of an application.
        public var architecture: EMRServerlessClientTypes.Architecture?
        /// The ARN of the application.
        /// This member is required.
        public var arn: Swift.String?
        /// The date and time when the application was created.
        /// This member is required.
        public var createdAt: Foundation.Date?
        /// The ID of the application.
        /// This member is required.
        public var id: Swift.String?
        /// The name of the application.
        public var name: Swift.String?
        /// The Amazon EMR release associated with the application.
        /// This member is required.
        public var releaseLabel: Swift.String?
        /// The state of the application.
        /// This member is required.
        public var state: EMRServerlessClientTypes.ApplicationState?
        /// The state details of the application.
        public var stateDetails: Swift.String?
        /// The type of application, such as Spark or Hive.
        /// This member is required.
        public var type: Swift.String?
        /// The date and time when the application was last updated.
        /// This member is required.
        public var updatedAt: Foundation.Date?

        public init(
            architecture: EMRServerlessClientTypes.Architecture? = nil,
            arn: Swift.String? = nil,
            createdAt: Foundation.Date? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil,
            releaseLabel: Swift.String? = nil,
            state: EMRServerlessClientTypes.ApplicationState? = nil,
            stateDetails: Swift.String? = nil,
            type: Swift.String? = nil,
            updatedAt: Foundation.Date? = nil
        )
        {
            self.architecture = architecture
            self.arn = arn
            self.createdAt = createdAt
            self.id = id
            self.name = name
            self.releaseLabel = releaseLabel
            self.state = state
            self.stateDetails = stateDetails
            self.type = type
            self.updatedAt = updatedAt
        }
    }
}

/// The request could not be processed because of conflict in the current state of the resource.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Request processing failed because of an error or failure with the service.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified resource was not found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The input fails to satisfy the constraints specified by an Amazon Web Services service.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension EMRServerlessClientTypes {

    /// The image configuration.
    public struct ImageConfigurationInput: Swift.Sendable {
        /// The URI of an image in the Amazon ECR registry. This field is required when you create a new application. If you leave this field blank in an update, Amazon EMR will remove the image configuration.
        public var imageUri: Swift.String?

        public init(
            imageUri: Swift.String? = nil
        )
        {
            self.imageUri = imageUri
        }
    }
}

extension EMRServerlessClientTypes {

    /// The specifications for a worker type.
    public struct WorkerTypeSpecificationInput: Swift.Sendable {
        /// The image configuration for a worker type.
        public var imageConfiguration: EMRServerlessClientTypes.ImageConfigurationInput?

        public init(
            imageConfiguration: EMRServerlessClientTypes.ImageConfigurationInput? = nil
        )
        {
            self.imageConfiguration = imageConfiguration
        }
    }
}

public struct CreateApplicationOutput: Swift.Sendable {
    /// The output contains the application ID.
    /// This member is required.
    public var applicationId: Swift.String?
    /// The output contains the ARN of the application.
    /// This member is required.
    public var arn: Swift.String?
    /// The output contains the name of the application.
    public var name: Swift.String?

    public init(
        applicationId: Swift.String? = nil,
        arn: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.applicationId = applicationId
        self.arn = arn
        self.name = name
    }
}

public struct DeleteApplicationInput: Swift.Sendable {
    /// The ID of the application that will be deleted.
    /// This member is required.
    public var applicationId: Swift.String?

    public init(
        applicationId: Swift.String? = nil
    )
    {
        self.applicationId = applicationId
    }
}

public struct DeleteApplicationOutput: Swift.Sendable {

    public init() { }
}

public struct GetApplicationInput: Swift.Sendable {
    /// The ID of the application that will be described.
    /// This member is required.
    public var applicationId: Swift.String?

    public init(
        applicationId: Swift.String? = nil
    )
    {
        self.applicationId = applicationId
    }
}

public struct ListApplicationsInput: Swift.Sendable {
    /// The maximum number of applications that can be listed.
    public var maxResults: Swift.Int?
    /// The token for the next set of application results.
    public var nextToken: Swift.String?
    /// An optional filter for application states. Note that if this filter contains multiple states, the resulting list will be grouped by the state.
    public var states: [EMRServerlessClientTypes.ApplicationState]?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        states: [EMRServerlessClientTypes.ApplicationState]? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.states = states
    }
}

public struct ListApplicationsOutput: Swift.Sendable {
    /// The output lists the specified applications.
    /// This member is required.
    public var applications: [EMRServerlessClientTypes.ApplicationSummary]?
    /// The output displays the token for the next set of application results. This is required for pagination and is available as a response of the previous request.
    public var nextToken: Swift.String?

    public init(
        applications: [EMRServerlessClientTypes.ApplicationSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.applications = applications
        self.nextToken = nextToken
    }
}

/// The maximum number of resources per account has been reached.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceQuotaExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct StartApplicationInput: Swift.Sendable {
    /// The ID of the application to start.
    /// This member is required.
    public var applicationId: Swift.String?

    public init(
        applicationId: Swift.String? = nil
    )
    {
        self.applicationId = applicationId
    }
}

public struct StartApplicationOutput: Swift.Sendable {

    public init() { }
}

public struct StopApplicationInput: Swift.Sendable {
    /// The ID of the application to stop.
    /// This member is required.
    public var applicationId: Swift.String?

    public init(
        applicationId: Swift.String? = nil
    )
    {
        self.applicationId = applicationId
    }
}

public struct StopApplicationOutput: Swift.Sendable {

    public init() { }
}

public struct CancelJobRunInput: Swift.Sendable {
    /// The ID of the application on which the job run will be canceled.
    /// This member is required.
    public var applicationId: Swift.String?
    /// The ID of the job run to cancel.
    /// This member is required.
    public var jobRunId: Swift.String?

    public init(
        applicationId: Swift.String? = nil,
        jobRunId: Swift.String? = nil
    )
    {
        self.applicationId = applicationId
        self.jobRunId = jobRunId
    }
}

public struct CancelJobRunOutput: Swift.Sendable {
    /// The output contains the application ID on which the job run is cancelled.
    /// This member is required.
    public var applicationId: Swift.String?
    /// The output contains the ID of the cancelled job run.
    /// This member is required.
    public var jobRunId: Swift.String?

    public init(
        applicationId: Swift.String? = nil,
        jobRunId: Swift.String? = nil
    )
    {
        self.applicationId = applicationId
        self.jobRunId = jobRunId
    }
}

public struct GetDashboardForJobRunInput: Swift.Sendable {
    /// Allows access to system profile logs for Lake Formation-enabled jobs. Default is false.
    public var accessSystemProfileLogs: Swift.Bool?
    /// The ID of the application.
    /// This member is required.
    public var applicationId: Swift.String?
    /// An optimal parameter that indicates the amount of attempts for the job. If not specified, this value defaults to the attempt of the latest job.
    public var attempt: Swift.Int?
    /// The ID of the job run.
    /// This member is required.
    public var jobRunId: Swift.String?

    public init(
        accessSystemProfileLogs: Swift.Bool? = nil,
        applicationId: Swift.String? = nil,
        attempt: Swift.Int? = nil,
        jobRunId: Swift.String? = nil
    )
    {
        self.accessSystemProfileLogs = accessSystemProfileLogs
        self.applicationId = applicationId
        self.attempt = attempt
        self.jobRunId = jobRunId
    }
}

public struct GetDashboardForJobRunOutput: Swift.Sendable {
    /// The URL to view job run's dashboard.
    public var url: Swift.String?

    public init(
        url: Swift.String? = nil
    )
    {
        self.url = url
    }
}

public struct GetJobRunInput: Swift.Sendable {
    /// The ID of the application on which the job run is submitted.
    /// This member is required.
    public var applicationId: Swift.String?
    /// An optimal parameter that indicates the amount of attempts for the job. If not specified, this value defaults to the attempt of the latest job.
    public var attempt: Swift.Int?
    /// The ID of the job run.
    /// This member is required.
    public var jobRunId: Swift.String?

    public init(
        applicationId: Swift.String? = nil,
        attempt: Swift.Int? = nil,
        jobRunId: Swift.String? = nil
    )
    {
        self.applicationId = applicationId
        self.attempt = attempt
        self.jobRunId = jobRunId
    }
}

extension EMRServerlessClientTypes {

    /// The resource utilization for memory, storage, and vCPU for jobs.
    public struct ResourceUtilization: Swift.Sendable {
        /// The aggregated memory used per hour from the time the job starts executing until the job is terminated.
        public var memoryGBHour: Swift.Double?
        /// The aggregated storage used per hour from the time the job starts executing until the job is terminated.
        public var storageGBHour: Swift.Double?
        /// The aggregated vCPU used per hour from the time the job starts executing until the job is terminated.
        public var vCPUHour: Swift.Double?

        public init(
            memoryGBHour: Swift.Double? = nil,
            storageGBHour: Swift.Double? = nil,
            vCPUHour: Swift.Double? = nil
        )
        {
            self.memoryGBHour = memoryGBHour
            self.storageGBHour = storageGBHour
            self.vCPUHour = vCPUHour
        }
    }
}

extension EMRServerlessClientTypes {

    /// The configurations for the Hive job driver.
    public struct Hive: Swift.Sendable {
        /// The query file for the Hive job run.
        public var initQueryFile: Swift.String?
        /// The parameters for the Hive job run.
        public var parameters: Swift.String?
        /// The query for the Hive job run.
        /// This member is required.
        public var query: Swift.String?

        public init(
            initQueryFile: Swift.String? = nil,
            parameters: Swift.String? = nil,
            query: Swift.String? = nil
        )
        {
            self.initQueryFile = initQueryFile
            self.parameters = parameters
            self.query = query
        }
    }
}

extension EMRServerlessClientTypes.Hive: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "Hive(initQueryFile: \"CONTENT_REDACTED\", parameters: \"CONTENT_REDACTED\", query: \"CONTENT_REDACTED\")"}
}

extension EMRServerlessClientTypes {

    /// The configurations for the Spark submit job driver.
    public struct SparkSubmit: Swift.Sendable {
        /// The entry point for the Spark submit job run.
        /// This member is required.
        public var entryPoint: Swift.String?
        /// The arguments for the Spark submit job run.
        public var entryPointArguments: [Swift.String]?
        /// The parameters for the Spark submit job run.
        public var sparkSubmitParameters: Swift.String?

        public init(
            entryPoint: Swift.String? = nil,
            entryPointArguments: [Swift.String]? = nil,
            sparkSubmitParameters: Swift.String? = nil
        )
        {
            self.entryPoint = entryPoint
            self.entryPointArguments = entryPointArguments
            self.sparkSubmitParameters = sparkSubmitParameters
        }
    }
}

extension EMRServerlessClientTypes.SparkSubmit: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "SparkSubmit(entryPoint: \"CONTENT_REDACTED\", entryPointArguments: \"CONTENT_REDACTED\", sparkSubmitParameters: \"CONTENT_REDACTED\")"}
}

extension EMRServerlessClientTypes {

    /// The driver that the job runs on.
    public enum JobDriver: Swift.Sendable {
        /// The job driver parameters specified for Spark.
        case sparksubmit(EMRServerlessClientTypes.SparkSubmit)
        /// The job driver parameters specified for Hive.
        case hive(EMRServerlessClientTypes.Hive)
        case sdkUnknown(Swift.String)
    }
}

extension EMRServerlessClientTypes {

    public enum JobRunMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case batch
        case streaming
        case sdkUnknown(Swift.String)

        public static var allCases: [JobRunMode] {
            return [
                .batch,
                .streaming
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .batch: return "BATCH"
            case .streaming: return "STREAMING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EMRServerlessClientTypes {

    /// The retry policy to use for a job run.
    public struct RetryPolicy: Swift.Sendable {
        /// Maximum number of attempts for the job run. This parameter is only applicable for BATCH mode.
        public var maxAttempts: Swift.Int?
        /// Maximum number of failed attempts per hour. This [arameter is only applicable for STREAMING mode.
        public var maxFailedAttemptsPerHour: Swift.Int?

        public init(
            maxAttempts: Swift.Int? = nil,
            maxFailedAttemptsPerHour: Swift.Int? = nil
        )
        {
            self.maxAttempts = maxAttempts
            self.maxFailedAttemptsPerHour = maxFailedAttemptsPerHour
        }
    }
}

extension EMRServerlessClientTypes {

    public enum JobRunState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cancelled
        case cancelling
        case failed
        case pending
        case queued
        case running
        case scheduled
        case submitted
        case success
        case sdkUnknown(Swift.String)

        public static var allCases: [JobRunState] {
            return [
                .cancelled,
                .cancelling,
                .failed,
                .pending,
                .queued,
                .running,
                .scheduled,
                .submitted,
                .success
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cancelled: return "CANCELLED"
            case .cancelling: return "CANCELLING"
            case .failed: return "FAILED"
            case .pending: return "PENDING"
            case .queued: return "QUEUED"
            case .running: return "RUNNING"
            case .scheduled: return "SCHEDULED"
            case .submitted: return "SUBMITTED"
            case .success: return "SUCCESS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EMRServerlessClientTypes {

    /// The aggregate vCPU, memory, and storage resources used from the time job start executing till the time job is terminated, rounded up to the nearest second.
    public struct TotalResourceUtilization: Swift.Sendable {
        /// The aggregated memory used per hour from the time job start executing till the time job is terminated.
        public var memoryGBHour: Swift.Double?
        /// The aggregated storage used per hour from the time job start executing till the time job is terminated.
        public var storageGBHour: Swift.Double?
        /// The aggregated vCPU used per hour from the time job start executing till the time job is terminated.
        public var vCPUHour: Swift.Double?

        public init(
            memoryGBHour: Swift.Double? = nil,
            storageGBHour: Swift.Double? = nil,
            vCPUHour: Swift.Double? = nil
        )
        {
            self.memoryGBHour = memoryGBHour
            self.storageGBHour = storageGBHour
            self.vCPUHour = vCPUHour
        }
    }
}

public struct ListJobRunAttemptsInput: Swift.Sendable {
    /// The ID of the application for which to list job runs.
    /// This member is required.
    public var applicationId: Swift.String?
    /// The ID of the job run to list.
    /// This member is required.
    public var jobRunId: Swift.String?
    /// The maximum number of job run attempts to list.
    public var maxResults: Swift.Int?
    /// The token for the next set of job run attempt results.
    public var nextToken: Swift.String?

    public init(
        applicationId: Swift.String? = nil,
        jobRunId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.applicationId = applicationId
        self.jobRunId = jobRunId
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension EMRServerlessClientTypes {

    /// The summary of attributes associated with a job run attempt.
    public struct JobRunAttemptSummary: Swift.Sendable {
        /// The ID of the application the job is running on.
        /// This member is required.
        public var applicationId: Swift.String?
        /// The Amazon Resource Name (ARN) of the job run.
        /// This member is required.
        public var arn: Swift.String?
        /// The attempt number of the job run execution.
        public var attempt: Swift.Int?
        /// The date and time when the job run attempt was created.
        /// This member is required.
        public var createdAt: Foundation.Date?
        /// The user who created the job run.
        /// This member is required.
        public var createdBy: Swift.String?
        /// The Amazon Resource Name (ARN) of the execution role of the job run..
        /// This member is required.
        public var executionRole: Swift.String?
        /// The ID of the job run attempt.
        /// This member is required.
        public var id: Swift.String?
        /// The date and time of when the job run was created.
        /// This member is required.
        public var jobCreatedAt: Foundation.Date?
        /// The mode of the job run attempt.
        public var mode: EMRServerlessClientTypes.JobRunMode?
        /// The name of the job run attempt.
        public var name: Swift.String?
        /// The Amazon EMR release label of the job run attempt.
        /// This member is required.
        public var releaseLabel: Swift.String?
        /// The state of the job run attempt.
        /// This member is required.
        public var state: EMRServerlessClientTypes.JobRunState?
        /// The state details of the job run attempt.
        /// This member is required.
        public var stateDetails: Swift.String?
        /// The type of the job run, such as Spark or Hive.
        public var type: Swift.String?
        /// The date and time of when the job run attempt was last updated.
        /// This member is required.
        public var updatedAt: Foundation.Date?

        public init(
            applicationId: Swift.String? = nil,
            arn: Swift.String? = nil,
            attempt: Swift.Int? = nil,
            createdAt: Foundation.Date? = nil,
            createdBy: Swift.String? = nil,
            executionRole: Swift.String? = nil,
            id: Swift.String? = nil,
            jobCreatedAt: Foundation.Date? = nil,
            mode: EMRServerlessClientTypes.JobRunMode? = nil,
            name: Swift.String? = nil,
            releaseLabel: Swift.String? = nil,
            state: EMRServerlessClientTypes.JobRunState? = nil,
            stateDetails: Swift.String? = nil,
            type: Swift.String? = nil,
            updatedAt: Foundation.Date? = nil
        )
        {
            self.applicationId = applicationId
            self.arn = arn
            self.attempt = attempt
            self.createdAt = createdAt
            self.createdBy = createdBy
            self.executionRole = executionRole
            self.id = id
            self.jobCreatedAt = jobCreatedAt
            self.mode = mode
            self.name = name
            self.releaseLabel = releaseLabel
            self.state = state
            self.stateDetails = stateDetails
            self.type = type
            self.updatedAt = updatedAt
        }
    }
}

public struct ListJobRunAttemptsOutput: Swift.Sendable {
    /// The array of the listed job run attempt objects.
    /// This member is required.
    public var jobRunAttempts: [EMRServerlessClientTypes.JobRunAttemptSummary]?
    /// The output displays the token for the next set of application results. This is required for pagination and is available as a response of the previous request.
    public var nextToken: Swift.String?

    public init(
        jobRunAttempts: [EMRServerlessClientTypes.JobRunAttemptSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.jobRunAttempts = jobRunAttempts
        self.nextToken = nextToken
    }
}

public struct ListJobRunsInput: Swift.Sendable {
    /// The ID of the application for which to list the job run.
    /// This member is required.
    public var applicationId: Swift.String?
    /// The lower bound of the option to filter by creation date and time.
    public var createdAtAfter: Foundation.Date?
    /// The upper bound of the option to filter by creation date and time.
    public var createdAtBefore: Foundation.Date?
    /// The maximum number of job runs that can be listed.
    public var maxResults: Swift.Int?
    /// The mode of the job runs to list.
    public var mode: EMRServerlessClientTypes.JobRunMode?
    /// The token for the next set of job run results.
    public var nextToken: Swift.String?
    /// An optional filter for job run states. Note that if this filter contains multiple states, the resulting list will be grouped by the state.
    public var states: [EMRServerlessClientTypes.JobRunState]?

    public init(
        applicationId: Swift.String? = nil,
        createdAtAfter: Foundation.Date? = nil,
        createdAtBefore: Foundation.Date? = nil,
        maxResults: Swift.Int? = nil,
        mode: EMRServerlessClientTypes.JobRunMode? = nil,
        nextToken: Swift.String? = nil,
        states: [EMRServerlessClientTypes.JobRunState]? = nil
    )
    {
        self.applicationId = applicationId
        self.createdAtAfter = createdAtAfter
        self.createdAtBefore = createdAtBefore
        self.maxResults = maxResults
        self.mode = mode
        self.nextToken = nextToken
        self.states = states
    }
}

extension EMRServerlessClientTypes {

    /// The summary of attributes associated with a job run.
    public struct JobRunSummary: Swift.Sendable {
        /// The ID of the application the job is running on.
        /// This member is required.
        public var applicationId: Swift.String?
        /// The ARN of the job run.
        /// This member is required.
        public var arn: Swift.String?
        /// The attempt number of the job run execution.
        public var attempt: Swift.Int?
        /// The date and time of when the job run attempt was created.
        public var attemptCreatedAt: Foundation.Date?
        /// The date and time of when the job run attempt was last updated.
        public var attemptUpdatedAt: Foundation.Date?
        /// The date and time when the job run was created.
        /// This member is required.
        public var createdAt: Foundation.Date?
        /// The user who created the job run.
        /// This member is required.
        public var createdBy: Swift.String?
        /// The execution role ARN of the job run.
        /// This member is required.
        public var executionRole: Swift.String?
        /// The ID of the job run.
        /// This member is required.
        public var id: Swift.String?
        /// The mode of the job run.
        public var mode: EMRServerlessClientTypes.JobRunMode?
        /// The optional job run name. This doesn't have to be unique.
        public var name: Swift.String?
        /// The Amazon EMR release associated with the application your job is running on.
        /// This member is required.
        public var releaseLabel: Swift.String?
        /// The state of the job run.
        /// This member is required.
        public var state: EMRServerlessClientTypes.JobRunState?
        /// The state details of the job run.
        /// This member is required.
        public var stateDetails: Swift.String?
        /// The type of job run, such as Spark or Hive.
        public var type: Swift.String?
        /// The date and time when the job run was last updated.
        /// This member is required.
        public var updatedAt: Foundation.Date?

        public init(
            applicationId: Swift.String? = nil,
            arn: Swift.String? = nil,
            attempt: Swift.Int? = nil,
            attemptCreatedAt: Foundation.Date? = nil,
            attemptUpdatedAt: Foundation.Date? = nil,
            createdAt: Foundation.Date? = nil,
            createdBy: Swift.String? = nil,
            executionRole: Swift.String? = nil,
            id: Swift.String? = nil,
            mode: EMRServerlessClientTypes.JobRunMode? = nil,
            name: Swift.String? = nil,
            releaseLabel: Swift.String? = nil,
            state: EMRServerlessClientTypes.JobRunState? = nil,
            stateDetails: Swift.String? = nil,
            type: Swift.String? = nil,
            updatedAt: Foundation.Date? = nil
        )
        {
            self.applicationId = applicationId
            self.arn = arn
            self.attempt = attempt
            self.attemptCreatedAt = attemptCreatedAt
            self.attemptUpdatedAt = attemptUpdatedAt
            self.createdAt = createdAt
            self.createdBy = createdBy
            self.executionRole = executionRole
            self.id = id
            self.mode = mode
            self.name = name
            self.releaseLabel = releaseLabel
            self.state = state
            self.stateDetails = stateDetails
            self.type = type
            self.updatedAt = updatedAt
        }
    }
}

public struct ListJobRunsOutput: Swift.Sendable {
    /// The output lists information about the specified job runs.
    /// This member is required.
    public var jobRuns: [EMRServerlessClientTypes.JobRunSummary]?
    /// The output displays the token for the next set of job run results. This is required for pagination and is available as a response of the previous request.
    public var nextToken: Swift.String?

    public init(
        jobRuns: [EMRServerlessClientTypes.JobRunSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.jobRuns = jobRuns
        self.nextToken = nextToken
    }
}

public struct StartJobRunOutput: Swift.Sendable {
    /// This output displays the application ID on which the job run was submitted.
    /// This member is required.
    public var applicationId: Swift.String?
    /// This output displays the ARN of the job run..
    /// This member is required.
    public var arn: Swift.String?
    /// The output contains the ID of the started job run.
    /// This member is required.
    public var jobRunId: Swift.String?

    public init(
        applicationId: Swift.String? = nil,
        arn: Swift.String? = nil,
        jobRunId: Swift.String? = nil
    )
    {
        self.applicationId = applicationId
        self.arn = arn
        self.jobRunId = jobRunId
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) that identifies the resource to list the tags for. Currently, the supported resources are Amazon EMR Serverless applications and job runs.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// The tags for the resource.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) that identifies the resource to list the tags for. Currently, the supported resources are Amazon EMR Serverless applications and job runs.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tags to add to the resource. A tag is an array of key-value pairs.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) that identifies the resource to list the tags for. Currently, the supported resources are Amazon EMR Serverless applications and job runs.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The keys of the tags to be removed.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

extension EMRServerlessClientTypes {

    /// A configuration specification to be used when provisioning an application. A configuration consists of a classification, properties, and optional nested configurations. A classification refers to an application-specific configuration file. Properties are the settings you want to change in that file.
    public struct Configuration: Swift.Sendable {
        /// The classification within a configuration.
        /// This member is required.
        public var classification: Swift.String?
        /// A list of additional configurations to apply within a configuration object.
        public var configurations: [EMRServerlessClientTypes.Configuration]?
        /// A set of properties specified within a configuration classification.
        public var properties: [Swift.String: Swift.String]?

        public init(
            classification: Swift.String? = nil,
            configurations: [EMRServerlessClientTypes.Configuration]? = nil,
            properties: [Swift.String: Swift.String]? = nil
        )
        {
            self.classification = classification
            self.configurations = configurations
            self.properties = properties
        }
    }
}

extension EMRServerlessClientTypes.Configuration: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "Configuration(classification: \(Swift.String(describing: classification)), configurations: \(Swift.String(describing: configurations)), properties: \"CONTENT_REDACTED\")"}
}

extension EMRServerlessClientTypes {

    /// Information about an application. Amazon EMR Serverless uses applications to run jobs.
    public struct Application: Swift.Sendable {
        /// The ID of the application.
        /// This member is required.
        public var applicationId: Swift.String?
        /// The CPU architecture of an application.
        public var architecture: EMRServerlessClientTypes.Architecture?
        /// The ARN of the application.
        /// This member is required.
        public var arn: Swift.String?
        /// The configuration for an application to automatically start on job submission.
        public var autoStartConfiguration: EMRServerlessClientTypes.AutoStartConfig?
        /// The configuration for an application to automatically stop after a certain amount of time being idle.
        public var autoStopConfiguration: EMRServerlessClientTypes.AutoStopConfig?
        /// The date and time when the application run was created.
        /// This member is required.
        public var createdAt: Foundation.Date?
        /// The image configuration applied to all worker types.
        public var imageConfiguration: EMRServerlessClientTypes.ImageConfiguration?
        /// The initial capacity of the application.
        public var initialCapacity: [Swift.String: EMRServerlessClientTypes.InitialCapacityConfig]?
        /// The interactive configuration object that enables the interactive use cases for an application.
        public var interactiveConfiguration: EMRServerlessClientTypes.InteractiveConfiguration?
        /// The maximum capacity of the application. This is cumulative across all workers at any given point in time during the lifespan of the application is created. No new resources will be created once any one of the defined limits is hit.
        public var maximumCapacity: EMRServerlessClientTypes.MaximumAllowedResources?
        /// The configuration setting for monitoring.
        public var monitoringConfiguration: EMRServerlessClientTypes.MonitoringConfiguration?
        /// The name of the application.
        public var name: Swift.String?
        /// The network configuration for customer VPC connectivity for the application.
        public var networkConfiguration: EMRServerlessClientTypes.NetworkConfiguration?
        /// The Amazon EMR release associated with the application.
        /// This member is required.
        public var releaseLabel: Swift.String?
        /// The [Configuration](https://docs.aws.amazon.com/emr-serverless/latest/APIReference/API_Configuration.html) specifications of an application. Each configuration consists of a classification and properties. You use this parameter when creating or updating an application. To see the runtimeConfiguration object of an application, run the [GetApplication](https://docs.aws.amazon.com/emr-serverless/latest/APIReference/API_GetApplication.html) API operation.
        public var runtimeConfiguration: [EMRServerlessClientTypes.Configuration]?
        /// The scheduler configuration for batch and streaming jobs running on this application. Supported with release labels emr-7.0.0 and above.
        public var schedulerConfiguration: EMRServerlessClientTypes.SchedulerConfiguration?
        /// The state of the application.
        /// This member is required.
        public var state: EMRServerlessClientTypes.ApplicationState?
        /// The state details of the application.
        public var stateDetails: Swift.String?
        /// The tags assigned to the application.
        public var tags: [Swift.String: Swift.String]?
        /// The type of application, such as Spark or Hive.
        /// This member is required.
        public var type: Swift.String?
        /// The date and time when the application run was last updated.
        /// This member is required.
        public var updatedAt: Foundation.Date?
        /// The specification applied to each worker type.
        public var workerTypeSpecifications: [Swift.String: EMRServerlessClientTypes.WorkerTypeSpecification]?

        public init(
            applicationId: Swift.String? = nil,
            architecture: EMRServerlessClientTypes.Architecture? = nil,
            arn: Swift.String? = nil,
            autoStartConfiguration: EMRServerlessClientTypes.AutoStartConfig? = nil,
            autoStopConfiguration: EMRServerlessClientTypes.AutoStopConfig? = nil,
            createdAt: Foundation.Date? = nil,
            imageConfiguration: EMRServerlessClientTypes.ImageConfiguration? = nil,
            initialCapacity: [Swift.String: EMRServerlessClientTypes.InitialCapacityConfig]? = nil,
            interactiveConfiguration: EMRServerlessClientTypes.InteractiveConfiguration? = nil,
            maximumCapacity: EMRServerlessClientTypes.MaximumAllowedResources? = nil,
            monitoringConfiguration: EMRServerlessClientTypes.MonitoringConfiguration? = nil,
            name: Swift.String? = nil,
            networkConfiguration: EMRServerlessClientTypes.NetworkConfiguration? = nil,
            releaseLabel: Swift.String? = nil,
            runtimeConfiguration: [EMRServerlessClientTypes.Configuration]? = nil,
            schedulerConfiguration: EMRServerlessClientTypes.SchedulerConfiguration? = nil,
            state: EMRServerlessClientTypes.ApplicationState? = nil,
            stateDetails: Swift.String? = nil,
            tags: [Swift.String: Swift.String]? = nil,
            type: Swift.String? = nil,
            updatedAt: Foundation.Date? = nil,
            workerTypeSpecifications: [Swift.String: EMRServerlessClientTypes.WorkerTypeSpecification]? = nil
        )
        {
            self.applicationId = applicationId
            self.architecture = architecture
            self.arn = arn
            self.autoStartConfiguration = autoStartConfiguration
            self.autoStopConfiguration = autoStopConfiguration
            self.createdAt = createdAt
            self.imageConfiguration = imageConfiguration
            self.initialCapacity = initialCapacity
            self.interactiveConfiguration = interactiveConfiguration
            self.maximumCapacity = maximumCapacity
            self.monitoringConfiguration = monitoringConfiguration
            self.name = name
            self.networkConfiguration = networkConfiguration
            self.releaseLabel = releaseLabel
            self.runtimeConfiguration = runtimeConfiguration
            self.schedulerConfiguration = schedulerConfiguration
            self.state = state
            self.stateDetails = stateDetails
            self.tags = tags
            self.type = type
            self.updatedAt = updatedAt
            self.workerTypeSpecifications = workerTypeSpecifications
        }
    }
}

extension EMRServerlessClientTypes {

    /// A configuration specification to be used to override existing configurations.
    public struct ConfigurationOverrides: Swift.Sendable {
        /// The override configurations for the application.
        public var applicationConfiguration: [EMRServerlessClientTypes.Configuration]?
        /// The override configurations for monitoring.
        public var monitoringConfiguration: EMRServerlessClientTypes.MonitoringConfiguration?

        public init(
            applicationConfiguration: [EMRServerlessClientTypes.Configuration]? = nil,
            monitoringConfiguration: EMRServerlessClientTypes.MonitoringConfiguration? = nil
        )
        {
            self.applicationConfiguration = applicationConfiguration
            self.monitoringConfiguration = monitoringConfiguration
        }
    }
}

public struct CreateApplicationInput: Swift.Sendable {
    /// The CPU architecture of an application.
    public var architecture: EMRServerlessClientTypes.Architecture?
    /// The configuration for an application to automatically start on job submission.
    public var autoStartConfiguration: EMRServerlessClientTypes.AutoStartConfig?
    /// The configuration for an application to automatically stop after a certain amount of time being idle.
    public var autoStopConfiguration: EMRServerlessClientTypes.AutoStopConfig?
    /// The client idempotency token of the application to create. Its value must be unique for each request.
    /// This member is required.
    public var clientToken: Swift.String?
    /// The image configuration for all worker types. You can either set this parameter or imageConfiguration for each worker type in workerTypeSpecifications.
    public var imageConfiguration: EMRServerlessClientTypes.ImageConfigurationInput?
    /// The capacity to initialize when the application is created.
    public var initialCapacity: [Swift.String: EMRServerlessClientTypes.InitialCapacityConfig]?
    /// The interactive configuration object that enables the interactive use cases to use when running an application.
    public var interactiveConfiguration: EMRServerlessClientTypes.InteractiveConfiguration?
    /// The maximum capacity to allocate when the application is created. This is cumulative across all workers at any given point in time, not just when an application is created. No new resources will be created once any one of the defined limits is hit.
    public var maximumCapacity: EMRServerlessClientTypes.MaximumAllowedResources?
    /// The configuration setting for monitoring.
    public var monitoringConfiguration: EMRServerlessClientTypes.MonitoringConfiguration?
    /// The name of the application.
    public var name: Swift.String?
    /// The network configuration for customer VPC connectivity.
    public var networkConfiguration: EMRServerlessClientTypes.NetworkConfiguration?
    /// The Amazon EMR release associated with the application.
    /// This member is required.
    public var releaseLabel: Swift.String?
    /// The [Configuration](https://docs.aws.amazon.com/emr-serverless/latest/APIReference/API_Configuration.html) specifications to use when creating an application. Each configuration consists of a classification and properties. This configuration is applied to all the job runs submitted under the application.
    public var runtimeConfiguration: [EMRServerlessClientTypes.Configuration]?
    /// The scheduler configuration for batch and streaming jobs running on this application. Supported with release labels emr-7.0.0 and above.
    public var schedulerConfiguration: EMRServerlessClientTypes.SchedulerConfiguration?
    /// The tags assigned to the application.
    public var tags: [Swift.String: Swift.String]?
    /// The type of application you want to start, such as Spark or Hive.
    /// This member is required.
    public var type: Swift.String?
    /// The key-value pairs that specify worker type to WorkerTypeSpecificationInput. This parameter must contain all valid worker types for a Spark or Hive application. Valid worker types include Driver and Executor for Spark applications and HiveDriver and TezTask for Hive applications. You can either set image details in this parameter for each worker type, or in imageConfiguration for all worker types.
    public var workerTypeSpecifications: [Swift.String: EMRServerlessClientTypes.WorkerTypeSpecificationInput]?

    public init(
        architecture: EMRServerlessClientTypes.Architecture? = nil,
        autoStartConfiguration: EMRServerlessClientTypes.AutoStartConfig? = nil,
        autoStopConfiguration: EMRServerlessClientTypes.AutoStopConfig? = nil,
        clientToken: Swift.String? = nil,
        imageConfiguration: EMRServerlessClientTypes.ImageConfigurationInput? = nil,
        initialCapacity: [Swift.String: EMRServerlessClientTypes.InitialCapacityConfig]? = nil,
        interactiveConfiguration: EMRServerlessClientTypes.InteractiveConfiguration? = nil,
        maximumCapacity: EMRServerlessClientTypes.MaximumAllowedResources? = nil,
        monitoringConfiguration: EMRServerlessClientTypes.MonitoringConfiguration? = nil,
        name: Swift.String? = nil,
        networkConfiguration: EMRServerlessClientTypes.NetworkConfiguration? = nil,
        releaseLabel: Swift.String? = nil,
        runtimeConfiguration: [EMRServerlessClientTypes.Configuration]? = nil,
        schedulerConfiguration: EMRServerlessClientTypes.SchedulerConfiguration? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        type: Swift.String? = nil,
        workerTypeSpecifications: [Swift.String: EMRServerlessClientTypes.WorkerTypeSpecificationInput]? = nil
    )
    {
        self.architecture = architecture
        self.autoStartConfiguration = autoStartConfiguration
        self.autoStopConfiguration = autoStopConfiguration
        self.clientToken = clientToken
        self.imageConfiguration = imageConfiguration
        self.initialCapacity = initialCapacity
        self.interactiveConfiguration = interactiveConfiguration
        self.maximumCapacity = maximumCapacity
        self.monitoringConfiguration = monitoringConfiguration
        self.name = name
        self.networkConfiguration = networkConfiguration
        self.releaseLabel = releaseLabel
        self.runtimeConfiguration = runtimeConfiguration
        self.schedulerConfiguration = schedulerConfiguration
        self.tags = tags
        self.type = type
        self.workerTypeSpecifications = workerTypeSpecifications
    }
}

public struct UpdateApplicationInput: Swift.Sendable {
    /// The ID of the application to update.
    /// This member is required.
    public var applicationId: Swift.String?
    /// The CPU architecture of an application.
    public var architecture: EMRServerlessClientTypes.Architecture?
    /// The configuration for an application to automatically start on job submission.
    public var autoStartConfiguration: EMRServerlessClientTypes.AutoStartConfig?
    /// The configuration for an application to automatically stop after a certain amount of time being idle.
    public var autoStopConfiguration: EMRServerlessClientTypes.AutoStopConfig?
    /// The client idempotency token of the application to update. Its value must be unique for each request.
    /// This member is required.
    public var clientToken: Swift.String?
    /// The image configuration to be used for all worker types. You can either set this parameter or imageConfiguration for each worker type in WorkerTypeSpecificationInput.
    public var imageConfiguration: EMRServerlessClientTypes.ImageConfigurationInput?
    /// The capacity to initialize when the application is updated.
    public var initialCapacity: [Swift.String: EMRServerlessClientTypes.InitialCapacityConfig]?
    /// The interactive configuration object that contains new interactive use cases when the application is updated.
    public var interactiveConfiguration: EMRServerlessClientTypes.InteractiveConfiguration?
    /// The maximum capacity to allocate when the application is updated. This is cumulative across all workers at any given point in time during the lifespan of the application. No new resources will be created once any one of the defined limits is hit.
    public var maximumCapacity: EMRServerlessClientTypes.MaximumAllowedResources?
    /// The configuration setting for monitoring.
    public var monitoringConfiguration: EMRServerlessClientTypes.MonitoringConfiguration?
    /// The network configuration for customer VPC connectivity.
    public var networkConfiguration: EMRServerlessClientTypes.NetworkConfiguration?
    /// The Amazon EMR release label for the application. You can change the release label to use a different release of Amazon EMR.
    public var releaseLabel: Swift.String?
    /// The [Configuration](https://docs.aws.amazon.com/emr-serverless/latest/APIReference/API_Configuration.html) specifications to use when updating an application. Each configuration consists of a classification and properties. This configuration is applied across all the job runs submitted under the application.
    public var runtimeConfiguration: [EMRServerlessClientTypes.Configuration]?
    /// The scheduler configuration for batch and streaming jobs running on this application. Supported with release labels emr-7.0.0 and above.
    public var schedulerConfiguration: EMRServerlessClientTypes.SchedulerConfiguration?
    /// The key-value pairs that specify worker type to WorkerTypeSpecificationInput. This parameter must contain all valid worker types for a Spark or Hive application. Valid worker types include Driver and Executor for Spark applications and HiveDriver and TezTask for Hive applications. You can either set image details in this parameter for each worker type, or in imageConfiguration for all worker types.
    public var workerTypeSpecifications: [Swift.String: EMRServerlessClientTypes.WorkerTypeSpecificationInput]?

    public init(
        applicationId: Swift.String? = nil,
        architecture: EMRServerlessClientTypes.Architecture? = nil,
        autoStartConfiguration: EMRServerlessClientTypes.AutoStartConfig? = nil,
        autoStopConfiguration: EMRServerlessClientTypes.AutoStopConfig? = nil,
        clientToken: Swift.String? = nil,
        imageConfiguration: EMRServerlessClientTypes.ImageConfigurationInput? = nil,
        initialCapacity: [Swift.String: EMRServerlessClientTypes.InitialCapacityConfig]? = nil,
        interactiveConfiguration: EMRServerlessClientTypes.InteractiveConfiguration? = nil,
        maximumCapacity: EMRServerlessClientTypes.MaximumAllowedResources? = nil,
        monitoringConfiguration: EMRServerlessClientTypes.MonitoringConfiguration? = nil,
        networkConfiguration: EMRServerlessClientTypes.NetworkConfiguration? = nil,
        releaseLabel: Swift.String? = nil,
        runtimeConfiguration: [EMRServerlessClientTypes.Configuration]? = nil,
        schedulerConfiguration: EMRServerlessClientTypes.SchedulerConfiguration? = nil,
        workerTypeSpecifications: [Swift.String: EMRServerlessClientTypes.WorkerTypeSpecificationInput]? = nil
    )
    {
        self.applicationId = applicationId
        self.architecture = architecture
        self.autoStartConfiguration = autoStartConfiguration
        self.autoStopConfiguration = autoStopConfiguration
        self.clientToken = clientToken
        self.imageConfiguration = imageConfiguration
        self.initialCapacity = initialCapacity
        self.interactiveConfiguration = interactiveConfiguration
        self.maximumCapacity = maximumCapacity
        self.monitoringConfiguration = monitoringConfiguration
        self.networkConfiguration = networkConfiguration
        self.releaseLabel = releaseLabel
        self.runtimeConfiguration = runtimeConfiguration
        self.schedulerConfiguration = schedulerConfiguration
        self.workerTypeSpecifications = workerTypeSpecifications
    }
}

extension EMRServerlessClientTypes {

    /// Information about a job run. A job run is a unit of work, such as a Spark JAR, Hive query, or SparkSQL query, that you submit to an Amazon EMR Serverless application.
    public struct JobRun: Swift.Sendable {
        /// The ID of the application the job is running on.
        /// This member is required.
        public var applicationId: Swift.String?
        /// The execution role ARN of the job run.
        /// This member is required.
        public var arn: Swift.String?
        /// The attempt of the job run.
        public var attempt: Swift.Int?
        /// The date and time of when the job run attempt was created.
        public var attemptCreatedAt: Foundation.Date?
        /// The date and time of when the job run attempt was last updated.
        public var attemptUpdatedAt: Foundation.Date?
        /// The aggregate vCPU, memory, and storage that Amazon Web Services has billed for the job run. The billed resources include a 1-minute minimum usage for workers, plus additional storage over 20 GB per worker. Note that billed resources do not include usage for idle pre-initialized workers.
        public var billedResourceUtilization: EMRServerlessClientTypes.ResourceUtilization?
        /// The configuration settings that are used to override default configuration.
        public var configurationOverrides: EMRServerlessClientTypes.ConfigurationOverrides?
        /// The date and time when the job run was created.
        /// This member is required.
        public var createdAt: Foundation.Date?
        /// The user who created the job run.
        /// This member is required.
        public var createdBy: Swift.String?
        /// The date and time when the job was terminated.
        public var endedAt: Foundation.Date?
        /// The execution role ARN of the job run.
        /// This member is required.
        public var executionRole: Swift.String?
        /// Returns the job run timeout value from the StartJobRun call. If no timeout was specified, then it returns the default timeout of 720 minutes.
        public var executionTimeoutMinutes: Swift.Int?
        /// The job driver for the job run.
        /// This member is required.
        public var jobDriver: EMRServerlessClientTypes.JobDriver?
        /// The ID of the job run.
        /// This member is required.
        public var jobRunId: Swift.String?
        /// The mode of the job run.
        public var mode: EMRServerlessClientTypes.JobRunMode?
        /// The optional job run name. This doesn't have to be unique.
        public var name: Swift.String?
        /// The network configuration for customer VPC connectivity.
        public var networkConfiguration: EMRServerlessClientTypes.NetworkConfiguration?
        /// The total time for a job in the QUEUED state in milliseconds.
        public var queuedDurationMilliseconds: Swift.Int?
        /// The Amazon EMR release associated with the application your job is running on.
        /// This member is required.
        public var releaseLabel: Swift.String?
        /// The retry policy of the job run.
        public var retryPolicy: EMRServerlessClientTypes.RetryPolicy?
        /// The date and time when the job moved to the RUNNING state.
        public var startedAt: Foundation.Date?
        /// The state of the job run.
        /// This member is required.
        public var state: EMRServerlessClientTypes.JobRunState?
        /// The state details of the job run.
        /// This member is required.
        public var stateDetails: Swift.String?
        /// The tags assigned to the job run.
        public var tags: [Swift.String: Swift.String]?
        /// The job run total execution duration in seconds. This field is only available for job runs in a COMPLETED, FAILED, or CANCELLED state.
        public var totalExecutionDurationSeconds: Swift.Int?
        /// The aggregate vCPU, memory, and storage resources used from the time the job starts to execute, until the time the job terminates, rounded up to the nearest second.
        public var totalResourceUtilization: EMRServerlessClientTypes.TotalResourceUtilization?
        /// The date and time when the job run was updated.
        /// This member is required.
        public var updatedAt: Foundation.Date?

        public init(
            applicationId: Swift.String? = nil,
            arn: Swift.String? = nil,
            attempt: Swift.Int? = nil,
            attemptCreatedAt: Foundation.Date? = nil,
            attemptUpdatedAt: Foundation.Date? = nil,
            billedResourceUtilization: EMRServerlessClientTypes.ResourceUtilization? = nil,
            configurationOverrides: EMRServerlessClientTypes.ConfigurationOverrides? = nil,
            createdAt: Foundation.Date? = nil,
            createdBy: Swift.String? = nil,
            endedAt: Foundation.Date? = nil,
            executionRole: Swift.String? = nil,
            executionTimeoutMinutes: Swift.Int? = 0,
            jobDriver: EMRServerlessClientTypes.JobDriver? = nil,
            jobRunId: Swift.String? = nil,
            mode: EMRServerlessClientTypes.JobRunMode? = nil,
            name: Swift.String? = nil,
            networkConfiguration: EMRServerlessClientTypes.NetworkConfiguration? = nil,
            queuedDurationMilliseconds: Swift.Int? = nil,
            releaseLabel: Swift.String? = nil,
            retryPolicy: EMRServerlessClientTypes.RetryPolicy? = nil,
            startedAt: Foundation.Date? = nil,
            state: EMRServerlessClientTypes.JobRunState? = nil,
            stateDetails: Swift.String? = nil,
            tags: [Swift.String: Swift.String]? = nil,
            totalExecutionDurationSeconds: Swift.Int? = nil,
            totalResourceUtilization: EMRServerlessClientTypes.TotalResourceUtilization? = nil,
            updatedAt: Foundation.Date? = nil
        )
        {
            self.applicationId = applicationId
            self.arn = arn
            self.attempt = attempt
            self.attemptCreatedAt = attemptCreatedAt
            self.attemptUpdatedAt = attemptUpdatedAt
            self.billedResourceUtilization = billedResourceUtilization
            self.configurationOverrides = configurationOverrides
            self.createdAt = createdAt
            self.createdBy = createdBy
            self.endedAt = endedAt
            self.executionRole = executionRole
            self.executionTimeoutMinutes = executionTimeoutMinutes
            self.jobDriver = jobDriver
            self.jobRunId = jobRunId
            self.mode = mode
            self.name = name
            self.networkConfiguration = networkConfiguration
            self.queuedDurationMilliseconds = queuedDurationMilliseconds
            self.releaseLabel = releaseLabel
            self.retryPolicy = retryPolicy
            self.startedAt = startedAt
            self.state = state
            self.stateDetails = stateDetails
            self.tags = tags
            self.totalExecutionDurationSeconds = totalExecutionDurationSeconds
            self.totalResourceUtilization = totalResourceUtilization
            self.updatedAt = updatedAt
        }
    }
}

public struct GetApplicationOutput: Swift.Sendable {
    /// The output displays information about the specified application.
    /// This member is required.
    public var application: EMRServerlessClientTypes.Application?

    public init(
        application: EMRServerlessClientTypes.Application? = nil
    )
    {
        self.application = application
    }
}

public struct StartJobRunInput: Swift.Sendable {
    /// The ID of the application on which to run the job.
    /// This member is required.
    public var applicationId: Swift.String?
    /// The client idempotency token of the job run to start. Its value must be unique for each request.
    /// This member is required.
    public var clientToken: Swift.String?
    /// The configuration overrides for the job run.
    public var configurationOverrides: EMRServerlessClientTypes.ConfigurationOverrides?
    /// The execution role ARN for the job run.
    /// This member is required.
    public var executionRoleArn: Swift.String?
    /// The maximum duration for the job run to run. If the job run runs beyond this duration, it will be automatically cancelled.
    public var executionTimeoutMinutes: Swift.Int?
    /// The job driver for the job run.
    public var jobDriver: EMRServerlessClientTypes.JobDriver?
    /// The mode of the job run when it starts.
    public var mode: EMRServerlessClientTypes.JobRunMode?
    /// The optional job run name. This doesn't have to be unique.
    public var name: Swift.String?
    /// The retry policy when job run starts.
    public var retryPolicy: EMRServerlessClientTypes.RetryPolicy?
    /// The tags assigned to the job run.
    public var tags: [Swift.String: Swift.String]?

    public init(
        applicationId: Swift.String? = nil,
        clientToken: Swift.String? = nil,
        configurationOverrides: EMRServerlessClientTypes.ConfigurationOverrides? = nil,
        executionRoleArn: Swift.String? = nil,
        executionTimeoutMinutes: Swift.Int? = 0,
        jobDriver: EMRServerlessClientTypes.JobDriver? = nil,
        mode: EMRServerlessClientTypes.JobRunMode? = nil,
        name: Swift.String? = nil,
        retryPolicy: EMRServerlessClientTypes.RetryPolicy? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.applicationId = applicationId
        self.clientToken = clientToken
        self.configurationOverrides = configurationOverrides
        self.executionRoleArn = executionRoleArn
        self.executionTimeoutMinutes = executionTimeoutMinutes
        self.jobDriver = jobDriver
        self.mode = mode
        self.name = name
        self.retryPolicy = retryPolicy
        self.tags = tags
    }
}

public struct UpdateApplicationOutput: Swift.Sendable {
    /// Information about the updated application.
    /// This member is required.
    public var application: EMRServerlessClientTypes.Application?

    public init(
        application: EMRServerlessClientTypes.Application? = nil
    )
    {
        self.application = application
    }
}

public struct GetJobRunOutput: Swift.Sendable {
    /// The output displays information about the job run.
    /// This member is required.
    public var jobRun: EMRServerlessClientTypes.JobRun?

    public init(
        jobRun: EMRServerlessClientTypes.JobRun? = nil
    )
    {
        self.jobRun = jobRun
    }
}

extension CancelJobRunInput {

    static func urlPathProvider(_ value: CancelJobRunInput) -> Swift.String? {
        guard let applicationId = value.applicationId else {
            return nil
        }
        guard let jobRunId = value.jobRunId else {
            return nil
        }
        return "/applications/\(applicationId.urlPercentEncoding())/jobruns/\(jobRunId.urlPercentEncoding())"
    }
}

extension CreateApplicationInput {

    static func urlPathProvider(_ value: CreateApplicationInput) -> Swift.String? {
        return "/applications"
    }
}

extension DeleteApplicationInput {

    static func urlPathProvider(_ value: DeleteApplicationInput) -> Swift.String? {
        guard let applicationId = value.applicationId else {
            return nil
        }
        return "/applications/\(applicationId.urlPercentEncoding())"
    }
}

extension GetApplicationInput {

    static func urlPathProvider(_ value: GetApplicationInput) -> Swift.String? {
        guard let applicationId = value.applicationId else {
            return nil
        }
        return "/applications/\(applicationId.urlPercentEncoding())"
    }
}

extension GetDashboardForJobRunInput {

    static func urlPathProvider(_ value: GetDashboardForJobRunInput) -> Swift.String? {
        guard let applicationId = value.applicationId else {
            return nil
        }
        guard let jobRunId = value.jobRunId else {
            return nil
        }
        return "/applications/\(applicationId.urlPercentEncoding())/jobruns/\(jobRunId.urlPercentEncoding())/dashboard"
    }
}

extension GetDashboardForJobRunInput {

    static func queryItemProvider(_ value: GetDashboardForJobRunInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let accessSystemProfileLogs = value.accessSystemProfileLogs {
            let accessSystemProfileLogsQueryItem = Smithy.URIQueryItem(name: "accessSystemProfileLogs".urlPercentEncoding(), value: Swift.String(accessSystemProfileLogs).urlPercentEncoding())
            items.append(accessSystemProfileLogsQueryItem)
        }
        if let attempt = value.attempt {
            let attemptQueryItem = Smithy.URIQueryItem(name: "attempt".urlPercentEncoding(), value: Swift.String(attempt).urlPercentEncoding())
            items.append(attemptQueryItem)
        }
        return items
    }
}

extension GetJobRunInput {

    static func urlPathProvider(_ value: GetJobRunInput) -> Swift.String? {
        guard let applicationId = value.applicationId else {
            return nil
        }
        guard let jobRunId = value.jobRunId else {
            return nil
        }
        return "/applications/\(applicationId.urlPercentEncoding())/jobruns/\(jobRunId.urlPercentEncoding())"
    }
}

extension GetJobRunInput {

    static func queryItemProvider(_ value: GetJobRunInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let attempt = value.attempt {
            let attemptQueryItem = Smithy.URIQueryItem(name: "attempt".urlPercentEncoding(), value: Swift.String(attempt).urlPercentEncoding())
            items.append(attemptQueryItem)
        }
        return items
    }
}

extension ListApplicationsInput {

    static func urlPathProvider(_ value: ListApplicationsInput) -> Swift.String? {
        return "/applications"
    }
}

extension ListApplicationsInput {

    static func queryItemProvider(_ value: ListApplicationsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let states = value.states {
            states.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "states".urlPercentEncoding(), value: Swift.String(queryItemValue.rawValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        return items
    }
}

extension ListJobRunAttemptsInput {

    static func urlPathProvider(_ value: ListJobRunAttemptsInput) -> Swift.String? {
        guard let applicationId = value.applicationId else {
            return nil
        }
        guard let jobRunId = value.jobRunId else {
            return nil
        }
        return "/applications/\(applicationId.urlPercentEncoding())/jobruns/\(jobRunId.urlPercentEncoding())/attempts"
    }
}

extension ListJobRunAttemptsInput {

    static func queryItemProvider(_ value: ListJobRunAttemptsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListJobRunsInput {

    static func urlPathProvider(_ value: ListJobRunsInput) -> Swift.String? {
        guard let applicationId = value.applicationId else {
            return nil
        }
        return "/applications/\(applicationId.urlPercentEncoding())/jobruns"
    }
}

extension ListJobRunsInput {

    static func queryItemProvider(_ value: ListJobRunsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let mode = value.mode {
            let modeQueryItem = Smithy.URIQueryItem(name: "mode".urlPercentEncoding(), value: Swift.String(mode.rawValue).urlPercentEncoding())
            items.append(modeQueryItem)
        }
        if let createdAtAfter = value.createdAtAfter {
            let createdAtAfterQueryItem = Smithy.URIQueryItem(name: "createdAtAfter".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: createdAtAfter)).urlPercentEncoding())
            items.append(createdAtAfterQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let createdAtBefore = value.createdAtBefore {
            let createdAtBeforeQueryItem = Smithy.URIQueryItem(name: "createdAtBefore".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: createdAtBefore)).urlPercentEncoding())
            items.append(createdAtBeforeQueryItem)
        }
        if let states = value.states {
            states.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "states".urlPercentEncoding(), value: Swift.String(queryItemValue.rawValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        return items
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension StartApplicationInput {

    static func urlPathProvider(_ value: StartApplicationInput) -> Swift.String? {
        guard let applicationId = value.applicationId else {
            return nil
        }
        return "/applications/\(applicationId.urlPercentEncoding())/start"
    }
}

extension StartJobRunInput {

    static func urlPathProvider(_ value: StartJobRunInput) -> Swift.String? {
        guard let applicationId = value.applicationId else {
            return nil
        }
        return "/applications/\(applicationId.urlPercentEncoding())/jobruns"
    }
}

extension StopApplicationInput {

    static func urlPathProvider(_ value: StopApplicationInput) -> Swift.String? {
        guard let applicationId = value.applicationId else {
            return nil
        }
        return "/applications/\(applicationId.urlPercentEncoding())/stop"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension UpdateApplicationInput {

    static func urlPathProvider(_ value: UpdateApplicationInput) -> Swift.String? {
        guard let applicationId = value.applicationId else {
            return nil
        }
        return "/applications/\(applicationId.urlPercentEncoding())"
    }
}

extension CreateApplicationInput {

    static func write(value: CreateApplicationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["architecture"].write(value.architecture)
        try writer["autoStartConfiguration"].write(value.autoStartConfiguration, with: EMRServerlessClientTypes.AutoStartConfig.write(value:to:))
        try writer["autoStopConfiguration"].write(value.autoStopConfiguration, with: EMRServerlessClientTypes.AutoStopConfig.write(value:to:))
        try writer["clientToken"].write(value.clientToken)
        try writer["imageConfiguration"].write(value.imageConfiguration, with: EMRServerlessClientTypes.ImageConfigurationInput.write(value:to:))
        try writer["initialCapacity"].writeMap(value.initialCapacity, valueWritingClosure: EMRServerlessClientTypes.InitialCapacityConfig.write(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["interactiveConfiguration"].write(value.interactiveConfiguration, with: EMRServerlessClientTypes.InteractiveConfiguration.write(value:to:))
        try writer["maximumCapacity"].write(value.maximumCapacity, with: EMRServerlessClientTypes.MaximumAllowedResources.write(value:to:))
        try writer["monitoringConfiguration"].write(value.monitoringConfiguration, with: EMRServerlessClientTypes.MonitoringConfiguration.write(value:to:))
        try writer["name"].write(value.name)
        try writer["networkConfiguration"].write(value.networkConfiguration, with: EMRServerlessClientTypes.NetworkConfiguration.write(value:to:))
        try writer["releaseLabel"].write(value.releaseLabel)
        try writer["runtimeConfiguration"].writeList(value.runtimeConfiguration, memberWritingClosure: EMRServerlessClientTypes.Configuration.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["schedulerConfiguration"].write(value.schedulerConfiguration, with: EMRServerlessClientTypes.SchedulerConfiguration.write(value:to:))
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["type"].write(value.type)
        try writer["workerTypeSpecifications"].writeMap(value.workerTypeSpecifications, valueWritingClosure: EMRServerlessClientTypes.WorkerTypeSpecificationInput.write(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension StartJobRunInput {

    static func write(value: StartJobRunInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["clientToken"].write(value.clientToken)
        try writer["configurationOverrides"].write(value.configurationOverrides, with: EMRServerlessClientTypes.ConfigurationOverrides.write(value:to:))
        try writer["executionRoleArn"].write(value.executionRoleArn)
        try writer["executionTimeoutMinutes"].write(value.executionTimeoutMinutes)
        try writer["jobDriver"].write(value.jobDriver, with: EMRServerlessClientTypes.JobDriver.write(value:to:))
        try writer["mode"].write(value.mode)
        try writer["name"].write(value.name)
        try writer["retryPolicy"].write(value.retryPolicy, with: EMRServerlessClientTypes.RetryPolicy.write(value:to:))
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension UpdateApplicationInput {

    static func write(value: UpdateApplicationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["architecture"].write(value.architecture)
        try writer["autoStartConfiguration"].write(value.autoStartConfiguration, with: EMRServerlessClientTypes.AutoStartConfig.write(value:to:))
        try writer["autoStopConfiguration"].write(value.autoStopConfiguration, with: EMRServerlessClientTypes.AutoStopConfig.write(value:to:))
        try writer["clientToken"].write(value.clientToken)
        try writer["imageConfiguration"].write(value.imageConfiguration, with: EMRServerlessClientTypes.ImageConfigurationInput.write(value:to:))
        try writer["initialCapacity"].writeMap(value.initialCapacity, valueWritingClosure: EMRServerlessClientTypes.InitialCapacityConfig.write(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["interactiveConfiguration"].write(value.interactiveConfiguration, with: EMRServerlessClientTypes.InteractiveConfiguration.write(value:to:))
        try writer["maximumCapacity"].write(value.maximumCapacity, with: EMRServerlessClientTypes.MaximumAllowedResources.write(value:to:))
        try writer["monitoringConfiguration"].write(value.monitoringConfiguration, with: EMRServerlessClientTypes.MonitoringConfiguration.write(value:to:))
        try writer["networkConfiguration"].write(value.networkConfiguration, with: EMRServerlessClientTypes.NetworkConfiguration.write(value:to:))
        try writer["releaseLabel"].write(value.releaseLabel)
        try writer["runtimeConfiguration"].writeList(value.runtimeConfiguration, memberWritingClosure: EMRServerlessClientTypes.Configuration.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["schedulerConfiguration"].write(value.schedulerConfiguration, with: EMRServerlessClientTypes.SchedulerConfiguration.write(value:to:))
        try writer["workerTypeSpecifications"].writeMap(value.workerTypeSpecifications, valueWritingClosure: EMRServerlessClientTypes.WorkerTypeSpecificationInput.write(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CancelJobRunOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CancelJobRunOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CancelJobRunOutput()
        value.applicationId = try reader["applicationId"].readIfPresent() ?? ""
        value.jobRunId = try reader["jobRunId"].readIfPresent() ?? ""
        return value
    }
}

extension CreateApplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateApplicationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateApplicationOutput()
        value.applicationId = try reader["applicationId"].readIfPresent() ?? ""
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent()
        return value
    }
}

extension DeleteApplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteApplicationOutput {
        return DeleteApplicationOutput()
    }
}

extension GetApplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetApplicationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetApplicationOutput()
        value.application = try reader["application"].readIfPresent(with: EMRServerlessClientTypes.Application.read(from:))
        return value
    }
}

extension GetDashboardForJobRunOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetDashboardForJobRunOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetDashboardForJobRunOutput()
        value.url = try reader["url"].readIfPresent()
        return value
    }
}

extension GetJobRunOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetJobRunOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetJobRunOutput()
        value.jobRun = try reader["jobRun"].readIfPresent(with: EMRServerlessClientTypes.JobRun.read(from:))
        return value
    }
}

extension ListApplicationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListApplicationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListApplicationsOutput()
        value.applications = try reader["applications"].readListIfPresent(memberReadingClosure: EMRServerlessClientTypes.ApplicationSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListJobRunAttemptsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListJobRunAttemptsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListJobRunAttemptsOutput()
        value.jobRunAttempts = try reader["jobRunAttempts"].readListIfPresent(memberReadingClosure: EMRServerlessClientTypes.JobRunAttemptSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListJobRunsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListJobRunsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListJobRunsOutput()
        value.jobRuns = try reader["jobRuns"].readListIfPresent(memberReadingClosure: EMRServerlessClientTypes.JobRunSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension StartApplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartApplicationOutput {
        return StartApplicationOutput()
    }
}

extension StartJobRunOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartJobRunOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartJobRunOutput()
        value.applicationId = try reader["applicationId"].readIfPresent() ?? ""
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.jobRunId = try reader["jobRunId"].readIfPresent() ?? ""
        return value
    }
}

extension StopApplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopApplicationOutput {
        return StopApplicationOutput()
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateApplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateApplicationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateApplicationOutput()
        value.application = try reader["application"].readIfPresent(with: EMRServerlessClientTypes.Application.read(from:))
        return value
    }
}

enum CancelJobRunOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateApplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteApplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetApplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetDashboardForJobRunOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetJobRunOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListApplicationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListJobRunAttemptsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListJobRunsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartApplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartJobRunOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopApplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateApplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceQuotaExceededException {
        let reader = baseError.errorBodyReader
        var value = ServiceQuotaExceededException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension EMRServerlessClientTypes.Application {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.Application {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.Application()
        value.applicationId = try reader["applicationId"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.releaseLabel = try reader["releaseLabel"].readIfPresent() ?? ""
        value.type = try reader["type"].readIfPresent() ?? ""
        value.state = try reader["state"].readIfPresent() ?? .sdkUnknown("")
        value.stateDetails = try reader["stateDetails"].readIfPresent()
        value.initialCapacity = try reader["initialCapacity"].readMapIfPresent(valueReadingClosure: EMRServerlessClientTypes.InitialCapacityConfig.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.maximumCapacity = try reader["maximumCapacity"].readIfPresent(with: EMRServerlessClientTypes.MaximumAllowedResources.read(from:))
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.updatedAt = try reader["updatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.autoStartConfiguration = try reader["autoStartConfiguration"].readIfPresent(with: EMRServerlessClientTypes.AutoStartConfig.read(from:))
        value.autoStopConfiguration = try reader["autoStopConfiguration"].readIfPresent(with: EMRServerlessClientTypes.AutoStopConfig.read(from:))
        value.networkConfiguration = try reader["networkConfiguration"].readIfPresent(with: EMRServerlessClientTypes.NetworkConfiguration.read(from:))
        value.architecture = try reader["architecture"].readIfPresent()
        value.imageConfiguration = try reader["imageConfiguration"].readIfPresent(with: EMRServerlessClientTypes.ImageConfiguration.read(from:))
        value.workerTypeSpecifications = try reader["workerTypeSpecifications"].readMapIfPresent(valueReadingClosure: EMRServerlessClientTypes.WorkerTypeSpecification.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.runtimeConfiguration = try reader["runtimeConfiguration"].readListIfPresent(memberReadingClosure: EMRServerlessClientTypes.Configuration.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.monitoringConfiguration = try reader["monitoringConfiguration"].readIfPresent(with: EMRServerlessClientTypes.MonitoringConfiguration.read(from:))
        value.interactiveConfiguration = try reader["interactiveConfiguration"].readIfPresent(with: EMRServerlessClientTypes.InteractiveConfiguration.read(from:))
        value.schedulerConfiguration = try reader["schedulerConfiguration"].readIfPresent(with: EMRServerlessClientTypes.SchedulerConfiguration.read(from:))
        return value
    }
}

extension EMRServerlessClientTypes.SchedulerConfiguration {

    static func write(value: EMRServerlessClientTypes.SchedulerConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxConcurrentRuns"].write(value.maxConcurrentRuns)
        try writer["queueTimeoutMinutes"].write(value.queueTimeoutMinutes)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.SchedulerConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.SchedulerConfiguration()
        value.queueTimeoutMinutes = try reader["queueTimeoutMinutes"].readIfPresent()
        value.maxConcurrentRuns = try reader["maxConcurrentRuns"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.InteractiveConfiguration {

    static func write(value: EMRServerlessClientTypes.InteractiveConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["livyEndpointEnabled"].write(value.livyEndpointEnabled)
        try writer["studioEnabled"].write(value.studioEnabled)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.InteractiveConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.InteractiveConfiguration()
        value.studioEnabled = try reader["studioEnabled"].readIfPresent()
        value.livyEndpointEnabled = try reader["livyEndpointEnabled"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.MonitoringConfiguration {

    static func write(value: EMRServerlessClientTypes.MonitoringConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["cloudWatchLoggingConfiguration"].write(value.cloudWatchLoggingConfiguration, with: EMRServerlessClientTypes.CloudWatchLoggingConfiguration.write(value:to:))
        try writer["managedPersistenceMonitoringConfiguration"].write(value.managedPersistenceMonitoringConfiguration, with: EMRServerlessClientTypes.ManagedPersistenceMonitoringConfiguration.write(value:to:))
        try writer["prometheusMonitoringConfiguration"].write(value.prometheusMonitoringConfiguration, with: EMRServerlessClientTypes.PrometheusMonitoringConfiguration.write(value:to:))
        try writer["s3MonitoringConfiguration"].write(value.s3MonitoringConfiguration, with: EMRServerlessClientTypes.S3MonitoringConfiguration.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.MonitoringConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.MonitoringConfiguration()
        value.s3MonitoringConfiguration = try reader["s3MonitoringConfiguration"].readIfPresent(with: EMRServerlessClientTypes.S3MonitoringConfiguration.read(from:))
        value.managedPersistenceMonitoringConfiguration = try reader["managedPersistenceMonitoringConfiguration"].readIfPresent(with: EMRServerlessClientTypes.ManagedPersistenceMonitoringConfiguration.read(from:))
        value.cloudWatchLoggingConfiguration = try reader["cloudWatchLoggingConfiguration"].readIfPresent(with: EMRServerlessClientTypes.CloudWatchLoggingConfiguration.read(from:))
        value.prometheusMonitoringConfiguration = try reader["prometheusMonitoringConfiguration"].readIfPresent(with: EMRServerlessClientTypes.PrometheusMonitoringConfiguration.read(from:))
        return value
    }
}

extension EMRServerlessClientTypes.PrometheusMonitoringConfiguration {

    static func write(value: EMRServerlessClientTypes.PrometheusMonitoringConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["remoteWriteUrl"].write(value.remoteWriteUrl)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.PrometheusMonitoringConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.PrometheusMonitoringConfiguration()
        value.remoteWriteUrl = try reader["remoteWriteUrl"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.CloudWatchLoggingConfiguration {

    static func write(value: EMRServerlessClientTypes.CloudWatchLoggingConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["enabled"].write(value.enabled)
        try writer["encryptionKeyArn"].write(value.encryptionKeyArn)
        try writer["logGroupName"].write(value.logGroupName)
        try writer["logStreamNamePrefix"].write(value.logStreamNamePrefix)
        try writer["logTypes"].writeMap(value.logTypes, valueWritingClosure: SmithyReadWrite.listWritingClosure(memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.CloudWatchLoggingConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.CloudWatchLoggingConfiguration()
        value.enabled = try reader["enabled"].readIfPresent() ?? false
        value.logGroupName = try reader["logGroupName"].readIfPresent()
        value.logStreamNamePrefix = try reader["logStreamNamePrefix"].readIfPresent()
        value.encryptionKeyArn = try reader["encryptionKeyArn"].readIfPresent()
        value.logTypes = try reader["logTypes"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.listReadingClosure(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension EMRServerlessClientTypes.ManagedPersistenceMonitoringConfiguration {

    static func write(value: EMRServerlessClientTypes.ManagedPersistenceMonitoringConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["enabled"].write(value.enabled)
        try writer["encryptionKeyArn"].write(value.encryptionKeyArn)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.ManagedPersistenceMonitoringConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.ManagedPersistenceMonitoringConfiguration()
        value.enabled = try reader["enabled"].readIfPresent()
        value.encryptionKeyArn = try reader["encryptionKeyArn"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.S3MonitoringConfiguration {

    static func write(value: EMRServerlessClientTypes.S3MonitoringConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["encryptionKeyArn"].write(value.encryptionKeyArn)
        try writer["logUri"].write(value.logUri)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.S3MonitoringConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.S3MonitoringConfiguration()
        value.logUri = try reader["logUri"].readIfPresent()
        value.encryptionKeyArn = try reader["encryptionKeyArn"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.Configuration {

    static func write(value: EMRServerlessClientTypes.Configuration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["classification"].write(value.classification)
        try writer["configurations"].writeList(value.configurations, memberWritingClosure: EMRServerlessClientTypes.Configuration.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["properties"].writeMap(value.properties, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.Configuration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.Configuration()
        value.classification = try reader["classification"].readIfPresent() ?? ""
        value.properties = try reader["properties"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.configurations = try reader["configurations"].readListIfPresent(memberReadingClosure: EMRServerlessClientTypes.Configuration.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension EMRServerlessClientTypes.WorkerTypeSpecification {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.WorkerTypeSpecification {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.WorkerTypeSpecification()
        value.imageConfiguration = try reader["imageConfiguration"].readIfPresent(with: EMRServerlessClientTypes.ImageConfiguration.read(from:))
        return value
    }
}

extension EMRServerlessClientTypes.ImageConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.ImageConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.ImageConfiguration()
        value.imageUri = try reader["imageUri"].readIfPresent() ?? ""
        value.resolvedImageDigest = try reader["resolvedImageDigest"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.NetworkConfiguration {

    static func write(value: EMRServerlessClientTypes.NetworkConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["securityGroupIds"].writeList(value.securityGroupIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["subnetIds"].writeList(value.subnetIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.NetworkConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.NetworkConfiguration()
        value.subnetIds = try reader["subnetIds"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.securityGroupIds = try reader["securityGroupIds"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension EMRServerlessClientTypes.AutoStopConfig {

    static func write(value: EMRServerlessClientTypes.AutoStopConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["enabled"].write(value.enabled)
        try writer["idleTimeoutMinutes"].write(value.idleTimeoutMinutes)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.AutoStopConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.AutoStopConfig()
        value.enabled = try reader["enabled"].readIfPresent()
        value.idleTimeoutMinutes = try reader["idleTimeoutMinutes"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.AutoStartConfig {

    static func write(value: EMRServerlessClientTypes.AutoStartConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["enabled"].write(value.enabled)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.AutoStartConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.AutoStartConfig()
        value.enabled = try reader["enabled"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.MaximumAllowedResources {

    static func write(value: EMRServerlessClientTypes.MaximumAllowedResources?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["cpu"].write(value.cpu)
        try writer["disk"].write(value.disk)
        try writer["memory"].write(value.memory)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.MaximumAllowedResources {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.MaximumAllowedResources()
        value.cpu = try reader["cpu"].readIfPresent() ?? ""
        value.memory = try reader["memory"].readIfPresent() ?? ""
        value.disk = try reader["disk"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.InitialCapacityConfig {

    static func write(value: EMRServerlessClientTypes.InitialCapacityConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["workerConfiguration"].write(value.workerConfiguration, with: EMRServerlessClientTypes.WorkerResourceConfig.write(value:to:))
        try writer["workerCount"].write(value.workerCount)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.InitialCapacityConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.InitialCapacityConfig()
        value.workerCount = try reader["workerCount"].readIfPresent() ?? 0
        value.workerConfiguration = try reader["workerConfiguration"].readIfPresent(with: EMRServerlessClientTypes.WorkerResourceConfig.read(from:))
        return value
    }
}

extension EMRServerlessClientTypes.WorkerResourceConfig {

    static func write(value: EMRServerlessClientTypes.WorkerResourceConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["cpu"].write(value.cpu)
        try writer["disk"].write(value.disk)
        try writer["diskType"].write(value.diskType)
        try writer["memory"].write(value.memory)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.WorkerResourceConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.WorkerResourceConfig()
        value.cpu = try reader["cpu"].readIfPresent() ?? ""
        value.memory = try reader["memory"].readIfPresent() ?? ""
        value.disk = try reader["disk"].readIfPresent()
        value.diskType = try reader["diskType"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.JobRun {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.JobRun {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.JobRun()
        value.applicationId = try reader["applicationId"].readIfPresent() ?? ""
        value.jobRunId = try reader["jobRunId"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.createdBy = try reader["createdBy"].readIfPresent() ?? ""
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.updatedAt = try reader["updatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.executionRole = try reader["executionRole"].readIfPresent() ?? ""
        value.state = try reader["state"].readIfPresent() ?? .sdkUnknown("")
        value.stateDetails = try reader["stateDetails"].readIfPresent() ?? ""
        value.releaseLabel = try reader["releaseLabel"].readIfPresent() ?? ""
        value.configurationOverrides = try reader["configurationOverrides"].readIfPresent(with: EMRServerlessClientTypes.ConfigurationOverrides.read(from:))
        value.jobDriver = try reader["jobDriver"].readIfPresent(with: EMRServerlessClientTypes.JobDriver.read(from:))
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.totalResourceUtilization = try reader["totalResourceUtilization"].readIfPresent(with: EMRServerlessClientTypes.TotalResourceUtilization.read(from:))
        value.networkConfiguration = try reader["networkConfiguration"].readIfPresent(with: EMRServerlessClientTypes.NetworkConfiguration.read(from:))
        value.totalExecutionDurationSeconds = try reader["totalExecutionDurationSeconds"].readIfPresent()
        value.executionTimeoutMinutes = try reader["executionTimeoutMinutes"].readIfPresent()
        value.billedResourceUtilization = try reader["billedResourceUtilization"].readIfPresent(with: EMRServerlessClientTypes.ResourceUtilization.read(from:))
        value.mode = try reader["mode"].readIfPresent()
        value.retryPolicy = try reader["retryPolicy"].readIfPresent(with: EMRServerlessClientTypes.RetryPolicy.read(from:))
        value.attempt = try reader["attempt"].readIfPresent()
        value.attemptCreatedAt = try reader["attemptCreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.attemptUpdatedAt = try reader["attemptUpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.startedAt = try reader["startedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.endedAt = try reader["endedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.queuedDurationMilliseconds = try reader["queuedDurationMilliseconds"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.RetryPolicy {

    static func write(value: EMRServerlessClientTypes.RetryPolicy?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxAttempts"].write(value.maxAttempts)
        try writer["maxFailedAttemptsPerHour"].write(value.maxFailedAttemptsPerHour)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.RetryPolicy {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.RetryPolicy()
        value.maxAttempts = try reader["maxAttempts"].readIfPresent()
        value.maxFailedAttemptsPerHour = try reader["maxFailedAttemptsPerHour"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.ResourceUtilization {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.ResourceUtilization {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.ResourceUtilization()
        value.vCPUHour = try reader["vCPUHour"].readIfPresent()
        value.memoryGBHour = try reader["memoryGBHour"].readIfPresent()
        value.storageGBHour = try reader["storageGBHour"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.TotalResourceUtilization {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.TotalResourceUtilization {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.TotalResourceUtilization()
        value.vCPUHour = try reader["vCPUHour"].readIfPresent()
        value.memoryGBHour = try reader["memoryGBHour"].readIfPresent()
        value.storageGBHour = try reader["storageGBHour"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.JobDriver {

    static func write(value: EMRServerlessClientTypes.JobDriver?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        switch value {
            case let .hive(hive):
                try writer["hive"].write(hive, with: EMRServerlessClientTypes.Hive.write(value:to:))
            case let .sparksubmit(sparksubmit):
                try writer["sparkSubmit"].write(sparksubmit, with: EMRServerlessClientTypes.SparkSubmit.write(value:to:))
            case let .sdkUnknown(sdkUnknown):
                try writer["sdkUnknown"].write(sdkUnknown)
        }
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.JobDriver {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "sparkSubmit":
                return .sparksubmit(try reader["sparkSubmit"].read(with: EMRServerlessClientTypes.SparkSubmit.read(from:)))
            case "hive":
                return .hive(try reader["hive"].read(with: EMRServerlessClientTypes.Hive.read(from:)))
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension EMRServerlessClientTypes.Hive {

    static func write(value: EMRServerlessClientTypes.Hive?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["initQueryFile"].write(value.initQueryFile)
        try writer["parameters"].write(value.parameters)
        try writer["query"].write(value.query)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.Hive {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.Hive()
        value.query = try reader["query"].readIfPresent() ?? ""
        value.initQueryFile = try reader["initQueryFile"].readIfPresent()
        value.parameters = try reader["parameters"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.SparkSubmit {

    static func write(value: EMRServerlessClientTypes.SparkSubmit?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["entryPoint"].write(value.entryPoint)
        try writer["entryPointArguments"].writeList(value.entryPointArguments, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["sparkSubmitParameters"].write(value.sparkSubmitParameters)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.SparkSubmit {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.SparkSubmit()
        value.entryPoint = try reader["entryPoint"].readIfPresent() ?? ""
        value.entryPointArguments = try reader["entryPointArguments"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.sparkSubmitParameters = try reader["sparkSubmitParameters"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.ConfigurationOverrides {

    static func write(value: EMRServerlessClientTypes.ConfigurationOverrides?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["applicationConfiguration"].writeList(value.applicationConfiguration, memberWritingClosure: EMRServerlessClientTypes.Configuration.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["monitoringConfiguration"].write(value.monitoringConfiguration, with: EMRServerlessClientTypes.MonitoringConfiguration.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.ConfigurationOverrides {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.ConfigurationOverrides()
        value.applicationConfiguration = try reader["applicationConfiguration"].readListIfPresent(memberReadingClosure: EMRServerlessClientTypes.Configuration.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.monitoringConfiguration = try reader["monitoringConfiguration"].readIfPresent(with: EMRServerlessClientTypes.MonitoringConfiguration.read(from:))
        return value
    }
}

extension EMRServerlessClientTypes.ApplicationSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.ApplicationSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.ApplicationSummary()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.releaseLabel = try reader["releaseLabel"].readIfPresent() ?? ""
        value.type = try reader["type"].readIfPresent() ?? ""
        value.state = try reader["state"].readIfPresent() ?? .sdkUnknown("")
        value.stateDetails = try reader["stateDetails"].readIfPresent()
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.updatedAt = try reader["updatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.architecture = try reader["architecture"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.JobRunAttemptSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.JobRunAttemptSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.JobRunAttemptSummary()
        value.applicationId = try reader["applicationId"].readIfPresent() ?? ""
        value.id = try reader["id"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent()
        value.mode = try reader["mode"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.createdBy = try reader["createdBy"].readIfPresent() ?? ""
        value.jobCreatedAt = try reader["jobCreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.updatedAt = try reader["updatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.executionRole = try reader["executionRole"].readIfPresent() ?? ""
        value.state = try reader["state"].readIfPresent() ?? .sdkUnknown("")
        value.stateDetails = try reader["stateDetails"].readIfPresent() ?? ""
        value.releaseLabel = try reader["releaseLabel"].readIfPresent() ?? ""
        value.type = try reader["type"].readIfPresent()
        value.attempt = try reader["attempt"].readIfPresent()
        return value
    }
}

extension EMRServerlessClientTypes.JobRunSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRServerlessClientTypes.JobRunSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRServerlessClientTypes.JobRunSummary()
        value.applicationId = try reader["applicationId"].readIfPresent() ?? ""
        value.id = try reader["id"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent()
        value.mode = try reader["mode"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.createdBy = try reader["createdBy"].readIfPresent() ?? ""
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.updatedAt = try reader["updatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.executionRole = try reader["executionRole"].readIfPresent() ?? ""
        value.state = try reader["state"].readIfPresent() ?? .sdkUnknown("")
        value.stateDetails = try reader["stateDetails"].readIfPresent() ?? ""
        value.releaseLabel = try reader["releaseLabel"].readIfPresent() ?? ""
        value.type = try reader["type"].readIfPresent()
        value.attempt = try reader["attempt"].readIfPresent()
        value.attemptCreatedAt = try reader["attemptCreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.attemptUpdatedAt = try reader["attemptUpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension EMRServerlessClientTypes.ImageConfigurationInput {

    static func write(value: EMRServerlessClientTypes.ImageConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["imageUri"].write(value.imageUri)
    }
}

extension EMRServerlessClientTypes.WorkerTypeSpecificationInput {

    static func write(value: EMRServerlessClientTypes.WorkerTypeSpecificationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["imageConfiguration"].write(value.imageConfiguration, with: EMRServerlessClientTypes.ImageConfigurationInput.write(value:to:))
    }
}

public enum EMRServerlessClientTypes {}
