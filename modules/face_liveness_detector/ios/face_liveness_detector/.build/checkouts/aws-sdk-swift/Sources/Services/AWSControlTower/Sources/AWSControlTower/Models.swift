//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import SmithyJSON
import class Smithy<PERSON><PERSON><PERSON>I.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.Document
import struct Smithy.URIQueryItem
@_spi(SmithyReadWrite) import struct SmithyReadWrite.WritingClosureBox

/// You do not have sufficient access to perform this action.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// An unexpected error occurred during processing of a request.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request references a resource that does not exist.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request was denied due to request throttling.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The ID of the service quota that was exceeded.
        public internal(set) var quotaCode: Swift.String? = nil
        /// The number of seconds the caller should wait before retrying.
        public internal(set) var retryAfterSeconds: Swift.Int? = nil
        /// The ID of the service that is associated with the error.
        public internal(set) var serviceCode: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { true }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        quotaCode: Swift.String? = nil,
        retryAfterSeconds: Swift.Int? = nil,
        serviceCode: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.quotaCode = quotaCode
        self.properties.retryAfterSeconds = retryAfterSeconds
        self.properties.serviceCode = serviceCode
    }
}

/// The input does not satisfy the constraints specified by an Amazon Web Services service.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct GetBaselineOperationInput: Swift.Sendable {
    /// The operation ID returned from mutating asynchronous APIs (Enable, Disable, Update, Reset).
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        operationIdentifier: Swift.String? = nil
    )
    {
        self.operationIdentifier = operationIdentifier
    }
}

extension ControlTowerClientTypes {

    public enum BaselineOperationType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disableBaseline
        case enableBaseline
        case resetEnabledBaseline
        case updateEnabledBaseline
        case sdkUnknown(Swift.String)

        public static var allCases: [BaselineOperationType] {
            return [
                .disableBaseline,
                .enableBaseline,
                .resetEnabledBaseline,
                .updateEnabledBaseline
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disableBaseline: return "DISABLE_BASELINE"
            case .enableBaseline: return "ENABLE_BASELINE"
            case .resetEnabledBaseline: return "RESET_ENABLED_BASELINE"
            case .updateEnabledBaseline: return "UPDATE_ENABLED_BASELINE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ControlTowerClientTypes {

    public enum BaselineOperationStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case inProgress
        case succeeded
        case sdkUnknown(Swift.String)

        public static var allCases: [BaselineOperationStatus] {
            return [
                .failed,
                .inProgress,
                .succeeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case .succeeded: return "SUCCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ControlTowerClientTypes {

    /// An object of shape BaselineOperation, returning details about the specified Baseline operation ID.
    public struct BaselineOperation: Swift.Sendable {
        /// The end time of the operation (if applicable), in ISO 8601 format.
        public var endTime: Foundation.Date?
        /// The identifier of the specified operation.
        public var operationIdentifier: Swift.String?
        /// An enumerated type (enum) with possible values of ENABLE_BASELINE, DISABLE_BASELINE, UPDATE_ENABLED_BASELINE, or RESET_ENABLED_BASELINE.
        public var operationType: ControlTowerClientTypes.BaselineOperationType?
        /// The start time of the operation, in ISO 8601 format.
        public var startTime: Foundation.Date?
        /// An enumerated type (enum) with possible values of SUCCEEDED, FAILED, or IN_PROGRESS.
        public var status: ControlTowerClientTypes.BaselineOperationStatus?
        /// A status message that gives more information about the operation's status, if applicable.
        public var statusMessage: Swift.String?

        public init(
            endTime: Foundation.Date? = nil,
            operationIdentifier: Swift.String? = nil,
            operationType: ControlTowerClientTypes.BaselineOperationType? = nil,
            startTime: Foundation.Date? = nil,
            status: ControlTowerClientTypes.BaselineOperationStatus? = nil,
            statusMessage: Swift.String? = nil
        )
        {
            self.endTime = endTime
            self.operationIdentifier = operationIdentifier
            self.operationType = operationType
            self.startTime = startTime
            self.status = status
            self.statusMessage = statusMessage
        }
    }
}

public struct GetBaselineOperationOutput: Swift.Sendable {
    /// A baselineOperation object that shows information about the specified operation ID.
    /// This member is required.
    public var baselineOperation: ControlTowerClientTypes.BaselineOperation?

    public init(
        baselineOperation: ControlTowerClientTypes.BaselineOperation? = nil
    )
    {
        self.baselineOperation = baselineOperation
    }
}

public struct GetBaselineInput: Swift.Sendable {
    /// The ARN of the Baseline resource to be retrieved.
    /// This member is required.
    public var baselineIdentifier: Swift.String?

    public init(
        baselineIdentifier: Swift.String? = nil
    )
    {
        self.baselineIdentifier = baselineIdentifier
    }
}

public struct GetBaselineOutput: Swift.Sendable {
    /// The baseline ARN.
    /// This member is required.
    public var arn: Swift.String?
    /// A description of the baseline.
    public var description: Swift.String?
    /// A user-friendly name for the baseline.
    /// This member is required.
    public var name: Swift.String?

    public init(
        arn: Swift.String? = nil,
        description: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.arn = arn
        self.description = description
        self.name = name
    }
}

public struct ListBaselinesInput: Swift.Sendable {
    /// The maximum number of results to be shown.
    public var maxResults: Swift.Int?
    /// A pagination token.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension ControlTowerClientTypes {

    /// Returns a summary of information about a Baseline object.
    public struct BaselineSummary: Swift.Sendable {
        /// The full ARN of a Baseline.
        /// This member is required.
        public var arn: Swift.String?
        /// A summary description of a Baseline.
        public var description: Swift.String?
        /// The human-readable name of a Baseline.
        /// This member is required.
        public var name: Swift.String?

        public init(
            arn: Swift.String? = nil,
            description: Swift.String? = nil,
            name: Swift.String? = nil
        )
        {
            self.arn = arn
            self.description = description
            self.name = name
        }
    }
}

public struct ListBaselinesOutput: Swift.Sendable {
    /// A list of Baseline object details.
    /// This member is required.
    public var baselines: [ControlTowerClientTypes.BaselineSummary]?
    /// A pagination token.
    public var nextToken: Swift.String?

    public init(
        baselines: [ControlTowerClientTypes.BaselineSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.baselines = baselines
        self.nextToken = nextToken
    }
}

public struct GetControlOperationInput: Swift.Sendable {
    /// The ID of the asynchronous operation, which is used to track status. The operation is available for 90 days.
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        operationIdentifier: Swift.String? = nil
    )
    {
        self.operationIdentifier = operationIdentifier
    }
}

extension ControlTowerClientTypes {

    public enum ControlOperationType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disableControl
        case enableControl
        case resetEnabledControl
        case updateEnabledControl
        case sdkUnknown(Swift.String)

        public static var allCases: [ControlOperationType] {
            return [
                .disableControl,
                .enableControl,
                .resetEnabledControl,
                .updateEnabledControl
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disableControl: return "DISABLE_CONTROL"
            case .enableControl: return "ENABLE_CONTROL"
            case .resetEnabledControl: return "RESET_ENABLED_CONTROL"
            case .updateEnabledControl: return "UPDATE_ENABLED_CONTROL"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ControlTowerClientTypes {

    public enum ControlOperationStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case inProgress
        case succeeded
        case sdkUnknown(Swift.String)

        public static var allCases: [ControlOperationStatus] {
            return [
                .failed,
                .inProgress,
                .succeeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case .succeeded: return "SUCCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ControlTowerClientTypes {

    /// An operation performed by the control.
    public struct ControlOperation: Swift.Sendable {
        /// The controlIdentifier of the control for the operation.
        public var controlIdentifier: Swift.String?
        /// The controlIdentifier of the enabled control.
        public var enabledControlIdentifier: Swift.String?
        /// The time that the operation finished.
        public var endTime: Foundation.Date?
        /// The identifier of the specified operation.
        public var operationIdentifier: Swift.String?
        /// One of ENABLE_CONTROL or DISABLE_CONTROL.
        public var operationType: ControlTowerClientTypes.ControlOperationType?
        /// The time that the operation began.
        public var startTime: Foundation.Date?
        /// One of IN_PROGRESS, SUCEEDED, or FAILED.
        public var status: ControlTowerClientTypes.ControlOperationStatus?
        /// If the operation result is FAILED, this string contains a message explaining why the operation failed.
        public var statusMessage: Swift.String?
        /// The target upon which the control operation is working.
        public var targetIdentifier: Swift.String?

        public init(
            controlIdentifier: Swift.String? = nil,
            enabledControlIdentifier: Swift.String? = nil,
            endTime: Foundation.Date? = nil,
            operationIdentifier: Swift.String? = nil,
            operationType: ControlTowerClientTypes.ControlOperationType? = nil,
            startTime: Foundation.Date? = nil,
            status: ControlTowerClientTypes.ControlOperationStatus? = nil,
            statusMessage: Swift.String? = nil,
            targetIdentifier: Swift.String? = nil
        )
        {
            self.controlIdentifier = controlIdentifier
            self.enabledControlIdentifier = enabledControlIdentifier
            self.endTime = endTime
            self.operationIdentifier = operationIdentifier
            self.operationType = operationType
            self.startTime = startTime
            self.status = status
            self.statusMessage = statusMessage
            self.targetIdentifier = targetIdentifier
        }
    }
}

public struct GetControlOperationOutput: Swift.Sendable {
    /// An operation performed by the control.
    /// This member is required.
    public var controlOperation: ControlTowerClientTypes.ControlOperation?

    public init(
        controlOperation: ControlTowerClientTypes.ControlOperation? = nil
    )
    {
        self.controlOperation = controlOperation
    }
}

extension ControlTowerClientTypes {

    /// A filter object that lets you call ListControlOperations with a specific filter.
    public struct ControlOperationFilter: Swift.Sendable {
        /// The set of controlIdentifier returned by the filter.
        public var controlIdentifiers: [Swift.String]?
        /// The set of ControlOperation objects returned by the filter.
        public var controlOperationTypes: [ControlTowerClientTypes.ControlOperationType]?
        /// The set controlIdentifier of enabled controls selected by the filter.
        public var enabledControlIdentifiers: [Swift.String]?
        /// Lists the status of control operations.
        public var statuses: [ControlTowerClientTypes.ControlOperationStatus]?
        /// The set of targetIdentifier objects returned by the filter.
        public var targetIdentifiers: [Swift.String]?

        public init(
            controlIdentifiers: [Swift.String]? = nil,
            controlOperationTypes: [ControlTowerClientTypes.ControlOperationType]? = nil,
            enabledControlIdentifiers: [Swift.String]? = nil,
            statuses: [ControlTowerClientTypes.ControlOperationStatus]? = nil,
            targetIdentifiers: [Swift.String]? = nil
        )
        {
            self.controlIdentifiers = controlIdentifiers
            self.controlOperationTypes = controlOperationTypes
            self.enabledControlIdentifiers = enabledControlIdentifiers
            self.statuses = statuses
            self.targetIdentifiers = targetIdentifiers
        }
    }
}

public struct ListControlOperationsInput: Swift.Sendable {
    /// An input filter for the ListControlOperations API that lets you select the types of control operations to view.
    public var filter: ControlTowerClientTypes.ControlOperationFilter?
    /// The maximum number of results to be shown.
    public var maxResults: Swift.Int?
    /// A pagination token.
    public var nextToken: Swift.String?

    public init(
        filter: ControlTowerClientTypes.ControlOperationFilter? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filter = filter
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension ControlTowerClientTypes {

    /// A summary of information about the specified control operation.
    public struct ControlOperationSummary: Swift.Sendable {
        /// The controlIdentifier of a control.
        public var controlIdentifier: Swift.String?
        /// The controlIdentifier of an enabled control.
        public var enabledControlIdentifier: Swift.String?
        /// The time at which the control operation was completed.
        public var endTime: Foundation.Date?
        /// The unique identifier of a control operation.
        public var operationIdentifier: Swift.String?
        /// The type of operation.
        public var operationType: ControlTowerClientTypes.ControlOperationType?
        /// The time at which a control operation began.
        public var startTime: Foundation.Date?
        /// The status of the specified control operation.
        public var status: ControlTowerClientTypes.ControlOperationStatus?
        /// A speficic message displayed as part of the control status.
        public var statusMessage: Swift.String?
        /// The unique identifier of the target of a control operation.
        public var targetIdentifier: Swift.String?

        public init(
            controlIdentifier: Swift.String? = nil,
            enabledControlIdentifier: Swift.String? = nil,
            endTime: Foundation.Date? = nil,
            operationIdentifier: Swift.String? = nil,
            operationType: ControlTowerClientTypes.ControlOperationType? = nil,
            startTime: Foundation.Date? = nil,
            status: ControlTowerClientTypes.ControlOperationStatus? = nil,
            statusMessage: Swift.String? = nil,
            targetIdentifier: Swift.String? = nil
        )
        {
            self.controlIdentifier = controlIdentifier
            self.enabledControlIdentifier = enabledControlIdentifier
            self.endTime = endTime
            self.operationIdentifier = operationIdentifier
            self.operationType = operationType
            self.startTime = startTime
            self.status = status
            self.statusMessage = statusMessage
            self.targetIdentifier = targetIdentifier
        }
    }
}

public struct ListControlOperationsOutput: Swift.Sendable {
    /// Returns a list of output from control operations.
    /// This member is required.
    public var controlOperations: [ControlTowerClientTypes.ControlOperationSummary]?
    /// A pagination token.
    public var nextToken: Swift.String?

    public init(
        controlOperations: [ControlTowerClientTypes.ControlOperationSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.controlOperations = controlOperations
        self.nextToken = nextToken
    }
}

/// Updating or deleting the resource can cause an inconsistent state.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request would cause a service quota to be exceeded. The limit is 10 concurrent operations.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceQuotaExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DisableControlInput: Swift.Sendable {
    /// The ARN of the control. Only Strongly recommended and Elective controls are permitted, with the exception of the Region deny control. For information on how to find the controlIdentifier, see [the overview page](https://docs.aws.amazon.com/controltower/latest/APIReference/Welcome.html).
    /// This member is required.
    public var controlIdentifier: Swift.String?
    /// The ARN of the organizational unit. For information on how to find the targetIdentifier, see [the overview page](https://docs.aws.amazon.com/controltower/latest/APIReference/Welcome.html).
    /// This member is required.
    public var targetIdentifier: Swift.String?

    public init(
        controlIdentifier: Swift.String? = nil,
        targetIdentifier: Swift.String? = nil
    )
    {
        self.controlIdentifier = controlIdentifier
        self.targetIdentifier = targetIdentifier
    }
}

public struct DisableControlOutput: Swift.Sendable {
    /// The ID of the asynchronous operation, which is used to track status. The operation is available for 90 days.
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        operationIdentifier: Swift.String? = nil
    )
    {
        self.operationIdentifier = operationIdentifier
    }
}

public struct DisableBaselineInput: Swift.Sendable {
    /// Identifier of the EnabledBaseline resource to be deactivated, in ARN format.
    /// This member is required.
    public var enabledBaselineIdentifier: Swift.String?

    public init(
        enabledBaselineIdentifier: Swift.String? = nil
    )
    {
        self.enabledBaselineIdentifier = enabledBaselineIdentifier
    }
}

public struct DisableBaselineOutput: Swift.Sendable {
    /// The ID (in UUID format) of the asynchronous DisableBaseline operation. This operationIdentifier is used to track status through calls to the GetBaselineOperation API.
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        operationIdentifier: Swift.String? = nil
    )
    {
        self.operationIdentifier = operationIdentifier
    }
}

extension ControlTowerClientTypes {

    /// A key-value parameter to an EnabledBaseline resource.
    public struct EnabledBaselineParameter: Swift.Sendable {
        /// A string denoting the parameter key.
        /// This member is required.
        public var key: Swift.String?
        /// A low-level Document object of any type (for example, a Java Object).
        /// This member is required.
        public var value: Smithy.Document?

        public init(
            key: Swift.String? = nil,
            value: Smithy.Document? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

public struct EnableBaselineInput: Swift.Sendable {
    /// The ARN of the baseline to be enabled.
    /// This member is required.
    public var baselineIdentifier: Swift.String?
    /// The specific version to be enabled of the specified baseline.
    /// This member is required.
    public var baselineVersion: Swift.String?
    /// A list of key-value objects that specify enablement parameters, where key is a string and value is a document of any type.
    public var parameters: [ControlTowerClientTypes.EnabledBaselineParameter]?
    /// Tags associated with input to EnableBaseline.
    public var tags: [Swift.String: Swift.String]?
    /// The ARN of the target on which the baseline will be enabled. Only OUs are supported as targets.
    /// This member is required.
    public var targetIdentifier: Swift.String?

    public init(
        baselineIdentifier: Swift.String? = nil,
        baselineVersion: Swift.String? = nil,
        parameters: [ControlTowerClientTypes.EnabledBaselineParameter]? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        targetIdentifier: Swift.String? = nil
    )
    {
        self.baselineIdentifier = baselineIdentifier
        self.baselineVersion = baselineVersion
        self.parameters = parameters
        self.tags = tags
        self.targetIdentifier = targetIdentifier
    }
}

public struct EnableBaselineOutput: Swift.Sendable {
    /// The ARN of the EnabledBaseline resource.
    /// This member is required.
    public var arn: Swift.String?
    /// The ID (in UUID format) of the asynchronous EnableBaseline operation. This operationIdentifier is used to track status through calls to the GetBaselineOperation API.
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        arn: Swift.String? = nil,
        operationIdentifier: Swift.String? = nil
    )
    {
        self.arn = arn
        self.operationIdentifier = operationIdentifier
    }
}

public struct GetEnabledBaselineInput: Swift.Sendable {
    /// Identifier of the EnabledBaseline resource to be retrieved, in ARN format.
    /// This member is required.
    public var enabledBaselineIdentifier: Swift.String?

    public init(
        enabledBaselineIdentifier: Swift.String? = nil
    )
    {
        self.enabledBaselineIdentifier = enabledBaselineIdentifier
    }
}

extension ControlTowerClientTypes {

    /// Summary of an applied parameter to an EnabledBaseline resource.
    public struct EnabledBaselineParameterSummary: Swift.Sendable {
        /// A string denoting the parameter key.
        /// This member is required.
        public var key: Swift.String?
        /// A low-level document object of any type (for example, a Java Object).
        /// This member is required.
        public var value: Smithy.Document?

        public init(
            key: Swift.String? = nil,
            value: Smithy.Document? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

extension ControlTowerClientTypes {

    public enum EnablementStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case succeeded
        case underChange
        case sdkUnknown(Swift.String)

        public static var allCases: [EnablementStatus] {
            return [
                .failed,
                .succeeded,
                .underChange
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .succeeded: return "SUCCEEDED"
            case .underChange: return "UNDER_CHANGE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ControlTowerClientTypes {

    /// The deployment summary of an EnabledControl or EnabledBaseline resource.
    public struct EnablementStatusSummary: Swift.Sendable {
        /// The last operation identifier for the enabled resource.
        public var lastOperationIdentifier: Swift.String?
        /// The deployment status of the enabled resource. Valid values:
        ///
        /// * SUCCEEDED: The EnabledControl or EnabledBaseline configuration was deployed successfully.
        ///
        /// * UNDER_CHANGE: The EnabledControl or EnabledBaseline configuration is changing.
        ///
        /// * FAILED: The EnabledControl or EnabledBaseline configuration failed to deploy.
        public var status: ControlTowerClientTypes.EnablementStatus?

        public init(
            lastOperationIdentifier: Swift.String? = nil,
            status: ControlTowerClientTypes.EnablementStatus? = nil
        )
        {
            self.lastOperationIdentifier = lastOperationIdentifier
            self.status = status
        }
    }
}

extension ControlTowerClientTypes {

    /// Details of the EnabledBaseline resource.
    public struct EnabledBaselineDetails: Swift.Sendable {
        /// The ARN of the EnabledBaseline resource.
        /// This member is required.
        public var arn: Swift.String?
        /// The specific Baseline enabled as part of the EnabledBaseline resource.
        /// This member is required.
        public var baselineIdentifier: Swift.String?
        /// The enabled version of the Baseline.
        public var baselineVersion: Swift.String?
        /// Shows the parameters that are applied when enabling this Baseline.
        public var parameters: [ControlTowerClientTypes.EnabledBaselineParameterSummary]?
        /// An ARN that represents the parent EnabledBaseline at the Organizational Unit (OU) level, from which the child EnabledBaseline inherits its configuration. The value is returned by GetEnabledBaseline.
        public var parentIdentifier: Swift.String?
        /// The deployment summary of an EnabledControl or EnabledBaseline resource.
        /// This member is required.
        public var statusSummary: ControlTowerClientTypes.EnablementStatusSummary?
        /// The target on which to enable the Baseline.
        /// This member is required.
        public var targetIdentifier: Swift.String?

        public init(
            arn: Swift.String? = nil,
            baselineIdentifier: Swift.String? = nil,
            baselineVersion: Swift.String? = nil,
            parameters: [ControlTowerClientTypes.EnabledBaselineParameterSummary]? = nil,
            parentIdentifier: Swift.String? = nil,
            statusSummary: ControlTowerClientTypes.EnablementStatusSummary? = nil,
            targetIdentifier: Swift.String? = nil
        )
        {
            self.arn = arn
            self.baselineIdentifier = baselineIdentifier
            self.baselineVersion = baselineVersion
            self.parameters = parameters
            self.parentIdentifier = parentIdentifier
            self.statusSummary = statusSummary
            self.targetIdentifier = targetIdentifier
        }
    }
}

public struct GetEnabledBaselineOutput: Swift.Sendable {
    /// Details of the EnabledBaseline resource.
    public var enabledBaselineDetails: ControlTowerClientTypes.EnabledBaselineDetails?

    public init(
        enabledBaselineDetails: ControlTowerClientTypes.EnabledBaselineDetails? = nil
    )
    {
        self.enabledBaselineDetails = enabledBaselineDetails
    }
}

extension ControlTowerClientTypes {

    /// A filter applied on the ListEnabledBaseline operation. Allowed filters are baselineIdentifiers and targetIdentifiers. The filter can be applied for either, or both.
    public struct EnabledBaselineFilter: Swift.Sendable {
        /// Identifiers for the Baseline objects returned as part of the filter operation.
        public var baselineIdentifiers: [Swift.String]?
        /// An optional filter that sets up a list of parentIdentifiers to filter the results of the ListEnabledBaseline output.
        public var parentIdentifiers: [Swift.String]?
        /// Identifiers for the targets of the Baseline filter operation.
        public var targetIdentifiers: [Swift.String]?

        public init(
            baselineIdentifiers: [Swift.String]? = nil,
            parentIdentifiers: [Swift.String]? = nil,
            targetIdentifiers: [Swift.String]? = nil
        )
        {
            self.baselineIdentifiers = baselineIdentifiers
            self.parentIdentifiers = parentIdentifiers
            self.targetIdentifiers = targetIdentifiers
        }
    }
}

public struct ListEnabledBaselinesInput: Swift.Sendable {
    /// A filter applied on the ListEnabledBaseline operation. Allowed filters are baselineIdentifiers and targetIdentifiers. The filter can be applied for either, or both.
    public var filter: ControlTowerClientTypes.EnabledBaselineFilter?
    /// A value that can be set to include the child enabled baselines in responses. The default value is false.
    public var includeChildren: Swift.Bool?
    /// The maximum number of results to be shown.
    public var maxResults: Swift.Int?
    /// A pagination token.
    public var nextToken: Swift.String?

    public init(
        filter: ControlTowerClientTypes.EnabledBaselineFilter? = nil,
        includeChildren: Swift.Bool? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filter = filter
        self.includeChildren = includeChildren
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension ControlTowerClientTypes {

    /// Returns a summary of information about an EnabledBaseline object.
    public struct EnabledBaselineSummary: Swift.Sendable {
        /// The ARN of the EnabledBaseline resource
        /// This member is required.
        public var arn: Swift.String?
        /// The specific baseline that is enabled as part of the EnabledBaseline resource.
        /// This member is required.
        public var baselineIdentifier: Swift.String?
        /// The enabled version of the baseline.
        public var baselineVersion: Swift.String?
        /// An ARN that represents an object returned by ListEnabledBaseline, to describe an enabled baseline.
        public var parentIdentifier: Swift.String?
        /// The deployment summary of an EnabledControl or EnabledBaseline resource.
        /// This member is required.
        public var statusSummary: ControlTowerClientTypes.EnablementStatusSummary?
        /// The target upon which the baseline is enabled.
        /// This member is required.
        public var targetIdentifier: Swift.String?

        public init(
            arn: Swift.String? = nil,
            baselineIdentifier: Swift.String? = nil,
            baselineVersion: Swift.String? = nil,
            parentIdentifier: Swift.String? = nil,
            statusSummary: ControlTowerClientTypes.EnablementStatusSummary? = nil,
            targetIdentifier: Swift.String? = nil
        )
        {
            self.arn = arn
            self.baselineIdentifier = baselineIdentifier
            self.baselineVersion = baselineVersion
            self.parentIdentifier = parentIdentifier
            self.statusSummary = statusSummary
            self.targetIdentifier = targetIdentifier
        }
    }
}

public struct ListEnabledBaselinesOutput: Swift.Sendable {
    /// Retuens a list of summaries of EnabledBaseline resources.
    /// This member is required.
    public var enabledBaselines: [ControlTowerClientTypes.EnabledBaselineSummary]?
    /// A pagination token.
    public var nextToken: Swift.String?

    public init(
        enabledBaselines: [ControlTowerClientTypes.EnabledBaselineSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.enabledBaselines = enabledBaselines
        self.nextToken = nextToken
    }
}

public struct ResetEnabledBaselineInput: Swift.Sendable {
    /// Specifies the ID of the EnabledBaseline resource to be re-enabled, in ARN format.
    /// This member is required.
    public var enabledBaselineIdentifier: Swift.String?

    public init(
        enabledBaselineIdentifier: Swift.String? = nil
    )
    {
        self.enabledBaselineIdentifier = enabledBaselineIdentifier
    }
}

public struct ResetEnabledBaselineOutput: Swift.Sendable {
    /// The ID (in UUID format) of the asynchronous ResetEnabledBaseline operation. This operationIdentifier is used to track status through calls to the GetBaselineOperation API.
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        operationIdentifier: Swift.String? = nil
    )
    {
        self.operationIdentifier = operationIdentifier
    }
}

public struct UpdateEnabledBaselineInput: Swift.Sendable {
    /// Specifies the new Baseline version, to which the EnabledBaseline should be updated.
    /// This member is required.
    public var baselineVersion: Swift.String?
    /// Specifies the EnabledBaseline resource to be updated.
    /// This member is required.
    public var enabledBaselineIdentifier: Swift.String?
    /// Parameters to apply when making an update.
    public var parameters: [ControlTowerClientTypes.EnabledBaselineParameter]?

    public init(
        baselineVersion: Swift.String? = nil,
        enabledBaselineIdentifier: Swift.String? = nil,
        parameters: [ControlTowerClientTypes.EnabledBaselineParameter]? = nil
    )
    {
        self.baselineVersion = baselineVersion
        self.enabledBaselineIdentifier = enabledBaselineIdentifier
        self.parameters = parameters
    }
}

public struct UpdateEnabledBaselineOutput: Swift.Sendable {
    /// The ID (in UUID format) of the asynchronous UpdateEnabledBaseline operation. This operationIdentifier is used to track status through calls to the GetBaselineOperation API.
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        operationIdentifier: Swift.String? = nil
    )
    {
        self.operationIdentifier = operationIdentifier
    }
}

extension ControlTowerClientTypes {

    /// A key/value pair, where Key is of type String and Value is of type Document.
    public struct EnabledControlParameter: Swift.Sendable {
        /// The key of a key/value pair.
        /// This member is required.
        public var key: Swift.String?
        /// The value of a key/value pair.
        /// This member is required.
        public var value: Smithy.Document?

        public init(
            key: Swift.String? = nil,
            value: Smithy.Document? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

public struct EnableControlInput: Swift.Sendable {
    /// The ARN of the control. Only Strongly recommended and Elective controls are permitted, with the exception of the Region deny control. For information on how to find the controlIdentifier, see [the overview page](https://docs.aws.amazon.com/controltower/latest/APIReference/Welcome.html).
    /// This member is required.
    public var controlIdentifier: Swift.String?
    /// A list of input parameter values, which are specified to configure the control when you enable it.
    public var parameters: [ControlTowerClientTypes.EnabledControlParameter]?
    /// Tags to be applied to the EnabledControl resource.
    public var tags: [Swift.String: Swift.String]?
    /// The ARN of the organizational unit. For information on how to find the targetIdentifier, see [the overview page](https://docs.aws.amazon.com/controltower/latest/APIReference/Welcome.html).
    /// This member is required.
    public var targetIdentifier: Swift.String?

    public init(
        controlIdentifier: Swift.String? = nil,
        parameters: [ControlTowerClientTypes.EnabledControlParameter]? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        targetIdentifier: Swift.String? = nil
    )
    {
        self.controlIdentifier = controlIdentifier
        self.parameters = parameters
        self.tags = tags
        self.targetIdentifier = targetIdentifier
    }
}

public struct EnableControlOutput: Swift.Sendable {
    /// The ARN of the EnabledControl resource.
    public var arn: Swift.String?
    /// The ID of the asynchronous operation, which is used to track status. The operation is available for 90 days.
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        arn: Swift.String? = nil,
        operationIdentifier: Swift.String? = nil
    )
    {
        self.arn = arn
        self.operationIdentifier = operationIdentifier
    }
}

public struct GetEnabledControlInput: Swift.Sendable {
    /// The controlIdentifier of the enabled control.
    /// This member is required.
    public var enabledControlIdentifier: Swift.String?

    public init(
        enabledControlIdentifier: Swift.String? = nil
    )
    {
        self.enabledControlIdentifier = enabledControlIdentifier
    }
}

extension ControlTowerClientTypes {

    public enum DriftStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case drifted
        case inSync
        case notChecking
        case unknown
        case sdkUnknown(Swift.String)

        public static var allCases: [DriftStatus] {
            return [
                .drifted,
                .inSync,
                .notChecking,
                .unknown
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .drifted: return "DRIFTED"
            case .inSync: return "IN_SYNC"
            case .notChecking: return "NOT_CHECKING"
            case .unknown: return "UNKNOWN"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ControlTowerClientTypes {

    /// The drift summary of the enabled control. Amazon Web Services Control Tower expects the enabled control configuration to include all supported and governed Regions. If the enabled control differs from the expected configuration, it is defined to be in a state of drift. You can repair this drift by resetting the enabled control.
    public struct DriftStatusSummary: Swift.Sendable {
        /// The drift status of the enabled control. Valid values:
        ///
        /// * DRIFTED: The enabledControl deployed in this configuration doesn’t match the configuration that Amazon Web Services Control Tower expected.
        ///
        /// * IN_SYNC: The enabledControl deployed in this configuration matches the configuration that Amazon Web Services Control Tower expected.
        ///
        /// * NOT_CHECKING: Amazon Web Services Control Tower does not check drift for this enabled control. Drift is not supported for the control type.
        ///
        /// * UNKNOWN: Amazon Web Services Control Tower is not able to check the drift status for the enabled control.
        public var driftStatus: ControlTowerClientTypes.DriftStatus?

        public init(
            driftStatus: ControlTowerClientTypes.DriftStatus? = nil
        )
        {
            self.driftStatus = driftStatus
        }
    }
}

extension ControlTowerClientTypes {

    /// Returns a summary of information about the parameters of an enabled control.
    public struct EnabledControlParameterSummary: Swift.Sendable {
        /// The key of a key/value pair.
        /// This member is required.
        public var key: Swift.String?
        /// The value of a key/value pair.
        /// This member is required.
        public var value: Smithy.Document?

        public init(
            key: Swift.String? = nil,
            value: Smithy.Document? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

extension ControlTowerClientTypes {

    /// An Amazon Web Services Region in which Amazon Web Services Control Tower expects to find the control deployed. The expected Regions are based on the Regions that are governed by the landing zone. In certain cases, a control is not actually enabled in the Region as expected, such as during drift, or [mixed governance](https://docs.aws.amazon.com/controltower/latest/userguide/region-how.html#mixed-governance).
    public struct Region: Swift.Sendable {
        /// The Amazon Web Services Region name.
        public var name: Swift.String?

        public init(
            name: Swift.String? = nil
        )
        {
            self.name = name
        }
    }
}

extension ControlTowerClientTypes {

    /// Information about the enabled control.
    public struct EnabledControlDetails: Swift.Sendable {
        /// The ARN of the enabled control.
        public var arn: Swift.String?
        /// The control identifier of the enabled control. For information on how to find the controlIdentifier, see [the overview page](https://docs.aws.amazon.com/controltower/latest/APIReference/Welcome.html).
        public var controlIdentifier: Swift.String?
        /// The drift status of the enabled control.
        public var driftStatusSummary: ControlTowerClientTypes.DriftStatusSummary?
        /// Array of EnabledControlParameter objects.
        public var parameters: [ControlTowerClientTypes.EnabledControlParameterSummary]?
        /// The deployment summary of the enabled control.
        public var statusSummary: ControlTowerClientTypes.EnablementStatusSummary?
        /// The ARN of the organizational unit. For information on how to find the targetIdentifier, see [the overview page](https://docs.aws.amazon.com/controltower/latest/APIReference/Welcome.html).
        public var targetIdentifier: Swift.String?
        /// Target Amazon Web Services Regions for the enabled control.
        public var targetRegions: [ControlTowerClientTypes.Region]?

        public init(
            arn: Swift.String? = nil,
            controlIdentifier: Swift.String? = nil,
            driftStatusSummary: ControlTowerClientTypes.DriftStatusSummary? = nil,
            parameters: [ControlTowerClientTypes.EnabledControlParameterSummary]? = nil,
            statusSummary: ControlTowerClientTypes.EnablementStatusSummary? = nil,
            targetIdentifier: Swift.String? = nil,
            targetRegions: [ControlTowerClientTypes.Region]? = nil
        )
        {
            self.arn = arn
            self.controlIdentifier = controlIdentifier
            self.driftStatusSummary = driftStatusSummary
            self.parameters = parameters
            self.statusSummary = statusSummary
            self.targetIdentifier = targetIdentifier
            self.targetRegions = targetRegions
        }
    }
}

public struct GetEnabledControlOutput: Swift.Sendable {
    /// Information about the enabled control.
    /// This member is required.
    public var enabledControlDetails: ControlTowerClientTypes.EnabledControlDetails?

    public init(
        enabledControlDetails: ControlTowerClientTypes.EnabledControlDetails? = nil
    )
    {
        self.enabledControlDetails = enabledControlDetails
    }
}

extension ControlTowerClientTypes {

    /// A structure that returns a set of control identifiers, the control status for each control in the set, and the drift status for each control in the set.
    public struct EnabledControlFilter: Swift.Sendable {
        /// The set of controlIdentifier returned by the filter.
        public var controlIdentifiers: [Swift.String]?
        /// A list of DriftStatus items.
        public var driftStatuses: [ControlTowerClientTypes.DriftStatus]?
        /// A list of EnablementStatus items.
        public var statuses: [ControlTowerClientTypes.EnablementStatus]?

        public init(
            controlIdentifiers: [Swift.String]? = nil,
            driftStatuses: [ControlTowerClientTypes.DriftStatus]? = nil,
            statuses: [ControlTowerClientTypes.EnablementStatus]? = nil
        )
        {
            self.controlIdentifiers = controlIdentifiers
            self.driftStatuses = driftStatuses
            self.statuses = statuses
        }
    }
}

public struct ListEnabledControlsInput: Swift.Sendable {
    /// An input filter for the ListEnabledControls API that lets you select the types of control operations to view.
    public var filter: ControlTowerClientTypes.EnabledControlFilter?
    /// How many results to return per API call.
    public var maxResults: Swift.Int?
    /// The token to continue the list from a previous API call with the same parameters.
    public var nextToken: Swift.String?
    /// The ARN of the organizational unit. For information on how to find the targetIdentifier, see [the overview page](https://docs.aws.amazon.com/controltower/latest/APIReference/Welcome.html).
    public var targetIdentifier: Swift.String?

    public init(
        filter: ControlTowerClientTypes.EnabledControlFilter? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        targetIdentifier: Swift.String? = nil
    )
    {
        self.filter = filter
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.targetIdentifier = targetIdentifier
    }
}

extension ControlTowerClientTypes {

    /// Returns a summary of information about an enabled control.
    public struct EnabledControlSummary: Swift.Sendable {
        /// The ARN of the enabled control.
        public var arn: Swift.String?
        /// The controlIdentifier of the enabled control.
        public var controlIdentifier: Swift.String?
        /// The drift status of the enabled control.
        public var driftStatusSummary: ControlTowerClientTypes.DriftStatusSummary?
        /// A short description of the status of the enabled control.
        public var statusSummary: ControlTowerClientTypes.EnablementStatusSummary?
        /// The ARN of the organizational unit.
        public var targetIdentifier: Swift.String?

        public init(
            arn: Swift.String? = nil,
            controlIdentifier: Swift.String? = nil,
            driftStatusSummary: ControlTowerClientTypes.DriftStatusSummary? = nil,
            statusSummary: ControlTowerClientTypes.EnablementStatusSummary? = nil,
            targetIdentifier: Swift.String? = nil
        )
        {
            self.arn = arn
            self.controlIdentifier = controlIdentifier
            self.driftStatusSummary = driftStatusSummary
            self.statusSummary = statusSummary
            self.targetIdentifier = targetIdentifier
        }
    }
}

public struct ListEnabledControlsOutput: Swift.Sendable {
    /// Lists the controls enabled by Amazon Web Services Control Tower on the specified organizational unit and the accounts it contains.
    /// This member is required.
    public var enabledControls: [ControlTowerClientTypes.EnabledControlSummary]?
    /// Retrieves the next page of results. If the string is empty, the response is the end of the results.
    public var nextToken: Swift.String?

    public init(
        enabledControls: [ControlTowerClientTypes.EnabledControlSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.enabledControls = enabledControls
        self.nextToken = nextToken
    }
}

public struct ResetEnabledControlInput: Swift.Sendable {
    /// The ARN of the enabled control to be reset.
    /// This member is required.
    public var enabledControlIdentifier: Swift.String?

    public init(
        enabledControlIdentifier: Swift.String? = nil
    )
    {
        self.enabledControlIdentifier = enabledControlIdentifier
    }
}

public struct ResetEnabledControlOutput: Swift.Sendable {
    /// The operation identifier for this ResetEnabledControl operation.
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        operationIdentifier: Swift.String? = nil
    )
    {
        self.operationIdentifier = operationIdentifier
    }
}

public struct UpdateEnabledControlInput: Swift.Sendable {
    /// The ARN of the enabled control that will be updated.
    /// This member is required.
    public var enabledControlIdentifier: Swift.String?
    /// A key/value pair, where Key is of type String and Value is of type Document.
    /// This member is required.
    public var parameters: [ControlTowerClientTypes.EnabledControlParameter]?

    public init(
        enabledControlIdentifier: Swift.String? = nil,
        parameters: [ControlTowerClientTypes.EnabledControlParameter]? = nil
    )
    {
        self.enabledControlIdentifier = enabledControlIdentifier
        self.parameters = parameters
    }
}

public struct UpdateEnabledControlOutput: Swift.Sendable {
    /// The operation identifier for this UpdateEnabledControl operation.
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        operationIdentifier: Swift.String? = nil
    )
    {
        self.operationIdentifier = operationIdentifier
    }
}

public struct GetLandingZoneOperationInput: Swift.Sendable {
    /// A unique identifier assigned to a landing zone operation.
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        operationIdentifier: Swift.String? = nil
    )
    {
        self.operationIdentifier = operationIdentifier
    }
}

extension ControlTowerClientTypes {

    public enum LandingZoneOperationType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case create
        case delete
        case reset
        case update
        case sdkUnknown(Swift.String)

        public static var allCases: [LandingZoneOperationType] {
            return [
                .create,
                .delete,
                .reset,
                .update
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .create: return "CREATE"
            case .delete: return "DELETE"
            case .reset: return "RESET"
            case .update: return "UPDATE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ControlTowerClientTypes {

    public enum LandingZoneOperationStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case inProgress
        case succeeded
        case sdkUnknown(Swift.String)

        public static var allCases: [LandingZoneOperationStatus] {
            return [
                .failed,
                .inProgress,
                .succeeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case .succeeded: return "SUCCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ControlTowerClientTypes {

    /// Information about a landing zone operation.
    public struct LandingZoneOperationDetail: Swift.Sendable {
        /// The landing zone operation end time.
        public var endTime: Foundation.Date?
        /// The operationIdentifier of the landing zone operation.
        public var operationIdentifier: Swift.String?
        /// The landing zone operation type. Valid values:
        ///
        /// * DELETE: The DeleteLandingZone operation.
        ///
        /// * CREATE: The CreateLandingZone operation.
        ///
        /// * UPDATE: The UpdateLandingZone operation.
        ///
        /// * RESET: The ResetLandingZone operation.
        public var operationType: ControlTowerClientTypes.LandingZoneOperationType?
        /// The landing zone operation start time.
        public var startTime: Foundation.Date?
        /// Valid values:
        ///
        /// * SUCCEEDED: The landing zone operation succeeded.
        ///
        /// * IN_PROGRESS: The landing zone operation is in progress.
        ///
        /// * FAILED: The landing zone operation failed.
        public var status: ControlTowerClientTypes.LandingZoneOperationStatus?
        /// If the operation result is FAILED, this string contains a message explaining why the operation failed.
        public var statusMessage: Swift.String?

        public init(
            endTime: Foundation.Date? = nil,
            operationIdentifier: Swift.String? = nil,
            operationType: ControlTowerClientTypes.LandingZoneOperationType? = nil,
            startTime: Foundation.Date? = nil,
            status: ControlTowerClientTypes.LandingZoneOperationStatus? = nil,
            statusMessage: Swift.String? = nil
        )
        {
            self.endTime = endTime
            self.operationIdentifier = operationIdentifier
            self.operationType = operationType
            self.startTime = startTime
            self.status = status
            self.statusMessage = statusMessage
        }
    }
}

public struct GetLandingZoneOperationOutput: Swift.Sendable {
    /// Details about a landing zone operation.
    /// This member is required.
    public var operationDetails: ControlTowerClientTypes.LandingZoneOperationDetail?

    public init(
        operationDetails: ControlTowerClientTypes.LandingZoneOperationDetail? = nil
    )
    {
        self.operationDetails = operationDetails
    }
}

extension ControlTowerClientTypes {

    /// A filter object that lets you call ListLandingZoneOperations with a specific filter.
    public struct LandingZoneOperationFilter: Swift.Sendable {
        /// The statuses of the set of landing zone operations selected by the filter.
        public var statuses: [ControlTowerClientTypes.LandingZoneOperationStatus]?
        /// The set of landing zone operation types selected by the filter.
        public var types: [ControlTowerClientTypes.LandingZoneOperationType]?

        public init(
            statuses: [ControlTowerClientTypes.LandingZoneOperationStatus]? = nil,
            types: [ControlTowerClientTypes.LandingZoneOperationType]? = nil
        )
        {
            self.statuses = statuses
            self.types = types
        }
    }
}

public struct ListLandingZoneOperationsInput: Swift.Sendable {
    /// An input filter for the ListLandingZoneOperations API that lets you select the types of landing zone operations to view.
    public var filter: ControlTowerClientTypes.LandingZoneOperationFilter?
    /// How many results to return per API call.
    public var maxResults: Swift.Int?
    /// The token to continue the list from a previous API call with the same parameters.
    public var nextToken: Swift.String?

    public init(
        filter: ControlTowerClientTypes.LandingZoneOperationFilter? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filter = filter
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension ControlTowerClientTypes {

    /// Returns a summary of information about a landing zone operation.
    public struct LandingZoneOperationSummary: Swift.Sendable {
        /// The operationIdentifier of the landing zone operation.
        public var operationIdentifier: Swift.String?
        /// The type of the landing zone operation.
        public var operationType: ControlTowerClientTypes.LandingZoneOperationType?
        /// The status of the landing zone operation.
        public var status: ControlTowerClientTypes.LandingZoneOperationStatus?

        public init(
            operationIdentifier: Swift.String? = nil,
            operationType: ControlTowerClientTypes.LandingZoneOperationType? = nil,
            status: ControlTowerClientTypes.LandingZoneOperationStatus? = nil
        )
        {
            self.operationIdentifier = operationIdentifier
            self.operationType = operationType
            self.status = status
        }
    }
}

public struct ListLandingZoneOperationsOutput: Swift.Sendable {
    /// Lists landing zone operations.
    /// This member is required.
    public var landingZoneOperations: [ControlTowerClientTypes.LandingZoneOperationSummary]?
    /// Retrieves the next page of results. If the string is empty, the response is the end of the results.
    public var nextToken: Swift.String?

    public init(
        landingZoneOperations: [ControlTowerClientTypes.LandingZoneOperationSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.landingZoneOperations = landingZoneOperations
        self.nextToken = nextToken
    }
}

public struct CreateLandingZoneInput: Swift.Sendable {
    /// The manifest JSON file is a text file that describes your Amazon Web Services resources. For examples, review [Launch your landing zone](https://docs.aws.amazon.com/controltower/latest/userguide/lz-api-launch).
    /// This member is required.
    public var manifest: Smithy.Document?
    /// Tags to be applied to the landing zone.
    public var tags: [Swift.String: Swift.String]?
    /// The landing zone version, for example, 3.0.
    /// This member is required.
    public var version: Swift.String?

    public init(
        manifest: Smithy.Document? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        version: Swift.String? = nil
    )
    {
        self.manifest = manifest
        self.tags = tags
        self.version = version
    }
}

public struct CreateLandingZoneOutput: Swift.Sendable {
    /// The ARN of the landing zone resource.
    /// This member is required.
    public var arn: Swift.String?
    /// A unique identifier assigned to a CreateLandingZone operation. You can use this identifier as an input of GetLandingZoneOperation to check the operation's status.
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        arn: Swift.String? = nil,
        operationIdentifier: Swift.String? = nil
    )
    {
        self.arn = arn
        self.operationIdentifier = operationIdentifier
    }
}

public struct DeleteLandingZoneInput: Swift.Sendable {
    /// The unique identifier of the landing zone.
    /// This member is required.
    public var landingZoneIdentifier: Swift.String?

    public init(
        landingZoneIdentifier: Swift.String? = nil
    )
    {
        self.landingZoneIdentifier = landingZoneIdentifier
    }
}

public struct DeleteLandingZoneOutput: Swift.Sendable {
    /// >A unique identifier assigned to a DeleteLandingZone operation. You can use this identifier as an input parameter of GetLandingZoneOperation to check the operation's status.
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        operationIdentifier: Swift.String? = nil
    )
    {
        self.operationIdentifier = operationIdentifier
    }
}

public struct GetLandingZoneInput: Swift.Sendable {
    /// The unique identifier of the landing zone.
    /// This member is required.
    public var landingZoneIdentifier: Swift.String?

    public init(
        landingZoneIdentifier: Swift.String? = nil
    )
    {
        self.landingZoneIdentifier = landingZoneIdentifier
    }
}

extension ControlTowerClientTypes {

    public enum LandingZoneDriftStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case drifted
        case inSync
        case sdkUnknown(Swift.String)

        public static var allCases: [LandingZoneDriftStatus] {
            return [
                .drifted,
                .inSync
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .drifted: return "DRIFTED"
            case .inSync: return "IN_SYNC"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ControlTowerClientTypes {

    /// The drift status summary of the landing zone. If the landing zone differs from the expected configuration, it is defined to be in a state of drift. You can repair this drift by resetting the landing zone.
    public struct LandingZoneDriftStatusSummary: Swift.Sendable {
        /// The drift status of the landing zone. Valid values:
        ///
        /// * DRIFTED: The landing zone deployed in this configuration does not match the configuration that Amazon Web Services Control Tower expected.
        ///
        /// * IN_SYNC: The landing zone deployed in this configuration matches the configuration that Amazon Web Services Control Tower expected.
        public var status: ControlTowerClientTypes.LandingZoneDriftStatus?

        public init(
            status: ControlTowerClientTypes.LandingZoneDriftStatus? = nil
        )
        {
            self.status = status
        }
    }
}

extension ControlTowerClientTypes {

    public enum LandingZoneStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case active
        case failed
        case processing
        case sdkUnknown(Swift.String)

        public static var allCases: [LandingZoneStatus] {
            return [
                .active,
                .failed,
                .processing
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .active: return "ACTIVE"
            case .failed: return "FAILED"
            case .processing: return "PROCESSING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ControlTowerClientTypes {

    /// Information about the landing zone.
    public struct LandingZoneDetail: Swift.Sendable {
        /// The ARN of the landing zone.
        public var arn: Swift.String?
        /// The drift status of the landing zone.
        public var driftStatus: ControlTowerClientTypes.LandingZoneDriftStatusSummary?
        /// The latest available version of the landing zone.
        public var latestAvailableVersion: Swift.String?
        /// The landing zone manifest JSON text file that specifies the landing zone configurations.
        /// This member is required.
        public var manifest: Smithy.Document?
        /// The landing zone deployment status. One of ACTIVE, PROCESSING, FAILED.
        public var status: ControlTowerClientTypes.LandingZoneStatus?
        /// The landing zone's current deployed version.
        /// This member is required.
        public var version: Swift.String?

        public init(
            arn: Swift.String? = nil,
            driftStatus: ControlTowerClientTypes.LandingZoneDriftStatusSummary? = nil,
            latestAvailableVersion: Swift.String? = nil,
            manifest: Smithy.Document? = nil,
            status: ControlTowerClientTypes.LandingZoneStatus? = nil,
            version: Swift.String? = nil
        )
        {
            self.arn = arn
            self.driftStatus = driftStatus
            self.latestAvailableVersion = latestAvailableVersion
            self.manifest = manifest
            self.status = status
            self.version = version
        }
    }
}

public struct GetLandingZoneOutput: Swift.Sendable {
    /// Information about the landing zone.
    /// This member is required.
    public var landingZone: ControlTowerClientTypes.LandingZoneDetail?

    public init(
        landingZone: ControlTowerClientTypes.LandingZoneDetail? = nil
    )
    {
        self.landingZone = landingZone
    }
}

public struct ListLandingZonesInput: Swift.Sendable {
    /// The maximum number of returned landing zone ARNs, which is one.
    public var maxResults: Swift.Int?
    /// The token to continue the list from a previous API call with the same parameters.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension ControlTowerClientTypes {

    /// Returns a summary of information about a landing zone.
    public struct LandingZoneSummary: Swift.Sendable {
        /// The ARN of the landing zone.
        public var arn: Swift.String?

        public init(
            arn: Swift.String? = nil
        )
        {
            self.arn = arn
        }
    }
}

public struct ListLandingZonesOutput: Swift.Sendable {
    /// The ARN of the landing zone.
    /// This member is required.
    public var landingZones: [ControlTowerClientTypes.LandingZoneSummary]?
    /// Retrieves the next page of results. If the string is empty, the response is the end of the results.
    public var nextToken: Swift.String?

    public init(
        landingZones: [ControlTowerClientTypes.LandingZoneSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.landingZones = landingZones
        self.nextToken = nextToken
    }
}

public struct ResetLandingZoneInput: Swift.Sendable {
    /// The unique identifier of the landing zone.
    /// This member is required.
    public var landingZoneIdentifier: Swift.String?

    public init(
        landingZoneIdentifier: Swift.String? = nil
    )
    {
        self.landingZoneIdentifier = landingZoneIdentifier
    }
}

public struct ResetLandingZoneOutput: Swift.Sendable {
    /// A unique identifier assigned to a ResetLandingZone operation. You can use this identifier as an input parameter of GetLandingZoneOperation to check the operation's status.
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        operationIdentifier: Swift.String? = nil
    )
    {
        self.operationIdentifier = operationIdentifier
    }
}

public struct UpdateLandingZoneInput: Swift.Sendable {
    /// The unique identifier of the landing zone.
    /// This member is required.
    public var landingZoneIdentifier: Swift.String?
    /// The manifest file (JSON) is a text file that describes your Amazon Web Services resources. For an example, review [Launch your landing zone](https://docs.aws.amazon.com/controltower/latest/userguide/lz-api-launch). The example manifest file contains each of the available parameters. The schema for the landing zone's JSON manifest file is not published, by design.
    /// This member is required.
    public var manifest: Smithy.Document?
    /// The landing zone version, for example, 3.2.
    /// This member is required.
    public var version: Swift.String?

    public init(
        landingZoneIdentifier: Swift.String? = nil,
        manifest: Smithy.Document? = nil,
        version: Swift.String? = nil
    )
    {
        self.landingZoneIdentifier = landingZoneIdentifier
        self.manifest = manifest
        self.version = version
    }
}

public struct UpdateLandingZoneOutput: Swift.Sendable {
    /// A unique identifier assigned to a UpdateLandingZone operation. You can use this identifier as an input of GetLandingZoneOperation to check the operation's status.
    /// This member is required.
    public var operationIdentifier: Swift.String?

    public init(
        operationIdentifier: Swift.String? = nil
    )
    {
        self.operationIdentifier = operationIdentifier
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The ARN of the resource.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// A list of tags, as key:value strings.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The ARN of the resource to be tagged.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// Tags to be applied to the resource.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The ARN of the resource.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// Tag keys to be removed from the resource.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

extension CreateLandingZoneInput {

    static func urlPathProvider(_ value: CreateLandingZoneInput) -> Swift.String? {
        return "/create-landingzone"
    }
}

extension DeleteLandingZoneInput {

    static func urlPathProvider(_ value: DeleteLandingZoneInput) -> Swift.String? {
        return "/delete-landingzone"
    }
}

extension DisableBaselineInput {

    static func urlPathProvider(_ value: DisableBaselineInput) -> Swift.String? {
        return "/disable-baseline"
    }
}

extension DisableControlInput {

    static func urlPathProvider(_ value: DisableControlInput) -> Swift.String? {
        return "/disable-control"
    }
}

extension EnableBaselineInput {

    static func urlPathProvider(_ value: EnableBaselineInput) -> Swift.String? {
        return "/enable-baseline"
    }
}

extension EnableControlInput {

    static func urlPathProvider(_ value: EnableControlInput) -> Swift.String? {
        return "/enable-control"
    }
}

extension GetBaselineInput {

    static func urlPathProvider(_ value: GetBaselineInput) -> Swift.String? {
        return "/get-baseline"
    }
}

extension GetBaselineOperationInput {

    static func urlPathProvider(_ value: GetBaselineOperationInput) -> Swift.String? {
        return "/get-baseline-operation"
    }
}

extension GetControlOperationInput {

    static func urlPathProvider(_ value: GetControlOperationInput) -> Swift.String? {
        return "/get-control-operation"
    }
}

extension GetEnabledBaselineInput {

    static func urlPathProvider(_ value: GetEnabledBaselineInput) -> Swift.String? {
        return "/get-enabled-baseline"
    }
}

extension GetEnabledControlInput {

    static func urlPathProvider(_ value: GetEnabledControlInput) -> Swift.String? {
        return "/get-enabled-control"
    }
}

extension GetLandingZoneInput {

    static func urlPathProvider(_ value: GetLandingZoneInput) -> Swift.String? {
        return "/get-landingzone"
    }
}

extension GetLandingZoneOperationInput {

    static func urlPathProvider(_ value: GetLandingZoneOperationInput) -> Swift.String? {
        return "/get-landingzone-operation"
    }
}

extension ListBaselinesInput {

    static func urlPathProvider(_ value: ListBaselinesInput) -> Swift.String? {
        return "/list-baselines"
    }
}

extension ListControlOperationsInput {

    static func urlPathProvider(_ value: ListControlOperationsInput) -> Swift.String? {
        return "/list-control-operations"
    }
}

extension ListEnabledBaselinesInput {

    static func urlPathProvider(_ value: ListEnabledBaselinesInput) -> Swift.String? {
        return "/list-enabled-baselines"
    }
}

extension ListEnabledControlsInput {

    static func urlPathProvider(_ value: ListEnabledControlsInput) -> Swift.String? {
        return "/list-enabled-controls"
    }
}

extension ListLandingZoneOperationsInput {

    static func urlPathProvider(_ value: ListLandingZoneOperationsInput) -> Swift.String? {
        return "/list-landingzone-operations"
    }
}

extension ListLandingZonesInput {

    static func urlPathProvider(_ value: ListLandingZonesInput) -> Swift.String? {
        return "/list-landingzones"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension ResetEnabledBaselineInput {

    static func urlPathProvider(_ value: ResetEnabledBaselineInput) -> Swift.String? {
        return "/reset-enabled-baseline"
    }
}

extension ResetEnabledControlInput {

    static func urlPathProvider(_ value: ResetEnabledControlInput) -> Swift.String? {
        return "/reset-enabled-control"
    }
}

extension ResetLandingZoneInput {

    static func urlPathProvider(_ value: ResetLandingZoneInput) -> Swift.String? {
        return "/reset-landingzone"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension UpdateEnabledBaselineInput {

    static func urlPathProvider(_ value: UpdateEnabledBaselineInput) -> Swift.String? {
        return "/update-enabled-baseline"
    }
}

extension UpdateEnabledControlInput {

    static func urlPathProvider(_ value: UpdateEnabledControlInput) -> Swift.String? {
        return "/update-enabled-control"
    }
}

extension UpdateLandingZoneInput {

    static func urlPathProvider(_ value: UpdateLandingZoneInput) -> Swift.String? {
        return "/update-landingzone"
    }
}

extension CreateLandingZoneInput {

    static func write(value: CreateLandingZoneInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["manifest"].write(value.manifest)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["version"].write(value.version)
    }
}

extension DeleteLandingZoneInput {

    static func write(value: DeleteLandingZoneInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["landingZoneIdentifier"].write(value.landingZoneIdentifier)
    }
}

extension DisableBaselineInput {

    static func write(value: DisableBaselineInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["enabledBaselineIdentifier"].write(value.enabledBaselineIdentifier)
    }
}

extension DisableControlInput {

    static func write(value: DisableControlInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["controlIdentifier"].write(value.controlIdentifier)
        try writer["targetIdentifier"].write(value.targetIdentifier)
    }
}

extension EnableBaselineInput {

    static func write(value: EnableBaselineInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["baselineIdentifier"].write(value.baselineIdentifier)
        try writer["baselineVersion"].write(value.baselineVersion)
        try writer["parameters"].writeList(value.parameters, memberWritingClosure: ControlTowerClientTypes.EnabledBaselineParameter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["targetIdentifier"].write(value.targetIdentifier)
    }
}

extension EnableControlInput {

    static func write(value: EnableControlInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["controlIdentifier"].write(value.controlIdentifier)
        try writer["parameters"].writeList(value.parameters, memberWritingClosure: ControlTowerClientTypes.EnabledControlParameter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["targetIdentifier"].write(value.targetIdentifier)
    }
}

extension GetBaselineInput {

    static func write(value: GetBaselineInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["baselineIdentifier"].write(value.baselineIdentifier)
    }
}

extension GetBaselineOperationInput {

    static func write(value: GetBaselineOperationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["operationIdentifier"].write(value.operationIdentifier)
    }
}

extension GetControlOperationInput {

    static func write(value: GetControlOperationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["operationIdentifier"].write(value.operationIdentifier)
    }
}

extension GetEnabledBaselineInput {

    static func write(value: GetEnabledBaselineInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["enabledBaselineIdentifier"].write(value.enabledBaselineIdentifier)
    }
}

extension GetEnabledControlInput {

    static func write(value: GetEnabledControlInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["enabledControlIdentifier"].write(value.enabledControlIdentifier)
    }
}

extension GetLandingZoneInput {

    static func write(value: GetLandingZoneInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["landingZoneIdentifier"].write(value.landingZoneIdentifier)
    }
}

extension GetLandingZoneOperationInput {

    static func write(value: GetLandingZoneOperationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["operationIdentifier"].write(value.operationIdentifier)
    }
}

extension ListBaselinesInput {

    static func write(value: ListBaselinesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension ListControlOperationsInput {

    static func write(value: ListControlOperationsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filter"].write(value.filter, with: ControlTowerClientTypes.ControlOperationFilter.write(value:to:))
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension ListEnabledBaselinesInput {

    static func write(value: ListEnabledBaselinesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filter"].write(value.filter, with: ControlTowerClientTypes.EnabledBaselineFilter.write(value:to:))
        try writer["includeChildren"].write(value.includeChildren)
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension ListEnabledControlsInput {

    static func write(value: ListEnabledControlsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filter"].write(value.filter, with: ControlTowerClientTypes.EnabledControlFilter.write(value:to:))
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["targetIdentifier"].write(value.targetIdentifier)
    }
}

extension ListLandingZoneOperationsInput {

    static func write(value: ListLandingZoneOperationsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filter"].write(value.filter, with: ControlTowerClientTypes.LandingZoneOperationFilter.write(value:to:))
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension ListLandingZonesInput {

    static func write(value: ListLandingZonesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension ResetEnabledBaselineInput {

    static func write(value: ResetEnabledBaselineInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["enabledBaselineIdentifier"].write(value.enabledBaselineIdentifier)
    }
}

extension ResetEnabledControlInput {

    static func write(value: ResetEnabledControlInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["enabledControlIdentifier"].write(value.enabledControlIdentifier)
    }
}

extension ResetLandingZoneInput {

    static func write(value: ResetLandingZoneInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["landingZoneIdentifier"].write(value.landingZoneIdentifier)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension UpdateEnabledBaselineInput {

    static func write(value: UpdateEnabledBaselineInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["baselineVersion"].write(value.baselineVersion)
        try writer["enabledBaselineIdentifier"].write(value.enabledBaselineIdentifier)
        try writer["parameters"].writeList(value.parameters, memberWritingClosure: ControlTowerClientTypes.EnabledBaselineParameter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdateEnabledControlInput {

    static func write(value: UpdateEnabledControlInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["enabledControlIdentifier"].write(value.enabledControlIdentifier)
        try writer["parameters"].writeList(value.parameters, memberWritingClosure: ControlTowerClientTypes.EnabledControlParameter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdateLandingZoneInput {

    static func write(value: UpdateLandingZoneInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["landingZoneIdentifier"].write(value.landingZoneIdentifier)
        try writer["manifest"].write(value.manifest)
        try writer["version"].write(value.version)
    }
}

extension CreateLandingZoneOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLandingZoneOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateLandingZoneOutput()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent() ?? ""
        return value
    }
}

extension DeleteLandingZoneOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteLandingZoneOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteLandingZoneOutput()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent() ?? ""
        return value
    }
}

extension DisableBaselineOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisableBaselineOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DisableBaselineOutput()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent() ?? ""
        return value
    }
}

extension DisableControlOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisableControlOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DisableControlOutput()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent() ?? ""
        return value
    }
}

extension EnableBaselineOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> EnableBaselineOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = EnableBaselineOutput()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent() ?? ""
        return value
    }
}

extension EnableControlOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> EnableControlOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = EnableControlOutput()
        value.arn = try reader["arn"].readIfPresent()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent() ?? ""
        return value
    }
}

extension GetBaselineOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetBaselineOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetBaselineOutput()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.description = try reader["description"].readIfPresent()
        value.name = try reader["name"].readIfPresent() ?? ""
        return value
    }
}

extension GetBaselineOperationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetBaselineOperationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetBaselineOperationOutput()
        value.baselineOperation = try reader["baselineOperation"].readIfPresent(with: ControlTowerClientTypes.BaselineOperation.read(from:))
        return value
    }
}

extension GetControlOperationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetControlOperationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetControlOperationOutput()
        value.controlOperation = try reader["controlOperation"].readIfPresent(with: ControlTowerClientTypes.ControlOperation.read(from:))
        return value
    }
}

extension GetEnabledBaselineOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetEnabledBaselineOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetEnabledBaselineOutput()
        value.enabledBaselineDetails = try reader["enabledBaselineDetails"].readIfPresent(with: ControlTowerClientTypes.EnabledBaselineDetails.read(from:))
        return value
    }
}

extension GetEnabledControlOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetEnabledControlOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetEnabledControlOutput()
        value.enabledControlDetails = try reader["enabledControlDetails"].readIfPresent(with: ControlTowerClientTypes.EnabledControlDetails.read(from:))
        return value
    }
}

extension GetLandingZoneOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetLandingZoneOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetLandingZoneOutput()
        value.landingZone = try reader["landingZone"].readIfPresent(with: ControlTowerClientTypes.LandingZoneDetail.read(from:))
        return value
    }
}

extension GetLandingZoneOperationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetLandingZoneOperationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetLandingZoneOperationOutput()
        value.operationDetails = try reader["operationDetails"].readIfPresent(with: ControlTowerClientTypes.LandingZoneOperationDetail.read(from:))
        return value
    }
}

extension ListBaselinesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListBaselinesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListBaselinesOutput()
        value.baselines = try reader["baselines"].readListIfPresent(memberReadingClosure: ControlTowerClientTypes.BaselineSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListControlOperationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListControlOperationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListControlOperationsOutput()
        value.controlOperations = try reader["controlOperations"].readListIfPresent(memberReadingClosure: ControlTowerClientTypes.ControlOperationSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListEnabledBaselinesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListEnabledBaselinesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListEnabledBaselinesOutput()
        value.enabledBaselines = try reader["enabledBaselines"].readListIfPresent(memberReadingClosure: ControlTowerClientTypes.EnabledBaselineSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListEnabledControlsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListEnabledControlsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListEnabledControlsOutput()
        value.enabledControls = try reader["enabledControls"].readListIfPresent(memberReadingClosure: ControlTowerClientTypes.EnabledControlSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListLandingZoneOperationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListLandingZoneOperationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListLandingZoneOperationsOutput()
        value.landingZoneOperations = try reader["landingZoneOperations"].readListIfPresent(memberReadingClosure: ControlTowerClientTypes.LandingZoneOperationSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListLandingZonesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListLandingZonesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListLandingZonesOutput()
        value.landingZones = try reader["landingZones"].readListIfPresent(memberReadingClosure: ControlTowerClientTypes.LandingZoneSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false) ?? [:]
        return value
    }
}

extension ResetEnabledBaselineOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ResetEnabledBaselineOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ResetEnabledBaselineOutput()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent() ?? ""
        return value
    }
}

extension ResetEnabledControlOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ResetEnabledControlOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ResetEnabledControlOutput()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent() ?? ""
        return value
    }
}

extension ResetLandingZoneOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ResetLandingZoneOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ResetLandingZoneOutput()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent() ?? ""
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateEnabledBaselineOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateEnabledBaselineOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateEnabledBaselineOutput()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent() ?? ""
        return value
    }
}

extension UpdateEnabledControlOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateEnabledControlOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateEnabledControlOutput()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent() ?? ""
        return value
    }
}

extension UpdateLandingZoneOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLandingZoneOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateLandingZoneOutput()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent() ?? ""
        return value
    }
}

enum CreateLandingZoneOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteLandingZoneOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisableBaselineOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisableControlOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum EnableBaselineOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum EnableControlOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetBaselineOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetBaselineOperationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetControlOperationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetEnabledBaselineOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetEnabledControlOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetLandingZoneOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetLandingZoneOperationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListBaselinesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListControlOperationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListEnabledBaselinesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListEnabledControlsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListLandingZoneOperationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListLandingZonesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ResetEnabledBaselineOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ResetEnabledControlOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ResetLandingZoneOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateEnabledBaselineOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateEnabledControlOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLandingZoneOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ThrottlingException()
        if let retryAfterSecondsHeaderValue = httpResponse.headers.value(for: "Retry-After") {
            value.properties.retryAfterSeconds = Swift.Int(retryAfterSecondsHeaderValue) ?? 0
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.quotaCode = try reader["quotaCode"].readIfPresent()
        value.properties.serviceCode = try reader["serviceCode"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceQuotaExceededException {
        let reader = baseError.errorBodyReader
        var value = ServiceQuotaExceededException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ControlTowerClientTypes.BaselineOperation {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.BaselineOperation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.BaselineOperation()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent()
        value.operationType = try reader["operationType"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        value.startTime = try reader["startTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.endTime = try reader["endTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.statusMessage = try reader["statusMessage"].readIfPresent()
        return value
    }
}

extension ControlTowerClientTypes.ControlOperation {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.ControlOperation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.ControlOperation()
        value.operationType = try reader["operationType"].readIfPresent()
        value.startTime = try reader["startTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.endTime = try reader["endTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.status = try reader["status"].readIfPresent()
        value.statusMessage = try reader["statusMessage"].readIfPresent()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent()
        value.controlIdentifier = try reader["controlIdentifier"].readIfPresent()
        value.targetIdentifier = try reader["targetIdentifier"].readIfPresent()
        value.enabledControlIdentifier = try reader["enabledControlIdentifier"].readIfPresent()
        return value
    }
}

extension ControlTowerClientTypes.EnabledBaselineDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.EnabledBaselineDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.EnabledBaselineDetails()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.baselineIdentifier = try reader["baselineIdentifier"].readIfPresent() ?? ""
        value.baselineVersion = try reader["baselineVersion"].readIfPresent()
        value.targetIdentifier = try reader["targetIdentifier"].readIfPresent() ?? ""
        value.parentIdentifier = try reader["parentIdentifier"].readIfPresent()
        value.statusSummary = try reader["statusSummary"].readIfPresent(with: ControlTowerClientTypes.EnablementStatusSummary.read(from:))
        value.parameters = try reader["parameters"].readListIfPresent(memberReadingClosure: ControlTowerClientTypes.EnabledBaselineParameterSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ControlTowerClientTypes.EnabledBaselineParameterSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.EnabledBaselineParameterSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.EnabledBaselineParameterSummary()
        value.key = try reader["key"].readIfPresent() ?? ""
        value.value = try reader["value"].readIfPresent() ?? [:]
        return value
    }
}

extension ControlTowerClientTypes.EnablementStatusSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.EnablementStatusSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.EnablementStatusSummary()
        value.status = try reader["status"].readIfPresent()
        value.lastOperationIdentifier = try reader["lastOperationIdentifier"].readIfPresent()
        return value
    }
}

extension ControlTowerClientTypes.EnabledControlDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.EnabledControlDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.EnabledControlDetails()
        value.arn = try reader["arn"].readIfPresent()
        value.controlIdentifier = try reader["controlIdentifier"].readIfPresent()
        value.targetIdentifier = try reader["targetIdentifier"].readIfPresent()
        value.targetRegions = try reader["targetRegions"].readListIfPresent(memberReadingClosure: ControlTowerClientTypes.Region.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.statusSummary = try reader["statusSummary"].readIfPresent(with: ControlTowerClientTypes.EnablementStatusSummary.read(from:))
        value.driftStatusSummary = try reader["driftStatusSummary"].readIfPresent(with: ControlTowerClientTypes.DriftStatusSummary.read(from:))
        value.parameters = try reader["parameters"].readListIfPresent(memberReadingClosure: ControlTowerClientTypes.EnabledControlParameterSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ControlTowerClientTypes.EnabledControlParameterSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.EnabledControlParameterSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.EnabledControlParameterSummary()
        value.key = try reader["key"].readIfPresent() ?? ""
        value.value = try reader["value"].readIfPresent() ?? [:]
        return value
    }
}

extension ControlTowerClientTypes.DriftStatusSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.DriftStatusSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.DriftStatusSummary()
        value.driftStatus = try reader["driftStatus"].readIfPresent()
        return value
    }
}

extension ControlTowerClientTypes.Region {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.Region {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.Region()
        value.name = try reader["name"].readIfPresent()
        return value
    }
}

extension ControlTowerClientTypes.LandingZoneDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.LandingZoneDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.LandingZoneDetail()
        value.version = try reader["version"].readIfPresent() ?? ""
        value.manifest = try reader["manifest"].readIfPresent() ?? [:]
        value.arn = try reader["arn"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        value.latestAvailableVersion = try reader["latestAvailableVersion"].readIfPresent()
        value.driftStatus = try reader["driftStatus"].readIfPresent(with: ControlTowerClientTypes.LandingZoneDriftStatusSummary.read(from:))
        return value
    }
}

extension ControlTowerClientTypes.LandingZoneDriftStatusSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.LandingZoneDriftStatusSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.LandingZoneDriftStatusSummary()
        value.status = try reader["status"].readIfPresent()
        return value
    }
}

extension ControlTowerClientTypes.LandingZoneOperationDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.LandingZoneOperationDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.LandingZoneOperationDetail()
        value.operationType = try reader["operationType"].readIfPresent()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        value.startTime = try reader["startTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.endTime = try reader["endTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.statusMessage = try reader["statusMessage"].readIfPresent()
        return value
    }
}

extension ControlTowerClientTypes.BaselineSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.BaselineSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.BaselineSummary()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent() ?? ""
        value.description = try reader["description"].readIfPresent()
        return value
    }
}

extension ControlTowerClientTypes.ControlOperationSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.ControlOperationSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.ControlOperationSummary()
        value.operationType = try reader["operationType"].readIfPresent()
        value.startTime = try reader["startTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.endTime = try reader["endTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.status = try reader["status"].readIfPresent()
        value.statusMessage = try reader["statusMessage"].readIfPresent()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent()
        value.controlIdentifier = try reader["controlIdentifier"].readIfPresent()
        value.targetIdentifier = try reader["targetIdentifier"].readIfPresent()
        value.enabledControlIdentifier = try reader["enabledControlIdentifier"].readIfPresent()
        return value
    }
}

extension ControlTowerClientTypes.EnabledBaselineSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.EnabledBaselineSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.EnabledBaselineSummary()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.baselineIdentifier = try reader["baselineIdentifier"].readIfPresent() ?? ""
        value.baselineVersion = try reader["baselineVersion"].readIfPresent()
        value.targetIdentifier = try reader["targetIdentifier"].readIfPresent() ?? ""
        value.parentIdentifier = try reader["parentIdentifier"].readIfPresent()
        value.statusSummary = try reader["statusSummary"].readIfPresent(with: ControlTowerClientTypes.EnablementStatusSummary.read(from:))
        return value
    }
}

extension ControlTowerClientTypes.EnabledControlSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.EnabledControlSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.EnabledControlSummary()
        value.controlIdentifier = try reader["controlIdentifier"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.targetIdentifier = try reader["targetIdentifier"].readIfPresent()
        value.statusSummary = try reader["statusSummary"].readIfPresent(with: ControlTowerClientTypes.EnablementStatusSummary.read(from:))
        value.driftStatusSummary = try reader["driftStatusSummary"].readIfPresent(with: ControlTowerClientTypes.DriftStatusSummary.read(from:))
        return value
    }
}

extension ControlTowerClientTypes.LandingZoneOperationSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.LandingZoneOperationSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.LandingZoneOperationSummary()
        value.operationType = try reader["operationType"].readIfPresent()
        value.operationIdentifier = try reader["operationIdentifier"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        return value
    }
}

extension ControlTowerClientTypes.LandingZoneSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlTowerClientTypes.LandingZoneSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlTowerClientTypes.LandingZoneSummary()
        value.arn = try reader["arn"].readIfPresent()
        return value
    }
}

extension ControlTowerClientTypes.EnabledBaselineParameter {

    static func write(value: ControlTowerClientTypes.EnabledBaselineParameter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["key"].write(value.key)
        try writer["value"].write(value.value)
    }
}

extension ControlTowerClientTypes.EnabledControlParameter {

    static func write(value: ControlTowerClientTypes.EnabledControlParameter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["key"].write(value.key)
        try writer["value"].write(value.value)
    }
}

extension ControlTowerClientTypes.ControlOperationFilter {

    static func write(value: ControlTowerClientTypes.ControlOperationFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["controlIdentifiers"].writeList(value.controlIdentifiers, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["controlOperationTypes"].writeList(value.controlOperationTypes, memberWritingClosure: SmithyReadWrite.WritingClosureBox<ControlTowerClientTypes.ControlOperationType>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["enabledControlIdentifiers"].writeList(value.enabledControlIdentifiers, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["statuses"].writeList(value.statuses, memberWritingClosure: SmithyReadWrite.WritingClosureBox<ControlTowerClientTypes.ControlOperationStatus>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["targetIdentifiers"].writeList(value.targetIdentifiers, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ControlTowerClientTypes.EnabledBaselineFilter {

    static func write(value: ControlTowerClientTypes.EnabledBaselineFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["baselineIdentifiers"].writeList(value.baselineIdentifiers, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["parentIdentifiers"].writeList(value.parentIdentifiers, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["targetIdentifiers"].writeList(value.targetIdentifiers, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ControlTowerClientTypes.EnabledControlFilter {

    static func write(value: ControlTowerClientTypes.EnabledControlFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["controlIdentifiers"].writeList(value.controlIdentifiers, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["driftStatuses"].writeList(value.driftStatuses, memberWritingClosure: SmithyReadWrite.WritingClosureBox<ControlTowerClientTypes.DriftStatus>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["statuses"].writeList(value.statuses, memberWritingClosure: SmithyReadWrite.WritingClosureBox<ControlTowerClientTypes.EnablementStatus>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ControlTowerClientTypes.LandingZoneOperationFilter {

    static func write(value: ControlTowerClientTypes.LandingZoneOperationFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["statuses"].writeList(value.statuses, memberWritingClosure: SmithyReadWrite.WritingClosureBox<ControlTowerClientTypes.LandingZoneOperationStatus>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["types"].writeList(value.types, memberWritingClosure: SmithyReadWrite.WritingClosureBox<ControlTowerClientTypes.LandingZoneOperationType>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

public enum ControlTowerClientTypes {}
