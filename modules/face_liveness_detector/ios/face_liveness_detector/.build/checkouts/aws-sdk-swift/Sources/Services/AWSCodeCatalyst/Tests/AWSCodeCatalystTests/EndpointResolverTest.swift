//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@testable import AWSCodeCatalyst
import XCTest
import enum SmithyTestUtil.TestInitializer
import struct SmithyHTTPAPI.Endpoint
import struct SmithyHTTPAPI.Headers

class EndpointResolverTest: XCTestCase {

    override class func setUp() {
        SmithyTestUtil.TestInitializer.initialize()
    }

    /// Override endpoint
    func testResolve1() throws {
        let endpointParams = EndpointParams(
            endpoint: "https://test.codecatalyst.global.api.aws"
        )
        let resolver = try DefaultEndpointResolver()

        let actual = try resolver.resolve(params: endpointParams)

        let properties: [String: AnyHashable] =
            [:]

        let headers = SmithyHTTPAPI.Headers()
        let expected = try SmithyHTTPAPI.Endpoint(urlString: "https://test.codecatalyst.global.api.aws", headers: headers, properties: properties)

        XCTAssertEqual(expected, actual)
    }

    /// Default endpoint (region not set)
    func testResolve2() throws {
        let endpointParams = EndpointParams(
        )
        let resolver = try DefaultEndpointResolver()

        let actual = try resolver.resolve(params: endpointParams)

        let properties: [String: AnyHashable] =
            [:]

        let headers = SmithyHTTPAPI.Headers()
        let expected = try SmithyHTTPAPI.Endpoint(urlString: "https://codecatalyst.global.api.aws", headers: headers, properties: properties)

        XCTAssertEqual(expected, actual)
    }

    /// Default FIPS endpoint (region not set)
    func testResolve3() throws {
        let endpointParams = EndpointParams(
            useFIPS: true
        )
        let resolver = try DefaultEndpointResolver()

        let actual = try resolver.resolve(params: endpointParams)

        let properties: [String: AnyHashable] =
            [:]

        let headers = SmithyHTTPAPI.Headers()
        let expected = try SmithyHTTPAPI.Endpoint(urlString: "https://codecatalyst-fips.global.api.aws", headers: headers, properties: properties)

        XCTAssertEqual(expected, actual)
    }

    /// Default endpoint (region: aws-global)
    func testResolve4() throws {
        let endpointParams = EndpointParams(
            region: "aws-global"
        )
        let resolver = try DefaultEndpointResolver()

        let actual = try resolver.resolve(params: endpointParams)

        let properties: [String: AnyHashable] =
            [:]

        let headers = SmithyHTTPAPI.Headers()
        let expected = try SmithyHTTPAPI.Endpoint(urlString: "https://codecatalyst.global.api.aws", headers: headers, properties: properties)

        XCTAssertEqual(expected, actual)
    }

    /// Default FIPS endpoint (region: aws-global)
    func testResolve5() throws {
        let endpointParams = EndpointParams(
            region: "aws-global",
            useFIPS: true
        )
        let resolver = try DefaultEndpointResolver()

        let actual = try resolver.resolve(params: endpointParams)

        let properties: [String: AnyHashable] =
            [:]

        let headers = SmithyHTTPAPI.Headers()
        let expected = try SmithyHTTPAPI.Endpoint(urlString: "https://codecatalyst-fips.global.api.aws", headers: headers, properties: properties)

        XCTAssertEqual(expected, actual)
    }

    /// Default endpoint for a valid home region (region: us-west-2)
    func testResolve6() throws {
        let endpointParams = EndpointParams(
            region: "us-west-2"
        )
        let resolver = try DefaultEndpointResolver()

        let actual = try resolver.resolve(params: endpointParams)

        let properties: [String: AnyHashable] =
            [:]

        let headers = SmithyHTTPAPI.Headers()
        let expected = try SmithyHTTPAPI.Endpoint(urlString: "https://codecatalyst.global.api.aws", headers: headers, properties: properties)

        XCTAssertEqual(expected, actual)
    }

    /// Default FIPS endpoint for a valid home region (region: us-west-2)
    func testResolve7() throws {
        let endpointParams = EndpointParams(
            region: "us-west-2",
            useFIPS: true
        )
        let resolver = try DefaultEndpointResolver()

        let actual = try resolver.resolve(params: endpointParams)

        let properties: [String: AnyHashable] =
            [:]

        let headers = SmithyHTTPAPI.Headers()
        let expected = try SmithyHTTPAPI.Endpoint(urlString: "https://codecatalyst-fips.global.api.aws", headers: headers, properties: properties)

        XCTAssertEqual(expected, actual)
    }

    /// Default endpoint for an unavailable home region (region: us-east-1)
    func testResolve8() throws {
        let endpointParams = EndpointParams(
            region: "us-east-1"
        )
        let resolver = try DefaultEndpointResolver()

        let actual = try resolver.resolve(params: endpointParams)

        let properties: [String: AnyHashable] =
            [:]

        let headers = SmithyHTTPAPI.Headers()
        let expected = try SmithyHTTPAPI.Endpoint(urlString: "https://codecatalyst.global.api.aws", headers: headers, properties: properties)

        XCTAssertEqual(expected, actual)
    }

    /// Default FIPS endpoint for an unavailable home region (region: us-east-1)
    func testResolve9() throws {
        let endpointParams = EndpointParams(
            region: "us-east-1",
            useFIPS: true
        )
        let resolver = try DefaultEndpointResolver()

        let actual = try resolver.resolve(params: endpointParams)

        let properties: [String: AnyHashable] =
            [:]

        let headers = SmithyHTTPAPI.Headers()
        let expected = try SmithyHTTPAPI.Endpoint(urlString: "https://codecatalyst-fips.global.api.aws", headers: headers, properties: properties)

        XCTAssertEqual(expected, actual)
    }

}
