//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import class SmithyWaitersAPI.Waiter
import enum SmithyWaitersAPI.JMESUtils
import protocol ClientRuntime.ServiceError
import struct Smithy<PERSON>ait<PERSON>API.WaiterConfiguration
import struct SmithyWaitersAPI.WaiterOptions
import struct SmithyWaitersAPI.WaiterOutcome

extension ElastiCacheClient {

    static func cacheClusterAvailableWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeCacheClustersInput, DescribeCacheClustersOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeCacheClustersInput, DescribeCacheClustersOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeCacheClustersInput, result: Swift.Result<DescribeCacheClustersOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CacheClusters[].CacheClusterStatus"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "available"
                guard case .success(let output) = result else { return false }
                let cacheClusters = output.cacheClusters
                let projection: [Swift.String]? = cacheClusters?.compactMap { original in
                    let cacheClusterStatus = original.cacheClusterStatus
                    return cacheClusterStatus
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "available") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeCacheClustersInput, result: Swift.Result<DescribeCacheClustersOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CacheClusters[].CacheClusterStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleted"
                guard case .success(let output) = result else { return false }
                let cacheClusters = output.cacheClusters
                let projection: [Swift.String]? = cacheClusters?.compactMap { original in
                    let cacheClusterStatus = original.cacheClusterStatus
                    return cacheClusterStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleted") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeCacheClustersInput, result: Swift.Result<DescribeCacheClustersOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CacheClusters[].CacheClusterStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleting"
                guard case .success(let output) = result else { return false }
                let cacheClusters = output.cacheClusters
                let projection: [Swift.String]? = cacheClusters?.compactMap { original in
                    let cacheClusterStatus = original.cacheClusterStatus
                    return cacheClusterStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleting") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeCacheClustersInput, result: Swift.Result<DescribeCacheClustersOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CacheClusters[].CacheClusterStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "incompatible-network"
                guard case .success(let output) = result else { return false }
                let cacheClusters = output.cacheClusters
                let projection: [Swift.String]? = cacheClusters?.compactMap { original in
                    let cacheClusterStatus = original.cacheClusterStatus
                    return cacheClusterStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "incompatible-network") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeCacheClustersInput, result: Swift.Result<DescribeCacheClustersOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CacheClusters[].CacheClusterStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "restore-failed"
                guard case .success(let output) = result else { return false }
                let cacheClusters = output.cacheClusters
                let projection: [Swift.String]? = cacheClusters?.compactMap { original in
                    let cacheClusterStatus = original.cacheClusterStatus
                    return cacheClusterStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "restore-failed") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeCacheClustersInput, DescribeCacheClustersOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the CacheClusterAvailable event on the describeCacheClusters operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeCacheClustersInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilCacheClusterAvailable(options: SmithyWaitersAPI.WaiterOptions, input: DescribeCacheClustersInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeCacheClustersOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.cacheClusterAvailableWaiterConfig(), operation: self.describeCacheClusters(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func cacheClusterDeletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeCacheClustersInput, DescribeCacheClustersOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeCacheClustersInput, DescribeCacheClustersOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeCacheClustersInput, result: Swift.Result<DescribeCacheClustersOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CacheClusters[].CacheClusterStatus"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "deleted"
                guard case .success(let output) = result else { return false }
                let cacheClusters = output.cacheClusters
                let projection: [Swift.String]? = cacheClusters?.compactMap { original in
                    let cacheClusterStatus = original.cacheClusterStatus
                    return cacheClusterStatus
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleted") } ?? false)
            }),
            .init(state: .success, matcher: { (input: DescribeCacheClustersInput, result: Swift.Result<DescribeCacheClustersOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "CacheClusterNotFound"
            }),
            .init(state: .failure, matcher: { (input: DescribeCacheClustersInput, result: Swift.Result<DescribeCacheClustersOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CacheClusters[].CacheClusterStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "available"
                guard case .success(let output) = result else { return false }
                let cacheClusters = output.cacheClusters
                let projection: [Swift.String]? = cacheClusters?.compactMap { original in
                    let cacheClusterStatus = original.cacheClusterStatus
                    return cacheClusterStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "available") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeCacheClustersInput, result: Swift.Result<DescribeCacheClustersOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CacheClusters[].CacheClusterStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "creating"
                guard case .success(let output) = result else { return false }
                let cacheClusters = output.cacheClusters
                let projection: [Swift.String]? = cacheClusters?.compactMap { original in
                    let cacheClusterStatus = original.cacheClusterStatus
                    return cacheClusterStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "creating") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeCacheClustersInput, result: Swift.Result<DescribeCacheClustersOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CacheClusters[].CacheClusterStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "incompatible-network"
                guard case .success(let output) = result else { return false }
                let cacheClusters = output.cacheClusters
                let projection: [Swift.String]? = cacheClusters?.compactMap { original in
                    let cacheClusterStatus = original.cacheClusterStatus
                    return cacheClusterStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "incompatible-network") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeCacheClustersInput, result: Swift.Result<DescribeCacheClustersOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CacheClusters[].CacheClusterStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "modifying"
                guard case .success(let output) = result else { return false }
                let cacheClusters = output.cacheClusters
                let projection: [Swift.String]? = cacheClusters?.compactMap { original in
                    let cacheClusterStatus = original.cacheClusterStatus
                    return cacheClusterStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "modifying") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeCacheClustersInput, result: Swift.Result<DescribeCacheClustersOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CacheClusters[].CacheClusterStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "restore-failed"
                guard case .success(let output) = result else { return false }
                let cacheClusters = output.cacheClusters
                let projection: [Swift.String]? = cacheClusters?.compactMap { original in
                    let cacheClusterStatus = original.cacheClusterStatus
                    return cacheClusterStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "restore-failed") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeCacheClustersInput, result: Swift.Result<DescribeCacheClustersOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CacheClusters[].CacheClusterStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "snapshotting"
                guard case .success(let output) = result else { return false }
                let cacheClusters = output.cacheClusters
                let projection: [Swift.String]? = cacheClusters?.compactMap { original in
                    let cacheClusterStatus = original.cacheClusterStatus
                    return cacheClusterStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "snapshotting") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeCacheClustersInput, DescribeCacheClustersOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the CacheClusterDeleted event on the describeCacheClusters operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeCacheClustersInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilCacheClusterDeleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeCacheClustersInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeCacheClustersOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.cacheClusterDeletedWaiterConfig(), operation: self.describeCacheClusters(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func replicationGroupAvailableWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationGroupsInput, DescribeReplicationGroupsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationGroupsInput, DescribeReplicationGroupsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeReplicationGroupsInput, result: Swift.Result<DescribeReplicationGroupsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationGroups[].Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "available"
                guard case .success(let output) = result else { return false }
                let replicationGroups = output.replicationGroups
                let projection: [Swift.String]? = replicationGroups?.compactMap { original in
                    let status = original.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "available") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationGroupsInput, result: Swift.Result<DescribeReplicationGroupsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationGroups[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleted"
                guard case .success(let output) = result else { return false }
                let replicationGroups = output.replicationGroups
                let projection: [Swift.String]? = replicationGroups?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleted") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationGroupsInput, DescribeReplicationGroupsOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ReplicationGroupAvailable event on the describeReplicationGroups operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeReplicationGroupsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilReplicationGroupAvailable(options: SmithyWaitersAPI.WaiterOptions, input: DescribeReplicationGroupsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeReplicationGroupsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.replicationGroupAvailableWaiterConfig(), operation: self.describeReplicationGroups(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func replicationGroupDeletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationGroupsInput, DescribeReplicationGroupsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationGroupsInput, DescribeReplicationGroupsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeReplicationGroupsInput, result: Swift.Result<DescribeReplicationGroupsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationGroups[].Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "deleted"
                guard case .success(let output) = result else { return false }
                let replicationGroups = output.replicationGroups
                let projection: [Swift.String]? = replicationGroups?.compactMap { original in
                    let status = original.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleted") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationGroupsInput, result: Swift.Result<DescribeReplicationGroupsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationGroups[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "available"
                guard case .success(let output) = result else { return false }
                let replicationGroups = output.replicationGroups
                let projection: [Swift.String]? = replicationGroups?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "available") }) ?? false
            }),
            .init(state: .success, matcher: { (input: DescribeReplicationGroupsInput, result: Swift.Result<DescribeReplicationGroupsOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "ReplicationGroupNotFoundFault"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationGroupsInput, DescribeReplicationGroupsOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ReplicationGroupDeleted event on the describeReplicationGroups operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeReplicationGroupsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilReplicationGroupDeleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeReplicationGroupsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeReplicationGroupsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.replicationGroupDeletedWaiterConfig(), operation: self.describeReplicationGroups(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }
}
