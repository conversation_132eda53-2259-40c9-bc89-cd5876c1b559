//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import Foundation
import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension DataSyncClient {
    /// Paginate over `[DescribeStorageSystemResourceMetricsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeStorageSystemResourceMetricsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeStorageSystemResourceMetricsOutput`
    public func describeStorageSystemResourceMetricsPaginated(input: DescribeStorageSystemResourceMetricsInput) -> ClientRuntime.PaginatorSequence<DescribeStorageSystemResourceMetricsInput, DescribeStorageSystemResourceMetricsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeStorageSystemResourceMetricsInput, DescribeStorageSystemResourceMetricsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeStorageSystemResourceMetrics(input:))
    }
}

extension DescribeStorageSystemResourceMetricsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeStorageSystemResourceMetricsInput {
        return DescribeStorageSystemResourceMetricsInput(
            discoveryJobArn: self.discoveryJobArn,
            endTime: self.endTime,
            maxResults: self.maxResults,
            nextToken: token,
            resourceId: self.resourceId,
            resourceType: self.resourceType,
            startTime: self.startTime
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeStorageSystemResourceMetricsInput, OperationStackOutput == DescribeStorageSystemResourceMetricsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeStorageSystemResourceMetricsPaginated`
    /// to access the nested member `[DataSyncClientTypes.ResourceMetrics]`
    /// - Returns: `[DataSyncClientTypes.ResourceMetrics]`
    public func metrics() async throws -> [DataSyncClientTypes.ResourceMetrics] {
        return try await self.asyncCompactMap { item in item.metrics }
    }
}
extension DataSyncClient {
    /// Paginate over `[DescribeStorageSystemResourcesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeStorageSystemResourcesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeStorageSystemResourcesOutput`
    public func describeStorageSystemResourcesPaginated(input: DescribeStorageSystemResourcesInput) -> ClientRuntime.PaginatorSequence<DescribeStorageSystemResourcesInput, DescribeStorageSystemResourcesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeStorageSystemResourcesInput, DescribeStorageSystemResourcesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeStorageSystemResources(input:))
    }
}

extension DescribeStorageSystemResourcesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeStorageSystemResourcesInput {
        return DescribeStorageSystemResourcesInput(
            discoveryJobArn: self.discoveryJobArn,
            filter: self.filter,
            maxResults: self.maxResults,
            nextToken: token,
            resourceIds: self.resourceIds,
            resourceType: self.resourceType
        )}
}
extension DataSyncClient {
    /// Paginate over `[ListAgentsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListAgentsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListAgentsOutput`
    public func listAgentsPaginated(input: ListAgentsInput) -> ClientRuntime.PaginatorSequence<ListAgentsInput, ListAgentsOutput> {
        return ClientRuntime.PaginatorSequence<ListAgentsInput, ListAgentsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listAgents(input:))
    }
}

extension ListAgentsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListAgentsInput {
        return ListAgentsInput(
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListAgentsInput, OperationStackOutput == ListAgentsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listAgentsPaginated`
    /// to access the nested member `[DataSyncClientTypes.AgentListEntry]`
    /// - Returns: `[DataSyncClientTypes.AgentListEntry]`
    public func agents() async throws -> [DataSyncClientTypes.AgentListEntry] {
        return try await self.asyncCompactMap { item in item.agents }
    }
}
extension DataSyncClient {
    /// Paginate over `[ListDiscoveryJobsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListDiscoveryJobsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListDiscoveryJobsOutput`
    public func listDiscoveryJobsPaginated(input: ListDiscoveryJobsInput) -> ClientRuntime.PaginatorSequence<ListDiscoveryJobsInput, ListDiscoveryJobsOutput> {
        return ClientRuntime.PaginatorSequence<ListDiscoveryJobsInput, ListDiscoveryJobsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listDiscoveryJobs(input:))
    }
}

extension ListDiscoveryJobsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListDiscoveryJobsInput {
        return ListDiscoveryJobsInput(
            maxResults: self.maxResults,
            nextToken: token,
            storageSystemArn: self.storageSystemArn
        )}
}

extension PaginatorSequence where OperationStackInput == ListDiscoveryJobsInput, OperationStackOutput == ListDiscoveryJobsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listDiscoveryJobsPaginated`
    /// to access the nested member `[DataSyncClientTypes.DiscoveryJobListEntry]`
    /// - Returns: `[DataSyncClientTypes.DiscoveryJobListEntry]`
    public func discoveryJobs() async throws -> [DataSyncClientTypes.DiscoveryJobListEntry] {
        return try await self.asyncCompactMap { item in item.discoveryJobs }
    }
}
extension DataSyncClient {
    /// Paginate over `[ListLocationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListLocationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListLocationsOutput`
    public func listLocationsPaginated(input: ListLocationsInput) -> ClientRuntime.PaginatorSequence<ListLocationsInput, ListLocationsOutput> {
        return ClientRuntime.PaginatorSequence<ListLocationsInput, ListLocationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listLocations(input:))
    }
}

extension ListLocationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListLocationsInput {
        return ListLocationsInput(
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListLocationsInput, OperationStackOutput == ListLocationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listLocationsPaginated`
    /// to access the nested member `[DataSyncClientTypes.LocationListEntry]`
    /// - Returns: `[DataSyncClientTypes.LocationListEntry]`
    public func locations() async throws -> [DataSyncClientTypes.LocationListEntry] {
        return try await self.asyncCompactMap { item in item.locations }
    }
}
extension DataSyncClient {
    /// Paginate over `[ListStorageSystemsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListStorageSystemsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListStorageSystemsOutput`
    public func listStorageSystemsPaginated(input: ListStorageSystemsInput) -> ClientRuntime.PaginatorSequence<ListStorageSystemsInput, ListStorageSystemsOutput> {
        return ClientRuntime.PaginatorSequence<ListStorageSystemsInput, ListStorageSystemsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listStorageSystems(input:))
    }
}

extension ListStorageSystemsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListStorageSystemsInput {
        return ListStorageSystemsInput(
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListStorageSystemsInput, OperationStackOutput == ListStorageSystemsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listStorageSystemsPaginated`
    /// to access the nested member `[DataSyncClientTypes.StorageSystemListEntry]`
    /// - Returns: `[DataSyncClientTypes.StorageSystemListEntry]`
    public func storageSystems() async throws -> [DataSyncClientTypes.StorageSystemListEntry] {
        return try await self.asyncCompactMap { item in item.storageSystems }
    }
}
extension DataSyncClient {
    /// Paginate over `[ListTagsForResourceOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListTagsForResourceInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListTagsForResourceOutput`
    public func listTagsForResourcePaginated(input: ListTagsForResourceInput) -> ClientRuntime.PaginatorSequence<ListTagsForResourceInput, ListTagsForResourceOutput> {
        return ClientRuntime.PaginatorSequence<ListTagsForResourceInput, ListTagsForResourceOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listTagsForResource(input:))
    }
}

extension ListTagsForResourceInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListTagsForResourceInput {
        return ListTagsForResourceInput(
            maxResults: self.maxResults,
            nextToken: token,
            resourceArn: self.resourceArn
        )}
}

extension PaginatorSequence where OperationStackInput == ListTagsForResourceInput, OperationStackOutput == ListTagsForResourceOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listTagsForResourcePaginated`
    /// to access the nested member `[DataSyncClientTypes.TagListEntry]`
    /// - Returns: `[DataSyncClientTypes.TagListEntry]`
    public func tags() async throws -> [DataSyncClientTypes.TagListEntry] {
        return try await self.asyncCompactMap { item in item.tags }
    }
}
extension DataSyncClient {
    /// Paginate over `[ListTaskExecutionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListTaskExecutionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListTaskExecutionsOutput`
    public func listTaskExecutionsPaginated(input: ListTaskExecutionsInput) -> ClientRuntime.PaginatorSequence<ListTaskExecutionsInput, ListTaskExecutionsOutput> {
        return ClientRuntime.PaginatorSequence<ListTaskExecutionsInput, ListTaskExecutionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listTaskExecutions(input:))
    }
}

extension ListTaskExecutionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListTaskExecutionsInput {
        return ListTaskExecutionsInput(
            maxResults: self.maxResults,
            nextToken: token,
            taskArn: self.taskArn
        )}
}

extension PaginatorSequence where OperationStackInput == ListTaskExecutionsInput, OperationStackOutput == ListTaskExecutionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listTaskExecutionsPaginated`
    /// to access the nested member `[DataSyncClientTypes.TaskExecutionListEntry]`
    /// - Returns: `[DataSyncClientTypes.TaskExecutionListEntry]`
    public func taskExecutions() async throws -> [DataSyncClientTypes.TaskExecutionListEntry] {
        return try await self.asyncCompactMap { item in item.taskExecutions }
    }
}
extension DataSyncClient {
    /// Paginate over `[ListTasksOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListTasksInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListTasksOutput`
    public func listTasksPaginated(input: ListTasksInput) -> ClientRuntime.PaginatorSequence<ListTasksInput, ListTasksOutput> {
        return ClientRuntime.PaginatorSequence<ListTasksInput, ListTasksOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listTasks(input:))
    }
}

extension ListTasksInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListTasksInput {
        return ListTasksInput(
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListTasksInput, OperationStackOutput == ListTasksOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listTasksPaginated`
    /// to access the nested member `[DataSyncClientTypes.TaskListEntry]`
    /// - Returns: `[DataSyncClientTypes.TaskListEntry]`
    public func tasks() async throws -> [DataSyncClientTypes.TaskListEntry] {
        return try await self.asyncCompactMap { item in item.tasks }
    }
}
