//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
@_spi(SmithyReadWrite) import func SmithyReadWrite.listReadingClosure
@_spi(SmithyReadWrite) import func SmithyReadWrite.listWritingClosure
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
@_spi(SmithyReadWrite) import struct SmithyReadWrite.ReadingClosureBox
@_spi(SmithyReadWrite) import struct SmithyReadWrite.WritingClosureBox
@_spi(SmithyTimestamps) import struct SmithyTimestamps.TimestampFormatter


public struct DeleteCampaignChannelSubtypeConfigOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteCampaignCommunicationLimitsOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteCampaignCommunicationTimeOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteCampaignOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteConnectInstanceConfigOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteConnectInstanceIntegrationOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteInstanceOnboardingJobOutput: Swift.Sendable {

    public init() { }
}

public struct PauseCampaignOutput: Swift.Sendable {

    public init() { }
}

public struct PutConnectInstanceIntegrationOutput: Swift.Sendable {

    public init() { }
}

public struct ResumeCampaignOutput: Swift.Sendable {

    public init() { }
}

public struct StartCampaignOutput: Swift.Sendable {

    public init() { }
}

public struct StopCampaignOutput: Swift.Sendable {

    public init() { }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateCampaignChannelSubtypeConfigOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateCampaignCommunicationLimitsOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateCampaignCommunicationTimeOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateCampaignFlowAssociationOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateCampaignNameOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateCampaignScheduleOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateCampaignSourceOutput: Swift.Sendable {

    public init() { }
}

/// You do not have sufficient access to perform this action.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Agentless config
    public struct AgentlessConfig: Swift.Sendable {

        public init() { }
    }
}

/// The request could not be processed because of conflict in the current state of the resource.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

/// Request processing failed because of an error or failure with the service.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

/// The specified resource was not found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

/// Request would cause a service quota to be exceeded.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceQuotaExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

/// The request was denied due to request throttling.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

/// The input fails to satisfy the constraints specified by an AWS service.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Default Email Outbound config
    public struct EmailOutboundConfig: Swift.Sendable {
        /// Source/Destination Email address used for Email messages
        /// This member is required.
        public var connectSourceEmailAddress: Swift.String?
        /// Display name for Email Address
        public var sourceEmailAddressDisplayName: Swift.String?
        /// Amazon Resource Names(ARN)
        /// This member is required.
        public var wisdomTemplateArn: Swift.String?

        public init(
            connectSourceEmailAddress: Swift.String? = nil,
            sourceEmailAddressDisplayName: Swift.String? = nil,
            wisdomTemplateArn: Swift.String? = nil
        )
        {
            self.connectSourceEmailAddress = connectSourceEmailAddress
            self.sourceEmailAddressDisplayName = sourceEmailAddressDisplayName
            self.wisdomTemplateArn = wisdomTemplateArn
        }
    }
}

extension ConnectCampaignsV2ClientTypes.EmailOutboundConfig: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "EmailOutboundConfig(wisdomTemplateArn: \(Swift.String(describing: wisdomTemplateArn)), connectSourceEmailAddress: \"CONTENT_REDACTED\", sourceEmailAddressDisplayName: \"CONTENT_REDACTED\")"}
}

extension ConnectCampaignsV2ClientTypes {

    /// Email Outbound Mode
    public enum EmailOutboundMode: Swift.Sendable {
        /// Agentless config
        case agentless(ConnectCampaignsV2ClientTypes.AgentlessConfig)
        case sdkUnknown(Swift.String)
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Email Channel Subtype config
    public struct EmailChannelSubtypeConfig: Swift.Sendable {
        /// Allocates outbound capacity for the specific channel subtype of this campaign between multiple active campaigns
        public var capacity: Swift.Double?
        /// Default Email Outbound config
        /// This member is required.
        public var defaultOutboundConfig: ConnectCampaignsV2ClientTypes.EmailOutboundConfig?
        /// Email Outbound Mode
        /// This member is required.
        public var outboundMode: ConnectCampaignsV2ClientTypes.EmailOutboundMode?

        public init(
            capacity: Swift.Double? = nil,
            defaultOutboundConfig: ConnectCampaignsV2ClientTypes.EmailOutboundConfig? = nil,
            outboundMode: ConnectCampaignsV2ClientTypes.EmailOutboundMode? = nil
        )
        {
            self.capacity = capacity
            self.defaultOutboundConfig = defaultOutboundConfig
            self.outboundMode = outboundMode
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Default SMS Outbound config
    public struct SmsOutboundConfig: Swift.Sendable {
        /// Amazon Resource Names(ARN)
        /// This member is required.
        public var connectSourcePhoneNumberArn: Swift.String?
        /// Amazon Resource Names(ARN)
        /// This member is required.
        public var wisdomTemplateArn: Swift.String?

        public init(
            connectSourcePhoneNumberArn: Swift.String? = nil,
            wisdomTemplateArn: Swift.String? = nil
        )
        {
            self.connectSourcePhoneNumberArn = connectSourcePhoneNumberArn
            self.wisdomTemplateArn = wisdomTemplateArn
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// SMS Outbound Mode
    public enum SmsOutboundMode: Swift.Sendable {
        /// Agentless config
        case agentless(ConnectCampaignsV2ClientTypes.AgentlessConfig)
        case sdkUnknown(Swift.String)
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// SMS Channel Subtype config
    public struct SmsChannelSubtypeConfig: Swift.Sendable {
        /// Allocates outbound capacity for the specific channel subtype of this campaign between multiple active campaigns
        public var capacity: Swift.Double?
        /// Default SMS Outbound config
        /// This member is required.
        public var defaultOutboundConfig: ConnectCampaignsV2ClientTypes.SmsOutboundConfig?
        /// SMS Outbound Mode
        /// This member is required.
        public var outboundMode: ConnectCampaignsV2ClientTypes.SmsOutboundMode?

        public init(
            capacity: Swift.Double? = nil,
            defaultOutboundConfig: ConnectCampaignsV2ClientTypes.SmsOutboundConfig? = nil,
            outboundMode: ConnectCampaignsV2ClientTypes.SmsOutboundMode? = nil
        )
        {
            self.capacity = capacity
            self.defaultOutboundConfig = defaultOutboundConfig
            self.outboundMode = outboundMode
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Answering Machine Detection config
    public struct AnswerMachineDetectionConfig: Swift.Sendable {
        /// Enable or disable await answer machine prompt
        public var awaitAnswerMachinePrompt: Swift.Bool?
        /// Enable or disable answering machine detection
        /// This member is required.
        public var enableAnswerMachineDetection: Swift.Bool?

        public init(
            awaitAnswerMachinePrompt: Swift.Bool? = nil,
            enableAnswerMachineDetection: Swift.Bool? = nil
        )
        {
            self.awaitAnswerMachinePrompt = awaitAnswerMachinePrompt
            self.enableAnswerMachineDetection = enableAnswerMachineDetection
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Default Telephony Outbound config
    public struct TelephonyOutboundConfig: Swift.Sendable {
        /// Answering Machine Detection config
        public var answerMachineDetectionConfig: ConnectCampaignsV2ClientTypes.AnswerMachineDetectionConfig?
        /// The identifier of the contact flow for the outbound call.
        /// This member is required.
        public var connectContactFlowId: Swift.String?
        /// The phone number associated with the Amazon Connect instance, in E.164 format. If you do not specify a source phone number, you must specify a queue.
        public var connectSourcePhoneNumber: Swift.String?

        public init(
            answerMachineDetectionConfig: ConnectCampaignsV2ClientTypes.AnswerMachineDetectionConfig? = nil,
            connectContactFlowId: Swift.String? = nil,
            connectSourcePhoneNumber: Swift.String? = nil
        )
        {
            self.answerMachineDetectionConfig = answerMachineDetectionConfig
            self.connectContactFlowId = connectContactFlowId
            self.connectSourcePhoneNumber = connectSourcePhoneNumber
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Predictive config
    public struct PredictiveConfig: Swift.Sendable {
        /// The bandwidth allocation of a queue resource.
        /// This member is required.
        public var bandwidthAllocation: Swift.Double?

        public init(
            bandwidthAllocation: Swift.Double? = nil
        )
        {
            self.bandwidthAllocation = bandwidthAllocation
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Progressive config
    public struct ProgressiveConfig: Swift.Sendable {
        /// The bandwidth allocation of a queue resource.
        /// This member is required.
        public var bandwidthAllocation: Swift.Double?

        public init(
            bandwidthAllocation: Swift.Double? = nil
        )
        {
            self.bandwidthAllocation = bandwidthAllocation
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Telephony Outbound Mode
    public enum TelephonyOutboundMode: Swift.Sendable {
        /// Progressive config
        case progressive(ConnectCampaignsV2ClientTypes.ProgressiveConfig)
        /// Predictive config
        case predictive(ConnectCampaignsV2ClientTypes.PredictiveConfig)
        /// Agentless config
        case agentless(ConnectCampaignsV2ClientTypes.AgentlessConfig)
        case sdkUnknown(Swift.String)
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Telephony Channel Subtype config
    public struct TelephonyChannelSubtypeConfig: Swift.Sendable {
        /// Allocates outbound capacity for the specific channel subtype of this campaign between multiple active campaigns
        public var capacity: Swift.Double?
        /// The queue for the call. If you specify a queue, the phone displayed for caller ID is the phone number specified in the queue. If you do not specify a queue, the queue defined in the contact flow is used. If you do not specify a queue, you must specify a source phone number.
        public var connectQueueId: Swift.String?
        /// Default Telephony Outbound config
        /// This member is required.
        public var defaultOutboundConfig: ConnectCampaignsV2ClientTypes.TelephonyOutboundConfig?
        /// Telephony Outbound Mode
        /// This member is required.
        public var outboundMode: ConnectCampaignsV2ClientTypes.TelephonyOutboundMode?

        public init(
            capacity: Swift.Double? = nil,
            connectQueueId: Swift.String? = nil,
            defaultOutboundConfig: ConnectCampaignsV2ClientTypes.TelephonyOutboundConfig? = nil,
            outboundMode: ConnectCampaignsV2ClientTypes.TelephonyOutboundMode? = nil
        )
        {
            self.capacity = capacity
            self.connectQueueId = connectQueueId
            self.defaultOutboundConfig = defaultOutboundConfig
            self.outboundMode = outboundMode
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Campaign Channel Subtype config
    public struct ChannelSubtypeConfig: Swift.Sendable {
        /// Email Channel Subtype config
        public var email: ConnectCampaignsV2ClientTypes.EmailChannelSubtypeConfig?
        /// SMS Channel Subtype config
        public var sms: ConnectCampaignsV2ClientTypes.SmsChannelSubtypeConfig?
        /// Telephony Channel Subtype config
        public var telephony: ConnectCampaignsV2ClientTypes.TelephonyChannelSubtypeConfig?

        public init(
            email: ConnectCampaignsV2ClientTypes.EmailChannelSubtypeConfig? = nil,
            sms: ConnectCampaignsV2ClientTypes.SmsChannelSubtypeConfig? = nil,
            telephony: ConnectCampaignsV2ClientTypes.TelephonyChannelSubtypeConfig? = nil
        )
        {
            self.email = email
            self.sms = sms
            self.telephony = telephony
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// The communication limit time unit.
    public enum CommunicationLimitTimeUnit: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case day
        case sdkUnknown(Swift.String)

        public static var allCases: [CommunicationLimitTimeUnit] {
            return [
                .day
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .day: return "DAY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Communication Limit
    public struct CommunicationLimit: Swift.Sendable {
        /// The number of days to consider with regards to this limit.
        /// This member is required.
        public var frequency: Swift.Int?
        /// Maximum number of contacts allowed for a given target within the given frequency.
        /// This member is required.
        public var maxCountPerRecipient: Swift.Int?
        /// The communication limit time unit.
        /// This member is required.
        public var unit: ConnectCampaignsV2ClientTypes.CommunicationLimitTimeUnit?

        public init(
            frequency: Swift.Int? = nil,
            maxCountPerRecipient: Swift.Int? = nil,
            unit: ConnectCampaignsV2ClientTypes.CommunicationLimitTimeUnit? = nil
        )
        {
            self.frequency = frequency
            self.maxCountPerRecipient = maxCountPerRecipient
            self.unit = unit
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Communication limits
    public enum CommunicationLimits: Swift.Sendable {
        /// List of communication limit
        case communicationlimitslist([ConnectCampaignsV2ClientTypes.CommunicationLimit])
        case sdkUnknown(Swift.String)
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Communication limits config
    public struct CommunicationLimitsConfig: Swift.Sendable {
        /// Communication limits
        public var allChannelSubtypes: ConnectCampaignsV2ClientTypes.CommunicationLimits?

        public init(
            allChannelSubtypes: ConnectCampaignsV2ClientTypes.CommunicationLimits? = nil
        )
        {
            self.allChannelSubtypes = allChannelSubtypes
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Day of week enum
    public enum DayOfWeek: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case friday
        case monday
        case saturday
        case sunday
        case thursday
        case tuesday
        case wednesday
        case sdkUnknown(Swift.String)

        public static var allCases: [DayOfWeek] {
            return [
                .friday,
                .monday,
                .saturday,
                .sunday,
                .thursday,
                .tuesday,
                .wednesday
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .friday: return "FRIDAY"
            case .monday: return "MONDAY"
            case .saturday: return "SATURDAY"
            case .sunday: return "SUNDAY"
            case .thursday: return "THURSDAY"
            case .tuesday: return "TUESDAY"
            case .wednesday: return "WEDNESDAY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Time range in 24 hour format
    public struct TimeRange: Swift.Sendable {
        /// Time in ISO 8601 format, e.g. T23:11
        /// This member is required.
        public var endTime: Swift.String?
        /// Time in ISO 8601 format, e.g. T23:11
        /// This member is required.
        public var startTime: Swift.String?

        public init(
            endTime: Swift.String? = nil,
            startTime: Swift.String? = nil
        )
        {
            self.endTime = endTime
            self.startTime = startTime
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Open Hours config
    public enum OpenHours: Swift.Sendable {
        /// Daily Hours map
        case dailyhours([Swift.String: [ConnectCampaignsV2ClientTypes.TimeRange]])
        case sdkUnknown(Swift.String)
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Restricted period
    public struct RestrictedPeriod: Swift.Sendable {
        /// Date in ISO 8601 format, e.g. 2024-01-01
        /// This member is required.
        public var endDate: Swift.String?
        /// The name of a restricted period.
        public var name: Swift.String?
        /// Date in ISO 8601 format, e.g. 2024-01-01
        /// This member is required.
        public var startDate: Swift.String?

        public init(
            endDate: Swift.String? = nil,
            name: Swift.String? = nil,
            startDate: Swift.String? = nil
        )
        {
            self.endDate = endDate
            self.name = name
            self.startDate = startDate
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Restricted period config
    public enum RestrictedPeriods: Swift.Sendable {
        /// List of restricted period
        case restrictedperiodlist([ConnectCampaignsV2ClientTypes.RestrictedPeriod])
        case sdkUnknown(Swift.String)
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Time window config
    public struct TimeWindow: Swift.Sendable {
        /// Open Hours config
        /// This member is required.
        public var openHours: ConnectCampaignsV2ClientTypes.OpenHours?
        /// Restricted period config
        public var restrictedPeriods: ConnectCampaignsV2ClientTypes.RestrictedPeriods?

        public init(
            openHours: ConnectCampaignsV2ClientTypes.OpenHours? = nil,
            restrictedPeriods: ConnectCampaignsV2ClientTypes.RestrictedPeriods? = nil
        )
        {
            self.openHours = openHours
            self.restrictedPeriods = restrictedPeriods
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Local TimeZone Detection method.
    public enum LocalTimeZoneDetectionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case areaCode
        case zipCode
        case sdkUnknown(Swift.String)

        public static var allCases: [LocalTimeZoneDetectionType] {
            return [
                .areaCode,
                .zipCode
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .areaCode: return "AREA_CODE"
            case .zipCode: return "ZIP_CODE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Local time zone config
    public struct LocalTimeZoneConfig: Swift.Sendable {
        /// Time Zone Id in the IANA format
        public var defaultTimeZone: Swift.String?
        /// Local TimeZone Detection method list
        public var localTimeZoneDetection: [ConnectCampaignsV2ClientTypes.LocalTimeZoneDetectionType]?

        public init(
            defaultTimeZone: Swift.String? = nil,
            localTimeZoneDetection: [ConnectCampaignsV2ClientTypes.LocalTimeZoneDetectionType]? = nil
        )
        {
            self.defaultTimeZone = defaultTimeZone
            self.localTimeZoneDetection = localTimeZoneDetection
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Campaign communication time config
    public struct CommunicationTimeConfig: Swift.Sendable {
        /// Time window config
        public var email: ConnectCampaignsV2ClientTypes.TimeWindow?
        /// Local time zone config
        /// This member is required.
        public var localTimeZoneConfig: ConnectCampaignsV2ClientTypes.LocalTimeZoneConfig?
        /// Time window config
        public var sms: ConnectCampaignsV2ClientTypes.TimeWindow?
        /// Time window config
        public var telephony: ConnectCampaignsV2ClientTypes.TimeWindow?

        public init(
            email: ConnectCampaignsV2ClientTypes.TimeWindow? = nil,
            localTimeZoneConfig: ConnectCampaignsV2ClientTypes.LocalTimeZoneConfig? = nil,
            sms: ConnectCampaignsV2ClientTypes.TimeWindow? = nil,
            telephony: ConnectCampaignsV2ClientTypes.TimeWindow? = nil
        )
        {
            self.email = email
            self.localTimeZoneConfig = localTimeZoneConfig
            self.sms = sms
            self.telephony = telephony
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Campaign schedule
    public struct Schedule: Swift.Sendable {
        /// Timestamp with no UTC offset or timezone
        /// This member is required.
        public var endTime: Foundation.Date?
        /// Time duration in ISO 8601 format
        public var refreshFrequency: Swift.String?
        /// Timestamp with no UTC offset or timezone
        /// This member is required.
        public var startTime: Foundation.Date?

        public init(
            endTime: Foundation.Date? = nil,
            refreshFrequency: Swift.String? = nil,
            startTime: Foundation.Date? = nil
        )
        {
            self.endTime = endTime
            self.refreshFrequency = refreshFrequency
            self.startTime = startTime
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Event trigger of the campaign
    public struct EventTrigger: Swift.Sendable {
        /// Amazon Resource Names(ARN)
        public var customerProfilesDomainArn: Swift.String?

        public init(
            customerProfilesDomainArn: Swift.String? = nil
        )
        {
            self.customerProfilesDomainArn = customerProfilesDomainArn
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Source of the campaign
    public enum Source: Swift.Sendable {
        /// Amazon Resource Names(ARN)
        case customerprofilessegmentarn(Swift.String)
        /// Event trigger of the campaign
        case eventtrigger(ConnectCampaignsV2ClientTypes.EventTrigger)
        case sdkUnknown(Swift.String)
    }
}

/// The request for CreateCampaign API.
public struct CreateCampaignInput: Swift.Sendable {
    /// Campaign Channel Subtype config
    /// This member is required.
    public var channelSubtypeConfig: ConnectCampaignsV2ClientTypes.ChannelSubtypeConfig?
    /// Communication limits config
    public var communicationLimitsOverride: ConnectCampaignsV2ClientTypes.CommunicationLimitsConfig?
    /// Campaign communication time config
    public var communicationTimeConfig: ConnectCampaignsV2ClientTypes.CommunicationTimeConfig?
    /// Amazon Resource Names(ARN)
    public var connectCampaignFlowArn: Swift.String?
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?
    /// The name of an Amazon Connect Campaign name.
    /// This member is required.
    public var name: Swift.String?
    /// Campaign schedule
    public var schedule: ConnectCampaignsV2ClientTypes.Schedule?
    /// Source of the campaign
    public var source: ConnectCampaignsV2ClientTypes.Source?
    /// Tag map with key and value.
    public var tags: [Swift.String: Swift.String]?

    public init(
        channelSubtypeConfig: ConnectCampaignsV2ClientTypes.ChannelSubtypeConfig? = nil,
        communicationLimitsOverride: ConnectCampaignsV2ClientTypes.CommunicationLimitsConfig? = nil,
        communicationTimeConfig: ConnectCampaignsV2ClientTypes.CommunicationTimeConfig? = nil,
        connectCampaignFlowArn: Swift.String? = nil,
        connectInstanceId: Swift.String? = nil,
        name: Swift.String? = nil,
        schedule: ConnectCampaignsV2ClientTypes.Schedule? = nil,
        source: ConnectCampaignsV2ClientTypes.Source? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.channelSubtypeConfig = channelSubtypeConfig
        self.communicationLimitsOverride = communicationLimitsOverride
        self.communicationTimeConfig = communicationTimeConfig
        self.connectCampaignFlowArn = connectCampaignFlowArn
        self.connectInstanceId = connectInstanceId
        self.name = name
        self.schedule = schedule
        self.source = source
        self.tags = tags
    }
}

/// The response for Create Campaign API
public struct CreateCampaignOutput: Swift.Sendable {
    /// The resource name of an Amazon Connect campaign.
    public var arn: Swift.String?
    /// Identifier representing a Campaign
    public var id: Swift.String?
    /// Tag map with key and value.
    public var tags: [Swift.String: Swift.String]?

    public init(
        arn: Swift.String? = nil,
        id: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.arn = arn
        self.id = id
        self.tags = tags
    }
}

/// The request for DeleteCampaign API.
public struct DeleteCampaignInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// The type of campaign channel subtype.
    public enum ChannelSubtype: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case email
        case sms
        case telephony
        case sdkUnknown(Swift.String)

        public static var allCases: [ChannelSubtype] {
            return [
                .email,
                .sms,
                .telephony
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .email: return "EMAIL"
            case .sms: return "SMS"
            case .telephony: return "TELEPHONY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The request for DeleteCampaignChannelSubtypeConfig API.
public struct DeleteCampaignChannelSubtypeConfigInput: Swift.Sendable {
    /// The type of campaign channel subtype.
    /// This member is required.
    public var channelSubtype: ConnectCampaignsV2ClientTypes.ChannelSubtype?
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        channelSubtype: ConnectCampaignsV2ClientTypes.ChannelSubtype? = nil,
        id: Swift.String? = nil
    )
    {
        self.channelSubtype = channelSubtype
        self.id = id
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// State of a campaign
    public enum CampaignState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// Campaign is in completed state
        case completed
        /// Campaign is in failed state
        case failed
        /// Campaign is in initialized state
        case initialized
        /// Campaign is in paused state
        case paused
        /// Campaign is in running state
        case running
        /// Campaign is in stopped state
        case stopped
        case sdkUnknown(Swift.String)

        public static var allCases: [CampaignState] {
            return [
                .completed,
                .failed,
                .initialized,
                .paused,
                .running,
                .stopped
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .completed: return "Completed"
            case .failed: return "Failed"
            case .initialized: return "Initialized"
            case .paused: return "Paused"
            case .running: return "Running"
            case .stopped: return "Stopped"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The request could not be processed because of conflict in the current state of the campaign.
public struct InvalidCampaignStateException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// State of a campaign
        /// This member is required.
        public internal(set) var state: ConnectCampaignsV2ClientTypes.CampaignState? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidCampaignStateException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        state: ConnectCampaignsV2ClientTypes.CampaignState? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.state = state
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// The type of campaign communication limits config.
    public enum CommunicationLimitsConfigType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case allChannelSubtypes
        case sdkUnknown(Swift.String)

        public static var allCases: [CommunicationLimitsConfigType] {
            return [
                .allChannelSubtypes
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .allChannelSubtypes: return "ALL_CHANNEL_SUBTYPES"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The request for DeleteCampaignCommunicationLimits API.
public struct DeleteCampaignCommunicationLimitsInput: Swift.Sendable {
    /// The type of campaign communication limits config.
    /// This member is required.
    public var config: ConnectCampaignsV2ClientTypes.CommunicationLimitsConfigType?
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        config: ConnectCampaignsV2ClientTypes.CommunicationLimitsConfigType? = nil,
        id: Swift.String? = nil
    )
    {
        self.config = config
        self.id = id
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// The type of campaign communication time config
    public enum CommunicationTimeConfigType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case email
        case sms
        case telephony
        case sdkUnknown(Swift.String)

        public static var allCases: [CommunicationTimeConfigType] {
            return [
                .email,
                .sms,
                .telephony
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .email: return "EMAIL"
            case .sms: return "SMS"
            case .telephony: return "TELEPHONY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The request for DeleteCampaignCommunicationTime API.
public struct DeleteCampaignCommunicationTimeInput: Swift.Sendable {
    /// The type of campaign communication time config
    /// This member is required.
    public var config: ConnectCampaignsV2ClientTypes.CommunicationTimeConfigType?
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        config: ConnectCampaignsV2ClientTypes.CommunicationTimeConfigType? = nil,
        id: Swift.String? = nil
    )
    {
        self.config = config
        self.id = id
    }
}

/// The request could not be processed because of conflict in the current state.
public struct InvalidStateException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidStateException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Enumeration of the policies to enact on existing campaigns during instance config deletion
    public enum CampaignDeletionPolicy: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case deleteAll
        case retainAll
        case sdkUnknown(Swift.String)

        public static var allCases: [CampaignDeletionPolicy] {
            return [
                .deleteAll,
                .retainAll
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .deleteAll: return "DELETE_ALL"
            case .retainAll: return "RETAIN_ALL"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The request for DeleteConnectInstanceConfig API.
public struct DeleteConnectInstanceConfigInput: Swift.Sendable {
    /// Enumeration of the policies to enact on existing campaigns during instance config deletion
    public var campaignDeletionPolicy: ConnectCampaignsV2ClientTypes.CampaignDeletionPolicy?
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?

    public init(
        campaignDeletionPolicy: ConnectCampaignsV2ClientTypes.CampaignDeletionPolicy? = nil,
        connectInstanceId: Swift.String? = nil
    )
    {
        self.campaignDeletionPolicy = campaignDeletionPolicy
        self.connectInstanceId = connectInstanceId
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Customer Profiles integration identifier
    public struct CustomerProfilesIntegrationIdentifier: Swift.Sendable {
        /// Amazon Resource Names(ARN)
        /// This member is required.
        public var domainArn: Swift.String?

        public init(
            domainArn: Swift.String? = nil
        )
        {
            self.domainArn = domainArn
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Q Connect integration identifier
    public struct QConnectIntegrationIdentifier: Swift.Sendable {
        /// Amazon Resource Names(ARN)
        /// This member is required.
        public var knowledgeBaseArn: Swift.String?

        public init(
            knowledgeBaseArn: Swift.String? = nil
        )
        {
            self.knowledgeBaseArn = knowledgeBaseArn
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Integration identifier for Connect instance
    public enum IntegrationIdentifier: Swift.Sendable {
        /// Customer Profiles integration identifier
        case customerprofiles(ConnectCampaignsV2ClientTypes.CustomerProfilesIntegrationIdentifier)
        /// Q Connect integration identifier
        case qconnect(ConnectCampaignsV2ClientTypes.QConnectIntegrationIdentifier)
        case sdkUnknown(Swift.String)
    }
}

/// The request for DeleteConnectInstanceIntegration API.
public struct DeleteConnectInstanceIntegrationInput: Swift.Sendable {
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?
    /// Integration identifier for Connect instance
    /// This member is required.
    public var integrationIdentifier: ConnectCampaignsV2ClientTypes.IntegrationIdentifier?

    public init(
        connectInstanceId: Swift.String? = nil,
        integrationIdentifier: ConnectCampaignsV2ClientTypes.IntegrationIdentifier? = nil
    )
    {
        self.connectInstanceId = connectInstanceId
        self.integrationIdentifier = integrationIdentifier
    }
}

/// The request for DeleteInstanceOnboardingJob API.
public struct DeleteInstanceOnboardingJobInput: Swift.Sendable {
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?

    public init(
        connectInstanceId: Swift.String? = nil
    )
    {
        self.connectInstanceId = connectInstanceId
    }
}

/// The request for DescribeCampaign API.
public struct DescribeCampaignInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// An Amazon Connect campaign.
    public struct Campaign: Swift.Sendable {
        /// The resource name of an Amazon Connect campaign.
        /// This member is required.
        public var arn: Swift.String?
        /// Campaign Channel Subtype config
        /// This member is required.
        public var channelSubtypeConfig: ConnectCampaignsV2ClientTypes.ChannelSubtypeConfig?
        /// Communication limits config
        public var communicationLimitsOverride: ConnectCampaignsV2ClientTypes.CommunicationLimitsConfig?
        /// Campaign communication time config
        public var communicationTimeConfig: ConnectCampaignsV2ClientTypes.CommunicationTimeConfig?
        /// Amazon Resource Names(ARN)
        public var connectCampaignFlowArn: Swift.String?
        /// Amazon Connect Instance Id
        /// This member is required.
        public var connectInstanceId: Swift.String?
        /// Identifier representing a Campaign
        /// This member is required.
        public var id: Swift.String?
        /// The name of an Amazon Connect Campaign name.
        /// This member is required.
        public var name: Swift.String?
        /// Campaign schedule
        public var schedule: ConnectCampaignsV2ClientTypes.Schedule?
        /// Source of the campaign
        public var source: ConnectCampaignsV2ClientTypes.Source?
        /// Tag map with key and value.
        public var tags: [Swift.String: Swift.String]?

        public init(
            arn: Swift.String? = nil,
            channelSubtypeConfig: ConnectCampaignsV2ClientTypes.ChannelSubtypeConfig? = nil,
            communicationLimitsOverride: ConnectCampaignsV2ClientTypes.CommunicationLimitsConfig? = nil,
            communicationTimeConfig: ConnectCampaignsV2ClientTypes.CommunicationTimeConfig? = nil,
            connectCampaignFlowArn: Swift.String? = nil,
            connectInstanceId: Swift.String? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil,
            schedule: ConnectCampaignsV2ClientTypes.Schedule? = nil,
            source: ConnectCampaignsV2ClientTypes.Source? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.arn = arn
            self.channelSubtypeConfig = channelSubtypeConfig
            self.communicationLimitsOverride = communicationLimitsOverride
            self.communicationTimeConfig = communicationTimeConfig
            self.connectCampaignFlowArn = connectCampaignFlowArn
            self.connectInstanceId = connectInstanceId
            self.id = id
            self.name = name
            self.schedule = schedule
            self.source = source
            self.tags = tags
        }
    }
}

/// The response for DescribeCampaign API.
public struct DescribeCampaignOutput: Swift.Sendable {
    /// An Amazon Connect campaign.
    public var campaign: ConnectCampaignsV2ClientTypes.Campaign?

    public init(
        campaign: ConnectCampaignsV2ClientTypes.Campaign? = nil
    )
    {
        self.campaign = campaign
    }
}

/// The request for GetCampaignState API.
public struct GetCampaignStateInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

/// The response for GetCampaignState API.
public struct GetCampaignStateOutput: Swift.Sendable {
    /// State of a campaign
    public var state: ConnectCampaignsV2ClientTypes.CampaignState?

    public init(
        state: ConnectCampaignsV2ClientTypes.CampaignState? = nil
    )
    {
        self.state = state
    }
}

/// The request for GetCampaignStateBatch API.
public struct GetCampaignStateBatchInput: Swift.Sendable {
    /// List of CampaignId
    /// This member is required.
    public var campaignIds: [Swift.String]?

    public init(
        campaignIds: [Swift.String]? = nil
    )
    {
        self.campaignIds = campaignIds
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// A predefined code indicating the error that caused the failure in getting state of campaigns
    public enum GetCampaignStateBatchFailureCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// The specified resource was not found
        case resourceNotFound
        /// Unexpected error during processing of request
        case unknownError
        case sdkUnknown(Swift.String)

        public static var allCases: [GetCampaignStateBatchFailureCode] {
            return [
                .resourceNotFound,
                .unknownError
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .resourceNotFound: return "ResourceNotFound"
            case .unknownError: return "UnknownError"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Failed response of campaign state
    public struct FailedCampaignStateResponse: Swift.Sendable {
        /// Identifier representing a Campaign
        public var campaignId: Swift.String?
        /// A predefined code indicating the error that caused the failure in getting state of campaigns
        public var failureCode: ConnectCampaignsV2ClientTypes.GetCampaignStateBatchFailureCode?

        public init(
            campaignId: Swift.String? = nil,
            failureCode: ConnectCampaignsV2ClientTypes.GetCampaignStateBatchFailureCode? = nil
        )
        {
            self.campaignId = campaignId
            self.failureCode = failureCode
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Successful response of campaign state
    public struct SuccessfulCampaignStateResponse: Swift.Sendable {
        /// Identifier representing a Campaign
        public var campaignId: Swift.String?
        /// State of a campaign
        public var state: ConnectCampaignsV2ClientTypes.CampaignState?

        public init(
            campaignId: Swift.String? = nil,
            state: ConnectCampaignsV2ClientTypes.CampaignState? = nil
        )
        {
            self.campaignId = campaignId
            self.state = state
        }
    }
}

/// The response for GetCampaignStateBatch API.
public struct GetCampaignStateBatchOutput: Swift.Sendable {
    /// List of failed requests of campaign state
    public var failedRequests: [ConnectCampaignsV2ClientTypes.FailedCampaignStateResponse]?
    /// List of successful response of campaign state
    public var successfulRequests: [ConnectCampaignsV2ClientTypes.SuccessfulCampaignStateResponse]?

    public init(
        failedRequests: [ConnectCampaignsV2ClientTypes.FailedCampaignStateResponse]? = nil,
        successfulRequests: [ConnectCampaignsV2ClientTypes.SuccessfulCampaignStateResponse]? = nil
    )
    {
        self.failedRequests = failedRequests
        self.successfulRequests = successfulRequests
    }
}

/// The request for GetConnectInstanceConfig API.
public struct GetConnectInstanceConfigInput: Swift.Sendable {
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?

    public init(
        connectInstanceId: Swift.String? = nil
    )
    {
        self.connectInstanceId = connectInstanceId
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Server-side encryption type.
    public enum EncryptionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case kms
        case sdkUnknown(Swift.String)

        public static var allCases: [EncryptionType] {
            return [
                .kms
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .kms: return "KMS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Encryption config for Connect Instance. Note that sensitive data will always be encrypted. If disabled, service will perform encryption with its own key. If enabled, a KMS key id needs to be provided and KMS charges will apply. KMS is only type supported
    public struct EncryptionConfig: Swift.Sendable {
        /// Boolean to indicate if custom encryption has been enabled.
        /// This member is required.
        public var enabled: Swift.Bool
        /// Server-side encryption type.
        public var encryptionType: ConnectCampaignsV2ClientTypes.EncryptionType?
        /// KMS key id/arn for encryption config.
        public var keyArn: Swift.String?

        public init(
            enabled: Swift.Bool = false,
            encryptionType: ConnectCampaignsV2ClientTypes.EncryptionType? = nil,
            keyArn: Swift.String? = nil
        )
        {
            self.enabled = enabled
            self.encryptionType = encryptionType
            self.keyArn = keyArn
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Instance config object
    public struct InstanceConfig: Swift.Sendable {
        /// Amazon Connect Instance Id
        /// This member is required.
        public var connectInstanceId: Swift.String?
        /// Encryption config for Connect Instance. Note that sensitive data will always be encrypted. If disabled, service will perform encryption with its own key. If enabled, a KMS key id needs to be provided and KMS charges will apply. KMS is only type supported
        /// This member is required.
        public var encryptionConfig: ConnectCampaignsV2ClientTypes.EncryptionConfig?
        /// Service linked role arn
        /// This member is required.
        public var serviceLinkedRoleArn: Swift.String?

        public init(
            connectInstanceId: Swift.String? = nil,
            encryptionConfig: ConnectCampaignsV2ClientTypes.EncryptionConfig? = nil,
            serviceLinkedRoleArn: Swift.String? = nil
        )
        {
            self.connectInstanceId = connectInstanceId
            self.encryptionConfig = encryptionConfig
            self.serviceLinkedRoleArn = serviceLinkedRoleArn
        }
    }
}

/// The response for GetConnectInstanceConfig API.
public struct GetConnectInstanceConfigOutput: Swift.Sendable {
    /// Instance config object
    public var connectInstanceConfig: ConnectCampaignsV2ClientTypes.InstanceConfig?

    public init(
        connectInstanceConfig: ConnectCampaignsV2ClientTypes.InstanceConfig? = nil
    )
    {
        self.connectInstanceConfig = connectInstanceConfig
    }
}

/// The request for GetInstanceOnboardingJobStatus API.
public struct GetInstanceOnboardingJobStatusInput: Swift.Sendable {
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?

    public init(
        connectInstanceId: Swift.String? = nil
    )
    {
        self.connectInstanceId = connectInstanceId
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Enumeration of the possible failure codes for instance onboarding job
    public enum InstanceOnboardingJobFailureCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case eventBridgeAccessDenied
        case eventBridgeManagedRuleLimitExceeded
        case iamAccessDenied
        case internalFailure
        case kmsAccessDenied
        case kmsKeyNotFound
        case sdkUnknown(Swift.String)

        public static var allCases: [InstanceOnboardingJobFailureCode] {
            return [
                .eventBridgeAccessDenied,
                .eventBridgeManagedRuleLimitExceeded,
                .iamAccessDenied,
                .internalFailure,
                .kmsAccessDenied,
                .kmsKeyNotFound
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .eventBridgeAccessDenied: return "EVENT_BRIDGE_ACCESS_DENIED"
            case .eventBridgeManagedRuleLimitExceeded: return "EVENT_BRIDGE_MANAGED_RULE_LIMIT_EXCEEDED"
            case .iamAccessDenied: return "IAM_ACCESS_DENIED"
            case .internalFailure: return "INTERNAL_FAILURE"
            case .kmsAccessDenied: return "KMS_ACCESS_DENIED"
            case .kmsKeyNotFound: return "KMS_KEY_NOT_FOUND"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Enumeration of the possible states for instance onboarding job
    public enum InstanceOnboardingJobStatusCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case inProgress
        case succeeded
        case sdkUnknown(Swift.String)

        public static var allCases: [InstanceOnboardingJobStatusCode] {
            return [
                .failed,
                .inProgress,
                .succeeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case .succeeded: return "SUCCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Instance onboarding job status object
    public struct InstanceOnboardingJobStatus: Swift.Sendable {
        /// Amazon Connect Instance Id
        /// This member is required.
        public var connectInstanceId: Swift.String?
        /// Enumeration of the possible failure codes for instance onboarding job
        public var failureCode: ConnectCampaignsV2ClientTypes.InstanceOnboardingJobFailureCode?
        /// Enumeration of the possible states for instance onboarding job
        /// This member is required.
        public var status: ConnectCampaignsV2ClientTypes.InstanceOnboardingJobStatusCode?

        public init(
            connectInstanceId: Swift.String? = nil,
            failureCode: ConnectCampaignsV2ClientTypes.InstanceOnboardingJobFailureCode? = nil,
            status: ConnectCampaignsV2ClientTypes.InstanceOnboardingJobStatusCode? = nil
        )
        {
            self.connectInstanceId = connectInstanceId
            self.failureCode = failureCode
            self.status = status
        }
    }
}

/// The response for GetInstanceOnboardingJobStatus API.
public struct GetInstanceOnboardingJobStatusOutput: Swift.Sendable {
    /// Instance onboarding job status object
    public var connectInstanceOnboardingJobStatus: ConnectCampaignsV2ClientTypes.InstanceOnboardingJobStatus?

    public init(
        connectInstanceOnboardingJobStatus: ConnectCampaignsV2ClientTypes.InstanceOnboardingJobStatus? = nil
    )
    {
        self.connectInstanceOnboardingJobStatus = connectInstanceOnboardingJobStatus
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Operators for Connect instance identifier filter
    public enum InstanceIdFilterOperator: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// Equals operator
        case eq
        case sdkUnknown(Swift.String)

        public static var allCases: [InstanceIdFilterOperator] {
            return [
                .eq
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .eq: return "Eq"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Connect instance identifier filter
    public struct InstanceIdFilter: Swift.Sendable {
        /// Operators for Connect instance identifier filter
        /// This member is required.
        public var `operator`: ConnectCampaignsV2ClientTypes.InstanceIdFilterOperator?
        /// Amazon Connect Instance Id
        /// This member is required.
        public var value: Swift.String?

        public init(
            `operator`: ConnectCampaignsV2ClientTypes.InstanceIdFilterOperator? = nil,
            value: Swift.String? = nil
        )
        {
            self.`operator` = `operator`
            self.value = value
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Filter model by type
    public struct CampaignFilters: Swift.Sendable {
        /// Connect instance identifier filter
        public var instanceIdFilter: ConnectCampaignsV2ClientTypes.InstanceIdFilter?

        public init(
            instanceIdFilter: ConnectCampaignsV2ClientTypes.InstanceIdFilter? = nil
        )
        {
            self.instanceIdFilter = instanceIdFilter
        }
    }
}

/// The request for ListCampaigns API.
public struct ListCampaignsInput: Swift.Sendable {
    /// Filter model by type
    public var filters: ConnectCampaignsV2ClientTypes.CampaignFilters?
    /// The maximum number of results to return per page.
    public var maxResults: Swift.Int?
    /// The token for the next set of results.
    public var nextToken: Swift.String?

    public init(
        filters: ConnectCampaignsV2ClientTypes.CampaignFilters? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// An Amazon Connect campaign summary.
    public struct CampaignSummary: Swift.Sendable {
        /// The resource name of an Amazon Connect campaign.
        /// This member is required.
        public var arn: Swift.String?
        /// Channel subtype list
        /// This member is required.
        public var channelSubtypes: [ConnectCampaignsV2ClientTypes.ChannelSubtype]?
        /// Amazon Resource Names(ARN)
        public var connectCampaignFlowArn: Swift.String?
        /// Amazon Connect Instance Id
        /// This member is required.
        public var connectInstanceId: Swift.String?
        /// Identifier representing a Campaign
        /// This member is required.
        public var id: Swift.String?
        /// The name of an Amazon Connect Campaign name.
        /// This member is required.
        public var name: Swift.String?
        /// Campaign schedule
        public var schedule: ConnectCampaignsV2ClientTypes.Schedule?

        public init(
            arn: Swift.String? = nil,
            channelSubtypes: [ConnectCampaignsV2ClientTypes.ChannelSubtype]? = nil,
            connectCampaignFlowArn: Swift.String? = nil,
            connectInstanceId: Swift.String? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil,
            schedule: ConnectCampaignsV2ClientTypes.Schedule? = nil
        )
        {
            self.arn = arn
            self.channelSubtypes = channelSubtypes
            self.connectCampaignFlowArn = connectCampaignFlowArn
            self.connectInstanceId = connectInstanceId
            self.id = id
            self.name = name
            self.schedule = schedule
        }
    }
}

/// The response for ListCampaigns API.
public struct ListCampaignsOutput: Swift.Sendable {
    /// A list of Amazon Connect campaigns.
    public var campaignSummaryList: [ConnectCampaignsV2ClientTypes.CampaignSummary]?
    /// The token for the next set of results.
    public var nextToken: Swift.String?

    public init(
        campaignSummaryList: [ConnectCampaignsV2ClientTypes.CampaignSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.campaignSummaryList = campaignSummaryList
        self.nextToken = nextToken
    }
}

/// The request for ListConnectInstanceIntegrations API.
public struct ListConnectInstanceIntegrationsInput: Swift.Sendable {
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?
    /// The maximum number of results to return per page.
    public var maxResults: Swift.Int?
    /// The token for the next set of results.
    public var nextToken: Swift.String?

    public init(
        connectInstanceId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.connectInstanceId = connectInstanceId
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Enumeration of Customer Profiles event type
    public enum EventType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case campaignEmail
        case campaignOrchestration
        case campaignSms
        case campaignTelephony
        case sdkUnknown(Swift.String)

        public static var allCases: [EventType] {
            return [
                .campaignEmail,
                .campaignOrchestration,
                .campaignSms,
                .campaignTelephony
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .campaignEmail: return "Campaign-Email"
            case .campaignOrchestration: return "Campaign-Orchestration"
            case .campaignSms: return "Campaign-SMS"
            case .campaignTelephony: return "Campaign-Telephony"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Customer Profiles integration summary
    public struct CustomerProfilesIntegrationSummary: Swift.Sendable {
        /// Amazon Resource Names(ARN)
        /// This member is required.
        public var domainArn: Swift.String?
        /// Object type names map.
        /// This member is required.
        public var objectTypeNames: [Swift.String: Swift.String]?

        public init(
            domainArn: Swift.String? = nil,
            objectTypeNames: [Swift.String: Swift.String]? = nil
        )
        {
            self.domainArn = domainArn
            self.objectTypeNames = objectTypeNames
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Q Connect integration summary
    public struct QConnectIntegrationSummary: Swift.Sendable {
        /// Amazon Resource Names(ARN)
        /// This member is required.
        public var knowledgeBaseArn: Swift.String?

        public init(
            knowledgeBaseArn: Swift.String? = nil
        )
        {
            self.knowledgeBaseArn = knowledgeBaseArn
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Integration summary for Connect instance.
    public enum IntegrationSummary: Swift.Sendable {
        /// Customer Profiles integration summary
        case customerprofiles(ConnectCampaignsV2ClientTypes.CustomerProfilesIntegrationSummary)
        /// Q Connect integration summary
        case qconnect(ConnectCampaignsV2ClientTypes.QConnectIntegrationSummary)
        case sdkUnknown(Swift.String)
    }
}

/// The response for ListConnectInstanceIntegrations API.
public struct ListConnectInstanceIntegrationsOutput: Swift.Sendable {
    /// A list of Amazon Connect Instance Integrations.
    public var integrationSummaryList: [ConnectCampaignsV2ClientTypes.IntegrationSummary]?
    /// The token for the next set of results.
    public var nextToken: Swift.String?

    public init(
        integrationSummaryList: [ConnectCampaignsV2ClientTypes.IntegrationSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.integrationSummaryList = integrationSummaryList
        self.nextToken = nextToken
    }
}

/// The request for ListTagsForResource API.
public struct ListTagsForResourceInput: Swift.Sendable {
    /// Amazon Resource Names(ARN)
    /// This member is required.
    public var arn: Swift.String?

    public init(
        arn: Swift.String? = nil
    )
    {
        self.arn = arn
    }
}

/// The request for ListTagsForResource API.
public struct ListTagsForResourceOutput: Swift.Sendable {
    /// Tag map with key and value.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

/// The request for PauseCampaign API.
public struct PauseCampaignInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Customer Profiles integration config
    public struct CustomerProfilesIntegrationConfig: Swift.Sendable {
        /// Amazon Resource Names(ARN)
        /// This member is required.
        public var domainArn: Swift.String?
        /// Object type names map.
        /// This member is required.
        public var objectTypeNames: [Swift.String: Swift.String]?

        public init(
            domainArn: Swift.String? = nil,
            objectTypeNames: [Swift.String: Swift.String]? = nil
        )
        {
            self.domainArn = domainArn
            self.objectTypeNames = objectTypeNames
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Q Connect integration config
    public struct QConnectIntegrationConfig: Swift.Sendable {
        /// Amazon Resource Names(ARN)
        /// This member is required.
        public var knowledgeBaseArn: Swift.String?

        public init(
            knowledgeBaseArn: Swift.String? = nil
        )
        {
            self.knowledgeBaseArn = knowledgeBaseArn
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Integration config for Connect Instance
    public enum IntegrationConfig: Swift.Sendable {
        /// Customer Profiles integration config
        case customerprofiles(ConnectCampaignsV2ClientTypes.CustomerProfilesIntegrationConfig)
        /// Q Connect integration config
        case qconnect(ConnectCampaignsV2ClientTypes.QConnectIntegrationConfig)
        case sdkUnknown(Swift.String)
    }
}

/// The request for PutConnectInstanceIntegration API.
public struct PutConnectInstanceIntegrationInput: Swift.Sendable {
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?
    /// Integration config for Connect Instance
    /// This member is required.
    public var integrationConfig: ConnectCampaignsV2ClientTypes.IntegrationConfig?

    public init(
        connectInstanceId: Swift.String? = nil,
        integrationConfig: ConnectCampaignsV2ClientTypes.IntegrationConfig? = nil
    )
    {
        self.connectInstanceId = connectInstanceId
        self.integrationConfig = integrationConfig
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Parameters for the Email Channel Subtype
    public struct EmailChannelSubtypeParameters: Swift.Sendable {
        /// Source/Destination Email address used for Email messages
        public var connectSourceEmailAddress: Swift.String?
        /// Source/Destination Email address used for Email messages
        /// This member is required.
        public var destinationEmailAddress: Swift.String?
        /// Amazon Resource Names(ARN)
        public var templateArn: Swift.String?
        /// A custom key-value pair using an attribute map. The attributes are standard Amazon Connect attributes, and can be accessed in contact flows just like any other contact attributes.
        /// This member is required.
        public var templateParameters: [Swift.String: Swift.String]?

        public init(
            connectSourceEmailAddress: Swift.String? = nil,
            destinationEmailAddress: Swift.String? = nil,
            templateArn: Swift.String? = nil,
            templateParameters: [Swift.String: Swift.String]? = nil
        )
        {
            self.connectSourceEmailAddress = connectSourceEmailAddress
            self.destinationEmailAddress = destinationEmailAddress
            self.templateArn = templateArn
            self.templateParameters = templateParameters
        }
    }
}

extension ConnectCampaignsV2ClientTypes.EmailChannelSubtypeParameters: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "EmailChannelSubtypeParameters(templateArn: \(Swift.String(describing: templateArn)), connectSourceEmailAddress: \"CONTENT_REDACTED\", destinationEmailAddress: \"CONTENT_REDACTED\", templateParameters: \"CONTENT_REDACTED\")"}
}

extension ConnectCampaignsV2ClientTypes {

    /// Parameters for the SMS Channel Subtype
    public struct SmsChannelSubtypeParameters: Swift.Sendable {
        /// Amazon Resource Names(ARN)
        public var connectSourcePhoneNumberArn: Swift.String?
        /// The phone number of the customer, in E.164 format.
        /// This member is required.
        public var destinationPhoneNumber: Swift.String?
        /// Amazon Resource Names(ARN)
        public var templateArn: Swift.String?
        /// A custom key-value pair using an attribute map. The attributes are standard Amazon Connect attributes, and can be accessed in contact flows just like any other contact attributes.
        /// This member is required.
        public var templateParameters: [Swift.String: Swift.String]?

        public init(
            connectSourcePhoneNumberArn: Swift.String? = nil,
            destinationPhoneNumber: Swift.String? = nil,
            templateArn: Swift.String? = nil,
            templateParameters: [Swift.String: Swift.String]? = nil
        )
        {
            self.connectSourcePhoneNumberArn = connectSourcePhoneNumberArn
            self.destinationPhoneNumber = destinationPhoneNumber
            self.templateArn = templateArn
            self.templateParameters = templateParameters
        }
    }
}

extension ConnectCampaignsV2ClientTypes.SmsChannelSubtypeParameters: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "SmsChannelSubtypeParameters(connectSourcePhoneNumberArn: \(Swift.String(describing: connectSourcePhoneNumberArn)), templateArn: \(Swift.String(describing: templateArn)), destinationPhoneNumber: \"CONTENT_REDACTED\", templateParameters: \"CONTENT_REDACTED\")"}
}

extension ConnectCampaignsV2ClientTypes {

    /// Parameters for the Telephony Channel Subtype
    public struct TelephonyChannelSubtypeParameters: Swift.Sendable {
        /// Answering Machine Detection config
        public var answerMachineDetectionConfig: ConnectCampaignsV2ClientTypes.AnswerMachineDetectionConfig?
        /// A custom key-value pair using an attribute map. The attributes are standard Amazon Connect attributes, and can be accessed in contact flows just like any other contact attributes.
        /// This member is required.
        public var attributes: [Swift.String: Swift.String]?
        /// The phone number associated with the Amazon Connect instance, in E.164 format. If you do not specify a source phone number, you must specify a queue.
        public var connectSourcePhoneNumber: Swift.String?
        /// The phone number of the customer, in E.164 format.
        /// This member is required.
        public var destinationPhoneNumber: Swift.String?

        public init(
            answerMachineDetectionConfig: ConnectCampaignsV2ClientTypes.AnswerMachineDetectionConfig? = nil,
            attributes: [Swift.String: Swift.String]? = nil,
            connectSourcePhoneNumber: Swift.String? = nil,
            destinationPhoneNumber: Swift.String? = nil
        )
        {
            self.answerMachineDetectionConfig = answerMachineDetectionConfig
            self.attributes = attributes
            self.connectSourcePhoneNumber = connectSourcePhoneNumber
            self.destinationPhoneNumber = destinationPhoneNumber
        }
    }
}

extension ConnectCampaignsV2ClientTypes.TelephonyChannelSubtypeParameters: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "TelephonyChannelSubtypeParameters(answerMachineDetectionConfig: \(Swift.String(describing: answerMachineDetectionConfig)), connectSourcePhoneNumber: \(Swift.String(describing: connectSourcePhoneNumber)), attributes: \"CONTENT_REDACTED\", destinationPhoneNumber: \"CONTENT_REDACTED\")"}
}

extension ConnectCampaignsV2ClientTypes {

    /// ChannelSubtypeParameters for an outbound request
    public enum ChannelSubtypeParameters: Swift.Sendable {
        /// Parameters for the Telephony Channel Subtype
        case telephony(ConnectCampaignsV2ClientTypes.TelephonyChannelSubtypeParameters)
        /// Parameters for the SMS Channel Subtype
        case sms(ConnectCampaignsV2ClientTypes.SmsChannelSubtypeParameters)
        /// Parameters for the Email Channel Subtype
        case email(ConnectCampaignsV2ClientTypes.EmailChannelSubtypeParameters)
        case sdkUnknown(Swift.String)
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// An outbound request for a campaign.
    public struct OutboundRequest: Swift.Sendable {
        /// ChannelSubtypeParameters for an outbound request
        /// This member is required.
        public var channelSubtypeParameters: ConnectCampaignsV2ClientTypes.ChannelSubtypeParameters?
        /// Client provided parameter used for idempotency. Its value must be unique for each request.
        /// This member is required.
        public var clientToken: Swift.String?
        /// Timestamp with no UTC offset or timezone
        /// This member is required.
        public var expirationTime: Foundation.Date?

        public init(
            channelSubtypeParameters: ConnectCampaignsV2ClientTypes.ChannelSubtypeParameters? = nil,
            clientToken: Swift.String? = nil,
            expirationTime: Foundation.Date? = nil
        )
        {
            self.channelSubtypeParameters = channelSubtypeParameters
            self.clientToken = clientToken
            self.expirationTime = expirationTime
        }
    }
}

/// The request for PutOutboundRequestBatch API.
public struct PutOutboundRequestBatchInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?
    /// A list of outbound requests.
    /// This member is required.
    public var outboundRequests: [ConnectCampaignsV2ClientTypes.OutboundRequest]?

    public init(
        id: Swift.String? = nil,
        outboundRequests: [ConnectCampaignsV2ClientTypes.OutboundRequest]? = nil
    )
    {
        self.id = id
        self.outboundRequests = outboundRequests
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// A predefined code indicating the error that caused the failure.
    public enum FailureCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// Request throttled due to large number of pending dial requests
        case bufferLimitExceeded
        /// The request failed to satisfy the constraints specified by the service
        case invalidInput
        /// The request was throttled due to excessive usage
        case requestThrottled
        /// Unexpected error during processing of request
        case unknownError
        case sdkUnknown(Swift.String)

        public static var allCases: [FailureCode] {
            return [
                .bufferLimitExceeded,
                .invalidInput,
                .requestThrottled,
                .unknownError
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .bufferLimitExceeded: return "BufferLimitExceeded"
            case .invalidInput: return "InvalidInput"
            case .requestThrottled: return "RequestThrottled"
            case .unknownError: return "UnknownError"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// A failed request identified by the unique client token.
    public struct FailedRequest: Swift.Sendable {
        /// Client provided parameter used for idempotency. Its value must be unique for each request.
        public var clientToken: Swift.String?
        /// A predefined code indicating the error that caused the failure.
        public var failureCode: ConnectCampaignsV2ClientTypes.FailureCode?
        /// Identifier representing a Dial request
        public var id: Swift.String?

        public init(
            clientToken: Swift.String? = nil,
            failureCode: ConnectCampaignsV2ClientTypes.FailureCode? = nil,
            id: Swift.String? = nil
        )
        {
            self.clientToken = clientToken
            self.failureCode = failureCode
            self.id = id
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// A successful request identified by the unique client token.
    public struct SuccessfulRequest: Swift.Sendable {
        /// Client provided parameter used for idempotency. Its value must be unique for each request.
        public var clientToken: Swift.String?
        /// Identifier representing a Dial request
        public var id: Swift.String?

        public init(
            clientToken: Swift.String? = nil,
            id: Swift.String? = nil
        )
        {
            self.clientToken = clientToken
            self.id = id
        }
    }
}

/// The response for PutOutboundRequestBatch API.
public struct PutOutboundRequestBatchOutput: Swift.Sendable {
    /// A list of failed requests.
    public var failedRequests: [ConnectCampaignsV2ClientTypes.FailedRequest]?
    /// A list of successful requests identified by the unique client token.
    public var successfulRequests: [ConnectCampaignsV2ClientTypes.SuccessfulRequest]?

    public init(
        failedRequests: [ConnectCampaignsV2ClientTypes.FailedRequest]? = nil,
        successfulRequests: [ConnectCampaignsV2ClientTypes.SuccessfulRequest]? = nil
    )
    {
        self.failedRequests = failedRequests
        self.successfulRequests = successfulRequests
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Information about a profile outbound request
    public struct ProfileOutboundRequest: Swift.Sendable {
        /// Client provided parameter used for idempotency. Its value must be unique for each request.
        /// This member is required.
        public var clientToken: Swift.String?
        /// Timestamp with no UTC offset or timezone
        public var expirationTime: Foundation.Date?
        /// Identifier of the customer profile
        /// This member is required.
        public var profileId: Swift.String?

        public init(
            clientToken: Swift.String? = nil,
            expirationTime: Foundation.Date? = nil,
            profileId: Swift.String? = nil
        )
        {
            self.clientToken = clientToken
            self.expirationTime = expirationTime
            self.profileId = profileId
        }
    }
}

/// The request for PutProfileOutboundRequestBatch API
public struct PutProfileOutboundRequestBatchInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?
    /// List of profile outbound requests
    /// This member is required.
    public var profileOutboundRequests: [ConnectCampaignsV2ClientTypes.ProfileOutboundRequest]?

    public init(
        id: Swift.String? = nil,
        profileOutboundRequests: [ConnectCampaignsV2ClientTypes.ProfileOutboundRequest]? = nil
    )
    {
        self.id = id
        self.profileOutboundRequests = profileOutboundRequests
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Predefined code indicating the error that caused the failure
    public enum ProfileOutboundRequestFailureCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// The specified resource conflicts with another resource
        case conflict
        /// The request failed to satisfy the constraints specified by the service
        case invalidInput
        /// Request throttled due to large number of requests
        case requestThrottled
        /// The specified resource was not found
        case resourceNotFound
        /// Unexpected error during processing of request
        case unknownError
        case sdkUnknown(Swift.String)

        public static var allCases: [ProfileOutboundRequestFailureCode] {
            return [
                .conflict,
                .invalidInput,
                .requestThrottled,
                .resourceNotFound,
                .unknownError
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .conflict: return "Conflict"
            case .invalidInput: return "InvalidInput"
            case .requestThrottled: return "RequestThrottled"
            case .resourceNotFound: return "ResourceNotFound"
            case .unknownError: return "UnknownError"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Failure details for a profile outbound request
    public struct FailedProfileOutboundRequest: Swift.Sendable {
        /// Client provided parameter used for idempotency. Its value must be unique for each request.
        public var clientToken: Swift.String?
        /// Predefined code indicating the error that caused the failure
        public var failureCode: ConnectCampaignsV2ClientTypes.ProfileOutboundRequestFailureCode?
        /// Identifier of the profile outbound request
        public var id: Swift.String?

        public init(
            clientToken: Swift.String? = nil,
            failureCode: ConnectCampaignsV2ClientTypes.ProfileOutboundRequestFailureCode? = nil,
            id: Swift.String? = nil
        )
        {
            self.clientToken = clientToken
            self.failureCode = failureCode
            self.id = id
        }
    }
}

extension ConnectCampaignsV2ClientTypes {

    /// Success details for a profile outbound request
    public struct SuccessfulProfileOutboundRequest: Swift.Sendable {
        /// Client provided parameter used for idempotency. Its value must be unique for each request.
        public var clientToken: Swift.String?
        /// Identifier of the profile outbound request
        public var id: Swift.String?

        public init(
            clientToken: Swift.String? = nil,
            id: Swift.String? = nil
        )
        {
            self.clientToken = clientToken
            self.id = id
        }
    }
}

/// The response for PutProfileOutboundRequestBatch API
public struct PutProfileOutboundRequestBatchOutput: Swift.Sendable {
    /// List of failed profile outbound requests
    public var failedRequests: [ConnectCampaignsV2ClientTypes.FailedProfileOutboundRequest]?
    /// List of successful profile outbound requests
    public var successfulRequests: [ConnectCampaignsV2ClientTypes.SuccessfulProfileOutboundRequest]?

    public init(
        failedRequests: [ConnectCampaignsV2ClientTypes.FailedProfileOutboundRequest]? = nil,
        successfulRequests: [ConnectCampaignsV2ClientTypes.SuccessfulProfileOutboundRequest]? = nil
    )
    {
        self.failedRequests = failedRequests
        self.successfulRequests = successfulRequests
    }
}

/// The request for ResumeCampaign API.
public struct ResumeCampaignInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

/// The request for StartCampaign API.
public struct StartCampaignInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

/// The request for StartInstanceOnboardingJob API.
public struct StartInstanceOnboardingJobInput: Swift.Sendable {
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?
    /// Encryption config for Connect Instance. Note that sensitive data will always be encrypted. If disabled, service will perform encryption with its own key. If enabled, a KMS key id needs to be provided and KMS charges will apply. KMS is only type supported
    /// This member is required.
    public var encryptionConfig: ConnectCampaignsV2ClientTypes.EncryptionConfig?

    public init(
        connectInstanceId: Swift.String? = nil,
        encryptionConfig: ConnectCampaignsV2ClientTypes.EncryptionConfig? = nil
    )
    {
        self.connectInstanceId = connectInstanceId
        self.encryptionConfig = encryptionConfig
    }
}

/// The response for StartInstanceOnboardingJob API.
public struct StartInstanceOnboardingJobOutput: Swift.Sendable {
    /// Instance onboarding job status object
    public var connectInstanceOnboardingJobStatus: ConnectCampaignsV2ClientTypes.InstanceOnboardingJobStatus?

    public init(
        connectInstanceOnboardingJobStatus: ConnectCampaignsV2ClientTypes.InstanceOnboardingJobStatus? = nil
    )
    {
        self.connectInstanceOnboardingJobStatus = connectInstanceOnboardingJobStatus
    }
}

/// The request for StopCampaign API.
public struct StopCampaignInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

/// The request for TagResource API.
public struct TagResourceInput: Swift.Sendable {
    /// Amazon Resource Names(ARN)
    /// This member is required.
    public var arn: Swift.String?
    /// Tag map with key and value.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        arn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.arn = arn
        self.tags = tags
    }
}

/// The request for UntagResource API.
public struct UntagResourceInput: Swift.Sendable {
    /// Amazon Resource Names(ARN)
    /// This member is required.
    public var arn: Swift.String?
    /// List of tag keys.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        arn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.arn = arn
        self.tagKeys = tagKeys
    }
}

/// The request for UpdateCampaignChannelSubtypeConfig API.
public struct UpdateCampaignChannelSubtypeConfigInput: Swift.Sendable {
    /// Campaign Channel Subtype config
    /// This member is required.
    public var channelSubtypeConfig: ConnectCampaignsV2ClientTypes.ChannelSubtypeConfig?
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        channelSubtypeConfig: ConnectCampaignsV2ClientTypes.ChannelSubtypeConfig? = nil,
        id: Swift.String? = nil
    )
    {
        self.channelSubtypeConfig = channelSubtypeConfig
        self.id = id
    }
}

/// The request for UpdateCampaignCommunicationLimits API.
public struct UpdateCampaignCommunicationLimitsInput: Swift.Sendable {
    /// Communication limits config
    /// This member is required.
    public var communicationLimitsOverride: ConnectCampaignsV2ClientTypes.CommunicationLimitsConfig?
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        communicationLimitsOverride: ConnectCampaignsV2ClientTypes.CommunicationLimitsConfig? = nil,
        id: Swift.String? = nil
    )
    {
        self.communicationLimitsOverride = communicationLimitsOverride
        self.id = id
    }
}

/// The request for UpdateCampaignCommunicationTime API.
public struct UpdateCampaignCommunicationTimeInput: Swift.Sendable {
    /// Campaign communication time config
    /// This member is required.
    public var communicationTimeConfig: ConnectCampaignsV2ClientTypes.CommunicationTimeConfig?
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        communicationTimeConfig: ConnectCampaignsV2ClientTypes.CommunicationTimeConfig? = nil,
        id: Swift.String? = nil
    )
    {
        self.communicationTimeConfig = communicationTimeConfig
        self.id = id
    }
}

/// The request for UpdateCampaignFlowAssociation API.
public struct UpdateCampaignFlowAssociationInput: Swift.Sendable {
    /// Amazon Resource Names(ARN)
    /// This member is required.
    public var connectCampaignFlowArn: Swift.String?
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        connectCampaignFlowArn: Swift.String? = nil,
        id: Swift.String? = nil
    )
    {
        self.connectCampaignFlowArn = connectCampaignFlowArn
        self.id = id
    }
}

/// The request for UpdateCampaignName API.
public struct UpdateCampaignNameInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?
    /// The name of an Amazon Connect Campaign name.
    /// This member is required.
    public var name: Swift.String?

    public init(
        id: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.id = id
        self.name = name
    }
}

/// The request for UpdateCampaignSchedule API.
public struct UpdateCampaignScheduleInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?
    /// Campaign schedule
    /// This member is required.
    public var schedule: ConnectCampaignsV2ClientTypes.Schedule?

    public init(
        id: Swift.String? = nil,
        schedule: ConnectCampaignsV2ClientTypes.Schedule? = nil
    )
    {
        self.id = id
        self.schedule = schedule
    }
}

/// The request for UpdateCampaignSource API.
public struct UpdateCampaignSourceInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?
    /// Source of the campaign
    /// This member is required.
    public var source: ConnectCampaignsV2ClientTypes.Source?

    public init(
        id: Swift.String? = nil,
        source: ConnectCampaignsV2ClientTypes.Source? = nil
    )
    {
        self.id = id
        self.source = source
    }
}

extension CreateCampaignInput {

    static func urlPathProvider(_ value: CreateCampaignInput) -> Swift.String? {
        return "/v2/campaigns"
    }
}

extension DeleteCampaignInput {

    static func urlPathProvider(_ value: DeleteCampaignInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())"
    }
}

extension DeleteCampaignChannelSubtypeConfigInput {

    static func urlPathProvider(_ value: DeleteCampaignChannelSubtypeConfigInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/channel-subtype-config"
    }
}

extension DeleteCampaignChannelSubtypeConfigInput {

    static func queryItemProvider(_ value: DeleteCampaignChannelSubtypeConfigInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let channelSubtype = value.channelSubtype else {
            let message = "Creating a URL Query Item failed. channelSubtype is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let channelSubtypeQueryItem = Smithy.URIQueryItem(name: "channelSubtype".urlPercentEncoding(), value: Swift.String(channelSubtype.rawValue).urlPercentEncoding())
        items.append(channelSubtypeQueryItem)
        return items
    }
}

extension DeleteCampaignCommunicationLimitsInput {

    static func urlPathProvider(_ value: DeleteCampaignCommunicationLimitsInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/communication-limits"
    }
}

extension DeleteCampaignCommunicationLimitsInput {

    static func queryItemProvider(_ value: DeleteCampaignCommunicationLimitsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let config = value.config else {
            let message = "Creating a URL Query Item failed. config is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let configQueryItem = Smithy.URIQueryItem(name: "config".urlPercentEncoding(), value: Swift.String(config.rawValue).urlPercentEncoding())
        items.append(configQueryItem)
        return items
    }
}

extension DeleteCampaignCommunicationTimeInput {

    static func urlPathProvider(_ value: DeleteCampaignCommunicationTimeInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/communication-time"
    }
}

extension DeleteCampaignCommunicationTimeInput {

    static func queryItemProvider(_ value: DeleteCampaignCommunicationTimeInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let config = value.config else {
            let message = "Creating a URL Query Item failed. config is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let configQueryItem = Smithy.URIQueryItem(name: "config".urlPercentEncoding(), value: Swift.String(config.rawValue).urlPercentEncoding())
        items.append(configQueryItem)
        return items
    }
}

extension DeleteConnectInstanceConfigInput {

    static func urlPathProvider(_ value: DeleteConnectInstanceConfigInput) -> Swift.String? {
        guard let connectInstanceId = value.connectInstanceId else {
            return nil
        }
        return "/v2/connect-instance/\(connectInstanceId.urlPercentEncoding())/config"
    }
}

extension DeleteConnectInstanceConfigInput {

    static func queryItemProvider(_ value: DeleteConnectInstanceConfigInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let campaignDeletionPolicy = value.campaignDeletionPolicy {
            let campaignDeletionPolicyQueryItem = Smithy.URIQueryItem(name: "campaignDeletionPolicy".urlPercentEncoding(), value: Swift.String(campaignDeletionPolicy.rawValue).urlPercentEncoding())
            items.append(campaignDeletionPolicyQueryItem)
        }
        return items
    }
}

extension DeleteConnectInstanceIntegrationInput {

    static func urlPathProvider(_ value: DeleteConnectInstanceIntegrationInput) -> Swift.String? {
        guard let connectInstanceId = value.connectInstanceId else {
            return nil
        }
        return "/v2/connect-instance/\(connectInstanceId.urlPercentEncoding())/integrations/delete"
    }
}

extension DeleteInstanceOnboardingJobInput {

    static func urlPathProvider(_ value: DeleteInstanceOnboardingJobInput) -> Swift.String? {
        guard let connectInstanceId = value.connectInstanceId else {
            return nil
        }
        return "/v2/connect-instance/\(connectInstanceId.urlPercentEncoding())/onboarding"
    }
}

extension DescribeCampaignInput {

    static func urlPathProvider(_ value: DescribeCampaignInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())"
    }
}

extension GetCampaignStateInput {

    static func urlPathProvider(_ value: GetCampaignStateInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/state"
    }
}

extension GetCampaignStateBatchInput {

    static func urlPathProvider(_ value: GetCampaignStateBatchInput) -> Swift.String? {
        return "/v2/campaigns-state"
    }
}

extension GetConnectInstanceConfigInput {

    static func urlPathProvider(_ value: GetConnectInstanceConfigInput) -> Swift.String? {
        guard let connectInstanceId = value.connectInstanceId else {
            return nil
        }
        return "/v2/connect-instance/\(connectInstanceId.urlPercentEncoding())/config"
    }
}

extension GetInstanceOnboardingJobStatusInput {

    static func urlPathProvider(_ value: GetInstanceOnboardingJobStatusInput) -> Swift.String? {
        guard let connectInstanceId = value.connectInstanceId else {
            return nil
        }
        return "/v2/connect-instance/\(connectInstanceId.urlPercentEncoding())/onboarding"
    }
}

extension ListCampaignsInput {

    static func urlPathProvider(_ value: ListCampaignsInput) -> Swift.String? {
        return "/v2/campaigns-summary"
    }
}

extension ListConnectInstanceIntegrationsInput {

    static func urlPathProvider(_ value: ListConnectInstanceIntegrationsInput) -> Swift.String? {
        guard let connectInstanceId = value.connectInstanceId else {
            return nil
        }
        return "/v2/connect-instance/\(connectInstanceId.urlPercentEncoding())/integrations"
    }
}

extension ListConnectInstanceIntegrationsInput {

    static func queryItemProvider(_ value: ListConnectInstanceIntegrationsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        return items
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let arn = value.arn else {
            return nil
        }
        return "/v2/tags/\(arn.urlPercentEncoding())"
    }
}

extension PauseCampaignInput {

    static func urlPathProvider(_ value: PauseCampaignInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/pause"
    }
}

extension PutConnectInstanceIntegrationInput {

    static func urlPathProvider(_ value: PutConnectInstanceIntegrationInput) -> Swift.String? {
        guard let connectInstanceId = value.connectInstanceId else {
            return nil
        }
        return "/v2/connect-instance/\(connectInstanceId.urlPercentEncoding())/integrations"
    }
}

extension PutOutboundRequestBatchInput {

    static func urlPathProvider(_ value: PutOutboundRequestBatchInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/outbound-requests"
    }
}

extension PutProfileOutboundRequestBatchInput {

    static func urlPathProvider(_ value: PutProfileOutboundRequestBatchInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/profile-outbound-requests"
    }
}

extension ResumeCampaignInput {

    static func urlPathProvider(_ value: ResumeCampaignInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/resume"
    }
}

extension StartCampaignInput {

    static func urlPathProvider(_ value: StartCampaignInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/start"
    }
}

extension StartInstanceOnboardingJobInput {

    static func urlPathProvider(_ value: StartInstanceOnboardingJobInput) -> Swift.String? {
        guard let connectInstanceId = value.connectInstanceId else {
            return nil
        }
        return "/v2/connect-instance/\(connectInstanceId.urlPercentEncoding())/onboarding"
    }
}

extension StopCampaignInput {

    static func urlPathProvider(_ value: StopCampaignInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/stop"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let arn = value.arn else {
            return nil
        }
        return "/v2/tags/\(arn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let arn = value.arn else {
            return nil
        }
        return "/v2/tags/\(arn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension UpdateCampaignChannelSubtypeConfigInput {

    static func urlPathProvider(_ value: UpdateCampaignChannelSubtypeConfigInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/channel-subtype-config"
    }
}

extension UpdateCampaignCommunicationLimitsInput {

    static func urlPathProvider(_ value: UpdateCampaignCommunicationLimitsInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/communication-limits"
    }
}

extension UpdateCampaignCommunicationTimeInput {

    static func urlPathProvider(_ value: UpdateCampaignCommunicationTimeInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/communication-time"
    }
}

extension UpdateCampaignFlowAssociationInput {

    static func urlPathProvider(_ value: UpdateCampaignFlowAssociationInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/flow"
    }
}

extension UpdateCampaignNameInput {

    static func urlPathProvider(_ value: UpdateCampaignNameInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/name"
    }
}

extension UpdateCampaignScheduleInput {

    static func urlPathProvider(_ value: UpdateCampaignScheduleInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/schedule"
    }
}

extension UpdateCampaignSourceInput {

    static func urlPathProvider(_ value: UpdateCampaignSourceInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v2/campaigns/\(id.urlPercentEncoding())/source"
    }
}

extension CreateCampaignInput {

    static func write(value: CreateCampaignInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["channelSubtypeConfig"].write(value.channelSubtypeConfig, with: ConnectCampaignsV2ClientTypes.ChannelSubtypeConfig.write(value:to:))
        try writer["communicationLimitsOverride"].write(value.communicationLimitsOverride, with: ConnectCampaignsV2ClientTypes.CommunicationLimitsConfig.write(value:to:))
        try writer["communicationTimeConfig"].write(value.communicationTimeConfig, with: ConnectCampaignsV2ClientTypes.CommunicationTimeConfig.write(value:to:))
        try writer["connectCampaignFlowArn"].write(value.connectCampaignFlowArn)
        try writer["connectInstanceId"].write(value.connectInstanceId)
        try writer["name"].write(value.name)
        try writer["schedule"].write(value.schedule, with: ConnectCampaignsV2ClientTypes.Schedule.write(value:to:))
        try writer["source"].write(value.source, with: ConnectCampaignsV2ClientTypes.Source.write(value:to:))
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension DeleteConnectInstanceIntegrationInput {

    static func write(value: DeleteConnectInstanceIntegrationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["integrationIdentifier"].write(value.integrationIdentifier, with: ConnectCampaignsV2ClientTypes.IntegrationIdentifier.write(value:to:))
    }
}

extension GetCampaignStateBatchInput {

    static func write(value: GetCampaignStateBatchInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["campaignIds"].writeList(value.campaignIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ListCampaignsInput {

    static func write(value: ListCampaignsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filters"].write(value.filters, with: ConnectCampaignsV2ClientTypes.CampaignFilters.write(value:to:))
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension PutConnectInstanceIntegrationInput {

    static func write(value: PutConnectInstanceIntegrationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["integrationConfig"].write(value.integrationConfig, with: ConnectCampaignsV2ClientTypes.IntegrationConfig.write(value:to:))
    }
}

extension PutOutboundRequestBatchInput {

    static func write(value: PutOutboundRequestBatchInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["outboundRequests"].writeList(value.outboundRequests, memberWritingClosure: ConnectCampaignsV2ClientTypes.OutboundRequest.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension PutProfileOutboundRequestBatchInput {

    static func write(value: PutProfileOutboundRequestBatchInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["profileOutboundRequests"].writeList(value.profileOutboundRequests, memberWritingClosure: ConnectCampaignsV2ClientTypes.ProfileOutboundRequest.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension StartInstanceOnboardingJobInput {

    static func write(value: StartInstanceOnboardingJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["encryptionConfig"].write(value.encryptionConfig, with: ConnectCampaignsV2ClientTypes.EncryptionConfig.write(value:to:))
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension UpdateCampaignChannelSubtypeConfigInput {

    static func write(value: UpdateCampaignChannelSubtypeConfigInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["channelSubtypeConfig"].write(value.channelSubtypeConfig, with: ConnectCampaignsV2ClientTypes.ChannelSubtypeConfig.write(value:to:))
    }
}

extension UpdateCampaignCommunicationLimitsInput {

    static func write(value: UpdateCampaignCommunicationLimitsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["communicationLimitsOverride"].write(value.communicationLimitsOverride, with: ConnectCampaignsV2ClientTypes.CommunicationLimitsConfig.write(value:to:))
    }
}

extension UpdateCampaignCommunicationTimeInput {

    static func write(value: UpdateCampaignCommunicationTimeInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["communicationTimeConfig"].write(value.communicationTimeConfig, with: ConnectCampaignsV2ClientTypes.CommunicationTimeConfig.write(value:to:))
    }
}

extension UpdateCampaignFlowAssociationInput {

    static func write(value: UpdateCampaignFlowAssociationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["connectCampaignFlowArn"].write(value.connectCampaignFlowArn)
    }
}

extension UpdateCampaignNameInput {

    static func write(value: UpdateCampaignNameInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["name"].write(value.name)
    }
}

extension UpdateCampaignScheduleInput {

    static func write(value: UpdateCampaignScheduleInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["schedule"].write(value.schedule, with: ConnectCampaignsV2ClientTypes.Schedule.write(value:to:))
    }
}

extension UpdateCampaignSourceInput {

    static func write(value: UpdateCampaignSourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["source"].write(value.source, with: ConnectCampaignsV2ClientTypes.Source.write(value:to:))
    }
}

extension CreateCampaignOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateCampaignOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateCampaignOutput()
        value.arn = try reader["arn"].readIfPresent()
        value.id = try reader["id"].readIfPresent()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DeleteCampaignOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteCampaignOutput {
        return DeleteCampaignOutput()
    }
}

extension DeleteCampaignChannelSubtypeConfigOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteCampaignChannelSubtypeConfigOutput {
        return DeleteCampaignChannelSubtypeConfigOutput()
    }
}

extension DeleteCampaignCommunicationLimitsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteCampaignCommunicationLimitsOutput {
        return DeleteCampaignCommunicationLimitsOutput()
    }
}

extension DeleteCampaignCommunicationTimeOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteCampaignCommunicationTimeOutput {
        return DeleteCampaignCommunicationTimeOutput()
    }
}

extension DeleteConnectInstanceConfigOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteConnectInstanceConfigOutput {
        return DeleteConnectInstanceConfigOutput()
    }
}

extension DeleteConnectInstanceIntegrationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteConnectInstanceIntegrationOutput {
        return DeleteConnectInstanceIntegrationOutput()
    }
}

extension DeleteInstanceOnboardingJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteInstanceOnboardingJobOutput {
        return DeleteInstanceOnboardingJobOutput()
    }
}

extension DescribeCampaignOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeCampaignOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeCampaignOutput()
        value.campaign = try reader["campaign"].readIfPresent(with: ConnectCampaignsV2ClientTypes.Campaign.read(from:))
        return value
    }
}

extension GetCampaignStateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetCampaignStateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetCampaignStateOutput()
        value.state = try reader["state"].readIfPresent()
        return value
    }
}

extension GetCampaignStateBatchOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetCampaignStateBatchOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetCampaignStateBatchOutput()
        value.failedRequests = try reader["failedRequests"].readListIfPresent(memberReadingClosure: ConnectCampaignsV2ClientTypes.FailedCampaignStateResponse.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.successfulRequests = try reader["successfulRequests"].readListIfPresent(memberReadingClosure: ConnectCampaignsV2ClientTypes.SuccessfulCampaignStateResponse.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension GetConnectInstanceConfigOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetConnectInstanceConfigOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetConnectInstanceConfigOutput()
        value.connectInstanceConfig = try reader["connectInstanceConfig"].readIfPresent(with: ConnectCampaignsV2ClientTypes.InstanceConfig.read(from:))
        return value
    }
}

extension GetInstanceOnboardingJobStatusOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetInstanceOnboardingJobStatusOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetInstanceOnboardingJobStatusOutput()
        value.connectInstanceOnboardingJobStatus = try reader["connectInstanceOnboardingJobStatus"].readIfPresent(with: ConnectCampaignsV2ClientTypes.InstanceOnboardingJobStatus.read(from:))
        return value
    }
}

extension ListCampaignsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListCampaignsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListCampaignsOutput()
        value.campaignSummaryList = try reader["campaignSummaryList"].readListIfPresent(memberReadingClosure: ConnectCampaignsV2ClientTypes.CampaignSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListConnectInstanceIntegrationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListConnectInstanceIntegrationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListConnectInstanceIntegrationsOutput()
        value.integrationSummaryList = try reader["integrationSummaryList"].readListIfPresent(memberReadingClosure: ConnectCampaignsV2ClientTypes.IntegrationSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension PauseCampaignOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PauseCampaignOutput {
        return PauseCampaignOutput()
    }
}

extension PutConnectInstanceIntegrationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutConnectInstanceIntegrationOutput {
        return PutConnectInstanceIntegrationOutput()
    }
}

extension PutOutboundRequestBatchOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutOutboundRequestBatchOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutOutboundRequestBatchOutput()
        value.failedRequests = try reader["failedRequests"].readListIfPresent(memberReadingClosure: ConnectCampaignsV2ClientTypes.FailedRequest.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.successfulRequests = try reader["successfulRequests"].readListIfPresent(memberReadingClosure: ConnectCampaignsV2ClientTypes.SuccessfulRequest.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension PutProfileOutboundRequestBatchOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutProfileOutboundRequestBatchOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutProfileOutboundRequestBatchOutput()
        value.failedRequests = try reader["failedRequests"].readListIfPresent(memberReadingClosure: ConnectCampaignsV2ClientTypes.FailedProfileOutboundRequest.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.successfulRequests = try reader["successfulRequests"].readListIfPresent(memberReadingClosure: ConnectCampaignsV2ClientTypes.SuccessfulProfileOutboundRequest.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ResumeCampaignOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ResumeCampaignOutput {
        return ResumeCampaignOutput()
    }
}

extension StartCampaignOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartCampaignOutput {
        return StartCampaignOutput()
    }
}

extension StartInstanceOnboardingJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartInstanceOnboardingJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartInstanceOnboardingJobOutput()
        value.connectInstanceOnboardingJobStatus = try reader["connectInstanceOnboardingJobStatus"].readIfPresent(with: ConnectCampaignsV2ClientTypes.InstanceOnboardingJobStatus.read(from:))
        return value
    }
}

extension StopCampaignOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopCampaignOutput {
        return StopCampaignOutput()
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateCampaignChannelSubtypeConfigOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateCampaignChannelSubtypeConfigOutput {
        return UpdateCampaignChannelSubtypeConfigOutput()
    }
}

extension UpdateCampaignCommunicationLimitsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateCampaignCommunicationLimitsOutput {
        return UpdateCampaignCommunicationLimitsOutput()
    }
}

extension UpdateCampaignCommunicationTimeOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateCampaignCommunicationTimeOutput {
        return UpdateCampaignCommunicationTimeOutput()
    }
}

extension UpdateCampaignFlowAssociationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateCampaignFlowAssociationOutput {
        return UpdateCampaignFlowAssociationOutput()
    }
}

extension UpdateCampaignNameOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateCampaignNameOutput {
        return UpdateCampaignNameOutput()
    }
}

extension UpdateCampaignScheduleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateCampaignScheduleOutput {
        return UpdateCampaignScheduleOutput()
    }
}

extension UpdateCampaignSourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateCampaignSourceOutput {
        return UpdateCampaignSourceOutput()
    }
}

enum CreateCampaignOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteCampaignOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteCampaignChannelSubtypeConfigOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteCampaignCommunicationLimitsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteCampaignCommunicationTimeOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteConnectInstanceConfigOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidStateException": return try InvalidStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteConnectInstanceIntegrationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteInstanceOnboardingJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidStateException": return try InvalidStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeCampaignOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetCampaignStateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetCampaignStateBatchOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetConnectInstanceConfigOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetInstanceOnboardingJobStatusOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListCampaignsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListConnectInstanceIntegrationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PauseCampaignOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutConnectInstanceIntegrationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutOutboundRequestBatchOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutProfileOutboundRequestBatchOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ResumeCampaignOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartCampaignOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartInstanceOnboardingJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopCampaignOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateCampaignChannelSubtypeConfigOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateCampaignCommunicationLimitsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateCampaignCommunicationTimeOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateCampaignFlowAssociationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateCampaignNameOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateCampaignScheduleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateCampaignSourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = AccessDeniedException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ValidationException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = InternalServerException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ResourceNotFoundException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ThrottlingException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ConflictException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceQuotaExceededException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ServiceQuotaExceededException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidCampaignStateException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InvalidCampaignStateException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = InvalidCampaignStateException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.state = try reader["state"].readIfPresent() ?? .sdkUnknown("")
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidStateException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InvalidStateException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = InvalidStateException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.Campaign {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.Campaign {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.Campaign()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent() ?? ""
        value.connectInstanceId = try reader["connectInstanceId"].readIfPresent() ?? ""
        value.channelSubtypeConfig = try reader["channelSubtypeConfig"].readIfPresent(with: ConnectCampaignsV2ClientTypes.ChannelSubtypeConfig.read(from:))
        value.source = try reader["source"].readIfPresent(with: ConnectCampaignsV2ClientTypes.Source.read(from:))
        value.connectCampaignFlowArn = try reader["connectCampaignFlowArn"].readIfPresent()
        value.schedule = try reader["schedule"].readIfPresent(with: ConnectCampaignsV2ClientTypes.Schedule.read(from:))
        value.communicationTimeConfig = try reader["communicationTimeConfig"].readIfPresent(with: ConnectCampaignsV2ClientTypes.CommunicationTimeConfig.read(from:))
        value.communicationLimitsOverride = try reader["communicationLimitsOverride"].readIfPresent(with: ConnectCampaignsV2ClientTypes.CommunicationLimitsConfig.read(from:))
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.CommunicationLimitsConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.CommunicationLimitsConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["allChannelSubtypes"].write(value.allChannelSubtypes, with: ConnectCampaignsV2ClientTypes.CommunicationLimits.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.CommunicationLimitsConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.CommunicationLimitsConfig()
        value.allChannelSubtypes = try reader["allChannelSubtypes"].readIfPresent(with: ConnectCampaignsV2ClientTypes.CommunicationLimits.read(from:))
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.CommunicationLimits {

    static func write(value: ConnectCampaignsV2ClientTypes.CommunicationLimits?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        switch value {
            case let .communicationlimitslist(communicationlimitslist):
                try writer["communicationLimitsList"].writeList(communicationlimitslist, memberWritingClosure: ConnectCampaignsV2ClientTypes.CommunicationLimit.write(value:to:), memberNodeInfo: "member", isFlattened: false)
            case let .sdkUnknown(sdkUnknown):
                try writer["sdkUnknown"].write(sdkUnknown)
        }
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.CommunicationLimits {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "communicationLimitsList":
                return .communicationlimitslist(try reader["communicationLimitsList"].readList(memberReadingClosure: ConnectCampaignsV2ClientTypes.CommunicationLimit.read(from:), memberNodeInfo: "member", isFlattened: false))
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension ConnectCampaignsV2ClientTypes.CommunicationLimit {

    static func write(value: ConnectCampaignsV2ClientTypes.CommunicationLimit?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["frequency"].write(value.frequency)
        try writer["maxCountPerRecipient"].write(value.maxCountPerRecipient)
        try writer["unit"].write(value.unit)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.CommunicationLimit {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.CommunicationLimit()
        value.maxCountPerRecipient = try reader["maxCountPerRecipient"].readIfPresent() ?? 0
        value.frequency = try reader["frequency"].readIfPresent() ?? 0
        value.unit = try reader["unit"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.CommunicationTimeConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.CommunicationTimeConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["email"].write(value.email, with: ConnectCampaignsV2ClientTypes.TimeWindow.write(value:to:))
        try writer["localTimeZoneConfig"].write(value.localTimeZoneConfig, with: ConnectCampaignsV2ClientTypes.LocalTimeZoneConfig.write(value:to:))
        try writer["sms"].write(value.sms, with: ConnectCampaignsV2ClientTypes.TimeWindow.write(value:to:))
        try writer["telephony"].write(value.telephony, with: ConnectCampaignsV2ClientTypes.TimeWindow.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.CommunicationTimeConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.CommunicationTimeConfig()
        value.localTimeZoneConfig = try reader["localTimeZoneConfig"].readIfPresent(with: ConnectCampaignsV2ClientTypes.LocalTimeZoneConfig.read(from:))
        value.telephony = try reader["telephony"].readIfPresent(with: ConnectCampaignsV2ClientTypes.TimeWindow.read(from:))
        value.sms = try reader["sms"].readIfPresent(with: ConnectCampaignsV2ClientTypes.TimeWindow.read(from:))
        value.email = try reader["email"].readIfPresent(with: ConnectCampaignsV2ClientTypes.TimeWindow.read(from:))
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.TimeWindow {

    static func write(value: ConnectCampaignsV2ClientTypes.TimeWindow?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["openHours"].write(value.openHours, with: ConnectCampaignsV2ClientTypes.OpenHours.write(value:to:))
        try writer["restrictedPeriods"].write(value.restrictedPeriods, with: ConnectCampaignsV2ClientTypes.RestrictedPeriods.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.TimeWindow {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.TimeWindow()
        value.openHours = try reader["openHours"].readIfPresent(with: ConnectCampaignsV2ClientTypes.OpenHours.read(from:))
        value.restrictedPeriods = try reader["restrictedPeriods"].readIfPresent(with: ConnectCampaignsV2ClientTypes.RestrictedPeriods.read(from:))
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.RestrictedPeriods {

    static func write(value: ConnectCampaignsV2ClientTypes.RestrictedPeriods?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        switch value {
            case let .restrictedperiodlist(restrictedperiodlist):
                try writer["restrictedPeriodList"].writeList(restrictedperiodlist, memberWritingClosure: ConnectCampaignsV2ClientTypes.RestrictedPeriod.write(value:to:), memberNodeInfo: "member", isFlattened: false)
            case let .sdkUnknown(sdkUnknown):
                try writer["sdkUnknown"].write(sdkUnknown)
        }
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.RestrictedPeriods {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "restrictedPeriodList":
                return .restrictedperiodlist(try reader["restrictedPeriodList"].readList(memberReadingClosure: ConnectCampaignsV2ClientTypes.RestrictedPeriod.read(from:), memberNodeInfo: "member", isFlattened: false))
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension ConnectCampaignsV2ClientTypes.RestrictedPeriod {

    static func write(value: ConnectCampaignsV2ClientTypes.RestrictedPeriod?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["endDate"].write(value.endDate)
        try writer["name"].write(value.name)
        try writer["startDate"].write(value.startDate)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.RestrictedPeriod {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.RestrictedPeriod()
        value.name = try reader["name"].readIfPresent()
        value.startDate = try reader["startDate"].readIfPresent() ?? ""
        value.endDate = try reader["endDate"].readIfPresent() ?? ""
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.OpenHours {

    static func write(value: ConnectCampaignsV2ClientTypes.OpenHours?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        switch value {
            case let .dailyhours(dailyhours):
                try writer["dailyHours"].writeMap(dailyhours, valueWritingClosure: SmithyReadWrite.listWritingClosure(memberWritingClosure: ConnectCampaignsV2ClientTypes.TimeRange.write(value:to:), memberNodeInfo: "member", isFlattened: false), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
            case let .sdkUnknown(sdkUnknown):
                try writer["sdkUnknown"].write(sdkUnknown)
        }
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.OpenHours {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "dailyHours":
                return .dailyhours(try reader["dailyHours"].readMap(valueReadingClosure: SmithyReadWrite.listReadingClosure(memberReadingClosure: ConnectCampaignsV2ClientTypes.TimeRange.read(from:), memberNodeInfo: "member", isFlattened: false), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false))
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension ConnectCampaignsV2ClientTypes.TimeRange {

    static func write(value: ConnectCampaignsV2ClientTypes.TimeRange?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["endTime"].write(value.endTime)
        try writer["startTime"].write(value.startTime)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.TimeRange {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.TimeRange()
        value.startTime = try reader["startTime"].readIfPresent() ?? ""
        value.endTime = try reader["endTime"].readIfPresent() ?? ""
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.LocalTimeZoneConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.LocalTimeZoneConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["defaultTimeZone"].write(value.defaultTimeZone)
        try writer["localTimeZoneDetection"].writeList(value.localTimeZoneDetection, memberWritingClosure: SmithyReadWrite.WritingClosureBox<ConnectCampaignsV2ClientTypes.LocalTimeZoneDetectionType>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.LocalTimeZoneConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.LocalTimeZoneConfig()
        value.defaultTimeZone = try reader["defaultTimeZone"].readIfPresent()
        value.localTimeZoneDetection = try reader["localTimeZoneDetection"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<ConnectCampaignsV2ClientTypes.LocalTimeZoneDetectionType>().read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.Schedule {

    static func write(value: ConnectCampaignsV2ClientTypes.Schedule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["endTime"].writeTimestamp(value.endTime, format: SmithyTimestamps.TimestampFormat.dateTime)
        try writer["refreshFrequency"].write(value.refreshFrequency)
        try writer["startTime"].writeTimestamp(value.startTime, format: SmithyTimestamps.TimestampFormat.dateTime)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.Schedule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.Schedule()
        value.startTime = try reader["startTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.endTime = try reader["endTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.refreshFrequency = try reader["refreshFrequency"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.Source {

    static func write(value: ConnectCampaignsV2ClientTypes.Source?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        switch value {
            case let .customerprofilessegmentarn(customerprofilessegmentarn):
                try writer["customerProfilesSegmentArn"].write(customerprofilessegmentarn)
            case let .eventtrigger(eventtrigger):
                try writer["eventTrigger"].write(eventtrigger, with: ConnectCampaignsV2ClientTypes.EventTrigger.write(value:to:))
            case let .sdkUnknown(sdkUnknown):
                try writer["sdkUnknown"].write(sdkUnknown)
        }
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.Source {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "customerProfilesSegmentArn":
                return .customerprofilessegmentarn(try reader["customerProfilesSegmentArn"].read())
            case "eventTrigger":
                return .eventtrigger(try reader["eventTrigger"].read(with: ConnectCampaignsV2ClientTypes.EventTrigger.read(from:)))
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension ConnectCampaignsV2ClientTypes.EventTrigger {

    static func write(value: ConnectCampaignsV2ClientTypes.EventTrigger?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["customerProfilesDomainArn"].write(value.customerProfilesDomainArn)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.EventTrigger {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.EventTrigger()
        value.customerProfilesDomainArn = try reader["customerProfilesDomainArn"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.ChannelSubtypeConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.ChannelSubtypeConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["email"].write(value.email, with: ConnectCampaignsV2ClientTypes.EmailChannelSubtypeConfig.write(value:to:))
        try writer["sms"].write(value.sms, with: ConnectCampaignsV2ClientTypes.SmsChannelSubtypeConfig.write(value:to:))
        try writer["telephony"].write(value.telephony, with: ConnectCampaignsV2ClientTypes.TelephonyChannelSubtypeConfig.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.ChannelSubtypeConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.ChannelSubtypeConfig()
        value.telephony = try reader["telephony"].readIfPresent(with: ConnectCampaignsV2ClientTypes.TelephonyChannelSubtypeConfig.read(from:))
        value.sms = try reader["sms"].readIfPresent(with: ConnectCampaignsV2ClientTypes.SmsChannelSubtypeConfig.read(from:))
        value.email = try reader["email"].readIfPresent(with: ConnectCampaignsV2ClientTypes.EmailChannelSubtypeConfig.read(from:))
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.EmailChannelSubtypeConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.EmailChannelSubtypeConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["capacity"].write(value.capacity)
        try writer["defaultOutboundConfig"].write(value.defaultOutboundConfig, with: ConnectCampaignsV2ClientTypes.EmailOutboundConfig.write(value:to:))
        try writer["outboundMode"].write(value.outboundMode, with: ConnectCampaignsV2ClientTypes.EmailOutboundMode.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.EmailChannelSubtypeConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.EmailChannelSubtypeConfig()
        value.capacity = try reader["capacity"].readIfPresent()
        value.outboundMode = try reader["outboundMode"].readIfPresent(with: ConnectCampaignsV2ClientTypes.EmailOutboundMode.read(from:))
        value.defaultOutboundConfig = try reader["defaultOutboundConfig"].readIfPresent(with: ConnectCampaignsV2ClientTypes.EmailOutboundConfig.read(from:))
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.EmailOutboundConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.EmailOutboundConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["connectSourceEmailAddress"].write(value.connectSourceEmailAddress)
        try writer["sourceEmailAddressDisplayName"].write(value.sourceEmailAddressDisplayName)
        try writer["wisdomTemplateArn"].write(value.wisdomTemplateArn)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.EmailOutboundConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.EmailOutboundConfig()
        value.connectSourceEmailAddress = try reader["connectSourceEmailAddress"].readIfPresent() ?? ""
        value.sourceEmailAddressDisplayName = try reader["sourceEmailAddressDisplayName"].readIfPresent()
        value.wisdomTemplateArn = try reader["wisdomTemplateArn"].readIfPresent() ?? ""
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.EmailOutboundMode {

    static func write(value: ConnectCampaignsV2ClientTypes.EmailOutboundMode?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        switch value {
            case let .agentless(agentless):
                try writer["agentless"].write(agentless, with: ConnectCampaignsV2ClientTypes.AgentlessConfig.write(value:to:))
            case let .sdkUnknown(sdkUnknown):
                try writer["sdkUnknown"].write(sdkUnknown)
        }
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.EmailOutboundMode {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "agentless":
                return .agentless(try reader["agentless"].read(with: ConnectCampaignsV2ClientTypes.AgentlessConfig.read(from:)))
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension ConnectCampaignsV2ClientTypes.AgentlessConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.AgentlessConfig?, to writer: SmithyJSON.Writer) throws {
        guard value != nil else { return }
        _ = writer[""]  // create an empty structure
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.AgentlessConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        return ConnectCampaignsV2ClientTypes.AgentlessConfig()
    }
}

extension ConnectCampaignsV2ClientTypes.SmsChannelSubtypeConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.SmsChannelSubtypeConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["capacity"].write(value.capacity)
        try writer["defaultOutboundConfig"].write(value.defaultOutboundConfig, with: ConnectCampaignsV2ClientTypes.SmsOutboundConfig.write(value:to:))
        try writer["outboundMode"].write(value.outboundMode, with: ConnectCampaignsV2ClientTypes.SmsOutboundMode.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.SmsChannelSubtypeConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.SmsChannelSubtypeConfig()
        value.capacity = try reader["capacity"].readIfPresent()
        value.outboundMode = try reader["outboundMode"].readIfPresent(with: ConnectCampaignsV2ClientTypes.SmsOutboundMode.read(from:))
        value.defaultOutboundConfig = try reader["defaultOutboundConfig"].readIfPresent(with: ConnectCampaignsV2ClientTypes.SmsOutboundConfig.read(from:))
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.SmsOutboundConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.SmsOutboundConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["connectSourcePhoneNumberArn"].write(value.connectSourcePhoneNumberArn)
        try writer["wisdomTemplateArn"].write(value.wisdomTemplateArn)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.SmsOutboundConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.SmsOutboundConfig()
        value.connectSourcePhoneNumberArn = try reader["connectSourcePhoneNumberArn"].readIfPresent() ?? ""
        value.wisdomTemplateArn = try reader["wisdomTemplateArn"].readIfPresent() ?? ""
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.SmsOutboundMode {

    static func write(value: ConnectCampaignsV2ClientTypes.SmsOutboundMode?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        switch value {
            case let .agentless(agentless):
                try writer["agentless"].write(agentless, with: ConnectCampaignsV2ClientTypes.AgentlessConfig.write(value:to:))
            case let .sdkUnknown(sdkUnknown):
                try writer["sdkUnknown"].write(sdkUnknown)
        }
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.SmsOutboundMode {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "agentless":
                return .agentless(try reader["agentless"].read(with: ConnectCampaignsV2ClientTypes.AgentlessConfig.read(from:)))
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension ConnectCampaignsV2ClientTypes.TelephonyChannelSubtypeConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.TelephonyChannelSubtypeConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["capacity"].write(value.capacity)
        try writer["connectQueueId"].write(value.connectQueueId)
        try writer["defaultOutboundConfig"].write(value.defaultOutboundConfig, with: ConnectCampaignsV2ClientTypes.TelephonyOutboundConfig.write(value:to:))
        try writer["outboundMode"].write(value.outboundMode, with: ConnectCampaignsV2ClientTypes.TelephonyOutboundMode.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.TelephonyChannelSubtypeConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.TelephonyChannelSubtypeConfig()
        value.capacity = try reader["capacity"].readIfPresent()
        value.connectQueueId = try reader["connectQueueId"].readIfPresent()
        value.outboundMode = try reader["outboundMode"].readIfPresent(with: ConnectCampaignsV2ClientTypes.TelephonyOutboundMode.read(from:))
        value.defaultOutboundConfig = try reader["defaultOutboundConfig"].readIfPresent(with: ConnectCampaignsV2ClientTypes.TelephonyOutboundConfig.read(from:))
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.TelephonyOutboundConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.TelephonyOutboundConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["answerMachineDetectionConfig"].write(value.answerMachineDetectionConfig, with: ConnectCampaignsV2ClientTypes.AnswerMachineDetectionConfig.write(value:to:))
        try writer["connectContactFlowId"].write(value.connectContactFlowId)
        try writer["connectSourcePhoneNumber"].write(value.connectSourcePhoneNumber)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.TelephonyOutboundConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.TelephonyOutboundConfig()
        value.connectContactFlowId = try reader["connectContactFlowId"].readIfPresent() ?? ""
        value.connectSourcePhoneNumber = try reader["connectSourcePhoneNumber"].readIfPresent()
        value.answerMachineDetectionConfig = try reader["answerMachineDetectionConfig"].readIfPresent(with: ConnectCampaignsV2ClientTypes.AnswerMachineDetectionConfig.read(from:))
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.AnswerMachineDetectionConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.AnswerMachineDetectionConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["awaitAnswerMachinePrompt"].write(value.awaitAnswerMachinePrompt)
        try writer["enableAnswerMachineDetection"].write(value.enableAnswerMachineDetection)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.AnswerMachineDetectionConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.AnswerMachineDetectionConfig()
        value.enableAnswerMachineDetection = try reader["enableAnswerMachineDetection"].readIfPresent() ?? false
        value.awaitAnswerMachinePrompt = try reader["awaitAnswerMachinePrompt"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.TelephonyOutboundMode {

    static func write(value: ConnectCampaignsV2ClientTypes.TelephonyOutboundMode?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        switch value {
            case let .agentless(agentless):
                try writer["agentless"].write(agentless, with: ConnectCampaignsV2ClientTypes.AgentlessConfig.write(value:to:))
            case let .predictive(predictive):
                try writer["predictive"].write(predictive, with: ConnectCampaignsV2ClientTypes.PredictiveConfig.write(value:to:))
            case let .progressive(progressive):
                try writer["progressive"].write(progressive, with: ConnectCampaignsV2ClientTypes.ProgressiveConfig.write(value:to:))
            case let .sdkUnknown(sdkUnknown):
                try writer["sdkUnknown"].write(sdkUnknown)
        }
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.TelephonyOutboundMode {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "progressive":
                return .progressive(try reader["progressive"].read(with: ConnectCampaignsV2ClientTypes.ProgressiveConfig.read(from:)))
            case "predictive":
                return .predictive(try reader["predictive"].read(with: ConnectCampaignsV2ClientTypes.PredictiveConfig.read(from:)))
            case "agentless":
                return .agentless(try reader["agentless"].read(with: ConnectCampaignsV2ClientTypes.AgentlessConfig.read(from:)))
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension ConnectCampaignsV2ClientTypes.PredictiveConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.PredictiveConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["bandwidthAllocation"].write(value.bandwidthAllocation)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.PredictiveConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.PredictiveConfig()
        value.bandwidthAllocation = try reader["bandwidthAllocation"].readIfPresent() ?? 0.0
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.ProgressiveConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.ProgressiveConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["bandwidthAllocation"].write(value.bandwidthAllocation)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.ProgressiveConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.ProgressiveConfig()
        value.bandwidthAllocation = try reader["bandwidthAllocation"].readIfPresent() ?? 0.0
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.SuccessfulCampaignStateResponse {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.SuccessfulCampaignStateResponse {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.SuccessfulCampaignStateResponse()
        value.campaignId = try reader["campaignId"].readIfPresent()
        value.state = try reader["state"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.FailedCampaignStateResponse {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.FailedCampaignStateResponse {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.FailedCampaignStateResponse()
        value.campaignId = try reader["campaignId"].readIfPresent()
        value.failureCode = try reader["failureCode"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.InstanceConfig {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.InstanceConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.InstanceConfig()
        value.connectInstanceId = try reader["connectInstanceId"].readIfPresent() ?? ""
        value.serviceLinkedRoleArn = try reader["serviceLinkedRoleArn"].readIfPresent() ?? ""
        value.encryptionConfig = try reader["encryptionConfig"].readIfPresent(with: ConnectCampaignsV2ClientTypes.EncryptionConfig.read(from:))
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.EncryptionConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.EncryptionConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["enabled"].write(value.enabled)
        try writer["encryptionType"].write(value.encryptionType)
        try writer["keyArn"].write(value.keyArn)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.EncryptionConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.EncryptionConfig()
        value.enabled = try reader["enabled"].readIfPresent() ?? false
        value.encryptionType = try reader["encryptionType"].readIfPresent()
        value.keyArn = try reader["keyArn"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.InstanceOnboardingJobStatus {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.InstanceOnboardingJobStatus {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.InstanceOnboardingJobStatus()
        value.connectInstanceId = try reader["connectInstanceId"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        value.failureCode = try reader["failureCode"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.CampaignSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.CampaignSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.CampaignSummary()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent() ?? ""
        value.connectInstanceId = try reader["connectInstanceId"].readIfPresent() ?? ""
        value.channelSubtypes = try reader["channelSubtypes"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<ConnectCampaignsV2ClientTypes.ChannelSubtype>().read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.schedule = try reader["schedule"].readIfPresent(with: ConnectCampaignsV2ClientTypes.Schedule.read(from:))
        value.connectCampaignFlowArn = try reader["connectCampaignFlowArn"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.IntegrationSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.IntegrationSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "customerProfiles":
                return .customerprofiles(try reader["customerProfiles"].read(with: ConnectCampaignsV2ClientTypes.CustomerProfilesIntegrationSummary.read(from:)))
            case "qConnect":
                return .qconnect(try reader["qConnect"].read(with: ConnectCampaignsV2ClientTypes.QConnectIntegrationSummary.read(from:)))
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension ConnectCampaignsV2ClientTypes.QConnectIntegrationSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.QConnectIntegrationSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.QConnectIntegrationSummary()
        value.knowledgeBaseArn = try reader["knowledgeBaseArn"].readIfPresent() ?? ""
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.CustomerProfilesIntegrationSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.CustomerProfilesIntegrationSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.CustomerProfilesIntegrationSummary()
        value.domainArn = try reader["domainArn"].readIfPresent() ?? ""
        value.objectTypeNames = try reader["objectTypeNames"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false) ?? [:]
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.SuccessfulRequest {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.SuccessfulRequest {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.SuccessfulRequest()
        value.clientToken = try reader["clientToken"].readIfPresent()
        value.id = try reader["id"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.FailedRequest {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.FailedRequest {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.FailedRequest()
        value.clientToken = try reader["clientToken"].readIfPresent()
        value.id = try reader["id"].readIfPresent()
        value.failureCode = try reader["failureCode"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.SuccessfulProfileOutboundRequest {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.SuccessfulProfileOutboundRequest {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.SuccessfulProfileOutboundRequest()
        value.clientToken = try reader["clientToken"].readIfPresent()
        value.id = try reader["id"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.FailedProfileOutboundRequest {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsV2ClientTypes.FailedProfileOutboundRequest {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsV2ClientTypes.FailedProfileOutboundRequest()
        value.clientToken = try reader["clientToken"].readIfPresent()
        value.id = try reader["id"].readIfPresent()
        value.failureCode = try reader["failureCode"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsV2ClientTypes.IntegrationIdentifier {

    static func write(value: ConnectCampaignsV2ClientTypes.IntegrationIdentifier?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        switch value {
            case let .customerprofiles(customerprofiles):
                try writer["customerProfiles"].write(customerprofiles, with: ConnectCampaignsV2ClientTypes.CustomerProfilesIntegrationIdentifier.write(value:to:))
            case let .qconnect(qconnect):
                try writer["qConnect"].write(qconnect, with: ConnectCampaignsV2ClientTypes.QConnectIntegrationIdentifier.write(value:to:))
            case let .sdkUnknown(sdkUnknown):
                try writer["sdkUnknown"].write(sdkUnknown)
        }
    }
}

extension ConnectCampaignsV2ClientTypes.QConnectIntegrationIdentifier {

    static func write(value: ConnectCampaignsV2ClientTypes.QConnectIntegrationIdentifier?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["knowledgeBaseArn"].write(value.knowledgeBaseArn)
    }
}

extension ConnectCampaignsV2ClientTypes.CustomerProfilesIntegrationIdentifier {

    static func write(value: ConnectCampaignsV2ClientTypes.CustomerProfilesIntegrationIdentifier?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["domainArn"].write(value.domainArn)
    }
}

extension ConnectCampaignsV2ClientTypes.CampaignFilters {

    static func write(value: ConnectCampaignsV2ClientTypes.CampaignFilters?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["instanceIdFilter"].write(value.instanceIdFilter, with: ConnectCampaignsV2ClientTypes.InstanceIdFilter.write(value:to:))
    }
}

extension ConnectCampaignsV2ClientTypes.InstanceIdFilter {

    static func write(value: ConnectCampaignsV2ClientTypes.InstanceIdFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["operator"].write(value.`operator`)
        try writer["value"].write(value.value)
    }
}

extension ConnectCampaignsV2ClientTypes.IntegrationConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.IntegrationConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        switch value {
            case let .customerprofiles(customerprofiles):
                try writer["customerProfiles"].write(customerprofiles, with: ConnectCampaignsV2ClientTypes.CustomerProfilesIntegrationConfig.write(value:to:))
            case let .qconnect(qconnect):
                try writer["qConnect"].write(qconnect, with: ConnectCampaignsV2ClientTypes.QConnectIntegrationConfig.write(value:to:))
            case let .sdkUnknown(sdkUnknown):
                try writer["sdkUnknown"].write(sdkUnknown)
        }
    }
}

extension ConnectCampaignsV2ClientTypes.QConnectIntegrationConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.QConnectIntegrationConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["knowledgeBaseArn"].write(value.knowledgeBaseArn)
    }
}

extension ConnectCampaignsV2ClientTypes.CustomerProfilesIntegrationConfig {

    static func write(value: ConnectCampaignsV2ClientTypes.CustomerProfilesIntegrationConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["domainArn"].write(value.domainArn)
        try writer["objectTypeNames"].writeMap(value.objectTypeNames, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension ConnectCampaignsV2ClientTypes.OutboundRequest {

    static func write(value: ConnectCampaignsV2ClientTypes.OutboundRequest?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["channelSubtypeParameters"].write(value.channelSubtypeParameters, with: ConnectCampaignsV2ClientTypes.ChannelSubtypeParameters.write(value:to:))
        try writer["clientToken"].write(value.clientToken)
        try writer["expirationTime"].writeTimestamp(value.expirationTime, format: SmithyTimestamps.TimestampFormat.dateTime)
    }
}

extension ConnectCampaignsV2ClientTypes.ChannelSubtypeParameters {

    static func write(value: ConnectCampaignsV2ClientTypes.ChannelSubtypeParameters?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        switch value {
            case let .email(email):
                try writer["email"].write(email, with: ConnectCampaignsV2ClientTypes.EmailChannelSubtypeParameters.write(value:to:))
            case let .sms(sms):
                try writer["sms"].write(sms, with: ConnectCampaignsV2ClientTypes.SmsChannelSubtypeParameters.write(value:to:))
            case let .telephony(telephony):
                try writer["telephony"].write(telephony, with: ConnectCampaignsV2ClientTypes.TelephonyChannelSubtypeParameters.write(value:to:))
            case let .sdkUnknown(sdkUnknown):
                try writer["sdkUnknown"].write(sdkUnknown)
        }
    }
}

extension ConnectCampaignsV2ClientTypes.EmailChannelSubtypeParameters {

    static func write(value: ConnectCampaignsV2ClientTypes.EmailChannelSubtypeParameters?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["connectSourceEmailAddress"].write(value.connectSourceEmailAddress)
        try writer["destinationEmailAddress"].write(value.destinationEmailAddress)
        try writer["templateArn"].write(value.templateArn)
        try writer["templateParameters"].writeMap(value.templateParameters, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension ConnectCampaignsV2ClientTypes.SmsChannelSubtypeParameters {

    static func write(value: ConnectCampaignsV2ClientTypes.SmsChannelSubtypeParameters?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["connectSourcePhoneNumberArn"].write(value.connectSourcePhoneNumberArn)
        try writer["destinationPhoneNumber"].write(value.destinationPhoneNumber)
        try writer["templateArn"].write(value.templateArn)
        try writer["templateParameters"].writeMap(value.templateParameters, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension ConnectCampaignsV2ClientTypes.TelephonyChannelSubtypeParameters {

    static func write(value: ConnectCampaignsV2ClientTypes.TelephonyChannelSubtypeParameters?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["answerMachineDetectionConfig"].write(value.answerMachineDetectionConfig, with: ConnectCampaignsV2ClientTypes.AnswerMachineDetectionConfig.write(value:to:))
        try writer["attributes"].writeMap(value.attributes, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["connectSourcePhoneNumber"].write(value.connectSourcePhoneNumber)
        try writer["destinationPhoneNumber"].write(value.destinationPhoneNumber)
    }
}

extension ConnectCampaignsV2ClientTypes.ProfileOutboundRequest {

    static func write(value: ConnectCampaignsV2ClientTypes.ProfileOutboundRequest?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["clientToken"].write(value.clientToken)
        try writer["expirationTime"].writeTimestamp(value.expirationTime, format: SmithyTimestamps.TimestampFormat.dateTime)
        try writer["profileId"].write(value.profileId)
    }
}

public enum ConnectCampaignsV2ClientTypes {}
