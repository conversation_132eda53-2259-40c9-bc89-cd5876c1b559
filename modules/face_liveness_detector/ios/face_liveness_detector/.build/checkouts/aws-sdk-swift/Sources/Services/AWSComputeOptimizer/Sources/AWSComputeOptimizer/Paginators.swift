//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension ComputeOptimizerClient {
    /// Paginate over `[DescribeRecommendationExportJobsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeRecommendationExportJobsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeRecommendationExportJobsOutput`
    public func describeRecommendationExportJobsPaginated(input: DescribeRecommendationExportJobsInput) -> ClientRuntime.PaginatorSequence<DescribeRecommendationExportJobsInput, DescribeRecommendationExportJobsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeRecommendationExportJobsInput, DescribeRecommendationExportJobsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeRecommendationExportJobs(input:))
    }
}

extension DescribeRecommendationExportJobsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeRecommendationExportJobsInput {
        return DescribeRecommendationExportJobsInput(
            filters: self.filters,
            jobIds: self.jobIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeRecommendationExportJobsInput, OperationStackOutput == DescribeRecommendationExportJobsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeRecommendationExportJobsPaginated`
    /// to access the nested member `[ComputeOptimizerClientTypes.RecommendationExportJob]`
    /// - Returns: `[ComputeOptimizerClientTypes.RecommendationExportJob]`
    public func recommendationExportJobs() async throws -> [ComputeOptimizerClientTypes.RecommendationExportJob] {
        return try await self.asyncCompactMap { item in item.recommendationExportJobs }
    }
}
extension ComputeOptimizerClient {
    /// Paginate over `[GetEnrollmentStatusesForOrganizationOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetEnrollmentStatusesForOrganizationInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetEnrollmentStatusesForOrganizationOutput`
    public func getEnrollmentStatusesForOrganizationPaginated(input: GetEnrollmentStatusesForOrganizationInput) -> ClientRuntime.PaginatorSequence<GetEnrollmentStatusesForOrganizationInput, GetEnrollmentStatusesForOrganizationOutput> {
        return ClientRuntime.PaginatorSequence<GetEnrollmentStatusesForOrganizationInput, GetEnrollmentStatusesForOrganizationOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getEnrollmentStatusesForOrganization(input:))
    }
}

extension GetEnrollmentStatusesForOrganizationInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetEnrollmentStatusesForOrganizationInput {
        return GetEnrollmentStatusesForOrganizationInput(
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == GetEnrollmentStatusesForOrganizationInput, OperationStackOutput == GetEnrollmentStatusesForOrganizationOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getEnrollmentStatusesForOrganizationPaginated`
    /// to access the nested member `[ComputeOptimizerClientTypes.AccountEnrollmentStatus]`
    /// - Returns: `[ComputeOptimizerClientTypes.AccountEnrollmentStatus]`
    public func accountEnrollmentStatuses() async throws -> [ComputeOptimizerClientTypes.AccountEnrollmentStatus] {
        return try await self.asyncCompactMap { item in item.accountEnrollmentStatuses }
    }
}
extension ComputeOptimizerClient {
    /// Paginate over `[GetLambdaFunctionRecommendationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetLambdaFunctionRecommendationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetLambdaFunctionRecommendationsOutput`
    public func getLambdaFunctionRecommendationsPaginated(input: GetLambdaFunctionRecommendationsInput) -> ClientRuntime.PaginatorSequence<GetLambdaFunctionRecommendationsInput, GetLambdaFunctionRecommendationsOutput> {
        return ClientRuntime.PaginatorSequence<GetLambdaFunctionRecommendationsInput, GetLambdaFunctionRecommendationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getLambdaFunctionRecommendations(input:))
    }
}

extension GetLambdaFunctionRecommendationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetLambdaFunctionRecommendationsInput {
        return GetLambdaFunctionRecommendationsInput(
            accountIds: self.accountIds,
            filters: self.filters,
            functionArns: self.functionArns,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == GetLambdaFunctionRecommendationsInput, OperationStackOutput == GetLambdaFunctionRecommendationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getLambdaFunctionRecommendationsPaginated`
    /// to access the nested member `[ComputeOptimizerClientTypes.LambdaFunctionRecommendation]`
    /// - Returns: `[ComputeOptimizerClientTypes.LambdaFunctionRecommendation]`
    public func lambdaFunctionRecommendations() async throws -> [ComputeOptimizerClientTypes.LambdaFunctionRecommendation] {
        return try await self.asyncCompactMap { item in item.lambdaFunctionRecommendations }
    }
}
extension ComputeOptimizerClient {
    /// Paginate over `[GetRecommendationPreferencesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetRecommendationPreferencesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetRecommendationPreferencesOutput`
    public func getRecommendationPreferencesPaginated(input: GetRecommendationPreferencesInput) -> ClientRuntime.PaginatorSequence<GetRecommendationPreferencesInput, GetRecommendationPreferencesOutput> {
        return ClientRuntime.PaginatorSequence<GetRecommendationPreferencesInput, GetRecommendationPreferencesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getRecommendationPreferences(input:))
    }
}

extension GetRecommendationPreferencesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetRecommendationPreferencesInput {
        return GetRecommendationPreferencesInput(
            maxResults: self.maxResults,
            nextToken: token,
            resourceType: self.resourceType,
            scope: self.scope
        )}
}

extension PaginatorSequence where OperationStackInput == GetRecommendationPreferencesInput, OperationStackOutput == GetRecommendationPreferencesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getRecommendationPreferencesPaginated`
    /// to access the nested member `[ComputeOptimizerClientTypes.RecommendationPreferencesDetail]`
    /// - Returns: `[ComputeOptimizerClientTypes.RecommendationPreferencesDetail]`
    public func recommendationPreferencesDetails() async throws -> [ComputeOptimizerClientTypes.RecommendationPreferencesDetail] {
        return try await self.asyncCompactMap { item in item.recommendationPreferencesDetails }
    }
}
extension ComputeOptimizerClient {
    /// Paginate over `[GetRecommendationSummariesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetRecommendationSummariesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetRecommendationSummariesOutput`
    public func getRecommendationSummariesPaginated(input: GetRecommendationSummariesInput) -> ClientRuntime.PaginatorSequence<GetRecommendationSummariesInput, GetRecommendationSummariesOutput> {
        return ClientRuntime.PaginatorSequence<GetRecommendationSummariesInput, GetRecommendationSummariesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getRecommendationSummaries(input:))
    }
}

extension GetRecommendationSummariesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetRecommendationSummariesInput {
        return GetRecommendationSummariesInput(
            accountIds: self.accountIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == GetRecommendationSummariesInput, OperationStackOutput == GetRecommendationSummariesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getRecommendationSummariesPaginated`
    /// to access the nested member `[ComputeOptimizerClientTypes.RecommendationSummary]`
    /// - Returns: `[ComputeOptimizerClientTypes.RecommendationSummary]`
    public func recommendationSummaries() async throws -> [ComputeOptimizerClientTypes.RecommendationSummary] {
        return try await self.asyncCompactMap { item in item.recommendationSummaries }
    }
}
