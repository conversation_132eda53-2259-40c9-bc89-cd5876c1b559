//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.<PERSON>y<PERSON><PERSON>er
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
@_spi(SmithyTimestamps) import struct SmithyTimestamps.TimestampFormatter


public struct VerifySessionInput: Swift.Sendable {

    public init() { }
}

/// The request was denied because you don't have sufficient access to perform this action. Verify that you are a member of a role that allows this action.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct CreateAccessTokenInput: Swift.Sendable {
    /// The date and time the personal access token expires, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
    public var expiresTime: Foundation.Date?
    /// The friendly name of the personal access token.
    /// This member is required.
    public var name: Swift.String?

    public init(
        expiresTime: Foundation.Date? = nil,
        name: Swift.String? = nil
    )
    {
        self.expiresTime = expiresTime
        self.name = name
    }
}

public struct CreateAccessTokenOutput: Swift.Sendable {
    /// The system-generated unique ID of the access token.
    /// This member is required.
    public var accessTokenId: Swift.String?
    /// The date and time the personal access token expires, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6). If not specified, the default is one year from creation.
    /// This member is required.
    public var expiresTime: Foundation.Date?
    /// The friendly name of the personal access token.
    /// This member is required.
    public var name: Swift.String?
    /// The secret value of the personal access token.
    /// This member is required.
    public var secret: Swift.String?

    public init(
        accessTokenId: Swift.String? = nil,
        expiresTime: Foundation.Date? = nil,
        name: Swift.String? = nil,
        secret: Swift.String? = nil
    )
    {
        self.accessTokenId = accessTokenId
        self.expiresTime = expiresTime
        self.name = name
        self.secret = secret
    }
}

extension CreateAccessTokenOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateAccessTokenOutput(accessTokenId: \(Swift.String(describing: accessTokenId)), expiresTime: \(Swift.String(describing: expiresTime)), name: \(Swift.String(describing: name)), secret: \"CONTENT_REDACTED\")"}
}

public struct DeleteAccessTokenInput: Swift.Sendable {
    /// The ID of the personal access token to delete. You can find the IDs of all PATs associated with your Amazon Web Services Builder ID in a space by calling [ListAccessTokens].
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

public struct DeleteAccessTokenOutput: Swift.Sendable {

    public init() { }
}

public struct ListAccessTokensInput: Swift.Sendable {
    /// The maximum number of results to show in a single call to this API. If the number of results is larger than the number you specified, the response will include a NextToken element, which you can use to obtain additional results.
    public var maxResults: Swift.Int?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension CodeCatalystClientTypes {

    /// Information about a specified personal access token (PAT).
    public struct AccessTokenSummary: Swift.Sendable {
        /// The date and time when the personal access token will expire, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
        public var expiresTime: Foundation.Date?
        /// The system-generated ID of the personal access token.
        /// This member is required.
        public var id: Swift.String?
        /// The friendly name of the personal access token.
        /// This member is required.
        public var name: Swift.String?

        public init(
            expiresTime: Foundation.Date? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil
        )
        {
            self.expiresTime = expiresTime
            self.id = id
            self.name = name
        }
    }
}

public struct ListAccessTokensOutput: Swift.Sendable {
    /// A list of personal access tokens (PATs) associated with the calling user identity.
    /// This member is required.
    public var items: [CodeCatalystClientTypes.AccessTokenSummary]?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?

    public init(
        items: [CodeCatalystClientTypes.AccessTokenSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

/// The request was denied because the requested operation would cause a conflict with the current state of a service resource associated with the request. Another user might have updated the resource. Reload, make sure you have the latest data, and then try again.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct GetUserDetailsInput: Swift.Sendable {
    /// The system-generated unique ID of the user.
    public var id: Swift.String?
    /// The name of the user as displayed in Amazon CodeCatalyst.
    public var userName: Swift.String?

    public init(
        id: Swift.String? = nil,
        userName: Swift.String? = nil
    )
    {
        self.id = id
        self.userName = userName
    }
}

extension CodeCatalystClientTypes {

    /// Information about an email address.
    public struct EmailAddress: Swift.Sendable {
        /// The email address.
        public var email: Swift.String?
        /// Whether the email address has been verified.
        public var verified: Swift.Bool?

        public init(
            email: Swift.String? = nil,
            verified: Swift.Bool? = nil
        )
        {
            self.email = email
            self.verified = verified
        }
    }
}

public struct GetUserDetailsOutput: Swift.Sendable {
    /// The friendly name displayed for the user in Amazon CodeCatalyst.
    public var displayName: Swift.String?
    /// The email address provided by the user when they signed up.
    public var primaryEmail: CodeCatalystClientTypes.EmailAddress?
    /// The system-generated unique ID of the user.
    public var userId: Swift.String?
    /// The name of the user as displayed in Amazon CodeCatalyst.
    public var userName: Swift.String?
    ///
    public var version: Swift.String?

    public init(
        displayName: Swift.String? = nil,
        primaryEmail: CodeCatalystClientTypes.EmailAddress? = nil,
        userId: Swift.String? = nil,
        userName: Swift.String? = nil,
        version: Swift.String? = nil
    )
    {
        self.displayName = displayName
        self.primaryEmail = primaryEmail
        self.userId = userId
        self.userName = userName
        self.version = version
    }
}

/// The request was denied because the specified resource was not found. Verify that the spelling is correct and that you have access to the resource.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request was denied because one or more resources has reached its limits for the tier the space belongs to. Either reduce the number of resources, or change the tier if applicable.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceQuotaExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DeleteSpaceInput: Swift.Sendable {
    /// The name of the space. To retrieve a list of space names, use [ListSpaces].
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct DeleteSpaceOutput: Swift.Sendable {
    /// The friendly name of the space displayed to users of the space in Amazon CodeCatalyst.
    public var displayName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var name: Swift.String?

    public init(
        displayName: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.displayName = displayName
        self.name = name
    }
}

public struct ListEventLogsInput: Swift.Sendable {
    /// The time after which you do not want any events retrieved, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
    /// This member is required.
    public var endTime: Foundation.Date?
    /// The name of the event.
    public var eventName: Swift.String?
    /// The maximum number of results to show in a single call to this API. If the number of results is larger than the number you specified, the response will include a NextToken element, which you can use to obtain additional results.
    public var maxResults: Swift.Int?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?
    /// The date and time when you want to start retrieving events, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
    /// This member is required.
    public var startTime: Foundation.Date?

    public init(
        endTime: Foundation.Date? = nil,
        eventName: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        spaceName: Swift.String? = nil,
        startTime: Foundation.Date? = nil
    )
    {
        self.endTime = endTime
        self.eventName = eventName
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.spaceName = spaceName
        self.startTime = startTime
    }
}

extension CodeCatalystClientTypes {

    public enum OperationType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case mutation
        case readonly
        case sdkUnknown(Swift.String)

        public static var allCases: [OperationType] {
            return [
                .mutation,
                .readonly
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .mutation: return "MUTATION"
            case .readonly: return "READONLY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeCatalystClientTypes {

    /// Information about a project in a space.
    public struct ProjectInformation: Swift.Sendable {
        /// The name of the project in the space.
        public var name: Swift.String?
        /// The system-generated unique ID of the project.
        public var projectId: Swift.String?

        public init(
            name: Swift.String? = nil,
            projectId: Swift.String? = nil
        )
        {
            self.name = name
            self.projectId = projectId
        }
    }
}

extension CodeCatalystClientTypes {

    /// Information about the payload of an event recording Amazon CodeCatalyst activity.
    public struct EventPayload: Swift.Sendable {
        /// The type of content in the event payload.
        public var contentType: Swift.String?
        /// The data included in the event payload.
        public var data: Swift.String?

        public init(
            contentType: Swift.String? = nil,
            data: Swift.String? = nil
        )
        {
            self.contentType = contentType
            self.data = data
        }
    }
}

extension CodeCatalystClientTypes {

    public enum UserType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case awsAccount
        case unknown
        case user
        case sdkUnknown(Swift.String)

        public static var allCases: [UserType] {
            return [
                .awsAccount,
                .unknown,
                .user
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .awsAccount: return "AWS_ACCOUNT"
            case .unknown: return "UNKNOWN"
            case .user: return "USER"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeCatalystClientTypes {

    /// Information about a user whose activity is recorded in an event for a space.
    public struct UserIdentity: Swift.Sendable {
        /// The Amazon Web Services account number of the user in Amazon Web Services, if any.
        public var awsAccountId: Swift.String?
        /// The ID of the Amazon CodeCatalyst service principal.
        /// This member is required.
        public var principalId: Swift.String?
        /// The display name of the user in Amazon CodeCatalyst.
        public var userName: Swift.String?
        /// The role assigned to the user in a Amazon CodeCatalyst space or project when the event occurred.
        /// This member is required.
        public var userType: CodeCatalystClientTypes.UserType?

        public init(
            awsAccountId: Swift.String? = nil,
            principalId: Swift.String? = nil,
            userName: Swift.String? = nil,
            userType: CodeCatalystClientTypes.UserType? = nil
        )
        {
            self.awsAccountId = awsAccountId
            self.principalId = principalId
            self.userName = userName
            self.userType = userType
        }
    }
}

extension CodeCatalystClientTypes {

    /// Information about an entry in an event log of Amazon CodeCatalyst activity.
    public struct EventLogEntry: Swift.Sendable {
        /// The code of the error, if any.
        public var errorCode: Swift.String?
        /// The category for the event.
        /// This member is required.
        public var eventCategory: Swift.String?
        /// The name of the event.
        /// This member is required.
        public var eventName: Swift.String?
        /// The source of the event.
        /// This member is required.
        public var eventSource: Swift.String?
        /// The time the event took place, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
        /// This member is required.
        public var eventTime: Foundation.Date?
        /// The type of the event.
        /// This member is required.
        public var eventType: Swift.String?
        /// The system-generated unique ID of the event.
        /// This member is required.
        public var id: Swift.String?
        /// The type of the event.
        /// This member is required.
        public var operationType: CodeCatalystClientTypes.OperationType?
        /// Information about the project where the event occurred.
        public var projectInformation: CodeCatalystClientTypes.ProjectInformation?
        /// The system-generated unique ID of the request.
        public var requestId: Swift.String?
        /// Information about the payload of the request.
        public var requestPayload: CodeCatalystClientTypes.EventPayload?
        /// Information about the payload of the response, if any.
        public var responsePayload: CodeCatalystClientTypes.EventPayload?
        /// The IP address of the user whose actions are recorded in the event.
        public var sourceIpAddress: Swift.String?
        /// The user agent whose actions are recorded in the event.
        public var userAgent: Swift.String?
        /// The system-generated unique ID of the user whose actions are recorded in the event.
        /// This member is required.
        public var userIdentity: CodeCatalystClientTypes.UserIdentity?

        public init(
            errorCode: Swift.String? = nil,
            eventCategory: Swift.String? = nil,
            eventName: Swift.String? = nil,
            eventSource: Swift.String? = nil,
            eventTime: Foundation.Date? = nil,
            eventType: Swift.String? = nil,
            id: Swift.String? = nil,
            operationType: CodeCatalystClientTypes.OperationType? = nil,
            projectInformation: CodeCatalystClientTypes.ProjectInformation? = nil,
            requestId: Swift.String? = nil,
            requestPayload: CodeCatalystClientTypes.EventPayload? = nil,
            responsePayload: CodeCatalystClientTypes.EventPayload? = nil,
            sourceIpAddress: Swift.String? = nil,
            userAgent: Swift.String? = nil,
            userIdentity: CodeCatalystClientTypes.UserIdentity? = nil
        )
        {
            self.errorCode = errorCode
            self.eventCategory = eventCategory
            self.eventName = eventName
            self.eventSource = eventSource
            self.eventTime = eventTime
            self.eventType = eventType
            self.id = id
            self.operationType = operationType
            self.projectInformation = projectInformation
            self.requestId = requestId
            self.requestPayload = requestPayload
            self.responsePayload = responsePayload
            self.sourceIpAddress = sourceIpAddress
            self.userAgent = userAgent
            self.userIdentity = userIdentity
        }
    }
}

public struct ListEventLogsOutput: Swift.Sendable {
    /// Information about each event retrieved in the list.
    /// This member is required.
    public var items: [CodeCatalystClientTypes.EventLogEntry]?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?

    public init(
        items: [CodeCatalystClientTypes.EventLogEntry]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

public struct GetSpaceInput: Swift.Sendable {
    /// The name of the space.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct GetSpaceOutput: Swift.Sendable {
    /// The description of the space.
    public var description: Swift.String?
    /// The friendly name of the space displayed to users.
    public var displayName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var name: Swift.String?
    /// The Amazon Web Services Region where the space exists.
    /// This member is required.
    public var regionName: Swift.String?

    public init(
        description: Swift.String? = nil,
        displayName: Swift.String? = nil,
        name: Swift.String? = nil,
        regionName: Swift.String? = nil
    )
    {
        self.description = description
        self.displayName = displayName
        self.name = name
        self.regionName = regionName
    }
}

extension CodeCatalystClientTypes {

    /// Information about a filter used to limit results of a query.
    public struct Filter: Swift.Sendable {
        /// The operator used to compare the fields.
        public var comparisonOperator: Swift.String?
        /// A key that can be used to sort results.
        /// This member is required.
        public var key: Swift.String?
        /// The values of the key.
        /// This member is required.
        public var values: [Swift.String]?

        public init(
            comparisonOperator: Swift.String? = nil,
            key: Swift.String? = nil,
            values: [Swift.String]? = nil
        )
        {
            self.comparisonOperator = comparisonOperator
            self.key = key
            self.values = values
        }
    }
}

public struct ListDevEnvironmentsInput: Swift.Sendable {
    /// Information about filters to apply to narrow the results returned in the list.
    public var filters: [CodeCatalystClientTypes.Filter]?
    /// The maximum number of results to show in a single call to this API. If the number of results is larger than the number you specified, the response will include a NextToken element, which you can use to obtain additional results.
    public var maxResults: Swift.Int?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?
    /// The name of the project in the space.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        filters: [CodeCatalystClientTypes.Filter]? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

extension CodeCatalystClientTypes {

    /// Information about an integrated development environment (IDE) used in a Dev Environment.
    public struct Ide: Swift.Sendable {
        /// The name of the IDE.
        public var name: Swift.String?
        /// A link to the IDE runtime image.
        public var runtime: Swift.String?

        public init(
            name: Swift.String? = nil,
            runtime: Swift.String? = nil
        )
        {
            self.name = name
            self.runtime = runtime
        }
    }
}

extension CodeCatalystClientTypes {

    public enum InstanceType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case devStandard1Large
        case devStandard1Medium
        case devStandard1Small
        case devStandard1Xlarge
        case sdkUnknown(Swift.String)

        public static var allCases: [InstanceType] {
            return [
                .devStandard1Large,
                .devStandard1Medium,
                .devStandard1Small,
                .devStandard1Xlarge
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .devStandard1Large: return "dev.standard1.large"
            case .devStandard1Medium: return "dev.standard1.medium"
            case .devStandard1Small: return "dev.standard1.small"
            case .devStandard1Xlarge: return "dev.standard1.xlarge"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeCatalystClientTypes {

    /// Information about the persistent storage for a Dev Environment.
    public struct PersistentStorage: Swift.Sendable {
        /// The size of the persistent storage in gigabytes (specifically GiB). Valid values for storage are based on memory sizes in 16GB increments. Valid values are 16, 32, and 64.
        /// This member is required.
        public var sizeInGiB: Swift.Int?

        public init(
            sizeInGiB: Swift.Int? = nil
        )
        {
            self.sizeInGiB = sizeInGiB
        }
    }
}

extension CodeCatalystClientTypes {

    /// Information about the source repsitory for a Dev Environment.
    public struct DevEnvironmentRepositorySummary: Swift.Sendable {
        /// The name of the branch in a source repository cloned into the Dev Environment.
        public var branchName: Swift.String?
        /// The name of the source repository.
        /// This member is required.
        public var repositoryName: Swift.String?

        public init(
            branchName: Swift.String? = nil,
            repositoryName: Swift.String? = nil
        )
        {
            self.branchName = branchName
            self.repositoryName = repositoryName
        }
    }
}

extension CodeCatalystClientTypes {

    public enum DevEnvironmentStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case deleted
        case deleting
        case failed
        case pending
        case running
        case starting
        case stopped
        case stopping
        case sdkUnknown(Swift.String)

        public static var allCases: [DevEnvironmentStatus] {
            return [
                .deleted,
                .deleting,
                .failed,
                .pending,
                .running,
                .starting,
                .stopped,
                .stopping
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .deleted: return "DELETED"
            case .deleting: return "DELETING"
            case .failed: return "FAILED"
            case .pending: return "PENDING"
            case .running: return "RUNNING"
            case .starting: return "STARTING"
            case .stopped: return "STOPPED"
            case .stopping: return "STOPPING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeCatalystClientTypes {

    /// Information about a Dev Environment.
    public struct DevEnvironmentSummary: Swift.Sendable {
        /// The user-specified alias for the Dev Environment.
        public var alias: Swift.String?
        /// The system-generated unique ID of the user who created the Dev Environment.
        /// This member is required.
        public var creatorId: Swift.String?
        /// The system-generated unique ID for the Dev Environment.
        /// This member is required.
        public var id: Swift.String?
        /// Information about the integrated development environment (IDE) configured for a Dev Environment.
        public var ides: [CodeCatalystClientTypes.Ide]?
        /// The amount of time the Dev Environment will run without any activity detected before stopping, in minutes. Dev Environments consume compute minutes when running.
        /// This member is required.
        public var inactivityTimeoutMinutes: Swift.Int
        /// The Amazon EC2 instace type used for the Dev Environment.
        /// This member is required.
        public var instanceType: CodeCatalystClientTypes.InstanceType?
        /// The time when the Dev Environment was last updated, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
        /// This member is required.
        public var lastUpdatedTime: Foundation.Date?
        /// Information about the configuration of persistent storage for the Dev Environment.
        /// This member is required.
        public var persistentStorage: CodeCatalystClientTypes.PersistentStorage?
        /// The name of the project in the space.
        public var projectName: Swift.String?
        /// Information about the repositories that will be cloned into the Dev Environment. If no rvalue is specified, no repository is cloned.
        /// This member is required.
        public var repositories: [CodeCatalystClientTypes.DevEnvironmentRepositorySummary]?
        /// The name of the space.
        public var spaceName: Swift.String?
        /// The status of the Dev Environment.
        /// This member is required.
        public var status: CodeCatalystClientTypes.DevEnvironmentStatus?
        /// The reason for the status.
        public var statusReason: Swift.String?
        /// The name of the connection used to connect to Amazon VPC used when the Dev Environment was created, if any.
        public var vpcConnectionName: Swift.String?

        public init(
            alias: Swift.String? = nil,
            creatorId: Swift.String? = nil,
            id: Swift.String? = nil,
            ides: [CodeCatalystClientTypes.Ide]? = nil,
            inactivityTimeoutMinutes: Swift.Int = 0,
            instanceType: CodeCatalystClientTypes.InstanceType? = nil,
            lastUpdatedTime: Foundation.Date? = nil,
            persistentStorage: CodeCatalystClientTypes.PersistentStorage? = nil,
            projectName: Swift.String? = nil,
            repositories: [CodeCatalystClientTypes.DevEnvironmentRepositorySummary]? = nil,
            spaceName: Swift.String? = nil,
            status: CodeCatalystClientTypes.DevEnvironmentStatus? = nil,
            statusReason: Swift.String? = nil,
            vpcConnectionName: Swift.String? = nil
        )
        {
            self.alias = alias
            self.creatorId = creatorId
            self.id = id
            self.ides = ides
            self.inactivityTimeoutMinutes = inactivityTimeoutMinutes
            self.instanceType = instanceType
            self.lastUpdatedTime = lastUpdatedTime
            self.persistentStorage = persistentStorage
            self.projectName = projectName
            self.repositories = repositories
            self.spaceName = spaceName
            self.status = status
            self.statusReason = statusReason
            self.vpcConnectionName = vpcConnectionName
        }
    }
}

public struct ListDevEnvironmentsOutput: Swift.Sendable {
    /// Information about the Dev Environments in a project.
    /// This member is required.
    public var items: [CodeCatalystClientTypes.DevEnvironmentSummary]?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?

    public init(
        items: [CodeCatalystClientTypes.DevEnvironmentSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

public struct ListSpacesInput: Swift.Sendable {
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?

    public init(
        nextToken: Swift.String? = nil
    )
    {
        self.nextToken = nextToken
    }
}

extension CodeCatalystClientTypes {

    /// Information about an space.
    public struct SpaceSummary: Swift.Sendable {
        /// The description of the space.
        public var description: Swift.String?
        /// The friendly name of the space displayed to users.
        public var displayName: Swift.String?
        /// The name of the space.
        /// This member is required.
        public var name: Swift.String?
        /// The Amazon Web Services Region where the space exists.
        /// This member is required.
        public var regionName: Swift.String?

        public init(
            description: Swift.String? = nil,
            displayName: Swift.String? = nil,
            name: Swift.String? = nil,
            regionName: Swift.String? = nil
        )
        {
            self.description = description
            self.displayName = displayName
            self.name = name
            self.regionName = regionName
        }
    }
}

public struct ListSpacesOutput: Swift.Sendable {
    /// Information about the spaces.
    public var items: [CodeCatalystClientTypes.SpaceSummary]?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?

    public init(
        items: [CodeCatalystClientTypes.SpaceSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

public struct CreateProjectInput: Swift.Sendable {
    /// The description of the project. This description will be displayed to all users of the project. We recommend providing a brief description of the project and its intended purpose.
    public var description: Swift.String?
    /// The friendly name of the project that will be displayed to users.
    /// This member is required.
    public var displayName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        description: Swift.String? = nil,
        displayName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.description = description
        self.displayName = displayName
        self.spaceName = spaceName
    }
}

public struct CreateProjectOutput: Swift.Sendable {
    /// The description of the project.
    public var description: Swift.String?
    /// The friendly name of the project.
    public var displayName: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var name: Swift.String?
    /// The name of the space.
    public var spaceName: Swift.String?

    public init(
        description: Swift.String? = nil,
        displayName: Swift.String? = nil,
        name: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.description = description
        self.displayName = displayName
        self.name = name
        self.spaceName = spaceName
    }
}

public struct DeleteProjectInput: Swift.Sendable {
    /// The name of the project in the space. To retrieve a list of project names, use [ListProjects].
    /// This member is required.
    public var name: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        name: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.name = name
        self.spaceName = spaceName
    }
}

public struct DeleteProjectOutput: Swift.Sendable {
    /// The friendly name displayed to users of the project in Amazon CodeCatalyst.
    public var displayName: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var name: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        displayName: Swift.String? = nil,
        name: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.displayName = displayName
        self.name = name
        self.spaceName = spaceName
    }
}

extension CodeCatalystClientTypes {

    /// Information about the configuration of an integrated development environment (IDE) for a Dev Environment.
    public struct IdeConfiguration: Swift.Sendable {
        /// The name of the IDE. Valid values include Cloud9, IntelliJ, PyCharm, GoLand, and VSCode.
        public var name: Swift.String?
        /// A link to the IDE runtime image. This parameter is not required for VSCode.
        public var runtime: Swift.String?

        public init(
            name: Swift.String? = nil,
            runtime: Swift.String? = nil
        )
        {
            self.name = name
            self.runtime = runtime
        }
    }
}

extension CodeCatalystClientTypes {

    /// Information about the configuration of persistent storage for a Dev Environment.
    public struct PersistentStorageConfiguration: Swift.Sendable {
        /// The size of the persistent storage in gigabytes (specifically GiB). Valid values for storage are based on memory sizes in 16GB increments. Valid values are 16, 32, and 64.
        /// This member is required.
        public var sizeInGiB: Swift.Int?

        public init(
            sizeInGiB: Swift.Int? = nil
        )
        {
            self.sizeInGiB = sizeInGiB
        }
    }
}

extension CodeCatalystClientTypes {

    /// Information about a repository that will be cloned to a Dev Environment.
    public struct RepositoryInput: Swift.Sendable {
        /// The name of the branch in a source repository.
        public var branchName: Swift.String?
        /// The name of the source repository.
        /// This member is required.
        public var repositoryName: Swift.String?

        public init(
            branchName: Swift.String? = nil,
            repositoryName: Swift.String? = nil
        )
        {
            self.branchName = branchName
            self.repositoryName = repositoryName
        }
    }
}

public struct CreateDevEnvironmentInput: Swift.Sendable {
    /// The user-defined alias for a Dev Environment.
    public var alias: Swift.String?
    /// A user-specified idempotency token. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, the subsequent retries return the result from the original successful request and have no additional effect.
    public var clientToken: Swift.String?
    /// Information about the integrated development environment (IDE) configured for a Dev Environment. An IDE is required to create a Dev Environment. For Dev Environment creation, this field contains configuration information and must be provided.
    public var ides: [CodeCatalystClientTypes.IdeConfiguration]?
    /// The amount of time the Dev Environment will run without any activity detected before stopping, in minutes. Only whole integers are allowed. Dev Environments consume compute minutes when running.
    public var inactivityTimeoutMinutes: Swift.Int?
    /// The Amazon EC2 instace type to use for the Dev Environment.
    /// This member is required.
    public var instanceType: CodeCatalystClientTypes.InstanceType?
    /// Information about the amount of storage allocated to the Dev Environment. By default, a Dev Environment is configured to have 16GB of persistent storage when created from the Amazon CodeCatalyst console, but there is no default when programmatically creating a Dev Environment. Valid values for persistent storage are based on memory sizes in 16GB increments. Valid values are 16, 32, and 64.
    /// This member is required.
    public var persistentStorage: CodeCatalystClientTypes.PersistentStorageConfiguration?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The source repository that contains the branch to clone into the Dev Environment.
    public var repositories: [CodeCatalystClientTypes.RepositoryInput]?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?
    /// The name of the connection that will be used to connect to Amazon VPC, if any.
    public var vpcConnectionName: Swift.String?

    public init(
        alias: Swift.String? = nil,
        clientToken: Swift.String? = nil,
        ides: [CodeCatalystClientTypes.IdeConfiguration]? = nil,
        inactivityTimeoutMinutes: Swift.Int? = 0,
        instanceType: CodeCatalystClientTypes.InstanceType? = nil,
        persistentStorage: CodeCatalystClientTypes.PersistentStorageConfiguration? = nil,
        projectName: Swift.String? = nil,
        repositories: [CodeCatalystClientTypes.RepositoryInput]? = nil,
        spaceName: Swift.String? = nil,
        vpcConnectionName: Swift.String? = nil
    )
    {
        self.alias = alias
        self.clientToken = clientToken
        self.ides = ides
        self.inactivityTimeoutMinutes = inactivityTimeoutMinutes
        self.instanceType = instanceType
        self.persistentStorage = persistentStorage
        self.projectName = projectName
        self.repositories = repositories
        self.spaceName = spaceName
        self.vpcConnectionName = vpcConnectionName
    }
}

public struct CreateDevEnvironmentOutput: Swift.Sendable {
    /// The system-generated unique ID of the Dev Environment.
    /// This member is required.
    public var id: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?
    /// The name of the connection used to connect to Amazon VPC used when the Dev Environment was created, if any.
    public var vpcConnectionName: Swift.String?

    public init(
        id: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil,
        vpcConnectionName: Swift.String? = nil
    )
    {
        self.id = id
        self.projectName = projectName
        self.spaceName = spaceName
        self.vpcConnectionName = vpcConnectionName
    }
}

public struct DeleteDevEnvironmentInput: Swift.Sendable {
    /// The system-generated unique ID of the Dev Environment you want to delete. To retrieve a list of Dev Environment IDs, use [ListDevEnvironments].
    /// This member is required.
    public var id: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        id: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.id = id
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

public struct DeleteDevEnvironmentOutput: Swift.Sendable {
    /// The system-generated unique ID of the deleted Dev Environment.
    /// This member is required.
    public var id: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        id: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.id = id
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

public struct GetDevEnvironmentInput: Swift.Sendable {
    /// The system-generated unique ID of the Dev Environment for which you want to view information. To retrieve a list of Dev Environment IDs, use [ListDevEnvironments].
    /// This member is required.
    public var id: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        id: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.id = id
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

public struct GetDevEnvironmentOutput: Swift.Sendable {
    /// The user-specified alias for the Dev Environment.
    public var alias: Swift.String?
    /// The system-generated unique ID of the user who created the Dev Environment.
    /// This member is required.
    public var creatorId: Swift.String?
    /// The system-generated unique ID of the Dev Environment.
    /// This member is required.
    public var id: Swift.String?
    /// Information about the integrated development environment (IDE) configured for the Dev Environment.
    public var ides: [CodeCatalystClientTypes.Ide]?
    /// The amount of time the Dev Environment will run without any activity detected before stopping, in minutes.
    /// This member is required.
    public var inactivityTimeoutMinutes: Swift.Int
    /// The Amazon EC2 instace type to use for the Dev Environment.
    /// This member is required.
    public var instanceType: CodeCatalystClientTypes.InstanceType?
    /// The time when the Dev Environment was last updated, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
    /// This member is required.
    public var lastUpdatedTime: Foundation.Date?
    /// Information about the amount of storage allocated to the Dev Environment. By default, a Dev Environment is configured to have 16GB of persistent storage.
    /// This member is required.
    public var persistentStorage: CodeCatalystClientTypes.PersistentStorage?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The source repository that contains the branch cloned into the Dev Environment.
    /// This member is required.
    public var repositories: [CodeCatalystClientTypes.DevEnvironmentRepositorySummary]?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?
    /// The current status of the Dev Environment.
    /// This member is required.
    public var status: CodeCatalystClientTypes.DevEnvironmentStatus?
    /// The reason for the status.
    public var statusReason: Swift.String?
    /// The name of the connection used to connect to Amazon VPC used when the Dev Environment was created, if any.
    public var vpcConnectionName: Swift.String?

    public init(
        alias: Swift.String? = nil,
        creatorId: Swift.String? = nil,
        id: Swift.String? = nil,
        ides: [CodeCatalystClientTypes.Ide]? = nil,
        inactivityTimeoutMinutes: Swift.Int = 0,
        instanceType: CodeCatalystClientTypes.InstanceType? = nil,
        lastUpdatedTime: Foundation.Date? = nil,
        persistentStorage: CodeCatalystClientTypes.PersistentStorage? = nil,
        projectName: Swift.String? = nil,
        repositories: [CodeCatalystClientTypes.DevEnvironmentRepositorySummary]? = nil,
        spaceName: Swift.String? = nil,
        status: CodeCatalystClientTypes.DevEnvironmentStatus? = nil,
        statusReason: Swift.String? = nil,
        vpcConnectionName: Swift.String? = nil
    )
    {
        self.alias = alias
        self.creatorId = creatorId
        self.id = id
        self.ides = ides
        self.inactivityTimeoutMinutes = inactivityTimeoutMinutes
        self.instanceType = instanceType
        self.lastUpdatedTime = lastUpdatedTime
        self.persistentStorage = persistentStorage
        self.projectName = projectName
        self.repositories = repositories
        self.spaceName = spaceName
        self.status = status
        self.statusReason = statusReason
        self.vpcConnectionName = vpcConnectionName
    }
}

public struct ListDevEnvironmentSessionsInput: Swift.Sendable {
    /// The system-generated unique ID of the Dev Environment.
    /// This member is required.
    public var devEnvironmentId: Swift.String?
    /// The maximum number of results to show in a single call to this API. If the number of results is larger than the number you specified, the response will include a NextToken element, which you can use to obtain additional results.
    public var maxResults: Swift.Int?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        devEnvironmentId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.devEnvironmentId = devEnvironmentId
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

extension CodeCatalystClientTypes {

    /// Information about active sessions for a Dev Environment.
    public struct DevEnvironmentSessionSummary: Swift.Sendable {
        /// The system-generated unique ID of the Dev Environment.
        /// This member is required.
        public var devEnvironmentId: Swift.String?
        /// The system-generated unique ID of the Dev Environment session.
        /// This member is required.
        public var id: Swift.String?
        /// The name of the project in the space.
        /// This member is required.
        public var projectName: Swift.String?
        /// The name of the space.
        /// This member is required.
        public var spaceName: Swift.String?
        /// The date and time the session started, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6)
        /// This member is required.
        public var startedTime: Foundation.Date?

        public init(
            devEnvironmentId: Swift.String? = nil,
            id: Swift.String? = nil,
            projectName: Swift.String? = nil,
            spaceName: Swift.String? = nil,
            startedTime: Foundation.Date? = nil
        )
        {
            self.devEnvironmentId = devEnvironmentId
            self.id = id
            self.projectName = projectName
            self.spaceName = spaceName
            self.startedTime = startedTime
        }
    }
}

public struct ListDevEnvironmentSessionsOutput: Swift.Sendable {
    /// Information about each session retrieved in the list.
    /// This member is required.
    public var items: [CodeCatalystClientTypes.DevEnvironmentSessionSummary]?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?

    public init(
        items: [CodeCatalystClientTypes.DevEnvironmentSessionSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

public struct StartDevEnvironmentInput: Swift.Sendable {
    /// The system-generated unique ID of the Dev Environment.
    /// This member is required.
    public var id: Swift.String?
    /// Information about the integrated development environment (IDE) configured for a Dev Environment.
    public var ides: [CodeCatalystClientTypes.IdeConfiguration]?
    /// The amount of time the Dev Environment will run without any activity detected before stopping, in minutes. Only whole integers are allowed. Dev Environments consume compute minutes when running.
    public var inactivityTimeoutMinutes: Swift.Int?
    /// The Amazon EC2 instace type to use for the Dev Environment.
    public var instanceType: CodeCatalystClientTypes.InstanceType?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        id: Swift.String? = nil,
        ides: [CodeCatalystClientTypes.IdeConfiguration]? = nil,
        inactivityTimeoutMinutes: Swift.Int? = 0,
        instanceType: CodeCatalystClientTypes.InstanceType? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.id = id
        self.ides = ides
        self.inactivityTimeoutMinutes = inactivityTimeoutMinutes
        self.instanceType = instanceType
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

public struct StartDevEnvironmentOutput: Swift.Sendable {
    /// The system-generated unique ID of the Dev Environment.
    /// This member is required.
    public var id: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?
    /// The status of the Dev Environment.
    /// This member is required.
    public var status: CodeCatalystClientTypes.DevEnvironmentStatus?

    public init(
        id: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil,
        status: CodeCatalystClientTypes.DevEnvironmentStatus? = nil
    )
    {
        self.id = id
        self.projectName = projectName
        self.spaceName = spaceName
        self.status = status
    }
}

extension CodeCatalystClientTypes {

    /// Information about the commands that will be run on a Dev Environment when an SSH session begins.
    public struct ExecuteCommandSessionConfiguration: Swift.Sendable {
        /// An array of arguments containing arguments and members.
        public var arguments: [Swift.String]?
        /// The command used at the beginning of the SSH session to a Dev Environment.
        /// This member is required.
        public var command: Swift.String?

        public init(
            arguments: [Swift.String]? = nil,
            command: Swift.String? = nil
        )
        {
            self.arguments = arguments
            self.command = command
        }
    }
}

extension CodeCatalystClientTypes {

    public enum DevEnvironmentSessionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case ssh
        case ssm
        case sdkUnknown(Swift.String)

        public static var allCases: [DevEnvironmentSessionType] {
            return [
                .ssh,
                .ssm
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .ssh: return "SSH"
            case .ssm: return "SSM"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeCatalystClientTypes {

    /// Information about the configuration of a Dev Environment session.
    public struct DevEnvironmentSessionConfiguration: Swift.Sendable {
        /// Information about optional commands that will be run on the Dev Environment when the SSH session begins.
        public var executeCommandSessionConfiguration: CodeCatalystClientTypes.ExecuteCommandSessionConfiguration?
        /// The type of the session.
        /// This member is required.
        public var sessionType: CodeCatalystClientTypes.DevEnvironmentSessionType?

        public init(
            executeCommandSessionConfiguration: CodeCatalystClientTypes.ExecuteCommandSessionConfiguration? = nil,
            sessionType: CodeCatalystClientTypes.DevEnvironmentSessionType? = nil
        )
        {
            self.executeCommandSessionConfiguration = executeCommandSessionConfiguration
            self.sessionType = sessionType
        }
    }
}

public struct StartDevEnvironmentSessionInput: Swift.Sendable {
    /// The system-generated unique ID of the Dev Environment.
    /// This member is required.
    public var id: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// Information about the configuration of a Dev Environment session.
    /// This member is required.
    public var sessionConfiguration: CodeCatalystClientTypes.DevEnvironmentSessionConfiguration?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        id: Swift.String? = nil,
        projectName: Swift.String? = nil,
        sessionConfiguration: CodeCatalystClientTypes.DevEnvironmentSessionConfiguration? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.id = id
        self.projectName = projectName
        self.sessionConfiguration = sessionConfiguration
        self.spaceName = spaceName
    }
}

extension CodeCatalystClientTypes {

    /// Information about connection details for a Dev Environment.
    public struct DevEnvironmentAccessDetails: Swift.Sendable {
        /// The URL used to send commands to and from the Dev Environment.
        /// This member is required.
        public var streamUrl: Swift.String?
        /// An encrypted token value that contains session and caller information used to authenticate the connection.
        /// This member is required.
        public var tokenValue: Swift.String?

        public init(
            streamUrl: Swift.String? = nil,
            tokenValue: Swift.String? = nil
        )
        {
            self.streamUrl = streamUrl
            self.tokenValue = tokenValue
        }
    }
}

extension CodeCatalystClientTypes.DevEnvironmentAccessDetails: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CONTENT_REDACTED"
    }
}

public struct StartDevEnvironmentSessionOutput: Swift.Sendable {
    /// Information about connection details for a Dev Environment.
    /// This member is required.
    public var accessDetails: CodeCatalystClientTypes.DevEnvironmentAccessDetails?
    /// The system-generated unique ID of the Dev Environment.
    /// This member is required.
    public var id: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The system-generated unique ID of the Dev Environment session.
    public var sessionId: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        accessDetails: CodeCatalystClientTypes.DevEnvironmentAccessDetails? = nil,
        id: Swift.String? = nil,
        projectName: Swift.String? = nil,
        sessionId: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.accessDetails = accessDetails
        self.id = id
        self.projectName = projectName
        self.sessionId = sessionId
        self.spaceName = spaceName
    }
}

extension StartDevEnvironmentSessionOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "StartDevEnvironmentSessionOutput(id: \(Swift.String(describing: id)), projectName: \(Swift.String(describing: projectName)), sessionId: \(Swift.String(describing: sessionId)), spaceName: \(Swift.String(describing: spaceName)), accessDetails: \"CONTENT_REDACTED\")"}
}

public struct StopDevEnvironmentInput: Swift.Sendable {
    /// The system-generated unique ID of the Dev Environment.
    /// This member is required.
    public var id: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        id: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.id = id
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

public struct StopDevEnvironmentOutput: Swift.Sendable {
    /// The system-generated unique ID of the Dev Environment.
    /// This member is required.
    public var id: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?
    /// The status of the Dev Environment.
    /// This member is required.
    public var status: CodeCatalystClientTypes.DevEnvironmentStatus?

    public init(
        id: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil,
        status: CodeCatalystClientTypes.DevEnvironmentStatus? = nil
    )
    {
        self.id = id
        self.projectName = projectName
        self.spaceName = spaceName
        self.status = status
    }
}

public struct StopDevEnvironmentSessionInput: Swift.Sendable {
    /// The system-generated unique ID of the Dev Environment. To obtain this ID, use [ListDevEnvironments].
    /// This member is required.
    public var id: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The system-generated unique ID of the Dev Environment session. This ID is returned by [StartDevEnvironmentSession].
    /// This member is required.
    public var sessionId: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        id: Swift.String? = nil,
        projectName: Swift.String? = nil,
        sessionId: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.id = id
        self.projectName = projectName
        self.sessionId = sessionId
        self.spaceName = spaceName
    }
}

public struct StopDevEnvironmentSessionOutput: Swift.Sendable {
    /// The system-generated unique ID of the Dev Environment.
    /// This member is required.
    public var id: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The system-generated unique ID of the Dev Environment session.
    /// This member is required.
    public var sessionId: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        id: Swift.String? = nil,
        projectName: Swift.String? = nil,
        sessionId: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.id = id
        self.projectName = projectName
        self.sessionId = sessionId
        self.spaceName = spaceName
    }
}

public struct UpdateDevEnvironmentInput: Swift.Sendable {
    /// The user-specified alias for the Dev Environment. Changing this value will not cause a restart.
    public var alias: Swift.String?
    /// A user-specified idempotency token. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, the subsequent retries return the result from the original successful request and have no additional effect.
    public var clientToken: Swift.String?
    /// The system-generated unique ID of the Dev Environment.
    /// This member is required.
    public var id: Swift.String?
    /// Information about the integrated development environment (IDE) configured for a Dev Environment.
    public var ides: [CodeCatalystClientTypes.IdeConfiguration]?
    /// The amount of time the Dev Environment will run without any activity detected before stopping, in minutes. Only whole integers are allowed. Dev Environments consume compute minutes when running. Changing this value will cause a restart of the Dev Environment if it is running.
    public var inactivityTimeoutMinutes: Swift.Int?
    /// The Amazon EC2 instace type to use for the Dev Environment. Changing this value will cause a restart of the Dev Environment if it is running.
    public var instanceType: CodeCatalystClientTypes.InstanceType?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        alias: Swift.String? = nil,
        clientToken: Swift.String? = nil,
        id: Swift.String? = nil,
        ides: [CodeCatalystClientTypes.IdeConfiguration]? = nil,
        inactivityTimeoutMinutes: Swift.Int? = 0,
        instanceType: CodeCatalystClientTypes.InstanceType? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.alias = alias
        self.clientToken = clientToken
        self.id = id
        self.ides = ides
        self.inactivityTimeoutMinutes = inactivityTimeoutMinutes
        self.instanceType = instanceType
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

public struct UpdateDevEnvironmentOutput: Swift.Sendable {
    /// The user-specified alias for the Dev Environment.
    public var alias: Swift.String?
    /// A user-specified idempotency token. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, the subsequent retries return the result from the original successful request and have no additional effect.
    public var clientToken: Swift.String?
    /// The system-generated unique ID of the Dev Environment.
    /// This member is required.
    public var id: Swift.String?
    /// Information about the integrated development environment (IDE) configured for the Dev Environment.
    public var ides: [CodeCatalystClientTypes.IdeConfiguration]?
    /// The amount of time the Dev Environment will run without any activity detected before stopping, in minutes.
    public var inactivityTimeoutMinutes: Swift.Int
    /// The Amazon EC2 instace type to use for the Dev Environment.
    public var instanceType: CodeCatalystClientTypes.InstanceType?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        alias: Swift.String? = nil,
        clientToken: Swift.String? = nil,
        id: Swift.String? = nil,
        ides: [CodeCatalystClientTypes.IdeConfiguration]? = nil,
        inactivityTimeoutMinutes: Swift.Int = 0,
        instanceType: CodeCatalystClientTypes.InstanceType? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.alias = alias
        self.clientToken = clientToken
        self.id = id
        self.ides = ides
        self.inactivityTimeoutMinutes = inactivityTimeoutMinutes
        self.instanceType = instanceType
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

public struct GetProjectInput: Swift.Sendable {
    /// The name of the project in the space.
    /// This member is required.
    public var name: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        name: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.name = name
        self.spaceName = spaceName
    }
}

public struct GetProjectOutput: Swift.Sendable {
    /// The description of the project.
    public var description: Swift.String?
    /// The friendly name of the project displayed to users in Amazon CodeCatalyst.
    public var displayName: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var name: Swift.String?
    /// The name of the space.
    public var spaceName: Swift.String?

    public init(
        description: Swift.String? = nil,
        displayName: Swift.String? = nil,
        name: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.description = description
        self.displayName = displayName
        self.name = name
        self.spaceName = spaceName
    }
}

extension CodeCatalystClientTypes {

    public enum ComparisonOperator: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case beginsWith
        case equals
        case greaterThan
        case greaterThanOrEquals
        case lessThan
        case lessThanOrEquals
        case sdkUnknown(Swift.String)

        public static var allCases: [ComparisonOperator] {
            return [
                .beginsWith,
                .equals,
                .greaterThan,
                .greaterThanOrEquals,
                .lessThan,
                .lessThanOrEquals
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .beginsWith: return "BEGINS_WITH"
            case .equals: return "EQ"
            case .greaterThan: return "GT"
            case .greaterThanOrEquals: return "GE"
            case .lessThan: return "LT"
            case .lessThanOrEquals: return "LE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeCatalystClientTypes {

    public enum FilterKey: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case hasAccessTo
        case name
        case sdkUnknown(Swift.String)

        public static var allCases: [FilterKey] {
            return [
                .hasAccessTo,
                .name
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .hasAccessTo: return "hasAccessTo"
            case .name: return "name"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeCatalystClientTypes {

    /// nformation about the filter used to narrow the results returned in a list of projects.
    public struct ProjectListFilter: Swift.Sendable {
        /// The operator used to compare the fields.
        public var comparisonOperator: CodeCatalystClientTypes.ComparisonOperator?
        /// A key that can be used to sort results.
        /// This member is required.
        public var key: CodeCatalystClientTypes.FilterKey?
        /// The values of the key.
        /// This member is required.
        public var values: [Swift.String]?

        public init(
            comparisonOperator: CodeCatalystClientTypes.ComparisonOperator? = nil,
            key: CodeCatalystClientTypes.FilterKey? = nil,
            values: [Swift.String]? = nil
        )
        {
            self.comparisonOperator = comparisonOperator
            self.key = key
            self.values = values
        }
    }
}

public struct ListProjectsInput: Swift.Sendable {
    /// Information about filters to apply to narrow the results returned in the list.
    public var filters: [CodeCatalystClientTypes.ProjectListFilter]?
    /// The maximum number of results to show in a single call to this API. If the number of results is larger than the number you specified, the response will include a NextToken element, which you can use to obtain additional results.
    public var maxResults: Swift.Int?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        filters: [CodeCatalystClientTypes.ProjectListFilter]? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.spaceName = spaceName
    }
}

extension CodeCatalystClientTypes {

    /// Information about a project.
    public struct ProjectSummary: Swift.Sendable {
        /// The description of the project.
        public var description: Swift.String?
        /// The friendly name displayed to users of the project in Amazon CodeCatalyst.
        public var displayName: Swift.String?
        /// The name of the project in the space.
        /// This member is required.
        public var name: Swift.String?

        public init(
            description: Swift.String? = nil,
            displayName: Swift.String? = nil,
            name: Swift.String? = nil
        )
        {
            self.description = description
            self.displayName = displayName
            self.name = name
        }
    }
}

public struct ListProjectsOutput: Swift.Sendable {
    /// Information about the projects.
    public var items: [CodeCatalystClientTypes.ProjectSummary]?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?

    public init(
        items: [CodeCatalystClientTypes.ProjectSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

public struct CreateSourceRepositoryInput: Swift.Sendable {
    /// The description of the source repository.
    public var description: Swift.String?
    /// The name of the source repository. For more information about name requirements, see [Quotas for source repositories](https://docs.aws.amazon.com/codecatalyst/latest/userguide/source-quotas.html).
    /// This member is required.
    public var name: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        description: Swift.String? = nil,
        name: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.description = description
        self.name = name
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

public struct CreateSourceRepositoryOutput: Swift.Sendable {
    /// The description of the source repository.
    public var description: Swift.String?
    /// The name of the source repository.
    /// This member is required.
    public var name: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        description: Swift.String? = nil,
        name: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.description = description
        self.name = name
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

public struct DeleteSourceRepositoryInput: Swift.Sendable {
    /// The name of the source repository.
    /// This member is required.
    public var name: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        name: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.name = name
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

public struct DeleteSourceRepositoryOutput: Swift.Sendable {
    /// The name of the repository.
    /// This member is required.
    public var name: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        name: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.name = name
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

public struct GetSourceRepositoryInput: Swift.Sendable {
    /// The name of the source repository.
    /// This member is required.
    public var name: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        name: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.name = name
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

public struct GetSourceRepositoryOutput: Swift.Sendable {
    /// The time the source repository was created, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
    /// This member is required.
    public var createdTime: Foundation.Date?
    /// The description of the source repository.
    public var description: Swift.String?
    /// The time the source repository was last updated, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
    /// This member is required.
    public var lastUpdatedTime: Foundation.Date?
    /// The name of the source repository.
    /// This member is required.
    public var name: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        createdTime: Foundation.Date? = nil,
        description: Swift.String? = nil,
        lastUpdatedTime: Foundation.Date? = nil,
        name: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.createdTime = createdTime
        self.description = description
        self.lastUpdatedTime = lastUpdatedTime
        self.name = name
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

public struct GetSourceRepositoryCloneUrlsInput: Swift.Sendable {
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the source repository.
    /// This member is required.
    public var sourceRepositoryName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        projectName: Swift.String? = nil,
        sourceRepositoryName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.projectName = projectName
        self.sourceRepositoryName = sourceRepositoryName
        self.spaceName = spaceName
    }
}

public struct GetSourceRepositoryCloneUrlsOutput: Swift.Sendable {
    /// The HTTPS URL to use when cloning the source repository.
    /// This member is required.
    public var https: Swift.String?

    public init(
        https: Swift.String? = nil
    )
    {
        self.https = https
    }
}

public struct ListSourceRepositoriesInput: Swift.Sendable {
    /// The maximum number of results to show in a single call to this API. If the number of results is larger than the number you specified, the response will include a NextToken element, which you can use to obtain additional results.
    public var maxResults: Swift.Int?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

extension CodeCatalystClientTypes {

    /// Information about a source repository returned in a list of source repositories.
    public struct ListSourceRepositoriesItem: Swift.Sendable {
        /// The time the source repository was created, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
        /// This member is required.
        public var createdTime: Foundation.Date?
        /// The description of the repository, if any.
        public var description: Swift.String?
        /// The system-generated unique ID of the source repository.
        /// This member is required.
        public var id: Swift.String?
        /// The time the source repository was last updated, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
        /// This member is required.
        public var lastUpdatedTime: Foundation.Date?
        /// The name of the source repository.
        /// This member is required.
        public var name: Swift.String?

        public init(
            createdTime: Foundation.Date? = nil,
            description: Swift.String? = nil,
            id: Swift.String? = nil,
            lastUpdatedTime: Foundation.Date? = nil,
            name: Swift.String? = nil
        )
        {
            self.createdTime = createdTime
            self.description = description
            self.id = id
            self.lastUpdatedTime = lastUpdatedTime
            self.name = name
        }
    }
}

public struct ListSourceRepositoriesOutput: Swift.Sendable {
    /// Information about the source repositories.
    public var items: [CodeCatalystClientTypes.ListSourceRepositoriesItem]?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?

    public init(
        items: [CodeCatalystClientTypes.ListSourceRepositoriesItem]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

public struct CreateSourceRepositoryBranchInput: Swift.Sendable {
    /// The commit ID in an existing branch from which you want to create the new branch.
    public var headCommitId: Swift.String?
    /// The name for the branch you're creating.
    /// This member is required.
    public var name: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the repository where you want to create a branch.
    /// This member is required.
    public var sourceRepositoryName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        headCommitId: Swift.String? = nil,
        name: Swift.String? = nil,
        projectName: Swift.String? = nil,
        sourceRepositoryName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.headCommitId = headCommitId
        self.name = name
        self.projectName = projectName
        self.sourceRepositoryName = sourceRepositoryName
        self.spaceName = spaceName
    }
}

public struct CreateSourceRepositoryBranchOutput: Swift.Sendable {
    /// The commit ID of the tip of the newly created branch.
    public var headCommitId: Swift.String?
    /// The time the branch was last updated, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
    public var lastUpdatedTime: Foundation.Date?
    /// The name of the newly created branch.
    public var name: Swift.String?
    /// The Git reference name of the branch.
    public var ref: Swift.String?

    public init(
        headCommitId: Swift.String? = nil,
        lastUpdatedTime: Foundation.Date? = nil,
        name: Swift.String? = nil,
        ref: Swift.String? = nil
    )
    {
        self.headCommitId = headCommitId
        self.lastUpdatedTime = lastUpdatedTime
        self.name = name
        self.ref = ref
    }
}

public struct ListSourceRepositoryBranchesInput: Swift.Sendable {
    /// The maximum number of results to show in a single call to this API. If the number of results is larger than the number you specified, the response will include a NextToken element, which you can use to obtain additional results.
    public var maxResults: Swift.Int?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the source repository.
    /// This member is required.
    public var sourceRepositoryName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        projectName: Swift.String? = nil,
        sourceRepositoryName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.projectName = projectName
        self.sourceRepositoryName = sourceRepositoryName
        self.spaceName = spaceName
    }
}

extension CodeCatalystClientTypes {

    /// Information about a branch of a source repository returned in a list of branches.
    public struct ListSourceRepositoryBranchesItem: Swift.Sendable {
        /// The commit ID of the tip of the branch at the time of the request, also known as the head commit.
        public var headCommitId: Swift.String?
        /// The time the branch was last updated, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
        public var lastUpdatedTime: Foundation.Date?
        /// The name of the branch.
        public var name: Swift.String?
        /// The Git reference name of the branch.
        public var ref: Swift.String?

        public init(
            headCommitId: Swift.String? = nil,
            lastUpdatedTime: Foundation.Date? = nil,
            name: Swift.String? = nil,
            ref: Swift.String? = nil
        )
        {
            self.headCommitId = headCommitId
            self.lastUpdatedTime = lastUpdatedTime
            self.name = name
            self.ref = ref
        }
    }
}

public struct ListSourceRepositoryBranchesOutput: Swift.Sendable {
    /// Information about the source branches.
    /// This member is required.
    public var items: [CodeCatalystClientTypes.ListSourceRepositoryBranchesItem]?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?

    public init(
        items: [CodeCatalystClientTypes.ListSourceRepositoryBranchesItem]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

public struct UpdateProjectInput: Swift.Sendable {
    /// The description of the project.
    public var description: Swift.String?
    /// The name of the project.
    /// This member is required.
    public var name: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        description: Swift.String? = nil,
        name: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.description = description
        self.name = name
        self.spaceName = spaceName
    }
}

public struct UpdateProjectOutput: Swift.Sendable {
    /// The description of the project.
    public var description: Swift.String?
    /// The friendly name of the project displayed to users in Amazon CodeCatalyst.
    public var displayName: Swift.String?
    /// The name of the project.
    public var name: Swift.String?
    /// The name of the space.
    public var spaceName: Swift.String?

    public init(
        description: Swift.String? = nil,
        displayName: Swift.String? = nil,
        name: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.description = description
        self.displayName = displayName
        self.name = name
        self.spaceName = spaceName
    }
}

public struct GetWorkflowInput: Swift.Sendable {
    /// The ID of the workflow. To rerieve a list of workflow IDs, use [ListWorkflows].
    /// This member is required.
    public var id: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        id: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.id = id
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

extension CodeCatalystClientTypes {

    /// Information about a workflow definition file.
    public struct WorkflowDefinition: Swift.Sendable {
        /// The path to the workflow definition file stored in the source repository for the project, including the file name.
        /// This member is required.
        public var path: Swift.String?

        public init(
            path: Swift.String? = nil
        )
        {
            self.path = path
        }
    }
}

extension CodeCatalystClientTypes {

    public enum WorkflowRunMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case parallel
        case queued
        case superseded
        case sdkUnknown(Swift.String)

        public static var allCases: [WorkflowRunMode] {
            return [
                .parallel,
                .queued,
                .superseded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .parallel: return "PARALLEL"
            case .queued: return "QUEUED"
            case .superseded: return "SUPERSEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeCatalystClientTypes {

    public enum WorkflowStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case active
        case invalid
        case sdkUnknown(Swift.String)

        public static var allCases: [WorkflowStatus] {
            return [
                .active,
                .invalid
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .active: return "ACTIVE"
            case .invalid: return "INVALID"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct GetWorkflowOutput: Swift.Sendable {
    /// The date and time the workflow was created, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6)
    /// This member is required.
    public var createdTime: Foundation.Date?
    /// Information about the workflow definition file for the workflow.
    /// This member is required.
    public var definition: CodeCatalystClientTypes.WorkflowDefinition?
    /// The ID of the workflow.
    /// This member is required.
    public var id: Swift.String?
    /// The date and time the workflow was last updated, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6)
    /// This member is required.
    public var lastUpdatedTime: Foundation.Date?
    /// The name of the workflow.
    /// This member is required.
    public var name: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The behavior to use when multiple workflows occur at the same time. For more information, see [https://docs.aws.amazon.com/codecatalyst/latest/userguide/workflows-configure-runs.html](https://docs.aws.amazon.com/codecatalyst/latest/userguide/workflows-configure-runs.html) in the Amazon CodeCatalyst User Guide.
    /// This member is required.
    public var runMode: CodeCatalystClientTypes.WorkflowRunMode?
    /// The name of the branch that contains the workflow YAML.
    public var sourceBranchName: Swift.String?
    /// The name of the source repository where the workflow YAML is stored.
    public var sourceRepositoryName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?
    /// The status of the workflow.
    /// This member is required.
    public var status: CodeCatalystClientTypes.WorkflowStatus?

    public init(
        createdTime: Foundation.Date? = nil,
        definition: CodeCatalystClientTypes.WorkflowDefinition? = nil,
        id: Swift.String? = nil,
        lastUpdatedTime: Foundation.Date? = nil,
        name: Swift.String? = nil,
        projectName: Swift.String? = nil,
        runMode: CodeCatalystClientTypes.WorkflowRunMode? = nil,
        sourceBranchName: Swift.String? = nil,
        sourceRepositoryName: Swift.String? = nil,
        spaceName: Swift.String? = nil,
        status: CodeCatalystClientTypes.WorkflowStatus? = nil
    )
    {
        self.createdTime = createdTime
        self.definition = definition
        self.id = id
        self.lastUpdatedTime = lastUpdatedTime
        self.name = name
        self.projectName = projectName
        self.runMode = runMode
        self.sourceBranchName = sourceBranchName
        self.sourceRepositoryName = sourceRepositoryName
        self.spaceName = spaceName
        self.status = status
    }
}

extension CodeCatalystClientTypes {

    /// Information used to sort workflows in the returned list.
    public struct WorkflowSortCriteria: Swift.Sendable {

        public init() { }
    }
}

public struct ListWorkflowsInput: Swift.Sendable {
    /// The maximum number of results to show in a single call to this API. If the number of results is larger than the number you specified, the response will include a NextToken element, which you can use to obtain additional results.
    public var maxResults: Swift.Int?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// Information used to sort the items in the returned list.
    public var sortBy: [CodeCatalystClientTypes.WorkflowSortCriteria]?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        projectName: Swift.String? = nil,
        sortBy: [CodeCatalystClientTypes.WorkflowSortCriteria]? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.projectName = projectName
        self.sortBy = sortBy
        self.spaceName = spaceName
    }
}

extension CodeCatalystClientTypes {

    /// Information about a workflow definition.
    public struct WorkflowDefinitionSummary: Swift.Sendable {
        /// The path to the workflow definition file stored in the source repository for the project, including the file name.
        /// This member is required.
        public var path: Swift.String?

        public init(
            path: Swift.String? = nil
        )
        {
            self.path = path
        }
    }
}

extension CodeCatalystClientTypes {

    /// Information about a workflow.
    public struct WorkflowSummary: Swift.Sendable {
        /// The date and time the workflow was created, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6)
        /// This member is required.
        public var createdTime: Foundation.Date?
        /// Information about the workflow definition file.
        /// This member is required.
        public var definition: CodeCatalystClientTypes.WorkflowDefinitionSummary?
        /// The system-generated unique ID of a workflow.
        /// This member is required.
        public var id: Swift.String?
        /// The date and time the workflow was last updated, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6)
        /// This member is required.
        public var lastUpdatedTime: Foundation.Date?
        /// The name of the workflow.
        /// This member is required.
        public var name: Swift.String?
        /// The run mode of the workflow.
        /// This member is required.
        public var runMode: CodeCatalystClientTypes.WorkflowRunMode?
        /// The name of the branch of the source repository where the workflow definition file is stored.
        /// This member is required.
        public var sourceBranchName: Swift.String?
        /// The name of the source repository where the workflow definition file is stored.
        /// This member is required.
        public var sourceRepositoryName: Swift.String?
        /// The status of the workflow.
        /// This member is required.
        public var status: CodeCatalystClientTypes.WorkflowStatus?

        public init(
            createdTime: Foundation.Date? = nil,
            definition: CodeCatalystClientTypes.WorkflowDefinitionSummary? = nil,
            id: Swift.String? = nil,
            lastUpdatedTime: Foundation.Date? = nil,
            name: Swift.String? = nil,
            runMode: CodeCatalystClientTypes.WorkflowRunMode? = nil,
            sourceBranchName: Swift.String? = nil,
            sourceRepositoryName: Swift.String? = nil,
            status: CodeCatalystClientTypes.WorkflowStatus? = nil
        )
        {
            self.createdTime = createdTime
            self.definition = definition
            self.id = id
            self.lastUpdatedTime = lastUpdatedTime
            self.name = name
            self.runMode = runMode
            self.sourceBranchName = sourceBranchName
            self.sourceRepositoryName = sourceRepositoryName
            self.status = status
        }
    }
}

public struct ListWorkflowsOutput: Swift.Sendable {
    /// Information about the workflows in a project.
    public var items: [CodeCatalystClientTypes.WorkflowSummary]?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?

    public init(
        items: [CodeCatalystClientTypes.WorkflowSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

public struct GetWorkflowRunInput: Swift.Sendable {
    /// The ID of the workflow run. To retrieve a list of workflow run IDs, use [ListWorkflowRuns].
    /// This member is required.
    public var id: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        id: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil
    )
    {
        self.id = id
        self.projectName = projectName
        self.spaceName = spaceName
    }
}

extension CodeCatalystClientTypes {

    public enum WorkflowRunStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case abandoned
        case cancelled
        case failed
        case inProgress
        case notRun
        case provisioning
        case stopped
        case stopping
        case succeeded
        case superseded
        case validating
        case sdkUnknown(Swift.String)

        public static var allCases: [WorkflowRunStatus] {
            return [
                .abandoned,
                .cancelled,
                .failed,
                .inProgress,
                .notRun,
                .provisioning,
                .stopped,
                .stopping,
                .succeeded,
                .superseded,
                .validating
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .abandoned: return "ABANDONED"
            case .cancelled: return "CANCELLED"
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case .notRun: return "NOT_RUN"
            case .provisioning: return "PROVISIONING"
            case .stopped: return "STOPPED"
            case .stopping: return "STOPPING"
            case .succeeded: return "SUCCEEDED"
            case .superseded: return "SUPERSEDED"
            case .validating: return "VALIDATING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeCatalystClientTypes {

    /// Information about the status of a workflow run.
    public struct WorkflowRunStatusReason: Swift.Sendable {

        public init() { }
    }
}

public struct GetWorkflowRunOutput: Swift.Sendable {
    /// The date and time the workflow run ended, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
    public var endTime: Foundation.Date?
    /// The ID of the workflow run.
    /// This member is required.
    public var id: Swift.String?
    /// The date and time the workflow run status was last updated, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6)
    /// This member is required.
    public var lastUpdatedTime: Foundation.Date?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?
    /// The date and time the workflow run began, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6)
    /// This member is required.
    public var startTime: Foundation.Date?
    /// The status of the workflow run.
    /// This member is required.
    public var status: CodeCatalystClientTypes.WorkflowRunStatus?
    /// Information about the reasons for the status of the workflow run.
    public var statusReasons: [CodeCatalystClientTypes.WorkflowRunStatusReason]?
    /// The ID of the workflow.
    /// This member is required.
    public var workflowId: Swift.String?

    public init(
        endTime: Foundation.Date? = nil,
        id: Swift.String? = nil,
        lastUpdatedTime: Foundation.Date? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil,
        startTime: Foundation.Date? = nil,
        status: CodeCatalystClientTypes.WorkflowRunStatus? = nil,
        statusReasons: [CodeCatalystClientTypes.WorkflowRunStatusReason]? = nil,
        workflowId: Swift.String? = nil
    )
    {
        self.endTime = endTime
        self.id = id
        self.lastUpdatedTime = lastUpdatedTime
        self.projectName = projectName
        self.spaceName = spaceName
        self.startTime = startTime
        self.status = status
        self.statusReasons = statusReasons
        self.workflowId = workflowId
    }
}

extension CodeCatalystClientTypes {

    /// Information used to sort workflow runs in the returned list.
    public struct WorkflowRunSortCriteria: Swift.Sendable {

        public init() { }
    }
}

public struct ListWorkflowRunsInput: Swift.Sendable {
    /// The maximum number of results to show in a single call to this API. If the number of results is larger than the number you specified, the response will include a NextToken element, which you can use to obtain additional results.
    public var maxResults: Swift.Int?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// Information used to sort the items in the returned list.
    public var sortBy: [CodeCatalystClientTypes.WorkflowRunSortCriteria]?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?
    /// The ID of the workflow. To retrieve a list of workflow IDs, use [ListWorkflows].
    public var workflowId: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        projectName: Swift.String? = nil,
        sortBy: [CodeCatalystClientTypes.WorkflowRunSortCriteria]? = nil,
        spaceName: Swift.String? = nil,
        workflowId: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.projectName = projectName
        self.sortBy = sortBy
        self.spaceName = spaceName
        self.workflowId = workflowId
    }
}

extension CodeCatalystClientTypes {

    /// Information about a workflow run.
    public struct WorkflowRunSummary: Swift.Sendable {
        /// The date and time the workflow run ended, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6)
        public var endTime: Foundation.Date?
        /// The system-generated unique ID of the workflow run.
        /// This member is required.
        public var id: Swift.String?
        /// The date and time the workflow was last updated, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6)
        /// This member is required.
        public var lastUpdatedTime: Foundation.Date?
        /// The date and time the workflow run began, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
        /// This member is required.
        public var startTime: Foundation.Date?
        /// The status of the workflow run.
        /// This member is required.
        public var status: CodeCatalystClientTypes.WorkflowRunStatus?
        /// The reasons for the workflow run status.
        public var statusReasons: [CodeCatalystClientTypes.WorkflowRunStatusReason]?
        /// The system-generated unique ID of the workflow.
        /// This member is required.
        public var workflowId: Swift.String?
        /// The name of the workflow.
        /// This member is required.
        public var workflowName: Swift.String?

        public init(
            endTime: Foundation.Date? = nil,
            id: Swift.String? = nil,
            lastUpdatedTime: Foundation.Date? = nil,
            startTime: Foundation.Date? = nil,
            status: CodeCatalystClientTypes.WorkflowRunStatus? = nil,
            statusReasons: [CodeCatalystClientTypes.WorkflowRunStatusReason]? = nil,
            workflowId: Swift.String? = nil,
            workflowName: Swift.String? = nil
        )
        {
            self.endTime = endTime
            self.id = id
            self.lastUpdatedTime = lastUpdatedTime
            self.startTime = startTime
            self.status = status
            self.statusReasons = statusReasons
            self.workflowId = workflowId
            self.workflowName = workflowName
        }
    }
}

public struct ListWorkflowRunsOutput: Swift.Sendable {
    /// Information about the runs of a workflow.
    public var items: [CodeCatalystClientTypes.WorkflowRunSummary]?
    /// A token returned from a call to this API to indicate the next batch of results to return, if any.
    public var nextToken: Swift.String?

    public init(
        items: [CodeCatalystClientTypes.WorkflowRunSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

public struct StartWorkflowRunInput: Swift.Sendable {
    /// A user-specified idempotency token. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, the subsequent retries return the result from the original successful request and have no additional effect.
    public var clientToken: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?
    /// The system-generated unique ID of the workflow. To retrieve a list of workflow IDs, use [ListWorkflows].
    /// This member is required.
    public var workflowId: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil,
        workflowId: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.projectName = projectName
        self.spaceName = spaceName
        self.workflowId = workflowId
    }
}

public struct StartWorkflowRunOutput: Swift.Sendable {
    /// The system-generated unique ID of the workflow run.
    /// This member is required.
    public var id: Swift.String?
    /// The name of the project in the space.
    /// This member is required.
    public var projectName: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?
    /// The system-generated unique ID of the workflow.
    /// This member is required.
    public var workflowId: Swift.String?

    public init(
        id: Swift.String? = nil,
        projectName: Swift.String? = nil,
        spaceName: Swift.String? = nil,
        workflowId: Swift.String? = nil
    )
    {
        self.id = id
        self.projectName = projectName
        self.spaceName = spaceName
        self.workflowId = workflowId
    }
}

public struct GetSubscriptionInput: Swift.Sendable {
    /// The name of the space.
    /// This member is required.
    public var spaceName: Swift.String?

    public init(
        spaceName: Swift.String? = nil
    )
    {
        self.spaceName = spaceName
    }
}

public struct GetSubscriptionOutput: Swift.Sendable {
    /// The display name of the Amazon Web Services account used for billing for the space.
    public var awsAccountName: Swift.String?
    /// The day and time the pending change will be applied to the space, in coordinated universal time (UTC) timestamp format as specified in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339#section-5.6).
    public var pendingSubscriptionStartTime: Foundation.Date?
    /// The type of the billing plan that the space will be changed to at the start of the next billing cycle. This applies only to changes that reduce the functionality available for the space. Billing plan changes that increase functionality are applied immediately. For more information, see [Pricing](https://codecatalyst.aws/explore/pricing).
    public var pendingSubscriptionType: Swift.String?
    /// The type of the billing plan for the space.
    public var subscriptionType: Swift.String?

    public init(
        awsAccountName: Swift.String? = nil,
        pendingSubscriptionStartTime: Foundation.Date? = nil,
        pendingSubscriptionType: Swift.String? = nil,
        subscriptionType: Swift.String? = nil
    )
    {
        self.awsAccountName = awsAccountName
        self.pendingSubscriptionStartTime = pendingSubscriptionStartTime
        self.pendingSubscriptionType = pendingSubscriptionType
        self.subscriptionType = subscriptionType
    }
}

public struct UpdateSpaceInput: Swift.Sendable {
    /// The description of the space.
    public var description: Swift.String?
    /// The name of the space.
    /// This member is required.
    public var name: Swift.String?

    public init(
        description: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.description = description
        self.name = name
    }
}

public struct UpdateSpaceOutput: Swift.Sendable {
    /// The description of the space.
    public var description: Swift.String?
    /// The friendly name of the space displayed to users in Amazon CodeCatalyst.
    public var displayName: Swift.String?
    /// The name of the space.
    public var name: Swift.String?

    public init(
        description: Swift.String? = nil,
        displayName: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.description = description
        self.displayName = displayName
        self.name = name
    }
}

/// The request was denied due to request throttling.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request was denied because an input failed to satisfy the constraints specified by the service. Check the spelling and input requirements, and then try again.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct VerifySessionOutput: Swift.Sendable {
    /// The system-generated unique ID of the user in Amazon CodeCatalyst.
    public var identity: Swift.String?

    public init(
        identity: Swift.String? = nil
    )
    {
        self.identity = identity
    }
}

extension CreateAccessTokenInput {

    static func urlPathProvider(_ value: CreateAccessTokenInput) -> Swift.String? {
        return "/v1/accessTokens"
    }
}

extension CreateDevEnvironmentInput {

    static func urlPathProvider(_ value: CreateDevEnvironmentInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/devEnvironments"
    }
}

extension CreateProjectInput {

    static func urlPathProvider(_ value: CreateProjectInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects"
    }
}

extension CreateSourceRepositoryInput {

    static func urlPathProvider(_ value: CreateSourceRepositoryInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let name = value.name else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/sourceRepositories/\(name.urlPercentEncoding())"
    }
}

extension CreateSourceRepositoryBranchInput {

    static func urlPathProvider(_ value: CreateSourceRepositoryBranchInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let sourceRepositoryName = value.sourceRepositoryName else {
            return nil
        }
        guard let name = value.name else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/sourceRepositories/\(sourceRepositoryName.urlPercentEncoding())/branches/\(name.urlPercentEncoding())"
    }
}

extension DeleteAccessTokenInput {

    static func urlPathProvider(_ value: DeleteAccessTokenInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/v1/accessTokens/\(id.urlPercentEncoding())"
    }
}

extension DeleteDevEnvironmentInput {

    static func urlPathProvider(_ value: DeleteDevEnvironmentInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let id = value.id else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/devEnvironments/\(id.urlPercentEncoding())"
    }
}

extension DeleteProjectInput {

    static func urlPathProvider(_ value: DeleteProjectInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let name = value.name else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(name.urlPercentEncoding())"
    }
}

extension DeleteSourceRepositoryInput {

    static func urlPathProvider(_ value: DeleteSourceRepositoryInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let name = value.name else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/sourceRepositories/\(name.urlPercentEncoding())"
    }
}

extension DeleteSpaceInput {

    static func urlPathProvider(_ value: DeleteSpaceInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/v1/spaces/\(name.urlPercentEncoding())"
    }
}

extension GetDevEnvironmentInput {

    static func urlPathProvider(_ value: GetDevEnvironmentInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let id = value.id else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/devEnvironments/\(id.urlPercentEncoding())"
    }
}

extension GetProjectInput {

    static func urlPathProvider(_ value: GetProjectInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let name = value.name else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(name.urlPercentEncoding())"
    }
}

extension GetSourceRepositoryInput {

    static func urlPathProvider(_ value: GetSourceRepositoryInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let name = value.name else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/sourceRepositories/\(name.urlPercentEncoding())"
    }
}

extension GetSourceRepositoryCloneUrlsInput {

    static func urlPathProvider(_ value: GetSourceRepositoryCloneUrlsInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let sourceRepositoryName = value.sourceRepositoryName else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/sourceRepositories/\(sourceRepositoryName.urlPercentEncoding())/cloneUrls"
    }
}

extension GetSpaceInput {

    static func urlPathProvider(_ value: GetSpaceInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/v1/spaces/\(name.urlPercentEncoding())"
    }
}

extension GetSubscriptionInput {

    static func urlPathProvider(_ value: GetSubscriptionInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/subscription"
    }
}

extension GetUserDetailsInput {

    static func urlPathProvider(_ value: GetUserDetailsInput) -> Swift.String? {
        return "/userDetails"
    }
}

extension GetUserDetailsInput {

    static func queryItemProvider(_ value: GetUserDetailsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let id = value.id {
            let idQueryItem = Smithy.URIQueryItem(name: "id".urlPercentEncoding(), value: Swift.String(id).urlPercentEncoding())
            items.append(idQueryItem)
        }
        if let userName = value.userName {
            let userNameQueryItem = Smithy.URIQueryItem(name: "userName".urlPercentEncoding(), value: Swift.String(userName).urlPercentEncoding())
            items.append(userNameQueryItem)
        }
        return items
    }
}

extension GetWorkflowInput {

    static func urlPathProvider(_ value: GetWorkflowInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let id = value.id else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/workflows/\(id.urlPercentEncoding())"
    }
}

extension GetWorkflowRunInput {

    static func urlPathProvider(_ value: GetWorkflowRunInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let id = value.id else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/workflowRuns/\(id.urlPercentEncoding())"
    }
}

extension ListAccessTokensInput {

    static func urlPathProvider(_ value: ListAccessTokensInput) -> Swift.String? {
        return "/v1/accessTokens"
    }
}

extension ListDevEnvironmentsInput {

    static func urlPathProvider(_ value: ListDevEnvironmentsInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/devEnvironments"
    }
}

extension ListDevEnvironmentSessionsInput {

    static func urlPathProvider(_ value: ListDevEnvironmentSessionsInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let devEnvironmentId = value.devEnvironmentId else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/devEnvironments/\(devEnvironmentId.urlPercentEncoding())/sessions"
    }
}

extension ListEventLogsInput {

    static func urlPathProvider(_ value: ListEventLogsInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/eventLogs"
    }
}

extension ListProjectsInput {

    static func urlPathProvider(_ value: ListProjectsInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects"
    }
}

extension ListSourceRepositoriesInput {

    static func urlPathProvider(_ value: ListSourceRepositoriesInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/sourceRepositories"
    }
}

extension ListSourceRepositoryBranchesInput {

    static func urlPathProvider(_ value: ListSourceRepositoryBranchesInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let sourceRepositoryName = value.sourceRepositoryName else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/sourceRepositories/\(sourceRepositoryName.urlPercentEncoding())/branches"
    }
}

extension ListSpacesInput {

    static func urlPathProvider(_ value: ListSpacesInput) -> Swift.String? {
        return "/v1/spaces"
    }
}

extension ListWorkflowRunsInput {

    static func urlPathProvider(_ value: ListWorkflowRunsInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/workflowRuns"
    }
}

extension ListWorkflowRunsInput {

    static func queryItemProvider(_ value: ListWorkflowRunsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let workflowId = value.workflowId {
            let workflowIdQueryItem = Smithy.URIQueryItem(name: "workflowId".urlPercentEncoding(), value: Swift.String(workflowId).urlPercentEncoding())
            items.append(workflowIdQueryItem)
        }
        return items
    }
}

extension ListWorkflowsInput {

    static func urlPathProvider(_ value: ListWorkflowsInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/workflows"
    }
}

extension ListWorkflowsInput {

    static func queryItemProvider(_ value: ListWorkflowsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension StartDevEnvironmentInput {

    static func urlPathProvider(_ value: StartDevEnvironmentInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let id = value.id else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/devEnvironments/\(id.urlPercentEncoding())/start"
    }
}

extension StartDevEnvironmentSessionInput {

    static func urlPathProvider(_ value: StartDevEnvironmentSessionInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let id = value.id else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/devEnvironments/\(id.urlPercentEncoding())/session"
    }
}

extension StartWorkflowRunInput {

    static func urlPathProvider(_ value: StartWorkflowRunInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/workflowRuns"
    }
}

extension StartWorkflowRunInput {

    static func queryItemProvider(_ value: StartWorkflowRunInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let workflowId = value.workflowId else {
            let message = "Creating a URL Query Item failed. workflowId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let workflowIdQueryItem = Smithy.URIQueryItem(name: "workflowId".urlPercentEncoding(), value: Swift.String(workflowId).urlPercentEncoding())
        items.append(workflowIdQueryItem)
        return items
    }
}

extension StopDevEnvironmentInput {

    static func urlPathProvider(_ value: StopDevEnvironmentInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let id = value.id else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/devEnvironments/\(id.urlPercentEncoding())/stop"
    }
}

extension StopDevEnvironmentSessionInput {

    static func urlPathProvider(_ value: StopDevEnvironmentSessionInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let id = value.id else {
            return nil
        }
        guard let sessionId = value.sessionId else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/devEnvironments/\(id.urlPercentEncoding())/session/\(sessionId.urlPercentEncoding())"
    }
}

extension UpdateDevEnvironmentInput {

    static func urlPathProvider(_ value: UpdateDevEnvironmentInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let projectName = value.projectName else {
            return nil
        }
        guard let id = value.id else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(projectName.urlPercentEncoding())/devEnvironments/\(id.urlPercentEncoding())"
    }
}

extension UpdateProjectInput {

    static func urlPathProvider(_ value: UpdateProjectInput) -> Swift.String? {
        guard let spaceName = value.spaceName else {
            return nil
        }
        guard let name = value.name else {
            return nil
        }
        return "/v1/spaces/\(spaceName.urlPercentEncoding())/projects/\(name.urlPercentEncoding())"
    }
}

extension UpdateSpaceInput {

    static func urlPathProvider(_ value: UpdateSpaceInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/v1/spaces/\(name.urlPercentEncoding())"
    }
}

extension VerifySessionInput {

    static func urlPathProvider(_ value: VerifySessionInput) -> Swift.String? {
        return "/session"
    }
}

extension CreateAccessTokenInput {

    static func write(value: CreateAccessTokenInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["expiresTime"].writeTimestamp(value.expiresTime, format: SmithyTimestamps.TimestampFormat.dateTime)
        try writer["name"].write(value.name)
    }
}

extension CreateDevEnvironmentInput {

    static func write(value: CreateDevEnvironmentInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["alias"].write(value.alias)
        try writer["clientToken"].write(value.clientToken)
        try writer["ides"].writeList(value.ides, memberWritingClosure: CodeCatalystClientTypes.IdeConfiguration.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["inactivityTimeoutMinutes"].write(value.inactivityTimeoutMinutes)
        try writer["instanceType"].write(value.instanceType)
        try writer["persistentStorage"].write(value.persistentStorage, with: CodeCatalystClientTypes.PersistentStorageConfiguration.write(value:to:))
        try writer["repositories"].writeList(value.repositories, memberWritingClosure: CodeCatalystClientTypes.RepositoryInput.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["vpcConnectionName"].write(value.vpcConnectionName)
    }
}

extension CreateProjectInput {

    static func write(value: CreateProjectInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["description"].write(value.description)
        try writer["displayName"].write(value.displayName)
    }
}

extension CreateSourceRepositoryInput {

    static func write(value: CreateSourceRepositoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["description"].write(value.description)
    }
}

extension CreateSourceRepositoryBranchInput {

    static func write(value: CreateSourceRepositoryBranchInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["headCommitId"].write(value.headCommitId)
    }
}

extension ListAccessTokensInput {

    static func write(value: ListAccessTokensInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension ListDevEnvironmentsInput {

    static func write(value: ListDevEnvironmentsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filters"].writeList(value.filters, memberWritingClosure: CodeCatalystClientTypes.Filter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["projectName"].write(value.projectName)
    }
}

extension ListDevEnvironmentSessionsInput {

    static func write(value: ListDevEnvironmentSessionsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension ListEventLogsInput {

    static func write(value: ListEventLogsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["endTime"].writeTimestamp(value.endTime, format: SmithyTimestamps.TimestampFormat.dateTime)
        try writer["eventName"].write(value.eventName)
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["startTime"].writeTimestamp(value.startTime, format: SmithyTimestamps.TimestampFormat.dateTime)
    }
}

extension ListProjectsInput {

    static func write(value: ListProjectsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filters"].writeList(value.filters, memberWritingClosure: CodeCatalystClientTypes.ProjectListFilter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension ListSourceRepositoriesInput {

    static func write(value: ListSourceRepositoriesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension ListSourceRepositoryBranchesInput {

    static func write(value: ListSourceRepositoryBranchesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension ListSpacesInput {

    static func write(value: ListSpacesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["nextToken"].write(value.nextToken)
    }
}

extension ListWorkflowRunsInput {

    static func write(value: ListWorkflowRunsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sortBy"].writeList(value.sortBy, memberWritingClosure: CodeCatalystClientTypes.WorkflowRunSortCriteria.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ListWorkflowsInput {

    static func write(value: ListWorkflowsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sortBy"].writeList(value.sortBy, memberWritingClosure: CodeCatalystClientTypes.WorkflowSortCriteria.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension StartDevEnvironmentInput {

    static func write(value: StartDevEnvironmentInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ides"].writeList(value.ides, memberWritingClosure: CodeCatalystClientTypes.IdeConfiguration.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["inactivityTimeoutMinutes"].write(value.inactivityTimeoutMinutes)
        try writer["instanceType"].write(value.instanceType)
    }
}

extension StartDevEnvironmentSessionInput {

    static func write(value: StartDevEnvironmentSessionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sessionConfiguration"].write(value.sessionConfiguration, with: CodeCatalystClientTypes.DevEnvironmentSessionConfiguration.write(value:to:))
    }
}

extension StartWorkflowRunInput {

    static func write(value: StartWorkflowRunInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["clientToken"].write(value.clientToken)
    }
}

extension UpdateDevEnvironmentInput {

    static func write(value: UpdateDevEnvironmentInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["alias"].write(value.alias)
        try writer["clientToken"].write(value.clientToken)
        try writer["ides"].writeList(value.ides, memberWritingClosure: CodeCatalystClientTypes.IdeConfiguration.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["inactivityTimeoutMinutes"].write(value.inactivityTimeoutMinutes)
        try writer["instanceType"].write(value.instanceType)
    }
}

extension UpdateProjectInput {

    static func write(value: UpdateProjectInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["description"].write(value.description)
    }
}

extension UpdateSpaceInput {

    static func write(value: UpdateSpaceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["description"].write(value.description)
    }
}

extension CreateAccessTokenOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateAccessTokenOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateAccessTokenOutput()
        value.accessTokenId = try reader["accessTokenId"].readIfPresent() ?? ""
        value.expiresTime = try reader["expiresTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.name = try reader["name"].readIfPresent() ?? ""
        value.secret = try reader["secret"].readIfPresent() ?? ""
        return value
    }
}

extension CreateDevEnvironmentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateDevEnvironmentOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateDevEnvironmentOutput()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        value.vpcConnectionName = try reader["vpcConnectionName"].readIfPresent()
        return value
    }
}

extension CreateProjectOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateProjectOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateProjectOutput()
        value.description = try reader["description"].readIfPresent()
        value.displayName = try reader["displayName"].readIfPresent()
        value.name = try reader["name"].readIfPresent() ?? ""
        value.spaceName = try reader["spaceName"].readIfPresent()
        return value
    }
}

extension CreateSourceRepositoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateSourceRepositoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateSourceRepositoryOutput()
        value.description = try reader["description"].readIfPresent()
        value.name = try reader["name"].readIfPresent() ?? ""
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        return value
    }
}

extension CreateSourceRepositoryBranchOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateSourceRepositoryBranchOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateSourceRepositoryBranchOutput()
        value.headCommitId = try reader["headCommitId"].readIfPresent()
        value.lastUpdatedTime = try reader["lastUpdatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.name = try reader["name"].readIfPresent()
        value.ref = try reader["ref"].readIfPresent()
        return value
    }
}

extension DeleteAccessTokenOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteAccessTokenOutput {
        return DeleteAccessTokenOutput()
    }
}

extension DeleteDevEnvironmentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteDevEnvironmentOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteDevEnvironmentOutput()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        return value
    }
}

extension DeleteProjectOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteProjectOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteProjectOutput()
        value.displayName = try reader["displayName"].readIfPresent()
        value.name = try reader["name"].readIfPresent() ?? ""
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        return value
    }
}

extension DeleteSourceRepositoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteSourceRepositoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteSourceRepositoryOutput()
        value.name = try reader["name"].readIfPresent() ?? ""
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        return value
    }
}

extension DeleteSpaceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteSpaceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteSpaceOutput()
        value.displayName = try reader["displayName"].readIfPresent()
        value.name = try reader["name"].readIfPresent() ?? ""
        return value
    }
}

extension GetDevEnvironmentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetDevEnvironmentOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetDevEnvironmentOutput()
        value.alias = try reader["alias"].readIfPresent()
        value.creatorId = try reader["creatorId"].readIfPresent() ?? ""
        value.id = try reader["id"].readIfPresent() ?? ""
        value.ides = try reader["ides"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.Ide.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.inactivityTimeoutMinutes = try reader["inactivityTimeoutMinutes"].readIfPresent() ?? 0
        value.instanceType = try reader["instanceType"].readIfPresent() ?? .sdkUnknown("")
        value.lastUpdatedTime = try reader["lastUpdatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.persistentStorage = try reader["persistentStorage"].readIfPresent(with: CodeCatalystClientTypes.PersistentStorage.read(from:))
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.repositories = try reader["repositories"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.DevEnvironmentRepositorySummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        value.statusReason = try reader["statusReason"].readIfPresent()
        value.vpcConnectionName = try reader["vpcConnectionName"].readIfPresent()
        return value
    }
}

extension GetProjectOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetProjectOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetProjectOutput()
        value.description = try reader["description"].readIfPresent()
        value.displayName = try reader["displayName"].readIfPresent()
        value.name = try reader["name"].readIfPresent() ?? ""
        value.spaceName = try reader["spaceName"].readIfPresent()
        return value
    }
}

extension GetSourceRepositoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetSourceRepositoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetSourceRepositoryOutput()
        value.createdTime = try reader["createdTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.description = try reader["description"].readIfPresent()
        value.lastUpdatedTime = try reader["lastUpdatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.name = try reader["name"].readIfPresent() ?? ""
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        return value
    }
}

extension GetSourceRepositoryCloneUrlsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetSourceRepositoryCloneUrlsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetSourceRepositoryCloneUrlsOutput()
        value.https = try reader["https"].readIfPresent() ?? ""
        return value
    }
}

extension GetSpaceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetSpaceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetSpaceOutput()
        value.description = try reader["description"].readIfPresent()
        value.displayName = try reader["displayName"].readIfPresent()
        value.name = try reader["name"].readIfPresent() ?? ""
        value.regionName = try reader["regionName"].readIfPresent() ?? ""
        return value
    }
}

extension GetSubscriptionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetSubscriptionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetSubscriptionOutput()
        value.awsAccountName = try reader["awsAccountName"].readIfPresent()
        value.pendingSubscriptionStartTime = try reader["pendingSubscriptionStartTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.pendingSubscriptionType = try reader["pendingSubscriptionType"].readIfPresent()
        value.subscriptionType = try reader["subscriptionType"].readIfPresent()
        return value
    }
}

extension GetUserDetailsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetUserDetailsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetUserDetailsOutput()
        value.displayName = try reader["displayName"].readIfPresent()
        value.primaryEmail = try reader["primaryEmail"].readIfPresent(with: CodeCatalystClientTypes.EmailAddress.read(from:))
        value.userId = try reader["userId"].readIfPresent()
        value.userName = try reader["userName"].readIfPresent()
        value.version = try reader["version"].readIfPresent()
        return value
    }
}

extension GetWorkflowOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetWorkflowOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetWorkflowOutput()
        value.createdTime = try reader["createdTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.definition = try reader["definition"].readIfPresent(with: CodeCatalystClientTypes.WorkflowDefinition.read(from:))
        value.id = try reader["id"].readIfPresent() ?? ""
        value.lastUpdatedTime = try reader["lastUpdatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.name = try reader["name"].readIfPresent() ?? ""
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.runMode = try reader["runMode"].readIfPresent() ?? .sdkUnknown("")
        value.sourceBranchName = try reader["sourceBranchName"].readIfPresent()
        value.sourceRepositoryName = try reader["sourceRepositoryName"].readIfPresent()
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension GetWorkflowRunOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetWorkflowRunOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetWorkflowRunOutput()
        value.endTime = try reader["endTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.id = try reader["id"].readIfPresent() ?? ""
        value.lastUpdatedTime = try reader["lastUpdatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        value.startTime = try reader["startTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        value.statusReasons = try reader["statusReasons"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.WorkflowRunStatusReason.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.workflowId = try reader["workflowId"].readIfPresent() ?? ""
        return value
    }
}

extension ListAccessTokensOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListAccessTokensOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListAccessTokensOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.AccessTokenSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListDevEnvironmentsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListDevEnvironmentsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListDevEnvironmentsOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.DevEnvironmentSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListDevEnvironmentSessionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListDevEnvironmentSessionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListDevEnvironmentSessionsOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.DevEnvironmentSessionSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListEventLogsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListEventLogsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListEventLogsOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.EventLogEntry.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListProjectsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListProjectsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListProjectsOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.ProjectSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListSourceRepositoriesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListSourceRepositoriesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListSourceRepositoriesOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.ListSourceRepositoriesItem.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListSourceRepositoryBranchesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListSourceRepositoryBranchesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListSourceRepositoryBranchesOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.ListSourceRepositoryBranchesItem.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListSpacesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListSpacesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListSpacesOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.SpaceSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListWorkflowRunsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListWorkflowRunsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListWorkflowRunsOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.WorkflowRunSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListWorkflowsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListWorkflowsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListWorkflowsOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.WorkflowSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension StartDevEnvironmentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartDevEnvironmentOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartDevEnvironmentOutput()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension StartDevEnvironmentSessionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartDevEnvironmentSessionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartDevEnvironmentSessionOutput()
        value.accessDetails = try reader["accessDetails"].readIfPresent(with: CodeCatalystClientTypes.DevEnvironmentAccessDetails.read(from:))
        value.id = try reader["id"].readIfPresent() ?? ""
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.sessionId = try reader["sessionId"].readIfPresent()
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        return value
    }
}

extension StartWorkflowRunOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartWorkflowRunOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartWorkflowRunOutput()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        value.workflowId = try reader["workflowId"].readIfPresent() ?? ""
        return value
    }
}

extension StopDevEnvironmentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopDevEnvironmentOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StopDevEnvironmentOutput()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension StopDevEnvironmentSessionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopDevEnvironmentSessionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StopDevEnvironmentSessionOutput()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.sessionId = try reader["sessionId"].readIfPresent() ?? ""
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        return value
    }
}

extension UpdateDevEnvironmentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateDevEnvironmentOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateDevEnvironmentOutput()
        value.alias = try reader["alias"].readIfPresent()
        value.clientToken = try reader["clientToken"].readIfPresent()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.ides = try reader["ides"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.IdeConfiguration.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.inactivityTimeoutMinutes = try reader["inactivityTimeoutMinutes"].readIfPresent() ?? 0
        value.instanceType = try reader["instanceType"].readIfPresent()
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        return value
    }
}

extension UpdateProjectOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateProjectOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateProjectOutput()
        value.description = try reader["description"].readIfPresent()
        value.displayName = try reader["displayName"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.spaceName = try reader["spaceName"].readIfPresent()
        return value
    }
}

extension UpdateSpaceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateSpaceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateSpaceOutput()
        value.description = try reader["description"].readIfPresent()
        value.displayName = try reader["displayName"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        return value
    }
}

extension VerifySessionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> VerifySessionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = VerifySessionOutput()
        value.identity = try reader["identity"].readIfPresent()
        return value
    }
}

func httpServiceError(baseError: AWSClientRuntime.RestJSONError) throws -> Swift.Error? {
    switch baseError.code {
        case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
        case "ConflictException": return try ConflictException.makeError(baseError: baseError)
        case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
        case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
        case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
        case "ValidationException": return try ValidationException.makeError(baseError: baseError)
        default: return nil
    }
}

enum CreateAccessTokenOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateDevEnvironmentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateProjectOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateSourceRepositoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateSourceRepositoryBranchOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteAccessTokenOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteDevEnvironmentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteProjectOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteSourceRepositoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteSpaceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetDevEnvironmentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetProjectOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetSourceRepositoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetSourceRepositoryCloneUrlsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetSpaceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetSubscriptionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetUserDetailsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetWorkflowOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetWorkflowRunOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListAccessTokensOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListDevEnvironmentsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListDevEnvironmentSessionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListEventLogsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListProjectsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListSourceRepositoriesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListSourceRepositoryBranchesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListSpacesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListWorkflowRunsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListWorkflowsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartDevEnvironmentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartDevEnvironmentSessionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartWorkflowRunOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopDevEnvironmentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopDevEnvironmentSessionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateDevEnvironmentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateProjectOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateSpaceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum VerifySessionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceQuotaExceededException {
        let reader = baseError.errorBodyReader
        var value = ServiceQuotaExceededException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        var value = ThrottlingException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CodeCatalystClientTypes.DevEnvironmentRepositorySummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.DevEnvironmentRepositorySummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.DevEnvironmentRepositorySummary()
        value.repositoryName = try reader["repositoryName"].readIfPresent() ?? ""
        value.branchName = try reader["branchName"].readIfPresent()
        return value
    }
}

extension CodeCatalystClientTypes.Ide {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.Ide {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.Ide()
        value.runtime = try reader["runtime"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        return value
    }
}

extension CodeCatalystClientTypes.PersistentStorage {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.PersistentStorage {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.PersistentStorage()
        value.sizeInGiB = try reader["sizeInGiB"].readIfPresent() ?? 0
        return value
    }
}

extension CodeCatalystClientTypes.EmailAddress {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.EmailAddress {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.EmailAddress()
        value.email = try reader["email"].readIfPresent()
        value.verified = try reader["verified"].readIfPresent()
        return value
    }
}

extension CodeCatalystClientTypes.WorkflowDefinition {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.WorkflowDefinition {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.WorkflowDefinition()
        value.path = try reader["path"].readIfPresent() ?? ""
        return value
    }
}

extension CodeCatalystClientTypes.WorkflowRunStatusReason {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.WorkflowRunStatusReason {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        return CodeCatalystClientTypes.WorkflowRunStatusReason()
    }
}

extension CodeCatalystClientTypes.AccessTokenSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.AccessTokenSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.AccessTokenSummary()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent() ?? ""
        value.expiresTime = try reader["expiresTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension CodeCatalystClientTypes.DevEnvironmentSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.DevEnvironmentSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.DevEnvironmentSummary()
        value.spaceName = try reader["spaceName"].readIfPresent()
        value.projectName = try reader["projectName"].readIfPresent()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.lastUpdatedTime = try reader["lastUpdatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.creatorId = try reader["creatorId"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        value.statusReason = try reader["statusReason"].readIfPresent()
        value.repositories = try reader["repositories"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.DevEnvironmentRepositorySummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.alias = try reader["alias"].readIfPresent()
        value.ides = try reader["ides"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.Ide.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.instanceType = try reader["instanceType"].readIfPresent() ?? .sdkUnknown("")
        value.inactivityTimeoutMinutes = try reader["inactivityTimeoutMinutes"].readIfPresent() ?? 0
        value.persistentStorage = try reader["persistentStorage"].readIfPresent(with: CodeCatalystClientTypes.PersistentStorage.read(from:))
        value.vpcConnectionName = try reader["vpcConnectionName"].readIfPresent()
        return value
    }
}

extension CodeCatalystClientTypes.DevEnvironmentSessionSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.DevEnvironmentSessionSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.DevEnvironmentSessionSummary()
        value.spaceName = try reader["spaceName"].readIfPresent() ?? ""
        value.projectName = try reader["projectName"].readIfPresent() ?? ""
        value.devEnvironmentId = try reader["devEnvironmentId"].readIfPresent() ?? ""
        value.startedTime = try reader["startedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.id = try reader["id"].readIfPresent() ?? ""
        return value
    }
}

extension CodeCatalystClientTypes.EventLogEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.EventLogEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.EventLogEntry()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.eventName = try reader["eventName"].readIfPresent() ?? ""
        value.eventType = try reader["eventType"].readIfPresent() ?? ""
        value.eventCategory = try reader["eventCategory"].readIfPresent() ?? ""
        value.eventSource = try reader["eventSource"].readIfPresent() ?? ""
        value.eventTime = try reader["eventTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.operationType = try reader["operationType"].readIfPresent() ?? .sdkUnknown("")
        value.userIdentity = try reader["userIdentity"].readIfPresent(with: CodeCatalystClientTypes.UserIdentity.read(from:))
        value.projectInformation = try reader["projectInformation"].readIfPresent(with: CodeCatalystClientTypes.ProjectInformation.read(from:))
        value.requestId = try reader["requestId"].readIfPresent()
        value.requestPayload = try reader["requestPayload"].readIfPresent(with: CodeCatalystClientTypes.EventPayload.read(from:))
        value.responsePayload = try reader["responsePayload"].readIfPresent(with: CodeCatalystClientTypes.EventPayload.read(from:))
        value.errorCode = try reader["errorCode"].readIfPresent()
        value.sourceIpAddress = try reader["sourceIpAddress"].readIfPresent()
        value.userAgent = try reader["userAgent"].readIfPresent()
        return value
    }
}

extension CodeCatalystClientTypes.EventPayload {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.EventPayload {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.EventPayload()
        value.contentType = try reader["contentType"].readIfPresent()
        value.data = try reader["data"].readIfPresent()
        return value
    }
}

extension CodeCatalystClientTypes.ProjectInformation {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.ProjectInformation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.ProjectInformation()
        value.name = try reader["name"].readIfPresent()
        value.projectId = try reader["projectId"].readIfPresent()
        return value
    }
}

extension CodeCatalystClientTypes.UserIdentity {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.UserIdentity {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.UserIdentity()
        value.userType = try reader["userType"].readIfPresent() ?? .sdkUnknown("")
        value.principalId = try reader["principalId"].readIfPresent() ?? ""
        value.userName = try reader["userName"].readIfPresent()
        value.awsAccountId = try reader["awsAccountId"].readIfPresent()
        return value
    }
}

extension CodeCatalystClientTypes.ProjectSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.ProjectSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.ProjectSummary()
        value.name = try reader["name"].readIfPresent() ?? ""
        value.displayName = try reader["displayName"].readIfPresent()
        value.description = try reader["description"].readIfPresent()
        return value
    }
}

extension CodeCatalystClientTypes.ListSourceRepositoriesItem {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.ListSourceRepositoriesItem {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.ListSourceRepositoriesItem()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent() ?? ""
        value.description = try reader["description"].readIfPresent()
        value.lastUpdatedTime = try reader["lastUpdatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.createdTime = try reader["createdTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension CodeCatalystClientTypes.ListSourceRepositoryBranchesItem {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.ListSourceRepositoryBranchesItem {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.ListSourceRepositoryBranchesItem()
        value.ref = try reader["ref"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.lastUpdatedTime = try reader["lastUpdatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.headCommitId = try reader["headCommitId"].readIfPresent()
        return value
    }
}

extension CodeCatalystClientTypes.SpaceSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.SpaceSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.SpaceSummary()
        value.name = try reader["name"].readIfPresent() ?? ""
        value.regionName = try reader["regionName"].readIfPresent() ?? ""
        value.displayName = try reader["displayName"].readIfPresent()
        value.description = try reader["description"].readIfPresent()
        return value
    }
}

extension CodeCatalystClientTypes.WorkflowRunSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.WorkflowRunSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.WorkflowRunSummary()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.workflowId = try reader["workflowId"].readIfPresent() ?? ""
        value.workflowName = try reader["workflowName"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        value.statusReasons = try reader["statusReasons"].readListIfPresent(memberReadingClosure: CodeCatalystClientTypes.WorkflowRunStatusReason.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.startTime = try reader["startTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.endTime = try reader["endTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.lastUpdatedTime = try reader["lastUpdatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension CodeCatalystClientTypes.WorkflowSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.WorkflowSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.WorkflowSummary()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent() ?? ""
        value.sourceRepositoryName = try reader["sourceRepositoryName"].readIfPresent() ?? ""
        value.sourceBranchName = try reader["sourceBranchName"].readIfPresent() ?? ""
        value.definition = try reader["definition"].readIfPresent(with: CodeCatalystClientTypes.WorkflowDefinitionSummary.read(from:))
        value.createdTime = try reader["createdTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.lastUpdatedTime = try reader["lastUpdatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.runMode = try reader["runMode"].readIfPresent() ?? .sdkUnknown("")
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension CodeCatalystClientTypes.WorkflowDefinitionSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.WorkflowDefinitionSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.WorkflowDefinitionSummary()
        value.path = try reader["path"].readIfPresent() ?? ""
        return value
    }
}

extension CodeCatalystClientTypes.DevEnvironmentAccessDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.DevEnvironmentAccessDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.DevEnvironmentAccessDetails()
        value.streamUrl = try reader["streamUrl"].readIfPresent() ?? ""
        value.tokenValue = try reader["tokenValue"].readIfPresent() ?? ""
        return value
    }
}

extension CodeCatalystClientTypes.IdeConfiguration {

    static func write(value: CodeCatalystClientTypes.IdeConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["name"].write(value.name)
        try writer["runtime"].write(value.runtime)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeCatalystClientTypes.IdeConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeCatalystClientTypes.IdeConfiguration()
        value.runtime = try reader["runtime"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        return value
    }
}

extension CodeCatalystClientTypes.RepositoryInput {

    static func write(value: CodeCatalystClientTypes.RepositoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["branchName"].write(value.branchName)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension CodeCatalystClientTypes.PersistentStorageConfiguration {

    static func write(value: CodeCatalystClientTypes.PersistentStorageConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sizeInGiB"].write(value.sizeInGiB)
    }
}

extension CodeCatalystClientTypes.Filter {

    static func write(value: CodeCatalystClientTypes.Filter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["comparisonOperator"].write(value.comparisonOperator)
        try writer["key"].write(value.key)
        try writer["values"].writeList(value.values, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CodeCatalystClientTypes.ProjectListFilter {

    static func write(value: CodeCatalystClientTypes.ProjectListFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["comparisonOperator"].write(value.comparisonOperator)
        try writer["key"].write(value.key)
        try writer["values"].writeList(value.values, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CodeCatalystClientTypes.WorkflowRunSortCriteria {

    static func write(value: CodeCatalystClientTypes.WorkflowRunSortCriteria?, to writer: SmithyJSON.Writer) throws {
        guard value != nil else { return }
        _ = writer[""]  // create an empty structure
    }
}

extension CodeCatalystClientTypes.WorkflowSortCriteria {

    static func write(value: CodeCatalystClientTypes.WorkflowSortCriteria?, to writer: SmithyJSON.Writer) throws {
        guard value != nil else { return }
        _ = writer[""]  // create an empty structure
    }
}

extension CodeCatalystClientTypes.DevEnvironmentSessionConfiguration {

    static func write(value: CodeCatalystClientTypes.DevEnvironmentSessionConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["executeCommandSessionConfiguration"].write(value.executeCommandSessionConfiguration, with: CodeCatalystClientTypes.ExecuteCommandSessionConfiguration.write(value:to:))
        try writer["sessionType"].write(value.sessionType)
    }
}

extension CodeCatalystClientTypes.ExecuteCommandSessionConfiguration {

    static func write(value: CodeCatalystClientTypes.ExecuteCommandSessionConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["arguments"].writeList(value.arguments, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["command"].write(value.command)
    }
}

public enum CodeCatalystClientTypes {}
