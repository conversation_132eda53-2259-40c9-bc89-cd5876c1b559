//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTP<PERSON>I.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
@_spi(SmithyReadWrite) import func SmithyReadWrite.listWritingClosure
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.AWSJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError

/// This exception is thrown when an error occurs in the DataSync service.
public struct InternalException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var errorCode: Swift.String? = nil
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

/// This exception is thrown when the client submits a malformed request.
public struct InvalidRequestException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var datasyncErrorCode: Swift.String? = nil
        public internal(set) var errorCode: Swift.String? = nil
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidRequestException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        datasyncErrorCode: Swift.String? = nil,
        errorCode: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.datasyncErrorCode = datasyncErrorCode
        self.properties.errorCode = errorCode
        self.properties.message = message
    }
}

extension DataSyncClientTypes {

    /// The credentials that provide DataSync Discovery read access to your on-premises storage system's management interface. DataSync Discovery stores these credentials in [Secrets Manager](https://docs.aws.amazon.com/secretsmanager/latest/userguide/intro.html). For more information, see [Accessing your on-premises storage system](https://docs.aws.amazon.com/datasync/latest/userguide/discovery-configure-storage.html).
    public struct Credentials: Swift.Sendable {
        /// Specifies the password for your storage system's management interface.
        /// This member is required.
        public var password: Swift.String?
        /// Specifies the user name for your storage system's management interface.
        /// This member is required.
        public var username: Swift.String?

        public init(
            password: Swift.String? = nil,
            username: Swift.String? = nil
        )
        {
            self.password = password
            self.username = username
        }
    }
}

extension DataSyncClientTypes.Credentials: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "Credentials(password: \"CONTENT_REDACTED\", username: \"CONTENT_REDACTED\")"}
}

extension DataSyncClientTypes {

    /// The network settings that DataSync Discovery uses to connect with your on-premises storage system's management interface.
    public struct DiscoveryServerConfiguration: Swift.Sendable {
        /// The domain name or IP address of your storage system's management interface.
        /// This member is required.
        public var serverHostname: Swift.String?
        /// The network port for accessing the storage system's management interface.
        public var serverPort: Swift.Int?

        public init(
            serverHostname: Swift.String? = nil,
            serverPort: Swift.Int? = nil
        )
        {
            self.serverHostname = serverHostname
            self.serverPort = serverPort
        }
    }
}

extension DataSyncClientTypes {

    public enum DiscoverySystemType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case netappontap
        case sdkUnknown(Swift.String)

        public static var allCases: [DiscoverySystemType] {
            return [
                .netappontap
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .netappontap: return "NetAppONTAP"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// A key-value pair representing a single tag that's been applied to an Amazon Web Services resource.
    public struct TagListEntry: Swift.Sendable {
        /// The key for an Amazon Web Services resource tag.
        /// This member is required.
        public var key: Swift.String?
        /// The value for an Amazon Web Services resource tag.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

public struct AddStorageSystemInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the DataSync agent that connects to and reads from your on-premises storage system's management interface. You can only specify one ARN.
    /// This member is required.
    public var agentArns: [Swift.String]?
    /// Specifies a client token to make sure requests with this API operation are idempotent. If you don't specify a client token, DataSync generates one for you automatically.
    /// This member is required.
    public var clientToken: Swift.String?
    /// Specifies the ARN of the Amazon CloudWatch log group for monitoring and logging discovery job events.
    public var cloudWatchLogGroupArn: Swift.String?
    /// Specifies the user name and password for accessing your on-premises storage system's management interface.
    /// This member is required.
    public var credentials: DataSyncClientTypes.Credentials?
    /// Specifies a familiar name for your on-premises storage system.
    public var name: Swift.String?
    /// Specifies the server name and network port required to connect with the management interface of your on-premises storage system.
    /// This member is required.
    public var serverConfiguration: DataSyncClientTypes.DiscoveryServerConfiguration?
    /// Specifies the type of on-premises storage system that you want DataSync Discovery to collect information about. DataSync Discovery currently supports NetApp Fabric-Attached Storage (FAS) and All Flash FAS (AFF) systems running ONTAP 9.7 or later.
    /// This member is required.
    public var systemType: DataSyncClientTypes.DiscoverySystemType?
    /// Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your on-premises storage system.
    public var tags: [DataSyncClientTypes.TagListEntry]?

    public init(
        agentArns: [Swift.String]? = nil,
        clientToken: Swift.String? = nil,
        cloudWatchLogGroupArn: Swift.String? = nil,
        credentials: DataSyncClientTypes.Credentials? = nil,
        name: Swift.String? = nil,
        serverConfiguration: DataSyncClientTypes.DiscoveryServerConfiguration? = nil,
        systemType: DataSyncClientTypes.DiscoverySystemType? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil
    )
    {
        self.agentArns = agentArns
        self.clientToken = clientToken
        self.cloudWatchLogGroupArn = cloudWatchLogGroupArn
        self.credentials = credentials
        self.name = name
        self.serverConfiguration = serverConfiguration
        self.systemType = systemType
        self.tags = tags
    }
}

public struct AddStorageSystemOutput: Swift.Sendable {
    /// The ARN of the on-premises storage system that you can use with DataSync Discovery.
    /// This member is required.
    public var storageSystemArn: Swift.String?

    public init(
        storageSystemArn: Swift.String? = nil
    )
    {
        self.storageSystemArn = storageSystemArn
    }
}

extension DataSyncClientTypes {

    /// The platform-related details about the DataSync agent, such as the version number.
    public struct Platform: Swift.Sendable {
        /// The version of the DataSync agent.
        public var version: Swift.String?

        public init(
            version: Swift.String? = nil
        )
        {
            self.version = version
        }
    }
}

extension DataSyncClientTypes {

    public enum AgentStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case offline
        case online
        case sdkUnknown(Swift.String)

        public static var allCases: [AgentStatus] {
            return [
                .offline,
                .online
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .offline: return "OFFLINE"
            case .online: return "ONLINE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// Represents a single entry in a list (or array) of DataSync agents when you call the [ListAgents](https://docs.aws.amazon.com/datasync/latest/userguide/API_ListAgents.html) operation.
    public struct AgentListEntry: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of a DataSync agent.
        public var agentArn: Swift.String?
        /// The name of an agent.
        public var name: Swift.String?
        /// The platform-related details about the agent, such as the version number.
        public var platform: DataSyncClientTypes.Platform?
        /// The status of an agent.
        ///
        /// * If the status is ONLINE, the agent is configured properly and ready to use.
        ///
        /// * If the status is OFFLINE, the agent has been out of contact with DataSync for five minutes or longer. This can happen for a few reasons. For more information, see [What do I do if my agent is offline?](https://docs.aws.amazon.com/datasync/latest/userguide/troubleshooting-datasync-agents.html#troubleshoot-agent-offline)
        public var status: DataSyncClientTypes.AgentStatus?

        public init(
            agentArn: Swift.String? = nil,
            name: Swift.String? = nil,
            platform: DataSyncClientTypes.Platform? = nil,
            status: DataSyncClientTypes.AgentStatus? = nil
        )
        {
            self.agentArn = agentArn
            self.name = name
            self.platform = platform
            self.status = status
        }
    }
}

extension DataSyncClientTypes {

    public enum Atime: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case bestEffort
        case `none`
        case sdkUnknown(Swift.String)

        public static var allCases: [Atime] {
            return [
                .bestEffort,
                .none
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .bestEffort: return "BEST_EFFORT"
            case .none: return "NONE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum AzureAccessTier: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case archive
        case cool
        case hot
        case sdkUnknown(Swift.String)

        public static var allCases: [AzureAccessTier] {
            return [
                .archive,
                .cool,
                .hot
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .archive: return "ARCHIVE"
            case .cool: return "COOL"
            case .hot: return "HOT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum AzureBlobAuthenticationType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case sas
        case sdkUnknown(Swift.String)

        public static var allCases: [AzureBlobAuthenticationType] {
            return [
                .sas
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .sas: return "SAS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// The shared access signature (SAS) configuration that allows DataSync to access your Microsoft Azure Blob Storage. For more information, see [SAS tokens](https://docs.aws.amazon.com/datasync/latest/userguide/creating-azure-blob-location.html#azure-blob-sas-tokens) for accessing your Azure Blob Storage.
    public struct AzureBlobSasConfiguration: Swift.Sendable {
        /// Specifies a SAS token that provides permissions to access your Azure Blob Storage. The token is part of the SAS URI string that comes after the storage resource URI and a question mark. A token looks something like this: sp=r&st=2023-12-20T14:54:52Z&se=2023-12-20T22:54:52Z&spr=https&sv=2021-06-08&sr=c&sig=aBBKDWQvyuVcTPH9EBp%2FXTI9E%2F%2Fmq171%2BZU178wcwqU%3D
        /// This member is required.
        public var token: Swift.String?

        public init(
            token: Swift.String? = nil
        )
        {
            self.token = token
        }
    }
}

extension DataSyncClientTypes.AzureBlobSasConfiguration: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "AzureBlobSasConfiguration(token: \"CONTENT_REDACTED\")"}
}

extension DataSyncClientTypes {

    public enum AzureBlobType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case block
        case sdkUnknown(Swift.String)

        public static var allCases: [AzureBlobType] {
            return [
                .block
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .block: return "BLOCK"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// CancelTaskExecutionRequest
public struct CancelTaskExecutionInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the task execution to stop.
    /// This member is required.
    public var taskExecutionArn: Swift.String?

    public init(
        taskExecutionArn: Swift.String? = nil
    )
    {
        self.taskExecutionArn = taskExecutionArn
    }
}

public struct CancelTaskExecutionOutput: Swift.Sendable {

    public init() { }
}

extension DataSyncClientTypes {

    /// The storage capacity of an on-premises storage system resource (for example, a volume).
    public struct Capacity: Swift.Sendable {
        /// The amount of space in the cluster that's in cloud storage (for example, if you're using data tiering).
        public var clusterCloudStorageUsed: Swift.Int?
        /// The amount of space that's being used in a storage system resource without accounting for compression or deduplication.
        public var logicalUsed: Swift.Int?
        /// The total amount of space available in a storage system resource.
        public var provisioned: Swift.Int?
        /// The amount of space that's being used in a storage system resource.
        public var used: Swift.Int?

        public init(
            clusterCloudStorageUsed: Swift.Int? = nil,
            logicalUsed: Swift.Int? = nil,
            provisioned: Swift.Int? = nil,
            used: Swift.Int? = nil
        )
        {
            self.clusterCloudStorageUsed = clusterCloudStorageUsed
            self.logicalUsed = logicalUsed
            self.provisioned = provisioned
            self.used = used
        }
    }
}

/// CreateAgentRequest
public struct CreateAgentInput: Swift.Sendable {
    /// Specifies your DataSync agent's activation key. If you don't have an activation key, see [Activating your agent](https://docs.aws.amazon.com/datasync/latest/userguide/activate-agent.html).
    /// This member is required.
    public var activationKey: Swift.String?
    /// Specifies a name for your agent. We recommend specifying a name that you can remember.
    public var agentName: Swift.String?
    /// Specifies the Amazon Resource Name (ARN) of the security group that allows traffic between your agent and VPC service endpoint. You can only specify one ARN.
    public var securityGroupArns: [Swift.String]?
    /// Specifies the ARN of the subnet where your VPC service endpoint is located. You can only specify one ARN.
    public var subnetArns: [Swift.String]?
    /// Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least one tag for your agent.
    public var tags: [DataSyncClientTypes.TagListEntry]?
    /// Specifies the ID of the [VPC service endpoint](https://docs.aws.amazon.com/datasync/latest/userguide/choose-service-endpoint.html#datasync-in-vpc) that you're using. For example, a VPC endpoint ID looks like vpce-01234d5aff67890e1. The VPC service endpoint you use must include the DataSync service name (for example, com.amazonaws.us-east-2.datasync).
    public var vpcEndpointId: Swift.String?

    public init(
        activationKey: Swift.String? = nil,
        agentName: Swift.String? = nil,
        securityGroupArns: [Swift.String]? = nil,
        subnetArns: [Swift.String]? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil,
        vpcEndpointId: Swift.String? = nil
    )
    {
        self.activationKey = activationKey
        self.agentName = agentName
        self.securityGroupArns = securityGroupArns
        self.subnetArns = subnetArns
        self.tags = tags
        self.vpcEndpointId = vpcEndpointId
    }
}

/// CreateAgentResponse
public struct CreateAgentOutput: Swift.Sendable {
    /// The ARN of the agent that you just activated. Use the [ListAgents](https://docs.aws.amazon.com/datasync/latest/userguide/API_ListAgents.html) operation to return a list of agents in your Amazon Web Services account and Amazon Web Services Region.
    public var agentArn: Swift.String?

    public init(
        agentArn: Swift.String? = nil
    )
    {
        self.agentArn = agentArn
    }
}

public struct CreateLocationAzureBlobInput: Swift.Sendable {
    /// Specifies the access tier that you want your objects or files transferred into. This only applies when using the location as a transfer destination. For more information, see [Access tiers](https://docs.aws.amazon.com/datasync/latest/userguide/creating-azure-blob-location.html#azure-blob-access-tiers).
    public var accessTier: DataSyncClientTypes.AzureAccessTier?
    /// Specifies the Amazon Resource Name (ARN) of the DataSync agent that can connect with your Azure Blob Storage container. You can specify more than one agent. For more information, see [Using multiple agents for your transfer](https://docs.aws.amazon.com/datasync/latest/userguide/multiple-agents.html).
    /// This member is required.
    public var agentArns: [Swift.String]?
    /// Specifies the authentication method DataSync uses to access your Azure Blob Storage. DataSync can access blob storage using a shared access signature (SAS).
    /// This member is required.
    public var authenticationType: DataSyncClientTypes.AzureBlobAuthenticationType?
    /// Specifies the type of blob that you want your objects or files to be when transferring them into Azure Blob Storage. Currently, DataSync only supports moving data into Azure Blob Storage as block blobs. For more information on blob types, see the [Azure Blob Storage documentation](https://learn.microsoft.com/en-us/rest/api/storageservices/understanding-block-blobs--append-blobs--and-page-blobs).
    public var blobType: DataSyncClientTypes.AzureBlobType?
    /// Specifies the URL of the Azure Blob Storage container involved in your transfer.
    /// This member is required.
    public var containerUrl: Swift.String?
    /// Specifies the SAS configuration that allows DataSync to access your Azure Blob Storage.
    public var sasConfiguration: DataSyncClientTypes.AzureBlobSasConfiguration?
    /// Specifies path segments if you want to limit your transfer to a virtual directory in your container (for example, /my/images).
    public var subdirectory: Swift.String?
    /// Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your transfer location.
    public var tags: [DataSyncClientTypes.TagListEntry]?

    public init(
        accessTier: DataSyncClientTypes.AzureAccessTier? = nil,
        agentArns: [Swift.String]? = nil,
        authenticationType: DataSyncClientTypes.AzureBlobAuthenticationType? = nil,
        blobType: DataSyncClientTypes.AzureBlobType? = nil,
        containerUrl: Swift.String? = nil,
        sasConfiguration: DataSyncClientTypes.AzureBlobSasConfiguration? = nil,
        subdirectory: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil
    )
    {
        self.accessTier = accessTier
        self.agentArns = agentArns
        self.authenticationType = authenticationType
        self.blobType = blobType
        self.containerUrl = containerUrl
        self.sasConfiguration = sasConfiguration
        self.subdirectory = subdirectory
        self.tags = tags
    }
}

public struct CreateLocationAzureBlobOutput: Swift.Sendable {
    /// The ARN of the Azure Blob Storage transfer location that you created.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

extension DataSyncClientTypes {

    /// The subnet and security groups that DataSync uses to connect to one of your Amazon EFS file system's [mount targets](https://docs.aws.amazon.com/efs/latest/ug/accessing-fs.html).
    public struct Ec2Config: Swift.Sendable {
        /// Specifies the Amazon Resource Names (ARNs) of the security groups associated with an Amazon EFS file system's mount target.
        /// This member is required.
        public var securityGroupArns: [Swift.String]?
        /// Specifies the ARN of a subnet where DataSync creates the [network interfaces](https://docs.aws.amazon.com/datasync/latest/userguide/datasync-network.html#required-network-interfaces) for managing traffic during your transfer. The subnet must be located:
        ///
        /// * In the same virtual private cloud (VPC) as the Amazon EFS file system.
        ///
        /// * In the same Availability Zone as at least one mount target for the Amazon EFS file system.
        ///
        ///
        /// You don't need to specify a subnet that includes a file system mount target.
        /// This member is required.
        public var subnetArn: Swift.String?

        public init(
            securityGroupArns: [Swift.String]? = nil,
            subnetArn: Swift.String? = nil
        )
        {
            self.securityGroupArns = securityGroupArns
            self.subnetArn = subnetArn
        }
    }
}

extension DataSyncClientTypes {

    public enum EfsInTransitEncryption: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case `none`
        case tls12
        case sdkUnknown(Swift.String)

        public static var allCases: [EfsInTransitEncryption] {
            return [
                .none,
                .tls12
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .none: return "NONE"
            case .tls12: return "TLS1_2"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// CreateLocationEfsRequest
public struct CreateLocationEfsInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the access point that DataSync uses to mount your Amazon EFS file system. For more information, see [Accessing restricted file systems](https://docs.aws.amazon.com/datasync/latest/userguide/create-efs-location.html#create-efs-location-iam).
    public var accessPointArn: Swift.String?
    /// Specifies the subnet and security groups DataSync uses to connect to one of your Amazon EFS file system's [mount targets](https://docs.aws.amazon.com/efs/latest/ug/accessing-fs.html).
    /// This member is required.
    public var ec2Config: DataSyncClientTypes.Ec2Config?
    /// Specifies the ARN for your Amazon EFS file system.
    /// This member is required.
    public var efsFilesystemArn: Swift.String?
    /// Specifies an Identity and Access Management (IAM) role that allows DataSync to access your Amazon EFS file system. For information on creating this role, see [Creating a DataSync IAM role for file system access](https://docs.aws.amazon.com/datasync/latest/userguide/create-efs-location.html#create-efs-location-iam-role).
    public var fileSystemAccessRoleArn: Swift.String?
    /// Specifies whether you want DataSync to use Transport Layer Security (TLS) 1.2 encryption when it transfers data to or from your Amazon EFS file system. If you specify an access point using AccessPointArn or an IAM role using FileSystemAccessRoleArn, you must set this parameter to TLS1_2.
    public var inTransitEncryption: DataSyncClientTypes.EfsInTransitEncryption?
    /// Specifies a mount path for your Amazon EFS file system. This is where DataSync reads or writes data on your file system (depending on if this is a source or destination location). By default, DataSync uses the root directory (or [access point](https://docs.aws.amazon.com/efs/latest/ug/efs-access-points.html) if you provide one by using AccessPointArn). You can also include subdirectories using forward slashes (for example, /path/to/folder).
    public var subdirectory: Swift.String?
    /// Specifies the key-value pair that represents a tag that you want to add to the resource. The value can be an empty string. This value helps you manage, filter, and search for your resources. We recommend that you create a name tag for your location.
    public var tags: [DataSyncClientTypes.TagListEntry]?

    public init(
        accessPointArn: Swift.String? = nil,
        ec2Config: DataSyncClientTypes.Ec2Config? = nil,
        efsFilesystemArn: Swift.String? = nil,
        fileSystemAccessRoleArn: Swift.String? = nil,
        inTransitEncryption: DataSyncClientTypes.EfsInTransitEncryption? = nil,
        subdirectory: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil
    )
    {
        self.accessPointArn = accessPointArn
        self.ec2Config = ec2Config
        self.efsFilesystemArn = efsFilesystemArn
        self.fileSystemAccessRoleArn = fileSystemAccessRoleArn
        self.inTransitEncryption = inTransitEncryption
        self.subdirectory = subdirectory
        self.tags = tags
    }
}

/// CreateLocationEfs
public struct CreateLocationEfsOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the Amazon EFS file system location that you create.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

public struct CreateLocationFsxLustreInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the FSx for Lustre file system.
    /// This member is required.
    public var fsxFilesystemArn: Swift.String?
    /// Specifies the Amazon Resource Names (ARNs) of up to five security groups that provide access to your FSx for Lustre file system. The security groups must be able to access the file system's ports. The file system must also allow access from the security groups. For information about file system access, see the [ Amazon FSx for Lustre User Guide ](https://docs.aws.amazon.com/fsx/latest/LustreGuide/limit-access-security-groups.html).
    /// This member is required.
    public var securityGroupArns: [Swift.String]?
    /// Specifies a mount path for your FSx for Lustre file system. The path can include subdirectories. When the location is used as a source, DataSync reads data from the mount path. When the location is used as a destination, DataSync writes data to the mount path. If you don't include this parameter, DataSync uses the file system's root directory (/).
    public var subdirectory: Swift.String?
    /// Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your location.
    public var tags: [DataSyncClientTypes.TagListEntry]?

    public init(
        fsxFilesystemArn: Swift.String? = nil,
        securityGroupArns: [Swift.String]? = nil,
        subdirectory: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil
    )
    {
        self.fsxFilesystemArn = fsxFilesystemArn
        self.securityGroupArns = securityGroupArns
        self.subdirectory = subdirectory
        self.tags = tags
    }
}

public struct CreateLocationFsxLustreOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the FSx for Lustre file system location that you created.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

extension DataSyncClientTypes {

    public enum NfsVersion: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case automatic
        case nfs3
        case nfs40
        case nfs41
        case sdkUnknown(Swift.String)

        public static var allCases: [NfsVersion] {
            return [
                .automatic,
                .nfs3,
                .nfs40,
                .nfs41
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .automatic: return "AUTOMATIC"
            case .nfs3: return "NFS3"
            case .nfs40: return "NFS4_0"
            case .nfs41: return "NFS4_1"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// Specifies how DataSync can access a location using the NFS protocol.
    public struct NfsMountOptions: Swift.Sendable {
        /// Specifies the NFS version that you want DataSync to use when mounting your NFS share. If the server refuses to use the version specified, the task fails. You can specify the following options:
        ///
        /// * AUTOMATIC (default): DataSync chooses NFS version 4.1.
        ///
        /// * NFS3: Stateless protocol version that allows for asynchronous writes on the server.
        ///
        /// * NFSv4_0: Stateful, firewall-friendly protocol version that supports delegations and pseudo file systems.
        ///
        /// * NFSv4_1: Stateful protocol version that supports sessions, directory delegations, and parallel data processing. NFS version 4.1 also includes all features available in version 4.0.
        ///
        ///
        /// DataSync currently only supports NFS version 3 with Amazon FSx for NetApp ONTAP locations.
        public var version: DataSyncClientTypes.NfsVersion?

        public init(
            version: DataSyncClientTypes.NfsVersion? = nil
        )
        {
            self.version = version
        }
    }
}

extension DataSyncClientTypes {

    /// Specifies the Network File System (NFS) protocol configuration that DataSync uses to access your FSx for OpenZFS file system or FSx for ONTAP file system's storage virtual machine (SVM).
    public struct FsxProtocolNfs: Swift.Sendable {
        /// Specifies how DataSync can access a location using the NFS protocol.
        public var mountOptions: DataSyncClientTypes.NfsMountOptions?

        public init(
            mountOptions: DataSyncClientTypes.NfsMountOptions? = nil
        )
        {
            self.mountOptions = mountOptions
        }
    }
}

extension DataSyncClientTypes {

    public enum SmbVersion: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case automatic
        case smb1
        case smb2
        case smb20
        case smb3
        case sdkUnknown(Swift.String)

        public static var allCases: [SmbVersion] {
            return [
                .automatic,
                .smb1,
                .smb2,
                .smb20,
                .smb3
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .automatic: return "AUTOMATIC"
            case .smb1: return "SMB1"
            case .smb2: return "SMB2"
            case .smb20: return "SMB2_0"
            case .smb3: return "SMB3"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// Specifies the version of the Server Message Block (SMB) protocol that DataSync uses to access an SMB file server.
    public struct SmbMountOptions: Swift.Sendable {
        /// By default, DataSync automatically chooses an SMB protocol version based on negotiation with your SMB file server. You also can configure DataSync to use a specific SMB version, but we recommend doing this only if DataSync has trouble negotiating with the SMB file server automatically. These are the following options for configuring the SMB version:
        ///
        /// * AUTOMATIC (default): DataSync and the SMB file server negotiate the highest version of SMB that they mutually support between 2.1 and 3.1.1. This is the recommended option. If you instead choose a specific version that your file server doesn't support, you may get an Operation Not Supported error.
        ///
        /// * SMB3: Restricts the protocol negotiation to only SMB version 3.0.2.
        ///
        /// * SMB2: Restricts the protocol negotiation to only SMB version 2.1.
        ///
        /// * SMB2_0: Restricts the protocol negotiation to only SMB version 2.0.
        ///
        /// * SMB1: Restricts the protocol negotiation to only SMB version 1.0. The SMB1 option isn't available when [creating an Amazon FSx for NetApp ONTAP location](https://docs.aws.amazon.com/datasync/latest/userguide/API_CreateLocationFsxOntap.html).
        public var version: DataSyncClientTypes.SmbVersion?

        public init(
            version: DataSyncClientTypes.SmbVersion? = nil
        )
        {
            self.version = version
        }
    }
}

extension DataSyncClientTypes {

    /// Specifies the Server Message Block (SMB) protocol configuration that DataSync uses to access your Amazon FSx for NetApp ONTAP file system's storage virtual machine (SVM). For more information, see [Providing DataSync access to FSx for ONTAP file systems](https://docs.aws.amazon.com/datasync/latest/userguide/create-ontap-location.html#create-ontap-location-access).
    public struct FsxProtocolSmb: Swift.Sendable {
        /// Specifies the name of the Windows domain that your storage virtual machine (SVM) belongs to. If you have multiple domains in your environment, configuring this setting makes sure that DataSync connects to the right SVM. If you have multiple Active Directory domains in your environment, configuring this parameter makes sure that DataSync connects to the right SVM.
        public var domain: Swift.String?
        /// Specifies the version of the Server Message Block (SMB) protocol that DataSync uses to access an SMB file server.
        public var mountOptions: DataSyncClientTypes.SmbMountOptions?
        /// Specifies the password of a user who has permission to access your SVM.
        /// This member is required.
        public var password: Swift.String?
        /// Specifies a user that can mount and access the files, folders, and metadata in your SVM. For information about choosing a user with the right level of access for your transfer, see [Using the SMB protocol](https://docs.aws.amazon.com/datasync/latest/userguide/create-ontap-location.html#create-ontap-location-smb).
        /// This member is required.
        public var user: Swift.String?

        public init(
            domain: Swift.String? = nil,
            mountOptions: DataSyncClientTypes.SmbMountOptions? = nil,
            password: Swift.String? = nil,
            user: Swift.String? = nil
        )
        {
            self.domain = domain
            self.mountOptions = mountOptions
            self.password = password
            self.user = user
        }
    }
}

extension DataSyncClientTypes.FsxProtocolSmb: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "FsxProtocolSmb(domain: \(Swift.String(describing: domain)), mountOptions: \(Swift.String(describing: mountOptions)), user: \(Swift.String(describing: user)), password: \"CONTENT_REDACTED\")"}
}

extension DataSyncClientTypes {

    /// Specifies the data transfer protocol that DataSync uses to access your Amazon FSx file system.
    public struct FsxProtocol: Swift.Sendable {
        /// Specifies the Network File System (NFS) protocol configuration that DataSync uses to access your FSx for OpenZFS file system or FSx for ONTAP file system's storage virtual machine (SVM).
        public var nfs: DataSyncClientTypes.FsxProtocolNfs?
        /// Specifies the Server Message Block (SMB) protocol configuration that DataSync uses to access your FSx for ONTAP file system's SVM.
        public var smb: DataSyncClientTypes.FsxProtocolSmb?

        public init(
            nfs: DataSyncClientTypes.FsxProtocolNfs? = nil,
            smb: DataSyncClientTypes.FsxProtocolSmb? = nil
        )
        {
            self.nfs = nfs
            self.smb = smb
        }
    }
}

public struct CreateLocationFsxOntapInput: Swift.Sendable {
    /// Specifies the data transfer protocol that DataSync uses to access your Amazon FSx file system.
    /// This member is required.
    public var `protocol`: DataSyncClientTypes.FsxProtocol?
    /// Specifies the Amazon EC2 security groups that provide access to your file system's preferred subnet. The security groups must allow outbound traffic on the following ports (depending on the protocol you use):
    ///
    /// * Network File System (NFS): TCP ports 111, 635, and 2049
    ///
    /// * Server Message Block (SMB): TCP port 445
    ///
    ///
    /// Your file system's security groups must also allow inbound traffic on the same ports.
    /// This member is required.
    public var securityGroupArns: [Swift.String]?
    /// Specifies the ARN of the storage virtual machine (SVM) in your file system where you want to copy data to or from.
    /// This member is required.
    public var storageVirtualMachineArn: Swift.String?
    /// Specifies a path to the file share in the SVM where you want to transfer data to or from. You can specify a junction path (also known as a mount point), qtree path (for NFS file shares), or share name (for SMB file shares). For example, your mount path might be /vol1, /vol1/tree1, or /share1. Don't specify a junction path in the SVM's root volume. For more information, see [Managing FSx for ONTAP storage virtual machines](https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/managing-svms.html) in the Amazon FSx for NetApp ONTAP User Guide.
    public var subdirectory: Swift.String?
    /// Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your location.
    public var tags: [DataSyncClientTypes.TagListEntry]?

    public init(
        `protocol`: DataSyncClientTypes.FsxProtocol? = nil,
        securityGroupArns: [Swift.String]? = nil,
        storageVirtualMachineArn: Swift.String? = nil,
        subdirectory: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil
    )
    {
        self.`protocol` = `protocol`
        self.securityGroupArns = securityGroupArns
        self.storageVirtualMachineArn = storageVirtualMachineArn
        self.subdirectory = subdirectory
        self.tags = tags
    }
}

public struct CreateLocationFsxOntapOutput: Swift.Sendable {
    /// Specifies the ARN of the FSx for ONTAP file system location that you create.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

public struct CreateLocationFsxOpenZfsInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the FSx for OpenZFS file system.
    /// This member is required.
    public var fsxFilesystemArn: Swift.String?
    /// The type of protocol that DataSync uses to access your file system.
    /// This member is required.
    public var `protocol`: DataSyncClientTypes.FsxProtocol?
    /// The ARNs of the security groups that are used to configure the FSx for OpenZFS file system.
    /// This member is required.
    public var securityGroupArns: [Swift.String]?
    /// A subdirectory in the location's path that must begin with /fsx. DataSync uses this subdirectory to read or write data (depending on whether the file system is a source or destination location).
    public var subdirectory: Swift.String?
    /// The key-value pair that represents a tag that you want to add to the resource. The value can be an empty string. This value helps you manage, filter, and search for your resources. We recommend that you create a name tag for your location.
    public var tags: [DataSyncClientTypes.TagListEntry]?

    public init(
        fsxFilesystemArn: Swift.String? = nil,
        `protocol`: DataSyncClientTypes.FsxProtocol? = nil,
        securityGroupArns: [Swift.String]? = nil,
        subdirectory: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil
    )
    {
        self.fsxFilesystemArn = fsxFilesystemArn
        self.`protocol` = `protocol`
        self.securityGroupArns = securityGroupArns
        self.subdirectory = subdirectory
        self.tags = tags
    }
}

public struct CreateLocationFsxOpenZfsOutput: Swift.Sendable {
    /// The ARN of the FSx for OpenZFS file system location that you created.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

public struct CreateLocationFsxWindowsInput: Swift.Sendable {
    /// Specifies the name of the Windows domain that the FSx for Windows File Server file system belongs to. If you have multiple Active Directory domains in your environment, configuring this parameter makes sure that DataSync connects to the right file system.
    public var domain: Swift.String?
    /// Specifies the Amazon Resource Name (ARN) for the FSx for Windows File Server file system.
    /// This member is required.
    public var fsxFilesystemArn: Swift.String?
    /// Specifies the password of the user with the permissions to mount and access the files, folders, and file metadata in your FSx for Windows File Server file system.
    /// This member is required.
    public var password: Swift.String?
    /// Specifies the ARNs of the Amazon EC2 security groups that provide access to your file system's preferred subnet. The security groups that you specify must be able to communicate with your file system's security groups. For information about configuring security groups for file system access, see the [ Amazon FSx for Windows File Server User Guide ](https://docs.aws.amazon.com/fsx/latest/WindowsGuide/limit-access-security-groups.html). If you choose a security group that doesn't allow connections from within itself, do one of the following:
    ///
    /// * Configure the security group to allow it to communicate within itself.
    ///
    /// * Choose a different security group that can communicate with the mount target's security group.
    /// This member is required.
    public var securityGroupArns: [Swift.String]?
    /// Specifies a mount path for your file system using forward slashes. This is where DataSync reads or writes data (depending on if this is a source or destination location).
    public var subdirectory: Swift.String?
    /// Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your location.
    public var tags: [DataSyncClientTypes.TagListEntry]?
    /// Specifies the user with the permissions to mount and access the files, folders, and file metadata in your FSx for Windows File Server file system. For information about choosing a user with the right level of access for your transfer, see [required permissions](https://docs.aws.amazon.com/datasync/latest/userguide/create-fsx-location.html#create-fsx-windows-location-permissions) for FSx for Windows File Server locations.
    /// This member is required.
    public var user: Swift.String?

    public init(
        domain: Swift.String? = nil,
        fsxFilesystemArn: Swift.String? = nil,
        password: Swift.String? = nil,
        securityGroupArns: [Swift.String]? = nil,
        subdirectory: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil,
        user: Swift.String? = nil
    )
    {
        self.domain = domain
        self.fsxFilesystemArn = fsxFilesystemArn
        self.password = password
        self.securityGroupArns = securityGroupArns
        self.subdirectory = subdirectory
        self.tags = tags
        self.user = user
    }
}

extension CreateLocationFsxWindowsInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateLocationFsxWindowsInput(domain: \(Swift.String(describing: domain)), fsxFilesystemArn: \(Swift.String(describing: fsxFilesystemArn)), securityGroupArns: \(Swift.String(describing: securityGroupArns)), subdirectory: \(Swift.String(describing: subdirectory)), tags: \(Swift.String(describing: tags)), user: \(Swift.String(describing: user)), password: \"CONTENT_REDACTED\")"}
}

public struct CreateLocationFsxWindowsOutput: Swift.Sendable {
    /// The ARN of the FSx for Windows File Server file system location you created.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

extension DataSyncClientTypes {

    public enum HdfsAuthenticationType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case kerberos
        case simple
        case sdkUnknown(Swift.String)

        public static var allCases: [HdfsAuthenticationType] {
            return [
                .kerberos,
                .simple
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .kerberos: return "KERBEROS"
            case .simple: return "SIMPLE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// The NameNode of the Hadoop Distributed File System (HDFS). The NameNode manages the file system's namespace. The NameNode performs operations such as opening, closing, and renaming files and directories. The NameNode contains the information to map blocks of data to the DataNodes.
    public struct HdfsNameNode: Swift.Sendable {
        /// The hostname of the NameNode in the HDFS cluster. This value is the IP address or Domain Name Service (DNS) name of the NameNode. An agent that's installed on-premises uses this hostname to communicate with the NameNode in the network.
        /// This member is required.
        public var hostname: Swift.String?
        /// The port that the NameNode uses to listen to client requests.
        /// This member is required.
        public var port: Swift.Int?

        public init(
            hostname: Swift.String? = nil,
            port: Swift.Int? = nil
        )
        {
            self.hostname = hostname
            self.port = port
        }
    }
}

extension DataSyncClientTypes {

    public enum HdfsDataTransferProtection: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case authentication
        case disabled
        case integrity
        case privacy
        case sdkUnknown(Swift.String)

        public static var allCases: [HdfsDataTransferProtection] {
            return [
                .authentication,
                .disabled,
                .integrity,
                .privacy
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .authentication: return "AUTHENTICATION"
            case .disabled: return "DISABLED"
            case .integrity: return "INTEGRITY"
            case .privacy: return "PRIVACY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum HdfsRpcProtection: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case authentication
        case disabled
        case integrity
        case privacy
        case sdkUnknown(Swift.String)

        public static var allCases: [HdfsRpcProtection] {
            return [
                .authentication,
                .disabled,
                .integrity,
                .privacy
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .authentication: return "AUTHENTICATION"
            case .disabled: return "DISABLED"
            case .integrity: return "INTEGRITY"
            case .privacy: return "PRIVACY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// The Quality of Protection (QOP) configuration specifies the Remote Procedure Call (RPC) and data transfer privacy settings configured on the Hadoop Distributed File System (HDFS) cluster.
    public struct QopConfiguration: Swift.Sendable {
        /// The data transfer protection setting configured on the HDFS cluster. This setting corresponds to your dfs.data.transfer.protection setting in the hdfs-site.xml file on your Hadoop cluster.
        public var dataTransferProtection: DataSyncClientTypes.HdfsDataTransferProtection?
        /// The RPC protection setting configured on the HDFS cluster. This setting corresponds to your hadoop.rpc.protection setting in your core-site.xml file on your Hadoop cluster.
        public var rpcProtection: DataSyncClientTypes.HdfsRpcProtection?

        public init(
            dataTransferProtection: DataSyncClientTypes.HdfsDataTransferProtection? = nil,
            rpcProtection: DataSyncClientTypes.HdfsRpcProtection? = nil
        )
        {
            self.dataTransferProtection = dataTransferProtection
            self.rpcProtection = rpcProtection
        }
    }
}

public struct CreateLocationHdfsInput: Swift.Sendable {
    /// The Amazon Resource Names (ARNs) of the DataSync agents that can connect to your HDFS cluster.
    /// This member is required.
    public var agentArns: [Swift.String]?
    /// The type of authentication used to determine the identity of the user.
    /// This member is required.
    public var authenticationType: DataSyncClientTypes.HdfsAuthenticationType?
    /// The size of data blocks to write into the HDFS cluster. The block size must be a multiple of 512 bytes. The default block size is 128 mebibytes (MiB).
    public var blockSize: Swift.Int?
    /// The Kerberos key table (keytab) that contains mappings between the defined Kerberos principal and the encrypted keys. You can load the keytab from a file by providing the file's address. If you're using the CLI, it performs base64 encoding for you. Otherwise, provide the base64-encoded text. If KERBEROS is specified for AuthenticationType, this parameter is required.
    public var kerberosKeytab: Foundation.Data?
    /// The krb5.conf file that contains the Kerberos configuration information. You can load the krb5.conf file by providing the file's address. If you're using the CLI, it performs the base64 encoding for you. Otherwise, provide the base64-encoded text. If KERBEROS is specified for AuthenticationType, this parameter is required.
    public var kerberosKrb5Conf: Foundation.Data?
    /// The Kerberos principal with access to the files and folders on the HDFS cluster. If KERBEROS is specified for AuthenticationType, this parameter is required.
    public var kerberosPrincipal: Swift.String?
    /// The URI of the HDFS cluster's Key Management Server (KMS).
    public var kmsKeyProviderUri: Swift.String?
    /// The NameNode that manages the HDFS namespace. The NameNode performs operations such as opening, closing, and renaming files and directories. The NameNode contains the information to map blocks of data to the DataNodes. You can use only one NameNode.
    /// This member is required.
    public var nameNodes: [DataSyncClientTypes.HdfsNameNode]?
    /// The Quality of Protection (QOP) configuration specifies the Remote Procedure Call (RPC) and data transfer protection settings configured on the Hadoop Distributed File System (HDFS) cluster. If QopConfiguration isn't specified, RpcProtection and DataTransferProtection default to PRIVACY. If you set RpcProtection or DataTransferProtection, the other parameter assumes the same value.
    public var qopConfiguration: DataSyncClientTypes.QopConfiguration?
    /// The number of DataNodes to replicate the data to when writing to the HDFS cluster. By default, data is replicated to three DataNodes.
    public var replicationFactor: Swift.Int?
    /// The user name used to identify the client on the host operating system. If SIMPLE is specified for AuthenticationType, this parameter is required.
    public var simpleUser: Swift.String?
    /// A subdirectory in the HDFS cluster. This subdirectory is used to read data from or write data to the HDFS cluster. If the subdirectory isn't specified, it will default to /.
    public var subdirectory: Swift.String?
    /// The key-value pair that represents the tag that you want to add to the location. The value can be an empty string. We recommend using tags to name your resources.
    public var tags: [DataSyncClientTypes.TagListEntry]?

    public init(
        agentArns: [Swift.String]? = nil,
        authenticationType: DataSyncClientTypes.HdfsAuthenticationType? = nil,
        blockSize: Swift.Int? = nil,
        kerberosKeytab: Foundation.Data? = nil,
        kerberosKrb5Conf: Foundation.Data? = nil,
        kerberosPrincipal: Swift.String? = nil,
        kmsKeyProviderUri: Swift.String? = nil,
        nameNodes: [DataSyncClientTypes.HdfsNameNode]? = nil,
        qopConfiguration: DataSyncClientTypes.QopConfiguration? = nil,
        replicationFactor: Swift.Int? = nil,
        simpleUser: Swift.String? = nil,
        subdirectory: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil
    )
    {
        self.agentArns = agentArns
        self.authenticationType = authenticationType
        self.blockSize = blockSize
        self.kerberosKeytab = kerberosKeytab
        self.kerberosKrb5Conf = kerberosKrb5Conf
        self.kerberosPrincipal = kerberosPrincipal
        self.kmsKeyProviderUri = kmsKeyProviderUri
        self.nameNodes = nameNodes
        self.qopConfiguration = qopConfiguration
        self.replicationFactor = replicationFactor
        self.simpleUser = simpleUser
        self.subdirectory = subdirectory
        self.tags = tags
    }
}

public struct CreateLocationHdfsOutput: Swift.Sendable {
    /// The ARN of the source HDFS cluster location that you create.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

extension DataSyncClientTypes {

    /// The DataSync agents that can connect to your Network File System (NFS) file server.
    public struct OnPremConfig: Swift.Sendable {
        /// The Amazon Resource Names (ARNs) of the DataSync agents that can connect to your NFS file server. You can specify more than one agent. For more information, see [Using multiple DataSync agents](https://docs.aws.amazon.com/datasync/latest/userguide/do-i-need-datasync-agent.html#multiple-agents).
        /// This member is required.
        public var agentArns: [Swift.String]?

        public init(
            agentArns: [Swift.String]? = nil
        )
        {
            self.agentArns = agentArns
        }
    }
}

/// CreateLocationNfsRequest
public struct CreateLocationNfsInput: Swift.Sendable {
    /// Specifies the options that DataSync can use to mount your NFS file server.
    public var mountOptions: DataSyncClientTypes.NfsMountOptions?
    /// Specifies the Amazon Resource Name (ARN) of the DataSync agent that can connect to your NFS file server. You can specify more than one agent. For more information, see [Using multiple DataSync agents](https://docs.aws.amazon.com/datasync/latest/userguide/do-i-need-datasync-agent.html#multiple-agents).
    /// This member is required.
    public var onPremConfig: DataSyncClientTypes.OnPremConfig?
    /// Specifies the Domain Name System (DNS) name or IP version 4 address of the NFS file server that your DataSync agent connects to.
    /// This member is required.
    public var serverHostname: Swift.String?
    /// Specifies the export path in your NFS file server that you want DataSync to mount. This path (or a subdirectory of the path) is where DataSync transfers data to or from. For information on configuring an export for DataSync, see [Accessing NFS file servers](https://docs.aws.amazon.com/datasync/latest/userguide/create-nfs-location.html#accessing-nfs).
    /// This member is required.
    public var subdirectory: Swift.String?
    /// Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your location.
    public var tags: [DataSyncClientTypes.TagListEntry]?

    public init(
        mountOptions: DataSyncClientTypes.NfsMountOptions? = nil,
        onPremConfig: DataSyncClientTypes.OnPremConfig? = nil,
        serverHostname: Swift.String? = nil,
        subdirectory: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil
    )
    {
        self.mountOptions = mountOptions
        self.onPremConfig = onPremConfig
        self.serverHostname = serverHostname
        self.subdirectory = subdirectory
        self.tags = tags
    }
}

/// CreateLocationNfsResponse
public struct CreateLocationNfsOutput: Swift.Sendable {
    /// The ARN of the transfer location that you created for your NFS file server.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

extension DataSyncClientTypes {

    public enum ObjectStorageServerProtocol: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case http
        case https
        case sdkUnknown(Swift.String)

        public static var allCases: [ObjectStorageServerProtocol] {
            return [
                .http,
                .https
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .http: return "HTTP"
            case .https: return "HTTPS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// CreateLocationObjectStorageRequest
public struct CreateLocationObjectStorageInput: Swift.Sendable {
    /// Specifies the access key (for example, a user name) if credentials are required to authenticate with the object storage server.
    public var accessKey: Swift.String?
    /// Specifies the Amazon Resource Names (ARNs) of the DataSync agents that can connect with your object storage system.
    /// This member is required.
    public var agentArns: [Swift.String]?
    /// Specifies the name of the object storage bucket involved in the transfer.
    /// This member is required.
    public var bucketName: Swift.String?
    /// Specifies the secret key (for example, a password) if credentials are required to authenticate with the object storage server.
    public var secretKey: Swift.String?
    /// Specifies a certificate chain for DataSync to authenticate with your object storage system if the system uses a private or self-signed certificate authority (CA). You must specify a single .pem file with a full certificate chain (for example, file:///home/<USER>/.ssh/object_storage_certificates.pem). The certificate chain might include:
    ///
    /// * The object storage system's certificate
    ///
    /// * All intermediate certificates (if there are any)
    ///
    /// * The root certificate of the signing CA
    ///
    ///
    /// You can concatenate your certificates into a .pem file (which can be up to 32768 bytes before base64 encoding). The following example cat command creates an object_storage_certificates.pem file that includes three certificates: cat object_server_certificate.pem intermediate_certificate.pem ca_root_certificate.pem > object_storage_certificates.pem To use this parameter, configure ServerProtocol to HTTPS.
    public var serverCertificate: Foundation.Data?
    /// Specifies the domain name or IP address of the object storage server. A DataSync agent uses this hostname to mount the object storage server in a network.
    /// This member is required.
    public var serverHostname: Swift.String?
    /// Specifies the port that your object storage server accepts inbound network traffic on (for example, port 443).
    public var serverPort: Swift.Int?
    /// Specifies the protocol that your object storage server uses to communicate.
    public var serverProtocol: DataSyncClientTypes.ObjectStorageServerProtocol?
    /// Specifies the object prefix for your object storage server. If this is a source location, DataSync only copies objects with this prefix. If this is a destination location, DataSync writes all objects with this prefix.
    public var subdirectory: Swift.String?
    /// Specifies the key-value pair that represents a tag that you want to add to the resource. Tags can help you manage, filter, and search for your resources. We recommend creating a name tag for your location.
    public var tags: [DataSyncClientTypes.TagListEntry]?

    public init(
        accessKey: Swift.String? = nil,
        agentArns: [Swift.String]? = nil,
        bucketName: Swift.String? = nil,
        secretKey: Swift.String? = nil,
        serverCertificate: Foundation.Data? = nil,
        serverHostname: Swift.String? = nil,
        serverPort: Swift.Int? = nil,
        serverProtocol: DataSyncClientTypes.ObjectStorageServerProtocol? = nil,
        subdirectory: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil
    )
    {
        self.accessKey = accessKey
        self.agentArns = agentArns
        self.bucketName = bucketName
        self.secretKey = secretKey
        self.serverCertificate = serverCertificate
        self.serverHostname = serverHostname
        self.serverPort = serverPort
        self.serverProtocol = serverProtocol
        self.subdirectory = subdirectory
        self.tags = tags
    }
}

extension CreateLocationObjectStorageInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateLocationObjectStorageInput(accessKey: \(Swift.String(describing: accessKey)), agentArns: \(Swift.String(describing: agentArns)), bucketName: \(Swift.String(describing: bucketName)), serverCertificate: \(Swift.String(describing: serverCertificate)), serverHostname: \(Swift.String(describing: serverHostname)), serverPort: \(Swift.String(describing: serverPort)), serverProtocol: \(Swift.String(describing: serverProtocol)), subdirectory: \(Swift.String(describing: subdirectory)), tags: \(Swift.String(describing: tags)), secretKey: \"CONTENT_REDACTED\")"}
}

/// CreateLocationObjectStorageResponse
public struct CreateLocationObjectStorageOutput: Swift.Sendable {
    /// Specifies the ARN of the object storage system location that you create.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

extension DataSyncClientTypes {

    /// Specifies the Amazon Resource Name (ARN) of the Identity and Access Management (IAM) role that DataSync uses to access your S3 bucket. For more information, see [Providing DataSync access to S3 buckets](https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#create-s3-location-access).
    public struct S3Config: Swift.Sendable {
        /// Specifies the ARN of the IAM role that DataSync uses to access your S3 bucket.
        /// This member is required.
        public var bucketAccessRoleArn: Swift.String?

        public init(
            bucketAccessRoleArn: Swift.String? = nil
        )
        {
            self.bucketAccessRoleArn = bucketAccessRoleArn
        }
    }
}

extension DataSyncClientTypes {

    public enum S3StorageClass: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case deepArchive
        case glacier
        case glacierInstantRetrieval
        case intelligentTiering
        case onezoneIa
        case outposts
        case standard
        case standardIa
        case sdkUnknown(Swift.String)

        public static var allCases: [S3StorageClass] {
            return [
                .deepArchive,
                .glacier,
                .glacierInstantRetrieval,
                .intelligentTiering,
                .onezoneIa,
                .outposts,
                .standard,
                .standardIa
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .deepArchive: return "DEEP_ARCHIVE"
            case .glacier: return "GLACIER"
            case .glacierInstantRetrieval: return "GLACIER_INSTANT_RETRIEVAL"
            case .intelligentTiering: return "INTELLIGENT_TIERING"
            case .onezoneIa: return "ONEZONE_IA"
            case .outposts: return "OUTPOSTS"
            case .standard: return "STANDARD"
            case .standardIa: return "STANDARD_IA"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// CreateLocationS3Request
public struct CreateLocationS3Input: Swift.Sendable {
    /// (Amazon S3 on Outposts only) Specifies the Amazon Resource Name (ARN) of the DataSync agent on your Outpost. For more information, see [Deploy your DataSync agent on Outposts](https://docs.aws.amazon.com/datasync/latest/userguide/deploy-agents.html#outposts-agent).
    public var agentArns: [Swift.String]?
    /// Specifies the ARN of the S3 bucket that you want to use as a location. (When creating your DataSync task later, you specify whether this location is a transfer source or destination.) If your S3 bucket is located on an Outposts resource, you must specify an Amazon S3 access point. For more information, see [Managing data access with Amazon S3 access points](https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-points.html) in the Amazon S3 User Guide.
    /// This member is required.
    public var s3BucketArn: Swift.String?
    /// Specifies the Amazon Resource Name (ARN) of the Identity and Access Management (IAM) role that DataSync uses to access your S3 bucket. For more information, see [Providing DataSync access to S3 buckets](https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#create-s3-location-access).
    /// This member is required.
    public var s3Config: DataSyncClientTypes.S3Config?
    /// Specifies the storage class that you want your objects to use when Amazon S3 is a transfer destination. For buckets in Amazon Web Services Regions, the storage class defaults to STANDARD. For buckets on Outposts, the storage class defaults to OUTPOSTS. For more information, see [Storage class considerations with Amazon S3 transfers](https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes).
    public var s3StorageClass: DataSyncClientTypes.S3StorageClass?
    /// Specifies a prefix in the S3 bucket that DataSync reads from or writes to (depending on whether the bucket is a source or destination location). DataSync can't transfer objects with a prefix that begins with a slash (/) or includes //, /./, or /../ patterns. For example:
    ///
    /// * /photos
    ///
    /// * photos//2006/January
    ///
    /// * photos/./2006/February
    ///
    /// * photos/../2006/March
    public var subdirectory: Swift.String?
    /// Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your transfer location.
    public var tags: [DataSyncClientTypes.TagListEntry]?

    public init(
        agentArns: [Swift.String]? = nil,
        s3BucketArn: Swift.String? = nil,
        s3Config: DataSyncClientTypes.S3Config? = nil,
        s3StorageClass: DataSyncClientTypes.S3StorageClass? = nil,
        subdirectory: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil
    )
    {
        self.agentArns = agentArns
        self.s3BucketArn = s3BucketArn
        self.s3Config = s3Config
        self.s3StorageClass = s3StorageClass
        self.subdirectory = subdirectory
        self.tags = tags
    }
}

/// CreateLocationS3Response
public struct CreateLocationS3Output: Swift.Sendable {
    /// The ARN of the S3 location that you created.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

/// CreateLocationSmbRequest
public struct CreateLocationSmbInput: Swift.Sendable {
    /// Specifies the DataSync agent (or agents) that can connect to your SMB file server. You specify an agent by using its Amazon Resource Name (ARN).
    /// This member is required.
    public var agentArns: [Swift.String]?
    /// Specifies the name of the Active Directory domain that your SMB file server belongs to. If you have multiple Active Directory domains in your environment, configuring this parameter makes sure that DataSync connects to the right file server.
    public var domain: Swift.String?
    /// Specifies the version of the SMB protocol that DataSync uses to access your SMB file server.
    public var mountOptions: DataSyncClientTypes.SmbMountOptions?
    /// Specifies the password of the user who can mount your SMB file server and has permission to access the files and folders involved in your transfer. For more information, see [required permissions](https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions) for SMB locations.
    /// This member is required.
    public var password: Swift.String?
    /// Specifies the Domain Name Service (DNS) name or IP address of the SMB file server that your DataSync agent will mount. You can't specify an IP version 6 (IPv6) address.
    /// This member is required.
    public var serverHostname: Swift.String?
    /// Specifies the name of the share exported by your SMB file server where DataSync will read or write data. You can include a subdirectory in the share path (for example, /path/to/subdirectory). Make sure that other SMB clients in your network can also mount this path. To copy all data in the subdirectory, DataSync must be able to mount the SMB share and access all of its data. For more information, see [required permissions](https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions) for SMB locations.
    /// This member is required.
    public var subdirectory: Swift.String?
    /// Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your location.
    public var tags: [DataSyncClientTypes.TagListEntry]?
    /// Specifies the user that can mount and access the files, folders, and file metadata in your SMB file server. For information about choosing a user with the right level of access for your transfer, see [required permissions](https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions) for SMB locations.
    /// This member is required.
    public var user: Swift.String?

    public init(
        agentArns: [Swift.String]? = nil,
        domain: Swift.String? = nil,
        mountOptions: DataSyncClientTypes.SmbMountOptions? = nil,
        password: Swift.String? = nil,
        serverHostname: Swift.String? = nil,
        subdirectory: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil,
        user: Swift.String? = nil
    )
    {
        self.agentArns = agentArns
        self.domain = domain
        self.mountOptions = mountOptions
        self.password = password
        self.serverHostname = serverHostname
        self.subdirectory = subdirectory
        self.tags = tags
        self.user = user
    }
}

extension CreateLocationSmbInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateLocationSmbInput(agentArns: \(Swift.String(describing: agentArns)), domain: \(Swift.String(describing: domain)), mountOptions: \(Swift.String(describing: mountOptions)), serverHostname: \(Swift.String(describing: serverHostname)), subdirectory: \(Swift.String(describing: subdirectory)), tags: \(Swift.String(describing: tags)), user: \(Swift.String(describing: user)), password: \"CONTENT_REDACTED\")"}
}

/// CreateLocationSmbResponse
public struct CreateLocationSmbOutput: Swift.Sendable {
    /// The ARN of the SMB location that you created.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

extension DataSyncClientTypes {

    public enum FilterType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case simplePattern
        case sdkUnknown(Swift.String)

        public static var allCases: [FilterType] {
            return [
                .simplePattern
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .simplePattern: return "SIMPLE_PATTERN"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// Specifies which files, folders, and objects to include or exclude when transferring files from source to destination.
    public struct FilterRule: Swift.Sendable {
        /// The type of filter rule to apply. DataSync only supports the SIMPLE_PATTERN rule type.
        public var filterType: DataSyncClientTypes.FilterType?
        /// A single filter string that consists of the patterns to include or exclude. The patterns are delimited by "|" (that is, a pipe), for example: /folder1|/folder2
        public var value: Swift.String?

        public init(
            filterType: DataSyncClientTypes.FilterType? = nil,
            value: Swift.String? = nil
        )
        {
            self.filterType = filterType
            self.value = value
        }
    }
}

extension DataSyncClientTypes {

    public enum ManifestAction: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case transfer
        case sdkUnknown(Swift.String)

        public static var allCases: [ManifestAction] {
            return [
                .transfer
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .transfer: return "TRANSFER"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum ManifestFormat: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case csv
        case sdkUnknown(Swift.String)

        public static var allCases: [ManifestFormat] {
            return [
                .csv
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .csv: return "CSV"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// Specifies the S3 bucket where you're hosting the manifest that you want DataSync to use. For more information and configuration examples, see [Specifying what DataSync transfers by using a manifest](https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html).
    public struct S3ManifestConfig: Swift.Sendable {
        /// Specifies the Identity and Access Management (IAM) role that allows DataSync to access your manifest. For more information, see [Providing DataSync access to your manifest](https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html#transferring-with-manifest-access).
        /// This member is required.
        public var bucketAccessRoleArn: Swift.String?
        /// Specifies the Amazon S3 object key of your manifest. This can include a prefix (for example, prefix/my-manifest.csv).
        /// This member is required.
        public var manifestObjectPath: Swift.String?
        /// Specifies the object version ID of the manifest that you want DataSync to use. If you don't set this, DataSync uses the latest version of the object.
        public var manifestObjectVersionId: Swift.String?
        /// Specifies the Amazon Resource Name (ARN) of the S3 bucket where you're hosting your manifest.
        /// This member is required.
        public var s3BucketArn: Swift.String?

        public init(
            bucketAccessRoleArn: Swift.String? = nil,
            manifestObjectPath: Swift.String? = nil,
            manifestObjectVersionId: Swift.String? = nil,
            s3BucketArn: Swift.String? = nil
        )
        {
            self.bucketAccessRoleArn = bucketAccessRoleArn
            self.manifestObjectPath = manifestObjectPath
            self.manifestObjectVersionId = manifestObjectVersionId
            self.s3BucketArn = s3BucketArn
        }
    }
}

extension DataSyncClientTypes {

    /// Specifies the manifest that you want DataSync to use and where it's hosted. For more information and configuration examples, see [Specifying what DataSync transfers by using a manifest](https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html).
    public struct SourceManifestConfig: Swift.Sendable {
        /// Specifies the S3 bucket where you're hosting your manifest.
        /// This member is required.
        public var s3: DataSyncClientTypes.S3ManifestConfig?

        public init(
            s3: DataSyncClientTypes.S3ManifestConfig? = nil
        )
        {
            self.s3 = s3
        }
    }
}

extension DataSyncClientTypes {

    /// Configures a manifest, which is a list of files or objects that you want DataSync to transfer. For more information and configuration examples, see [Specifying what DataSync transfers by using a manifest](https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html).
    public struct ManifestConfig: Swift.Sendable {
        /// Specifies what DataSync uses the manifest for.
        public var action: DataSyncClientTypes.ManifestAction?
        /// Specifies the file format of your manifest. For more information, see [Creating a manifest](https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html#transferring-with-manifest-create).
        public var format: DataSyncClientTypes.ManifestFormat?
        /// Specifies the manifest that you want DataSync to use and where it's hosted. You must specify this parameter if you're configuring a new manifest on or after February 7, 2024. If you don't, you'll get a 400 status code and ValidationException error stating that you're missing the IAM role for DataSync to access the S3 bucket where you're hosting your manifest. For more information, see [Providing DataSync access to your manifest](https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html#transferring-with-manifest-access).
        public var source: DataSyncClientTypes.SourceManifestConfig?

        public init(
            action: DataSyncClientTypes.ManifestAction? = nil,
            format: DataSyncClientTypes.ManifestFormat? = nil,
            source: DataSyncClientTypes.SourceManifestConfig? = nil
        )
        {
            self.action = action
            self.format = format
            self.source = source
        }
    }
}

extension DataSyncClientTypes {

    public enum Gid: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case both
        case intValue
        case name
        case `none`
        case sdkUnknown(Swift.String)

        public static var allCases: [Gid] {
            return [
                .both,
                .intValue,
                .name,
                .none
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .both: return "BOTH"
            case .intValue: return "INT_VALUE"
            case .name: return "NAME"
            case .none: return "NONE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum LogLevel: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case basic
        case off
        case transfer
        case sdkUnknown(Swift.String)

        public static var allCases: [LogLevel] {
            return [
                .basic,
                .off,
                .transfer
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .basic: return "BASIC"
            case .off: return "OFF"
            case .transfer: return "TRANSFER"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum Mtime: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case `none`
        case preserve
        case sdkUnknown(Swift.String)

        public static var allCases: [Mtime] {
            return [
                .none,
                .preserve
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .none: return "NONE"
            case .preserve: return "PRESERVE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum ObjectTags: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case `none`
        case preserve
        case sdkUnknown(Swift.String)

        public static var allCases: [ObjectTags] {
            return [
                .none,
                .preserve
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .none: return "NONE"
            case .preserve: return "PRESERVE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum OverwriteMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case always
        case never
        case sdkUnknown(Swift.String)

        public static var allCases: [OverwriteMode] {
            return [
                .always,
                .never
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .always: return "ALWAYS"
            case .never: return "NEVER"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum PosixPermissions: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case `none`
        case preserve
        case sdkUnknown(Swift.String)

        public static var allCases: [PosixPermissions] {
            return [
                .none,
                .preserve
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .none: return "NONE"
            case .preserve: return "PRESERVE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum PreserveDeletedFiles: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case preserve
        case remove
        case sdkUnknown(Swift.String)

        public static var allCases: [PreserveDeletedFiles] {
            return [
                .preserve,
                .remove
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .preserve: return "PRESERVE"
            case .remove: return "REMOVE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum PreserveDevices: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case `none`
        case preserve
        case sdkUnknown(Swift.String)

        public static var allCases: [PreserveDevices] {
            return [
                .none,
                .preserve
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .none: return "NONE"
            case .preserve: return "PRESERVE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum SmbSecurityDescriptorCopyFlags: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case `none`
        case ownerDacl
        case ownerDaclSacl
        case sdkUnknown(Swift.String)

        public static var allCases: [SmbSecurityDescriptorCopyFlags] {
            return [
                .none,
                .ownerDacl,
                .ownerDaclSacl
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .none: return "NONE"
            case .ownerDacl: return "OWNER_DACL"
            case .ownerDaclSacl: return "OWNER_DACL_SACL"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum TaskQueueing: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case enabled
        case sdkUnknown(Swift.String)

        public static var allCases: [TaskQueueing] {
            return [
                .disabled,
                .enabled
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "DISABLED"
            case .enabled: return "ENABLED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum TransferMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case all
        case changed
        case sdkUnknown(Swift.String)

        public static var allCases: [TransferMode] {
            return [
                .all,
                .changed
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .all: return "ALL"
            case .changed: return "CHANGED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum Uid: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case both
        case intValue
        case name
        case `none`
        case sdkUnknown(Swift.String)

        public static var allCases: [Uid] {
            return [
                .both,
                .intValue,
                .name,
                .none
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .both: return "BOTH"
            case .intValue: return "INT_VALUE"
            case .name: return "NAME"
            case .none: return "NONE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum VerifyMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case `none`
        case onlyFilesTransferred
        case pointInTimeConsistent
        case sdkUnknown(Swift.String)

        public static var allCases: [VerifyMode] {
            return [
                .none,
                .onlyFilesTransferred,
                .pointInTimeConsistent
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .none: return "NONE"
            case .onlyFilesTransferred: return "ONLY_FILES_TRANSFERRED"
            case .pointInTimeConsistent: return "POINT_IN_TIME_CONSISTENT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// Indicates how your transfer task is configured. These options include how DataSync handles files, objects, and their associated metadata during your transfer. You also can specify how to verify data integrity, set bandwidth limits for your task, among other options. Each option has a default value. Unless you need to, you don't have to configure any option before calling [StartTaskExecution](https://docs.aws.amazon.com/datasync/latest/userguide/API_StartTaskExecution.html). You also can override your task options for each task execution. For example, you might want to adjust the LogLevel for an individual execution.
    public struct Options: Swift.Sendable {
        /// Specifies whether to preserve metadata indicating the last time a file was read or written to. The behavior of Atime isn't fully standard across platforms, so DataSync can only do this on a best-effort basis.
        ///
        /// * BEST_EFFORT (default) - DataSync attempts to preserve the original Atime attribute on all source files (that is, the version before the PREPARING steps of the task execution). This option is recommended.
        ///
        /// * NONE - Ignores Atime.
        ///
        ///
        /// If Atime is set to BEST_EFFORT, Mtime must be set to PRESERVE. If Atime is set to NONE, Mtime must also be NONE.
        public var atime: DataSyncClientTypes.Atime?
        /// Limits the bandwidth used by a DataSync task. For example, if you want DataSync to use a maximum of 1 MB, set this value to 1048576 (=1024*1024). Not applicable to [Enhanced mode tasks](https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html).
        public var bytesPerSecond: Swift.Int?
        /// Specifies the POSIX group ID (GID) of the file's owners.
        ///
        /// * INT_VALUE (default) - Preserves the integer value of user ID (UID) and GID, which is recommended.
        ///
        /// * NONE - Ignores UID and GID.
        ///
        ///
        /// For more information, see [Understanding how DataSync handles file and object metadata](https://docs.aws.amazon.com/datasync/latest/userguide/metadata-copied.html).
        public var gid: DataSyncClientTypes.Gid?
        /// Specifies the type of logs that DataSync publishes to a Amazon CloudWatch Logs log group. To specify the log group, see [CloudWatchLogGroupArn](https://docs.aws.amazon.com/datasync/latest/userguide/API_CreateTask.html#DataSync-CreateTask-request-CloudWatchLogGroupArn).
        ///
        /// * BASIC - Publishes logs with only basic information (such as transfer errors).
        ///
        /// * TRANSFER - Publishes logs for all files or objects that your DataSync task transfers and performs data-integrity checks on.
        ///
        /// * OFF - No logs are published.
        public var logLevel: DataSyncClientTypes.LogLevel?
        /// Specifies whether to preserve metadata indicating the last time that a file was written to before the PREPARING step of your task execution. This option is required when you need to run the a task more than once.
        ///
        /// * PRESERVE (default) - Preserves original Mtime, which is recommended.
        ///
        /// * NONE - Ignores Mtime.
        ///
        ///
        /// If Mtime is set to PRESERVE, Atime must be set to BEST_EFFORT. If Mtime is set to NONE, Atime must also be set to NONE.
        public var mtime: DataSyncClientTypes.Mtime?
        /// Specifies whether you want DataSync to PRESERVE object tags (default behavior) when transferring between object storage systems. If you want your DataSync task to ignore object tags, specify the NONE value.
        public var objectTags: DataSyncClientTypes.ObjectTags?
        /// Specifies whether DataSync should modify or preserve data at the destination location.
        ///
        /// * ALWAYS (default) - DataSync modifies data in the destination location when source data (including metadata) has changed. If DataSync overwrites objects, you might incur additional charges for certain Amazon S3 storage classes (for example, for retrieval or early deletion). For more information, see [Storage class considerations with Amazon S3 transfers](https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes).
        ///
        /// * NEVER - DataSync doesn't overwrite data in the destination location even if the source data has changed. You can use this option to protect against overwriting changes made to files or objects in the destination.
        public var overwriteMode: DataSyncClientTypes.OverwriteMode?
        /// Specifies which users or groups can access a file for a specific purpose such as reading, writing, or execution of the file. For more information, see [Understanding how DataSync handles file and object metadata](https://docs.aws.amazon.com/datasync/latest/userguide/metadata-copied.html).
        ///
        /// * PRESERVE (default) - Preserves POSIX-style permissions, which is recommended.
        ///
        /// * NONE - Ignores POSIX-style permissions.
        ///
        ///
        /// DataSync can preserve extant permissions of a source location.
        public var posixPermissions: DataSyncClientTypes.PosixPermissions?
        /// Specifies whether files in the destination location that don't exist in the source should be preserved. This option can affect your Amazon S3 storage cost. If your task deletes objects, you might incur minimum storage duration charges for certain storage classes. For detailed information, see [Considerations when working with Amazon S3 storage classes in DataSync](https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes).
        ///
        /// * PRESERVE (default) - Ignores such destination files, which is recommended.
        ///
        /// * REMOVE - Deletes destination files that aren’t present in the source.
        ///
        ///
        /// If you set this parameter to REMOVE, you can't set TransferMode to ALL. When you transfer all data, DataSync doesn't scan your destination location and doesn't know what to delete.
        public var preserveDeletedFiles: DataSyncClientTypes.PreserveDeletedFiles?
        /// Specifies whether DataSync should preserve the metadata of block and character devices in the source location and recreate the files with that device name and metadata on the destination. DataSync copies only the name and metadata of such devices. DataSync can't copy the actual contents of these devices because they're nonterminal and don't return an end-of-file (EOF) marker.
        ///
        /// * NONE (default) - Ignores special devices (recommended).
        ///
        /// * PRESERVE - Preserves character and block device metadata. This option currently isn't supported for Amazon EFS.
        public var preserveDevices: DataSyncClientTypes.PreserveDevices?
        /// Specifies which components of the SMB security descriptor are copied from source to destination objects. This value is only used for transfers between SMB and Amazon FSx for Windows File Server locations or between two FSx for Windows File Server locations. For more information, see [Understanding how DataSync handles file and object metadata](https://docs.aws.amazon.com/datasync/latest/userguide/metadata-copied.html).
        ///
        /// * OWNER_DACL (default) - For each copied object, DataSync copies the following metadata:
        ///
        /// * The object owner.
        ///
        /// * NTFS discretionary access control lists (DACLs), which determine whether to grant access to an object. DataSync won't copy NTFS system access control lists (SACLs) with this option.
        ///
        ///
        ///
        ///
        /// * OWNER_DACL_SACL - For each copied object, DataSync copies the following metadata:
        ///
        /// * The object owner.
        ///
        /// * NTFS discretionary access control lists (DACLs), which determine whether to grant access to an object.
        ///
        /// * SACLs, which are used by administrators to log attempts to access a secured object. Copying SACLs requires granting additional permissions to the Windows user that DataSync uses to access your SMB location. For information about choosing a user with the right permissions, see required permissions for [SMB](https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions), [FSx for Windows File Server](https://docs.aws.amazon.com/datasync/latest/userguide/create-fsx-location.html#create-fsx-windows-location-permissions), or [FSx for ONTAP](https://docs.aws.amazon.com/datasync/latest/userguide/create-ontap-location.html#create-ontap-location-smb) (depending on the type of location in your transfer).
        ///
        ///
        ///
        ///
        /// * NONE - None of the SMB security descriptor components are copied. Destination objects are owned by the user that was provided for accessing the destination location. DACLs and SACLs are set based on the destination server’s configuration.
        public var securityDescriptorCopyFlags: DataSyncClientTypes.SmbSecurityDescriptorCopyFlags?
        /// Specifies whether your transfer tasks should be put into a queue during certain scenarios when [running multiple tasks](https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#running-multiple-tasks). This is ENABLED by default.
        public var taskQueueing: DataSyncClientTypes.TaskQueueing?
        /// Specifies whether DataSync transfers only the data (including metadata) that differs between locations following an initial copy or transfers all data every time you run the task. If you're planning on recurring transfers, you might only want to transfer what's changed since your previous task execution.
        ///
        /// * CHANGED (default) - After your initial full transfer, DataSync copies only the data and metadata that differs between the source and destination location.
        ///
        /// * ALL - DataSync copies everything in the source to the destination without comparing differences between the locations.
        public var transferMode: DataSyncClientTypes.TransferMode?
        /// Specifies the POSIX user ID (UID) of the file's owner.
        ///
        /// * INT_VALUE (default) - Preserves the integer value of UID and group ID (GID), which is recommended.
        ///
        /// * NONE - Ignores UID and GID.
        ///
        ///
        /// For more information, see [Metadata copied by DataSync](https://docs.aws.amazon.com/datasync/latest/userguide/special-files.html#metadata-copied).
        public var uid: DataSyncClientTypes.Uid?
        /// Specifies if and how DataSync checks the integrity of your data at the end of your transfer.
        ///
        /// * ONLY_FILES_TRANSFERRED (recommended) - DataSync calculates the checksum of transferred data (including metadata) at the source location. At the end of the transfer, DataSync then compares this checksum to the checksum calculated on that data at the destination. This is the default option for [Enhanced mode tasks](https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html). We recommend this option when transferring to S3 Glacier Flexible Retrieval or S3 Glacier Deep Archive storage classes. For more information, see [Storage class considerations with Amazon S3 locations](https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes).
        ///
        /// * POINT_IN_TIME_CONSISTENT - At the end of the transfer, DataSync checks the entire source and destination to verify that both locations are fully synchronized. The is the default option for [Basic mode tasks](https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html) and isn't currently supported with Enhanced mode tasks. If you use a [manifest](https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html), DataSync only scans and verifies what's listed in the manifest. You can't use this option when transferring to S3 Glacier Flexible Retrieval or S3 Glacier Deep Archive storage classes. For more information, see [Storage class considerations with Amazon S3 locations](https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes).
        ///
        /// * NONE - DataSync performs data integrity checks only during your transfer. Unlike other options, there's no additional verification at the end of your transfer.
        public var verifyMode: DataSyncClientTypes.VerifyMode?

        public init(
            atime: DataSyncClientTypes.Atime? = nil,
            bytesPerSecond: Swift.Int? = nil,
            gid: DataSyncClientTypes.Gid? = nil,
            logLevel: DataSyncClientTypes.LogLevel? = nil,
            mtime: DataSyncClientTypes.Mtime? = nil,
            objectTags: DataSyncClientTypes.ObjectTags? = nil,
            overwriteMode: DataSyncClientTypes.OverwriteMode? = nil,
            posixPermissions: DataSyncClientTypes.PosixPermissions? = nil,
            preserveDeletedFiles: DataSyncClientTypes.PreserveDeletedFiles? = nil,
            preserveDevices: DataSyncClientTypes.PreserveDevices? = nil,
            securityDescriptorCopyFlags: DataSyncClientTypes.SmbSecurityDescriptorCopyFlags? = nil,
            taskQueueing: DataSyncClientTypes.TaskQueueing? = nil,
            transferMode: DataSyncClientTypes.TransferMode? = nil,
            uid: DataSyncClientTypes.Uid? = nil,
            verifyMode: DataSyncClientTypes.VerifyMode? = nil
        )
        {
            self.atime = atime
            self.bytesPerSecond = bytesPerSecond
            self.gid = gid
            self.logLevel = logLevel
            self.mtime = mtime
            self.objectTags = objectTags
            self.overwriteMode = overwriteMode
            self.posixPermissions = posixPermissions
            self.preserveDeletedFiles = preserveDeletedFiles
            self.preserveDevices = preserveDevices
            self.securityDescriptorCopyFlags = securityDescriptorCopyFlags
            self.taskQueueing = taskQueueing
            self.transferMode = transferMode
            self.uid = uid
            self.verifyMode = verifyMode
        }
    }
}

extension DataSyncClientTypes {

    public enum ScheduleStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case enabled
        case sdkUnknown(Swift.String)

        public static var allCases: [ScheduleStatus] {
            return [
                .disabled,
                .enabled
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "DISABLED"
            case .enabled: return "ENABLED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// Configures your DataSync task to run on a [schedule](https://docs.aws.amazon.com/datasync/latest/userguide/task-scheduling.html) (at a minimum interval of 1 hour).
    public struct TaskSchedule: Swift.Sendable {
        /// Specifies your task schedule by using a cron or rate expression. Use cron expressions for task schedules that run on a specific time and day. For example, the following cron expression creates a task schedule that runs at 8 AM on the first Wednesday of every month: cron(0 8 * * 3#1) Use rate expressions for task schedules that run on a regular interval. For example, the following rate expression creates a task schedule that runs every 12 hours: rate(12 hours) For information about cron and rate expression syntax, see the [ Amazon EventBridge User Guide ](https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-scheduled-rule-pattern.html).
        /// This member is required.
        public var scheduleExpression: Swift.String?
        /// Specifies whether to enable or disable your task schedule. Your schedule is enabled by default, but there can be situations where you need to disable it. For example, you might need to pause a recurring transfer to fix an issue with your task or perform maintenance on your storage system. DataSync might disable your schedule automatically if your task fails repeatedly with the same error. For more information, see [TaskScheduleDetails](https://docs.aws.amazon.com/datasync/latest/userguide/API_TaskScheduleDetails.html).
        public var status: DataSyncClientTypes.ScheduleStatus?

        public init(
            scheduleExpression: Swift.String? = nil,
            status: DataSyncClientTypes.ScheduleStatus? = nil
        )
        {
            self.scheduleExpression = scheduleExpression
            self.status = status
        }
    }
}

extension DataSyncClientTypes {

    public enum TaskMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case basic
        case enhanced
        case sdkUnknown(Swift.String)

        public static var allCases: [TaskMode] {
            return [
                .basic,
                .enhanced
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .basic: return "BASIC"
            case .enhanced: return "ENHANCED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// Specifies the Amazon S3 bucket where DataSync uploads your [task report](https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html).
    public struct ReportDestinationS3: Swift.Sendable {
        /// Specifies the Amazon Resource Name (ARN) of the IAM policy that allows DataSync to upload a task report to your S3 bucket. For more information, see [Allowing DataSync to upload a task report to an Amazon S3 bucket](https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html).
        /// This member is required.
        public var bucketAccessRoleArn: Swift.String?
        /// Specifies the ARN of the S3 bucket where DataSync uploads your report.
        /// This member is required.
        public var s3BucketArn: Swift.String?
        /// Specifies a bucket prefix for your report.
        public var subdirectory: Swift.String?

        public init(
            bucketAccessRoleArn: Swift.String? = nil,
            s3BucketArn: Swift.String? = nil,
            subdirectory: Swift.String? = nil
        )
        {
            self.bucketAccessRoleArn = bucketAccessRoleArn
            self.s3BucketArn = s3BucketArn
            self.subdirectory = subdirectory
        }
    }
}

extension DataSyncClientTypes {

    /// Specifies where DataSync uploads your [task report](https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html).
    public struct ReportDestination: Swift.Sendable {
        /// Specifies the Amazon S3 bucket where DataSync uploads your task report.
        public var s3: DataSyncClientTypes.ReportDestinationS3?

        public init(
            s3: DataSyncClientTypes.ReportDestinationS3? = nil
        )
        {
            self.s3 = s3
        }
    }
}

extension DataSyncClientTypes {

    public enum ObjectVersionIds: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case include
        case `none`
        case sdkUnknown(Swift.String)

        public static var allCases: [ObjectVersionIds] {
            return [
                .include,
                .none
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .include: return "INCLUDE"
            case .none: return "NONE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum ReportOutputType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case standard
        case summaryOnly
        case sdkUnknown(Swift.String)

        public static var allCases: [ReportOutputType] {
            return [
                .standard,
                .summaryOnly
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .standard: return "STANDARD"
            case .summaryOnly: return "SUMMARY_ONLY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum ReportLevel: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case errorsOnly
        case successesAndErrors
        case sdkUnknown(Swift.String)

        public static var allCases: [ReportLevel] {
            return [
                .errorsOnly,
                .successesAndErrors
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .errorsOnly: return "ERRORS_ONLY"
            case .successesAndErrors: return "SUCCESSES_AND_ERRORS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// Specifies the level of detail for a particular aspect of your DataSync [task report](https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html).
    public struct ReportOverride: Swift.Sendable {
        /// Specifies whether your task report includes errors only or successes and errors. For example, your report might mostly include only what didn't go well in your transfer (ERRORS_ONLY). At the same time, you want to verify that your [task filter](https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html) is working correctly. In this situation, you can get a list of what files DataSync successfully skipped and if something transferred that you didn't to transfer (SUCCESSES_AND_ERRORS).
        public var reportLevel: DataSyncClientTypes.ReportLevel?

        public init(
            reportLevel: DataSyncClientTypes.ReportLevel? = nil
        )
        {
            self.reportLevel = reportLevel
        }
    }
}

extension DataSyncClientTypes {

    /// The level of detail included in each aspect of your DataSync [task report](https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html).
    public struct ReportOverrides: Swift.Sendable {
        /// Specifies the level of reporting for the files, objects, and directories that DataSync attempted to delete in your destination location. This only applies if you [configure your task](https://docs.aws.amazon.com/datasync/latest/userguide/configure-metadata.html) to delete data in the destination that isn't in the source.
        public var deleted: DataSyncClientTypes.ReportOverride?
        /// Specifies the level of reporting for the files, objects, and directories that DataSync attempted to skip during your transfer.
        public var skipped: DataSyncClientTypes.ReportOverride?
        /// Specifies the level of reporting for the files, objects, and directories that DataSync attempted to transfer.
        public var transferred: DataSyncClientTypes.ReportOverride?
        /// Specifies the level of reporting for the files, objects, and directories that DataSync attempted to verify at the end of your transfer.
        public var verified: DataSyncClientTypes.ReportOverride?

        public init(
            deleted: DataSyncClientTypes.ReportOverride? = nil,
            skipped: DataSyncClientTypes.ReportOverride? = nil,
            transferred: DataSyncClientTypes.ReportOverride? = nil,
            verified: DataSyncClientTypes.ReportOverride? = nil
        )
        {
            self.deleted = deleted
            self.skipped = skipped
            self.transferred = transferred
            self.verified = verified
        }
    }
}

extension DataSyncClientTypes {

    /// Specifies how you want to configure a task report, which provides detailed information about for your DataSync transfer. For more information, see [Task reports](https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html).
    public struct TaskReportConfig: Swift.Sendable {
        /// Specifies the Amazon S3 bucket where DataSync uploads your task report. For more information, see [Task reports](https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html#task-report-access).
        public var destination: DataSyncClientTypes.ReportDestination?
        /// Specifies whether your task report includes the new version of each object transferred into an S3 bucket. This only applies if you [enable versioning on your bucket](https://docs.aws.amazon.com/AmazonS3/latest/userguide/manage-versioning-examples.html). Keep in mind that setting this to INCLUDE can increase the duration of your task execution.
        public var objectVersionIds: DataSyncClientTypes.ObjectVersionIds?
        /// Specifies the type of task report that you want:
        ///
        /// * SUMMARY_ONLY: Provides necessary details about your task, including the number of files, objects, and directories transferred and transfer duration.
        ///
        /// * STANDARD: Provides complete details about your task, including a full list of files, objects, and directories that were transferred, skipped, verified, and more.
        public var outputType: DataSyncClientTypes.ReportOutputType?
        /// Customizes the reporting level for aspects of your task report. For example, your report might generally only include errors, but you could specify that you want a list of successes and errors just for the files that DataSync attempted to delete in your destination location.
        public var overrides: DataSyncClientTypes.ReportOverrides?
        /// Specifies whether you want your task report to include only what went wrong with your transfer or a list of what succeeded and didn't.
        ///
        /// * ERRORS_ONLY: A report shows what DataSync was unable to transfer, skip, verify, and delete.
        ///
        /// * SUCCESSES_AND_ERRORS: A report shows what DataSync was able and unable to transfer, skip, verify, and delete.
        public var reportLevel: DataSyncClientTypes.ReportLevel?

        public init(
            destination: DataSyncClientTypes.ReportDestination? = nil,
            objectVersionIds: DataSyncClientTypes.ObjectVersionIds? = nil,
            outputType: DataSyncClientTypes.ReportOutputType? = nil,
            overrides: DataSyncClientTypes.ReportOverrides? = nil,
            reportLevel: DataSyncClientTypes.ReportLevel? = nil
        )
        {
            self.destination = destination
            self.objectVersionIds = objectVersionIds
            self.outputType = outputType
            self.overrides = overrides
            self.reportLevel = reportLevel
        }
    }
}

/// CreateTaskRequest
public struct CreateTaskInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of an Amazon CloudWatch log group for monitoring your task. For Enhanced mode tasks, you don't need to specify anything. DataSync automatically sends logs to a CloudWatch log group named /aws/datasync.
    public var cloudWatchLogGroupArn: Swift.String?
    /// Specifies the ARN of your transfer's destination location.
    /// This member is required.
    public var destinationLocationArn: Swift.String?
    /// Specifies exclude filters that define the files, objects, and folders in your source location that you don't want DataSync to transfer. For more information and examples, see [Specifying what DataSync transfers by using filters](https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html).
    public var excludes: [DataSyncClientTypes.FilterRule]?
    /// Specifies include filters that define the files, objects, and folders in your source location that you want DataSync to transfer. For more information and examples, see [Specifying what DataSync transfers by using filters](https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html).
    public var includes: [DataSyncClientTypes.FilterRule]?
    /// Configures a manifest, which is a list of files or objects that you want DataSync to transfer. For more information and configuration examples, see [Specifying what DataSync transfers by using a manifest](https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html). When using this parameter, your caller identity (the role that you're using DataSync with) must have the iam:PassRole permission. The [AWSDataSyncFullAccess](https://docs.aws.amazon.com/datasync/latest/userguide/security-iam-awsmanpol.html#security-iam-awsmanpol-awsdatasyncfullaccess) policy includes this permission.
    public var manifestConfig: DataSyncClientTypes.ManifestConfig?
    /// Specifies the name of your task.
    public var name: Swift.String?
    /// Specifies your task's settings, such as preserving file metadata, verifying data integrity, among other options.
    public var options: DataSyncClientTypes.Options?
    /// Specifies a schedule for when you want your task to run. For more information, see [Scheduling your task](https://docs.aws.amazon.com/datasync/latest/userguide/task-scheduling.html).
    public var schedule: DataSyncClientTypes.TaskSchedule?
    /// Specifies the ARN of your transfer's source location.
    /// This member is required.
    public var sourceLocationArn: Swift.String?
    /// Specifies the tags that you want to apply to your task. Tags are key-value pairs that help you manage, filter, and search for your DataSync resources.
    public var tags: [DataSyncClientTypes.TagListEntry]?
    /// Specifies one of the following task modes for your data transfer:
    ///
    /// * ENHANCED - Transfer virtually unlimited numbers of objects with higher performance than Basic mode. Enhanced mode tasks optimize the data transfer process by listing, preparing, transferring, and verifying data in parallel. Enhanced mode is currently available for transfers between Amazon S3 locations. To create an Enhanced mode task, the IAM role that you use to call the CreateTask operation must have the iam:CreateServiceLinkedRole permission.
    ///
    /// * BASIC (default) - Transfer files or objects between Amazon Web Services storage and all other supported DataSync locations. Basic mode tasks are subject to [quotas](https://docs.aws.amazon.com/datasync/latest/userguide/datasync-limits.html) on the number of files, objects, and directories in a dataset. Basic mode sequentially prepares, transfers, and verifies data, making it slower than Enhanced mode for most workloads.
    ///
    ///
    /// For more information, see [Understanding task mode differences](https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html#task-mode-differences).
    public var taskMode: DataSyncClientTypes.TaskMode?
    /// Specifies how you want to configure a task report, which provides detailed information about your DataSync transfer. For more information, see [Monitoring your DataSync transfers with task reports](https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html). When using this parameter, your caller identity (the role that you're using DataSync with) must have the iam:PassRole permission. The [AWSDataSyncFullAccess](https://docs.aws.amazon.com/datasync/latest/userguide/security-iam-awsmanpol.html#security-iam-awsmanpol-awsdatasyncfullaccess) policy includes this permission.
    public var taskReportConfig: DataSyncClientTypes.TaskReportConfig?

    public init(
        cloudWatchLogGroupArn: Swift.String? = nil,
        destinationLocationArn: Swift.String? = nil,
        excludes: [DataSyncClientTypes.FilterRule]? = nil,
        includes: [DataSyncClientTypes.FilterRule]? = nil,
        manifestConfig: DataSyncClientTypes.ManifestConfig? = nil,
        name: Swift.String? = nil,
        options: DataSyncClientTypes.Options? = nil,
        schedule: DataSyncClientTypes.TaskSchedule? = nil,
        sourceLocationArn: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil,
        taskMode: DataSyncClientTypes.TaskMode? = nil,
        taskReportConfig: DataSyncClientTypes.TaskReportConfig? = nil
    )
    {
        self.cloudWatchLogGroupArn = cloudWatchLogGroupArn
        self.destinationLocationArn = destinationLocationArn
        self.excludes = excludes
        self.includes = includes
        self.manifestConfig = manifestConfig
        self.name = name
        self.options = options
        self.schedule = schedule
        self.sourceLocationArn = sourceLocationArn
        self.tags = tags
        self.taskMode = taskMode
        self.taskReportConfig = taskReportConfig
    }
}

/// CreateTaskResponse
public struct CreateTaskOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the task.
    public var taskArn: Swift.String?

    public init(
        taskArn: Swift.String? = nil
    )
    {
        self.taskArn = taskArn
    }
}

/// DeleteAgentRequest
public struct DeleteAgentInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the agent to delete. Use the ListAgents operation to return a list of agents for your account and Amazon Web Services Region.
    /// This member is required.
    public var agentArn: Swift.String?

    public init(
        agentArn: Swift.String? = nil
    )
    {
        self.agentArn = agentArn
    }
}

public struct DeleteAgentOutput: Swift.Sendable {

    public init() { }
}

/// DeleteLocation
public struct DeleteLocationInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the location to delete.
    /// This member is required.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

public struct DeleteLocationOutput: Swift.Sendable {

    public init() { }
}

/// DeleteTask
public struct DeleteTaskInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the task that you want to delete.
    /// This member is required.
    public var taskArn: Swift.String?

    public init(
        taskArn: Swift.String? = nil
    )
    {
        self.taskArn = taskArn
    }
}

public struct DeleteTaskOutput: Swift.Sendable {

    public init() { }
}

/// DescribeAgent
public struct DescribeAgentInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the DataSync agent that you want information about.
    /// This member is required.
    public var agentArn: Swift.String?

    public init(
        agentArn: Swift.String? = nil
    )
    {
        self.agentArn = agentArn
    }
}

extension DataSyncClientTypes {

    public enum EndpointType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case fips
        case privateLink
        case `public`
        case sdkUnknown(Swift.String)

        public static var allCases: [EndpointType] {
            return [
                .fips,
                .privateLink,
                .public
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .fips: return "FIPS"
            case .privateLink: return "PRIVATE_LINK"
            case .public: return "PUBLIC"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// Specifies how your DataSync agent connects to Amazon Web Services using a [virtual private cloud (VPC) service endpoint](https://docs.aws.amazon.com/datasync/latest/userguide/choose-service-endpoint.html#choose-service-endpoint-vpc). An agent that uses a VPC endpoint isn't accessible over the public internet.
    public struct PrivateLinkConfig: Swift.Sendable {
        /// Specifies the VPC endpoint provided by [Amazon Web Services PrivateLink](https://docs.aws.amazon.com/vpc/latest/privatelink/privatelink-share-your-services.html) that your agent connects to.
        public var privateLinkEndpoint: Swift.String?
        /// Specifies the Amazon Resource Names (ARN) of the security group that provides DataSync access to your VPC endpoint. You can only specify one ARN.
        public var securityGroupArns: [Swift.String]?
        /// Specifies the ARN of the subnet where your VPC endpoint is located. You can only specify one ARN.
        public var subnetArns: [Swift.String]?
        /// Specifies the ID of the VPC endpoint that your agent connects to.
        public var vpcEndpointId: Swift.String?

        public init(
            privateLinkEndpoint: Swift.String? = nil,
            securityGroupArns: [Swift.String]? = nil,
            subnetArns: [Swift.String]? = nil,
            vpcEndpointId: Swift.String? = nil
        )
        {
            self.privateLinkEndpoint = privateLinkEndpoint
            self.securityGroupArns = securityGroupArns
            self.subnetArns = subnetArns
            self.vpcEndpointId = vpcEndpointId
        }
    }
}

/// DescribeAgentResponse
public struct DescribeAgentOutput: Swift.Sendable {
    /// The ARN of the agent.
    public var agentArn: Swift.String?
    /// The time that the agent was [activated](https://docs.aws.amazon.com/datasync/latest/userguide/activate-agent.html).
    public var creationTime: Foundation.Date?
    /// The type of [service endpoint](https://docs.aws.amazon.com/datasync/latest/userguide/choose-service-endpoint.html) that your agent is connected to.
    public var endpointType: DataSyncClientTypes.EndpointType?
    /// The last time that the agent was communicating with the DataSync service.
    public var lastConnectionTime: Foundation.Date?
    /// The name of the agent.
    public var name: Swift.String?
    /// The platform-related details about the agent, such as the version number.
    public var platform: DataSyncClientTypes.Platform?
    /// The network configuration that the agent uses when connecting to a [VPC service endpoint](https://docs.aws.amazon.com/datasync/latest/userguide/choose-service-endpoint.html#choose-service-endpoint-vpc).
    public var privateLinkConfig: DataSyncClientTypes.PrivateLinkConfig?
    /// The status of the agent.
    ///
    /// * If the status is ONLINE, the agent is configured properly and ready to use.
    ///
    /// * If the status is OFFLINE, the agent has been out of contact with DataSync for five minutes or longer. This can happen for a few reasons. For more information, see [What do I do if my agent is offline?](https://docs.aws.amazon.com/datasync/latest/userguide/troubleshooting-datasync-agents.html#troubleshoot-agent-offline)
    public var status: DataSyncClientTypes.AgentStatus?

    public init(
        agentArn: Swift.String? = nil,
        creationTime: Foundation.Date? = nil,
        endpointType: DataSyncClientTypes.EndpointType? = nil,
        lastConnectionTime: Foundation.Date? = nil,
        name: Swift.String? = nil,
        platform: DataSyncClientTypes.Platform? = nil,
        privateLinkConfig: DataSyncClientTypes.PrivateLinkConfig? = nil,
        status: DataSyncClientTypes.AgentStatus? = nil
    )
    {
        self.agentArn = agentArn
        self.creationTime = creationTime
        self.endpointType = endpointType
        self.lastConnectionTime = lastConnectionTime
        self.name = name
        self.platform = platform
        self.privateLinkConfig = privateLinkConfig
        self.status = status
    }
}

public struct DescribeDiscoveryJobInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the discovery job that you want information about.
    /// This member is required.
    public var discoveryJobArn: Swift.String?

    public init(
        discoveryJobArn: Swift.String? = nil
    )
    {
        self.discoveryJobArn = discoveryJobArn
    }
}

extension DataSyncClientTypes {

    public enum DiscoveryJobStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case completed
        case completedWithIssues
        case failed
        case running
        case stopped
        case terminated
        case warning
        case sdkUnknown(Swift.String)

        public static var allCases: [DiscoveryJobStatus] {
            return [
                .completed,
                .completedWithIssues,
                .failed,
                .running,
                .stopped,
                .terminated,
                .warning
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .completed: return "COMPLETED"
            case .completedWithIssues: return "COMPLETED_WITH_ISSUES"
            case .failed: return "FAILED"
            case .running: return "RUNNING"
            case .stopped: return "STOPPED"
            case .terminated: return "TERMINATED"
            case .warning: return "WARNING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeDiscoveryJobOutput: Swift.Sendable {
    /// The number of minutes that the discovery job runs.
    public var collectionDurationMinutes: Swift.Int?
    /// The ARN of the discovery job.
    public var discoveryJobArn: Swift.String?
    /// The time when the discovery job ended.
    public var jobEndTime: Foundation.Date?
    /// The time when the discovery job started.
    public var jobStartTime: Foundation.Date?
    /// Indicates the status of a discovery job. For more information, see [Discovery job statuses](https://docs.aws.amazon.com/datasync/latest/userguide/discovery-job-statuses.html#discovery-job-statuses-table).
    public var status: DataSyncClientTypes.DiscoveryJobStatus?
    /// The ARN of the on-premises storage system you're running the discovery job on.
    public var storageSystemArn: Swift.String?

    public init(
        collectionDurationMinutes: Swift.Int? = nil,
        discoveryJobArn: Swift.String? = nil,
        jobEndTime: Foundation.Date? = nil,
        jobStartTime: Foundation.Date? = nil,
        status: DataSyncClientTypes.DiscoveryJobStatus? = nil,
        storageSystemArn: Swift.String? = nil
    )
    {
        self.collectionDurationMinutes = collectionDurationMinutes
        self.discoveryJobArn = discoveryJobArn
        self.jobEndTime = jobEndTime
        self.jobStartTime = jobStartTime
        self.status = status
        self.storageSystemArn = storageSystemArn
    }
}

public struct DescribeLocationAzureBlobInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of your Azure Blob Storage transfer location.
    /// This member is required.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

public struct DescribeLocationAzureBlobOutput: Swift.Sendable {
    /// The access tier that you want your objects or files transferred into. This only applies when using the location as a transfer destination. For more information, see [Access tiers](https://docs.aws.amazon.com/datasync/latest/userguide/creating-azure-blob-location.html#azure-blob-access-tiers).
    public var accessTier: DataSyncClientTypes.AzureAccessTier?
    /// The ARNs of the DataSync agents that can connect with your Azure Blob Storage container.
    public var agentArns: [Swift.String]?
    /// The authentication method DataSync uses to access your Azure Blob Storage. DataSync can access blob storage using a shared access signature (SAS).
    public var authenticationType: DataSyncClientTypes.AzureBlobAuthenticationType?
    /// The type of blob that you want your objects or files to be when transferring them into Azure Blob Storage. Currently, DataSync only supports moving data into Azure Blob Storage as block blobs. For more information on blob types, see the [Azure Blob Storage documentation](https://learn.microsoft.com/en-us/rest/api/storageservices/understanding-block-blobs--append-blobs--and-page-blobs).
    public var blobType: DataSyncClientTypes.AzureBlobType?
    /// The time that your Azure Blob Storage transfer location was created.
    public var creationTime: Foundation.Date?
    /// The ARN of your Azure Blob Storage transfer location.
    public var locationArn: Swift.String?
    /// The URL of the Azure Blob Storage container involved in your transfer.
    public var locationUri: Swift.String?

    public init(
        accessTier: DataSyncClientTypes.AzureAccessTier? = nil,
        agentArns: [Swift.String]? = nil,
        authenticationType: DataSyncClientTypes.AzureBlobAuthenticationType? = nil,
        blobType: DataSyncClientTypes.AzureBlobType? = nil,
        creationTime: Foundation.Date? = nil,
        locationArn: Swift.String? = nil,
        locationUri: Swift.String? = nil
    )
    {
        self.accessTier = accessTier
        self.agentArns = agentArns
        self.authenticationType = authenticationType
        self.blobType = blobType
        self.creationTime = creationTime
        self.locationArn = locationArn
        self.locationUri = locationUri
    }
}

/// DescribeLocationEfsRequest
public struct DescribeLocationEfsInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the Amazon EFS file system location that you want information about.
    /// This member is required.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

/// DescribeLocationEfsResponse
public struct DescribeLocationEfsOutput: Swift.Sendable {
    /// The ARN of the access point that DataSync uses to access the Amazon EFS file system. For more information, see [Accessing restricted file systems](https://docs.aws.amazon.com/datasync/latest/userguide/create-efs-location.html#create-efs-location-iam).
    public var accessPointArn: Swift.String?
    /// The time that the location was created.
    public var creationTime: Foundation.Date?
    /// The subnet and security groups that DataSync uses to connect to one of your Amazon EFS file system's [mount targets](https://docs.aws.amazon.com/efs/latest/ug/accessing-fs.html).
    public var ec2Config: DataSyncClientTypes.Ec2Config?
    /// The Identity and Access Management (IAM) role that allows DataSync to access your Amazon EFS file system. For more information, see [Creating a DataSync IAM role for file system access](https://docs.aws.amazon.com/datasync/latest/userguide/create-efs-location.html#create-efs-location-iam-role).
    public var fileSystemAccessRoleArn: Swift.String?
    /// Indicates whether DataSync uses Transport Layer Security (TLS) encryption when transferring data to or from the Amazon EFS file system.
    public var inTransitEncryption: DataSyncClientTypes.EfsInTransitEncryption?
    /// The ARN of the Amazon EFS file system location.
    public var locationArn: Swift.String?
    /// The URL of the Amazon EFS file system location.
    public var locationUri: Swift.String?

    public init(
        accessPointArn: Swift.String? = nil,
        creationTime: Foundation.Date? = nil,
        ec2Config: DataSyncClientTypes.Ec2Config? = nil,
        fileSystemAccessRoleArn: Swift.String? = nil,
        inTransitEncryption: DataSyncClientTypes.EfsInTransitEncryption? = nil,
        locationArn: Swift.String? = nil,
        locationUri: Swift.String? = nil
    )
    {
        self.accessPointArn = accessPointArn
        self.creationTime = creationTime
        self.ec2Config = ec2Config
        self.fileSystemAccessRoleArn = fileSystemAccessRoleArn
        self.inTransitEncryption = inTransitEncryption
        self.locationArn = locationArn
        self.locationUri = locationUri
    }
}

public struct DescribeLocationFsxLustreInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the FSx for Lustre location to describe.
    /// This member is required.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

public struct DescribeLocationFsxLustreOutput: Swift.Sendable {
    /// The time that the FSx for Lustre location was created.
    public var creationTime: Foundation.Date?
    /// The Amazon Resource Name (ARN) of the FSx for Lustre location that was described.
    public var locationArn: Swift.String?
    /// The URI of the FSx for Lustre location that was described.
    public var locationUri: Swift.String?
    /// The Amazon Resource Names (ARNs) of the security groups that are configured for the FSx for Lustre file system.
    public var securityGroupArns: [Swift.String]?

    public init(
        creationTime: Foundation.Date? = nil,
        locationArn: Swift.String? = nil,
        locationUri: Swift.String? = nil,
        securityGroupArns: [Swift.String]? = nil
    )
    {
        self.creationTime = creationTime
        self.locationArn = locationArn
        self.locationUri = locationUri
        self.securityGroupArns = securityGroupArns
    }
}

public struct DescribeLocationFsxOntapInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the FSx for ONTAP file system location that you want information about.
    /// This member is required.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

public struct DescribeLocationFsxOntapOutput: Swift.Sendable {
    /// The time that the location was created.
    public var creationTime: Foundation.Date?
    /// The ARN of the FSx for ONTAP file system.
    public var fsxFilesystemArn: Swift.String?
    /// The ARN of the FSx for ONTAP file system location.
    public var locationArn: Swift.String?
    /// The uniform resource identifier (URI) of the FSx for ONTAP file system location.
    public var locationUri: Swift.String?
    /// Specifies the data transfer protocol that DataSync uses to access your Amazon FSx file system.
    public var `protocol`: DataSyncClientTypes.FsxProtocol?
    /// The security groups that DataSync uses to access your FSx for ONTAP file system.
    public var securityGroupArns: [Swift.String]?
    /// The ARN of the storage virtual machine (SVM) on your FSx for ONTAP file system where you're copying data to or from.
    public var storageVirtualMachineArn: Swift.String?

    public init(
        creationTime: Foundation.Date? = nil,
        fsxFilesystemArn: Swift.String? = nil,
        locationArn: Swift.String? = nil,
        locationUri: Swift.String? = nil,
        `protocol`: DataSyncClientTypes.FsxProtocol? = nil,
        securityGroupArns: [Swift.String]? = nil,
        storageVirtualMachineArn: Swift.String? = nil
    )
    {
        self.creationTime = creationTime
        self.fsxFilesystemArn = fsxFilesystemArn
        self.locationArn = locationArn
        self.locationUri = locationUri
        self.`protocol` = `protocol`
        self.securityGroupArns = securityGroupArns
        self.storageVirtualMachineArn = storageVirtualMachineArn
    }
}

public struct DescribeLocationFsxOpenZfsInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the FSx for OpenZFS location to describe.
    /// This member is required.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

public struct DescribeLocationFsxOpenZfsOutput: Swift.Sendable {
    /// The time that the FSx for OpenZFS location was created.
    public var creationTime: Foundation.Date?
    /// The ARN of the FSx for OpenZFS location that was described.
    public var locationArn: Swift.String?
    /// The uniform resource identifier (URI) of the FSx for OpenZFS location that was described. Example: fsxz://us-west-2.fs-1234567890abcdef02/fsx/folderA/folder
    public var locationUri: Swift.String?
    /// The type of protocol that DataSync uses to access your file system.
    public var `protocol`: DataSyncClientTypes.FsxProtocol?
    /// The ARNs of the security groups that are configured for the FSx for OpenZFS file system.
    public var securityGroupArns: [Swift.String]?

    public init(
        creationTime: Foundation.Date? = nil,
        locationArn: Swift.String? = nil,
        locationUri: Swift.String? = nil,
        `protocol`: DataSyncClientTypes.FsxProtocol? = nil,
        securityGroupArns: [Swift.String]? = nil
    )
    {
        self.creationTime = creationTime
        self.locationArn = locationArn
        self.locationUri = locationUri
        self.`protocol` = `protocol`
        self.securityGroupArns = securityGroupArns
    }
}

public struct DescribeLocationFsxWindowsInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the FSx for Windows File Server location.
    /// This member is required.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

public struct DescribeLocationFsxWindowsOutput: Swift.Sendable {
    /// The time that the FSx for Windows File Server location was created.
    public var creationTime: Foundation.Date?
    /// The name of the Microsoft Active Directory domain that the FSx for Windows File Server file system belongs to.
    public var domain: Swift.String?
    /// The ARN of the FSx for Windows File Server location.
    public var locationArn: Swift.String?
    /// The uniform resource identifier (URI) of the FSx for Windows File Server location.
    public var locationUri: Swift.String?
    /// The ARNs of the Amazon EC2 security groups that provide access to your file system's preferred subnet. For information about configuring security groups for file system access, see the [ Amazon FSx for Windows File Server User Guide ](https://docs.aws.amazon.com/fsx/latest/WindowsGuide/limit-access-security-groups.html).
    public var securityGroupArns: [Swift.String]?
    /// The user with the permissions to mount and access the FSx for Windows File Server file system.
    public var user: Swift.String?

    public init(
        creationTime: Foundation.Date? = nil,
        domain: Swift.String? = nil,
        locationArn: Swift.String? = nil,
        locationUri: Swift.String? = nil,
        securityGroupArns: [Swift.String]? = nil,
        user: Swift.String? = nil
    )
    {
        self.creationTime = creationTime
        self.domain = domain
        self.locationArn = locationArn
        self.locationUri = locationUri
        self.securityGroupArns = securityGroupArns
        self.user = user
    }
}

public struct DescribeLocationHdfsInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the HDFS location.
    /// This member is required.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

public struct DescribeLocationHdfsOutput: Swift.Sendable {
    /// The ARNs of the DataSync agents that can connect with your HDFS cluster.
    public var agentArns: [Swift.String]?
    /// The type of authentication used to determine the identity of the user.
    public var authenticationType: DataSyncClientTypes.HdfsAuthenticationType?
    /// The size of the data blocks to write into the HDFS cluster.
    public var blockSize: Swift.Int?
    /// The time that the HDFS location was created.
    public var creationTime: Foundation.Date?
    /// The Kerberos principal with access to the files and folders on the HDFS cluster. This parameter is used if the AuthenticationType is defined as KERBEROS.
    public var kerberosPrincipal: Swift.String?
    /// The URI of the HDFS cluster's Key Management Server (KMS).
    public var kmsKeyProviderUri: Swift.String?
    /// The ARN of the HDFS location.
    public var locationArn: Swift.String?
    /// The URI of the HDFS location.
    public var locationUri: Swift.String?
    /// The NameNode that manages the HDFS namespace.
    public var nameNodes: [DataSyncClientTypes.HdfsNameNode]?
    /// The Quality of Protection (QOP) configuration, which specifies the Remote Procedure Call (RPC) and data transfer protection settings configured on the HDFS cluster.
    public var qopConfiguration: DataSyncClientTypes.QopConfiguration?
    /// The number of DataNodes to replicate the data to when writing to the HDFS cluster.
    public var replicationFactor: Swift.Int?
    /// The user name to identify the client on the host operating system. This parameter is used if the AuthenticationType is defined as SIMPLE.
    public var simpleUser: Swift.String?

    public init(
        agentArns: [Swift.String]? = nil,
        authenticationType: DataSyncClientTypes.HdfsAuthenticationType? = nil,
        blockSize: Swift.Int? = nil,
        creationTime: Foundation.Date? = nil,
        kerberosPrincipal: Swift.String? = nil,
        kmsKeyProviderUri: Swift.String? = nil,
        locationArn: Swift.String? = nil,
        locationUri: Swift.String? = nil,
        nameNodes: [DataSyncClientTypes.HdfsNameNode]? = nil,
        qopConfiguration: DataSyncClientTypes.QopConfiguration? = nil,
        replicationFactor: Swift.Int? = nil,
        simpleUser: Swift.String? = nil
    )
    {
        self.agentArns = agentArns
        self.authenticationType = authenticationType
        self.blockSize = blockSize
        self.creationTime = creationTime
        self.kerberosPrincipal = kerberosPrincipal
        self.kmsKeyProviderUri = kmsKeyProviderUri
        self.locationArn = locationArn
        self.locationUri = locationUri
        self.nameNodes = nameNodes
        self.qopConfiguration = qopConfiguration
        self.replicationFactor = replicationFactor
        self.simpleUser = simpleUser
    }
}

/// DescribeLocationNfsRequest
public struct DescribeLocationNfsInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the NFS location that you want information about.
    /// This member is required.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

/// DescribeLocationNfsResponse
public struct DescribeLocationNfsOutput: Swift.Sendable {
    /// The time when the NFS location was created.
    public var creationTime: Foundation.Date?
    /// The ARN of the NFS location.
    public var locationArn: Swift.String?
    /// The URI of the NFS location.
    public var locationUri: Swift.String?
    /// The mount options that DataSync uses to mount your NFS file server.
    public var mountOptions: DataSyncClientTypes.NfsMountOptions?
    /// The DataSync agents that can connect to your Network File System (NFS) file server.
    public var onPremConfig: DataSyncClientTypes.OnPremConfig?

    public init(
        creationTime: Foundation.Date? = nil,
        locationArn: Swift.String? = nil,
        locationUri: Swift.String? = nil,
        mountOptions: DataSyncClientTypes.NfsMountOptions? = nil,
        onPremConfig: DataSyncClientTypes.OnPremConfig? = nil
    )
    {
        self.creationTime = creationTime
        self.locationArn = locationArn
        self.locationUri = locationUri
        self.mountOptions = mountOptions
        self.onPremConfig = onPremConfig
    }
}

/// DescribeLocationObjectStorageRequest
public struct DescribeLocationObjectStorageInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the object storage system location.
    /// This member is required.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

/// DescribeLocationObjectStorageResponse
public struct DescribeLocationObjectStorageOutput: Swift.Sendable {
    /// The access key (for example, a user name) required to authenticate with the object storage system.
    public var accessKey: Swift.String?
    /// The ARNs of the DataSync agents that can connect with your object storage system.
    public var agentArns: [Swift.String]?
    /// The time that the location was created.
    public var creationTime: Foundation.Date?
    /// The ARN of the object storage system location.
    public var locationArn: Swift.String?
    /// The URI of the object storage system location.
    public var locationUri: Swift.String?
    /// The certificate chain for DataSync to authenticate with your object storage system if the system uses a private or self-signed certificate authority (CA).
    public var serverCertificate: Foundation.Data?
    /// The port that your object storage server accepts inbound network traffic on (for example, port 443).
    public var serverPort: Swift.Int?
    /// The protocol that your object storage system uses to communicate.
    public var serverProtocol: DataSyncClientTypes.ObjectStorageServerProtocol?

    public init(
        accessKey: Swift.String? = nil,
        agentArns: [Swift.String]? = nil,
        creationTime: Foundation.Date? = nil,
        locationArn: Swift.String? = nil,
        locationUri: Swift.String? = nil,
        serverCertificate: Foundation.Data? = nil,
        serverPort: Swift.Int? = nil,
        serverProtocol: DataSyncClientTypes.ObjectStorageServerProtocol? = nil
    )
    {
        self.accessKey = accessKey
        self.agentArns = agentArns
        self.creationTime = creationTime
        self.locationArn = locationArn
        self.locationUri = locationUri
        self.serverCertificate = serverCertificate
        self.serverPort = serverPort
        self.serverProtocol = serverProtocol
    }
}

/// DescribeLocationS3Request
public struct DescribeLocationS3Input: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the Amazon S3 location.
    /// This member is required.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

/// DescribeLocationS3Response
public struct DescribeLocationS3Output: Swift.Sendable {
    /// The ARNs of the DataSync agents deployed on your Outpost when using working with Amazon S3 on Outposts. For more information, see [Deploy your DataSync agent on Outposts](https://docs.aws.amazon.com/datasync/latest/userguide/deploy-agents.html#outposts-agent).
    public var agentArns: [Swift.String]?
    /// The time that the Amazon S3 location was created.
    public var creationTime: Foundation.Date?
    /// The ARN of the Amazon S3 location.
    public var locationArn: Swift.String?
    /// The URL of the Amazon S3 location that was described.
    public var locationUri: Swift.String?
    /// Specifies the Amazon Resource Name (ARN) of the Identity and Access Management (IAM) role that DataSync uses to access your S3 bucket. For more information, see [Providing DataSync access to S3 buckets](https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#create-s3-location-access).
    public var s3Config: DataSyncClientTypes.S3Config?
    /// When Amazon S3 is a destination location, this is the storage class that you chose for your objects. Some storage classes have behaviors that can affect your Amazon S3 storage costs. For more information, see [Storage class considerations with Amazon S3 transfers](https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes).
    public var s3StorageClass: DataSyncClientTypes.S3StorageClass?

    public init(
        agentArns: [Swift.String]? = nil,
        creationTime: Foundation.Date? = nil,
        locationArn: Swift.String? = nil,
        locationUri: Swift.String? = nil,
        s3Config: DataSyncClientTypes.S3Config? = nil,
        s3StorageClass: DataSyncClientTypes.S3StorageClass? = nil
    )
    {
        self.agentArns = agentArns
        self.creationTime = creationTime
        self.locationArn = locationArn
        self.locationUri = locationUri
        self.s3Config = s3Config
        self.s3StorageClass = s3StorageClass
    }
}

/// DescribeLocationSmbRequest
public struct DescribeLocationSmbInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the SMB location that you want information about.
    /// This member is required.
    public var locationArn: Swift.String?

    public init(
        locationArn: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
    }
}

/// DescribeLocationSmbResponse
public struct DescribeLocationSmbOutput: Swift.Sendable {
    /// The ARNs of the DataSync agents that can connect with your SMB file server.
    public var agentArns: [Swift.String]?
    /// The time that the SMB location was created.
    public var creationTime: Foundation.Date?
    /// The name of the Microsoft Active Directory domain that the SMB file server belongs to.
    public var domain: Swift.String?
    /// The ARN of the SMB location.
    public var locationArn: Swift.String?
    /// The URI of the SMB location.
    public var locationUri: Swift.String?
    /// The protocol that DataSync use to access your SMB file.
    public var mountOptions: DataSyncClientTypes.SmbMountOptions?
    /// The user that can mount and access the files, folders, and file metadata in your SMB file server.
    public var user: Swift.String?

    public init(
        agentArns: [Swift.String]? = nil,
        creationTime: Foundation.Date? = nil,
        domain: Swift.String? = nil,
        locationArn: Swift.String? = nil,
        locationUri: Swift.String? = nil,
        mountOptions: DataSyncClientTypes.SmbMountOptions? = nil,
        user: Swift.String? = nil
    )
    {
        self.agentArns = agentArns
        self.creationTime = creationTime
        self.domain = domain
        self.locationArn = locationArn
        self.locationUri = locationUri
        self.mountOptions = mountOptions
        self.user = user
    }
}

public struct DescribeStorageSystemInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of an on-premises storage system that you're using with DataSync Discovery.
    /// This member is required.
    public var storageSystemArn: Swift.String?

    public init(
        storageSystemArn: Swift.String? = nil
    )
    {
        self.storageSystemArn = storageSystemArn
    }
}

extension DataSyncClientTypes {

    public enum StorageSystemConnectivityStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case fail
        case pass
        case unknown
        case sdkUnknown(Swift.String)

        public static var allCases: [StorageSystemConnectivityStatus] {
            return [
                .fail,
                .pass,
                .unknown
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .fail: return "FAIL"
            case .pass: return "PASS"
            case .unknown: return "UNKNOWN"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeStorageSystemOutput: Swift.Sendable {
    /// The ARN of the DataSync agent that connects to and reads from your on-premises storage system.
    public var agentArns: [Swift.String]?
    /// The ARN of the Amazon CloudWatch log group that's used to monitor and log discovery job events.
    public var cloudWatchLogGroupArn: Swift.String?
    /// Indicates whether your DataSync agent can connect to your on-premises storage system.
    public var connectivityStatus: DataSyncClientTypes.StorageSystemConnectivityStatus?
    /// The time when you added the on-premises storage system to DataSync Discovery.
    public var creationTime: Foundation.Date?
    /// Describes the connectivity error that the DataSync agent is encountering with your on-premises storage system.
    public var errorMessage: Swift.String?
    /// The name that you gave your on-premises storage system when adding it to DataSync Discovery.
    public var name: Swift.String?
    /// The ARN of the secret that stores your on-premises storage system's credentials. DataSync Discovery stores these credentials in [Secrets Manager](https://docs.aws.amazon.com/datasync/latest/userguide/discovery-configure-storage.html#discovery-add-storage).
    public var secretsManagerArn: Swift.String?
    /// The server name and network port required to connect with your on-premises storage system's management interface.
    public var serverConfiguration: DataSyncClientTypes.DiscoveryServerConfiguration?
    /// The ARN of the on-premises storage system that the discovery job looked at.
    public var storageSystemArn: Swift.String?
    /// The type of on-premises storage system. DataSync Discovery currently only supports NetApp Fabric-Attached Storage (FAS) and All Flash FAS (AFF) systems running ONTAP 9.7 or later.
    public var systemType: DataSyncClientTypes.DiscoverySystemType?

    public init(
        agentArns: [Swift.String]? = nil,
        cloudWatchLogGroupArn: Swift.String? = nil,
        connectivityStatus: DataSyncClientTypes.StorageSystemConnectivityStatus? = nil,
        creationTime: Foundation.Date? = nil,
        errorMessage: Swift.String? = nil,
        name: Swift.String? = nil,
        secretsManagerArn: Swift.String? = nil,
        serverConfiguration: DataSyncClientTypes.DiscoveryServerConfiguration? = nil,
        storageSystemArn: Swift.String? = nil,
        systemType: DataSyncClientTypes.DiscoverySystemType? = nil
    )
    {
        self.agentArns = agentArns
        self.cloudWatchLogGroupArn = cloudWatchLogGroupArn
        self.connectivityStatus = connectivityStatus
        self.creationTime = creationTime
        self.errorMessage = errorMessage
        self.name = name
        self.secretsManagerArn = secretsManagerArn
        self.serverConfiguration = serverConfiguration
        self.storageSystemArn = storageSystemArn
        self.systemType = systemType
    }
}

extension DataSyncClientTypes {

    public enum DiscoveryResourceType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cluster
        case svm
        case volume
        case sdkUnknown(Swift.String)

        public static var allCases: [DiscoveryResourceType] {
            return [
                .cluster,
                .svm,
                .volume
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cluster: return "CLUSTER"
            case .svm: return "SVM"
            case .volume: return "VOLUME"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeStorageSystemResourceMetricsInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the discovery job that collects information about your on-premises storage system.
    /// This member is required.
    public var discoveryJobArn: Swift.String?
    /// Specifies a time within the total duration that the discovery job ran. To see information gathered during a certain time frame, use this parameter with StartTime.
    public var endTime: Foundation.Date?
    /// Specifies how many results that you want in the response.
    public var maxResults: Swift.Int?
    /// Specifies an opaque string that indicates the position to begin the next list of results in the response.
    public var nextToken: Swift.String?
    /// Specifies the universally unique identifier (UUID) of the storage system resource that you want information about.
    /// This member is required.
    public var resourceId: Swift.String?
    /// Specifies the kind of storage system resource that you want information about.
    /// This member is required.
    public var resourceType: DataSyncClientTypes.DiscoveryResourceType?
    /// Specifies a time within the total duration that the discovery job ran. To see information gathered during a certain time frame, use this parameter with EndTime.
    public var startTime: Foundation.Date?

    public init(
        discoveryJobArn: Swift.String? = nil,
        endTime: Foundation.Date? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: DataSyncClientTypes.DiscoveryResourceType? = nil,
        startTime: Foundation.Date? = nil
    )
    {
        self.discoveryJobArn = discoveryJobArn
        self.endTime = endTime
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.resourceId = resourceId
        self.resourceType = resourceType
        self.startTime = startTime
    }
}

extension DataSyncClientTypes {

    /// The IOPS peaks for an on-premises storage system resource. Each data point represents the 95th percentile peak value during a 1-hour interval.
    public struct IOPS: Swift.Sendable {
        /// Peak IOPS unrelated to read and write operations.
        public var other: Swift.Double?
        /// Peak IOPS related to read operations.
        public var read: Swift.Double?
        /// Peak total IOPS on your on-premises storage system resource.
        public var total: Swift.Double?
        /// Peak IOPS related to write operations.
        public var write: Swift.Double?

        public init(
            other: Swift.Double? = nil,
            read: Swift.Double? = nil,
            total: Swift.Double? = nil,
            write: Swift.Double? = nil
        )
        {
            self.other = other
            self.read = read
            self.total = total
            self.write = write
        }
    }
}

extension DataSyncClientTypes {

    /// The latency peaks for an on-premises storage system resource. Each data point represents the 95th percentile peak value during a 1-hour interval.
    public struct Latency: Swift.Sendable {
        /// Peak latency for operations unrelated to read and write operations.
        public var other: Swift.Double?
        /// Peak latency for read operations.
        public var read: Swift.Double?
        /// Peak latency for write operations.
        public var write: Swift.Double?

        public init(
            other: Swift.Double? = nil,
            read: Swift.Double? = nil,
            write: Swift.Double? = nil
        )
        {
            self.other = other
            self.read = read
            self.write = write
        }
    }
}

extension DataSyncClientTypes {

    /// The throughput peaks for an on-premises storage system volume. Each data point represents the 95th percentile peak value during a 1-hour interval.
    public struct Throughput: Swift.Sendable {
        /// Peak throughput unrelated to read and write operations.
        public var other: Swift.Double?
        /// Peak throughput related to read operations.
        public var read: Swift.Double?
        /// Peak total throughput on your on-premises storage system resource.
        public var total: Swift.Double?
        /// Peak throughput related to write operations.
        public var write: Swift.Double?

        public init(
            other: Swift.Double? = nil,
            read: Swift.Double? = nil,
            total: Swift.Double? = nil,
            write: Swift.Double? = nil
        )
        {
            self.other = other
            self.read = read
            self.total = total
            self.write = write
        }
    }
}

extension DataSyncClientTypes {

    /// The types of performance data that DataSync Discovery collects about an on-premises storage system resource.
    public struct P95Metrics: Swift.Sendable {
        /// The IOPS peaks for an on-premises storage system resource. Each data point represents the 95th percentile peak value during a 1-hour interval.
        public var iops: DataSyncClientTypes.IOPS?
        /// The latency peaks for an on-premises storage system resource. Each data point represents the 95th percentile peak value during a 1-hour interval.
        public var latency: DataSyncClientTypes.Latency?
        /// The throughput peaks for an on-premises storage system resource. Each data point represents the 95th percentile peak value during a 1-hour interval.
        public var throughput: DataSyncClientTypes.Throughput?

        public init(
            iops: DataSyncClientTypes.IOPS? = nil,
            latency: DataSyncClientTypes.Latency? = nil,
            throughput: DataSyncClientTypes.Throughput? = nil
        )
        {
            self.iops = iops
            self.latency = latency
            self.throughput = throughput
        }
    }
}

extension DataSyncClientTypes {

    /// Information, including performance data and capacity usage, provided by DataSync Discovery about a resource in your on-premises storage system.
    public struct ResourceMetrics: Swift.Sendable {
        /// The storage capacity of the on-premises storage system resource.
        public var capacity: DataSyncClientTypes.Capacity?
        /// The types of performance data that DataSync Discovery collects about the on-premises storage system resource.
        public var p95Metrics: DataSyncClientTypes.P95Metrics?
        /// The universally unique identifier (UUID) of the on-premises storage system resource.
        public var resourceId: Swift.String?
        /// The type of on-premises storage system resource.
        public var resourceType: DataSyncClientTypes.DiscoveryResourceType?
        /// The time when DataSync Discovery collected this information from the resource.
        public var timestamp: Foundation.Date?

        public init(
            capacity: DataSyncClientTypes.Capacity? = nil,
            p95Metrics: DataSyncClientTypes.P95Metrics? = nil,
            resourceId: Swift.String? = nil,
            resourceType: DataSyncClientTypes.DiscoveryResourceType? = nil,
            timestamp: Foundation.Date? = nil
        )
        {
            self.capacity = capacity
            self.p95Metrics = p95Metrics
            self.resourceId = resourceId
            self.resourceType = resourceType
            self.timestamp = timestamp
        }
    }
}

public struct DescribeStorageSystemResourceMetricsOutput: Swift.Sendable {
    /// The details that your discovery job collected about your storage system resource.
    public var metrics: [DataSyncClientTypes.ResourceMetrics]?
    /// The opaque string that indicates the position to begin the next list of results in the response.
    public var nextToken: Swift.String?

    public init(
        metrics: [DataSyncClientTypes.ResourceMetrics]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.metrics = metrics
        self.nextToken = nextToken
    }
}

extension DataSyncClientTypes {

    public enum DiscoveryResourceFilter: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case svm
        case sdkUnknown(Swift.String)

        public static var allCases: [DiscoveryResourceFilter] {
            return [
                .svm
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .svm: return "SVM"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeStorageSystemResourcesInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the discovery job that's collecting data from your on-premises storage system.
    /// This member is required.
    public var discoveryJobArn: Swift.String?
    /// Filters the storage system resources that you want returned. For example, this might be volumes associated with a specific storage virtual machine (SVM).
    public var filter: [Swift.String: [Swift.String]]?
    /// Specifies the maximum number of storage system resources that you want to list in a response.
    public var maxResults: Swift.Int?
    /// Specifies an opaque string that indicates the position to begin the next list of results in the response.
    public var nextToken: Swift.String?
    /// Specifies the universally unique identifiers (UUIDs) of the storage system resources that you want information about. You can't use this parameter in combination with the Filter parameter.
    public var resourceIds: [Swift.String]?
    /// Specifies what kind of storage system resources that you want information about.
    /// This member is required.
    public var resourceType: DataSyncClientTypes.DiscoveryResourceType?

    public init(
        discoveryJobArn: Swift.String? = nil,
        filter: [Swift.String: [Swift.String]]? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        resourceIds: [Swift.String]? = nil,
        resourceType: DataSyncClientTypes.DiscoveryResourceType? = nil
    )
    {
        self.discoveryJobArn = discoveryJobArn
        self.filter = filter
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.resourceIds = resourceIds
        self.resourceType = resourceType
    }
}

extension DataSyncClientTypes {

    /// The performance data that DataSync Discovery collects about an on-premises storage system resource.
    public struct MaxP95Performance: Swift.Sendable {
        /// Peak IOPS unrelated to read and write operations.
        public var iopsOther: Swift.Double?
        /// Peak IOPS related to read operations.
        public var iopsRead: Swift.Double?
        /// Peak total IOPS on your on-premises storage system resource.
        public var iopsTotal: Swift.Double?
        /// Peak IOPS related to write operations.
        public var iopsWrite: Swift.Double?
        /// Peak latency for operations unrelated to read and write operations.
        public var latencyOther: Swift.Double?
        /// Peak latency for read operations.
        public var latencyRead: Swift.Double?
        /// Peak latency for write operations.
        public var latencyWrite: Swift.Double?
        /// Peak throughput unrelated to read and write operations.
        public var throughputOther: Swift.Double?
        /// Peak throughput related to read operations.
        public var throughputRead: Swift.Double?
        /// Peak total throughput on your on-premises storage system resource.
        public var throughputTotal: Swift.Double?
        /// Peak throughput related to write operations.
        public var throughputWrite: Swift.Double?

        public init(
            iopsOther: Swift.Double? = nil,
            iopsRead: Swift.Double? = nil,
            iopsTotal: Swift.Double? = nil,
            iopsWrite: Swift.Double? = nil,
            latencyOther: Swift.Double? = nil,
            latencyRead: Swift.Double? = nil,
            latencyWrite: Swift.Double? = nil,
            throughputOther: Swift.Double? = nil,
            throughputRead: Swift.Double? = nil,
            throughputTotal: Swift.Double? = nil,
            throughputWrite: Swift.Double? = nil
        )
        {
            self.iopsOther = iopsOther
            self.iopsRead = iopsRead
            self.iopsTotal = iopsTotal
            self.iopsWrite = iopsWrite
            self.latencyOther = latencyOther
            self.latencyRead = latencyRead
            self.latencyWrite = latencyWrite
            self.throughputOther = throughputOther
            self.throughputRead = throughputRead
            self.throughputTotal = throughputTotal
            self.throughputWrite = throughputWrite
        }
    }
}

extension DataSyncClientTypes {

    /// The details about an Amazon Web Services storage service that DataSync Discovery recommends for a resource in your on-premises storage system. For more information, see [Recommendations provided by DataSync Discovery](https://docs.aws.amazon.com/datasync/latest/userguide/discovery-understand-recommendations.html).
    public struct Recommendation: Swift.Sendable {
        /// The estimated monthly cost of the recommended Amazon Web Services storage service.
        public var estimatedMonthlyStorageCost: Swift.String?
        /// Information about how you can set up a recommended Amazon Web Services storage service.
        public var storageConfiguration: [Swift.String: Swift.String]?
        /// A recommended Amazon Web Services storage service that you can migrate data to based on information that DataSync Discovery collects about your on-premises storage system.
        public var storageType: Swift.String?

        public init(
            estimatedMonthlyStorageCost: Swift.String? = nil,
            storageConfiguration: [Swift.String: Swift.String]? = nil,
            storageType: Swift.String? = nil
        )
        {
            self.estimatedMonthlyStorageCost = estimatedMonthlyStorageCost
            self.storageConfiguration = storageConfiguration
            self.storageType = storageType
        }
    }
}

extension DataSyncClientTypes {

    public enum RecommendationStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case completed
        case failed
        case inProgress
        case `none`
        case sdkUnknown(Swift.String)

        public static var allCases: [RecommendationStatus] {
            return [
                .completed,
                .failed,
                .inProgress,
                .none
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .completed: return "COMPLETED"
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case .none: return "NONE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// The information that DataSync Discovery collects about an on-premises storage system cluster.
    public struct NetAppONTAPCluster: Swift.Sendable {
        /// The number of CIFS shares in the cluster.
        public var cifsShareCount: Swift.Int?
        /// The storage space that's being used in the cluster without accounting for compression or deduplication.
        public var clusterBlockStorageLogicalUsed: Swift.Int?
        /// The total storage space that's available in the cluster.
        public var clusterBlockStorageSize: Swift.Int?
        /// The storage space that's being used in a cluster.
        public var clusterBlockStorageUsed: Swift.Int?
        /// The amount of space in the cluster that's in cloud storage (for example, if you're using data tiering).
        public var clusterCloudStorageUsed: Swift.Int?
        /// The name of the cluster.
        public var clusterName: Swift.String?
        /// The number of LUNs (logical unit numbers) in the cluster.
        public var lunCount: Swift.Int?
        /// The performance data that DataSync Discovery collects about the cluster.
        public var maxP95Performance: DataSyncClientTypes.MaxP95Performance?
        /// The number of NFS volumes in the cluster.
        public var nfsExportedVolumes: Swift.Int?
        /// Indicates whether DataSync Discovery recommendations for the cluster are ready to view, incomplete, or can't be determined. For more information, see [Recommendation statuses](https://docs.aws.amazon.com/datasync/latest/userguide/discovery-job-statuses.html#recommendation-statuses-table).
        public var recommendationStatus: DataSyncClientTypes.RecommendationStatus?
        /// The Amazon Web Services storage services that DataSync Discovery recommends for the cluster. For more information, see [Recommendations provided by DataSync Discovery](https://docs.aws.amazon.com/datasync/latest/userguide/discovery-understand-recommendations.html).
        public var recommendations: [DataSyncClientTypes.Recommendation]?
        /// The universally unique identifier (UUID) of the cluster.
        public var resourceId: Swift.String?

        public init(
            cifsShareCount: Swift.Int? = nil,
            clusterBlockStorageLogicalUsed: Swift.Int? = nil,
            clusterBlockStorageSize: Swift.Int? = nil,
            clusterBlockStorageUsed: Swift.Int? = nil,
            clusterCloudStorageUsed: Swift.Int? = nil,
            clusterName: Swift.String? = nil,
            lunCount: Swift.Int? = nil,
            maxP95Performance: DataSyncClientTypes.MaxP95Performance? = nil,
            nfsExportedVolumes: Swift.Int? = nil,
            recommendationStatus: DataSyncClientTypes.RecommendationStatus? = nil,
            recommendations: [DataSyncClientTypes.Recommendation]? = nil,
            resourceId: Swift.String? = nil
        )
        {
            self.cifsShareCount = cifsShareCount
            self.clusterBlockStorageLogicalUsed = clusterBlockStorageLogicalUsed
            self.clusterBlockStorageSize = clusterBlockStorageSize
            self.clusterBlockStorageUsed = clusterBlockStorageUsed
            self.clusterCloudStorageUsed = clusterCloudStorageUsed
            self.clusterName = clusterName
            self.lunCount = lunCount
            self.maxP95Performance = maxP95Performance
            self.nfsExportedVolumes = nfsExportedVolumes
            self.recommendationStatus = recommendationStatus
            self.recommendations = recommendations
            self.resourceId = resourceId
        }
    }
}

extension DataSyncClientTypes {

    /// The information that DataSync Discovery collects about a storage virtual machine (SVM) in your on-premises storage system.
    public struct NetAppONTAPSVM: Swift.Sendable {
        /// The number of CIFS shares in the SVM.
        public var cifsShareCount: Swift.Int?
        /// The universally unique identifier (UUID) of the cluster associated with the SVM.
        public var clusterUuid: Swift.String?
        /// The data transfer protocols (such as NFS) configured for the SVM.
        public var enabledProtocols: [Swift.String]?
        /// The number of LUNs (logical unit numbers) in the SVM.
        public var lunCount: Swift.Int?
        /// The performance data that DataSync Discovery collects about the SVM.
        public var maxP95Performance: DataSyncClientTypes.MaxP95Performance?
        /// The number of NFS volumes in the SVM.
        public var nfsExportedVolumes: Swift.Int?
        /// Indicates whether DataSync Discovery recommendations for the SVM are ready to view, incomplete, or can't be determined. For more information, see [Recommendation statuses](https://docs.aws.amazon.com/datasync/latest/userguide/discovery-job-statuses.html#recommendation-statuses-table).
        public var recommendationStatus: DataSyncClientTypes.RecommendationStatus?
        /// The Amazon Web Services storage services that DataSync Discovery recommends for the SVM. For more information, see [Recommendations provided by DataSync Discovery](https://docs.aws.amazon.com/datasync/latest/userguide/discovery-understand-recommendations.html).
        public var recommendations: [DataSyncClientTypes.Recommendation]?
        /// The UUID of the SVM.
        public var resourceId: Swift.String?
        /// The name of the SVM
        public var svmName: Swift.String?
        /// The total storage space that's available in the SVM.
        public var totalCapacityProvisioned: Swift.Int?
        /// The storage space that's being used in the SVM.
        public var totalCapacityUsed: Swift.Int?
        /// The storage space that's being used in the SVM without accounting for compression or deduplication.
        public var totalLogicalCapacityUsed: Swift.Int?
        /// The amount of storage in the SVM that's being used for snapshots.
        public var totalSnapshotCapacityUsed: Swift.Int?

        public init(
            cifsShareCount: Swift.Int? = nil,
            clusterUuid: Swift.String? = nil,
            enabledProtocols: [Swift.String]? = nil,
            lunCount: Swift.Int? = nil,
            maxP95Performance: DataSyncClientTypes.MaxP95Performance? = nil,
            nfsExportedVolumes: Swift.Int? = nil,
            recommendationStatus: DataSyncClientTypes.RecommendationStatus? = nil,
            recommendations: [DataSyncClientTypes.Recommendation]? = nil,
            resourceId: Swift.String? = nil,
            svmName: Swift.String? = nil,
            totalCapacityProvisioned: Swift.Int? = nil,
            totalCapacityUsed: Swift.Int? = nil,
            totalLogicalCapacityUsed: Swift.Int? = nil,
            totalSnapshotCapacityUsed: Swift.Int? = nil
        )
        {
            self.cifsShareCount = cifsShareCount
            self.clusterUuid = clusterUuid
            self.enabledProtocols = enabledProtocols
            self.lunCount = lunCount
            self.maxP95Performance = maxP95Performance
            self.nfsExportedVolumes = nfsExportedVolumes
            self.recommendationStatus = recommendationStatus
            self.recommendations = recommendations
            self.resourceId = resourceId
            self.svmName = svmName
            self.totalCapacityProvisioned = totalCapacityProvisioned
            self.totalCapacityUsed = totalCapacityUsed
            self.totalLogicalCapacityUsed = totalLogicalCapacityUsed
            self.totalSnapshotCapacityUsed = totalSnapshotCapacityUsed
        }
    }
}

extension DataSyncClientTypes {

    /// The information that DataSync Discovery collects about a volume in your on-premises storage system.
    public struct NetAppONTAPVolume: Swift.Sendable {
        /// The total storage space that's available in the volume.
        public var capacityProvisioned: Swift.Int?
        /// The storage space that's being used in the volume.
        public var capacityUsed: Swift.Int?
        /// The number of CIFS shares in the volume.
        public var cifsShareCount: Swift.Int?
        /// The storage space that's being used in the volume without accounting for compression or deduplication.
        public var logicalCapacityUsed: Swift.Int?
        /// The number of LUNs (logical unit numbers) in the volume.
        public var lunCount: Swift.Int?
        /// The performance data that DataSync Discovery collects about the volume.
        public var maxP95Performance: DataSyncClientTypes.MaxP95Performance?
        /// The number of NFS volumes in the volume.
        public var nfsExported: Swift.Bool
        /// Indicates whether DataSync Discovery recommendations for the volume are ready to view, incomplete, or can't be determined. For more information, see [Recommendation statuses](https://docs.aws.amazon.com/datasync/latest/userguide/discovery-job-statuses.html#recommendation-statuses-table).
        public var recommendationStatus: DataSyncClientTypes.RecommendationStatus?
        /// The Amazon Web Services storage services that DataSync Discovery recommends for the volume. For more information, see [Recommendations provided by DataSync Discovery](https://docs.aws.amazon.com/datasync/latest/userguide/discovery-understand-recommendations.html).
        public var recommendations: [DataSyncClientTypes.Recommendation]?
        /// The universally unique identifier (UUID) of the volume.
        public var resourceId: Swift.String?
        /// The volume's security style (such as Unix or NTFS).
        public var securityStyle: Swift.String?
        /// The amount of storage in the volume that's being used for snapshots.
        public var snapshotCapacityUsed: Swift.Int?
        /// The name of the SVM associated with the volume.
        public var svmName: Swift.String?
        /// The UUID of the storage virtual machine (SVM) associated with the volume.
        public var svmUuid: Swift.String?
        /// The name of the volume.
        public var volumeName: Swift.String?

        public init(
            capacityProvisioned: Swift.Int? = nil,
            capacityUsed: Swift.Int? = nil,
            cifsShareCount: Swift.Int? = nil,
            logicalCapacityUsed: Swift.Int? = nil,
            lunCount: Swift.Int? = nil,
            maxP95Performance: DataSyncClientTypes.MaxP95Performance? = nil,
            nfsExported: Swift.Bool = false,
            recommendationStatus: DataSyncClientTypes.RecommendationStatus? = nil,
            recommendations: [DataSyncClientTypes.Recommendation]? = nil,
            resourceId: Swift.String? = nil,
            securityStyle: Swift.String? = nil,
            snapshotCapacityUsed: Swift.Int? = nil,
            svmName: Swift.String? = nil,
            svmUuid: Swift.String? = nil,
            volumeName: Swift.String? = nil
        )
        {
            self.capacityProvisioned = capacityProvisioned
            self.capacityUsed = capacityUsed
            self.cifsShareCount = cifsShareCount
            self.logicalCapacityUsed = logicalCapacityUsed
            self.lunCount = lunCount
            self.maxP95Performance = maxP95Performance
            self.nfsExported = nfsExported
            self.recommendationStatus = recommendationStatus
            self.recommendations = recommendations
            self.resourceId = resourceId
            self.securityStyle = securityStyle
            self.snapshotCapacityUsed = snapshotCapacityUsed
            self.svmName = svmName
            self.svmUuid = svmUuid
            self.volumeName = volumeName
        }
    }
}

extension DataSyncClientTypes {

    /// Information provided by DataSync Discovery about the resources in your on-premises storage system.
    public struct ResourceDetails: Swift.Sendable {
        /// The information that DataSync Discovery collects about the cluster in your on-premises storage system.
        public var netAppONTAPClusters: [DataSyncClientTypes.NetAppONTAPCluster]?
        /// The information that DataSync Discovery collects about storage virtual machines (SVMs) in your on-premises storage system.
        public var netAppONTAPSVMs: [DataSyncClientTypes.NetAppONTAPSVM]?
        /// The information that DataSync Discovery collects about volumes in your on-premises storage system.
        public var netAppONTAPVolumes: [DataSyncClientTypes.NetAppONTAPVolume]?

        public init(
            netAppONTAPClusters: [DataSyncClientTypes.NetAppONTAPCluster]? = nil,
            netAppONTAPSVMs: [DataSyncClientTypes.NetAppONTAPSVM]? = nil,
            netAppONTAPVolumes: [DataSyncClientTypes.NetAppONTAPVolume]? = nil
        )
        {
            self.netAppONTAPClusters = netAppONTAPClusters
            self.netAppONTAPSVMs = netAppONTAPSVMs
            self.netAppONTAPVolumes = netAppONTAPVolumes
        }
    }
}

public struct DescribeStorageSystemResourcesOutput: Swift.Sendable {
    /// The opaque string that indicates the position to begin the next list of results in the response.
    public var nextToken: Swift.String?
    /// The information collected about your storage system's resources. A response can also include Amazon Web Services storage service recommendations. For more information, see [storage resource information](https://docs.aws.amazon.com/datasync/latest/userguide/discovery-understand-findings.html) collected by and [recommendations](https://docs.aws.amazon.com/datasync/latest/userguide/discovery-understand-recommendations.html) provided by DataSync Discovery.
    public var resourceDetails: DataSyncClientTypes.ResourceDetails?

    public init(
        nextToken: Swift.String? = nil,
        resourceDetails: DataSyncClientTypes.ResourceDetails? = nil
    )
    {
        self.nextToken = nextToken
        self.resourceDetails = resourceDetails
    }
}

/// DescribeTaskRequest
public struct DescribeTaskInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the transfer task that you want information about.
    /// This member is required.
    public var taskArn: Swift.String?

    public init(
        taskArn: Swift.String? = nil
    )
    {
        self.taskArn = taskArn
    }
}

extension DataSyncClientTypes {

    public enum ScheduleDisabledBy: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case service
        case user
        case sdkUnknown(Swift.String)

        public static var allCases: [ScheduleDisabledBy] {
            return [
                .service,
                .user
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .service: return "SERVICE"
            case .user: return "USER"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// Provides information about your DataSync [task schedule](https://docs.aws.amazon.com/datasync/latest/userguide/task-scheduling.html).
    public struct TaskScheduleDetails: Swift.Sendable {
        /// Indicates how your task schedule was disabled.
        ///
        /// * USER - Your schedule was manually disabled by using the [UpdateTask](https://docs.aws.amazon.com/datasync/latest/userguide/API_UpdateTask.html) operation or DataSync console.
        ///
        /// * SERVICE - Your schedule was automatically disabled by DataSync because the task failed repeatedly with the same error.
        public var disabledBy: DataSyncClientTypes.ScheduleDisabledBy?
        /// Provides a reason if the task schedule is disabled. If your schedule is disabled by USER, you see a Manually disabled by user. message. If your schedule is disabled by SERVICE, you see an error message to help you understand why the task keeps failing. For information on resolving DataSync errors, see [Troubleshooting issues with DataSync transfers](https://docs.aws.amazon.com/datasync/latest/userguide/troubleshooting-datasync-locations-tasks.html).
        public var disabledReason: Swift.String?
        /// Indicates the last time the status of your task schedule changed. For example, if DataSync automatically disables your schedule because of a repeated error, you can see when the schedule was disabled.
        public var statusUpdateTime: Foundation.Date?

        public init(
            disabledBy: DataSyncClientTypes.ScheduleDisabledBy? = nil,
            disabledReason: Swift.String? = nil,
            statusUpdateTime: Foundation.Date? = nil
        )
        {
            self.disabledBy = disabledBy
            self.disabledReason = disabledReason
            self.statusUpdateTime = statusUpdateTime
        }
    }
}

extension DataSyncClientTypes {

    public enum TaskStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case available
        case creating
        case queued
        case running
        case unavailable
        case sdkUnknown(Swift.String)

        public static var allCases: [TaskStatus] {
            return [
                .available,
                .creating,
                .queued,
                .running,
                .unavailable
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .available: return "AVAILABLE"
            case .creating: return "CREATING"
            case .queued: return "QUEUED"
            case .running: return "RUNNING"
            case .unavailable: return "UNAVAILABLE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// DescribeTaskResponse
public struct DescribeTaskOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of an Amazon CloudWatch log group for monitoring your task. For more information, see [Monitoring data transfers with CloudWatch Logs](https://docs.aws.amazon.com/datasync/latest/userguide/configure-logging.html).
    public var cloudWatchLogGroupArn: Swift.String?
    /// The time that the task was created.
    public var creationTime: Foundation.Date?
    /// The ARN of the most recent task execution.
    public var currentTaskExecutionArn: Swift.String?
    /// The ARN of your transfer's destination location.
    public var destinationLocationArn: Swift.String?
    /// The ARNs of the [network interfaces](https://docs.aws.amazon.com/datasync/latest/userguide/datasync-network.html#required-network-interfaces) that DataSync created for your destination location.
    public var destinationNetworkInterfaceArns: [Swift.String]?
    /// If there's an issue with your task, you can use the error code to help you troubleshoot the problem. For more information, see [Troubleshooting issues with DataSync transfers](https://docs.aws.amazon.com/datasync/latest/userguide/troubleshooting-datasync-locations-tasks.html).
    public var errorCode: Swift.String?
    /// If there's an issue with your task, you can use the error details to help you troubleshoot the problem. For more information, see [Troubleshooting issues with DataSync transfers](https://docs.aws.amazon.com/datasync/latest/userguide/troubleshooting-datasync-locations-tasks.html).
    public var errorDetail: Swift.String?
    /// The exclude filters that define the files, objects, and folders in your source location that you don't want DataSync to transfer. For more information and examples, see [Specifying what DataSync transfers by using filters](https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html).
    public var excludes: [DataSyncClientTypes.FilterRule]?
    /// The include filters that define the files, objects, and folders in your source location that you want DataSync to transfer. For more information and examples, see [Specifying what DataSync transfers by using filters](https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html).
    public var includes: [DataSyncClientTypes.FilterRule]?
    /// The configuration of the manifest that lists the files or objects that you want DataSync to transfer. For more information, see [Specifying what DataSync transfers by using a manifest](https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html).
    public var manifestConfig: DataSyncClientTypes.ManifestConfig?
    /// The name of your task.
    public var name: Swift.String?
    /// The task's settings. For example, what file metadata gets preserved, how data integrity gets verified at the end of your transfer, bandwidth limits, among other options.
    public var options: DataSyncClientTypes.Options?
    /// The schedule for when you want your task to run. For more information, see [Scheduling your task](https://docs.aws.amazon.com/datasync/latest/userguide/task-scheduling.html).
    public var schedule: DataSyncClientTypes.TaskSchedule?
    /// The details about your [task schedule](https://docs.aws.amazon.com/datasync/latest/userguide/task-scheduling.html).
    public var scheduleDetails: DataSyncClientTypes.TaskScheduleDetails?
    /// The ARN of your transfer's source location.
    public var sourceLocationArn: Swift.String?
    /// The ARNs of the [network interfaces](https://docs.aws.amazon.com/datasync/latest/userguide/datasync-network.html#required-network-interfaces) that DataSync created for your source location.
    public var sourceNetworkInterfaceArns: [Swift.String]?
    /// The status of your task. For information about what each status means, see [Task statuses](https://docs.aws.amazon.com/datasync/latest/userguide/understand-task-statuses.html#understand-task-creation-statuses).
    public var status: DataSyncClientTypes.TaskStatus?
    /// The ARN of your task.
    public var taskArn: Swift.String?
    /// The task mode that you're using. For more information, see [Choosing a task mode for your data transfer](https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html).
    public var taskMode: DataSyncClientTypes.TaskMode?
    /// The configuration of your task report, which provides detailed information about your DataSync transfer. For more information, see [Monitoring your DataSync transfers with task reports](https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html).
    public var taskReportConfig: DataSyncClientTypes.TaskReportConfig?

    public init(
        cloudWatchLogGroupArn: Swift.String? = nil,
        creationTime: Foundation.Date? = nil,
        currentTaskExecutionArn: Swift.String? = nil,
        destinationLocationArn: Swift.String? = nil,
        destinationNetworkInterfaceArns: [Swift.String]? = nil,
        errorCode: Swift.String? = nil,
        errorDetail: Swift.String? = nil,
        excludes: [DataSyncClientTypes.FilterRule]? = nil,
        includes: [DataSyncClientTypes.FilterRule]? = nil,
        manifestConfig: DataSyncClientTypes.ManifestConfig? = nil,
        name: Swift.String? = nil,
        options: DataSyncClientTypes.Options? = nil,
        schedule: DataSyncClientTypes.TaskSchedule? = nil,
        scheduleDetails: DataSyncClientTypes.TaskScheduleDetails? = nil,
        sourceLocationArn: Swift.String? = nil,
        sourceNetworkInterfaceArns: [Swift.String]? = nil,
        status: DataSyncClientTypes.TaskStatus? = nil,
        taskArn: Swift.String? = nil,
        taskMode: DataSyncClientTypes.TaskMode? = nil,
        taskReportConfig: DataSyncClientTypes.TaskReportConfig? = nil
    )
    {
        self.cloudWatchLogGroupArn = cloudWatchLogGroupArn
        self.creationTime = creationTime
        self.currentTaskExecutionArn = currentTaskExecutionArn
        self.destinationLocationArn = destinationLocationArn
        self.destinationNetworkInterfaceArns = destinationNetworkInterfaceArns
        self.errorCode = errorCode
        self.errorDetail = errorDetail
        self.excludes = excludes
        self.includes = includes
        self.manifestConfig = manifestConfig
        self.name = name
        self.options = options
        self.schedule = schedule
        self.scheduleDetails = scheduleDetails
        self.sourceLocationArn = sourceLocationArn
        self.sourceNetworkInterfaceArns = sourceNetworkInterfaceArns
        self.status = status
        self.taskArn = taskArn
        self.taskMode = taskMode
        self.taskReportConfig = taskReportConfig
    }
}

/// DescribeTaskExecutionRequest
public struct DescribeTaskExecutionInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the task execution that you want information about.
    /// This member is required.
    public var taskExecutionArn: Swift.String?

    public init(
        taskExecutionArn: Swift.String? = nil
    )
    {
        self.taskExecutionArn = taskExecutionArn
    }
}

extension DataSyncClientTypes {

    /// The number of objects that DataSync fails to prepare, transfer, verify, and delete during your task execution. Applies only to [Enhanced mode tasks](https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html).
    public struct TaskExecutionFilesFailedDetail: Swift.Sendable {
        /// The number of objects that DataSync fails to delete during your task execution.
        public var delete: Swift.Int
        /// The number of objects that DataSync fails to prepare during your task execution.
        public var prepare: Swift.Int
        /// The number of objects that DataSync fails to transfer during your task execution.
        public var transfer: Swift.Int
        /// The number of objects that DataSync fails to verify during your task execution.
        public var verify: Swift.Int

        public init(
            delete: Swift.Int = 0,
            prepare: Swift.Int = 0,
            transfer: Swift.Int = 0,
            verify: Swift.Int = 0
        )
        {
            self.delete = delete
            self.prepare = prepare
            self.transfer = transfer
            self.verify = verify
        }
    }
}

extension DataSyncClientTypes {

    /// The number of objects that DataSync finds at your locations. Applies only to [Enhanced mode tasks](https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html).
    public struct TaskExecutionFilesListedDetail: Swift.Sendable {
        /// The number of objects that DataSync finds at your destination location. This counter is only applicable if you [configure your task](https://docs.aws.amazon.com/datasync/latest/userguide/configure-metadata.html#task-option-file-object-handling) to delete data in the destination that isn't in the source.
        public var atDestinationForDelete: Swift.Int
        /// The number of objects that DataSync finds at your source location.
        ///
        /// * With a [manifest](https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html), DataSync lists only what's in your manifest (and not everything at your source location).
        ///
        /// * With an include [filter](https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html), DataSync lists only what matches the filter at your source location.
        ///
        /// * With an exclude filter, DataSync lists everything at your source location before applying the filter.
        public var atSource: Swift.Int

        public init(
            atDestinationForDelete: Swift.Int = 0,
            atSource: Swift.Int = 0
        )
        {
            self.atDestinationForDelete = atDestinationForDelete
            self.atSource = atSource
        }
    }
}

extension DataSyncClientTypes {

    public enum PhaseStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case error
        case pending
        case success
        case sdkUnknown(Swift.String)

        public static var allCases: [PhaseStatus] {
            return [
                .error,
                .pending,
                .success
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .error: return "ERROR"
            case .pending: return "PENDING"
            case .success: return "SUCCESS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// Indicates whether DataSync created a complete [task report](https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html) for your transfer.
    public struct ReportResult: Swift.Sendable {
        /// Indicates the code associated with the error if DataSync can't create a complete report.
        public var errorCode: Swift.String?
        /// Provides details about issues creating a report.
        public var errorDetail: Swift.String?
        /// Indicates whether DataSync is still working on your report, created a report, or can't create a complete report.
        public var status: DataSyncClientTypes.PhaseStatus?

        public init(
            errorCode: Swift.String? = nil,
            errorDetail: Swift.String? = nil,
            status: DataSyncClientTypes.PhaseStatus? = nil
        )
        {
            self.errorCode = errorCode
            self.errorDetail = errorDetail
            self.status = status
        }
    }
}

extension DataSyncClientTypes {

    /// Provides detailed information about the result of your DataSync task execution.
    public struct TaskExecutionResultDetail: Swift.Sendable {
        /// An error that DataSync encountered during your task execution. You can use this information to help [troubleshoot issues](https://docs.aws.amazon.com/datasync/latest/userguide/troubleshooting-datasync-locations-tasks.html).
        public var errorCode: Swift.String?
        /// The detailed description of an error that DataSync encountered during your task execution. You can use this information to help [troubleshoot issues](https://docs.aws.amazon.com/datasync/latest/userguide/troubleshooting-datasync-locations-tasks.html).
        public var errorDetail: Swift.String?
        /// The time in milliseconds that your task execution was in the PREPARING step. For more information, see [Task execution statuses](https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#understand-task-execution-statuses). For Enhanced mode tasks, the value is always 0. For more information, see [How DataSync prepares your data transfer](https://docs.aws.amazon.com/datasync/latest/userguide/how-datasync-transfer-works.html#how-datasync-prepares).
        public var prepareDuration: Swift.Int?
        /// The status of the PREPARING step for your task execution. For more information, see [Task execution statuses](https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#understand-task-execution-statuses).
        public var prepareStatus: DataSyncClientTypes.PhaseStatus?
        /// The time in milliseconds that your task execution ran.
        public var totalDuration: Swift.Int?
        /// The time in milliseconds that your task execution was in the TRANSFERRING step. For more information, see [Task execution statuses](https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#understand-task-execution-statuses). For Enhanced mode tasks, the value is always 0. For more information, see [How DataSync transfers your data](https://docs.aws.amazon.com/datasync/latest/userguide/how-datasync-transfer-works.html#how-datasync-transfers).
        public var transferDuration: Swift.Int?
        /// The status of the TRANSFERRING step for your task execution. For more information, see [Task execution statuses](https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#understand-task-execution-statuses).
        public var transferStatus: DataSyncClientTypes.PhaseStatus?
        /// The time in milliseconds that your task execution was in the VERIFYING step. For more information, see [Task execution statuses](https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#understand-task-execution-statuses). For Enhanced mode tasks, the value is always 0. For more information, see [How DataSync verifies your data's integrity](https://docs.aws.amazon.com/datasync/latest/userguide/how-datasync-transfer-works.html#how-verifying-works).
        public var verifyDuration: Swift.Int?
        /// The status of the VERIFYING step for your task execution. For more information, see [Task execution statuses](https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#understand-task-execution-statuses).
        public var verifyStatus: DataSyncClientTypes.PhaseStatus?

        public init(
            errorCode: Swift.String? = nil,
            errorDetail: Swift.String? = nil,
            prepareDuration: Swift.Int? = nil,
            prepareStatus: DataSyncClientTypes.PhaseStatus? = nil,
            totalDuration: Swift.Int? = nil,
            transferDuration: Swift.Int? = nil,
            transferStatus: DataSyncClientTypes.PhaseStatus? = nil,
            verifyDuration: Swift.Int? = nil,
            verifyStatus: DataSyncClientTypes.PhaseStatus? = nil
        )
        {
            self.errorCode = errorCode
            self.errorDetail = errorDetail
            self.prepareDuration = prepareDuration
            self.prepareStatus = prepareStatus
            self.totalDuration = totalDuration
            self.transferDuration = transferDuration
            self.transferStatus = transferStatus
            self.verifyDuration = verifyDuration
            self.verifyStatus = verifyStatus
        }
    }
}

extension DataSyncClientTypes {

    public enum TaskExecutionStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cancelling
        case error
        case launching
        case preparing
        case queued
        case success
        case transferring
        case verifying
        case sdkUnknown(Swift.String)

        public static var allCases: [TaskExecutionStatus] {
            return [
                .cancelling,
                .error,
                .launching,
                .preparing,
                .queued,
                .success,
                .transferring,
                .verifying
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cancelling: return "CANCELLING"
            case .error: return "ERROR"
            case .launching: return "LAUNCHING"
            case .preparing: return "PREPARING"
            case .queued: return "QUEUED"
            case .success: return "SUCCESS"
            case .transferring: return "TRANSFERRING"
            case .verifying: return "VERIFYING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// DescribeTaskExecutionResponse
public struct DescribeTaskExecutionOutput: Swift.Sendable {
    /// The number of physical bytes that DataSync transfers over the network after compression (if compression is possible). This number is typically less than [BytesTransferred](https://docs.aws.amazon.com/datasync/latest/userguide/API_DescribeTaskExecution.html#DataSync-DescribeTaskExecution-response-BytesTransferred) unless the data isn't compressible.
    public var bytesCompressed: Swift.Int
    /// The number of bytes that DataSync sends to the network before compression (if compression is possible). For the number of bytes transferred over the network, see [BytesCompressed](https://docs.aws.amazon.com/datasync/latest/userguide/API_DescribeTaskExecution.html#DataSync-DescribeTaskExecution-response-BytesCompressed).
    public var bytesTransferred: Swift.Int
    /// The number of logical bytes that DataSync actually writes to the destination location.
    public var bytesWritten: Swift.Int
    /// The number of logical bytes that DataSync expects to write to the destination location.
    public var estimatedBytesToTransfer: Swift.Int
    /// The number of files, objects, and directories that DataSync expects to delete in your destination location. If you don't configure your task to [delete data in the destination that isn't in the source](https://docs.aws.amazon.com/datasync/latest/userguide/configure-metadata.html), the value is always 0.
    public var estimatedFilesToDelete: Swift.Int
    /// The number of files, objects, and directories that DataSync expects to transfer over the network. This value is calculated while DataSync [prepares](https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#understand-task-execution-statuses) the transfer. How this gets calculated depends primarily on your task’s [transfer mode](https://docs.aws.amazon.com/datasync/latest/userguide/API_Options.html#DataSync-Type-Options-TransferMode) configuration:
    ///
    /// * If TranserMode is set to CHANGED - The calculation is based on comparing the content of the source and destination locations and determining the difference that needs to be transferred. The difference can include:
    ///
    /// * Anything that's added or modified at the source location.
    ///
    /// * Anything that's in both locations and modified at the destination after an initial transfer (unless [OverwriteMode](https://docs.aws.amazon.com/datasync/latest/userguide/API_Options.html#DataSync-Type-Options-OverwriteMode) is set to NEVER).
    ///
    /// * (Basic task mode only) The number of items that DataSync expects to delete (if [PreserveDeletedFiles](https://docs.aws.amazon.com/datasync/latest/userguide/API_Options.html#DataSync-Type-Options-PreserveDeletedFiles) is set to REMOVE).
    ///
    ///
    ///
    ///
    /// * If TranserMode is set to ALL - The calculation is based only on the items that DataSync finds at the source location.
    public var estimatedFilesToTransfer: Swift.Int
    /// A list of filter rules that exclude specific data during your transfer. For more information and examples, see [Filtering data transferred by DataSync](https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html).
    public var excludes: [DataSyncClientTypes.FilterRule]?
    /// The number of files, objects, and directories that DataSync actually deletes in your destination location. If you don't configure your task to [delete data in the destination that isn't in the source](https://docs.aws.amazon.com/datasync/latest/userguide/configure-metadata.html), the value is always 0.
    public var filesDeleted: Swift.Int
    /// The number of objects that DataSync fails to prepare, transfer, verify, and delete during your task execution. Applies only to [Enhanced mode tasks](https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html).
    public var filesFailed: DataSyncClientTypes.TaskExecutionFilesFailedDetail?
    /// The number of objects that DataSync finds at your locations. Applies only to [Enhanced mode tasks](https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html).
    public var filesListed: DataSyncClientTypes.TaskExecutionFilesListedDetail?
    /// The number of objects that DataSync will attempt to transfer after comparing your source and destination locations. Applies only to [Enhanced mode tasks](https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html). This counter isn't applicable if you configure your task to [transfer all data](https://docs.aws.amazon.com/datasync/latest/userguide/configure-metadata.html#task-option-transfer-mode). In that scenario, DataSync copies everything from the source to the destination without comparing differences between the locations.
    public var filesPrepared: Swift.Int
    /// The number of files, objects, and directories that DataSync skips during your transfer.
    public var filesSkipped: Swift.Int
    /// The number of files, objects, and directories that DataSync actually transfers over the network. This value is updated periodically during your task execution when something is read from the source and sent over the network. If DataSync fails to transfer something, this value can be less than EstimatedFilesToTransfer. In some cases, this value can also be greater than EstimatedFilesToTransfer. This element is implementation-specific for some location types, so don't use it as an exact indication of what's transferring or to monitor your task execution.
    public var filesTransferred: Swift.Int
    /// The number of files, objects, and directories that DataSync verifies during your transfer. When you configure your task to [verify only the data that's transferred](https://docs.aws.amazon.com/datasync/latest/userguide/configure-data-verification-options.html), DataSync doesn't verify directories in some situations or files that fail to transfer.
    public var filesVerified: Swift.Int
    /// A list of filter rules that include specific data during your transfer. For more information and examples, see [Filtering data transferred by DataSync](https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html).
    public var includes: [DataSyncClientTypes.FilterRule]?
    /// The configuration of the manifest that lists the files or objects to transfer. For more information, see [Specifying what DataSync transfers by using a manifest](https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html).
    public var manifestConfig: DataSyncClientTypes.ManifestConfig?
    /// Indicates how your transfer task is configured. These options include how DataSync handles files, objects, and their associated metadata during your transfer. You also can specify how to verify data integrity, set bandwidth limits for your task, among other options. Each option has a default value. Unless you need to, you don't have to configure any option before calling [StartTaskExecution](https://docs.aws.amazon.com/datasync/latest/userguide/API_StartTaskExecution.html). You also can override your task options for each task execution. For example, you might want to adjust the LogLevel for an individual execution.
    public var options: DataSyncClientTypes.Options?
    /// Indicates whether DataSync generated a complete [task report](https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html) for your transfer.
    public var reportResult: DataSyncClientTypes.ReportResult?
    /// The result of the task execution.
    public var result: DataSyncClientTypes.TaskExecutionResultDetail?
    /// The time when the task execution started.
    public var startTime: Foundation.Date?
    /// The status of the task execution.
    public var status: DataSyncClientTypes.TaskExecutionStatus?
    /// The ARN of the task execution that you wanted information about. TaskExecutionArn is hierarchical and includes TaskArn for the task that was executed. For example, a TaskExecution value with the ARN arn:aws:datasync:us-east-1:111222333444:task/task-0208075f79cedf4a2/execution/exec-08ef1e88ec491019b executed the task with the ARN arn:aws:datasync:us-east-1:111222333444:task/task-0208075f79cedf4a2.
    public var taskExecutionArn: Swift.String?
    /// The task mode that you're using. For more information, see [Choosing a task mode for your data transfer](https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html).
    public var taskMode: DataSyncClientTypes.TaskMode?
    /// The configuration of your task report, which provides detailed information about for your DataSync transfer. For more information, see [Creating a task report](https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html).
    public var taskReportConfig: DataSyncClientTypes.TaskReportConfig?

    public init(
        bytesCompressed: Swift.Int = 0,
        bytesTransferred: Swift.Int = 0,
        bytesWritten: Swift.Int = 0,
        estimatedBytesToTransfer: Swift.Int = 0,
        estimatedFilesToDelete: Swift.Int = 0,
        estimatedFilesToTransfer: Swift.Int = 0,
        excludes: [DataSyncClientTypes.FilterRule]? = nil,
        filesDeleted: Swift.Int = 0,
        filesFailed: DataSyncClientTypes.TaskExecutionFilesFailedDetail? = nil,
        filesListed: DataSyncClientTypes.TaskExecutionFilesListedDetail? = nil,
        filesPrepared: Swift.Int = 0,
        filesSkipped: Swift.Int = 0,
        filesTransferred: Swift.Int = 0,
        filesVerified: Swift.Int = 0,
        includes: [DataSyncClientTypes.FilterRule]? = nil,
        manifestConfig: DataSyncClientTypes.ManifestConfig? = nil,
        options: DataSyncClientTypes.Options? = nil,
        reportResult: DataSyncClientTypes.ReportResult? = nil,
        result: DataSyncClientTypes.TaskExecutionResultDetail? = nil,
        startTime: Foundation.Date? = nil,
        status: DataSyncClientTypes.TaskExecutionStatus? = nil,
        taskExecutionArn: Swift.String? = nil,
        taskMode: DataSyncClientTypes.TaskMode? = nil,
        taskReportConfig: DataSyncClientTypes.TaskReportConfig? = nil
    )
    {
        self.bytesCompressed = bytesCompressed
        self.bytesTransferred = bytesTransferred
        self.bytesWritten = bytesWritten
        self.estimatedBytesToTransfer = estimatedBytesToTransfer
        self.estimatedFilesToDelete = estimatedFilesToDelete
        self.estimatedFilesToTransfer = estimatedFilesToTransfer
        self.excludes = excludes
        self.filesDeleted = filesDeleted
        self.filesFailed = filesFailed
        self.filesListed = filesListed
        self.filesPrepared = filesPrepared
        self.filesSkipped = filesSkipped
        self.filesTransferred = filesTransferred
        self.filesVerified = filesVerified
        self.includes = includes
        self.manifestConfig = manifestConfig
        self.options = options
        self.reportResult = reportResult
        self.result = result
        self.startTime = startTime
        self.status = status
        self.taskExecutionArn = taskExecutionArn
        self.taskMode = taskMode
        self.taskReportConfig = taskReportConfig
    }
}

extension DataSyncClientTypes {

    /// The details about a specific DataSync discovery job.
    public struct DiscoveryJobListEntry: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of a discovery job.
        public var discoveryJobArn: Swift.String?
        /// The status of a discovery job. For more information, see [Discovery job statuses](https://docs.aws.amazon.com/datasync/latest/userguide/discovery-job-statuses.html#discovery-job-statuses-table).
        public var status: DataSyncClientTypes.DiscoveryJobStatus?

        public init(
            discoveryJobArn: Swift.String? = nil,
            status: DataSyncClientTypes.DiscoveryJobStatus? = nil
        )
        {
            self.discoveryJobArn = discoveryJobArn
            self.status = status
        }
    }
}

public struct GenerateRecommendationsInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the discovery job that collects information about your on-premises storage system.
    /// This member is required.
    public var discoveryJobArn: Swift.String?
    /// Specifies the universally unique identifiers (UUIDs) of the resources in your storage system that you want recommendations on.
    /// This member is required.
    public var resourceIds: [Swift.String]?
    /// Specifies the type of resource in your storage system that you want recommendations on.
    /// This member is required.
    public var resourceType: DataSyncClientTypes.DiscoveryResourceType?

    public init(
        discoveryJobArn: Swift.String? = nil,
        resourceIds: [Swift.String]? = nil,
        resourceType: DataSyncClientTypes.DiscoveryResourceType? = nil
    )
    {
        self.discoveryJobArn = discoveryJobArn
        self.resourceIds = resourceIds
        self.resourceType = resourceType
    }
}

public struct GenerateRecommendationsOutput: Swift.Sendable {

    public init() { }
}

/// ListAgentsRequest
public struct ListAgentsInput: Swift.Sendable {
    /// Specifies the maximum number of DataSync agents to list in a response. By default, a response shows a maximum of 100 agents.
    public var maxResults: Swift.Int?
    /// Specifies an opaque string that indicates the position to begin the next list of results in the response.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

/// ListAgentsResponse
public struct ListAgentsOutput: Swift.Sendable {
    /// A list of DataSync agents in your Amazon Web Services account in the Amazon Web Services Region specified in the request. The list is ordered by the agents' Amazon Resource Names (ARNs).
    public var agents: [DataSyncClientTypes.AgentListEntry]?
    /// The opaque string that indicates the position to begin the next list of results in the response.
    public var nextToken: Swift.String?

    public init(
        agents: [DataSyncClientTypes.AgentListEntry]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.agents = agents
        self.nextToken = nextToken
    }
}

public struct ListDiscoveryJobsInput: Swift.Sendable {
    /// Specifies how many results you want in the response.
    public var maxResults: Swift.Int?
    /// Specifies an opaque string that indicates the position to begin the next list of results in the response.
    public var nextToken: Swift.String?
    /// Specifies the Amazon Resource Name (ARN) of an on-premises storage system. Use this parameter if you only want to list the discovery jobs that are associated with a specific storage system.
    public var storageSystemArn: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        storageSystemArn: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.storageSystemArn = storageSystemArn
    }
}

public struct ListDiscoveryJobsOutput: Swift.Sendable {
    /// The discovery jobs that you've run.
    public var discoveryJobs: [DataSyncClientTypes.DiscoveryJobListEntry]?
    /// The opaque string that indicates the position to begin the next list of results in the response.
    public var nextToken: Swift.String?

    public init(
        discoveryJobs: [DataSyncClientTypes.DiscoveryJobListEntry]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.discoveryJobs = discoveryJobs
        self.nextToken = nextToken
    }
}

extension DataSyncClientTypes {

    public enum LocationFilterName: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case creationtime
        case locationtype
        case locationuri
        case sdkUnknown(Swift.String)

        public static var allCases: [LocationFilterName] {
            return [
                .creationtime,
                .locationtype,
                .locationuri
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .creationtime: return "CreationTime"
            case .locationtype: return "LocationType"
            case .locationuri: return "LocationUri"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    public enum Operator: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case beginsWith
        case contains
        case eq
        case ge
        case gt
        case `in`
        case le
        case lt
        case ne
        case notContains
        case sdkUnknown(Swift.String)

        public static var allCases: [Operator] {
            return [
                .beginsWith,
                .contains,
                .eq,
                .ge,
                .gt,
                .in,
                .le,
                .lt,
                .ne,
                .notContains
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .beginsWith: return "BeginsWith"
            case .contains: return "Contains"
            case .eq: return "Equals"
            case .ge: return "GreaterThanOrEqual"
            case .gt: return "GreaterThan"
            case .in: return "In"
            case .le: return "LessThanOrEqual"
            case .lt: return "LessThan"
            case .ne: return "NotEquals"
            case .notContains: return "NotContains"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// Narrow down the list of resources returned by ListLocations. For example, to see all your Amazon S3 locations, create a filter using "Name": "LocationType", "Operator": "Equals", and "Values": "S3". For more information, see [filtering resources](https://docs.aws.amazon.com/datasync/latest/userguide/query-resources.html).
    public struct LocationFilter: Swift.Sendable {
        /// The name of the filter being used. Each API call supports a list of filters that are available for it (for example, LocationType for ListLocations).
        /// This member is required.
        public var name: DataSyncClientTypes.LocationFilterName?
        /// The operator that is used to compare filter values (for example, Equals or Contains).
        /// This member is required.
        public var `operator`: DataSyncClientTypes.Operator?
        /// The values that you want to filter for. For example, you might want to display only Amazon S3 locations.
        /// This member is required.
        public var values: [Swift.String]?

        public init(
            name: DataSyncClientTypes.LocationFilterName? = nil,
            `operator`: DataSyncClientTypes.Operator? = nil,
            values: [Swift.String]? = nil
        )
        {
            self.name = name
            self.`operator` = `operator`
            self.values = values
        }
    }
}

/// ListLocationsRequest
public struct ListLocationsInput: Swift.Sendable {
    /// You can use API filters to narrow down the list of resources returned by ListLocations. For example, to retrieve all tasks on a specific source location, you can use ListLocations with filter name LocationType S3 and Operator Equals.
    public var filters: [DataSyncClientTypes.LocationFilter]?
    /// The maximum number of locations to return.
    public var maxResults: Swift.Int?
    /// An opaque string that indicates the position at which to begin the next list of locations.
    public var nextToken: Swift.String?

    public init(
        filters: [DataSyncClientTypes.LocationFilter]? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DataSyncClientTypes {

    /// Represents a single entry in a list of locations. LocationListEntry returns an array that contains a list of locations when the [ListLocations](https://docs.aws.amazon.com/datasync/latest/userguide/API_ListLocations.html) operation is called.
    public struct LocationListEntry: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the location. For Network File System (NFS) or Amazon EFS, the location is the export path. For Amazon S3, the location is the prefix path that you want to mount and use as the root of the location.
        public var locationArn: Swift.String?
        /// Represents a list of URIs of a location. LocationUri returns an array that contains a list of locations when the [ListLocations](https://docs.aws.amazon.com/datasync/latest/userguide/API_ListLocations.html) operation is called. Format: TYPE://GLOBAL_ID/SUBDIR. TYPE designates the type of location (for example, nfs or s3). GLOBAL_ID is the globally unique identifier of the resource that backs the location. An example for EFS is us-east-2.fs-abcd1234. An example for Amazon S3 is the bucket name, such as myBucket. An example for NFS is a valid IPv4 address or a hostname that is compliant with Domain Name Service (DNS). SUBDIR is a valid file system path, delimited by forward slashes as is the *nix convention. For NFS and Amazon EFS, it's the export path to mount the location. For Amazon S3, it's the prefix path that you mount to and treat as the root of the location.
        public var locationUri: Swift.String?

        public init(
            locationArn: Swift.String? = nil,
            locationUri: Swift.String? = nil
        )
        {
            self.locationArn = locationArn
            self.locationUri = locationUri
        }
    }
}

/// ListLocationsResponse
public struct ListLocationsOutput: Swift.Sendable {
    /// An array that contains a list of locations.
    public var locations: [DataSyncClientTypes.LocationListEntry]?
    /// An opaque string that indicates the position at which to begin returning the next list of locations.
    public var nextToken: Swift.String?

    public init(
        locations: [DataSyncClientTypes.LocationListEntry]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.locations = locations
        self.nextToken = nextToken
    }
}

public struct ListStorageSystemsInput: Swift.Sendable {
    /// Specifies how many results you want in the response.
    public var maxResults: Swift.Int?
    /// Specifies an opaque string that indicates the position to begin the next list of results in the response.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DataSyncClientTypes {

    /// Information that identifies an on-premises storage system that you're using with DataSync Discovery.
    public struct StorageSystemListEntry: Swift.Sendable {
        /// The name of an on-premises storage system that you added to DataSync Discovery.
        public var name: Swift.String?
        /// The Amazon Resource Names (ARN) of an on-premises storage system that you added to DataSync Discovery.
        public var storageSystemArn: Swift.String?

        public init(
            name: Swift.String? = nil,
            storageSystemArn: Swift.String? = nil
        )
        {
            self.name = name
            self.storageSystemArn = storageSystemArn
        }
    }
}

public struct ListStorageSystemsOutput: Swift.Sendable {
    /// The opaque string that indicates the position to begin the next list of results in the response.
    public var nextToken: Swift.String?
    /// The Amazon Resource Names ARNs) of the on-premises storage systems that you're using with DataSync Discovery.
    public var storageSystems: [DataSyncClientTypes.StorageSystemListEntry]?

    public init(
        nextToken: Swift.String? = nil,
        storageSystems: [DataSyncClientTypes.StorageSystemListEntry]? = nil
    )
    {
        self.nextToken = nextToken
        self.storageSystems = storageSystems
    }
}

/// ListTagsForResourceRequest
public struct ListTagsForResourceInput: Swift.Sendable {
    /// Specifies how many results that you want in the response.
    public var maxResults: Swift.Int?
    /// Specifies an opaque string that indicates the position to begin the next list of results in the response.
    public var nextToken: Swift.String?
    /// Specifies the Amazon Resource Name (ARN) of the resource that you want tag information on.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        resourceArn: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.resourceArn = resourceArn
    }
}

/// ListTagsForResourceResponse
public struct ListTagsForResourceOutput: Swift.Sendable {
    /// The opaque string that indicates the position to begin the next list of results in the response.
    public var nextToken: Swift.String?
    /// An array of tags applied to the specified resource.
    public var tags: [DataSyncClientTypes.TagListEntry]?

    public init(
        nextToken: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil
    )
    {
        self.nextToken = nextToken
        self.tags = tags
    }
}

/// ListTaskExecutions
public struct ListTaskExecutionsInput: Swift.Sendable {
    /// Specifies how many results you want in the response.
    public var maxResults: Swift.Int?
    /// Specifies an opaque string that indicates the position at which to begin the next list of results in the response.
    public var nextToken: Swift.String?
    /// Specifies the Amazon Resource Name (ARN) of the task that you want execution information about.
    public var taskArn: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        taskArn: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.taskArn = taskArn
    }
}

extension DataSyncClientTypes {

    /// Represents a single entry in a list of DataSync task executions that's returned with the [ListTaskExecutions](https://docs.aws.amazon.com/datasync/latest/userguide/API_ListTaskExecutions.html) operation.
    public struct TaskExecutionListEntry: Swift.Sendable {
        /// The status of a task execution. For more information, see [Task execution statuses](https://docs.aws.amazon.com/datasync/latest/userguide/understand-task-statuses.html#understand-task-execution-statuses).
        public var status: DataSyncClientTypes.TaskExecutionStatus?
        /// The Amazon Resource Name (ARN) of a task execution.
        public var taskExecutionArn: Swift.String?
        /// The task mode that you're using. For more information, see [Choosing a task mode for your data transfer](https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html).
        public var taskMode: DataSyncClientTypes.TaskMode?

        public init(
            status: DataSyncClientTypes.TaskExecutionStatus? = nil,
            taskExecutionArn: Swift.String? = nil,
            taskMode: DataSyncClientTypes.TaskMode? = nil
        )
        {
            self.status = status
            self.taskExecutionArn = taskExecutionArn
            self.taskMode = taskMode
        }
    }
}

/// ListTaskExecutionsResponse
public struct ListTaskExecutionsOutput: Swift.Sendable {
    /// The opaque string that indicates the position to begin the next list of results in the response.
    public var nextToken: Swift.String?
    /// A list of the task's executions.
    public var taskExecutions: [DataSyncClientTypes.TaskExecutionListEntry]?

    public init(
        nextToken: Swift.String? = nil,
        taskExecutions: [DataSyncClientTypes.TaskExecutionListEntry]? = nil
    )
    {
        self.nextToken = nextToken
        self.taskExecutions = taskExecutions
    }
}

extension DataSyncClientTypes {

    public enum TaskFilterName: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case creationtime
        case locationid
        case sdkUnknown(Swift.String)

        public static var allCases: [TaskFilterName] {
            return [
                .creationtime,
                .locationid
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .creationtime: return "CreationTime"
            case .locationid: return "LocationId"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataSyncClientTypes {

    /// You can use API filters to narrow down the list of resources returned by ListTasks. For example, to retrieve all tasks on a source location, you can use ListTasks with filter name LocationId and Operator Equals with the ARN for the location. For more information, see [filtering DataSync resources](https://docs.aws.amazon.com/datasync/latest/userguide/query-resources.html).
    public struct TaskFilter: Swift.Sendable {
        /// The name of the filter being used. Each API call supports a list of filters that are available for it. For example, LocationId for ListTasks.
        /// This member is required.
        public var name: DataSyncClientTypes.TaskFilterName?
        /// The operator that is used to compare filter values (for example, Equals or Contains).
        /// This member is required.
        public var `operator`: DataSyncClientTypes.Operator?
        /// The values that you want to filter for. For example, you might want to display only tasks for a specific destination location.
        /// This member is required.
        public var values: [Swift.String]?

        public init(
            name: DataSyncClientTypes.TaskFilterName? = nil,
            `operator`: DataSyncClientTypes.Operator? = nil,
            values: [Swift.String]? = nil
        )
        {
            self.name = name
            self.`operator` = `operator`
            self.values = values
        }
    }
}

/// ListTasksRequest
public struct ListTasksInput: Swift.Sendable {
    /// You can use API filters to narrow down the list of resources returned by ListTasks. For example, to retrieve all tasks on a specific source location, you can use ListTasks with filter name LocationId and Operator Equals with the ARN for the location.
    public var filters: [DataSyncClientTypes.TaskFilter]?
    /// The maximum number of tasks to return.
    public var maxResults: Swift.Int?
    /// An opaque string that indicates the position at which to begin the next list of tasks.
    public var nextToken: Swift.String?

    public init(
        filters: [DataSyncClientTypes.TaskFilter]? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DataSyncClientTypes {

    /// Represents a single entry in a list of tasks. TaskListEntry returns an array that contains a list of tasks when the [ListTasks](https://docs.aws.amazon.com/datasync/latest/userguide/API_ListTasks.html) operation is called. A task includes the source and destination file systems to sync and the options to use for the tasks.
    public struct TaskListEntry: Swift.Sendable {
        /// The name of the task.
        public var name: Swift.String?
        /// The status of the task.
        public var status: DataSyncClientTypes.TaskStatus?
        /// The Amazon Resource Name (ARN) of the task.
        public var taskArn: Swift.String?
        /// The task mode that you're using. For more information, see [Choosing a task mode for your data transfer](https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html).
        public var taskMode: DataSyncClientTypes.TaskMode?

        public init(
            name: Swift.String? = nil,
            status: DataSyncClientTypes.TaskStatus? = nil,
            taskArn: Swift.String? = nil,
            taskMode: DataSyncClientTypes.TaskMode? = nil
        )
        {
            self.name = name
            self.status = status
            self.taskArn = taskArn
            self.taskMode = taskMode
        }
    }
}

/// ListTasksResponse
public struct ListTasksOutput: Swift.Sendable {
    /// An opaque string that indicates the position at which to begin returning the next list of tasks.
    public var nextToken: Swift.String?
    /// A list of all the tasks that are returned.
    public var tasks: [DataSyncClientTypes.TaskListEntry]?

    public init(
        nextToken: Swift.String? = nil,
        tasks: [DataSyncClientTypes.TaskListEntry]? = nil
    )
    {
        self.nextToken = nextToken
        self.tasks = tasks
    }
}

public struct RemoveStorageSystemInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the storage system that you want to permanently remove from DataSync Discovery.
    /// This member is required.
    public var storageSystemArn: Swift.String?

    public init(
        storageSystemArn: Swift.String? = nil
    )
    {
        self.storageSystemArn = storageSystemArn
    }
}

public struct RemoveStorageSystemOutput: Swift.Sendable {

    public init() { }
}

public struct StartDiscoveryJobInput: Swift.Sendable {
    /// Specifies a client token to make sure requests with this API operation are idempotent. If you don't specify a client token, DataSync generates one for you automatically.
    /// This member is required.
    public var clientToken: Swift.String?
    /// Specifies in minutes how long you want the discovery job to run. For more accurate recommendations, we recommend a duration of at least 14 days. Longer durations allow time to collect a sufficient number of data points and provide a realistic representation of storage performance and utilization.
    /// This member is required.
    public var collectionDurationMinutes: Swift.Int?
    /// Specifies the Amazon Resource Name (ARN) of the on-premises storage system that you want to run the discovery job on.
    /// This member is required.
    public var storageSystemArn: Swift.String?
    /// Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources.
    public var tags: [DataSyncClientTypes.TagListEntry]?

    public init(
        clientToken: Swift.String? = nil,
        collectionDurationMinutes: Swift.Int? = nil,
        storageSystemArn: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil
    )
    {
        self.clientToken = clientToken
        self.collectionDurationMinutes = collectionDurationMinutes
        self.storageSystemArn = storageSystemArn
        self.tags = tags
    }
}

public struct StartDiscoveryJobOutput: Swift.Sendable {
    /// The ARN of the discovery job that you started.
    public var discoveryJobArn: Swift.String?

    public init(
        discoveryJobArn: Swift.String? = nil
    )
    {
        self.discoveryJobArn = discoveryJobArn
    }
}

/// StartTaskExecutionRequest
public struct StartTaskExecutionInput: Swift.Sendable {
    /// Specifies a list of filter rules that determines which files to exclude from a task. The list contains a single filter string that consists of the patterns to exclude. The patterns are delimited by "|" (that is, a pipe), for example, "/folder1|/folder2".
    public var excludes: [DataSyncClientTypes.FilterRule]?
    /// Specifies a list of filter rules that determines which files to include when running a task. The pattern should contain a single filter string that consists of the patterns to include. The patterns are delimited by "|" (that is, a pipe), for example, "/folder1|/folder2".
    public var includes: [DataSyncClientTypes.FilterRule]?
    /// Configures a manifest, which is a list of files or objects that you want DataSync to transfer. For more information and configuration examples, see [Specifying what DataSync transfers by using a manifest](https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html). When using this parameter, your caller identity (the role that you're using DataSync with) must have the iam:PassRole permission. The [AWSDataSyncFullAccess](https://docs.aws.amazon.com/datasync/latest/userguide/security-iam-awsmanpol.html#security-iam-awsmanpol-awsdatasyncfullaccess) policy includes this permission. To remove a manifest configuration, specify this parameter with an empty value.
    public var manifestConfig: DataSyncClientTypes.ManifestConfig?
    /// Indicates how your transfer task is configured. These options include how DataSync handles files, objects, and their associated metadata during your transfer. You also can specify how to verify data integrity, set bandwidth limits for your task, among other options. Each option has a default value. Unless you need to, you don't have to configure any option before calling [StartTaskExecution](https://docs.aws.amazon.com/datasync/latest/userguide/API_StartTaskExecution.html). You also can override your task options for each task execution. For example, you might want to adjust the LogLevel for an individual execution.
    public var overrideOptions: DataSyncClientTypes.Options?
    /// Specifies the tags that you want to apply to the Amazon Resource Name (ARN) representing the task execution. Tags are key-value pairs that help you manage, filter, and search for your DataSync resources.
    public var tags: [DataSyncClientTypes.TagListEntry]?
    /// Specifies the Amazon Resource Name (ARN) of the task that you want to start.
    /// This member is required.
    public var taskArn: Swift.String?
    /// Specifies how you want to configure a task report, which provides detailed information about your DataSync transfer. For more information, see [Monitoring your DataSync transfers with task reports](https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html). When using this parameter, your caller identity (the role that you're using DataSync with) must have the iam:PassRole permission. The [AWSDataSyncFullAccess](https://docs.aws.amazon.com/datasync/latest/userguide/security-iam-awsmanpol.html#security-iam-awsmanpol-awsdatasyncfullaccess) policy includes this permission. To remove a task report configuration, specify this parameter as empty.
    public var taskReportConfig: DataSyncClientTypes.TaskReportConfig?

    public init(
        excludes: [DataSyncClientTypes.FilterRule]? = nil,
        includes: [DataSyncClientTypes.FilterRule]? = nil,
        manifestConfig: DataSyncClientTypes.ManifestConfig? = nil,
        overrideOptions: DataSyncClientTypes.Options? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil,
        taskArn: Swift.String? = nil,
        taskReportConfig: DataSyncClientTypes.TaskReportConfig? = nil
    )
    {
        self.excludes = excludes
        self.includes = includes
        self.manifestConfig = manifestConfig
        self.overrideOptions = overrideOptions
        self.tags = tags
        self.taskArn = taskArn
        self.taskReportConfig = taskReportConfig
    }
}

/// StartTaskExecutionResponse
public struct StartTaskExecutionOutput: Swift.Sendable {
    /// The ARN of the running task execution.
    public var taskExecutionArn: Swift.String?

    public init(
        taskExecutionArn: Swift.String? = nil
    )
    {
        self.taskExecutionArn = taskExecutionArn
    }
}

public struct StopDiscoveryJobInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the discovery job that you want to stop.
    /// This member is required.
    public var discoveryJobArn: Swift.String?

    public init(
        discoveryJobArn: Swift.String? = nil
    )
    {
        self.discoveryJobArn = discoveryJobArn
    }
}

public struct StopDiscoveryJobOutput: Swift.Sendable {

    public init() { }
}

/// TagResourceRequest
public struct TagResourceInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the resource to apply the tag to.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// Specifies the tags that you want to apply to the resource.
    /// This member is required.
    public var tags: [DataSyncClientTypes.TagListEntry]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [DataSyncClientTypes.TagListEntry]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

/// UntagResourceRequest
public struct UntagResourceInput: Swift.Sendable {
    /// Specifies the keys in the tags that you want to remove.
    /// This member is required.
    public var keys: [Swift.String]?
    /// Specifies the Amazon Resource Name (ARN) of the resource to remove the tags from.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        keys: [Swift.String]? = nil,
        resourceArn: Swift.String? = nil
    )
    {
        self.keys = keys
        self.resourceArn = resourceArn
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

/// UpdateAgentRequest
public struct UpdateAgentInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the agent to update.
    /// This member is required.
    public var agentArn: Swift.String?
    /// The name that you want to use to configure the agent.
    public var name: Swift.String?

    public init(
        agentArn: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.agentArn = agentArn
        self.name = name
    }
}

public struct UpdateAgentOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateDiscoveryJobInput: Swift.Sendable {
    /// Specifies in minutes how long that you want the discovery job to run. (You can't set this parameter to less than the number of minutes that the job has already run for.)
    /// This member is required.
    public var collectionDurationMinutes: Swift.Int?
    /// Specifies the Amazon Resource Name (ARN) of the discovery job that you want to update.
    /// This member is required.
    public var discoveryJobArn: Swift.String?

    public init(
        collectionDurationMinutes: Swift.Int? = nil,
        discoveryJobArn: Swift.String? = nil
    )
    {
        self.collectionDurationMinutes = collectionDurationMinutes
        self.discoveryJobArn = discoveryJobArn
    }
}

public struct UpdateDiscoveryJobOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateLocationAzureBlobInput: Swift.Sendable {
    /// Specifies the access tier that you want your objects or files transferred into. This only applies when using the location as a transfer destination. For more information, see [Access tiers](https://docs.aws.amazon.com/datasync/latest/userguide/creating-azure-blob-location.html#azure-blob-access-tiers).
    public var accessTier: DataSyncClientTypes.AzureAccessTier?
    /// Specifies the Amazon Resource Name (ARN) of the DataSync agent that can connect with your Azure Blob Storage container. You can specify more than one agent. For more information, see [Using multiple agents for your transfer](https://docs.aws.amazon.com/datasync/latest/userguide/multiple-agents.html).
    public var agentArns: [Swift.String]?
    /// Specifies the authentication method DataSync uses to access your Azure Blob Storage. DataSync can access blob storage using a shared access signature (SAS).
    public var authenticationType: DataSyncClientTypes.AzureBlobAuthenticationType?
    /// Specifies the type of blob that you want your objects or files to be when transferring them into Azure Blob Storage. Currently, DataSync only supports moving data into Azure Blob Storage as block blobs. For more information on blob types, see the [Azure Blob Storage documentation](https://learn.microsoft.com/en-us/rest/api/storageservices/understanding-block-blobs--append-blobs--and-page-blobs).
    public var blobType: DataSyncClientTypes.AzureBlobType?
    /// Specifies the ARN of the Azure Blob Storage transfer location that you're updating.
    /// This member is required.
    public var locationArn: Swift.String?
    /// Specifies the SAS configuration that allows DataSync to access your Azure Blob Storage.
    public var sasConfiguration: DataSyncClientTypes.AzureBlobSasConfiguration?
    /// Specifies path segments if you want to limit your transfer to a virtual directory in your container (for example, /my/images).
    public var subdirectory: Swift.String?

    public init(
        accessTier: DataSyncClientTypes.AzureAccessTier? = nil,
        agentArns: [Swift.String]? = nil,
        authenticationType: DataSyncClientTypes.AzureBlobAuthenticationType? = nil,
        blobType: DataSyncClientTypes.AzureBlobType? = nil,
        locationArn: Swift.String? = nil,
        sasConfiguration: DataSyncClientTypes.AzureBlobSasConfiguration? = nil,
        subdirectory: Swift.String? = nil
    )
    {
        self.accessTier = accessTier
        self.agentArns = agentArns
        self.authenticationType = authenticationType
        self.blobType = blobType
        self.locationArn = locationArn
        self.sasConfiguration = sasConfiguration
        self.subdirectory = subdirectory
    }
}

public struct UpdateLocationAzureBlobOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateLocationEfsInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the access point that DataSync uses to mount your Amazon EFS file system. For more information, see [Accessing restricted Amazon EFS file systems](https://docs.aws.amazon.com/datasync/latest/userguide/create-efs-location.html#create-efs-location-iam).
    public var accessPointArn: Swift.String?
    /// Specifies an Identity and Access Management (IAM) role that allows DataSync to access your Amazon EFS file system. For information on creating this role, see [Creating a DataSync IAM role for Amazon EFS file system access](https://docs.aws.amazon.com/datasync/latest/userguide/create-efs-location.html#create-efs-location-iam-role).
    public var fileSystemAccessRoleArn: Swift.String?
    /// Specifies whether you want DataSync to use Transport Layer Security (TLS) 1.2 encryption when it transfers data to or from your Amazon EFS file system. If you specify an access point using AccessPointArn or an IAM role using FileSystemAccessRoleArn, you must set this parameter to TLS1_2.
    public var inTransitEncryption: DataSyncClientTypes.EfsInTransitEncryption?
    /// Specifies the Amazon Resource Name (ARN) of the Amazon EFS transfer location that you're updating.
    /// This member is required.
    public var locationArn: Swift.String?
    /// Specifies a mount path for your Amazon EFS file system. This is where DataSync reads or writes data on your file system (depending on if this is a source or destination location). By default, DataSync uses the root directory (or [access point](https://docs.aws.amazon.com/efs/latest/ug/efs-access-points.html) if you provide one by using AccessPointArn). You can also include subdirectories using forward slashes (for example, /path/to/folder).
    public var subdirectory: Swift.String?

    public init(
        accessPointArn: Swift.String? = nil,
        fileSystemAccessRoleArn: Swift.String? = nil,
        inTransitEncryption: DataSyncClientTypes.EfsInTransitEncryption? = nil,
        locationArn: Swift.String? = nil,
        subdirectory: Swift.String? = nil
    )
    {
        self.accessPointArn = accessPointArn
        self.fileSystemAccessRoleArn = fileSystemAccessRoleArn
        self.inTransitEncryption = inTransitEncryption
        self.locationArn = locationArn
        self.subdirectory = subdirectory
    }
}

public struct UpdateLocationEfsOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateLocationFsxLustreInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the FSx for Lustre transfer location that you're updating.
    /// This member is required.
    public var locationArn: Swift.String?
    /// Specifies a mount path for your FSx for Lustre file system. The path can include subdirectories. When the location is used as a source, DataSync reads data from the mount path. When the location is used as a destination, DataSync writes data to the mount path. If you don't include this parameter, DataSync uses the file system's root directory (/).
    public var subdirectory: Swift.String?

    public init(
        locationArn: Swift.String? = nil,
        subdirectory: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
        self.subdirectory = subdirectory
    }
}

public struct UpdateLocationFsxLustreOutput: Swift.Sendable {

    public init() { }
}

extension DataSyncClientTypes {

    /// Specifies the Server Message Block (SMB) protocol configuration that DataSync uses to access your Amazon FSx for NetApp ONTAP file system's storage virtual machine (SVM). For more information, see [Providing DataSync access to FSx for ONTAP file systems](https://docs.aws.amazon.com/datasync/latest/userguide/create-ontap-location.html#create-ontap-location-access).
    public struct FsxUpdateProtocolSmb: Swift.Sendable {
        /// Specifies the name of the Windows domain that your storage virtual machine (SVM) belongs to. If you have multiple Active Directory domains in your environment, configuring this parameter makes sure that DataSync connects to the right SVM.
        public var domain: Swift.String?
        /// Specifies the version of the Server Message Block (SMB) protocol that DataSync uses to access an SMB file server.
        public var mountOptions: DataSyncClientTypes.SmbMountOptions?
        /// Specifies the password of a user who has permission to access your SVM.
        public var password: Swift.String?
        /// Specifies a user that can mount and access the files, folders, and metadata in your SVM. For information about choosing a user with the right level of access for your transfer, see [Using the SMB protocol](https://docs.aws.amazon.com/datasync/latest/userguide/create-ontap-location.html#create-ontap-location-smb).
        public var user: Swift.String?

        public init(
            domain: Swift.String? = nil,
            mountOptions: DataSyncClientTypes.SmbMountOptions? = nil,
            password: Swift.String? = nil,
            user: Swift.String? = nil
        )
        {
            self.domain = domain
            self.mountOptions = mountOptions
            self.password = password
            self.user = user
        }
    }
}

extension DataSyncClientTypes.FsxUpdateProtocolSmb: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "FsxUpdateProtocolSmb(domain: \(Swift.String(describing: domain)), mountOptions: \(Swift.String(describing: mountOptions)), user: \(Swift.String(describing: user)), password: \"CONTENT_REDACTED\")"}
}

extension DataSyncClientTypes {

    /// Specifies the data transfer protocol that DataSync uses to access your Amazon FSx file system. You can't update the Network File System (NFS) protocol configuration for FSx for ONTAP locations. DataSync currently only supports NFS version 3 with this location type.
    public struct FsxUpdateProtocol: Swift.Sendable {
        /// Specifies the Network File System (NFS) protocol configuration that DataSync uses to access your FSx for OpenZFS file system or FSx for ONTAP file system's storage virtual machine (SVM).
        public var nfs: DataSyncClientTypes.FsxProtocolNfs?
        /// Specifies the Server Message Block (SMB) protocol configuration that DataSync uses to access your FSx for ONTAP file system's storage virtual machine (SVM).
        public var smb: DataSyncClientTypes.FsxUpdateProtocolSmb?

        public init(
            nfs: DataSyncClientTypes.FsxProtocolNfs? = nil,
            smb: DataSyncClientTypes.FsxUpdateProtocolSmb? = nil
        )
        {
            self.nfs = nfs
            self.smb = smb
        }
    }
}

public struct UpdateLocationFsxOntapInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the FSx for ONTAP transfer location that you're updating.
    /// This member is required.
    public var locationArn: Swift.String?
    /// Specifies the data transfer protocol that DataSync uses to access your Amazon FSx file system.
    public var `protocol`: DataSyncClientTypes.FsxUpdateProtocol?
    /// Specifies a path to the file share in the storage virtual machine (SVM) where you want to transfer data to or from. You can specify a junction path (also known as a mount point), qtree path (for NFS file shares), or share name (for SMB file shares). For example, your mount path might be /vol1, /vol1/tree1, or /share1. Don't specify a junction path in the SVM's root volume. For more information, see [Managing FSx for ONTAP storage virtual machines](https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/managing-svms.html) in the Amazon FSx for NetApp ONTAP User Guide.
    public var subdirectory: Swift.String?

    public init(
        locationArn: Swift.String? = nil,
        `protocol`: DataSyncClientTypes.FsxUpdateProtocol? = nil,
        subdirectory: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
        self.`protocol` = `protocol`
        self.subdirectory = subdirectory
    }
}

public struct UpdateLocationFsxOntapOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateLocationFsxOpenZfsInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the FSx for OpenZFS transfer location that you're updating.
    /// This member is required.
    public var locationArn: Swift.String?
    /// Specifies the data transfer protocol that DataSync uses to access your Amazon FSx file system.
    public var `protocol`: DataSyncClientTypes.FsxProtocol?
    /// Specifies a subdirectory in the location's path that must begin with /fsx. DataSync uses this subdirectory to read or write data (depending on whether the file system is a source or destination location).
    public var subdirectory: Swift.String?

    public init(
        locationArn: Swift.String? = nil,
        `protocol`: DataSyncClientTypes.FsxProtocol? = nil,
        subdirectory: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
        self.`protocol` = `protocol`
        self.subdirectory = subdirectory
    }
}

public struct UpdateLocationFsxOpenZfsOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateLocationFsxWindowsInput: Swift.Sendable {
    /// Specifies the name of the Windows domain that your FSx for Windows File Server file system belongs to. If you have multiple Active Directory domains in your environment, configuring this parameter makes sure that DataSync connects to the right file system.
    public var domain: Swift.String?
    /// Specifies the ARN of the FSx for Windows File Server transfer location that you're updating.
    /// This member is required.
    public var locationArn: Swift.String?
    /// Specifies the password of the user with the permissions to mount and access the files, folders, and file metadata in your FSx for Windows File Server file system.
    public var password: Swift.String?
    /// Specifies a mount path for your file system using forward slashes. DataSync uses this subdirectory to read or write data (depending on whether the file system is a source or destination location).
    public var subdirectory: Swift.String?
    /// Specifies the user with the permissions to mount and access the files, folders, and file metadata in your FSx for Windows File Server file system. For information about choosing a user with the right level of access for your transfer, see [required permissions](https://docs.aws.amazon.com/datasync/latest/userguide/create-fsx-location.html#create-fsx-windows-location-permissions) for FSx for Windows File Server locations.
    public var user: Swift.String?

    public init(
        domain: Swift.String? = nil,
        locationArn: Swift.String? = nil,
        password: Swift.String? = nil,
        subdirectory: Swift.String? = nil,
        user: Swift.String? = nil
    )
    {
        self.domain = domain
        self.locationArn = locationArn
        self.password = password
        self.subdirectory = subdirectory
        self.user = user
    }
}

extension UpdateLocationFsxWindowsInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "UpdateLocationFsxWindowsInput(domain: \(Swift.String(describing: domain)), locationArn: \(Swift.String(describing: locationArn)), subdirectory: \(Swift.String(describing: subdirectory)), user: \(Swift.String(describing: user)), password: \"CONTENT_REDACTED\")"}
}

public struct UpdateLocationFsxWindowsOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateLocationHdfsInput: Swift.Sendable {
    /// The Amazon Resource Names (ARNs) of the DataSync agents that can connect to your HDFS cluster.
    public var agentArns: [Swift.String]?
    /// The type of authentication used to determine the identity of the user.
    public var authenticationType: DataSyncClientTypes.HdfsAuthenticationType?
    /// The size of the data blocks to write into the HDFS cluster.
    public var blockSize: Swift.Int?
    /// The Kerberos key table (keytab) that contains mappings between the defined Kerberos principal and the encrypted keys. You can load the keytab from a file by providing the file's address. If you use the CLI, it performs base64 encoding for you. Otherwise, provide the base64-encoded text.
    public var kerberosKeytab: Foundation.Data?
    /// The krb5.conf file that contains the Kerberos configuration information. You can load the krb5.conf file by providing the file's address. If you're using the CLI, it performs the base64 encoding for you. Otherwise, provide the base64-encoded text.
    public var kerberosKrb5Conf: Foundation.Data?
    /// The Kerberos principal with access to the files and folders on the HDFS cluster.
    public var kerberosPrincipal: Swift.String?
    /// The URI of the HDFS cluster's Key Management Server (KMS).
    public var kmsKeyProviderUri: Swift.String?
    /// The Amazon Resource Name (ARN) of the source HDFS cluster location.
    /// This member is required.
    public var locationArn: Swift.String?
    /// The NameNode that manages the HDFS namespace. The NameNode performs operations such as opening, closing, and renaming files and directories. The NameNode contains the information to map blocks of data to the DataNodes. You can use only one NameNode.
    public var nameNodes: [DataSyncClientTypes.HdfsNameNode]?
    /// The Quality of Protection (QOP) configuration specifies the Remote Procedure Call (RPC) and data transfer privacy settings configured on the Hadoop Distributed File System (HDFS) cluster.
    public var qopConfiguration: DataSyncClientTypes.QopConfiguration?
    /// The number of DataNodes to replicate the data to when writing to the HDFS cluster.
    public var replicationFactor: Swift.Int?
    /// The user name used to identify the client on the host operating system.
    public var simpleUser: Swift.String?
    /// A subdirectory in the HDFS cluster. This subdirectory is used to read data from or write data to the HDFS cluster.
    public var subdirectory: Swift.String?

    public init(
        agentArns: [Swift.String]? = nil,
        authenticationType: DataSyncClientTypes.HdfsAuthenticationType? = nil,
        blockSize: Swift.Int? = nil,
        kerberosKeytab: Foundation.Data? = nil,
        kerberosKrb5Conf: Foundation.Data? = nil,
        kerberosPrincipal: Swift.String? = nil,
        kmsKeyProviderUri: Swift.String? = nil,
        locationArn: Swift.String? = nil,
        nameNodes: [DataSyncClientTypes.HdfsNameNode]? = nil,
        qopConfiguration: DataSyncClientTypes.QopConfiguration? = nil,
        replicationFactor: Swift.Int? = nil,
        simpleUser: Swift.String? = nil,
        subdirectory: Swift.String? = nil
    )
    {
        self.agentArns = agentArns
        self.authenticationType = authenticationType
        self.blockSize = blockSize
        self.kerberosKeytab = kerberosKeytab
        self.kerberosKrb5Conf = kerberosKrb5Conf
        self.kerberosPrincipal = kerberosPrincipal
        self.kmsKeyProviderUri = kmsKeyProviderUri
        self.locationArn = locationArn
        self.nameNodes = nameNodes
        self.qopConfiguration = qopConfiguration
        self.replicationFactor = replicationFactor
        self.simpleUser = simpleUser
        self.subdirectory = subdirectory
    }
}

public struct UpdateLocationHdfsOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateLocationNfsInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the NFS transfer location that you want to update.
    /// This member is required.
    public var locationArn: Swift.String?
    /// Specifies how DataSync can access a location using the NFS protocol.
    public var mountOptions: DataSyncClientTypes.NfsMountOptions?
    /// The DataSync agents that can connect to your Network File System (NFS) file server.
    public var onPremConfig: DataSyncClientTypes.OnPremConfig?
    /// Specifies the export path in your NFS file server that you want DataSync to mount. This path (or a subdirectory of the path) is where DataSync transfers data to or from. For information on configuring an export for DataSync, see [Accessing NFS file servers](https://docs.aws.amazon.com/datasync/latest/userguide/create-nfs-location.html#accessing-nfs).
    public var subdirectory: Swift.String?

    public init(
        locationArn: Swift.String? = nil,
        mountOptions: DataSyncClientTypes.NfsMountOptions? = nil,
        onPremConfig: DataSyncClientTypes.OnPremConfig? = nil,
        subdirectory: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
        self.mountOptions = mountOptions
        self.onPremConfig = onPremConfig
        self.subdirectory = subdirectory
    }
}

public struct UpdateLocationNfsOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateLocationObjectStorageInput: Swift.Sendable {
    /// Specifies the access key (for example, a user name) if credentials are required to authenticate with the object storage server.
    public var accessKey: Swift.String?
    /// Specifies the Amazon Resource Names (ARNs) of the DataSync agents that can connect with your object storage system.
    public var agentArns: [Swift.String]?
    /// Specifies the ARN of the object storage system location that you're updating.
    /// This member is required.
    public var locationArn: Swift.String?
    /// Specifies the secret key (for example, a password) if credentials are required to authenticate with the object storage server.
    public var secretKey: Swift.String?
    /// Specifies a certificate chain for DataSync to authenticate with your object storage system if the system uses a private or self-signed certificate authority (CA). You must specify a single .pem file with a full certificate chain (for example, file:///home/<USER>/.ssh/object_storage_certificates.pem). The certificate chain might include:
    ///
    /// * The object storage system's certificate
    ///
    /// * All intermediate certificates (if there are any)
    ///
    /// * The root certificate of the signing CA
    ///
    ///
    /// You can concatenate your certificates into a .pem file (which can be up to 32768 bytes before base64 encoding). The following example cat command creates an object_storage_certificates.pem file that includes three certificates: cat object_server_certificate.pem intermediate_certificate.pem ca_root_certificate.pem > object_storage_certificates.pem To use this parameter, configure ServerProtocol to HTTPS. Updating this parameter doesn't interfere with tasks that you have in progress.
    public var serverCertificate: Foundation.Data?
    /// Specifies the port that your object storage server accepts inbound network traffic on (for example, port 443).
    public var serverPort: Swift.Int?
    /// Specifies the protocol that your object storage server uses to communicate.
    public var serverProtocol: DataSyncClientTypes.ObjectStorageServerProtocol?
    /// Specifies the object prefix for your object storage server. If this is a source location, DataSync only copies objects with this prefix. If this is a destination location, DataSync writes all objects with this prefix.
    public var subdirectory: Swift.String?

    public init(
        accessKey: Swift.String? = nil,
        agentArns: [Swift.String]? = nil,
        locationArn: Swift.String? = nil,
        secretKey: Swift.String? = nil,
        serverCertificate: Foundation.Data? = nil,
        serverPort: Swift.Int? = nil,
        serverProtocol: DataSyncClientTypes.ObjectStorageServerProtocol? = nil,
        subdirectory: Swift.String? = nil
    )
    {
        self.accessKey = accessKey
        self.agentArns = agentArns
        self.locationArn = locationArn
        self.secretKey = secretKey
        self.serverCertificate = serverCertificate
        self.serverPort = serverPort
        self.serverProtocol = serverProtocol
        self.subdirectory = subdirectory
    }
}

extension UpdateLocationObjectStorageInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "UpdateLocationObjectStorageInput(accessKey: \(Swift.String(describing: accessKey)), agentArns: \(Swift.String(describing: agentArns)), locationArn: \(Swift.String(describing: locationArn)), serverCertificate: \(Swift.String(describing: serverCertificate)), serverPort: \(Swift.String(describing: serverPort)), serverProtocol: \(Swift.String(describing: serverProtocol)), subdirectory: \(Swift.String(describing: subdirectory)), secretKey: \"CONTENT_REDACTED\")"}
}

public struct UpdateLocationObjectStorageOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateLocationS3Input: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the Amazon S3 transfer location that you're updating.
    /// This member is required.
    public var locationArn: Swift.String?
    /// Specifies the Amazon Resource Name (ARN) of the Identity and Access Management (IAM) role that DataSync uses to access your S3 bucket. For more information, see [Providing DataSync access to S3 buckets](https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#create-s3-location-access).
    public var s3Config: DataSyncClientTypes.S3Config?
    /// Specifies the storage class that you want your objects to use when Amazon S3 is a transfer destination. For buckets in Amazon Web Services Regions, the storage class defaults to STANDARD. For buckets on Outposts, the storage class defaults to OUTPOSTS. For more information, see [Storage class considerations with Amazon S3 transfers](https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes).
    public var s3StorageClass: DataSyncClientTypes.S3StorageClass?
    /// Specifies a prefix in the S3 bucket that DataSync reads from or writes to (depending on whether the bucket is a source or destination location). DataSync can't transfer objects with a prefix that begins with a slash (/) or includes //, /./, or /../ patterns. For example:
    ///
    /// * /photos
    ///
    /// * photos//2006/January
    ///
    /// * photos/./2006/February
    ///
    /// * photos/../2006/March
    public var subdirectory: Swift.String?

    public init(
        locationArn: Swift.String? = nil,
        s3Config: DataSyncClientTypes.S3Config? = nil,
        s3StorageClass: DataSyncClientTypes.S3StorageClass? = nil,
        subdirectory: Swift.String? = nil
    )
    {
        self.locationArn = locationArn
        self.s3Config = s3Config
        self.s3StorageClass = s3StorageClass
        self.subdirectory = subdirectory
    }
}

public struct UpdateLocationS3Output: Swift.Sendable {

    public init() { }
}

public struct UpdateLocationSmbInput: Swift.Sendable {
    /// Specifies the DataSync agent (or agents) that can connect to your SMB file server. You specify an agent by using its Amazon Resource Name (ARN).
    public var agentArns: [Swift.String]?
    /// Specifies the Windows domain name that your SMB file server belongs to. If you have multiple domains in your environment, configuring this parameter makes sure that DataSync connects to the right file server. For more information, see [required permissions](https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions) for SMB locations.
    public var domain: Swift.String?
    /// Specifies the ARN of the SMB location that you want to update.
    /// This member is required.
    public var locationArn: Swift.String?
    /// Specifies the version of the Server Message Block (SMB) protocol that DataSync uses to access an SMB file server.
    public var mountOptions: DataSyncClientTypes.SmbMountOptions?
    /// Specifies the password of the user who can mount your SMB file server and has permission to access the files and folders involved in your transfer. For more information, see [required permissions](https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions) for SMB locations.
    public var password: Swift.String?
    /// Specifies the name of the share exported by your SMB file server where DataSync will read or write data. You can include a subdirectory in the share path (for example, /path/to/subdirectory). Make sure that other SMB clients in your network can also mount this path. To copy all data in the specified subdirectory, DataSync must be able to mount the SMB share and access all of its data. For more information, see [required permissions](https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions) for SMB locations.
    public var subdirectory: Swift.String?
    /// Specifies the user name that can mount your SMB file server and has permission to access the files and folders involved in your transfer. For information about choosing a user with the right level of access for your transfer, see [required permissions](https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions) for SMB locations.
    public var user: Swift.String?

    public init(
        agentArns: [Swift.String]? = nil,
        domain: Swift.String? = nil,
        locationArn: Swift.String? = nil,
        mountOptions: DataSyncClientTypes.SmbMountOptions? = nil,
        password: Swift.String? = nil,
        subdirectory: Swift.String? = nil,
        user: Swift.String? = nil
    )
    {
        self.agentArns = agentArns
        self.domain = domain
        self.locationArn = locationArn
        self.mountOptions = mountOptions
        self.password = password
        self.subdirectory = subdirectory
        self.user = user
    }
}

extension UpdateLocationSmbInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "UpdateLocationSmbInput(agentArns: \(Swift.String(describing: agentArns)), domain: \(Swift.String(describing: domain)), locationArn: \(Swift.String(describing: locationArn)), mountOptions: \(Swift.String(describing: mountOptions)), subdirectory: \(Swift.String(describing: subdirectory)), user: \(Swift.String(describing: user)), password: \"CONTENT_REDACTED\")"}
}

public struct UpdateLocationSmbOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateStorageSystemInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of the DataSync agent that connects to and reads your on-premises storage system. You can only specify one ARN.
    public var agentArns: [Swift.String]?
    /// Specifies the ARN of the Amazon CloudWatch log group for monitoring and logging discovery job events.
    public var cloudWatchLogGroupArn: Swift.String?
    /// Specifies the user name and password for accessing your on-premises storage system's management interface.
    public var credentials: DataSyncClientTypes.Credentials?
    /// Specifies a familiar name for your on-premises storage system.
    public var name: Swift.String?
    /// Specifies the server name and network port required to connect with your on-premises storage system's management interface.
    public var serverConfiguration: DataSyncClientTypes.DiscoveryServerConfiguration?
    /// Specifies the ARN of the on-premises storage system that you want reconfigure.
    /// This member is required.
    public var storageSystemArn: Swift.String?

    public init(
        agentArns: [Swift.String]? = nil,
        cloudWatchLogGroupArn: Swift.String? = nil,
        credentials: DataSyncClientTypes.Credentials? = nil,
        name: Swift.String? = nil,
        serverConfiguration: DataSyncClientTypes.DiscoveryServerConfiguration? = nil,
        storageSystemArn: Swift.String? = nil
    )
    {
        self.agentArns = agentArns
        self.cloudWatchLogGroupArn = cloudWatchLogGroupArn
        self.credentials = credentials
        self.name = name
        self.serverConfiguration = serverConfiguration
        self.storageSystemArn = storageSystemArn
    }
}

public struct UpdateStorageSystemOutput: Swift.Sendable {

    public init() { }
}

/// UpdateTaskResponse
public struct UpdateTaskInput: Swift.Sendable {
    /// Specifies the Amazon Resource Name (ARN) of an Amazon CloudWatch log group for monitoring your task. For Enhanced mode tasks, you must use /aws/datasync as your log group name. For example: arn:aws:logs:us-east-1:111222333444:log-group:/aws/datasync:* For more information, see [Monitoring data transfers with CloudWatch Logs](https://docs.aws.amazon.com/datasync/latest/userguide/configure-logging.html).
    public var cloudWatchLogGroupArn: Swift.String?
    /// Specifies exclude filters that define the files, objects, and folders in your source location that you don't want DataSync to transfer. For more information and examples, see [Specifying what DataSync transfers by using filters](https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html).
    public var excludes: [DataSyncClientTypes.FilterRule]?
    /// Specifies include filters define the files, objects, and folders in your source location that you want DataSync to transfer. For more information and examples, see [Specifying what DataSync transfers by using filters](https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html).
    public var includes: [DataSyncClientTypes.FilterRule]?
    /// Configures a manifest, which is a list of files or objects that you want DataSync to transfer. For more information and configuration examples, see [Specifying what DataSync transfers by using a manifest](https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html). When using this parameter, your caller identity (the IAM role that you're using DataSync with) must have the iam:PassRole permission. The [AWSDataSyncFullAccess](https://docs.aws.amazon.com/datasync/latest/userguide/security-iam-awsmanpol.html#security-iam-awsmanpol-awsdatasyncfullaccess) policy includes this permission. To remove a manifest configuration, specify this parameter as empty.
    public var manifestConfig: DataSyncClientTypes.ManifestConfig?
    /// Specifies the name of your task.
    public var name: Swift.String?
    /// Indicates how your transfer task is configured. These options include how DataSync handles files, objects, and their associated metadata during your transfer. You also can specify how to verify data integrity, set bandwidth limits for your task, among other options. Each option has a default value. Unless you need to, you don't have to configure any option before calling [StartTaskExecution](https://docs.aws.amazon.com/datasync/latest/userguide/API_StartTaskExecution.html). You also can override your task options for each task execution. For example, you might want to adjust the LogLevel for an individual execution.
    public var options: DataSyncClientTypes.Options?
    /// Specifies a schedule for when you want your task to run. For more information, see [Scheduling your task](https://docs.aws.amazon.com/datasync/latest/userguide/task-scheduling.html).
    public var schedule: DataSyncClientTypes.TaskSchedule?
    /// Specifies the ARN of the task that you want to update.
    /// This member is required.
    public var taskArn: Swift.String?
    /// Specifies how you want to configure a task report, which provides detailed information about your DataSync transfer. For more information, see [Monitoring your DataSync transfers with task reports](https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html). When using this parameter, your caller identity (the IAM role that you're using DataSync with) must have the iam:PassRole permission. The [AWSDataSyncFullAccess](https://docs.aws.amazon.com/datasync/latest/userguide/security-iam-awsmanpol.html#security-iam-awsmanpol-awsdatasyncfullaccess) policy includes this permission. To remove a task report configuration, specify this parameter as empty.
    public var taskReportConfig: DataSyncClientTypes.TaskReportConfig?

    public init(
        cloudWatchLogGroupArn: Swift.String? = nil,
        excludes: [DataSyncClientTypes.FilterRule]? = nil,
        includes: [DataSyncClientTypes.FilterRule]? = nil,
        manifestConfig: DataSyncClientTypes.ManifestConfig? = nil,
        name: Swift.String? = nil,
        options: DataSyncClientTypes.Options? = nil,
        schedule: DataSyncClientTypes.TaskSchedule? = nil,
        taskArn: Swift.String? = nil,
        taskReportConfig: DataSyncClientTypes.TaskReportConfig? = nil
    )
    {
        self.cloudWatchLogGroupArn = cloudWatchLogGroupArn
        self.excludes = excludes
        self.includes = includes
        self.manifestConfig = manifestConfig
        self.name = name
        self.options = options
        self.schedule = schedule
        self.taskArn = taskArn
        self.taskReportConfig = taskReportConfig
    }
}

public struct UpdateTaskOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateTaskExecutionInput: Swift.Sendable {
    /// Indicates how your transfer task is configured. These options include how DataSync handles files, objects, and their associated metadata during your transfer. You also can specify how to verify data integrity, set bandwidth limits for your task, among other options. Each option has a default value. Unless you need to, you don't have to configure any option before calling [StartTaskExecution](https://docs.aws.amazon.com/datasync/latest/userguide/API_StartTaskExecution.html). You also can override your task options for each task execution. For example, you might want to adjust the LogLevel for an individual execution.
    /// This member is required.
    public var options: DataSyncClientTypes.Options?
    /// Specifies the Amazon Resource Name (ARN) of the task execution that you're updating.
    /// This member is required.
    public var taskExecutionArn: Swift.String?

    public init(
        options: DataSyncClientTypes.Options? = nil,
        taskExecutionArn: Swift.String? = nil
    )
    {
        self.options = options
        self.taskExecutionArn = taskExecutionArn
    }
}

public struct UpdateTaskExecutionOutput: Swift.Sendable {

    public init() { }
}

extension AddStorageSystemInput {

    static func urlPathProvider(_ value: AddStorageSystemInput) -> Swift.String? {
        return "/"
    }
}

extension CancelTaskExecutionInput {

    static func urlPathProvider(_ value: CancelTaskExecutionInput) -> Swift.String? {
        return "/"
    }
}

extension CreateAgentInput {

    static func urlPathProvider(_ value: CreateAgentInput) -> Swift.String? {
        return "/"
    }
}

extension CreateLocationAzureBlobInput {

    static func urlPathProvider(_ value: CreateLocationAzureBlobInput) -> Swift.String? {
        return "/"
    }
}

extension CreateLocationEfsInput {

    static func urlPathProvider(_ value: CreateLocationEfsInput) -> Swift.String? {
        return "/"
    }
}

extension CreateLocationFsxLustreInput {

    static func urlPathProvider(_ value: CreateLocationFsxLustreInput) -> Swift.String? {
        return "/"
    }
}

extension CreateLocationFsxOntapInput {

    static func urlPathProvider(_ value: CreateLocationFsxOntapInput) -> Swift.String? {
        return "/"
    }
}

extension CreateLocationFsxOpenZfsInput {

    static func urlPathProvider(_ value: CreateLocationFsxOpenZfsInput) -> Swift.String? {
        return "/"
    }
}

extension CreateLocationFsxWindowsInput {

    static func urlPathProvider(_ value: CreateLocationFsxWindowsInput) -> Swift.String? {
        return "/"
    }
}

extension CreateLocationHdfsInput {

    static func urlPathProvider(_ value: CreateLocationHdfsInput) -> Swift.String? {
        return "/"
    }
}

extension CreateLocationNfsInput {

    static func urlPathProvider(_ value: CreateLocationNfsInput) -> Swift.String? {
        return "/"
    }
}

extension CreateLocationObjectStorageInput {

    static func urlPathProvider(_ value: CreateLocationObjectStorageInput) -> Swift.String? {
        return "/"
    }
}

extension CreateLocationS3Input {

    static func urlPathProvider(_ value: CreateLocationS3Input) -> Swift.String? {
        return "/"
    }
}

extension CreateLocationSmbInput {

    static func urlPathProvider(_ value: CreateLocationSmbInput) -> Swift.String? {
        return "/"
    }
}

extension CreateTaskInput {

    static func urlPathProvider(_ value: CreateTaskInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteAgentInput {

    static func urlPathProvider(_ value: DeleteAgentInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteLocationInput {

    static func urlPathProvider(_ value: DeleteLocationInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteTaskInput {

    static func urlPathProvider(_ value: DeleteTaskInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeAgentInput {

    static func urlPathProvider(_ value: DescribeAgentInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeDiscoveryJobInput {

    static func urlPathProvider(_ value: DescribeDiscoveryJobInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeLocationAzureBlobInput {

    static func urlPathProvider(_ value: DescribeLocationAzureBlobInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeLocationEfsInput {

    static func urlPathProvider(_ value: DescribeLocationEfsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeLocationFsxLustreInput {

    static func urlPathProvider(_ value: DescribeLocationFsxLustreInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeLocationFsxOntapInput {

    static func urlPathProvider(_ value: DescribeLocationFsxOntapInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeLocationFsxOpenZfsInput {

    static func urlPathProvider(_ value: DescribeLocationFsxOpenZfsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeLocationFsxWindowsInput {

    static func urlPathProvider(_ value: DescribeLocationFsxWindowsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeLocationHdfsInput {

    static func urlPathProvider(_ value: DescribeLocationHdfsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeLocationNfsInput {

    static func urlPathProvider(_ value: DescribeLocationNfsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeLocationObjectStorageInput {

    static func urlPathProvider(_ value: DescribeLocationObjectStorageInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeLocationS3Input {

    static func urlPathProvider(_ value: DescribeLocationS3Input) -> Swift.String? {
        return "/"
    }
}

extension DescribeLocationSmbInput {

    static func urlPathProvider(_ value: DescribeLocationSmbInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeStorageSystemInput {

    static func urlPathProvider(_ value: DescribeStorageSystemInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeStorageSystemResourceMetricsInput {

    static func urlPathProvider(_ value: DescribeStorageSystemResourceMetricsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeStorageSystemResourcesInput {

    static func urlPathProvider(_ value: DescribeStorageSystemResourcesInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeTaskInput {

    static func urlPathProvider(_ value: DescribeTaskInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeTaskExecutionInput {

    static func urlPathProvider(_ value: DescribeTaskExecutionInput) -> Swift.String? {
        return "/"
    }
}

extension GenerateRecommendationsInput {

    static func urlPathProvider(_ value: GenerateRecommendationsInput) -> Swift.String? {
        return "/"
    }
}

extension ListAgentsInput {

    static func urlPathProvider(_ value: ListAgentsInput) -> Swift.String? {
        return "/"
    }
}

extension ListDiscoveryJobsInput {

    static func urlPathProvider(_ value: ListDiscoveryJobsInput) -> Swift.String? {
        return "/"
    }
}

extension ListLocationsInput {

    static func urlPathProvider(_ value: ListLocationsInput) -> Swift.String? {
        return "/"
    }
}

extension ListStorageSystemsInput {

    static func urlPathProvider(_ value: ListStorageSystemsInput) -> Swift.String? {
        return "/"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        return "/"
    }
}

extension ListTaskExecutionsInput {

    static func urlPathProvider(_ value: ListTaskExecutionsInput) -> Swift.String? {
        return "/"
    }
}

extension ListTasksInput {

    static func urlPathProvider(_ value: ListTasksInput) -> Swift.String? {
        return "/"
    }
}

extension RemoveStorageSystemInput {

    static func urlPathProvider(_ value: RemoveStorageSystemInput) -> Swift.String? {
        return "/"
    }
}

extension StartDiscoveryJobInput {

    static func urlPathProvider(_ value: StartDiscoveryJobInput) -> Swift.String? {
        return "/"
    }
}

extension StartTaskExecutionInput {

    static func urlPathProvider(_ value: StartTaskExecutionInput) -> Swift.String? {
        return "/"
    }
}

extension StopDiscoveryJobInput {

    static func urlPathProvider(_ value: StopDiscoveryJobInput) -> Swift.String? {
        return "/"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        return "/"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateAgentInput {

    static func urlPathProvider(_ value: UpdateAgentInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateDiscoveryJobInput {

    static func urlPathProvider(_ value: UpdateDiscoveryJobInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateLocationAzureBlobInput {

    static func urlPathProvider(_ value: UpdateLocationAzureBlobInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateLocationEfsInput {

    static func urlPathProvider(_ value: UpdateLocationEfsInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateLocationFsxLustreInput {

    static func urlPathProvider(_ value: UpdateLocationFsxLustreInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateLocationFsxOntapInput {

    static func urlPathProvider(_ value: UpdateLocationFsxOntapInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateLocationFsxOpenZfsInput {

    static func urlPathProvider(_ value: UpdateLocationFsxOpenZfsInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateLocationFsxWindowsInput {

    static func urlPathProvider(_ value: UpdateLocationFsxWindowsInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateLocationHdfsInput {

    static func urlPathProvider(_ value: UpdateLocationHdfsInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateLocationNfsInput {

    static func urlPathProvider(_ value: UpdateLocationNfsInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateLocationObjectStorageInput {

    static func urlPathProvider(_ value: UpdateLocationObjectStorageInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateLocationS3Input {

    static func urlPathProvider(_ value: UpdateLocationS3Input) -> Swift.String? {
        return "/"
    }
}

extension UpdateLocationSmbInput {

    static func urlPathProvider(_ value: UpdateLocationSmbInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateStorageSystemInput {

    static func urlPathProvider(_ value: UpdateStorageSystemInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateTaskInput {

    static func urlPathProvider(_ value: UpdateTaskInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateTaskExecutionInput {

    static func urlPathProvider(_ value: UpdateTaskExecutionInput) -> Swift.String? {
        return "/"
    }
}

extension AddStorageSystemInput {

    static func write(value: AddStorageSystemInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AgentArns"].writeList(value.agentArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ClientToken"].write(value.clientToken)
        try writer["CloudWatchLogGroupArn"].write(value.cloudWatchLogGroupArn)
        try writer["Credentials"].write(value.credentials, with: DataSyncClientTypes.Credentials.write(value:to:))
        try writer["Name"].write(value.name)
        try writer["ServerConfiguration"].write(value.serverConfiguration, with: DataSyncClientTypes.DiscoveryServerConfiguration.write(value:to:))
        try writer["SystemType"].write(value.systemType)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CancelTaskExecutionInput {

    static func write(value: CancelTaskExecutionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["TaskExecutionArn"].write(value.taskExecutionArn)
    }
}

extension CreateAgentInput {

    static func write(value: CreateAgentInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ActivationKey"].write(value.activationKey)
        try writer["AgentName"].write(value.agentName)
        try writer["SecurityGroupArns"].writeList(value.securityGroupArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["SubnetArns"].writeList(value.subnetArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["VpcEndpointId"].write(value.vpcEndpointId)
    }
}

extension CreateLocationAzureBlobInput {

    static func write(value: CreateLocationAzureBlobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AccessTier"].write(value.accessTier)
        try writer["AgentArns"].writeList(value.agentArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["AuthenticationType"].write(value.authenticationType)
        try writer["BlobType"].write(value.blobType)
        try writer["ContainerUrl"].write(value.containerUrl)
        try writer["SasConfiguration"].write(value.sasConfiguration, with: DataSyncClientTypes.AzureBlobSasConfiguration.write(value:to:))
        try writer["Subdirectory"].write(value.subdirectory)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateLocationEfsInput {

    static func write(value: CreateLocationEfsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AccessPointArn"].write(value.accessPointArn)
        try writer["Ec2Config"].write(value.ec2Config, with: DataSyncClientTypes.Ec2Config.write(value:to:))
        try writer["EfsFilesystemArn"].write(value.efsFilesystemArn)
        try writer["FileSystemAccessRoleArn"].write(value.fileSystemAccessRoleArn)
        try writer["InTransitEncryption"].write(value.inTransitEncryption)
        try writer["Subdirectory"].write(value.subdirectory)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateLocationFsxLustreInput {

    static func write(value: CreateLocationFsxLustreInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["FsxFilesystemArn"].write(value.fsxFilesystemArn)
        try writer["SecurityGroupArns"].writeList(value.securityGroupArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Subdirectory"].write(value.subdirectory)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateLocationFsxOntapInput {

    static func write(value: CreateLocationFsxOntapInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Protocol"].write(value.`protocol`, with: DataSyncClientTypes.FsxProtocol.write(value:to:))
        try writer["SecurityGroupArns"].writeList(value.securityGroupArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["StorageVirtualMachineArn"].write(value.storageVirtualMachineArn)
        try writer["Subdirectory"].write(value.subdirectory)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateLocationFsxOpenZfsInput {

    static func write(value: CreateLocationFsxOpenZfsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["FsxFilesystemArn"].write(value.fsxFilesystemArn)
        try writer["Protocol"].write(value.`protocol`, with: DataSyncClientTypes.FsxProtocol.write(value:to:))
        try writer["SecurityGroupArns"].writeList(value.securityGroupArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Subdirectory"].write(value.subdirectory)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateLocationFsxWindowsInput {

    static func write(value: CreateLocationFsxWindowsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Domain"].write(value.domain)
        try writer["FsxFilesystemArn"].write(value.fsxFilesystemArn)
        try writer["Password"].write(value.password)
        try writer["SecurityGroupArns"].writeList(value.securityGroupArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Subdirectory"].write(value.subdirectory)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["User"].write(value.user)
    }
}

extension CreateLocationHdfsInput {

    static func write(value: CreateLocationHdfsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AgentArns"].writeList(value.agentArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["AuthenticationType"].write(value.authenticationType)
        try writer["BlockSize"].write(value.blockSize)
        try writer["KerberosKeytab"].write(value.kerberosKeytab)
        try writer["KerberosKrb5Conf"].write(value.kerberosKrb5Conf)
        try writer["KerberosPrincipal"].write(value.kerberosPrincipal)
        try writer["KmsKeyProviderUri"].write(value.kmsKeyProviderUri)
        try writer["NameNodes"].writeList(value.nameNodes, memberWritingClosure: DataSyncClientTypes.HdfsNameNode.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["QopConfiguration"].write(value.qopConfiguration, with: DataSyncClientTypes.QopConfiguration.write(value:to:))
        try writer["ReplicationFactor"].write(value.replicationFactor)
        try writer["SimpleUser"].write(value.simpleUser)
        try writer["Subdirectory"].write(value.subdirectory)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateLocationNfsInput {

    static func write(value: CreateLocationNfsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MountOptions"].write(value.mountOptions, with: DataSyncClientTypes.NfsMountOptions.write(value:to:))
        try writer["OnPremConfig"].write(value.onPremConfig, with: DataSyncClientTypes.OnPremConfig.write(value:to:))
        try writer["ServerHostname"].write(value.serverHostname)
        try writer["Subdirectory"].write(value.subdirectory)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateLocationObjectStorageInput {

    static func write(value: CreateLocationObjectStorageInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AccessKey"].write(value.accessKey)
        try writer["AgentArns"].writeList(value.agentArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["BucketName"].write(value.bucketName)
        try writer["SecretKey"].write(value.secretKey)
        try writer["ServerCertificate"].write(value.serverCertificate)
        try writer["ServerHostname"].write(value.serverHostname)
        try writer["ServerPort"].write(value.serverPort)
        try writer["ServerProtocol"].write(value.serverProtocol)
        try writer["Subdirectory"].write(value.subdirectory)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateLocationS3Input {

    static func write(value: CreateLocationS3Input?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AgentArns"].writeList(value.agentArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["S3BucketArn"].write(value.s3BucketArn)
        try writer["S3Config"].write(value.s3Config, with: DataSyncClientTypes.S3Config.write(value:to:))
        try writer["S3StorageClass"].write(value.s3StorageClass)
        try writer["Subdirectory"].write(value.subdirectory)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateLocationSmbInput {

    static func write(value: CreateLocationSmbInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AgentArns"].writeList(value.agentArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Domain"].write(value.domain)
        try writer["MountOptions"].write(value.mountOptions, with: DataSyncClientTypes.SmbMountOptions.write(value:to:))
        try writer["Password"].write(value.password)
        try writer["ServerHostname"].write(value.serverHostname)
        try writer["Subdirectory"].write(value.subdirectory)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["User"].write(value.user)
    }
}

extension CreateTaskInput {

    static func write(value: CreateTaskInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CloudWatchLogGroupArn"].write(value.cloudWatchLogGroupArn)
        try writer["DestinationLocationArn"].write(value.destinationLocationArn)
        try writer["Excludes"].writeList(value.excludes, memberWritingClosure: DataSyncClientTypes.FilterRule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Includes"].writeList(value.includes, memberWritingClosure: DataSyncClientTypes.FilterRule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ManifestConfig"].write(value.manifestConfig, with: DataSyncClientTypes.ManifestConfig.write(value:to:))
        try writer["Name"].write(value.name)
        try writer["Options"].write(value.options, with: DataSyncClientTypes.Options.write(value:to:))
        try writer["Schedule"].write(value.schedule, with: DataSyncClientTypes.TaskSchedule.write(value:to:))
        try writer["SourceLocationArn"].write(value.sourceLocationArn)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["TaskMode"].write(value.taskMode)
        try writer["TaskReportConfig"].write(value.taskReportConfig, with: DataSyncClientTypes.TaskReportConfig.write(value:to:))
    }
}

extension DeleteAgentInput {

    static func write(value: DeleteAgentInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AgentArn"].write(value.agentArn)
    }
}

extension DeleteLocationInput {

    static func write(value: DeleteLocationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
    }
}

extension DeleteTaskInput {

    static func write(value: DeleteTaskInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["TaskArn"].write(value.taskArn)
    }
}

extension DescribeAgentInput {

    static func write(value: DescribeAgentInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AgentArn"].write(value.agentArn)
    }
}

extension DescribeDiscoveryJobInput {

    static func write(value: DescribeDiscoveryJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DiscoveryJobArn"].write(value.discoveryJobArn)
    }
}

extension DescribeLocationAzureBlobInput {

    static func write(value: DescribeLocationAzureBlobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
    }
}

extension DescribeLocationEfsInput {

    static func write(value: DescribeLocationEfsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
    }
}

extension DescribeLocationFsxLustreInput {

    static func write(value: DescribeLocationFsxLustreInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
    }
}

extension DescribeLocationFsxOntapInput {

    static func write(value: DescribeLocationFsxOntapInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
    }
}

extension DescribeLocationFsxOpenZfsInput {

    static func write(value: DescribeLocationFsxOpenZfsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
    }
}

extension DescribeLocationFsxWindowsInput {

    static func write(value: DescribeLocationFsxWindowsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
    }
}

extension DescribeLocationHdfsInput {

    static func write(value: DescribeLocationHdfsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
    }
}

extension DescribeLocationNfsInput {

    static func write(value: DescribeLocationNfsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
    }
}

extension DescribeLocationObjectStorageInput {

    static func write(value: DescribeLocationObjectStorageInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
    }
}

extension DescribeLocationS3Input {

    static func write(value: DescribeLocationS3Input?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
    }
}

extension DescribeLocationSmbInput {

    static func write(value: DescribeLocationSmbInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
    }
}

extension DescribeStorageSystemInput {

    static func write(value: DescribeStorageSystemInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["StorageSystemArn"].write(value.storageSystemArn)
    }
}

extension DescribeStorageSystemResourceMetricsInput {

    static func write(value: DescribeStorageSystemResourceMetricsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DiscoveryJobArn"].write(value.discoveryJobArn)
        try writer["EndTime"].writeTimestamp(value.endTime, format: SmithyTimestamps.TimestampFormat.epochSeconds)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["ResourceId"].write(value.resourceId)
        try writer["ResourceType"].write(value.resourceType)
        try writer["StartTime"].writeTimestamp(value.startTime, format: SmithyTimestamps.TimestampFormat.epochSeconds)
    }
}

extension DescribeStorageSystemResourcesInput {

    static func write(value: DescribeStorageSystemResourcesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DiscoveryJobArn"].write(value.discoveryJobArn)
        try writer["Filter"].writeMap(value.filter, valueWritingClosure: SmithyReadWrite.listWritingClosure(memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["ResourceIds"].writeList(value.resourceIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ResourceType"].write(value.resourceType)
    }
}

extension DescribeTaskInput {

    static func write(value: DescribeTaskInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["TaskArn"].write(value.taskArn)
    }
}

extension DescribeTaskExecutionInput {

    static func write(value: DescribeTaskExecutionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["TaskExecutionArn"].write(value.taskExecutionArn)
    }
}

extension GenerateRecommendationsInput {

    static func write(value: GenerateRecommendationsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DiscoveryJobArn"].write(value.discoveryJobArn)
        try writer["ResourceIds"].writeList(value.resourceIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ResourceType"].write(value.resourceType)
    }
}

extension ListAgentsInput {

    static func write(value: ListAgentsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListDiscoveryJobsInput {

    static func write(value: ListDiscoveryJobsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["StorageSystemArn"].write(value.storageSystemArn)
    }
}

extension ListLocationsInput {

    static func write(value: ListLocationsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DataSyncClientTypes.LocationFilter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListStorageSystemsInput {

    static func write(value: ListStorageSystemsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListTagsForResourceInput {

    static func write(value: ListTagsForResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["ResourceArn"].write(value.resourceArn)
    }
}

extension ListTaskExecutionsInput {

    static func write(value: ListTaskExecutionsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["TaskArn"].write(value.taskArn)
    }
}

extension ListTasksInput {

    static func write(value: ListTasksInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DataSyncClientTypes.TaskFilter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension RemoveStorageSystemInput {

    static func write(value: RemoveStorageSystemInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["StorageSystemArn"].write(value.storageSystemArn)
    }
}

extension StartDiscoveryJobInput {

    static func write(value: StartDiscoveryJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["CollectionDurationMinutes"].write(value.collectionDurationMinutes)
        try writer["StorageSystemArn"].write(value.storageSystemArn)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension StartTaskExecutionInput {

    static func write(value: StartTaskExecutionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Excludes"].writeList(value.excludes, memberWritingClosure: DataSyncClientTypes.FilterRule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Includes"].writeList(value.includes, memberWritingClosure: DataSyncClientTypes.FilterRule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ManifestConfig"].write(value.manifestConfig, with: DataSyncClientTypes.ManifestConfig.write(value:to:))
        try writer["OverrideOptions"].write(value.overrideOptions, with: DataSyncClientTypes.Options.write(value:to:))
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["TaskArn"].write(value.taskArn)
        try writer["TaskReportConfig"].write(value.taskReportConfig, with: DataSyncClientTypes.TaskReportConfig.write(value:to:))
    }
}

extension StopDiscoveryJobInput {

    static func write(value: StopDiscoveryJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DiscoveryJobArn"].write(value.discoveryJobArn)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceArn"].write(value.resourceArn)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DataSyncClientTypes.TagListEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UntagResourceInput {

    static func write(value: UntagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Keys"].writeList(value.keys, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ResourceArn"].write(value.resourceArn)
    }
}

extension UpdateAgentInput {

    static func write(value: UpdateAgentInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AgentArn"].write(value.agentArn)
        try writer["Name"].write(value.name)
    }
}

extension UpdateDiscoveryJobInput {

    static func write(value: UpdateDiscoveryJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CollectionDurationMinutes"].write(value.collectionDurationMinutes)
        try writer["DiscoveryJobArn"].write(value.discoveryJobArn)
    }
}

extension UpdateLocationAzureBlobInput {

    static func write(value: UpdateLocationAzureBlobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AccessTier"].write(value.accessTier)
        try writer["AgentArns"].writeList(value.agentArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["AuthenticationType"].write(value.authenticationType)
        try writer["BlobType"].write(value.blobType)
        try writer["LocationArn"].write(value.locationArn)
        try writer["SasConfiguration"].write(value.sasConfiguration, with: DataSyncClientTypes.AzureBlobSasConfiguration.write(value:to:))
        try writer["Subdirectory"].write(value.subdirectory)
    }
}

extension UpdateLocationEfsInput {

    static func write(value: UpdateLocationEfsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AccessPointArn"].write(value.accessPointArn)
        try writer["FileSystemAccessRoleArn"].write(value.fileSystemAccessRoleArn)
        try writer["InTransitEncryption"].write(value.inTransitEncryption)
        try writer["LocationArn"].write(value.locationArn)
        try writer["Subdirectory"].write(value.subdirectory)
    }
}

extension UpdateLocationFsxLustreInput {

    static func write(value: UpdateLocationFsxLustreInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
        try writer["Subdirectory"].write(value.subdirectory)
    }
}

extension UpdateLocationFsxOntapInput {

    static func write(value: UpdateLocationFsxOntapInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
        try writer["Protocol"].write(value.`protocol`, with: DataSyncClientTypes.FsxUpdateProtocol.write(value:to:))
        try writer["Subdirectory"].write(value.subdirectory)
    }
}

extension UpdateLocationFsxOpenZfsInput {

    static func write(value: UpdateLocationFsxOpenZfsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
        try writer["Protocol"].write(value.`protocol`, with: DataSyncClientTypes.FsxProtocol.write(value:to:))
        try writer["Subdirectory"].write(value.subdirectory)
    }
}

extension UpdateLocationFsxWindowsInput {

    static func write(value: UpdateLocationFsxWindowsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Domain"].write(value.domain)
        try writer["LocationArn"].write(value.locationArn)
        try writer["Password"].write(value.password)
        try writer["Subdirectory"].write(value.subdirectory)
        try writer["User"].write(value.user)
    }
}

extension UpdateLocationHdfsInput {

    static func write(value: UpdateLocationHdfsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AgentArns"].writeList(value.agentArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["AuthenticationType"].write(value.authenticationType)
        try writer["BlockSize"].write(value.blockSize)
        try writer["KerberosKeytab"].write(value.kerberosKeytab)
        try writer["KerberosKrb5Conf"].write(value.kerberosKrb5Conf)
        try writer["KerberosPrincipal"].write(value.kerberosPrincipal)
        try writer["KmsKeyProviderUri"].write(value.kmsKeyProviderUri)
        try writer["LocationArn"].write(value.locationArn)
        try writer["NameNodes"].writeList(value.nameNodes, memberWritingClosure: DataSyncClientTypes.HdfsNameNode.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["QopConfiguration"].write(value.qopConfiguration, with: DataSyncClientTypes.QopConfiguration.write(value:to:))
        try writer["ReplicationFactor"].write(value.replicationFactor)
        try writer["SimpleUser"].write(value.simpleUser)
        try writer["Subdirectory"].write(value.subdirectory)
    }
}

extension UpdateLocationNfsInput {

    static func write(value: UpdateLocationNfsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
        try writer["MountOptions"].write(value.mountOptions, with: DataSyncClientTypes.NfsMountOptions.write(value:to:))
        try writer["OnPremConfig"].write(value.onPremConfig, with: DataSyncClientTypes.OnPremConfig.write(value:to:))
        try writer["Subdirectory"].write(value.subdirectory)
    }
}

extension UpdateLocationObjectStorageInput {

    static func write(value: UpdateLocationObjectStorageInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AccessKey"].write(value.accessKey)
        try writer["AgentArns"].writeList(value.agentArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["LocationArn"].write(value.locationArn)
        try writer["SecretKey"].write(value.secretKey)
        try writer["ServerCertificate"].write(value.serverCertificate)
        try writer["ServerPort"].write(value.serverPort)
        try writer["ServerProtocol"].write(value.serverProtocol)
        try writer["Subdirectory"].write(value.subdirectory)
    }
}

extension UpdateLocationS3Input {

    static func write(value: UpdateLocationS3Input?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LocationArn"].write(value.locationArn)
        try writer["S3Config"].write(value.s3Config, with: DataSyncClientTypes.S3Config.write(value:to:))
        try writer["S3StorageClass"].write(value.s3StorageClass)
        try writer["Subdirectory"].write(value.subdirectory)
    }
}

extension UpdateLocationSmbInput {

    static func write(value: UpdateLocationSmbInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AgentArns"].writeList(value.agentArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Domain"].write(value.domain)
        try writer["LocationArn"].write(value.locationArn)
        try writer["MountOptions"].write(value.mountOptions, with: DataSyncClientTypes.SmbMountOptions.write(value:to:))
        try writer["Password"].write(value.password)
        try writer["Subdirectory"].write(value.subdirectory)
        try writer["User"].write(value.user)
    }
}

extension UpdateStorageSystemInput {

    static func write(value: UpdateStorageSystemInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AgentArns"].writeList(value.agentArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["CloudWatchLogGroupArn"].write(value.cloudWatchLogGroupArn)
        try writer["Credentials"].write(value.credentials, with: DataSyncClientTypes.Credentials.write(value:to:))
        try writer["Name"].write(value.name)
        try writer["ServerConfiguration"].write(value.serverConfiguration, with: DataSyncClientTypes.DiscoveryServerConfiguration.write(value:to:))
        try writer["StorageSystemArn"].write(value.storageSystemArn)
    }
}

extension UpdateTaskInput {

    static func write(value: UpdateTaskInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CloudWatchLogGroupArn"].write(value.cloudWatchLogGroupArn)
        try writer["Excludes"].writeList(value.excludes, memberWritingClosure: DataSyncClientTypes.FilterRule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Includes"].writeList(value.includes, memberWritingClosure: DataSyncClientTypes.FilterRule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ManifestConfig"].write(value.manifestConfig, with: DataSyncClientTypes.ManifestConfig.write(value:to:))
        try writer["Name"].write(value.name)
        try writer["Options"].write(value.options, with: DataSyncClientTypes.Options.write(value:to:))
        try writer["Schedule"].write(value.schedule, with: DataSyncClientTypes.TaskSchedule.write(value:to:))
        try writer["TaskArn"].write(value.taskArn)
        try writer["TaskReportConfig"].write(value.taskReportConfig, with: DataSyncClientTypes.TaskReportConfig.write(value:to:))
    }
}

extension UpdateTaskExecutionInput {

    static func write(value: UpdateTaskExecutionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Options"].write(value.options, with: DataSyncClientTypes.Options.write(value:to:))
        try writer["TaskExecutionArn"].write(value.taskExecutionArn)
    }
}

extension AddStorageSystemOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AddStorageSystemOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = AddStorageSystemOutput()
        value.storageSystemArn = try reader["StorageSystemArn"].readIfPresent() ?? ""
        return value
    }
}

extension CancelTaskExecutionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CancelTaskExecutionOutput {
        return CancelTaskExecutionOutput()
    }
}

extension CreateAgentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateAgentOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateAgentOutput()
        value.agentArn = try reader["AgentArn"].readIfPresent()
        return value
    }
}

extension CreateLocationAzureBlobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLocationAzureBlobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateLocationAzureBlobOutput()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        return value
    }
}

extension CreateLocationEfsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLocationEfsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateLocationEfsOutput()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        return value
    }
}

extension CreateLocationFsxLustreOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLocationFsxLustreOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateLocationFsxLustreOutput()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        return value
    }
}

extension CreateLocationFsxOntapOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLocationFsxOntapOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateLocationFsxOntapOutput()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        return value
    }
}

extension CreateLocationFsxOpenZfsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLocationFsxOpenZfsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateLocationFsxOpenZfsOutput()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        return value
    }
}

extension CreateLocationFsxWindowsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLocationFsxWindowsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateLocationFsxWindowsOutput()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        return value
    }
}

extension CreateLocationHdfsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLocationHdfsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateLocationHdfsOutput()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        return value
    }
}

extension CreateLocationNfsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLocationNfsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateLocationNfsOutput()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        return value
    }
}

extension CreateLocationObjectStorageOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLocationObjectStorageOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateLocationObjectStorageOutput()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        return value
    }
}

extension CreateLocationS3Output {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLocationS3Output {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateLocationS3Output()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        return value
    }
}

extension CreateLocationSmbOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLocationSmbOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateLocationSmbOutput()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        return value
    }
}

extension CreateTaskOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateTaskOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateTaskOutput()
        value.taskArn = try reader["TaskArn"].readIfPresent()
        return value
    }
}

extension DeleteAgentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteAgentOutput {
        return DeleteAgentOutput()
    }
}

extension DeleteLocationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteLocationOutput {
        return DeleteLocationOutput()
    }
}

extension DeleteTaskOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteTaskOutput {
        return DeleteTaskOutput()
    }
}

extension DescribeAgentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeAgentOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeAgentOutput()
        value.agentArn = try reader["AgentArn"].readIfPresent()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.endpointType = try reader["EndpointType"].readIfPresent()
        value.lastConnectionTime = try reader["LastConnectionTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.name = try reader["Name"].readIfPresent()
        value.platform = try reader["Platform"].readIfPresent(with: DataSyncClientTypes.Platform.read(from:))
        value.privateLinkConfig = try reader["PrivateLinkConfig"].readIfPresent(with: DataSyncClientTypes.PrivateLinkConfig.read(from:))
        value.status = try reader["Status"].readIfPresent()
        return value
    }
}

extension DescribeDiscoveryJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDiscoveryJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeDiscoveryJobOutput()
        value.collectionDurationMinutes = try reader["CollectionDurationMinutes"].readIfPresent()
        value.discoveryJobArn = try reader["DiscoveryJobArn"].readIfPresent()
        value.jobEndTime = try reader["JobEndTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.jobStartTime = try reader["JobStartTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.status = try reader["Status"].readIfPresent()
        value.storageSystemArn = try reader["StorageSystemArn"].readIfPresent()
        return value
    }
}

extension DescribeLocationAzureBlobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeLocationAzureBlobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeLocationAzureBlobOutput()
        value.accessTier = try reader["AccessTier"].readIfPresent()
        value.agentArns = try reader["AgentArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.authenticationType = try reader["AuthenticationType"].readIfPresent()
        value.blobType = try reader["BlobType"].readIfPresent()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.locationArn = try reader["LocationArn"].readIfPresent()
        value.locationUri = try reader["LocationUri"].readIfPresent()
        return value
    }
}

extension DescribeLocationEfsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeLocationEfsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeLocationEfsOutput()
        value.accessPointArn = try reader["AccessPointArn"].readIfPresent()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.ec2Config = try reader["Ec2Config"].readIfPresent(with: DataSyncClientTypes.Ec2Config.read(from:))
        value.fileSystemAccessRoleArn = try reader["FileSystemAccessRoleArn"].readIfPresent()
        value.inTransitEncryption = try reader["InTransitEncryption"].readIfPresent()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        value.locationUri = try reader["LocationUri"].readIfPresent()
        return value
    }
}

extension DescribeLocationFsxLustreOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeLocationFsxLustreOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeLocationFsxLustreOutput()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.locationArn = try reader["LocationArn"].readIfPresent()
        value.locationUri = try reader["LocationUri"].readIfPresent()
        value.securityGroupArns = try reader["SecurityGroupArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeLocationFsxOntapOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeLocationFsxOntapOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeLocationFsxOntapOutput()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.fsxFilesystemArn = try reader["FsxFilesystemArn"].readIfPresent()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        value.locationUri = try reader["LocationUri"].readIfPresent()
        value.`protocol` = try reader["Protocol"].readIfPresent(with: DataSyncClientTypes.FsxProtocol.read(from:))
        value.securityGroupArns = try reader["SecurityGroupArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.storageVirtualMachineArn = try reader["StorageVirtualMachineArn"].readIfPresent()
        return value
    }
}

extension DescribeLocationFsxOpenZfsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeLocationFsxOpenZfsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeLocationFsxOpenZfsOutput()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.locationArn = try reader["LocationArn"].readIfPresent()
        value.locationUri = try reader["LocationUri"].readIfPresent()
        value.`protocol` = try reader["Protocol"].readIfPresent(with: DataSyncClientTypes.FsxProtocol.read(from:))
        value.securityGroupArns = try reader["SecurityGroupArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeLocationFsxWindowsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeLocationFsxWindowsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeLocationFsxWindowsOutput()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.domain = try reader["Domain"].readIfPresent()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        value.locationUri = try reader["LocationUri"].readIfPresent()
        value.securityGroupArns = try reader["SecurityGroupArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.user = try reader["User"].readIfPresent()
        return value
    }
}

extension DescribeLocationHdfsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeLocationHdfsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeLocationHdfsOutput()
        value.agentArns = try reader["AgentArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.authenticationType = try reader["AuthenticationType"].readIfPresent()
        value.blockSize = try reader["BlockSize"].readIfPresent()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.kerberosPrincipal = try reader["KerberosPrincipal"].readIfPresent()
        value.kmsKeyProviderUri = try reader["KmsKeyProviderUri"].readIfPresent()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        value.locationUri = try reader["LocationUri"].readIfPresent()
        value.nameNodes = try reader["NameNodes"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.HdfsNameNode.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.qopConfiguration = try reader["QopConfiguration"].readIfPresent(with: DataSyncClientTypes.QopConfiguration.read(from:))
        value.replicationFactor = try reader["ReplicationFactor"].readIfPresent()
        value.simpleUser = try reader["SimpleUser"].readIfPresent()
        return value
    }
}

extension DescribeLocationNfsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeLocationNfsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeLocationNfsOutput()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.locationArn = try reader["LocationArn"].readIfPresent()
        value.locationUri = try reader["LocationUri"].readIfPresent()
        value.mountOptions = try reader["MountOptions"].readIfPresent(with: DataSyncClientTypes.NfsMountOptions.read(from:))
        value.onPremConfig = try reader["OnPremConfig"].readIfPresent(with: DataSyncClientTypes.OnPremConfig.read(from:))
        return value
    }
}

extension DescribeLocationObjectStorageOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeLocationObjectStorageOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeLocationObjectStorageOutput()
        value.accessKey = try reader["AccessKey"].readIfPresent()
        value.agentArns = try reader["AgentArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.locationArn = try reader["LocationArn"].readIfPresent()
        value.locationUri = try reader["LocationUri"].readIfPresent()
        value.serverCertificate = try reader["ServerCertificate"].readIfPresent()
        value.serverPort = try reader["ServerPort"].readIfPresent()
        value.serverProtocol = try reader["ServerProtocol"].readIfPresent()
        return value
    }
}

extension DescribeLocationS3Output {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeLocationS3Output {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeLocationS3Output()
        value.agentArns = try reader["AgentArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.locationArn = try reader["LocationArn"].readIfPresent()
        value.locationUri = try reader["LocationUri"].readIfPresent()
        value.s3Config = try reader["S3Config"].readIfPresent(with: DataSyncClientTypes.S3Config.read(from:))
        value.s3StorageClass = try reader["S3StorageClass"].readIfPresent()
        return value
    }
}

extension DescribeLocationSmbOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeLocationSmbOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeLocationSmbOutput()
        value.agentArns = try reader["AgentArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.domain = try reader["Domain"].readIfPresent()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        value.locationUri = try reader["LocationUri"].readIfPresent()
        value.mountOptions = try reader["MountOptions"].readIfPresent(with: DataSyncClientTypes.SmbMountOptions.read(from:))
        value.user = try reader["User"].readIfPresent()
        return value
    }
}

extension DescribeStorageSystemOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeStorageSystemOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeStorageSystemOutput()
        value.agentArns = try reader["AgentArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.cloudWatchLogGroupArn = try reader["CloudWatchLogGroupArn"].readIfPresent()
        value.connectivityStatus = try reader["ConnectivityStatus"].readIfPresent()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.errorMessage = try reader["ErrorMessage"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.secretsManagerArn = try reader["SecretsManagerArn"].readIfPresent()
        value.serverConfiguration = try reader["ServerConfiguration"].readIfPresent(with: DataSyncClientTypes.DiscoveryServerConfiguration.read(from:))
        value.storageSystemArn = try reader["StorageSystemArn"].readIfPresent()
        value.systemType = try reader["SystemType"].readIfPresent()
        return value
    }
}

extension DescribeStorageSystemResourceMetricsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeStorageSystemResourceMetricsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeStorageSystemResourceMetricsOutput()
        value.metrics = try reader["Metrics"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.ResourceMetrics.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension DescribeStorageSystemResourcesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeStorageSystemResourcesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeStorageSystemResourcesOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.resourceDetails = try reader["ResourceDetails"].readIfPresent(with: DataSyncClientTypes.ResourceDetails.read(from:))
        return value
    }
}

extension DescribeTaskOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeTaskOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeTaskOutput()
        value.cloudWatchLogGroupArn = try reader["CloudWatchLogGroupArn"].readIfPresent()
        value.creationTime = try reader["CreationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.currentTaskExecutionArn = try reader["CurrentTaskExecutionArn"].readIfPresent()
        value.destinationLocationArn = try reader["DestinationLocationArn"].readIfPresent()
        value.destinationNetworkInterfaceArns = try reader["DestinationNetworkInterfaceArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.errorCode = try reader["ErrorCode"].readIfPresent()
        value.errorDetail = try reader["ErrorDetail"].readIfPresent()
        value.excludes = try reader["Excludes"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.FilterRule.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.includes = try reader["Includes"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.FilterRule.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.manifestConfig = try reader["ManifestConfig"].readIfPresent(with: DataSyncClientTypes.ManifestConfig.read(from:))
        value.name = try reader["Name"].readIfPresent()
        value.options = try reader["Options"].readIfPresent(with: DataSyncClientTypes.Options.read(from:))
        value.schedule = try reader["Schedule"].readIfPresent(with: DataSyncClientTypes.TaskSchedule.read(from:))
        value.scheduleDetails = try reader["ScheduleDetails"].readIfPresent(with: DataSyncClientTypes.TaskScheduleDetails.read(from:))
        value.sourceLocationArn = try reader["SourceLocationArn"].readIfPresent()
        value.sourceNetworkInterfaceArns = try reader["SourceNetworkInterfaceArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.status = try reader["Status"].readIfPresent()
        value.taskArn = try reader["TaskArn"].readIfPresent()
        value.taskMode = try reader["TaskMode"].readIfPresent()
        value.taskReportConfig = try reader["TaskReportConfig"].readIfPresent(with: DataSyncClientTypes.TaskReportConfig.read(from:))
        return value
    }
}

extension DescribeTaskExecutionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeTaskExecutionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeTaskExecutionOutput()
        value.bytesCompressed = try reader["BytesCompressed"].readIfPresent() ?? 0
        value.bytesTransferred = try reader["BytesTransferred"].readIfPresent() ?? 0
        value.bytesWritten = try reader["BytesWritten"].readIfPresent() ?? 0
        value.estimatedBytesToTransfer = try reader["EstimatedBytesToTransfer"].readIfPresent() ?? 0
        value.estimatedFilesToDelete = try reader["EstimatedFilesToDelete"].readIfPresent() ?? 0
        value.estimatedFilesToTransfer = try reader["EstimatedFilesToTransfer"].readIfPresent() ?? 0
        value.excludes = try reader["Excludes"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.FilterRule.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.filesDeleted = try reader["FilesDeleted"].readIfPresent() ?? 0
        value.filesFailed = try reader["FilesFailed"].readIfPresent(with: DataSyncClientTypes.TaskExecutionFilesFailedDetail.read(from:))
        value.filesListed = try reader["FilesListed"].readIfPresent(with: DataSyncClientTypes.TaskExecutionFilesListedDetail.read(from:))
        value.filesPrepared = try reader["FilesPrepared"].readIfPresent() ?? 0
        value.filesSkipped = try reader["FilesSkipped"].readIfPresent() ?? 0
        value.filesTransferred = try reader["FilesTransferred"].readIfPresent() ?? 0
        value.filesVerified = try reader["FilesVerified"].readIfPresent() ?? 0
        value.includes = try reader["Includes"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.FilterRule.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.manifestConfig = try reader["ManifestConfig"].readIfPresent(with: DataSyncClientTypes.ManifestConfig.read(from:))
        value.options = try reader["Options"].readIfPresent(with: DataSyncClientTypes.Options.read(from:))
        value.reportResult = try reader["ReportResult"].readIfPresent(with: DataSyncClientTypes.ReportResult.read(from:))
        value.result = try reader["Result"].readIfPresent(with: DataSyncClientTypes.TaskExecutionResultDetail.read(from:))
        value.startTime = try reader["StartTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.status = try reader["Status"].readIfPresent()
        value.taskExecutionArn = try reader["TaskExecutionArn"].readIfPresent()
        value.taskMode = try reader["TaskMode"].readIfPresent()
        value.taskReportConfig = try reader["TaskReportConfig"].readIfPresent(with: DataSyncClientTypes.TaskReportConfig.read(from:))
        return value
    }
}

extension GenerateRecommendationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GenerateRecommendationsOutput {
        return GenerateRecommendationsOutput()
    }
}

extension ListAgentsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListAgentsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListAgentsOutput()
        value.agents = try reader["Agents"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.AgentListEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListDiscoveryJobsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListDiscoveryJobsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListDiscoveryJobsOutput()
        value.discoveryJobs = try reader["DiscoveryJobs"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.DiscoveryJobListEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListLocationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListLocationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListLocationsOutput()
        value.locations = try reader["Locations"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.LocationListEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListStorageSystemsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListStorageSystemsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListStorageSystemsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.storageSystems = try reader["StorageSystems"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.StorageSystemListEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.TagListEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListTaskExecutionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTaskExecutionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTaskExecutionsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.taskExecutions = try reader["TaskExecutions"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.TaskExecutionListEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListTasksOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTasksOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTasksOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.tasks = try reader["Tasks"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.TaskListEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension RemoveStorageSystemOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RemoveStorageSystemOutput {
        return RemoveStorageSystemOutput()
    }
}

extension StartDiscoveryJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartDiscoveryJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartDiscoveryJobOutput()
        value.discoveryJobArn = try reader["DiscoveryJobArn"].readIfPresent()
        return value
    }
}

extension StartTaskExecutionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartTaskExecutionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartTaskExecutionOutput()
        value.taskExecutionArn = try reader["TaskExecutionArn"].readIfPresent()
        return value
    }
}

extension StopDiscoveryJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopDiscoveryJobOutput {
        return StopDiscoveryJobOutput()
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateAgentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateAgentOutput {
        return UpdateAgentOutput()
    }
}

extension UpdateDiscoveryJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateDiscoveryJobOutput {
        return UpdateDiscoveryJobOutput()
    }
}

extension UpdateLocationAzureBlobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLocationAzureBlobOutput {
        return UpdateLocationAzureBlobOutput()
    }
}

extension UpdateLocationEfsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLocationEfsOutput {
        return UpdateLocationEfsOutput()
    }
}

extension UpdateLocationFsxLustreOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLocationFsxLustreOutput {
        return UpdateLocationFsxLustreOutput()
    }
}

extension UpdateLocationFsxOntapOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLocationFsxOntapOutput {
        return UpdateLocationFsxOntapOutput()
    }
}

extension UpdateLocationFsxOpenZfsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLocationFsxOpenZfsOutput {
        return UpdateLocationFsxOpenZfsOutput()
    }
}

extension UpdateLocationFsxWindowsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLocationFsxWindowsOutput {
        return UpdateLocationFsxWindowsOutput()
    }
}

extension UpdateLocationHdfsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLocationHdfsOutput {
        return UpdateLocationHdfsOutput()
    }
}

extension UpdateLocationNfsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLocationNfsOutput {
        return UpdateLocationNfsOutput()
    }
}

extension UpdateLocationObjectStorageOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLocationObjectStorageOutput {
        return UpdateLocationObjectStorageOutput()
    }
}

extension UpdateLocationS3Output {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLocationS3Output {
        return UpdateLocationS3Output()
    }
}

extension UpdateLocationSmbOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLocationSmbOutput {
        return UpdateLocationSmbOutput()
    }
}

extension UpdateStorageSystemOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateStorageSystemOutput {
        return UpdateStorageSystemOutput()
    }
}

extension UpdateTaskOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateTaskOutput {
        return UpdateTaskOutput()
    }
}

extension UpdateTaskExecutionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateTaskExecutionOutput {
        return UpdateTaskExecutionOutput()
    }
}

enum AddStorageSystemOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CancelTaskExecutionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateAgentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateLocationAzureBlobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateLocationEfsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateLocationFsxLustreOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateLocationFsxOntapOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateLocationFsxOpenZfsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateLocationFsxWindowsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateLocationHdfsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateLocationNfsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateLocationObjectStorageOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateLocationS3OutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateLocationSmbOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateTaskOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteAgentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteLocationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteTaskOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeAgentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDiscoveryJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeLocationAzureBlobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeLocationEfsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeLocationFsxLustreOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeLocationFsxOntapOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeLocationFsxOpenZfsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeLocationFsxWindowsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeLocationHdfsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeLocationNfsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeLocationObjectStorageOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeLocationS3OutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeLocationSmbOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeStorageSystemOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeStorageSystemResourceMetricsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeStorageSystemResourcesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeTaskOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeTaskExecutionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GenerateRecommendationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListAgentsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListDiscoveryJobsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListLocationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListStorageSystemsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTaskExecutionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTasksOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RemoveStorageSystemOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartDiscoveryJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartTaskExecutionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopDiscoveryJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateAgentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateDiscoveryJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLocationAzureBlobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLocationEfsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLocationFsxLustreOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLocationFsxOntapOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLocationFsxOpenZfsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLocationFsxWindowsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLocationHdfsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLocationNfsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLocationObjectStorageOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLocationS3OutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLocationSmbOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateStorageSystemOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateTaskOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateTaskExecutionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalException": return try InternalException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension InternalException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InternalException {
        let reader = baseError.errorBodyReader
        var value = InternalException()
        value.properties.errorCode = try reader["errorCode"].readIfPresent()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidRequestException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidRequestException {
        let reader = baseError.errorBodyReader
        var value = InvalidRequestException()
        value.properties.datasyncErrorCode = try reader["datasyncErrorCode"].readIfPresent()
        value.properties.errorCode = try reader["errorCode"].readIfPresent()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DataSyncClientTypes.PrivateLinkConfig {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.PrivateLinkConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.PrivateLinkConfig()
        value.vpcEndpointId = try reader["VpcEndpointId"].readIfPresent()
        value.privateLinkEndpoint = try reader["PrivateLinkEndpoint"].readIfPresent()
        value.subnetArns = try reader["SubnetArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.securityGroupArns = try reader["SecurityGroupArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DataSyncClientTypes.Platform {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.Platform {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.Platform()
        value.version = try reader["Version"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.Ec2Config {

    static func write(value: DataSyncClientTypes.Ec2Config?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["SecurityGroupArns"].writeList(value.securityGroupArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["SubnetArn"].write(value.subnetArn)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.Ec2Config {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.Ec2Config()
        value.subnetArn = try reader["SubnetArn"].readIfPresent() ?? ""
        value.securityGroupArns = try reader["SecurityGroupArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DataSyncClientTypes.FsxProtocol {

    static func write(value: DataSyncClientTypes.FsxProtocol?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["NFS"].write(value.nfs, with: DataSyncClientTypes.FsxProtocolNfs.write(value:to:))
        try writer["SMB"].write(value.smb, with: DataSyncClientTypes.FsxProtocolSmb.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.FsxProtocol {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.FsxProtocol()
        value.nfs = try reader["NFS"].readIfPresent(with: DataSyncClientTypes.FsxProtocolNfs.read(from:))
        value.smb = try reader["SMB"].readIfPresent(with: DataSyncClientTypes.FsxProtocolSmb.read(from:))
        return value
    }
}

extension DataSyncClientTypes.FsxProtocolSmb {

    static func write(value: DataSyncClientTypes.FsxProtocolSmb?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Domain"].write(value.domain)
        try writer["MountOptions"].write(value.mountOptions, with: DataSyncClientTypes.SmbMountOptions.write(value:to:))
        try writer["Password"].write(value.password)
        try writer["User"].write(value.user)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.FsxProtocolSmb {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.FsxProtocolSmb()
        value.domain = try reader["Domain"].readIfPresent()
        value.mountOptions = try reader["MountOptions"].readIfPresent(with: DataSyncClientTypes.SmbMountOptions.read(from:))
        value.password = try reader["Password"].readIfPresent() ?? ""
        value.user = try reader["User"].readIfPresent() ?? ""
        return value
    }
}

extension DataSyncClientTypes.SmbMountOptions {

    static func write(value: DataSyncClientTypes.SmbMountOptions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Version"].write(value.version)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.SmbMountOptions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.SmbMountOptions()
        value.version = try reader["Version"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.FsxProtocolNfs {

    static func write(value: DataSyncClientTypes.FsxProtocolNfs?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MountOptions"].write(value.mountOptions, with: DataSyncClientTypes.NfsMountOptions.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.FsxProtocolNfs {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.FsxProtocolNfs()
        value.mountOptions = try reader["MountOptions"].readIfPresent(with: DataSyncClientTypes.NfsMountOptions.read(from:))
        return value
    }
}

extension DataSyncClientTypes.NfsMountOptions {

    static func write(value: DataSyncClientTypes.NfsMountOptions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Version"].write(value.version)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.NfsMountOptions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.NfsMountOptions()
        value.version = try reader["Version"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.HdfsNameNode {

    static func write(value: DataSyncClientTypes.HdfsNameNode?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Hostname"].write(value.hostname)
        try writer["Port"].write(value.port)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.HdfsNameNode {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.HdfsNameNode()
        value.hostname = try reader["Hostname"].readIfPresent() ?? ""
        value.port = try reader["Port"].readIfPresent() ?? 0
        return value
    }
}

extension DataSyncClientTypes.QopConfiguration {

    static func write(value: DataSyncClientTypes.QopConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DataTransferProtection"].write(value.dataTransferProtection)
        try writer["RpcProtection"].write(value.rpcProtection)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.QopConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.QopConfiguration()
        value.rpcProtection = try reader["RpcProtection"].readIfPresent()
        value.dataTransferProtection = try reader["DataTransferProtection"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.OnPremConfig {

    static func write(value: DataSyncClientTypes.OnPremConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AgentArns"].writeList(value.agentArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.OnPremConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.OnPremConfig()
        value.agentArns = try reader["AgentArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DataSyncClientTypes.S3Config {

    static func write(value: DataSyncClientTypes.S3Config?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["BucketAccessRoleArn"].write(value.bucketAccessRoleArn)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.S3Config {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.S3Config()
        value.bucketAccessRoleArn = try reader["BucketAccessRoleArn"].readIfPresent() ?? ""
        return value
    }
}

extension DataSyncClientTypes.DiscoveryServerConfiguration {

    static func write(value: DataSyncClientTypes.DiscoveryServerConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ServerHostname"].write(value.serverHostname)
        try writer["ServerPort"].write(value.serverPort)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.DiscoveryServerConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.DiscoveryServerConfiguration()
        value.serverHostname = try reader["ServerHostname"].readIfPresent() ?? ""
        value.serverPort = try reader["ServerPort"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.ResourceMetrics {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.ResourceMetrics {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.ResourceMetrics()
        value.timestamp = try reader["Timestamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.p95Metrics = try reader["P95Metrics"].readIfPresent(with: DataSyncClientTypes.P95Metrics.read(from:))
        value.capacity = try reader["Capacity"].readIfPresent(with: DataSyncClientTypes.Capacity.read(from:))
        value.resourceId = try reader["ResourceId"].readIfPresent()
        value.resourceType = try reader["ResourceType"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.Capacity {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.Capacity {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.Capacity()
        value.used = try reader["Used"].readIfPresent()
        value.provisioned = try reader["Provisioned"].readIfPresent()
        value.logicalUsed = try reader["LogicalUsed"].readIfPresent()
        value.clusterCloudStorageUsed = try reader["ClusterCloudStorageUsed"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.P95Metrics {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.P95Metrics {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.P95Metrics()
        value.iops = try reader["IOPS"].readIfPresent(with: DataSyncClientTypes.IOPS.read(from:))
        value.throughput = try reader["Throughput"].readIfPresent(with: DataSyncClientTypes.Throughput.read(from:))
        value.latency = try reader["Latency"].readIfPresent(with: DataSyncClientTypes.Latency.read(from:))
        return value
    }
}

extension DataSyncClientTypes.Latency {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.Latency {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.Latency()
        value.read = try reader["Read"].readIfPresent()
        value.write = try reader["Write"].readIfPresent()
        value.other = try reader["Other"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.Throughput {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.Throughput {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.Throughput()
        value.read = try reader["Read"].readIfPresent()
        value.write = try reader["Write"].readIfPresent()
        value.other = try reader["Other"].readIfPresent()
        value.total = try reader["Total"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.IOPS {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.IOPS {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.IOPS()
        value.read = try reader["Read"].readIfPresent()
        value.write = try reader["Write"].readIfPresent()
        value.other = try reader["Other"].readIfPresent()
        value.total = try reader["Total"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.ResourceDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.ResourceDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.ResourceDetails()
        value.netAppONTAPSVMs = try reader["NetAppONTAPSVMs"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.NetAppONTAPSVM.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.netAppONTAPVolumes = try reader["NetAppONTAPVolumes"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.NetAppONTAPVolume.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.netAppONTAPClusters = try reader["NetAppONTAPClusters"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.NetAppONTAPCluster.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DataSyncClientTypes.NetAppONTAPCluster {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.NetAppONTAPCluster {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.NetAppONTAPCluster()
        value.cifsShareCount = try reader["CifsShareCount"].readIfPresent()
        value.nfsExportedVolumes = try reader["NfsExportedVolumes"].readIfPresent()
        value.resourceId = try reader["ResourceId"].readIfPresent()
        value.clusterName = try reader["ClusterName"].readIfPresent()
        value.maxP95Performance = try reader["MaxP95Performance"].readIfPresent(with: DataSyncClientTypes.MaxP95Performance.read(from:))
        value.clusterBlockStorageSize = try reader["ClusterBlockStorageSize"].readIfPresent()
        value.clusterBlockStorageUsed = try reader["ClusterBlockStorageUsed"].readIfPresent()
        value.clusterBlockStorageLogicalUsed = try reader["ClusterBlockStorageLogicalUsed"].readIfPresent()
        value.recommendations = try reader["Recommendations"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.Recommendation.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.recommendationStatus = try reader["RecommendationStatus"].readIfPresent()
        value.lunCount = try reader["LunCount"].readIfPresent()
        value.clusterCloudStorageUsed = try reader["ClusterCloudStorageUsed"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.Recommendation {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.Recommendation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.Recommendation()
        value.storageType = try reader["StorageType"].readIfPresent()
        value.storageConfiguration = try reader["StorageConfiguration"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.estimatedMonthlyStorageCost = try reader["EstimatedMonthlyStorageCost"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.MaxP95Performance {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.MaxP95Performance {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.MaxP95Performance()
        value.iopsRead = try reader["IopsRead"].readIfPresent()
        value.iopsWrite = try reader["IopsWrite"].readIfPresent()
        value.iopsOther = try reader["IopsOther"].readIfPresent()
        value.iopsTotal = try reader["IopsTotal"].readIfPresent()
        value.throughputRead = try reader["ThroughputRead"].readIfPresent()
        value.throughputWrite = try reader["ThroughputWrite"].readIfPresent()
        value.throughputOther = try reader["ThroughputOther"].readIfPresent()
        value.throughputTotal = try reader["ThroughputTotal"].readIfPresent()
        value.latencyRead = try reader["LatencyRead"].readIfPresent()
        value.latencyWrite = try reader["LatencyWrite"].readIfPresent()
        value.latencyOther = try reader["LatencyOther"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.NetAppONTAPVolume {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.NetAppONTAPVolume {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.NetAppONTAPVolume()
        value.volumeName = try reader["VolumeName"].readIfPresent()
        value.resourceId = try reader["ResourceId"].readIfPresent()
        value.cifsShareCount = try reader["CifsShareCount"].readIfPresent()
        value.securityStyle = try reader["SecurityStyle"].readIfPresent()
        value.svmUuid = try reader["SvmUuid"].readIfPresent()
        value.svmName = try reader["SvmName"].readIfPresent()
        value.capacityUsed = try reader["CapacityUsed"].readIfPresent()
        value.capacityProvisioned = try reader["CapacityProvisioned"].readIfPresent()
        value.logicalCapacityUsed = try reader["LogicalCapacityUsed"].readIfPresent()
        value.nfsExported = try reader["NfsExported"].readIfPresent() ?? false
        value.snapshotCapacityUsed = try reader["SnapshotCapacityUsed"].readIfPresent()
        value.maxP95Performance = try reader["MaxP95Performance"].readIfPresent(with: DataSyncClientTypes.MaxP95Performance.read(from:))
        value.recommendations = try reader["Recommendations"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.Recommendation.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.recommendationStatus = try reader["RecommendationStatus"].readIfPresent()
        value.lunCount = try reader["LunCount"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.NetAppONTAPSVM {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.NetAppONTAPSVM {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.NetAppONTAPSVM()
        value.clusterUuid = try reader["ClusterUuid"].readIfPresent()
        value.resourceId = try reader["ResourceId"].readIfPresent()
        value.svmName = try reader["SvmName"].readIfPresent()
        value.cifsShareCount = try reader["CifsShareCount"].readIfPresent()
        value.enabledProtocols = try reader["EnabledProtocols"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.totalCapacityUsed = try reader["TotalCapacityUsed"].readIfPresent()
        value.totalCapacityProvisioned = try reader["TotalCapacityProvisioned"].readIfPresent()
        value.totalLogicalCapacityUsed = try reader["TotalLogicalCapacityUsed"].readIfPresent()
        value.maxP95Performance = try reader["MaxP95Performance"].readIfPresent(with: DataSyncClientTypes.MaxP95Performance.read(from:))
        value.recommendations = try reader["Recommendations"].readListIfPresent(memberReadingClosure: DataSyncClientTypes.Recommendation.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nfsExportedVolumes = try reader["NfsExportedVolumes"].readIfPresent()
        value.recommendationStatus = try reader["RecommendationStatus"].readIfPresent()
        value.totalSnapshotCapacityUsed = try reader["TotalSnapshotCapacityUsed"].readIfPresent()
        value.lunCount = try reader["LunCount"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.Options {

    static func write(value: DataSyncClientTypes.Options?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Atime"].write(value.atime)
        try writer["BytesPerSecond"].write(value.bytesPerSecond)
        try writer["Gid"].write(value.gid)
        try writer["LogLevel"].write(value.logLevel)
        try writer["Mtime"].write(value.mtime)
        try writer["ObjectTags"].write(value.objectTags)
        try writer["OverwriteMode"].write(value.overwriteMode)
        try writer["PosixPermissions"].write(value.posixPermissions)
        try writer["PreserveDeletedFiles"].write(value.preserveDeletedFiles)
        try writer["PreserveDevices"].write(value.preserveDevices)
        try writer["SecurityDescriptorCopyFlags"].write(value.securityDescriptorCopyFlags)
        try writer["TaskQueueing"].write(value.taskQueueing)
        try writer["TransferMode"].write(value.transferMode)
        try writer["Uid"].write(value.uid)
        try writer["VerifyMode"].write(value.verifyMode)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.Options {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.Options()
        value.verifyMode = try reader["VerifyMode"].readIfPresent()
        value.overwriteMode = try reader["OverwriteMode"].readIfPresent()
        value.atime = try reader["Atime"].readIfPresent()
        value.mtime = try reader["Mtime"].readIfPresent()
        value.uid = try reader["Uid"].readIfPresent()
        value.gid = try reader["Gid"].readIfPresent()
        value.preserveDeletedFiles = try reader["PreserveDeletedFiles"].readIfPresent()
        value.preserveDevices = try reader["PreserveDevices"].readIfPresent()
        value.posixPermissions = try reader["PosixPermissions"].readIfPresent()
        value.bytesPerSecond = try reader["BytesPerSecond"].readIfPresent()
        value.taskQueueing = try reader["TaskQueueing"].readIfPresent()
        value.logLevel = try reader["LogLevel"].readIfPresent()
        value.transferMode = try reader["TransferMode"].readIfPresent()
        value.securityDescriptorCopyFlags = try reader["SecurityDescriptorCopyFlags"].readIfPresent()
        value.objectTags = try reader["ObjectTags"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.FilterRule {

    static func write(value: DataSyncClientTypes.FilterRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["FilterType"].write(value.filterType)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.FilterRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.FilterRule()
        value.filterType = try reader["FilterType"].readIfPresent()
        value.value = try reader["Value"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.TaskSchedule {

    static func write(value: DataSyncClientTypes.TaskSchedule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ScheduleExpression"].write(value.scheduleExpression)
        try writer["Status"].write(value.status)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.TaskSchedule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.TaskSchedule()
        value.scheduleExpression = try reader["ScheduleExpression"].readIfPresent() ?? ""
        value.status = try reader["Status"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.ManifestConfig {

    static func write(value: DataSyncClientTypes.ManifestConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Action"].write(value.action)
        try writer["Format"].write(value.format)
        try writer["Source"].write(value.source, with: DataSyncClientTypes.SourceManifestConfig.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.ManifestConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.ManifestConfig()
        value.action = try reader["Action"].readIfPresent()
        value.format = try reader["Format"].readIfPresent()
        value.source = try reader["Source"].readIfPresent(with: DataSyncClientTypes.SourceManifestConfig.read(from:))
        return value
    }
}

extension DataSyncClientTypes.SourceManifestConfig {

    static func write(value: DataSyncClientTypes.SourceManifestConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["S3"].write(value.s3, with: DataSyncClientTypes.S3ManifestConfig.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.SourceManifestConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.SourceManifestConfig()
        value.s3 = try reader["S3"].readIfPresent(with: DataSyncClientTypes.S3ManifestConfig.read(from:))
        return value
    }
}

extension DataSyncClientTypes.S3ManifestConfig {

    static func write(value: DataSyncClientTypes.S3ManifestConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["BucketAccessRoleArn"].write(value.bucketAccessRoleArn)
        try writer["ManifestObjectPath"].write(value.manifestObjectPath)
        try writer["ManifestObjectVersionId"].write(value.manifestObjectVersionId)
        try writer["S3BucketArn"].write(value.s3BucketArn)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.S3ManifestConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.S3ManifestConfig()
        value.manifestObjectPath = try reader["ManifestObjectPath"].readIfPresent() ?? ""
        value.bucketAccessRoleArn = try reader["BucketAccessRoleArn"].readIfPresent() ?? ""
        value.s3BucketArn = try reader["S3BucketArn"].readIfPresent() ?? ""
        value.manifestObjectVersionId = try reader["ManifestObjectVersionId"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.TaskReportConfig {

    static func write(value: DataSyncClientTypes.TaskReportConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Destination"].write(value.destination, with: DataSyncClientTypes.ReportDestination.write(value:to:))
        try writer["ObjectVersionIds"].write(value.objectVersionIds)
        try writer["OutputType"].write(value.outputType)
        try writer["Overrides"].write(value.overrides, with: DataSyncClientTypes.ReportOverrides.write(value:to:))
        try writer["ReportLevel"].write(value.reportLevel)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.TaskReportConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.TaskReportConfig()
        value.destination = try reader["Destination"].readIfPresent(with: DataSyncClientTypes.ReportDestination.read(from:))
        value.outputType = try reader["OutputType"].readIfPresent()
        value.reportLevel = try reader["ReportLevel"].readIfPresent()
        value.objectVersionIds = try reader["ObjectVersionIds"].readIfPresent()
        value.overrides = try reader["Overrides"].readIfPresent(with: DataSyncClientTypes.ReportOverrides.read(from:))
        return value
    }
}

extension DataSyncClientTypes.ReportOverrides {

    static func write(value: DataSyncClientTypes.ReportOverrides?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Deleted"].write(value.deleted, with: DataSyncClientTypes.ReportOverride.write(value:to:))
        try writer["Skipped"].write(value.skipped, with: DataSyncClientTypes.ReportOverride.write(value:to:))
        try writer["Transferred"].write(value.transferred, with: DataSyncClientTypes.ReportOverride.write(value:to:))
        try writer["Verified"].write(value.verified, with: DataSyncClientTypes.ReportOverride.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.ReportOverrides {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.ReportOverrides()
        value.transferred = try reader["Transferred"].readIfPresent(with: DataSyncClientTypes.ReportOverride.read(from:))
        value.verified = try reader["Verified"].readIfPresent(with: DataSyncClientTypes.ReportOverride.read(from:))
        value.deleted = try reader["Deleted"].readIfPresent(with: DataSyncClientTypes.ReportOverride.read(from:))
        value.skipped = try reader["Skipped"].readIfPresent(with: DataSyncClientTypes.ReportOverride.read(from:))
        return value
    }
}

extension DataSyncClientTypes.ReportOverride {

    static func write(value: DataSyncClientTypes.ReportOverride?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ReportLevel"].write(value.reportLevel)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.ReportOverride {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.ReportOverride()
        value.reportLevel = try reader["ReportLevel"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.ReportDestination {

    static func write(value: DataSyncClientTypes.ReportDestination?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["S3"].write(value.s3, with: DataSyncClientTypes.ReportDestinationS3.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.ReportDestination {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.ReportDestination()
        value.s3 = try reader["S3"].readIfPresent(with: DataSyncClientTypes.ReportDestinationS3.read(from:))
        return value
    }
}

extension DataSyncClientTypes.ReportDestinationS3 {

    static func write(value: DataSyncClientTypes.ReportDestinationS3?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["BucketAccessRoleArn"].write(value.bucketAccessRoleArn)
        try writer["S3BucketArn"].write(value.s3BucketArn)
        try writer["Subdirectory"].write(value.subdirectory)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.ReportDestinationS3 {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.ReportDestinationS3()
        value.subdirectory = try reader["Subdirectory"].readIfPresent()
        value.s3BucketArn = try reader["S3BucketArn"].readIfPresent() ?? ""
        value.bucketAccessRoleArn = try reader["BucketAccessRoleArn"].readIfPresent() ?? ""
        return value
    }
}

extension DataSyncClientTypes.TaskScheduleDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.TaskScheduleDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.TaskScheduleDetails()
        value.statusUpdateTime = try reader["StatusUpdateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.disabledReason = try reader["DisabledReason"].readIfPresent()
        value.disabledBy = try reader["DisabledBy"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.TaskExecutionResultDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.TaskExecutionResultDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.TaskExecutionResultDetail()
        value.prepareDuration = try reader["PrepareDuration"].readIfPresent()
        value.prepareStatus = try reader["PrepareStatus"].readIfPresent()
        value.totalDuration = try reader["TotalDuration"].readIfPresent()
        value.transferDuration = try reader["TransferDuration"].readIfPresent()
        value.transferStatus = try reader["TransferStatus"].readIfPresent()
        value.verifyDuration = try reader["VerifyDuration"].readIfPresent()
        value.verifyStatus = try reader["VerifyStatus"].readIfPresent()
        value.errorCode = try reader["ErrorCode"].readIfPresent()
        value.errorDetail = try reader["ErrorDetail"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.ReportResult {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.ReportResult {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.ReportResult()
        value.status = try reader["Status"].readIfPresent()
        value.errorCode = try reader["ErrorCode"].readIfPresent()
        value.errorDetail = try reader["ErrorDetail"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.TaskExecutionFilesListedDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.TaskExecutionFilesListedDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.TaskExecutionFilesListedDetail()
        value.atSource = try reader["AtSource"].readIfPresent() ?? 0
        value.atDestinationForDelete = try reader["AtDestinationForDelete"].readIfPresent() ?? 0
        return value
    }
}

extension DataSyncClientTypes.TaskExecutionFilesFailedDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.TaskExecutionFilesFailedDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.TaskExecutionFilesFailedDetail()
        value.prepare = try reader["Prepare"].readIfPresent() ?? 0
        value.transfer = try reader["Transfer"].readIfPresent() ?? 0
        value.verify = try reader["Verify"].readIfPresent() ?? 0
        value.delete = try reader["Delete"].readIfPresent() ?? 0
        return value
    }
}

extension DataSyncClientTypes.AgentListEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.AgentListEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.AgentListEntry()
        value.agentArn = try reader["AgentArn"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.platform = try reader["Platform"].readIfPresent(with: DataSyncClientTypes.Platform.read(from:))
        return value
    }
}

extension DataSyncClientTypes.DiscoveryJobListEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.DiscoveryJobListEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.DiscoveryJobListEntry()
        value.discoveryJobArn = try reader["DiscoveryJobArn"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.LocationListEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.LocationListEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.LocationListEntry()
        value.locationArn = try reader["LocationArn"].readIfPresent()
        value.locationUri = try reader["LocationUri"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.StorageSystemListEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.StorageSystemListEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.StorageSystemListEntry()
        value.storageSystemArn = try reader["StorageSystemArn"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.TagListEntry {

    static func write(value: DataSyncClientTypes.TagListEntry?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Key"].write(value.key)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.TagListEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.TagListEntry()
        value.key = try reader["Key"].readIfPresent() ?? ""
        value.value = try reader["Value"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.TaskExecutionListEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.TaskExecutionListEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.TaskExecutionListEntry()
        value.taskExecutionArn = try reader["TaskExecutionArn"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.taskMode = try reader["TaskMode"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.TaskListEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DataSyncClientTypes.TaskListEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataSyncClientTypes.TaskListEntry()
        value.taskArn = try reader["TaskArn"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.taskMode = try reader["TaskMode"].readIfPresent()
        return value
    }
}

extension DataSyncClientTypes.Credentials {

    static func write(value: DataSyncClientTypes.Credentials?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Password"].write(value.password)
        try writer["Username"].write(value.username)
    }
}

extension DataSyncClientTypes.AzureBlobSasConfiguration {

    static func write(value: DataSyncClientTypes.AzureBlobSasConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Token"].write(value.token)
    }
}

extension DataSyncClientTypes.LocationFilter {

    static func write(value: DataSyncClientTypes.LocationFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
        try writer["Operator"].write(value.`operator`)
        try writer["Values"].writeList(value.values, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DataSyncClientTypes.TaskFilter {

    static func write(value: DataSyncClientTypes.TaskFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
        try writer["Operator"].write(value.`operator`)
        try writer["Values"].writeList(value.values, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DataSyncClientTypes.FsxUpdateProtocol {

    static func write(value: DataSyncClientTypes.FsxUpdateProtocol?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["NFS"].write(value.nfs, with: DataSyncClientTypes.FsxProtocolNfs.write(value:to:))
        try writer["SMB"].write(value.smb, with: DataSyncClientTypes.FsxUpdateProtocolSmb.write(value:to:))
    }
}

extension DataSyncClientTypes.FsxUpdateProtocolSmb {

    static func write(value: DataSyncClientTypes.FsxUpdateProtocolSmb?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Domain"].write(value.domain)
        try writer["MountOptions"].write(value.mountOptions, with: DataSyncClientTypes.SmbMountOptions.write(value:to:))
        try writer["Password"].write(value.password)
        try writer["User"].write(value.user)
    }
}

public enum DataSyncClientTypes {}
