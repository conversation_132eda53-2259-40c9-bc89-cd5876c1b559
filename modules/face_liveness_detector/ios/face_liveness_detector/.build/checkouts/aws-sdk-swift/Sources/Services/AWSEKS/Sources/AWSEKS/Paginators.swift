//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension EKSClient {
    /// Paginate over `[DescribeAddonVersionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeAddonVersionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeAddonVersionsOutput`
    public func describeAddonVersionsPaginated(input: DescribeAddonVersionsInput) -> ClientRuntime.PaginatorSequence<DescribeAddonVersionsInput, DescribeAddonVersionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeAddonVersionsInput, DescribeAddonVersionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeAddonVersions(input:))
    }
}

extension DescribeAddonVersionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeAddonVersionsInput {
        return DescribeAddonVersionsInput(
            addonName: self.addonName,
            kubernetesVersion: self.kubernetesVersion,
            maxResults: self.maxResults,
            nextToken: token,
            owners: self.owners,
            publishers: self.publishers,
            types: self.types
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeAddonVersionsInput, OperationStackOutput == DescribeAddonVersionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeAddonVersionsPaginated`
    /// to access the nested member `[EKSClientTypes.AddonInfo]`
    /// - Returns: `[EKSClientTypes.AddonInfo]`
    public func addons() async throws -> [EKSClientTypes.AddonInfo] {
        return try await self.asyncCompactMap { item in item.addons }
    }
}
extension EKSClient {
    /// Paginate over `[DescribeClusterVersionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeClusterVersionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeClusterVersionsOutput`
    public func describeClusterVersionsPaginated(input: DescribeClusterVersionsInput) -> ClientRuntime.PaginatorSequence<DescribeClusterVersionsInput, DescribeClusterVersionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeClusterVersionsInput, DescribeClusterVersionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeClusterVersions(input:))
    }
}

extension DescribeClusterVersionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeClusterVersionsInput {
        return DescribeClusterVersionsInput(
            clusterType: self.clusterType,
            clusterVersions: self.clusterVersions,
            defaultOnly: self.defaultOnly,
            includeAll: self.includeAll,
            maxResults: self.maxResults,
            nextToken: token,
            status: self.status
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeClusterVersionsInput, OperationStackOutput == DescribeClusterVersionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeClusterVersionsPaginated`
    /// to access the nested member `[EKSClientTypes.ClusterVersionInformation]`
    /// - Returns: `[EKSClientTypes.ClusterVersionInformation]`
    public func clusterVersions() async throws -> [EKSClientTypes.ClusterVersionInformation] {
        return try await self.asyncCompactMap { item in item.clusterVersions }
    }
}
extension EKSClient {
    /// Paginate over `[ListAccessEntriesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListAccessEntriesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListAccessEntriesOutput`
    public func listAccessEntriesPaginated(input: ListAccessEntriesInput) -> ClientRuntime.PaginatorSequence<ListAccessEntriesInput, ListAccessEntriesOutput> {
        return ClientRuntime.PaginatorSequence<ListAccessEntriesInput, ListAccessEntriesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listAccessEntries(input:))
    }
}

extension ListAccessEntriesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListAccessEntriesInput {
        return ListAccessEntriesInput(
            associatedPolicyArn: self.associatedPolicyArn,
            clusterName: self.clusterName,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListAccessEntriesInput, OperationStackOutput == ListAccessEntriesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listAccessEntriesPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func accessEntries() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.accessEntries }
    }
}
extension EKSClient {
    /// Paginate over `[ListAccessPoliciesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListAccessPoliciesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListAccessPoliciesOutput`
    public func listAccessPoliciesPaginated(input: ListAccessPoliciesInput) -> ClientRuntime.PaginatorSequence<ListAccessPoliciesInput, ListAccessPoliciesOutput> {
        return ClientRuntime.PaginatorSequence<ListAccessPoliciesInput, ListAccessPoliciesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listAccessPolicies(input:))
    }
}

extension ListAccessPoliciesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListAccessPoliciesInput {
        return ListAccessPoliciesInput(
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListAccessPoliciesInput, OperationStackOutput == ListAccessPoliciesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listAccessPoliciesPaginated`
    /// to access the nested member `[EKSClientTypes.AccessPolicy]`
    /// - Returns: `[EKSClientTypes.AccessPolicy]`
    public func accessPolicies() async throws -> [EKSClientTypes.AccessPolicy] {
        return try await self.asyncCompactMap { item in item.accessPolicies }
    }
}
extension EKSClient {
    /// Paginate over `[ListAddonsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListAddonsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListAddonsOutput`
    public func listAddonsPaginated(input: ListAddonsInput) -> ClientRuntime.PaginatorSequence<ListAddonsInput, ListAddonsOutput> {
        return ClientRuntime.PaginatorSequence<ListAddonsInput, ListAddonsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listAddons(input:))
    }
}

extension ListAddonsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListAddonsInput {
        return ListAddonsInput(
            clusterName: self.clusterName,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListAddonsInput, OperationStackOutput == ListAddonsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listAddonsPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func addons() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.addons }
    }
}
extension EKSClient {
    /// Paginate over `[ListAssociatedAccessPoliciesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListAssociatedAccessPoliciesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListAssociatedAccessPoliciesOutput`
    public func listAssociatedAccessPoliciesPaginated(input: ListAssociatedAccessPoliciesInput) -> ClientRuntime.PaginatorSequence<ListAssociatedAccessPoliciesInput, ListAssociatedAccessPoliciesOutput> {
        return ClientRuntime.PaginatorSequence<ListAssociatedAccessPoliciesInput, ListAssociatedAccessPoliciesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listAssociatedAccessPolicies(input:))
    }
}

extension ListAssociatedAccessPoliciesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListAssociatedAccessPoliciesInput {
        return ListAssociatedAccessPoliciesInput(
            clusterName: self.clusterName,
            maxResults: self.maxResults,
            nextToken: token,
            principalArn: self.principalArn
        )}
}

extension PaginatorSequence where OperationStackInput == ListAssociatedAccessPoliciesInput, OperationStackOutput == ListAssociatedAccessPoliciesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listAssociatedAccessPoliciesPaginated`
    /// to access the nested member `[EKSClientTypes.AssociatedAccessPolicy]`
    /// - Returns: `[EKSClientTypes.AssociatedAccessPolicy]`
    public func associatedAccessPolicies() async throws -> [EKSClientTypes.AssociatedAccessPolicy] {
        return try await self.asyncCompactMap { item in item.associatedAccessPolicies }
    }
}
extension EKSClient {
    /// Paginate over `[ListClustersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListClustersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListClustersOutput`
    public func listClustersPaginated(input: ListClustersInput) -> ClientRuntime.PaginatorSequence<ListClustersInput, ListClustersOutput> {
        return ClientRuntime.PaginatorSequence<ListClustersInput, ListClustersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listClusters(input:))
    }
}

extension ListClustersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListClustersInput {
        return ListClustersInput(
            include: self.include,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListClustersInput, OperationStackOutput == ListClustersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listClustersPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func clusters() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.clusters }
    }
}
extension EKSClient {
    /// Paginate over `[ListEksAnywhereSubscriptionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListEksAnywhereSubscriptionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListEksAnywhereSubscriptionsOutput`
    public func listEksAnywhereSubscriptionsPaginated(input: ListEksAnywhereSubscriptionsInput) -> ClientRuntime.PaginatorSequence<ListEksAnywhereSubscriptionsInput, ListEksAnywhereSubscriptionsOutput> {
        return ClientRuntime.PaginatorSequence<ListEksAnywhereSubscriptionsInput, ListEksAnywhereSubscriptionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listEksAnywhereSubscriptions(input:))
    }
}

extension ListEksAnywhereSubscriptionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListEksAnywhereSubscriptionsInput {
        return ListEksAnywhereSubscriptionsInput(
            includeStatus: self.includeStatus,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListEksAnywhereSubscriptionsInput, OperationStackOutput == ListEksAnywhereSubscriptionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listEksAnywhereSubscriptionsPaginated`
    /// to access the nested member `[EKSClientTypes.EksAnywhereSubscription]`
    /// - Returns: `[EKSClientTypes.EksAnywhereSubscription]`
    public func subscriptions() async throws -> [EKSClientTypes.EksAnywhereSubscription] {
        return try await self.asyncCompactMap { item in item.subscriptions }
    }
}
extension EKSClient {
    /// Paginate over `[ListFargateProfilesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListFargateProfilesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListFargateProfilesOutput`
    public func listFargateProfilesPaginated(input: ListFargateProfilesInput) -> ClientRuntime.PaginatorSequence<ListFargateProfilesInput, ListFargateProfilesOutput> {
        return ClientRuntime.PaginatorSequence<ListFargateProfilesInput, ListFargateProfilesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listFargateProfiles(input:))
    }
}

extension ListFargateProfilesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListFargateProfilesInput {
        return ListFargateProfilesInput(
            clusterName: self.clusterName,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListFargateProfilesInput, OperationStackOutput == ListFargateProfilesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listFargateProfilesPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func fargateProfileNames() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.fargateProfileNames }
    }
}
extension EKSClient {
    /// Paginate over `[ListIdentityProviderConfigsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListIdentityProviderConfigsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListIdentityProviderConfigsOutput`
    public func listIdentityProviderConfigsPaginated(input: ListIdentityProviderConfigsInput) -> ClientRuntime.PaginatorSequence<ListIdentityProviderConfigsInput, ListIdentityProviderConfigsOutput> {
        return ClientRuntime.PaginatorSequence<ListIdentityProviderConfigsInput, ListIdentityProviderConfigsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listIdentityProviderConfigs(input:))
    }
}

extension ListIdentityProviderConfigsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListIdentityProviderConfigsInput {
        return ListIdentityProviderConfigsInput(
            clusterName: self.clusterName,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListIdentityProviderConfigsInput, OperationStackOutput == ListIdentityProviderConfigsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listIdentityProviderConfigsPaginated`
    /// to access the nested member `[EKSClientTypes.IdentityProviderConfig]`
    /// - Returns: `[EKSClientTypes.IdentityProviderConfig]`
    public func identityProviderConfigs() async throws -> [EKSClientTypes.IdentityProviderConfig] {
        return try await self.asyncCompactMap { item in item.identityProviderConfigs }
    }
}
extension EKSClient {
    /// Paginate over `[ListInsightsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListInsightsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListInsightsOutput`
    public func listInsightsPaginated(input: ListInsightsInput) -> ClientRuntime.PaginatorSequence<ListInsightsInput, ListInsightsOutput> {
        return ClientRuntime.PaginatorSequence<ListInsightsInput, ListInsightsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listInsights(input:))
    }
}

extension ListInsightsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListInsightsInput {
        return ListInsightsInput(
            clusterName: self.clusterName,
            filter: self.filter,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListInsightsInput, OperationStackOutput == ListInsightsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listInsightsPaginated`
    /// to access the nested member `[EKSClientTypes.InsightSummary]`
    /// - Returns: `[EKSClientTypes.InsightSummary]`
    public func insights() async throws -> [EKSClientTypes.InsightSummary] {
        return try await self.asyncCompactMap { item in item.insights }
    }
}
extension EKSClient {
    /// Paginate over `[ListNodegroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListNodegroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListNodegroupsOutput`
    public func listNodegroupsPaginated(input: ListNodegroupsInput) -> ClientRuntime.PaginatorSequence<ListNodegroupsInput, ListNodegroupsOutput> {
        return ClientRuntime.PaginatorSequence<ListNodegroupsInput, ListNodegroupsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listNodegroups(input:))
    }
}

extension ListNodegroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListNodegroupsInput {
        return ListNodegroupsInput(
            clusterName: self.clusterName,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListNodegroupsInput, OperationStackOutput == ListNodegroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listNodegroupsPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func nodegroups() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.nodegroups }
    }
}
extension EKSClient {
    /// Paginate over `[ListPodIdentityAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListPodIdentityAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListPodIdentityAssociationsOutput`
    public func listPodIdentityAssociationsPaginated(input: ListPodIdentityAssociationsInput) -> ClientRuntime.PaginatorSequence<ListPodIdentityAssociationsInput, ListPodIdentityAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<ListPodIdentityAssociationsInput, ListPodIdentityAssociationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listPodIdentityAssociations(input:))
    }
}

extension ListPodIdentityAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListPodIdentityAssociationsInput {
        return ListPodIdentityAssociationsInput(
            clusterName: self.clusterName,
            maxResults: self.maxResults,
            namespace: self.namespace,
            nextToken: token,
            serviceAccount: self.serviceAccount
        )}
}

extension PaginatorSequence where OperationStackInput == ListPodIdentityAssociationsInput, OperationStackOutput == ListPodIdentityAssociationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listPodIdentityAssociationsPaginated`
    /// to access the nested member `[EKSClientTypes.PodIdentityAssociationSummary]`
    /// - Returns: `[EKSClientTypes.PodIdentityAssociationSummary]`
    public func associations() async throws -> [EKSClientTypes.PodIdentityAssociationSummary] {
        return try await self.asyncCompactMap { item in item.associations }
    }
}
extension EKSClient {
    /// Paginate over `[ListUpdatesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListUpdatesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListUpdatesOutput`
    public func listUpdatesPaginated(input: ListUpdatesInput) -> ClientRuntime.PaginatorSequence<ListUpdatesInput, ListUpdatesOutput> {
        return ClientRuntime.PaginatorSequence<ListUpdatesInput, ListUpdatesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listUpdates(input:))
    }
}

extension ListUpdatesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListUpdatesInput {
        return ListUpdatesInput(
            addonName: self.addonName,
            maxResults: self.maxResults,
            name: self.name,
            nextToken: token,
            nodegroupName: self.nodegroupName
        )}
}

extension PaginatorSequence where OperationStackInput == ListUpdatesInput, OperationStackOutput == ListUpdatesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listUpdatesPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func updateIds() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.updateIds }
    }
}
