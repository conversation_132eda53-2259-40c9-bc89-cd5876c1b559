//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.AWSJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
@_spi(SmithyReadWrite) import struct SmithyReadWrite.ReadingClosureBox
@_spi(SmithyReadWrite) import struct SmithyReadWrite.WritingClosureBox

/// The specified parameter is invalid. Review the available parameters for the API request.
public struct InvalidParameterException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidParameterException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified repository could not be found. Check the spelling of the specified repository and ensure that you are performing operations on the correct registry.
public struct RepositoryNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RepositoryNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// These errors are usually caused by a server-side issue.
public struct ServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct BatchCheckLayerAvailabilityInput: Swift.Sendable {
    /// The digests of the image layers to check.
    /// This member is required.
    public var layerDigests: [Swift.String]?
    /// The Amazon Web Services account ID associated with the registry that contains the image layers to check. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository that is associated with the image layers to check.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        layerDigests: [Swift.String]? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.layerDigests = layerDigests
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

extension ECRClientTypes {

    public enum LayerFailureCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case invalidlayerdigest
        case missinglayerdigest
        case sdkUnknown(Swift.String)

        public static var allCases: [LayerFailureCode] {
            return [
                .invalidlayerdigest,
                .missinglayerdigest
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .invalidlayerdigest: return "InvalidLayerDigest"
            case .missinglayerdigest: return "MissingLayerDigest"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// An object representing an Amazon ECR image layer failure.
    public struct LayerFailure: Swift.Sendable {
        /// The failure code associated with the failure.
        public var failureCode: ECRClientTypes.LayerFailureCode?
        /// The reason for the failure.
        public var failureReason: Swift.String?
        /// The layer digest associated with the failure.
        public var layerDigest: Swift.String?

        public init(
            failureCode: ECRClientTypes.LayerFailureCode? = nil,
            failureReason: Swift.String? = nil,
            layerDigest: Swift.String? = nil
        )
        {
            self.failureCode = failureCode
            self.failureReason = failureReason
            self.layerDigest = layerDigest
        }
    }
}

extension ECRClientTypes {

    public enum LayerAvailability: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case available
        case unavailable
        case sdkUnknown(Swift.String)

        public static var allCases: [LayerAvailability] {
            return [
                .available,
                .unavailable
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .available: return "AVAILABLE"
            case .unavailable: return "UNAVAILABLE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// An object representing an Amazon ECR image layer.
    public struct Layer: Swift.Sendable {
        /// The availability status of the image layer.
        public var layerAvailability: ECRClientTypes.LayerAvailability?
        /// The sha256 digest of the image layer.
        public var layerDigest: Swift.String?
        /// The size, in bytes, of the image layer.
        public var layerSize: Swift.Int?
        /// The media type of the layer, such as application/vnd.docker.image.rootfs.diff.tar.gzip or application/vnd.oci.image.layer.v1.tar+gzip.
        public var mediaType: Swift.String?

        public init(
            layerAvailability: ECRClientTypes.LayerAvailability? = nil,
            layerDigest: Swift.String? = nil,
            layerSize: Swift.Int? = nil,
            mediaType: Swift.String? = nil
        )
        {
            self.layerAvailability = layerAvailability
            self.layerDigest = layerDigest
            self.layerSize = layerSize
            self.mediaType = mediaType
        }
    }
}

public struct BatchCheckLayerAvailabilityOutput: Swift.Sendable {
    /// Any failures associated with the call.
    public var failures: [ECRClientTypes.LayerFailure]?
    /// A list of image layer objects corresponding to the image layer references in the request.
    public var layers: [ECRClientTypes.Layer]?

    public init(
        failures: [ECRClientTypes.LayerFailure]? = nil,
        layers: [ECRClientTypes.Layer]? = nil
    )
    {
        self.failures = failures
        self.layers = layers
    }
}

extension ECRClientTypes {

    /// An object with identifying information for an image in an Amazon ECR repository.
    public struct ImageIdentifier: Swift.Sendable {
        /// The sha256 digest of the image manifest.
        public var imageDigest: Swift.String?
        /// The tag used for the image.
        public var imageTag: Swift.String?

        public init(
            imageDigest: Swift.String? = nil,
            imageTag: Swift.String? = nil
        )
        {
            self.imageDigest = imageDigest
            self.imageTag = imageTag
        }
    }
}

/// Deletes specified images within a specified repository. Images are specified with either the imageTag or imageDigest.
public struct BatchDeleteImageInput: Swift.Sendable {
    /// A list of image ID references that correspond to images to delete. The format of the imageIds reference is imageTag=tag or imageDigest=digest.
    /// This member is required.
    public var imageIds: [ECRClientTypes.ImageIdentifier]?
    /// The Amazon Web Services account ID associated with the registry that contains the image to delete. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The repository that contains the image to delete.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        imageIds: [ECRClientTypes.ImageIdentifier]? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageIds = imageIds
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

extension ECRClientTypes {

    public enum ImageFailureCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case imagenotfound
        case imagereferencedbymanifestlist
        case imagetagdoesnotmatchdigest
        case invalidimagedigest
        case invalidimagetag
        case kmserror
        case missingdigestandtag
        case upstreamaccessdenied
        case upstreamtoomanyrequests
        case upstreamunavailable
        case sdkUnknown(Swift.String)

        public static var allCases: [ImageFailureCode] {
            return [
                .imagenotfound,
                .imagereferencedbymanifestlist,
                .imagetagdoesnotmatchdigest,
                .invalidimagedigest,
                .invalidimagetag,
                .kmserror,
                .missingdigestandtag,
                .upstreamaccessdenied,
                .upstreamtoomanyrequests,
                .upstreamunavailable
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .imagenotfound: return "ImageNotFound"
            case .imagereferencedbymanifestlist: return "ImageReferencedByManifestList"
            case .imagetagdoesnotmatchdigest: return "ImageTagDoesNotMatchDigest"
            case .invalidimagedigest: return "InvalidImageDigest"
            case .invalidimagetag: return "InvalidImageTag"
            case .kmserror: return "KmsError"
            case .missingdigestandtag: return "MissingDigestAndTag"
            case .upstreamaccessdenied: return "UpstreamAccessDenied"
            case .upstreamtoomanyrequests: return "UpstreamTooManyRequests"
            case .upstreamunavailable: return "UpstreamUnavailable"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// An object representing an Amazon ECR image failure.
    public struct ImageFailure: Swift.Sendable {
        /// The code associated with the failure.
        public var failureCode: ECRClientTypes.ImageFailureCode?
        /// The reason for the failure.
        public var failureReason: Swift.String?
        /// The image ID associated with the failure.
        public var imageId: ECRClientTypes.ImageIdentifier?

        public init(
            failureCode: ECRClientTypes.ImageFailureCode? = nil,
            failureReason: Swift.String? = nil,
            imageId: ECRClientTypes.ImageIdentifier? = nil
        )
        {
            self.failureCode = failureCode
            self.failureReason = failureReason
            self.imageId = imageId
        }
    }
}

public struct BatchDeleteImageOutput: Swift.Sendable {
    /// Any failures associated with the call.
    public var failures: [ECRClientTypes.ImageFailure]?
    /// The image IDs of the deleted images.
    public var imageIds: [ECRClientTypes.ImageIdentifier]?

    public init(
        failures: [ECRClientTypes.ImageFailure]? = nil,
        imageIds: [ECRClientTypes.ImageIdentifier]? = nil
    )
    {
        self.failures = failures
        self.imageIds = imageIds
    }
}

/// The operation did not succeed because it would have exceeded a service limit for your account. For more information, see [Amazon ECR service quotas](https://docs.aws.amazon.com/AmazonECR/latest/userguide/service-quotas.html) in the Amazon Elastic Container Registry User Guide.
public struct LimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The image or images were unable to be pulled using the pull through cache rule. This is usually caused because of an issue with the Secrets Manager secret containing the credentials for the upstream registry.
public struct UnableToGetUpstreamImageException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UnableToGetUpstreamImageException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct BatchGetImageInput: Swift.Sendable {
    /// The accepted media types for the request. Valid values: application/vnd.docker.distribution.manifest.v1+json | application/vnd.docker.distribution.manifest.v2+json | application/vnd.oci.image.manifest.v1+json
    public var acceptedMediaTypes: [Swift.String]?
    /// A list of image ID references that correspond to images to describe. The format of the imageIds reference is imageTag=tag or imageDigest=digest.
    /// This member is required.
    public var imageIds: [ECRClientTypes.ImageIdentifier]?
    /// The Amazon Web Services account ID associated with the registry that contains the images to describe. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The repository that contains the images to describe.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        acceptedMediaTypes: [Swift.String]? = nil,
        imageIds: [ECRClientTypes.ImageIdentifier]? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.acceptedMediaTypes = acceptedMediaTypes
        self.imageIds = imageIds
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

extension ECRClientTypes {

    /// An object representing an Amazon ECR image.
    public struct Image: Swift.Sendable {
        /// An object containing the image tag and image digest associated with an image.
        public var imageId: ECRClientTypes.ImageIdentifier?
        /// The image manifest associated with the image.
        public var imageManifest: Swift.String?
        /// The manifest media type of the image.
        public var imageManifestMediaType: Swift.String?
        /// The Amazon Web Services account ID associated with the registry containing the image.
        public var registryId: Swift.String?
        /// The name of the repository associated with the image.
        public var repositoryName: Swift.String?

        public init(
            imageId: ECRClientTypes.ImageIdentifier? = nil,
            imageManifest: Swift.String? = nil,
            imageManifestMediaType: Swift.String? = nil,
            registryId: Swift.String? = nil,
            repositoryName: Swift.String? = nil
        )
        {
            self.imageId = imageId
            self.imageManifest = imageManifest
            self.imageManifestMediaType = imageManifestMediaType
            self.registryId = registryId
            self.repositoryName = repositoryName
        }
    }
}

public struct BatchGetImageOutput: Swift.Sendable {
    /// Any failures associated with the call.
    public var failures: [ECRClientTypes.ImageFailure]?
    /// A list of image objects corresponding to the image references in the request.
    public var images: [ECRClientTypes.Image]?

    public init(
        failures: [ECRClientTypes.ImageFailure]? = nil,
        images: [ECRClientTypes.Image]? = nil
    )
    {
        self.failures = failures
        self.images = images
    }
}

/// There was an exception validating this request.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct BatchGetRepositoryScanningConfigurationInput: Swift.Sendable {
    /// One or more repository names to get the scanning configuration for.
    /// This member is required.
    public var repositoryNames: [Swift.String]?

    public init(
        repositoryNames: [Swift.String]? = nil
    )
    {
        self.repositoryNames = repositoryNames
    }
}

extension ECRClientTypes {

    public enum ScanningConfigurationFailureCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case repositoryNotFound
        case sdkUnknown(Swift.String)

        public static var allCases: [ScanningConfigurationFailureCode] {
            return [
                .repositoryNotFound
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .repositoryNotFound: return "REPOSITORY_NOT_FOUND"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// The details about any failures associated with the scanning configuration of a repository.
    public struct RepositoryScanningConfigurationFailure: Swift.Sendable {
        /// The failure code.
        public var failureCode: ECRClientTypes.ScanningConfigurationFailureCode?
        /// The reason for the failure.
        public var failureReason: Swift.String?
        /// The name of the repository.
        public var repositoryName: Swift.String?

        public init(
            failureCode: ECRClientTypes.ScanningConfigurationFailureCode? = nil,
            failureReason: Swift.String? = nil,
            repositoryName: Swift.String? = nil
        )
        {
            self.failureCode = failureCode
            self.failureReason = failureReason
            self.repositoryName = repositoryName
        }
    }
}

extension ECRClientTypes {

    public enum ScanningRepositoryFilterType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case wildcard
        case sdkUnknown(Swift.String)

        public static var allCases: [ScanningRepositoryFilterType] {
            return [
                .wildcard
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .wildcard: return "WILDCARD"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// The details of a scanning repository filter. For more information on how to use filters, see [Using filters](https://docs.aws.amazon.com/AmazonECR/latest/userguide/image-scanning.html#image-scanning-filters) in the Amazon Elastic Container Registry User Guide.
    public struct ScanningRepositoryFilter: Swift.Sendable {
        /// The filter to use when scanning.
        /// This member is required.
        public var filter: Swift.String?
        /// The type associated with the filter.
        /// This member is required.
        public var filterType: ECRClientTypes.ScanningRepositoryFilterType?

        public init(
            filter: Swift.String? = nil,
            filterType: ECRClientTypes.ScanningRepositoryFilterType? = nil
        )
        {
            self.filter = filter
            self.filterType = filterType
        }
    }
}

extension ECRClientTypes {

    public enum ScanFrequency: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case continuousScan
        case manual
        case scanOnPush
        case sdkUnknown(Swift.String)

        public static var allCases: [ScanFrequency] {
            return [
                .continuousScan,
                .manual,
                .scanOnPush
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .continuousScan: return "CONTINUOUS_SCAN"
            case .manual: return "MANUAL"
            case .scanOnPush: return "SCAN_ON_PUSH"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// The details of the scanning configuration for a repository.
    public struct RepositoryScanningConfiguration: Swift.Sendable {
        /// The scan filters applied to the repository.
        public var appliedScanFilters: [ECRClientTypes.ScanningRepositoryFilter]?
        /// The ARN of the repository.
        public var repositoryArn: Swift.String?
        /// The name of the repository.
        public var repositoryName: Swift.String?
        /// The scan frequency for the repository.
        public var scanFrequency: ECRClientTypes.ScanFrequency?
        /// Whether or not scan on push is configured for the repository.
        public var scanOnPush: Swift.Bool

        public init(
            appliedScanFilters: [ECRClientTypes.ScanningRepositoryFilter]? = nil,
            repositoryArn: Swift.String? = nil,
            repositoryName: Swift.String? = nil,
            scanFrequency: ECRClientTypes.ScanFrequency? = nil,
            scanOnPush: Swift.Bool = false
        )
        {
            self.appliedScanFilters = appliedScanFilters
            self.repositoryArn = repositoryArn
            self.repositoryName = repositoryName
            self.scanFrequency = scanFrequency
            self.scanOnPush = scanOnPush
        }
    }
}

public struct BatchGetRepositoryScanningConfigurationOutput: Swift.Sendable {
    /// Any failures associated with the call.
    public var failures: [ECRClientTypes.RepositoryScanningConfigurationFailure]?
    /// The scanning configuration for the requested repositories.
    public var scanningConfigurations: [ECRClientTypes.RepositoryScanningConfiguration]?

    public init(
        failures: [ECRClientTypes.RepositoryScanningConfigurationFailure]? = nil,
        scanningConfigurations: [ECRClientTypes.RepositoryScanningConfiguration]? = nil
    )
    {
        self.failures = failures
        self.scanningConfigurations = scanningConfigurations
    }
}

/// The specified layer upload does not contain any layer parts.
public struct EmptyUploadException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "EmptyUploadException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The layer digest calculation performed by Amazon ECR upon receipt of the image layer does not match the digest specified.
public struct InvalidLayerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidLayerException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The operation failed due to a KMS exception.
public struct KmsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code returned by KMS.
        public internal(set) var kmsError: Swift.String? = nil
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "KmsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        kmsError: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.kmsError = kmsError
        self.properties.message = message
    }
}

/// The image layer already exists in the associated repository.
public struct LayerAlreadyExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LayerAlreadyExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Layer parts must be at least 5 MiB in size.
public struct LayerPartTooSmallException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LayerPartTooSmallException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The upload could not be found, or the specified upload ID is not valid for this repository.
public struct UploadNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UploadNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct CompleteLayerUploadInput: Swift.Sendable {
    /// The sha256 digest of the image layer.
    /// This member is required.
    public var layerDigests: [Swift.String]?
    /// The Amazon Web Services account ID associated with the registry to which to upload layers. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository to associate with the image layer.
    /// This member is required.
    public var repositoryName: Swift.String?
    /// The upload ID from a previous [InitiateLayerUpload] operation to associate with the image layer.
    /// This member is required.
    public var uploadId: Swift.String?

    public init(
        layerDigests: [Swift.String]? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil,
        uploadId: Swift.String? = nil
    )
    {
        self.layerDigests = layerDigests
        self.registryId = registryId
        self.repositoryName = repositoryName
        self.uploadId = uploadId
    }
}

public struct CompleteLayerUploadOutput: Swift.Sendable {
    /// The sha256 digest of the image layer.
    public var layerDigest: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?
    /// The upload ID associated with the layer.
    public var uploadId: Swift.String?

    public init(
        layerDigest: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil,
        uploadId: Swift.String? = nil
    )
    {
        self.layerDigest = layerDigest
        self.registryId = registryId
        self.repositoryName = repositoryName
        self.uploadId = uploadId
    }
}

/// A pull through cache rule with these settings already exists for the private registry.
public struct PullThroughCacheRuleAlreadyExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "PullThroughCacheRuleAlreadyExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The ARN of the secret specified in the pull through cache rule was not found. Update the pull through cache rule with a valid secret ARN and try again.
public struct SecretNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SecretNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The secret is unable to be accessed. Verify the resource permissions for the secret and try again.
public struct UnableToAccessSecretException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UnableToAccessSecretException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The secret is accessible but is unable to be decrypted. Verify the resource permisisons and try again.
public struct UnableToDecryptSecretValueException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UnableToDecryptSecretValueException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified upstream registry isn't supported.
public struct UnsupportedUpstreamRegistryException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UnsupportedUpstreamRegistryException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension ECRClientTypes {

    public enum UpstreamRegistry: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case azurecontainerregistry
        case dockerhub
        case ecrpublic
        case githubcontainerregistry
        case gitlabcontainerregistry
        case k8s
        case quay
        case sdkUnknown(Swift.String)

        public static var allCases: [UpstreamRegistry] {
            return [
                .azurecontainerregistry,
                .dockerhub,
                .ecrpublic,
                .githubcontainerregistry,
                .gitlabcontainerregistry,
                .k8s,
                .quay
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .azurecontainerregistry: return "azure-container-registry"
            case .dockerhub: return "docker-hub"
            case .ecrpublic: return "ecr-public"
            case .githubcontainerregistry: return "github-container-registry"
            case .gitlabcontainerregistry: return "gitlab-container-registry"
            case .k8s: return "k8s"
            case .quay: return "quay"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct CreatePullThroughCacheRuleInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the Amazon Web Services Secrets Manager secret that identifies the credentials to authenticate to the upstream registry.
    public var credentialArn: Swift.String?
    /// The repository name prefix to use when caching images from the source registry.
    /// This member is required.
    public var ecrRepositoryPrefix: Swift.String?
    /// The Amazon Web Services account ID associated with the registry to create the pull through cache rule for. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the upstream registry.
    public var upstreamRegistry: ECRClientTypes.UpstreamRegistry?
    /// The registry URL of the upstream public registry to use as the source for the pull through cache rule. The following is the syntax to use for each supported upstream registry.
    ///
    /// * Amazon ECR Public (ecr-public) - public.ecr.aws
    ///
    /// * Docker Hub (docker-hub) - registry-1.docker.io
    ///
    /// * Quay (quay) - quay.io
    ///
    /// * Kubernetes (k8s) - registry.k8s.io
    ///
    /// * GitHub Container Registry (github-container-registry) - ghcr.io
    ///
    /// * Microsoft Azure Container Registry (azure-container-registry) - .azurecr.io
    /// This member is required.
    public var upstreamRegistryUrl: Swift.String?

    public init(
        credentialArn: Swift.String? = nil,
        ecrRepositoryPrefix: Swift.String? = nil,
        registryId: Swift.String? = nil,
        upstreamRegistry: ECRClientTypes.UpstreamRegistry? = nil,
        upstreamRegistryUrl: Swift.String? = nil
    )
    {
        self.credentialArn = credentialArn
        self.ecrRepositoryPrefix = ecrRepositoryPrefix
        self.registryId = registryId
        self.upstreamRegistry = upstreamRegistry
        self.upstreamRegistryUrl = upstreamRegistryUrl
    }
}

public struct CreatePullThroughCacheRuleOutput: Swift.Sendable {
    /// The date and time, in JavaScript date format, when the pull through cache rule was created.
    public var createdAt: Foundation.Date?
    /// The Amazon Resource Name (ARN) of the Amazon Web Services Secrets Manager secret associated with the pull through cache rule.
    public var credentialArn: Swift.String?
    /// The Amazon ECR repository prefix associated with the pull through cache rule.
    public var ecrRepositoryPrefix: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The name of the upstream registry associated with the pull through cache rule.
    public var upstreamRegistry: ECRClientTypes.UpstreamRegistry?
    /// The upstream registry URL associated with the pull through cache rule.
    public var upstreamRegistryUrl: Swift.String?

    public init(
        createdAt: Foundation.Date? = nil,
        credentialArn: Swift.String? = nil,
        ecrRepositoryPrefix: Swift.String? = nil,
        registryId: Swift.String? = nil,
        upstreamRegistry: ECRClientTypes.UpstreamRegistry? = nil,
        upstreamRegistryUrl: Swift.String? = nil
    )
    {
        self.createdAt = createdAt
        self.credentialArn = credentialArn
        self.ecrRepositoryPrefix = ecrRepositoryPrefix
        self.registryId = registryId
        self.upstreamRegistry = upstreamRegistry
        self.upstreamRegistryUrl = upstreamRegistryUrl
    }
}

/// An invalid parameter has been specified. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
public struct InvalidTagParameterException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidTagParameterException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified repository already exists in the specified registry.
public struct RepositoryAlreadyExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RepositoryAlreadyExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The list of tags on the repository is over the limit. The maximum number of tags that can be applied to a repository is 50.
public struct TooManyTagsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TooManyTagsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension ECRClientTypes {

    public enum EncryptionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case aes256
        case kms
        case kmsDsse
        case sdkUnknown(Swift.String)

        public static var allCases: [EncryptionType] {
            return [
                .aes256,
                .kms,
                .kmsDsse
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .aes256: return "AES256"
            case .kms: return "KMS"
            case .kmsDsse: return "KMS_DSSE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// The encryption configuration for the repository. This determines how the contents of your repository are encrypted at rest. By default, when no encryption configuration is set or the AES256 encryption type is used, Amazon ECR uses server-side encryption with Amazon S3-managed encryption keys which encrypts your data at rest using an AES256 encryption algorithm. This does not require any action on your part. For more control over the encryption of the contents of your repository, you can use server-side encryption with Key Management Service key stored in Key Management Service (KMS) to encrypt your images. For more information, see [Amazon ECR encryption at rest](https://docs.aws.amazon.com/AmazonECR/latest/userguide/encryption-at-rest.html) in the Amazon Elastic Container Registry User Guide.
    public struct EncryptionConfiguration: Swift.Sendable {
        /// The encryption type to use. If you use the KMS encryption type, the contents of the repository will be encrypted using server-side encryption with Key Management Service key stored in KMS. When you use KMS to encrypt your data, you can either use the default Amazon Web Services managed KMS key for Amazon ECR, or specify your own KMS key, which you already created. If you use the KMS_DSSE encryption type, the contents of the repository will be encrypted with two layers of encryption using server-side encryption with the KMS Management Service key stored in KMS. Similar to the KMS encryption type, you can either use the default Amazon Web Services managed KMS key for Amazon ECR, or specify your own KMS key, which you've already created. If you use the AES256 encryption type, Amazon ECR uses server-side encryption with Amazon S3-managed encryption keys which encrypts the images in the repository using an AES256 encryption algorithm. For more information, see [Amazon ECR encryption at rest](https://docs.aws.amazon.com/AmazonECR/latest/userguide/encryption-at-rest.html) in the Amazon Elastic Container Registry User Guide.
        /// This member is required.
        public var encryptionType: ECRClientTypes.EncryptionType?
        /// If you use the KMS encryption type, specify the KMS key to use for encryption. The alias, key ID, or full ARN of the KMS key can be specified. The key must exist in the same Region as the repository. If no key is specified, the default Amazon Web Services managed KMS key for Amazon ECR will be used.
        public var kmsKey: Swift.String?

        public init(
            encryptionType: ECRClientTypes.EncryptionType? = nil,
            kmsKey: Swift.String? = nil
        )
        {
            self.encryptionType = encryptionType
            self.kmsKey = kmsKey
        }
    }
}

extension ECRClientTypes {

    /// The image scanning configuration for a repository.
    public struct ImageScanningConfiguration: Swift.Sendable {
        /// The setting that determines whether images are scanned after being pushed to a repository. If set to true, images will be scanned after being pushed. If this parameter is not specified, it will default to false and images will not be scanned unless a scan is manually started with the [API_StartImageScan](https://docs.aws.amazon.com/AmazonECR/latest/APIReference/API_StartImageScan.html) API.
        public var scanOnPush: Swift.Bool

        public init(
            scanOnPush: Swift.Bool = false
        )
        {
            self.scanOnPush = scanOnPush
        }
    }
}

extension ECRClientTypes {

    public enum ImageTagMutability: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case immutable
        case mutable
        case sdkUnknown(Swift.String)

        public static var allCases: [ImageTagMutability] {
            return [
                .immutable,
                .mutable
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .immutable: return "IMMUTABLE"
            case .mutable: return "MUTABLE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// The metadata to apply to a resource to help you categorize and organize them. Each tag consists of a key and a value, both of which you define. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
    public struct Tag: Swift.Sendable {
        /// One part of a key-value pair that make up a tag. A key is a general label that acts like a category for more specific tag values.
        /// This member is required.
        public var key: Swift.String?
        /// A value acts as a descriptor within a tag category (key).
        /// This member is required.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

public struct CreateRepositoryInput: Swift.Sendable {
    /// The encryption configuration for the repository. This determines how the contents of your repository are encrypted at rest.
    public var encryptionConfiguration: ECRClientTypes.EncryptionConfiguration?
    /// The image scanning configuration for the repository. This determines whether images are scanned for known vulnerabilities after being pushed to the repository.
    public var imageScanningConfiguration: ECRClientTypes.ImageScanningConfiguration?
    /// The tag mutability setting for the repository. If this parameter is omitted, the default setting of MUTABLE will be used which will allow image tags to be overwritten. If IMMUTABLE is specified, all image tags within the repository will be immutable which will prevent them from being overwritten.
    public var imageTagMutability: ECRClientTypes.ImageTagMutability?
    /// The Amazon Web Services account ID associated with the registry to create the repository. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name to use for the repository. The repository name may be specified on its own (such as nginx-web-app) or it can be prepended with a namespace to group the repository into a category (such as project-a/nginx-web-app). The repository name must start with a letter and can only contain lowercase letters, numbers, hyphens, underscores, and forward slashes.
    /// This member is required.
    public var repositoryName: Swift.String?
    /// The metadata that you apply to the repository to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
    public var tags: [ECRClientTypes.Tag]?

    public init(
        encryptionConfiguration: ECRClientTypes.EncryptionConfiguration? = nil,
        imageScanningConfiguration: ECRClientTypes.ImageScanningConfiguration? = nil,
        imageTagMutability: ECRClientTypes.ImageTagMutability? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil,
        tags: [ECRClientTypes.Tag]? = nil
    )
    {
        self.encryptionConfiguration = encryptionConfiguration
        self.imageScanningConfiguration = imageScanningConfiguration
        self.imageTagMutability = imageTagMutability
        self.registryId = registryId
        self.repositoryName = repositoryName
        self.tags = tags
    }
}

extension ECRClientTypes {

    /// An object representing a repository.
    public struct Repository: Swift.Sendable {
        /// The date and time, in JavaScript date format, when the repository was created.
        public var createdAt: Foundation.Date?
        /// The encryption configuration for the repository. This determines how the contents of your repository are encrypted at rest.
        public var encryptionConfiguration: ECRClientTypes.EncryptionConfiguration?
        /// The image scanning configuration for a repository.
        public var imageScanningConfiguration: ECRClientTypes.ImageScanningConfiguration?
        /// The tag mutability setting for the repository.
        public var imageTagMutability: ECRClientTypes.ImageTagMutability?
        /// The Amazon Web Services account ID associated with the registry that contains the repository.
        public var registryId: Swift.String?
        /// The Amazon Resource Name (ARN) that identifies the repository. The ARN contains the arn:aws:ecr namespace, followed by the region of the repository, Amazon Web Services account ID of the repository owner, repository namespace, and repository name. For example, arn:aws:ecr:region:************:repository-namespace/repository-name.
        public var repositoryArn: Swift.String?
        /// The name of the repository.
        public var repositoryName: Swift.String?
        /// The URI for the repository. You can use this URI for container image push and pull operations.
        public var repositoryUri: Swift.String?

        public init(
            createdAt: Foundation.Date? = nil,
            encryptionConfiguration: ECRClientTypes.EncryptionConfiguration? = nil,
            imageScanningConfiguration: ECRClientTypes.ImageScanningConfiguration? = nil,
            imageTagMutability: ECRClientTypes.ImageTagMutability? = nil,
            registryId: Swift.String? = nil,
            repositoryArn: Swift.String? = nil,
            repositoryName: Swift.String? = nil,
            repositoryUri: Swift.String? = nil
        )
        {
            self.createdAt = createdAt
            self.encryptionConfiguration = encryptionConfiguration
            self.imageScanningConfiguration = imageScanningConfiguration
            self.imageTagMutability = imageTagMutability
            self.registryId = registryId
            self.repositoryArn = repositoryArn
            self.repositoryName = repositoryName
            self.repositoryUri = repositoryUri
        }
    }
}

public struct CreateRepositoryOutput: Swift.Sendable {
    /// The repository that was created.
    public var repository: ECRClientTypes.Repository?

    public init(
        repository: ECRClientTypes.Repository? = nil
    )
    {
        self.repository = repository
    }
}

/// The repository creation template already exists. Specify a unique prefix and try again.
public struct TemplateAlreadyExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TemplateAlreadyExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension ECRClientTypes {

    public enum RCTAppliedFor: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case pullThroughCache
        case replication
        case sdkUnknown(Swift.String)

        public static var allCases: [RCTAppliedFor] {
            return [
                .pullThroughCache,
                .replication
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .pullThroughCache: return "PULL_THROUGH_CACHE"
            case .replication: return "REPLICATION"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// The encryption configuration to associate with the repository creation template.
    public struct EncryptionConfigurationForRepositoryCreationTemplate: Swift.Sendable {
        /// The encryption type to use. If you use the KMS encryption type, the contents of the repository will be encrypted using server-side encryption with Key Management Service key stored in KMS. When you use KMS to encrypt your data, you can either use the default Amazon Web Services managed KMS key for Amazon ECR, or specify your own KMS key, which you already created. For more information, see [Protecting data using server-side encryption with an KMS key stored in Key Management Service (SSE-KMS)](https://docs.aws.amazon.com/AmazonS3/latest/dev/UsingKMSEncryption.html) in the Amazon Simple Storage Service Console Developer Guide. If you use the AES256 encryption type, Amazon ECR uses server-side encryption with Amazon S3-managed encryption keys which encrypts the images in the repository using an AES256 encryption algorithm. For more information, see [Protecting data using server-side encryption with Amazon S3-managed encryption keys (SSE-S3)](https://docs.aws.amazon.com/AmazonS3/latest/dev/UsingServerSideEncryption.html) in the Amazon Simple Storage Service Console Developer Guide.
        /// This member is required.
        public var encryptionType: ECRClientTypes.EncryptionType?
        /// If you use the KMS encryption type, specify the KMS key to use for encryption. The full ARN of the KMS key must be specified. The key must exist in the same Region as the repository. If no key is specified, the default Amazon Web Services managed KMS key for Amazon ECR will be used.
        public var kmsKey: Swift.String?

        public init(
            encryptionType: ECRClientTypes.EncryptionType? = nil,
            kmsKey: Swift.String? = nil
        )
        {
            self.encryptionType = encryptionType
            self.kmsKey = kmsKey
        }
    }
}

public struct CreateRepositoryCreationTemplateInput: Swift.Sendable {
    /// A list of enumerable strings representing the Amazon ECR repository creation scenarios that this template will apply towards. The two supported scenarios are PULL_THROUGH_CACHE and REPLICATION
    /// This member is required.
    public var appliedFor: [ECRClientTypes.RCTAppliedFor]?
    /// The ARN of the role to be assumed by Amazon ECR. This role must be in the same account as the registry that you are configuring. Amazon ECR will assume your supplied role when the customRoleArn is specified. When this field isn't specified, Amazon ECR will use the service-linked role for the repository creation template.
    public var customRoleArn: Swift.String?
    /// A description for the repository creation template.
    public var description: Swift.String?
    /// The encryption configuration to use for repositories created using the template.
    public var encryptionConfiguration: ECRClientTypes.EncryptionConfigurationForRepositoryCreationTemplate?
    /// The tag mutability setting for the repository. If this parameter is omitted, the default setting of MUTABLE will be used which will allow image tags to be overwritten. If IMMUTABLE is specified, all image tags within the repository will be immutable which will prevent them from being overwritten.
    public var imageTagMutability: ECRClientTypes.ImageTagMutability?
    /// The lifecycle policy to use for repositories created using the template.
    public var lifecyclePolicy: Swift.String?
    /// The repository namespace prefix to associate with the template. All repositories created using this namespace prefix will have the settings defined in this template applied. For example, a prefix of prod would apply to all repositories beginning with prod/. Similarly, a prefix of prod/team would apply to all repositories beginning with prod/team/. To apply a template to all repositories in your registry that don't have an associated creation template, you can use ROOT as the prefix. There is always an assumed / applied to the end of the prefix. If you specify ecr-public as the prefix, Amazon ECR treats that as ecr-public/. When using a pull through cache rule, the repository prefix you specify during rule creation is what you should specify as your repository creation template prefix as well.
    /// This member is required.
    public var `prefix`: Swift.String?
    /// The repository policy to apply to repositories created using the template. A repository policy is a permissions policy associated with a repository to control access permissions.
    public var repositoryPolicy: Swift.String?
    /// The metadata to apply to the repository to help you categorize and organize. Each tag consists of a key and an optional value, both of which you define. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
    public var resourceTags: [ECRClientTypes.Tag]?

    public init(
        appliedFor: [ECRClientTypes.RCTAppliedFor]? = nil,
        customRoleArn: Swift.String? = nil,
        description: Swift.String? = nil,
        encryptionConfiguration: ECRClientTypes.EncryptionConfigurationForRepositoryCreationTemplate? = nil,
        imageTagMutability: ECRClientTypes.ImageTagMutability? = nil,
        lifecyclePolicy: Swift.String? = nil,
        `prefix`: Swift.String? = nil,
        repositoryPolicy: Swift.String? = nil,
        resourceTags: [ECRClientTypes.Tag]? = nil
    )
    {
        self.appliedFor = appliedFor
        self.customRoleArn = customRoleArn
        self.description = description
        self.encryptionConfiguration = encryptionConfiguration
        self.imageTagMutability = imageTagMutability
        self.lifecyclePolicy = lifecyclePolicy
        self.`prefix` = `prefix`
        self.repositoryPolicy = repositoryPolicy
        self.resourceTags = resourceTags
    }
}

extension ECRClientTypes {

    /// The details of the repository creation template associated with the request.
    public struct RepositoryCreationTemplate: Swift.Sendable {
        /// A list of enumerable Strings representing the repository creation scenarios that this template will apply towards. The two supported scenarios are PULL_THROUGH_CACHE and REPLICATION
        public var appliedFor: [ECRClientTypes.RCTAppliedFor]?
        /// The date and time, in JavaScript date format, when the repository creation template was created.
        public var createdAt: Foundation.Date?
        /// The ARN of the role to be assumed by Amazon ECR. Amazon ECR will assume your supplied role when the customRoleArn is specified. When this field isn't specified, Amazon ECR will use the service-linked role for the repository creation template.
        public var customRoleArn: Swift.String?
        /// The description associated with the repository creation template.
        public var description: Swift.String?
        /// The encryption configuration associated with the repository creation template.
        public var encryptionConfiguration: ECRClientTypes.EncryptionConfigurationForRepositoryCreationTemplate?
        /// The tag mutability setting for the repository. If this parameter is omitted, the default setting of MUTABLE will be used which will allow image tags to be overwritten. If IMMUTABLE is specified, all image tags within the repository will be immutable which will prevent them from being overwritten.
        public var imageTagMutability: ECRClientTypes.ImageTagMutability?
        /// The lifecycle policy to use for repositories created using the template.
        public var lifecyclePolicy: Swift.String?
        /// The repository namespace prefix associated with the repository creation template.
        public var `prefix`: Swift.String?
        /// he repository policy to apply to repositories created using the template. A repository policy is a permissions policy associated with a repository to control access permissions.
        public var repositoryPolicy: Swift.String?
        /// The metadata to apply to the repository to help you categorize and organize. Each tag consists of a key and an optional value, both of which you define. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
        public var resourceTags: [ECRClientTypes.Tag]?
        /// The date and time, in JavaScript date format, when the repository creation template was last updated.
        public var updatedAt: Foundation.Date?

        public init(
            appliedFor: [ECRClientTypes.RCTAppliedFor]? = nil,
            createdAt: Foundation.Date? = nil,
            customRoleArn: Swift.String? = nil,
            description: Swift.String? = nil,
            encryptionConfiguration: ECRClientTypes.EncryptionConfigurationForRepositoryCreationTemplate? = nil,
            imageTagMutability: ECRClientTypes.ImageTagMutability? = nil,
            lifecyclePolicy: Swift.String? = nil,
            `prefix`: Swift.String? = nil,
            repositoryPolicy: Swift.String? = nil,
            resourceTags: [ECRClientTypes.Tag]? = nil,
            updatedAt: Foundation.Date? = nil
        )
        {
            self.appliedFor = appliedFor
            self.createdAt = createdAt
            self.customRoleArn = customRoleArn
            self.description = description
            self.encryptionConfiguration = encryptionConfiguration
            self.imageTagMutability = imageTagMutability
            self.lifecyclePolicy = lifecyclePolicy
            self.`prefix` = `prefix`
            self.repositoryPolicy = repositoryPolicy
            self.resourceTags = resourceTags
            self.updatedAt = updatedAt
        }
    }
}

public struct CreateRepositoryCreationTemplateOutput: Swift.Sendable {
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The details of the repository creation template associated with the request.
    public var repositoryCreationTemplate: ECRClientTypes.RepositoryCreationTemplate?

    public init(
        registryId: Swift.String? = nil,
        repositoryCreationTemplate: ECRClientTypes.RepositoryCreationTemplate? = nil
    )
    {
        self.registryId = registryId
        self.repositoryCreationTemplate = repositoryCreationTemplate
    }
}

/// The lifecycle policy could not be found, and no policy is set to the repository.
public struct LifecyclePolicyNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LifecyclePolicyNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DeleteLifecyclePolicyInput: Swift.Sendable {
    /// The Amazon Web Services account ID associated with the registry that contains the repository. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct DeleteLifecyclePolicyOutput: Swift.Sendable {
    /// The time stamp of the last time that the lifecycle policy was run.
    public var lastEvaluatedAt: Foundation.Date?
    /// The JSON lifecycle policy text.
    public var lifecyclePolicyText: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?

    public init(
        lastEvaluatedAt: Foundation.Date? = nil,
        lifecyclePolicyText: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.lastEvaluatedAt = lastEvaluatedAt
        self.lifecyclePolicyText = lifecyclePolicyText
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

/// The pull through cache rule was not found. Specify a valid pull through cache rule and try again.
public struct PullThroughCacheRuleNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "PullThroughCacheRuleNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DeletePullThroughCacheRuleInput: Swift.Sendable {
    /// The Amazon ECR repository prefix associated with the pull through cache rule to delete.
    /// This member is required.
    public var ecrRepositoryPrefix: Swift.String?
    /// The Amazon Web Services account ID associated with the registry that contains the pull through cache rule. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?

    public init(
        ecrRepositoryPrefix: Swift.String? = nil,
        registryId: Swift.String? = nil
    )
    {
        self.ecrRepositoryPrefix = ecrRepositoryPrefix
        self.registryId = registryId
    }
}

public struct DeletePullThroughCacheRuleOutput: Swift.Sendable {
    /// The timestamp associated with the pull through cache rule.
    public var createdAt: Foundation.Date?
    /// The Amazon Resource Name (ARN) of the Amazon Web Services Secrets Manager secret associated with the pull through cache rule.
    public var credentialArn: Swift.String?
    /// The Amazon ECR repository prefix associated with the request.
    public var ecrRepositoryPrefix: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The upstream registry URL associated with the pull through cache rule.
    public var upstreamRegistryUrl: Swift.String?

    public init(
        createdAt: Foundation.Date? = nil,
        credentialArn: Swift.String? = nil,
        ecrRepositoryPrefix: Swift.String? = nil,
        registryId: Swift.String? = nil,
        upstreamRegistryUrl: Swift.String? = nil
    )
    {
        self.createdAt = createdAt
        self.credentialArn = credentialArn
        self.ecrRepositoryPrefix = ecrRepositoryPrefix
        self.registryId = registryId
        self.upstreamRegistryUrl = upstreamRegistryUrl
    }
}

/// The registry doesn't have an associated registry policy.
public struct RegistryPolicyNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RegistryPolicyNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DeleteRegistryPolicyInput: Swift.Sendable {

    public init() { }
}

public struct DeleteRegistryPolicyOutput: Swift.Sendable {
    /// The contents of the registry permissions policy that was deleted.
    public var policyText: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?

    public init(
        policyText: Swift.String? = nil,
        registryId: Swift.String? = nil
    )
    {
        self.policyText = policyText
        self.registryId = registryId
    }
}

/// The specified repository contains images. To delete a repository that contains images, you must force the deletion with the force parameter.
public struct RepositoryNotEmptyException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RepositoryNotEmptyException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DeleteRepositoryInput: Swift.Sendable {
    /// If true, deleting the repository force deletes the contents of the repository. If false, the repository must be empty before attempting to delete it.
    public var force: Swift.Bool?
    /// The Amazon Web Services account ID associated with the registry that contains the repository to delete. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository to delete.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        force: Swift.Bool? = false,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.force = force
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct DeleteRepositoryOutput: Swift.Sendable {
    /// The repository that was deleted.
    public var repository: ECRClientTypes.Repository?

    public init(
        repository: ECRClientTypes.Repository? = nil
    )
    {
        self.repository = repository
    }
}

/// The specified repository creation template can't be found. Verify the registry ID and prefix and try again.
public struct TemplateNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TemplateNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DeleteRepositoryCreationTemplateInput: Swift.Sendable {
    /// The repository namespace prefix associated with the repository creation template.
    /// This member is required.
    public var `prefix`: Swift.String?

    public init(
        `prefix`: Swift.String? = nil
    )
    {
        self.`prefix` = `prefix`
    }
}

public struct DeleteRepositoryCreationTemplateOutput: Swift.Sendable {
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The details of the repository creation template that was deleted.
    public var repositoryCreationTemplate: ECRClientTypes.RepositoryCreationTemplate?

    public init(
        registryId: Swift.String? = nil,
        repositoryCreationTemplate: ECRClientTypes.RepositoryCreationTemplate? = nil
    )
    {
        self.registryId = registryId
        self.repositoryCreationTemplate = repositoryCreationTemplate
    }
}

/// The specified repository and registry combination does not have an associated repository policy.
public struct RepositoryPolicyNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RepositoryPolicyNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DeleteRepositoryPolicyInput: Swift.Sendable {
    /// The Amazon Web Services account ID associated with the registry that contains the repository policy to delete. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository that is associated with the repository policy to delete.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct DeleteRepositoryPolicyOutput: Swift.Sendable {
    /// The JSON repository policy that was deleted from the repository.
    public var policyText: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?

    public init(
        policyText: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.policyText = policyText
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

/// The image requested does not exist in the specified repository.
public struct ImageNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ImageNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DescribeImageReplicationStatusInput: Swift.Sendable {
    /// An object with identifying information for an image in an Amazon ECR repository.
    /// This member is required.
    public var imageId: ECRClientTypes.ImageIdentifier?
    /// The Amazon Web Services account ID associated with the registry. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository that the image is in.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        imageId: ECRClientTypes.ImageIdentifier? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageId = imageId
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

extension ECRClientTypes {

    public enum ReplicationStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case complete
        case failed
        case inProgress
        case sdkUnknown(Swift.String)

        public static var allCases: [ReplicationStatus] {
            return [
                .complete,
                .failed,
                .inProgress
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .complete: return "COMPLETE"
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// The status of the replication process for an image.
    public struct ImageReplicationStatus: Swift.Sendable {
        /// The failure code for a replication that has failed.
        public var failureCode: Swift.String?
        /// The destination Region for the image replication.
        public var region: Swift.String?
        /// The Amazon Web Services account ID associated with the registry to which the image belongs.
        public var registryId: Swift.String?
        /// The image replication status.
        public var status: ECRClientTypes.ReplicationStatus?

        public init(
            failureCode: Swift.String? = nil,
            region: Swift.String? = nil,
            registryId: Swift.String? = nil,
            status: ECRClientTypes.ReplicationStatus? = nil
        )
        {
            self.failureCode = failureCode
            self.region = region
            self.registryId = registryId
            self.status = status
        }
    }
}

public struct DescribeImageReplicationStatusOutput: Swift.Sendable {
    /// An object with identifying information for an image in an Amazon ECR repository.
    public var imageId: ECRClientTypes.ImageIdentifier?
    /// The replication status details for the images in the specified repository.
    public var replicationStatuses: [ECRClientTypes.ImageReplicationStatus]?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?

    public init(
        imageId: ECRClientTypes.ImageIdentifier? = nil,
        replicationStatuses: [ECRClientTypes.ImageReplicationStatus]? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageId = imageId
        self.replicationStatuses = replicationStatuses
        self.repositoryName = repositoryName
    }
}

extension ECRClientTypes {

    public enum TagStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case any
        case tagged
        case untagged
        case sdkUnknown(Swift.String)

        public static var allCases: [TagStatus] {
            return [
                .any,
                .tagged,
                .untagged
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .any: return "ANY"
            case .tagged: return "TAGGED"
            case .untagged: return "UNTAGGED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// An object representing a filter on a [DescribeImages] operation.
    public struct DescribeImagesFilter: Swift.Sendable {
        /// The tag status with which to filter your [DescribeImages] results. You can filter results based on whether they are TAGGED or UNTAGGED.
        public var tagStatus: ECRClientTypes.TagStatus?

        public init(
            tagStatus: ECRClientTypes.TagStatus? = nil
        )
        {
            self.tagStatus = tagStatus
        }
    }
}

public struct DescribeImagesInput: Swift.Sendable {
    /// The filter key and value with which to filter your DescribeImages results.
    public var filter: ECRClientTypes.DescribeImagesFilter?
    /// The list of image IDs for the requested repository.
    public var imageIds: [ECRClientTypes.ImageIdentifier]?
    /// The maximum number of repository results returned by DescribeImages in paginated output. When this parameter is used, DescribeImages only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another DescribeImages request with the returned nextToken value. This value can be between 1 and 1000. If this parameter is not used, then DescribeImages returns up to 100 results and a nextToken value, if applicable. This option cannot be used when you specify images with imageIds.
    public var maxResults: Swift.Int?
    /// The nextToken value returned from a previous paginated DescribeImages request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This value is null when there are no more results to return. This option cannot be used when you specify images with imageIds.
    public var nextToken: Swift.String?
    /// The Amazon Web Services account ID associated with the registry that contains the repository in which to describe images. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The repository that contains the images to describe.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        filter: ECRClientTypes.DescribeImagesFilter? = nil,
        imageIds: [ECRClientTypes.ImageIdentifier]? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.filter = filter
        self.imageIds = imageIds
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

extension ECRClientTypes {

    public enum FindingSeverity: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case critical
        case high
        case informational
        case low
        case medium
        case undefined
        case sdkUnknown(Swift.String)

        public static var allCases: [FindingSeverity] {
            return [
                .critical,
                .high,
                .informational,
                .low,
                .medium,
                .undefined
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .critical: return "CRITICAL"
            case .high: return "HIGH"
            case .informational: return "INFORMATIONAL"
            case .low: return "LOW"
            case .medium: return "MEDIUM"
            case .undefined: return "UNDEFINED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// A summary of the last completed image scan.
    public struct ImageScanFindingsSummary: Swift.Sendable {
        /// The image vulnerability counts, sorted by severity.
        public var findingSeverityCounts: [Swift.String: Swift.Int]?
        /// The time of the last completed image scan.
        public var imageScanCompletedAt: Foundation.Date?
        /// The time when the vulnerability data was last scanned.
        public var vulnerabilitySourceUpdatedAt: Foundation.Date?

        public init(
            findingSeverityCounts: [Swift.String: Swift.Int]? = nil,
            imageScanCompletedAt: Foundation.Date? = nil,
            vulnerabilitySourceUpdatedAt: Foundation.Date? = nil
        )
        {
            self.findingSeverityCounts = findingSeverityCounts
            self.imageScanCompletedAt = imageScanCompletedAt
            self.vulnerabilitySourceUpdatedAt = vulnerabilitySourceUpdatedAt
        }
    }
}

extension ECRClientTypes {

    public enum ScanStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case active
        case complete
        case failed
        case findingsUnavailable
        case inProgress
        case pending
        case scanEligibilityExpired
        case unsupportedImage
        case sdkUnknown(Swift.String)

        public static var allCases: [ScanStatus] {
            return [
                .active,
                .complete,
                .failed,
                .findingsUnavailable,
                .inProgress,
                .pending,
                .scanEligibilityExpired,
                .unsupportedImage
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .active: return "ACTIVE"
            case .complete: return "COMPLETE"
            case .failed: return "FAILED"
            case .findingsUnavailable: return "FINDINGS_UNAVAILABLE"
            case .inProgress: return "IN_PROGRESS"
            case .pending: return "PENDING"
            case .scanEligibilityExpired: return "SCAN_ELIGIBILITY_EXPIRED"
            case .unsupportedImage: return "UNSUPPORTED_IMAGE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// The current status of an image scan.
    public struct ImageScanStatus: Swift.Sendable {
        /// The description of the image scan status.
        public var description: Swift.String?
        /// The current state of an image scan.
        public var status: ECRClientTypes.ScanStatus?

        public init(
            description: Swift.String? = nil,
            status: ECRClientTypes.ScanStatus? = nil
        )
        {
            self.description = description
            self.status = status
        }
    }
}

extension ECRClientTypes {

    /// An object that describes an image returned by a [DescribeImages] operation.
    public struct ImageDetail: Swift.Sendable {
        /// The artifact media type of the image.
        public var artifactMediaType: Swift.String?
        /// The sha256 digest of the image manifest.
        public var imageDigest: Swift.String?
        /// The media type of the image manifest.
        public var imageManifestMediaType: Swift.String?
        /// The date and time, expressed in standard JavaScript date format, at which the current image was pushed to the repository.
        public var imagePushedAt: Foundation.Date?
        /// A summary of the last completed image scan.
        public var imageScanFindingsSummary: ECRClientTypes.ImageScanFindingsSummary?
        /// The current state of the scan.
        public var imageScanStatus: ECRClientTypes.ImageScanStatus?
        /// The size, in bytes, of the image in the repository. If the image is a manifest list, this will be the max size of all manifests in the list. Beginning with Docker version 1.9, the Docker client compresses image layers before pushing them to a V2 Docker registry. The output of the docker images command shows the uncompressed image size, so it may return a larger image size than the image sizes returned by [DescribeImages].
        public var imageSizeInBytes: Swift.Int?
        /// The list of tags associated with this image.
        public var imageTags: [Swift.String]?
        /// The date and time, expressed in standard JavaScript date format, when Amazon ECR recorded the last image pull. Amazon ECR refreshes the last image pull timestamp at least once every 24 hours. For example, if you pull an image once a day then the lastRecordedPullTime timestamp will indicate the exact time that the image was last pulled. However, if you pull an image once an hour, because Amazon ECR refreshes the lastRecordedPullTime timestamp at least once every 24 hours, the result may not be the exact time that the image was last pulled.
        public var lastRecordedPullTime: Foundation.Date?
        /// The Amazon Web Services account ID associated with the registry to which this image belongs.
        public var registryId: Swift.String?
        /// The name of the repository to which this image belongs.
        public var repositoryName: Swift.String?

        public init(
            artifactMediaType: Swift.String? = nil,
            imageDigest: Swift.String? = nil,
            imageManifestMediaType: Swift.String? = nil,
            imagePushedAt: Foundation.Date? = nil,
            imageScanFindingsSummary: ECRClientTypes.ImageScanFindingsSummary? = nil,
            imageScanStatus: ECRClientTypes.ImageScanStatus? = nil,
            imageSizeInBytes: Swift.Int? = nil,
            imageTags: [Swift.String]? = nil,
            lastRecordedPullTime: Foundation.Date? = nil,
            registryId: Swift.String? = nil,
            repositoryName: Swift.String? = nil
        )
        {
            self.artifactMediaType = artifactMediaType
            self.imageDigest = imageDigest
            self.imageManifestMediaType = imageManifestMediaType
            self.imagePushedAt = imagePushedAt
            self.imageScanFindingsSummary = imageScanFindingsSummary
            self.imageScanStatus = imageScanStatus
            self.imageSizeInBytes = imageSizeInBytes
            self.imageTags = imageTags
            self.lastRecordedPullTime = lastRecordedPullTime
            self.registryId = registryId
            self.repositoryName = repositoryName
        }
    }
}

public struct DescribeImagesOutput: Swift.Sendable {
    /// A list of [ImageDetail] objects that contain data about the image.
    public var imageDetails: [ECRClientTypes.ImageDetail]?
    /// The nextToken value to include in a future DescribeImages request. When the results of a DescribeImages request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
    public var nextToken: Swift.String?

    public init(
        imageDetails: [ECRClientTypes.ImageDetail]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.imageDetails = imageDetails
        self.nextToken = nextToken
    }
}

/// The specified image scan could not be found. Ensure that image scanning is enabled on the repository and try again.
public struct ScanNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ScanNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DescribeImageScanFindingsInput: Swift.Sendable {
    /// An object with identifying information for an image in an Amazon ECR repository.
    /// This member is required.
    public var imageId: ECRClientTypes.ImageIdentifier?
    /// The maximum number of image scan results returned by DescribeImageScanFindings in paginated output. When this parameter is used, DescribeImageScanFindings only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another DescribeImageScanFindings request with the returned nextToken value. This value can be between 1 and 1000. If this parameter is not used, then DescribeImageScanFindings returns up to 100 results and a nextToken value, if applicable.
    public var maxResults: Swift.Int?
    /// The nextToken value returned from a previous paginated DescribeImageScanFindings request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This value is null when there are no more results to return.
    public var nextToken: Swift.String?
    /// The Amazon Web Services account ID associated with the registry that contains the repository in which to describe the image scan findings for. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The repository for the image for which to describe the scan findings.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        imageId: ECRClientTypes.ImageIdentifier? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageId = imageId
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

extension ECRClientTypes {

    /// The CVSS score for a finding.
    public struct CvssScore: Swift.Sendable {
        /// The base CVSS score used for the finding.
        public var baseScore: Swift.Double
        /// The vector string of the CVSS score.
        public var scoringVector: Swift.String?
        /// The source of the CVSS score.
        public var source: Swift.String?
        /// The version of CVSS used for the score.
        public var version: Swift.String?

        public init(
            baseScore: Swift.Double = 0.0,
            scoringVector: Swift.String? = nil,
            source: Swift.String? = nil,
            version: Swift.String? = nil
        )
        {
            self.baseScore = baseScore
            self.scoringVector = scoringVector
            self.source = source
            self.version = version
        }
    }
}

extension ECRClientTypes {

    /// Information on the vulnerable package identified by a finding.
    public struct VulnerablePackage: Swift.Sendable {
        /// The architecture of the vulnerable package.
        public var arch: Swift.String?
        /// The epoch of the vulnerable package.
        public var epoch: Swift.Int?
        /// The file path of the vulnerable package.
        public var filePath: Swift.String?
        /// The version of the package that contains the vulnerability fix.
        public var fixedInVersion: Swift.String?
        /// The name of the vulnerable package.
        public var name: Swift.String?
        /// The package manager of the vulnerable package.
        public var packageManager: Swift.String?
        /// The release of the vulnerable package.
        public var release: Swift.String?
        /// The source layer hash of the vulnerable package.
        public var sourceLayerHash: Swift.String?
        /// The version of the vulnerable package.
        public var version: Swift.String?

        public init(
            arch: Swift.String? = nil,
            epoch: Swift.Int? = nil,
            filePath: Swift.String? = nil,
            fixedInVersion: Swift.String? = nil,
            name: Swift.String? = nil,
            packageManager: Swift.String? = nil,
            release: Swift.String? = nil,
            sourceLayerHash: Swift.String? = nil,
            version: Swift.String? = nil
        )
        {
            self.arch = arch
            self.epoch = epoch
            self.filePath = filePath
            self.fixedInVersion = fixedInVersion
            self.name = name
            self.packageManager = packageManager
            self.release = release
            self.sourceLayerHash = sourceLayerHash
            self.version = version
        }
    }
}

extension ECRClientTypes {

    /// Information about a package vulnerability finding.
    public struct PackageVulnerabilityDetails: Swift.Sendable {
        /// An object that contains details about the CVSS score of a finding.
        public var cvss: [ECRClientTypes.CvssScore]?
        /// One or more URLs that contain details about this vulnerability type.
        public var referenceUrls: [Swift.String]?
        /// One or more vulnerabilities related to the one identified in this finding.
        public var relatedVulnerabilities: [Swift.String]?
        /// The source of the vulnerability information.
        public var source: Swift.String?
        /// A URL to the source of the vulnerability information.
        public var sourceUrl: Swift.String?
        /// The date and time that this vulnerability was first added to the vendor's database.
        public var vendorCreatedAt: Foundation.Date?
        /// The severity the vendor has given to this vulnerability type.
        public var vendorSeverity: Swift.String?
        /// The date and time the vendor last updated this vulnerability in their database.
        public var vendorUpdatedAt: Foundation.Date?
        /// The ID given to this vulnerability.
        public var vulnerabilityId: Swift.String?
        /// The packages impacted by this vulnerability.
        public var vulnerablePackages: [ECRClientTypes.VulnerablePackage]?

        public init(
            cvss: [ECRClientTypes.CvssScore]? = nil,
            referenceUrls: [Swift.String]? = nil,
            relatedVulnerabilities: [Swift.String]? = nil,
            source: Swift.String? = nil,
            sourceUrl: Swift.String? = nil,
            vendorCreatedAt: Foundation.Date? = nil,
            vendorSeverity: Swift.String? = nil,
            vendorUpdatedAt: Foundation.Date? = nil,
            vulnerabilityId: Swift.String? = nil,
            vulnerablePackages: [ECRClientTypes.VulnerablePackage]? = nil
        )
        {
            self.cvss = cvss
            self.referenceUrls = referenceUrls
            self.relatedVulnerabilities = relatedVulnerabilities
            self.source = source
            self.sourceUrl = sourceUrl
            self.vendorCreatedAt = vendorCreatedAt
            self.vendorSeverity = vendorSeverity
            self.vendorUpdatedAt = vendorUpdatedAt
            self.vulnerabilityId = vulnerabilityId
            self.vulnerablePackages = vulnerablePackages
        }
    }
}

extension ECRClientTypes {

    /// Details about the recommended course of action to remediate the finding.
    public struct Recommendation: Swift.Sendable {
        /// The recommended course of action to remediate the finding.
        public var text: Swift.String?
        /// The URL address to the CVE remediation recommendations.
        public var url: Swift.String?

        public init(
            text: Swift.String? = nil,
            url: Swift.String? = nil
        )
        {
            self.text = text
            self.url = url
        }
    }
}

extension ECRClientTypes {

    /// Information on how to remediate a finding.
    public struct Remediation: Swift.Sendable {
        /// An object that contains information about the recommended course of action to remediate the finding.
        public var recommendation: ECRClientTypes.Recommendation?

        public init(
            recommendation: ECRClientTypes.Recommendation? = nil
        )
        {
            self.recommendation = recommendation
        }
    }
}

extension ECRClientTypes {

    /// The image details of the Amazon ECR container image.
    public struct AwsEcrContainerImageDetails: Swift.Sendable {
        /// The architecture of the Amazon ECR container image.
        public var architecture: Swift.String?
        /// The image author of the Amazon ECR container image.
        public var author: Swift.String?
        /// The image hash of the Amazon ECR container image.
        public var imageHash: Swift.String?
        /// The image tags attached to the Amazon ECR container image.
        public var imageTags: [Swift.String]?
        /// The platform of the Amazon ECR container image.
        public var platform: Swift.String?
        /// The date and time the Amazon ECR container image was pushed.
        public var pushedAt: Foundation.Date?
        /// The registry the Amazon ECR container image belongs to.
        public var registry: Swift.String?
        /// The name of the repository the Amazon ECR container image resides in.
        public var repositoryName: Swift.String?

        public init(
            architecture: Swift.String? = nil,
            author: Swift.String? = nil,
            imageHash: Swift.String? = nil,
            imageTags: [Swift.String]? = nil,
            platform: Swift.String? = nil,
            pushedAt: Foundation.Date? = nil,
            registry: Swift.String? = nil,
            repositoryName: Swift.String? = nil
        )
        {
            self.architecture = architecture
            self.author = author
            self.imageHash = imageHash
            self.imageTags = imageTags
            self.platform = platform
            self.pushedAt = pushedAt
            self.registry = registry
            self.repositoryName = repositoryName
        }
    }
}

extension ECRClientTypes {

    /// Contains details about the resource involved in the finding.
    public struct ResourceDetails: Swift.Sendable {
        /// An object that contains details about the Amazon ECR container image involved in the finding.
        public var awsEcrContainerImage: ECRClientTypes.AwsEcrContainerImageDetails?

        public init(
            awsEcrContainerImage: ECRClientTypes.AwsEcrContainerImageDetails? = nil
        )
        {
            self.awsEcrContainerImage = awsEcrContainerImage
        }
    }
}

extension ECRClientTypes {

    /// Details about the resource involved in a finding.
    public struct Resource: Swift.Sendable {
        /// An object that contains details about the resource involved in a finding.
        public var details: ECRClientTypes.ResourceDetails?
        /// The ID of the resource.
        public var id: Swift.String?
        /// The tags attached to the resource.
        public var tags: [Swift.String: Swift.String]?
        /// The type of resource.
        public var type: Swift.String?

        public init(
            details: ECRClientTypes.ResourceDetails? = nil,
            id: Swift.String? = nil,
            tags: [Swift.String: Swift.String]? = nil,
            type: Swift.String? = nil
        )
        {
            self.details = details
            self.id = id
            self.tags = tags
            self.type = type
        }
    }
}

extension ECRClientTypes {

    /// Details on adjustments Amazon Inspector made to the CVSS score for a finding.
    public struct CvssScoreAdjustment: Swift.Sendable {
        /// The metric used to adjust the CVSS score.
        public var metric: Swift.String?
        /// The reason the CVSS score has been adjustment.
        public var reason: Swift.String?

        public init(
            metric: Swift.String? = nil,
            reason: Swift.String? = nil
        )
        {
            self.metric = metric
            self.reason = reason
        }
    }
}

extension ECRClientTypes {

    /// Information about the CVSS score.
    public struct CvssScoreDetails: Swift.Sendable {
        /// An object that contains details about adjustment Amazon Inspector made to the CVSS score.
        public var adjustments: [ECRClientTypes.CvssScoreAdjustment]?
        /// The CVSS score.
        public var score: Swift.Double
        /// The source for the CVSS score.
        public var scoreSource: Swift.String?
        /// The vector for the CVSS score.
        public var scoringVector: Swift.String?
        /// The CVSS version used in scoring.
        public var version: Swift.String?

        public init(
            adjustments: [ECRClientTypes.CvssScoreAdjustment]? = nil,
            score: Swift.Double = 0.0,
            scoreSource: Swift.String? = nil,
            scoringVector: Swift.String? = nil,
            version: Swift.String? = nil
        )
        {
            self.adjustments = adjustments
            self.score = score
            self.scoreSource = scoreSource
            self.scoringVector = scoringVector
            self.version = version
        }
    }
}

extension ECRClientTypes {

    /// Information about the Amazon Inspector score given to a finding.
    public struct ScoreDetails: Swift.Sendable {
        /// An object that contains details about the CVSS score given to a finding.
        public var cvss: ECRClientTypes.CvssScoreDetails?

        public init(
            cvss: ECRClientTypes.CvssScoreDetails? = nil
        )
        {
            self.cvss = cvss
        }
    }
}

extension ECRClientTypes {

    /// The details of an enhanced image scan. This is returned when enhanced scanning is enabled for your private registry.
    public struct EnhancedImageScanFinding: Swift.Sendable {
        /// The Amazon Web Services account ID associated with the image.
        public var awsAccountId: Swift.String?
        /// The description of the finding.
        public var description: Swift.String?
        /// If a finding discovered in your environment has an exploit available.
        public var exploitAvailable: Swift.String?
        /// The Amazon Resource Number (ARN) of the finding.
        public var findingArn: Swift.String?
        /// The date and time that the finding was first observed.
        public var firstObservedAt: Foundation.Date?
        /// Details on whether a fix is available through a version update. This value can be YES, NO, or PARTIAL. A PARTIAL fix means that some, but not all, of the packages identified in the finding have fixes available through updated versions.
        public var fixAvailable: Swift.String?
        /// The date and time that the finding was last observed.
        public var lastObservedAt: Foundation.Date?
        /// An object that contains the details of a package vulnerability finding.
        public var packageVulnerabilityDetails: ECRClientTypes.PackageVulnerabilityDetails?
        /// An object that contains the details about how to remediate a finding.
        public var remediation: ECRClientTypes.Remediation?
        /// Contains information on the resources involved in a finding.
        public var resources: [ECRClientTypes.Resource]?
        /// The Amazon Inspector score given to the finding.
        public var score: Swift.Double
        /// An object that contains details of the Amazon Inspector score.
        public var scoreDetails: ECRClientTypes.ScoreDetails?
        /// The severity of the finding.
        public var severity: Swift.String?
        /// The status of the finding.
        public var status: Swift.String?
        /// The title of the finding.
        public var title: Swift.String?
        /// The type of the finding.
        public var type: Swift.String?
        /// The date and time the finding was last updated at.
        public var updatedAt: Foundation.Date?

        public init(
            awsAccountId: Swift.String? = nil,
            description: Swift.String? = nil,
            exploitAvailable: Swift.String? = nil,
            findingArn: Swift.String? = nil,
            firstObservedAt: Foundation.Date? = nil,
            fixAvailable: Swift.String? = nil,
            lastObservedAt: Foundation.Date? = nil,
            packageVulnerabilityDetails: ECRClientTypes.PackageVulnerabilityDetails? = nil,
            remediation: ECRClientTypes.Remediation? = nil,
            resources: [ECRClientTypes.Resource]? = nil,
            score: Swift.Double = 0.0,
            scoreDetails: ECRClientTypes.ScoreDetails? = nil,
            severity: Swift.String? = nil,
            status: Swift.String? = nil,
            title: Swift.String? = nil,
            type: Swift.String? = nil,
            updatedAt: Foundation.Date? = nil
        )
        {
            self.awsAccountId = awsAccountId
            self.description = description
            self.exploitAvailable = exploitAvailable
            self.findingArn = findingArn
            self.firstObservedAt = firstObservedAt
            self.fixAvailable = fixAvailable
            self.lastObservedAt = lastObservedAt
            self.packageVulnerabilityDetails = packageVulnerabilityDetails
            self.remediation = remediation
            self.resources = resources
            self.score = score
            self.scoreDetails = scoreDetails
            self.severity = severity
            self.status = status
            self.title = title
            self.type = type
            self.updatedAt = updatedAt
        }
    }
}

extension ECRClientTypes {

    /// This data type is used in the [ImageScanFinding] data type.
    public struct Attribute: Swift.Sendable {
        /// The attribute key.
        /// This member is required.
        public var key: Swift.String?
        /// The value assigned to the attribute key.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

extension ECRClientTypes {

    /// Contains information about an image scan finding.
    public struct ImageScanFinding: Swift.Sendable {
        /// A collection of attributes of the host from which the finding is generated.
        public var attributes: [ECRClientTypes.Attribute]?
        /// The description of the finding.
        public var description: Swift.String?
        /// The name associated with the finding, usually a CVE number.
        public var name: Swift.String?
        /// The finding severity.
        public var severity: ECRClientTypes.FindingSeverity?
        /// A link containing additional details about the security vulnerability.
        public var uri: Swift.String?

        public init(
            attributes: [ECRClientTypes.Attribute]? = nil,
            description: Swift.String? = nil,
            name: Swift.String? = nil,
            severity: ECRClientTypes.FindingSeverity? = nil,
            uri: Swift.String? = nil
        )
        {
            self.attributes = attributes
            self.description = description
            self.name = name
            self.severity = severity
            self.uri = uri
        }
    }
}

extension ECRClientTypes {

    /// The details of an image scan.
    public struct ImageScanFindings: Swift.Sendable {
        /// Details about the enhanced scan findings from Amazon Inspector.
        public var enhancedFindings: [ECRClientTypes.EnhancedImageScanFinding]?
        /// The image vulnerability counts, sorted by severity.
        public var findingSeverityCounts: [Swift.String: Swift.Int]?
        /// The findings from the image scan.
        public var findings: [ECRClientTypes.ImageScanFinding]?
        /// The time of the last completed image scan.
        public var imageScanCompletedAt: Foundation.Date?
        /// The time when the vulnerability data was last scanned.
        public var vulnerabilitySourceUpdatedAt: Foundation.Date?

        public init(
            enhancedFindings: [ECRClientTypes.EnhancedImageScanFinding]? = nil,
            findingSeverityCounts: [Swift.String: Swift.Int]? = nil,
            findings: [ECRClientTypes.ImageScanFinding]? = nil,
            imageScanCompletedAt: Foundation.Date? = nil,
            vulnerabilitySourceUpdatedAt: Foundation.Date? = nil
        )
        {
            self.enhancedFindings = enhancedFindings
            self.findingSeverityCounts = findingSeverityCounts
            self.findings = findings
            self.imageScanCompletedAt = imageScanCompletedAt
            self.vulnerabilitySourceUpdatedAt = vulnerabilitySourceUpdatedAt
        }
    }
}

public struct DescribeImageScanFindingsOutput: Swift.Sendable {
    /// An object with identifying information for an image in an Amazon ECR repository.
    public var imageId: ECRClientTypes.ImageIdentifier?
    /// The information contained in the image scan findings.
    public var imageScanFindings: ECRClientTypes.ImageScanFindings?
    /// The current state of the scan.
    public var imageScanStatus: ECRClientTypes.ImageScanStatus?
    /// The nextToken value to include in a future DescribeImageScanFindings request. When the results of a DescribeImageScanFindings request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
    public var nextToken: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?

    public init(
        imageId: ECRClientTypes.ImageIdentifier? = nil,
        imageScanFindings: ECRClientTypes.ImageScanFindings? = nil,
        imageScanStatus: ECRClientTypes.ImageScanStatus? = nil,
        nextToken: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageId = imageId
        self.imageScanFindings = imageScanFindings
        self.imageScanStatus = imageScanStatus
        self.nextToken = nextToken
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct DescribePullThroughCacheRulesInput: Swift.Sendable {
    /// The Amazon ECR repository prefixes associated with the pull through cache rules to return. If no repository prefix value is specified, all pull through cache rules are returned.
    public var ecrRepositoryPrefixes: [Swift.String]?
    /// The maximum number of pull through cache rules returned by DescribePullThroughCacheRulesRequest in paginated output. When this parameter is used, DescribePullThroughCacheRulesRequest only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another DescribePullThroughCacheRulesRequest request with the returned nextToken value. This value can be between 1 and 1000. If this parameter is not used, then DescribePullThroughCacheRulesRequest returns up to 100 results and a nextToken value, if applicable.
    public var maxResults: Swift.Int?
    /// The nextToken value returned from a previous paginated DescribePullThroughCacheRulesRequest request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This value is null when there are no more results to return.
    public var nextToken: Swift.String?
    /// The Amazon Web Services account ID associated with the registry to return the pull through cache rules for. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?

    public init(
        ecrRepositoryPrefixes: [Swift.String]? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        registryId: Swift.String? = nil
    )
    {
        self.ecrRepositoryPrefixes = ecrRepositoryPrefixes
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.registryId = registryId
    }
}

extension ECRClientTypes {

    /// The details of a pull through cache rule.
    public struct PullThroughCacheRule: Swift.Sendable {
        /// The date and time the pull through cache was created.
        public var createdAt: Foundation.Date?
        /// The ARN of the Secrets Manager secret associated with the pull through cache rule.
        public var credentialArn: Swift.String?
        /// The Amazon ECR repository prefix associated with the pull through cache rule.
        public var ecrRepositoryPrefix: Swift.String?
        /// The Amazon Web Services account ID associated with the registry the pull through cache rule is associated with.
        public var registryId: Swift.String?
        /// The date and time, in JavaScript date format, when the pull through cache rule was last updated.
        public var updatedAt: Foundation.Date?
        /// The name of the upstream source registry associated with the pull through cache rule.
        public var upstreamRegistry: ECRClientTypes.UpstreamRegistry?
        /// The upstream registry URL associated with the pull through cache rule.
        public var upstreamRegistryUrl: Swift.String?

        public init(
            createdAt: Foundation.Date? = nil,
            credentialArn: Swift.String? = nil,
            ecrRepositoryPrefix: Swift.String? = nil,
            registryId: Swift.String? = nil,
            updatedAt: Foundation.Date? = nil,
            upstreamRegistry: ECRClientTypes.UpstreamRegistry? = nil,
            upstreamRegistryUrl: Swift.String? = nil
        )
        {
            self.createdAt = createdAt
            self.credentialArn = credentialArn
            self.ecrRepositoryPrefix = ecrRepositoryPrefix
            self.registryId = registryId
            self.updatedAt = updatedAt
            self.upstreamRegistry = upstreamRegistry
            self.upstreamRegistryUrl = upstreamRegistryUrl
        }
    }
}

public struct DescribePullThroughCacheRulesOutput: Swift.Sendable {
    /// The nextToken value to include in a future DescribePullThroughCacheRulesRequest request. When the results of a DescribePullThroughCacheRulesRequest request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
    public var nextToken: Swift.String?
    /// The details of the pull through cache rules.
    public var pullThroughCacheRules: [ECRClientTypes.PullThroughCacheRule]?

    public init(
        nextToken: Swift.String? = nil,
        pullThroughCacheRules: [ECRClientTypes.PullThroughCacheRule]? = nil
    )
    {
        self.nextToken = nextToken
        self.pullThroughCacheRules = pullThroughCacheRules
    }
}

public struct DescribeRegistryInput: Swift.Sendable {

    public init() { }
}

extension ECRClientTypes {

    /// An array of objects representing the destination for a replication rule.
    public struct ReplicationDestination: Swift.Sendable {
        /// The Region to replicate to.
        /// This member is required.
        public var region: Swift.String?
        /// The Amazon Web Services account ID of the Amazon ECR private registry to replicate to. When configuring cross-Region replication within your own registry, specify your own account ID.
        /// This member is required.
        public var registryId: Swift.String?

        public init(
            region: Swift.String? = nil,
            registryId: Swift.String? = nil
        )
        {
            self.region = region
            self.registryId = registryId
        }
    }
}

extension ECRClientTypes {

    public enum RepositoryFilterType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case prefixMatch
        case sdkUnknown(Swift.String)

        public static var allCases: [RepositoryFilterType] {
            return [
                .prefixMatch
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .prefixMatch: return "PREFIX_MATCH"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// The filter settings used with image replication. Specifying a repository filter to a replication rule provides a method for controlling which repositories in a private registry are replicated. If no filters are added, the contents of all repositories are replicated.
    public struct RepositoryFilter: Swift.Sendable {
        /// The repository filter details. When the PREFIX_MATCH filter type is specified, this value is required and should be the repository name prefix to configure replication for.
        /// This member is required.
        public var filter: Swift.String?
        /// The repository filter type. The only supported value is PREFIX_MATCH, which is a repository name prefix specified with the filter parameter.
        /// This member is required.
        public var filterType: ECRClientTypes.RepositoryFilterType?

        public init(
            filter: Swift.String? = nil,
            filterType: ECRClientTypes.RepositoryFilterType? = nil
        )
        {
            self.filter = filter
            self.filterType = filterType
        }
    }
}

extension ECRClientTypes {

    /// An array of objects representing the replication destinations and repository filters for a replication configuration.
    public struct ReplicationRule: Swift.Sendable {
        /// An array of objects representing the destination for a replication rule.
        /// This member is required.
        public var destinations: [ECRClientTypes.ReplicationDestination]?
        /// An array of objects representing the filters for a replication rule. Specifying a repository filter for a replication rule provides a method for controlling which repositories in a private registry are replicated.
        public var repositoryFilters: [ECRClientTypes.RepositoryFilter]?

        public init(
            destinations: [ECRClientTypes.ReplicationDestination]? = nil,
            repositoryFilters: [ECRClientTypes.RepositoryFilter]? = nil
        )
        {
            self.destinations = destinations
            self.repositoryFilters = repositoryFilters
        }
    }
}

extension ECRClientTypes {

    /// The replication configuration for a registry.
    public struct ReplicationConfiguration: Swift.Sendable {
        /// An array of objects representing the replication destinations and repository filters for a replication configuration.
        /// This member is required.
        public var rules: [ECRClientTypes.ReplicationRule]?

        public init(
            rules: [ECRClientTypes.ReplicationRule]? = nil
        )
        {
            self.rules = rules
        }
    }
}

public struct DescribeRegistryOutput: Swift.Sendable {
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The replication configuration for the registry.
    public var replicationConfiguration: ECRClientTypes.ReplicationConfiguration?

    public init(
        registryId: Swift.String? = nil,
        replicationConfiguration: ECRClientTypes.ReplicationConfiguration? = nil
    )
    {
        self.registryId = registryId
        self.replicationConfiguration = replicationConfiguration
    }
}

public struct DescribeRepositoriesInput: Swift.Sendable {
    /// The maximum number of repository results returned by DescribeRepositories in paginated output. When this parameter is used, DescribeRepositories only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another DescribeRepositories request with the returned nextToken value. This value can be between 1 and 1000. If this parameter is not used, then DescribeRepositories returns up to 100 results and a nextToken value, if applicable. This option cannot be used when you specify repositories with repositoryNames.
    public var maxResults: Swift.Int?
    /// The nextToken value returned from a previous paginated DescribeRepositories request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This value is null when there are no more results to return. This option cannot be used when you specify repositories with repositoryNames. This token should be treated as an opaque identifier that is only used to retrieve the next items in a list and not for other programmatic purposes.
    public var nextToken: Swift.String?
    /// The Amazon Web Services account ID associated with the registry that contains the repositories to be described. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// A list of repositories to describe. If this parameter is omitted, then all repositories in a registry are described.
    public var repositoryNames: [Swift.String]?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryNames: [Swift.String]? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.registryId = registryId
        self.repositoryNames = repositoryNames
    }
}

public struct DescribeRepositoriesOutput: Swift.Sendable {
    /// The nextToken value to include in a future DescribeRepositories request. When the results of a DescribeRepositories request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
    public var nextToken: Swift.String?
    /// A list of repository objects corresponding to valid repositories.
    public var repositories: [ECRClientTypes.Repository]?

    public init(
        nextToken: Swift.String? = nil,
        repositories: [ECRClientTypes.Repository]? = nil
    )
    {
        self.nextToken = nextToken
        self.repositories = repositories
    }
}

public struct DescribeRepositoryCreationTemplatesInput: Swift.Sendable {
    /// The maximum number of repository results returned by DescribeRepositoryCreationTemplatesRequest in paginated output. When this parameter is used, DescribeRepositoryCreationTemplatesRequest only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another DescribeRepositoryCreationTemplatesRequest request with the returned nextToken value. This value can be between 1 and 1000. If this parameter is not used, then DescribeRepositoryCreationTemplatesRequest returns up to 100 results and a nextToken value, if applicable.
    public var maxResults: Swift.Int?
    /// The nextToken value returned from a previous paginated DescribeRepositoryCreationTemplates request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This value is null when there are no more results to return. This token should be treated as an opaque identifier that is only used to retrieve the next items in a list and not for other programmatic purposes.
    public var nextToken: Swift.String?
    /// The repository namespace prefixes associated with the repository creation templates to describe. If this value is not specified, all repository creation templates are returned.
    public var prefixes: [Swift.String]?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        prefixes: [Swift.String]? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.prefixes = prefixes
    }
}

public struct DescribeRepositoryCreationTemplatesOutput: Swift.Sendable {
    /// The nextToken value to include in a future DescribeRepositoryCreationTemplates request. When the results of a DescribeRepositoryCreationTemplates request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
    public var nextToken: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The details of the repository creation templates.
    public var repositoryCreationTemplates: [ECRClientTypes.RepositoryCreationTemplate]?

    public init(
        nextToken: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryCreationTemplates: [ECRClientTypes.RepositoryCreationTemplate]? = nil
    )
    {
        self.nextToken = nextToken
        self.registryId = registryId
        self.repositoryCreationTemplates = repositoryCreationTemplates
    }
}

public struct GetAccountSettingInput: Swift.Sendable {
    /// The name of the account setting, such as BASIC_SCAN_TYPE_VERSION or REGISTRY_POLICY_SCOPE.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct GetAccountSettingOutput: Swift.Sendable {
    /// Retrieves the name of the account setting.
    public var name: Swift.String?
    /// The setting value for the setting name. The following are valid values for the basic scan type being used: AWS_NATIVE or CLAIR. The following are valid values for the registry policy scope being used: V1 or V2.
    public var value: Swift.String?

    public init(
        name: Swift.String? = nil,
        value: Swift.String? = nil
    )
    {
        self.name = name
        self.value = value
    }
}

public struct GetAuthorizationTokenInput: Swift.Sendable {
    /// A list of Amazon Web Services account IDs that are associated with the registries for which to get AuthorizationData objects. If you do not specify a registry, the default registry is assumed.
    @available(*, deprecated, message: "This field is deprecated. The returned authorization token can be used to access any Amazon ECR registry that the IAM principal has access to, specifying a registry ID doesn't change the permissions scope of the authorization token.")
    public var registryIds: [Swift.String]?

    public init(
        registryIds: [Swift.String]? = nil
    )
    {
        self.registryIds = registryIds
    }
}

extension ECRClientTypes {

    /// An object representing authorization data for an Amazon ECR registry.
    public struct AuthorizationData: Swift.Sendable {
        /// A base64-encoded string that contains authorization data for the specified Amazon ECR registry. When the string is decoded, it is presented in the format user:password for private registry authentication using docker login.
        public var authorizationToken: Swift.String?
        /// The Unix time in seconds and milliseconds when the authorization token expires. Authorization tokens are valid for 12 hours.
        public var expiresAt: Foundation.Date?
        /// The registry URL to use for this authorization token in a docker login command. The Amazon ECR registry URL format is https://aws_account_id.dkr.ecr.region.amazonaws.com. For example, https://************.dkr.ecr.us-east-1.amazonaws.com..
        public var proxyEndpoint: Swift.String?

        public init(
            authorizationToken: Swift.String? = nil,
            expiresAt: Foundation.Date? = nil,
            proxyEndpoint: Swift.String? = nil
        )
        {
            self.authorizationToken = authorizationToken
            self.expiresAt = expiresAt
            self.proxyEndpoint = proxyEndpoint
        }
    }
}

public struct GetAuthorizationTokenOutput: Swift.Sendable {
    /// A list of authorization token data objects that correspond to the registryIds values in the request.
    public var authorizationData: [ECRClientTypes.AuthorizationData]?

    public init(
        authorizationData: [ECRClientTypes.AuthorizationData]? = nil
    )
    {
        self.authorizationData = authorizationData
    }
}

/// The specified layer is not available because it is not associated with an image. Unassociated image layers may be cleaned up at any time.
public struct LayerInaccessibleException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LayerInaccessibleException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified layers could not be found, or the specified layer is not valid for this repository.
public struct LayersNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LayersNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// There was an issue getting the upstream layer matching the pull through cache rule.
public struct UnableToGetUpstreamLayerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UnableToGetUpstreamLayerException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct GetDownloadUrlForLayerInput: Swift.Sendable {
    /// The digest of the image layer to download.
    /// This member is required.
    public var layerDigest: Swift.String?
    /// The Amazon Web Services account ID associated with the registry that contains the image layer to download. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository that is associated with the image layer to download.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        layerDigest: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.layerDigest = layerDigest
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct GetDownloadUrlForLayerOutput: Swift.Sendable {
    /// The pre-signed Amazon S3 download URL for the requested layer.
    public var downloadUrl: Swift.String?
    /// The digest of the image layer to download.
    public var layerDigest: Swift.String?

    public init(
        downloadUrl: Swift.String? = nil,
        layerDigest: Swift.String? = nil
    )
    {
        self.downloadUrl = downloadUrl
        self.layerDigest = layerDigest
    }
}

public struct GetLifecyclePolicyInput: Swift.Sendable {
    /// The Amazon Web Services account ID associated with the registry that contains the repository. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct GetLifecyclePolicyOutput: Swift.Sendable {
    /// The time stamp of the last time that the lifecycle policy was run.
    public var lastEvaluatedAt: Foundation.Date?
    /// The JSON lifecycle policy text.
    public var lifecyclePolicyText: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?

    public init(
        lastEvaluatedAt: Foundation.Date? = nil,
        lifecyclePolicyText: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.lastEvaluatedAt = lastEvaluatedAt
        self.lifecyclePolicyText = lifecyclePolicyText
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

/// There is no dry run for this repository.
public struct LifecyclePolicyPreviewNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LifecyclePolicyPreviewNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension ECRClientTypes {

    /// The filter for the lifecycle policy preview.
    public struct LifecyclePolicyPreviewFilter: Swift.Sendable {
        /// The tag status of the image.
        public var tagStatus: ECRClientTypes.TagStatus?

        public init(
            tagStatus: ECRClientTypes.TagStatus? = nil
        )
        {
            self.tagStatus = tagStatus
        }
    }
}

public struct GetLifecyclePolicyPreviewInput: Swift.Sendable {
    /// An optional parameter that filters results based on image tag status and all tags, if tagged.
    public var filter: ECRClientTypes.LifecyclePolicyPreviewFilter?
    /// The list of imageIDs to be included.
    public var imageIds: [ECRClientTypes.ImageIdentifier]?
    /// The maximum number of repository results returned by GetLifecyclePolicyPreviewRequest in  paginated output. When this parameter is used, GetLifecyclePolicyPreviewRequest only returns  maxResults results in a single page along with a nextToken  response element. The remaining results of the initial request can be seen by sending  another GetLifecyclePolicyPreviewRequest request with the returned nextToken  value. This value can be between 1 and 1000. If this  parameter is not used, then GetLifecyclePolicyPreviewRequest returns up to  100 results and a nextToken value, if  applicable. This option cannot be used when you specify images with imageIds.
    public var maxResults: Swift.Int?
    /// The nextToken value returned from a previous paginated  GetLifecyclePolicyPreviewRequest request where maxResults was used and the  results exceeded the value of that parameter. Pagination continues from the end of the  previous results that returned the nextToken value. This value is  null when there are no more results to return. This option cannot be used when you specify images with imageIds.
    public var nextToken: Swift.String?
    /// The Amazon Web Services account ID associated with the registry that contains the repository. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        filter: ECRClientTypes.LifecyclePolicyPreviewFilter? = nil,
        imageIds: [ECRClientTypes.ImageIdentifier]? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.filter = filter
        self.imageIds = imageIds
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

extension ECRClientTypes {

    public enum ImageActionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case expire
        case sdkUnknown(Swift.String)

        public static var allCases: [ImageActionType] {
            return [
                .expire
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .expire: return "EXPIRE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// The type of action to be taken.
    public struct LifecyclePolicyRuleAction: Swift.Sendable {
        /// The type of action to be taken.
        public var type: ECRClientTypes.ImageActionType?

        public init(
            type: ECRClientTypes.ImageActionType? = nil
        )
        {
            self.type = type
        }
    }
}

extension ECRClientTypes {

    /// The result of the lifecycle policy preview.
    public struct LifecyclePolicyPreviewResult: Swift.Sendable {
        /// The type of action to be taken.
        public var action: ECRClientTypes.LifecyclePolicyRuleAction?
        /// The priority of the applied rule.
        public var appliedRulePriority: Swift.Int?
        /// The sha256 digest of the image manifest.
        public var imageDigest: Swift.String?
        /// The date and time, expressed in standard JavaScript date format, at which the current image was pushed to the repository.
        public var imagePushedAt: Foundation.Date?
        /// The list of tags associated with this image.
        public var imageTags: [Swift.String]?

        public init(
            action: ECRClientTypes.LifecyclePolicyRuleAction? = nil,
            appliedRulePriority: Swift.Int? = nil,
            imageDigest: Swift.String? = nil,
            imagePushedAt: Foundation.Date? = nil,
            imageTags: [Swift.String]? = nil
        )
        {
            self.action = action
            self.appliedRulePriority = appliedRulePriority
            self.imageDigest = imageDigest
            self.imagePushedAt = imagePushedAt
            self.imageTags = imageTags
        }
    }
}

extension ECRClientTypes {

    public enum LifecyclePolicyPreviewStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case complete
        case expired
        case failed
        case inProgress
        case sdkUnknown(Swift.String)

        public static var allCases: [LifecyclePolicyPreviewStatus] {
            return [
                .complete,
                .expired,
                .failed,
                .inProgress
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .complete: return "COMPLETE"
            case .expired: return "EXPIRED"
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// The summary of the lifecycle policy preview request.
    public struct LifecyclePolicyPreviewSummary: Swift.Sendable {
        /// The number of expiring images.
        public var expiringImageTotalCount: Swift.Int?

        public init(
            expiringImageTotalCount: Swift.Int? = nil
        )
        {
            self.expiringImageTotalCount = expiringImageTotalCount
        }
    }
}

public struct GetLifecyclePolicyPreviewOutput: Swift.Sendable {
    /// The JSON lifecycle policy text.
    public var lifecyclePolicyText: Swift.String?
    /// The nextToken value to include in a future GetLifecyclePolicyPreview request. When the results of a GetLifecyclePolicyPreview request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
    public var nextToken: Swift.String?
    /// The results of the lifecycle policy preview request.
    public var previewResults: [ECRClientTypes.LifecyclePolicyPreviewResult]?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?
    /// The status of the lifecycle policy preview request.
    public var status: ECRClientTypes.LifecyclePolicyPreviewStatus?
    /// The list of images that is returned as a result of the action.
    public var summary: ECRClientTypes.LifecyclePolicyPreviewSummary?

    public init(
        lifecyclePolicyText: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        previewResults: [ECRClientTypes.LifecyclePolicyPreviewResult]? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil,
        status: ECRClientTypes.LifecyclePolicyPreviewStatus? = nil,
        summary: ECRClientTypes.LifecyclePolicyPreviewSummary? = nil
    )
    {
        self.lifecyclePolicyText = lifecyclePolicyText
        self.nextToken = nextToken
        self.previewResults = previewResults
        self.registryId = registryId
        self.repositoryName = repositoryName
        self.status = status
        self.summary = summary
    }
}

public struct GetRegistryPolicyInput: Swift.Sendable {

    public init() { }
}

public struct GetRegistryPolicyOutput: Swift.Sendable {
    /// The JSON text of the permissions policy for a registry.
    public var policyText: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?

    public init(
        policyText: Swift.String? = nil,
        registryId: Swift.String? = nil
    )
    {
        self.policyText = policyText
        self.registryId = registryId
    }
}

public struct GetRegistryScanningConfigurationInput: Swift.Sendable {

    public init() { }
}

extension ECRClientTypes {

    /// The details of a scanning rule for a private registry.
    public struct RegistryScanningRule: Swift.Sendable {
        /// The repository filters associated with the scanning configuration for a private registry.
        /// This member is required.
        public var repositoryFilters: [ECRClientTypes.ScanningRepositoryFilter]?
        /// The frequency that scans are performed at for a private registry. When the ENHANCED scan type is specified, the supported scan frequencies are CONTINUOUS_SCAN and SCAN_ON_PUSH. When the BASIC scan type is specified, the SCAN_ON_PUSH scan frequency is supported. If scan on push is not specified, then the MANUAL scan frequency is set by default.
        /// This member is required.
        public var scanFrequency: ECRClientTypes.ScanFrequency?

        public init(
            repositoryFilters: [ECRClientTypes.ScanningRepositoryFilter]? = nil,
            scanFrequency: ECRClientTypes.ScanFrequency? = nil
        )
        {
            self.repositoryFilters = repositoryFilters
            self.scanFrequency = scanFrequency
        }
    }
}

extension ECRClientTypes {

    public enum ScanType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case basic
        case enhanced
        case sdkUnknown(Swift.String)

        public static var allCases: [ScanType] {
            return [
                .basic,
                .enhanced
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .basic: return "BASIC"
            case .enhanced: return "ENHANCED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRClientTypes {

    /// The scanning configuration for a private registry.
    public struct RegistryScanningConfiguration: Swift.Sendable {
        /// The scanning rules associated with the registry.
        public var rules: [ECRClientTypes.RegistryScanningRule]?
        /// The type of scanning configured for the registry.
        public var scanType: ECRClientTypes.ScanType?

        public init(
            rules: [ECRClientTypes.RegistryScanningRule]? = nil,
            scanType: ECRClientTypes.ScanType? = nil
        )
        {
            self.rules = rules
            self.scanType = scanType
        }
    }
}

public struct GetRegistryScanningConfigurationOutput: Swift.Sendable {
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The scanning configuration for the registry.
    public var scanningConfiguration: ECRClientTypes.RegistryScanningConfiguration?

    public init(
        registryId: Swift.String? = nil,
        scanningConfiguration: ECRClientTypes.RegistryScanningConfiguration? = nil
    )
    {
        self.registryId = registryId
        self.scanningConfiguration = scanningConfiguration
    }
}

public struct GetRepositoryPolicyInput: Swift.Sendable {
    /// The Amazon Web Services account ID associated with the registry that contains the repository. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository with the policy to retrieve.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct GetRepositoryPolicyOutput: Swift.Sendable {
    /// The JSON repository policy text associated with the repository.
    public var policyText: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?

    public init(
        policyText: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.policyText = policyText
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct InitiateLayerUploadInput: Swift.Sendable {
    /// The Amazon Web Services account ID associated with the registry to which you intend to upload layers. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository to which you intend to upload layers.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct InitiateLayerUploadOutput: Swift.Sendable {
    /// The size, in bytes, that Amazon ECR expects future layer part uploads to be.
    public var partSize: Swift.Int?
    /// The upload ID for the layer upload. This parameter is passed to further [UploadLayerPart] and [CompleteLayerUpload] operations.
    public var uploadId: Swift.String?

    public init(
        partSize: Swift.Int? = nil,
        uploadId: Swift.String? = nil
    )
    {
        self.partSize = partSize
        self.uploadId = uploadId
    }
}

extension ECRClientTypes {

    /// An object representing a filter on a [ListImages] operation.
    public struct ListImagesFilter: Swift.Sendable {
        /// The tag status with which to filter your [ListImages] results. You can filter results based on whether they are TAGGED or UNTAGGED.
        public var tagStatus: ECRClientTypes.TagStatus?

        public init(
            tagStatus: ECRClientTypes.TagStatus? = nil
        )
        {
            self.tagStatus = tagStatus
        }
    }
}

public struct ListImagesInput: Swift.Sendable {
    /// The filter key and value with which to filter your ListImages results.
    public var filter: ECRClientTypes.ListImagesFilter?
    /// The maximum number of image results returned by ListImages in paginated output. When this parameter is used, ListImages only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another ListImages request with the returned nextToken value. This value can be between 1 and 1000. If this parameter is not used, then ListImages returns up to 100 results and a nextToken value, if applicable.
    public var maxResults: Swift.Int?
    /// The nextToken value returned from a previous paginated ListImages request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This value is null when there are no more results to return. This token should be treated as an opaque identifier that is only used to retrieve the next items in a list and not for other programmatic purposes.
    public var nextToken: Swift.String?
    /// The Amazon Web Services account ID associated with the registry that contains the repository in which to list images. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The repository with image IDs to be listed.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        filter: ECRClientTypes.ListImagesFilter? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.filter = filter
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct ListImagesOutput: Swift.Sendable {
    /// The list of image IDs for the requested repository.
    public var imageIds: [ECRClientTypes.ImageIdentifier]?
    /// The nextToken value to include in a future ListImages request. When the results of a ListImages request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
    public var nextToken: Swift.String?

    public init(
        imageIds: [ECRClientTypes.ImageIdentifier]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.imageIds = imageIds
        self.nextToken = nextToken
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) that identifies the resource for which to list the tags. Currently, the only supported resource is an Amazon ECR repository.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// The tags for the resource.
    public var tags: [ECRClientTypes.Tag]?

    public init(
        tags: [ECRClientTypes.Tag]? = nil
    )
    {
        self.tags = tags
    }
}

public struct PutAccountSettingInput: Swift.Sendable {
    /// The name of the account setting, such as BASIC_SCAN_TYPE_VERSION or REGISTRY_POLICY_SCOPE.
    /// This member is required.
    public var name: Swift.String?
    /// Setting value that is specified. The following are valid values for the basic scan type being used: AWS_NATIVE or CLAIR. The following are valid values for the registry policy scope being used: V1 or V2.
    /// This member is required.
    public var value: Swift.String?

    public init(
        name: Swift.String? = nil,
        value: Swift.String? = nil
    )
    {
        self.name = name
        self.value = value
    }
}

public struct PutAccountSettingOutput: Swift.Sendable {
    /// Retrieves the name of the account setting.
    public var name: Swift.String?
    /// Retrieves the value of the specified account setting.
    public var value: Swift.String?

    public init(
        name: Swift.String? = nil,
        value: Swift.String? = nil
    )
    {
        self.name = name
        self.value = value
    }
}

/// The specified image has already been pushed, and there were no changes to the manifest or image tag after the last push.
public struct ImageAlreadyExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ImageAlreadyExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified image digest does not match the digest that Amazon ECR calculated for the image.
public struct ImageDigestDoesNotMatchException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ImageDigestDoesNotMatchException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified image is tagged with a tag that already exists. The repository is configured for tag immutability.
public struct ImageTagAlreadyExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ImageTagAlreadyExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The manifest list is referencing an image that does not exist.
public struct ReferencedImagesNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ReferencedImagesNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct PutImageInput: Swift.Sendable {
    /// The image digest of the image manifest corresponding to the image.
    public var imageDigest: Swift.String?
    /// The image manifest corresponding to the image to be uploaded.
    /// This member is required.
    public var imageManifest: Swift.String?
    /// The media type of the image manifest. If you push an image manifest that does not contain the mediaType field, you must specify the imageManifestMediaType in the request.
    public var imageManifestMediaType: Swift.String?
    /// The tag to associate with the image. This parameter is required for images that use the Docker Image Manifest V2 Schema 2 or Open Container Initiative (OCI) formats.
    public var imageTag: Swift.String?
    /// The Amazon Web Services account ID associated with the registry that contains the repository in which to put the image. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository in which to put the image.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        imageDigest: Swift.String? = nil,
        imageManifest: Swift.String? = nil,
        imageManifestMediaType: Swift.String? = nil,
        imageTag: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageDigest = imageDigest
        self.imageManifest = imageManifest
        self.imageManifestMediaType = imageManifestMediaType
        self.imageTag = imageTag
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct PutImageOutput: Swift.Sendable {
    /// Details of the image uploaded.
    public var image: ECRClientTypes.Image?

    public init(
        image: ECRClientTypes.Image? = nil
    )
    {
        self.image = image
    }
}

public struct PutImageScanningConfigurationInput: Swift.Sendable {
    /// The image scanning configuration for the repository. This setting determines whether images are scanned for known vulnerabilities after being pushed to the repository.
    /// This member is required.
    public var imageScanningConfiguration: ECRClientTypes.ImageScanningConfiguration?
    /// The Amazon Web Services account ID associated with the registry that contains the repository in which to update the image scanning configuration setting. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository in which to update the image scanning configuration setting.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        imageScanningConfiguration: ECRClientTypes.ImageScanningConfiguration? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageScanningConfiguration = imageScanningConfiguration
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct PutImageScanningConfigurationOutput: Swift.Sendable {
    /// The image scanning configuration setting for the repository.
    public var imageScanningConfiguration: ECRClientTypes.ImageScanningConfiguration?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?

    public init(
        imageScanningConfiguration: ECRClientTypes.ImageScanningConfiguration? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageScanningConfiguration = imageScanningConfiguration
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct PutImageTagMutabilityInput: Swift.Sendable {
    /// The tag mutability setting for the repository. If MUTABLE is specified, image tags can be overwritten. If IMMUTABLE is specified, all image tags within the repository will be immutable which will prevent them from being overwritten.
    /// This member is required.
    public var imageTagMutability: ECRClientTypes.ImageTagMutability?
    /// The Amazon Web Services account ID associated with the registry that contains the repository in which to update the image tag mutability settings. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository in which to update the image tag mutability settings.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        imageTagMutability: ECRClientTypes.ImageTagMutability? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageTagMutability = imageTagMutability
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct PutImageTagMutabilityOutput: Swift.Sendable {
    /// The image tag mutability setting for the repository.
    public var imageTagMutability: ECRClientTypes.ImageTagMutability?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?

    public init(
        imageTagMutability: ECRClientTypes.ImageTagMutability? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageTagMutability = imageTagMutability
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct PutLifecyclePolicyInput: Swift.Sendable {
    /// The JSON repository policy text to apply to the repository.
    /// This member is required.
    public var lifecyclePolicyText: Swift.String?
    /// The Amazon Web Services account ID associated with the registry that contains the repository. If you do  not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository to receive the policy.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        lifecyclePolicyText: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.lifecyclePolicyText = lifecyclePolicyText
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct PutLifecyclePolicyOutput: Swift.Sendable {
    /// The JSON repository policy text.
    public var lifecyclePolicyText: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?

    public init(
        lifecyclePolicyText: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.lifecyclePolicyText = lifecyclePolicyText
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct PutRegistryPolicyInput: Swift.Sendable {
    /// The JSON policy text to apply to your registry. The policy text follows the same format as IAM policy text. For more information, see [Registry permissions](https://docs.aws.amazon.com/AmazonECR/latest/userguide/registry-permissions.html) in the Amazon Elastic Container Registry User Guide.
    /// This member is required.
    public var policyText: Swift.String?

    public init(
        policyText: Swift.String? = nil
    )
    {
        self.policyText = policyText
    }
}

public struct PutRegistryPolicyOutput: Swift.Sendable {
    /// The JSON policy text for your registry.
    public var policyText: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?

    public init(
        policyText: Swift.String? = nil,
        registryId: Swift.String? = nil
    )
    {
        self.policyText = policyText
        self.registryId = registryId
    }
}

public struct PutRegistryScanningConfigurationInput: Swift.Sendable {
    /// The scanning rules to use for the registry. A scanning rule is used to determine which repository filters are used and at what frequency scanning will occur.
    public var rules: [ECRClientTypes.RegistryScanningRule]?
    /// The scanning type to set for the registry. When a registry scanning configuration is not defined, by default the BASIC scan type is used. When basic scanning is used, you may specify filters to determine which individual repositories, or all repositories, are scanned when new images are pushed to those repositories. Alternatively, you can do manual scans of images with basic scanning. When the ENHANCED scan type is set, Amazon Inspector provides automated vulnerability scanning. You may choose between continuous scanning or scan on push and you may specify filters to determine which individual repositories, or all repositories, are scanned.
    public var scanType: ECRClientTypes.ScanType?

    public init(
        rules: [ECRClientTypes.RegistryScanningRule]? = nil,
        scanType: ECRClientTypes.ScanType? = nil
    )
    {
        self.rules = rules
        self.scanType = scanType
    }
}

public struct PutRegistryScanningConfigurationOutput: Swift.Sendable {
    /// The scanning configuration for your registry.
    public var registryScanningConfiguration: ECRClientTypes.RegistryScanningConfiguration?

    public init(
        registryScanningConfiguration: ECRClientTypes.RegistryScanningConfiguration? = nil
    )
    {
        self.registryScanningConfiguration = registryScanningConfiguration
    }
}

public struct PutReplicationConfigurationInput: Swift.Sendable {
    /// An object representing the replication configuration for a registry.
    /// This member is required.
    public var replicationConfiguration: ECRClientTypes.ReplicationConfiguration?

    public init(
        replicationConfiguration: ECRClientTypes.ReplicationConfiguration? = nil
    )
    {
        self.replicationConfiguration = replicationConfiguration
    }
}

public struct PutReplicationConfigurationOutput: Swift.Sendable {
    /// The contents of the replication configuration for the registry.
    public var replicationConfiguration: ECRClientTypes.ReplicationConfiguration?

    public init(
        replicationConfiguration: ECRClientTypes.ReplicationConfiguration? = nil
    )
    {
        self.replicationConfiguration = replicationConfiguration
    }
}

public struct SetRepositoryPolicyInput: Swift.Sendable {
    /// If the policy you are attempting to set on a repository policy would prevent you from setting another policy in the future, you must force the [SetRepositoryPolicy] operation. This is intended to prevent accidental repository lock outs.
    public var force: Swift.Bool?
    /// The JSON repository policy text to apply to the repository. For more information, see [Amazon ECR repository policies](https://docs.aws.amazon.com/AmazonECR/latest/userguide/repository-policy-examples.html) in the Amazon Elastic Container Registry User Guide.
    /// This member is required.
    public var policyText: Swift.String?
    /// The Amazon Web Services account ID associated with the registry that contains the repository. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository to receive the policy.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        force: Swift.Bool? = false,
        policyText: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.force = force
        self.policyText = policyText
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct SetRepositoryPolicyOutput: Swift.Sendable {
    /// The JSON repository policy text applied to the repository.
    public var policyText: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?

    public init(
        policyText: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.policyText = policyText
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

/// The image is of a type that cannot be scanned.
public struct UnsupportedImageTypeException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UnsupportedImageTypeException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct StartImageScanInput: Swift.Sendable {
    /// An object with identifying information for an image in an Amazon ECR repository.
    /// This member is required.
    public var imageId: ECRClientTypes.ImageIdentifier?
    /// The Amazon Web Services account ID associated with the registry that contains the repository in which to start an image scan request. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository that contains the images to scan.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        imageId: ECRClientTypes.ImageIdentifier? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageId = imageId
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct StartImageScanOutput: Swift.Sendable {
    /// An object with identifying information for an image in an Amazon ECR repository.
    public var imageId: ECRClientTypes.ImageIdentifier?
    /// The current state of the scan.
    public var imageScanStatus: ECRClientTypes.ImageScanStatus?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?

    public init(
        imageId: ECRClientTypes.ImageIdentifier? = nil,
        imageScanStatus: ECRClientTypes.ImageScanStatus? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageId = imageId
        self.imageScanStatus = imageScanStatus
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

/// The previous lifecycle policy preview request has not completed. Wait and try again.
public struct LifecyclePolicyPreviewInProgressException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LifecyclePolicyPreviewInProgressException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct StartLifecyclePolicyPreviewInput: Swift.Sendable {
    /// The policy to be evaluated against. If you do not specify a policy, the current policy for the repository is used.
    public var lifecyclePolicyText: Swift.String?
    /// The Amazon Web Services account ID associated with the registry that contains the repository. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository to be evaluated.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        lifecyclePolicyText: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.lifecyclePolicyText = lifecyclePolicyText
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct StartLifecyclePolicyPreviewOutput: Swift.Sendable {
    /// The JSON repository policy text.
    public var lifecyclePolicyText: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?
    /// The status of the lifecycle policy preview request.
    public var status: ECRClientTypes.LifecyclePolicyPreviewStatus?

    public init(
        lifecyclePolicyText: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil,
        status: ECRClientTypes.LifecyclePolicyPreviewStatus? = nil
    )
    {
        self.lifecyclePolicyText = lifecyclePolicyText
        self.registryId = registryId
        self.repositoryName = repositoryName
        self.status = status
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the the resource to which to add tags. Currently, the only supported resource is an Amazon ECR repository.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tags to add to the resource. A tag is an array of key-value pairs. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
    /// This member is required.
    public var tags: [ECRClientTypes.Tag]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [ECRClientTypes.Tag]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource from which to remove tags. Currently, the only supported resource is an Amazon ECR repository.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The keys of the tags to be removed.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UpdatePullThroughCacheRuleInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the Amazon Web Services Secrets Manager secret that identifies the credentials to authenticate to the upstream registry.
    /// This member is required.
    public var credentialArn: Swift.String?
    /// The repository name prefix to use when caching images from the source registry.
    /// This member is required.
    public var ecrRepositoryPrefix: Swift.String?
    /// The Amazon Web Services account ID associated with the registry associated with the pull through cache rule. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?

    public init(
        credentialArn: Swift.String? = nil,
        ecrRepositoryPrefix: Swift.String? = nil,
        registryId: Swift.String? = nil
    )
    {
        self.credentialArn = credentialArn
        self.ecrRepositoryPrefix = ecrRepositoryPrefix
        self.registryId = registryId
    }
}

public struct UpdatePullThroughCacheRuleOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the Amazon Web Services Secrets Manager secret associated with the pull through cache rule.
    public var credentialArn: Swift.String?
    /// The Amazon ECR repository prefix associated with the pull through cache rule.
    public var ecrRepositoryPrefix: Swift.String?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The date and time, in JavaScript date format, when the pull through cache rule was updated.
    public var updatedAt: Foundation.Date?

    public init(
        credentialArn: Swift.String? = nil,
        ecrRepositoryPrefix: Swift.String? = nil,
        registryId: Swift.String? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.credentialArn = credentialArn
        self.ecrRepositoryPrefix = ecrRepositoryPrefix
        self.registryId = registryId
        self.updatedAt = updatedAt
    }
}

public struct UpdateRepositoryCreationTemplateInput: Swift.Sendable {
    /// Updates the list of enumerable strings representing the Amazon ECR repository creation scenarios that this template will apply towards. The two supported scenarios are PULL_THROUGH_CACHE and REPLICATION
    public var appliedFor: [ECRClientTypes.RCTAppliedFor]?
    /// The ARN of the role to be assumed by Amazon ECR. This role must be in the same account as the registry that you are configuring. Amazon ECR will assume your supplied role when the customRoleArn is specified. When this field isn't specified, Amazon ECR will use the service-linked role for the repository creation template.
    public var customRoleArn: Swift.String?
    /// A description for the repository creation template.
    public var description: Swift.String?
    /// The encryption configuration to associate with the repository creation template.
    public var encryptionConfiguration: ECRClientTypes.EncryptionConfigurationForRepositoryCreationTemplate?
    /// Updates the tag mutability setting for the repository. If this parameter is omitted, the default setting of MUTABLE will be used which will allow image tags to be overwritten. If IMMUTABLE is specified, all image tags within the repository will be immutable which will prevent them from being overwritten.
    public var imageTagMutability: ECRClientTypes.ImageTagMutability?
    /// Updates the lifecycle policy associated with the specified repository creation template.
    public var lifecyclePolicy: Swift.String?
    /// The repository namespace prefix that matches an existing repository creation template in the registry. All repositories created using this namespace prefix will have the settings defined in this template applied. For example, a prefix of prod would apply to all repositories beginning with prod/. This includes a repository named prod/team1 as well as a repository named prod/repository1. To apply a template to all repositories in your registry that don't have an associated creation template, you can use ROOT as the prefix.
    /// This member is required.
    public var `prefix`: Swift.String?
    /// Updates the repository policy created using the template. A repository policy is a permissions policy associated with a repository to control access permissions.
    public var repositoryPolicy: Swift.String?
    /// The metadata to apply to the repository to help you categorize and organize. Each tag consists of a key and an optional value, both of which you define. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
    public var resourceTags: [ECRClientTypes.Tag]?

    public init(
        appliedFor: [ECRClientTypes.RCTAppliedFor]? = nil,
        customRoleArn: Swift.String? = nil,
        description: Swift.String? = nil,
        encryptionConfiguration: ECRClientTypes.EncryptionConfigurationForRepositoryCreationTemplate? = nil,
        imageTagMutability: ECRClientTypes.ImageTagMutability? = nil,
        lifecyclePolicy: Swift.String? = nil,
        `prefix`: Swift.String? = nil,
        repositoryPolicy: Swift.String? = nil,
        resourceTags: [ECRClientTypes.Tag]? = nil
    )
    {
        self.appliedFor = appliedFor
        self.customRoleArn = customRoleArn
        self.description = description
        self.encryptionConfiguration = encryptionConfiguration
        self.imageTagMutability = imageTagMutability
        self.lifecyclePolicy = lifecyclePolicy
        self.`prefix` = `prefix`
        self.repositoryPolicy = repositoryPolicy
        self.resourceTags = resourceTags
    }
}

public struct UpdateRepositoryCreationTemplateOutput: Swift.Sendable {
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The details of the repository creation template associated with the request.
    public var repositoryCreationTemplate: ECRClientTypes.RepositoryCreationTemplate?

    public init(
        registryId: Swift.String? = nil,
        repositoryCreationTemplate: ECRClientTypes.RepositoryCreationTemplate? = nil
    )
    {
        self.registryId = registryId
        self.repositoryCreationTemplate = repositoryCreationTemplate
    }
}

/// The layer part size is not valid, or the first byte specified is not consecutive to the last byte of a previous layer part upload.
public struct InvalidLayerPartException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The last valid byte received from the layer part upload that is associated with the exception.
        public internal(set) var lastValidByteReceived: Swift.Int? = nil
        /// The error message associated with the exception.
        public internal(set) var message: Swift.String? = nil
        /// The registry ID associated with the exception.
        public internal(set) var registryId: Swift.String? = nil
        /// The repository name associated with the exception.
        public internal(set) var repositoryName: Swift.String? = nil
        /// The upload ID associated with the exception.
        public internal(set) var uploadId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidLayerPartException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        lastValidByteReceived: Swift.Int? = nil,
        message: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil,
        uploadId: Swift.String? = nil
    )
    {
        self.properties.lastValidByteReceived = lastValidByteReceived
        self.properties.message = message
        self.properties.registryId = registryId
        self.properties.repositoryName = repositoryName
        self.properties.uploadId = uploadId
    }
}

public struct UploadLayerPartInput: Swift.Sendable {
    /// The base64-encoded layer part payload.
    /// This member is required.
    public var layerPartBlob: Foundation.Data?
    /// The position of the first byte of the layer part witin the overall image layer.
    /// This member is required.
    public var partFirstByte: Swift.Int?
    /// The position of the last byte of the layer part within the overall image layer.
    /// This member is required.
    public var partLastByte: Swift.Int?
    /// The Amazon Web Services account ID associated with the registry to which you are uploading layer parts. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository to which you are uploading layer parts.
    /// This member is required.
    public var repositoryName: Swift.String?
    /// The upload ID from a previous [InitiateLayerUpload] operation to associate with the layer part upload.
    /// This member is required.
    public var uploadId: Swift.String?

    public init(
        layerPartBlob: Foundation.Data? = nil,
        partFirstByte: Swift.Int? = nil,
        partLastByte: Swift.Int? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil,
        uploadId: Swift.String? = nil
    )
    {
        self.layerPartBlob = layerPartBlob
        self.partFirstByte = partFirstByte
        self.partLastByte = partLastByte
        self.registryId = registryId
        self.repositoryName = repositoryName
        self.uploadId = uploadId
    }
}

public struct UploadLayerPartOutput: Swift.Sendable {
    /// The integer value of the last byte received in the request.
    public var lastByteReceived: Swift.Int?
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The repository name associated with the request.
    public var repositoryName: Swift.String?
    /// The upload ID associated with the request.
    public var uploadId: Swift.String?

    public init(
        lastByteReceived: Swift.Int? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil,
        uploadId: Swift.String? = nil
    )
    {
        self.lastByteReceived = lastByteReceived
        self.registryId = registryId
        self.repositoryName = repositoryName
        self.uploadId = uploadId
    }
}

public struct ValidatePullThroughCacheRuleInput: Swift.Sendable {
    /// The repository name prefix associated with the pull through cache rule.
    /// This member is required.
    public var ecrRepositoryPrefix: Swift.String?
    /// The registry ID associated with the pull through cache rule. If you do not specify a registry, the default registry is assumed.
    public var registryId: Swift.String?

    public init(
        ecrRepositoryPrefix: Swift.String? = nil,
        registryId: Swift.String? = nil
    )
    {
        self.ecrRepositoryPrefix = ecrRepositoryPrefix
        self.registryId = registryId
    }
}

public struct ValidatePullThroughCacheRuleOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the Amazon Web Services Secrets Manager secret associated with the pull through cache rule.
    public var credentialArn: Swift.String?
    /// The Amazon ECR repository prefix associated with the pull through cache rule.
    public var ecrRepositoryPrefix: Swift.String?
    /// The reason the validation failed. For more details about possible causes and how to address them, see [Using pull through cache rules](https://docs.aws.amazon.com/AmazonECR/latest/userguide/pull-through-cache.html) in the Amazon Elastic Container Registry User Guide.
    public var failure: Swift.String?
    /// Whether or not the pull through cache rule was validated. If true, Amazon ECR was able to reach the upstream registry and authentication was successful. If false, there was an issue and validation failed. The failure reason indicates the cause.
    public var isValid: Swift.Bool
    /// The registry ID associated with the request.
    public var registryId: Swift.String?
    /// The upstream registry URL associated with the pull through cache rule.
    public var upstreamRegistryUrl: Swift.String?

    public init(
        credentialArn: Swift.String? = nil,
        ecrRepositoryPrefix: Swift.String? = nil,
        failure: Swift.String? = nil,
        isValid: Swift.Bool = false,
        registryId: Swift.String? = nil,
        upstreamRegistryUrl: Swift.String? = nil
    )
    {
        self.credentialArn = credentialArn
        self.ecrRepositoryPrefix = ecrRepositoryPrefix
        self.failure = failure
        self.isValid = isValid
        self.registryId = registryId
        self.upstreamRegistryUrl = upstreamRegistryUrl
    }
}

extension BatchCheckLayerAvailabilityInput {

    static func urlPathProvider(_ value: BatchCheckLayerAvailabilityInput) -> Swift.String? {
        return "/"
    }
}

extension BatchDeleteImageInput {

    static func urlPathProvider(_ value: BatchDeleteImageInput) -> Swift.String? {
        return "/"
    }
}

extension BatchGetImageInput {

    static func urlPathProvider(_ value: BatchGetImageInput) -> Swift.String? {
        return "/"
    }
}

extension BatchGetRepositoryScanningConfigurationInput {

    static func urlPathProvider(_ value: BatchGetRepositoryScanningConfigurationInput) -> Swift.String? {
        return "/"
    }
}

extension CompleteLayerUploadInput {

    static func urlPathProvider(_ value: CompleteLayerUploadInput) -> Swift.String? {
        return "/"
    }
}

extension CreatePullThroughCacheRuleInput {

    static func urlPathProvider(_ value: CreatePullThroughCacheRuleInput) -> Swift.String? {
        return "/"
    }
}

extension CreateRepositoryInput {

    static func urlPathProvider(_ value: CreateRepositoryInput) -> Swift.String? {
        return "/"
    }
}

extension CreateRepositoryCreationTemplateInput {

    static func urlPathProvider(_ value: CreateRepositoryCreationTemplateInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteLifecyclePolicyInput {

    static func urlPathProvider(_ value: DeleteLifecyclePolicyInput) -> Swift.String? {
        return "/"
    }
}

extension DeletePullThroughCacheRuleInput {

    static func urlPathProvider(_ value: DeletePullThroughCacheRuleInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteRegistryPolicyInput {

    static func urlPathProvider(_ value: DeleteRegistryPolicyInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteRepositoryInput {

    static func urlPathProvider(_ value: DeleteRepositoryInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteRepositoryCreationTemplateInput {

    static func urlPathProvider(_ value: DeleteRepositoryCreationTemplateInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteRepositoryPolicyInput {

    static func urlPathProvider(_ value: DeleteRepositoryPolicyInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeImageReplicationStatusInput {

    static func urlPathProvider(_ value: DescribeImageReplicationStatusInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeImagesInput {

    static func urlPathProvider(_ value: DescribeImagesInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeImageScanFindingsInput {

    static func urlPathProvider(_ value: DescribeImageScanFindingsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribePullThroughCacheRulesInput {

    static func urlPathProvider(_ value: DescribePullThroughCacheRulesInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeRegistryInput {

    static func urlPathProvider(_ value: DescribeRegistryInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeRepositoriesInput {

    static func urlPathProvider(_ value: DescribeRepositoriesInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeRepositoryCreationTemplatesInput {

    static func urlPathProvider(_ value: DescribeRepositoryCreationTemplatesInput) -> Swift.String? {
        return "/"
    }
}

extension GetAccountSettingInput {

    static func urlPathProvider(_ value: GetAccountSettingInput) -> Swift.String? {
        return "/"
    }
}

extension GetAuthorizationTokenInput {

    static func urlPathProvider(_ value: GetAuthorizationTokenInput) -> Swift.String? {
        return "/"
    }
}

extension GetDownloadUrlForLayerInput {

    static func urlPathProvider(_ value: GetDownloadUrlForLayerInput) -> Swift.String? {
        return "/"
    }
}

extension GetLifecyclePolicyInput {

    static func urlPathProvider(_ value: GetLifecyclePolicyInput) -> Swift.String? {
        return "/"
    }
}

extension GetLifecyclePolicyPreviewInput {

    static func urlPathProvider(_ value: GetLifecyclePolicyPreviewInput) -> Swift.String? {
        return "/"
    }
}

extension GetRegistryPolicyInput {

    static func urlPathProvider(_ value: GetRegistryPolicyInput) -> Swift.String? {
        return "/"
    }
}

extension GetRegistryScanningConfigurationInput {

    static func urlPathProvider(_ value: GetRegistryScanningConfigurationInput) -> Swift.String? {
        return "/"
    }
}

extension GetRepositoryPolicyInput {

    static func urlPathProvider(_ value: GetRepositoryPolicyInput) -> Swift.String? {
        return "/"
    }
}

extension InitiateLayerUploadInput {

    static func urlPathProvider(_ value: InitiateLayerUploadInput) -> Swift.String? {
        return "/"
    }
}

extension ListImagesInput {

    static func urlPathProvider(_ value: ListImagesInput) -> Swift.String? {
        return "/"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        return "/"
    }
}

extension PutAccountSettingInput {

    static func urlPathProvider(_ value: PutAccountSettingInput) -> Swift.String? {
        return "/"
    }
}

extension PutImageInput {

    static func urlPathProvider(_ value: PutImageInput) -> Swift.String? {
        return "/"
    }
}

extension PutImageScanningConfigurationInput {

    static func urlPathProvider(_ value: PutImageScanningConfigurationInput) -> Swift.String? {
        return "/"
    }
}

extension PutImageTagMutabilityInput {

    static func urlPathProvider(_ value: PutImageTagMutabilityInput) -> Swift.String? {
        return "/"
    }
}

extension PutLifecyclePolicyInput {

    static func urlPathProvider(_ value: PutLifecyclePolicyInput) -> Swift.String? {
        return "/"
    }
}

extension PutRegistryPolicyInput {

    static func urlPathProvider(_ value: PutRegistryPolicyInput) -> Swift.String? {
        return "/"
    }
}

extension PutRegistryScanningConfigurationInput {

    static func urlPathProvider(_ value: PutRegistryScanningConfigurationInput) -> Swift.String? {
        return "/"
    }
}

extension PutReplicationConfigurationInput {

    static func urlPathProvider(_ value: PutReplicationConfigurationInput) -> Swift.String? {
        return "/"
    }
}

extension SetRepositoryPolicyInput {

    static func urlPathProvider(_ value: SetRepositoryPolicyInput) -> Swift.String? {
        return "/"
    }
}

extension StartImageScanInput {

    static func urlPathProvider(_ value: StartImageScanInput) -> Swift.String? {
        return "/"
    }
}

extension StartLifecyclePolicyPreviewInput {

    static func urlPathProvider(_ value: StartLifecyclePolicyPreviewInput) -> Swift.String? {
        return "/"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        return "/"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        return "/"
    }
}

extension UpdatePullThroughCacheRuleInput {

    static func urlPathProvider(_ value: UpdatePullThroughCacheRuleInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateRepositoryCreationTemplateInput {

    static func urlPathProvider(_ value: UpdateRepositoryCreationTemplateInput) -> Swift.String? {
        return "/"
    }
}

extension UploadLayerPartInput {

    static func urlPathProvider(_ value: UploadLayerPartInput) -> Swift.String? {
        return "/"
    }
}

extension ValidatePullThroughCacheRuleInput {

    static func urlPathProvider(_ value: ValidatePullThroughCacheRuleInput) -> Swift.String? {
        return "/"
    }
}

extension BatchCheckLayerAvailabilityInput {

    static func write(value: BatchCheckLayerAvailabilityInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["layerDigests"].writeList(value.layerDigests, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension BatchDeleteImageInput {

    static func write(value: BatchDeleteImageInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["imageIds"].writeList(value.imageIds, memberWritingClosure: ECRClientTypes.ImageIdentifier.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension BatchGetImageInput {

    static func write(value: BatchGetImageInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["acceptedMediaTypes"].writeList(value.acceptedMediaTypes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["imageIds"].writeList(value.imageIds, memberWritingClosure: ECRClientTypes.ImageIdentifier.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension BatchGetRepositoryScanningConfigurationInput {

    static func write(value: BatchGetRepositoryScanningConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["repositoryNames"].writeList(value.repositoryNames, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CompleteLayerUploadInput {

    static func write(value: CompleteLayerUploadInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["layerDigests"].writeList(value.layerDigests, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
        try writer["uploadId"].write(value.uploadId)
    }
}

extension CreatePullThroughCacheRuleInput {

    static func write(value: CreatePullThroughCacheRuleInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["credentialArn"].write(value.credentialArn)
        try writer["ecrRepositoryPrefix"].write(value.ecrRepositoryPrefix)
        try writer["registryId"].write(value.registryId)
        try writer["upstreamRegistry"].write(value.upstreamRegistry)
        try writer["upstreamRegistryUrl"].write(value.upstreamRegistryUrl)
    }
}

extension CreateRepositoryInput {

    static func write(value: CreateRepositoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["encryptionConfiguration"].write(value.encryptionConfiguration, with: ECRClientTypes.EncryptionConfiguration.write(value:to:))
        try writer["imageScanningConfiguration"].write(value.imageScanningConfiguration, with: ECRClientTypes.ImageScanningConfiguration.write(value:to:))
        try writer["imageTagMutability"].write(value.imageTagMutability)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
        try writer["tags"].writeList(value.tags, memberWritingClosure: ECRClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateRepositoryCreationTemplateInput {

    static func write(value: CreateRepositoryCreationTemplateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["appliedFor"].writeList(value.appliedFor, memberWritingClosure: SmithyReadWrite.WritingClosureBox<ECRClientTypes.RCTAppliedFor>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["customRoleArn"].write(value.customRoleArn)
        try writer["description"].write(value.description)
        try writer["encryptionConfiguration"].write(value.encryptionConfiguration, with: ECRClientTypes.EncryptionConfigurationForRepositoryCreationTemplate.write(value:to:))
        try writer["imageTagMutability"].write(value.imageTagMutability)
        try writer["lifecyclePolicy"].write(value.lifecyclePolicy)
        try writer["prefix"].write(value.`prefix`)
        try writer["repositoryPolicy"].write(value.repositoryPolicy)
        try writer["resourceTags"].writeList(value.resourceTags, memberWritingClosure: ECRClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DeleteLifecyclePolicyInput {

    static func write(value: DeleteLifecyclePolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension DeletePullThroughCacheRuleInput {

    static func write(value: DeletePullThroughCacheRuleInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ecrRepositoryPrefix"].write(value.ecrRepositoryPrefix)
        try writer["registryId"].write(value.registryId)
    }
}

extension DeleteRegistryPolicyInput {

    static func write(value: DeleteRegistryPolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard value != nil else { return }
        _ = writer[""]  // create an empty structure
    }
}

extension DeleteRepositoryInput {

    static func write(value: DeleteRepositoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["force"].write(value.force)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension DeleteRepositoryCreationTemplateInput {

    static func write(value: DeleteRepositoryCreationTemplateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["prefix"].write(value.`prefix`)
    }
}

extension DeleteRepositoryPolicyInput {

    static func write(value: DeleteRepositoryPolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension DescribeImageReplicationStatusInput {

    static func write(value: DescribeImageReplicationStatusInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["imageId"].write(value.imageId, with: ECRClientTypes.ImageIdentifier.write(value:to:))
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension DescribeImagesInput {

    static func write(value: DescribeImagesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filter"].write(value.filter, with: ECRClientTypes.DescribeImagesFilter.write(value:to:))
        try writer["imageIds"].writeList(value.imageIds, memberWritingClosure: ECRClientTypes.ImageIdentifier.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension DescribeImageScanFindingsInput {

    static func write(value: DescribeImageScanFindingsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["imageId"].write(value.imageId, with: ECRClientTypes.ImageIdentifier.write(value:to:))
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension DescribePullThroughCacheRulesInput {

    static func write(value: DescribePullThroughCacheRulesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ecrRepositoryPrefixes"].writeList(value.ecrRepositoryPrefixes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["registryId"].write(value.registryId)
    }
}

extension DescribeRegistryInput {

    static func write(value: DescribeRegistryInput?, to writer: SmithyJSON.Writer) throws {
        guard value != nil else { return }
        _ = writer[""]  // create an empty structure
    }
}

extension DescribeRepositoriesInput {

    static func write(value: DescribeRepositoriesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryNames"].writeList(value.repositoryNames, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DescribeRepositoryCreationTemplatesInput {

    static func write(value: DescribeRepositoryCreationTemplatesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["prefixes"].writeList(value.prefixes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension GetAccountSettingInput {

    static func write(value: GetAccountSettingInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["name"].write(value.name)
    }
}

extension GetAuthorizationTokenInput {

    static func write(value: GetAuthorizationTokenInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["registryIds"].writeList(value.registryIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension GetDownloadUrlForLayerInput {

    static func write(value: GetDownloadUrlForLayerInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["layerDigest"].write(value.layerDigest)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension GetLifecyclePolicyInput {

    static func write(value: GetLifecyclePolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension GetLifecyclePolicyPreviewInput {

    static func write(value: GetLifecyclePolicyPreviewInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filter"].write(value.filter, with: ECRClientTypes.LifecyclePolicyPreviewFilter.write(value:to:))
        try writer["imageIds"].writeList(value.imageIds, memberWritingClosure: ECRClientTypes.ImageIdentifier.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension GetRegistryPolicyInput {

    static func write(value: GetRegistryPolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard value != nil else { return }
        _ = writer[""]  // create an empty structure
    }
}

extension GetRegistryScanningConfigurationInput {

    static func write(value: GetRegistryScanningConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard value != nil else { return }
        _ = writer[""]  // create an empty structure
    }
}

extension GetRepositoryPolicyInput {

    static func write(value: GetRepositoryPolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension InitiateLayerUploadInput {

    static func write(value: InitiateLayerUploadInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension ListImagesInput {

    static func write(value: ListImagesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filter"].write(value.filter, with: ECRClientTypes.ListImagesFilter.write(value:to:))
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension ListTagsForResourceInput {

    static func write(value: ListTagsForResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["resourceArn"].write(value.resourceArn)
    }
}

extension PutAccountSettingInput {

    static func write(value: PutAccountSettingInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["name"].write(value.name)
        try writer["value"].write(value.value)
    }
}

extension PutImageInput {

    static func write(value: PutImageInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["imageDigest"].write(value.imageDigest)
        try writer["imageManifest"].write(value.imageManifest)
        try writer["imageManifestMediaType"].write(value.imageManifestMediaType)
        try writer["imageTag"].write(value.imageTag)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension PutImageScanningConfigurationInput {

    static func write(value: PutImageScanningConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["imageScanningConfiguration"].write(value.imageScanningConfiguration, with: ECRClientTypes.ImageScanningConfiguration.write(value:to:))
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension PutImageTagMutabilityInput {

    static func write(value: PutImageTagMutabilityInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["imageTagMutability"].write(value.imageTagMutability)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension PutLifecyclePolicyInput {

    static func write(value: PutLifecyclePolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["lifecyclePolicyText"].write(value.lifecyclePolicyText)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension PutRegistryPolicyInput {

    static func write(value: PutRegistryPolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["policyText"].write(value.policyText)
    }
}

extension PutRegistryScanningConfigurationInput {

    static func write(value: PutRegistryScanningConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["rules"].writeList(value.rules, memberWritingClosure: ECRClientTypes.RegistryScanningRule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["scanType"].write(value.scanType)
    }
}

extension PutReplicationConfigurationInput {

    static func write(value: PutReplicationConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["replicationConfiguration"].write(value.replicationConfiguration, with: ECRClientTypes.ReplicationConfiguration.write(value:to:))
    }
}

extension SetRepositoryPolicyInput {

    static func write(value: SetRepositoryPolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["force"].write(value.force)
        try writer["policyText"].write(value.policyText)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension StartImageScanInput {

    static func write(value: StartImageScanInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["imageId"].write(value.imageId, with: ECRClientTypes.ImageIdentifier.write(value:to:))
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension StartLifecyclePolicyPreviewInput {

    static func write(value: StartLifecyclePolicyPreviewInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["lifecyclePolicyText"].write(value.lifecyclePolicyText)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["resourceArn"].write(value.resourceArn)
        try writer["tags"].writeList(value.tags, memberWritingClosure: ECRClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UntagResourceInput {

    static func write(value: UntagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["resourceArn"].write(value.resourceArn)
        try writer["tagKeys"].writeList(value.tagKeys, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdatePullThroughCacheRuleInput {

    static func write(value: UpdatePullThroughCacheRuleInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["credentialArn"].write(value.credentialArn)
        try writer["ecrRepositoryPrefix"].write(value.ecrRepositoryPrefix)
        try writer["registryId"].write(value.registryId)
    }
}

extension UpdateRepositoryCreationTemplateInput {

    static func write(value: UpdateRepositoryCreationTemplateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["appliedFor"].writeList(value.appliedFor, memberWritingClosure: SmithyReadWrite.WritingClosureBox<ECRClientTypes.RCTAppliedFor>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["customRoleArn"].write(value.customRoleArn)
        try writer["description"].write(value.description)
        try writer["encryptionConfiguration"].write(value.encryptionConfiguration, with: ECRClientTypes.EncryptionConfigurationForRepositoryCreationTemplate.write(value:to:))
        try writer["imageTagMutability"].write(value.imageTagMutability)
        try writer["lifecyclePolicy"].write(value.lifecyclePolicy)
        try writer["prefix"].write(value.`prefix`)
        try writer["repositoryPolicy"].write(value.repositoryPolicy)
        try writer["resourceTags"].writeList(value.resourceTags, memberWritingClosure: ECRClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UploadLayerPartInput {

    static func write(value: UploadLayerPartInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["layerPartBlob"].write(value.layerPartBlob)
        try writer["partFirstByte"].write(value.partFirstByte)
        try writer["partLastByte"].write(value.partLastByte)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
        try writer["uploadId"].write(value.uploadId)
    }
}

extension ValidatePullThroughCacheRuleInput {

    static func write(value: ValidatePullThroughCacheRuleInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ecrRepositoryPrefix"].write(value.ecrRepositoryPrefix)
        try writer["registryId"].write(value.registryId)
    }
}

extension BatchCheckLayerAvailabilityOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> BatchCheckLayerAvailabilityOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = BatchCheckLayerAvailabilityOutput()
        value.failures = try reader["failures"].readListIfPresent(memberReadingClosure: ECRClientTypes.LayerFailure.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.layers = try reader["layers"].readListIfPresent(memberReadingClosure: ECRClientTypes.Layer.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension BatchDeleteImageOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> BatchDeleteImageOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = BatchDeleteImageOutput()
        value.failures = try reader["failures"].readListIfPresent(memberReadingClosure: ECRClientTypes.ImageFailure.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.imageIds = try reader["imageIds"].readListIfPresent(memberReadingClosure: ECRClientTypes.ImageIdentifier.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension BatchGetImageOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> BatchGetImageOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = BatchGetImageOutput()
        value.failures = try reader["failures"].readListIfPresent(memberReadingClosure: ECRClientTypes.ImageFailure.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.images = try reader["images"].readListIfPresent(memberReadingClosure: ECRClientTypes.Image.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension BatchGetRepositoryScanningConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> BatchGetRepositoryScanningConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = BatchGetRepositoryScanningConfigurationOutput()
        value.failures = try reader["failures"].readListIfPresent(memberReadingClosure: ECRClientTypes.RepositoryScanningConfigurationFailure.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.scanningConfigurations = try reader["scanningConfigurations"].readListIfPresent(memberReadingClosure: ECRClientTypes.RepositoryScanningConfiguration.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension CompleteLayerUploadOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CompleteLayerUploadOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CompleteLayerUploadOutput()
        value.layerDigest = try reader["layerDigest"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.uploadId = try reader["uploadId"].readIfPresent()
        return value
    }
}

extension CreatePullThroughCacheRuleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreatePullThroughCacheRuleOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreatePullThroughCacheRuleOutput()
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.credentialArn = try reader["credentialArn"].readIfPresent()
        value.ecrRepositoryPrefix = try reader["ecrRepositoryPrefix"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.upstreamRegistry = try reader["upstreamRegistry"].readIfPresent()
        value.upstreamRegistryUrl = try reader["upstreamRegistryUrl"].readIfPresent()
        return value
    }
}

extension CreateRepositoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateRepositoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateRepositoryOutput()
        value.repository = try reader["repository"].readIfPresent(with: ECRClientTypes.Repository.read(from:))
        return value
    }
}

extension CreateRepositoryCreationTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateRepositoryCreationTemplateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateRepositoryCreationTemplateOutput()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryCreationTemplate = try reader["repositoryCreationTemplate"].readIfPresent(with: ECRClientTypes.RepositoryCreationTemplate.read(from:))
        return value
    }
}

extension DeleteLifecyclePolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteLifecyclePolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteLifecyclePolicyOutput()
        value.lastEvaluatedAt = try reader["lastEvaluatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lifecyclePolicyText = try reader["lifecyclePolicyText"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension DeletePullThroughCacheRuleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeletePullThroughCacheRuleOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeletePullThroughCacheRuleOutput()
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.credentialArn = try reader["credentialArn"].readIfPresent()
        value.ecrRepositoryPrefix = try reader["ecrRepositoryPrefix"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.upstreamRegistryUrl = try reader["upstreamRegistryUrl"].readIfPresent()
        return value
    }
}

extension DeleteRegistryPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteRegistryPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteRegistryPolicyOutput()
        value.policyText = try reader["policyText"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        return value
    }
}

extension DeleteRepositoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteRepositoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteRepositoryOutput()
        value.repository = try reader["repository"].readIfPresent(with: ECRClientTypes.Repository.read(from:))
        return value
    }
}

extension DeleteRepositoryCreationTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteRepositoryCreationTemplateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteRepositoryCreationTemplateOutput()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryCreationTemplate = try reader["repositoryCreationTemplate"].readIfPresent(with: ECRClientTypes.RepositoryCreationTemplate.read(from:))
        return value
    }
}

extension DeleteRepositoryPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteRepositoryPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteRepositoryPolicyOutput()
        value.policyText = try reader["policyText"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension DescribeImageReplicationStatusOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeImageReplicationStatusOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeImageReplicationStatusOutput()
        value.imageId = try reader["imageId"].readIfPresent(with: ECRClientTypes.ImageIdentifier.read(from:))
        value.replicationStatuses = try reader["replicationStatuses"].readListIfPresent(memberReadingClosure: ECRClientTypes.ImageReplicationStatus.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension DescribeImagesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeImagesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeImagesOutput()
        value.imageDetails = try reader["imageDetails"].readListIfPresent(memberReadingClosure: ECRClientTypes.ImageDetail.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension DescribeImageScanFindingsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeImageScanFindingsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeImageScanFindingsOutput()
        value.imageId = try reader["imageId"].readIfPresent(with: ECRClientTypes.ImageIdentifier.read(from:))
        value.imageScanFindings = try reader["imageScanFindings"].readIfPresent(with: ECRClientTypes.ImageScanFindings.read(from:))
        value.imageScanStatus = try reader["imageScanStatus"].readIfPresent(with: ECRClientTypes.ImageScanStatus.read(from:))
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension DescribePullThroughCacheRulesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribePullThroughCacheRulesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribePullThroughCacheRulesOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.pullThroughCacheRules = try reader["pullThroughCacheRules"].readListIfPresent(memberReadingClosure: ECRClientTypes.PullThroughCacheRule.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeRegistryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeRegistryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeRegistryOutput()
        value.registryId = try reader["registryId"].readIfPresent()
        value.replicationConfiguration = try reader["replicationConfiguration"].readIfPresent(with: ECRClientTypes.ReplicationConfiguration.read(from:))
        return value
    }
}

extension DescribeRepositoriesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeRepositoriesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeRepositoriesOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.repositories = try reader["repositories"].readListIfPresent(memberReadingClosure: ECRClientTypes.Repository.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeRepositoryCreationTemplatesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeRepositoryCreationTemplatesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeRepositoryCreationTemplatesOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryCreationTemplates = try reader["repositoryCreationTemplates"].readListIfPresent(memberReadingClosure: ECRClientTypes.RepositoryCreationTemplate.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension GetAccountSettingOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetAccountSettingOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetAccountSettingOutput()
        value.name = try reader["name"].readIfPresent()
        value.value = try reader["value"].readIfPresent()
        return value
    }
}

extension GetAuthorizationTokenOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetAuthorizationTokenOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetAuthorizationTokenOutput()
        value.authorizationData = try reader["authorizationData"].readListIfPresent(memberReadingClosure: ECRClientTypes.AuthorizationData.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension GetDownloadUrlForLayerOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetDownloadUrlForLayerOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetDownloadUrlForLayerOutput()
        value.downloadUrl = try reader["downloadUrl"].readIfPresent()
        value.layerDigest = try reader["layerDigest"].readIfPresent()
        return value
    }
}

extension GetLifecyclePolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetLifecyclePolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetLifecyclePolicyOutput()
        value.lastEvaluatedAt = try reader["lastEvaluatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lifecyclePolicyText = try reader["lifecyclePolicyText"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension GetLifecyclePolicyPreviewOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetLifecyclePolicyPreviewOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetLifecyclePolicyPreviewOutput()
        value.lifecyclePolicyText = try reader["lifecyclePolicyText"].readIfPresent()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.previewResults = try reader["previewResults"].readListIfPresent(memberReadingClosure: ECRClientTypes.LifecyclePolicyPreviewResult.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        value.summary = try reader["summary"].readIfPresent(with: ECRClientTypes.LifecyclePolicyPreviewSummary.read(from:))
        return value
    }
}

extension GetRegistryPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetRegistryPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetRegistryPolicyOutput()
        value.policyText = try reader["policyText"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        return value
    }
}

extension GetRegistryScanningConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetRegistryScanningConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetRegistryScanningConfigurationOutput()
        value.registryId = try reader["registryId"].readIfPresent()
        value.scanningConfiguration = try reader["scanningConfiguration"].readIfPresent(with: ECRClientTypes.RegistryScanningConfiguration.read(from:))
        return value
    }
}

extension GetRepositoryPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetRepositoryPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetRepositoryPolicyOutput()
        value.policyText = try reader["policyText"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension InitiateLayerUploadOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> InitiateLayerUploadOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = InitiateLayerUploadOutput()
        value.partSize = try reader["partSize"].readIfPresent()
        value.uploadId = try reader["uploadId"].readIfPresent()
        return value
    }
}

extension ListImagesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListImagesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListImagesOutput()
        value.imageIds = try reader["imageIds"].readListIfPresent(memberReadingClosure: ECRClientTypes.ImageIdentifier.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["tags"].readListIfPresent(memberReadingClosure: ECRClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension PutAccountSettingOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutAccountSettingOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutAccountSettingOutput()
        value.name = try reader["name"].readIfPresent()
        value.value = try reader["value"].readIfPresent()
        return value
    }
}

extension PutImageOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutImageOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutImageOutput()
        value.image = try reader["image"].readIfPresent(with: ECRClientTypes.Image.read(from:))
        return value
    }
}

extension PutImageScanningConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutImageScanningConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutImageScanningConfigurationOutput()
        value.imageScanningConfiguration = try reader["imageScanningConfiguration"].readIfPresent(with: ECRClientTypes.ImageScanningConfiguration.read(from:))
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension PutImageTagMutabilityOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutImageTagMutabilityOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutImageTagMutabilityOutput()
        value.imageTagMutability = try reader["imageTagMutability"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension PutLifecyclePolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutLifecyclePolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutLifecyclePolicyOutput()
        value.lifecyclePolicyText = try reader["lifecyclePolicyText"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension PutRegistryPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutRegistryPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutRegistryPolicyOutput()
        value.policyText = try reader["policyText"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        return value
    }
}

extension PutRegistryScanningConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutRegistryScanningConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutRegistryScanningConfigurationOutput()
        value.registryScanningConfiguration = try reader["registryScanningConfiguration"].readIfPresent(with: ECRClientTypes.RegistryScanningConfiguration.read(from:))
        return value
    }
}

extension PutReplicationConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutReplicationConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutReplicationConfigurationOutput()
        value.replicationConfiguration = try reader["replicationConfiguration"].readIfPresent(with: ECRClientTypes.ReplicationConfiguration.read(from:))
        return value
    }
}

extension SetRepositoryPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SetRepositoryPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = SetRepositoryPolicyOutput()
        value.policyText = try reader["policyText"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension StartImageScanOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartImageScanOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartImageScanOutput()
        value.imageId = try reader["imageId"].readIfPresent(with: ECRClientTypes.ImageIdentifier.read(from:))
        value.imageScanStatus = try reader["imageScanStatus"].readIfPresent(with: ECRClientTypes.ImageScanStatus.read(from:))
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension StartLifecyclePolicyPreviewOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartLifecyclePolicyPreviewOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartLifecyclePolicyPreviewOutput()
        value.lifecyclePolicyText = try reader["lifecyclePolicyText"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdatePullThroughCacheRuleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdatePullThroughCacheRuleOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdatePullThroughCacheRuleOutput()
        value.credentialArn = try reader["credentialArn"].readIfPresent()
        value.ecrRepositoryPrefix = try reader["ecrRepositoryPrefix"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.updatedAt = try reader["updatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension UpdateRepositoryCreationTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateRepositoryCreationTemplateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateRepositoryCreationTemplateOutput()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryCreationTemplate = try reader["repositoryCreationTemplate"].readIfPresent(with: ECRClientTypes.RepositoryCreationTemplate.read(from:))
        return value
    }
}

extension UploadLayerPartOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UploadLayerPartOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UploadLayerPartOutput()
        value.lastByteReceived = try reader["lastByteReceived"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.uploadId = try reader["uploadId"].readIfPresent()
        return value
    }
}

extension ValidatePullThroughCacheRuleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ValidatePullThroughCacheRuleOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ValidatePullThroughCacheRuleOutput()
        value.credentialArn = try reader["credentialArn"].readIfPresent()
        value.ecrRepositoryPrefix = try reader["ecrRepositoryPrefix"].readIfPresent()
        value.failure = try reader["failure"].readIfPresent()
        value.isValid = try reader["isValid"].readIfPresent() ?? false
        value.registryId = try reader["registryId"].readIfPresent()
        value.upstreamRegistryUrl = try reader["upstreamRegistryUrl"].readIfPresent()
        return value
    }
}

enum BatchCheckLayerAvailabilityOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum BatchDeleteImageOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum BatchGetImageOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnableToGetUpstreamImageException": return try UnableToGetUpstreamImageException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum BatchGetRepositoryScanningConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CompleteLayerUploadOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "EmptyUploadException": return try EmptyUploadException.makeError(baseError: baseError)
            case "InvalidLayerException": return try InvalidLayerException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "KmsException": return try KmsException.makeError(baseError: baseError)
            case "LayerAlreadyExistsException": return try LayerAlreadyExistsException.makeError(baseError: baseError)
            case "LayerPartTooSmallException": return try LayerPartTooSmallException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UploadNotFoundException": return try UploadNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreatePullThroughCacheRuleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "PullThroughCacheRuleAlreadyExistsException": return try PullThroughCacheRuleAlreadyExistsException.makeError(baseError: baseError)
            case "SecretNotFoundException": return try SecretNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnableToAccessSecretException": return try UnableToAccessSecretException.makeError(baseError: baseError)
            case "UnableToDecryptSecretValueException": return try UnableToDecryptSecretValueException.makeError(baseError: baseError)
            case "UnsupportedUpstreamRegistryException": return try UnsupportedUpstreamRegistryException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateRepositoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "InvalidTagParameterException": return try InvalidTagParameterException.makeError(baseError: baseError)
            case "KmsException": return try KmsException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "RepositoryAlreadyExistsException": return try RepositoryAlreadyExistsException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "TooManyTagsException": return try TooManyTagsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateRepositoryCreationTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "TemplateAlreadyExistsException": return try TemplateAlreadyExistsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteLifecyclePolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LifecyclePolicyNotFoundException": return try LifecyclePolicyNotFoundException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeletePullThroughCacheRuleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "PullThroughCacheRuleNotFoundException": return try PullThroughCacheRuleNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteRegistryPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RegistryPolicyNotFoundException": return try RegistryPolicyNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteRepositoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "KmsException": return try KmsException.makeError(baseError: baseError)
            case "RepositoryNotEmptyException": return try RepositoryNotEmptyException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteRepositoryCreationTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "TemplateNotFoundException": return try TemplateNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteRepositoryPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "RepositoryPolicyNotFoundException": return try RepositoryPolicyNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeImageReplicationStatusOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ImageNotFoundException": return try ImageNotFoundException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeImagesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ImageNotFoundException": return try ImageNotFoundException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeImageScanFindingsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ImageNotFoundException": return try ImageNotFoundException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ScanNotFoundException": return try ScanNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribePullThroughCacheRulesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "PullThroughCacheRuleNotFoundException": return try PullThroughCacheRuleNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeRegistryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeRepositoriesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeRepositoryCreationTemplatesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetAccountSettingOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetAuthorizationTokenOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetDownloadUrlForLayerOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LayerInaccessibleException": return try LayerInaccessibleException.makeError(baseError: baseError)
            case "LayersNotFoundException": return try LayersNotFoundException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnableToGetUpstreamLayerException": return try UnableToGetUpstreamLayerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetLifecyclePolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LifecyclePolicyNotFoundException": return try LifecyclePolicyNotFoundException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetLifecyclePolicyPreviewOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LifecyclePolicyPreviewNotFoundException": return try LifecyclePolicyPreviewNotFoundException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetRegistryPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RegistryPolicyNotFoundException": return try RegistryPolicyNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetRegistryScanningConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetRepositoryPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "RepositoryPolicyNotFoundException": return try RepositoryPolicyNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum InitiateLayerUploadOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "KmsException": return try KmsException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListImagesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutAccountSettingOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutImageOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ImageAlreadyExistsException": return try ImageAlreadyExistsException.makeError(baseError: baseError)
            case "ImageDigestDoesNotMatchException": return try ImageDigestDoesNotMatchException.makeError(baseError: baseError)
            case "ImageTagAlreadyExistsException": return try ImageTagAlreadyExistsException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "KmsException": return try KmsException.makeError(baseError: baseError)
            case "LayersNotFoundException": return try LayersNotFoundException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ReferencedImagesNotFoundException": return try ReferencedImagesNotFoundException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutImageScanningConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutImageTagMutabilityOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutLifecyclePolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutRegistryPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutRegistryScanningConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutReplicationConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SetRepositoryPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartImageScanOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ImageNotFoundException": return try ImageNotFoundException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedImageTypeException": return try UnsupportedImageTypeException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartLifecyclePolicyPreviewOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LifecyclePolicyNotFoundException": return try LifecyclePolicyNotFoundException.makeError(baseError: baseError)
            case "LifecyclePolicyPreviewInProgressException": return try LifecyclePolicyPreviewInProgressException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "InvalidTagParameterException": return try InvalidTagParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "TooManyTagsException": return try TooManyTagsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "InvalidTagParameterException": return try InvalidTagParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "TooManyTagsException": return try TooManyTagsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdatePullThroughCacheRuleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "PullThroughCacheRuleNotFoundException": return try PullThroughCacheRuleNotFoundException.makeError(baseError: baseError)
            case "SecretNotFoundException": return try SecretNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnableToAccessSecretException": return try UnableToAccessSecretException.makeError(baseError: baseError)
            case "UnableToDecryptSecretValueException": return try UnableToDecryptSecretValueException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateRepositoryCreationTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "TemplateNotFoundException": return try TemplateNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UploadLayerPartOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidLayerPartException": return try InvalidLayerPartException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "KmsException": return try KmsException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UploadNotFoundException": return try UploadNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ValidatePullThroughCacheRuleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "PullThroughCacheRuleNotFoundException": return try PullThroughCacheRuleNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension RepositoryNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> RepositoryNotFoundException {
        let reader = baseError.errorBodyReader
        var value = RepositoryNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServerException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ServerException {
        let reader = baseError.errorBodyReader
        var value = ServerException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidParameterException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidParameterException {
        let reader = baseError.errorBodyReader
        var value = InvalidParameterException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LimitExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LimitExceededException {
        let reader = baseError.errorBodyReader
        var value = LimitExceededException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UnableToGetUpstreamImageException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UnableToGetUpstreamImageException {
        let reader = baseError.errorBodyReader
        var value = UnableToGetUpstreamImageException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension EmptyUploadException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> EmptyUploadException {
        let reader = baseError.errorBodyReader
        var value = EmptyUploadException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LayerPartTooSmallException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LayerPartTooSmallException {
        let reader = baseError.errorBodyReader
        var value = LayerPartTooSmallException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidLayerException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidLayerException {
        let reader = baseError.errorBodyReader
        var value = InvalidLayerException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UploadNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UploadNotFoundException {
        let reader = baseError.errorBodyReader
        var value = UploadNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension KmsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> KmsException {
        let reader = baseError.errorBodyReader
        var value = KmsException()
        value.properties.kmsError = try reader["kmsError"].readIfPresent()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LayerAlreadyExistsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LayerAlreadyExistsException {
        let reader = baseError.errorBodyReader
        var value = LayerAlreadyExistsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension PullThroughCacheRuleAlreadyExistsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> PullThroughCacheRuleAlreadyExistsException {
        let reader = baseError.errorBodyReader
        var value = PullThroughCacheRuleAlreadyExistsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UnsupportedUpstreamRegistryException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UnsupportedUpstreamRegistryException {
        let reader = baseError.errorBodyReader
        var value = UnsupportedUpstreamRegistryException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SecretNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> SecretNotFoundException {
        let reader = baseError.errorBodyReader
        var value = SecretNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UnableToAccessSecretException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UnableToAccessSecretException {
        let reader = baseError.errorBodyReader
        var value = UnableToAccessSecretException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UnableToDecryptSecretValueException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UnableToDecryptSecretValueException {
        let reader = baseError.errorBodyReader
        var value = UnableToDecryptSecretValueException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidTagParameterException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidTagParameterException {
        let reader = baseError.errorBodyReader
        var value = InvalidTagParameterException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension RepositoryAlreadyExistsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> RepositoryAlreadyExistsException {
        let reader = baseError.errorBodyReader
        var value = RepositoryAlreadyExistsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TooManyTagsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> TooManyTagsException {
        let reader = baseError.errorBodyReader
        var value = TooManyTagsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TemplateAlreadyExistsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> TemplateAlreadyExistsException {
        let reader = baseError.errorBodyReader
        var value = TemplateAlreadyExistsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LifecyclePolicyNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LifecyclePolicyNotFoundException {
        let reader = baseError.errorBodyReader
        var value = LifecyclePolicyNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension PullThroughCacheRuleNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> PullThroughCacheRuleNotFoundException {
        let reader = baseError.errorBodyReader
        var value = PullThroughCacheRuleNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension RegistryPolicyNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> RegistryPolicyNotFoundException {
        let reader = baseError.errorBodyReader
        var value = RegistryPolicyNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension RepositoryNotEmptyException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> RepositoryNotEmptyException {
        let reader = baseError.errorBodyReader
        var value = RepositoryNotEmptyException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TemplateNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> TemplateNotFoundException {
        let reader = baseError.errorBodyReader
        var value = TemplateNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension RepositoryPolicyNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> RepositoryPolicyNotFoundException {
        let reader = baseError.errorBodyReader
        var value = RepositoryPolicyNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ImageNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ImageNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ImageNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ScanNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ScanNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ScanNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UnableToGetUpstreamLayerException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UnableToGetUpstreamLayerException {
        let reader = baseError.errorBodyReader
        var value = UnableToGetUpstreamLayerException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LayerInaccessibleException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LayerInaccessibleException {
        let reader = baseError.errorBodyReader
        var value = LayerInaccessibleException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LayersNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LayersNotFoundException {
        let reader = baseError.errorBodyReader
        var value = LayersNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LifecyclePolicyPreviewNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LifecyclePolicyPreviewNotFoundException {
        let reader = baseError.errorBodyReader
        var value = LifecyclePolicyPreviewNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ImageDigestDoesNotMatchException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ImageDigestDoesNotMatchException {
        let reader = baseError.errorBodyReader
        var value = ImageDigestDoesNotMatchException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ReferencedImagesNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ReferencedImagesNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ReferencedImagesNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ImageTagAlreadyExistsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ImageTagAlreadyExistsException {
        let reader = baseError.errorBodyReader
        var value = ImageTagAlreadyExistsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ImageAlreadyExistsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ImageAlreadyExistsException {
        let reader = baseError.errorBodyReader
        var value = ImageAlreadyExistsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UnsupportedImageTypeException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UnsupportedImageTypeException {
        let reader = baseError.errorBodyReader
        var value = UnsupportedImageTypeException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LifecyclePolicyPreviewInProgressException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LifecyclePolicyPreviewInProgressException {
        let reader = baseError.errorBodyReader
        var value = LifecyclePolicyPreviewInProgressException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidLayerPartException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidLayerPartException {
        let reader = baseError.errorBodyReader
        var value = InvalidLayerPartException()
        value.properties.lastValidByteReceived = try reader["lastValidByteReceived"].readIfPresent()
        value.properties.message = try reader["message"].readIfPresent()
        value.properties.registryId = try reader["registryId"].readIfPresent()
        value.properties.repositoryName = try reader["repositoryName"].readIfPresent()
        value.properties.uploadId = try reader["uploadId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ECRClientTypes.Layer {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.Layer {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.Layer()
        value.layerDigest = try reader["layerDigest"].readIfPresent()
        value.layerAvailability = try reader["layerAvailability"].readIfPresent()
        value.layerSize = try reader["layerSize"].readIfPresent()
        value.mediaType = try reader["mediaType"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.LayerFailure {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.LayerFailure {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.LayerFailure()
        value.layerDigest = try reader["layerDigest"].readIfPresent()
        value.failureCode = try reader["failureCode"].readIfPresent()
        value.failureReason = try reader["failureReason"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.ImageIdentifier {

    static func write(value: ECRClientTypes.ImageIdentifier?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["imageDigest"].write(value.imageDigest)
        try writer["imageTag"].write(value.imageTag)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ImageIdentifier {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ImageIdentifier()
        value.imageDigest = try reader["imageDigest"].readIfPresent()
        value.imageTag = try reader["imageTag"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.ImageFailure {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ImageFailure {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ImageFailure()
        value.imageId = try reader["imageId"].readIfPresent(with: ECRClientTypes.ImageIdentifier.read(from:))
        value.failureCode = try reader["failureCode"].readIfPresent()
        value.failureReason = try reader["failureReason"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.Image {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.Image {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.Image()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.imageId = try reader["imageId"].readIfPresent(with: ECRClientTypes.ImageIdentifier.read(from:))
        value.imageManifest = try reader["imageManifest"].readIfPresent()
        value.imageManifestMediaType = try reader["imageManifestMediaType"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.RepositoryScanningConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.RepositoryScanningConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.RepositoryScanningConfiguration()
        value.repositoryArn = try reader["repositoryArn"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.scanOnPush = try reader["scanOnPush"].readIfPresent() ?? false
        value.scanFrequency = try reader["scanFrequency"].readIfPresent()
        value.appliedScanFilters = try reader["appliedScanFilters"].readListIfPresent(memberReadingClosure: ECRClientTypes.ScanningRepositoryFilter.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ECRClientTypes.ScanningRepositoryFilter {

    static func write(value: ECRClientTypes.ScanningRepositoryFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filter"].write(value.filter)
        try writer["filterType"].write(value.filterType)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ScanningRepositoryFilter {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ScanningRepositoryFilter()
        value.filter = try reader["filter"].readIfPresent() ?? ""
        value.filterType = try reader["filterType"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension ECRClientTypes.RepositoryScanningConfigurationFailure {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.RepositoryScanningConfigurationFailure {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.RepositoryScanningConfigurationFailure()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.failureCode = try reader["failureCode"].readIfPresent()
        value.failureReason = try reader["failureReason"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.Repository {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.Repository {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.Repository()
        value.repositoryArn = try reader["repositoryArn"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.repositoryUri = try reader["repositoryUri"].readIfPresent()
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.imageTagMutability = try reader["imageTagMutability"].readIfPresent()
        value.imageScanningConfiguration = try reader["imageScanningConfiguration"].readIfPresent(with: ECRClientTypes.ImageScanningConfiguration.read(from:))
        value.encryptionConfiguration = try reader["encryptionConfiguration"].readIfPresent(with: ECRClientTypes.EncryptionConfiguration.read(from:))
        return value
    }
}

extension ECRClientTypes.EncryptionConfiguration {

    static func write(value: ECRClientTypes.EncryptionConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["encryptionType"].write(value.encryptionType)
        try writer["kmsKey"].write(value.kmsKey)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.EncryptionConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.EncryptionConfiguration()
        value.encryptionType = try reader["encryptionType"].readIfPresent() ?? .sdkUnknown("")
        value.kmsKey = try reader["kmsKey"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.ImageScanningConfiguration {

    static func write(value: ECRClientTypes.ImageScanningConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["scanOnPush"].write(value.scanOnPush)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ImageScanningConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ImageScanningConfiguration()
        value.scanOnPush = try reader["scanOnPush"].readIfPresent() ?? false
        return value
    }
}

extension ECRClientTypes.RepositoryCreationTemplate {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.RepositoryCreationTemplate {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.RepositoryCreationTemplate()
        value.`prefix` = try reader["prefix"].readIfPresent()
        value.description = try reader["description"].readIfPresent()
        value.encryptionConfiguration = try reader["encryptionConfiguration"].readIfPresent(with: ECRClientTypes.EncryptionConfigurationForRepositoryCreationTemplate.read(from:))
        value.resourceTags = try reader["resourceTags"].readListIfPresent(memberReadingClosure: ECRClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.imageTagMutability = try reader["imageTagMutability"].readIfPresent()
        value.repositoryPolicy = try reader["repositoryPolicy"].readIfPresent()
        value.lifecyclePolicy = try reader["lifecyclePolicy"].readIfPresent()
        value.appliedFor = try reader["appliedFor"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<ECRClientTypes.RCTAppliedFor>().read(from:), memberNodeInfo: "member", isFlattened: false)
        value.customRoleArn = try reader["customRoleArn"].readIfPresent()
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.updatedAt = try reader["updatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension ECRClientTypes.Tag {

    static func write(value: ECRClientTypes.Tag?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Key"].write(value.key)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.Tag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.Tag()
        value.key = try reader["Key"].readIfPresent() ?? ""
        value.value = try reader["Value"].readIfPresent() ?? ""
        return value
    }
}

extension ECRClientTypes.EncryptionConfigurationForRepositoryCreationTemplate {

    static func write(value: ECRClientTypes.EncryptionConfigurationForRepositoryCreationTemplate?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["encryptionType"].write(value.encryptionType)
        try writer["kmsKey"].write(value.kmsKey)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.EncryptionConfigurationForRepositoryCreationTemplate {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.EncryptionConfigurationForRepositoryCreationTemplate()
        value.encryptionType = try reader["encryptionType"].readIfPresent() ?? .sdkUnknown("")
        value.kmsKey = try reader["kmsKey"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.ImageReplicationStatus {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ImageReplicationStatus {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ImageReplicationStatus()
        value.region = try reader["region"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        value.failureCode = try reader["failureCode"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.ImageDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ImageDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ImageDetail()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.imageDigest = try reader["imageDigest"].readIfPresent()
        value.imageTags = try reader["imageTags"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.imageSizeInBytes = try reader["imageSizeInBytes"].readIfPresent()
        value.imagePushedAt = try reader["imagePushedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.imageScanStatus = try reader["imageScanStatus"].readIfPresent(with: ECRClientTypes.ImageScanStatus.read(from:))
        value.imageScanFindingsSummary = try reader["imageScanFindingsSummary"].readIfPresent(with: ECRClientTypes.ImageScanFindingsSummary.read(from:))
        value.imageManifestMediaType = try reader["imageManifestMediaType"].readIfPresent()
        value.artifactMediaType = try reader["artifactMediaType"].readIfPresent()
        value.lastRecordedPullTime = try reader["lastRecordedPullTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension ECRClientTypes.ImageScanFindingsSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ImageScanFindingsSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ImageScanFindingsSummary()
        value.imageScanCompletedAt = try reader["imageScanCompletedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.vulnerabilitySourceUpdatedAt = try reader["vulnerabilitySourceUpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.findingSeverityCounts = try reader["findingSeverityCounts"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readInt(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension ECRClientTypes.ImageScanStatus {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ImageScanStatus {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ImageScanStatus()
        value.status = try reader["status"].readIfPresent()
        value.description = try reader["description"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.ImageScanFindings {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ImageScanFindings {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ImageScanFindings()
        value.imageScanCompletedAt = try reader["imageScanCompletedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.vulnerabilitySourceUpdatedAt = try reader["vulnerabilitySourceUpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.findingSeverityCounts = try reader["findingSeverityCounts"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readInt(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.findings = try reader["findings"].readListIfPresent(memberReadingClosure: ECRClientTypes.ImageScanFinding.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.enhancedFindings = try reader["enhancedFindings"].readListIfPresent(memberReadingClosure: ECRClientTypes.EnhancedImageScanFinding.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ECRClientTypes.EnhancedImageScanFinding {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.EnhancedImageScanFinding {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.EnhancedImageScanFinding()
        value.awsAccountId = try reader["awsAccountId"].readIfPresent()
        value.description = try reader["description"].readIfPresent()
        value.findingArn = try reader["findingArn"].readIfPresent()
        value.firstObservedAt = try reader["firstObservedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastObservedAt = try reader["lastObservedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.packageVulnerabilityDetails = try reader["packageVulnerabilityDetails"].readIfPresent(with: ECRClientTypes.PackageVulnerabilityDetails.read(from:))
        value.remediation = try reader["remediation"].readIfPresent(with: ECRClientTypes.Remediation.read(from:))
        value.resources = try reader["resources"].readListIfPresent(memberReadingClosure: ECRClientTypes.Resource.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.score = try reader["score"].readIfPresent() ?? 0
        value.scoreDetails = try reader["scoreDetails"].readIfPresent(with: ECRClientTypes.ScoreDetails.read(from:))
        value.severity = try reader["severity"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        value.title = try reader["title"].readIfPresent()
        value.type = try reader["type"].readIfPresent()
        value.updatedAt = try reader["updatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.fixAvailable = try reader["fixAvailable"].readIfPresent()
        value.exploitAvailable = try reader["exploitAvailable"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.ScoreDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ScoreDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ScoreDetails()
        value.cvss = try reader["cvss"].readIfPresent(with: ECRClientTypes.CvssScoreDetails.read(from:))
        return value
    }
}

extension ECRClientTypes.CvssScoreDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.CvssScoreDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.CvssScoreDetails()
        value.adjustments = try reader["adjustments"].readListIfPresent(memberReadingClosure: ECRClientTypes.CvssScoreAdjustment.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.score = try reader["score"].readIfPresent() ?? 0
        value.scoreSource = try reader["scoreSource"].readIfPresent()
        value.scoringVector = try reader["scoringVector"].readIfPresent()
        value.version = try reader["version"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.CvssScoreAdjustment {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.CvssScoreAdjustment {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.CvssScoreAdjustment()
        value.metric = try reader["metric"].readIfPresent()
        value.reason = try reader["reason"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.Resource {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.Resource {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.Resource()
        value.details = try reader["details"].readIfPresent(with: ECRClientTypes.ResourceDetails.read(from:))
        value.id = try reader["id"].readIfPresent()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.type = try reader["type"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.ResourceDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ResourceDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ResourceDetails()
        value.awsEcrContainerImage = try reader["awsEcrContainerImage"].readIfPresent(with: ECRClientTypes.AwsEcrContainerImageDetails.read(from:))
        return value
    }
}

extension ECRClientTypes.AwsEcrContainerImageDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.AwsEcrContainerImageDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.AwsEcrContainerImageDetails()
        value.architecture = try reader["architecture"].readIfPresent()
        value.author = try reader["author"].readIfPresent()
        value.imageHash = try reader["imageHash"].readIfPresent()
        value.imageTags = try reader["imageTags"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.platform = try reader["platform"].readIfPresent()
        value.pushedAt = try reader["pushedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.registry = try reader["registry"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.Remediation {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.Remediation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.Remediation()
        value.recommendation = try reader["recommendation"].readIfPresent(with: ECRClientTypes.Recommendation.read(from:))
        return value
    }
}

extension ECRClientTypes.Recommendation {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.Recommendation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.Recommendation()
        value.url = try reader["url"].readIfPresent()
        value.text = try reader["text"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.PackageVulnerabilityDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.PackageVulnerabilityDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.PackageVulnerabilityDetails()
        value.cvss = try reader["cvss"].readListIfPresent(memberReadingClosure: ECRClientTypes.CvssScore.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.referenceUrls = try reader["referenceUrls"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.relatedVulnerabilities = try reader["relatedVulnerabilities"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.source = try reader["source"].readIfPresent()
        value.sourceUrl = try reader["sourceUrl"].readIfPresent()
        value.vendorCreatedAt = try reader["vendorCreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.vendorSeverity = try reader["vendorSeverity"].readIfPresent()
        value.vendorUpdatedAt = try reader["vendorUpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.vulnerabilityId = try reader["vulnerabilityId"].readIfPresent()
        value.vulnerablePackages = try reader["vulnerablePackages"].readListIfPresent(memberReadingClosure: ECRClientTypes.VulnerablePackage.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ECRClientTypes.VulnerablePackage {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.VulnerablePackage {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.VulnerablePackage()
        value.arch = try reader["arch"].readIfPresent()
        value.epoch = try reader["epoch"].readIfPresent()
        value.filePath = try reader["filePath"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.packageManager = try reader["packageManager"].readIfPresent()
        value.release = try reader["release"].readIfPresent()
        value.sourceLayerHash = try reader["sourceLayerHash"].readIfPresent()
        value.version = try reader["version"].readIfPresent()
        value.fixedInVersion = try reader["fixedInVersion"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.CvssScore {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.CvssScore {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.CvssScore()
        value.baseScore = try reader["baseScore"].readIfPresent() ?? 0
        value.scoringVector = try reader["scoringVector"].readIfPresent()
        value.source = try reader["source"].readIfPresent()
        value.version = try reader["version"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.ImageScanFinding {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ImageScanFinding {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ImageScanFinding()
        value.name = try reader["name"].readIfPresent()
        value.description = try reader["description"].readIfPresent()
        value.uri = try reader["uri"].readIfPresent()
        value.severity = try reader["severity"].readIfPresent()
        value.attributes = try reader["attributes"].readListIfPresent(memberReadingClosure: ECRClientTypes.Attribute.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ECRClientTypes.Attribute {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.Attribute {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.Attribute()
        value.key = try reader["key"].readIfPresent() ?? ""
        value.value = try reader["value"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.PullThroughCacheRule {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.PullThroughCacheRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.PullThroughCacheRule()
        value.ecrRepositoryPrefix = try reader["ecrRepositoryPrefix"].readIfPresent()
        value.upstreamRegistryUrl = try reader["upstreamRegistryUrl"].readIfPresent()
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.registryId = try reader["registryId"].readIfPresent()
        value.credentialArn = try reader["credentialArn"].readIfPresent()
        value.upstreamRegistry = try reader["upstreamRegistry"].readIfPresent()
        value.updatedAt = try reader["updatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension ECRClientTypes.ReplicationConfiguration {

    static func write(value: ECRClientTypes.ReplicationConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["rules"].writeList(value.rules, memberWritingClosure: ECRClientTypes.ReplicationRule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ReplicationConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ReplicationConfiguration()
        value.rules = try reader["rules"].readListIfPresent(memberReadingClosure: ECRClientTypes.ReplicationRule.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ECRClientTypes.ReplicationRule {

    static func write(value: ECRClientTypes.ReplicationRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["destinations"].writeList(value.destinations, memberWritingClosure: ECRClientTypes.ReplicationDestination.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["repositoryFilters"].writeList(value.repositoryFilters, memberWritingClosure: ECRClientTypes.RepositoryFilter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ReplicationRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ReplicationRule()
        value.destinations = try reader["destinations"].readListIfPresent(memberReadingClosure: ECRClientTypes.ReplicationDestination.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.repositoryFilters = try reader["repositoryFilters"].readListIfPresent(memberReadingClosure: ECRClientTypes.RepositoryFilter.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ECRClientTypes.RepositoryFilter {

    static func write(value: ECRClientTypes.RepositoryFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filter"].write(value.filter)
        try writer["filterType"].write(value.filterType)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.RepositoryFilter {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.RepositoryFilter()
        value.filter = try reader["filter"].readIfPresent() ?? ""
        value.filterType = try reader["filterType"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension ECRClientTypes.ReplicationDestination {

    static func write(value: ECRClientTypes.ReplicationDestination?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["region"].write(value.region)
        try writer["registryId"].write(value.registryId)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.ReplicationDestination {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.ReplicationDestination()
        value.region = try reader["region"].readIfPresent() ?? ""
        value.registryId = try reader["registryId"].readIfPresent() ?? ""
        return value
    }
}

extension ECRClientTypes.AuthorizationData {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.AuthorizationData {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.AuthorizationData()
        value.authorizationToken = try reader["authorizationToken"].readIfPresent()
        value.expiresAt = try reader["expiresAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.proxyEndpoint = try reader["proxyEndpoint"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.LifecyclePolicyPreviewResult {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.LifecyclePolicyPreviewResult {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.LifecyclePolicyPreviewResult()
        value.imageTags = try reader["imageTags"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.imageDigest = try reader["imageDigest"].readIfPresent()
        value.imagePushedAt = try reader["imagePushedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.action = try reader["action"].readIfPresent(with: ECRClientTypes.LifecyclePolicyRuleAction.read(from:))
        value.appliedRulePriority = try reader["appliedRulePriority"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.LifecyclePolicyRuleAction {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.LifecyclePolicyRuleAction {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.LifecyclePolicyRuleAction()
        value.type = try reader["type"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.LifecyclePolicyPreviewSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.LifecyclePolicyPreviewSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.LifecyclePolicyPreviewSummary()
        value.expiringImageTotalCount = try reader["expiringImageTotalCount"].readIfPresent()
        return value
    }
}

extension ECRClientTypes.RegistryScanningConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.RegistryScanningConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.RegistryScanningConfiguration()
        value.scanType = try reader["scanType"].readIfPresent()
        value.rules = try reader["rules"].readListIfPresent(memberReadingClosure: ECRClientTypes.RegistryScanningRule.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ECRClientTypes.RegistryScanningRule {

    static func write(value: ECRClientTypes.RegistryScanningRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["repositoryFilters"].writeList(value.repositoryFilters, memberWritingClosure: ECRClientTypes.ScanningRepositoryFilter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["scanFrequency"].write(value.scanFrequency)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ECRClientTypes.RegistryScanningRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRClientTypes.RegistryScanningRule()
        value.scanFrequency = try reader["scanFrequency"].readIfPresent() ?? .sdkUnknown("")
        value.repositoryFilters = try reader["repositoryFilters"].readListIfPresent(memberReadingClosure: ECRClientTypes.ScanningRepositoryFilter.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ECRClientTypes.DescribeImagesFilter {

    static func write(value: ECRClientTypes.DescribeImagesFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tagStatus"].write(value.tagStatus)
    }
}

extension ECRClientTypes.LifecyclePolicyPreviewFilter {

    static func write(value: ECRClientTypes.LifecyclePolicyPreviewFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tagStatus"].write(value.tagStatus)
    }
}

extension ECRClientTypes.ListImagesFilter {

    static func write(value: ECRClientTypes.ListImagesFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tagStatus"].write(value.tagStatus)
    }
}

public enum ECRClientTypes {}
