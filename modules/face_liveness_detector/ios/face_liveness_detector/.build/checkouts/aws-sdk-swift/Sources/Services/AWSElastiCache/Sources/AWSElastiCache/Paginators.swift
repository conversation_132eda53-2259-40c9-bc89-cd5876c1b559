//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import Foundation
import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension ElastiCacheClient {
    /// Paginate over `[DescribeCacheClustersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCacheClustersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCacheClustersOutput`
    public func describeCacheClustersPaginated(input: DescribeCacheClustersInput) -> ClientRuntime.PaginatorSequence<DescribeCacheClustersInput, DescribeCacheClustersOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCacheClustersInput, DescribeCacheClustersOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeCacheClusters(input:))
    }
}

extension DescribeCacheClustersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCacheClustersInput {
        return DescribeCacheClustersInput(
            cacheClusterId: self.cacheClusterId,
            marker: token,
            maxRecords: self.maxRecords,
            showCacheClustersNotInReplicationGroups: self.showCacheClustersNotInReplicationGroups,
            showCacheNodeInfo: self.showCacheNodeInfo
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCacheClustersInput, OperationStackOutput == DescribeCacheClustersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCacheClustersPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.CacheCluster]`
    /// - Returns: `[ElastiCacheClientTypes.CacheCluster]`
    public func cacheClusters() async throws -> [ElastiCacheClientTypes.CacheCluster] {
        return try await self.asyncCompactMap { item in item.cacheClusters }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeCacheEngineVersionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCacheEngineVersionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCacheEngineVersionsOutput`
    public func describeCacheEngineVersionsPaginated(input: DescribeCacheEngineVersionsInput) -> ClientRuntime.PaginatorSequence<DescribeCacheEngineVersionsInput, DescribeCacheEngineVersionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCacheEngineVersionsInput, DescribeCacheEngineVersionsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeCacheEngineVersions(input:))
    }
}

extension DescribeCacheEngineVersionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCacheEngineVersionsInput {
        return DescribeCacheEngineVersionsInput(
            cacheParameterGroupFamily: self.cacheParameterGroupFamily,
            defaultOnly: self.defaultOnly,
            engine: self.engine,
            engineVersion: self.engineVersion,
            marker: token,
            maxRecords: self.maxRecords
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCacheEngineVersionsInput, OperationStackOutput == DescribeCacheEngineVersionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCacheEngineVersionsPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.CacheEngineVersion]`
    /// - Returns: `[ElastiCacheClientTypes.CacheEngineVersion]`
    public func cacheEngineVersions() async throws -> [ElastiCacheClientTypes.CacheEngineVersion] {
        return try await self.asyncCompactMap { item in item.cacheEngineVersions }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeCacheParameterGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCacheParameterGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCacheParameterGroupsOutput`
    public func describeCacheParameterGroupsPaginated(input: DescribeCacheParameterGroupsInput) -> ClientRuntime.PaginatorSequence<DescribeCacheParameterGroupsInput, DescribeCacheParameterGroupsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCacheParameterGroupsInput, DescribeCacheParameterGroupsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeCacheParameterGroups(input:))
    }
}

extension DescribeCacheParameterGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCacheParameterGroupsInput {
        return DescribeCacheParameterGroupsInput(
            cacheParameterGroupName: self.cacheParameterGroupName,
            marker: token,
            maxRecords: self.maxRecords
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCacheParameterGroupsInput, OperationStackOutput == DescribeCacheParameterGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCacheParameterGroupsPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.CacheParameterGroup]`
    /// - Returns: `[ElastiCacheClientTypes.CacheParameterGroup]`
    public func cacheParameterGroups() async throws -> [ElastiCacheClientTypes.CacheParameterGroup] {
        return try await self.asyncCompactMap { item in item.cacheParameterGroups }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeCacheParametersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCacheParametersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCacheParametersOutput`
    public func describeCacheParametersPaginated(input: DescribeCacheParametersInput) -> ClientRuntime.PaginatorSequence<DescribeCacheParametersInput, DescribeCacheParametersOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCacheParametersInput, DescribeCacheParametersOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeCacheParameters(input:))
    }
}

extension DescribeCacheParametersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCacheParametersInput {
        return DescribeCacheParametersInput(
            cacheParameterGroupName: self.cacheParameterGroupName,
            marker: token,
            maxRecords: self.maxRecords,
            source: self.source
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCacheParametersInput, OperationStackOutput == DescribeCacheParametersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCacheParametersPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.Parameter]`
    /// - Returns: `[ElastiCacheClientTypes.Parameter]`
    public func parameters() async throws -> [ElastiCacheClientTypes.Parameter] {
        return try await self.asyncCompactMap { item in item.parameters }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeCacheSecurityGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCacheSecurityGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCacheSecurityGroupsOutput`
    public func describeCacheSecurityGroupsPaginated(input: DescribeCacheSecurityGroupsInput) -> ClientRuntime.PaginatorSequence<DescribeCacheSecurityGroupsInput, DescribeCacheSecurityGroupsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCacheSecurityGroupsInput, DescribeCacheSecurityGroupsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeCacheSecurityGroups(input:))
    }
}

extension DescribeCacheSecurityGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCacheSecurityGroupsInput {
        return DescribeCacheSecurityGroupsInput(
            cacheSecurityGroupName: self.cacheSecurityGroupName,
            marker: token,
            maxRecords: self.maxRecords
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCacheSecurityGroupsInput, OperationStackOutput == DescribeCacheSecurityGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCacheSecurityGroupsPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.CacheSecurityGroup]`
    /// - Returns: `[ElastiCacheClientTypes.CacheSecurityGroup]`
    public func cacheSecurityGroups() async throws -> [ElastiCacheClientTypes.CacheSecurityGroup] {
        return try await self.asyncCompactMap { item in item.cacheSecurityGroups }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeCacheSubnetGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCacheSubnetGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCacheSubnetGroupsOutput`
    public func describeCacheSubnetGroupsPaginated(input: DescribeCacheSubnetGroupsInput) -> ClientRuntime.PaginatorSequence<DescribeCacheSubnetGroupsInput, DescribeCacheSubnetGroupsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCacheSubnetGroupsInput, DescribeCacheSubnetGroupsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeCacheSubnetGroups(input:))
    }
}

extension DescribeCacheSubnetGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCacheSubnetGroupsInput {
        return DescribeCacheSubnetGroupsInput(
            cacheSubnetGroupName: self.cacheSubnetGroupName,
            marker: token,
            maxRecords: self.maxRecords
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCacheSubnetGroupsInput, OperationStackOutput == DescribeCacheSubnetGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCacheSubnetGroupsPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.CacheSubnetGroup]`
    /// - Returns: `[ElastiCacheClientTypes.CacheSubnetGroup]`
    public func cacheSubnetGroups() async throws -> [ElastiCacheClientTypes.CacheSubnetGroup] {
        return try await self.asyncCompactMap { item in item.cacheSubnetGroups }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeEngineDefaultParametersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeEngineDefaultParametersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeEngineDefaultParametersOutput`
    public func describeEngineDefaultParametersPaginated(input: DescribeEngineDefaultParametersInput) -> ClientRuntime.PaginatorSequence<DescribeEngineDefaultParametersInput, DescribeEngineDefaultParametersOutput> {
        return ClientRuntime.PaginatorSequence<DescribeEngineDefaultParametersInput, DescribeEngineDefaultParametersOutput>(input: input, inputKey: \.marker, outputKey: \.engineDefaults?.marker, paginationFunction: self.describeEngineDefaultParameters(input:))
    }
}

extension DescribeEngineDefaultParametersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeEngineDefaultParametersInput {
        return DescribeEngineDefaultParametersInput(
            cacheParameterGroupFamily: self.cacheParameterGroupFamily,
            marker: token,
            maxRecords: self.maxRecords
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeEngineDefaultParametersInput, OperationStackOutput == DescribeEngineDefaultParametersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeEngineDefaultParametersPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.Parameter]`
    /// - Returns: `[ElastiCacheClientTypes.Parameter]`
    public func parameters() async throws -> [ElastiCacheClientTypes.Parameter] {
        return try await self.asyncCompactMap { item in item.engineDefaults?.parameters }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeEventsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeEventsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeEventsOutput`
    public func describeEventsPaginated(input: DescribeEventsInput) -> ClientRuntime.PaginatorSequence<DescribeEventsInput, DescribeEventsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeEventsInput, DescribeEventsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeEvents(input:))
    }
}

extension DescribeEventsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeEventsInput {
        return DescribeEventsInput(
            duration: self.duration,
            endTime: self.endTime,
            marker: token,
            maxRecords: self.maxRecords,
            sourceIdentifier: self.sourceIdentifier,
            sourceType: self.sourceType,
            startTime: self.startTime
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeEventsInput, OperationStackOutput == DescribeEventsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeEventsPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.Event]`
    /// - Returns: `[ElastiCacheClientTypes.Event]`
    public func events() async throws -> [ElastiCacheClientTypes.Event] {
        return try await self.asyncCompactMap { item in item.events }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeGlobalReplicationGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeGlobalReplicationGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeGlobalReplicationGroupsOutput`
    public func describeGlobalReplicationGroupsPaginated(input: DescribeGlobalReplicationGroupsInput) -> ClientRuntime.PaginatorSequence<DescribeGlobalReplicationGroupsInput, DescribeGlobalReplicationGroupsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeGlobalReplicationGroupsInput, DescribeGlobalReplicationGroupsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeGlobalReplicationGroups(input:))
    }
}

extension DescribeGlobalReplicationGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeGlobalReplicationGroupsInput {
        return DescribeGlobalReplicationGroupsInput(
            globalReplicationGroupId: self.globalReplicationGroupId,
            marker: token,
            maxRecords: self.maxRecords,
            showMemberInfo: self.showMemberInfo
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeGlobalReplicationGroupsInput, OperationStackOutput == DescribeGlobalReplicationGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeGlobalReplicationGroupsPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.GlobalReplicationGroup]`
    /// - Returns: `[ElastiCacheClientTypes.GlobalReplicationGroup]`
    public func globalReplicationGroups() async throws -> [ElastiCacheClientTypes.GlobalReplicationGroup] {
        return try await self.asyncCompactMap { item in item.globalReplicationGroups }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeReplicationGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReplicationGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReplicationGroupsOutput`
    public func describeReplicationGroupsPaginated(input: DescribeReplicationGroupsInput) -> ClientRuntime.PaginatorSequence<DescribeReplicationGroupsInput, DescribeReplicationGroupsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReplicationGroupsInput, DescribeReplicationGroupsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeReplicationGroups(input:))
    }
}

extension DescribeReplicationGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReplicationGroupsInput {
        return DescribeReplicationGroupsInput(
            marker: token,
            maxRecords: self.maxRecords,
            replicationGroupId: self.replicationGroupId
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeReplicationGroupsInput, OperationStackOutput == DescribeReplicationGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeReplicationGroupsPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.ReplicationGroup]`
    /// - Returns: `[ElastiCacheClientTypes.ReplicationGroup]`
    public func replicationGroups() async throws -> [ElastiCacheClientTypes.ReplicationGroup] {
        return try await self.asyncCompactMap { item in item.replicationGroups }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeReservedCacheNodesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReservedCacheNodesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReservedCacheNodesOutput`
    public func describeReservedCacheNodesPaginated(input: DescribeReservedCacheNodesInput) -> ClientRuntime.PaginatorSequence<DescribeReservedCacheNodesInput, DescribeReservedCacheNodesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReservedCacheNodesInput, DescribeReservedCacheNodesOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeReservedCacheNodes(input:))
    }
}

extension DescribeReservedCacheNodesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReservedCacheNodesInput {
        return DescribeReservedCacheNodesInput(
            cacheNodeType: self.cacheNodeType,
            duration: self.duration,
            marker: token,
            maxRecords: self.maxRecords,
            offeringType: self.offeringType,
            productDescription: self.productDescription,
            reservedCacheNodeId: self.reservedCacheNodeId,
            reservedCacheNodesOfferingId: self.reservedCacheNodesOfferingId
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeReservedCacheNodesInput, OperationStackOutput == DescribeReservedCacheNodesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeReservedCacheNodesPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.ReservedCacheNode]`
    /// - Returns: `[ElastiCacheClientTypes.ReservedCacheNode]`
    public func reservedCacheNodes() async throws -> [ElastiCacheClientTypes.ReservedCacheNode] {
        return try await self.asyncCompactMap { item in item.reservedCacheNodes }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeReservedCacheNodesOfferingsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReservedCacheNodesOfferingsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReservedCacheNodesOfferingsOutput`
    public func describeReservedCacheNodesOfferingsPaginated(input: DescribeReservedCacheNodesOfferingsInput) -> ClientRuntime.PaginatorSequence<DescribeReservedCacheNodesOfferingsInput, DescribeReservedCacheNodesOfferingsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReservedCacheNodesOfferingsInput, DescribeReservedCacheNodesOfferingsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeReservedCacheNodesOfferings(input:))
    }
}

extension DescribeReservedCacheNodesOfferingsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReservedCacheNodesOfferingsInput {
        return DescribeReservedCacheNodesOfferingsInput(
            cacheNodeType: self.cacheNodeType,
            duration: self.duration,
            marker: token,
            maxRecords: self.maxRecords,
            offeringType: self.offeringType,
            productDescription: self.productDescription,
            reservedCacheNodesOfferingId: self.reservedCacheNodesOfferingId
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeReservedCacheNodesOfferingsInput, OperationStackOutput == DescribeReservedCacheNodesOfferingsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeReservedCacheNodesOfferingsPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.ReservedCacheNodesOffering]`
    /// - Returns: `[ElastiCacheClientTypes.ReservedCacheNodesOffering]`
    public func reservedCacheNodesOfferings() async throws -> [ElastiCacheClientTypes.ReservedCacheNodesOffering] {
        return try await self.asyncCompactMap { item in item.reservedCacheNodesOfferings }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeServerlessCachesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeServerlessCachesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeServerlessCachesOutput`
    public func describeServerlessCachesPaginated(input: DescribeServerlessCachesInput) -> ClientRuntime.PaginatorSequence<DescribeServerlessCachesInput, DescribeServerlessCachesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeServerlessCachesInput, DescribeServerlessCachesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeServerlessCaches(input:))
    }
}

extension DescribeServerlessCachesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeServerlessCachesInput {
        return DescribeServerlessCachesInput(
            maxResults: self.maxResults,
            nextToken: token,
            serverlessCacheName: self.serverlessCacheName
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeServerlessCachesInput, OperationStackOutput == DescribeServerlessCachesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeServerlessCachesPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.ServerlessCache]`
    /// - Returns: `[ElastiCacheClientTypes.ServerlessCache]`
    public func serverlessCaches() async throws -> [ElastiCacheClientTypes.ServerlessCache] {
        return try await self.asyncCompactMap { item in item.serverlessCaches }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeServerlessCacheSnapshotsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeServerlessCacheSnapshotsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeServerlessCacheSnapshotsOutput`
    public func describeServerlessCacheSnapshotsPaginated(input: DescribeServerlessCacheSnapshotsInput) -> ClientRuntime.PaginatorSequence<DescribeServerlessCacheSnapshotsInput, DescribeServerlessCacheSnapshotsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeServerlessCacheSnapshotsInput, DescribeServerlessCacheSnapshotsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeServerlessCacheSnapshots(input:))
    }
}

extension DescribeServerlessCacheSnapshotsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeServerlessCacheSnapshotsInput {
        return DescribeServerlessCacheSnapshotsInput(
            maxResults: self.maxResults,
            nextToken: token,
            serverlessCacheName: self.serverlessCacheName,
            serverlessCacheSnapshotName: self.serverlessCacheSnapshotName,
            snapshotType: self.snapshotType
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeServerlessCacheSnapshotsInput, OperationStackOutput == DescribeServerlessCacheSnapshotsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeServerlessCacheSnapshotsPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.ServerlessCacheSnapshot]`
    /// - Returns: `[ElastiCacheClientTypes.ServerlessCacheSnapshot]`
    public func serverlessCacheSnapshots() async throws -> [ElastiCacheClientTypes.ServerlessCacheSnapshot] {
        return try await self.asyncCompactMap { item in item.serverlessCacheSnapshots }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeServiceUpdatesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeServiceUpdatesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeServiceUpdatesOutput`
    public func describeServiceUpdatesPaginated(input: DescribeServiceUpdatesInput) -> ClientRuntime.PaginatorSequence<DescribeServiceUpdatesInput, DescribeServiceUpdatesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeServiceUpdatesInput, DescribeServiceUpdatesOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeServiceUpdates(input:))
    }
}

extension DescribeServiceUpdatesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeServiceUpdatesInput {
        return DescribeServiceUpdatesInput(
            marker: token,
            maxRecords: self.maxRecords,
            serviceUpdateName: self.serviceUpdateName,
            serviceUpdateStatus: self.serviceUpdateStatus
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeServiceUpdatesInput, OperationStackOutput == DescribeServiceUpdatesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeServiceUpdatesPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.ServiceUpdate]`
    /// - Returns: `[ElastiCacheClientTypes.ServiceUpdate]`
    public func serviceUpdates() async throws -> [ElastiCacheClientTypes.ServiceUpdate] {
        return try await self.asyncCompactMap { item in item.serviceUpdates }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeSnapshotsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeSnapshotsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeSnapshotsOutput`
    public func describeSnapshotsPaginated(input: DescribeSnapshotsInput) -> ClientRuntime.PaginatorSequence<DescribeSnapshotsInput, DescribeSnapshotsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeSnapshotsInput, DescribeSnapshotsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeSnapshots(input:))
    }
}

extension DescribeSnapshotsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeSnapshotsInput {
        return DescribeSnapshotsInput(
            cacheClusterId: self.cacheClusterId,
            marker: token,
            maxRecords: self.maxRecords,
            replicationGroupId: self.replicationGroupId,
            showNodeGroupConfig: self.showNodeGroupConfig,
            snapshotName: self.snapshotName,
            snapshotSource: self.snapshotSource
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeSnapshotsInput, OperationStackOutput == DescribeSnapshotsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeSnapshotsPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.Snapshot]`
    /// - Returns: `[ElastiCacheClientTypes.Snapshot]`
    public func snapshots() async throws -> [ElastiCacheClientTypes.Snapshot] {
        return try await self.asyncCompactMap { item in item.snapshots }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeUpdateActionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeUpdateActionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeUpdateActionsOutput`
    public func describeUpdateActionsPaginated(input: DescribeUpdateActionsInput) -> ClientRuntime.PaginatorSequence<DescribeUpdateActionsInput, DescribeUpdateActionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeUpdateActionsInput, DescribeUpdateActionsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeUpdateActions(input:))
    }
}

extension DescribeUpdateActionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeUpdateActionsInput {
        return DescribeUpdateActionsInput(
            cacheClusterIds: self.cacheClusterIds,
            engine: self.engine,
            marker: token,
            maxRecords: self.maxRecords,
            replicationGroupIds: self.replicationGroupIds,
            serviceUpdateName: self.serviceUpdateName,
            serviceUpdateStatus: self.serviceUpdateStatus,
            serviceUpdateTimeRange: self.serviceUpdateTimeRange,
            showNodeLevelUpdateStatus: self.showNodeLevelUpdateStatus,
            updateActionStatus: self.updateActionStatus
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeUpdateActionsInput, OperationStackOutput == DescribeUpdateActionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeUpdateActionsPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.UpdateAction]`
    /// - Returns: `[ElastiCacheClientTypes.UpdateAction]`
    public func updateActions() async throws -> [ElastiCacheClientTypes.UpdateAction] {
        return try await self.asyncCompactMap { item in item.updateActions }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeUserGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeUserGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeUserGroupsOutput`
    public func describeUserGroupsPaginated(input: DescribeUserGroupsInput) -> ClientRuntime.PaginatorSequence<DescribeUserGroupsInput, DescribeUserGroupsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeUserGroupsInput, DescribeUserGroupsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeUserGroups(input:))
    }
}

extension DescribeUserGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeUserGroupsInput {
        return DescribeUserGroupsInput(
            marker: token,
            maxRecords: self.maxRecords,
            userGroupId: self.userGroupId
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeUserGroupsInput, OperationStackOutput == DescribeUserGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeUserGroupsPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.UserGroup]`
    /// - Returns: `[ElastiCacheClientTypes.UserGroup]`
    public func userGroups() async throws -> [ElastiCacheClientTypes.UserGroup] {
        return try await self.asyncCompactMap { item in item.userGroups }
    }
}
extension ElastiCacheClient {
    /// Paginate over `[DescribeUsersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeUsersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeUsersOutput`
    public func describeUsersPaginated(input: DescribeUsersInput) -> ClientRuntime.PaginatorSequence<DescribeUsersInput, DescribeUsersOutput> {
        return ClientRuntime.PaginatorSequence<DescribeUsersInput, DescribeUsersOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeUsers(input:))
    }
}

extension DescribeUsersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeUsersInput {
        return DescribeUsersInput(
            engine: self.engine,
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            userId: self.userId
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeUsersInput, OperationStackOutput == DescribeUsersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeUsersPaginated`
    /// to access the nested member `[ElastiCacheClientTypes.User]`
    /// - Returns: `[ElastiCacheClientTypes.User]`
    public func users() async throws -> [ElastiCacheClientTypes.User] {
        return try await self.asyncCompactMap { item in item.users }
    }
}
