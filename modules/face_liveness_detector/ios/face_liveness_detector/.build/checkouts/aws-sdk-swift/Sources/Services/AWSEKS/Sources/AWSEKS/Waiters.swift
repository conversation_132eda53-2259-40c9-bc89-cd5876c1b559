//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import class SmithyWaitersAPI.Waiter
import enum SmithyWaitersAPI.JMESUtils
import protocol ClientRuntime.ServiceError
import struct SmithyWaitersAPI.WaiterConfiguration
import struct SmithyWaitersAPI.WaiterOptions
import struct SmithyWaitersAPI.WaiterOutcome

extension EKSClient {

    static func addonActiveWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeAddonInput, DescribeAddonOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeAddonInput, DescribeAddonOutput>.Acceptor] = [
            .init(state: .failure, matcher: { (input: DescribeAddonInput, result: Swift.Result<DescribeAddonOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "addon.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "CREATE_FAILED"
                guard case .success(let output) = result else { return false }
                let addon = output.addon
                let status = addon?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "CREATE_FAILED")
            }),
            .init(state: .failure, matcher: { (input: DescribeAddonInput, result: Swift.Result<DescribeAddonOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "addon.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "DEGRADED"
                guard case .success(let output) = result else { return false }
                let addon = output.addon
                let status = addon?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "DEGRADED")
            }),
            .init(state: .success, matcher: { (input: DescribeAddonInput, result: Swift.Result<DescribeAddonOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "addon.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "ACTIVE"
                guard case .success(let output) = result else { return false }
                let addon = output.addon
                let status = addon?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "ACTIVE")
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeAddonInput, DescribeAddonOutput>(acceptors: acceptors, minDelay: 10.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the AddonActive event on the describeAddon operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeAddonInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilAddonActive(options: SmithyWaitersAPI.WaiterOptions, input: DescribeAddonInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeAddonOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.addonActiveWaiterConfig(), operation: self.describeAddon(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func addonDeletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeAddonInput, DescribeAddonOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeAddonInput, DescribeAddonOutput>.Acceptor] = [
            .init(state: .failure, matcher: { (input: DescribeAddonInput, result: Swift.Result<DescribeAddonOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "addon.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "DELETE_FAILED"
                guard case .success(let output) = result else { return false }
                let addon = output.addon
                let status = addon?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "DELETE_FAILED")
            }),
            .init(state: .success, matcher: { (input: DescribeAddonInput, result: Swift.Result<DescribeAddonOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "ResourceNotFoundException"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeAddonInput, DescribeAddonOutput>(acceptors: acceptors, minDelay: 10.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the AddonDeleted event on the describeAddon operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeAddonInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilAddonDeleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeAddonInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeAddonOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.addonDeletedWaiterConfig(), operation: self.describeAddon(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func clusterActiveWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeClusterInput, DescribeClusterOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeClusterInput, DescribeClusterOutput>.Acceptor] = [
            .init(state: .failure, matcher: { (input: DescribeClusterInput, result: Swift.Result<DescribeClusterOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "cluster.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "DELETING"
                guard case .success(let output) = result else { return false }
                let cluster = output.cluster
                let status = cluster?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "DELETING")
            }),
            .init(state: .failure, matcher: { (input: DescribeClusterInput, result: Swift.Result<DescribeClusterOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "cluster.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "FAILED"
                guard case .success(let output) = result else { return false }
                let cluster = output.cluster
                let status = cluster?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "FAILED")
            }),
            .init(state: .success, matcher: { (input: DescribeClusterInput, result: Swift.Result<DescribeClusterOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "cluster.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "ACTIVE"
                guard case .success(let output) = result else { return false }
                let cluster = output.cluster
                let status = cluster?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "ACTIVE")
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeClusterInput, DescribeClusterOutput>(acceptors: acceptors, minDelay: 30.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ClusterActive event on the describeCluster operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeClusterInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilClusterActive(options: SmithyWaitersAPI.WaiterOptions, input: DescribeClusterInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeClusterOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.clusterActiveWaiterConfig(), operation: self.describeCluster(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func clusterDeletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeClusterInput, DescribeClusterOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeClusterInput, DescribeClusterOutput>.Acceptor] = [
            .init(state: .failure, matcher: { (input: DescribeClusterInput, result: Swift.Result<DescribeClusterOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "cluster.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "ACTIVE"
                guard case .success(let output) = result else { return false }
                let cluster = output.cluster
                let status = cluster?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "ACTIVE")
            }),
            .init(state: .failure, matcher: { (input: DescribeClusterInput, result: Swift.Result<DescribeClusterOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "cluster.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "CREATING"
                guard case .success(let output) = result else { return false }
                let cluster = output.cluster
                let status = cluster?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "CREATING")
            }),
            .init(state: .failure, matcher: { (input: DescribeClusterInput, result: Swift.Result<DescribeClusterOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "cluster.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "PENDING"
                guard case .success(let output) = result else { return false }
                let cluster = output.cluster
                let status = cluster?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "PENDING")
            }),
            .init(state: .success, matcher: { (input: DescribeClusterInput, result: Swift.Result<DescribeClusterOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "ResourceNotFoundException"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeClusterInput, DescribeClusterOutput>(acceptors: acceptors, minDelay: 30.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ClusterDeleted event on the describeCluster operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeClusterInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilClusterDeleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeClusterInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeClusterOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.clusterDeletedWaiterConfig(), operation: self.describeCluster(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func fargateProfileActiveWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeFargateProfileInput, DescribeFargateProfileOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeFargateProfileInput, DescribeFargateProfileOutput>.Acceptor] = [
            .init(state: .failure, matcher: { (input: DescribeFargateProfileInput, result: Swift.Result<DescribeFargateProfileOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "fargateProfile.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "CREATE_FAILED"
                guard case .success(let output) = result else { return false }
                let fargateProfile = output.fargateProfile
                let status = fargateProfile?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "CREATE_FAILED")
            }),
            .init(state: .success, matcher: { (input: DescribeFargateProfileInput, result: Swift.Result<DescribeFargateProfileOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "fargateProfile.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "ACTIVE"
                guard case .success(let output) = result else { return false }
                let fargateProfile = output.fargateProfile
                let status = fargateProfile?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "ACTIVE")
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeFargateProfileInput, DescribeFargateProfileOutput>(acceptors: acceptors, minDelay: 10.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the FargateProfileActive event on the describeFargateProfile operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeFargateProfileInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilFargateProfileActive(options: SmithyWaitersAPI.WaiterOptions, input: DescribeFargateProfileInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeFargateProfileOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.fargateProfileActiveWaiterConfig(), operation: self.describeFargateProfile(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func fargateProfileDeletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeFargateProfileInput, DescribeFargateProfileOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeFargateProfileInput, DescribeFargateProfileOutput>.Acceptor] = [
            .init(state: .failure, matcher: { (input: DescribeFargateProfileInput, result: Swift.Result<DescribeFargateProfileOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "fargateProfile.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "DELETE_FAILED"
                guard case .success(let output) = result else { return false }
                let fargateProfile = output.fargateProfile
                let status = fargateProfile?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "DELETE_FAILED")
            }),
            .init(state: .success, matcher: { (input: DescribeFargateProfileInput, result: Swift.Result<DescribeFargateProfileOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "ResourceNotFoundException"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeFargateProfileInput, DescribeFargateProfileOutput>(acceptors: acceptors, minDelay: 30.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the FargateProfileDeleted event on the describeFargateProfile operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeFargateProfileInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilFargateProfileDeleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeFargateProfileInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeFargateProfileOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.fargateProfileDeletedWaiterConfig(), operation: self.describeFargateProfile(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func nodegroupActiveWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeNodegroupInput, DescribeNodegroupOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeNodegroupInput, DescribeNodegroupOutput>.Acceptor] = [
            .init(state: .failure, matcher: { (input: DescribeNodegroupInput, result: Swift.Result<DescribeNodegroupOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "nodegroup.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "CREATE_FAILED"
                guard case .success(let output) = result else { return false }
                let nodegroup = output.nodegroup
                let status = nodegroup?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "CREATE_FAILED")
            }),
            .init(state: .success, matcher: { (input: DescribeNodegroupInput, result: Swift.Result<DescribeNodegroupOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "nodegroup.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "ACTIVE"
                guard case .success(let output) = result else { return false }
                let nodegroup = output.nodegroup
                let status = nodegroup?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "ACTIVE")
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeNodegroupInput, DescribeNodegroupOutput>(acceptors: acceptors, minDelay: 30.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the NodegroupActive event on the describeNodegroup operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeNodegroupInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilNodegroupActive(options: SmithyWaitersAPI.WaiterOptions, input: DescribeNodegroupInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeNodegroupOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.nodegroupActiveWaiterConfig(), operation: self.describeNodegroup(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func nodegroupDeletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeNodegroupInput, DescribeNodegroupOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeNodegroupInput, DescribeNodegroupOutput>.Acceptor] = [
            .init(state: .failure, matcher: { (input: DescribeNodegroupInput, result: Swift.Result<DescribeNodegroupOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "nodegroup.status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "DELETE_FAILED"
                guard case .success(let output) = result else { return false }
                let nodegroup = output.nodegroup
                let status = nodegroup?.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "DELETE_FAILED")
            }),
            .init(state: .success, matcher: { (input: DescribeNodegroupInput, result: Swift.Result<DescribeNodegroupOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "ResourceNotFoundException"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeNodegroupInput, DescribeNodegroupOutput>(acceptors: acceptors, minDelay: 30.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the NodegroupDeleted event on the describeNodegroup operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeNodegroupInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilNodegroupDeleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeNodegroupInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeNodegroupOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.nodegroupDeletedWaiterConfig(), operation: self.describeNodegroup(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }
}
