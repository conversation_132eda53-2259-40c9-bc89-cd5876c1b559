//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension EFSClient {
    /// Paginate over `[DescribeAccessPointsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeAccessPointsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeAccessPointsOutput`
    public func describeAccessPointsPaginated(input: DescribeAccessPointsInput) -> ClientRuntime.PaginatorSequence<DescribeAccessPointsInput, DescribeAccessPointsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeAccessPointsInput, DescribeAccessPointsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeAccessPoints(input:))
    }
}

extension DescribeAccessPointsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeAccessPointsInput {
        return DescribeAccessPointsInput(
            accessPointId: self.accessPointId,
            fileSystemId: self.fileSystemId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeAccessPointsInput, OperationStackOutput == DescribeAccessPointsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeAccessPointsPaginated`
    /// to access the nested member `[EFSClientTypes.AccessPointDescription]`
    /// - Returns: `[EFSClientTypes.AccessPointDescription]`
    public func accessPoints() async throws -> [EFSClientTypes.AccessPointDescription] {
        return try await self.asyncCompactMap { item in item.accessPoints }
    }
}
extension EFSClient {
    /// Paginate over `[DescribeFileSystemsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeFileSystemsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeFileSystemsOutput`
    public func describeFileSystemsPaginated(input: DescribeFileSystemsInput) -> ClientRuntime.PaginatorSequence<DescribeFileSystemsInput, DescribeFileSystemsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeFileSystemsInput, DescribeFileSystemsOutput>(input: input, inputKey: \.marker, outputKey: \.nextMarker, paginationFunction: self.describeFileSystems(input:))
    }
}

extension DescribeFileSystemsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeFileSystemsInput {
        return DescribeFileSystemsInput(
            creationToken: self.creationToken,
            fileSystemId: self.fileSystemId,
            marker: token,
            maxItems: self.maxItems
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeFileSystemsInput, OperationStackOutput == DescribeFileSystemsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeFileSystemsPaginated`
    /// to access the nested member `[EFSClientTypes.FileSystemDescription]`
    /// - Returns: `[EFSClientTypes.FileSystemDescription]`
    public func fileSystems() async throws -> [EFSClientTypes.FileSystemDescription] {
        return try await self.asyncCompactMap { item in item.fileSystems }
    }
}
extension EFSClient {
    /// Paginate over `[DescribeMountTargetsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeMountTargetsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeMountTargetsOutput`
    public func describeMountTargetsPaginated(input: DescribeMountTargetsInput) -> ClientRuntime.PaginatorSequence<DescribeMountTargetsInput, DescribeMountTargetsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeMountTargetsInput, DescribeMountTargetsOutput>(input: input, inputKey: \.marker, outputKey: \.nextMarker, paginationFunction: self.describeMountTargets(input:))
    }
}

extension DescribeMountTargetsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeMountTargetsInput {
        return DescribeMountTargetsInput(
            accessPointId: self.accessPointId,
            fileSystemId: self.fileSystemId,
            marker: token,
            maxItems: self.maxItems,
            mountTargetId: self.mountTargetId
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeMountTargetsInput, OperationStackOutput == DescribeMountTargetsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeMountTargetsPaginated`
    /// to access the nested member `[EFSClientTypes.MountTargetDescription]`
    /// - Returns: `[EFSClientTypes.MountTargetDescription]`
    public func mountTargets() async throws -> [EFSClientTypes.MountTargetDescription] {
        return try await self.asyncCompactMap { item in item.mountTargets }
    }
}
extension EFSClient {
    /// Paginate over `[DescribeReplicationConfigurationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReplicationConfigurationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReplicationConfigurationsOutput`
    public func describeReplicationConfigurationsPaginated(input: DescribeReplicationConfigurationsInput) -> ClientRuntime.PaginatorSequence<DescribeReplicationConfigurationsInput, DescribeReplicationConfigurationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReplicationConfigurationsInput, DescribeReplicationConfigurationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeReplicationConfigurations(input:))
    }
}

extension DescribeReplicationConfigurationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReplicationConfigurationsInput {
        return DescribeReplicationConfigurationsInput(
            fileSystemId: self.fileSystemId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeReplicationConfigurationsInput, OperationStackOutput == DescribeReplicationConfigurationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeReplicationConfigurationsPaginated`
    /// to access the nested member `[EFSClientTypes.ReplicationConfigurationDescription]`
    /// - Returns: `[EFSClientTypes.ReplicationConfigurationDescription]`
    public func replications() async throws -> [EFSClientTypes.ReplicationConfigurationDescription] {
        return try await self.asyncCompactMap { item in item.replications }
    }
}
extension EFSClient {
    /// Paginate over `[DescribeTagsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTagsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTagsOutput`
    public func describeTagsPaginated(input: DescribeTagsInput) -> ClientRuntime.PaginatorSequence<DescribeTagsInput, DescribeTagsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTagsInput, DescribeTagsOutput>(input: input, inputKey: \.marker, outputKey: \.nextMarker, paginationFunction: self.describeTags(input:))
    }
}

extension DescribeTagsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTagsInput {
        return DescribeTagsInput(
            fileSystemId: self.fileSystemId,
            marker: token,
            maxItems: self.maxItems
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTagsInput, OperationStackOutput == DescribeTagsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTagsPaginated`
    /// to access the nested member `[EFSClientTypes.Tag]`
    /// - Returns: `[EFSClientTypes.Tag]`
    public func tags() async throws -> [EFSClientTypes.Tag] {
        return try await self.asyncCompactMap { item in item.tags }
    }
}
extension EFSClient {
    /// Paginate over `[ListTagsForResourceOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListTagsForResourceInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListTagsForResourceOutput`
    public func listTagsForResourcePaginated(input: ListTagsForResourceInput) -> ClientRuntime.PaginatorSequence<ListTagsForResourceInput, ListTagsForResourceOutput> {
        return ClientRuntime.PaginatorSequence<ListTagsForResourceInput, ListTagsForResourceOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listTagsForResource(input:))
    }
}

extension ListTagsForResourceInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListTagsForResourceInput {
        return ListTagsForResourceInput(
            maxResults: self.maxResults,
            nextToken: token,
            resourceId: self.resourceId
        )}
}
