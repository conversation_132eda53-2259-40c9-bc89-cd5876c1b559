//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import class Smithy<PERSON><PERSON><PERSON>I.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem

extension DirectoryServiceDataClientTypes {

    public enum AccessDeniedReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case dataDisabled
        case directoryAuth
        case iamAuth
        case sdkUnknown(Swift.String)

        public static var allCases: [AccessDeniedReason] {
            return [
                .dataDisabled,
                .directoryAuth,
                .iamAuth
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .dataDisabled: return "DATA_DISABLED"
            case .directoryAuth: return "DIRECTORY_AUTH"
            case .iamAuth: return "IAM_AUTH"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// You don't have permission to perform the request or access the directory. It can also occur when the DirectoryId doesn't exist or the user, member, or group might be outside of your organizational unit (OU). Make sure that you have the authentication and authorization to perform the action. Review the directory information in the request, and make sure that the object isn't outside of your OU.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
        /// Reason the request was unauthorized.
        public internal(set) var reason: DirectoryServiceDataClientTypes.AccessDeniedReason? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        reason: DirectoryServiceDataClientTypes.AccessDeniedReason? = nil
    )
    {
        self.properties.message = message
        self.properties.reason = reason
    }
}

/// This error will occur when you try to create a resource that conflicts with an existing object. It can also occur when adding a member to a group that the member is already in. This error can be caused by a request sent within the 8-hour idempotency window with the same client token but different input parameters. Client tokens should not be re-used across different requests. After 8 hours, any request with the same client token is treated as a new request.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension DirectoryServiceDataClientTypes {

    public enum DirectoryUnavailableReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case directoryResourcesExceeded
        case directoryTimeout
        case invalidDirectoryState
        case noDiskSpace
        case trustAuthFailure
        case sdkUnknown(Swift.String)

        public static var allCases: [DirectoryUnavailableReason] {
            return [
                .directoryResourcesExceeded,
                .directoryTimeout,
                .invalidDirectoryState,
                .noDiskSpace,
                .trustAuthFailure
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .directoryResourcesExceeded: return "DIRECTORY_RESOURCES_EXCEEDED"
            case .directoryTimeout: return "DIRECTORY_TIMEOUT"
            case .invalidDirectoryState: return "INVALID_DIRECTORY_STATE"
            case .noDiskSpace: return "NO_DISK_SPACE"
            case .trustAuthFailure: return "TRUST_AUTH_FAILURE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The request could not be completed due to a problem in the configuration or current state of the specified directory.
public struct DirectoryUnavailableException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
        /// Reason the request failed for the specified directory.
        public internal(set) var reason: DirectoryServiceDataClientTypes.DirectoryUnavailableReason? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DirectoryUnavailableException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        reason: DirectoryServiceDataClientTypes.DirectoryUnavailableReason? = nil
    )
    {
        self.properties.message = message
        self.properties.reason = reason
    }
}

/// The operation didn't succeed because an internal error occurred. Try again later.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The resource couldn't be found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The limit on the number of requests per second has been exceeded.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The recommended amount of seconds to retry after a throttling exception.
        public internal(set) var retryAfterSeconds: Swift.Int? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { true }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        retryAfterSeconds: Swift.Int? = nil
    )
    {
        self.properties.message = message
        self.properties.retryAfterSeconds = retryAfterSeconds
    }
}

extension DirectoryServiceDataClientTypes {

    public enum ValidationExceptionReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case attributeExists
        case duplicateAttribute
        case invalidAttributeForGroup
        case invalidAttributeForModify
        case invalidAttributeForSearch
        case invalidAttributeForUser
        case invalidAttributeName
        case invalidAttributeValue
        case invalidDirectoryType
        case invalidNextToken
        case invalidRealm
        case invalidSecondaryRegion
        case ldapSizeLimitExceeded
        case ldapUnsupportedOperation
        case missingAttribute
        case sdkUnknown(Swift.String)

        public static var allCases: [ValidationExceptionReason] {
            return [
                .attributeExists,
                .duplicateAttribute,
                .invalidAttributeForGroup,
                .invalidAttributeForModify,
                .invalidAttributeForSearch,
                .invalidAttributeForUser,
                .invalidAttributeName,
                .invalidAttributeValue,
                .invalidDirectoryType,
                .invalidNextToken,
                .invalidRealm,
                .invalidSecondaryRegion,
                .ldapSizeLimitExceeded,
                .ldapUnsupportedOperation,
                .missingAttribute
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .attributeExists: return "ATTRIBUTE_EXISTS"
            case .duplicateAttribute: return "DUPLICATE_ATTRIBUTE"
            case .invalidAttributeForGroup: return "INVALID_ATTRIBUTE_FOR_GROUP"
            case .invalidAttributeForModify: return "INVALID_ATTRIBUTE_FOR_MODIFY"
            case .invalidAttributeForSearch: return "INVALID_ATTRIBUTE_FOR_SEARCH"
            case .invalidAttributeForUser: return "INVALID_ATTRIBUTE_FOR_USER"
            case .invalidAttributeName: return "INVALID_ATTRIBUTE_NAME"
            case .invalidAttributeValue: return "INVALID_ATTRIBUTE_VALUE"
            case .invalidDirectoryType: return "INVALID_DIRECTORY_TYPE"
            case .invalidNextToken: return "INVALID_NEXT_TOKEN"
            case .invalidRealm: return "INVALID_REALM"
            case .invalidSecondaryRegion: return "INVALID_SECONDARY_REGION"
            case .ldapSizeLimitExceeded: return "LDAP_SIZE_LIMIT_EXCEEDED"
            case .ldapUnsupportedOperation: return "LDAP_UNSUPPORTED_OPERATION"
            case .missingAttribute: return "MISSING_ATTRIBUTE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The request isn't valid. Review the details in the error message to update the invalid parameters or values in your request.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
        /// Reason the request failed validation.
        public internal(set) var reason: DirectoryServiceDataClientTypes.ValidationExceptionReason? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        reason: DirectoryServiceDataClientTypes.ValidationExceptionReason? = nil
    )
    {
        self.properties.message = message
        self.properties.reason = reason
    }
}

public struct AddGroupMemberInput: Swift.Sendable {
    /// A unique and case-sensitive identifier that you provide to make sure the idempotency of the request, so multiple identical calls have the same effect as one single call. A client token is valid for 8 hours after the first request that uses it completes. After 8 hours, any request with the same client token is treated as a new request. If the request succeeds, any future uses of that token will be idempotent for another 8 hours. If you submit a request with the same client token but change one of the other parameters within the 8-hour idempotency window, Directory Service Data returns an ConflictException. This parameter is optional when using the CLI or SDK.
    public var clientToken: Swift.String?
    /// The identifier (ID) of the directory that's associated with the group.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The name of the group.
    /// This member is required.
    public var groupName: Swift.String?
    /// The SAMAccountName of the user, group, or computer to add as a group member.
    /// This member is required.
    public var memberName: Swift.String?
    /// The domain name that's associated with the group member. This parameter is required only when adding a member outside of your Managed Microsoft AD domain to a group inside of your Managed Microsoft AD domain. This parameter defaults to the Managed Microsoft AD domain. This parameter is case insensitive.
    public var memberRealm: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        directoryId: Swift.String? = nil,
        groupName: Swift.String? = nil,
        memberName: Swift.String? = nil,
        memberRealm: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.directoryId = directoryId
        self.groupName = groupName
        self.memberName = memberName
        self.memberRealm = memberRealm
    }
}

public struct AddGroupMemberOutput: Swift.Sendable {

    public init() { }
}

extension DirectoryServiceDataClientTypes {

    /// The data type for an attribute. Each attribute value is described as a name-value pair. The name is the AD schema name, and the value is the data itself. For a list of supported attributes, see [Directory Service Data Attributes](https://docs.aws.amazon.com/directoryservice/latest/admin-guide/ad_data_attributes.html).
    public enum AttributeValue: Swift.Sendable {
        /// Indicates that the attribute type value is a string. For example: "S": "S Group"
        case s(Swift.String)
        /// Indicates that the attribute type value is a number. For example: "N": "16"
        case n(Swift.Int)
        /// Indicates that the attribute type value is a boolean. For example: "BOOL": true
        case bool(Swift.Bool)
        /// Indicates that the attribute type value is a string set. For example: "SS": ["sample_service_class/host.sample.com:1234/sample_service_name_1", "sample_service_class/host.sample.com:1234/sample_service_name_2"]
        case ss([Swift.String])
        case sdkUnknown(Swift.String)
    }
}

extension DirectoryServiceDataClientTypes {

    public enum GroupScope: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case builtinLocal
        case domainLocal
        case global
        case universal
        case sdkUnknown(Swift.String)

        public static var allCases: [GroupScope] {
            return [
                .builtinLocal,
                .domainLocal,
                .global,
                .universal
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .builtinLocal: return "BuiltinLocal"
            case .domainLocal: return "DomainLocal"
            case .global: return "Global"
            case .universal: return "Universal"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryServiceDataClientTypes {

    public enum GroupType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case distribution
        case security
        case sdkUnknown(Swift.String)

        public static var allCases: [GroupType] {
            return [
                .distribution,
                .security
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .distribution: return "Distribution"
            case .security: return "Security"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct CreateGroupInput: Swift.Sendable {
    /// A unique and case-sensitive identifier that you provide to make sure the idempotency of the request, so multiple identical calls have the same effect as one single call. A client token is valid for 8 hours after the first request that uses it completes. After 8 hours, any request with the same client token is treated as a new request. If the request succeeds, any future uses of that token will be idempotent for another 8 hours. If you submit a request with the same client token but change one of the other parameters within the 8-hour idempotency window, Directory Service Data returns an ConflictException. This parameter is optional when using the CLI or SDK.
    public var clientToken: Swift.String?
    /// The identifier (ID) of the directory that's associated with the group.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The scope of the AD group. For details, see [Active Directory security group scope](https://learn.microsoft.com/en-us/windows-server/identity/ad-ds/manage/understand-security-groups#group-scope).
    public var groupScope: DirectoryServiceDataClientTypes.GroupScope?
    /// The AD group type. For details, see [Active Directory security group type](https://learn.microsoft.com/en-us/windows-server/identity/ad-ds/manage/understand-security-groups#how-active-directory-security-groups-work).
    public var groupType: DirectoryServiceDataClientTypes.GroupType?
    /// An expression that defines one or more attributes with the data type and value of each attribute.
    public var otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]?
    /// The name of the group.
    /// This member is required.
    public var samAccountName: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        directoryId: Swift.String? = nil,
        groupScope: DirectoryServiceDataClientTypes.GroupScope? = nil,
        groupType: DirectoryServiceDataClientTypes.GroupType? = nil,
        otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]? = nil,
        samAccountName: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.directoryId = directoryId
        self.groupScope = groupScope
        self.groupType = groupType
        self.otherAttributes = otherAttributes
        self.samAccountName = samAccountName
    }
}

public struct CreateGroupOutput: Swift.Sendable {
    /// The identifier (ID) of the directory that's associated with the group.
    public var directoryId: Swift.String?
    /// The name of the group.
    public var samAccountName: Swift.String?
    /// The unique security identifier (SID) of the group.
    public var sid: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        samAccountName: Swift.String? = nil,
        sid: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.samAccountName = samAccountName
        self.sid = sid
    }
}

public struct CreateUserInput: Swift.Sendable {
    /// A unique and case-sensitive identifier that you provide to make sure the idempotency of the request, so multiple identical calls have the same effect as one single call. A client token is valid for 8 hours after the first request that uses it completes. After 8 hours, any request with the same client token is treated as a new request. If the request succeeds, any future uses of that token will be idempotent for another 8 hours. If you submit a request with the same client token but change one of the other parameters within the 8-hour idempotency window, Directory Service Data returns an ConflictException. This parameter is optional when using the CLI or SDK.
    public var clientToken: Swift.String?
    /// The identifier (ID) of the directory that’s associated with the user.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The email address of the user.
    public var emailAddress: Swift.String?
    /// The first name of the user.
    public var givenName: Swift.String?
    /// An expression that defines one or more attribute names with the data type and value of each attribute. A key is an attribute name, and the value is a list of maps. For a list of supported attributes, see [Directory Service Data Attributes](https://docs.aws.amazon.com/directoryservice/latest/admin-guide/ad_data_attributes.html). Attribute names are case insensitive.
    public var otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]?
    /// The name of the user.
    /// This member is required.
    public var samAccountName: Swift.String?
    /// The last name of the user.
    public var surname: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        directoryId: Swift.String? = nil,
        emailAddress: Swift.String? = nil,
        givenName: Swift.String? = nil,
        otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]? = nil,
        samAccountName: Swift.String? = nil,
        surname: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.directoryId = directoryId
        self.emailAddress = emailAddress
        self.givenName = givenName
        self.otherAttributes = otherAttributes
        self.samAccountName = samAccountName
        self.surname = surname
    }
}

extension CreateUserInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateUserInput(clientToken: \(Swift.String(describing: clientToken)), directoryId: \(Swift.String(describing: directoryId)), otherAttributes: \(Swift.String(describing: otherAttributes)), samAccountName: \(Swift.String(describing: samAccountName)), emailAddress: \"CONTENT_REDACTED\", givenName: \"CONTENT_REDACTED\", surname: \"CONTENT_REDACTED\")"}
}

public struct CreateUserOutput: Swift.Sendable {
    /// The identifier (ID) of the directory where the address block is added.
    public var directoryId: Swift.String?
    /// The name of the user.
    public var samAccountName: Swift.String?
    /// The unique security identifier (SID) of the user.
    public var sid: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        samAccountName: Swift.String? = nil,
        sid: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.samAccountName = samAccountName
        self.sid = sid
    }
}

public struct DeleteGroupInput: Swift.Sendable {
    /// A unique and case-sensitive identifier that you provide to make sure the idempotency of the request, so multiple identical calls have the same effect as one single call. A client token is valid for 8 hours after the first request that uses it completes. After 8 hours, any request with the same client token is treated as a new request. If the request succeeds, any future uses of that token will be idempotent for another 8 hours. If you submit a request with the same client token but change one of the other parameters within the 8-hour idempotency window, Directory Service Data returns an ConflictException. This parameter is optional when using the CLI or SDK.
    public var clientToken: Swift.String?
    /// The identifier (ID) of the directory that's associated with the group.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The name of the group.
    /// This member is required.
    public var samAccountName: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        directoryId: Swift.String? = nil,
        samAccountName: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.directoryId = directoryId
        self.samAccountName = samAccountName
    }
}

public struct DeleteGroupOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteUserInput: Swift.Sendable {
    /// A unique and case-sensitive identifier that you provide to make sure the idempotency of the request, so multiple identical calls have the same effect as one single call. A client token is valid for 8 hours after the first request that uses it completes. After 8 hours, any request with the same client token is treated as a new request. If the request succeeds, any future uses of that token will be idempotent for another 8 hours. If you submit a request with the same client token but change one of the other parameters within the 8-hour idempotency window, Directory Service Data returns an ConflictException. This parameter is optional when using the CLI or SDK.
    public var clientToken: Swift.String?
    /// The identifier (ID) of the directory that's associated with the user.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The name of the user.
    /// This member is required.
    public var samAccountName: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        directoryId: Swift.String? = nil,
        samAccountName: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.directoryId = directoryId
        self.samAccountName = samAccountName
    }
}

public struct DeleteUserOutput: Swift.Sendable {

    public init() { }
}

public struct DescribeGroupInput: Swift.Sendable {
    /// The Identifier (ID) of the directory associated with the group.
    /// This member is required.
    public var directoryId: Swift.String?
    /// One or more attributes to be returned for the group. For a list of supported attributes, see [Directory Service Data Attributes](https://docs.aws.amazon.com/directoryservice/latest/admin-guide/ad_data-attributes.html).
    public var otherAttributes: [Swift.String]?
    /// The domain name that's associated with the group. This parameter is optional, so you can return groups outside of your Managed Microsoft AD domain. When no value is defined, only your Managed Microsoft AD groups are returned. This value is case insensitive.
    public var realm: Swift.String?
    /// The name of the group.
    /// This member is required.
    public var samAccountName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        otherAttributes: [Swift.String]? = nil,
        realm: Swift.String? = nil,
        samAccountName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.otherAttributes = otherAttributes
        self.realm = realm
        self.samAccountName = samAccountName
    }
}

public struct DescribeGroupOutput: Swift.Sendable {
    /// The identifier (ID) of the directory that's associated with the group.
    public var directoryId: Swift.String?
    /// The [distinguished name](https://learn.microsoft.com/en-us/windows/win32/ad/object-names-and-identities#distinguished-name) of the object.
    public var distinguishedName: Swift.String?
    /// The scope of the AD group. For details, see [Active Directory security groups](https://learn.microsoft.com/en-us/windows-server/identity/ad-ds/manage/understand-security-groups#group-scope).
    public var groupScope: DirectoryServiceDataClientTypes.GroupScope?
    /// The AD group type. For details, see [Active Directory security group type](https://learn.microsoft.com/en-us/windows-server/identity/ad-ds/manage/understand-security-groups#how-active-directory-security-groups-work).
    public var groupType: DirectoryServiceDataClientTypes.GroupType?
    /// The attribute values that are returned for the attribute names that are included in the request.
    public var otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]?
    /// The domain name that's associated with the group.
    public var realm: Swift.String?
    /// The name of the group.
    public var samAccountName: Swift.String?
    /// The unique security identifier (SID) of the group.
    public var sid: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        distinguishedName: Swift.String? = nil,
        groupScope: DirectoryServiceDataClientTypes.GroupScope? = nil,
        groupType: DirectoryServiceDataClientTypes.GroupType? = nil,
        otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]? = nil,
        realm: Swift.String? = nil,
        samAccountName: Swift.String? = nil,
        sid: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.distinguishedName = distinguishedName
        self.groupScope = groupScope
        self.groupType = groupType
        self.otherAttributes = otherAttributes
        self.realm = realm
        self.samAccountName = samAccountName
        self.sid = sid
    }
}

extension DescribeGroupOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "DescribeGroupOutput(directoryId: \(Swift.String(describing: directoryId)), groupScope: \(Swift.String(describing: groupScope)), groupType: \(Swift.String(describing: groupType)), otherAttributes: \(Swift.String(describing: otherAttributes)), realm: \(Swift.String(describing: realm)), samAccountName: \(Swift.String(describing: samAccountName)), sid: \(Swift.String(describing: sid)), distinguishedName: \"CONTENT_REDACTED\")"}
}

public struct DescribeUserInput: Swift.Sendable {
    /// The identifier (ID) of the directory that's associated with the user.
    /// This member is required.
    public var directoryId: Swift.String?
    /// One or more attribute names to be returned for the user. A key is an attribute name, and the value is a list of maps. For a list of supported attributes, see [Directory Service Data Attributes](https://docs.aws.amazon.com/directoryservice/latest/admin-guide/ad_data_attributes.html).
    public var otherAttributes: [Swift.String]?
    /// The domain name that's associated with the user. This parameter is optional, so you can return users outside your Managed Microsoft AD domain. When no value is defined, only your Managed Microsoft AD users are returned. This value is case insensitive.
    public var realm: Swift.String?
    /// The name of the user.
    /// This member is required.
    public var samAccountName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        otherAttributes: [Swift.String]? = nil,
        realm: Swift.String? = nil,
        samAccountName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.otherAttributes = otherAttributes
        self.realm = realm
        self.samAccountName = samAccountName
    }
}

public struct DescribeUserOutput: Swift.Sendable {
    /// The identifier (ID) of the directory that's associated with the user.
    public var directoryId: Swift.String?
    /// The [distinguished name](https://learn.microsoft.com/en-us/windows/win32/ad/object-names-and-identities#distinguished-name) of the object.
    public var distinguishedName: Swift.String?
    /// The email address of the user.
    public var emailAddress: Swift.String?
    /// Indicates whether the user account is active.
    public var enabled: Swift.Bool?
    /// The first name of the user.
    public var givenName: Swift.String?
    /// The attribute values that are returned for the attribute names that are included in the request. Attribute names are case insensitive.
    public var otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]?
    /// The domain name that's associated with the user.
    public var realm: Swift.String?
    /// The name of the user.
    public var samAccountName: Swift.String?
    /// The unique security identifier (SID) of the user.
    public var sid: Swift.String?
    /// The last name of the user.
    public var surname: Swift.String?
    /// The UPN that is an Internet-style login name for a user and is based on the Internet standard [RFC 822](https://www.ietf.org/rfc/rfc0822.txt). The UPN is shorter than the distinguished name and easier to remember.
    public var userPrincipalName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        distinguishedName: Swift.String? = nil,
        emailAddress: Swift.String? = nil,
        enabled: Swift.Bool? = nil,
        givenName: Swift.String? = nil,
        otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]? = nil,
        realm: Swift.String? = nil,
        samAccountName: Swift.String? = nil,
        sid: Swift.String? = nil,
        surname: Swift.String? = nil,
        userPrincipalName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.distinguishedName = distinguishedName
        self.emailAddress = emailAddress
        self.enabled = enabled
        self.givenName = givenName
        self.otherAttributes = otherAttributes
        self.realm = realm
        self.samAccountName = samAccountName
        self.sid = sid
        self.surname = surname
        self.userPrincipalName = userPrincipalName
    }
}

extension DescribeUserOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "DescribeUserOutput(directoryId: \(Swift.String(describing: directoryId)), enabled: \(Swift.String(describing: enabled)), otherAttributes: \(Swift.String(describing: otherAttributes)), realm: \(Swift.String(describing: realm)), samAccountName: \(Swift.String(describing: samAccountName)), sid: \(Swift.String(describing: sid)), distinguishedName: \"CONTENT_REDACTED\", emailAddress: \"CONTENT_REDACTED\", givenName: \"CONTENT_REDACTED\", surname: \"CONTENT_REDACTED\", userPrincipalName: \"CONTENT_REDACTED\")"}
}

public struct DisableUserInput: Swift.Sendable {
    /// A unique and case-sensitive identifier that you provide to make sure the idempotency of the request, so multiple identical calls have the same effect as one single call. A client token is valid for 8 hours after the first request that uses it completes. After 8 hours, any request with the same client token is treated as a new request. If the request succeeds, any future uses of that token will be idempotent for another 8 hours. If you submit a request with the same client token but change one of the other parameters within the 8-hour idempotency window, Directory Service Data returns an ConflictException. This parameter is optional when using the CLI or SDK.
    public var clientToken: Swift.String?
    /// The identifier (ID) of the directory that's associated with the user.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The name of the user.
    /// This member is required.
    public var samAccountName: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        directoryId: Swift.String? = nil,
        samAccountName: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.directoryId = directoryId
        self.samAccountName = samAccountName
    }
}

public struct DisableUserOutput: Swift.Sendable {

    public init() { }
}

public struct ListGroupMembersInput: Swift.Sendable {
    /// The identifier (ID) of the directory that's associated with the group.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The maximum number of results to be returned per request.
    public var maxResults: Swift.Int?
    /// The domain name that's associated with the group member. This parameter defaults to the Managed Microsoft AD domain. This parameter is optional and case insensitive.
    public var memberRealm: Swift.String?
    /// An encoded paging token for paginated calls that can be passed back to retrieve the next page.
    public var nextToken: Swift.String?
    /// The domain name that's associated with the group. This parameter is optional, so you can return members from a group outside of your Managed Microsoft AD domain. When no value is defined, only members of your Managed Microsoft AD groups are returned. This value is case insensitive.
    public var realm: Swift.String?
    /// The name of the group.
    /// This member is required.
    public var samAccountName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        memberRealm: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        realm: Swift.String? = nil,
        samAccountName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.maxResults = maxResults
        self.memberRealm = memberRealm
        self.nextToken = nextToken
        self.realm = realm
        self.samAccountName = samAccountName
    }
}

extension ListGroupMembersInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ListGroupMembersInput(directoryId: \(Swift.String(describing: directoryId)), maxResults: \(Swift.String(describing: maxResults)), memberRealm: \(Swift.String(describing: memberRealm)), realm: \(Swift.String(describing: realm)), samAccountName: \(Swift.String(describing: samAccountName)), nextToken: \"CONTENT_REDACTED\")"}
}

extension DirectoryServiceDataClientTypes {

    public enum MemberType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case computer
        case group
        case user
        case sdkUnknown(Swift.String)

        public static var allCases: [MemberType] {
            return [
                .computer,
                .group,
                .user
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .computer: return "COMPUTER"
            case .group: return "GROUP"
            case .user: return "USER"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryServiceDataClientTypes {

    /// A member object that contains identifying information for a specified member.
    public struct Member: Swift.Sendable {
        /// The AD type of the member object.
        /// This member is required.
        public var memberType: DirectoryServiceDataClientTypes.MemberType?
        /// The name of the group member.
        /// This member is required.
        public var samAccountName: Swift.String?
        /// The unique security identifier (SID) of the group member.
        /// This member is required.
        public var sid: Swift.String?

        public init(
            memberType: DirectoryServiceDataClientTypes.MemberType? = nil,
            samAccountName: Swift.String? = nil,
            sid: Swift.String? = nil
        )
        {
            self.memberType = memberType
            self.samAccountName = samAccountName
            self.sid = sid
        }
    }
}

public struct ListGroupMembersOutput: Swift.Sendable {
    /// Identifier (ID) of the directory associated with the group.
    public var directoryId: Swift.String?
    /// The domain name that's associated with the member.
    public var memberRealm: Swift.String?
    /// The member information that the request returns.
    public var members: [DirectoryServiceDataClientTypes.Member]?
    /// An encoded paging token for paginated calls that can be passed back to retrieve the next page.
    public var nextToken: Swift.String?
    /// The domain name that's associated with the group.
    public var realm: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        memberRealm: Swift.String? = nil,
        members: [DirectoryServiceDataClientTypes.Member]? = nil,
        nextToken: Swift.String? = nil,
        realm: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.memberRealm = memberRealm
        self.members = members
        self.nextToken = nextToken
        self.realm = realm
    }
}

extension ListGroupMembersOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ListGroupMembersOutput(directoryId: \(Swift.String(describing: directoryId)), memberRealm: \(Swift.String(describing: memberRealm)), members: \(Swift.String(describing: members)), realm: \(Swift.String(describing: realm)), nextToken: \"CONTENT_REDACTED\")"}
}

public struct ListGroupsInput: Swift.Sendable {
    /// The identifier (ID) of the directory that's associated with the group.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The maximum number of results to be returned per request.
    public var maxResults: Swift.Int?
    /// An encoded paging token for paginated calls that can be passed back to retrieve the next page.
    public var nextToken: Swift.String?
    /// The domain name associated with the directory. This parameter is optional, so you can return groups outside of your Managed Microsoft AD domain. When no value is defined, only your Managed Microsoft AD groups are returned. This value is case insensitive.
    public var realm: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        realm: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.realm = realm
    }
}

extension ListGroupsInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ListGroupsInput(directoryId: \(Swift.String(describing: directoryId)), maxResults: \(Swift.String(describing: maxResults)), realm: \(Swift.String(describing: realm)), nextToken: \"CONTENT_REDACTED\")"}
}

extension DirectoryServiceDataClientTypes {

    /// A structure containing a subset of fields of a group object from a directory.
    public struct GroupSummary: Swift.Sendable {
        /// The scope of the AD group. For details, see [Active Directory security groups](https://learn.microsoft.com/en-us/windows-server/identity/ad-ds/manage/understand-security-groups#group-scope).
        /// This member is required.
        public var groupScope: DirectoryServiceDataClientTypes.GroupScope?
        /// The AD group type. For details, see [Active Directory security group type](https://learn.microsoft.com/en-us/windows-server/identity/ad-ds/manage/understand-security-groups#how-active-directory-security-groups-work).
        /// This member is required.
        public var groupType: DirectoryServiceDataClientTypes.GroupType?
        /// The name of the group.
        /// This member is required.
        public var samAccountName: Swift.String?
        /// The unique security identifier (SID) of the group.
        /// This member is required.
        public var sid: Swift.String?

        public init(
            groupScope: DirectoryServiceDataClientTypes.GroupScope? = nil,
            groupType: DirectoryServiceDataClientTypes.GroupType? = nil,
            samAccountName: Swift.String? = nil,
            sid: Swift.String? = nil
        )
        {
            self.groupScope = groupScope
            self.groupType = groupType
            self.samAccountName = samAccountName
            self.sid = sid
        }
    }
}

public struct ListGroupsOutput: Swift.Sendable {
    /// The identifier (ID) of the directory that's associated with the group.
    public var directoryId: Swift.String?
    /// The group information that the request returns.
    public var groups: [DirectoryServiceDataClientTypes.GroupSummary]?
    /// An encoded paging token for paginated calls that can be passed back to retrieve the next page.
    public var nextToken: Swift.String?
    /// The domain name associated with the group.
    public var realm: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        groups: [DirectoryServiceDataClientTypes.GroupSummary]? = nil,
        nextToken: Swift.String? = nil,
        realm: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.groups = groups
        self.nextToken = nextToken
        self.realm = realm
    }
}

extension ListGroupsOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ListGroupsOutput(directoryId: \(Swift.String(describing: directoryId)), groups: \(Swift.String(describing: groups)), realm: \(Swift.String(describing: realm)), nextToken: \"CONTENT_REDACTED\")"}
}

public struct ListGroupsForMemberInput: Swift.Sendable {
    /// The identifier (ID) of the directory that's associated with the member.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The maximum number of results to be returned per request.
    public var maxResults: Swift.Int?
    /// The domain name that's associated with the group member. This parameter is optional, so you can limit your results to the group members in a specific domain. This parameter is case insensitive and defaults to Realm
    public var memberRealm: Swift.String?
    /// An encoded paging token for paginated calls that can be passed back to retrieve the next page.
    public var nextToken: Swift.String?
    /// The domain name that's associated with the group. This parameter is optional, so you can return groups outside of your Managed Microsoft AD domain. When no value is defined, only your Managed Microsoft AD groups are returned. This value is case insensitive and defaults to your Managed Microsoft AD domain.
    public var realm: Swift.String?
    /// The SAMAccountName of the user, group, or computer that's a member of the group.
    /// This member is required.
    public var samAccountName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        memberRealm: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        realm: Swift.String? = nil,
        samAccountName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.maxResults = maxResults
        self.memberRealm = memberRealm
        self.nextToken = nextToken
        self.realm = realm
        self.samAccountName = samAccountName
    }
}

extension ListGroupsForMemberInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ListGroupsForMemberInput(directoryId: \(Swift.String(describing: directoryId)), maxResults: \(Swift.String(describing: maxResults)), memberRealm: \(Swift.String(describing: memberRealm)), realm: \(Swift.String(describing: realm)), samAccountName: \(Swift.String(describing: samAccountName)), nextToken: \"CONTENT_REDACTED\")"}
}

public struct ListGroupsForMemberOutput: Swift.Sendable {
    /// The identifier (ID) of the directory that's associated with the member.
    public var directoryId: Swift.String?
    /// The group information that the request returns.
    public var groups: [DirectoryServiceDataClientTypes.GroupSummary]?
    /// The domain that's associated with the member.
    public var memberRealm: Swift.String?
    /// An encoded paging token for paginated calls that can be passed back to retrieve the next page.
    public var nextToken: Swift.String?
    /// The domain that's associated with the group.
    public var realm: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        groups: [DirectoryServiceDataClientTypes.GroupSummary]? = nil,
        memberRealm: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        realm: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.groups = groups
        self.memberRealm = memberRealm
        self.nextToken = nextToken
        self.realm = realm
    }
}

extension ListGroupsForMemberOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ListGroupsForMemberOutput(directoryId: \(Swift.String(describing: directoryId)), groups: \(Swift.String(describing: groups)), memberRealm: \(Swift.String(describing: memberRealm)), realm: \(Swift.String(describing: realm)), nextToken: \"CONTENT_REDACTED\")"}
}

public struct ListUsersInput: Swift.Sendable {
    /// The identifier (ID) of the directory that's associated with the user.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The maximum number of results to be returned per request.
    public var maxResults: Swift.Int?
    /// An encoded paging token for paginated calls that can be passed back to retrieve the next page.
    public var nextToken: Swift.String?
    /// The domain name that's associated with the user. This parameter is optional, so you can return users outside of your Managed Microsoft AD domain. When no value is defined, only your Managed Microsoft AD users are returned. This value is case insensitive.
    public var realm: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        realm: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.realm = realm
    }
}

extension ListUsersInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ListUsersInput(directoryId: \(Swift.String(describing: directoryId)), maxResults: \(Swift.String(describing: maxResults)), realm: \(Swift.String(describing: realm)), nextToken: \"CONTENT_REDACTED\")"}
}

extension DirectoryServiceDataClientTypes {

    /// A structure containing a subset of the fields of a user object from a directory.
    public struct UserSummary: Swift.Sendable {
        /// Indicates whether the user account is active.
        /// This member is required.
        public var enabled: Swift.Bool?
        /// The first name of the user.
        public var givenName: Swift.String?
        /// The name of the user.
        /// This member is required.
        public var samAccountName: Swift.String?
        /// The unique security identifier (SID) of the user.
        /// This member is required.
        public var sid: Swift.String?
        /// The last name of the user.
        public var surname: Swift.String?

        public init(
            enabled: Swift.Bool? = nil,
            givenName: Swift.String? = nil,
            samAccountName: Swift.String? = nil,
            sid: Swift.String? = nil,
            surname: Swift.String? = nil
        )
        {
            self.enabled = enabled
            self.givenName = givenName
            self.samAccountName = samAccountName
            self.sid = sid
            self.surname = surname
        }
    }
}

extension DirectoryServiceDataClientTypes.UserSummary: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "UserSummary(enabled: \(Swift.String(describing: enabled)), samAccountName: \(Swift.String(describing: samAccountName)), sid: \(Swift.String(describing: sid)), givenName: \"CONTENT_REDACTED\", surname: \"CONTENT_REDACTED\")"}
}

public struct ListUsersOutput: Swift.Sendable {
    /// The identifier (ID) of the directory that's associated with the user.
    public var directoryId: Swift.String?
    /// An encoded paging token for paginated calls that can be passed back to retrieve the next page.
    public var nextToken: Swift.String?
    /// The domain that's associated with the user.
    public var realm: Swift.String?
    /// The user information that the request returns.
    public var users: [DirectoryServiceDataClientTypes.UserSummary]?

    public init(
        directoryId: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        realm: Swift.String? = nil,
        users: [DirectoryServiceDataClientTypes.UserSummary]? = nil
    )
    {
        self.directoryId = directoryId
        self.nextToken = nextToken
        self.realm = realm
        self.users = users
    }
}

extension ListUsersOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ListUsersOutput(directoryId: \(Swift.String(describing: directoryId)), realm: \(Swift.String(describing: realm)), users: \(Swift.String(describing: users)), nextToken: \"CONTENT_REDACTED\")"}
}

public struct RemoveGroupMemberInput: Swift.Sendable {
    /// A unique and case-sensitive identifier that you provide to make sure the idempotency of the request, so multiple identical calls have the same effect as one single call. A client token is valid for 8 hours after the first request that uses it completes. After 8 hours, any request with the same client token is treated as a new request. If the request succeeds, any future uses of that token will be idempotent for another 8 hours. If you submit a request with the same client token but change one of the other parameters within the 8-hour idempotency window, Directory Service Data returns an ConflictException. This parameter is optional when using the CLI or SDK.
    public var clientToken: Swift.String?
    /// The identifier (ID) of the directory that's associated with the member.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The name of the group.
    /// This member is required.
    public var groupName: Swift.String?
    /// The SAMAccountName of the user, group, or computer to remove from the group.
    /// This member is required.
    public var memberName: Swift.String?
    /// The domain name that's associated with the group member. This parameter defaults to the Managed Microsoft AD domain. This parameter is optional and case insensitive.
    public var memberRealm: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        directoryId: Swift.String? = nil,
        groupName: Swift.String? = nil,
        memberName: Swift.String? = nil,
        memberRealm: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.directoryId = directoryId
        self.groupName = groupName
        self.memberName = memberName
        self.memberRealm = memberRealm
    }
}

public struct RemoveGroupMemberOutput: Swift.Sendable {

    public init() { }
}

public struct SearchGroupsInput: Swift.Sendable {
    /// The identifier (ID) of the directory that's associated with the group.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The maximum number of results to be returned per request.
    public var maxResults: Swift.Int?
    /// An encoded paging token for paginated calls that can be passed back to retrieve the next page.
    public var nextToken: Swift.String?
    /// The domain name that's associated with the group. This parameter is optional, so you can return groups outside of your Managed Microsoft AD domain. When no value is defined, only your Managed Microsoft AD groups are returned. This value is case insensitive.
    public var realm: Swift.String?
    /// One or more data attributes that are used to search for a group. For a list of supported attributes, see [Directory Service Data Attributes](https://docs.aws.amazon.com/directoryservice/latest/admin-guide/ad_data_attributes.html).
    /// This member is required.
    public var searchAttributes: [Swift.String]?
    /// The attribute value that you want to search for. Wildcard (*) searches aren't supported. For a list of supported attributes, see [Directory Service Data Attributes](https://docs.aws.amazon.com/directoryservice/latest/admin-guide/ad_data_attributes.html).
    /// This member is required.
    public var searchString: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        realm: Swift.String? = nil,
        searchAttributes: [Swift.String]? = nil,
        searchString: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.realm = realm
        self.searchAttributes = searchAttributes
        self.searchString = searchString
    }
}

extension SearchGroupsInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "SearchGroupsInput(directoryId: \(Swift.String(describing: directoryId)), maxResults: \(Swift.String(describing: maxResults)), realm: \(Swift.String(describing: realm)), searchAttributes: \(Swift.String(describing: searchAttributes)), nextToken: \"CONTENT_REDACTED\", searchString: \"CONTENT_REDACTED\")"}
}

extension DirectoryServiceDataClientTypes {

    /// A group object that contains identifying information and attributes for a specified group.
    public struct Group: Swift.Sendable {
        /// The [distinguished name](https://learn.microsoft.com/en-us/windows/win32/ad/object-names-and-identities#distinguished-name) of the object.
        public var distinguishedName: Swift.String?
        /// The scope of the AD group. For details, see [Active Directory security groups](https://learn.microsoft.com/en-us/windows-server/identity/ad-ds/manage/understand-security-groups#group-scope)
        public var groupScope: DirectoryServiceDataClientTypes.GroupScope?
        /// The AD group type. For details, see [Active Directory security group type](https://learn.microsoft.com/en-us/windows-server/identity/ad-ds/manage/understand-security-groups#how-active-directory-security-groups-work).
        public var groupType: DirectoryServiceDataClientTypes.GroupType?
        /// An expression of one or more attributes, data types, and the values of a group.
        public var otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]?
        /// The name of the group.
        /// This member is required.
        public var samAccountName: Swift.String?
        /// The unique security identifier (SID) of the group.
        public var sid: Swift.String?

        public init(
            distinguishedName: Swift.String? = nil,
            groupScope: DirectoryServiceDataClientTypes.GroupScope? = nil,
            groupType: DirectoryServiceDataClientTypes.GroupType? = nil,
            otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]? = nil,
            samAccountName: Swift.String? = nil,
            sid: Swift.String? = nil
        )
        {
            self.distinguishedName = distinguishedName
            self.groupScope = groupScope
            self.groupType = groupType
            self.otherAttributes = otherAttributes
            self.samAccountName = samAccountName
            self.sid = sid
        }
    }
}

extension DirectoryServiceDataClientTypes.Group: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "Group(groupScope: \(Swift.String(describing: groupScope)), groupType: \(Swift.String(describing: groupType)), otherAttributes: \(Swift.String(describing: otherAttributes)), samAccountName: \(Swift.String(describing: samAccountName)), sid: \(Swift.String(describing: sid)), distinguishedName: \"CONTENT_REDACTED\")"}
}

public struct SearchGroupsOutput: Swift.Sendable {
    /// The identifier (ID) of the directory that's associated with the group.
    public var directoryId: Swift.String?
    /// The group information that the request returns.
    public var groups: [DirectoryServiceDataClientTypes.Group]?
    /// An encoded paging token for paginated calls that can be passed back to retrieve the next page.
    public var nextToken: Swift.String?
    /// The domain that's associated with the group.
    public var realm: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        groups: [DirectoryServiceDataClientTypes.Group]? = nil,
        nextToken: Swift.String? = nil,
        realm: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.groups = groups
        self.nextToken = nextToken
        self.realm = realm
    }
}

extension SearchGroupsOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "SearchGroupsOutput(directoryId: \(Swift.String(describing: directoryId)), groups: \(Swift.String(describing: groups)), realm: \(Swift.String(describing: realm)), nextToken: \"CONTENT_REDACTED\")"}
}

public struct SearchUsersInput: Swift.Sendable {
    /// The identifier (ID) of the directory that's associated with the user.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The maximum number of results to be returned per request.
    public var maxResults: Swift.Int?
    /// An encoded paging token for paginated calls that can be passed back to retrieve the next page.
    public var nextToken: Swift.String?
    /// The domain name that's associated with the user. This parameter is optional, so you can return users outside of your Managed Microsoft AD domain. When no value is defined, only your Managed Microsoft AD users are returned. This value is case insensitive.
    public var realm: Swift.String?
    /// One or more data attributes that are used to search for a user. For a list of supported attributes, see [Directory Service Data Attributes](https://docs.aws.amazon.com/directoryservice/latest/admin-guide/ad_data_attributes.html).
    /// This member is required.
    public var searchAttributes: [Swift.String]?
    /// The attribute value that you want to search for. Wildcard (*) searches aren't supported. For a list of supported attributes, see [Directory Service Data Attributes](https://docs.aws.amazon.com/directoryservice/latest/admin-guide/ad_data_attributes.html).
    /// This member is required.
    public var searchString: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        realm: Swift.String? = nil,
        searchAttributes: [Swift.String]? = nil,
        searchString: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.realm = realm
        self.searchAttributes = searchAttributes
        self.searchString = searchString
    }
}

extension SearchUsersInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "SearchUsersInput(directoryId: \(Swift.String(describing: directoryId)), maxResults: \(Swift.String(describing: maxResults)), realm: \(Swift.String(describing: realm)), searchAttributes: \(Swift.String(describing: searchAttributes)), nextToken: \"CONTENT_REDACTED\", searchString: \"CONTENT_REDACTED\")"}
}

extension DirectoryServiceDataClientTypes {

    /// A user object that contains identifying information and attributes for a specified user.
    public struct User: Swift.Sendable {
        /// The [distinguished name](https://learn.microsoft.com/en-us/windows/win32/ad/object-names-and-identities#distinguished-name) of the object.
        public var distinguishedName: Swift.String?
        /// The email address of the user.
        public var emailAddress: Swift.String?
        /// Indicates whether the user account is active.
        public var enabled: Swift.Bool?
        /// The first name of the user.
        public var givenName: Swift.String?
        /// An expression that includes one or more attributes, data types, and values of a user.
        public var otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]?
        /// The name of the user.
        /// This member is required.
        public var samAccountName: Swift.String?
        /// The unique security identifier (SID) of the user.
        public var sid: Swift.String?
        /// The last name of the user.
        public var surname: Swift.String?
        /// The UPN that is an internet-style login name for a user and based on the internet standard [RFC 822](https://www.ietf.org/rfc/rfc0822.txt). The UPN is shorter than the distinguished name and easier to remember.
        public var userPrincipalName: Swift.String?

        public init(
            distinguishedName: Swift.String? = nil,
            emailAddress: Swift.String? = nil,
            enabled: Swift.Bool? = nil,
            givenName: Swift.String? = nil,
            otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]? = nil,
            samAccountName: Swift.String? = nil,
            sid: Swift.String? = nil,
            surname: Swift.String? = nil,
            userPrincipalName: Swift.String? = nil
        )
        {
            self.distinguishedName = distinguishedName
            self.emailAddress = emailAddress
            self.enabled = enabled
            self.givenName = givenName
            self.otherAttributes = otherAttributes
            self.samAccountName = samAccountName
            self.sid = sid
            self.surname = surname
            self.userPrincipalName = userPrincipalName
        }
    }
}

extension DirectoryServiceDataClientTypes.User: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "User(enabled: \(Swift.String(describing: enabled)), otherAttributes: \(Swift.String(describing: otherAttributes)), samAccountName: \(Swift.String(describing: samAccountName)), sid: \(Swift.String(describing: sid)), distinguishedName: \"CONTENT_REDACTED\", emailAddress: \"CONTENT_REDACTED\", givenName: \"CONTENT_REDACTED\", surname: \"CONTENT_REDACTED\", userPrincipalName: \"CONTENT_REDACTED\")"}
}

public struct SearchUsersOutput: Swift.Sendable {
    /// The identifier (ID) of the directory where the address block is added.
    public var directoryId: Swift.String?
    /// An encoded paging token for paginated calls that can be passed back to retrieve the next page.
    public var nextToken: Swift.String?
    /// The domain that's associated with the user.
    public var realm: Swift.String?
    /// The user information that the request returns.
    public var users: [DirectoryServiceDataClientTypes.User]?

    public init(
        directoryId: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        realm: Swift.String? = nil,
        users: [DirectoryServiceDataClientTypes.User]? = nil
    )
    {
        self.directoryId = directoryId
        self.nextToken = nextToken
        self.realm = realm
        self.users = users
    }
}

extension SearchUsersOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "SearchUsersOutput(directoryId: \(Swift.String(describing: directoryId)), realm: \(Swift.String(describing: realm)), users: \(Swift.String(describing: users)), nextToken: \"CONTENT_REDACTED\")"}
}

extension DirectoryServiceDataClientTypes {

    public enum UpdateType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case add
        case remove
        case replace
        case sdkUnknown(Swift.String)

        public static var allCases: [UpdateType] {
            return [
                .add,
                .remove,
                .replace
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .add: return "ADD"
            case .remove: return "REMOVE"
            case .replace: return "REPLACE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct UpdateGroupInput: Swift.Sendable {
    /// A unique and case-sensitive identifier that you provide to make sure the idempotency of the request, so multiple identical calls have the same effect as one single call. A client token is valid for 8 hours after the first request that uses it completes. After 8 hours, any request with the same client token is treated as a new request. If the request succeeds, any future uses of that token will be idempotent for another 8 hours. If you submit a request with the same client token but change one of the other parameters within the 8-hour idempotency window, Directory Service Data returns an ConflictException. This parameter is optional when using the CLI or SDK.
    public var clientToken: Swift.String?
    /// The identifier (ID) of the directory that's associated with the group.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The scope of the AD group. For details, see [Active Directory security groups](https://learn.microsoft.com/en-us/windows-server/identity/ad-ds/manage/understand-security-groups#group-scope).
    public var groupScope: DirectoryServiceDataClientTypes.GroupScope?
    /// The AD group type. For details, see [Active Directory security group type](https://learn.microsoft.com/en-us/windows-server/identity/ad-ds/manage/understand-security-groups#how-active-directory-security-groups-work).
    public var groupType: DirectoryServiceDataClientTypes.GroupType?
    /// An expression that defines one or more attributes with the data type and the value of each attribute.
    public var otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]?
    /// The name of the group.
    /// This member is required.
    public var samAccountName: Swift.String?
    /// The type of update to be performed. If no value exists for the attribute, use ADD. Otherwise, use REPLACE to change an attribute value or REMOVE to clear the attribute value.
    public var updateType: DirectoryServiceDataClientTypes.UpdateType?

    public init(
        clientToken: Swift.String? = nil,
        directoryId: Swift.String? = nil,
        groupScope: DirectoryServiceDataClientTypes.GroupScope? = nil,
        groupType: DirectoryServiceDataClientTypes.GroupType? = nil,
        otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]? = nil,
        samAccountName: Swift.String? = nil,
        updateType: DirectoryServiceDataClientTypes.UpdateType? = nil
    )
    {
        self.clientToken = clientToken
        self.directoryId = directoryId
        self.groupScope = groupScope
        self.groupType = groupType
        self.otherAttributes = otherAttributes
        self.samAccountName = samAccountName
        self.updateType = updateType
    }
}

public struct UpdateGroupOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateUserInput: Swift.Sendable {
    /// A unique and case-sensitive identifier that you provide to make sure the idempotency of the request, so multiple identical calls have the same effect as one single call. A client token is valid for 8 hours after the first request that uses it completes. After 8 hours, any request with the same client token is treated as a new request. If the request succeeds, any future uses of that token will be idempotent for another 8 hours. If you submit a request with the same client token but change one of the other parameters within the 8-hour idempotency window, Directory Service Data returns an ConflictException. This parameter is optional when using the CLI or SDK.
    public var clientToken: Swift.String?
    /// The identifier (ID) of the directory that's associated with the user.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The email address of the user.
    public var emailAddress: Swift.String?
    /// The first name of the user.
    public var givenName: Swift.String?
    /// An expression that defines one or more attribute names with the data type and value of each attribute. A key is an attribute name, and the value is a list of maps. For a list of supported attributes, see [Directory Service Data Attributes](https://docs.aws.amazon.com/directoryservice/latest/admin-guide/ad_data-attributes.html). Attribute names are case insensitive.
    public var otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]?
    /// The name of the user.
    /// This member is required.
    public var samAccountName: Swift.String?
    /// The last name of the user.
    public var surname: Swift.String?
    /// The type of update to be performed. If no value exists for the attribute, use ADD. Otherwise, use REPLACE to change an attribute value or REMOVE to clear the attribute value.
    public var updateType: DirectoryServiceDataClientTypes.UpdateType?

    public init(
        clientToken: Swift.String? = nil,
        directoryId: Swift.String? = nil,
        emailAddress: Swift.String? = nil,
        givenName: Swift.String? = nil,
        otherAttributes: [Swift.String: DirectoryServiceDataClientTypes.AttributeValue]? = nil,
        samAccountName: Swift.String? = nil,
        surname: Swift.String? = nil,
        updateType: DirectoryServiceDataClientTypes.UpdateType? = nil
    )
    {
        self.clientToken = clientToken
        self.directoryId = directoryId
        self.emailAddress = emailAddress
        self.givenName = givenName
        self.otherAttributes = otherAttributes
        self.samAccountName = samAccountName
        self.surname = surname
        self.updateType = updateType
    }
}

extension UpdateUserInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "UpdateUserInput(clientToken: \(Swift.String(describing: clientToken)), directoryId: \(Swift.String(describing: directoryId)), otherAttributes: \(Swift.String(describing: otherAttributes)), samAccountName: \(Swift.String(describing: samAccountName)), updateType: \(Swift.String(describing: updateType)), emailAddress: \"CONTENT_REDACTED\", givenName: \"CONTENT_REDACTED\", surname: \"CONTENT_REDACTED\")"}
}

public struct UpdateUserOutput: Swift.Sendable {

    public init() { }
}

extension AddGroupMemberInput {

    static func urlPathProvider(_ value: AddGroupMemberInput) -> Swift.String? {
        return "/GroupMemberships/AddGroupMember"
    }
}

extension AddGroupMemberInput {

    static func queryItemProvider(_ value: AddGroupMemberInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension CreateGroupInput {

    static func urlPathProvider(_ value: CreateGroupInput) -> Swift.String? {
        return "/Groups/CreateGroup"
    }
}

extension CreateGroupInput {

    static func queryItemProvider(_ value: CreateGroupInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension CreateUserInput {

    static func urlPathProvider(_ value: CreateUserInput) -> Swift.String? {
        return "/Users/<USER>"
    }
}

extension CreateUserInput {

    static func queryItemProvider(_ value: CreateUserInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension DeleteGroupInput {

    static func urlPathProvider(_ value: DeleteGroupInput) -> Swift.String? {
        return "/Groups/DeleteGroup"
    }
}

extension DeleteGroupInput {

    static func queryItemProvider(_ value: DeleteGroupInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension DeleteUserInput {

    static func urlPathProvider(_ value: DeleteUserInput) -> Swift.String? {
        return "/Users/<USER>"
    }
}

extension DeleteUserInput {

    static func queryItemProvider(_ value: DeleteUserInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension DescribeGroupInput {

    static func urlPathProvider(_ value: DescribeGroupInput) -> Swift.String? {
        return "/Groups/DescribeGroup"
    }
}

extension DescribeGroupInput {

    static func queryItemProvider(_ value: DescribeGroupInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension DescribeUserInput {

    static func urlPathProvider(_ value: DescribeUserInput) -> Swift.String? {
        return "/Users/<USER>"
    }
}

extension DescribeUserInput {

    static func queryItemProvider(_ value: DescribeUserInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension DisableUserInput {

    static func urlPathProvider(_ value: DisableUserInput) -> Swift.String? {
        return "/Users/<USER>"
    }
}

extension DisableUserInput {

    static func queryItemProvider(_ value: DisableUserInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension ListGroupMembersInput {

    static func urlPathProvider(_ value: ListGroupMembersInput) -> Swift.String? {
        return "/GroupMemberships/ListGroupMembers"
    }
}

extension ListGroupMembersInput {

    static func queryItemProvider(_ value: ListGroupMembersInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension ListGroupsInput {

    static func urlPathProvider(_ value: ListGroupsInput) -> Swift.String? {
        return "/Groups/ListGroups"
    }
}

extension ListGroupsInput {

    static func queryItemProvider(_ value: ListGroupsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension ListGroupsForMemberInput {

    static func urlPathProvider(_ value: ListGroupsForMemberInput) -> Swift.String? {
        return "/GroupMemberships/ListGroupsForMember"
    }
}

extension ListGroupsForMemberInput {

    static func queryItemProvider(_ value: ListGroupsForMemberInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension ListUsersInput {

    static func urlPathProvider(_ value: ListUsersInput) -> Swift.String? {
        return "/Users/<USER>"
    }
}

extension ListUsersInput {

    static func queryItemProvider(_ value: ListUsersInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension RemoveGroupMemberInput {

    static func urlPathProvider(_ value: RemoveGroupMemberInput) -> Swift.String? {
        return "/GroupMemberships/RemoveGroupMember"
    }
}

extension RemoveGroupMemberInput {

    static func queryItemProvider(_ value: RemoveGroupMemberInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension SearchGroupsInput {

    static func urlPathProvider(_ value: SearchGroupsInput) -> Swift.String? {
        return "/Groups/SearchGroups"
    }
}

extension SearchGroupsInput {

    static func queryItemProvider(_ value: SearchGroupsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension SearchUsersInput {

    static func urlPathProvider(_ value: SearchUsersInput) -> Swift.String? {
        return "/Users/<USER>"
    }
}

extension SearchUsersInput {

    static func queryItemProvider(_ value: SearchUsersInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension UpdateGroupInput {

    static func urlPathProvider(_ value: UpdateGroupInput) -> Swift.String? {
        return "/Groups/UpdateGroup"
    }
}

extension UpdateGroupInput {

    static func queryItemProvider(_ value: UpdateGroupInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension UpdateUserInput {

    static func urlPathProvider(_ value: UpdateUserInput) -> Swift.String? {
        return "/Users/<USER>"
    }
}

extension UpdateUserInput {

    static func queryItemProvider(_ value: UpdateUserInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let directoryId = value.directoryId else {
            let message = "Creating a URL Query Item failed. directoryId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let directoryIdQueryItem = Smithy.URIQueryItem(name: "DirectoryId".urlPercentEncoding(), value: Swift.String(directoryId).urlPercentEncoding())
        items.append(directoryIdQueryItem)
        return items
    }
}

extension AddGroupMemberInput {

    static func write(value: AddGroupMemberInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["GroupName"].write(value.groupName)
        try writer["MemberName"].write(value.memberName)
        try writer["MemberRealm"].write(value.memberRealm)
    }
}

extension CreateGroupInput {

    static func write(value: CreateGroupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["GroupScope"].write(value.groupScope)
        try writer["GroupType"].write(value.groupType)
        try writer["OtherAttributes"].writeMap(value.otherAttributes, valueWritingClosure: DirectoryServiceDataClientTypes.AttributeValue.write(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["SAMAccountName"].write(value.samAccountName)
    }
}

extension CreateUserInput {

    static func write(value: CreateUserInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["EmailAddress"].write(value.emailAddress)
        try writer["GivenName"].write(value.givenName)
        try writer["OtherAttributes"].writeMap(value.otherAttributes, valueWritingClosure: DirectoryServiceDataClientTypes.AttributeValue.write(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["SAMAccountName"].write(value.samAccountName)
        try writer["Surname"].write(value.surname)
    }
}

extension DeleteGroupInput {

    static func write(value: DeleteGroupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["SAMAccountName"].write(value.samAccountName)
    }
}

extension DeleteUserInput {

    static func write(value: DeleteUserInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["SAMAccountName"].write(value.samAccountName)
    }
}

extension DescribeGroupInput {

    static func write(value: DescribeGroupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["OtherAttributes"].writeList(value.otherAttributes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Realm"].write(value.realm)
        try writer["SAMAccountName"].write(value.samAccountName)
    }
}

extension DescribeUserInput {

    static func write(value: DescribeUserInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["OtherAttributes"].writeList(value.otherAttributes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Realm"].write(value.realm)
        try writer["SAMAccountName"].write(value.samAccountName)
    }
}

extension DisableUserInput {

    static func write(value: DisableUserInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["SAMAccountName"].write(value.samAccountName)
    }
}

extension ListGroupMembersInput {

    static func write(value: ListGroupMembersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["MemberRealm"].write(value.memberRealm)
        try writer["NextToken"].write(value.nextToken)
        try writer["Realm"].write(value.realm)
        try writer["SAMAccountName"].write(value.samAccountName)
    }
}

extension ListGroupsInput {

    static func write(value: ListGroupsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["Realm"].write(value.realm)
    }
}

extension ListGroupsForMemberInput {

    static func write(value: ListGroupsForMemberInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["MemberRealm"].write(value.memberRealm)
        try writer["NextToken"].write(value.nextToken)
        try writer["Realm"].write(value.realm)
        try writer["SAMAccountName"].write(value.samAccountName)
    }
}

extension ListUsersInput {

    static func write(value: ListUsersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["Realm"].write(value.realm)
    }
}

extension RemoveGroupMemberInput {

    static func write(value: RemoveGroupMemberInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["GroupName"].write(value.groupName)
        try writer["MemberName"].write(value.memberName)
        try writer["MemberRealm"].write(value.memberRealm)
    }
}

extension SearchGroupsInput {

    static func write(value: SearchGroupsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["Realm"].write(value.realm)
        try writer["SearchAttributes"].writeList(value.searchAttributes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["SearchString"].write(value.searchString)
    }
}

extension SearchUsersInput {

    static func write(value: SearchUsersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["Realm"].write(value.realm)
        try writer["SearchAttributes"].writeList(value.searchAttributes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["SearchString"].write(value.searchString)
    }
}

extension UpdateGroupInput {

    static func write(value: UpdateGroupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["GroupScope"].write(value.groupScope)
        try writer["GroupType"].write(value.groupType)
        try writer["OtherAttributes"].writeMap(value.otherAttributes, valueWritingClosure: DirectoryServiceDataClientTypes.AttributeValue.write(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["SAMAccountName"].write(value.samAccountName)
        try writer["UpdateType"].write(value.updateType)
    }
}

extension UpdateUserInput {

    static func write(value: UpdateUserInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["EmailAddress"].write(value.emailAddress)
        try writer["GivenName"].write(value.givenName)
        try writer["OtherAttributes"].writeMap(value.otherAttributes, valueWritingClosure: DirectoryServiceDataClientTypes.AttributeValue.write(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["SAMAccountName"].write(value.samAccountName)
        try writer["Surname"].write(value.surname)
        try writer["UpdateType"].write(value.updateType)
    }
}

extension AddGroupMemberOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AddGroupMemberOutput {
        return AddGroupMemberOutput()
    }
}

extension CreateGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateGroupOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.samAccountName = try reader["SAMAccountName"].readIfPresent()
        value.sid = try reader["SID"].readIfPresent()
        return value
    }
}

extension CreateUserOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateUserOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateUserOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.samAccountName = try reader["SAMAccountName"].readIfPresent()
        value.sid = try reader["SID"].readIfPresent()
        return value
    }
}

extension DeleteGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteGroupOutput {
        return DeleteGroupOutput()
    }
}

extension DeleteUserOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteUserOutput {
        return DeleteUserOutput()
    }
}

extension DescribeGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeGroupOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.distinguishedName = try reader["DistinguishedName"].readIfPresent()
        value.groupScope = try reader["GroupScope"].readIfPresent()
        value.groupType = try reader["GroupType"].readIfPresent()
        value.otherAttributes = try reader["OtherAttributes"].readMapIfPresent(valueReadingClosure: DirectoryServiceDataClientTypes.AttributeValue.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.realm = try reader["Realm"].readIfPresent()
        value.samAccountName = try reader["SAMAccountName"].readIfPresent()
        value.sid = try reader["SID"].readIfPresent()
        return value
    }
}

extension DescribeUserOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeUserOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeUserOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.distinguishedName = try reader["DistinguishedName"].readIfPresent()
        value.emailAddress = try reader["EmailAddress"].readIfPresent()
        value.enabled = try reader["Enabled"].readIfPresent()
        value.givenName = try reader["GivenName"].readIfPresent()
        value.otherAttributes = try reader["OtherAttributes"].readMapIfPresent(valueReadingClosure: DirectoryServiceDataClientTypes.AttributeValue.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.realm = try reader["Realm"].readIfPresent()
        value.samAccountName = try reader["SAMAccountName"].readIfPresent()
        value.sid = try reader["SID"].readIfPresent()
        value.surname = try reader["Surname"].readIfPresent()
        value.userPrincipalName = try reader["UserPrincipalName"].readIfPresent()
        return value
    }
}

extension DisableUserOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisableUserOutput {
        return DisableUserOutput()
    }
}

extension ListGroupMembersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListGroupMembersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListGroupMembersOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.memberRealm = try reader["MemberRealm"].readIfPresent()
        value.members = try reader["Members"].readListIfPresent(memberReadingClosure: DirectoryServiceDataClientTypes.Member.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.realm = try reader["Realm"].readIfPresent()
        return value
    }
}

extension ListGroupsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListGroupsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListGroupsOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.groups = try reader["Groups"].readListIfPresent(memberReadingClosure: DirectoryServiceDataClientTypes.GroupSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.realm = try reader["Realm"].readIfPresent()
        return value
    }
}

extension ListGroupsForMemberOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListGroupsForMemberOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListGroupsForMemberOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.groups = try reader["Groups"].readListIfPresent(memberReadingClosure: DirectoryServiceDataClientTypes.GroupSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.memberRealm = try reader["MemberRealm"].readIfPresent()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.realm = try reader["Realm"].readIfPresent()
        return value
    }
}

extension ListUsersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListUsersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListUsersOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.realm = try reader["Realm"].readIfPresent()
        value.users = try reader["Users"].readListIfPresent(memberReadingClosure: DirectoryServiceDataClientTypes.UserSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension RemoveGroupMemberOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RemoveGroupMemberOutput {
        return RemoveGroupMemberOutput()
    }
}

extension SearchGroupsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SearchGroupsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = SearchGroupsOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.groups = try reader["Groups"].readListIfPresent(memberReadingClosure: DirectoryServiceDataClientTypes.Group.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.realm = try reader["Realm"].readIfPresent()
        return value
    }
}

extension SearchUsersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SearchUsersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = SearchUsersOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.realm = try reader["Realm"].readIfPresent()
        value.users = try reader["Users"].readListIfPresent(memberReadingClosure: DirectoryServiceDataClientTypes.User.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension UpdateGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateGroupOutput {
        return UpdateGroupOutput()
    }
}

extension UpdateUserOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateUserOutput {
        return UpdateUserOutput()
    }
}

enum AddGroupMemberOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateUserOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteUserOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeUserOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisableUserOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListGroupMembersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListGroupsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListGroupsForMemberOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListUsersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RemoveGroupMemberOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SearchGroupsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SearchUsersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateUserOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.reason = try reader["Reason"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.reason = try reader["Reason"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DirectoryUnavailableException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> DirectoryUnavailableException {
        let reader = baseError.errorBodyReader
        var value = DirectoryUnavailableException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.reason = try reader["Reason"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ThrottlingException()
        if let retryAfterSecondsHeaderValue = httpResponse.headers.value(for: "Retry-After") {
            value.properties.retryAfterSeconds = Swift.Int(retryAfterSecondsHeaderValue) ?? 0
        }
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DirectoryServiceDataClientTypes.AttributeValue {

    static func write(value: DirectoryServiceDataClientTypes.AttributeValue?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        switch value {
            case let .bool(bool):
                try writer["BOOL"].write(bool)
            case let .n(n):
                try writer["N"].write(n)
            case let .s(s):
                try writer["S"].write(s)
            case let .ss(ss):
                try writer["SS"].writeList(ss, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
            case let .sdkUnknown(sdkUnknown):
                try writer["sdkUnknown"].write(sdkUnknown)
        }
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryServiceDataClientTypes.AttributeValue {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "S":
                return .s(try reader["S"].read())
            case "N":
                return .n(try reader["N"].read())
            case "BOOL":
                return .bool(try reader["BOOL"].read())
            case "SS":
                return .ss(try reader["SS"].readList(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false))
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension DirectoryServiceDataClientTypes.Member {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryServiceDataClientTypes.Member {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryServiceDataClientTypes.Member()
        value.sid = try reader["SID"].readIfPresent() ?? ""
        value.samAccountName = try reader["SAMAccountName"].readIfPresent() ?? ""
        value.memberType = try reader["MemberType"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension DirectoryServiceDataClientTypes.GroupSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryServiceDataClientTypes.GroupSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryServiceDataClientTypes.GroupSummary()
        value.sid = try reader["SID"].readIfPresent() ?? ""
        value.samAccountName = try reader["SAMAccountName"].readIfPresent() ?? ""
        value.groupType = try reader["GroupType"].readIfPresent() ?? .sdkUnknown("")
        value.groupScope = try reader["GroupScope"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension DirectoryServiceDataClientTypes.UserSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryServiceDataClientTypes.UserSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryServiceDataClientTypes.UserSummary()
        value.sid = try reader["SID"].readIfPresent() ?? ""
        value.samAccountName = try reader["SAMAccountName"].readIfPresent() ?? ""
        value.givenName = try reader["GivenName"].readIfPresent()
        value.surname = try reader["Surname"].readIfPresent()
        value.enabled = try reader["Enabled"].readIfPresent() ?? false
        return value
    }
}

extension DirectoryServiceDataClientTypes.Group {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryServiceDataClientTypes.Group {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryServiceDataClientTypes.Group()
        value.sid = try reader["SID"].readIfPresent()
        value.samAccountName = try reader["SAMAccountName"].readIfPresent() ?? ""
        value.distinguishedName = try reader["DistinguishedName"].readIfPresent()
        value.groupType = try reader["GroupType"].readIfPresent()
        value.groupScope = try reader["GroupScope"].readIfPresent()
        value.otherAttributes = try reader["OtherAttributes"].readMapIfPresent(valueReadingClosure: DirectoryServiceDataClientTypes.AttributeValue.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DirectoryServiceDataClientTypes.User {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryServiceDataClientTypes.User {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryServiceDataClientTypes.User()
        value.sid = try reader["SID"].readIfPresent()
        value.samAccountName = try reader["SAMAccountName"].readIfPresent() ?? ""
        value.distinguishedName = try reader["DistinguishedName"].readIfPresent()
        value.userPrincipalName = try reader["UserPrincipalName"].readIfPresent()
        value.emailAddress = try reader["EmailAddress"].readIfPresent()
        value.givenName = try reader["GivenName"].readIfPresent()
        value.surname = try reader["Surname"].readIfPresent()
        value.enabled = try reader["Enabled"].readIfPresent()
        value.otherAttributes = try reader["OtherAttributes"].readMapIfPresent(valueReadingClosure: DirectoryServiceDataClientTypes.AttributeValue.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

public enum DirectoryServiceDataClientTypes {}
