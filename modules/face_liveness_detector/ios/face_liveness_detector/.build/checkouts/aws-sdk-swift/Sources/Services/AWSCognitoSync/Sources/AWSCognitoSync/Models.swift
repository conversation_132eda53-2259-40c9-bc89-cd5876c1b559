//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
import struct SmithyHTTPAPI.Header
import struct SmithyHTTPAPI.Headers


public struct SetCognitoEventsOutput: Swift.Sendable {

    public init() { }
}

/// An exception thrown when a bulk publish operation is requested less than 24 hours after a previous bulk publish operation completed successfully.
public struct AlreadyStreamedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message associated with the AlreadyStreamedException exception.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AlreadyStreamed" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// An exception thrown when there is an IN_PROGRESS bulk publish operation for the given identity pool.
public struct DuplicateRequestException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message associated with the DuplicateRequestException exception.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DuplicateRequest" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Indicates an internal service error.
public struct InternalErrorException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Message returned by InternalErrorException.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalError" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Thrown when a request parameter does not comply with the associated constraints.
public struct InvalidParameterException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Message returned by InvalidParameterException.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidParameter" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Thrown when a user is not authorized to access the requested resource.
public struct NotAuthorizedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message returned by a NotAuthorizedException.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "NotAuthorizedError" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Thrown if the resource doesn't exist.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Message returned by a ResourceNotFoundException.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The input for the BulkPublish operation.
public struct BulkPublishInput: Swift.Sendable {
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        identityPoolId: Swift.String? = nil
    )
    {
        self.identityPoolId = identityPoolId
    }
}

/// The output for the BulkPublish operation.
public struct BulkPublishOutput: Swift.Sendable {
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    public var identityPoolId: Swift.String?

    public init(
        identityPoolId: Swift.String? = nil
    )
    {
        self.identityPoolId = identityPoolId
    }
}

/// Thrown if an update can't be applied because the resource was changed by another call and this would result in a conflict.
public struct ResourceConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message returned by a ResourceConflictException.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceConflict" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Thrown if the request is throttled.
public struct TooManyRequestsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Message returned by a TooManyRequestsException.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TooManyRequests" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// A request to delete the specific dataset.
public struct DeleteDatasetInput: Swift.Sendable {
    /// A string of up to 128 characters. Allowed characters are a-z, A-Z, 0-9, '_' (underscore), '-' (dash), and '.' (dot).
    /// This member is required.
    public var datasetName: Swift.String?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityId: Swift.String?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        datasetName: Swift.String? = nil,
        identityId: Swift.String? = nil,
        identityPoolId: Swift.String? = nil
    )
    {
        self.datasetName = datasetName
        self.identityId = identityId
        self.identityPoolId = identityPoolId
    }
}

extension CognitoSyncClientTypes {

    /// A collection of data for an identity pool. An identity pool can have multiple datasets. A dataset is per identity and can be general or associated with a particular entity in an application (like a saved game). Datasets are automatically created if they don't exist. Data is synced by dataset, and a dataset can hold up to 1MB of key-value pairs.
    public struct Dataset: Swift.Sendable {
        /// Date on which the dataset was created.
        public var creationDate: Foundation.Date?
        /// Total size in bytes of the records in this dataset.
        public var dataStorage: Swift.Int?
        /// A string of up to 128 characters. Allowed characters are a-z, A-Z, 0-9, '_' (underscore), '-' (dash), and '.' (dot).
        public var datasetName: Swift.String?
        /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
        public var identityId: Swift.String?
        /// The device that made the last change to this dataset.
        public var lastModifiedBy: Swift.String?
        /// Date when the dataset was last modified.
        public var lastModifiedDate: Foundation.Date?
        /// Number of records in this dataset.
        public var numRecords: Swift.Int?

        public init(
            creationDate: Foundation.Date? = nil,
            dataStorage: Swift.Int? = nil,
            datasetName: Swift.String? = nil,
            identityId: Swift.String? = nil,
            lastModifiedBy: Swift.String? = nil,
            lastModifiedDate: Foundation.Date? = nil,
            numRecords: Swift.Int? = nil
        )
        {
            self.creationDate = creationDate
            self.dataStorage = dataStorage
            self.datasetName = datasetName
            self.identityId = identityId
            self.lastModifiedBy = lastModifiedBy
            self.lastModifiedDate = lastModifiedDate
            self.numRecords = numRecords
        }
    }
}

/// Response to a successful DeleteDataset request.
public struct DeleteDatasetOutput: Swift.Sendable {
    /// A collection of data for an identity pool. An identity pool can have multiple datasets. A dataset is per identity and can be general or associated with a particular entity in an application (like a saved game). Datasets are automatically created if they don't exist. Data is synced by dataset, and a dataset can hold up to 1MB of key-value pairs.
    public var dataset: CognitoSyncClientTypes.Dataset?

    public init(
        dataset: CognitoSyncClientTypes.Dataset? = nil
    )
    {
        self.dataset = dataset
    }
}

/// A request for meta data about a dataset (creation date, number of records, size) by owner and dataset name.
public struct DescribeDatasetInput: Swift.Sendable {
    /// A string of up to 128 characters. Allowed characters are a-z, A-Z, 0-9, '_' (underscore), '-' (dash), and '.' (dot).
    /// This member is required.
    public var datasetName: Swift.String?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityId: Swift.String?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        datasetName: Swift.String? = nil,
        identityId: Swift.String? = nil,
        identityPoolId: Swift.String? = nil
    )
    {
        self.datasetName = datasetName
        self.identityId = identityId
        self.identityPoolId = identityPoolId
    }
}

/// Response to a successful DescribeDataset request.
public struct DescribeDatasetOutput: Swift.Sendable {
    /// Meta data for a collection of data for an identity. An identity can have multiple datasets. A dataset can be general or associated with a particular entity in an application (like a saved game). Datasets are automatically created if they don't exist. Data is synced by dataset, and a dataset can hold up to 1MB of key-value pairs.
    public var dataset: CognitoSyncClientTypes.Dataset?

    public init(
        dataset: CognitoSyncClientTypes.Dataset? = nil
    )
    {
        self.dataset = dataset
    }
}

/// A request for usage information about the identity pool.
public struct DescribeIdentityPoolUsageInput: Swift.Sendable {
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        identityPoolId: Swift.String? = nil
    )
    {
        self.identityPoolId = identityPoolId
    }
}

extension CognitoSyncClientTypes {

    /// Usage information for the identity pool.
    public struct IdentityPoolUsage: Swift.Sendable {
        /// Data storage information for the identity pool.
        public var dataStorage: Swift.Int?
        /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
        public var identityPoolId: Swift.String?
        /// Date on which the identity pool was last modified.
        public var lastModifiedDate: Foundation.Date?
        /// Number of sync sessions for the identity pool.
        public var syncSessionsCount: Swift.Int?

        public init(
            dataStorage: Swift.Int? = nil,
            identityPoolId: Swift.String? = nil,
            lastModifiedDate: Foundation.Date? = nil,
            syncSessionsCount: Swift.Int? = nil
        )
        {
            self.dataStorage = dataStorage
            self.identityPoolId = identityPoolId
            self.lastModifiedDate = lastModifiedDate
            self.syncSessionsCount = syncSessionsCount
        }
    }
}

/// Response to a successful DescribeIdentityPoolUsage request.
public struct DescribeIdentityPoolUsageOutput: Swift.Sendable {
    /// Information about the usage of the identity pool.
    public var identityPoolUsage: CognitoSyncClientTypes.IdentityPoolUsage?

    public init(
        identityPoolUsage: CognitoSyncClientTypes.IdentityPoolUsage? = nil
    )
    {
        self.identityPoolUsage = identityPoolUsage
    }
}

/// A request for information about the usage of an identity pool.
public struct DescribeIdentityUsageInput: Swift.Sendable {
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityId: Swift.String?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        identityId: Swift.String? = nil,
        identityPoolId: Swift.String? = nil
    )
    {
        self.identityId = identityId
        self.identityPoolId = identityPoolId
    }
}

extension CognitoSyncClientTypes {

    /// Usage information for the identity.
    public struct IdentityUsage: Swift.Sendable {
        /// Total data storage for this identity.
        public var dataStorage: Swift.Int?
        /// Number of datasets for the identity.
        public var datasetCount: Swift.Int
        /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
        public var identityId: Swift.String?
        /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
        public var identityPoolId: Swift.String?
        /// Date on which the identity was last modified.
        public var lastModifiedDate: Foundation.Date?

        public init(
            dataStorage: Swift.Int? = nil,
            datasetCount: Swift.Int = 0,
            identityId: Swift.String? = nil,
            identityPoolId: Swift.String? = nil,
            lastModifiedDate: Foundation.Date? = nil
        )
        {
            self.dataStorage = dataStorage
            self.datasetCount = datasetCount
            self.identityId = identityId
            self.identityPoolId = identityPoolId
            self.lastModifiedDate = lastModifiedDate
        }
    }
}

/// The response to a successful DescribeIdentityUsage request.
public struct DescribeIdentityUsageOutput: Swift.Sendable {
    /// Usage information for the identity.
    public var identityUsage: CognitoSyncClientTypes.IdentityUsage?

    public init(
        identityUsage: CognitoSyncClientTypes.IdentityUsage? = nil
    )
    {
        self.identityUsage = identityUsage
    }
}

/// The input for the GetBulkPublishDetails operation.
public struct GetBulkPublishDetailsInput: Swift.Sendable {
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        identityPoolId: Swift.String? = nil
    )
    {
        self.identityPoolId = identityPoolId
    }
}

extension CognitoSyncClientTypes {

    public enum BulkPublishStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case inProgress
        case notStarted
        case succeeded
        case sdkUnknown(Swift.String)

        public static var allCases: [BulkPublishStatus] {
            return [
                .failed,
                .inProgress,
                .notStarted,
                .succeeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case .notStarted: return "NOT_STARTED"
            case .succeeded: return "SUCCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The output for the GetBulkPublishDetails operation.
public struct GetBulkPublishDetailsOutput: Swift.Sendable {
    /// If BulkPublishStatus is SUCCEEDED, the time the last bulk publish operation completed.
    public var bulkPublishCompleteTime: Foundation.Date?
    /// The date/time at which the last bulk publish was initiated.
    public var bulkPublishStartTime: Foundation.Date?
    /// Status of the last bulk publish operation, valid values are: NOT_STARTED - No bulk publish has been requested for this identity pool IN_PROGRESS - Data is being published to the configured stream SUCCEEDED - All data for the identity pool has been published to the configured stream FAILED - Some portion of the data has failed to publish, check FailureMessage for the cause.
    public var bulkPublishStatus: CognitoSyncClientTypes.BulkPublishStatus?
    /// If BulkPublishStatus is FAILED this field will contain the error message that caused the bulk publish to fail.
    public var failureMessage: Swift.String?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    public var identityPoolId: Swift.String?

    public init(
        bulkPublishCompleteTime: Foundation.Date? = nil,
        bulkPublishStartTime: Foundation.Date? = nil,
        bulkPublishStatus: CognitoSyncClientTypes.BulkPublishStatus? = nil,
        failureMessage: Swift.String? = nil,
        identityPoolId: Swift.String? = nil
    )
    {
        self.bulkPublishCompleteTime = bulkPublishCompleteTime
        self.bulkPublishStartTime = bulkPublishStartTime
        self.bulkPublishStatus = bulkPublishStatus
        self.failureMessage = failureMessage
        self.identityPoolId = identityPoolId
    }
}

/// A request for a list of the configured Cognito Events
public struct GetCognitoEventsInput: Swift.Sendable {
    /// The Cognito Identity Pool ID for the request
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        identityPoolId: Swift.String? = nil
    )
    {
        self.identityPoolId = identityPoolId
    }
}

/// The response from the GetCognitoEvents request
public struct GetCognitoEventsOutput: Swift.Sendable {
    /// The Cognito Events returned from the GetCognitoEvents request
    public var events: [Swift.String: Swift.String]?

    public init(
        events: [Swift.String: Swift.String]? = nil
    )
    {
        self.events = events
    }
}

/// The input for the GetIdentityPoolConfiguration operation.
public struct GetIdentityPoolConfigurationInput: Swift.Sendable {
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. This is the ID of the pool for which to return a configuration.
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        identityPoolId: Swift.String? = nil
    )
    {
        self.identityPoolId = identityPoolId
    }
}

extension CognitoSyncClientTypes {

    public enum StreamingStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case enabled
        case sdkUnknown(Swift.String)

        public static var allCases: [StreamingStatus] {
            return [
                .disabled,
                .enabled
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "DISABLED"
            case .enabled: return "ENABLED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CognitoSyncClientTypes {

    /// Configuration options for configure Cognito streams.
    public struct CognitoStreams: Swift.Sendable {
        /// The ARN of the role Amazon Cognito can assume in order to publish to the stream. This role must grant access to Amazon Cognito (cognito-sync) to invoke PutRecord on your Cognito stream.
        public var roleArn: Swift.String?
        /// The name of the Cognito stream to receive updates. This stream must be in the developers account and in the same region as the identity pool.
        public var streamName: Swift.String?
        /// Status of the Cognito streams. Valid values are: ENABLED - Streaming of updates to identity pool is enabled. DISABLED - Streaming of updates to identity pool is disabled. Bulk publish will also fail if StreamingStatus is DISABLED.
        public var streamingStatus: CognitoSyncClientTypes.StreamingStatus?

        public init(
            roleArn: Swift.String? = nil,
            streamName: Swift.String? = nil,
            streamingStatus: CognitoSyncClientTypes.StreamingStatus? = nil
        )
        {
            self.roleArn = roleArn
            self.streamName = streamName
            self.streamingStatus = streamingStatus
        }
    }
}

extension CognitoSyncClientTypes {

    /// Configuration options to be applied to the identity pool.
    public struct PushSync: Swift.Sendable {
        /// List of SNS platform application ARNs that could be used by clients.
        public var applicationArns: [Swift.String]?
        /// A role configured to allow Cognito to call SNS on behalf of the developer.
        public var roleArn: Swift.String?

        public init(
            applicationArns: [Swift.String]? = nil,
            roleArn: Swift.String? = nil
        )
        {
            self.applicationArns = applicationArns
            self.roleArn = roleArn
        }
    }
}

/// The output for the GetIdentityPoolConfiguration operation.
public struct GetIdentityPoolConfigurationOutput: Swift.Sendable {
    /// Options to apply to this identity pool for Amazon Cognito streams.
    public var cognitoStreams: CognitoSyncClientTypes.CognitoStreams?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito.
    public var identityPoolId: Swift.String?
    /// Options to apply to this identity pool for push synchronization.
    public var pushSync: CognitoSyncClientTypes.PushSync?

    public init(
        cognitoStreams: CognitoSyncClientTypes.CognitoStreams? = nil,
        identityPoolId: Swift.String? = nil,
        pushSync: CognitoSyncClientTypes.PushSync? = nil
    )
    {
        self.cognitoStreams = cognitoStreams
        self.identityPoolId = identityPoolId
        self.pushSync = pushSync
    }
}

/// Request for a list of datasets for an identity.
public struct ListDatasetsInput: Swift.Sendable {
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityId: Swift.String?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// The maximum number of results to be returned.
    public var maxResults: Swift.Int?
    /// A pagination token for obtaining the next page of results.
    public var nextToken: Swift.String?

    public init(
        identityId: Swift.String? = nil,
        identityPoolId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.identityId = identityId
        self.identityPoolId = identityPoolId
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

/// Returned for a successful ListDatasets request.
public struct ListDatasetsOutput: Swift.Sendable {
    /// Number of datasets returned.
    public var count: Swift.Int
    /// A set of datasets.
    public var datasets: [CognitoSyncClientTypes.Dataset]?
    /// A pagination token for obtaining the next page of results.
    public var nextToken: Swift.String?

    public init(
        count: Swift.Int = 0,
        datasets: [CognitoSyncClientTypes.Dataset]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.count = count
        self.datasets = datasets
        self.nextToken = nextToken
    }
}

/// A request for usage information on an identity pool.
public struct ListIdentityPoolUsageInput: Swift.Sendable {
    /// The maximum number of results to be returned.
    public var maxResults: Swift.Int?
    /// A pagination token for obtaining the next page of results.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

/// Returned for a successful ListIdentityPoolUsage request.
public struct ListIdentityPoolUsageOutput: Swift.Sendable {
    /// Total number of identities for the identity pool.
    public var count: Swift.Int
    /// Usage information for the identity pools.
    public var identityPoolUsages: [CognitoSyncClientTypes.IdentityPoolUsage]?
    /// The maximum number of results to be returned.
    public var maxResults: Swift.Int
    /// A pagination token for obtaining the next page of results.
    public var nextToken: Swift.String?

    public init(
        count: Swift.Int = 0,
        identityPoolUsages: [CognitoSyncClientTypes.IdentityPoolUsage]? = nil,
        maxResults: Swift.Int = 0,
        nextToken: Swift.String? = nil
    )
    {
        self.count = count
        self.identityPoolUsages = identityPoolUsages
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

/// A request for a list of records.
public struct ListRecordsInput: Swift.Sendable {
    /// A string of up to 128 characters. Allowed characters are a-z, A-Z, 0-9, '_' (underscore), '-' (dash), and '.' (dot).
    /// This member is required.
    public var datasetName: Swift.String?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityId: Swift.String?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// The last server sync count for this record.
    public var lastSyncCount: Swift.Int?
    /// The maximum number of results to be returned.
    public var maxResults: Swift.Int?
    /// A pagination token for obtaining the next page of results.
    public var nextToken: Swift.String?
    /// A token containing a session ID, identity ID, and expiration.
    public var syncSessionToken: Swift.String?

    public init(
        datasetName: Swift.String? = nil,
        identityId: Swift.String? = nil,
        identityPoolId: Swift.String? = nil,
        lastSyncCount: Swift.Int? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        syncSessionToken: Swift.String? = nil
    )
    {
        self.datasetName = datasetName
        self.identityId = identityId
        self.identityPoolId = identityPoolId
        self.lastSyncCount = lastSyncCount
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.syncSessionToken = syncSessionToken
    }
}

extension CognitoSyncClientTypes {

    /// The basic data structure of a dataset.
    public struct Record: Swift.Sendable {
        /// The last modified date of the client device.
        public var deviceLastModifiedDate: Foundation.Date?
        /// The key for the record.
        public var key: Swift.String?
        /// The user/device that made the last change to this record.
        public var lastModifiedBy: Swift.String?
        /// The date on which the record was last modified.
        public var lastModifiedDate: Foundation.Date?
        /// The server sync count for this record.
        public var syncCount: Swift.Int?
        /// The value for the record.
        public var value: Swift.String?

        public init(
            deviceLastModifiedDate: Foundation.Date? = nil,
            key: Swift.String? = nil,
            lastModifiedBy: Swift.String? = nil,
            lastModifiedDate: Foundation.Date? = nil,
            syncCount: Swift.Int? = nil,
            value: Swift.String? = nil
        )
        {
            self.deviceLastModifiedDate = deviceLastModifiedDate
            self.key = key
            self.lastModifiedBy = lastModifiedBy
            self.lastModifiedDate = lastModifiedDate
            self.syncCount = syncCount
            self.value = value
        }
    }
}

/// Returned for a successful ListRecordsRequest.
public struct ListRecordsOutput: Swift.Sendable {
    /// Total number of records.
    public var count: Swift.Int
    /// A boolean value specifying whether to delete the dataset locally.
    public var datasetDeletedAfterRequestedSyncCount: Swift.Bool
    /// Indicates whether the dataset exists.
    public var datasetExists: Swift.Bool
    /// Server sync count for this dataset.
    public var datasetSyncCount: Swift.Int?
    /// The user/device that made the last change to this record.
    public var lastModifiedBy: Swift.String?
    /// Names of merged datasets.
    public var mergedDatasetNames: [Swift.String]?
    /// A pagination token for obtaining the next page of results.
    public var nextToken: Swift.String?
    /// A list of all records.
    public var records: [CognitoSyncClientTypes.Record]?
    /// A token containing a session ID, identity ID, and expiration.
    public var syncSessionToken: Swift.String?

    public init(
        count: Swift.Int = 0,
        datasetDeletedAfterRequestedSyncCount: Swift.Bool = false,
        datasetExists: Swift.Bool = false,
        datasetSyncCount: Swift.Int? = nil,
        lastModifiedBy: Swift.String? = nil,
        mergedDatasetNames: [Swift.String]? = nil,
        nextToken: Swift.String? = nil,
        records: [CognitoSyncClientTypes.Record]? = nil,
        syncSessionToken: Swift.String? = nil
    )
    {
        self.count = count
        self.datasetDeletedAfterRequestedSyncCount = datasetDeletedAfterRequestedSyncCount
        self.datasetExists = datasetExists
        self.datasetSyncCount = datasetSyncCount
        self.lastModifiedBy = lastModifiedBy
        self.mergedDatasetNames = mergedDatasetNames
        self.nextToken = nextToken
        self.records = records
        self.syncSessionToken = syncSessionToken
    }
}

public struct InvalidConfigurationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Message returned by InvalidConfigurationException.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidConfiguration" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CognitoSyncClientTypes {

    public enum Platform: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case adm
        case apns
        case apnsSandbox
        case gcm
        case sdkUnknown(Swift.String)

        public static var allCases: [Platform] {
            return [
                .adm,
                .apns,
                .apnsSandbox,
                .gcm
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .adm: return "ADM"
            case .apns: return "APNS"
            case .apnsSandbox: return "APNS_SANDBOX"
            case .gcm: return "GCM"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// A request to RegisterDevice.
public struct RegisterDeviceInput: Swift.Sendable {
    /// The unique ID for this identity.
    /// This member is required.
    public var identityId: Swift.String?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. Here, the ID of the pool that the identity belongs to.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// The SNS platform type (e.g. GCM, SDM, APNS, APNS_SANDBOX).
    /// This member is required.
    public var platform: CognitoSyncClientTypes.Platform?
    /// The push token.
    /// This member is required.
    public var token: Swift.String?

    public init(
        identityId: Swift.String? = nil,
        identityPoolId: Swift.String? = nil,
        platform: CognitoSyncClientTypes.Platform? = nil,
        token: Swift.String? = nil
    )
    {
        self.identityId = identityId
        self.identityPoolId = identityPoolId
        self.platform = platform
        self.token = token
    }
}

/// Response to a RegisterDevice request.
public struct RegisterDeviceOutput: Swift.Sendable {
    /// The unique ID generated for this device by Cognito.
    public var deviceId: Swift.String?

    public init(
        deviceId: Swift.String? = nil
    )
    {
        self.deviceId = deviceId
    }
}

/// A request to configure Cognito Events""
public struct SetCognitoEventsInput: Swift.Sendable {
    /// The events to configure
    /// This member is required.
    public var events: [Swift.String: Swift.String]?
    /// The Cognito Identity Pool to use when configuring Cognito Events
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        events: [Swift.String: Swift.String]? = nil,
        identityPoolId: Swift.String? = nil
    )
    {
        self.events = events
        self.identityPoolId = identityPoolId
    }
}

/// Thrown if there are parallel requests to modify a resource.
public struct ConcurrentModificationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message returned by a ConcurrentModicationException.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConcurrentModification" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The input for the SetIdentityPoolConfiguration operation.
public struct SetIdentityPoolConfigurationInput: Swift.Sendable {
    /// Options to apply to this identity pool for Amazon Cognito streams.
    public var cognitoStreams: CognitoSyncClientTypes.CognitoStreams?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. This is the ID of the pool to modify.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// Options to apply to this identity pool for push synchronization.
    public var pushSync: CognitoSyncClientTypes.PushSync?

    public init(
        cognitoStreams: CognitoSyncClientTypes.CognitoStreams? = nil,
        identityPoolId: Swift.String? = nil,
        pushSync: CognitoSyncClientTypes.PushSync? = nil
    )
    {
        self.cognitoStreams = cognitoStreams
        self.identityPoolId = identityPoolId
        self.pushSync = pushSync
    }
}

/// The output for the SetIdentityPoolConfiguration operation
public struct SetIdentityPoolConfigurationOutput: Swift.Sendable {
    /// Options to apply to this identity pool for Amazon Cognito streams.
    public var cognitoStreams: CognitoSyncClientTypes.CognitoStreams?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito.
    public var identityPoolId: Swift.String?
    /// Options to apply to this identity pool for push synchronization.
    public var pushSync: CognitoSyncClientTypes.PushSync?

    public init(
        cognitoStreams: CognitoSyncClientTypes.CognitoStreams? = nil,
        identityPoolId: Swift.String? = nil,
        pushSync: CognitoSyncClientTypes.PushSync? = nil
    )
    {
        self.cognitoStreams = cognitoStreams
        self.identityPoolId = identityPoolId
        self.pushSync = pushSync
    }
}

/// A request to SubscribeToDatasetRequest.
public struct SubscribeToDatasetInput: Swift.Sendable {
    /// The name of the dataset to subcribe to.
    /// This member is required.
    public var datasetName: Swift.String?
    /// The unique ID generated for this device by Cognito.
    /// This member is required.
    public var deviceId: Swift.String?
    /// Unique ID for this identity.
    /// This member is required.
    public var identityId: Swift.String?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. The ID of the pool to which the identity belongs.
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        datasetName: Swift.String? = nil,
        deviceId: Swift.String? = nil,
        identityId: Swift.String? = nil,
        identityPoolId: Swift.String? = nil
    )
    {
        self.datasetName = datasetName
        self.deviceId = deviceId
        self.identityId = identityId
        self.identityPoolId = identityPoolId
    }
}

/// Response to a SubscribeToDataset request.
public struct SubscribeToDatasetOutput: Swift.Sendable {

    public init() { }
}

/// A request to UnsubscribeFromDataset.
public struct UnsubscribeFromDatasetInput: Swift.Sendable {
    /// The name of the dataset from which to unsubcribe.
    /// This member is required.
    public var datasetName: Swift.String?
    /// The unique ID generated for this device by Cognito.
    /// This member is required.
    public var deviceId: Swift.String?
    /// Unique ID for this identity.
    /// This member is required.
    public var identityId: Swift.String?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. The ID of the pool to which this identity belongs.
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        datasetName: Swift.String? = nil,
        deviceId: Swift.String? = nil,
        identityId: Swift.String? = nil,
        identityPoolId: Swift.String? = nil
    )
    {
        self.datasetName = datasetName
        self.deviceId = deviceId
        self.identityId = identityId
        self.identityPoolId = identityPoolId
    }
}

/// Response to an UnsubscribeFromDataset request.
public struct UnsubscribeFromDatasetOutput: Swift.Sendable {

    public init() { }
}

/// The AWS Lambda function returned invalid output or an exception.
public struct InvalidLambdaFunctionOutputException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// A message returned when an InvalidLambdaFunctionOutputException occurs
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidLambdaFunctionOutput" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// AWS Lambda throttled your account, please contact AWS Support
public struct LambdaThrottledException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// A message returned when an LambdaThrottledException is thrown
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LambdaThrottled" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Thrown when the limit on the number of objects or operations has been exceeded.
public struct LimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Message returned by LimitExceededException.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LimitExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CognitoSyncClientTypes {

    public enum Operation: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case remove
        case replace
        case sdkUnknown(Swift.String)

        public static var allCases: [Operation] {
            return [
                .remove,
                .replace
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .remove: return "remove"
            case .replace: return "replace"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CognitoSyncClientTypes {

    /// An update operation for a record.
    public struct RecordPatch: Swift.Sendable {
        /// The last modified date of the client device.
        public var deviceLastModifiedDate: Foundation.Date?
        /// The key associated with the record patch.
        /// This member is required.
        public var key: Swift.String?
        /// An operation, either replace or remove.
        /// This member is required.
        public var op: CognitoSyncClientTypes.Operation?
        /// Last known server sync count for this record. Set to 0 if unknown.
        /// This member is required.
        public var syncCount: Swift.Int?
        /// The value associated with the record patch.
        public var value: Swift.String?

        public init(
            deviceLastModifiedDate: Foundation.Date? = nil,
            key: Swift.String? = nil,
            op: CognitoSyncClientTypes.Operation? = nil,
            syncCount: Swift.Int? = nil,
            value: Swift.String? = nil
        )
        {
            self.deviceLastModifiedDate = deviceLastModifiedDate
            self.key = key
            self.op = op
            self.syncCount = syncCount
            self.value = value
        }
    }
}

/// A request to post updates to records or add and delete records for a dataset and user.
public struct UpdateRecordsInput: Swift.Sendable {
    /// Intended to supply a device ID that will populate the lastModifiedBy field referenced in other methods. The ClientContext field is not yet implemented.
    public var clientContext: Swift.String?
    /// A string of up to 128 characters. Allowed characters are a-z, A-Z, 0-9, '_' (underscore), '-' (dash), and '.' (dot).
    /// This member is required.
    public var datasetName: Swift.String?
    /// The unique ID generated for this device by Cognito.
    public var deviceId: Swift.String?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityId: Swift.String?
    /// A name-spaced GUID (for example, us-east-1:23EC4050-6AEA-7089-A2DD-08002EXAMPLE) created by Amazon Cognito. GUID generation is unique within a region.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// A list of patch operations.
    public var recordPatches: [CognitoSyncClientTypes.RecordPatch]?
    /// The SyncSessionToken returned by a previous call to ListRecords for this dataset and identity.
    /// This member is required.
    public var syncSessionToken: Swift.String?

    public init(
        clientContext: Swift.String? = nil,
        datasetName: Swift.String? = nil,
        deviceId: Swift.String? = nil,
        identityId: Swift.String? = nil,
        identityPoolId: Swift.String? = nil,
        recordPatches: [CognitoSyncClientTypes.RecordPatch]? = nil,
        syncSessionToken: Swift.String? = nil
    )
    {
        self.clientContext = clientContext
        self.datasetName = datasetName
        self.deviceId = deviceId
        self.identityId = identityId
        self.identityPoolId = identityPoolId
        self.recordPatches = recordPatches
        self.syncSessionToken = syncSessionToken
    }
}

/// Returned for a successful UpdateRecordsRequest.
public struct UpdateRecordsOutput: Swift.Sendable {
    /// A list of records that have been updated.
    public var records: [CognitoSyncClientTypes.Record]?

    public init(
        records: [CognitoSyncClientTypes.Record]? = nil
    )
    {
        self.records = records
    }
}

extension BulkPublishInput {

    static func urlPathProvider(_ value: BulkPublishInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/bulkpublish"
    }
}

extension DeleteDatasetInput {

    static func urlPathProvider(_ value: DeleteDatasetInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        guard let identityId = value.identityId else {
            return nil
        }
        guard let datasetName = value.datasetName else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/identities/\(identityId.urlPercentEncoding())/datasets/\(datasetName.urlPercentEncoding())"
    }
}

extension DescribeDatasetInput {

    static func urlPathProvider(_ value: DescribeDatasetInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        guard let identityId = value.identityId else {
            return nil
        }
        guard let datasetName = value.datasetName else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/identities/\(identityId.urlPercentEncoding())/datasets/\(datasetName.urlPercentEncoding())"
    }
}

extension DescribeIdentityPoolUsageInput {

    static func urlPathProvider(_ value: DescribeIdentityPoolUsageInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())"
    }
}

extension DescribeIdentityUsageInput {

    static func urlPathProvider(_ value: DescribeIdentityUsageInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        guard let identityId = value.identityId else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/identities/\(identityId.urlPercentEncoding())"
    }
}

extension GetBulkPublishDetailsInput {

    static func urlPathProvider(_ value: GetBulkPublishDetailsInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/getBulkPublishDetails"
    }
}

extension GetCognitoEventsInput {

    static func urlPathProvider(_ value: GetCognitoEventsInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/events"
    }
}

extension GetIdentityPoolConfigurationInput {

    static func urlPathProvider(_ value: GetIdentityPoolConfigurationInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/configuration"
    }
}

extension ListDatasetsInput {

    static func urlPathProvider(_ value: ListDatasetsInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        guard let identityId = value.identityId else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/identities/\(identityId.urlPercentEncoding())/datasets"
    }
}

extension ListDatasetsInput {

    static func queryItemProvider(_ value: ListDatasetsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListIdentityPoolUsageInput {

    static func urlPathProvider(_ value: ListIdentityPoolUsageInput) -> Swift.String? {
        return "/identitypools"
    }
}

extension ListIdentityPoolUsageInput {

    static func queryItemProvider(_ value: ListIdentityPoolUsageInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListRecordsInput {

    static func urlPathProvider(_ value: ListRecordsInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        guard let identityId = value.identityId else {
            return nil
        }
        guard let datasetName = value.datasetName else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/identities/\(identityId.urlPercentEncoding())/datasets/\(datasetName.urlPercentEncoding())/records"
    }
}

extension ListRecordsInput {

    static func queryItemProvider(_ value: ListRecordsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let syncSessionToken = value.syncSessionToken {
            let syncSessionTokenQueryItem = Smithy.URIQueryItem(name: "syncSessionToken".urlPercentEncoding(), value: Swift.String(syncSessionToken).urlPercentEncoding())
            items.append(syncSessionTokenQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let lastSyncCount = value.lastSyncCount {
            let lastSyncCountQueryItem = Smithy.URIQueryItem(name: "lastSyncCount".urlPercentEncoding(), value: Swift.String(lastSyncCount).urlPercentEncoding())
            items.append(lastSyncCountQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension RegisterDeviceInput {

    static func urlPathProvider(_ value: RegisterDeviceInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        guard let identityId = value.identityId else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/identity/\(identityId.urlPercentEncoding())/device"
    }
}

extension SetCognitoEventsInput {

    static func urlPathProvider(_ value: SetCognitoEventsInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/events"
    }
}

extension SetIdentityPoolConfigurationInput {

    static func urlPathProvider(_ value: SetIdentityPoolConfigurationInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/configuration"
    }
}

extension SubscribeToDatasetInput {

    static func urlPathProvider(_ value: SubscribeToDatasetInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        guard let identityId = value.identityId else {
            return nil
        }
        guard let datasetName = value.datasetName else {
            return nil
        }
        guard let deviceId = value.deviceId else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/identities/\(identityId.urlPercentEncoding())/datasets/\(datasetName.urlPercentEncoding())/subscriptions/\(deviceId.urlPercentEncoding())"
    }
}

extension UnsubscribeFromDatasetInput {

    static func urlPathProvider(_ value: UnsubscribeFromDatasetInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        guard let identityId = value.identityId else {
            return nil
        }
        guard let datasetName = value.datasetName else {
            return nil
        }
        guard let deviceId = value.deviceId else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/identities/\(identityId.urlPercentEncoding())/datasets/\(datasetName.urlPercentEncoding())/subscriptions/\(deviceId.urlPercentEncoding())"
    }
}

extension UpdateRecordsInput {

    static func urlPathProvider(_ value: UpdateRecordsInput) -> Swift.String? {
        guard let identityPoolId = value.identityPoolId else {
            return nil
        }
        guard let identityId = value.identityId else {
            return nil
        }
        guard let datasetName = value.datasetName else {
            return nil
        }
        return "/identitypools/\(identityPoolId.urlPercentEncoding())/identities/\(identityId.urlPercentEncoding())/datasets/\(datasetName.urlPercentEncoding())"
    }
}

extension UpdateRecordsInput {

    static func headerProvider(_ value: UpdateRecordsInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let clientContext = value.clientContext {
            items.add(SmithyHTTPAPI.Header(name: "x-amz-Client-Context", value: Swift.String(clientContext)))
        }
        return items
    }
}

extension RegisterDeviceInput {

    static func write(value: RegisterDeviceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Platform"].write(value.platform)
        try writer["Token"].write(value.token)
    }
}

extension SetCognitoEventsInput {

    static func write(value: SetCognitoEventsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Events"].writeMap(value.events, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension SetIdentityPoolConfigurationInput {

    static func write(value: SetIdentityPoolConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CognitoStreams"].write(value.cognitoStreams, with: CognitoSyncClientTypes.CognitoStreams.write(value:to:))
        try writer["PushSync"].write(value.pushSync, with: CognitoSyncClientTypes.PushSync.write(value:to:))
    }
}

extension UpdateRecordsInput {

    static func write(value: UpdateRecordsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DeviceId"].write(value.deviceId)
        try writer["RecordPatches"].writeList(value.recordPatches, memberWritingClosure: CognitoSyncClientTypes.RecordPatch.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["SyncSessionToken"].write(value.syncSessionToken)
    }
}

extension BulkPublishOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> BulkPublishOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = BulkPublishOutput()
        value.identityPoolId = try reader["IdentityPoolId"].readIfPresent()
        return value
    }
}

extension DeleteDatasetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteDatasetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteDatasetOutput()
        value.dataset = try reader["Dataset"].readIfPresent(with: CognitoSyncClientTypes.Dataset.read(from:))
        return value
    }
}

extension DescribeDatasetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDatasetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeDatasetOutput()
        value.dataset = try reader["Dataset"].readIfPresent(with: CognitoSyncClientTypes.Dataset.read(from:))
        return value
    }
}

extension DescribeIdentityPoolUsageOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeIdentityPoolUsageOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeIdentityPoolUsageOutput()
        value.identityPoolUsage = try reader["IdentityPoolUsage"].readIfPresent(with: CognitoSyncClientTypes.IdentityPoolUsage.read(from:))
        return value
    }
}

extension DescribeIdentityUsageOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeIdentityUsageOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeIdentityUsageOutput()
        value.identityUsage = try reader["IdentityUsage"].readIfPresent(with: CognitoSyncClientTypes.IdentityUsage.read(from:))
        return value
    }
}

extension GetBulkPublishDetailsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetBulkPublishDetailsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetBulkPublishDetailsOutput()
        value.bulkPublishCompleteTime = try reader["BulkPublishCompleteTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.bulkPublishStartTime = try reader["BulkPublishStartTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.bulkPublishStatus = try reader["BulkPublishStatus"].readIfPresent()
        value.failureMessage = try reader["FailureMessage"].readIfPresent()
        value.identityPoolId = try reader["IdentityPoolId"].readIfPresent()
        return value
    }
}

extension GetCognitoEventsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetCognitoEventsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetCognitoEventsOutput()
        value.events = try reader["Events"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension GetIdentityPoolConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetIdentityPoolConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetIdentityPoolConfigurationOutput()
        value.cognitoStreams = try reader["CognitoStreams"].readIfPresent(with: CognitoSyncClientTypes.CognitoStreams.read(from:))
        value.identityPoolId = try reader["IdentityPoolId"].readIfPresent()
        value.pushSync = try reader["PushSync"].readIfPresent(with: CognitoSyncClientTypes.PushSync.read(from:))
        return value
    }
}

extension ListDatasetsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListDatasetsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListDatasetsOutput()
        value.count = try reader["Count"].readIfPresent() ?? 0
        value.datasets = try reader["Datasets"].readListIfPresent(memberReadingClosure: CognitoSyncClientTypes.Dataset.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListIdentityPoolUsageOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListIdentityPoolUsageOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListIdentityPoolUsageOutput()
        value.count = try reader["Count"].readIfPresent() ?? 0
        value.identityPoolUsages = try reader["IdentityPoolUsages"].readListIfPresent(memberReadingClosure: CognitoSyncClientTypes.IdentityPoolUsage.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.maxResults = try reader["MaxResults"].readIfPresent() ?? 0
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListRecordsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRecordsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRecordsOutput()
        value.count = try reader["Count"].readIfPresent() ?? 0
        value.datasetDeletedAfterRequestedSyncCount = try reader["DatasetDeletedAfterRequestedSyncCount"].readIfPresent() ?? false
        value.datasetExists = try reader["DatasetExists"].readIfPresent() ?? false
        value.datasetSyncCount = try reader["DatasetSyncCount"].readIfPresent()
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.mergedDatasetNames = try reader["MergedDatasetNames"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.records = try reader["Records"].readListIfPresent(memberReadingClosure: CognitoSyncClientTypes.Record.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.syncSessionToken = try reader["SyncSessionToken"].readIfPresent()
        return value
    }
}

extension RegisterDeviceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RegisterDeviceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = RegisterDeviceOutput()
        value.deviceId = try reader["DeviceId"].readIfPresent()
        return value
    }
}

extension SetCognitoEventsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SetCognitoEventsOutput {
        return SetCognitoEventsOutput()
    }
}

extension SetIdentityPoolConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SetIdentityPoolConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = SetIdentityPoolConfigurationOutput()
        value.cognitoStreams = try reader["CognitoStreams"].readIfPresent(with: CognitoSyncClientTypes.CognitoStreams.read(from:))
        value.identityPoolId = try reader["IdentityPoolId"].readIfPresent()
        value.pushSync = try reader["PushSync"].readIfPresent(with: CognitoSyncClientTypes.PushSync.read(from:))
        return value
    }
}

extension SubscribeToDatasetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SubscribeToDatasetOutput {
        return SubscribeToDatasetOutput()
    }
}

extension UnsubscribeFromDatasetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UnsubscribeFromDatasetOutput {
        return UnsubscribeFromDatasetOutput()
    }
}

extension UpdateRecordsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateRecordsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateRecordsOutput()
        value.records = try reader["Records"].readListIfPresent(memberReadingClosure: CognitoSyncClientTypes.Record.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

enum BulkPublishOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AlreadyStreamed": return try AlreadyStreamedException.makeError(baseError: baseError)
            case "DuplicateRequest": return try DuplicateRequestException.makeError(baseError: baseError)
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFound": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteDatasetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceConflict": return try ResourceConflictException.makeError(baseError: baseError)
            case "ResourceNotFound": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDatasetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFound": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeIdentityPoolUsageOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFound": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeIdentityUsageOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFound": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetBulkPublishDetailsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFound": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetCognitoEventsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFound": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetIdentityPoolConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFound": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListDatasetsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListIdentityPoolUsageOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRecordsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RegisterDeviceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidConfiguration": return try InvalidConfigurationException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFound": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SetCognitoEventsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFound": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SetIdentityPoolConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConcurrentModification": return try ConcurrentModificationException.makeError(baseError: baseError)
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFound": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SubscribeToDatasetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidConfiguration": return try InvalidConfigurationException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFound": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UnsubscribeFromDatasetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidConfiguration": return try InvalidConfigurationException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFound": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateRecordsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalError": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidLambdaFunctionOutput": return try InvalidLambdaFunctionOutputException.makeError(baseError: baseError)
            case "InvalidParameter": return try InvalidParameterException.makeError(baseError: baseError)
            case "LambdaThrottled": return try LambdaThrottledException.makeError(baseError: baseError)
            case "LimitExceeded": return try LimitExceededException.makeError(baseError: baseError)
            case "NotAuthorizedError": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceConflict": return try ResourceConflictException.makeError(baseError: baseError)
            case "ResourceNotFound": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequests": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DuplicateRequestException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> DuplicateRequestException {
        let reader = baseError.errorBodyReader
        var value = DuplicateRequestException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension NotAuthorizedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> NotAuthorizedException {
        let reader = baseError.errorBodyReader
        var value = NotAuthorizedException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AlreadyStreamedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AlreadyStreamedException {
        let reader = baseError.errorBodyReader
        var value = AlreadyStreamedException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidParameterException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InvalidParameterException {
        let reader = baseError.errorBodyReader
        var value = InvalidParameterException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalErrorException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalErrorException {
        let reader = baseError.errorBodyReader
        var value = InternalErrorException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TooManyRequestsException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> TooManyRequestsException {
        let reader = baseError.errorBodyReader
        var value = TooManyRequestsException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceConflictException {
        let reader = baseError.errorBodyReader
        var value = ResourceConflictException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidConfigurationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InvalidConfigurationException {
        let reader = baseError.errorBodyReader
        var value = InvalidConfigurationException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConcurrentModificationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConcurrentModificationException {
        let reader = baseError.errorBodyReader
        var value = ConcurrentModificationException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LambdaThrottledException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> LambdaThrottledException {
        let reader = baseError.errorBodyReader
        var value = LambdaThrottledException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LimitExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> LimitExceededException {
        let reader = baseError.errorBodyReader
        var value = LimitExceededException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidLambdaFunctionOutputException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InvalidLambdaFunctionOutputException {
        let reader = baseError.errorBodyReader
        var value = InvalidLambdaFunctionOutputException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CognitoSyncClientTypes.Dataset {

    static func read(from reader: SmithyJSON.Reader) throws -> CognitoSyncClientTypes.Dataset {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CognitoSyncClientTypes.Dataset()
        value.identityId = try reader["IdentityId"].readIfPresent()
        value.datasetName = try reader["DatasetName"].readIfPresent()
        value.creationDate = try reader["CreationDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.dataStorage = try reader["DataStorage"].readIfPresent()
        value.numRecords = try reader["NumRecords"].readIfPresent()
        return value
    }
}

extension CognitoSyncClientTypes.IdentityPoolUsage {

    static func read(from reader: SmithyJSON.Reader) throws -> CognitoSyncClientTypes.IdentityPoolUsage {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CognitoSyncClientTypes.IdentityPoolUsage()
        value.identityPoolId = try reader["IdentityPoolId"].readIfPresent()
        value.syncSessionsCount = try reader["SyncSessionsCount"].readIfPresent()
        value.dataStorage = try reader["DataStorage"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension CognitoSyncClientTypes.IdentityUsage {

    static func read(from reader: SmithyJSON.Reader) throws -> CognitoSyncClientTypes.IdentityUsage {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CognitoSyncClientTypes.IdentityUsage()
        value.identityId = try reader["IdentityId"].readIfPresent()
        value.identityPoolId = try reader["IdentityPoolId"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.datasetCount = try reader["DatasetCount"].readIfPresent() ?? 0
        value.dataStorage = try reader["DataStorage"].readIfPresent()
        return value
    }
}

extension CognitoSyncClientTypes.PushSync {

    static func write(value: CognitoSyncClientTypes.PushSync?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ApplicationArns"].writeList(value.applicationArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["RoleArn"].write(value.roleArn)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CognitoSyncClientTypes.PushSync {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CognitoSyncClientTypes.PushSync()
        value.applicationArns = try reader["ApplicationArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.roleArn = try reader["RoleArn"].readIfPresent()
        return value
    }
}

extension CognitoSyncClientTypes.CognitoStreams {

    static func write(value: CognitoSyncClientTypes.CognitoStreams?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["RoleArn"].write(value.roleArn)
        try writer["StreamName"].write(value.streamName)
        try writer["StreamingStatus"].write(value.streamingStatus)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CognitoSyncClientTypes.CognitoStreams {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CognitoSyncClientTypes.CognitoStreams()
        value.streamName = try reader["StreamName"].readIfPresent()
        value.roleArn = try reader["RoleArn"].readIfPresent()
        value.streamingStatus = try reader["StreamingStatus"].readIfPresent()
        return value
    }
}

extension CognitoSyncClientTypes.Record {

    static func read(from reader: SmithyJSON.Reader) throws -> CognitoSyncClientTypes.Record {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CognitoSyncClientTypes.Record()
        value.key = try reader["Key"].readIfPresent()
        value.value = try reader["Value"].readIfPresent()
        value.syncCount = try reader["SyncCount"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.deviceLastModifiedDate = try reader["DeviceLastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension CognitoSyncClientTypes.RecordPatch {

    static func write(value: CognitoSyncClientTypes.RecordPatch?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DeviceLastModifiedDate"].writeTimestamp(value.deviceLastModifiedDate, format: SmithyTimestamps.TimestampFormat.epochSeconds)
        try writer["Key"].write(value.key)
        try writer["Op"].write(value.op)
        try writer["SyncCount"].write(value.syncCount)
        try writer["Value"].write(value.value)
    }
}

public enum CognitoSyncClientTypes {}
