//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import class Smithy<PERSON><PERSON><PERSON>I.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError

/// You do not have sufficient access to perform this action.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Request processing failed due to an error or failure with the service.
public struct InternalServiceException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServiceException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request is not valid.
public struct InvalidRequestException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidRequestException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified resource was not found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The throttling limit has been exceeded.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct ListRealtimeContactAnalysisSegmentsInput: Swift.Sendable {
    /// The identifier of the contact.
    /// This member is required.
    public var contactId: Swift.String?
    /// The identifier of the Amazon Connect instance.
    /// This member is required.
    public var instanceId: Swift.String?
    /// The maximum number of results to return per page.
    public var maxResults: Swift.Int?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        contactId: Swift.String? = nil,
        instanceId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.contactId = contactId
        self.instanceId = instanceId
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension ConnectContactLensClientTypes {

    /// The section of the contact audio where that category rule was detected.
    public struct PointOfInterest: Swift.Sendable {
        /// The beginning offset in milliseconds where the category rule was detected.
        /// This member is required.
        public var beginOffsetMillis: Swift.Int?
        /// The ending offset in milliseconds where the category rule was detected.
        /// This member is required.
        public var endOffsetMillis: Swift.Int?

        public init(
            beginOffsetMillis: Swift.Int? = nil,
            endOffsetMillis: Swift.Int? = nil
        )
        {
            self.beginOffsetMillis = beginOffsetMillis
            self.endOffsetMillis = endOffsetMillis
        }
    }
}

extension ConnectContactLensClientTypes {

    /// Provides information about the category rule that was matched.
    public struct CategoryDetails: Swift.Sendable {
        /// The section of audio where the category rule was detected.
        /// This member is required.
        public var pointsOfInterest: [ConnectContactLensClientTypes.PointOfInterest]?

        public init(
            pointsOfInterest: [ConnectContactLensClientTypes.PointOfInterest]? = nil
        )
        {
            self.pointsOfInterest = pointsOfInterest
        }
    }
}

extension ConnectContactLensClientTypes {

    /// Provides the category rules that are used to automatically categorize contacts based on uttered keywords and phrases.
    public struct Categories: Swift.Sendable {
        /// The category rules that have been matched in the analyzed segment.
        /// This member is required.
        public var matchedCategories: [Swift.String]?
        /// The category rule that was matched and when it occurred in the transcript.
        /// This member is required.
        public var matchedDetails: [Swift.String: ConnectContactLensClientTypes.CategoryDetails]?

        public init(
            matchedCategories: [Swift.String]? = nil,
            matchedDetails: [Swift.String: ConnectContactLensClientTypes.CategoryDetails]? = nil
        )
        {
            self.matchedCategories = matchedCategories
            self.matchedDetails = matchedDetails
        }
    }
}

extension ConnectContactLensClientTypes {

    public enum PostContactSummaryFailureCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failedSafetyGuidelines
        case insufficientConversationContent
        case internalError
        case invalidAnalysisConfiguration
        case quotaExceeded
        case sdkUnknown(Swift.String)

        public static var allCases: [PostContactSummaryFailureCode] {
            return [
                .failedSafetyGuidelines,
                .insufficientConversationContent,
                .internalError,
                .invalidAnalysisConfiguration,
                .quotaExceeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failedSafetyGuidelines: return "FAILED_SAFETY_GUIDELINES"
            case .insufficientConversationContent: return "INSUFFICIENT_CONVERSATION_CONTENT"
            case .internalError: return "INTERNAL_ERROR"
            case .invalidAnalysisConfiguration: return "INVALID_ANALYSIS_CONFIGURATION"
            case .quotaExceeded: return "QUOTA_EXCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectContactLensClientTypes {

    public enum PostContactSummaryStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case completed
        case failed
        case sdkUnknown(Swift.String)

        public static var allCases: [PostContactSummaryStatus] {
            return [
                .completed,
                .failed
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .completed: return "COMPLETED"
            case .failed: return "FAILED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectContactLensClientTypes {

    /// Information about the post-contact summary.
    public struct PostContactSummary: Swift.Sendable {
        /// The content of the summary.
        public var content: Swift.String?
        /// If the summary failed to be generated, one of the following failure codes occurs:
        ///
        /// * QUOTA_EXCEEDED: The number of concurrent analytics jobs reached your service quota.
        ///
        /// * INSUFFICIENT_CONVERSATION_CONTENT: The conversation needs to have at least one turn from both the participants in order to generate the summary.
        ///
        /// * FAILED_SAFETY_GUIDELINES: The generated summary cannot be provided because it failed to meet system safety guidelines.
        ///
        /// * INVALID_ANALYSIS_CONFIGURATION: This code occurs when, for example, you're using a [language](https://docs.aws.amazon.com/connect/latest/adminguide/supported-languages.html#supported-languages-contact-lens) that isn't supported by generative AI-powered post-contact summaries.
        ///
        /// * INTERNAL_ERROR: Internal system error.
        public var failureCode: ConnectContactLensClientTypes.PostContactSummaryFailureCode?
        /// Whether the summary was successfully COMPLETED or FAILED to be generated.
        /// This member is required.
        public var status: ConnectContactLensClientTypes.PostContactSummaryStatus?

        public init(
            content: Swift.String? = nil,
            failureCode: ConnectContactLensClientTypes.PostContactSummaryFailureCode? = nil,
            status: ConnectContactLensClientTypes.PostContactSummaryStatus? = nil
        )
        {
            self.content = content
            self.failureCode = failureCode
            self.status = status
        }
    }
}

extension ConnectContactLensClientTypes {

    /// For characters that were detected as issues, where they occur in the transcript.
    public struct CharacterOffsets: Swift.Sendable {
        /// The beginning of the issue.
        /// This member is required.
        public var beginOffsetChar: Swift.Int?
        /// The end of the issue.
        /// This member is required.
        public var endOffsetChar: Swift.Int?

        public init(
            beginOffsetChar: Swift.Int? = nil,
            endOffsetChar: Swift.Int? = nil
        )
        {
            self.beginOffsetChar = beginOffsetChar
            self.endOffsetChar = endOffsetChar
        }
    }
}

extension ConnectContactLensClientTypes {

    /// Potential issues that are detected based on an artificial intelligence analysis of each turn in the conversation.
    public struct IssueDetected: Swift.Sendable {
        /// The offset for when the issue was detected in the segment.
        /// This member is required.
        public var characterOffsets: ConnectContactLensClientTypes.CharacterOffsets?

        public init(
            characterOffsets: ConnectContactLensClientTypes.CharacterOffsets? = nil
        )
        {
            self.characterOffsets = characterOffsets
        }
    }
}

extension ConnectContactLensClientTypes {

    public enum SentimentValue: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case negative
        case neutral
        case positive
        case sdkUnknown(Swift.String)

        public static var allCases: [SentimentValue] {
            return [
                .negative,
                .neutral,
                .positive
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .negative: return "NEGATIVE"
            case .neutral: return "NEUTRAL"
            case .positive: return "POSITIVE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectContactLensClientTypes {

    /// A list of messages in the session.
    public struct Transcript: Swift.Sendable {
        /// The beginning offset in the contact for this transcript.
        /// This member is required.
        public var beginOffsetMillis: Swift.Int?
        /// The content of the transcript.
        /// This member is required.
        public var content: Swift.String?
        /// The end offset in the contact for this transcript.
        /// This member is required.
        public var endOffsetMillis: Swift.Int?
        /// The identifier of the transcript.
        /// This member is required.
        public var id: Swift.String?
        /// List of positions where issues were detected on the transcript.
        public var issuesDetected: [ConnectContactLensClientTypes.IssueDetected]?
        /// The identifier of the participant. Valid values are CUSTOMER or AGENT.
        /// This member is required.
        public var participantId: Swift.String?
        /// The role of participant. For example, is it a customer, agent, or system.
        /// This member is required.
        public var participantRole: Swift.String?
        /// The sentiment detected for this piece of transcript.
        /// This member is required.
        public var sentiment: ConnectContactLensClientTypes.SentimentValue?

        public init(
            beginOffsetMillis: Swift.Int? = nil,
            content: Swift.String? = nil,
            endOffsetMillis: Swift.Int? = nil,
            id: Swift.String? = nil,
            issuesDetected: [ConnectContactLensClientTypes.IssueDetected]? = nil,
            participantId: Swift.String? = nil,
            participantRole: Swift.String? = nil,
            sentiment: ConnectContactLensClientTypes.SentimentValue? = nil
        )
        {
            self.beginOffsetMillis = beginOffsetMillis
            self.content = content
            self.endOffsetMillis = endOffsetMillis
            self.id = id
            self.issuesDetected = issuesDetected
            self.participantId = participantId
            self.participantRole = participantRole
            self.sentiment = sentiment
        }
    }
}

extension ConnectContactLensClientTypes {

    /// An analyzed segment for a real-time analysis session.
    public struct RealtimeContactAnalysisSegment: Swift.Sendable {
        /// The matched category rules.
        public var categories: ConnectContactLensClientTypes.Categories?
        /// Information about the post-contact summary.
        public var postContactSummary: ConnectContactLensClientTypes.PostContactSummary?
        /// The analyzed transcript.
        public var transcript: ConnectContactLensClientTypes.Transcript?

        public init(
            categories: ConnectContactLensClientTypes.Categories? = nil,
            postContactSummary: ConnectContactLensClientTypes.PostContactSummary? = nil,
            transcript: ConnectContactLensClientTypes.Transcript? = nil
        )
        {
            self.categories = categories
            self.postContactSummary = postContactSummary
            self.transcript = transcript
        }
    }
}

public struct ListRealtimeContactAnalysisSegmentsOutput: Swift.Sendable {
    /// If there are additional results, this is the token for the next set of results. If response includes nextToken there are two possible scenarios:
    ///
    /// * There are more segments so another call is required to get them.
    ///
    /// * There are no more segments at this time, but more may be available later (real-time analysis is in progress) so the client should call the operation again to get new segments.
    ///
    ///
    /// If response does not include nextToken, the analysis is completed (successfully or failed) and there are no more segments to retrieve.
    public var nextToken: Swift.String?
    /// An analyzed transcript or category.
    /// This member is required.
    public var segments: [ConnectContactLensClientTypes.RealtimeContactAnalysisSegment]?

    public init(
        nextToken: Swift.String? = nil,
        segments: [ConnectContactLensClientTypes.RealtimeContactAnalysisSegment]? = nil
    )
    {
        self.nextToken = nextToken
        self.segments = segments
    }
}

extension ListRealtimeContactAnalysisSegmentsInput {

    static func urlPathProvider(_ value: ListRealtimeContactAnalysisSegmentsInput) -> Swift.String? {
        return "/realtime-contact-analysis/analysis-segments"
    }
}

extension ListRealtimeContactAnalysisSegmentsInput {

    static func write(value: ListRealtimeContactAnalysisSegmentsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ContactId"].write(value.contactId)
        try writer["InstanceId"].write(value.instanceId)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListRealtimeContactAnalysisSegmentsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRealtimeContactAnalysisSegmentsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRealtimeContactAnalysisSegmentsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.segments = try reader["Segments"].readListIfPresent(memberReadingClosure: ConnectContactLensClientTypes.RealtimeContactAnalysisSegment.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

enum ListRealtimeContactAnalysisSegmentsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServiceException": return try InternalServiceException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension InternalServiceException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServiceException {
        let reader = baseError.errorBodyReader
        var value = InternalServiceException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidRequestException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InvalidRequestException {
        let reader = baseError.errorBodyReader
        var value = InvalidRequestException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        var value = ThrottlingException()
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConnectContactLensClientTypes.RealtimeContactAnalysisSegment {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectContactLensClientTypes.RealtimeContactAnalysisSegment {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectContactLensClientTypes.RealtimeContactAnalysisSegment()
        value.transcript = try reader["Transcript"].readIfPresent(with: ConnectContactLensClientTypes.Transcript.read(from:))
        value.categories = try reader["Categories"].readIfPresent(with: ConnectContactLensClientTypes.Categories.read(from:))
        value.postContactSummary = try reader["PostContactSummary"].readIfPresent(with: ConnectContactLensClientTypes.PostContactSummary.read(from:))
        return value
    }
}

extension ConnectContactLensClientTypes.PostContactSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectContactLensClientTypes.PostContactSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectContactLensClientTypes.PostContactSummary()
        value.content = try reader["Content"].readIfPresent()
        value.status = try reader["Status"].readIfPresent() ?? .sdkUnknown("")
        value.failureCode = try reader["FailureCode"].readIfPresent()
        return value
    }
}

extension ConnectContactLensClientTypes.Categories {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectContactLensClientTypes.Categories {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectContactLensClientTypes.Categories()
        value.matchedCategories = try reader["MatchedCategories"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.matchedDetails = try reader["MatchedDetails"].readMapIfPresent(valueReadingClosure: ConnectContactLensClientTypes.CategoryDetails.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false) ?? [:]
        return value
    }
}

extension ConnectContactLensClientTypes.CategoryDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectContactLensClientTypes.CategoryDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectContactLensClientTypes.CategoryDetails()
        value.pointsOfInterest = try reader["PointsOfInterest"].readListIfPresent(memberReadingClosure: ConnectContactLensClientTypes.PointOfInterest.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ConnectContactLensClientTypes.PointOfInterest {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectContactLensClientTypes.PointOfInterest {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectContactLensClientTypes.PointOfInterest()
        value.beginOffsetMillis = try reader["BeginOffsetMillis"].readIfPresent() ?? 0
        value.endOffsetMillis = try reader["EndOffsetMillis"].readIfPresent() ?? 0
        return value
    }
}

extension ConnectContactLensClientTypes.Transcript {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectContactLensClientTypes.Transcript {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectContactLensClientTypes.Transcript()
        value.id = try reader["Id"].readIfPresent() ?? ""
        value.participantId = try reader["ParticipantId"].readIfPresent() ?? ""
        value.participantRole = try reader["ParticipantRole"].readIfPresent() ?? ""
        value.content = try reader["Content"].readIfPresent() ?? ""
        value.beginOffsetMillis = try reader["BeginOffsetMillis"].readIfPresent() ?? 0
        value.endOffsetMillis = try reader["EndOffsetMillis"].readIfPresent() ?? 0
        value.sentiment = try reader["Sentiment"].readIfPresent() ?? .sdkUnknown("")
        value.issuesDetected = try reader["IssuesDetected"].readListIfPresent(memberReadingClosure: ConnectContactLensClientTypes.IssueDetected.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ConnectContactLensClientTypes.IssueDetected {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectContactLensClientTypes.IssueDetected {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectContactLensClientTypes.IssueDetected()
        value.characterOffsets = try reader["CharacterOffsets"].readIfPresent(with: ConnectContactLensClientTypes.CharacterOffsets.read(from:))
        return value
    }
}

extension ConnectContactLensClientTypes.CharacterOffsets {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectContactLensClientTypes.CharacterOffsets {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectContactLensClientTypes.CharacterOffsets()
        value.beginOffsetChar = try reader["BeginOffsetChar"].readIfPresent() ?? 0
        value.endOffsetChar = try reader["EndOffsetChar"].readIfPresent() ?? 0
        return value
    }
}

public enum ConnectContactLensClientTypes {}
