//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
@_spi(SmithyReadWrite) import class SmithyFormURL.Writer
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyXML.Reader
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.AWSQueryError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
@_spi(SmithyReadWrite) import struct SmithyReadWrite.WritingClosureBox


public struct AbortEnvironmentUpdateOutput: Swift.Sendable {

    public init() { }
}

public struct AssociateEnvironmentOperationsRoleOutput: Swift.Sendable {

    public init() { }
}

public struct CreateStorageLocationInput: Swift.Sendable {

    public init() { }
}

public struct DeleteApplicationOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteApplicationVersionOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteConfigurationTemplateOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteEnvironmentConfigurationOutput: Swift.Sendable {

    public init() { }
}

public struct DescribeAccountAttributesInput: Swift.Sendable {

    public init() { }
}

public struct DisassociateEnvironmentOperationsRoleOutput: Swift.Sendable {

    public init() { }
}

public struct ListAvailableSolutionStacksInput: Swift.Sendable {

    public init() { }
}

public struct RebuildEnvironmentOutput: Swift.Sendable {

    public init() { }
}

public struct RequestEnvironmentInfoOutput: Swift.Sendable {

    public init() { }
}

public struct RestartAppServerOutput: Swift.Sendable {

    public init() { }
}

public struct SwapEnvironmentCNAMEsOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateTagsForResourceOutput: Swift.Sendable {

    public init() { }
}

/// The specified account does not have sufficient privileges for one or more AWS services.
public struct InsufficientPrivilegesException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InsufficientPrivilegesException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

///
public struct AbortEnvironmentUpdateInput: Swift.Sendable {
    /// This specifies the ID of the environment with the in-progress update that you want to cancel.
    public var environmentId: Swift.String?
    /// This specifies the name of the environment with the in-progress update that you want to cancel.
    public var environmentName: Swift.String?

    public init(
        environmentId: Swift.String? = nil,
        environmentName: Swift.String? = nil
    )
    {
        self.environmentId = environmentId
        self.environmentName = environmentName
    }
}

extension ElasticBeanstalkClientTypes {

    public enum ActionHistoryStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case completed
        case failed
        case unknown
        case sdkUnknown(Swift.String)

        public static var allCases: [ActionHistoryStatus] {
            return [
                .completed,
                .failed,
                .unknown
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .completed: return "Completed"
            case .failed: return "Failed"
            case .unknown: return "Unknown"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticBeanstalkClientTypes {

    public enum ActionStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case pending
        case running
        case scheduled
        case unknown
        case sdkUnknown(Swift.String)

        public static var allCases: [ActionStatus] {
            return [
                .pending,
                .running,
                .scheduled,
                .unknown
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .pending: return "Pending"
            case .running: return "Running"
            case .scheduled: return "Scheduled"
            case .unknown: return "Unknown"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticBeanstalkClientTypes {

    public enum ActionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case instancerefresh
        case platformupdate
        case unknown
        case sdkUnknown(Swift.String)

        public static var allCases: [ActionType] {
            return [
                .instancerefresh,
                .platformupdate,
                .unknown
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .instancerefresh: return "InstanceRefresh"
            case .platformupdate: return "PlatformUpdate"
            case .unknown: return "Unknown"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// A lifecycle rule that deletes application versions after the specified number of days.
    public struct MaxAgeRule: Swift.Sendable {
        /// Set to true to delete a version's source bundle from Amazon S3 when Elastic Beanstalk deletes the application version.
        public var deleteSourceFromS3: Swift.Bool?
        /// Specify true to apply the rule, or false to disable it.
        /// This member is required.
        public var enabled: Swift.Bool?
        /// Specify the number of days to retain an application versions.
        public var maxAgeInDays: Swift.Int?

        public init(
            deleteSourceFromS3: Swift.Bool? = nil,
            enabled: Swift.Bool? = nil,
            maxAgeInDays: Swift.Int? = nil
        )
        {
            self.deleteSourceFromS3 = deleteSourceFromS3
            self.enabled = enabled
            self.maxAgeInDays = maxAgeInDays
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// A lifecycle rule that deletes the oldest application version when the maximum count is exceeded.
    public struct MaxCountRule: Swift.Sendable {
        /// Set to true to delete a version's source bundle from Amazon S3 when Elastic Beanstalk deletes the application version.
        public var deleteSourceFromS3: Swift.Bool?
        /// Specify true to apply the rule, or false to disable it.
        /// This member is required.
        public var enabled: Swift.Bool?
        /// Specify the maximum number of application versions to retain.
        public var maxCount: Swift.Int?

        public init(
            deleteSourceFromS3: Swift.Bool? = nil,
            enabled: Swift.Bool? = nil,
            maxCount: Swift.Int? = nil
        )
        {
            self.deleteSourceFromS3 = deleteSourceFromS3
            self.enabled = enabled
            self.maxCount = maxCount
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// The application version lifecycle settings for an application. Defines the rules that Elastic Beanstalk applies to an application's versions in order to avoid hitting the per-region limit for application versions. When Elastic Beanstalk deletes an application version from its database, you can no longer deploy that version to an environment. The source bundle remains in S3 unless you configure the rule to delete it.
    public struct ApplicationVersionLifecycleConfig: Swift.Sendable {
        /// Specify a max age rule to restrict the length of time that application versions are retained for an application.
        public var maxAgeRule: ElasticBeanstalkClientTypes.MaxAgeRule?
        /// Specify a max count rule to restrict the number of application versions that are retained for an application.
        public var maxCountRule: ElasticBeanstalkClientTypes.MaxCountRule?

        public init(
            maxAgeRule: ElasticBeanstalkClientTypes.MaxAgeRule? = nil,
            maxCountRule: ElasticBeanstalkClientTypes.MaxCountRule? = nil
        )
        {
            self.maxAgeRule = maxAgeRule
            self.maxCountRule = maxCountRule
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// The resource lifecycle configuration for an application. Defines lifecycle settings for resources that belong to the application, and the service role that AWS Elastic Beanstalk assumes in order to apply lifecycle settings. The version lifecycle configuration defines lifecycle settings for application versions.
    public struct ApplicationResourceLifecycleConfig: Swift.Sendable {
        /// The ARN of an IAM service role that Elastic Beanstalk has permission to assume. The ServiceRole property is required the first time that you provide a VersionLifecycleConfig for the application in one of the supporting calls (CreateApplication or UpdateApplicationResourceLifecycle). After you provide it once, in either one of the calls, Elastic Beanstalk persists the Service Role with the application, and you don't need to specify it again in subsequent UpdateApplicationResourceLifecycle calls. You can, however, specify it in subsequent calls to change the Service Role to another value.
        public var serviceRole: Swift.String?
        /// Defines lifecycle settings for application versions.
        public var versionLifecycleConfig: ElasticBeanstalkClientTypes.ApplicationVersionLifecycleConfig?

        public init(
            serviceRole: Swift.String? = nil,
            versionLifecycleConfig: ElasticBeanstalkClientTypes.ApplicationVersionLifecycleConfig? = nil
        )
        {
            self.serviceRole = serviceRole
            self.versionLifecycleConfig = versionLifecycleConfig
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes the properties of an application.
    public struct ApplicationDescription: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the application.
        public var applicationArn: Swift.String?
        /// The name of the application.
        public var applicationName: Swift.String?
        /// The names of the configuration templates associated with this application.
        public var configurationTemplates: [Swift.String]?
        /// The date when the application was created.
        public var dateCreated: Foundation.Date?
        /// The date when the application was last modified.
        public var dateUpdated: Foundation.Date?
        /// User-defined description of the application.
        public var description: Swift.String?
        /// The lifecycle settings for the application.
        public var resourceLifecycleConfig: ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig?
        /// The names of the versions for this application.
        public var versions: [Swift.String]?

        public init(
            applicationArn: Swift.String? = nil,
            applicationName: Swift.String? = nil,
            configurationTemplates: [Swift.String]? = nil,
            dateCreated: Foundation.Date? = nil,
            dateUpdated: Foundation.Date? = nil,
            description: Swift.String? = nil,
            resourceLifecycleConfig: ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig? = nil,
            versions: [Swift.String]? = nil
        )
        {
            self.applicationArn = applicationArn
            self.applicationName = applicationName
            self.configurationTemplates = configurationTemplates
            self.dateCreated = dateCreated
            self.dateUpdated = dateUpdated
            self.description = description
            self.resourceLifecycleConfig = resourceLifecycleConfig
            self.versions = versions
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Represents the average latency for the slowest X percent of requests over the last 10 seconds.
    public struct Latency: Swift.Sendable {
        /// The average latency for the slowest 90 percent of requests over the last 10 seconds.
        public var p10: Swift.Double?
        /// The average latency for the slowest 50 percent of requests over the last 10 seconds.
        public var p50: Swift.Double?
        /// The average latency for the slowest 25 percent of requests over the last 10 seconds.
        public var p75: Swift.Double?
        /// The average latency for the slowest 15 percent of requests over the last 10 seconds.
        public var p85: Swift.Double?
        /// The average latency for the slowest 10 percent of requests over the last 10 seconds.
        public var p90: Swift.Double?
        /// The average latency for the slowest 5 percent of requests over the last 10 seconds.
        public var p95: Swift.Double?
        /// The average latency for the slowest 1 percent of requests over the last 10 seconds.
        public var p99: Swift.Double?
        /// The average latency for the slowest 0.1 percent of requests over the last 10 seconds.
        public var p999: Swift.Double?

        public init(
            p10: Swift.Double? = nil,
            p50: Swift.Double? = nil,
            p75: Swift.Double? = nil,
            p85: Swift.Double? = nil,
            p90: Swift.Double? = nil,
            p95: Swift.Double? = nil,
            p99: Swift.Double? = nil,
            p999: Swift.Double? = nil
        )
        {
            self.p10 = p10
            self.p50 = p50
            self.p75 = p75
            self.p85 = p85
            self.p90 = p90
            self.p95 = p95
            self.p99 = p99
            self.p999 = p999
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Represents the percentage of requests over the last 10 seconds that resulted in each type of status code response. For more information, see [Status Code Definitions](http://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html).
    public struct StatusCodes: Swift.Sendable {
        /// The percentage of requests over the last 10 seconds that resulted in a 2xx (200, 201, etc.) status code.
        public var status2xx: Swift.Int?
        /// The percentage of requests over the last 10 seconds that resulted in a 3xx (300, 301, etc.) status code.
        public var status3xx: Swift.Int?
        /// The percentage of requests over the last 10 seconds that resulted in a 4xx (400, 401, etc.) status code.
        public var status4xx: Swift.Int?
        /// The percentage of requests over the last 10 seconds that resulted in a 5xx (500, 501, etc.) status code.
        public var status5xx: Swift.Int?

        public init(
            status2xx: Swift.Int? = nil,
            status3xx: Swift.Int? = nil,
            status4xx: Swift.Int? = nil,
            status5xx: Swift.Int? = nil
        )
        {
            self.status2xx = status2xx
            self.status3xx = status3xx
            self.status4xx = status4xx
            self.status5xx = status5xx
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Application request metrics for an AWS Elastic Beanstalk environment.
    public struct ApplicationMetrics: Swift.Sendable {
        /// The amount of time that the metrics cover (usually 10 seconds). For example, you might have 5 requests (request_count) within the most recent time slice of 10 seconds (duration).
        public var duration: Swift.Int?
        /// Represents the average latency for the slowest X percent of requests over the last 10 seconds. Latencies are in seconds with one millisecond resolution.
        public var latency: ElasticBeanstalkClientTypes.Latency?
        /// Average number of requests handled by the web server per second over the last 10 seconds.
        public var requestCount: Swift.Int
        /// Represents the percentage of requests over the last 10 seconds that resulted in each type of status code response.
        public var statusCodes: ElasticBeanstalkClientTypes.StatusCodes?

        public init(
            duration: Swift.Int? = nil,
            latency: ElasticBeanstalkClientTypes.Latency? = nil,
            requestCount: Swift.Int = 0,
            statusCodes: ElasticBeanstalkClientTypes.StatusCodes? = nil
        )
        {
            self.duration = duration
            self.latency = latency
            self.requestCount = requestCount
            self.statusCodes = statusCodes
        }
    }
}

extension ElasticBeanstalkClientTypes {

    public enum SourceRepository: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case codecommit
        case s3
        case sdkUnknown(Swift.String)

        public static var allCases: [SourceRepository] {
            return [
                .codecommit,
                .s3
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .codecommit: return "CodeCommit"
            case .s3: return "S3"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticBeanstalkClientTypes {

    public enum SourceType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case git
        case zip
        case sdkUnknown(Swift.String)

        public static var allCases: [SourceType] {
            return [
                .git,
                .zip
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .git: return "Git"
            case .zip: return "Zip"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Location of the source code for an application version.
    public struct SourceBuildInformation: Swift.Sendable {
        /// The location of the source code, as a formatted string, depending on the value of SourceRepository
        ///
        /// * For CodeCommit, the format is the repository name and commit ID, separated by a forward slash. For example, my-git-repo/265cfa0cf6af46153527f55d6503ec030551f57a.
        ///
        /// * For S3, the format is the S3 bucket name and object key, separated by a forward slash. For example, my-s3-bucket/Folders/my-source-file.
        /// This member is required.
        public var sourceLocation: Swift.String?
        /// Location where the repository is stored.
        ///
        /// * CodeCommit
        ///
        /// * S3
        /// This member is required.
        public var sourceRepository: ElasticBeanstalkClientTypes.SourceRepository?
        /// The type of repository.
        ///
        /// * Git
        ///
        /// * Zip
        /// This member is required.
        public var sourceType: ElasticBeanstalkClientTypes.SourceType?

        public init(
            sourceLocation: Swift.String? = nil,
            sourceRepository: ElasticBeanstalkClientTypes.SourceRepository? = nil,
            sourceType: ElasticBeanstalkClientTypes.SourceType? = nil
        )
        {
            self.sourceLocation = sourceLocation
            self.sourceRepository = sourceRepository
            self.sourceType = sourceType
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// The bucket and key of an item stored in Amazon S3.
    public struct S3Location: Swift.Sendable {
        /// The Amazon S3 bucket where the data is located.
        public var s3Bucket: Swift.String?
        /// The Amazon S3 key where the data is located.
        public var s3Key: Swift.String?

        public init(
            s3Bucket: Swift.String? = nil,
            s3Key: Swift.String? = nil
        )
        {
            self.s3Bucket = s3Bucket
            self.s3Key = s3Key
        }
    }
}

extension ElasticBeanstalkClientTypes {

    public enum ApplicationVersionStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case building
        case failed
        case processed
        case processing
        case unprocessed
        case sdkUnknown(Swift.String)

        public static var allCases: [ApplicationVersionStatus] {
            return [
                .building,
                .failed,
                .processed,
                .processing,
                .unprocessed
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .building: return "Building"
            case .failed: return "Failed"
            case .processed: return "Processed"
            case .processing: return "Processing"
            case .unprocessed: return "Unprocessed"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes the properties of an application version.
    public struct ApplicationVersionDescription: Swift.Sendable {
        /// The name of the application to which the application version belongs.
        public var applicationName: Swift.String?
        /// The Amazon Resource Name (ARN) of the application version.
        public var applicationVersionArn: Swift.String?
        /// Reference to the artifact from the AWS CodeBuild build.
        public var buildArn: Swift.String?
        /// The creation date of the application version.
        public var dateCreated: Foundation.Date?
        /// The last modified date of the application version.
        public var dateUpdated: Foundation.Date?
        /// The description of the application version.
        public var description: Swift.String?
        /// If the version's source code was retrieved from AWS CodeCommit, the location of the source code for the application version.
        public var sourceBuildInformation: ElasticBeanstalkClientTypes.SourceBuildInformation?
        /// The storage location of the application version's source bundle in Amazon S3.
        public var sourceBundle: ElasticBeanstalkClientTypes.S3Location?
        /// The processing status of the application version. Reflects the state of the application version during its creation. Many of the values are only applicable if you specified True for the Process parameter of the CreateApplicationVersion action. The following list describes the possible values.
        ///
        /// * Unprocessed – Application version wasn't pre-processed or validated. Elastic Beanstalk will validate configuration files during deployment of the application version to an environment.
        ///
        /// * Processing – Elastic Beanstalk is currently processing the application version.
        ///
        /// * Building – Application version is currently undergoing an AWS CodeBuild build.
        ///
        /// * Processed – Elastic Beanstalk was successfully pre-processed and validated.
        ///
        /// * Failed – Either the AWS CodeBuild build failed or configuration files didn't pass validation. This application version isn't usable.
        public var status: ElasticBeanstalkClientTypes.ApplicationVersionStatus?
        /// A unique identifier for the application version.
        public var versionLabel: Swift.String?

        public init(
            applicationName: Swift.String? = nil,
            applicationVersionArn: Swift.String? = nil,
            buildArn: Swift.String? = nil,
            dateCreated: Foundation.Date? = nil,
            dateUpdated: Foundation.Date? = nil,
            description: Swift.String? = nil,
            sourceBuildInformation: ElasticBeanstalkClientTypes.SourceBuildInformation? = nil,
            sourceBundle: ElasticBeanstalkClientTypes.S3Location? = nil,
            status: ElasticBeanstalkClientTypes.ApplicationVersionStatus? = nil,
            versionLabel: Swift.String? = nil
        )
        {
            self.applicationName = applicationName
            self.applicationVersionArn = applicationVersionArn
            self.buildArn = buildArn
            self.dateCreated = dateCreated
            self.dateUpdated = dateUpdated
            self.description = description
            self.sourceBuildInformation = sourceBuildInformation
            self.sourceBundle = sourceBundle
            self.status = status
            self.versionLabel = versionLabel
        }
    }
}

/// A generic service exception has occurred.
public struct ElasticBeanstalkServiceException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ElasticBeanstalkServiceException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Cannot modify the managed action in its current state.
public struct ManagedActionInvalidStateException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ManagedActionInvalidStateException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Request to execute a scheduled managed action immediately.
public struct ApplyEnvironmentManagedActionInput: Swift.Sendable {
    /// The action ID of the scheduled managed action to execute.
    /// This member is required.
    public var actionId: Swift.String?
    /// The environment ID of the target environment.
    public var environmentId: Swift.String?
    /// The name of the target environment.
    public var environmentName: Swift.String?

    public init(
        actionId: Swift.String? = nil,
        environmentId: Swift.String? = nil,
        environmentName: Swift.String? = nil
    )
    {
        self.actionId = actionId
        self.environmentId = environmentId
        self.environmentName = environmentName
    }
}

/// The result message containing information about the managed action.
public struct ApplyEnvironmentManagedActionOutput: Swift.Sendable {
    /// A description of the managed action.
    public var actionDescription: Swift.String?
    /// The action ID of the managed action.
    public var actionId: Swift.String?
    /// The type of managed action.
    public var actionType: ElasticBeanstalkClientTypes.ActionType?
    /// The status of the managed action.
    public var status: Swift.String?

    public init(
        actionDescription: Swift.String? = nil,
        actionId: Swift.String? = nil,
        actionType: ElasticBeanstalkClientTypes.ActionType? = nil,
        status: Swift.String? = nil
    )
    {
        self.actionDescription = actionDescription
        self.actionId = actionId
        self.actionType = actionType
        self.status = status
    }
}

/// Request to add or change the operations role used by an environment.
public struct AssociateEnvironmentOperationsRoleInput: Swift.Sendable {
    /// The name of the environment to which to set the operations role.
    /// This member is required.
    public var environmentName: Swift.String?
    /// The Amazon Resource Name (ARN) of an existing IAM role to be used as the environment's operations role.
    /// This member is required.
    public var operationsRole: Swift.String?

    public init(
        environmentName: Swift.String? = nil,
        operationsRole: Swift.String? = nil
    )
    {
        self.environmentName = environmentName
        self.operationsRole = operationsRole
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes an Auto Scaling launch configuration.
    public struct AutoScalingGroup: Swift.Sendable {
        /// The name of the AutoScalingGroup .
        public var name: Swift.String?

        public init(
            name: Swift.String? = nil
        )
        {
            self.name = name
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes the solution stack.
    public struct SolutionStackDescription: Swift.Sendable {
        /// The permitted file types allowed for a solution stack.
        public var permittedFileTypes: [Swift.String]?
        /// The name of the solution stack.
        public var solutionStackName: Swift.String?

        public init(
            permittedFileTypes: [Swift.String]? = nil,
            solutionStackName: Swift.String? = nil
        )
        {
            self.permittedFileTypes = permittedFileTypes
            self.solutionStackName = solutionStackName
        }
    }
}

/// Results message indicating whether a CNAME is available.
public struct CheckDNSAvailabilityInput: Swift.Sendable {
    /// The prefix used when this CNAME is reserved.
    /// This member is required.
    public var cnamePrefix: Swift.String?

    public init(
        cnamePrefix: Swift.String? = nil
    )
    {
        self.cnamePrefix = cnamePrefix
    }
}

/// Indicates if the specified CNAME is available.
public struct CheckDNSAvailabilityOutput: Swift.Sendable {
    /// Indicates if the specified CNAME is available:
    ///
    /// * true : The CNAME is available.
    ///
    /// * false : The CNAME is not available.
    public var available: Swift.Bool?
    /// The fully qualified CNAME to reserve when [CreateEnvironment] is called with the provided prefix.
    public var fullyQualifiedCNAME: Swift.String?

    public init(
        available: Swift.Bool? = nil,
        fullyQualifiedCNAME: Swift.String? = nil
    )
    {
        self.available = available
        self.fullyQualifiedCNAME = fullyQualifiedCNAME
    }
}

/// The specified account has reached its limit of environments.
public struct TooManyEnvironmentsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TooManyEnvironmentsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Request to create or update a group of environments.
public struct ComposeEnvironmentsInput: Swift.Sendable {
    /// The name of the application to which the specified source bundles belong.
    public var applicationName: Swift.String?
    /// The name of the group to which the target environments belong. Specify a group name only if the environment name defined in each target environment's manifest ends with a + (plus) character. See [Environment Manifest (env.yaml)](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/environment-cfg-manifest.html) for details.
    public var groupName: Swift.String?
    /// A list of version labels, specifying one or more application source bundles that belong to the target application. Each source bundle must include an environment manifest that specifies the name of the environment and the name of the solution stack to use, and optionally can specify environment links to create.
    public var versionLabels: [Swift.String]?

    public init(
        applicationName: Swift.String? = nil,
        groupName: Swift.String? = nil,
        versionLabels: [Swift.String]? = nil
    )
    {
        self.applicationName = applicationName
        self.groupName = groupName
        self.versionLabels = versionLabels
    }
}

extension ElasticBeanstalkClientTypes {

    /// A link to another environment, defined in the environment's manifest. Links provide connection information in system properties that can be used to connect to another environment in the same group. See [Environment Manifest (env.yaml)](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/environment-cfg-manifest.html) for details.
    public struct EnvironmentLink: Swift.Sendable {
        /// The name of the linked environment (the dependency).
        public var environmentName: Swift.String?
        /// The name of the link.
        public var linkName: Swift.String?

        public init(
            environmentName: Swift.String? = nil,
            linkName: Swift.String? = nil
        )
        {
            self.environmentName = environmentName
            self.linkName = linkName
        }
    }
}

extension ElasticBeanstalkClientTypes {

    public enum EnvironmentHealth: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case green
        case grey
        case red
        case yellow
        case sdkUnknown(Swift.String)

        public static var allCases: [EnvironmentHealth] {
            return [
                .green,
                .grey,
                .red,
                .yellow
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .green: return "Green"
            case .grey: return "Grey"
            case .red: return "Red"
            case .yellow: return "Yellow"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticBeanstalkClientTypes {

    public enum EnvironmentHealthStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case degraded
        case info
        case nodata
        case ok
        case pending
        case severe
        case suspended
        case unknown
        case warning
        case sdkUnknown(Swift.String)

        public static var allCases: [EnvironmentHealthStatus] {
            return [
                .degraded,
                .info,
                .nodata,
                .ok,
                .pending,
                .severe,
                .suspended,
                .unknown,
                .warning
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .degraded: return "Degraded"
            case .info: return "Info"
            case .nodata: return "NoData"
            case .ok: return "Ok"
            case .pending: return "Pending"
            case .severe: return "Severe"
            case .suspended: return "Suspended"
            case .unknown: return "Unknown"
            case .warning: return "Warning"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes the properties of a Listener for the LoadBalancer.
    public struct Listener: Swift.Sendable {
        /// The port that is used by the Listener.
        public var port: Swift.Int
        /// The protocol that is used by the Listener.
        public var `protocol`: Swift.String?

        public init(
            port: Swift.Int = 0,
            `protocol`: Swift.String? = nil
        )
        {
            self.port = port
            self.`protocol` = `protocol`
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes the details of a LoadBalancer.
    public struct LoadBalancerDescription: Swift.Sendable {
        /// The domain name of the LoadBalancer.
        public var domain: Swift.String?
        /// A list of Listeners used by the LoadBalancer.
        public var listeners: [ElasticBeanstalkClientTypes.Listener]?
        /// The name of the LoadBalancer.
        public var loadBalancerName: Swift.String?

        public init(
            domain: Swift.String? = nil,
            listeners: [ElasticBeanstalkClientTypes.Listener]? = nil,
            loadBalancerName: Swift.String? = nil
        )
        {
            self.domain = domain
            self.listeners = listeners
            self.loadBalancerName = loadBalancerName
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes the AWS resources in use by this environment. This data is not live data.
    public struct EnvironmentResourcesDescription: Swift.Sendable {
        /// Describes the LoadBalancer.
        public var loadBalancer: ElasticBeanstalkClientTypes.LoadBalancerDescription?

        public init(
            loadBalancer: ElasticBeanstalkClientTypes.LoadBalancerDescription? = nil
        )
        {
            self.loadBalancer = loadBalancer
        }
    }
}

extension ElasticBeanstalkClientTypes {

    public enum EnvironmentStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case aborting
        case launching
        case linkingfrom
        case linkingto
        case ready
        case terminated
        case terminating
        case updating
        case sdkUnknown(Swift.String)

        public static var allCases: [EnvironmentStatus] {
            return [
                .aborting,
                .launching,
                .linkingfrom,
                .linkingto,
                .ready,
                .terminated,
                .terminating,
                .updating
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .aborting: return "Aborting"
            case .launching: return "Launching"
            case .linkingfrom: return "LinkingFrom"
            case .linkingto: return "LinkingTo"
            case .ready: return "Ready"
            case .terminated: return "Terminated"
            case .terminating: return "Terminating"
            case .updating: return "Updating"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes the properties of an environment tier
    public struct EnvironmentTier: Swift.Sendable {
        /// The name of this environment tier. Valid values:
        ///
        /// * For Web server tier – WebServer
        ///
        /// * For Worker tier – Worker
        public var name: Swift.String?
        /// The type of this environment tier. Valid values:
        ///
        /// * For Web server tier – Standard
        ///
        /// * For Worker tier – SQS/HTTP
        public var type: Swift.String?
        /// The version of this environment tier. When you don't set a value to it, Elastic Beanstalk uses the latest compatible worker tier version. This member is deprecated. Any specific version that you set may become out of date. We recommend leaving it unspecified.
        public var version: Swift.String?

        public init(
            name: Swift.String? = nil,
            type: Swift.String? = nil,
            version: Swift.String? = nil
        )
        {
            self.name = name
            self.type = type
            self.version = version
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes the properties of an environment.
    public struct EnvironmentDescription: Swift.Sendable {
        /// Indicates if there is an in-progress environment configuration update or application version deployment that you can cancel. true: There is an update in progress. false: There are no updates currently in progress.
        public var abortableOperationInProgress: Swift.Bool?
        /// The name of the application associated with this environment.
        public var applicationName: Swift.String?
        /// The URL to the CNAME for this environment.
        public var cname: Swift.String?
        /// The creation date for this environment.
        public var dateCreated: Foundation.Date?
        /// The last modified date for this environment.
        public var dateUpdated: Foundation.Date?
        /// Describes this environment.
        public var description: Swift.String?
        /// For load-balanced, autoscaling environments, the URL to the LoadBalancer. For single-instance environments, the IP address of the instance.
        public var endpointURL: Swift.String?
        /// The environment's Amazon Resource Name (ARN), which can be used in other API requests that require an ARN.
        public var environmentArn: Swift.String?
        /// The ID of this environment.
        public var environmentId: Swift.String?
        /// A list of links to other environments in the same group.
        public var environmentLinks: [ElasticBeanstalkClientTypes.EnvironmentLink]?
        /// The name of this environment.
        public var environmentName: Swift.String?
        /// Describes the health status of the environment. AWS Elastic Beanstalk indicates the failure levels for a running environment:
        ///
        /// * Red: Indicates the environment is not responsive. Occurs when three or more consecutive failures occur for an environment.
        ///
        /// * Yellow: Indicates that something is wrong. Occurs when two consecutive failures occur for an environment.
        ///
        /// * Green: Indicates the environment is healthy and fully functional.
        ///
        /// * Grey: Default health for a new environment. The environment is not fully launched and health checks have not started or health checks are suspended during an UpdateEnvironment or RestartEnvironment request.
        ///
        ///
        /// Default: Grey
        public var health: ElasticBeanstalkClientTypes.EnvironmentHealth?
        /// Returns the health status of the application running in your environment. For more information, see [Health Colors and Statuses](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/health-enhanced-status.html).
        public var healthStatus: ElasticBeanstalkClientTypes.EnvironmentHealthStatus?
        /// The Amazon Resource Name (ARN) of the environment's operations role. For more information, see [Operations roles](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/iam-operationsrole.html) in the AWS Elastic Beanstalk Developer Guide.
        public var operationsRole: Swift.String?
        /// The ARN of the platform version.
        public var platformArn: Swift.String?
        /// The description of the AWS resources used by this environment.
        public var resources: ElasticBeanstalkClientTypes.EnvironmentResourcesDescription?
        /// The name of the SolutionStack deployed with this environment.
        public var solutionStackName: Swift.String?
        /// The current operational status of the environment:
        ///
        /// * Launching: Environment is in the process of initial deployment.
        ///
        /// * Updating: Environment is in the process of updating its configuration settings or application version.
        ///
        /// * Ready: Environment is available to have an action performed on it, such as update or terminate.
        ///
        /// * Terminating: Environment is in the shut-down process.
        ///
        /// * Terminated: Environment is not running.
        public var status: ElasticBeanstalkClientTypes.EnvironmentStatus?
        /// The name of the configuration template used to originally launch this environment.
        public var templateName: Swift.String?
        /// Describes the current tier of this environment.
        public var tier: ElasticBeanstalkClientTypes.EnvironmentTier?
        /// The application version deployed in this environment.
        public var versionLabel: Swift.String?

        public init(
            abortableOperationInProgress: Swift.Bool? = nil,
            applicationName: Swift.String? = nil,
            cname: Swift.String? = nil,
            dateCreated: Foundation.Date? = nil,
            dateUpdated: Foundation.Date? = nil,
            description: Swift.String? = nil,
            endpointURL: Swift.String? = nil,
            environmentArn: Swift.String? = nil,
            environmentId: Swift.String? = nil,
            environmentLinks: [ElasticBeanstalkClientTypes.EnvironmentLink]? = nil,
            environmentName: Swift.String? = nil,
            health: ElasticBeanstalkClientTypes.EnvironmentHealth? = nil,
            healthStatus: ElasticBeanstalkClientTypes.EnvironmentHealthStatus? = nil,
            operationsRole: Swift.String? = nil,
            platformArn: Swift.String? = nil,
            resources: ElasticBeanstalkClientTypes.EnvironmentResourcesDescription? = nil,
            solutionStackName: Swift.String? = nil,
            status: ElasticBeanstalkClientTypes.EnvironmentStatus? = nil,
            templateName: Swift.String? = nil,
            tier: ElasticBeanstalkClientTypes.EnvironmentTier? = nil,
            versionLabel: Swift.String? = nil
        )
        {
            self.abortableOperationInProgress = abortableOperationInProgress
            self.applicationName = applicationName
            self.cname = cname
            self.dateCreated = dateCreated
            self.dateUpdated = dateUpdated
            self.description = description
            self.endpointURL = endpointURL
            self.environmentArn = environmentArn
            self.environmentId = environmentId
            self.environmentLinks = environmentLinks
            self.environmentName = environmentName
            self.health = health
            self.healthStatus = healthStatus
            self.operationsRole = operationsRole
            self.platformArn = platformArn
            self.resources = resources
            self.solutionStackName = solutionStackName
            self.status = status
            self.templateName = templateName
            self.tier = tier
            self.versionLabel = versionLabel
        }
    }
}

/// Result message containing a list of environment descriptions.
public struct ComposeEnvironmentsOutput: Swift.Sendable {
    /// Returns an [EnvironmentDescription] list.
    public var environments: [ElasticBeanstalkClientTypes.EnvironmentDescription]?
    /// In a paginated request, the token that you can pass in a subsequent request to get the next response page.
    public var nextToken: Swift.String?

    public init(
        environments: [ElasticBeanstalkClientTypes.EnvironmentDescription]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.environments = environments
        self.nextToken = nextToken
    }
}

/// The specified account has reached its limit of applications.
public struct TooManyApplicationsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TooManyApplicationsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes a tag applied to a resource in an environment.
    public struct Tag: Swift.Sendable {
        /// The key of the tag.
        public var key: Swift.String?
        /// The value of the tag.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

/// Request to create an application.
public struct CreateApplicationInput: Swift.Sendable {
    /// The name of the application. Must be unique within your account.
    /// This member is required.
    public var applicationName: Swift.String?
    /// Your description of the application.
    public var description: Swift.String?
    /// Specifies an application resource lifecycle configuration to prevent your application from accumulating too many versions.
    public var resourceLifecycleConfig: ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig?
    /// Specifies the tags applied to the application. Elastic Beanstalk applies these tags only to the application. Environments that you create in the application don't inherit the tags.
    public var tags: [ElasticBeanstalkClientTypes.Tag]?

    public init(
        applicationName: Swift.String? = nil,
        description: Swift.String? = nil,
        resourceLifecycleConfig: ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig? = nil,
        tags: [ElasticBeanstalkClientTypes.Tag]? = nil
    )
    {
        self.applicationName = applicationName
        self.description = description
        self.resourceLifecycleConfig = resourceLifecycleConfig
        self.tags = tags
    }
}

/// Result message containing a single description of an application.
public struct CreateApplicationOutput: Swift.Sendable {
    /// The [ApplicationDescription] of the application.
    public var application: ElasticBeanstalkClientTypes.ApplicationDescription?

    public init(
        application: ElasticBeanstalkClientTypes.ApplicationDescription? = nil
    )
    {
        self.application = application
    }
}

/// AWS CodeBuild is not available in the specified region.
public struct CodeBuildNotInServiceRegionException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "CodeBuildNotInServiceRegionException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified S3 bucket does not belong to the S3 region in which the service is running. The following regions are supported:
///
/// * IAD/us-east-1
///
/// * PDX/us-west-2
///
/// * DUB/eu-west-1
public struct S3LocationNotInServiceRegionException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "S3LocationNotInServiceRegionException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified account has reached its limit of application versions.
public struct TooManyApplicationVersionsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TooManyApplicationVersionsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension ElasticBeanstalkClientTypes {

    public enum ComputeType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case buildGeneral1Large
        case buildGeneral1Medium
        case buildGeneral1Small
        case sdkUnknown(Swift.String)

        public static var allCases: [ComputeType] {
            return [
                .buildGeneral1Large,
                .buildGeneral1Medium,
                .buildGeneral1Small
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .buildGeneral1Large: return "BUILD_GENERAL1_LARGE"
            case .buildGeneral1Medium: return "BUILD_GENERAL1_MEDIUM"
            case .buildGeneral1Small: return "BUILD_GENERAL1_SMALL"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Settings for an AWS CodeBuild build.
    public struct BuildConfiguration: Swift.Sendable {
        /// The name of the artifact of the CodeBuild build. If provided, Elastic Beanstalk stores the build artifact in the S3 location S3-bucket/resources/application-name/codebuild/codebuild-version-label-artifact-name.zip. If not provided, Elastic Beanstalk stores the build artifact in the S3 location S3-bucket/resources/application-name/codebuild/codebuild-version-label.zip.
        public var artifactName: Swift.String?
        /// The Amazon Resource Name (ARN) of the AWS Identity and Access Management (IAM) role that enables AWS CodeBuild to interact with dependent AWS services on behalf of the AWS account.
        /// This member is required.
        public var codeBuildServiceRole: Swift.String?
        /// Information about the compute resources the build project will use.
        ///
        /// * BUILD_GENERAL1_SMALL: Use up to 3 GB memory and 2 vCPUs for builds
        ///
        /// * BUILD_GENERAL1_MEDIUM: Use up to 7 GB memory and 4 vCPUs for builds
        ///
        /// * BUILD_GENERAL1_LARGE: Use up to 15 GB memory and 8 vCPUs for builds
        public var computeType: ElasticBeanstalkClientTypes.ComputeType?
        /// The ID of the Docker image to use for this build project.
        /// This member is required.
        public var image: Swift.String?
        /// How long in minutes, from 5 to 480 (8 hours), for AWS CodeBuild to wait until timing out any related build that does not get marked as completed. The default is 60 minutes.
        public var timeoutInMinutes: Swift.Int?

        public init(
            artifactName: Swift.String? = nil,
            codeBuildServiceRole: Swift.String? = nil,
            computeType: ElasticBeanstalkClientTypes.ComputeType? = nil,
            image: Swift.String? = nil,
            timeoutInMinutes: Swift.Int? = nil
        )
        {
            self.artifactName = artifactName
            self.codeBuildServiceRole = codeBuildServiceRole
            self.computeType = computeType
            self.image = image
            self.timeoutInMinutes = timeoutInMinutes
        }
    }
}

///
public struct CreateApplicationVersionInput: Swift.Sendable {
    /// The name of the application. If no application is found with this name, and AutoCreateApplication is false, returns an InvalidParameterValue error.
    /// This member is required.
    public var applicationName: Swift.String?
    /// Set to true to create an application with the specified name if it doesn't already exist.
    public var autoCreateApplication: Swift.Bool?
    /// Settings for an AWS CodeBuild build.
    public var buildConfiguration: ElasticBeanstalkClientTypes.BuildConfiguration?
    /// A description of this application version.
    public var description: Swift.String?
    /// Pre-processes and validates the environment manifest (env.yaml) and configuration files (*.config files in the .ebextensions folder) in the source bundle. Validating configuration files can identify issues prior to deploying the application version to an environment. You must turn processing on for application versions that you create using AWS CodeBuild or AWS CodeCommit. For application versions built from a source bundle in Amazon S3, processing is optional. The Process option validates Elastic Beanstalk configuration files. It doesn't validate your application's configuration files, like proxy server or Docker configuration.
    public var process: Swift.Bool?
    /// Specify a commit in an AWS CodeCommit Git repository to use as the source code for the application version.
    public var sourceBuildInformation: ElasticBeanstalkClientTypes.SourceBuildInformation?
    /// The Amazon S3 bucket and key that identify the location of the source bundle for this version. The Amazon S3 bucket must be in the same region as the environment. Specify a source bundle in S3 or a commit in an AWS CodeCommit repository (with SourceBuildInformation), but not both. If neither SourceBundle nor SourceBuildInformation are provided, Elastic Beanstalk uses a sample application.
    public var sourceBundle: ElasticBeanstalkClientTypes.S3Location?
    /// Specifies the tags applied to the application version. Elastic Beanstalk applies these tags only to the application version. Environments that use the application version don't inherit the tags.
    public var tags: [ElasticBeanstalkClientTypes.Tag]?
    /// A label identifying this version. Constraint: Must be unique per application. If an application version already exists with this label for the specified application, AWS Elastic Beanstalk returns an InvalidParameterValue error.
    /// This member is required.
    public var versionLabel: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        autoCreateApplication: Swift.Bool? = nil,
        buildConfiguration: ElasticBeanstalkClientTypes.BuildConfiguration? = nil,
        description: Swift.String? = nil,
        process: Swift.Bool? = nil,
        sourceBuildInformation: ElasticBeanstalkClientTypes.SourceBuildInformation? = nil,
        sourceBundle: ElasticBeanstalkClientTypes.S3Location? = nil,
        tags: [ElasticBeanstalkClientTypes.Tag]? = nil,
        versionLabel: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.autoCreateApplication = autoCreateApplication
        self.buildConfiguration = buildConfiguration
        self.description = description
        self.process = process
        self.sourceBuildInformation = sourceBuildInformation
        self.sourceBundle = sourceBundle
        self.tags = tags
        self.versionLabel = versionLabel
    }
}

/// Result message wrapping a single description of an application version.
public struct CreateApplicationVersionOutput: Swift.Sendable {
    /// The [ApplicationVersionDescription] of the application version.
    public var applicationVersion: ElasticBeanstalkClientTypes.ApplicationVersionDescription?

    public init(
        applicationVersion: ElasticBeanstalkClientTypes.ApplicationVersionDescription? = nil
    )
    {
        self.applicationVersion = applicationVersion
    }
}

/// The specified account has reached its limit of Amazon S3 buckets.
public struct TooManyBucketsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TooManyBucketsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified account has reached its limit of configuration templates.
public struct TooManyConfigurationTemplatesException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TooManyConfigurationTemplatesException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension ElasticBeanstalkClientTypes {

    /// A specification identifying an individual configuration option along with its current value. For a list of possible namespaces and option values, see [Option Values](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/command-options.html) in the AWS Elastic Beanstalk Developer Guide.
    public struct ConfigurationOptionSetting: Swift.Sendable {
        /// A unique namespace that identifies the option's associated AWS resource.
        public var namespace: Swift.String?
        /// The name of the configuration option.
        public var optionName: Swift.String?
        /// A unique resource name for the option setting. Use it for a time–based scaling configuration option.
        public var resourceName: Swift.String?
        /// The current value for the configuration option.
        public var value: Swift.String?

        public init(
            namespace: Swift.String? = nil,
            optionName: Swift.String? = nil,
            resourceName: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.namespace = namespace
            self.optionName = optionName
            self.resourceName = resourceName
            self.value = value
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// A specification for an environment configuration.
    public struct SourceConfiguration: Swift.Sendable {
        /// The name of the application associated with the configuration.
        public var applicationName: Swift.String?
        /// The name of the configuration template.
        public var templateName: Swift.String?

        public init(
            applicationName: Swift.String? = nil,
            templateName: Swift.String? = nil
        )
        {
            self.applicationName = applicationName
            self.templateName = templateName
        }
    }
}

/// Request to create a configuration template.
public struct CreateConfigurationTemplateInput: Swift.Sendable {
    /// The name of the Elastic Beanstalk application to associate with this configuration template.
    /// This member is required.
    public var applicationName: Swift.String?
    /// An optional description for this configuration.
    public var description: Swift.String?
    /// The ID of an environment whose settings you want to use to create the configuration template. You must specify EnvironmentId if you don't specify PlatformArn, SolutionStackName, or SourceConfiguration.
    public var environmentId: Swift.String?
    /// Option values for the Elastic Beanstalk configuration, such as the instance type. If specified, these values override the values obtained from the solution stack or the source configuration template. For a complete list of Elastic Beanstalk configuration options, see [Option Values](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/command-options.html) in the AWS Elastic Beanstalk Developer Guide.
    public var optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]?
    /// The Amazon Resource Name (ARN) of the custom platform. For more information, see [ Custom Platforms](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/custom-platforms.html) in the AWS Elastic Beanstalk Developer Guide. If you specify PlatformArn, then don't specify SolutionStackName.
    public var platformArn: Swift.String?
    /// The name of an Elastic Beanstalk solution stack (platform version) that this configuration uses. For example, 64bit Amazon Linux 2013.09 running Tomcat 7 Java 7. A solution stack specifies the operating system, runtime, and application server for a configuration template. It also determines the set of configuration options as well as the possible and default values. For more information, see [Supported Platforms](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/concepts.platforms.html) in the AWS Elastic Beanstalk Developer Guide. You must specify SolutionStackName if you don't specify PlatformArn, EnvironmentId, or SourceConfiguration. Use the [ListAvailableSolutionStacks](https://docs.aws.amazon.com/elasticbeanstalk/latest/api/API_ListAvailableSolutionStacks.html) API to obtain a list of available solution stacks.
    public var solutionStackName: Swift.String?
    /// An Elastic Beanstalk configuration template to base this one on. If specified, Elastic Beanstalk uses the configuration values from the specified configuration template to create a new configuration. Values specified in OptionSettings override any values obtained from the SourceConfiguration. You must specify SourceConfiguration if you don't specify PlatformArn, EnvironmentId, or SolutionStackName. Constraint: If both solution stack name and source configuration are specified, the solution stack of the source configuration template must match the specified solution stack name.
    public var sourceConfiguration: ElasticBeanstalkClientTypes.SourceConfiguration?
    /// Specifies the tags applied to the configuration template.
    public var tags: [ElasticBeanstalkClientTypes.Tag]?
    /// The name of the configuration template. Constraint: This name must be unique per application.
    /// This member is required.
    public var templateName: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        description: Swift.String? = nil,
        environmentId: Swift.String? = nil,
        optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]? = nil,
        platformArn: Swift.String? = nil,
        solutionStackName: Swift.String? = nil,
        sourceConfiguration: ElasticBeanstalkClientTypes.SourceConfiguration? = nil,
        tags: [ElasticBeanstalkClientTypes.Tag]? = nil,
        templateName: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.description = description
        self.environmentId = environmentId
        self.optionSettings = optionSettings
        self.platformArn = platformArn
        self.solutionStackName = solutionStackName
        self.sourceConfiguration = sourceConfiguration
        self.tags = tags
        self.templateName = templateName
    }
}

extension ElasticBeanstalkClientTypes {

    public enum ConfigurationDeploymentStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case deployed
        case failed
        case pending
        case sdkUnknown(Swift.String)

        public static var allCases: [ConfigurationDeploymentStatus] {
            return [
                .deployed,
                .failed,
                .pending
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .deployed: return "deployed"
            case .failed: return "failed"
            case .pending: return "pending"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Describes the settings for a configuration set.
public struct CreateConfigurationTemplateOutput: Swift.Sendable {
    /// The name of the application associated with this configuration set.
    public var applicationName: Swift.String?
    /// The date (in UTC time) when this configuration set was created.
    public var dateCreated: Foundation.Date?
    /// The date (in UTC time) when this configuration set was last modified.
    public var dateUpdated: Foundation.Date?
    /// If this configuration set is associated with an environment, the DeploymentStatus parameter indicates the deployment status of this configuration set:
    ///
    /// * null: This configuration is not associated with a running environment.
    ///
    /// * pending: This is a draft configuration that is not deployed to the associated environment but is in the process of deploying.
    ///
    /// * deployed: This is the configuration that is currently deployed to the associated running environment.
    ///
    /// * failed: This is a draft configuration that failed to successfully deploy.
    public var deploymentStatus: ElasticBeanstalkClientTypes.ConfigurationDeploymentStatus?
    /// Describes this configuration set.
    public var description: Swift.String?
    /// If not null, the name of the environment for this configuration set.
    public var environmentName: Swift.String?
    /// A list of the configuration options and their values in this configuration set.
    public var optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]?
    /// The ARN of the platform version.
    public var platformArn: Swift.String?
    /// The name of the solution stack this configuration set uses.
    public var solutionStackName: Swift.String?
    /// If not null, the name of the configuration template for this configuration set.
    public var templateName: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        dateCreated: Foundation.Date? = nil,
        dateUpdated: Foundation.Date? = nil,
        deploymentStatus: ElasticBeanstalkClientTypes.ConfigurationDeploymentStatus? = nil,
        description: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]? = nil,
        platformArn: Swift.String? = nil,
        solutionStackName: Swift.String? = nil,
        templateName: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.dateCreated = dateCreated
        self.dateUpdated = dateUpdated
        self.deploymentStatus = deploymentStatus
        self.description = description
        self.environmentName = environmentName
        self.optionSettings = optionSettings
        self.platformArn = platformArn
        self.solutionStackName = solutionStackName
        self.templateName = templateName
    }
}

extension ElasticBeanstalkClientTypes {

    /// A specification identifying an individual configuration option.
    public struct OptionSpecification: Swift.Sendable {
        /// A unique namespace identifying the option's associated AWS resource.
        public var namespace: Swift.String?
        /// The name of the configuration option.
        public var optionName: Swift.String?
        /// A unique resource name for a time-based scaling configuration option.
        public var resourceName: Swift.String?

        public init(
            namespace: Swift.String? = nil,
            optionName: Swift.String? = nil,
            resourceName: Swift.String? = nil
        )
        {
            self.namespace = namespace
            self.optionName = optionName
            self.resourceName = resourceName
        }
    }
}

///
public struct CreateEnvironmentInput: Swift.Sendable {
    /// The name of the application that is associated with this environment.
    /// This member is required.
    public var applicationName: Swift.String?
    /// If specified, the environment attempts to use this value as the prefix for the CNAME in your Elastic Beanstalk environment URL. If not specified, the CNAME is generated automatically by appending a random alphanumeric string to the environment name.
    public var cnamePrefix: Swift.String?
    /// Your description for this environment.
    public var description: Swift.String?
    /// A unique name for the environment. Constraint: Must be from 4 to 40 characters in length. The name can contain only letters, numbers, and hyphens. It can't start or end with a hyphen. This name must be unique within a region in your account. If the specified name already exists in the region, Elastic Beanstalk returns an InvalidParameterValue error. If you don't specify the CNAMEPrefix parameter, the environment name becomes part of the CNAME, and therefore part of the visible URL for your application.
    public var environmentName: Swift.String?
    /// The name of the group to which the target environment belongs. Specify a group name only if the environment's name is specified in an environment manifest and not with the environment name parameter. See [Environment Manifest (env.yaml)](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/environment-cfg-manifest.html) for details.
    public var groupName: Swift.String?
    /// The Amazon Resource Name (ARN) of an existing IAM role to be used as the environment's operations role. If specified, Elastic Beanstalk uses the operations role for permissions to downstream services during this call and during subsequent calls acting on this environment. To specify an operations role, you must have the iam:PassRole permission for the role. For more information, see [Operations roles](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/iam-operationsrole.html) in the AWS Elastic Beanstalk Developer Guide.
    public var operationsRole: Swift.String?
    /// If specified, AWS Elastic Beanstalk sets the specified configuration options to the requested value in the configuration set for the new environment. These override the values obtained from the solution stack or the configuration template.
    public var optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]?
    /// A list of custom user-defined configuration options to remove from the configuration set for this new environment.
    public var optionsToRemove: [ElasticBeanstalkClientTypes.OptionSpecification]?
    /// The Amazon Resource Name (ARN) of the custom platform to use with the environment. For more information, see [Custom Platforms](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/custom-platforms.html) in the AWS Elastic Beanstalk Developer Guide. If you specify PlatformArn, don't specify SolutionStackName.
    public var platformArn: Swift.String?
    /// The name of an Elastic Beanstalk solution stack (platform version) to use with the environment. If specified, Elastic Beanstalk sets the configuration values to the default values associated with the specified solution stack. For a list of current solution stacks, see [Elastic Beanstalk Supported Platforms](https://docs.aws.amazon.com/elasticbeanstalk/latest/platforms/platforms-supported.html) in the AWS Elastic Beanstalk Platforms guide. If you specify SolutionStackName, don't specify PlatformArn or TemplateName.
    public var solutionStackName: Swift.String?
    /// Specifies the tags applied to resources in the environment.
    public var tags: [ElasticBeanstalkClientTypes.Tag]?
    /// The name of the Elastic Beanstalk configuration template to use with the environment. If you specify TemplateName, then don't specify SolutionStackName.
    public var templateName: Swift.String?
    /// Specifies the tier to use in creating this environment. The environment tier that you choose determines whether Elastic Beanstalk provisions resources to support a web application that handles HTTP(S) requests or a web application that handles background-processing tasks.
    public var tier: ElasticBeanstalkClientTypes.EnvironmentTier?
    /// The name of the application version to deploy. Default: If not specified, Elastic Beanstalk attempts to deploy the sample application.
    public var versionLabel: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        cnamePrefix: Swift.String? = nil,
        description: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        groupName: Swift.String? = nil,
        operationsRole: Swift.String? = nil,
        optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]? = nil,
        optionsToRemove: [ElasticBeanstalkClientTypes.OptionSpecification]? = nil,
        platformArn: Swift.String? = nil,
        solutionStackName: Swift.String? = nil,
        tags: [ElasticBeanstalkClientTypes.Tag]? = nil,
        templateName: Swift.String? = nil,
        tier: ElasticBeanstalkClientTypes.EnvironmentTier? = nil,
        versionLabel: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.cnamePrefix = cnamePrefix
        self.description = description
        self.environmentName = environmentName
        self.groupName = groupName
        self.operationsRole = operationsRole
        self.optionSettings = optionSettings
        self.optionsToRemove = optionsToRemove
        self.platformArn = platformArn
        self.solutionStackName = solutionStackName
        self.tags = tags
        self.templateName = templateName
        self.tier = tier
        self.versionLabel = versionLabel
    }
}

/// Describes the properties of an environment.
public struct CreateEnvironmentOutput: Swift.Sendable {
    /// Indicates if there is an in-progress environment configuration update or application version deployment that you can cancel. true: There is an update in progress. false: There are no updates currently in progress.
    public var abortableOperationInProgress: Swift.Bool?
    /// The name of the application associated with this environment.
    public var applicationName: Swift.String?
    /// The URL to the CNAME for this environment.
    public var cname: Swift.String?
    /// The creation date for this environment.
    public var dateCreated: Foundation.Date?
    /// The last modified date for this environment.
    public var dateUpdated: Foundation.Date?
    /// Describes this environment.
    public var description: Swift.String?
    /// For load-balanced, autoscaling environments, the URL to the LoadBalancer. For single-instance environments, the IP address of the instance.
    public var endpointURL: Swift.String?
    /// The environment's Amazon Resource Name (ARN), which can be used in other API requests that require an ARN.
    public var environmentArn: Swift.String?
    /// The ID of this environment.
    public var environmentId: Swift.String?
    /// A list of links to other environments in the same group.
    public var environmentLinks: [ElasticBeanstalkClientTypes.EnvironmentLink]?
    /// The name of this environment.
    public var environmentName: Swift.String?
    /// Describes the health status of the environment. AWS Elastic Beanstalk indicates the failure levels for a running environment:
    ///
    /// * Red: Indicates the environment is not responsive. Occurs when three or more consecutive failures occur for an environment.
    ///
    /// * Yellow: Indicates that something is wrong. Occurs when two consecutive failures occur for an environment.
    ///
    /// * Green: Indicates the environment is healthy and fully functional.
    ///
    /// * Grey: Default health for a new environment. The environment is not fully launched and health checks have not started or health checks are suspended during an UpdateEnvironment or RestartEnvironment request.
    ///
    ///
    /// Default: Grey
    public var health: ElasticBeanstalkClientTypes.EnvironmentHealth?
    /// Returns the health status of the application running in your environment. For more information, see [Health Colors and Statuses](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/health-enhanced-status.html).
    public var healthStatus: ElasticBeanstalkClientTypes.EnvironmentHealthStatus?
    /// The Amazon Resource Name (ARN) of the environment's operations role. For more information, see [Operations roles](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/iam-operationsrole.html) in the AWS Elastic Beanstalk Developer Guide.
    public var operationsRole: Swift.String?
    /// The ARN of the platform version.
    public var platformArn: Swift.String?
    /// The description of the AWS resources used by this environment.
    public var resources: ElasticBeanstalkClientTypes.EnvironmentResourcesDescription?
    /// The name of the SolutionStack deployed with this environment.
    public var solutionStackName: Swift.String?
    /// The current operational status of the environment:
    ///
    /// * Launching: Environment is in the process of initial deployment.
    ///
    /// * Updating: Environment is in the process of updating its configuration settings or application version.
    ///
    /// * Ready: Environment is available to have an action performed on it, such as update or terminate.
    ///
    /// * Terminating: Environment is in the shut-down process.
    ///
    /// * Terminated: Environment is not running.
    public var status: ElasticBeanstalkClientTypes.EnvironmentStatus?
    /// The name of the configuration template used to originally launch this environment.
    public var templateName: Swift.String?
    /// Describes the current tier of this environment.
    public var tier: ElasticBeanstalkClientTypes.EnvironmentTier?
    /// The application version deployed in this environment.
    public var versionLabel: Swift.String?

    public init(
        abortableOperationInProgress: Swift.Bool? = nil,
        applicationName: Swift.String? = nil,
        cname: Swift.String? = nil,
        dateCreated: Foundation.Date? = nil,
        dateUpdated: Foundation.Date? = nil,
        description: Swift.String? = nil,
        endpointURL: Swift.String? = nil,
        environmentArn: Swift.String? = nil,
        environmentId: Swift.String? = nil,
        environmentLinks: [ElasticBeanstalkClientTypes.EnvironmentLink]? = nil,
        environmentName: Swift.String? = nil,
        health: ElasticBeanstalkClientTypes.EnvironmentHealth? = nil,
        healthStatus: ElasticBeanstalkClientTypes.EnvironmentHealthStatus? = nil,
        operationsRole: Swift.String? = nil,
        platformArn: Swift.String? = nil,
        resources: ElasticBeanstalkClientTypes.EnvironmentResourcesDescription? = nil,
        solutionStackName: Swift.String? = nil,
        status: ElasticBeanstalkClientTypes.EnvironmentStatus? = nil,
        templateName: Swift.String? = nil,
        tier: ElasticBeanstalkClientTypes.EnvironmentTier? = nil,
        versionLabel: Swift.String? = nil
    )
    {
        self.abortableOperationInProgress = abortableOperationInProgress
        self.applicationName = applicationName
        self.cname = cname
        self.dateCreated = dateCreated
        self.dateUpdated = dateUpdated
        self.description = description
        self.endpointURL = endpointURL
        self.environmentArn = environmentArn
        self.environmentId = environmentId
        self.environmentLinks = environmentLinks
        self.environmentName = environmentName
        self.health = health
        self.healthStatus = healthStatus
        self.operationsRole = operationsRole
        self.platformArn = platformArn
        self.resources = resources
        self.solutionStackName = solutionStackName
        self.status = status
        self.templateName = templateName
        self.tier = tier
        self.versionLabel = versionLabel
    }
}

/// You have exceeded the maximum number of allowed platforms associated with the account.
public struct TooManyPlatformsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TooManyPlatformsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Request to create a new platform version.
public struct CreatePlatformVersionInput: Swift.Sendable {
    /// The name of the builder environment.
    public var environmentName: Swift.String?
    /// The configuration option settings to apply to the builder environment.
    public var optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]?
    /// The location of the platform definition archive in Amazon S3.
    /// This member is required.
    public var platformDefinitionBundle: ElasticBeanstalkClientTypes.S3Location?
    /// The name of your custom platform.
    /// This member is required.
    public var platformName: Swift.String?
    /// The number, such as 1.0.2, for the new platform version.
    /// This member is required.
    public var platformVersion: Swift.String?
    /// Specifies the tags applied to the new platform version. Elastic Beanstalk applies these tags only to the platform version. Environments that you create using the platform version don't inherit the tags.
    public var tags: [ElasticBeanstalkClientTypes.Tag]?

    public init(
        environmentName: Swift.String? = nil,
        optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]? = nil,
        platformDefinitionBundle: ElasticBeanstalkClientTypes.S3Location? = nil,
        platformName: Swift.String? = nil,
        platformVersion: Swift.String? = nil,
        tags: [ElasticBeanstalkClientTypes.Tag]? = nil
    )
    {
        self.environmentName = environmentName
        self.optionSettings = optionSettings
        self.platformDefinitionBundle = platformDefinitionBundle
        self.platformName = platformName
        self.platformVersion = platformVersion
        self.tags = tags
    }
}

extension ElasticBeanstalkClientTypes {

    /// The builder used to build the custom platform.
    public struct Builder: Swift.Sendable {
        /// The ARN of the builder.
        public var arn: Swift.String?

        public init(
            arn: Swift.String? = nil
        )
        {
            self.arn = arn
        }
    }
}

extension ElasticBeanstalkClientTypes {

    public enum PlatformStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case creating
        case deleted
        case deleting
        case failed
        case ready
        case sdkUnknown(Swift.String)

        public static var allCases: [PlatformStatus] {
            return [
                .creating,
                .deleted,
                .deleting,
                .failed,
                .ready
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .creating: return "Creating"
            case .deleted: return "Deleted"
            case .deleting: return "Deleting"
            case .failed: return "Failed"
            case .ready: return "Ready"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Summary information about a platform version.
    public struct PlatformSummary: Swift.Sendable {
        /// The operating system used by the platform version.
        public var operatingSystemName: Swift.String?
        /// The version of the operating system used by the platform version.
        public var operatingSystemVersion: Swift.String?
        /// The ARN of the platform version.
        public var platformArn: Swift.String?
        /// The state of the platform version's branch in its lifecycle. Possible values: beta | supported | deprecated | retired
        public var platformBranchLifecycleState: Swift.String?
        /// The platform branch to which the platform version belongs.
        public var platformBranchName: Swift.String?
        /// The category of platform version.
        public var platformCategory: Swift.String?
        /// The state of the platform version in its lifecycle. Possible values: recommended | empty If an empty value is returned, the platform version is supported but isn't the recommended one for its branch.
        public var platformLifecycleState: Swift.String?
        /// The AWS account ID of the person who created the platform version.
        public var platformOwner: Swift.String?
        /// The status of the platform version. You can create an environment from the platform version once it is ready.
        public var platformStatus: ElasticBeanstalkClientTypes.PlatformStatus?
        /// The version string of the platform version.
        public var platformVersion: Swift.String?
        /// The additions associated with the platform version.
        public var supportedAddonList: [Swift.String]?
        /// The tiers in which the platform version runs.
        public var supportedTierList: [Swift.String]?

        public init(
            operatingSystemName: Swift.String? = nil,
            operatingSystemVersion: Swift.String? = nil,
            platformArn: Swift.String? = nil,
            platformBranchLifecycleState: Swift.String? = nil,
            platformBranchName: Swift.String? = nil,
            platformCategory: Swift.String? = nil,
            platformLifecycleState: Swift.String? = nil,
            platformOwner: Swift.String? = nil,
            platformStatus: ElasticBeanstalkClientTypes.PlatformStatus? = nil,
            platformVersion: Swift.String? = nil,
            supportedAddonList: [Swift.String]? = nil,
            supportedTierList: [Swift.String]? = nil
        )
        {
            self.operatingSystemName = operatingSystemName
            self.operatingSystemVersion = operatingSystemVersion
            self.platformArn = platformArn
            self.platformBranchLifecycleState = platformBranchLifecycleState
            self.platformBranchName = platformBranchName
            self.platformCategory = platformCategory
            self.platformLifecycleState = platformLifecycleState
            self.platformOwner = platformOwner
            self.platformStatus = platformStatus
            self.platformVersion = platformVersion
            self.supportedAddonList = supportedAddonList
            self.supportedTierList = supportedTierList
        }
    }
}

public struct CreatePlatformVersionOutput: Swift.Sendable {
    /// The builder used to create the custom platform.
    public var builder: ElasticBeanstalkClientTypes.Builder?
    /// Detailed information about the new version of the custom platform.
    public var platformSummary: ElasticBeanstalkClientTypes.PlatformSummary?

    public init(
        builder: ElasticBeanstalkClientTypes.Builder? = nil,
        platformSummary: ElasticBeanstalkClientTypes.PlatformSummary? = nil
    )
    {
        self.builder = builder
        self.platformSummary = platformSummary
    }
}

/// The specified account does not have a subscription to Amazon S3.
public struct S3SubscriptionRequiredException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "S3SubscriptionRequiredException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Results of a [CreateStorageLocationResult] call.
public struct CreateStorageLocationOutput: Swift.Sendable {
    /// The name of the Amazon S3 bucket created.
    public var s3Bucket: Swift.String?

    public init(
        s3Bucket: Swift.String? = nil
    )
    {
        self.s3Bucket = s3Bucket
    }
}

/// Unable to perform the specified operation because another operation that effects an element in this activity is already in progress.
public struct OperationInProgressException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "OperationInProgressFailure" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Request to delete an application.
public struct DeleteApplicationInput: Swift.Sendable {
    /// The name of the application to delete.
    /// This member is required.
    public var applicationName: Swift.String?
    /// When set to true, running environments will be terminated before deleting the application.
    public var terminateEnvByForce: Swift.Bool?

    public init(
        applicationName: Swift.String? = nil,
        terminateEnvByForce: Swift.Bool? = nil
    )
    {
        self.applicationName = applicationName
        self.terminateEnvByForce = terminateEnvByForce
    }
}

/// Unable to delete the Amazon S3 source bundle associated with the application version. The application version was deleted successfully.
public struct SourceBundleDeletionException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SourceBundleDeletionFailure" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Request to delete an application version.
public struct DeleteApplicationVersionInput: Swift.Sendable {
    /// The name of the application to which the version belongs.
    /// This member is required.
    public var applicationName: Swift.String?
    /// Set to true to delete the source bundle from your storage bucket. Otherwise, the application version is deleted only from Elastic Beanstalk and the source bundle remains in Amazon S3.
    public var deleteSourceBundle: Swift.Bool?
    /// The label of the version to delete.
    /// This member is required.
    public var versionLabel: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        deleteSourceBundle: Swift.Bool? = nil,
        versionLabel: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.deleteSourceBundle = deleteSourceBundle
        self.versionLabel = versionLabel
    }
}

/// Request to delete a configuration template.
public struct DeleteConfigurationTemplateInput: Swift.Sendable {
    /// The name of the application to delete the configuration template from.
    /// This member is required.
    public var applicationName: Swift.String?
    /// The name of the configuration template to delete.
    /// This member is required.
    public var templateName: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        templateName: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.templateName = templateName
    }
}

/// Request to delete a draft environment configuration.
public struct DeleteEnvironmentConfigurationInput: Swift.Sendable {
    /// The name of the application the environment is associated with.
    /// This member is required.
    public var applicationName: Swift.String?
    /// The name of the environment to delete the draft configuration from.
    /// This member is required.
    public var environmentName: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        environmentName: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.environmentName = environmentName
    }
}

/// You cannot delete the platform version because there are still environments running on it.
public struct PlatformVersionStillReferencedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "PlatformVersionStillReferencedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DeletePlatformVersionInput: Swift.Sendable {
    /// The ARN of the version of the custom platform.
    public var platformArn: Swift.String?

    public init(
        platformArn: Swift.String? = nil
    )
    {
        self.platformArn = platformArn
    }
}

public struct DeletePlatformVersionOutput: Swift.Sendable {
    /// Detailed information about the version of the custom platform.
    public var platformSummary: ElasticBeanstalkClientTypes.PlatformSummary?

    public init(
        platformSummary: ElasticBeanstalkClientTypes.PlatformSummary? = nil
    )
    {
        self.platformSummary = platformSummary
    }
}

extension ElasticBeanstalkClientTypes {

    /// The AWS Elastic Beanstalk quota information for a single resource type in an AWS account. It reflects the resource's limits for this account.
    public struct ResourceQuota: Swift.Sendable {
        /// The maximum number of instances of this Elastic Beanstalk resource type that an AWS account can use.
        public var maximum: Swift.Int?

        public init(
            maximum: Swift.Int? = nil
        )
        {
            self.maximum = maximum
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// A set of per-resource AWS Elastic Beanstalk quotas associated with an AWS account. They reflect Elastic Beanstalk resource limits for this account.
    public struct ResourceQuotas: Swift.Sendable {
        /// The quota for applications in the AWS account.
        public var applicationQuota: ElasticBeanstalkClientTypes.ResourceQuota?
        /// The quota for application versions in the AWS account.
        public var applicationVersionQuota: ElasticBeanstalkClientTypes.ResourceQuota?
        /// The quota for configuration templates in the AWS account.
        public var configurationTemplateQuota: ElasticBeanstalkClientTypes.ResourceQuota?
        /// The quota for custom platforms in the AWS account.
        public var customPlatformQuota: ElasticBeanstalkClientTypes.ResourceQuota?
        /// The quota for environments in the AWS account.
        public var environmentQuota: ElasticBeanstalkClientTypes.ResourceQuota?

        public init(
            applicationQuota: ElasticBeanstalkClientTypes.ResourceQuota? = nil,
            applicationVersionQuota: ElasticBeanstalkClientTypes.ResourceQuota? = nil,
            configurationTemplateQuota: ElasticBeanstalkClientTypes.ResourceQuota? = nil,
            customPlatformQuota: ElasticBeanstalkClientTypes.ResourceQuota? = nil,
            environmentQuota: ElasticBeanstalkClientTypes.ResourceQuota? = nil
        )
        {
            self.applicationQuota = applicationQuota
            self.applicationVersionQuota = applicationVersionQuota
            self.configurationTemplateQuota = configurationTemplateQuota
            self.customPlatformQuota = customPlatformQuota
            self.environmentQuota = environmentQuota
        }
    }
}

public struct DescribeAccountAttributesOutput: Swift.Sendable {
    /// The Elastic Beanstalk resource quotas associated with the calling AWS account.
    public var resourceQuotas: ElasticBeanstalkClientTypes.ResourceQuotas?

    public init(
        resourceQuotas: ElasticBeanstalkClientTypes.ResourceQuotas? = nil
    )
    {
        self.resourceQuotas = resourceQuotas
    }
}

/// Request to describe one or more applications.
public struct DescribeApplicationsInput: Swift.Sendable {
    /// If specified, AWS Elastic Beanstalk restricts the returned descriptions to only include those with the specified names.
    public var applicationNames: [Swift.String]?

    public init(
        applicationNames: [Swift.String]? = nil
    )
    {
        self.applicationNames = applicationNames
    }
}

/// Result message containing a list of application descriptions.
public struct DescribeApplicationsOutput: Swift.Sendable {
    /// This parameter contains a list of [ApplicationDescription].
    public var applications: [ElasticBeanstalkClientTypes.ApplicationDescription]?

    public init(
        applications: [ElasticBeanstalkClientTypes.ApplicationDescription]? = nil
    )
    {
        self.applications = applications
    }
}

/// Request to describe application versions.
public struct DescribeApplicationVersionsInput: Swift.Sendable {
    /// Specify an application name to show only application versions for that application.
    public var applicationName: Swift.String?
    /// For a paginated request. Specify a maximum number of application versions to include in each response. If no MaxRecords is specified, all available application versions are retrieved in a single response.
    public var maxRecords: Swift.Int?
    /// For a paginated request. Specify a token from a previous response page to retrieve the next response page. All other parameter values must be identical to the ones specified in the initial request. If no NextToken is specified, the first page is retrieved.
    public var nextToken: Swift.String?
    /// Specify a version label to show a specific application version.
    public var versionLabels: [Swift.String]?

    public init(
        applicationName: Swift.String? = nil,
        maxRecords: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        versionLabels: [Swift.String]? = nil
    )
    {
        self.applicationName = applicationName
        self.maxRecords = maxRecords
        self.nextToken = nextToken
        self.versionLabels = versionLabels
    }
}

/// Result message wrapping a list of application version descriptions.
public struct DescribeApplicationVersionsOutput: Swift.Sendable {
    /// List of ApplicationVersionDescription objects sorted in order of creation.
    public var applicationVersions: [ElasticBeanstalkClientTypes.ApplicationVersionDescription]?
    /// In a paginated request, the token that you can pass in a subsequent request to get the next response page.
    public var nextToken: Swift.String?

    public init(
        applicationVersions: [ElasticBeanstalkClientTypes.ApplicationVersionDescription]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.applicationVersions = applicationVersions
        self.nextToken = nextToken
    }
}

/// Result message containing a list of application version descriptions.
public struct DescribeConfigurationOptionsInput: Swift.Sendable {
    /// The name of the application associated with the configuration template or environment. Only needed if you want to describe the configuration options associated with either the configuration template or environment.
    public var applicationName: Swift.String?
    /// The name of the environment whose configuration options you want to describe.
    public var environmentName: Swift.String?
    /// If specified, restricts the descriptions to only the specified options.
    public var options: [ElasticBeanstalkClientTypes.OptionSpecification]?
    /// The ARN of the custom platform.
    public var platformArn: Swift.String?
    /// The name of the solution stack whose configuration options you want to describe.
    public var solutionStackName: Swift.String?
    /// The name of the configuration template whose configuration options you want to describe.
    public var templateName: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        options: [ElasticBeanstalkClientTypes.OptionSpecification]? = nil,
        platformArn: Swift.String? = nil,
        solutionStackName: Swift.String? = nil,
        templateName: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.environmentName = environmentName
        self.options = options
        self.platformArn = platformArn
        self.solutionStackName = solutionStackName
        self.templateName = templateName
    }
}

extension ElasticBeanstalkClientTypes {

    /// A regular expression representing a restriction on a string configuration option value.
    public struct OptionRestrictionRegex: Swift.Sendable {
        /// A unique name representing this regular expression.
        public var label: Swift.String?
        /// The regular expression pattern that a string configuration option value with this restriction must match.
        public var pattern: Swift.String?

        public init(
            label: Swift.String? = nil,
            pattern: Swift.String? = nil
        )
        {
            self.label = label
            self.pattern = pattern
        }
    }
}

extension ElasticBeanstalkClientTypes {

    public enum ConfigurationOptionValueType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case list
        case scalar
        case sdkUnknown(Swift.String)

        public static var allCases: [ConfigurationOptionValueType] {
            return [
                .list,
                .scalar
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .list: return "List"
            case .scalar: return "Scalar"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes the possible values for a configuration option.
    public struct ConfigurationOptionDescription: Swift.Sendable {
        /// An indication of which action is required if the value for this configuration option changes:
        ///
        /// * NoInterruption : There is no interruption to the environment or application availability.
        ///
        /// * RestartEnvironment : The environment is entirely restarted, all AWS resources are deleted and recreated, and the environment is unavailable during the process.
        ///
        /// * RestartApplicationServer : The environment is available the entire time. However, a short application outage occurs when the application servers on the running Amazon EC2 instances are restarted.
        public var changeSeverity: Swift.String?
        /// The default value for this configuration option.
        public var defaultValue: Swift.String?
        /// If specified, the configuration option must be a string value no longer than this value.
        public var maxLength: Swift.Int?
        /// If specified, the configuration option must be a numeric value less than this value.
        public var maxValue: Swift.Int?
        /// If specified, the configuration option must be a numeric value greater than this value.
        public var minValue: Swift.Int?
        /// The name of the configuration option.
        public var name: Swift.String?
        /// A unique namespace identifying the option's associated AWS resource.
        public var namespace: Swift.String?
        /// If specified, the configuration option must be a string value that satisfies this regular expression.
        public var regex: ElasticBeanstalkClientTypes.OptionRestrictionRegex?
        /// An indication of whether the user defined this configuration option:
        ///
        /// * true : This configuration option was defined by the user. It is a valid choice for specifying if this as an Option to Remove when updating configuration settings.
        ///
        /// * false : This configuration was not defined by the user.
        ///
        ///
        /// Constraint: You can remove only UserDefined options from a configuration. Valid Values: true | false
        public var userDefined: Swift.Bool?
        /// If specified, values for the configuration option are selected from this list.
        public var valueOptions: [Swift.String]?
        /// An indication of which type of values this option has and whether it is allowable to select one or more than one of the possible values:
        ///
        /// * Scalar : Values for this option are a single selection from the possible values, or an unformatted string, or numeric value governed by the MIN/MAX/Regex constraints.
        ///
        /// * List : Values for this option are multiple selections from the possible values.
        ///
        /// * Boolean : Values for this option are either true or false .
        ///
        /// * Json : Values for this option are a JSON representation of a ConfigDocument.
        public var valueType: ElasticBeanstalkClientTypes.ConfigurationOptionValueType?

        public init(
            changeSeverity: Swift.String? = nil,
            defaultValue: Swift.String? = nil,
            maxLength: Swift.Int? = nil,
            maxValue: Swift.Int? = nil,
            minValue: Swift.Int? = nil,
            name: Swift.String? = nil,
            namespace: Swift.String? = nil,
            regex: ElasticBeanstalkClientTypes.OptionRestrictionRegex? = nil,
            userDefined: Swift.Bool? = nil,
            valueOptions: [Swift.String]? = nil,
            valueType: ElasticBeanstalkClientTypes.ConfigurationOptionValueType? = nil
        )
        {
            self.changeSeverity = changeSeverity
            self.defaultValue = defaultValue
            self.maxLength = maxLength
            self.maxValue = maxValue
            self.minValue = minValue
            self.name = name
            self.namespace = namespace
            self.regex = regex
            self.userDefined = userDefined
            self.valueOptions = valueOptions
            self.valueType = valueType
        }
    }
}

/// Describes the settings for a specified configuration set.
public struct DescribeConfigurationOptionsOutput: Swift.Sendable {
    /// A list of [ConfigurationOptionDescription].
    public var options: [ElasticBeanstalkClientTypes.ConfigurationOptionDescription]?
    /// The ARN of the platform version.
    public var platformArn: Swift.String?
    /// The name of the solution stack these configuration options belong to.
    public var solutionStackName: Swift.String?

    public init(
        options: [ElasticBeanstalkClientTypes.ConfigurationOptionDescription]? = nil,
        platformArn: Swift.String? = nil,
        solutionStackName: Swift.String? = nil
    )
    {
        self.options = options
        self.platformArn = platformArn
        self.solutionStackName = solutionStackName
    }
}

/// Result message containing all of the configuration settings for a specified solution stack or configuration template.
public struct DescribeConfigurationSettingsInput: Swift.Sendable {
    /// The application for the environment or configuration template.
    /// This member is required.
    public var applicationName: Swift.String?
    /// The name of the environment to describe. Condition: You must specify either this or a TemplateName, but not both. If you specify both, AWS Elastic Beanstalk returns an InvalidParameterCombination error. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentName: Swift.String?
    /// The name of the configuration template to describe. Conditional: You must specify either this parameter or an EnvironmentName, but not both. If you specify both, AWS Elastic Beanstalk returns an InvalidParameterCombination error. If you do not specify either, AWS Elastic Beanstalk returns a MissingRequiredParameter error.
    public var templateName: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        templateName: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.environmentName = environmentName
        self.templateName = templateName
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes the settings for a configuration set.
    public struct ConfigurationSettingsDescription: Swift.Sendable {
        /// The name of the application associated with this configuration set.
        public var applicationName: Swift.String?
        /// The date (in UTC time) when this configuration set was created.
        public var dateCreated: Foundation.Date?
        /// The date (in UTC time) when this configuration set was last modified.
        public var dateUpdated: Foundation.Date?
        /// If this configuration set is associated with an environment, the DeploymentStatus parameter indicates the deployment status of this configuration set:
        ///
        /// * null: This configuration is not associated with a running environment.
        ///
        /// * pending: This is a draft configuration that is not deployed to the associated environment but is in the process of deploying.
        ///
        /// * deployed: This is the configuration that is currently deployed to the associated running environment.
        ///
        /// * failed: This is a draft configuration that failed to successfully deploy.
        public var deploymentStatus: ElasticBeanstalkClientTypes.ConfigurationDeploymentStatus?
        /// Describes this configuration set.
        public var description: Swift.String?
        /// If not null, the name of the environment for this configuration set.
        public var environmentName: Swift.String?
        /// A list of the configuration options and their values in this configuration set.
        public var optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]?
        /// The ARN of the platform version.
        public var platformArn: Swift.String?
        /// The name of the solution stack this configuration set uses.
        public var solutionStackName: Swift.String?
        /// If not null, the name of the configuration template for this configuration set.
        public var templateName: Swift.String?

        public init(
            applicationName: Swift.String? = nil,
            dateCreated: Foundation.Date? = nil,
            dateUpdated: Foundation.Date? = nil,
            deploymentStatus: ElasticBeanstalkClientTypes.ConfigurationDeploymentStatus? = nil,
            description: Swift.String? = nil,
            environmentName: Swift.String? = nil,
            optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]? = nil,
            platformArn: Swift.String? = nil,
            solutionStackName: Swift.String? = nil,
            templateName: Swift.String? = nil
        )
        {
            self.applicationName = applicationName
            self.dateCreated = dateCreated
            self.dateUpdated = dateUpdated
            self.deploymentStatus = deploymentStatus
            self.description = description
            self.environmentName = environmentName
            self.optionSettings = optionSettings
            self.platformArn = platformArn
            self.solutionStackName = solutionStackName
            self.templateName = templateName
        }
    }
}

/// The results from a request to change the configuration settings of an environment.
public struct DescribeConfigurationSettingsOutput: Swift.Sendable {
    /// A list of [ConfigurationSettingsDescription].
    public var configurationSettings: [ElasticBeanstalkClientTypes.ConfigurationSettingsDescription]?

    public init(
        configurationSettings: [ElasticBeanstalkClientTypes.ConfigurationSettingsDescription]? = nil
    )
    {
        self.configurationSettings = configurationSettings
    }
}

/// One or more input parameters is not valid. Please correct the input parameters and try the operation again.
public struct InvalidRequestException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidRequestException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension ElasticBeanstalkClientTypes {

    public enum EnvironmentHealthAttribute: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case all
        case applicationmetrics
        case causes
        case color
        case healthstatus
        case instanceshealth
        case refreshedat
        case status
        case sdkUnknown(Swift.String)

        public static var allCases: [EnvironmentHealthAttribute] {
            return [
                .all,
                .applicationmetrics,
                .causes,
                .color,
                .healthstatus,
                .instanceshealth,
                .refreshedat,
                .status
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .all: return "All"
            case .applicationmetrics: return "ApplicationMetrics"
            case .causes: return "Causes"
            case .color: return "Color"
            case .healthstatus: return "HealthStatus"
            case .instanceshealth: return "InstancesHealth"
            case .refreshedat: return "RefreshedAt"
            case .status: return "Status"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// See the example below to learn how to create a request body.
public struct DescribeEnvironmentHealthInput: Swift.Sendable {
    /// Specify the response elements to return. To retrieve all attributes, set to All. If no attribute names are specified, returns the name of the environment.
    public var attributeNames: [ElasticBeanstalkClientTypes.EnvironmentHealthAttribute]?
    /// Specify the environment by ID. You must specify either this or an EnvironmentName, or both.
    public var environmentId: Swift.String?
    /// Specify the environment by name. You must specify either this or an EnvironmentName, or both.
    public var environmentName: Swift.String?

    public init(
        attributeNames: [ElasticBeanstalkClientTypes.EnvironmentHealthAttribute]? = nil,
        environmentId: Swift.String? = nil,
        environmentName: Swift.String? = nil
    )
    {
        self.attributeNames = attributeNames
        self.environmentId = environmentId
        self.environmentName = environmentName
    }
}

extension ElasticBeanstalkClientTypes {

    /// Represents summary information about the health of an instance. For more information, see [Health Colors and Statuses](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/health-enhanced-status.html).
    public struct InstanceHealthSummary: Swift.Sendable {
        /// Red. The health agent is reporting a high number of request failures or other issues for an instance or environment.
        public var degraded: Swift.Int?
        /// Green. An operation is in progress on an instance.
        public var info: Swift.Int?
        /// Grey. AWS Elastic Beanstalk and the health agent are reporting no data on an instance.
        public var noData: Swift.Int?
        /// Green. An instance is passing health checks and the health agent is not reporting any problems.
        public var ok: Swift.Int?
        /// Grey. An operation is in progress on an instance within the command timeout.
        public var pending: Swift.Int?
        /// Red. The health agent is reporting a very high number of request failures or other issues for an instance or environment.
        public var severe: Swift.Int?
        /// Grey. AWS Elastic Beanstalk and the health agent are reporting an insufficient amount of data on an instance.
        public var unknown: Swift.Int?
        /// Yellow. The health agent is reporting a moderate number of request failures or other issues for an instance or environment.
        public var warning: Swift.Int?

        public init(
            degraded: Swift.Int? = nil,
            info: Swift.Int? = nil,
            noData: Swift.Int? = nil,
            ok: Swift.Int? = nil,
            pending: Swift.Int? = nil,
            severe: Swift.Int? = nil,
            unknown: Swift.Int? = nil,
            warning: Swift.Int? = nil
        )
        {
            self.degraded = degraded
            self.info = info
            self.noData = noData
            self.ok = ok
            self.pending = pending
            self.severe = severe
            self.unknown = unknown
            self.warning = warning
        }
    }
}

/// Health details for an AWS Elastic Beanstalk environment.
public struct DescribeEnvironmentHealthOutput: Swift.Sendable {
    /// Application request metrics for the environment.
    public var applicationMetrics: ElasticBeanstalkClientTypes.ApplicationMetrics?
    /// Descriptions of the data that contributed to the environment's current health status.
    public var causes: [Swift.String]?
    /// The [health color](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/health-enhanced-status.html) of the environment.
    public var color: Swift.String?
    /// The environment's name.
    public var environmentName: Swift.String?
    /// The [health status](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/health-enhanced-status.html) of the environment. For example, Ok.
    public var healthStatus: Swift.String?
    /// Summary health information for the instances in the environment.
    public var instancesHealth: ElasticBeanstalkClientTypes.InstanceHealthSummary?
    /// The date and time that the health information was retrieved.
    public var refreshedAt: Foundation.Date?
    /// The environment's operational status. Ready, Launching, Updating, Terminating, or Terminated.
    public var status: ElasticBeanstalkClientTypes.EnvironmentHealth?

    public init(
        applicationMetrics: ElasticBeanstalkClientTypes.ApplicationMetrics? = nil,
        causes: [Swift.String]? = nil,
        color: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        healthStatus: Swift.String? = nil,
        instancesHealth: ElasticBeanstalkClientTypes.InstanceHealthSummary? = nil,
        refreshedAt: Foundation.Date? = nil,
        status: ElasticBeanstalkClientTypes.EnvironmentHealth? = nil
    )
    {
        self.applicationMetrics = applicationMetrics
        self.causes = causes
        self.color = color
        self.environmentName = environmentName
        self.healthStatus = healthStatus
        self.instancesHealth = instancesHealth
        self.refreshedAt = refreshedAt
        self.status = status
    }
}

/// Request to list completed and failed managed actions.
public struct DescribeEnvironmentManagedActionHistoryInput: Swift.Sendable {
    /// The environment ID of the target environment.
    public var environmentId: Swift.String?
    /// The name of the target environment.
    public var environmentName: Swift.String?
    /// The maximum number of items to return for a single request.
    public var maxItems: Swift.Int?
    /// The pagination token returned by a previous request.
    public var nextToken: Swift.String?

    public init(
        environmentId: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        maxItems: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.environmentId = environmentId
        self.environmentName = environmentName
        self.maxItems = maxItems
        self.nextToken = nextToken
    }
}

extension ElasticBeanstalkClientTypes {

    public enum FailureType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cancellationfailed
        case internalfailure
        case invalidenvironmentstate
        case permissionserror
        case rollbackfailed
        case rollbacksuccessful
        case updatecancelled
        case sdkUnknown(Swift.String)

        public static var allCases: [FailureType] {
            return [
                .cancellationfailed,
                .internalfailure,
                .invalidenvironmentstate,
                .permissionserror,
                .rollbackfailed,
                .rollbacksuccessful,
                .updatecancelled
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cancellationfailed: return "CancellationFailed"
            case .internalfailure: return "InternalFailure"
            case .invalidenvironmentstate: return "InvalidEnvironmentState"
            case .permissionserror: return "PermissionsError"
            case .rollbackfailed: return "RollbackFailed"
            case .rollbacksuccessful: return "RollbackSuccessful"
            case .updatecancelled: return "UpdateCancelled"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// The record of a completed or failed managed action.
    public struct ManagedActionHistoryItem: Swift.Sendable {
        /// A description of the managed action.
        public var actionDescription: Swift.String?
        /// A unique identifier for the managed action.
        public var actionId: Swift.String?
        /// The type of the managed action.
        public var actionType: ElasticBeanstalkClientTypes.ActionType?
        /// The date and time that the action started executing.
        public var executedTime: Foundation.Date?
        /// If the action failed, a description of the failure.
        public var failureDescription: Swift.String?
        /// If the action failed, the type of failure.
        public var failureType: ElasticBeanstalkClientTypes.FailureType?
        /// The date and time that the action finished executing.
        public var finishedTime: Foundation.Date?
        /// The status of the action.
        public var status: ElasticBeanstalkClientTypes.ActionHistoryStatus?

        public init(
            actionDescription: Swift.String? = nil,
            actionId: Swift.String? = nil,
            actionType: ElasticBeanstalkClientTypes.ActionType? = nil,
            executedTime: Foundation.Date? = nil,
            failureDescription: Swift.String? = nil,
            failureType: ElasticBeanstalkClientTypes.FailureType? = nil,
            finishedTime: Foundation.Date? = nil,
            status: ElasticBeanstalkClientTypes.ActionHistoryStatus? = nil
        )
        {
            self.actionDescription = actionDescription
            self.actionId = actionId
            self.actionType = actionType
            self.executedTime = executedTime
            self.failureDescription = failureDescription
            self.failureType = failureType
            self.finishedTime = finishedTime
            self.status = status
        }
    }
}

/// A result message containing a list of completed and failed managed actions.
public struct DescribeEnvironmentManagedActionHistoryOutput: Swift.Sendable {
    /// A list of completed and failed managed actions.
    public var managedActionHistoryItems: [ElasticBeanstalkClientTypes.ManagedActionHistoryItem]?
    /// A pagination token that you pass to [DescribeEnvironmentManagedActionHistory] to get the next page of results.
    public var nextToken: Swift.String?

    public init(
        managedActionHistoryItems: [ElasticBeanstalkClientTypes.ManagedActionHistoryItem]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.managedActionHistoryItems = managedActionHistoryItems
        self.nextToken = nextToken
    }
}

/// Request to list an environment's upcoming and in-progress managed actions.
public struct DescribeEnvironmentManagedActionsInput: Swift.Sendable {
    /// The environment ID of the target environment.
    public var environmentId: Swift.String?
    /// The name of the target environment.
    public var environmentName: Swift.String?
    /// To show only actions with a particular status, specify a status.
    public var status: ElasticBeanstalkClientTypes.ActionStatus?

    public init(
        environmentId: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        status: ElasticBeanstalkClientTypes.ActionStatus? = nil
    )
    {
        self.environmentId = environmentId
        self.environmentName = environmentName
        self.status = status
    }
}

extension ElasticBeanstalkClientTypes {

    /// The record of an upcoming or in-progress managed action.
    public struct ManagedAction: Swift.Sendable {
        /// A description of the managed action.
        public var actionDescription: Swift.String?
        /// A unique identifier for the managed action.
        public var actionId: Swift.String?
        /// The type of managed action.
        public var actionType: ElasticBeanstalkClientTypes.ActionType?
        /// The status of the managed action. If the action is Scheduled, you can apply it immediately with [ApplyEnvironmentManagedAction].
        public var status: ElasticBeanstalkClientTypes.ActionStatus?
        /// The start time of the maintenance window in which the managed action will execute.
        public var windowStartTime: Foundation.Date?

        public init(
            actionDescription: Swift.String? = nil,
            actionId: Swift.String? = nil,
            actionType: ElasticBeanstalkClientTypes.ActionType? = nil,
            status: ElasticBeanstalkClientTypes.ActionStatus? = nil,
            windowStartTime: Foundation.Date? = nil
        )
        {
            self.actionDescription = actionDescription
            self.actionId = actionId
            self.actionType = actionType
            self.status = status
            self.windowStartTime = windowStartTime
        }
    }
}

/// The result message containing a list of managed actions.
public struct DescribeEnvironmentManagedActionsOutput: Swift.Sendable {
    /// A list of upcoming and in-progress managed actions.
    public var managedActions: [ElasticBeanstalkClientTypes.ManagedAction]?

    public init(
        managedActions: [ElasticBeanstalkClientTypes.ManagedAction]? = nil
    )
    {
        self.managedActions = managedActions
    }
}

/// Request to describe the resources in an environment.
public struct DescribeEnvironmentResourcesInput: Swift.Sendable {
    /// The ID of the environment to retrieve AWS resource usage data. Condition: You must specify either this or an EnvironmentName, or both. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentId: Swift.String?
    /// The name of the environment to retrieve AWS resource usage data. Condition: You must specify either this or an EnvironmentId, or both. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentName: Swift.String?

    public init(
        environmentId: Swift.String? = nil,
        environmentName: Swift.String? = nil
    )
    {
        self.environmentId = environmentId
        self.environmentName = environmentName
    }
}

extension ElasticBeanstalkClientTypes {

    /// The description of an Amazon EC2 instance.
    public struct Instance: Swift.Sendable {
        /// The ID of the Amazon EC2 instance.
        public var id: Swift.String?

        public init(
            id: Swift.String? = nil
        )
        {
            self.id = id
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes an Auto Scaling launch configuration.
    public struct LaunchConfiguration: Swift.Sendable {
        /// The name of the launch configuration.
        public var name: Swift.String?

        public init(
            name: Swift.String? = nil
        )
        {
            self.name = name
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes an Amazon EC2 launch template.
    public struct LaunchTemplate: Swift.Sendable {
        /// The ID of the launch template.
        public var id: Swift.String?

        public init(
            id: Swift.String? = nil
        )
        {
            self.id = id
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes a LoadBalancer.
    public struct LoadBalancer: Swift.Sendable {
        /// The name of the LoadBalancer.
        public var name: Swift.String?

        public init(
            name: Swift.String? = nil
        )
        {
            self.name = name
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes a queue.
    public struct Queue: Swift.Sendable {
        /// The name of the queue.
        public var name: Swift.String?
        /// The URL of the queue.
        public var url: Swift.String?

        public init(
            name: Swift.String? = nil,
            url: Swift.String? = nil
        )
        {
            self.name = name
            self.url = url
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes a trigger.
    public struct Trigger: Swift.Sendable {
        /// The name of the trigger.
        public var name: Swift.String?

        public init(
            name: Swift.String? = nil
        )
        {
            self.name = name
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes the AWS resources in use by this environment. This data is live.
    public struct EnvironmentResourceDescription: Swift.Sendable {
        /// The AutoScalingGroups used by this environment.
        public var autoScalingGroups: [ElasticBeanstalkClientTypes.AutoScalingGroup]?
        /// The name of the environment.
        public var environmentName: Swift.String?
        /// The Amazon EC2 instances used by this environment.
        public var instances: [ElasticBeanstalkClientTypes.Instance]?
        /// The Auto Scaling launch configurations in use by this environment.
        public var launchConfigurations: [ElasticBeanstalkClientTypes.LaunchConfiguration]?
        /// The Amazon EC2 launch templates in use by this environment.
        public var launchTemplates: [ElasticBeanstalkClientTypes.LaunchTemplate]?
        /// The LoadBalancers in use by this environment.
        public var loadBalancers: [ElasticBeanstalkClientTypes.LoadBalancer]?
        /// The queues used by this environment.
        public var queues: [ElasticBeanstalkClientTypes.Queue]?
        /// The AutoScaling triggers in use by this environment.
        public var triggers: [ElasticBeanstalkClientTypes.Trigger]?

        public init(
            autoScalingGroups: [ElasticBeanstalkClientTypes.AutoScalingGroup]? = nil,
            environmentName: Swift.String? = nil,
            instances: [ElasticBeanstalkClientTypes.Instance]? = nil,
            launchConfigurations: [ElasticBeanstalkClientTypes.LaunchConfiguration]? = nil,
            launchTemplates: [ElasticBeanstalkClientTypes.LaunchTemplate]? = nil,
            loadBalancers: [ElasticBeanstalkClientTypes.LoadBalancer]? = nil,
            queues: [ElasticBeanstalkClientTypes.Queue]? = nil,
            triggers: [ElasticBeanstalkClientTypes.Trigger]? = nil
        )
        {
            self.autoScalingGroups = autoScalingGroups
            self.environmentName = environmentName
            self.instances = instances
            self.launchConfigurations = launchConfigurations
            self.launchTemplates = launchTemplates
            self.loadBalancers = loadBalancers
            self.queues = queues
            self.triggers = triggers
        }
    }
}

/// Result message containing a list of environment resource descriptions.
public struct DescribeEnvironmentResourcesOutput: Swift.Sendable {
    /// A list of [EnvironmentResourceDescription].
    public var environmentResources: ElasticBeanstalkClientTypes.EnvironmentResourceDescription?

    public init(
        environmentResources: ElasticBeanstalkClientTypes.EnvironmentResourceDescription? = nil
    )
    {
        self.environmentResources = environmentResources
    }
}

/// Request to describe one or more environments.
public struct DescribeEnvironmentsInput: Swift.Sendable {
    /// If specified, AWS Elastic Beanstalk restricts the returned descriptions to include only those that are associated with this application.
    public var applicationName: Swift.String?
    /// If specified, AWS Elastic Beanstalk restricts the returned descriptions to include only those that have the specified IDs.
    public var environmentIds: [Swift.String]?
    /// If specified, AWS Elastic Beanstalk restricts the returned descriptions to include only those that have the specified names.
    public var environmentNames: [Swift.String]?
    /// Indicates whether to include deleted environments: true: Environments that have been deleted after IncludedDeletedBackTo are displayed. false: Do not include deleted environments.
    public var includeDeleted: Swift.Bool?
    /// If specified when IncludeDeleted is set to true, then environments deleted after this date are displayed.
    public var includedDeletedBackTo: Foundation.Date?
    /// For a paginated request. Specify a maximum number of environments to include in each response. If no MaxRecords is specified, all available environments are retrieved in a single response.
    public var maxRecords: Swift.Int?
    /// For a paginated request. Specify a token from a previous response page to retrieve the next response page. All other parameter values must be identical to the ones specified in the initial request. If no NextToken is specified, the first page is retrieved.
    public var nextToken: Swift.String?
    /// If specified, AWS Elastic Beanstalk restricts the returned descriptions to include only those that are associated with this application version.
    public var versionLabel: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        environmentIds: [Swift.String]? = nil,
        environmentNames: [Swift.String]? = nil,
        includeDeleted: Swift.Bool? = nil,
        includedDeletedBackTo: Foundation.Date? = nil,
        maxRecords: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        versionLabel: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.environmentIds = environmentIds
        self.environmentNames = environmentNames
        self.includeDeleted = includeDeleted
        self.includedDeletedBackTo = includedDeletedBackTo
        self.maxRecords = maxRecords
        self.nextToken = nextToken
        self.versionLabel = versionLabel
    }
}

/// Result message containing a list of environment descriptions.
public struct DescribeEnvironmentsOutput: Swift.Sendable {
    /// Returns an [EnvironmentDescription] list.
    public var environments: [ElasticBeanstalkClientTypes.EnvironmentDescription]?
    /// In a paginated request, the token that you can pass in a subsequent request to get the next response page.
    public var nextToken: Swift.String?

    public init(
        environments: [ElasticBeanstalkClientTypes.EnvironmentDescription]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.environments = environments
        self.nextToken = nextToken
    }
}

extension ElasticBeanstalkClientTypes {

    public enum EventSeverity: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case debug
        case error
        case fatal
        case info
        case trace
        case warn
        case sdkUnknown(Swift.String)

        public static var allCases: [EventSeverity] {
            return [
                .debug,
                .error,
                .fatal,
                .info,
                .trace,
                .warn
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .debug: return "DEBUG"
            case .error: return "ERROR"
            case .fatal: return "FATAL"
            case .info: return "INFO"
            case .trace: return "TRACE"
            case .warn: return "WARN"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Request to retrieve a list of events for an environment.
public struct DescribeEventsInput: Swift.Sendable {
    /// If specified, AWS Elastic Beanstalk restricts the returned descriptions to include only those associated with this application.
    public var applicationName: Swift.String?
    /// If specified, AWS Elastic Beanstalk restricts the returned descriptions to those that occur up to, but not including, the EndTime.
    public var endTime: Foundation.Date?
    /// If specified, AWS Elastic Beanstalk restricts the returned descriptions to those associated with this environment.
    public var environmentId: Swift.String?
    /// If specified, AWS Elastic Beanstalk restricts the returned descriptions to those associated with this environment.
    public var environmentName: Swift.String?
    /// Specifies the maximum number of events that can be returned, beginning with the most recent event.
    public var maxRecords: Swift.Int?
    /// Pagination token. If specified, the events return the next batch of results.
    public var nextToken: Swift.String?
    /// The ARN of a custom platform version. If specified, AWS Elastic Beanstalk restricts the returned descriptions to those associated with this custom platform version.
    public var platformArn: Swift.String?
    /// If specified, AWS Elastic Beanstalk restricts the described events to include only those associated with this request ID.
    public var requestId: Swift.String?
    /// If specified, limits the events returned from this call to include only those with the specified severity or higher.
    public var severity: ElasticBeanstalkClientTypes.EventSeverity?
    /// If specified, AWS Elastic Beanstalk restricts the returned descriptions to those that occur on or after this time.
    public var startTime: Foundation.Date?
    /// If specified, AWS Elastic Beanstalk restricts the returned descriptions to those that are associated with this environment configuration.
    public var templateName: Swift.String?
    /// If specified, AWS Elastic Beanstalk restricts the returned descriptions to those associated with this application version.
    public var versionLabel: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        endTime: Foundation.Date? = nil,
        environmentId: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        maxRecords: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        platformArn: Swift.String? = nil,
        requestId: Swift.String? = nil,
        severity: ElasticBeanstalkClientTypes.EventSeverity? = nil,
        startTime: Foundation.Date? = nil,
        templateName: Swift.String? = nil,
        versionLabel: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.endTime = endTime
        self.environmentId = environmentId
        self.environmentName = environmentName
        self.maxRecords = maxRecords
        self.nextToken = nextToken
        self.platformArn = platformArn
        self.requestId = requestId
        self.severity = severity
        self.startTime = startTime
        self.templateName = templateName
        self.versionLabel = versionLabel
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes an event.
    public struct EventDescription: Swift.Sendable {
        /// The application associated with the event.
        public var applicationName: Swift.String?
        /// The name of the environment associated with this event.
        public var environmentName: Swift.String?
        /// The date when the event occurred.
        public var eventDate: Foundation.Date?
        /// The event message.
        public var message: Swift.String?
        /// The ARN of the platform version.
        public var platformArn: Swift.String?
        /// The web service request ID for the activity of this event.
        public var requestId: Swift.String?
        /// The severity level of this event.
        public var severity: ElasticBeanstalkClientTypes.EventSeverity?
        /// The name of the configuration associated with this event.
        public var templateName: Swift.String?
        /// The release label for the application version associated with this event.
        public var versionLabel: Swift.String?

        public init(
            applicationName: Swift.String? = nil,
            environmentName: Swift.String? = nil,
            eventDate: Foundation.Date? = nil,
            message: Swift.String? = nil,
            platformArn: Swift.String? = nil,
            requestId: Swift.String? = nil,
            severity: ElasticBeanstalkClientTypes.EventSeverity? = nil,
            templateName: Swift.String? = nil,
            versionLabel: Swift.String? = nil
        )
        {
            self.applicationName = applicationName
            self.environmentName = environmentName
            self.eventDate = eventDate
            self.message = message
            self.platformArn = platformArn
            self.requestId = requestId
            self.severity = severity
            self.templateName = templateName
            self.versionLabel = versionLabel
        }
    }
}

/// Result message wrapping a list of event descriptions.
public struct DescribeEventsOutput: Swift.Sendable {
    /// A list of [EventDescription].
    public var events: [ElasticBeanstalkClientTypes.EventDescription]?
    /// If returned, this indicates that there are more results to obtain. Use this token in the next [DescribeEvents] call to get the next batch of events.
    public var nextToken: Swift.String?

    public init(
        events: [ElasticBeanstalkClientTypes.EventDescription]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.events = events
        self.nextToken = nextToken
    }
}

extension ElasticBeanstalkClientTypes {

    public enum InstancesHealthAttribute: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case all
        case applicationmetrics
        case availabilityzone
        case causes
        case color
        case deployment
        case healthstatus
        case instancetype
        case launchedat
        case refreshedat
        case system
        case sdkUnknown(Swift.String)

        public static var allCases: [InstancesHealthAttribute] {
            return [
                .all,
                .applicationmetrics,
                .availabilityzone,
                .causes,
                .color,
                .deployment,
                .healthstatus,
                .instancetype,
                .launchedat,
                .refreshedat,
                .system
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .all: return "All"
            case .applicationmetrics: return "ApplicationMetrics"
            case .availabilityzone: return "AvailabilityZone"
            case .causes: return "Causes"
            case .color: return "Color"
            case .deployment: return "Deployment"
            case .healthstatus: return "HealthStatus"
            case .instancetype: return "InstanceType"
            case .launchedat: return "LaunchedAt"
            case .refreshedat: return "RefreshedAt"
            case .system: return "System"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Parameters for a call to DescribeInstancesHealth.
public struct DescribeInstancesHealthInput: Swift.Sendable {
    /// Specifies the response elements you wish to receive. To retrieve all attributes, set to All. If no attribute names are specified, returns a list of instances.
    public var attributeNames: [ElasticBeanstalkClientTypes.InstancesHealthAttribute]?
    /// Specify the AWS Elastic Beanstalk environment by ID.
    public var environmentId: Swift.String?
    /// Specify the AWS Elastic Beanstalk environment by name.
    public var environmentName: Swift.String?
    /// Specify the pagination token returned by a previous call.
    public var nextToken: Swift.String?

    public init(
        attributeNames: [ElasticBeanstalkClientTypes.InstancesHealthAttribute]? = nil,
        environmentId: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.attributeNames = attributeNames
        self.environmentId = environmentId
        self.environmentName = environmentName
        self.nextToken = nextToken
    }
}

extension ElasticBeanstalkClientTypes {

    /// Information about an application version deployment.
    public struct Deployment: Swift.Sendable {
        /// The ID of the deployment. This number increases by one each time that you deploy source code or change instance configuration settings.
        public var deploymentId: Swift.Int?
        /// For in-progress deployments, the time that the deployment started. For completed deployments, the time that the deployment ended.
        public var deploymentTime: Foundation.Date?
        /// The status of the deployment:
        ///
        /// * In Progress : The deployment is in progress.
        ///
        /// * Deployed : The deployment succeeded.
        ///
        /// * Failed : The deployment failed.
        public var status: Swift.String?
        /// The version label of the application version in the deployment.
        public var versionLabel: Swift.String?

        public init(
            deploymentId: Swift.Int? = nil,
            deploymentTime: Foundation.Date? = nil,
            status: Swift.String? = nil,
            versionLabel: Swift.String? = nil
        )
        {
            self.deploymentId = deploymentId
            self.deploymentTime = deploymentTime
            self.status = status
            self.versionLabel = versionLabel
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// CPU utilization metrics for an instance.
    public struct CPUUtilization: Swift.Sendable {
        /// Percentage of time that the CPU has spent in the Idle state over the last 10 seconds.
        public var idle: Swift.Double?
        /// Available on Linux environments only. Percentage of time that the CPU has spent in the I/O Wait state over the last 10 seconds.
        public var ioWait: Swift.Double?
        /// Available on Linux environments only. Percentage of time that the CPU has spent in the IRQ state over the last 10 seconds.
        public var irq: Swift.Double?
        /// Available on Linux environments only. Percentage of time that the CPU has spent in the Nice state over the last 10 seconds.
        public var nice: Swift.Double?
        /// Available on Windows environments only. Percentage of time that the CPU has spent in the Privileged state over the last 10 seconds.
        public var privileged: Swift.Double?
        /// Available on Linux environments only. Percentage of time that the CPU has spent in the SoftIRQ state over the last 10 seconds.
        public var softIRQ: Swift.Double?
        /// Available on Linux environments only. Percentage of time that the CPU has spent in the System state over the last 10 seconds.
        public var system: Swift.Double?
        /// Percentage of time that the CPU has spent in the User state over the last 10 seconds.
        public var user: Swift.Double?

        public init(
            idle: Swift.Double? = nil,
            ioWait: Swift.Double? = nil,
            irq: Swift.Double? = nil,
            nice: Swift.Double? = nil,
            privileged: Swift.Double? = nil,
            softIRQ: Swift.Double? = nil,
            system: Swift.Double? = nil,
            user: Swift.Double? = nil
        )
        {
            self.idle = idle
            self.ioWait = ioWait
            self.irq = irq
            self.nice = nice
            self.privileged = privileged
            self.softIRQ = softIRQ
            self.system = system
            self.user = user
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// CPU utilization and load average metrics for an Amazon EC2 instance.
    public struct SystemStatus: Swift.Sendable {
        /// CPU utilization metrics for the instance.
        public var cpuUtilization: ElasticBeanstalkClientTypes.CPUUtilization?
        /// Load average in the last 1-minute, 5-minute, and 15-minute periods. For more information, see [Operating System Metrics](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/health-enhanced-metrics.html#health-enhanced-metrics-os).
        public var loadAverage: [Swift.Double]?

        public init(
            cpuUtilization: ElasticBeanstalkClientTypes.CPUUtilization? = nil,
            loadAverage: [Swift.Double]? = nil
        )
        {
            self.cpuUtilization = cpuUtilization
            self.loadAverage = loadAverage
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Detailed health information about an Amazon EC2 instance in your Elastic Beanstalk environment.
    public struct SingleInstanceHealth: Swift.Sendable {
        /// Request metrics from your application.
        public var applicationMetrics: ElasticBeanstalkClientTypes.ApplicationMetrics?
        /// The availability zone in which the instance runs.
        public var availabilityZone: Swift.String?
        /// Represents the causes, which provide more information about the current health status.
        public var causes: [Swift.String]?
        /// Represents the color indicator that gives you information about the health of the EC2 instance. For more information, see [Health Colors and Statuses](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/health-enhanced-status.html).
        public var color: Swift.String?
        /// Information about the most recent deployment to an instance.
        public var deployment: ElasticBeanstalkClientTypes.Deployment?
        /// Returns the health status of the specified instance. For more information, see [Health Colors and Statuses](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/health-enhanced-status.html).
        public var healthStatus: Swift.String?
        /// The ID of the Amazon EC2 instance.
        public var instanceId: Swift.String?
        /// The instance's type.
        public var instanceType: Swift.String?
        /// The time at which the EC2 instance was launched.
        public var launchedAt: Foundation.Date?
        /// Operating system metrics from the instance.
        public var system: ElasticBeanstalkClientTypes.SystemStatus?

        public init(
            applicationMetrics: ElasticBeanstalkClientTypes.ApplicationMetrics? = nil,
            availabilityZone: Swift.String? = nil,
            causes: [Swift.String]? = nil,
            color: Swift.String? = nil,
            deployment: ElasticBeanstalkClientTypes.Deployment? = nil,
            healthStatus: Swift.String? = nil,
            instanceId: Swift.String? = nil,
            instanceType: Swift.String? = nil,
            launchedAt: Foundation.Date? = nil,
            system: ElasticBeanstalkClientTypes.SystemStatus? = nil
        )
        {
            self.applicationMetrics = applicationMetrics
            self.availabilityZone = availabilityZone
            self.causes = causes
            self.color = color
            self.deployment = deployment
            self.healthStatus = healthStatus
            self.instanceId = instanceId
            self.instanceType = instanceType
            self.launchedAt = launchedAt
            self.system = system
        }
    }
}

/// Detailed health information about the Amazon EC2 instances in an AWS Elastic Beanstalk environment.
public struct DescribeInstancesHealthOutput: Swift.Sendable {
    /// Detailed health information about each instance. The output differs slightly between Linux and Windows environments. There is a difference in the members that are supported under the  type.
    public var instanceHealthList: [ElasticBeanstalkClientTypes.SingleInstanceHealth]?
    /// Pagination token for the next page of results, if available.
    public var nextToken: Swift.String?
    /// The date and time that the health information was retrieved.
    public var refreshedAt: Foundation.Date?

    public init(
        instanceHealthList: [ElasticBeanstalkClientTypes.SingleInstanceHealth]? = nil,
        nextToken: Swift.String? = nil,
        refreshedAt: Foundation.Date? = nil
    )
    {
        self.instanceHealthList = instanceHealthList
        self.nextToken = nextToken
        self.refreshedAt = refreshedAt
    }
}

public struct DescribePlatformVersionInput: Swift.Sendable {
    /// The ARN of the platform version.
    public var platformArn: Swift.String?

    public init(
        platformArn: Swift.String? = nil
    )
    {
        self.platformArn = platformArn
    }
}

extension ElasticBeanstalkClientTypes {

    /// A custom AMI available to platforms.
    public struct CustomAmi: Swift.Sendable {
        /// THe ID of the image used to create the custom AMI.
        public var imageId: Swift.String?
        /// The type of virtualization used to create the custom AMI.
        public var virtualizationType: Swift.String?

        public init(
            imageId: Swift.String? = nil,
            virtualizationType: Swift.String? = nil
        )
        {
            self.imageId = imageId
            self.virtualizationType = virtualizationType
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// A framework supported by the platform.
    public struct PlatformFramework: Swift.Sendable {
        /// The name of the framework.
        public var name: Swift.String?
        /// The version of the framework.
        public var version: Swift.String?

        public init(
            name: Swift.String? = nil,
            version: Swift.String? = nil
        )
        {
            self.name = name
            self.version = version
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// A programming language supported by the platform.
    public struct PlatformProgrammingLanguage: Swift.Sendable {
        /// The name of the programming language.
        public var name: Swift.String?
        /// The version of the programming language.
        public var version: Swift.String?

        public init(
            name: Swift.String? = nil,
            version: Swift.String? = nil
        )
        {
            self.name = name
            self.version = version
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// Detailed information about a platform version.
    public struct PlatformDescription: Swift.Sendable {
        /// The custom AMIs supported by the platform version.
        public var customAmiList: [ElasticBeanstalkClientTypes.CustomAmi]?
        /// The date when the platform version was created.
        public var dateCreated: Foundation.Date?
        /// The date when the platform version was last updated.
        public var dateUpdated: Foundation.Date?
        /// The description of the platform version.
        public var description: Swift.String?
        /// The frameworks supported by the platform version.
        public var frameworks: [ElasticBeanstalkClientTypes.PlatformFramework]?
        /// Information about the maintainer of the platform version.
        public var maintainer: Swift.String?
        /// The operating system used by the platform version.
        public var operatingSystemName: Swift.String?
        /// The version of the operating system used by the platform version.
        public var operatingSystemVersion: Swift.String?
        /// The ARN of the platform version.
        public var platformArn: Swift.String?
        /// The state of the platform version's branch in its lifecycle. Possible values: Beta | Supported | Deprecated | Retired
        public var platformBranchLifecycleState: Swift.String?
        /// The platform branch to which the platform version belongs.
        public var platformBranchName: Swift.String?
        /// The category of the platform version.
        public var platformCategory: Swift.String?
        /// The state of the platform version in its lifecycle. Possible values: Recommended | null If a null value is returned, the platform version isn't the recommended one for its branch. Each platform branch has a single recommended platform version, typically the most recent one.
        public var platformLifecycleState: Swift.String?
        /// The name of the platform version.
        public var platformName: Swift.String?
        /// The AWS account ID of the person who created the platform version.
        public var platformOwner: Swift.String?
        /// The status of the platform version.
        public var platformStatus: ElasticBeanstalkClientTypes.PlatformStatus?
        /// The version of the platform version.
        public var platformVersion: Swift.String?
        /// The programming languages supported by the platform version.
        public var programmingLanguages: [ElasticBeanstalkClientTypes.PlatformProgrammingLanguage]?
        /// The name of the solution stack used by the platform version.
        public var solutionStackName: Swift.String?
        /// The additions supported by the platform version.
        public var supportedAddonList: [Swift.String]?
        /// The tiers supported by the platform version.
        public var supportedTierList: [Swift.String]?

        public init(
            customAmiList: [ElasticBeanstalkClientTypes.CustomAmi]? = nil,
            dateCreated: Foundation.Date? = nil,
            dateUpdated: Foundation.Date? = nil,
            description: Swift.String? = nil,
            frameworks: [ElasticBeanstalkClientTypes.PlatformFramework]? = nil,
            maintainer: Swift.String? = nil,
            operatingSystemName: Swift.String? = nil,
            operatingSystemVersion: Swift.String? = nil,
            platformArn: Swift.String? = nil,
            platformBranchLifecycleState: Swift.String? = nil,
            platformBranchName: Swift.String? = nil,
            platformCategory: Swift.String? = nil,
            platformLifecycleState: Swift.String? = nil,
            platformName: Swift.String? = nil,
            platformOwner: Swift.String? = nil,
            platformStatus: ElasticBeanstalkClientTypes.PlatformStatus? = nil,
            platformVersion: Swift.String? = nil,
            programmingLanguages: [ElasticBeanstalkClientTypes.PlatformProgrammingLanguage]? = nil,
            solutionStackName: Swift.String? = nil,
            supportedAddonList: [Swift.String]? = nil,
            supportedTierList: [Swift.String]? = nil
        )
        {
            self.customAmiList = customAmiList
            self.dateCreated = dateCreated
            self.dateUpdated = dateUpdated
            self.description = description
            self.frameworks = frameworks
            self.maintainer = maintainer
            self.operatingSystemName = operatingSystemName
            self.operatingSystemVersion = operatingSystemVersion
            self.platformArn = platformArn
            self.platformBranchLifecycleState = platformBranchLifecycleState
            self.platformBranchName = platformBranchName
            self.platformCategory = platformCategory
            self.platformLifecycleState = platformLifecycleState
            self.platformName = platformName
            self.platformOwner = platformOwner
            self.platformStatus = platformStatus
            self.platformVersion = platformVersion
            self.programmingLanguages = programmingLanguages
            self.solutionStackName = solutionStackName
            self.supportedAddonList = supportedAddonList
            self.supportedTierList = supportedTierList
        }
    }
}

public struct DescribePlatformVersionOutput: Swift.Sendable {
    /// Detailed information about the platform version.
    public var platformDescription: ElasticBeanstalkClientTypes.PlatformDescription?

    public init(
        platformDescription: ElasticBeanstalkClientTypes.PlatformDescription? = nil
    )
    {
        self.platformDescription = platformDescription
    }
}

/// Request to disassociate the operations role from an environment.
public struct DisassociateEnvironmentOperationsRoleInput: Swift.Sendable {
    /// The name of the environment from which to disassociate the operations role.
    /// This member is required.
    public var environmentName: Swift.String?

    public init(
        environmentName: Swift.String? = nil
    )
    {
        self.environmentName = environmentName
    }
}

/// A list of available AWS Elastic Beanstalk solution stacks.
public struct ListAvailableSolutionStacksOutput: Swift.Sendable {
    /// A list of available solution stacks and their [SolutionStackDescription].
    public var solutionStackDetails: [ElasticBeanstalkClientTypes.SolutionStackDescription]?
    /// A list of available solution stacks.
    public var solutionStacks: [Swift.String]?

    public init(
        solutionStackDetails: [ElasticBeanstalkClientTypes.SolutionStackDescription]? = nil,
        solutionStacks: [Swift.String]? = nil
    )
    {
        self.solutionStackDetails = solutionStackDetails
        self.solutionStacks = solutionStacks
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes criteria to restrict a list of results. For operators that apply a single value to the attribute, the filter is evaluated as follows: Attribute Operator Values[1] Some operators, e.g. in, can apply multiple values. In this case, the filter is evaluated as a logical union (OR) of applications of the operator to the attribute with each one of the values: (Attribute Operator Values[1]) OR (Attribute Operator Values[2]) OR ... The valid values for attributes of SearchFilter depend on the API action. For valid values, see the reference page for the API action you're calling that takes a SearchFilter parameter.
    public struct SearchFilter: Swift.Sendable {
        /// The result attribute to which the filter values are applied. Valid values vary by API action.
        public var attribute: Swift.String?
        /// The operator to apply to the Attribute with each of the Values. Valid values vary by Attribute.
        public var `operator`: Swift.String?
        /// The list of values applied to the Attribute and Operator attributes. Number of values and valid values vary by Attribute.
        public var values: [Swift.String]?

        public init(
            attribute: Swift.String? = nil,
            `operator`: Swift.String? = nil,
            values: [Swift.String]? = nil
        )
        {
            self.attribute = attribute
            self.`operator` = `operator`
            self.values = values
        }
    }
}

public struct ListPlatformBranchesInput: Swift.Sendable {
    /// Criteria for restricting the resulting list of platform branches. The filter is evaluated as a logical conjunction (AND) of the separate SearchFilter terms. The following list shows valid attribute values for each of the SearchFilter terms. Most operators take a single value. The in and not_in operators can take multiple values.
    ///
    /// * Attribute = BranchName:
    ///
    /// * Operator: = | != | begins_with | ends_with | contains | in | not_in
    ///
    ///
    ///
    ///
    /// * Attribute = LifecycleState:
    ///
    /// * Operator: = | != | in | not_in
    ///
    /// * Values: beta | supported | deprecated | retired
    ///
    ///
    ///
    ///
    /// * Attribute = PlatformName:
    ///
    /// * Operator: = | != | begins_with | ends_with | contains | in | not_in
    ///
    ///
    ///
    ///
    /// * Attribute = TierType:
    ///
    /// * Operator: = | !=
    ///
    /// * Values: WebServer/Standard | Worker/SQS/HTTP
    ///
    ///
    ///
    ///
    ///
    /// Array size: limited to 10 SearchFilter objects. Within each SearchFilter item, the Values array is limited to 10 items.
    public var filters: [ElasticBeanstalkClientTypes.SearchFilter]?
    /// The maximum number of platform branch values returned in one call.
    public var maxRecords: Swift.Int?
    /// For a paginated request. Specify a token from a previous response page to retrieve the next response page. All other parameter values must be identical to the ones specified in the initial request. If no NextToken is specified, the first page is retrieved.
    public var nextToken: Swift.String?

    public init(
        filters: [ElasticBeanstalkClientTypes.SearchFilter]? = nil,
        maxRecords: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxRecords = maxRecords
        self.nextToken = nextToken
    }
}

extension ElasticBeanstalkClientTypes {

    /// Summary information about a platform branch.
    public struct PlatformBranchSummary: Swift.Sendable {
        /// The name of the platform branch.
        public var branchName: Swift.String?
        /// An ordinal number that designates the order in which platform branches have been added to a platform. This can be helpful, for example, if your code calls the ListPlatformBranches action and then displays a list of platform branches. A larger BranchOrder value designates a newer platform branch within the platform.
        public var branchOrder: Swift.Int
        /// The support life cycle state of the platform branch. Possible values: beta | supported | deprecated | retired
        public var lifecycleState: Swift.String?
        /// The name of the platform to which this platform branch belongs.
        public var platformName: Swift.String?
        /// The environment tiers that platform versions in this branch support. Possible values: WebServer/Standard | Worker/SQS/HTTP
        public var supportedTierList: [Swift.String]?

        public init(
            branchName: Swift.String? = nil,
            branchOrder: Swift.Int = 0,
            lifecycleState: Swift.String? = nil,
            platformName: Swift.String? = nil,
            supportedTierList: [Swift.String]? = nil
        )
        {
            self.branchName = branchName
            self.branchOrder = branchOrder
            self.lifecycleState = lifecycleState
            self.platformName = platformName
            self.supportedTierList = supportedTierList
        }
    }
}

public struct ListPlatformBranchesOutput: Swift.Sendable {
    /// In a paginated request, if this value isn't null, it's the token that you can pass in a subsequent request to get the next response page.
    public var nextToken: Swift.String?
    /// Summary information about the platform branches.
    public var platformBranchSummaryList: [ElasticBeanstalkClientTypes.PlatformBranchSummary]?

    public init(
        nextToken: Swift.String? = nil,
        platformBranchSummaryList: [ElasticBeanstalkClientTypes.PlatformBranchSummary]? = nil
    )
    {
        self.nextToken = nextToken
        self.platformBranchSummaryList = platformBranchSummaryList
    }
}

extension ElasticBeanstalkClientTypes {

    /// Describes criteria to restrict the results when listing platform versions. The filter is evaluated as follows: Type Operator Values[1]
    public struct PlatformFilter: Swift.Sendable {
        /// The operator to apply to the Type with each of the Values. Valid values: = | != | < | <= | > | >= | contains | begins_with | ends_with
        public var `operator`: Swift.String?
        /// The platform version attribute to which the filter values are applied. Valid values: PlatformName | PlatformVersion | PlatformStatus | PlatformBranchName | PlatformLifecycleState | PlatformOwner | SupportedTier | SupportedAddon | ProgrammingLanguageName | OperatingSystemName
        public var type: Swift.String?
        /// The list of values applied to the filtering platform version attribute. Only one value is supported for all current operators. The following list shows valid filter values for some filter attributes.
        ///
        /// * PlatformStatus: Creating | Failed | Ready | Deleting | Deleted
        ///
        /// * PlatformLifecycleState: recommended
        ///
        /// * SupportedTier: WebServer/Standard | Worker/SQS/HTTP
        ///
        /// * SupportedAddon: Log/S3 | Monitoring/Healthd | WorkerDaemon/SQSD
        public var values: [Swift.String]?

        public init(
            `operator`: Swift.String? = nil,
            type: Swift.String? = nil,
            values: [Swift.String]? = nil
        )
        {
            self.`operator` = `operator`
            self.type = type
            self.values = values
        }
    }
}

public struct ListPlatformVersionsInput: Swift.Sendable {
    /// Criteria for restricting the resulting list of platform versions. The filter is interpreted as a logical conjunction (AND) of the separate PlatformFilter terms.
    public var filters: [ElasticBeanstalkClientTypes.PlatformFilter]?
    /// The maximum number of platform version values returned in one call.
    public var maxRecords: Swift.Int?
    /// For a paginated request. Specify a token from a previous response page to retrieve the next response page. All other parameter values must be identical to the ones specified in the initial request. If no NextToken is specified, the first page is retrieved.
    public var nextToken: Swift.String?

    public init(
        filters: [ElasticBeanstalkClientTypes.PlatformFilter]? = nil,
        maxRecords: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxRecords = maxRecords
        self.nextToken = nextToken
    }
}

public struct ListPlatformVersionsOutput: Swift.Sendable {
    /// In a paginated request, if this value isn't null, it's the token that you can pass in a subsequent request to get the next response page.
    public var nextToken: Swift.String?
    /// Summary information about the platform versions.
    public var platformSummaryList: [ElasticBeanstalkClientTypes.PlatformSummary]?

    public init(
        nextToken: Swift.String? = nil,
        platformSummaryList: [ElasticBeanstalkClientTypes.PlatformSummary]? = nil
    )
    {
        self.nextToken = nextToken
        self.platformSummaryList = platformSummaryList
    }
}

/// A resource doesn't exist for the specified Amazon Resource Name (ARN).
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The type of the specified Amazon Resource Name (ARN) isn't supported for this operation.
public struct ResourceTypeNotSupportedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceTypeNotSupportedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resouce for which a tag list is requested. Must be the ARN of an Elastic Beanstalk resource.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource for which a tag list was requested.
    public var resourceArn: Swift.String?
    /// A list of tag key-value pairs.
    public var resourceTags: [ElasticBeanstalkClientTypes.Tag]?

    public init(
        resourceArn: Swift.String? = nil,
        resourceTags: [ElasticBeanstalkClientTypes.Tag]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.resourceTags = resourceTags
    }
}

///
public struct RebuildEnvironmentInput: Swift.Sendable {
    /// The ID of the environment to rebuild. Condition: You must specify either this or an EnvironmentName, or both. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentId: Swift.String?
    /// The name of the environment to rebuild. Condition: You must specify either this or an EnvironmentId, or both. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentName: Swift.String?

    public init(
        environmentId: Swift.String? = nil,
        environmentName: Swift.String? = nil
    )
    {
        self.environmentId = environmentId
        self.environmentName = environmentName
    }
}

extension ElasticBeanstalkClientTypes {

    public enum EnvironmentInfoType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case bundle
        case tail
        case sdkUnknown(Swift.String)

        public static var allCases: [EnvironmentInfoType] {
            return [
                .bundle,
                .tail
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .bundle: return "bundle"
            case .tail: return "tail"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Request to retrieve logs from an environment and store them in your Elastic Beanstalk storage bucket.
public struct RequestEnvironmentInfoInput: Swift.Sendable {
    /// The ID of the environment of the requested data. If no such environment is found, RequestEnvironmentInfo returns an InvalidParameterValue error. Condition: You must specify either this or an EnvironmentName, or both. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentId: Swift.String?
    /// The name of the environment of the requested data. If no such environment is found, RequestEnvironmentInfo returns an InvalidParameterValue error. Condition: You must specify either this or an EnvironmentId, or both. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentName: Swift.String?
    /// The type of information to request.
    /// This member is required.
    public var infoType: ElasticBeanstalkClientTypes.EnvironmentInfoType?

    public init(
        environmentId: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        infoType: ElasticBeanstalkClientTypes.EnvironmentInfoType? = nil
    )
    {
        self.environmentId = environmentId
        self.environmentName = environmentName
        self.infoType = infoType
    }
}

///
public struct RestartAppServerInput: Swift.Sendable {
    /// The ID of the environment to restart the server for. Condition: You must specify either this or an EnvironmentName, or both. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentId: Swift.String?
    /// The name of the environment to restart the server for. Condition: You must specify either this or an EnvironmentId, or both. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentName: Swift.String?

    public init(
        environmentId: Swift.String? = nil,
        environmentName: Swift.String? = nil
    )
    {
        self.environmentId = environmentId
        self.environmentName = environmentName
    }
}

/// Request to download logs retrieved with [RequestEnvironmentInfo].
public struct RetrieveEnvironmentInfoInput: Swift.Sendable {
    /// The ID of the data's environment. If no such environment is found, returns an InvalidParameterValue error. Condition: You must specify either this or an EnvironmentName, or both. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentId: Swift.String?
    /// The name of the data's environment. If no such environment is found, returns an InvalidParameterValue error. Condition: You must specify either this or an EnvironmentId, or both. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentName: Swift.String?
    /// The type of information to retrieve.
    /// This member is required.
    public var infoType: ElasticBeanstalkClientTypes.EnvironmentInfoType?

    public init(
        environmentId: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        infoType: ElasticBeanstalkClientTypes.EnvironmentInfoType? = nil
    )
    {
        self.environmentId = environmentId
        self.environmentName = environmentName
        self.infoType = infoType
    }
}

extension ElasticBeanstalkClientTypes {

    /// The information retrieved from the Amazon EC2 instances.
    public struct EnvironmentInfoDescription: Swift.Sendable {
        /// The Amazon EC2 Instance ID for this information.
        public var ec2InstanceId: Swift.String?
        /// The type of information retrieved.
        public var infoType: ElasticBeanstalkClientTypes.EnvironmentInfoType?
        /// The retrieved information. Currently contains a presigned Amazon S3 URL. The files are deleted after 15 minutes. Anyone in possession of this URL can access the files before they are deleted. Make the URL available only to trusted parties.
        public var message: Swift.String?
        /// The time stamp when this information was retrieved.
        public var sampleTimestamp: Foundation.Date?

        public init(
            ec2InstanceId: Swift.String? = nil,
            infoType: ElasticBeanstalkClientTypes.EnvironmentInfoType? = nil,
            message: Swift.String? = nil,
            sampleTimestamp: Foundation.Date? = nil
        )
        {
            self.ec2InstanceId = ec2InstanceId
            self.infoType = infoType
            self.message = message
            self.sampleTimestamp = sampleTimestamp
        }
    }
}

/// Result message containing a description of the requested environment info.
public struct RetrieveEnvironmentInfoOutput: Swift.Sendable {
    /// The [EnvironmentInfoDescription] of the environment.
    public var environmentInfo: [ElasticBeanstalkClientTypes.EnvironmentInfoDescription]?

    public init(
        environmentInfo: [ElasticBeanstalkClientTypes.EnvironmentInfoDescription]? = nil
    )
    {
        self.environmentInfo = environmentInfo
    }
}

/// Swaps the CNAMEs of two environments.
public struct SwapEnvironmentCNAMEsInput: Swift.Sendable {
    /// The ID of the destination environment. Condition: You must specify at least the DestinationEnvironmentID or the DestinationEnvironmentName. You may also specify both. You must specify the SourceEnvironmentId with the DestinationEnvironmentId.
    public var destinationEnvironmentId: Swift.String?
    /// The name of the destination environment. Condition: You must specify at least the DestinationEnvironmentID or the DestinationEnvironmentName. You may also specify both. You must specify the SourceEnvironmentName with the DestinationEnvironmentName.
    public var destinationEnvironmentName: Swift.String?
    /// The ID of the source environment. Condition: You must specify at least the SourceEnvironmentID or the SourceEnvironmentName. You may also specify both. If you specify the SourceEnvironmentId, you must specify the DestinationEnvironmentId.
    public var sourceEnvironmentId: Swift.String?
    /// The name of the source environment. Condition: You must specify at least the SourceEnvironmentID or the SourceEnvironmentName. You may also specify both. If you specify the SourceEnvironmentName, you must specify the DestinationEnvironmentName.
    public var sourceEnvironmentName: Swift.String?

    public init(
        destinationEnvironmentId: Swift.String? = nil,
        destinationEnvironmentName: Swift.String? = nil,
        sourceEnvironmentId: Swift.String? = nil,
        sourceEnvironmentName: Swift.String? = nil
    )
    {
        self.destinationEnvironmentId = destinationEnvironmentId
        self.destinationEnvironmentName = destinationEnvironmentName
        self.sourceEnvironmentId = sourceEnvironmentId
        self.sourceEnvironmentName = sourceEnvironmentName
    }
}

/// Request to terminate an environment.
public struct TerminateEnvironmentInput: Swift.Sendable {
    /// The ID of the environment to terminate. Condition: You must specify either this or an EnvironmentName, or both. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentId: Swift.String?
    /// The name of the environment to terminate. Condition: You must specify either this or an EnvironmentId, or both. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentName: Swift.String?
    /// Terminates the target environment even if another environment in the same group is dependent on it.
    public var forceTerminate: Swift.Bool?
    /// Indicates whether the associated AWS resources should shut down when the environment is terminated:
    ///
    /// * true: The specified environment as well as the associated AWS resources, such as Auto Scaling group and LoadBalancer, are terminated.
    ///
    /// * false: AWS Elastic Beanstalk resource management is removed from the environment, but the AWS resources continue to operate.
    ///
    ///
    /// For more information, see the [ AWS Elastic Beanstalk User Guide. ](https://docs.aws.amazon.com/elasticbeanstalk/latest/ug/) Default: true Valid Values: true | false
    public var terminateResources: Swift.Bool?

    public init(
        environmentId: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        forceTerminate: Swift.Bool? = nil,
        terminateResources: Swift.Bool? = nil
    )
    {
        self.environmentId = environmentId
        self.environmentName = environmentName
        self.forceTerminate = forceTerminate
        self.terminateResources = terminateResources
    }
}

/// Describes the properties of an environment.
public struct TerminateEnvironmentOutput: Swift.Sendable {
    /// Indicates if there is an in-progress environment configuration update or application version deployment that you can cancel. true: There is an update in progress. false: There are no updates currently in progress.
    public var abortableOperationInProgress: Swift.Bool?
    /// The name of the application associated with this environment.
    public var applicationName: Swift.String?
    /// The URL to the CNAME for this environment.
    public var cname: Swift.String?
    /// The creation date for this environment.
    public var dateCreated: Foundation.Date?
    /// The last modified date for this environment.
    public var dateUpdated: Foundation.Date?
    /// Describes this environment.
    public var description: Swift.String?
    /// For load-balanced, autoscaling environments, the URL to the LoadBalancer. For single-instance environments, the IP address of the instance.
    public var endpointURL: Swift.String?
    /// The environment's Amazon Resource Name (ARN), which can be used in other API requests that require an ARN.
    public var environmentArn: Swift.String?
    /// The ID of this environment.
    public var environmentId: Swift.String?
    /// A list of links to other environments in the same group.
    public var environmentLinks: [ElasticBeanstalkClientTypes.EnvironmentLink]?
    /// The name of this environment.
    public var environmentName: Swift.String?
    /// Describes the health status of the environment. AWS Elastic Beanstalk indicates the failure levels for a running environment:
    ///
    /// * Red: Indicates the environment is not responsive. Occurs when three or more consecutive failures occur for an environment.
    ///
    /// * Yellow: Indicates that something is wrong. Occurs when two consecutive failures occur for an environment.
    ///
    /// * Green: Indicates the environment is healthy and fully functional.
    ///
    /// * Grey: Default health for a new environment. The environment is not fully launched and health checks have not started or health checks are suspended during an UpdateEnvironment or RestartEnvironment request.
    ///
    ///
    /// Default: Grey
    public var health: ElasticBeanstalkClientTypes.EnvironmentHealth?
    /// Returns the health status of the application running in your environment. For more information, see [Health Colors and Statuses](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/health-enhanced-status.html).
    public var healthStatus: ElasticBeanstalkClientTypes.EnvironmentHealthStatus?
    /// The Amazon Resource Name (ARN) of the environment's operations role. For more information, see [Operations roles](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/iam-operationsrole.html) in the AWS Elastic Beanstalk Developer Guide.
    public var operationsRole: Swift.String?
    /// The ARN of the platform version.
    public var platformArn: Swift.String?
    /// The description of the AWS resources used by this environment.
    public var resources: ElasticBeanstalkClientTypes.EnvironmentResourcesDescription?
    /// The name of the SolutionStack deployed with this environment.
    public var solutionStackName: Swift.String?
    /// The current operational status of the environment:
    ///
    /// * Launching: Environment is in the process of initial deployment.
    ///
    /// * Updating: Environment is in the process of updating its configuration settings or application version.
    ///
    /// * Ready: Environment is available to have an action performed on it, such as update or terminate.
    ///
    /// * Terminating: Environment is in the shut-down process.
    ///
    /// * Terminated: Environment is not running.
    public var status: ElasticBeanstalkClientTypes.EnvironmentStatus?
    /// The name of the configuration template used to originally launch this environment.
    public var templateName: Swift.String?
    /// Describes the current tier of this environment.
    public var tier: ElasticBeanstalkClientTypes.EnvironmentTier?
    /// The application version deployed in this environment.
    public var versionLabel: Swift.String?

    public init(
        abortableOperationInProgress: Swift.Bool? = nil,
        applicationName: Swift.String? = nil,
        cname: Swift.String? = nil,
        dateCreated: Foundation.Date? = nil,
        dateUpdated: Foundation.Date? = nil,
        description: Swift.String? = nil,
        endpointURL: Swift.String? = nil,
        environmentArn: Swift.String? = nil,
        environmentId: Swift.String? = nil,
        environmentLinks: [ElasticBeanstalkClientTypes.EnvironmentLink]? = nil,
        environmentName: Swift.String? = nil,
        health: ElasticBeanstalkClientTypes.EnvironmentHealth? = nil,
        healthStatus: ElasticBeanstalkClientTypes.EnvironmentHealthStatus? = nil,
        operationsRole: Swift.String? = nil,
        platformArn: Swift.String? = nil,
        resources: ElasticBeanstalkClientTypes.EnvironmentResourcesDescription? = nil,
        solutionStackName: Swift.String? = nil,
        status: ElasticBeanstalkClientTypes.EnvironmentStatus? = nil,
        templateName: Swift.String? = nil,
        tier: ElasticBeanstalkClientTypes.EnvironmentTier? = nil,
        versionLabel: Swift.String? = nil
    )
    {
        self.abortableOperationInProgress = abortableOperationInProgress
        self.applicationName = applicationName
        self.cname = cname
        self.dateCreated = dateCreated
        self.dateUpdated = dateUpdated
        self.description = description
        self.endpointURL = endpointURL
        self.environmentArn = environmentArn
        self.environmentId = environmentId
        self.environmentLinks = environmentLinks
        self.environmentName = environmentName
        self.health = health
        self.healthStatus = healthStatus
        self.operationsRole = operationsRole
        self.platformArn = platformArn
        self.resources = resources
        self.solutionStackName = solutionStackName
        self.status = status
        self.templateName = templateName
        self.tier = tier
        self.versionLabel = versionLabel
    }
}

/// Request to update an application.
public struct UpdateApplicationInput: Swift.Sendable {
    /// The name of the application to update. If no such application is found, UpdateApplication returns an InvalidParameterValue error.
    /// This member is required.
    public var applicationName: Swift.String?
    /// A new description for the application. Default: If not specified, AWS Elastic Beanstalk does not update the description.
    public var description: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        description: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.description = description
    }
}

/// Result message containing a single description of an application.
public struct UpdateApplicationOutput: Swift.Sendable {
    /// The [ApplicationDescription] of the application.
    public var application: ElasticBeanstalkClientTypes.ApplicationDescription?

    public init(
        application: ElasticBeanstalkClientTypes.ApplicationDescription? = nil
    )
    {
        self.application = application
    }
}

public struct UpdateApplicationResourceLifecycleInput: Swift.Sendable {
    /// The name of the application.
    /// This member is required.
    public var applicationName: Swift.String?
    /// The lifecycle configuration.
    /// This member is required.
    public var resourceLifecycleConfig: ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig?

    public init(
        applicationName: Swift.String? = nil,
        resourceLifecycleConfig: ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig? = nil
    )
    {
        self.applicationName = applicationName
        self.resourceLifecycleConfig = resourceLifecycleConfig
    }
}

public struct UpdateApplicationResourceLifecycleOutput: Swift.Sendable {
    /// The name of the application.
    public var applicationName: Swift.String?
    /// The lifecycle configuration.
    public var resourceLifecycleConfig: ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig?

    public init(
        applicationName: Swift.String? = nil,
        resourceLifecycleConfig: ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig? = nil
    )
    {
        self.applicationName = applicationName
        self.resourceLifecycleConfig = resourceLifecycleConfig
    }
}

///
public struct UpdateApplicationVersionInput: Swift.Sendable {
    /// The name of the application associated with this version. If no application is found with this name, UpdateApplication returns an InvalidParameterValue error.
    /// This member is required.
    public var applicationName: Swift.String?
    /// A new description for this version.
    public var description: Swift.String?
    /// The name of the version to update. If no application version is found with this label, UpdateApplication returns an InvalidParameterValue error.
    /// This member is required.
    public var versionLabel: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        description: Swift.String? = nil,
        versionLabel: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.description = description
        self.versionLabel = versionLabel
    }
}

/// Result message wrapping a single description of an application version.
public struct UpdateApplicationVersionOutput: Swift.Sendable {
    /// The [ApplicationVersionDescription] of the application version.
    public var applicationVersion: ElasticBeanstalkClientTypes.ApplicationVersionDescription?

    public init(
        applicationVersion: ElasticBeanstalkClientTypes.ApplicationVersionDescription? = nil
    )
    {
        self.applicationVersion = applicationVersion
    }
}

/// The result message containing the options for the specified solution stack.
public struct UpdateConfigurationTemplateInput: Swift.Sendable {
    /// The name of the application associated with the configuration template to update. If no application is found with this name, UpdateConfigurationTemplate returns an InvalidParameterValue error.
    /// This member is required.
    public var applicationName: Swift.String?
    /// A new description for the configuration.
    public var description: Swift.String?
    /// A list of configuration option settings to update with the new specified option value.
    public var optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]?
    /// A list of configuration options to remove from the configuration set. Constraint: You can remove only UserDefined configuration options.
    public var optionsToRemove: [ElasticBeanstalkClientTypes.OptionSpecification]?
    /// The name of the configuration template to update. If no configuration template is found with this name, UpdateConfigurationTemplate returns an InvalidParameterValue error.
    /// This member is required.
    public var templateName: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        description: Swift.String? = nil,
        optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]? = nil,
        optionsToRemove: [ElasticBeanstalkClientTypes.OptionSpecification]? = nil,
        templateName: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.description = description
        self.optionSettings = optionSettings
        self.optionsToRemove = optionsToRemove
        self.templateName = templateName
    }
}

/// Describes the settings for a configuration set.
public struct UpdateConfigurationTemplateOutput: Swift.Sendable {
    /// The name of the application associated with this configuration set.
    public var applicationName: Swift.String?
    /// The date (in UTC time) when this configuration set was created.
    public var dateCreated: Foundation.Date?
    /// The date (in UTC time) when this configuration set was last modified.
    public var dateUpdated: Foundation.Date?
    /// If this configuration set is associated with an environment, the DeploymentStatus parameter indicates the deployment status of this configuration set:
    ///
    /// * null: This configuration is not associated with a running environment.
    ///
    /// * pending: This is a draft configuration that is not deployed to the associated environment but is in the process of deploying.
    ///
    /// * deployed: This is the configuration that is currently deployed to the associated running environment.
    ///
    /// * failed: This is a draft configuration that failed to successfully deploy.
    public var deploymentStatus: ElasticBeanstalkClientTypes.ConfigurationDeploymentStatus?
    /// Describes this configuration set.
    public var description: Swift.String?
    /// If not null, the name of the environment for this configuration set.
    public var environmentName: Swift.String?
    /// A list of the configuration options and their values in this configuration set.
    public var optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]?
    /// The ARN of the platform version.
    public var platformArn: Swift.String?
    /// The name of the solution stack this configuration set uses.
    public var solutionStackName: Swift.String?
    /// If not null, the name of the configuration template for this configuration set.
    public var templateName: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        dateCreated: Foundation.Date? = nil,
        dateUpdated: Foundation.Date? = nil,
        deploymentStatus: ElasticBeanstalkClientTypes.ConfigurationDeploymentStatus? = nil,
        description: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]? = nil,
        platformArn: Swift.String? = nil,
        solutionStackName: Swift.String? = nil,
        templateName: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.dateCreated = dateCreated
        self.dateUpdated = dateUpdated
        self.deploymentStatus = deploymentStatus
        self.description = description
        self.environmentName = environmentName
        self.optionSettings = optionSettings
        self.platformArn = platformArn
        self.solutionStackName = solutionStackName
        self.templateName = templateName
    }
}

/// Request to update an environment.
public struct UpdateEnvironmentInput: Swift.Sendable {
    /// The name of the application with which the environment is associated.
    public var applicationName: Swift.String?
    /// If this parameter is specified, AWS Elastic Beanstalk updates the description of this environment.
    public var description: Swift.String?
    /// The ID of the environment to update. If no environment with this ID exists, AWS Elastic Beanstalk returns an InvalidParameterValue error. Condition: You must specify either this or an EnvironmentName, or both. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentId: Swift.String?
    /// The name of the environment to update. If no environment with this name exists, AWS Elastic Beanstalk returns an InvalidParameterValue error. Condition: You must specify either this or an EnvironmentId, or both. If you do not specify either, AWS Elastic Beanstalk returns MissingRequiredParameter error.
    public var environmentName: Swift.String?
    /// The name of the group to which the target environment belongs. Specify a group name only if the environment's name is specified in an environment manifest and not with the environment name or environment ID parameters. See [Environment Manifest (env.yaml)](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/environment-cfg-manifest.html) for details.
    public var groupName: Swift.String?
    /// If specified, AWS Elastic Beanstalk updates the configuration set associated with the running environment and sets the specified configuration options to the requested value.
    public var optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]?
    /// A list of custom user-defined configuration options to remove from the configuration set for this environment.
    public var optionsToRemove: [ElasticBeanstalkClientTypes.OptionSpecification]?
    /// The ARN of the platform, if used.
    public var platformArn: Swift.String?
    /// This specifies the platform version that the environment will run after the environment is updated.
    public var solutionStackName: Swift.String?
    /// If this parameter is specified, AWS Elastic Beanstalk deploys this configuration template to the environment. If no such configuration template is found, AWS Elastic Beanstalk returns an InvalidParameterValue error.
    public var templateName: Swift.String?
    /// This specifies the tier to use to update the environment. Condition: At this time, if you change the tier version, name, or type, AWS Elastic Beanstalk returns InvalidParameterValue error.
    public var tier: ElasticBeanstalkClientTypes.EnvironmentTier?
    /// If this parameter is specified, AWS Elastic Beanstalk deploys the named application version to the environment. If no such application version is found, returns an InvalidParameterValue error.
    public var versionLabel: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        description: Swift.String? = nil,
        environmentId: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        groupName: Swift.String? = nil,
        optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]? = nil,
        optionsToRemove: [ElasticBeanstalkClientTypes.OptionSpecification]? = nil,
        platformArn: Swift.String? = nil,
        solutionStackName: Swift.String? = nil,
        templateName: Swift.String? = nil,
        tier: ElasticBeanstalkClientTypes.EnvironmentTier? = nil,
        versionLabel: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.description = description
        self.environmentId = environmentId
        self.environmentName = environmentName
        self.groupName = groupName
        self.optionSettings = optionSettings
        self.optionsToRemove = optionsToRemove
        self.platformArn = platformArn
        self.solutionStackName = solutionStackName
        self.templateName = templateName
        self.tier = tier
        self.versionLabel = versionLabel
    }
}

/// Describes the properties of an environment.
public struct UpdateEnvironmentOutput: Swift.Sendable {
    /// Indicates if there is an in-progress environment configuration update or application version deployment that you can cancel. true: There is an update in progress. false: There are no updates currently in progress.
    public var abortableOperationInProgress: Swift.Bool?
    /// The name of the application associated with this environment.
    public var applicationName: Swift.String?
    /// The URL to the CNAME for this environment.
    public var cname: Swift.String?
    /// The creation date for this environment.
    public var dateCreated: Foundation.Date?
    /// The last modified date for this environment.
    public var dateUpdated: Foundation.Date?
    /// Describes this environment.
    public var description: Swift.String?
    /// For load-balanced, autoscaling environments, the URL to the LoadBalancer. For single-instance environments, the IP address of the instance.
    public var endpointURL: Swift.String?
    /// The environment's Amazon Resource Name (ARN), which can be used in other API requests that require an ARN.
    public var environmentArn: Swift.String?
    /// The ID of this environment.
    public var environmentId: Swift.String?
    /// A list of links to other environments in the same group.
    public var environmentLinks: [ElasticBeanstalkClientTypes.EnvironmentLink]?
    /// The name of this environment.
    public var environmentName: Swift.String?
    /// Describes the health status of the environment. AWS Elastic Beanstalk indicates the failure levels for a running environment:
    ///
    /// * Red: Indicates the environment is not responsive. Occurs when three or more consecutive failures occur for an environment.
    ///
    /// * Yellow: Indicates that something is wrong. Occurs when two consecutive failures occur for an environment.
    ///
    /// * Green: Indicates the environment is healthy and fully functional.
    ///
    /// * Grey: Default health for a new environment. The environment is not fully launched and health checks have not started or health checks are suspended during an UpdateEnvironment or RestartEnvironment request.
    ///
    ///
    /// Default: Grey
    public var health: ElasticBeanstalkClientTypes.EnvironmentHealth?
    /// Returns the health status of the application running in your environment. For more information, see [Health Colors and Statuses](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/health-enhanced-status.html).
    public var healthStatus: ElasticBeanstalkClientTypes.EnvironmentHealthStatus?
    /// The Amazon Resource Name (ARN) of the environment's operations role. For more information, see [Operations roles](https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/iam-operationsrole.html) in the AWS Elastic Beanstalk Developer Guide.
    public var operationsRole: Swift.String?
    /// The ARN of the platform version.
    public var platformArn: Swift.String?
    /// The description of the AWS resources used by this environment.
    public var resources: ElasticBeanstalkClientTypes.EnvironmentResourcesDescription?
    /// The name of the SolutionStack deployed with this environment.
    public var solutionStackName: Swift.String?
    /// The current operational status of the environment:
    ///
    /// * Launching: Environment is in the process of initial deployment.
    ///
    /// * Updating: Environment is in the process of updating its configuration settings or application version.
    ///
    /// * Ready: Environment is available to have an action performed on it, such as update or terminate.
    ///
    /// * Terminating: Environment is in the shut-down process.
    ///
    /// * Terminated: Environment is not running.
    public var status: ElasticBeanstalkClientTypes.EnvironmentStatus?
    /// The name of the configuration template used to originally launch this environment.
    public var templateName: Swift.String?
    /// Describes the current tier of this environment.
    public var tier: ElasticBeanstalkClientTypes.EnvironmentTier?
    /// The application version deployed in this environment.
    public var versionLabel: Swift.String?

    public init(
        abortableOperationInProgress: Swift.Bool? = nil,
        applicationName: Swift.String? = nil,
        cname: Swift.String? = nil,
        dateCreated: Foundation.Date? = nil,
        dateUpdated: Foundation.Date? = nil,
        description: Swift.String? = nil,
        endpointURL: Swift.String? = nil,
        environmentArn: Swift.String? = nil,
        environmentId: Swift.String? = nil,
        environmentLinks: [ElasticBeanstalkClientTypes.EnvironmentLink]? = nil,
        environmentName: Swift.String? = nil,
        health: ElasticBeanstalkClientTypes.EnvironmentHealth? = nil,
        healthStatus: ElasticBeanstalkClientTypes.EnvironmentHealthStatus? = nil,
        operationsRole: Swift.String? = nil,
        platformArn: Swift.String? = nil,
        resources: ElasticBeanstalkClientTypes.EnvironmentResourcesDescription? = nil,
        solutionStackName: Swift.String? = nil,
        status: ElasticBeanstalkClientTypes.EnvironmentStatus? = nil,
        templateName: Swift.String? = nil,
        tier: ElasticBeanstalkClientTypes.EnvironmentTier? = nil,
        versionLabel: Swift.String? = nil
    )
    {
        self.abortableOperationInProgress = abortableOperationInProgress
        self.applicationName = applicationName
        self.cname = cname
        self.dateCreated = dateCreated
        self.dateUpdated = dateUpdated
        self.description = description
        self.endpointURL = endpointURL
        self.environmentArn = environmentArn
        self.environmentId = environmentId
        self.environmentLinks = environmentLinks
        self.environmentName = environmentName
        self.health = health
        self.healthStatus = healthStatus
        self.operationsRole = operationsRole
        self.platformArn = platformArn
        self.resources = resources
        self.solutionStackName = solutionStackName
        self.status = status
        self.templateName = templateName
        self.tier = tier
        self.versionLabel = versionLabel
    }
}

/// The number of tags in the resource would exceed the number of tags that each resource can have. To calculate this, the operation considers both the number of tags the resource already has and the tags this operation would add if it succeeded.
public struct TooManyTagsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The exception error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TooManyTagsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct UpdateTagsForResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resouce to be updated. Must be the ARN of an Elastic Beanstalk resource.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// A list of tags to add or update. If a key of an existing tag is added, the tag's value is updated. Specify at least one of these parameters: TagsToAdd, TagsToRemove.
    public var tagsToAdd: [ElasticBeanstalkClientTypes.Tag]?
    /// A list of tag keys to remove. If a tag key doesn't exist, it is silently ignored. Specify at least one of these parameters: TagsToAdd, TagsToRemove.
    public var tagsToRemove: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagsToAdd: [ElasticBeanstalkClientTypes.Tag]? = nil,
        tagsToRemove: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagsToAdd = tagsToAdd
        self.tagsToRemove = tagsToRemove
    }
}

/// A list of validation messages for a specified configuration template.
public struct ValidateConfigurationSettingsInput: Swift.Sendable {
    /// The name of the application that the configuration template or environment belongs to.
    /// This member is required.
    public var applicationName: Swift.String?
    /// The name of the environment to validate the settings against. Condition: You cannot specify both this and a configuration template name.
    public var environmentName: Swift.String?
    /// A list of the options and desired values to evaluate.
    /// This member is required.
    public var optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]?
    /// The name of the configuration template to validate the settings against. Condition: You cannot specify both this and an environment name.
    public var templateName: Swift.String?

    public init(
        applicationName: Swift.String? = nil,
        environmentName: Swift.String? = nil,
        optionSettings: [ElasticBeanstalkClientTypes.ConfigurationOptionSetting]? = nil,
        templateName: Swift.String? = nil
    )
    {
        self.applicationName = applicationName
        self.environmentName = environmentName
        self.optionSettings = optionSettings
        self.templateName = templateName
    }
}

extension ElasticBeanstalkClientTypes {

    public enum ValidationSeverity: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case error
        case warning
        case sdkUnknown(Swift.String)

        public static var allCases: [ValidationSeverity] {
            return [
                .error,
                .warning
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .error: return "error"
            case .warning: return "warning"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticBeanstalkClientTypes {

    /// An error or warning for a desired configuration option value.
    public struct ValidationMessage: Swift.Sendable {
        /// A message describing the error or warning.
        public var message: Swift.String?
        /// The namespace to which the option belongs.
        public var namespace: Swift.String?
        /// The name of the option.
        public var optionName: Swift.String?
        /// An indication of the severity of this message:
        ///
        /// * error: This message indicates that this is not a valid setting for an option.
        ///
        /// * warning: This message is providing information you should take into account.
        public var severity: ElasticBeanstalkClientTypes.ValidationSeverity?

        public init(
            message: Swift.String? = nil,
            namespace: Swift.String? = nil,
            optionName: Swift.String? = nil,
            severity: ElasticBeanstalkClientTypes.ValidationSeverity? = nil
        )
        {
            self.message = message
            self.namespace = namespace
            self.optionName = optionName
            self.severity = severity
        }
    }
}

/// Provides a list of validation messages.
public struct ValidateConfigurationSettingsOutput: Swift.Sendable {
    /// A list of [ValidationMessage].
    public var messages: [ElasticBeanstalkClientTypes.ValidationMessage]?

    public init(
        messages: [ElasticBeanstalkClientTypes.ValidationMessage]? = nil
    )
    {
        self.messages = messages
    }
}

extension AbortEnvironmentUpdateInput {

    static func urlPathProvider(_ value: AbortEnvironmentUpdateInput) -> Swift.String? {
        return "/"
    }
}

extension ApplyEnvironmentManagedActionInput {

    static func urlPathProvider(_ value: ApplyEnvironmentManagedActionInput) -> Swift.String? {
        return "/"
    }
}

extension AssociateEnvironmentOperationsRoleInput {

    static func urlPathProvider(_ value: AssociateEnvironmentOperationsRoleInput) -> Swift.String? {
        return "/"
    }
}

extension CheckDNSAvailabilityInput {

    static func urlPathProvider(_ value: CheckDNSAvailabilityInput) -> Swift.String? {
        return "/"
    }
}

extension ComposeEnvironmentsInput {

    static func urlPathProvider(_ value: ComposeEnvironmentsInput) -> Swift.String? {
        return "/"
    }
}

extension CreateApplicationInput {

    static func urlPathProvider(_ value: CreateApplicationInput) -> Swift.String? {
        return "/"
    }
}

extension CreateApplicationVersionInput {

    static func urlPathProvider(_ value: CreateApplicationVersionInput) -> Swift.String? {
        return "/"
    }
}

extension CreateConfigurationTemplateInput {

    static func urlPathProvider(_ value: CreateConfigurationTemplateInput) -> Swift.String? {
        return "/"
    }
}

extension CreateEnvironmentInput {

    static func urlPathProvider(_ value: CreateEnvironmentInput) -> Swift.String? {
        return "/"
    }
}

extension CreatePlatformVersionInput {

    static func urlPathProvider(_ value: CreatePlatformVersionInput) -> Swift.String? {
        return "/"
    }
}

extension CreateStorageLocationInput {

    static func urlPathProvider(_ value: CreateStorageLocationInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteApplicationInput {

    static func urlPathProvider(_ value: DeleteApplicationInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteApplicationVersionInput {

    static func urlPathProvider(_ value: DeleteApplicationVersionInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteConfigurationTemplateInput {

    static func urlPathProvider(_ value: DeleteConfigurationTemplateInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteEnvironmentConfigurationInput {

    static func urlPathProvider(_ value: DeleteEnvironmentConfigurationInput) -> Swift.String? {
        return "/"
    }
}

extension DeletePlatformVersionInput {

    static func urlPathProvider(_ value: DeletePlatformVersionInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeAccountAttributesInput {

    static func urlPathProvider(_ value: DescribeAccountAttributesInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeApplicationsInput {

    static func urlPathProvider(_ value: DescribeApplicationsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeApplicationVersionsInput {

    static func urlPathProvider(_ value: DescribeApplicationVersionsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeConfigurationOptionsInput {

    static func urlPathProvider(_ value: DescribeConfigurationOptionsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeConfigurationSettingsInput {

    static func urlPathProvider(_ value: DescribeConfigurationSettingsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeEnvironmentHealthInput {

    static func urlPathProvider(_ value: DescribeEnvironmentHealthInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeEnvironmentManagedActionHistoryInput {

    static func urlPathProvider(_ value: DescribeEnvironmentManagedActionHistoryInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeEnvironmentManagedActionsInput {

    static func urlPathProvider(_ value: DescribeEnvironmentManagedActionsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeEnvironmentResourcesInput {

    static func urlPathProvider(_ value: DescribeEnvironmentResourcesInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeEnvironmentsInput {

    static func urlPathProvider(_ value: DescribeEnvironmentsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeEventsInput {

    static func urlPathProvider(_ value: DescribeEventsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeInstancesHealthInput {

    static func urlPathProvider(_ value: DescribeInstancesHealthInput) -> Swift.String? {
        return "/"
    }
}

extension DescribePlatformVersionInput {

    static func urlPathProvider(_ value: DescribePlatformVersionInput) -> Swift.String? {
        return "/"
    }
}

extension DisassociateEnvironmentOperationsRoleInput {

    static func urlPathProvider(_ value: DisassociateEnvironmentOperationsRoleInput) -> Swift.String? {
        return "/"
    }
}

extension ListAvailableSolutionStacksInput {

    static func urlPathProvider(_ value: ListAvailableSolutionStacksInput) -> Swift.String? {
        return "/"
    }
}

extension ListPlatformBranchesInput {

    static func urlPathProvider(_ value: ListPlatformBranchesInput) -> Swift.String? {
        return "/"
    }
}

extension ListPlatformVersionsInput {

    static func urlPathProvider(_ value: ListPlatformVersionsInput) -> Swift.String? {
        return "/"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        return "/"
    }
}

extension RebuildEnvironmentInput {

    static func urlPathProvider(_ value: RebuildEnvironmentInput) -> Swift.String? {
        return "/"
    }
}

extension RequestEnvironmentInfoInput {

    static func urlPathProvider(_ value: RequestEnvironmentInfoInput) -> Swift.String? {
        return "/"
    }
}

extension RestartAppServerInput {

    static func urlPathProvider(_ value: RestartAppServerInput) -> Swift.String? {
        return "/"
    }
}

extension RetrieveEnvironmentInfoInput {

    static func urlPathProvider(_ value: RetrieveEnvironmentInfoInput) -> Swift.String? {
        return "/"
    }
}

extension SwapEnvironmentCNAMEsInput {

    static func urlPathProvider(_ value: SwapEnvironmentCNAMEsInput) -> Swift.String? {
        return "/"
    }
}

extension TerminateEnvironmentInput {

    static func urlPathProvider(_ value: TerminateEnvironmentInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateApplicationInput {

    static func urlPathProvider(_ value: UpdateApplicationInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateApplicationResourceLifecycleInput {

    static func urlPathProvider(_ value: UpdateApplicationResourceLifecycleInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateApplicationVersionInput {

    static func urlPathProvider(_ value: UpdateApplicationVersionInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateConfigurationTemplateInput {

    static func urlPathProvider(_ value: UpdateConfigurationTemplateInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateEnvironmentInput {

    static func urlPathProvider(_ value: UpdateEnvironmentInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateTagsForResourceInput {

    static func urlPathProvider(_ value: UpdateTagsForResourceInput) -> Swift.String? {
        return "/"
    }
}

extension ValidateConfigurationSettingsInput {

    static func urlPathProvider(_ value: ValidateConfigurationSettingsInput) -> Swift.String? {
        return "/"
    }
}

extension AbortEnvironmentUpdateInput {

    static func write(value: AbortEnvironmentUpdateInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["Action"].write("AbortEnvironmentUpdate")
        try writer["Version"].write("2010-12-01")
    }
}

extension ApplyEnvironmentManagedActionInput {

    static func write(value: ApplyEnvironmentManagedActionInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ActionId"].write(value.actionId)
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["Action"].write("ApplyEnvironmentManagedAction")
        try writer["Version"].write("2010-12-01")
    }
}

extension AssociateEnvironmentOperationsRoleInput {

    static func write(value: AssociateEnvironmentOperationsRoleInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["OperationsRole"].write(value.operationsRole)
        try writer["Action"].write("AssociateEnvironmentOperationsRole")
        try writer["Version"].write("2010-12-01")
    }
}

extension CheckDNSAvailabilityInput {

    static func write(value: CheckDNSAvailabilityInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["CNAMEPrefix"].write(value.cnamePrefix)
        try writer["Action"].write("CheckDNSAvailability")
        try writer["Version"].write("2010-12-01")
    }
}

extension ComposeEnvironmentsInput {

    static func write(value: ComposeEnvironmentsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["GroupName"].write(value.groupName)
        try writer["VersionLabels"].writeList(value.versionLabels, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Action"].write("ComposeEnvironments")
        try writer["Version"].write("2010-12-01")
    }
}

extension CreateApplicationInput {

    static func write(value: CreateApplicationInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["Description"].write(value.description)
        try writer["ResourceLifecycleConfig"].write(value.resourceLifecycleConfig, with: ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig.write(value:to:))
        try writer["Tags"].writeList(value.tags, memberWritingClosure: ElasticBeanstalkClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Action"].write("CreateApplication")
        try writer["Version"].write("2010-12-01")
    }
}

extension CreateApplicationVersionInput {

    static func write(value: CreateApplicationVersionInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["AutoCreateApplication"].write(value.autoCreateApplication)
        try writer["BuildConfiguration"].write(value.buildConfiguration, with: ElasticBeanstalkClientTypes.BuildConfiguration.write(value:to:))
        try writer["Description"].write(value.description)
        try writer["Process"].write(value.process)
        try writer["SourceBuildInformation"].write(value.sourceBuildInformation, with: ElasticBeanstalkClientTypes.SourceBuildInformation.write(value:to:))
        try writer["SourceBundle"].write(value.sourceBundle, with: ElasticBeanstalkClientTypes.S3Location.write(value:to:))
        try writer["Tags"].writeList(value.tags, memberWritingClosure: ElasticBeanstalkClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["VersionLabel"].write(value.versionLabel)
        try writer["Action"].write("CreateApplicationVersion")
        try writer["Version"].write("2010-12-01")
    }
}

extension CreateConfigurationTemplateInput {

    static func write(value: CreateConfigurationTemplateInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["Description"].write(value.description)
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["OptionSettings"].writeList(value.optionSettings, memberWritingClosure: ElasticBeanstalkClientTypes.ConfigurationOptionSetting.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["PlatformArn"].write(value.platformArn)
        try writer["SolutionStackName"].write(value.solutionStackName)
        try writer["SourceConfiguration"].write(value.sourceConfiguration, with: ElasticBeanstalkClientTypes.SourceConfiguration.write(value:to:))
        try writer["Tags"].writeList(value.tags, memberWritingClosure: ElasticBeanstalkClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["TemplateName"].write(value.templateName)
        try writer["Action"].write("CreateConfigurationTemplate")
        try writer["Version"].write("2010-12-01")
    }
}

extension CreateEnvironmentInput {

    static func write(value: CreateEnvironmentInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["CNAMEPrefix"].write(value.cnamePrefix)
        try writer["Description"].write(value.description)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["GroupName"].write(value.groupName)
        try writer["OperationsRole"].write(value.operationsRole)
        try writer["OptionSettings"].writeList(value.optionSettings, memberWritingClosure: ElasticBeanstalkClientTypes.ConfigurationOptionSetting.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["OptionsToRemove"].writeList(value.optionsToRemove, memberWritingClosure: ElasticBeanstalkClientTypes.OptionSpecification.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["PlatformArn"].write(value.platformArn)
        try writer["SolutionStackName"].write(value.solutionStackName)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: ElasticBeanstalkClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["TemplateName"].write(value.templateName)
        try writer["Tier"].write(value.tier, with: ElasticBeanstalkClientTypes.EnvironmentTier.write(value:to:))
        try writer["VersionLabel"].write(value.versionLabel)
        try writer["Action"].write("CreateEnvironment")
        try writer["Version"].write("2010-12-01")
    }
}

extension CreatePlatformVersionInput {

    static func write(value: CreatePlatformVersionInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["OptionSettings"].writeList(value.optionSettings, memberWritingClosure: ElasticBeanstalkClientTypes.ConfigurationOptionSetting.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["PlatformDefinitionBundle"].write(value.platformDefinitionBundle, with: ElasticBeanstalkClientTypes.S3Location.write(value:to:))
        try writer["PlatformName"].write(value.platformName)
        try writer["PlatformVersion"].write(value.platformVersion)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: ElasticBeanstalkClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Action"].write("CreatePlatformVersion")
        try writer["Version"].write("2010-12-01")
    }
}

extension CreateStorageLocationInput {

    static func write(value: CreateStorageLocationInput?, to writer: SmithyFormURL.Writer) throws {
        guard value != nil else { return }
        _ = writer[""]  // create an empty structure
        try writer["Action"].write("CreateStorageLocation")
        try writer["Version"].write("2010-12-01")
    }
}

extension DeleteApplicationInput {

    static func write(value: DeleteApplicationInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["TerminateEnvByForce"].write(value.terminateEnvByForce)
        try writer["Action"].write("DeleteApplication")
        try writer["Version"].write("2010-12-01")
    }
}

extension DeleteApplicationVersionInput {

    static func write(value: DeleteApplicationVersionInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["DeleteSourceBundle"].write(value.deleteSourceBundle)
        try writer["VersionLabel"].write(value.versionLabel)
        try writer["Action"].write("DeleteApplicationVersion")
        try writer["Version"].write("2010-12-01")
    }
}

extension DeleteConfigurationTemplateInput {

    static func write(value: DeleteConfigurationTemplateInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["TemplateName"].write(value.templateName)
        try writer["Action"].write("DeleteConfigurationTemplate")
        try writer["Version"].write("2010-12-01")
    }
}

extension DeleteEnvironmentConfigurationInput {

    static func write(value: DeleteEnvironmentConfigurationInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["Action"].write("DeleteEnvironmentConfiguration")
        try writer["Version"].write("2010-12-01")
    }
}

extension DeletePlatformVersionInput {

    static func write(value: DeletePlatformVersionInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["PlatformArn"].write(value.platformArn)
        try writer["Action"].write("DeletePlatformVersion")
        try writer["Version"].write("2010-12-01")
    }
}

extension DescribeAccountAttributesInput {

    static func write(value: DescribeAccountAttributesInput?, to writer: SmithyFormURL.Writer) throws {
        guard value != nil else { return }
        _ = writer[""]  // create an empty structure
        try writer["Action"].write("DescribeAccountAttributes")
        try writer["Version"].write("2010-12-01")
    }
}

extension DescribeApplicationsInput {

    static func write(value: DescribeApplicationsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationNames"].writeList(value.applicationNames, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Action"].write("DescribeApplications")
        try writer["Version"].write("2010-12-01")
    }
}

extension DescribeApplicationVersionsInput {

    static func write(value: DescribeApplicationVersionsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["NextToken"].write(value.nextToken)
        try writer["VersionLabels"].writeList(value.versionLabels, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Action"].write("DescribeApplicationVersions")
        try writer["Version"].write("2010-12-01")
    }
}

extension DescribeConfigurationOptionsInput {

    static func write(value: DescribeConfigurationOptionsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["Options"].writeList(value.options, memberWritingClosure: ElasticBeanstalkClientTypes.OptionSpecification.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["PlatformArn"].write(value.platformArn)
        try writer["SolutionStackName"].write(value.solutionStackName)
        try writer["TemplateName"].write(value.templateName)
        try writer["Action"].write("DescribeConfigurationOptions")
        try writer["Version"].write("2010-12-01")
    }
}

extension DescribeConfigurationSettingsInput {

    static func write(value: DescribeConfigurationSettingsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["TemplateName"].write(value.templateName)
        try writer["Action"].write("DescribeConfigurationSettings")
        try writer["Version"].write("2010-12-01")
    }
}

extension DescribeEnvironmentHealthInput {

    static func write(value: DescribeEnvironmentHealthInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["AttributeNames"].writeList(value.attributeNames, memberWritingClosure: SmithyReadWrite.WritingClosureBox<ElasticBeanstalkClientTypes.EnvironmentHealthAttribute>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["Action"].write("DescribeEnvironmentHealth")
        try writer["Version"].write("2010-12-01")
    }
}

extension DescribeEnvironmentManagedActionHistoryInput {

    static func write(value: DescribeEnvironmentManagedActionHistoryInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["MaxItems"].write(value.maxItems)
        try writer["NextToken"].write(value.nextToken)
        try writer["Action"].write("DescribeEnvironmentManagedActionHistory")
        try writer["Version"].write("2010-12-01")
    }
}

extension DescribeEnvironmentManagedActionsInput {

    static func write(value: DescribeEnvironmentManagedActionsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["Status"].write(value.status)
        try writer["Action"].write("DescribeEnvironmentManagedActions")
        try writer["Version"].write("2010-12-01")
    }
}

extension DescribeEnvironmentResourcesInput {

    static func write(value: DescribeEnvironmentResourcesInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["Action"].write("DescribeEnvironmentResources")
        try writer["Version"].write("2010-12-01")
    }
}

extension DescribeEnvironmentsInput {

    static func write(value: DescribeEnvironmentsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["EnvironmentIds"].writeList(value.environmentIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["EnvironmentNames"].writeList(value.environmentNames, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["IncludeDeleted"].write(value.includeDeleted)
        try writer["IncludedDeletedBackTo"].writeTimestamp(value.includedDeletedBackTo, format: SmithyTimestamps.TimestampFormat.dateTime)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["NextToken"].write(value.nextToken)
        try writer["VersionLabel"].write(value.versionLabel)
        try writer["Action"].write("DescribeEnvironments")
        try writer["Version"].write("2010-12-01")
    }
}

extension DescribeEventsInput {

    static func write(value: DescribeEventsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["EndTime"].writeTimestamp(value.endTime, format: SmithyTimestamps.TimestampFormat.dateTime)
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["NextToken"].write(value.nextToken)
        try writer["PlatformArn"].write(value.platformArn)
        try writer["RequestId"].write(value.requestId)
        try writer["Severity"].write(value.severity)
        try writer["StartTime"].writeTimestamp(value.startTime, format: SmithyTimestamps.TimestampFormat.dateTime)
        try writer["TemplateName"].write(value.templateName)
        try writer["VersionLabel"].write(value.versionLabel)
        try writer["Action"].write("DescribeEvents")
        try writer["Version"].write("2010-12-01")
    }
}

extension DescribeInstancesHealthInput {

    static func write(value: DescribeInstancesHealthInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["AttributeNames"].writeList(value.attributeNames, memberWritingClosure: SmithyReadWrite.WritingClosureBox<ElasticBeanstalkClientTypes.InstancesHealthAttribute>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["NextToken"].write(value.nextToken)
        try writer["Action"].write("DescribeInstancesHealth")
        try writer["Version"].write("2010-12-01")
    }
}

extension DescribePlatformVersionInput {

    static func write(value: DescribePlatformVersionInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["PlatformArn"].write(value.platformArn)
        try writer["Action"].write("DescribePlatformVersion")
        try writer["Version"].write("2010-12-01")
    }
}

extension DisassociateEnvironmentOperationsRoleInput {

    static func write(value: DisassociateEnvironmentOperationsRoleInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["Action"].write("DisassociateEnvironmentOperationsRole")
        try writer["Version"].write("2010-12-01")
    }
}

extension ListAvailableSolutionStacksInput {

    static func write(value: ListAvailableSolutionStacksInput?, to writer: SmithyFormURL.Writer) throws {
        guard value != nil else { return }
        _ = writer[""]  // create an empty structure
        try writer["Action"].write("ListAvailableSolutionStacks")
        try writer["Version"].write("2010-12-01")
    }
}

extension ListPlatformBranchesInput {

    static func write(value: ListPlatformBranchesInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Filters"].writeList(value.filters, memberWritingClosure: ElasticBeanstalkClientTypes.SearchFilter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["NextToken"].write(value.nextToken)
        try writer["Action"].write("ListPlatformBranches")
        try writer["Version"].write("2010-12-01")
    }
}

extension ListPlatformVersionsInput {

    static func write(value: ListPlatformVersionsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Filters"].writeList(value.filters, memberWritingClosure: ElasticBeanstalkClientTypes.PlatformFilter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["NextToken"].write(value.nextToken)
        try writer["Action"].write("ListPlatformVersions")
        try writer["Version"].write("2010-12-01")
    }
}

extension ListTagsForResourceInput {

    static func write(value: ListTagsForResourceInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ResourceArn"].write(value.resourceArn)
        try writer["Action"].write("ListTagsForResource")
        try writer["Version"].write("2010-12-01")
    }
}

extension RebuildEnvironmentInput {

    static func write(value: RebuildEnvironmentInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["Action"].write("RebuildEnvironment")
        try writer["Version"].write("2010-12-01")
    }
}

extension RequestEnvironmentInfoInput {

    static func write(value: RequestEnvironmentInfoInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["InfoType"].write(value.infoType)
        try writer["Action"].write("RequestEnvironmentInfo")
        try writer["Version"].write("2010-12-01")
    }
}

extension RestartAppServerInput {

    static func write(value: RestartAppServerInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["Action"].write("RestartAppServer")
        try writer["Version"].write("2010-12-01")
    }
}

extension RetrieveEnvironmentInfoInput {

    static func write(value: RetrieveEnvironmentInfoInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["InfoType"].write(value.infoType)
        try writer["Action"].write("RetrieveEnvironmentInfo")
        try writer["Version"].write("2010-12-01")
    }
}

extension SwapEnvironmentCNAMEsInput {

    static func write(value: SwapEnvironmentCNAMEsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DestinationEnvironmentId"].write(value.destinationEnvironmentId)
        try writer["DestinationEnvironmentName"].write(value.destinationEnvironmentName)
        try writer["SourceEnvironmentId"].write(value.sourceEnvironmentId)
        try writer["SourceEnvironmentName"].write(value.sourceEnvironmentName)
        try writer["Action"].write("SwapEnvironmentCNAMEs")
        try writer["Version"].write("2010-12-01")
    }
}

extension TerminateEnvironmentInput {

    static func write(value: TerminateEnvironmentInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["ForceTerminate"].write(value.forceTerminate)
        try writer["TerminateResources"].write(value.terminateResources)
        try writer["Action"].write("TerminateEnvironment")
        try writer["Version"].write("2010-12-01")
    }
}

extension UpdateApplicationInput {

    static func write(value: UpdateApplicationInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["Description"].write(value.description)
        try writer["Action"].write("UpdateApplication")
        try writer["Version"].write("2010-12-01")
    }
}

extension UpdateApplicationResourceLifecycleInput {

    static func write(value: UpdateApplicationResourceLifecycleInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["ResourceLifecycleConfig"].write(value.resourceLifecycleConfig, with: ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig.write(value:to:))
        try writer["Action"].write("UpdateApplicationResourceLifecycle")
        try writer["Version"].write("2010-12-01")
    }
}

extension UpdateApplicationVersionInput {

    static func write(value: UpdateApplicationVersionInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["Description"].write(value.description)
        try writer["VersionLabel"].write(value.versionLabel)
        try writer["Action"].write("UpdateApplicationVersion")
        try writer["Version"].write("2010-12-01")
    }
}

extension UpdateConfigurationTemplateInput {

    static func write(value: UpdateConfigurationTemplateInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["Description"].write(value.description)
        try writer["OptionSettings"].writeList(value.optionSettings, memberWritingClosure: ElasticBeanstalkClientTypes.ConfigurationOptionSetting.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["OptionsToRemove"].writeList(value.optionsToRemove, memberWritingClosure: ElasticBeanstalkClientTypes.OptionSpecification.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["TemplateName"].write(value.templateName)
        try writer["Action"].write("UpdateConfigurationTemplate")
        try writer["Version"].write("2010-12-01")
    }
}

extension UpdateEnvironmentInput {

    static func write(value: UpdateEnvironmentInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["Description"].write(value.description)
        try writer["EnvironmentId"].write(value.environmentId)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["GroupName"].write(value.groupName)
        try writer["OptionSettings"].writeList(value.optionSettings, memberWritingClosure: ElasticBeanstalkClientTypes.ConfigurationOptionSetting.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["OptionsToRemove"].writeList(value.optionsToRemove, memberWritingClosure: ElasticBeanstalkClientTypes.OptionSpecification.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["PlatformArn"].write(value.platformArn)
        try writer["SolutionStackName"].write(value.solutionStackName)
        try writer["TemplateName"].write(value.templateName)
        try writer["Tier"].write(value.tier, with: ElasticBeanstalkClientTypes.EnvironmentTier.write(value:to:))
        try writer["VersionLabel"].write(value.versionLabel)
        try writer["Action"].write("UpdateEnvironment")
        try writer["Version"].write("2010-12-01")
    }
}

extension UpdateTagsForResourceInput {

    static func write(value: UpdateTagsForResourceInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ResourceArn"].write(value.resourceArn)
        try writer["TagsToAdd"].writeList(value.tagsToAdd, memberWritingClosure: ElasticBeanstalkClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["TagsToRemove"].writeList(value.tagsToRemove, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Action"].write("UpdateTagsForResource")
        try writer["Version"].write("2010-12-01")
    }
}

extension ValidateConfigurationSettingsInput {

    static func write(value: ValidateConfigurationSettingsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["EnvironmentName"].write(value.environmentName)
        try writer["OptionSettings"].writeList(value.optionSettings, memberWritingClosure: ElasticBeanstalkClientTypes.ConfigurationOptionSetting.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["TemplateName"].write(value.templateName)
        try writer["Action"].write("ValidateConfigurationSettings")
        try writer["Version"].write("2010-12-01")
    }
}

extension AbortEnvironmentUpdateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AbortEnvironmentUpdateOutput {
        return AbortEnvironmentUpdateOutput()
    }
}

extension ApplyEnvironmentManagedActionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ApplyEnvironmentManagedActionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ApplyEnvironmentManagedActionResult"]
        var value = ApplyEnvironmentManagedActionOutput()
        value.actionDescription = try reader["ActionDescription"].readIfPresent()
        value.actionId = try reader["ActionId"].readIfPresent()
        value.actionType = try reader["ActionType"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        return value
    }
}

extension AssociateEnvironmentOperationsRoleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AssociateEnvironmentOperationsRoleOutput {
        return AssociateEnvironmentOperationsRoleOutput()
    }
}

extension CheckDNSAvailabilityOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CheckDNSAvailabilityOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CheckDNSAvailabilityResult"]
        var value = CheckDNSAvailabilityOutput()
        value.available = try reader["Available"].readIfPresent()
        value.fullyQualifiedCNAME = try reader["FullyQualifiedCNAME"].readIfPresent()
        return value
    }
}

extension ComposeEnvironmentsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ComposeEnvironmentsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ComposeEnvironmentsResult"]
        var value = ComposeEnvironmentsOutput()
        value.environments = try reader["Environments"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.EnvironmentDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension CreateApplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateApplicationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CreateApplicationResult"]
        var value = CreateApplicationOutput()
        value.application = try reader["Application"].readIfPresent(with: ElasticBeanstalkClientTypes.ApplicationDescription.read(from:))
        return value
    }
}

extension CreateApplicationVersionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateApplicationVersionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CreateApplicationVersionResult"]
        var value = CreateApplicationVersionOutput()
        value.applicationVersion = try reader["ApplicationVersion"].readIfPresent(with: ElasticBeanstalkClientTypes.ApplicationVersionDescription.read(from:))
        return value
    }
}

extension CreateConfigurationTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateConfigurationTemplateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CreateConfigurationTemplateResult"]
        var value = CreateConfigurationTemplateOutput()
        value.applicationName = try reader["ApplicationName"].readIfPresent()
        value.dateCreated = try reader["DateCreated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dateUpdated = try reader["DateUpdated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.deploymentStatus = try reader["DeploymentStatus"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.environmentName = try reader["EnvironmentName"].readIfPresent()
        value.optionSettings = try reader["OptionSettings"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.ConfigurationOptionSetting.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.platformArn = try reader["PlatformArn"].readIfPresent()
        value.solutionStackName = try reader["SolutionStackName"].readIfPresent()
        value.templateName = try reader["TemplateName"].readIfPresent()
        return value
    }
}

extension CreateEnvironmentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateEnvironmentOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CreateEnvironmentResult"]
        var value = CreateEnvironmentOutput()
        value.abortableOperationInProgress = try reader["AbortableOperationInProgress"].readIfPresent()
        value.applicationName = try reader["ApplicationName"].readIfPresent()
        value.cname = try reader["CNAME"].readIfPresent()
        value.dateCreated = try reader["DateCreated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dateUpdated = try reader["DateUpdated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.description = try reader["Description"].readIfPresent()
        value.endpointURL = try reader["EndpointURL"].readIfPresent()
        value.environmentArn = try reader["EnvironmentArn"].readIfPresent()
        value.environmentId = try reader["EnvironmentId"].readIfPresent()
        value.environmentLinks = try reader["EnvironmentLinks"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.EnvironmentLink.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.environmentName = try reader["EnvironmentName"].readIfPresent()
        value.health = try reader["Health"].readIfPresent()
        value.healthStatus = try reader["HealthStatus"].readIfPresent()
        value.operationsRole = try reader["OperationsRole"].readIfPresent()
        value.platformArn = try reader["PlatformArn"].readIfPresent()
        value.resources = try reader["Resources"].readIfPresent(with: ElasticBeanstalkClientTypes.EnvironmentResourcesDescription.read(from:))
        value.solutionStackName = try reader["SolutionStackName"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.templateName = try reader["TemplateName"].readIfPresent()
        value.tier = try reader["Tier"].readIfPresent(with: ElasticBeanstalkClientTypes.EnvironmentTier.read(from:))
        value.versionLabel = try reader["VersionLabel"].readIfPresent()
        return value
    }
}

extension CreatePlatformVersionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreatePlatformVersionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CreatePlatformVersionResult"]
        var value = CreatePlatformVersionOutput()
        value.builder = try reader["Builder"].readIfPresent(with: ElasticBeanstalkClientTypes.Builder.read(from:))
        value.platformSummary = try reader["PlatformSummary"].readIfPresent(with: ElasticBeanstalkClientTypes.PlatformSummary.read(from:))
        return value
    }
}

extension CreateStorageLocationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateStorageLocationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CreateStorageLocationResult"]
        var value = CreateStorageLocationOutput()
        value.s3Bucket = try reader["S3Bucket"].readIfPresent()
        return value
    }
}

extension DeleteApplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteApplicationOutput {
        return DeleteApplicationOutput()
    }
}

extension DeleteApplicationVersionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteApplicationVersionOutput {
        return DeleteApplicationVersionOutput()
    }
}

extension DeleteConfigurationTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteConfigurationTemplateOutput {
        return DeleteConfigurationTemplateOutput()
    }
}

extension DeleteEnvironmentConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteEnvironmentConfigurationOutput {
        return DeleteEnvironmentConfigurationOutput()
    }
}

extension DeletePlatformVersionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeletePlatformVersionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DeletePlatformVersionResult"]
        var value = DeletePlatformVersionOutput()
        value.platformSummary = try reader["PlatformSummary"].readIfPresent(with: ElasticBeanstalkClientTypes.PlatformSummary.read(from:))
        return value
    }
}

extension DescribeAccountAttributesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeAccountAttributesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeAccountAttributesResult"]
        var value = DescribeAccountAttributesOutput()
        value.resourceQuotas = try reader["ResourceQuotas"].readIfPresent(with: ElasticBeanstalkClientTypes.ResourceQuotas.read(from:))
        return value
    }
}

extension DescribeApplicationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeApplicationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeApplicationsResult"]
        var value = DescribeApplicationsOutput()
        value.applications = try reader["Applications"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.ApplicationDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeApplicationVersionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeApplicationVersionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeApplicationVersionsResult"]
        var value = DescribeApplicationVersionsOutput()
        value.applicationVersions = try reader["ApplicationVersions"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.ApplicationVersionDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension DescribeConfigurationOptionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeConfigurationOptionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeConfigurationOptionsResult"]
        var value = DescribeConfigurationOptionsOutput()
        value.options = try reader["Options"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.ConfigurationOptionDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.platformArn = try reader["PlatformArn"].readIfPresent()
        value.solutionStackName = try reader["SolutionStackName"].readIfPresent()
        return value
    }
}

extension DescribeConfigurationSettingsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeConfigurationSettingsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeConfigurationSettingsResult"]
        var value = DescribeConfigurationSettingsOutput()
        value.configurationSettings = try reader["ConfigurationSettings"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.ConfigurationSettingsDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeEnvironmentHealthOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeEnvironmentHealthOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeEnvironmentHealthResult"]
        var value = DescribeEnvironmentHealthOutput()
        value.applicationMetrics = try reader["ApplicationMetrics"].readIfPresent(with: ElasticBeanstalkClientTypes.ApplicationMetrics.read(from:))
        value.causes = try reader["Causes"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.color = try reader["Color"].readIfPresent()
        value.environmentName = try reader["EnvironmentName"].readIfPresent()
        value.healthStatus = try reader["HealthStatus"].readIfPresent()
        value.instancesHealth = try reader["InstancesHealth"].readIfPresent(with: ElasticBeanstalkClientTypes.InstanceHealthSummary.read(from:))
        value.refreshedAt = try reader["RefreshedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.status = try reader["Status"].readIfPresent()
        return value
    }
}

extension DescribeEnvironmentManagedActionHistoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeEnvironmentManagedActionHistoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeEnvironmentManagedActionHistoryResult"]
        var value = DescribeEnvironmentManagedActionHistoryOutput()
        value.managedActionHistoryItems = try reader["ManagedActionHistoryItems"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.ManagedActionHistoryItem.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension DescribeEnvironmentManagedActionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeEnvironmentManagedActionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeEnvironmentManagedActionsResult"]
        var value = DescribeEnvironmentManagedActionsOutput()
        value.managedActions = try reader["ManagedActions"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.ManagedAction.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeEnvironmentResourcesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeEnvironmentResourcesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeEnvironmentResourcesResult"]
        var value = DescribeEnvironmentResourcesOutput()
        value.environmentResources = try reader["EnvironmentResources"].readIfPresent(with: ElasticBeanstalkClientTypes.EnvironmentResourceDescription.read(from:))
        return value
    }
}

extension DescribeEnvironmentsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeEnvironmentsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeEnvironmentsResult"]
        var value = DescribeEnvironmentsOutput()
        value.environments = try reader["Environments"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.EnvironmentDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension DescribeEventsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeEventsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeEventsResult"]
        var value = DescribeEventsOutput()
        value.events = try reader["Events"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.EventDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension DescribeInstancesHealthOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeInstancesHealthOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeInstancesHealthResult"]
        var value = DescribeInstancesHealthOutput()
        value.instanceHealthList = try reader["InstanceHealthList"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.SingleInstanceHealth.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.refreshedAt = try reader["RefreshedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension DescribePlatformVersionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribePlatformVersionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribePlatformVersionResult"]
        var value = DescribePlatformVersionOutput()
        value.platformDescription = try reader["PlatformDescription"].readIfPresent(with: ElasticBeanstalkClientTypes.PlatformDescription.read(from:))
        return value
    }
}

extension DisassociateEnvironmentOperationsRoleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisassociateEnvironmentOperationsRoleOutput {
        return DisassociateEnvironmentOperationsRoleOutput()
    }
}

extension ListAvailableSolutionStacksOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListAvailableSolutionStacksOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ListAvailableSolutionStacksResult"]
        var value = ListAvailableSolutionStacksOutput()
        value.solutionStackDetails = try reader["SolutionStackDetails"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.SolutionStackDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.solutionStacks = try reader["SolutionStacks"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListPlatformBranchesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListPlatformBranchesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ListPlatformBranchesResult"]
        var value = ListPlatformBranchesOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.platformBranchSummaryList = try reader["PlatformBranchSummaryList"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.PlatformBranchSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListPlatformVersionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListPlatformVersionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ListPlatformVersionsResult"]
        var value = ListPlatformVersionsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.platformSummaryList = try reader["PlatformSummaryList"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.PlatformSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ListTagsForResourceResult"]
        var value = ListTagsForResourceOutput()
        value.resourceArn = try reader["ResourceArn"].readIfPresent()
        value.resourceTags = try reader["ResourceTags"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension RebuildEnvironmentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RebuildEnvironmentOutput {
        return RebuildEnvironmentOutput()
    }
}

extension RequestEnvironmentInfoOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RequestEnvironmentInfoOutput {
        return RequestEnvironmentInfoOutput()
    }
}

extension RestartAppServerOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RestartAppServerOutput {
        return RestartAppServerOutput()
    }
}

extension RetrieveEnvironmentInfoOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RetrieveEnvironmentInfoOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["RetrieveEnvironmentInfoResult"]
        var value = RetrieveEnvironmentInfoOutput()
        value.environmentInfo = try reader["EnvironmentInfo"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.EnvironmentInfoDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension SwapEnvironmentCNAMEsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SwapEnvironmentCNAMEsOutput {
        return SwapEnvironmentCNAMEsOutput()
    }
}

extension TerminateEnvironmentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TerminateEnvironmentOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["TerminateEnvironmentResult"]
        var value = TerminateEnvironmentOutput()
        value.abortableOperationInProgress = try reader["AbortableOperationInProgress"].readIfPresent()
        value.applicationName = try reader["ApplicationName"].readIfPresent()
        value.cname = try reader["CNAME"].readIfPresent()
        value.dateCreated = try reader["DateCreated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dateUpdated = try reader["DateUpdated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.description = try reader["Description"].readIfPresent()
        value.endpointURL = try reader["EndpointURL"].readIfPresent()
        value.environmentArn = try reader["EnvironmentArn"].readIfPresent()
        value.environmentId = try reader["EnvironmentId"].readIfPresent()
        value.environmentLinks = try reader["EnvironmentLinks"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.EnvironmentLink.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.environmentName = try reader["EnvironmentName"].readIfPresent()
        value.health = try reader["Health"].readIfPresent()
        value.healthStatus = try reader["HealthStatus"].readIfPresent()
        value.operationsRole = try reader["OperationsRole"].readIfPresent()
        value.platformArn = try reader["PlatformArn"].readIfPresent()
        value.resources = try reader["Resources"].readIfPresent(with: ElasticBeanstalkClientTypes.EnvironmentResourcesDescription.read(from:))
        value.solutionStackName = try reader["SolutionStackName"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.templateName = try reader["TemplateName"].readIfPresent()
        value.tier = try reader["Tier"].readIfPresent(with: ElasticBeanstalkClientTypes.EnvironmentTier.read(from:))
        value.versionLabel = try reader["VersionLabel"].readIfPresent()
        return value
    }
}

extension UpdateApplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateApplicationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["UpdateApplicationResult"]
        var value = UpdateApplicationOutput()
        value.application = try reader["Application"].readIfPresent(with: ElasticBeanstalkClientTypes.ApplicationDescription.read(from:))
        return value
    }
}

extension UpdateApplicationResourceLifecycleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateApplicationResourceLifecycleOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["UpdateApplicationResourceLifecycleResult"]
        var value = UpdateApplicationResourceLifecycleOutput()
        value.applicationName = try reader["ApplicationName"].readIfPresent()
        value.resourceLifecycleConfig = try reader["ResourceLifecycleConfig"].readIfPresent(with: ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig.read(from:))
        return value
    }
}

extension UpdateApplicationVersionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateApplicationVersionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["UpdateApplicationVersionResult"]
        var value = UpdateApplicationVersionOutput()
        value.applicationVersion = try reader["ApplicationVersion"].readIfPresent(with: ElasticBeanstalkClientTypes.ApplicationVersionDescription.read(from:))
        return value
    }
}

extension UpdateConfigurationTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateConfigurationTemplateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["UpdateConfigurationTemplateResult"]
        var value = UpdateConfigurationTemplateOutput()
        value.applicationName = try reader["ApplicationName"].readIfPresent()
        value.dateCreated = try reader["DateCreated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dateUpdated = try reader["DateUpdated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.deploymentStatus = try reader["DeploymentStatus"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.environmentName = try reader["EnvironmentName"].readIfPresent()
        value.optionSettings = try reader["OptionSettings"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.ConfigurationOptionSetting.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.platformArn = try reader["PlatformArn"].readIfPresent()
        value.solutionStackName = try reader["SolutionStackName"].readIfPresent()
        value.templateName = try reader["TemplateName"].readIfPresent()
        return value
    }
}

extension UpdateEnvironmentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateEnvironmentOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["UpdateEnvironmentResult"]
        var value = UpdateEnvironmentOutput()
        value.abortableOperationInProgress = try reader["AbortableOperationInProgress"].readIfPresent()
        value.applicationName = try reader["ApplicationName"].readIfPresent()
        value.cname = try reader["CNAME"].readIfPresent()
        value.dateCreated = try reader["DateCreated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dateUpdated = try reader["DateUpdated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.description = try reader["Description"].readIfPresent()
        value.endpointURL = try reader["EndpointURL"].readIfPresent()
        value.environmentArn = try reader["EnvironmentArn"].readIfPresent()
        value.environmentId = try reader["EnvironmentId"].readIfPresent()
        value.environmentLinks = try reader["EnvironmentLinks"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.EnvironmentLink.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.environmentName = try reader["EnvironmentName"].readIfPresent()
        value.health = try reader["Health"].readIfPresent()
        value.healthStatus = try reader["HealthStatus"].readIfPresent()
        value.operationsRole = try reader["OperationsRole"].readIfPresent()
        value.platformArn = try reader["PlatformArn"].readIfPresent()
        value.resources = try reader["Resources"].readIfPresent(with: ElasticBeanstalkClientTypes.EnvironmentResourcesDescription.read(from:))
        value.solutionStackName = try reader["SolutionStackName"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.templateName = try reader["TemplateName"].readIfPresent()
        value.tier = try reader["Tier"].readIfPresent(with: ElasticBeanstalkClientTypes.EnvironmentTier.read(from:))
        value.versionLabel = try reader["VersionLabel"].readIfPresent()
        return value
    }
}

extension UpdateTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateTagsForResourceOutput {
        return UpdateTagsForResourceOutput()
    }
}

extension ValidateConfigurationSettingsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ValidateConfigurationSettingsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ValidateConfigurationSettingsResult"]
        var value = ValidateConfigurationSettingsOutput()
        value.messages = try reader["Messages"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.ValidationMessage.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

enum AbortEnvironmentUpdateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ApplyEnvironmentManagedActionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ElasticBeanstalkServiceException": return try ElasticBeanstalkServiceException.makeError(baseError: baseError)
            case "ManagedActionInvalidStateException": return try ManagedActionInvalidStateException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum AssociateEnvironmentOperationsRoleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CheckDNSAvailabilityOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ComposeEnvironmentsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            case "TooManyEnvironmentsException": return try TooManyEnvironmentsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateApplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "TooManyApplicationsException": return try TooManyApplicationsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateApplicationVersionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "CodeBuildNotInServiceRegionException": return try CodeBuildNotInServiceRegionException.makeError(baseError: baseError)
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            case "S3LocationNotInServiceRegionException": return try S3LocationNotInServiceRegionException.makeError(baseError: baseError)
            case "TooManyApplicationsException": return try TooManyApplicationsException.makeError(baseError: baseError)
            case "TooManyApplicationVersionsException": return try TooManyApplicationVersionsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateConfigurationTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            case "TooManyBucketsException": return try TooManyBucketsException.makeError(baseError: baseError)
            case "TooManyConfigurationTemplatesException": return try TooManyConfigurationTemplatesException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateEnvironmentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            case "TooManyEnvironmentsException": return try TooManyEnvironmentsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreatePlatformVersionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ElasticBeanstalkServiceException": return try ElasticBeanstalkServiceException.makeError(baseError: baseError)
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            case "TooManyPlatformsException": return try TooManyPlatformsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateStorageLocationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            case "S3SubscriptionRequiredException": return try S3SubscriptionRequiredException.makeError(baseError: baseError)
            case "TooManyBucketsException": return try TooManyBucketsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteApplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "OperationInProgressFailure": return try OperationInProgressException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteApplicationVersionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            case "OperationInProgressFailure": return try OperationInProgressException.makeError(baseError: baseError)
            case "S3LocationNotInServiceRegionException": return try S3LocationNotInServiceRegionException.makeError(baseError: baseError)
            case "SourceBundleDeletionFailure": return try SourceBundleDeletionException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteConfigurationTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "OperationInProgressFailure": return try OperationInProgressException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteEnvironmentConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeletePlatformVersionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ElasticBeanstalkServiceException": return try ElasticBeanstalkServiceException.makeError(baseError: baseError)
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            case "OperationInProgressFailure": return try OperationInProgressException.makeError(baseError: baseError)
            case "PlatformVersionStillReferencedException": return try PlatformVersionStillReferencedException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeAccountAttributesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeApplicationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeApplicationVersionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeConfigurationOptionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "TooManyBucketsException": return try TooManyBucketsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeConfigurationSettingsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "TooManyBucketsException": return try TooManyBucketsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeEnvironmentHealthOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ElasticBeanstalkServiceException": return try ElasticBeanstalkServiceException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeEnvironmentManagedActionHistoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ElasticBeanstalkServiceException": return try ElasticBeanstalkServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeEnvironmentManagedActionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ElasticBeanstalkServiceException": return try ElasticBeanstalkServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeEnvironmentResourcesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeEnvironmentsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeEventsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeInstancesHealthOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ElasticBeanstalkServiceException": return try ElasticBeanstalkServiceException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribePlatformVersionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ElasticBeanstalkServiceException": return try ElasticBeanstalkServiceException.makeError(baseError: baseError)
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisassociateEnvironmentOperationsRoleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListAvailableSolutionStacksOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListPlatformBranchesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListPlatformVersionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ElasticBeanstalkServiceException": return try ElasticBeanstalkServiceException.makeError(baseError: baseError)
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ResourceTypeNotSupportedException": return try ResourceTypeNotSupportedException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RebuildEnvironmentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RequestEnvironmentInfoOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RestartAppServerOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RetrieveEnvironmentInfoOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SwapEnvironmentCNAMEsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TerminateEnvironmentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateApplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateApplicationResourceLifecycleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateApplicationVersionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateConfigurationTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            case "TooManyBucketsException": return try TooManyBucketsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateEnvironmentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            case "TooManyBucketsException": return try TooManyBucketsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            case "OperationInProgressFailure": return try OperationInProgressException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ResourceTypeNotSupportedException": return try ResourceTypeNotSupportedException.makeError(baseError: baseError)
            case "TooManyTagsException": return try TooManyTagsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ValidateConfigurationSettingsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InsufficientPrivilegesException": return try InsufficientPrivilegesException.makeError(baseError: baseError)
            case "TooManyBucketsException": return try TooManyBucketsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension InsufficientPrivilegesException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InsufficientPrivilegesException {
        let reader = baseError.errorBodyReader
        var value = InsufficientPrivilegesException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ManagedActionInvalidStateException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> ManagedActionInvalidStateException {
        let reader = baseError.errorBodyReader
        var value = ManagedActionInvalidStateException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ElasticBeanstalkServiceException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> ElasticBeanstalkServiceException {
        let reader = baseError.errorBodyReader
        var value = ElasticBeanstalkServiceException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TooManyEnvironmentsException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> TooManyEnvironmentsException {
        let reader = baseError.errorBodyReader
        var value = TooManyEnvironmentsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TooManyApplicationsException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> TooManyApplicationsException {
        let reader = baseError.errorBodyReader
        var value = TooManyApplicationsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension S3LocationNotInServiceRegionException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> S3LocationNotInServiceRegionException {
        let reader = baseError.errorBodyReader
        var value = S3LocationNotInServiceRegionException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CodeBuildNotInServiceRegionException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> CodeBuildNotInServiceRegionException {
        let reader = baseError.errorBodyReader
        var value = CodeBuildNotInServiceRegionException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TooManyApplicationVersionsException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> TooManyApplicationVersionsException {
        let reader = baseError.errorBodyReader
        var value = TooManyApplicationVersionsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TooManyConfigurationTemplatesException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> TooManyConfigurationTemplatesException {
        let reader = baseError.errorBodyReader
        var value = TooManyConfigurationTemplatesException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TooManyBucketsException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> TooManyBucketsException {
        let reader = baseError.errorBodyReader
        var value = TooManyBucketsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TooManyPlatformsException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> TooManyPlatformsException {
        let reader = baseError.errorBodyReader
        var value = TooManyPlatformsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension S3SubscriptionRequiredException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> S3SubscriptionRequiredException {
        let reader = baseError.errorBodyReader
        var value = S3SubscriptionRequiredException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension OperationInProgressException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> OperationInProgressException {
        let reader = baseError.errorBodyReader
        var value = OperationInProgressException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SourceBundleDeletionException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> SourceBundleDeletionException {
        let reader = baseError.errorBodyReader
        var value = SourceBundleDeletionException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension PlatformVersionStillReferencedException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> PlatformVersionStillReferencedException {
        let reader = baseError.errorBodyReader
        var value = PlatformVersionStillReferencedException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidRequestException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InvalidRequestException {
        let reader = baseError.errorBodyReader
        var value = InvalidRequestException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceTypeNotSupportedException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> ResourceTypeNotSupportedException {
        let reader = baseError.errorBodyReader
        var value = ResourceTypeNotSupportedException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TooManyTagsException {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> TooManyTagsException {
        let reader = baseError.errorBodyReader
        var value = TooManyTagsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ElasticBeanstalkClientTypes.EnvironmentDescription {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.EnvironmentDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.EnvironmentDescription()
        value.environmentName = try reader["EnvironmentName"].readIfPresent()
        value.environmentId = try reader["EnvironmentId"].readIfPresent()
        value.applicationName = try reader["ApplicationName"].readIfPresent()
        value.versionLabel = try reader["VersionLabel"].readIfPresent()
        value.solutionStackName = try reader["SolutionStackName"].readIfPresent()
        value.platformArn = try reader["PlatformArn"].readIfPresent()
        value.templateName = try reader["TemplateName"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.endpointURL = try reader["EndpointURL"].readIfPresent()
        value.cname = try reader["CNAME"].readIfPresent()
        value.dateCreated = try reader["DateCreated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dateUpdated = try reader["DateUpdated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.status = try reader["Status"].readIfPresent()
        value.abortableOperationInProgress = try reader["AbortableOperationInProgress"].readIfPresent()
        value.health = try reader["Health"].readIfPresent()
        value.healthStatus = try reader["HealthStatus"].readIfPresent()
        value.resources = try reader["Resources"].readIfPresent(with: ElasticBeanstalkClientTypes.EnvironmentResourcesDescription.read(from:))
        value.tier = try reader["Tier"].readIfPresent(with: ElasticBeanstalkClientTypes.EnvironmentTier.read(from:))
        value.environmentLinks = try reader["EnvironmentLinks"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.EnvironmentLink.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.environmentArn = try reader["EnvironmentArn"].readIfPresent()
        value.operationsRole = try reader["OperationsRole"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.EnvironmentLink {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.EnvironmentLink {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.EnvironmentLink()
        value.linkName = try reader["LinkName"].readIfPresent()
        value.environmentName = try reader["EnvironmentName"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.EnvironmentTier {

    static func write(value: ElasticBeanstalkClientTypes.EnvironmentTier?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
        try writer["Type"].write(value.type)
        try writer["Version"].write(value.version)
    }

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.EnvironmentTier {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.EnvironmentTier()
        value.name = try reader["Name"].readIfPresent()
        value.type = try reader["Type"].readIfPresent()
        value.version = try reader["Version"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.EnvironmentResourcesDescription {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.EnvironmentResourcesDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.EnvironmentResourcesDescription()
        value.loadBalancer = try reader["LoadBalancer"].readIfPresent(with: ElasticBeanstalkClientTypes.LoadBalancerDescription.read(from:))
        return value
    }
}

extension ElasticBeanstalkClientTypes.LoadBalancerDescription {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.LoadBalancerDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.LoadBalancerDescription()
        value.loadBalancerName = try reader["LoadBalancerName"].readIfPresent()
        value.domain = try reader["Domain"].readIfPresent()
        value.listeners = try reader["Listeners"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.Listener.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ElasticBeanstalkClientTypes.Listener {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.Listener {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.Listener()
        value.`protocol` = try reader["Protocol"].readIfPresent()
        value.port = try reader["Port"].readIfPresent() ?? 0
        return value
    }
}

extension ElasticBeanstalkClientTypes.ApplicationDescription {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.ApplicationDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.ApplicationDescription()
        value.applicationArn = try reader["ApplicationArn"].readIfPresent()
        value.applicationName = try reader["ApplicationName"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.dateCreated = try reader["DateCreated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dateUpdated = try reader["DateUpdated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.versions = try reader["Versions"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.configurationTemplates = try reader["ConfigurationTemplates"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.resourceLifecycleConfig = try reader["ResourceLifecycleConfig"].readIfPresent(with: ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig.read(from:))
        return value
    }
}

extension ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig {

    static func write(value: ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ServiceRole"].write(value.serviceRole)
        try writer["VersionLifecycleConfig"].write(value.versionLifecycleConfig, with: ElasticBeanstalkClientTypes.ApplicationVersionLifecycleConfig.write(value:to:))
    }

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.ApplicationResourceLifecycleConfig()
        value.serviceRole = try reader["ServiceRole"].readIfPresent()
        value.versionLifecycleConfig = try reader["VersionLifecycleConfig"].readIfPresent(with: ElasticBeanstalkClientTypes.ApplicationVersionLifecycleConfig.read(from:))
        return value
    }
}

extension ElasticBeanstalkClientTypes.ApplicationVersionLifecycleConfig {

    static func write(value: ElasticBeanstalkClientTypes.ApplicationVersionLifecycleConfig?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["MaxAgeRule"].write(value.maxAgeRule, with: ElasticBeanstalkClientTypes.MaxAgeRule.write(value:to:))
        try writer["MaxCountRule"].write(value.maxCountRule, with: ElasticBeanstalkClientTypes.MaxCountRule.write(value:to:))
    }

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.ApplicationVersionLifecycleConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.ApplicationVersionLifecycleConfig()
        value.maxCountRule = try reader["MaxCountRule"].readIfPresent(with: ElasticBeanstalkClientTypes.MaxCountRule.read(from:))
        value.maxAgeRule = try reader["MaxAgeRule"].readIfPresent(with: ElasticBeanstalkClientTypes.MaxAgeRule.read(from:))
        return value
    }
}

extension ElasticBeanstalkClientTypes.MaxAgeRule {

    static func write(value: ElasticBeanstalkClientTypes.MaxAgeRule?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DeleteSourceFromS3"].write(value.deleteSourceFromS3)
        try writer["Enabled"].write(value.enabled)
        try writer["MaxAgeInDays"].write(value.maxAgeInDays)
    }

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.MaxAgeRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.MaxAgeRule()
        value.enabled = try reader["Enabled"].readIfPresent() ?? false
        value.maxAgeInDays = try reader["MaxAgeInDays"].readIfPresent()
        value.deleteSourceFromS3 = try reader["DeleteSourceFromS3"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.MaxCountRule {

    static func write(value: ElasticBeanstalkClientTypes.MaxCountRule?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DeleteSourceFromS3"].write(value.deleteSourceFromS3)
        try writer["Enabled"].write(value.enabled)
        try writer["MaxCount"].write(value.maxCount)
    }

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.MaxCountRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.MaxCountRule()
        value.enabled = try reader["Enabled"].readIfPresent() ?? false
        value.maxCount = try reader["MaxCount"].readIfPresent()
        value.deleteSourceFromS3 = try reader["DeleteSourceFromS3"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.ApplicationVersionDescription {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.ApplicationVersionDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.ApplicationVersionDescription()
        value.applicationVersionArn = try reader["ApplicationVersionArn"].readIfPresent()
        value.applicationName = try reader["ApplicationName"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.versionLabel = try reader["VersionLabel"].readIfPresent()
        value.sourceBuildInformation = try reader["SourceBuildInformation"].readIfPresent(with: ElasticBeanstalkClientTypes.SourceBuildInformation.read(from:))
        value.buildArn = try reader["BuildArn"].readIfPresent()
        value.sourceBundle = try reader["SourceBundle"].readIfPresent(with: ElasticBeanstalkClientTypes.S3Location.read(from:))
        value.dateCreated = try reader["DateCreated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dateUpdated = try reader["DateUpdated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.status = try reader["Status"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.S3Location {

    static func write(value: ElasticBeanstalkClientTypes.S3Location?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["S3Bucket"].write(value.s3Bucket)
        try writer["S3Key"].write(value.s3Key)
    }

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.S3Location {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.S3Location()
        value.s3Bucket = try reader["S3Bucket"].readIfPresent()
        value.s3Key = try reader["S3Key"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.SourceBuildInformation {

    static func write(value: ElasticBeanstalkClientTypes.SourceBuildInformation?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["SourceLocation"].write(value.sourceLocation)
        try writer["SourceRepository"].write(value.sourceRepository)
        try writer["SourceType"].write(value.sourceType)
    }

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.SourceBuildInformation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.SourceBuildInformation()
        value.sourceType = try reader["SourceType"].readIfPresent() ?? .sdkUnknown("")
        value.sourceRepository = try reader["SourceRepository"].readIfPresent() ?? .sdkUnknown("")
        value.sourceLocation = try reader["SourceLocation"].readIfPresent() ?? ""
        return value
    }
}

extension ElasticBeanstalkClientTypes.ConfigurationOptionSetting {

    static func write(value: ElasticBeanstalkClientTypes.ConfigurationOptionSetting?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Namespace"].write(value.namespace)
        try writer["OptionName"].write(value.optionName)
        try writer["ResourceName"].write(value.resourceName)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.ConfigurationOptionSetting {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.ConfigurationOptionSetting()
        value.resourceName = try reader["ResourceName"].readIfPresent()
        value.namespace = try reader["Namespace"].readIfPresent()
        value.optionName = try reader["OptionName"].readIfPresent()
        value.value = try reader["Value"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.PlatformSummary {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.PlatformSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.PlatformSummary()
        value.platformArn = try reader["PlatformArn"].readIfPresent()
        value.platformOwner = try reader["PlatformOwner"].readIfPresent()
        value.platformStatus = try reader["PlatformStatus"].readIfPresent()
        value.platformCategory = try reader["PlatformCategory"].readIfPresent()
        value.operatingSystemName = try reader["OperatingSystemName"].readIfPresent()
        value.operatingSystemVersion = try reader["OperatingSystemVersion"].readIfPresent()
        value.supportedTierList = try reader["SupportedTierList"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.supportedAddonList = try reader["SupportedAddonList"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.platformLifecycleState = try reader["PlatformLifecycleState"].readIfPresent()
        value.platformVersion = try reader["PlatformVersion"].readIfPresent()
        value.platformBranchName = try reader["PlatformBranchName"].readIfPresent()
        value.platformBranchLifecycleState = try reader["PlatformBranchLifecycleState"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.Builder {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.Builder {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.Builder()
        value.arn = try reader["ARN"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.ResourceQuotas {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.ResourceQuotas {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.ResourceQuotas()
        value.applicationQuota = try reader["ApplicationQuota"].readIfPresent(with: ElasticBeanstalkClientTypes.ResourceQuota.read(from:))
        value.applicationVersionQuota = try reader["ApplicationVersionQuota"].readIfPresent(with: ElasticBeanstalkClientTypes.ResourceQuota.read(from:))
        value.environmentQuota = try reader["EnvironmentQuota"].readIfPresent(with: ElasticBeanstalkClientTypes.ResourceQuota.read(from:))
        value.configurationTemplateQuota = try reader["ConfigurationTemplateQuota"].readIfPresent(with: ElasticBeanstalkClientTypes.ResourceQuota.read(from:))
        value.customPlatformQuota = try reader["CustomPlatformQuota"].readIfPresent(with: ElasticBeanstalkClientTypes.ResourceQuota.read(from:))
        return value
    }
}

extension ElasticBeanstalkClientTypes.ResourceQuota {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.ResourceQuota {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.ResourceQuota()
        value.maximum = try reader["Maximum"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.ConfigurationOptionDescription {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.ConfigurationOptionDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.ConfigurationOptionDescription()
        value.namespace = try reader["Namespace"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.defaultValue = try reader["DefaultValue"].readIfPresent()
        value.changeSeverity = try reader["ChangeSeverity"].readIfPresent()
        value.userDefined = try reader["UserDefined"].readIfPresent()
        value.valueType = try reader["ValueType"].readIfPresent()
        value.valueOptions = try reader["ValueOptions"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.minValue = try reader["MinValue"].readIfPresent()
        value.maxValue = try reader["MaxValue"].readIfPresent()
        value.maxLength = try reader["MaxLength"].readIfPresent()
        value.regex = try reader["Regex"].readIfPresent(with: ElasticBeanstalkClientTypes.OptionRestrictionRegex.read(from:))
        return value
    }
}

extension ElasticBeanstalkClientTypes.OptionRestrictionRegex {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.OptionRestrictionRegex {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.OptionRestrictionRegex()
        value.pattern = try reader["Pattern"].readIfPresent()
        value.label = try reader["Label"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.ConfigurationSettingsDescription {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.ConfigurationSettingsDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.ConfigurationSettingsDescription()
        value.solutionStackName = try reader["SolutionStackName"].readIfPresent()
        value.platformArn = try reader["PlatformArn"].readIfPresent()
        value.applicationName = try reader["ApplicationName"].readIfPresent()
        value.templateName = try reader["TemplateName"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.environmentName = try reader["EnvironmentName"].readIfPresent()
        value.deploymentStatus = try reader["DeploymentStatus"].readIfPresent()
        value.dateCreated = try reader["DateCreated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dateUpdated = try reader["DateUpdated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.optionSettings = try reader["OptionSettings"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.ConfigurationOptionSetting.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ElasticBeanstalkClientTypes.ApplicationMetrics {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.ApplicationMetrics {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.ApplicationMetrics()
        value.duration = try reader["Duration"].readIfPresent()
        value.requestCount = try reader["RequestCount"].readIfPresent() ?? 0
        value.statusCodes = try reader["StatusCodes"].readIfPresent(with: ElasticBeanstalkClientTypes.StatusCodes.read(from:))
        value.latency = try reader["Latency"].readIfPresent(with: ElasticBeanstalkClientTypes.Latency.read(from:))
        return value
    }
}

extension ElasticBeanstalkClientTypes.Latency {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.Latency {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.Latency()
        value.p999 = try reader["P999"].readIfPresent()
        value.p99 = try reader["P99"].readIfPresent()
        value.p95 = try reader["P95"].readIfPresent()
        value.p90 = try reader["P90"].readIfPresent()
        value.p85 = try reader["P85"].readIfPresent()
        value.p75 = try reader["P75"].readIfPresent()
        value.p50 = try reader["P50"].readIfPresent()
        value.p10 = try reader["P10"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.StatusCodes {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.StatusCodes {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.StatusCodes()
        value.status2xx = try reader["Status2xx"].readIfPresent()
        value.status3xx = try reader["Status3xx"].readIfPresent()
        value.status4xx = try reader["Status4xx"].readIfPresent()
        value.status5xx = try reader["Status5xx"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.InstanceHealthSummary {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.InstanceHealthSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.InstanceHealthSummary()
        value.noData = try reader["NoData"].readIfPresent()
        value.unknown = try reader["Unknown"].readIfPresent()
        value.pending = try reader["Pending"].readIfPresent()
        value.ok = try reader["Ok"].readIfPresent()
        value.info = try reader["Info"].readIfPresent()
        value.warning = try reader["Warning"].readIfPresent()
        value.degraded = try reader["Degraded"].readIfPresent()
        value.severe = try reader["Severe"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.ManagedActionHistoryItem {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.ManagedActionHistoryItem {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.ManagedActionHistoryItem()
        value.actionId = try reader["ActionId"].readIfPresent()
        value.actionType = try reader["ActionType"].readIfPresent()
        value.actionDescription = try reader["ActionDescription"].readIfPresent()
        value.failureType = try reader["FailureType"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.failureDescription = try reader["FailureDescription"].readIfPresent()
        value.executedTime = try reader["ExecutedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.finishedTime = try reader["FinishedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension ElasticBeanstalkClientTypes.ManagedAction {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.ManagedAction {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.ManagedAction()
        value.actionId = try reader["ActionId"].readIfPresent()
        value.actionDescription = try reader["ActionDescription"].readIfPresent()
        value.actionType = try reader["ActionType"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.windowStartTime = try reader["WindowStartTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension ElasticBeanstalkClientTypes.EnvironmentResourceDescription {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.EnvironmentResourceDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.EnvironmentResourceDescription()
        value.environmentName = try reader["EnvironmentName"].readIfPresent()
        value.autoScalingGroups = try reader["AutoScalingGroups"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.AutoScalingGroup.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.instances = try reader["Instances"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.Instance.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.launchConfigurations = try reader["LaunchConfigurations"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.LaunchConfiguration.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.launchTemplates = try reader["LaunchTemplates"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.LaunchTemplate.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.loadBalancers = try reader["LoadBalancers"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.LoadBalancer.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.triggers = try reader["Triggers"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.Trigger.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.queues = try reader["Queues"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.Queue.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ElasticBeanstalkClientTypes.Queue {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.Queue {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.Queue()
        value.name = try reader["Name"].readIfPresent()
        value.url = try reader["URL"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.Trigger {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.Trigger {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.Trigger()
        value.name = try reader["Name"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.LoadBalancer {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.LoadBalancer {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.LoadBalancer()
        value.name = try reader["Name"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.LaunchTemplate {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.LaunchTemplate {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.LaunchTemplate()
        value.id = try reader["Id"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.LaunchConfiguration {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.LaunchConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.LaunchConfiguration()
        value.name = try reader["Name"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.Instance {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.Instance {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.Instance()
        value.id = try reader["Id"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.AutoScalingGroup {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.AutoScalingGroup {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.AutoScalingGroup()
        value.name = try reader["Name"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.EventDescription {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.EventDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.EventDescription()
        value.eventDate = try reader["EventDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.message = try reader["Message"].readIfPresent()
        value.applicationName = try reader["ApplicationName"].readIfPresent()
        value.versionLabel = try reader["VersionLabel"].readIfPresent()
        value.templateName = try reader["TemplateName"].readIfPresent()
        value.environmentName = try reader["EnvironmentName"].readIfPresent()
        value.platformArn = try reader["PlatformArn"].readIfPresent()
        value.requestId = try reader["RequestId"].readIfPresent()
        value.severity = try reader["Severity"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.SingleInstanceHealth {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.SingleInstanceHealth {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.SingleInstanceHealth()
        value.instanceId = try reader["InstanceId"].readIfPresent()
        value.healthStatus = try reader["HealthStatus"].readIfPresent()
        value.color = try reader["Color"].readIfPresent()
        value.causes = try reader["Causes"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.launchedAt = try reader["LaunchedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.applicationMetrics = try reader["ApplicationMetrics"].readIfPresent(with: ElasticBeanstalkClientTypes.ApplicationMetrics.read(from:))
        value.system = try reader["System"].readIfPresent(with: ElasticBeanstalkClientTypes.SystemStatus.read(from:))
        value.deployment = try reader["Deployment"].readIfPresent(with: ElasticBeanstalkClientTypes.Deployment.read(from:))
        value.availabilityZone = try reader["AvailabilityZone"].readIfPresent()
        value.instanceType = try reader["InstanceType"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.Deployment {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.Deployment {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.Deployment()
        value.versionLabel = try reader["VersionLabel"].readIfPresent()
        value.deploymentId = try reader["DeploymentId"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.deploymentTime = try reader["DeploymentTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension ElasticBeanstalkClientTypes.SystemStatus {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.SystemStatus {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.SystemStatus()
        value.cpuUtilization = try reader["CPUUtilization"].readIfPresent(with: ElasticBeanstalkClientTypes.CPUUtilization.read(from:))
        value.loadAverage = try reader["LoadAverage"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readDouble(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ElasticBeanstalkClientTypes.CPUUtilization {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.CPUUtilization {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.CPUUtilization()
        value.user = try reader["User"].readIfPresent()
        value.nice = try reader["Nice"].readIfPresent()
        value.system = try reader["System"].readIfPresent()
        value.idle = try reader["Idle"].readIfPresent()
        value.ioWait = try reader["IOWait"].readIfPresent()
        value.irq = try reader["IRQ"].readIfPresent()
        value.softIRQ = try reader["SoftIRQ"].readIfPresent()
        value.privileged = try reader["Privileged"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.PlatformDescription {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.PlatformDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.PlatformDescription()
        value.platformArn = try reader["PlatformArn"].readIfPresent()
        value.platformOwner = try reader["PlatformOwner"].readIfPresent()
        value.platformName = try reader["PlatformName"].readIfPresent()
        value.platformVersion = try reader["PlatformVersion"].readIfPresent()
        value.solutionStackName = try reader["SolutionStackName"].readIfPresent()
        value.platformStatus = try reader["PlatformStatus"].readIfPresent()
        value.dateCreated = try reader["DateCreated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dateUpdated = try reader["DateUpdated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.platformCategory = try reader["PlatformCategory"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.maintainer = try reader["Maintainer"].readIfPresent()
        value.operatingSystemName = try reader["OperatingSystemName"].readIfPresent()
        value.operatingSystemVersion = try reader["OperatingSystemVersion"].readIfPresent()
        value.programmingLanguages = try reader["ProgrammingLanguages"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.PlatformProgrammingLanguage.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.frameworks = try reader["Frameworks"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.PlatformFramework.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.customAmiList = try reader["CustomAmiList"].readListIfPresent(memberReadingClosure: ElasticBeanstalkClientTypes.CustomAmi.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.supportedTierList = try reader["SupportedTierList"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.supportedAddonList = try reader["SupportedAddonList"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.platformLifecycleState = try reader["PlatformLifecycleState"].readIfPresent()
        value.platformBranchName = try reader["PlatformBranchName"].readIfPresent()
        value.platformBranchLifecycleState = try reader["PlatformBranchLifecycleState"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.CustomAmi {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.CustomAmi {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.CustomAmi()
        value.virtualizationType = try reader["VirtualizationType"].readIfPresent()
        value.imageId = try reader["ImageId"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.PlatformFramework {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.PlatformFramework {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.PlatformFramework()
        value.name = try reader["Name"].readIfPresent()
        value.version = try reader["Version"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.PlatformProgrammingLanguage {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.PlatformProgrammingLanguage {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.PlatformProgrammingLanguage()
        value.name = try reader["Name"].readIfPresent()
        value.version = try reader["Version"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.SolutionStackDescription {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.SolutionStackDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.SolutionStackDescription()
        value.solutionStackName = try reader["SolutionStackName"].readIfPresent()
        value.permittedFileTypes = try reader["PermittedFileTypes"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ElasticBeanstalkClientTypes.PlatformBranchSummary {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.PlatformBranchSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.PlatformBranchSummary()
        value.platformName = try reader["PlatformName"].readIfPresent()
        value.branchName = try reader["BranchName"].readIfPresent()
        value.lifecycleState = try reader["LifecycleState"].readIfPresent()
        value.branchOrder = try reader["BranchOrder"].readIfPresent() ?? 0
        value.supportedTierList = try reader["SupportedTierList"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ElasticBeanstalkClientTypes.Tag {

    static func write(value: ElasticBeanstalkClientTypes.Tag?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Key"].write(value.key)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.Tag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.Tag()
        value.key = try reader["Key"].readIfPresent()
        value.value = try reader["Value"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.EnvironmentInfoDescription {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.EnvironmentInfoDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.EnvironmentInfoDescription()
        value.infoType = try reader["InfoType"].readIfPresent()
        value.ec2InstanceId = try reader["Ec2InstanceId"].readIfPresent()
        value.sampleTimestamp = try reader["SampleTimestamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.message = try reader["Message"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.ValidationMessage {

    static func read(from reader: SmithyXML.Reader) throws -> ElasticBeanstalkClientTypes.ValidationMessage {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticBeanstalkClientTypes.ValidationMessage()
        value.message = try reader["Message"].readIfPresent()
        value.severity = try reader["Severity"].readIfPresent()
        value.namespace = try reader["Namespace"].readIfPresent()
        value.optionName = try reader["OptionName"].readIfPresent()
        return value
    }
}

extension ElasticBeanstalkClientTypes.BuildConfiguration {

    static func write(value: ElasticBeanstalkClientTypes.BuildConfiguration?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ArtifactName"].write(value.artifactName)
        try writer["CodeBuildServiceRole"].write(value.codeBuildServiceRole)
        try writer["ComputeType"].write(value.computeType)
        try writer["Image"].write(value.image)
        try writer["TimeoutInMinutes"].write(value.timeoutInMinutes)
    }
}

extension ElasticBeanstalkClientTypes.SourceConfiguration {

    static func write(value: ElasticBeanstalkClientTypes.SourceConfiguration?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplicationName"].write(value.applicationName)
        try writer["TemplateName"].write(value.templateName)
    }
}

extension ElasticBeanstalkClientTypes.OptionSpecification {

    static func write(value: ElasticBeanstalkClientTypes.OptionSpecification?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Namespace"].write(value.namespace)
        try writer["OptionName"].write(value.optionName)
        try writer["ResourceName"].write(value.resourceName)
    }
}

extension ElasticBeanstalkClientTypes.SearchFilter {

    static func write(value: ElasticBeanstalkClientTypes.SearchFilter?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Attribute"].write(value.attribute)
        try writer["Operator"].write(value.`operator`)
        try writer["Values"].writeList(value.values, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ElasticBeanstalkClientTypes.PlatformFilter {

    static func write(value: ElasticBeanstalkClientTypes.PlatformFilter?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Operator"].write(value.`operator`)
        try writer["Type"].write(value.type)
        try writer["Values"].writeList(value.values, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

public enum ElasticBeanstalkClientTypes {}
