//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHT<PERSON>API.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
@_spi(SmithyTimestamps) import struct SmithyTimestamps.TimestampFormatter

/// You do not have sufficient access to perform this action.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension ControlCatalogClientTypes {

    /// A summary of the domain that a common control or an objective belongs to.
    public struct AssociatedDomainSummary: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the related domain.
        public var arn: Swift.String?
        /// The name of the related domain.
        public var name: Swift.String?

        public init(
            arn: Swift.String? = nil,
            name: Swift.String? = nil
        )
        {
            self.arn = arn
            self.name = name
        }
    }
}

extension ControlCatalogClientTypes {

    /// A summary of the objective that a common control supports.
    public struct AssociatedObjectiveSummary: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the related objective.
        public var arn: Swift.String?
        /// The name of the related objective.
        public var name: Swift.String?

        public init(
            arn: Swift.String? = nil,
            name: Swift.String? = nil
        )
        {
            self.arn = arn
            self.name = name
        }
    }
}

extension ControlCatalogClientTypes {

    /// The objective resource that's being used as a filter.
    public struct ObjectiveResourceFilter: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the objective.
        public var arn: Swift.String?

        public init(
            arn: Swift.String? = nil
        )
        {
            self.arn = arn
        }
    }
}

extension ControlCatalogClientTypes {

    /// An optional filter that narrows the results to a specific objective.
    public struct CommonControlFilter: Swift.Sendable {
        /// The objective that's used as filter criteria. You can use this parameter to specify one objective ARN at a time. Passing multiple ARNs in the CommonControlFilter isn’t currently supported.
        public var objectives: [ControlCatalogClientTypes.ObjectiveResourceFilter]?

        public init(
            objectives: [ControlCatalogClientTypes.ObjectiveResourceFilter]? = nil
        )
        {
            self.objectives = objectives
        }
    }
}

/// An internal service error occurred during the processing of your request. Try again later.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request was denied due to request throttling.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { true }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request has invalid or missing parameters.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct ListCommonControlsInput: Swift.Sendable {
    /// An optional filter that narrows the results to a specific objective. This filter allows you to specify one objective ARN at a time. Passing multiple ARNs in the CommonControlFilter isn’t currently supported.
    public var commonControlFilter: ControlCatalogClientTypes.CommonControlFilter?
    /// The maximum number of results on a page or for an API request call.
    public var maxResults: Swift.Int?
    /// The pagination token that's used to fetch the next set of results.
    public var nextToken: Swift.String?

    public init(
        commonControlFilter: ControlCatalogClientTypes.CommonControlFilter? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.commonControlFilter = commonControlFilter
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension ControlCatalogClientTypes {

    /// A summary of metadata for a common control.
    public struct CommonControlSummary: Swift.Sendable {
        /// The Amazon Resource Name (ARN) that identifies the common control.
        /// This member is required.
        public var arn: Swift.String?
        /// The time when the common control was created.
        /// This member is required.
        public var createTime: Foundation.Date?
        /// The description of the common control.
        /// This member is required.
        public var description: Swift.String?
        /// The domain that the common control belongs to.
        /// This member is required.
        public var domain: ControlCatalogClientTypes.AssociatedDomainSummary?
        /// The time when the common control was most recently updated.
        /// This member is required.
        public var lastUpdateTime: Foundation.Date?
        /// The name of the common control.
        /// This member is required.
        public var name: Swift.String?
        /// The objective that the common control belongs to.
        /// This member is required.
        public var objective: ControlCatalogClientTypes.AssociatedObjectiveSummary?

        public init(
            arn: Swift.String? = nil,
            createTime: Foundation.Date? = nil,
            description: Swift.String? = nil,
            domain: ControlCatalogClientTypes.AssociatedDomainSummary? = nil,
            lastUpdateTime: Foundation.Date? = nil,
            name: Swift.String? = nil,
            objective: ControlCatalogClientTypes.AssociatedObjectiveSummary? = nil
        )
        {
            self.arn = arn
            self.createTime = createTime
            self.description = description
            self.domain = domain
            self.lastUpdateTime = lastUpdateTime
            self.name = name
            self.objective = objective
        }
    }
}

public struct ListCommonControlsOutput: Swift.Sendable {
    /// The list of common controls that the ListCommonControls API returns.
    /// This member is required.
    public var commonControls: [ControlCatalogClientTypes.CommonControlSummary]?
    /// The pagination token that's used to fetch the next set of results.
    public var nextToken: Swift.String?

    public init(
        commonControls: [ControlCatalogClientTypes.CommonControlSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.commonControls = commonControls
        self.nextToken = nextToken
    }
}

extension ControlCatalogClientTypes {

    public enum ControlBehavior: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case detective
        case preventive
        case proactive
        case sdkUnknown(Swift.String)

        public static var allCases: [ControlBehavior] {
            return [
                .detective,
                .preventive,
                .proactive
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .detective: return "DETECTIVE"
            case .preventive: return "PREVENTIVE"
            case .proactive: return "PROACTIVE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The requested resource does not exist.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct GetControlInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the control. It has one of the following formats: Global format arn:{PARTITION}:controlcatalog:::control/{CONTROL_CATALOG_OPAQUE_ID} Or Regional format arn:{PARTITION}:controltower:{REGION}::control/{CONTROL_TOWER_OPAQUE_ID} Here is a more general pattern that covers Amazon Web Services Control Tower and Control Catalog ARNs: ^arn:(aws(?:[-a-z]*)?):(controlcatalog|controltower):[a-zA-Z0-9-]*::control/[0-9a-zA-Z_\\-]+$
    /// This member is required.
    public var controlArn: Swift.String?

    public init(
        controlArn: Swift.String? = nil
    )
    {
        self.controlArn = controlArn
    }
}

extension ControlCatalogClientTypes {

    /// An object that describes the implementation type for a control. Our ImplementationDetailsType format has three required segments:
    ///
    /// * SERVICE-PROVIDER::SERVICE-NAME::RESOURCE-NAME
    ///
    ///
    /// For example, AWS::Config::ConfigRule or AWS::SecurityHub::SecurityControl resources have the format with three required segments. Our ImplementationDetailsType format has an optional fourth segment, which is present for applicable implementation types. The format is as follows:
    ///
    /// * SERVICE-PROVIDER::SERVICE-NAME::RESOURCE-NAME::RESOURCE-TYPE-DESCRIPTION
    ///
    ///
    /// For example, AWS::Organizations::Policy::SERVICE_CONTROL_POLICY or AWS::CloudFormation::Type::HOOK have the format with four segments. Although the format is similar, the values for the Type field do not match any Amazon Web Services CloudFormation values.
    public struct ImplementationDetails: Swift.Sendable {
        /// A string that describes a control's implementation type.
        /// This member is required.
        public var type: Swift.String?

        public init(
            type: Swift.String? = nil
        )
        {
            self.type = type
        }
    }
}

extension ControlCatalogClientTypes {

    /// Four types of control parameters are supported.
    ///
    /// * AllowedRegions: List of Amazon Web Services Regions exempted from the control. Each string is expected to be an Amazon Web Services Region code. This parameter is mandatory for the OU Region deny control, CT.MULTISERVICE.PV.1. Example: ["us-east-1","us-west-2"]
    ///
    /// * ExemptedActions: List of Amazon Web Services IAM actions exempted from the control. Each string is expected to be an IAM action. Example: ["logs:DescribeLogGroups","logs:StartQuery","logs:GetQueryResults"]
    ///
    /// * ExemptedPrincipalArns: List of Amazon Web Services IAM principal ARNs exempted from the control. Each string is expected to be an IAM principal that follows the pattern ^arn:(aws|aws-us-gov):(iam|sts)::.+:.+$ Example: ["arn:aws:iam::*:role/ReadOnly","arn:aws:sts::*:assumed-role/ReadOnly/*"]
    ///
    /// * ExemptedResourceArns: List of resource ARNs exempted from the control. Each string is expected to be a resource ARN. Example: ["arn:aws:s3:::my-bucket-name"]
    public struct ControlParameter: Swift.Sendable {
        /// The parameter name. This name is the parameter key when you call [EnableControl](https://docs.aws.amazon.com/controltower/latest/APIReference/API_EnableControl.html) or [UpdateEnabledControl](https://docs.aws.amazon.com/controltower/latest/APIReference/API_UpdateEnabledControl.html).
        /// This member is required.
        public var name: Swift.String?

        public init(
            name: Swift.String? = nil
        )
        {
            self.name = name
        }
    }
}

extension ControlCatalogClientTypes {

    public enum ControlScope: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case global
        case regional
        case sdkUnknown(Swift.String)

        public static var allCases: [ControlScope] {
            return [
                .global,
                .regional
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .global: return "GLOBAL"
            case .regional: return "REGIONAL"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ControlCatalogClientTypes {

    /// Returns information about the control, including the scope of the control, if enabled, and the Regions in which the control currently is available for deployment. For more information about scope, see [Global services](https://docs.aws.amazon.com/whitepapers/latest/aws-fault-isolation-boundaries/global-services.html). If you are applying controls through an Amazon Web Services Control Tower landing zone environment, remember that the values returned in the RegionConfiguration API operation are not related to the governed Regions in your landing zone. For example, if you are governing Regions A,B,and C while the control is available in Regions A, B, C, and D, you'd see a response with DeployableRegions of A, B, C, and D for a control with REGIONAL scope, even though you may not intend to deploy the control in Region D, because you do not govern it through your landing zone.
    public struct RegionConfiguration: Swift.Sendable {
        /// Regions in which the control is available to be deployed.
        public var deployableRegions: [Swift.String]?
        /// The coverage of the control, if deployed. Scope is an enumerated type, with value Regional, or Global. A control with Global scope is effective in all Amazon Web Services Regions, regardless of the Region from which it is enabled, or to which it is deployed. A control implemented by an SCP is usually Global in scope. A control with Regional scope has operations that are restricted specifically to the Region from which it is enabled and to which it is deployed. Controls implemented by Config rules and CloudFormation hooks usually are Regional in scope. Security Hub controls usually are Regional in scope.
        /// This member is required.
        public var scope: ControlCatalogClientTypes.ControlScope?

        public init(
            deployableRegions: [Swift.String]? = nil,
            scope: ControlCatalogClientTypes.ControlScope? = nil
        )
        {
            self.deployableRegions = deployableRegions
            self.scope = scope
        }
    }
}

public struct GetControlOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the control.
    /// This member is required.
    public var arn: Swift.String?
    /// A term that identifies the control's functional behavior. One of Preventive, Detective, Proactive
    /// This member is required.
    public var behavior: ControlCatalogClientTypes.ControlBehavior?
    /// A description of what the control does.
    /// This member is required.
    public var description: Swift.String?
    /// Returns information about the control, as an ImplementationDetails object that shows the underlying implementation type for a control.
    public var implementation: ControlCatalogClientTypes.ImplementationDetails?
    /// The display name of the control.
    /// This member is required.
    public var name: Swift.String?
    /// Returns an array of ControlParameter objects that specify the parameters a control supports. An empty list is returned for controls that don’t support parameters.
    public var parameters: [ControlCatalogClientTypes.ControlParameter]?
    /// Returns information about the control, including the scope of the control, if enabled, and the Regions in which the control currently is available for deployment. For more information about scope, see [Global services](https://docs.aws.amazon.com/whitepapers/latest/aws-fault-isolation-boundaries/global-services.html). If you are applying controls through an Amazon Web Services Control Tower landing zone environment, remember that the values returned in the RegionConfiguration API operation are not related to the governed Regions in your landing zone. For example, if you are governing Regions A,B,and C while the control is available in Regions A, B, C, and D, you'd see a response with DeployableRegions of A, B, C, and D for a control with REGIONAL scope, even though you may not intend to deploy the control in Region D, because you do not govern it through your landing zone.
    /// This member is required.
    public var regionConfiguration: ControlCatalogClientTypes.RegionConfiguration?

    public init(
        arn: Swift.String? = nil,
        behavior: ControlCatalogClientTypes.ControlBehavior? = nil,
        description: Swift.String? = nil,
        implementation: ControlCatalogClientTypes.ImplementationDetails? = nil,
        name: Swift.String? = nil,
        parameters: [ControlCatalogClientTypes.ControlParameter]? = nil,
        regionConfiguration: ControlCatalogClientTypes.RegionConfiguration? = nil
    )
    {
        self.arn = arn
        self.behavior = behavior
        self.description = description
        self.implementation = implementation
        self.name = name
        self.parameters = parameters
        self.regionConfiguration = regionConfiguration
    }
}

public struct ListControlsInput: Swift.Sendable {
    /// The maximum number of results on a page or for an API request call.
    public var maxResults: Swift.Int?
    /// The pagination token that's used to fetch the next set of results.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension ControlCatalogClientTypes {

    /// Overview of information about a control.
    public struct ControlSummary: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the control.
        /// This member is required.
        public var arn: Swift.String?
        /// A description of the control, as it may appear in the console. Describes the functionality of the control.
        /// This member is required.
        public var description: Swift.String?
        /// The display name of the control.
        /// This member is required.
        public var name: Swift.String?

        public init(
            arn: Swift.String? = nil,
            description: Swift.String? = nil,
            name: Swift.String? = nil
        )
        {
            self.arn = arn
            self.description = description
            self.name = name
        }
    }
}

public struct ListControlsOutput: Swift.Sendable {
    /// Returns a list of controls, given as structures of type controlSummary.
    /// This member is required.
    public var controls: [ControlCatalogClientTypes.ControlSummary]?
    /// The pagination token that's used to fetch the next set of results.
    public var nextToken: Swift.String?

    public init(
        controls: [ControlCatalogClientTypes.ControlSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.controls = controls
        self.nextToken = nextToken
    }
}

public struct ListDomainsInput: Swift.Sendable {
    /// The maximum number of results on a page or for an API request call.
    public var maxResults: Swift.Int?
    /// The pagination token that's used to fetch the next set of results.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension ControlCatalogClientTypes {

    /// A summary of metadata for a domain.
    public struct DomainSummary: Swift.Sendable {
        /// The Amazon Resource Name (ARN) that identifies the domain.
        /// This member is required.
        public var arn: Swift.String?
        /// The time when the domain was created.
        /// This member is required.
        public var createTime: Foundation.Date?
        /// The description of the domain.
        /// This member is required.
        public var description: Swift.String?
        /// The time when the domain was most recently updated.
        /// This member is required.
        public var lastUpdateTime: Foundation.Date?
        /// The name of the domain.
        /// This member is required.
        public var name: Swift.String?

        public init(
            arn: Swift.String? = nil,
            createTime: Foundation.Date? = nil,
            description: Swift.String? = nil,
            lastUpdateTime: Foundation.Date? = nil,
            name: Swift.String? = nil
        )
        {
            self.arn = arn
            self.createTime = createTime
            self.description = description
            self.lastUpdateTime = lastUpdateTime
            self.name = name
        }
    }
}

public struct ListDomainsOutput: Swift.Sendable {
    /// The list of domains that the ListDomains API returns.
    /// This member is required.
    public var domains: [ControlCatalogClientTypes.DomainSummary]?
    /// The pagination token that's used to fetch the next set of results.
    public var nextToken: Swift.String?

    public init(
        domains: [ControlCatalogClientTypes.DomainSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.domains = domains
        self.nextToken = nextToken
    }
}

extension ControlCatalogClientTypes {

    /// The domain resource that's being used as a filter.
    public struct DomainResourceFilter: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the domain.
        public var arn: Swift.String?

        public init(
            arn: Swift.String? = nil
        )
        {
            self.arn = arn
        }
    }
}

extension ControlCatalogClientTypes {

    /// An optional filter that narrows the list of objectives to a specific domain.
    public struct ObjectiveFilter: Swift.Sendable {
        /// The domain that's used as filter criteria. You can use this parameter to specify one domain ARN at a time. Passing multiple ARNs in the ObjectiveFilter isn’t currently supported.
        public var domains: [ControlCatalogClientTypes.DomainResourceFilter]?

        public init(
            domains: [ControlCatalogClientTypes.DomainResourceFilter]? = nil
        )
        {
            self.domains = domains
        }
    }
}

public struct ListObjectivesInput: Swift.Sendable {
    /// The maximum number of results on a page or for an API request call.
    public var maxResults: Swift.Int?
    /// The pagination token that's used to fetch the next set of results.
    public var nextToken: Swift.String?
    /// An optional filter that narrows the results to a specific domain. This filter allows you to specify one domain ARN at a time. Passing multiple ARNs in the ObjectiveFilter isn’t currently supported.
    public var objectiveFilter: ControlCatalogClientTypes.ObjectiveFilter?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        objectiveFilter: ControlCatalogClientTypes.ObjectiveFilter? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.objectiveFilter = objectiveFilter
    }
}

extension ControlCatalogClientTypes {

    /// A summary of metadata for an objective.
    public struct ObjectiveSummary: Swift.Sendable {
        /// The Amazon Resource Name (ARN) that identifies the objective.
        /// This member is required.
        public var arn: Swift.String?
        /// The time when the objective was created.
        /// This member is required.
        public var createTime: Foundation.Date?
        /// The description of the objective.
        /// This member is required.
        public var description: Swift.String?
        /// The domain that the objective belongs to.
        /// This member is required.
        public var domain: ControlCatalogClientTypes.AssociatedDomainSummary?
        /// The time when the objective was most recently updated.
        /// This member is required.
        public var lastUpdateTime: Foundation.Date?
        /// The name of the objective.
        /// This member is required.
        public var name: Swift.String?

        public init(
            arn: Swift.String? = nil,
            createTime: Foundation.Date? = nil,
            description: Swift.String? = nil,
            domain: ControlCatalogClientTypes.AssociatedDomainSummary? = nil,
            lastUpdateTime: Foundation.Date? = nil,
            name: Swift.String? = nil
        )
        {
            self.arn = arn
            self.createTime = createTime
            self.description = description
            self.domain = domain
            self.lastUpdateTime = lastUpdateTime
            self.name = name
        }
    }
}

public struct ListObjectivesOutput: Swift.Sendable {
    /// The pagination token that's used to fetch the next set of results.
    public var nextToken: Swift.String?
    /// The list of objectives that the ListObjectives API returns.
    /// This member is required.
    public var objectives: [ControlCatalogClientTypes.ObjectiveSummary]?

    public init(
        nextToken: Swift.String? = nil,
        objectives: [ControlCatalogClientTypes.ObjectiveSummary]? = nil
    )
    {
        self.nextToken = nextToken
        self.objectives = objectives
    }
}

extension GetControlInput {

    static func urlPathProvider(_ value: GetControlInput) -> Swift.String? {
        return "/get-control"
    }
}

extension ListCommonControlsInput {

    static func urlPathProvider(_ value: ListCommonControlsInput) -> Swift.String? {
        return "/common-controls"
    }
}

extension ListCommonControlsInput {

    static func queryItemProvider(_ value: ListCommonControlsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListControlsInput {

    static func urlPathProvider(_ value: ListControlsInput) -> Swift.String? {
        return "/list-controls"
    }
}

extension ListControlsInput {

    static func queryItemProvider(_ value: ListControlsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListDomainsInput {

    static func urlPathProvider(_ value: ListDomainsInput) -> Swift.String? {
        return "/domains"
    }
}

extension ListDomainsInput {

    static func queryItemProvider(_ value: ListDomainsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListObjectivesInput {

    static func urlPathProvider(_ value: ListObjectivesInput) -> Swift.String? {
        return "/objectives"
    }
}

extension ListObjectivesInput {

    static func queryItemProvider(_ value: ListObjectivesInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension GetControlInput {

    static func write(value: GetControlInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ControlArn"].write(value.controlArn)
    }
}

extension ListCommonControlsInput {

    static func write(value: ListCommonControlsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CommonControlFilter"].write(value.commonControlFilter, with: ControlCatalogClientTypes.CommonControlFilter.write(value:to:))
    }
}

extension ListObjectivesInput {

    static func write(value: ListObjectivesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ObjectiveFilter"].write(value.objectiveFilter, with: ControlCatalogClientTypes.ObjectiveFilter.write(value:to:))
    }
}

extension GetControlOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetControlOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetControlOutput()
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.behavior = try reader["Behavior"].readIfPresent() ?? .sdkUnknown("")
        value.description = try reader["Description"].readIfPresent() ?? ""
        value.implementation = try reader["Implementation"].readIfPresent(with: ControlCatalogClientTypes.ImplementationDetails.read(from:))
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.parameters = try reader["Parameters"].readListIfPresent(memberReadingClosure: ControlCatalogClientTypes.ControlParameter.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.regionConfiguration = try reader["RegionConfiguration"].readIfPresent(with: ControlCatalogClientTypes.RegionConfiguration.read(from:))
        return value
    }
}

extension ListCommonControlsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListCommonControlsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListCommonControlsOutput()
        value.commonControls = try reader["CommonControls"].readListIfPresent(memberReadingClosure: ControlCatalogClientTypes.CommonControlSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListControlsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListControlsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListControlsOutput()
        value.controls = try reader["Controls"].readListIfPresent(memberReadingClosure: ControlCatalogClientTypes.ControlSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListDomainsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListDomainsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListDomainsOutput()
        value.domains = try reader["Domains"].readListIfPresent(memberReadingClosure: ControlCatalogClientTypes.DomainSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListObjectivesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListObjectivesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListObjectivesOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.objectives = try reader["Objectives"].readListIfPresent(memberReadingClosure: ControlCatalogClientTypes.ObjectiveSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

enum GetControlOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListCommonControlsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListControlsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListDomainsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListObjectivesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        var value = ThrottlingException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ControlCatalogClientTypes.RegionConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlCatalogClientTypes.RegionConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlCatalogClientTypes.RegionConfiguration()
        value.scope = try reader["Scope"].readIfPresent() ?? .sdkUnknown("")
        value.deployableRegions = try reader["DeployableRegions"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ControlCatalogClientTypes.ImplementationDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlCatalogClientTypes.ImplementationDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlCatalogClientTypes.ImplementationDetails()
        value.type = try reader["Type"].readIfPresent() ?? ""
        return value
    }
}

extension ControlCatalogClientTypes.ControlParameter {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlCatalogClientTypes.ControlParameter {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlCatalogClientTypes.ControlParameter()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension ControlCatalogClientTypes.CommonControlSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlCatalogClientTypes.CommonControlSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlCatalogClientTypes.CommonControlSummary()
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.description = try reader["Description"].readIfPresent() ?? ""
        value.domain = try reader["Domain"].readIfPresent(with: ControlCatalogClientTypes.AssociatedDomainSummary.read(from:))
        value.objective = try reader["Objective"].readIfPresent(with: ControlCatalogClientTypes.AssociatedObjectiveSummary.read(from:))
        value.createTime = try reader["CreateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.lastUpdateTime = try reader["LastUpdateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension ControlCatalogClientTypes.AssociatedObjectiveSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlCatalogClientTypes.AssociatedObjectiveSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlCatalogClientTypes.AssociatedObjectiveSummary()
        value.arn = try reader["Arn"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        return value
    }
}

extension ControlCatalogClientTypes.AssociatedDomainSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlCatalogClientTypes.AssociatedDomainSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlCatalogClientTypes.AssociatedDomainSummary()
        value.arn = try reader["Arn"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        return value
    }
}

extension ControlCatalogClientTypes.ControlSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlCatalogClientTypes.ControlSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlCatalogClientTypes.ControlSummary()
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.description = try reader["Description"].readIfPresent() ?? ""
        return value
    }
}

extension ControlCatalogClientTypes.DomainSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlCatalogClientTypes.DomainSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlCatalogClientTypes.DomainSummary()
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.description = try reader["Description"].readIfPresent() ?? ""
        value.createTime = try reader["CreateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.lastUpdateTime = try reader["LastUpdateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension ControlCatalogClientTypes.ObjectiveSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ControlCatalogClientTypes.ObjectiveSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ControlCatalogClientTypes.ObjectiveSummary()
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.description = try reader["Description"].readIfPresent() ?? ""
        value.domain = try reader["Domain"].readIfPresent(with: ControlCatalogClientTypes.AssociatedDomainSummary.read(from:))
        value.createTime = try reader["CreateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.lastUpdateTime = try reader["LastUpdateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension ControlCatalogClientTypes.CommonControlFilter {

    static func write(value: ControlCatalogClientTypes.CommonControlFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Objectives"].writeList(value.objectives, memberWritingClosure: ControlCatalogClientTypes.ObjectiveResourceFilter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ControlCatalogClientTypes.ObjectiveResourceFilter {

    static func write(value: ControlCatalogClientTypes.ObjectiveResourceFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Arn"].write(value.arn)
    }
}

extension ControlCatalogClientTypes.ObjectiveFilter {

    static func write(value: ControlCatalogClientTypes.ObjectiveFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Domains"].writeList(value.domains, memberWritingClosure: ControlCatalogClientTypes.DomainResourceFilter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ControlCatalogClientTypes.DomainResourceFilter {

    static func write(value: ControlCatalogClientTypes.DomainResourceFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Arn"].write(value.arn)
    }
}

public enum ControlCatalogClientTypes {}
