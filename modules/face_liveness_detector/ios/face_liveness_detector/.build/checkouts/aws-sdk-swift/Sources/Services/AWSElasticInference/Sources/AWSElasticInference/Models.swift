//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import class SmithyHT<PERSON><PERSON>I.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem

extension ElasticInferenceClientTypes {

    /// The memory information of an Elastic Inference Accelerator type.
    public struct MemoryInfo: Swift.Sendable {
        /// The size in mebibytes of the Elastic Inference Accelerator type.
        public var sizeInMiB: Swift.Int

        public init(
            sizeInMiB: Swift.Int = 0
        )
        {
            self.sizeInMiB = sizeInMiB
        }
    }
}

extension ElasticInferenceClientTypes {

    /// A throughput entry for an Elastic Inference Accelerator type.
    public struct KeyValuePair: Swift.Sendable {
        /// The throughput value of the Elastic Inference Accelerator type. It can assume the following values: TFLOPS16bit: the throughput expressed in 16bit TeraFLOPS. TFLOPS32bit: the throughput expressed in 32bit TeraFLOPS.
        public var key: Swift.String?
        /// The throughput value of the Elastic Inference Accelerator type.
        public var value: Swift.Int

        public init(
            key: Swift.String? = nil,
            value: Swift.Int = 0
        )
        {
            self.key = key
            self.value = value
        }
    }
}

extension ElasticInferenceClientTypes {

    /// The details of an Elastic Inference Accelerator type.
    public struct AcceleratorType: Swift.Sendable {
        /// The name of the Elastic Inference Accelerator type.
        public var acceleratorTypeName: Swift.String?
        /// The memory information of the Elastic Inference Accelerator type.
        public var memoryInfo: ElasticInferenceClientTypes.MemoryInfo?
        /// The throughput information of the Elastic Inference Accelerator type.
        public var throughputInfo: [ElasticInferenceClientTypes.KeyValuePair]?

        public init(
            acceleratorTypeName: Swift.String? = nil,
            memoryInfo: ElasticInferenceClientTypes.MemoryInfo? = nil,
            throughputInfo: [ElasticInferenceClientTypes.KeyValuePair]? = nil
        )
        {
            self.acceleratorTypeName = acceleratorTypeName
            self.memoryInfo = memoryInfo
            self.throughputInfo = throughputInfo
        }
    }
}

extension ElasticInferenceClientTypes {

    public enum LocationType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case availabilityZone
        case availabilityZoneId
        case region
        case sdkUnknown(Swift.String)

        public static var allCases: [LocationType] {
            return [
                .availabilityZone,
                .availabilityZoneId,
                .region
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .availabilityZone: return "availability-zone"
            case .availabilityZoneId: return "availability-zone-id"
            case .region: return "region"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ElasticInferenceClientTypes {

    /// The offering for an Elastic Inference Accelerator type.
    public struct AcceleratorTypeOffering: Swift.Sendable {
        /// The name of the Elastic Inference Accelerator type.
        public var acceleratorType: Swift.String?
        /// The location for the offering. It will return either the region, availability zone or availability zone id for the offering depending on the locationType value.
        public var location: Swift.String?
        /// The location type for the offering. It can assume the following values: region: defines that the offering is at the regional level. availability-zone: defines that the offering is at the availability zone level. availability-zone-id: defines that the offering is at the availability zone level, defined by the availability zone id.
        public var locationType: ElasticInferenceClientTypes.LocationType?

        public init(
            acceleratorType: Swift.String? = nil,
            location: Swift.String? = nil,
            locationType: ElasticInferenceClientTypes.LocationType? = nil
        )
        {
            self.acceleratorType = acceleratorType
            self.location = location
            self.locationType = locationType
        }
    }
}

/// Raised when a malformed input has been provided to the API.
public struct BadRequestException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "BadRequestException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Raised when an unexpected error occurred during request processing.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Raised when the requested resource cannot be found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DescribeAcceleratorOfferingsInput: Swift.Sendable {
    /// The list of accelerator types to describe.
    public var acceleratorTypes: [Swift.String]?
    /// The location type that you want to describe accelerator type offerings for. It can assume the following values: region: will return the accelerator type offering at the regional level. availability-zone: will return the accelerator type offering at the availability zone level. availability-zone-id: will return the accelerator type offering at the availability zone level returning the availability zone id.
    /// This member is required.
    public var locationType: ElasticInferenceClientTypes.LocationType?

    public init(
        acceleratorTypes: [Swift.String]? = nil,
        locationType: ElasticInferenceClientTypes.LocationType? = nil
    )
    {
        self.acceleratorTypes = acceleratorTypes
        self.locationType = locationType
    }
}

public struct DescribeAcceleratorOfferingsOutput: Swift.Sendable {
    /// The list of accelerator type offerings for a specific location.
    public var acceleratorTypeOfferings: [ElasticInferenceClientTypes.AcceleratorTypeOffering]?

    public init(
        acceleratorTypeOfferings: [ElasticInferenceClientTypes.AcceleratorTypeOffering]? = nil
    )
    {
        self.acceleratorTypeOfferings = acceleratorTypeOfferings
    }
}

extension ElasticInferenceClientTypes {

    /// A filter expression for the Elastic Inference Accelerator list.
    public struct Filter: Swift.Sendable {
        /// The filter name for the Elastic Inference Accelerator list. It can assume the following values: accelerator-type: the type of Elastic Inference Accelerator to filter for. instance-id: an EC2 instance id to filter for.
        public var name: Swift.String?
        /// The values for the filter of the Elastic Inference Accelerator list.
        public var values: [Swift.String]?

        public init(
            name: Swift.String? = nil,
            values: [Swift.String]? = nil
        )
        {
            self.name = name
            self.values = values
        }
    }
}

public struct DescribeAcceleratorsInput: Swift.Sendable {
    /// The IDs of the accelerators to describe.
    public var acceleratorIds: [Swift.String]?
    /// One or more filters. Filter names and values are case-sensitive. Valid filter names are: accelerator-types: can provide a list of accelerator type names to filter for. instance-id: can provide a list of EC2 instance ids to filter for.
    public var filters: [ElasticInferenceClientTypes.Filter]?
    /// The total number of items to return in the command's output. If the total number of items available is more than the value specified, a NextToken is provided in the command's output. To resume pagination, provide the NextToken value in the starting-token argument of a subsequent command. Do not use the NextToken response element directly outside of the AWS CLI.
    public var maxResults: Swift.Int?
    /// A token to specify where to start paginating. This is the NextToken from a previously truncated response.
    public var nextToken: Swift.String?

    public init(
        acceleratorIds: [Swift.String]? = nil,
        filters: [ElasticInferenceClientTypes.Filter]? = nil,
        maxResults: Swift.Int? = 0,
        nextToken: Swift.String? = nil
    )
    {
        self.acceleratorIds = acceleratorIds
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension ElasticInferenceClientTypes {

    /// The health details of an Elastic Inference Accelerator.
    public struct ElasticInferenceAcceleratorHealth: Swift.Sendable {
        /// The health status of the Elastic Inference Accelerator.
        public var status: Swift.String?

        public init(
            status: Swift.String? = nil
        )
        {
            self.status = status
        }
    }
}

extension ElasticInferenceClientTypes {

    /// The details of an Elastic Inference Accelerator.
    public struct ElasticInferenceAccelerator: Swift.Sendable {
        /// The health of the Elastic Inference Accelerator.
        public var acceleratorHealth: ElasticInferenceClientTypes.ElasticInferenceAcceleratorHealth?
        /// The ID of the Elastic Inference Accelerator.
        public var acceleratorId: Swift.String?
        /// The type of the Elastic Inference Accelerator.
        public var acceleratorType: Swift.String?
        /// The ARN of the resource that the Elastic Inference Accelerator is attached to.
        public var attachedResource: Swift.String?
        /// The availability zone where the Elastic Inference Accelerator is present.
        public var availabilityZone: Swift.String?

        public init(
            acceleratorHealth: ElasticInferenceClientTypes.ElasticInferenceAcceleratorHealth? = nil,
            acceleratorId: Swift.String? = nil,
            acceleratorType: Swift.String? = nil,
            attachedResource: Swift.String? = nil,
            availabilityZone: Swift.String? = nil
        )
        {
            self.acceleratorHealth = acceleratorHealth
            self.acceleratorId = acceleratorId
            self.acceleratorType = acceleratorType
            self.attachedResource = attachedResource
            self.availabilityZone = availabilityZone
        }
    }
}

public struct DescribeAcceleratorsOutput: Swift.Sendable {
    /// The details of the Elastic Inference Accelerators.
    public var acceleratorSet: [ElasticInferenceClientTypes.ElasticInferenceAccelerator]?
    /// A token to specify where to start paginating. This is the NextToken from a previously truncated response.
    public var nextToken: Swift.String?

    public init(
        acceleratorSet: [ElasticInferenceClientTypes.ElasticInferenceAccelerator]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.acceleratorSet = acceleratorSet
        self.nextToken = nextToken
    }
}

public struct DescribeAcceleratorTypesInput: Swift.Sendable {

    public init() { }
}

public struct DescribeAcceleratorTypesOutput: Swift.Sendable {
    /// The available accelerator types.
    public var acceleratorTypes: [ElasticInferenceClientTypes.AcceleratorType]?

    public init(
        acceleratorTypes: [ElasticInferenceClientTypes.AcceleratorType]? = nil
    )
    {
        self.acceleratorTypes = acceleratorTypes
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The ARN of the Elastic Inference Accelerator to list the tags for.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// The tags of the Elastic Inference Accelerator.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The ARN of the Elastic Inference Accelerator to tag.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tags to add to the Elastic Inference Accelerator.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The ARN of the Elastic Inference Accelerator to untag.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The list of tags to remove from the Elastic Inference Accelerator.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

extension DescribeAcceleratorOfferingsInput {

    static func urlPathProvider(_ value: DescribeAcceleratorOfferingsInput) -> Swift.String? {
        return "/describe-accelerator-offerings"
    }
}

extension DescribeAcceleratorsInput {

    static func urlPathProvider(_ value: DescribeAcceleratorsInput) -> Swift.String? {
        return "/describe-accelerators"
    }
}

extension DescribeAcceleratorTypesInput {

    static func urlPathProvider(_ value: DescribeAcceleratorTypesInput) -> Swift.String? {
        return "/describe-accelerator-types"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension DescribeAcceleratorOfferingsInput {

    static func write(value: DescribeAcceleratorOfferingsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["acceleratorTypes"].writeList(value.acceleratorTypes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["locationType"].write(value.locationType)
    }
}

extension DescribeAcceleratorsInput {

    static func write(value: DescribeAcceleratorsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["acceleratorIds"].writeList(value.acceleratorIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["filters"].writeList(value.filters, memberWritingClosure: ElasticInferenceClientTypes.Filter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension DescribeAcceleratorOfferingsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeAcceleratorOfferingsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeAcceleratorOfferingsOutput()
        value.acceleratorTypeOfferings = try reader["acceleratorTypeOfferings"].readListIfPresent(memberReadingClosure: ElasticInferenceClientTypes.AcceleratorTypeOffering.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeAcceleratorsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeAcceleratorsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeAcceleratorsOutput()
        value.acceleratorSet = try reader["acceleratorSet"].readListIfPresent(memberReadingClosure: ElasticInferenceClientTypes.ElasticInferenceAccelerator.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension DescribeAcceleratorTypesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeAcceleratorTypesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeAcceleratorTypesOutput()
        value.acceleratorTypes = try reader["acceleratorTypes"].readListIfPresent(memberReadingClosure: ElasticInferenceClientTypes.AcceleratorType.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

enum DescribeAcceleratorOfferingsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequestException": return try BadRequestException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeAcceleratorsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequestException": return try BadRequestException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeAcceleratorTypesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequestException": return try BadRequestException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequestException": return try BadRequestException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "BadRequestException": return try BadRequestException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension BadRequestException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> BadRequestException {
        let reader = baseError.errorBodyReader
        var value = BadRequestException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ElasticInferenceClientTypes.AcceleratorTypeOffering {

    static func read(from reader: SmithyJSON.Reader) throws -> ElasticInferenceClientTypes.AcceleratorTypeOffering {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticInferenceClientTypes.AcceleratorTypeOffering()
        value.acceleratorType = try reader["acceleratorType"].readIfPresent()
        value.locationType = try reader["locationType"].readIfPresent()
        value.location = try reader["location"].readIfPresent()
        return value
    }
}

extension ElasticInferenceClientTypes.ElasticInferenceAccelerator {

    static func read(from reader: SmithyJSON.Reader) throws -> ElasticInferenceClientTypes.ElasticInferenceAccelerator {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticInferenceClientTypes.ElasticInferenceAccelerator()
        value.acceleratorHealth = try reader["acceleratorHealth"].readIfPresent(with: ElasticInferenceClientTypes.ElasticInferenceAcceleratorHealth.read(from:))
        value.acceleratorType = try reader["acceleratorType"].readIfPresent()
        value.acceleratorId = try reader["acceleratorId"].readIfPresent()
        value.availabilityZone = try reader["availabilityZone"].readIfPresent()
        value.attachedResource = try reader["attachedResource"].readIfPresent()
        return value
    }
}

extension ElasticInferenceClientTypes.ElasticInferenceAcceleratorHealth {

    static func read(from reader: SmithyJSON.Reader) throws -> ElasticInferenceClientTypes.ElasticInferenceAcceleratorHealth {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticInferenceClientTypes.ElasticInferenceAcceleratorHealth()
        value.status = try reader["status"].readIfPresent()
        return value
    }
}

extension ElasticInferenceClientTypes.AcceleratorType {

    static func read(from reader: SmithyJSON.Reader) throws -> ElasticInferenceClientTypes.AcceleratorType {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticInferenceClientTypes.AcceleratorType()
        value.acceleratorTypeName = try reader["acceleratorTypeName"].readIfPresent()
        value.memoryInfo = try reader["memoryInfo"].readIfPresent(with: ElasticInferenceClientTypes.MemoryInfo.read(from:))
        value.throughputInfo = try reader["throughputInfo"].readListIfPresent(memberReadingClosure: ElasticInferenceClientTypes.KeyValuePair.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ElasticInferenceClientTypes.KeyValuePair {

    static func read(from reader: SmithyJSON.Reader) throws -> ElasticInferenceClientTypes.KeyValuePair {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticInferenceClientTypes.KeyValuePair()
        value.key = try reader["key"].readIfPresent()
        value.value = try reader["value"].readIfPresent() ?? 0
        return value
    }
}

extension ElasticInferenceClientTypes.MemoryInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> ElasticInferenceClientTypes.MemoryInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ElasticInferenceClientTypes.MemoryInfo()
        value.sizeInMiB = try reader["sizeInMiB"].readIfPresent() ?? 0
        return value
    }
}

extension ElasticInferenceClientTypes.Filter {

    static func write(value: ElasticInferenceClientTypes.Filter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["name"].write(value.name)
        try writer["values"].writeList(value.values, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

public enum ElasticInferenceClientTypes {}
