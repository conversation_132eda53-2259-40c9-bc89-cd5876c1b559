//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import Foundation
import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension ConnectClient {
    /// Paginate over `[GetCurrentMetricDataOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetCurrentMetricDataInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetCurrentMetricDataOutput`
    public func getCurrentMetricDataPaginated(input: GetCurrentMetricDataInput) -> ClientRuntime.PaginatorSequence<GetCurrentMetricDataInput, GetCurrentMetricDataOutput> {
        return ClientRuntime.PaginatorSequence<GetCurrentMetricDataInput, GetCurrentMetricDataOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getCurrentMetricData(input:))
    }
}

extension GetCurrentMetricDataInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetCurrentMetricDataInput {
        return GetCurrentMetricDataInput(
            currentMetrics: self.currentMetrics,
            filters: self.filters,
            groupings: self.groupings,
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            sortCriteria: self.sortCriteria
        )}
}
extension ConnectClient {
    /// Paginate over `[GetCurrentUserDataOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetCurrentUserDataInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetCurrentUserDataOutput`
    public func getCurrentUserDataPaginated(input: GetCurrentUserDataInput) -> ClientRuntime.PaginatorSequence<GetCurrentUserDataInput, GetCurrentUserDataOutput> {
        return ClientRuntime.PaginatorSequence<GetCurrentUserDataInput, GetCurrentUserDataOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getCurrentUserData(input:))
    }
}

extension GetCurrentUserDataInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetCurrentUserDataInput {
        return GetCurrentUserDataInput(
            filters: self.filters,
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}
extension ConnectClient {
    /// Paginate over `[GetMetricDataOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetMetricDataInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetMetricDataOutput`
    public func getMetricDataPaginated(input: GetMetricDataInput) -> ClientRuntime.PaginatorSequence<GetMetricDataInput, GetMetricDataOutput> {
        return ClientRuntime.PaginatorSequence<GetMetricDataInput, GetMetricDataOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getMetricData(input:))
    }
}

extension GetMetricDataInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetMetricDataInput {
        return GetMetricDataInput(
            endTime: self.endTime,
            filters: self.filters,
            groupings: self.groupings,
            historicalMetrics: self.historicalMetrics,
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            startTime: self.startTime
        )}
}
extension ConnectClient {
    /// Paginate over `[GetMetricDataV2Output]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetMetricDataV2Input]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetMetricDataV2Output`
    public func getMetricDataV2Paginated(input: GetMetricDataV2Input) -> ClientRuntime.PaginatorSequence<GetMetricDataV2Input, GetMetricDataV2Output> {
        return ClientRuntime.PaginatorSequence<GetMetricDataV2Input, GetMetricDataV2Output>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getMetricDataV2(input:))
    }
}

extension GetMetricDataV2Input: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetMetricDataV2Input {
        return GetMetricDataV2Input(
            endTime: self.endTime,
            filters: self.filters,
            groupings: self.groupings,
            interval: self.interval,
            maxResults: self.maxResults,
            metrics: self.metrics,
            nextToken: token,
            resourceArn: self.resourceArn,
            startTime: self.startTime
        )}
}
extension ConnectClient {
    /// Paginate over `[ListAgentStatusesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListAgentStatusesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListAgentStatusesOutput`
    public func listAgentStatusesPaginated(input: ListAgentStatusesInput) -> ClientRuntime.PaginatorSequence<ListAgentStatusesInput, ListAgentStatusesOutput> {
        return ClientRuntime.PaginatorSequence<ListAgentStatusesInput, ListAgentStatusesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listAgentStatuses(input:))
    }
}

extension ListAgentStatusesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListAgentStatusesInput {
        return ListAgentStatusesInput(
            agentStatusTypes: self.agentStatusTypes,
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListAgentStatusesInput, OperationStackOutput == ListAgentStatusesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listAgentStatusesPaginated`
    /// to access the nested member `[ConnectClientTypes.AgentStatusSummary]`
    /// - Returns: `[ConnectClientTypes.AgentStatusSummary]`
    public func agentStatusSummaryList() async throws -> [ConnectClientTypes.AgentStatusSummary] {
        return try await self.asyncCompactMap { item in item.agentStatusSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListApprovedOriginsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListApprovedOriginsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListApprovedOriginsOutput`
    public func listApprovedOriginsPaginated(input: ListApprovedOriginsInput) -> ClientRuntime.PaginatorSequence<ListApprovedOriginsInput, ListApprovedOriginsOutput> {
        return ClientRuntime.PaginatorSequence<ListApprovedOriginsInput, ListApprovedOriginsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listApprovedOrigins(input:))
    }
}

extension ListApprovedOriginsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListApprovedOriginsInput {
        return ListApprovedOriginsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListApprovedOriginsInput, OperationStackOutput == ListApprovedOriginsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listApprovedOriginsPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func origins() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.origins }
    }
}
extension ConnectClient {
    /// Paginate over `[ListAuthenticationProfilesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListAuthenticationProfilesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListAuthenticationProfilesOutput`
    public func listAuthenticationProfilesPaginated(input: ListAuthenticationProfilesInput) -> ClientRuntime.PaginatorSequence<ListAuthenticationProfilesInput, ListAuthenticationProfilesOutput> {
        return ClientRuntime.PaginatorSequence<ListAuthenticationProfilesInput, ListAuthenticationProfilesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listAuthenticationProfiles(input:))
    }
}

extension ListAuthenticationProfilesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListAuthenticationProfilesInput {
        return ListAuthenticationProfilesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListAuthenticationProfilesInput, OperationStackOutput == ListAuthenticationProfilesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listAuthenticationProfilesPaginated`
    /// to access the nested member `[ConnectClientTypes.AuthenticationProfileSummary]`
    /// - Returns: `[ConnectClientTypes.AuthenticationProfileSummary]`
    public func authenticationProfileSummaryList() async throws -> [ConnectClientTypes.AuthenticationProfileSummary] {
        return try await self.asyncCompactMap { item in item.authenticationProfileSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListBotsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListBotsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListBotsOutput`
    public func listBotsPaginated(input: ListBotsInput) -> ClientRuntime.PaginatorSequence<ListBotsInput, ListBotsOutput> {
        return ClientRuntime.PaginatorSequence<ListBotsInput, ListBotsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listBots(input:))
    }
}

extension ListBotsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListBotsInput {
        return ListBotsInput(
            instanceId: self.instanceId,
            lexVersion: self.lexVersion,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListBotsInput, OperationStackOutput == ListBotsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listBotsPaginated`
    /// to access the nested member `[ConnectClientTypes.LexBotConfig]`
    /// - Returns: `[ConnectClientTypes.LexBotConfig]`
    public func lexBots() async throws -> [ConnectClientTypes.LexBotConfig] {
        return try await self.asyncCompactMap { item in item.lexBots }
    }
}
extension ConnectClient {
    /// Paginate over `[ListContactEvaluationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListContactEvaluationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListContactEvaluationsOutput`
    public func listContactEvaluationsPaginated(input: ListContactEvaluationsInput) -> ClientRuntime.PaginatorSequence<ListContactEvaluationsInput, ListContactEvaluationsOutput> {
        return ClientRuntime.PaginatorSequence<ListContactEvaluationsInput, ListContactEvaluationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listContactEvaluations(input:))
    }
}

extension ListContactEvaluationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListContactEvaluationsInput {
        return ListContactEvaluationsInput(
            contactId: self.contactId,
            instanceId: self.instanceId,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListContactEvaluationsInput, OperationStackOutput == ListContactEvaluationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listContactEvaluationsPaginated`
    /// to access the nested member `[ConnectClientTypes.EvaluationSummary]`
    /// - Returns: `[ConnectClientTypes.EvaluationSummary]`
    public func evaluationSummaryList() async throws -> [ConnectClientTypes.EvaluationSummary] {
        return try await self.asyncCompactMap { item in item.evaluationSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListContactFlowModulesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListContactFlowModulesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListContactFlowModulesOutput`
    public func listContactFlowModulesPaginated(input: ListContactFlowModulesInput) -> ClientRuntime.PaginatorSequence<ListContactFlowModulesInput, ListContactFlowModulesOutput> {
        return ClientRuntime.PaginatorSequence<ListContactFlowModulesInput, ListContactFlowModulesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listContactFlowModules(input:))
    }
}

extension ListContactFlowModulesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListContactFlowModulesInput {
        return ListContactFlowModulesInput(
            contactFlowModuleState: self.contactFlowModuleState,
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListContactFlowModulesInput, OperationStackOutput == ListContactFlowModulesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listContactFlowModulesPaginated`
    /// to access the nested member `[ConnectClientTypes.ContactFlowModuleSummary]`
    /// - Returns: `[ConnectClientTypes.ContactFlowModuleSummary]`
    public func contactFlowModulesSummaryList() async throws -> [ConnectClientTypes.ContactFlowModuleSummary] {
        return try await self.asyncCompactMap { item in item.contactFlowModulesSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListContactFlowsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListContactFlowsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListContactFlowsOutput`
    public func listContactFlowsPaginated(input: ListContactFlowsInput) -> ClientRuntime.PaginatorSequence<ListContactFlowsInput, ListContactFlowsOutput> {
        return ClientRuntime.PaginatorSequence<ListContactFlowsInput, ListContactFlowsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listContactFlows(input:))
    }
}

extension ListContactFlowsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListContactFlowsInput {
        return ListContactFlowsInput(
            contactFlowTypes: self.contactFlowTypes,
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListContactFlowsInput, OperationStackOutput == ListContactFlowsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listContactFlowsPaginated`
    /// to access the nested member `[ConnectClientTypes.ContactFlowSummary]`
    /// - Returns: `[ConnectClientTypes.ContactFlowSummary]`
    public func contactFlowSummaryList() async throws -> [ConnectClientTypes.ContactFlowSummary] {
        return try await self.asyncCompactMap { item in item.contactFlowSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListContactFlowVersionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListContactFlowVersionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListContactFlowVersionsOutput`
    public func listContactFlowVersionsPaginated(input: ListContactFlowVersionsInput) -> ClientRuntime.PaginatorSequence<ListContactFlowVersionsInput, ListContactFlowVersionsOutput> {
        return ClientRuntime.PaginatorSequence<ListContactFlowVersionsInput, ListContactFlowVersionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listContactFlowVersions(input:))
    }
}

extension ListContactFlowVersionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListContactFlowVersionsInput {
        return ListContactFlowVersionsInput(
            contactFlowId: self.contactFlowId,
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListContactFlowVersionsInput, OperationStackOutput == ListContactFlowVersionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listContactFlowVersionsPaginated`
    /// to access the nested member `[ConnectClientTypes.ContactFlowVersionSummary]`
    /// - Returns: `[ConnectClientTypes.ContactFlowVersionSummary]`
    public func contactFlowVersionSummaryList() async throws -> [ConnectClientTypes.ContactFlowVersionSummary] {
        return try await self.asyncCompactMap { item in item.contactFlowVersionSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListContactReferencesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListContactReferencesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListContactReferencesOutput`
    public func listContactReferencesPaginated(input: ListContactReferencesInput) -> ClientRuntime.PaginatorSequence<ListContactReferencesInput, ListContactReferencesOutput> {
        return ClientRuntime.PaginatorSequence<ListContactReferencesInput, ListContactReferencesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listContactReferences(input:))
    }
}

extension ListContactReferencesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListContactReferencesInput {
        return ListContactReferencesInput(
            contactId: self.contactId,
            instanceId: self.instanceId,
            nextToken: token,
            referenceTypes: self.referenceTypes
        )}
}

extension PaginatorSequence where OperationStackInput == ListContactReferencesInput, OperationStackOutput == ListContactReferencesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listContactReferencesPaginated`
    /// to access the nested member `[ConnectClientTypes.ReferenceSummary]`
    /// - Returns: `[ConnectClientTypes.ReferenceSummary]`
    public func referenceSummaryList() async throws -> [ConnectClientTypes.ReferenceSummary] {
        return try await self.asyncCompactMap { item in item.referenceSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListDefaultVocabulariesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListDefaultVocabulariesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListDefaultVocabulariesOutput`
    public func listDefaultVocabulariesPaginated(input: ListDefaultVocabulariesInput) -> ClientRuntime.PaginatorSequence<ListDefaultVocabulariesInput, ListDefaultVocabulariesOutput> {
        return ClientRuntime.PaginatorSequence<ListDefaultVocabulariesInput, ListDefaultVocabulariesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listDefaultVocabularies(input:))
    }
}

extension ListDefaultVocabulariesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListDefaultVocabulariesInput {
        return ListDefaultVocabulariesInput(
            instanceId: self.instanceId,
            languageCode: self.languageCode,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListDefaultVocabulariesInput, OperationStackOutput == ListDefaultVocabulariesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listDefaultVocabulariesPaginated`
    /// to access the nested member `[ConnectClientTypes.DefaultVocabulary]`
    /// - Returns: `[ConnectClientTypes.DefaultVocabulary]`
    public func defaultVocabularyList() async throws -> [ConnectClientTypes.DefaultVocabulary] {
        return try await self.asyncCompactMap { item in item.defaultVocabularyList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListEvaluationFormsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListEvaluationFormsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListEvaluationFormsOutput`
    public func listEvaluationFormsPaginated(input: ListEvaluationFormsInput) -> ClientRuntime.PaginatorSequence<ListEvaluationFormsInput, ListEvaluationFormsOutput> {
        return ClientRuntime.PaginatorSequence<ListEvaluationFormsInput, ListEvaluationFormsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listEvaluationForms(input:))
    }
}

extension ListEvaluationFormsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListEvaluationFormsInput {
        return ListEvaluationFormsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListEvaluationFormsInput, OperationStackOutput == ListEvaluationFormsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listEvaluationFormsPaginated`
    /// to access the nested member `[ConnectClientTypes.EvaluationFormSummary]`
    /// - Returns: `[ConnectClientTypes.EvaluationFormSummary]`
    public func evaluationFormSummaryList() async throws -> [ConnectClientTypes.EvaluationFormSummary] {
        return try await self.asyncCompactMap { item in item.evaluationFormSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListEvaluationFormVersionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListEvaluationFormVersionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListEvaluationFormVersionsOutput`
    public func listEvaluationFormVersionsPaginated(input: ListEvaluationFormVersionsInput) -> ClientRuntime.PaginatorSequence<ListEvaluationFormVersionsInput, ListEvaluationFormVersionsOutput> {
        return ClientRuntime.PaginatorSequence<ListEvaluationFormVersionsInput, ListEvaluationFormVersionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listEvaluationFormVersions(input:))
    }
}

extension ListEvaluationFormVersionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListEvaluationFormVersionsInput {
        return ListEvaluationFormVersionsInput(
            evaluationFormId: self.evaluationFormId,
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListEvaluationFormVersionsInput, OperationStackOutput == ListEvaluationFormVersionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listEvaluationFormVersionsPaginated`
    /// to access the nested member `[ConnectClientTypes.EvaluationFormVersionSummary]`
    /// - Returns: `[ConnectClientTypes.EvaluationFormVersionSummary]`
    public func evaluationFormVersionSummaryList() async throws -> [ConnectClientTypes.EvaluationFormVersionSummary] {
        return try await self.asyncCompactMap { item in item.evaluationFormVersionSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListFlowAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListFlowAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListFlowAssociationsOutput`
    public func listFlowAssociationsPaginated(input: ListFlowAssociationsInput) -> ClientRuntime.PaginatorSequence<ListFlowAssociationsInput, ListFlowAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<ListFlowAssociationsInput, ListFlowAssociationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listFlowAssociations(input:))
    }
}

extension ListFlowAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListFlowAssociationsInput {
        return ListFlowAssociationsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            resourceType: self.resourceType
        )}
}

extension PaginatorSequence where OperationStackInput == ListFlowAssociationsInput, OperationStackOutput == ListFlowAssociationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listFlowAssociationsPaginated`
    /// to access the nested member `[ConnectClientTypes.FlowAssociationSummary]`
    /// - Returns: `[ConnectClientTypes.FlowAssociationSummary]`
    public func flowAssociationSummaryList() async throws -> [ConnectClientTypes.FlowAssociationSummary] {
        return try await self.asyncCompactMap { item in item.flowAssociationSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListHoursOfOperationOverridesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListHoursOfOperationOverridesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListHoursOfOperationOverridesOutput`
    public func listHoursOfOperationOverridesPaginated(input: ListHoursOfOperationOverridesInput) -> ClientRuntime.PaginatorSequence<ListHoursOfOperationOverridesInput, ListHoursOfOperationOverridesOutput> {
        return ClientRuntime.PaginatorSequence<ListHoursOfOperationOverridesInput, ListHoursOfOperationOverridesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listHoursOfOperationOverrides(input:))
    }
}

extension ListHoursOfOperationOverridesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListHoursOfOperationOverridesInput {
        return ListHoursOfOperationOverridesInput(
            hoursOfOperationId: self.hoursOfOperationId,
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListHoursOfOperationOverridesInput, OperationStackOutput == ListHoursOfOperationOverridesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listHoursOfOperationOverridesPaginated`
    /// to access the nested member `[ConnectClientTypes.HoursOfOperationOverride]`
    /// - Returns: `[ConnectClientTypes.HoursOfOperationOverride]`
    public func hoursOfOperationOverrideList() async throws -> [ConnectClientTypes.HoursOfOperationOverride] {
        return try await self.asyncCompactMap { item in item.hoursOfOperationOverrideList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListHoursOfOperationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListHoursOfOperationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListHoursOfOperationsOutput`
    public func listHoursOfOperationsPaginated(input: ListHoursOfOperationsInput) -> ClientRuntime.PaginatorSequence<ListHoursOfOperationsInput, ListHoursOfOperationsOutput> {
        return ClientRuntime.PaginatorSequence<ListHoursOfOperationsInput, ListHoursOfOperationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listHoursOfOperations(input:))
    }
}

extension ListHoursOfOperationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListHoursOfOperationsInput {
        return ListHoursOfOperationsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListHoursOfOperationsInput, OperationStackOutput == ListHoursOfOperationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listHoursOfOperationsPaginated`
    /// to access the nested member `[ConnectClientTypes.HoursOfOperationSummary]`
    /// - Returns: `[ConnectClientTypes.HoursOfOperationSummary]`
    public func hoursOfOperationSummaryList() async throws -> [ConnectClientTypes.HoursOfOperationSummary] {
        return try await self.asyncCompactMap { item in item.hoursOfOperationSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListInstanceAttributesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListInstanceAttributesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListInstanceAttributesOutput`
    public func listInstanceAttributesPaginated(input: ListInstanceAttributesInput) -> ClientRuntime.PaginatorSequence<ListInstanceAttributesInput, ListInstanceAttributesOutput> {
        return ClientRuntime.PaginatorSequence<ListInstanceAttributesInput, ListInstanceAttributesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listInstanceAttributes(input:))
    }
}

extension ListInstanceAttributesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListInstanceAttributesInput {
        return ListInstanceAttributesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListInstanceAttributesInput, OperationStackOutput == ListInstanceAttributesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listInstanceAttributesPaginated`
    /// to access the nested member `[ConnectClientTypes.Attribute]`
    /// - Returns: `[ConnectClientTypes.Attribute]`
    public func attributes() async throws -> [ConnectClientTypes.Attribute] {
        return try await self.asyncCompactMap { item in item.attributes }
    }
}
extension ConnectClient {
    /// Paginate over `[ListInstancesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListInstancesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListInstancesOutput`
    public func listInstancesPaginated(input: ListInstancesInput) -> ClientRuntime.PaginatorSequence<ListInstancesInput, ListInstancesOutput> {
        return ClientRuntime.PaginatorSequence<ListInstancesInput, ListInstancesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listInstances(input:))
    }
}

extension ListInstancesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListInstancesInput {
        return ListInstancesInput(
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListInstancesInput, OperationStackOutput == ListInstancesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listInstancesPaginated`
    /// to access the nested member `[ConnectClientTypes.InstanceSummary]`
    /// - Returns: `[ConnectClientTypes.InstanceSummary]`
    public func instanceSummaryList() async throws -> [ConnectClientTypes.InstanceSummary] {
        return try await self.asyncCompactMap { item in item.instanceSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListInstanceStorageConfigsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListInstanceStorageConfigsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListInstanceStorageConfigsOutput`
    public func listInstanceStorageConfigsPaginated(input: ListInstanceStorageConfigsInput) -> ClientRuntime.PaginatorSequence<ListInstanceStorageConfigsInput, ListInstanceStorageConfigsOutput> {
        return ClientRuntime.PaginatorSequence<ListInstanceStorageConfigsInput, ListInstanceStorageConfigsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listInstanceStorageConfigs(input:))
    }
}

extension ListInstanceStorageConfigsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListInstanceStorageConfigsInput {
        return ListInstanceStorageConfigsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            resourceType: self.resourceType
        )}
}

extension PaginatorSequence where OperationStackInput == ListInstanceStorageConfigsInput, OperationStackOutput == ListInstanceStorageConfigsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listInstanceStorageConfigsPaginated`
    /// to access the nested member `[ConnectClientTypes.InstanceStorageConfig]`
    /// - Returns: `[ConnectClientTypes.InstanceStorageConfig]`
    public func storageConfigs() async throws -> [ConnectClientTypes.InstanceStorageConfig] {
        return try await self.asyncCompactMap { item in item.storageConfigs }
    }
}
extension ConnectClient {
    /// Paginate over `[ListIntegrationAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListIntegrationAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListIntegrationAssociationsOutput`
    public func listIntegrationAssociationsPaginated(input: ListIntegrationAssociationsInput) -> ClientRuntime.PaginatorSequence<ListIntegrationAssociationsInput, ListIntegrationAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<ListIntegrationAssociationsInput, ListIntegrationAssociationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listIntegrationAssociations(input:))
    }
}

extension ListIntegrationAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListIntegrationAssociationsInput {
        return ListIntegrationAssociationsInput(
            instanceId: self.instanceId,
            integrationArn: self.integrationArn,
            integrationType: self.integrationType,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListIntegrationAssociationsInput, OperationStackOutput == ListIntegrationAssociationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listIntegrationAssociationsPaginated`
    /// to access the nested member `[ConnectClientTypes.IntegrationAssociationSummary]`
    /// - Returns: `[ConnectClientTypes.IntegrationAssociationSummary]`
    public func integrationAssociationSummaryList() async throws -> [ConnectClientTypes.IntegrationAssociationSummary] {
        return try await self.asyncCompactMap { item in item.integrationAssociationSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListLambdaFunctionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListLambdaFunctionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListLambdaFunctionsOutput`
    public func listLambdaFunctionsPaginated(input: ListLambdaFunctionsInput) -> ClientRuntime.PaginatorSequence<ListLambdaFunctionsInput, ListLambdaFunctionsOutput> {
        return ClientRuntime.PaginatorSequence<ListLambdaFunctionsInput, ListLambdaFunctionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listLambdaFunctions(input:))
    }
}

extension ListLambdaFunctionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListLambdaFunctionsInput {
        return ListLambdaFunctionsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListLambdaFunctionsInput, OperationStackOutput == ListLambdaFunctionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listLambdaFunctionsPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func lambdaFunctions() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.lambdaFunctions }
    }
}
extension ConnectClient {
    /// Paginate over `[ListLexBotsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListLexBotsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListLexBotsOutput`
    public func listLexBotsPaginated(input: ListLexBotsInput) -> ClientRuntime.PaginatorSequence<ListLexBotsInput, ListLexBotsOutput> {
        return ClientRuntime.PaginatorSequence<ListLexBotsInput, ListLexBotsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listLexBots(input:))
    }
}

extension ListLexBotsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListLexBotsInput {
        return ListLexBotsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListLexBotsInput, OperationStackOutput == ListLexBotsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listLexBotsPaginated`
    /// to access the nested member `[ConnectClientTypes.LexBot]`
    /// - Returns: `[ConnectClientTypes.LexBot]`
    public func lexBots() async throws -> [ConnectClientTypes.LexBot] {
        return try await self.asyncCompactMap { item in item.lexBots }
    }
}
extension ConnectClient {
    /// Paginate over `[ListPhoneNumbersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListPhoneNumbersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListPhoneNumbersOutput`
    public func listPhoneNumbersPaginated(input: ListPhoneNumbersInput) -> ClientRuntime.PaginatorSequence<ListPhoneNumbersInput, ListPhoneNumbersOutput> {
        return ClientRuntime.PaginatorSequence<ListPhoneNumbersInput, ListPhoneNumbersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listPhoneNumbers(input:))
    }
}

extension ListPhoneNumbersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListPhoneNumbersInput {
        return ListPhoneNumbersInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            phoneNumberCountryCodes: self.phoneNumberCountryCodes,
            phoneNumberTypes: self.phoneNumberTypes
        )}
}

extension PaginatorSequence where OperationStackInput == ListPhoneNumbersInput, OperationStackOutput == ListPhoneNumbersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listPhoneNumbersPaginated`
    /// to access the nested member `[ConnectClientTypes.PhoneNumberSummary]`
    /// - Returns: `[ConnectClientTypes.PhoneNumberSummary]`
    public func phoneNumberSummaryList() async throws -> [ConnectClientTypes.PhoneNumberSummary] {
        return try await self.asyncCompactMap { item in item.phoneNumberSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListPhoneNumbersV2Output]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListPhoneNumbersV2Input]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListPhoneNumbersV2Output`
    public func listPhoneNumbersV2Paginated(input: ListPhoneNumbersV2Input) -> ClientRuntime.PaginatorSequence<ListPhoneNumbersV2Input, ListPhoneNumbersV2Output> {
        return ClientRuntime.PaginatorSequence<ListPhoneNumbersV2Input, ListPhoneNumbersV2Output>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listPhoneNumbersV2(input:))
    }
}

extension ListPhoneNumbersV2Input: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListPhoneNumbersV2Input {
        return ListPhoneNumbersV2Input(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            phoneNumberCountryCodes: self.phoneNumberCountryCodes,
            phoneNumberPrefix: self.phoneNumberPrefix,
            phoneNumberTypes: self.phoneNumberTypes,
            targetArn: self.targetArn
        )}
}

extension PaginatorSequence where OperationStackInput == ListPhoneNumbersV2Input, OperationStackOutput == ListPhoneNumbersV2Output {
    /// This paginator transforms the `AsyncSequence` returned by `listPhoneNumbersV2Paginated`
    /// to access the nested member `[ConnectClientTypes.ListPhoneNumbersSummary]`
    /// - Returns: `[ConnectClientTypes.ListPhoneNumbersSummary]`
    public func listPhoneNumbersSummaryList() async throws -> [ConnectClientTypes.ListPhoneNumbersSummary] {
        return try await self.asyncCompactMap { item in item.listPhoneNumbersSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListPredefinedAttributesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListPredefinedAttributesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListPredefinedAttributesOutput`
    public func listPredefinedAttributesPaginated(input: ListPredefinedAttributesInput) -> ClientRuntime.PaginatorSequence<ListPredefinedAttributesInput, ListPredefinedAttributesOutput> {
        return ClientRuntime.PaginatorSequence<ListPredefinedAttributesInput, ListPredefinedAttributesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listPredefinedAttributes(input:))
    }
}

extension ListPredefinedAttributesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListPredefinedAttributesInput {
        return ListPredefinedAttributesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListPredefinedAttributesInput, OperationStackOutput == ListPredefinedAttributesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listPredefinedAttributesPaginated`
    /// to access the nested member `[ConnectClientTypes.PredefinedAttributeSummary]`
    /// - Returns: `[ConnectClientTypes.PredefinedAttributeSummary]`
    public func predefinedAttributeSummaryList() async throws -> [ConnectClientTypes.PredefinedAttributeSummary] {
        return try await self.asyncCompactMap { item in item.predefinedAttributeSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListPromptsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListPromptsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListPromptsOutput`
    public func listPromptsPaginated(input: ListPromptsInput) -> ClientRuntime.PaginatorSequence<ListPromptsInput, ListPromptsOutput> {
        return ClientRuntime.PaginatorSequence<ListPromptsInput, ListPromptsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listPrompts(input:))
    }
}

extension ListPromptsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListPromptsInput {
        return ListPromptsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListPromptsInput, OperationStackOutput == ListPromptsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listPromptsPaginated`
    /// to access the nested member `[ConnectClientTypes.PromptSummary]`
    /// - Returns: `[ConnectClientTypes.PromptSummary]`
    public func promptSummaryList() async throws -> [ConnectClientTypes.PromptSummary] {
        return try await self.asyncCompactMap { item in item.promptSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListQueueQuickConnectsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListQueueQuickConnectsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListQueueQuickConnectsOutput`
    public func listQueueQuickConnectsPaginated(input: ListQueueQuickConnectsInput) -> ClientRuntime.PaginatorSequence<ListQueueQuickConnectsInput, ListQueueQuickConnectsOutput> {
        return ClientRuntime.PaginatorSequence<ListQueueQuickConnectsInput, ListQueueQuickConnectsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listQueueQuickConnects(input:))
    }
}

extension ListQueueQuickConnectsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListQueueQuickConnectsInput {
        return ListQueueQuickConnectsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            queueId: self.queueId
        )}
}

extension PaginatorSequence where OperationStackInput == ListQueueQuickConnectsInput, OperationStackOutput == ListQueueQuickConnectsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listQueueQuickConnectsPaginated`
    /// to access the nested member `[ConnectClientTypes.QuickConnectSummary]`
    /// - Returns: `[ConnectClientTypes.QuickConnectSummary]`
    public func quickConnectSummaryList() async throws -> [ConnectClientTypes.QuickConnectSummary] {
        return try await self.asyncCompactMap { item in item.quickConnectSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListQueuesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListQueuesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListQueuesOutput`
    public func listQueuesPaginated(input: ListQueuesInput) -> ClientRuntime.PaginatorSequence<ListQueuesInput, ListQueuesOutput> {
        return ClientRuntime.PaginatorSequence<ListQueuesInput, ListQueuesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listQueues(input:))
    }
}

extension ListQueuesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListQueuesInput {
        return ListQueuesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            queueTypes: self.queueTypes
        )}
}

extension PaginatorSequence where OperationStackInput == ListQueuesInput, OperationStackOutput == ListQueuesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listQueuesPaginated`
    /// to access the nested member `[ConnectClientTypes.QueueSummary]`
    /// - Returns: `[ConnectClientTypes.QueueSummary]`
    public func queueSummaryList() async throws -> [ConnectClientTypes.QueueSummary] {
        return try await self.asyncCompactMap { item in item.queueSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListQuickConnectsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListQuickConnectsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListQuickConnectsOutput`
    public func listQuickConnectsPaginated(input: ListQuickConnectsInput) -> ClientRuntime.PaginatorSequence<ListQuickConnectsInput, ListQuickConnectsOutput> {
        return ClientRuntime.PaginatorSequence<ListQuickConnectsInput, ListQuickConnectsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listQuickConnects(input:))
    }
}

extension ListQuickConnectsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListQuickConnectsInput {
        return ListQuickConnectsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            quickConnectTypes: self.quickConnectTypes
        )}
}

extension PaginatorSequence where OperationStackInput == ListQuickConnectsInput, OperationStackOutput == ListQuickConnectsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listQuickConnectsPaginated`
    /// to access the nested member `[ConnectClientTypes.QuickConnectSummary]`
    /// - Returns: `[ConnectClientTypes.QuickConnectSummary]`
    public func quickConnectSummaryList() async throws -> [ConnectClientTypes.QuickConnectSummary] {
        return try await self.asyncCompactMap { item in item.quickConnectSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListRealtimeContactAnalysisSegmentsV2Output]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListRealtimeContactAnalysisSegmentsV2Input]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListRealtimeContactAnalysisSegmentsV2Output`
    public func listRealtimeContactAnalysisSegmentsV2Paginated(input: ListRealtimeContactAnalysisSegmentsV2Input) -> ClientRuntime.PaginatorSequence<ListRealtimeContactAnalysisSegmentsV2Input, ListRealtimeContactAnalysisSegmentsV2Output> {
        return ClientRuntime.PaginatorSequence<ListRealtimeContactAnalysisSegmentsV2Input, ListRealtimeContactAnalysisSegmentsV2Output>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listRealtimeContactAnalysisSegmentsV2(input:))
    }
}

extension ListRealtimeContactAnalysisSegmentsV2Input: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListRealtimeContactAnalysisSegmentsV2Input {
        return ListRealtimeContactAnalysisSegmentsV2Input(
            contactId: self.contactId,
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            outputType: self.outputType,
            segmentTypes: self.segmentTypes
        )}
}
extension ConnectClient {
    /// Paginate over `[ListRoutingProfileQueuesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListRoutingProfileQueuesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListRoutingProfileQueuesOutput`
    public func listRoutingProfileQueuesPaginated(input: ListRoutingProfileQueuesInput) -> ClientRuntime.PaginatorSequence<ListRoutingProfileQueuesInput, ListRoutingProfileQueuesOutput> {
        return ClientRuntime.PaginatorSequence<ListRoutingProfileQueuesInput, ListRoutingProfileQueuesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listRoutingProfileQueues(input:))
    }
}

extension ListRoutingProfileQueuesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListRoutingProfileQueuesInput {
        return ListRoutingProfileQueuesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            routingProfileId: self.routingProfileId
        )}
}

extension PaginatorSequence where OperationStackInput == ListRoutingProfileQueuesInput, OperationStackOutput == ListRoutingProfileQueuesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listRoutingProfileQueuesPaginated`
    /// to access the nested member `[ConnectClientTypes.RoutingProfileQueueConfigSummary]`
    /// - Returns: `[ConnectClientTypes.RoutingProfileQueueConfigSummary]`
    public func routingProfileQueueConfigSummaryList() async throws -> [ConnectClientTypes.RoutingProfileQueueConfigSummary] {
        return try await self.asyncCompactMap { item in item.routingProfileQueueConfigSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListRoutingProfilesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListRoutingProfilesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListRoutingProfilesOutput`
    public func listRoutingProfilesPaginated(input: ListRoutingProfilesInput) -> ClientRuntime.PaginatorSequence<ListRoutingProfilesInput, ListRoutingProfilesOutput> {
        return ClientRuntime.PaginatorSequence<ListRoutingProfilesInput, ListRoutingProfilesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listRoutingProfiles(input:))
    }
}

extension ListRoutingProfilesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListRoutingProfilesInput {
        return ListRoutingProfilesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListRoutingProfilesInput, OperationStackOutput == ListRoutingProfilesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listRoutingProfilesPaginated`
    /// to access the nested member `[ConnectClientTypes.RoutingProfileSummary]`
    /// - Returns: `[ConnectClientTypes.RoutingProfileSummary]`
    public func routingProfileSummaryList() async throws -> [ConnectClientTypes.RoutingProfileSummary] {
        return try await self.asyncCompactMap { item in item.routingProfileSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListRulesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListRulesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListRulesOutput`
    public func listRulesPaginated(input: ListRulesInput) -> ClientRuntime.PaginatorSequence<ListRulesInput, ListRulesOutput> {
        return ClientRuntime.PaginatorSequence<ListRulesInput, ListRulesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listRules(input:))
    }
}

extension ListRulesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListRulesInput {
        return ListRulesInput(
            eventSourceName: self.eventSourceName,
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            publishStatus: self.publishStatus
        )}
}

extension PaginatorSequence where OperationStackInput == ListRulesInput, OperationStackOutput == ListRulesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listRulesPaginated`
    /// to access the nested member `[ConnectClientTypes.RuleSummary]`
    /// - Returns: `[ConnectClientTypes.RuleSummary]`
    public func ruleSummaryList() async throws -> [ConnectClientTypes.RuleSummary] {
        return try await self.asyncCompactMap { item in item.ruleSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListSecurityKeysOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListSecurityKeysInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListSecurityKeysOutput`
    public func listSecurityKeysPaginated(input: ListSecurityKeysInput) -> ClientRuntime.PaginatorSequence<ListSecurityKeysInput, ListSecurityKeysOutput> {
        return ClientRuntime.PaginatorSequence<ListSecurityKeysInput, ListSecurityKeysOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listSecurityKeys(input:))
    }
}

extension ListSecurityKeysInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListSecurityKeysInput {
        return ListSecurityKeysInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListSecurityKeysInput, OperationStackOutput == ListSecurityKeysOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listSecurityKeysPaginated`
    /// to access the nested member `[ConnectClientTypes.SecurityKey]`
    /// - Returns: `[ConnectClientTypes.SecurityKey]`
    public func securityKeys() async throws -> [ConnectClientTypes.SecurityKey] {
        return try await self.asyncCompactMap { item in item.securityKeys }
    }
}
extension ConnectClient {
    /// Paginate over `[ListSecurityProfileApplicationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListSecurityProfileApplicationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListSecurityProfileApplicationsOutput`
    public func listSecurityProfileApplicationsPaginated(input: ListSecurityProfileApplicationsInput) -> ClientRuntime.PaginatorSequence<ListSecurityProfileApplicationsInput, ListSecurityProfileApplicationsOutput> {
        return ClientRuntime.PaginatorSequence<ListSecurityProfileApplicationsInput, ListSecurityProfileApplicationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listSecurityProfileApplications(input:))
    }
}

extension ListSecurityProfileApplicationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListSecurityProfileApplicationsInput {
        return ListSecurityProfileApplicationsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            securityProfileId: self.securityProfileId
        )}
}

extension PaginatorSequence where OperationStackInput == ListSecurityProfileApplicationsInput, OperationStackOutput == ListSecurityProfileApplicationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listSecurityProfileApplicationsPaginated`
    /// to access the nested member `[ConnectClientTypes.Application]`
    /// - Returns: `[ConnectClientTypes.Application]`
    public func applications() async throws -> [ConnectClientTypes.Application] {
        return try await self.asyncCompactMap { item in item.applications }
    }
}
extension ConnectClient {
    /// Paginate over `[ListSecurityProfilePermissionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListSecurityProfilePermissionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListSecurityProfilePermissionsOutput`
    public func listSecurityProfilePermissionsPaginated(input: ListSecurityProfilePermissionsInput) -> ClientRuntime.PaginatorSequence<ListSecurityProfilePermissionsInput, ListSecurityProfilePermissionsOutput> {
        return ClientRuntime.PaginatorSequence<ListSecurityProfilePermissionsInput, ListSecurityProfilePermissionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listSecurityProfilePermissions(input:))
    }
}

extension ListSecurityProfilePermissionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListSecurityProfilePermissionsInput {
        return ListSecurityProfilePermissionsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            securityProfileId: self.securityProfileId
        )}
}

extension PaginatorSequence where OperationStackInput == ListSecurityProfilePermissionsInput, OperationStackOutput == ListSecurityProfilePermissionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listSecurityProfilePermissionsPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func permissions() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.permissions }
    }
}
extension ConnectClient {
    /// Paginate over `[ListSecurityProfilesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListSecurityProfilesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListSecurityProfilesOutput`
    public func listSecurityProfilesPaginated(input: ListSecurityProfilesInput) -> ClientRuntime.PaginatorSequence<ListSecurityProfilesInput, ListSecurityProfilesOutput> {
        return ClientRuntime.PaginatorSequence<ListSecurityProfilesInput, ListSecurityProfilesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listSecurityProfiles(input:))
    }
}

extension ListSecurityProfilesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListSecurityProfilesInput {
        return ListSecurityProfilesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListSecurityProfilesInput, OperationStackOutput == ListSecurityProfilesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listSecurityProfilesPaginated`
    /// to access the nested member `[ConnectClientTypes.SecurityProfileSummary]`
    /// - Returns: `[ConnectClientTypes.SecurityProfileSummary]`
    public func securityProfileSummaryList() async throws -> [ConnectClientTypes.SecurityProfileSummary] {
        return try await self.asyncCompactMap { item in item.securityProfileSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListTaskTemplatesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListTaskTemplatesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListTaskTemplatesOutput`
    public func listTaskTemplatesPaginated(input: ListTaskTemplatesInput) -> ClientRuntime.PaginatorSequence<ListTaskTemplatesInput, ListTaskTemplatesOutput> {
        return ClientRuntime.PaginatorSequence<ListTaskTemplatesInput, ListTaskTemplatesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listTaskTemplates(input:))
    }
}

extension ListTaskTemplatesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListTaskTemplatesInput {
        return ListTaskTemplatesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            name: self.name,
            nextToken: token,
            status: self.status
        )}
}

extension PaginatorSequence where OperationStackInput == ListTaskTemplatesInput, OperationStackOutput == ListTaskTemplatesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listTaskTemplatesPaginated`
    /// to access the nested member `[ConnectClientTypes.TaskTemplateMetadata]`
    /// - Returns: `[ConnectClientTypes.TaskTemplateMetadata]`
    public func taskTemplates() async throws -> [ConnectClientTypes.TaskTemplateMetadata] {
        return try await self.asyncCompactMap { item in item.taskTemplates }
    }
}
extension ConnectClient {
    /// Paginate over `[ListTrafficDistributionGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListTrafficDistributionGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListTrafficDistributionGroupsOutput`
    public func listTrafficDistributionGroupsPaginated(input: ListTrafficDistributionGroupsInput) -> ClientRuntime.PaginatorSequence<ListTrafficDistributionGroupsInput, ListTrafficDistributionGroupsOutput> {
        return ClientRuntime.PaginatorSequence<ListTrafficDistributionGroupsInput, ListTrafficDistributionGroupsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listTrafficDistributionGroups(input:))
    }
}

extension ListTrafficDistributionGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListTrafficDistributionGroupsInput {
        return ListTrafficDistributionGroupsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListTrafficDistributionGroupsInput, OperationStackOutput == ListTrafficDistributionGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listTrafficDistributionGroupsPaginated`
    /// to access the nested member `[ConnectClientTypes.TrafficDistributionGroupSummary]`
    /// - Returns: `[ConnectClientTypes.TrafficDistributionGroupSummary]`
    public func trafficDistributionGroupSummaryList() async throws -> [ConnectClientTypes.TrafficDistributionGroupSummary] {
        return try await self.asyncCompactMap { item in item.trafficDistributionGroupSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListTrafficDistributionGroupUsersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListTrafficDistributionGroupUsersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListTrafficDistributionGroupUsersOutput`
    public func listTrafficDistributionGroupUsersPaginated(input: ListTrafficDistributionGroupUsersInput) -> ClientRuntime.PaginatorSequence<ListTrafficDistributionGroupUsersInput, ListTrafficDistributionGroupUsersOutput> {
        return ClientRuntime.PaginatorSequence<ListTrafficDistributionGroupUsersInput, ListTrafficDistributionGroupUsersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listTrafficDistributionGroupUsers(input:))
    }
}

extension ListTrafficDistributionGroupUsersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListTrafficDistributionGroupUsersInput {
        return ListTrafficDistributionGroupUsersInput(
            maxResults: self.maxResults,
            nextToken: token,
            trafficDistributionGroupId: self.trafficDistributionGroupId
        )}
}

extension PaginatorSequence where OperationStackInput == ListTrafficDistributionGroupUsersInput, OperationStackOutput == ListTrafficDistributionGroupUsersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listTrafficDistributionGroupUsersPaginated`
    /// to access the nested member `[ConnectClientTypes.TrafficDistributionGroupUserSummary]`
    /// - Returns: `[ConnectClientTypes.TrafficDistributionGroupUserSummary]`
    public func trafficDistributionGroupUserSummaryList() async throws -> [ConnectClientTypes.TrafficDistributionGroupUserSummary] {
        return try await self.asyncCompactMap { item in item.trafficDistributionGroupUserSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListUseCasesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListUseCasesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListUseCasesOutput`
    public func listUseCasesPaginated(input: ListUseCasesInput) -> ClientRuntime.PaginatorSequence<ListUseCasesInput, ListUseCasesOutput> {
        return ClientRuntime.PaginatorSequence<ListUseCasesInput, ListUseCasesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listUseCases(input:))
    }
}

extension ListUseCasesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListUseCasesInput {
        return ListUseCasesInput(
            instanceId: self.instanceId,
            integrationAssociationId: self.integrationAssociationId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListUseCasesInput, OperationStackOutput == ListUseCasesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listUseCasesPaginated`
    /// to access the nested member `[ConnectClientTypes.UseCase]`
    /// - Returns: `[ConnectClientTypes.UseCase]`
    public func useCaseSummaryList() async throws -> [ConnectClientTypes.UseCase] {
        return try await self.asyncCompactMap { item in item.useCaseSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListUserHierarchyGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListUserHierarchyGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListUserHierarchyGroupsOutput`
    public func listUserHierarchyGroupsPaginated(input: ListUserHierarchyGroupsInput) -> ClientRuntime.PaginatorSequence<ListUserHierarchyGroupsInput, ListUserHierarchyGroupsOutput> {
        return ClientRuntime.PaginatorSequence<ListUserHierarchyGroupsInput, ListUserHierarchyGroupsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listUserHierarchyGroups(input:))
    }
}

extension ListUserHierarchyGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListUserHierarchyGroupsInput {
        return ListUserHierarchyGroupsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListUserHierarchyGroupsInput, OperationStackOutput == ListUserHierarchyGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listUserHierarchyGroupsPaginated`
    /// to access the nested member `[ConnectClientTypes.HierarchyGroupSummary]`
    /// - Returns: `[ConnectClientTypes.HierarchyGroupSummary]`
    public func userHierarchyGroupSummaryList() async throws -> [ConnectClientTypes.HierarchyGroupSummary] {
        return try await self.asyncCompactMap { item in item.userHierarchyGroupSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListUserProficienciesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListUserProficienciesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListUserProficienciesOutput`
    public func listUserProficienciesPaginated(input: ListUserProficienciesInput) -> ClientRuntime.PaginatorSequence<ListUserProficienciesInput, ListUserProficienciesOutput> {
        return ClientRuntime.PaginatorSequence<ListUserProficienciesInput, ListUserProficienciesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listUserProficiencies(input:))
    }
}

extension ListUserProficienciesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListUserProficienciesInput {
        return ListUserProficienciesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            userId: self.userId
        )}
}

extension PaginatorSequence where OperationStackInput == ListUserProficienciesInput, OperationStackOutput == ListUserProficienciesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listUserProficienciesPaginated`
    /// to access the nested member `[ConnectClientTypes.UserProficiency]`
    /// - Returns: `[ConnectClientTypes.UserProficiency]`
    public func userProficiencyList() async throws -> [ConnectClientTypes.UserProficiency] {
        return try await self.asyncCompactMap { item in item.userProficiencyList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListUsersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListUsersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListUsersOutput`
    public func listUsersPaginated(input: ListUsersInput) -> ClientRuntime.PaginatorSequence<ListUsersInput, ListUsersOutput> {
        return ClientRuntime.PaginatorSequence<ListUsersInput, ListUsersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listUsers(input:))
    }
}

extension ListUsersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListUsersInput {
        return ListUsersInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListUsersInput, OperationStackOutput == ListUsersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listUsersPaginated`
    /// to access the nested member `[ConnectClientTypes.UserSummary]`
    /// - Returns: `[ConnectClientTypes.UserSummary]`
    public func userSummaryList() async throws -> [ConnectClientTypes.UserSummary] {
        return try await self.asyncCompactMap { item in item.userSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListViewsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListViewsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListViewsOutput`
    public func listViewsPaginated(input: ListViewsInput) -> ClientRuntime.PaginatorSequence<ListViewsInput, ListViewsOutput> {
        return ClientRuntime.PaginatorSequence<ListViewsInput, ListViewsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listViews(input:))
    }
}

extension ListViewsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListViewsInput {
        return ListViewsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            type: self.type
        )}
}

extension PaginatorSequence where OperationStackInput == ListViewsInput, OperationStackOutput == ListViewsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listViewsPaginated`
    /// to access the nested member `[ConnectClientTypes.ViewSummary]`
    /// - Returns: `[ConnectClientTypes.ViewSummary]`
    public func viewsSummaryList() async throws -> [ConnectClientTypes.ViewSummary] {
        return try await self.asyncCompactMap { item in item.viewsSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[ListViewVersionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListViewVersionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListViewVersionsOutput`
    public func listViewVersionsPaginated(input: ListViewVersionsInput) -> ClientRuntime.PaginatorSequence<ListViewVersionsInput, ListViewVersionsOutput> {
        return ClientRuntime.PaginatorSequence<ListViewVersionsInput, ListViewVersionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listViewVersions(input:))
    }
}

extension ListViewVersionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListViewVersionsInput {
        return ListViewVersionsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            viewId: self.viewId
        )}
}

extension PaginatorSequence where OperationStackInput == ListViewVersionsInput, OperationStackOutput == ListViewVersionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listViewVersionsPaginated`
    /// to access the nested member `[ConnectClientTypes.ViewVersionSummary]`
    /// - Returns: `[ConnectClientTypes.ViewVersionSummary]`
    public func viewVersionSummaryList() async throws -> [ConnectClientTypes.ViewVersionSummary] {
        return try await self.asyncCompactMap { item in item.viewVersionSummaryList }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchAgentStatusesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchAgentStatusesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchAgentStatusesOutput`
    public func searchAgentStatusesPaginated(input: SearchAgentStatusesInput) -> ClientRuntime.PaginatorSequence<SearchAgentStatusesInput, SearchAgentStatusesOutput> {
        return ClientRuntime.PaginatorSequence<SearchAgentStatusesInput, SearchAgentStatusesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchAgentStatuses(input:))
    }
}

extension SearchAgentStatusesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchAgentStatusesInput {
        return SearchAgentStatusesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            searchCriteria: self.searchCriteria,
            searchFilter: self.searchFilter
        )}
}

extension PaginatorSequence where OperationStackInput == SearchAgentStatusesInput, OperationStackOutput == SearchAgentStatusesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchAgentStatusesPaginated`
    /// to access the nested member `[ConnectClientTypes.AgentStatus]`
    /// - Returns: `[ConnectClientTypes.AgentStatus]`
    public func agentStatuses() async throws -> [ConnectClientTypes.AgentStatus] {
        return try await self.asyncCompactMap { item in item.agentStatuses }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchAvailablePhoneNumbersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchAvailablePhoneNumbersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchAvailablePhoneNumbersOutput`
    public func searchAvailablePhoneNumbersPaginated(input: SearchAvailablePhoneNumbersInput) -> ClientRuntime.PaginatorSequence<SearchAvailablePhoneNumbersInput, SearchAvailablePhoneNumbersOutput> {
        return ClientRuntime.PaginatorSequence<SearchAvailablePhoneNumbersInput, SearchAvailablePhoneNumbersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchAvailablePhoneNumbers(input:))
    }
}

extension SearchAvailablePhoneNumbersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchAvailablePhoneNumbersInput {
        return SearchAvailablePhoneNumbersInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            phoneNumberCountryCode: self.phoneNumberCountryCode,
            phoneNumberPrefix: self.phoneNumberPrefix,
            phoneNumberType: self.phoneNumberType,
            targetArn: self.targetArn
        )}
}

extension PaginatorSequence where OperationStackInput == SearchAvailablePhoneNumbersInput, OperationStackOutput == SearchAvailablePhoneNumbersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchAvailablePhoneNumbersPaginated`
    /// to access the nested member `[ConnectClientTypes.AvailableNumberSummary]`
    /// - Returns: `[ConnectClientTypes.AvailableNumberSummary]`
    public func availableNumbersList() async throws -> [ConnectClientTypes.AvailableNumberSummary] {
        return try await self.asyncCompactMap { item in item.availableNumbersList }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchContactFlowModulesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchContactFlowModulesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchContactFlowModulesOutput`
    public func searchContactFlowModulesPaginated(input: SearchContactFlowModulesInput) -> ClientRuntime.PaginatorSequence<SearchContactFlowModulesInput, SearchContactFlowModulesOutput> {
        return ClientRuntime.PaginatorSequence<SearchContactFlowModulesInput, SearchContactFlowModulesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchContactFlowModules(input:))
    }
}

extension SearchContactFlowModulesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchContactFlowModulesInput {
        return SearchContactFlowModulesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            searchCriteria: self.searchCriteria,
            searchFilter: self.searchFilter
        )}
}

extension PaginatorSequence where OperationStackInput == SearchContactFlowModulesInput, OperationStackOutput == SearchContactFlowModulesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchContactFlowModulesPaginated`
    /// to access the nested member `[ConnectClientTypes.ContactFlowModule]`
    /// - Returns: `[ConnectClientTypes.ContactFlowModule]`
    public func contactFlowModules() async throws -> [ConnectClientTypes.ContactFlowModule] {
        return try await self.asyncCompactMap { item in item.contactFlowModules }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchContactFlowsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchContactFlowsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchContactFlowsOutput`
    public func searchContactFlowsPaginated(input: SearchContactFlowsInput) -> ClientRuntime.PaginatorSequence<SearchContactFlowsInput, SearchContactFlowsOutput> {
        return ClientRuntime.PaginatorSequence<SearchContactFlowsInput, SearchContactFlowsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchContactFlows(input:))
    }
}

extension SearchContactFlowsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchContactFlowsInput {
        return SearchContactFlowsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            searchCriteria: self.searchCriteria,
            searchFilter: self.searchFilter
        )}
}

extension PaginatorSequence where OperationStackInput == SearchContactFlowsInput, OperationStackOutput == SearchContactFlowsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchContactFlowsPaginated`
    /// to access the nested member `[ConnectClientTypes.ContactFlow]`
    /// - Returns: `[ConnectClientTypes.ContactFlow]`
    public func contactFlows() async throws -> [ConnectClientTypes.ContactFlow] {
        return try await self.asyncCompactMap { item in item.contactFlows }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchContactsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchContactsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchContactsOutput`
    public func searchContactsPaginated(input: SearchContactsInput) -> ClientRuntime.PaginatorSequence<SearchContactsInput, SearchContactsOutput> {
        return ClientRuntime.PaginatorSequence<SearchContactsInput, SearchContactsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchContacts(input:))
    }
}

extension SearchContactsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchContactsInput {
        return SearchContactsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            searchCriteria: self.searchCriteria,
            sort: self.sort,
            timeRange: self.timeRange
        )}
}

extension PaginatorSequence where OperationStackInput == SearchContactsInput, OperationStackOutput == SearchContactsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchContactsPaginated`
    /// to access the nested member `[ConnectClientTypes.ContactSearchSummary]`
    /// - Returns: `[ConnectClientTypes.ContactSearchSummary]`
    public func contacts() async throws -> [ConnectClientTypes.ContactSearchSummary] {
        return try await self.asyncCompactMap { item in item.contacts }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchHoursOfOperationOverridesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchHoursOfOperationOverridesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchHoursOfOperationOverridesOutput`
    public func searchHoursOfOperationOverridesPaginated(input: SearchHoursOfOperationOverridesInput) -> ClientRuntime.PaginatorSequence<SearchHoursOfOperationOverridesInput, SearchHoursOfOperationOverridesOutput> {
        return ClientRuntime.PaginatorSequence<SearchHoursOfOperationOverridesInput, SearchHoursOfOperationOverridesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchHoursOfOperationOverrides(input:))
    }
}

extension SearchHoursOfOperationOverridesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchHoursOfOperationOverridesInput {
        return SearchHoursOfOperationOverridesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            searchCriteria: self.searchCriteria,
            searchFilter: self.searchFilter
        )}
}

extension PaginatorSequence where OperationStackInput == SearchHoursOfOperationOverridesInput, OperationStackOutput == SearchHoursOfOperationOverridesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchHoursOfOperationOverridesPaginated`
    /// to access the nested member `[ConnectClientTypes.HoursOfOperationOverride]`
    /// - Returns: `[ConnectClientTypes.HoursOfOperationOverride]`
    public func hoursOfOperationOverrides() async throws -> [ConnectClientTypes.HoursOfOperationOverride] {
        return try await self.asyncCompactMap { item in item.hoursOfOperationOverrides }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchHoursOfOperationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchHoursOfOperationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchHoursOfOperationsOutput`
    public func searchHoursOfOperationsPaginated(input: SearchHoursOfOperationsInput) -> ClientRuntime.PaginatorSequence<SearchHoursOfOperationsInput, SearchHoursOfOperationsOutput> {
        return ClientRuntime.PaginatorSequence<SearchHoursOfOperationsInput, SearchHoursOfOperationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchHoursOfOperations(input:))
    }
}

extension SearchHoursOfOperationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchHoursOfOperationsInput {
        return SearchHoursOfOperationsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            searchCriteria: self.searchCriteria,
            searchFilter: self.searchFilter
        )}
}

extension PaginatorSequence where OperationStackInput == SearchHoursOfOperationsInput, OperationStackOutput == SearchHoursOfOperationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchHoursOfOperationsPaginated`
    /// to access the nested member `[ConnectClientTypes.HoursOfOperation]`
    /// - Returns: `[ConnectClientTypes.HoursOfOperation]`
    public func hoursOfOperations() async throws -> [ConnectClientTypes.HoursOfOperation] {
        return try await self.asyncCompactMap { item in item.hoursOfOperations }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchPredefinedAttributesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchPredefinedAttributesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchPredefinedAttributesOutput`
    public func searchPredefinedAttributesPaginated(input: SearchPredefinedAttributesInput) -> ClientRuntime.PaginatorSequence<SearchPredefinedAttributesInput, SearchPredefinedAttributesOutput> {
        return ClientRuntime.PaginatorSequence<SearchPredefinedAttributesInput, SearchPredefinedAttributesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchPredefinedAttributes(input:))
    }
}

extension SearchPredefinedAttributesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchPredefinedAttributesInput {
        return SearchPredefinedAttributesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            searchCriteria: self.searchCriteria
        )}
}

extension PaginatorSequence where OperationStackInput == SearchPredefinedAttributesInput, OperationStackOutput == SearchPredefinedAttributesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchPredefinedAttributesPaginated`
    /// to access the nested member `[ConnectClientTypes.PredefinedAttribute]`
    /// - Returns: `[ConnectClientTypes.PredefinedAttribute]`
    public func predefinedAttributes() async throws -> [ConnectClientTypes.PredefinedAttribute] {
        return try await self.asyncCompactMap { item in item.predefinedAttributes }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchPromptsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchPromptsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchPromptsOutput`
    public func searchPromptsPaginated(input: SearchPromptsInput) -> ClientRuntime.PaginatorSequence<SearchPromptsInput, SearchPromptsOutput> {
        return ClientRuntime.PaginatorSequence<SearchPromptsInput, SearchPromptsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchPrompts(input:))
    }
}

extension SearchPromptsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchPromptsInput {
        return SearchPromptsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            searchCriteria: self.searchCriteria,
            searchFilter: self.searchFilter
        )}
}

extension PaginatorSequence where OperationStackInput == SearchPromptsInput, OperationStackOutput == SearchPromptsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchPromptsPaginated`
    /// to access the nested member `[ConnectClientTypes.Prompt]`
    /// - Returns: `[ConnectClientTypes.Prompt]`
    public func prompts() async throws -> [ConnectClientTypes.Prompt] {
        return try await self.asyncCompactMap { item in item.prompts }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchQueuesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchQueuesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchQueuesOutput`
    public func searchQueuesPaginated(input: SearchQueuesInput) -> ClientRuntime.PaginatorSequence<SearchQueuesInput, SearchQueuesOutput> {
        return ClientRuntime.PaginatorSequence<SearchQueuesInput, SearchQueuesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchQueues(input:))
    }
}

extension SearchQueuesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchQueuesInput {
        return SearchQueuesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            searchCriteria: self.searchCriteria,
            searchFilter: self.searchFilter
        )}
}

extension PaginatorSequence where OperationStackInput == SearchQueuesInput, OperationStackOutput == SearchQueuesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchQueuesPaginated`
    /// to access the nested member `[ConnectClientTypes.Queue]`
    /// - Returns: `[ConnectClientTypes.Queue]`
    public func queues() async throws -> [ConnectClientTypes.Queue] {
        return try await self.asyncCompactMap { item in item.queues }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchQuickConnectsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchQuickConnectsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchQuickConnectsOutput`
    public func searchQuickConnectsPaginated(input: SearchQuickConnectsInput) -> ClientRuntime.PaginatorSequence<SearchQuickConnectsInput, SearchQuickConnectsOutput> {
        return ClientRuntime.PaginatorSequence<SearchQuickConnectsInput, SearchQuickConnectsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchQuickConnects(input:))
    }
}

extension SearchQuickConnectsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchQuickConnectsInput {
        return SearchQuickConnectsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            searchCriteria: self.searchCriteria,
            searchFilter: self.searchFilter
        )}
}

extension PaginatorSequence where OperationStackInput == SearchQuickConnectsInput, OperationStackOutput == SearchQuickConnectsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchQuickConnectsPaginated`
    /// to access the nested member `[ConnectClientTypes.QuickConnect]`
    /// - Returns: `[ConnectClientTypes.QuickConnect]`
    public func quickConnects() async throws -> [ConnectClientTypes.QuickConnect] {
        return try await self.asyncCompactMap { item in item.quickConnects }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchResourceTagsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchResourceTagsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchResourceTagsOutput`
    public func searchResourceTagsPaginated(input: SearchResourceTagsInput) -> ClientRuntime.PaginatorSequence<SearchResourceTagsInput, SearchResourceTagsOutput> {
        return ClientRuntime.PaginatorSequence<SearchResourceTagsInput, SearchResourceTagsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchResourceTags(input:))
    }
}

extension SearchResourceTagsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchResourceTagsInput {
        return SearchResourceTagsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            resourceTypes: self.resourceTypes,
            searchCriteria: self.searchCriteria
        )}
}

extension PaginatorSequence where OperationStackInput == SearchResourceTagsInput, OperationStackOutput == SearchResourceTagsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchResourceTagsPaginated`
    /// to access the nested member `[ConnectClientTypes.TagSet]`
    /// - Returns: `[ConnectClientTypes.TagSet]`
    public func tags() async throws -> [ConnectClientTypes.TagSet] {
        return try await self.asyncCompactMap { item in item.tags }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchRoutingProfilesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchRoutingProfilesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchRoutingProfilesOutput`
    public func searchRoutingProfilesPaginated(input: SearchRoutingProfilesInput) -> ClientRuntime.PaginatorSequence<SearchRoutingProfilesInput, SearchRoutingProfilesOutput> {
        return ClientRuntime.PaginatorSequence<SearchRoutingProfilesInput, SearchRoutingProfilesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchRoutingProfiles(input:))
    }
}

extension SearchRoutingProfilesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchRoutingProfilesInput {
        return SearchRoutingProfilesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            searchCriteria: self.searchCriteria,
            searchFilter: self.searchFilter
        )}
}

extension PaginatorSequence where OperationStackInput == SearchRoutingProfilesInput, OperationStackOutput == SearchRoutingProfilesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchRoutingProfilesPaginated`
    /// to access the nested member `[ConnectClientTypes.RoutingProfile]`
    /// - Returns: `[ConnectClientTypes.RoutingProfile]`
    public func routingProfiles() async throws -> [ConnectClientTypes.RoutingProfile] {
        return try await self.asyncCompactMap { item in item.routingProfiles }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchSecurityProfilesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchSecurityProfilesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchSecurityProfilesOutput`
    public func searchSecurityProfilesPaginated(input: SearchSecurityProfilesInput) -> ClientRuntime.PaginatorSequence<SearchSecurityProfilesInput, SearchSecurityProfilesOutput> {
        return ClientRuntime.PaginatorSequence<SearchSecurityProfilesInput, SearchSecurityProfilesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchSecurityProfiles(input:))
    }
}

extension SearchSecurityProfilesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchSecurityProfilesInput {
        return SearchSecurityProfilesInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            searchCriteria: self.searchCriteria,
            searchFilter: self.searchFilter
        )}
}

extension PaginatorSequence where OperationStackInput == SearchSecurityProfilesInput, OperationStackOutput == SearchSecurityProfilesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchSecurityProfilesPaginated`
    /// to access the nested member `[ConnectClientTypes.SecurityProfileSearchSummary]`
    /// - Returns: `[ConnectClientTypes.SecurityProfileSearchSummary]`
    public func securityProfiles() async throws -> [ConnectClientTypes.SecurityProfileSearchSummary] {
        return try await self.asyncCompactMap { item in item.securityProfiles }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchUserHierarchyGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchUserHierarchyGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchUserHierarchyGroupsOutput`
    public func searchUserHierarchyGroupsPaginated(input: SearchUserHierarchyGroupsInput) -> ClientRuntime.PaginatorSequence<SearchUserHierarchyGroupsInput, SearchUserHierarchyGroupsOutput> {
        return ClientRuntime.PaginatorSequence<SearchUserHierarchyGroupsInput, SearchUserHierarchyGroupsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchUserHierarchyGroups(input:))
    }
}

extension SearchUserHierarchyGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchUserHierarchyGroupsInput {
        return SearchUserHierarchyGroupsInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            searchCriteria: self.searchCriteria,
            searchFilter: self.searchFilter
        )}
}

extension PaginatorSequence where OperationStackInput == SearchUserHierarchyGroupsInput, OperationStackOutput == SearchUserHierarchyGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchUserHierarchyGroupsPaginated`
    /// to access the nested member `[ConnectClientTypes.HierarchyGroup]`
    /// - Returns: `[ConnectClientTypes.HierarchyGroup]`
    public func userHierarchyGroups() async throws -> [ConnectClientTypes.HierarchyGroup] {
        return try await self.asyncCompactMap { item in item.userHierarchyGroups }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchUsersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchUsersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchUsersOutput`
    public func searchUsersPaginated(input: SearchUsersInput) -> ClientRuntime.PaginatorSequence<SearchUsersInput, SearchUsersOutput> {
        return ClientRuntime.PaginatorSequence<SearchUsersInput, SearchUsersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchUsers(input:))
    }
}

extension SearchUsersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchUsersInput {
        return SearchUsersInput(
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token,
            searchCriteria: self.searchCriteria,
            searchFilter: self.searchFilter
        )}
}

extension PaginatorSequence where OperationStackInput == SearchUsersInput, OperationStackOutput == SearchUsersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchUsersPaginated`
    /// to access the nested member `[ConnectClientTypes.UserSearchSummary]`
    /// - Returns: `[ConnectClientTypes.UserSearchSummary]`
    public func users() async throws -> [ConnectClientTypes.UserSearchSummary] {
        return try await self.asyncCompactMap { item in item.users }
    }
}
extension ConnectClient {
    /// Paginate over `[SearchVocabulariesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchVocabulariesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchVocabulariesOutput`
    public func searchVocabulariesPaginated(input: SearchVocabulariesInput) -> ClientRuntime.PaginatorSequence<SearchVocabulariesInput, SearchVocabulariesOutput> {
        return ClientRuntime.PaginatorSequence<SearchVocabulariesInput, SearchVocabulariesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchVocabularies(input:))
    }
}

extension SearchVocabulariesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchVocabulariesInput {
        return SearchVocabulariesInput(
            instanceId: self.instanceId,
            languageCode: self.languageCode,
            maxResults: self.maxResults,
            nameStartsWith: self.nameStartsWith,
            nextToken: token,
            state: self.state
        )}
}

extension PaginatorSequence where OperationStackInput == SearchVocabulariesInput, OperationStackOutput == SearchVocabulariesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchVocabulariesPaginated`
    /// to access the nested member `[ConnectClientTypes.VocabularySummary]`
    /// - Returns: `[ConnectClientTypes.VocabularySummary]`
    public func vocabularySummaryList() async throws -> [ConnectClientTypes.VocabularySummary] {
        return try await self.asyncCompactMap { item in item.vocabularySummaryList }
    }
}
