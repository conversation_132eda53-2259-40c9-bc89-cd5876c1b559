// swift-tools-version: 5.9.0

import PackageDescription

let package = Package(
    name: "AWSECRPUBLIC",
    platforms: [
        .macOS(.v10_15), .iOS(.v13)
    ],
    products: [
        .library(name: "AWSECRPUBLIC", targets: ["AWSECRPUBLIC"])
    ],
    dependencies: [
        .package(
            id: "aws-sdk-swift.AWSClientRuntime",
            exact: "0.0.1"
        ),
        .package(
            id: "aws-sdk-swift.AWSSDKHTTPAuth",
            exact: "0.0.1"
        ),
        .package(
            url: "https://github.com/smithy-lang/smithy-swift",
            exact: "0.0.1"
        ),
    ],
    targets: [
        .target(
            name: "AWSECRPUBLIC",
            dependencies: [
                .product(
                    name: "AWSClientRuntime",
                    package: "aws-sdk-swift.AWSClientRuntime"
                ),
                .product(
                    name: "AWSSDKHTTPAuth",
                    package: "aws-sdk-swift.AWSSDKHTTPAuth"
                ),
                .product(
                    name: "<PERSON>y<PERSON><PERSON><PERSON>uthAPI",
                    package: "smithy-swift"
                ),
                .product(
                    name: "<PERSON><PERSON>",
                    package: "smithy-swift"
                ),
                .product(
                    name: "<PERSON><PERSON><PERSON>unt<PERSON>",
                    package: "smithy-swift"
                ),
                .product(
                    name: "SmithyIdentity",
                    package: "smithy-swift"
                ),
                .product(
                    name: "SmithyRetriesAPI",
                    package: "smithy-swift"
                ),
                .product(
                    name: "SmithyHTTPAPI",
                    package: "smithy-swift"
                ),
                .product(
                    name: "SmithyRetries",
                    package: "smithy-swift"
                ),
                .product(
                    name: "SmithyJSON",
                    package: "smithy-swift"
                ),
                .product(
                    name: "SmithyReadWrite",
                    package: "smithy-swift"
                ),
                .product(
                    name: "SmithyTimestamps",
                    package: "smithy-swift"
                ),
                .product(
                    name: "SmithyTestUtil",
                    package: "smithy-swift"
                ),
            ]
        ),
        .testTarget(
            name: "AWSECRPUBLICTests",
            dependencies: [
                "AWSECRPUBLIC",
                .product(
                    name: "SmithyTestUtil",
                    package: "smithy-swift"
                ),
            ]
        )
    ]
)
