//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension CloudWatchLogsClient {
    /// Paginate over `[DescribeConfigurationTemplatesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeConfigurationTemplatesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeConfigurationTemplatesOutput`
    public func describeConfigurationTemplatesPaginated(input: DescribeConfigurationTemplatesInput) -> ClientRuntime.PaginatorSequence<DescribeConfigurationTemplatesInput, DescribeConfigurationTemplatesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeConfigurationTemplatesInput, DescribeConfigurationTemplatesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeConfigurationTemplates(input:))
    }
}

extension DescribeConfigurationTemplatesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeConfigurationTemplatesInput {
        return DescribeConfigurationTemplatesInput(
            deliveryDestinationTypes: self.deliveryDestinationTypes,
            limit: self.limit,
            logTypes: self.logTypes,
            nextToken: token,
            resourceTypes: self.resourceTypes,
            service: self.service
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeConfigurationTemplatesInput, OperationStackOutput == DescribeConfigurationTemplatesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeConfigurationTemplatesPaginated`
    /// to access the nested member `[CloudWatchLogsClientTypes.ConfigurationTemplate]`
    /// - Returns: `[CloudWatchLogsClientTypes.ConfigurationTemplate]`
    public func configurationTemplates() async throws -> [CloudWatchLogsClientTypes.ConfigurationTemplate] {
        return try await self.asyncCompactMap { item in item.configurationTemplates }
    }
}
extension CloudWatchLogsClient {
    /// Paginate over `[DescribeDeliveriesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeDeliveriesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeDeliveriesOutput`
    public func describeDeliveriesPaginated(input: DescribeDeliveriesInput) -> ClientRuntime.PaginatorSequence<DescribeDeliveriesInput, DescribeDeliveriesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeDeliveriesInput, DescribeDeliveriesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeDeliveries(input:))
    }
}

extension DescribeDeliveriesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeDeliveriesInput {
        return DescribeDeliveriesInput(
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeDeliveriesInput, OperationStackOutput == DescribeDeliveriesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeDeliveriesPaginated`
    /// to access the nested member `[CloudWatchLogsClientTypes.Delivery]`
    /// - Returns: `[CloudWatchLogsClientTypes.Delivery]`
    public func deliveries() async throws -> [CloudWatchLogsClientTypes.Delivery] {
        return try await self.asyncCompactMap { item in item.deliveries }
    }
}
extension CloudWatchLogsClient {
    /// Paginate over `[DescribeDeliveryDestinationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeDeliveryDestinationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeDeliveryDestinationsOutput`
    public func describeDeliveryDestinationsPaginated(input: DescribeDeliveryDestinationsInput) -> ClientRuntime.PaginatorSequence<DescribeDeliveryDestinationsInput, DescribeDeliveryDestinationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeDeliveryDestinationsInput, DescribeDeliveryDestinationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeDeliveryDestinations(input:))
    }
}

extension DescribeDeliveryDestinationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeDeliveryDestinationsInput {
        return DescribeDeliveryDestinationsInput(
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeDeliveryDestinationsInput, OperationStackOutput == DescribeDeliveryDestinationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeDeliveryDestinationsPaginated`
    /// to access the nested member `[CloudWatchLogsClientTypes.DeliveryDestination]`
    /// - Returns: `[CloudWatchLogsClientTypes.DeliveryDestination]`
    public func deliveryDestinations() async throws -> [CloudWatchLogsClientTypes.DeliveryDestination] {
        return try await self.asyncCompactMap { item in item.deliveryDestinations }
    }
}
extension CloudWatchLogsClient {
    /// Paginate over `[DescribeDeliverySourcesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeDeliverySourcesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeDeliverySourcesOutput`
    public func describeDeliverySourcesPaginated(input: DescribeDeliverySourcesInput) -> ClientRuntime.PaginatorSequence<DescribeDeliverySourcesInput, DescribeDeliverySourcesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeDeliverySourcesInput, DescribeDeliverySourcesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeDeliverySources(input:))
    }
}

extension DescribeDeliverySourcesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeDeliverySourcesInput {
        return DescribeDeliverySourcesInput(
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeDeliverySourcesInput, OperationStackOutput == DescribeDeliverySourcesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeDeliverySourcesPaginated`
    /// to access the nested member `[CloudWatchLogsClientTypes.DeliverySource]`
    /// - Returns: `[CloudWatchLogsClientTypes.DeliverySource]`
    public func deliverySources() async throws -> [CloudWatchLogsClientTypes.DeliverySource] {
        return try await self.asyncCompactMap { item in item.deliverySources }
    }
}
extension CloudWatchLogsClient {
    /// Paginate over `[DescribeDestinationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeDestinationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeDestinationsOutput`
    public func describeDestinationsPaginated(input: DescribeDestinationsInput) -> ClientRuntime.PaginatorSequence<DescribeDestinationsInput, DescribeDestinationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeDestinationsInput, DescribeDestinationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeDestinations(input:))
    }
}

extension DescribeDestinationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeDestinationsInput {
        return DescribeDestinationsInput(
            destinationNamePrefix: self.destinationNamePrefix,
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeDestinationsInput, OperationStackOutput == DescribeDestinationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeDestinationsPaginated`
    /// to access the nested member `[CloudWatchLogsClientTypes.Destination]`
    /// - Returns: `[CloudWatchLogsClientTypes.Destination]`
    public func destinations() async throws -> [CloudWatchLogsClientTypes.Destination] {
        return try await self.asyncCompactMap { item in item.destinations }
    }
}
extension CloudWatchLogsClient {
    /// Paginate over `[DescribeLogGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeLogGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeLogGroupsOutput`
    public func describeLogGroupsPaginated(input: DescribeLogGroupsInput) -> ClientRuntime.PaginatorSequence<DescribeLogGroupsInput, DescribeLogGroupsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeLogGroupsInput, DescribeLogGroupsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeLogGroups(input:))
    }
}

extension DescribeLogGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeLogGroupsInput {
        return DescribeLogGroupsInput(
            accountIdentifiers: self.accountIdentifiers,
            includeLinkedAccounts: self.includeLinkedAccounts,
            limit: self.limit,
            logGroupClass: self.logGroupClass,
            logGroupNamePattern: self.logGroupNamePattern,
            logGroupNamePrefix: self.logGroupNamePrefix,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeLogGroupsInput, OperationStackOutput == DescribeLogGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeLogGroupsPaginated`
    /// to access the nested member `[CloudWatchLogsClientTypes.LogGroup]`
    /// - Returns: `[CloudWatchLogsClientTypes.LogGroup]`
    public func logGroups() async throws -> [CloudWatchLogsClientTypes.LogGroup] {
        return try await self.asyncCompactMap { item in item.logGroups }
    }
}
extension CloudWatchLogsClient {
    /// Paginate over `[DescribeLogStreamsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeLogStreamsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeLogStreamsOutput`
    public func describeLogStreamsPaginated(input: DescribeLogStreamsInput) -> ClientRuntime.PaginatorSequence<DescribeLogStreamsInput, DescribeLogStreamsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeLogStreamsInput, DescribeLogStreamsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeLogStreams(input:))
    }
}

extension DescribeLogStreamsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeLogStreamsInput {
        return DescribeLogStreamsInput(
            descending: self.descending,
            limit: self.limit,
            logGroupIdentifier: self.logGroupIdentifier,
            logGroupName: self.logGroupName,
            logStreamNamePrefix: self.logStreamNamePrefix,
            nextToken: token,
            orderBy: self.orderBy
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeLogStreamsInput, OperationStackOutput == DescribeLogStreamsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeLogStreamsPaginated`
    /// to access the nested member `[CloudWatchLogsClientTypes.LogStream]`
    /// - Returns: `[CloudWatchLogsClientTypes.LogStream]`
    public func logStreams() async throws -> [CloudWatchLogsClientTypes.LogStream] {
        return try await self.asyncCompactMap { item in item.logStreams }
    }
}
extension CloudWatchLogsClient {
    /// Paginate over `[DescribeMetricFiltersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeMetricFiltersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeMetricFiltersOutput`
    public func describeMetricFiltersPaginated(input: DescribeMetricFiltersInput) -> ClientRuntime.PaginatorSequence<DescribeMetricFiltersInput, DescribeMetricFiltersOutput> {
        return ClientRuntime.PaginatorSequence<DescribeMetricFiltersInput, DescribeMetricFiltersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeMetricFilters(input:))
    }
}

extension DescribeMetricFiltersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeMetricFiltersInput {
        return DescribeMetricFiltersInput(
            filterNamePrefix: self.filterNamePrefix,
            limit: self.limit,
            logGroupName: self.logGroupName,
            metricName: self.metricName,
            metricNamespace: self.metricNamespace,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeMetricFiltersInput, OperationStackOutput == DescribeMetricFiltersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeMetricFiltersPaginated`
    /// to access the nested member `[CloudWatchLogsClientTypes.MetricFilter]`
    /// - Returns: `[CloudWatchLogsClientTypes.MetricFilter]`
    public func metricFilters() async throws -> [CloudWatchLogsClientTypes.MetricFilter] {
        return try await self.asyncCompactMap { item in item.metricFilters }
    }
}
extension CloudWatchLogsClient {
    /// Paginate over `[DescribeSubscriptionFiltersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeSubscriptionFiltersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeSubscriptionFiltersOutput`
    public func describeSubscriptionFiltersPaginated(input: DescribeSubscriptionFiltersInput) -> ClientRuntime.PaginatorSequence<DescribeSubscriptionFiltersInput, DescribeSubscriptionFiltersOutput> {
        return ClientRuntime.PaginatorSequence<DescribeSubscriptionFiltersInput, DescribeSubscriptionFiltersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeSubscriptionFilters(input:))
    }
}

extension DescribeSubscriptionFiltersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeSubscriptionFiltersInput {
        return DescribeSubscriptionFiltersInput(
            filterNamePrefix: self.filterNamePrefix,
            limit: self.limit,
            logGroupName: self.logGroupName,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeSubscriptionFiltersInput, OperationStackOutput == DescribeSubscriptionFiltersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeSubscriptionFiltersPaginated`
    /// to access the nested member `[CloudWatchLogsClientTypes.SubscriptionFilter]`
    /// - Returns: `[CloudWatchLogsClientTypes.SubscriptionFilter]`
    public func subscriptionFilters() async throws -> [CloudWatchLogsClientTypes.SubscriptionFilter] {
        return try await self.asyncCompactMap { item in item.subscriptionFilters }
    }
}
extension CloudWatchLogsClient {
    /// Paginate over `[FilterLogEventsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[FilterLogEventsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `FilterLogEventsOutput`
    public func filterLogEventsPaginated(input: FilterLogEventsInput) -> ClientRuntime.PaginatorSequence<FilterLogEventsInput, FilterLogEventsOutput> {
        return ClientRuntime.PaginatorSequence<FilterLogEventsInput, FilterLogEventsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.filterLogEvents(input:))
    }
}

extension FilterLogEventsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> FilterLogEventsInput {
        return FilterLogEventsInput(
            endTime: self.endTime,
            filterPattern: self.filterPattern,
            interleaved: self.interleaved,
            limit: self.limit,
            logGroupIdentifier: self.logGroupIdentifier,
            logGroupName: self.logGroupName,
            logStreamNamePrefix: self.logStreamNamePrefix,
            logStreamNames: self.logStreamNames,
            nextToken: token,
            startTime: self.startTime,
            unmask: self.unmask
        )}
}
extension CloudWatchLogsClient {
    /// Paginate over `[GetLogEventsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetLogEventsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetLogEventsOutput`
    public func getLogEventsPaginated(input: GetLogEventsInput) -> ClientRuntime.PaginatorSequence<GetLogEventsInput, GetLogEventsOutput> {
        return ClientRuntime.PaginatorSequence<GetLogEventsInput, GetLogEventsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextForwardToken, paginationFunction: self.getLogEvents(input:))
    }
}

extension GetLogEventsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetLogEventsInput {
        return GetLogEventsInput(
            endTime: self.endTime,
            limit: self.limit,
            logGroupIdentifier: self.logGroupIdentifier,
            logGroupName: self.logGroupName,
            logStreamName: self.logStreamName,
            nextToken: token,
            startFromHead: self.startFromHead,
            startTime: self.startTime,
            unmask: self.unmask
        )}
}

extension PaginatorSequence where OperationStackInput == GetLogEventsInput, OperationStackOutput == GetLogEventsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getLogEventsPaginated`
    /// to access the nested member `[CloudWatchLogsClientTypes.OutputLogEvent]`
    /// - Returns: `[CloudWatchLogsClientTypes.OutputLogEvent]`
    public func events() async throws -> [CloudWatchLogsClientTypes.OutputLogEvent] {
        return try await self.asyncCompactMap { item in item.events }
    }
}
extension CloudWatchLogsClient {
    /// Paginate over `[ListAnomaliesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListAnomaliesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListAnomaliesOutput`
    public func listAnomaliesPaginated(input: ListAnomaliesInput) -> ClientRuntime.PaginatorSequence<ListAnomaliesInput, ListAnomaliesOutput> {
        return ClientRuntime.PaginatorSequence<ListAnomaliesInput, ListAnomaliesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listAnomalies(input:))
    }
}

extension ListAnomaliesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListAnomaliesInput {
        return ListAnomaliesInput(
            anomalyDetectorArn: self.anomalyDetectorArn,
            limit: self.limit,
            nextToken: token,
            suppressionState: self.suppressionState
        )}
}

extension PaginatorSequence where OperationStackInput == ListAnomaliesInput, OperationStackOutput == ListAnomaliesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listAnomaliesPaginated`
    /// to access the nested member `[CloudWatchLogsClientTypes.Anomaly]`
    /// - Returns: `[CloudWatchLogsClientTypes.Anomaly]`
    public func anomalies() async throws -> [CloudWatchLogsClientTypes.Anomaly] {
        return try await self.asyncCompactMap { item in item.anomalies }
    }
}
extension CloudWatchLogsClient {
    /// Paginate over `[ListLogAnomalyDetectorsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListLogAnomalyDetectorsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListLogAnomalyDetectorsOutput`
    public func listLogAnomalyDetectorsPaginated(input: ListLogAnomalyDetectorsInput) -> ClientRuntime.PaginatorSequence<ListLogAnomalyDetectorsInput, ListLogAnomalyDetectorsOutput> {
        return ClientRuntime.PaginatorSequence<ListLogAnomalyDetectorsInput, ListLogAnomalyDetectorsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listLogAnomalyDetectors(input:))
    }
}

extension ListLogAnomalyDetectorsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListLogAnomalyDetectorsInput {
        return ListLogAnomalyDetectorsInput(
            filterLogGroupArn: self.filterLogGroupArn,
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListLogAnomalyDetectorsInput, OperationStackOutput == ListLogAnomalyDetectorsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listLogAnomalyDetectorsPaginated`
    /// to access the nested member `[CloudWatchLogsClientTypes.AnomalyDetector]`
    /// - Returns: `[CloudWatchLogsClientTypes.AnomalyDetector]`
    public func anomalyDetectors() async throws -> [CloudWatchLogsClientTypes.AnomalyDetector] {
        return try await self.asyncCompactMap { item in item.anomalyDetectors }
    }
}
extension CloudWatchLogsClient {
    /// Paginate over `[ListLogGroupsForQueryOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListLogGroupsForQueryInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListLogGroupsForQueryOutput`
    public func listLogGroupsForQueryPaginated(input: ListLogGroupsForQueryInput) -> ClientRuntime.PaginatorSequence<ListLogGroupsForQueryInput, ListLogGroupsForQueryOutput> {
        return ClientRuntime.PaginatorSequence<ListLogGroupsForQueryInput, ListLogGroupsForQueryOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listLogGroupsForQuery(input:))
    }
}

extension ListLogGroupsForQueryInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListLogGroupsForQueryInput {
        return ListLogGroupsForQueryInput(
            maxResults: self.maxResults,
            nextToken: token,
            queryId: self.queryId
        )}
}

extension PaginatorSequence where OperationStackInput == ListLogGroupsForQueryInput, OperationStackOutput == ListLogGroupsForQueryOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listLogGroupsForQueryPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func logGroupIdentifiers() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.logGroupIdentifiers }
    }
}
