//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension DeadlineClient {
    /// Paginate over `[GetSessionsStatisticsAggregationOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetSessionsStatisticsAggregationInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetSessionsStatisticsAggregationOutput`
    public func getSessionsStatisticsAggregationPaginated(input: GetSessionsStatisticsAggregationInput) -> ClientRuntime.PaginatorSequence<GetSessionsStatisticsAggregationInput, GetSessionsStatisticsAggregationOutput> {
        return ClientRuntime.PaginatorSequence<GetSessionsStatisticsAggregationInput, GetSessionsStatisticsAggregationOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getSessionsStatisticsAggregation(input:))
    }
}

extension GetSessionsStatisticsAggregationInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetSessionsStatisticsAggregationInput {
        return GetSessionsStatisticsAggregationInput(
            aggregationId: self.aggregationId,
            farmId: self.farmId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == GetSessionsStatisticsAggregationInput, OperationStackOutput == GetSessionsStatisticsAggregationOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getSessionsStatisticsAggregationPaginated`
    /// to access the nested member `[DeadlineClientTypes.Statistics]`
    /// - Returns: `[DeadlineClientTypes.Statistics]`
    public func statistics() async throws -> [DeadlineClientTypes.Statistics] {
        return try await self.asyncCompactMap { item in item.statistics }
    }
}
extension DeadlineClient {
    /// Paginate over `[ListAvailableMeteredProductsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListAvailableMeteredProductsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListAvailableMeteredProductsOutput`
    public func listAvailableMeteredProductsPaginated(input: ListAvailableMeteredProductsInput) -> ClientRuntime.PaginatorSequence<ListAvailableMeteredProductsInput, ListAvailableMeteredProductsOutput> {
        return ClientRuntime.PaginatorSequence<ListAvailableMeteredProductsInput, ListAvailableMeteredProductsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listAvailableMeteredProducts(input:))
    }
}

extension ListAvailableMeteredProductsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListAvailableMeteredProductsInput {
        return ListAvailableMeteredProductsInput(
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListAvailableMeteredProductsInput, OperationStackOutput == ListAvailableMeteredProductsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listAvailableMeteredProductsPaginated`
    /// to access the nested member `[DeadlineClientTypes.MeteredProductSummary]`
    /// - Returns: `[DeadlineClientTypes.MeteredProductSummary]`
    public func meteredProducts() async throws -> [DeadlineClientTypes.MeteredProductSummary] {
        return try await self.asyncCompactMap { item in item.meteredProducts }
    }
}
extension DeadlineClient {
    /// Paginate over `[ListQueueFleetAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListQueueFleetAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListQueueFleetAssociationsOutput`
    public func listQueueFleetAssociationsPaginated(input: ListQueueFleetAssociationsInput) -> ClientRuntime.PaginatorSequence<ListQueueFleetAssociationsInput, ListQueueFleetAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<ListQueueFleetAssociationsInput, ListQueueFleetAssociationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listQueueFleetAssociations(input:))
    }
}

extension ListQueueFleetAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListQueueFleetAssociationsInput {
        return ListQueueFleetAssociationsInput(
            farmId: self.farmId,
            fleetId: self.fleetId,
            maxResults: self.maxResults,
            nextToken: token,
            queueId: self.queueId
        )}
}

extension PaginatorSequence where OperationStackInput == ListQueueFleetAssociationsInput, OperationStackOutput == ListQueueFleetAssociationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listQueueFleetAssociationsPaginated`
    /// to access the nested member `[DeadlineClientTypes.QueueFleetAssociationSummary]`
    /// - Returns: `[DeadlineClientTypes.QueueFleetAssociationSummary]`
    public func queueFleetAssociations() async throws -> [DeadlineClientTypes.QueueFleetAssociationSummary] {
        return try await self.asyncCompactMap { item in item.queueFleetAssociations }
    }
}
