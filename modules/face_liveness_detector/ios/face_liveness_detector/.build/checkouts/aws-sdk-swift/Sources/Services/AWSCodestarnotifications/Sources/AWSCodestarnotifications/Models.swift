//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHT<PERSON><PERSON>I.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem

/// CodeStar Notifications can't create the notification rule because you do not have sufficient permissions.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// CodeStar Notifications can't complete the request because the resource is being modified by another process. Wait a few minutes and try again.
public struct ConcurrentModificationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConcurrentModificationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Some or all of the configuration is incomplete, missing, or not valid.
public struct ConfigurationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConfigurationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// One of the CodeStar Notifications limits has been exceeded. Limits apply to accounts, notification rules, notifications, resources, and targets. For more information, see Limits.
public struct LimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// A resource with the same name or ID already exists. Notification rule names must be unique in your Amazon Web Services account.
public struct ResourceAlreadyExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceAlreadyExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// One or more parameter values are not valid.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CodestarnotificationsClientTypes {

    public enum DetailType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case basic
        case full
        case sdkUnknown(Swift.String)

        public static var allCases: [DetailType] {
            return [
                .basic,
                .full
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .basic: return "BASIC"
            case .full: return "FULL"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodestarnotificationsClientTypes {

    public enum NotificationRuleStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case enabled
        case sdkUnknown(Swift.String)

        public static var allCases: [NotificationRuleStatus] {
            return [
                .disabled,
                .enabled
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "DISABLED"
            case .enabled: return "ENABLED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodestarnotificationsClientTypes {

    /// Information about the Chatbot topics or Chatbot clients associated with a notification rule.
    public struct Target: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the Chatbot topic or Chatbot client.
        public var targetAddress: Swift.String?
        /// The target type. Can be an Chatbot topic or Chatbot client.
        ///
        /// * Chatbot topics are specified as SNS.
        ///
        /// * Chatbot clients are specified as AWSChatbotSlack.
        public var targetType: Swift.String?

        public init(
            targetAddress: Swift.String? = nil,
            targetType: Swift.String? = nil
        )
        {
            self.targetAddress = targetAddress
            self.targetType = targetType
        }
    }
}

extension CodestarnotificationsClientTypes.Target: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "Target(targetType: \(Swift.String(describing: targetType)), targetAddress: \"CONTENT_REDACTED\")"}
}

public struct CreateNotificationRuleInput: Swift.Sendable {
    /// A unique, client-generated idempotency token that, when provided in a request, ensures the request cannot be repeated with a changed parameter. If a request with the same parameters is received and a token is included, the request returns information about the initial request that used that token. The Amazon Web Services SDKs prepopulate client request tokens. If you are using an Amazon Web Services SDK, an idempotency token is created for you.
    public var clientRequestToken: Swift.String?
    /// The level of detail to include in the notifications for this resource. BASIC will include only the contents of the event as it would appear in Amazon CloudWatch. FULL will include any supplemental information provided by CodeStar Notifications and/or the service for the resource for which the notification is created.
    /// This member is required.
    public var detailType: CodestarnotificationsClientTypes.DetailType?
    /// A list of event types associated with this notification rule. For a list of allowed events, see [EventTypeSummary].
    /// This member is required.
    public var eventTypeIds: [Swift.String]?
    /// The name for the notification rule. Notification rule names must be unique in your Amazon Web Services account.
    /// This member is required.
    public var name: Swift.String?
    /// The Amazon Resource Name (ARN) of the resource to associate with the notification rule. Supported resources include pipelines in CodePipeline, repositories in CodeCommit, and build projects in CodeBuild.
    /// This member is required.
    public var resource: Swift.String?
    /// The status of the notification rule. The default value is ENABLED. If the status is set to DISABLED, notifications aren't sent for the notification rule.
    public var status: CodestarnotificationsClientTypes.NotificationRuleStatus?
    /// A list of tags to apply to this notification rule. Key names cannot start with "aws".
    public var tags: [Swift.String: Swift.String]?
    /// A list of Amazon Resource Names (ARNs) of Amazon Simple Notification Service topics and Chatbot clients to associate with the notification rule.
    /// This member is required.
    public var targets: [CodestarnotificationsClientTypes.Target]?

    public init(
        clientRequestToken: Swift.String? = nil,
        detailType: CodestarnotificationsClientTypes.DetailType? = nil,
        eventTypeIds: [Swift.String]? = nil,
        name: Swift.String? = nil,
        resource: Swift.String? = nil,
        status: CodestarnotificationsClientTypes.NotificationRuleStatus? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        targets: [CodestarnotificationsClientTypes.Target]? = nil
    )
    {
        self.clientRequestToken = clientRequestToken
        self.detailType = detailType
        self.eventTypeIds = eventTypeIds
        self.name = name
        self.resource = resource
        self.status = status
        self.tags = tags
        self.targets = targets
    }
}

extension CreateNotificationRuleInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateNotificationRuleInput(clientRequestToken: \(Swift.String(describing: clientRequestToken)), detailType: \(Swift.String(describing: detailType)), eventTypeIds: \(Swift.String(describing: eventTypeIds)), resource: \(Swift.String(describing: resource)), status: \(Swift.String(describing: status)), tags: \(Swift.String(describing: tags)), targets: \(Swift.String(describing: targets)), name: \"CONTENT_REDACTED\")"}
}

public struct CreateNotificationRuleOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the notification rule.
    public var arn: Swift.String?

    public init(
        arn: Swift.String? = nil
    )
    {
        self.arn = arn
    }
}

public struct DeleteNotificationRuleInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the notification rule you want to delete.
    /// This member is required.
    public var arn: Swift.String?

    public init(
        arn: Swift.String? = nil
    )
    {
        self.arn = arn
    }
}

public struct DeleteNotificationRuleOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the deleted notification rule.
    public var arn: Swift.String?

    public init(
        arn: Swift.String? = nil
    )
    {
        self.arn = arn
    }
}

public struct DeleteTargetInput: Swift.Sendable {
    /// A Boolean value that can be used to delete all associations with this Chatbot topic. The default value is FALSE. If set to TRUE, all associations between that target and every notification rule in your Amazon Web Services account are deleted.
    public var forceUnsubscribeAll: Swift.Bool?
    /// The Amazon Resource Name (ARN) of the Chatbot topic or Chatbot client to delete.
    /// This member is required.
    public var targetAddress: Swift.String?

    public init(
        forceUnsubscribeAll: Swift.Bool? = false,
        targetAddress: Swift.String? = nil
    )
    {
        self.forceUnsubscribeAll = forceUnsubscribeAll
        self.targetAddress = targetAddress
    }
}

extension DeleteTargetInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "DeleteTargetInput(forceUnsubscribeAll: \(Swift.String(describing: forceUnsubscribeAll)), targetAddress: \"CONTENT_REDACTED\")"}
}

public struct DeleteTargetOutput: Swift.Sendable {

    public init() { }
}

/// CodeStar Notifications can't find a resource that matches the provided ARN.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DescribeNotificationRuleInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the notification rule.
    /// This member is required.
    public var arn: Swift.String?

    public init(
        arn: Swift.String? = nil
    )
    {
        self.arn = arn
    }
}

extension CodestarnotificationsClientTypes {

    /// Returns information about an event that has triggered a notification rule.
    public struct EventTypeSummary: Swift.Sendable {
        /// The system-generated ID of the event. For a complete list of event types and IDs, see [Notification concepts](https://docs.aws.amazon.com/codestar-notifications/latest/userguide/concepts.html#concepts-api) in the Developer Tools Console User Guide.
        public var eventTypeId: Swift.String?
        /// The name of the event.
        public var eventTypeName: Swift.String?
        /// The resource type of the event.
        public var resourceType: Swift.String?
        /// The name of the service for which the event applies.
        public var serviceName: Swift.String?

        public init(
            eventTypeId: Swift.String? = nil,
            eventTypeName: Swift.String? = nil,
            resourceType: Swift.String? = nil,
            serviceName: Swift.String? = nil
        )
        {
            self.eventTypeId = eventTypeId
            self.eventTypeName = eventTypeName
            self.resourceType = resourceType
            self.serviceName = serviceName
        }
    }
}

extension CodestarnotificationsClientTypes {

    public enum TargetStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case active
        case deactivated
        case inactive
        case pending
        case unreachable
        case sdkUnknown(Swift.String)

        public static var allCases: [TargetStatus] {
            return [
                .active,
                .deactivated,
                .inactive,
                .pending,
                .unreachable
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .active: return "ACTIVE"
            case .deactivated: return "DEACTIVATED"
            case .inactive: return "INACTIVE"
            case .pending: return "PENDING"
            case .unreachable: return "UNREACHABLE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodestarnotificationsClientTypes {

    /// Information about the targets specified for a notification rule.
    public struct TargetSummary: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the Chatbot topic or Chatbot client.
        public var targetAddress: Swift.String?
        /// The status of the target.
        public var targetStatus: CodestarnotificationsClientTypes.TargetStatus?
        /// The type of the target (for example, SNS).
        ///
        /// * Chatbot topics are specified as SNS.
        ///
        /// * Chatbot clients are specified as AWSChatbotSlack.
        public var targetType: Swift.String?

        public init(
            targetAddress: Swift.String? = nil,
            targetStatus: CodestarnotificationsClientTypes.TargetStatus? = nil,
            targetType: Swift.String? = nil
        )
        {
            self.targetAddress = targetAddress
            self.targetStatus = targetStatus
            self.targetType = targetType
        }
    }
}

extension CodestarnotificationsClientTypes.TargetSummary: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "TargetSummary(targetStatus: \(Swift.String(describing: targetStatus)), targetType: \(Swift.String(describing: targetType)), targetAddress: \"CONTENT_REDACTED\")"}
}

public struct DescribeNotificationRuleOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the notification rule.
    /// This member is required.
    public var arn: Swift.String?
    /// The name or email alias of the person who created the notification rule.
    public var createdBy: Swift.String?
    /// The date and time the notification rule was created, in timestamp format.
    public var createdTimestamp: Foundation.Date?
    /// The level of detail included in the notifications for this resource. BASIC will include only the contents of the event as it would appear in Amazon CloudWatch. FULL will include any supplemental information provided by CodeStar Notifications and/or the service for the resource for which the notification is created.
    public var detailType: CodestarnotificationsClientTypes.DetailType?
    /// A list of the event types associated with the notification rule.
    public var eventTypes: [CodestarnotificationsClientTypes.EventTypeSummary]?
    /// The date and time the notification rule was most recently updated, in timestamp format.
    public var lastModifiedTimestamp: Foundation.Date?
    /// The name of the notification rule.
    public var name: Swift.String?
    /// The Amazon Resource Name (ARN) of the resource associated with the notification rule.
    public var resource: Swift.String?
    /// The status of the notification rule. Valid statuses are on (sending notifications) or off (not sending notifications).
    public var status: CodestarnotificationsClientTypes.NotificationRuleStatus?
    /// The tags associated with the notification rule.
    public var tags: [Swift.String: Swift.String]?
    /// A list of the Chatbot topics and Chatbot clients associated with the notification rule.
    public var targets: [CodestarnotificationsClientTypes.TargetSummary]?

    public init(
        arn: Swift.String? = nil,
        createdBy: Swift.String? = nil,
        createdTimestamp: Foundation.Date? = nil,
        detailType: CodestarnotificationsClientTypes.DetailType? = nil,
        eventTypes: [CodestarnotificationsClientTypes.EventTypeSummary]? = nil,
        lastModifiedTimestamp: Foundation.Date? = nil,
        name: Swift.String? = nil,
        resource: Swift.String? = nil,
        status: CodestarnotificationsClientTypes.NotificationRuleStatus? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        targets: [CodestarnotificationsClientTypes.TargetSummary]? = nil
    )
    {
        self.arn = arn
        self.createdBy = createdBy
        self.createdTimestamp = createdTimestamp
        self.detailType = detailType
        self.eventTypes = eventTypes
        self.lastModifiedTimestamp = lastModifiedTimestamp
        self.name = name
        self.resource = resource
        self.status = status
        self.tags = tags
        self.targets = targets
    }
}

extension DescribeNotificationRuleOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "DescribeNotificationRuleOutput(arn: \(Swift.String(describing: arn)), createdBy: \(Swift.String(describing: createdBy)), createdTimestamp: \(Swift.String(describing: createdTimestamp)), detailType: \(Swift.String(describing: detailType)), eventTypes: \(Swift.String(describing: eventTypes)), lastModifiedTimestamp: \(Swift.String(describing: lastModifiedTimestamp)), resource: \(Swift.String(describing: resource)), status: \(Swift.String(describing: status)), tags: \(Swift.String(describing: tags)), targets: \(Swift.String(describing: targets)), name: \"CONTENT_REDACTED\")"}
}

/// The value for the enumeration token used in the request to return the next batch of the results is not valid.
public struct InvalidNextTokenException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidNextTokenException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CodestarnotificationsClientTypes {

    public enum ListEventTypesFilterName: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case resourceType
        case serviceName
        case sdkUnknown(Swift.String)

        public static var allCases: [ListEventTypesFilterName] {
            return [
                .resourceType,
                .serviceName
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .resourceType: return "RESOURCE_TYPE"
            case .serviceName: return "SERVICE_NAME"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodestarnotificationsClientTypes {

    /// Information about a filter to apply to the list of returned event types. You can filter by resource type or service name.
    public struct ListEventTypesFilter: Swift.Sendable {
        /// The system-generated name of the filter type you want to filter by.
        /// This member is required.
        public var name: CodestarnotificationsClientTypes.ListEventTypesFilterName?
        /// The name of the resource type (for example, pipeline) or service name (for example, CodePipeline) that you want to filter by.
        /// This member is required.
        public var value: Swift.String?

        public init(
            name: CodestarnotificationsClientTypes.ListEventTypesFilterName? = nil,
            value: Swift.String? = nil
        )
        {
            self.name = name
            self.value = value
        }
    }
}

public struct ListEventTypesInput: Swift.Sendable {
    /// The filters to use to return information by service or resource type.
    public var filters: [CodestarnotificationsClientTypes.ListEventTypesFilter]?
    /// A non-negative integer used to limit the number of returned results. The default number is 50. The maximum number of results that can be returned is 100.
    public var maxResults: Swift.Int?
    /// An enumeration token that, when provided in a request, returns the next batch of the results.
    public var nextToken: Swift.String?

    public init(
        filters: [CodestarnotificationsClientTypes.ListEventTypesFilter]? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListEventTypesOutput: Swift.Sendable {
    /// Information about each event, including service name, resource type, event ID, and event name.
    public var eventTypes: [CodestarnotificationsClientTypes.EventTypeSummary]?
    /// An enumeration token that can be used in a request to return the next batch of the results.
    public var nextToken: Swift.String?

    public init(
        eventTypes: [CodestarnotificationsClientTypes.EventTypeSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.eventTypes = eventTypes
        self.nextToken = nextToken
    }
}

extension CodestarnotificationsClientTypes {

    public enum ListNotificationRulesFilterName: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case createdBy
        case eventTypeId
        case resource
        case targetAddress
        case sdkUnknown(Swift.String)

        public static var allCases: [ListNotificationRulesFilterName] {
            return [
                .createdBy,
                .eventTypeId,
                .resource,
                .targetAddress
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .createdBy: return "CREATED_BY"
            case .eventTypeId: return "EVENT_TYPE_ID"
            case .resource: return "RESOURCE"
            case .targetAddress: return "TARGET_ADDRESS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodestarnotificationsClientTypes {

    /// Information about a filter to apply to the list of returned notification rules. You can filter by event type, owner, resource, or target.
    public struct ListNotificationRulesFilter: Swift.Sendable {
        /// The name of the attribute you want to use to filter the returned notification rules.
        /// This member is required.
        public var name: CodestarnotificationsClientTypes.ListNotificationRulesFilterName?
        /// The value of the attribute you want to use to filter the returned notification rules. For example, if you specify filtering by RESOURCE in Name, you might specify the ARN of a pipeline in CodePipeline for the value.
        /// This member is required.
        public var value: Swift.String?

        public init(
            name: CodestarnotificationsClientTypes.ListNotificationRulesFilterName? = nil,
            value: Swift.String? = nil
        )
        {
            self.name = name
            self.value = value
        }
    }
}

public struct ListNotificationRulesInput: Swift.Sendable {
    /// The filters to use to return information by service or resource type. For valid values, see [ListNotificationRulesFilter]. A filter with the same name can appear more than once when used with OR statements. Filters with different names should be applied with AND statements.
    public var filters: [CodestarnotificationsClientTypes.ListNotificationRulesFilter]?
    /// A non-negative integer used to limit the number of returned results. The maximum number of results that can be returned is 100.
    public var maxResults: Swift.Int?
    /// An enumeration token that, when provided in a request, returns the next batch of the results.
    public var nextToken: Swift.String?

    public init(
        filters: [CodestarnotificationsClientTypes.ListNotificationRulesFilter]? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension CodestarnotificationsClientTypes {

    /// Information about a specified notification rule.
    public struct NotificationRuleSummary: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the notification rule.
        public var arn: Swift.String?
        /// The unique ID of the notification rule.
        public var id: Swift.String?

        public init(
            arn: Swift.String? = nil,
            id: Swift.String? = nil
        )
        {
            self.arn = arn
            self.id = id
        }
    }
}

public struct ListNotificationRulesOutput: Swift.Sendable {
    /// An enumeration token that can be used in a request to return the next batch of the results.
    public var nextToken: Swift.String?
    /// The list of notification rules for the Amazon Web Services account, by Amazon Resource Name (ARN) and ID.
    public var notificationRules: [CodestarnotificationsClientTypes.NotificationRuleSummary]?

    public init(
        nextToken: Swift.String? = nil,
        notificationRules: [CodestarnotificationsClientTypes.NotificationRuleSummary]? = nil
    )
    {
        self.nextToken = nextToken
        self.notificationRules = notificationRules
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) for the notification rule.
    /// This member is required.
    public var arn: Swift.String?

    public init(
        arn: Swift.String? = nil
    )
    {
        self.arn = arn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// The tags associated with the notification rule.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

extension CodestarnotificationsClientTypes {

    public enum ListTargetsFilterName: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case targetAddress
        case targetStatus
        case targetType
        case sdkUnknown(Swift.String)

        public static var allCases: [ListTargetsFilterName] {
            return [
                .targetAddress,
                .targetStatus,
                .targetType
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .targetAddress: return "TARGET_ADDRESS"
            case .targetStatus: return "TARGET_STATUS"
            case .targetType: return "TARGET_TYPE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodestarnotificationsClientTypes {

    /// Information about a filter to apply to the list of returned targets. You can filter by target type, address, or status. For example, to filter results to notification rules that have active Chatbot topics as targets, you could specify a ListTargetsFilter Name as TargetType and a Value of SNS, and a Name of TARGET_STATUS and a Value of ACTIVE.
    public struct ListTargetsFilter: Swift.Sendable {
        /// The name of the attribute you want to use to filter the returned targets.
        /// This member is required.
        public var name: CodestarnotificationsClientTypes.ListTargetsFilterName?
        /// The value of the attribute you want to use to filter the returned targets. For example, if you specify SNS for the Target type, you could specify an Amazon Resource Name (ARN) for a topic as the value.
        /// This member is required.
        public var value: Swift.String?

        public init(
            name: CodestarnotificationsClientTypes.ListTargetsFilterName? = nil,
            value: Swift.String? = nil
        )
        {
            self.name = name
            self.value = value
        }
    }
}

public struct ListTargetsInput: Swift.Sendable {
    /// The filters to use to return information by service or resource type. Valid filters include target type, target address, and target status. A filter with the same name can appear more than once when used with OR statements. Filters with different names should be applied with AND statements.
    public var filters: [CodestarnotificationsClientTypes.ListTargetsFilter]?
    /// A non-negative integer used to limit the number of returned results. The maximum number of results that can be returned is 100.
    public var maxResults: Swift.Int?
    /// An enumeration token that, when provided in a request, returns the next batch of the results.
    public var nextToken: Swift.String?

    public init(
        filters: [CodestarnotificationsClientTypes.ListTargetsFilter]? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListTargetsOutput: Swift.Sendable {
    /// An enumeration token that can be used in a request to return the next batch of results.
    public var nextToken: Swift.String?
    /// The list of notification rule targets.
    public var targets: [CodestarnotificationsClientTypes.TargetSummary]?

    public init(
        nextToken: Swift.String? = nil,
        targets: [CodestarnotificationsClientTypes.TargetSummary]? = nil
    )
    {
        self.nextToken = nextToken
        self.targets = targets
    }
}

public struct SubscribeInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the notification rule for which you want to create the association.
    /// This member is required.
    public var arn: Swift.String?
    /// An enumeration token that, when provided in a request, returns the next batch of the results.
    public var clientRequestToken: Swift.String?
    /// Information about the Chatbot topics or Chatbot clients associated with a notification rule.
    /// This member is required.
    public var target: CodestarnotificationsClientTypes.Target?

    public init(
        arn: Swift.String? = nil,
        clientRequestToken: Swift.String? = nil,
        target: CodestarnotificationsClientTypes.Target? = nil
    )
    {
        self.arn = arn
        self.clientRequestToken = clientRequestToken
        self.target = target
    }
}

public struct SubscribeOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the notification rule for which you have created assocations.
    public var arn: Swift.String?

    public init(
        arn: Swift.String? = nil
    )
    {
        self.arn = arn
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the notification rule to tag.
    /// This member is required.
    public var arn: Swift.String?
    /// The list of tags to associate with the resource. Tag key names cannot start with "aws".
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        arn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.arn = arn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {
    /// The list of tags associated with the resource.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

public struct UnsubscribeInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the notification rule.
    /// This member is required.
    public var arn: Swift.String?
    /// The ARN of the Chatbot topic to unsubscribe from the notification rule.
    /// This member is required.
    public var targetAddress: Swift.String?

    public init(
        arn: Swift.String? = nil,
        targetAddress: Swift.String? = nil
    )
    {
        self.arn = arn
        self.targetAddress = targetAddress
    }
}

extension UnsubscribeInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "UnsubscribeInput(arn: \(Swift.String(describing: arn)), targetAddress: \"CONTENT_REDACTED\")"}
}

public struct UnsubscribeOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the the notification rule from which you have removed a subscription.
    /// This member is required.
    public var arn: Swift.String?

    public init(
        arn: Swift.String? = nil
    )
    {
        self.arn = arn
    }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the notification rule from which to remove the tags.
    /// This member is required.
    public var arn: Swift.String?
    /// The key names of the tags to remove.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        arn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.arn = arn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateNotificationRuleInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the notification rule.
    /// This member is required.
    public var arn: Swift.String?
    /// The level of detail to include in the notifications for this resource. BASIC will include only the contents of the event as it would appear in Amazon CloudWatch. FULL will include any supplemental information provided by CodeStar Notifications and/or the service for the resource for which the notification is created.
    public var detailType: CodestarnotificationsClientTypes.DetailType?
    /// A list of event types associated with this notification rule. For a complete list of event types and IDs, see [Notification concepts](https://docs.aws.amazon.com/codestar-notifications/latest/userguide/concepts.html#concepts-api) in the Developer Tools Console User Guide.
    public var eventTypeIds: [Swift.String]?
    /// The name of the notification rule.
    public var name: Swift.String?
    /// The status of the notification rule. Valid statuses include enabled (sending notifications) or disabled (not sending notifications).
    public var status: CodestarnotificationsClientTypes.NotificationRuleStatus?
    /// The address and type of the targets to receive notifications from this notification rule.
    public var targets: [CodestarnotificationsClientTypes.Target]?

    public init(
        arn: Swift.String? = nil,
        detailType: CodestarnotificationsClientTypes.DetailType? = nil,
        eventTypeIds: [Swift.String]? = nil,
        name: Swift.String? = nil,
        status: CodestarnotificationsClientTypes.NotificationRuleStatus? = nil,
        targets: [CodestarnotificationsClientTypes.Target]? = nil
    )
    {
        self.arn = arn
        self.detailType = detailType
        self.eventTypeIds = eventTypeIds
        self.name = name
        self.status = status
        self.targets = targets
    }
}

extension UpdateNotificationRuleInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "UpdateNotificationRuleInput(arn: \(Swift.String(describing: arn)), detailType: \(Swift.String(describing: detailType)), eventTypeIds: \(Swift.String(describing: eventTypeIds)), status: \(Swift.String(describing: status)), targets: \(Swift.String(describing: targets)), name: \"CONTENT_REDACTED\")"}
}

public struct UpdateNotificationRuleOutput: Swift.Sendable {

    public init() { }
}

extension CreateNotificationRuleInput {

    static func urlPathProvider(_ value: CreateNotificationRuleInput) -> Swift.String? {
        return "/createNotificationRule"
    }
}

extension DeleteNotificationRuleInput {

    static func urlPathProvider(_ value: DeleteNotificationRuleInput) -> Swift.String? {
        return "/deleteNotificationRule"
    }
}

extension DeleteTargetInput {

    static func urlPathProvider(_ value: DeleteTargetInput) -> Swift.String? {
        return "/deleteTarget"
    }
}

extension DescribeNotificationRuleInput {

    static func urlPathProvider(_ value: DescribeNotificationRuleInput) -> Swift.String? {
        return "/describeNotificationRule"
    }
}

extension ListEventTypesInput {

    static func urlPathProvider(_ value: ListEventTypesInput) -> Swift.String? {
        return "/listEventTypes"
    }
}

extension ListNotificationRulesInput {

    static func urlPathProvider(_ value: ListNotificationRulesInput) -> Swift.String? {
        return "/listNotificationRules"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        return "/listTagsForResource"
    }
}

extension ListTargetsInput {

    static func urlPathProvider(_ value: ListTargetsInput) -> Swift.String? {
        return "/listTargets"
    }
}

extension SubscribeInput {

    static func urlPathProvider(_ value: SubscribeInput) -> Swift.String? {
        return "/subscribe"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        return "/tagResource"
    }
}

extension UnsubscribeInput {

    static func urlPathProvider(_ value: UnsubscribeInput) -> Swift.String? {
        return "/unsubscribe"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let arn = value.arn else {
            return nil
        }
        return "/untagResource/\(arn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension UpdateNotificationRuleInput {

    static func urlPathProvider(_ value: UpdateNotificationRuleInput) -> Swift.String? {
        return "/updateNotificationRule"
    }
}

extension CreateNotificationRuleInput {

    static func write(value: CreateNotificationRuleInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientRequestToken"].write(value.clientRequestToken)
        try writer["DetailType"].write(value.detailType)
        try writer["EventTypeIds"].writeList(value.eventTypeIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Name"].write(value.name)
        try writer["Resource"].write(value.resource)
        try writer["Status"].write(value.status)
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["Targets"].writeList(value.targets, memberWritingClosure: CodestarnotificationsClientTypes.Target.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DeleteNotificationRuleInput {

    static func write(value: DeleteNotificationRuleInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Arn"].write(value.arn)
    }
}

extension DeleteTargetInput {

    static func write(value: DeleteTargetInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ForceUnsubscribeAll"].write(value.forceUnsubscribeAll)
        try writer["TargetAddress"].write(value.targetAddress)
    }
}

extension DescribeNotificationRuleInput {

    static func write(value: DescribeNotificationRuleInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Arn"].write(value.arn)
    }
}

extension ListEventTypesInput {

    static func write(value: ListEventTypesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Filters"].writeList(value.filters, memberWritingClosure: CodestarnotificationsClientTypes.ListEventTypesFilter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListNotificationRulesInput {

    static func write(value: ListNotificationRulesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Filters"].writeList(value.filters, memberWritingClosure: CodestarnotificationsClientTypes.ListNotificationRulesFilter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListTagsForResourceInput {

    static func write(value: ListTagsForResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Arn"].write(value.arn)
    }
}

extension ListTargetsInput {

    static func write(value: ListTargetsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Filters"].writeList(value.filters, memberWritingClosure: CodestarnotificationsClientTypes.ListTargetsFilter.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension SubscribeInput {

    static func write(value: SubscribeInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Arn"].write(value.arn)
        try writer["ClientRequestToken"].write(value.clientRequestToken)
        try writer["Target"].write(value.target, with: CodestarnotificationsClientTypes.Target.write(value:to:))
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Arn"].write(value.arn)
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension UnsubscribeInput {

    static func write(value: UnsubscribeInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Arn"].write(value.arn)
        try writer["TargetAddress"].write(value.targetAddress)
    }
}

extension UpdateNotificationRuleInput {

    static func write(value: UpdateNotificationRuleInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Arn"].write(value.arn)
        try writer["DetailType"].write(value.detailType)
        try writer["EventTypeIds"].writeList(value.eventTypeIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Name"].write(value.name)
        try writer["Status"].write(value.status)
        try writer["Targets"].writeList(value.targets, memberWritingClosure: CodestarnotificationsClientTypes.Target.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateNotificationRuleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateNotificationRuleOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateNotificationRuleOutput()
        value.arn = try reader["Arn"].readIfPresent()
        return value
    }
}

extension DeleteNotificationRuleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteNotificationRuleOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteNotificationRuleOutput()
        value.arn = try reader["Arn"].readIfPresent()
        return value
    }
}

extension DeleteTargetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteTargetOutput {
        return DeleteTargetOutput()
    }
}

extension DescribeNotificationRuleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeNotificationRuleOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeNotificationRuleOutput()
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.createdBy = try reader["CreatedBy"].readIfPresent()
        value.createdTimestamp = try reader["CreatedTimestamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.detailType = try reader["DetailType"].readIfPresent()
        value.eventTypes = try reader["EventTypes"].readListIfPresent(memberReadingClosure: CodestarnotificationsClientTypes.EventTypeSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.lastModifiedTimestamp = try reader["LastModifiedTimestamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.name = try reader["Name"].readIfPresent()
        value.resource = try reader["Resource"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.targets = try reader["Targets"].readListIfPresent(memberReadingClosure: CodestarnotificationsClientTypes.TargetSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListEventTypesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListEventTypesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListEventTypesOutput()
        value.eventTypes = try reader["EventTypes"].readListIfPresent(memberReadingClosure: CodestarnotificationsClientTypes.EventTypeSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListNotificationRulesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListNotificationRulesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListNotificationRulesOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.notificationRules = try reader["NotificationRules"].readListIfPresent(memberReadingClosure: CodestarnotificationsClientTypes.NotificationRuleSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension ListTargetsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTargetsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTargetsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.targets = try reader["Targets"].readListIfPresent(memberReadingClosure: CodestarnotificationsClientTypes.TargetSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension SubscribeOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SubscribeOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = SubscribeOutput()
        value.arn = try reader["Arn"].readIfPresent()
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = TagResourceOutput()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension UnsubscribeOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UnsubscribeOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UnsubscribeOutput()
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        return value
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateNotificationRuleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateNotificationRuleOutput {
        return UpdateNotificationRuleOutput()
    }
}

enum CreateNotificationRuleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConcurrentModificationException": return try ConcurrentModificationException.makeError(baseError: baseError)
            case "ConfigurationException": return try ConfigurationException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ResourceAlreadyExistsException": return try ResourceAlreadyExistsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteNotificationRuleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConcurrentModificationException": return try ConcurrentModificationException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteTargetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeNotificationRuleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListEventTypesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListNotificationRulesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTargetsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SubscribeOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConfigurationException": return try ConfigurationException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConcurrentModificationException": return try ConcurrentModificationException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UnsubscribeOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConcurrentModificationException": return try ConcurrentModificationException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateNotificationRuleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConfigurationException": return try ConfigurationException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ConfigurationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConfigurationException {
        let reader = baseError.errorBodyReader
        var value = ConfigurationException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConcurrentModificationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConcurrentModificationException {
        let reader = baseError.errorBodyReader
        var value = ConcurrentModificationException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LimitExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> LimitExceededException {
        let reader = baseError.errorBodyReader
        var value = LimitExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceAlreadyExistsException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceAlreadyExistsException {
        let reader = baseError.errorBodyReader
        var value = ResourceAlreadyExistsException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidNextTokenException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InvalidNextTokenException {
        let reader = baseError.errorBodyReader
        var value = InvalidNextTokenException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CodestarnotificationsClientTypes.EventTypeSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodestarnotificationsClientTypes.EventTypeSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodestarnotificationsClientTypes.EventTypeSummary()
        value.eventTypeId = try reader["EventTypeId"].readIfPresent()
        value.serviceName = try reader["ServiceName"].readIfPresent()
        value.eventTypeName = try reader["EventTypeName"].readIfPresent()
        value.resourceType = try reader["ResourceType"].readIfPresent()
        return value
    }
}

extension CodestarnotificationsClientTypes.TargetSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodestarnotificationsClientTypes.TargetSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodestarnotificationsClientTypes.TargetSummary()
        value.targetAddress = try reader["TargetAddress"].readIfPresent()
        value.targetType = try reader["TargetType"].readIfPresent()
        value.targetStatus = try reader["TargetStatus"].readIfPresent()
        return value
    }
}

extension CodestarnotificationsClientTypes.NotificationRuleSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodestarnotificationsClientTypes.NotificationRuleSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodestarnotificationsClientTypes.NotificationRuleSummary()
        value.id = try reader["Id"].readIfPresent()
        value.arn = try reader["Arn"].readIfPresent()
        return value
    }
}

extension CodestarnotificationsClientTypes.Target {

    static func write(value: CodestarnotificationsClientTypes.Target?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["TargetAddress"].write(value.targetAddress)
        try writer["TargetType"].write(value.targetType)
    }
}

extension CodestarnotificationsClientTypes.ListEventTypesFilter {

    static func write(value: CodestarnotificationsClientTypes.ListEventTypesFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
        try writer["Value"].write(value.value)
    }
}

extension CodestarnotificationsClientTypes.ListNotificationRulesFilter {

    static func write(value: CodestarnotificationsClientTypes.ListNotificationRulesFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
        try writer["Value"].write(value.value)
    }
}

extension CodestarnotificationsClientTypes.ListTargetsFilter {

    static func write(value: CodestarnotificationsClientTypes.ListTargetsFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
        try writer["Value"].write(value.value)
    }
}

public enum CodestarnotificationsClientTypes {}
