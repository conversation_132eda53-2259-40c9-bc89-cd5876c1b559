//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHT<PERSON><PERSON>I.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem

/// Access to the specified resource was denied.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension DataBrewClientTypes {

    /// Configuration of statistics that are allowed to be run on columns that contain detected entities. When undefined, no statistics will be computed on columns that contain detected entities.
    public struct AllowedStatistics: Swift.Sendable {
        /// One or more column statistics to allow for columns that contain detected entities.
        /// This member is required.
        public var statistics: [Swift.String]?

        public init(
            statistics: [Swift.String]? = nil
        )
        {
            self.statistics = statistics
        }
    }
}

extension DataBrewClientTypes {

    public enum AnalyticsMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disable
        case enable
        case sdkUnknown(Swift.String)

        public static var allCases: [AnalyticsMode] {
            return [
                .disable,
                .enable
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disable: return "DISABLE"
            case .enable: return "ENABLE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Updating or deleting a resource can cause an inconsistent state.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// One or more resources can't be found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The input parameters for this request failed validation.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct BatchDeleteRecipeVersionInput: Swift.Sendable {
    /// The name of the recipe whose versions are to be deleted.
    /// This member is required.
    public var name: Swift.String?
    /// An array of version identifiers, for the recipe versions to be deleted. You can specify numeric versions (X.Y) or LATEST_WORKING. LATEST_PUBLISHED is not supported.
    /// This member is required.
    public var recipeVersions: [Swift.String]?

    public init(
        name: Swift.String? = nil,
        recipeVersions: [Swift.String]? = nil
    )
    {
        self.name = name
        self.recipeVersions = recipeVersions
    }
}

extension DataBrewClientTypes {

    /// Represents any errors encountered when attempting to delete multiple recipe versions.
    public struct RecipeVersionErrorDetail: Swift.Sendable {
        /// The HTTP status code for the error.
        public var errorCode: Swift.String?
        /// The text of the error message.
        public var errorMessage: Swift.String?
        /// The identifier for the recipe version associated with this error.
        public var recipeVersion: Swift.String?

        public init(
            errorCode: Swift.String? = nil,
            errorMessage: Swift.String? = nil,
            recipeVersion: Swift.String? = nil
        )
        {
            self.errorCode = errorCode
            self.errorMessage = errorMessage
            self.recipeVersion = recipeVersion
        }
    }
}

public struct BatchDeleteRecipeVersionOutput: Swift.Sendable {
    /// Errors, if any, that occurred while attempting to delete the recipe versions.
    public var errors: [DataBrewClientTypes.RecipeVersionErrorDetail]?
    /// The name of the recipe that was modified.
    /// This member is required.
    public var name: Swift.String?

    public init(
        errors: [DataBrewClientTypes.RecipeVersionErrorDetail]? = nil,
        name: Swift.String? = nil
    )
    {
        self.errors = errors
        self.name = name
    }
}

/// A service quota is exceeded.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceQuotaExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension DataBrewClientTypes {

    public enum InputFormat: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case csv
        case excel
        case json
        case orc
        case parquet
        case sdkUnknown(Swift.String)

        public static var allCases: [InputFormat] {
            return [
                .csv,
                .excel,
                .json,
                .orc,
                .parquet
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .csv: return "CSV"
            case .excel: return "EXCEL"
            case .json: return "JSON"
            case .orc: return "ORC"
            case .parquet: return "PARQUET"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataBrewClientTypes {

    /// Represents a set of options that define how DataBrew will read a comma-separated value (CSV) file when creating a dataset from that file.
    public struct CsvOptions: Swift.Sendable {
        /// A single character that specifies the delimiter being used in the CSV file.
        public var delimiter: Swift.String?
        /// A variable that specifies whether the first row in the file is parsed as the header. If this value is false, column names are auto-generated.
        public var headerRow: Swift.Bool?

        public init(
            delimiter: Swift.String? = nil,
            headerRow: Swift.Bool? = nil
        )
        {
            self.delimiter = delimiter
            self.headerRow = headerRow
        }
    }
}

extension DataBrewClientTypes {

    /// Represents a set of options that define how DataBrew will interpret a Microsoft Excel file when creating a dataset from that file.
    public struct ExcelOptions: Swift.Sendable {
        /// A variable that specifies whether the first row in the file is parsed as the header. If this value is false, column names are auto-generated.
        public var headerRow: Swift.Bool?
        /// One or more sheet numbers in the Excel file that will be included in the dataset.
        public var sheetIndexes: [Swift.Int]?
        /// One or more named sheets in the Excel file that will be included in the dataset.
        public var sheetNames: [Swift.String]?

        public init(
            headerRow: Swift.Bool? = nil,
            sheetIndexes: [Swift.Int]? = nil,
            sheetNames: [Swift.String]? = nil
        )
        {
            self.headerRow = headerRow
            self.sheetIndexes = sheetIndexes
            self.sheetNames = sheetNames
        }
    }
}

extension DataBrewClientTypes {

    /// Represents the JSON-specific options that define how input is to be interpreted by Glue DataBrew.
    public struct JsonOptions: Swift.Sendable {
        /// A value that specifies whether JSON input contains embedded new line characters.
        public var multiLine: Swift.Bool

        public init(
            multiLine: Swift.Bool = false
        )
        {
            self.multiLine = multiLine
        }
    }
}

extension DataBrewClientTypes {

    /// Represents a set of options that define the structure of either comma-separated value (CSV), Excel, or JSON input.
    public struct FormatOptions: Swift.Sendable {
        /// Options that define how CSV input is to be interpreted by DataBrew.
        public var csv: DataBrewClientTypes.CsvOptions?
        /// Options that define how Excel input is to be interpreted by DataBrew.
        public var excel: DataBrewClientTypes.ExcelOptions?
        /// Options that define how JSON input is to be interpreted by DataBrew.
        public var json: DataBrewClientTypes.JsonOptions?

        public init(
            csv: DataBrewClientTypes.CsvOptions? = nil,
            excel: DataBrewClientTypes.ExcelOptions? = nil,
            json: DataBrewClientTypes.JsonOptions? = nil
        )
        {
            self.csv = csv
            self.excel = excel
            self.json = json
        }
    }
}

extension DataBrewClientTypes {

    /// Represents an Amazon S3 location (bucket name, bucket owner, and object key) where DataBrew can read input data, or write output from a job.
    public struct S3Location: Swift.Sendable {
        /// The Amazon S3 bucket name.
        /// This member is required.
        public var bucket: Swift.String?
        /// The Amazon Web Services account ID of the bucket owner.
        public var bucketOwner: Swift.String?
        /// The unique name of the object in the bucket.
        public var key: Swift.String?

        public init(
            bucket: Swift.String? = nil,
            bucketOwner: Swift.String? = nil,
            key: Swift.String? = nil
        )
        {
            self.bucket = bucket
            self.bucketOwner = bucketOwner
            self.key = key
        }
    }
}

extension DataBrewClientTypes {

    /// Connection information for dataset input files stored in a database.
    public struct DatabaseInputDefinition: Swift.Sendable {
        /// The table within the target database.
        public var databaseTableName: Swift.String?
        /// The Glue Connection that stores the connection information for the target database.
        /// This member is required.
        public var glueConnectionName: Swift.String?
        /// Custom SQL to run against the provided Glue connection. This SQL will be used as the input for DataBrew projects and jobs.
        public var queryString: Swift.String?
        /// Represents an Amazon S3 location (bucket name, bucket owner, and object key) where DataBrew can read input data, or write output from a job.
        public var tempDirectory: DataBrewClientTypes.S3Location?

        public init(
            databaseTableName: Swift.String? = nil,
            glueConnectionName: Swift.String? = nil,
            queryString: Swift.String? = nil,
            tempDirectory: DataBrewClientTypes.S3Location? = nil
        )
        {
            self.databaseTableName = databaseTableName
            self.glueConnectionName = glueConnectionName
            self.queryString = queryString
            self.tempDirectory = tempDirectory
        }
    }
}

extension DataBrewClientTypes {

    /// Represents how metadata stored in the Glue Data Catalog is defined in a DataBrew dataset.
    public struct DataCatalogInputDefinition: Swift.Sendable {
        /// The unique identifier of the Amazon Web Services account that holds the Data Catalog that stores the data.
        public var catalogId: Swift.String?
        /// The name of a database in the Data Catalog.
        /// This member is required.
        public var databaseName: Swift.String?
        /// The name of a database table in the Data Catalog. This table corresponds to a DataBrew dataset.
        /// This member is required.
        public var tableName: Swift.String?
        /// Represents an Amazon location where DataBrew can store intermediate results.
        public var tempDirectory: DataBrewClientTypes.S3Location?

        public init(
            catalogId: Swift.String? = nil,
            databaseName: Swift.String? = nil,
            tableName: Swift.String? = nil,
            tempDirectory: DataBrewClientTypes.S3Location? = nil
        )
        {
            self.catalogId = catalogId
            self.databaseName = databaseName
            self.tableName = tableName
            self.tempDirectory = tempDirectory
        }
    }
}

extension DataBrewClientTypes {

    /// Contains additional resource information needed for specific datasets.
    public struct Metadata: Swift.Sendable {
        /// The Amazon Resource Name (ARN) associated with the dataset. Currently, DataBrew only supports ARNs from Amazon AppFlow.
        public var sourceArn: Swift.String?

        public init(
            sourceArn: Swift.String? = nil
        )
        {
            self.sourceArn = sourceArn
        }
    }
}

extension DataBrewClientTypes {

    /// Represents information on how DataBrew can find data, in either the Glue Data Catalog or Amazon S3.
    public struct Input: Swift.Sendable {
        /// The Glue Data Catalog parameters for the data.
        public var dataCatalogInputDefinition: DataBrewClientTypes.DataCatalogInputDefinition?
        /// Connection information for dataset input files stored in a database.
        public var databaseInputDefinition: DataBrewClientTypes.DatabaseInputDefinition?
        /// Contains additional resource information needed for specific datasets.
        public var metadata: DataBrewClientTypes.Metadata?
        /// The Amazon S3 location where the data is stored.
        public var s3InputDefinition: DataBrewClientTypes.S3Location?

        public init(
            dataCatalogInputDefinition: DataBrewClientTypes.DataCatalogInputDefinition? = nil,
            databaseInputDefinition: DataBrewClientTypes.DatabaseInputDefinition? = nil,
            metadata: DataBrewClientTypes.Metadata? = nil,
            s3InputDefinition: DataBrewClientTypes.S3Location? = nil
        )
        {
            self.dataCatalogInputDefinition = dataCatalogInputDefinition
            self.databaseInputDefinition = databaseInputDefinition
            self.metadata = metadata
            self.s3InputDefinition = s3InputDefinition
        }
    }
}

extension DataBrewClientTypes {

    public enum Order: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case ascending
        case descending
        case sdkUnknown(Swift.String)

        public static var allCases: [Order] {
            return [
                .ascending,
                .descending
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .ascending: return "ASCENDING"
            case .descending: return "DESCENDING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataBrewClientTypes {

    public enum OrderedBy: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case lastModifiedDate
        case sdkUnknown(Swift.String)

        public static var allCases: [OrderedBy] {
            return [
                .lastModifiedDate
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .lastModifiedDate: return "LAST_MODIFIED_DATE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataBrewClientTypes {

    /// Represents a limit imposed on number of Amazon S3 files that should be selected for a dataset from a connected Amazon S3 path.
    public struct FilesLimit: Swift.Sendable {
        /// The number of Amazon S3 files to select.
        /// This member is required.
        public var maxFiles: Swift.Int?
        /// A criteria to use for Amazon S3 files sorting before their selection. By default uses DESCENDING order, i.e. most recent files are selected first. Another possible value is ASCENDING.
        public var order: DataBrewClientTypes.Order?
        /// A criteria to use for Amazon S3 files sorting before their selection. By default uses LAST_MODIFIED_DATE as a sorting criteria. Currently it's the only allowed value.
        public var orderedBy: DataBrewClientTypes.OrderedBy?

        public init(
            maxFiles: Swift.Int? = nil,
            order: DataBrewClientTypes.Order? = nil,
            orderedBy: DataBrewClientTypes.OrderedBy? = nil
        )
        {
            self.maxFiles = maxFiles
            self.order = order
            self.orderedBy = orderedBy
        }
    }
}

extension DataBrewClientTypes {

    /// Represents a structure for defining parameter conditions. Supported conditions are described here: [Supported conditions for dynamic datasets](https://docs.aws.amazon.com/databrew/latest/dg/datasets.multiple-files.html#conditions.for.dynamic.datasets) in the Glue DataBrew Developer Guide.
    public struct FilterExpression: Swift.Sendable {
        /// The expression which includes condition names followed by substitution variables, possibly grouped and combined with other conditions. For example, "(starts_with :prefix1 or starts_with :prefix2) and (ends_with :suffix1 or ends_with :suffix2)". Substitution variables should start with ':' symbol.
        /// This member is required.
        public var expression: Swift.String?
        /// The map of substitution variable names to their values used in this filter expression.
        /// This member is required.
        public var valuesMap: [Swift.String: Swift.String]?

        public init(
            expression: Swift.String? = nil,
            valuesMap: [Swift.String: Swift.String]? = nil
        )
        {
            self.expression = expression
            self.valuesMap = valuesMap
        }
    }
}

extension DataBrewClientTypes {

    /// Represents additional options for correct interpretation of datetime parameters used in the Amazon S3 path of a dataset.
    public struct DatetimeOptions: Swift.Sendable {
        /// Required option, that defines the datetime format used for a date parameter in the Amazon S3 path. Should use only supported datetime specifiers and separation characters, all literal a-z or A-Z characters should be escaped with single quotes. E.g. "MM.dd.yyyy-'at'-HH:mm".
        /// This member is required.
        public var format: Swift.String?
        /// Optional value for a non-US locale code, needed for correct interpretation of some date formats.
        public var localeCode: Swift.String?
        /// Optional value for a timezone offset of the datetime parameter value in the Amazon S3 path. Shouldn't be used if Format for this parameter includes timezone fields. If no offset specified, UTC is assumed.
        public var timezoneOffset: Swift.String?

        public init(
            format: Swift.String? = nil,
            localeCode: Swift.String? = nil,
            timezoneOffset: Swift.String? = nil
        )
        {
            self.format = format
            self.localeCode = localeCode
            self.timezoneOffset = timezoneOffset
        }
    }
}

extension DataBrewClientTypes {

    public enum ParameterType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case datetime
        case number
        case string
        case sdkUnknown(Swift.String)

        public static var allCases: [ParameterType] {
            return [
                .datetime,
                .number,
                .string
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .datetime: return "Datetime"
            case .number: return "Number"
            case .string: return "String"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataBrewClientTypes {

    /// Represents a dataset parameter that defines type and conditions for a parameter in the Amazon S3 path of the dataset.
    public struct DatasetParameter: Swift.Sendable {
        /// Optional boolean value that defines whether the captured value of this parameter should be used to create a new column in a dataset.
        public var createColumn: Swift.Bool
        /// Additional parameter options such as a format and a timezone. Required for datetime parameters.
        public var datetimeOptions: DataBrewClientTypes.DatetimeOptions?
        /// The optional filter expression structure to apply additional matching criteria to the parameter.
        public var filter: DataBrewClientTypes.FilterExpression?
        /// The name of the parameter that is used in the dataset's Amazon S3 path.
        /// This member is required.
        public var name: Swift.String?
        /// The type of the dataset parameter, can be one of a 'String', 'Number' or 'Datetime'.
        /// This member is required.
        public var type: DataBrewClientTypes.ParameterType?

        public init(
            createColumn: Swift.Bool = false,
            datetimeOptions: DataBrewClientTypes.DatetimeOptions? = nil,
            filter: DataBrewClientTypes.FilterExpression? = nil,
            name: Swift.String? = nil,
            type: DataBrewClientTypes.ParameterType? = nil
        )
        {
            self.createColumn = createColumn
            self.datetimeOptions = datetimeOptions
            self.filter = filter
            self.name = name
            self.type = type
        }
    }
}

extension DataBrewClientTypes {

    /// Represents a set of options that define how DataBrew selects files for a given Amazon S3 path in a dataset.
    public struct PathOptions: Swift.Sendable {
        /// If provided, this structure imposes a limit on a number of files that should be selected.
        public var filesLimit: DataBrewClientTypes.FilesLimit?
        /// If provided, this structure defines a date range for matching Amazon S3 objects based on their LastModifiedDate attribute in Amazon S3.
        public var lastModifiedDateCondition: DataBrewClientTypes.FilterExpression?
        /// A structure that maps names of parameters used in the Amazon S3 path of a dataset to their definitions.
        public var parameters: [Swift.String: DataBrewClientTypes.DatasetParameter]?

        public init(
            filesLimit: DataBrewClientTypes.FilesLimit? = nil,
            lastModifiedDateCondition: DataBrewClientTypes.FilterExpression? = nil,
            parameters: [Swift.String: DataBrewClientTypes.DatasetParameter]? = nil
        )
        {
            self.filesLimit = filesLimit
            self.lastModifiedDateCondition = lastModifiedDateCondition
            self.parameters = parameters
        }
    }
}

public struct CreateDatasetInput: Swift.Sendable {
    /// The file format of a dataset that is created from an Amazon S3 file or folder.
    public var format: DataBrewClientTypes.InputFormat?
    /// Represents a set of options that define the structure of either comma-separated value (CSV), Excel, or JSON input.
    public var formatOptions: DataBrewClientTypes.FormatOptions?
    /// Represents information on how DataBrew can find data, in either the Glue Data Catalog or Amazon S3.
    /// This member is required.
    public var input: DataBrewClientTypes.Input?
    /// The name of the dataset to be created. Valid characters are alphanumeric (A-Z, a-z, 0-9), hyphen (-), period (.), and space.
    /// This member is required.
    public var name: Swift.String?
    /// A set of options that defines how DataBrew interprets an Amazon S3 path of the dataset.
    public var pathOptions: DataBrewClientTypes.PathOptions?
    /// Metadata tags to apply to this dataset.
    public var tags: [Swift.String: Swift.String]?

    public init(
        format: DataBrewClientTypes.InputFormat? = nil,
        formatOptions: DataBrewClientTypes.FormatOptions? = nil,
        input: DataBrewClientTypes.Input? = nil,
        name: Swift.String? = nil,
        pathOptions: DataBrewClientTypes.PathOptions? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.format = format
        self.formatOptions = formatOptions
        self.input = input
        self.name = name
        self.pathOptions = pathOptions
        self.tags = tags
    }
}

public struct CreateDatasetOutput: Swift.Sendable {
    /// The name of the dataset that you created.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

extension DataBrewClientTypes {

    /// Selector of a column from a dataset for profile job configuration. One selector includes either a column name or a regular expression.
    public struct ColumnSelector: Swift.Sendable {
        /// The name of a column from a dataset.
        public var name: Swift.String?
        /// A regular expression for selecting a column from a dataset.
        public var regex: Swift.String?

        public init(
            name: Swift.String? = nil,
            regex: Swift.String? = nil
        )
        {
            self.name = name
            self.regex = regex
        }
    }
}

extension DataBrewClientTypes {

    /// Override of a particular evaluation for a profile job.
    public struct StatisticOverride: Swift.Sendable {
        /// A map that includes overrides of an evaluation’s parameters.
        /// This member is required.
        public var parameters: [Swift.String: Swift.String]?
        /// The name of an evaluation
        /// This member is required.
        public var statistic: Swift.String?

        public init(
            parameters: [Swift.String: Swift.String]? = nil,
            statistic: Swift.String? = nil
        )
        {
            self.parameters = parameters
            self.statistic = statistic
        }
    }
}

extension DataBrewClientTypes {

    /// Configuration of evaluations for a profile job. This configuration can be used to select evaluations and override the parameters of selected evaluations.
    public struct StatisticsConfiguration: Swift.Sendable {
        /// List of included evaluations. When the list is undefined, all supported evaluations will be included.
        public var includedStatistics: [Swift.String]?
        /// List of overrides for evaluations.
        public var overrides: [DataBrewClientTypes.StatisticOverride]?

        public init(
            includedStatistics: [Swift.String]? = nil,
            overrides: [DataBrewClientTypes.StatisticOverride]? = nil
        )
        {
            self.includedStatistics = includedStatistics
            self.overrides = overrides
        }
    }
}

extension DataBrewClientTypes {

    /// Configuration for column evaluations for a profile job. ColumnStatisticsConfiguration can be used to select evaluations and override parameters of evaluations for particular columns.
    public struct ColumnStatisticsConfiguration: Swift.Sendable {
        /// List of column selectors. Selectors can be used to select columns from the dataset. When selectors are undefined, configuration will be applied to all supported columns.
        public var selectors: [DataBrewClientTypes.ColumnSelector]?
        /// Configuration for evaluations. Statistics can be used to select evaluations and override parameters of evaluations.
        /// This member is required.
        public var statistics: DataBrewClientTypes.StatisticsConfiguration?

        public init(
            selectors: [DataBrewClientTypes.ColumnSelector]? = nil,
            statistics: DataBrewClientTypes.StatisticsConfiguration? = nil
        )
        {
            self.selectors = selectors
            self.statistics = statistics
        }
    }
}

extension DataBrewClientTypes {

    /// Configuration of entity detection for a profile job. When undefined, entity detection is disabled.
    public struct EntityDetectorConfiguration: Swift.Sendable {
        /// Configuration of statistics that are allowed to be run on columns that contain detected entities. When undefined, no statistics will be computed on columns that contain detected entities.
        public var allowedStatistics: [DataBrewClientTypes.AllowedStatistics]?
        /// Entity types to detect. Can be any of the following:
        ///
        /// * USA_SSN
        ///
        /// * EMAIL
        ///
        /// * USA_ITIN
        ///
        /// * USA_PASSPORT_NUMBER
        ///
        /// * PHONE_NUMBER
        ///
        /// * USA_DRIVING_LICENSE
        ///
        /// * BANK_ACCOUNT
        ///
        /// * CREDIT_CARD
        ///
        /// * IP_ADDRESS
        ///
        /// * MAC_ADDRESS
        ///
        /// * USA_DEA_NUMBER
        ///
        /// * USA_HCPCS_CODE
        ///
        /// * USA_NATIONAL_PROVIDER_IDENTIFIER
        ///
        /// * USA_NATIONAL_DRUG_CODE
        ///
        /// * USA_HEALTH_INSURANCE_CLAIM_NUMBER
        ///
        /// * USA_MEDICARE_BENEFICIARY_IDENTIFIER
        ///
        /// * USA_CPT_CODE
        ///
        /// * PERSON_NAME
        ///
        /// * DATE
        ///
        ///
        /// The Entity type group USA_ALL is also supported, and includes all of the above entity types except PERSON_NAME and DATE.
        /// This member is required.
        public var entityTypes: [Swift.String]?

        public init(
            allowedStatistics: [DataBrewClientTypes.AllowedStatistics]? = nil,
            entityTypes: [Swift.String]? = nil
        )
        {
            self.allowedStatistics = allowedStatistics
            self.entityTypes = entityTypes
        }
    }
}

extension DataBrewClientTypes {

    /// Configuration for profile jobs. Configuration can be used to select columns, do evaluations, and override default parameters of evaluations. When configuration is undefined, the profile job will apply default settings to all supported columns.
    public struct ProfileConfiguration: Swift.Sendable {
        /// List of configurations for column evaluations. ColumnStatisticsConfigurations are used to select evaluations and override parameters of evaluations for particular columns. When ColumnStatisticsConfigurations is undefined, the profile job will profile all supported columns and run all supported evaluations.
        public var columnStatisticsConfigurations: [DataBrewClientTypes.ColumnStatisticsConfiguration]?
        /// Configuration for inter-column evaluations. Configuration can be used to select evaluations and override parameters of evaluations. When configuration is undefined, the profile job will run all supported inter-column evaluations.
        public var datasetStatisticsConfiguration: DataBrewClientTypes.StatisticsConfiguration?
        /// Configuration of entity detection for a profile job. When undefined, entity detection is disabled.
        public var entityDetectorConfiguration: DataBrewClientTypes.EntityDetectorConfiguration?
        /// List of column selectors. ProfileColumns can be used to select columns from the dataset. When ProfileColumns is undefined, the profile job will profile all supported columns.
        public var profileColumns: [DataBrewClientTypes.ColumnSelector]?

        public init(
            columnStatisticsConfigurations: [DataBrewClientTypes.ColumnStatisticsConfiguration]? = nil,
            datasetStatisticsConfiguration: DataBrewClientTypes.StatisticsConfiguration? = nil,
            entityDetectorConfiguration: DataBrewClientTypes.EntityDetectorConfiguration? = nil,
            profileColumns: [DataBrewClientTypes.ColumnSelector]? = nil
        )
        {
            self.columnStatisticsConfigurations = columnStatisticsConfigurations
            self.datasetStatisticsConfiguration = datasetStatisticsConfiguration
            self.entityDetectorConfiguration = entityDetectorConfiguration
            self.profileColumns = profileColumns
        }
    }
}

extension DataBrewClientTypes {

    public enum EncryptionMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case ssekms
        case sses3
        case sdkUnknown(Swift.String)

        public static var allCases: [EncryptionMode] {
            return [
                .ssekms,
                .sses3
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .ssekms: return "SSE-KMS"
            case .sses3: return "SSE-S3"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataBrewClientTypes {

    public enum SampleMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case customRows
        case fullDataset
        case sdkUnknown(Swift.String)

        public static var allCases: [SampleMode] {
            return [
                .customRows,
                .fullDataset
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .customRows: return "CUSTOM_ROWS"
            case .fullDataset: return "FULL_DATASET"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataBrewClientTypes {

    /// A sample configuration for profile jobs only, which determines the number of rows on which the profile job is run. If a JobSample value isn't provided, the default is used. The default value is CUSTOM_ROWS for the mode parameter and 20,000 for the size parameter.
    public struct JobSample: Swift.Sendable {
        /// A value that determines whether the profile job is run on the entire dataset or a specified number of rows. This value must be one of the following:
        ///
        /// * FULL_DATASET - The profile job is run on the entire dataset.
        ///
        /// * CUSTOM_ROWS - The profile job is run on the number of rows specified in the Size parameter.
        public var mode: DataBrewClientTypes.SampleMode?
        /// The Size parameter is only required when the mode is CUSTOM_ROWS. The profile job is run on the specified number of rows. The maximum value for size is Long.MAX_VALUE. Long.MAX_VALUE = 9223372036854775807
        public var size: Swift.Int?

        public init(
            mode: DataBrewClientTypes.SampleMode? = nil,
            size: Swift.Int? = nil
        )
        {
            self.mode = mode
            self.size = size
        }
    }
}

extension DataBrewClientTypes {

    public enum LogSubscription: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disable
        case enable
        case sdkUnknown(Swift.String)

        public static var allCases: [LogSubscription] {
            return [
                .disable,
                .enable
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disable: return "DISABLE"
            case .enable: return "ENABLE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataBrewClientTypes {

    public enum ValidationMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case checkAll
        case sdkUnknown(Swift.String)

        public static var allCases: [ValidationMode] {
            return [
                .checkAll
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .checkAll: return "CHECK_ALL"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataBrewClientTypes {

    /// Configuration for data quality validation. Used to select the Rulesets and Validation Mode to be used in the profile job. When ValidationConfiguration is null, the profile job will run without data quality validation.
    public struct ValidationConfiguration: Swift.Sendable {
        /// The Amazon Resource Name (ARN) for the ruleset to be validated in the profile job. The TargetArn of the selected ruleset should be the same as the Amazon Resource Name (ARN) of the dataset that is associated with the profile job.
        /// This member is required.
        public var rulesetArn: Swift.String?
        /// Mode of data quality validation. Default mode is “CHECK_ALL” which verifies all rules defined in the selected ruleset.
        public var validationMode: DataBrewClientTypes.ValidationMode?

        public init(
            rulesetArn: Swift.String? = nil,
            validationMode: DataBrewClientTypes.ValidationMode? = nil
        )
        {
            self.rulesetArn = rulesetArn
            self.validationMode = validationMode
        }
    }
}

public struct CreateProfileJobInput: Swift.Sendable {
    /// Configuration for profile jobs. Used to select columns, do evaluations, and override default parameters of evaluations. When configuration is null, the profile job will run with default settings.
    public var configuration: DataBrewClientTypes.ProfileConfiguration?
    /// The name of the dataset that this job is to act upon.
    /// This member is required.
    public var datasetName: Swift.String?
    /// The Amazon Resource Name (ARN) of an encryption key that is used to protect the job.
    public var encryptionKeyArn: Swift.String?
    /// The encryption mode for the job, which can be one of the following:
    ///
    /// * SSE-KMS - SSE-KMS - Server-side encryption with KMS-managed keys.
    ///
    /// * SSE-S3 - Server-side encryption with keys managed by Amazon S3.
    public var encryptionMode: DataBrewClientTypes.EncryptionMode?
    /// Sample configuration for profile jobs only. Determines the number of rows on which the profile job will be executed. If a JobSample value is not provided, the default value will be used. The default value is CUSTOM_ROWS for the mode parameter and 20000 for the size parameter.
    public var jobSample: DataBrewClientTypes.JobSample?
    /// Enables or disables Amazon CloudWatch logging for the job. If logging is enabled, CloudWatch writes one log stream for each job run.
    public var logSubscription: DataBrewClientTypes.LogSubscription?
    /// The maximum number of nodes that DataBrew can use when the job processes data.
    public var maxCapacity: Swift.Int?
    /// The maximum number of times to retry the job after a job run fails.
    public var maxRetries: Swift.Int?
    /// The name of the job to be created. Valid characters are alphanumeric (A-Z, a-z, 0-9), hyphen (-), period (.), and space.
    /// This member is required.
    public var name: Swift.String?
    /// Represents an Amazon S3 location (bucket name, bucket owner, and object key) where DataBrew can read input data, or write output from a job.
    /// This member is required.
    public var outputLocation: DataBrewClientTypes.S3Location?
    /// The Amazon Resource Name (ARN) of the Identity and Access Management (IAM) role to be assumed when DataBrew runs the job.
    /// This member is required.
    public var roleArn: Swift.String?
    /// Metadata tags to apply to this job.
    public var tags: [Swift.String: Swift.String]?
    /// The job's timeout in minutes. A job that attempts to run longer than this timeout period ends with a status of TIMEOUT.
    public var timeout: Swift.Int?
    /// List of validation configurations that are applied to the profile job.
    public var validationConfigurations: [DataBrewClientTypes.ValidationConfiguration]?

    public init(
        configuration: DataBrewClientTypes.ProfileConfiguration? = nil,
        datasetName: Swift.String? = nil,
        encryptionKeyArn: Swift.String? = nil,
        encryptionMode: DataBrewClientTypes.EncryptionMode? = nil,
        jobSample: DataBrewClientTypes.JobSample? = nil,
        logSubscription: DataBrewClientTypes.LogSubscription? = nil,
        maxCapacity: Swift.Int? = 0,
        maxRetries: Swift.Int? = 0,
        name: Swift.String? = nil,
        outputLocation: DataBrewClientTypes.S3Location? = nil,
        roleArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        timeout: Swift.Int? = 0,
        validationConfigurations: [DataBrewClientTypes.ValidationConfiguration]? = nil
    )
    {
        self.configuration = configuration
        self.datasetName = datasetName
        self.encryptionKeyArn = encryptionKeyArn
        self.encryptionMode = encryptionMode
        self.jobSample = jobSample
        self.logSubscription = logSubscription
        self.maxCapacity = maxCapacity
        self.maxRetries = maxRetries
        self.name = name
        self.outputLocation = outputLocation
        self.roleArn = roleArn
        self.tags = tags
        self.timeout = timeout
        self.validationConfigurations = validationConfigurations
    }
}

public struct CreateProfileJobOutput: Swift.Sendable {
    /// The name of the job that was created.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

/// An internal service failure occurred.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension DataBrewClientTypes {

    public enum SampleType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case firstN
        case lastN
        case random
        case sdkUnknown(Swift.String)

        public static var allCases: [SampleType] {
            return [
                .firstN,
                .lastN,
                .random
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .firstN: return "FIRST_N"
            case .lastN: return "LAST_N"
            case .random: return "RANDOM"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataBrewClientTypes {

    /// Represents the sample size and sampling type for DataBrew to use for interactive data analysis.
    public struct Sample: Swift.Sendable {
        /// The number of rows in the sample.
        public var size: Swift.Int?
        /// The way in which DataBrew obtains rows from a dataset.
        /// This member is required.
        public var type: DataBrewClientTypes.SampleType?

        public init(
            size: Swift.Int? = nil,
            type: DataBrewClientTypes.SampleType? = nil
        )
        {
            self.size = size
            self.type = type
        }
    }
}

public struct CreateProjectInput: Swift.Sendable {
    /// The name of an existing dataset to associate this project with.
    /// This member is required.
    public var datasetName: Swift.String?
    /// A unique name for the new project. Valid characters are alphanumeric (A-Z, a-z, 0-9), hyphen (-), period (.), and space.
    /// This member is required.
    public var name: Swift.String?
    /// The name of an existing recipe to associate with the project.
    /// This member is required.
    public var recipeName: Swift.String?
    /// The Amazon Resource Name (ARN) of the Identity and Access Management (IAM) role to be assumed for this request.
    /// This member is required.
    public var roleArn: Swift.String?
    /// Represents the sample size and sampling type for DataBrew to use for interactive data analysis.
    public var sample: DataBrewClientTypes.Sample?
    /// Metadata tags to apply to this project.
    public var tags: [Swift.String: Swift.String]?

    public init(
        datasetName: Swift.String? = nil,
        name: Swift.String? = nil,
        recipeName: Swift.String? = nil,
        roleArn: Swift.String? = nil,
        sample: DataBrewClientTypes.Sample? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.datasetName = datasetName
        self.name = name
        self.recipeName = recipeName
        self.roleArn = roleArn
        self.sample = sample
        self.tags = tags
    }
}

public struct CreateProjectOutput: Swift.Sendable {
    /// The name of the project that you created.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

extension DataBrewClientTypes {

    /// Represents a transformation and associated parameters that are used to apply a change to a DataBrew dataset. For more information, see [Recipe actions reference](https://docs.aws.amazon.com/databrew/latest/dg/recipe-actions-reference.html).
    public struct RecipeAction: Swift.Sendable {
        /// The name of a valid DataBrew transformation to be performed on the data.
        /// This member is required.
        public var operation: Swift.String?
        /// Contextual parameters for the transformation.
        public var parameters: [Swift.String: Swift.String]?

        public init(
            operation: Swift.String? = nil,
            parameters: [Swift.String: Swift.String]? = nil
        )
        {
            self.operation = operation
            self.parameters = parameters
        }
    }
}

extension DataBrewClientTypes {

    /// Represents an individual condition that evaluates to true or false. Conditions are used with recipe actions. The action is only performed for column values where the condition evaluates to true. If a recipe requires more than one condition, then the recipe must specify multiple ConditionExpression elements. Each condition is applied to the rows in a dataset first, before the recipe action is performed.
    public struct ConditionExpression: Swift.Sendable {
        /// A specific condition to apply to a recipe action. For more information, see [Recipe structure](https://docs.aws.amazon.com/databrew/latest/dg/recipes.html#recipes.structure) in the Glue DataBrew Developer Guide.
        /// This member is required.
        public var condition: Swift.String?
        /// A column to apply this condition to.
        /// This member is required.
        public var targetColumn: Swift.String?
        /// A value that the condition must evaluate to for the condition to succeed.
        public var value: Swift.String?

        public init(
            condition: Swift.String? = nil,
            targetColumn: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.condition = condition
            self.targetColumn = targetColumn
            self.value = value
        }
    }
}

extension DataBrewClientTypes {

    /// Represents a single step from a DataBrew recipe to be performed.
    public struct RecipeStep: Swift.Sendable {
        /// The particular action to be performed in the recipe step.
        /// This member is required.
        public var action: DataBrewClientTypes.RecipeAction?
        /// One or more conditions that must be met for the recipe step to succeed. All of the conditions in the array must be met. In other words, all of the conditions must be combined using a logical AND operation.
        public var conditionExpressions: [DataBrewClientTypes.ConditionExpression]?

        public init(
            action: DataBrewClientTypes.RecipeAction? = nil,
            conditionExpressions: [DataBrewClientTypes.ConditionExpression]? = nil
        )
        {
            self.action = action
            self.conditionExpressions = conditionExpressions
        }
    }
}

public struct CreateRecipeInput: Swift.Sendable {
    /// A description for the recipe.
    public var description: Swift.String?
    /// A unique name for the recipe. Valid characters are alphanumeric (A-Z, a-z, 0-9), hyphen (-), period (.), and space.
    /// This member is required.
    public var name: Swift.String?
    /// An array containing the steps to be performed by the recipe. Each recipe step consists of one recipe action and (optionally) an array of condition expressions.
    /// This member is required.
    public var steps: [DataBrewClientTypes.RecipeStep]?
    /// Metadata tags to apply to this recipe.
    public var tags: [Swift.String: Swift.String]?

    public init(
        description: Swift.String? = nil,
        name: Swift.String? = nil,
        steps: [DataBrewClientTypes.RecipeStep]? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.description = description
        self.name = name
        self.steps = steps
        self.tags = tags
    }
}

public struct CreateRecipeOutput: Swift.Sendable {
    /// The name of the recipe that you created.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

extension DataBrewClientTypes {

    /// Represents options that specify how and where DataBrew writes the database output generated by recipe jobs.
    public struct DatabaseTableOutputOptions: Swift.Sendable {
        /// A prefix for the name of a table DataBrew will create in the database.
        /// This member is required.
        public var tableName: Swift.String?
        /// Represents an Amazon S3 location (bucket name and object key) where DataBrew can store intermediate results.
        public var tempDirectory: DataBrewClientTypes.S3Location?

        public init(
            tableName: Swift.String? = nil,
            tempDirectory: DataBrewClientTypes.S3Location? = nil
        )
        {
            self.tableName = tableName
            self.tempDirectory = tempDirectory
        }
    }
}

extension DataBrewClientTypes {

    public enum DatabaseOutputMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case newTable
        case sdkUnknown(Swift.String)

        public static var allCases: [DatabaseOutputMode] {
            return [
                .newTable
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .newTable: return "NEW_TABLE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataBrewClientTypes {

    /// Represents a JDBC database output object which defines the output destination for a DataBrew recipe job to write into.
    public struct DatabaseOutput: Swift.Sendable {
        /// Represents options that specify how and where DataBrew writes the database output generated by recipe jobs.
        /// This member is required.
        public var databaseOptions: DataBrewClientTypes.DatabaseTableOutputOptions?
        /// The output mode to write into the database. Currently supported option: NEW_TABLE.
        public var databaseOutputMode: DataBrewClientTypes.DatabaseOutputMode?
        /// The Glue connection that stores the connection information for the target database.
        /// This member is required.
        public var glueConnectionName: Swift.String?

        public init(
            databaseOptions: DataBrewClientTypes.DatabaseTableOutputOptions? = nil,
            databaseOutputMode: DataBrewClientTypes.DatabaseOutputMode? = nil,
            glueConnectionName: Swift.String? = nil
        )
        {
            self.databaseOptions = databaseOptions
            self.databaseOutputMode = databaseOutputMode
            self.glueConnectionName = glueConnectionName
        }
    }
}

extension DataBrewClientTypes {

    /// Represents options that specify how and where DataBrew writes the Amazon S3 output generated by recipe jobs.
    public struct S3TableOutputOptions: Swift.Sendable {
        /// Represents an Amazon S3 location (bucket name and object key) where DataBrew can write output from a job.
        /// This member is required.
        public var location: DataBrewClientTypes.S3Location?

        public init(
            location: DataBrewClientTypes.S3Location? = nil
        )
        {
            self.location = location
        }
    }
}

extension DataBrewClientTypes {

    /// Represents options that specify how and where in the Glue Data Catalog DataBrew writes the output generated by recipe jobs.
    public struct DataCatalogOutput: Swift.Sendable {
        /// The unique identifier of the Amazon Web Services account that holds the Data Catalog that stores the data.
        public var catalogId: Swift.String?
        /// The name of a database in the Data Catalog.
        /// This member is required.
        public var databaseName: Swift.String?
        /// Represents options that specify how and where DataBrew writes the database output generated by recipe jobs.
        public var databaseOptions: DataBrewClientTypes.DatabaseTableOutputOptions?
        /// A value that, if true, means that any data in the location specified for output is overwritten with new output. Not supported with DatabaseOptions.
        public var overwrite: Swift.Bool
        /// Represents options that specify how and where DataBrew writes the Amazon S3 output generated by recipe jobs.
        public var s3Options: DataBrewClientTypes.S3TableOutputOptions?
        /// The name of a table in the Data Catalog.
        /// This member is required.
        public var tableName: Swift.String?

        public init(
            catalogId: Swift.String? = nil,
            databaseName: Swift.String? = nil,
            databaseOptions: DataBrewClientTypes.DatabaseTableOutputOptions? = nil,
            overwrite: Swift.Bool = false,
            s3Options: DataBrewClientTypes.S3TableOutputOptions? = nil,
            tableName: Swift.String? = nil
        )
        {
            self.catalogId = catalogId
            self.databaseName = databaseName
            self.databaseOptions = databaseOptions
            self.overwrite = overwrite
            self.s3Options = s3Options
            self.tableName = tableName
        }
    }
}

extension DataBrewClientTypes {

    public enum CompressionFormat: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case brotli
        case bzip2
        case deflate
        case gzip
        case lz4
        case lzo
        case snappy
        case zlib
        case zstd
        case sdkUnknown(Swift.String)

        public static var allCases: [CompressionFormat] {
            return [
                .brotli,
                .bzip2,
                .deflate,
                .gzip,
                .lz4,
                .lzo,
                .snappy,
                .zlib,
                .zstd
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .brotli: return "BROTLI"
            case .bzip2: return "BZIP2"
            case .deflate: return "DEFLATE"
            case .gzip: return "GZIP"
            case .lz4: return "LZ4"
            case .lzo: return "LZO"
            case .snappy: return "SNAPPY"
            case .zlib: return "ZLIB"
            case .zstd: return "ZSTD"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataBrewClientTypes {

    public enum OutputFormat: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case avro
        case csv
        case glueparquet
        case json
        case orc
        case parquet
        case tableauhyper
        case xml
        case sdkUnknown(Swift.String)

        public static var allCases: [OutputFormat] {
            return [
                .avro,
                .csv,
                .glueparquet,
                .json,
                .orc,
                .parquet,
                .tableauhyper,
                .xml
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .avro: return "AVRO"
            case .csv: return "CSV"
            case .glueparquet: return "GLUEPARQUET"
            case .json: return "JSON"
            case .orc: return "ORC"
            case .parquet: return "PARQUET"
            case .tableauhyper: return "TABLEAUHYPER"
            case .xml: return "XML"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataBrewClientTypes {

    /// Represents a set of options that define how DataBrew will write a comma-separated value (CSV) file.
    public struct CsvOutputOptions: Swift.Sendable {
        /// A single character that specifies the delimiter used to create CSV job output.
        public var delimiter: Swift.String?

        public init(
            delimiter: Swift.String? = nil
        )
        {
            self.delimiter = delimiter
        }
    }
}

extension DataBrewClientTypes {

    /// Represents a set of options that define the structure of comma-separated (CSV) job output.
    public struct OutputFormatOptions: Swift.Sendable {
        /// Represents a set of options that define the structure of comma-separated value (CSV) job output.
        public var csv: DataBrewClientTypes.CsvOutputOptions?

        public init(
            csv: DataBrewClientTypes.CsvOutputOptions? = nil
        )
        {
            self.csv = csv
        }
    }
}

extension DataBrewClientTypes {

    /// Represents options that specify how and where in Amazon S3 DataBrew writes the output generated by recipe jobs or profile jobs.
    public struct Output: Swift.Sendable {
        /// The compression algorithm used to compress the output text of the job.
        public var compressionFormat: DataBrewClientTypes.CompressionFormat?
        /// The data format of the output of the job.
        public var format: DataBrewClientTypes.OutputFormat?
        /// Represents options that define how DataBrew formats job output files.
        public var formatOptions: DataBrewClientTypes.OutputFormatOptions?
        /// The location in Amazon S3 where the job writes its output.
        /// This member is required.
        public var location: DataBrewClientTypes.S3Location?
        /// Maximum number of files to be generated by the job and written to the output folder. For output partitioned by column(s), the MaxOutputFiles value is the maximum number of files per partition.
        public var maxOutputFiles: Swift.Int?
        /// A value that, if true, means that any data in the location specified for output is overwritten with new output.
        public var overwrite: Swift.Bool
        /// The names of one or more partition columns for the output of the job.
        public var partitionColumns: [Swift.String]?

        public init(
            compressionFormat: DataBrewClientTypes.CompressionFormat? = nil,
            format: DataBrewClientTypes.OutputFormat? = nil,
            formatOptions: DataBrewClientTypes.OutputFormatOptions? = nil,
            location: DataBrewClientTypes.S3Location? = nil,
            maxOutputFiles: Swift.Int? = nil,
            overwrite: Swift.Bool = false,
            partitionColumns: [Swift.String]? = nil
        )
        {
            self.compressionFormat = compressionFormat
            self.format = format
            self.formatOptions = formatOptions
            self.location = location
            self.maxOutputFiles = maxOutputFiles
            self.overwrite = overwrite
            self.partitionColumns = partitionColumns
        }
    }
}

extension DataBrewClientTypes {

    /// Represents the name and version of a DataBrew recipe.
    public struct RecipeReference: Swift.Sendable {
        /// The name of the recipe.
        /// This member is required.
        public var name: Swift.String?
        /// The identifier for the version for the recipe.
        public var recipeVersion: Swift.String?

        public init(
            name: Swift.String? = nil,
            recipeVersion: Swift.String? = nil
        )
        {
            self.name = name
            self.recipeVersion = recipeVersion
        }
    }
}

public struct CreateRecipeJobInput: Swift.Sendable {
    /// One or more artifacts that represent the Glue Data Catalog output from running the job.
    public var dataCatalogOutputs: [DataBrewClientTypes.DataCatalogOutput]?
    /// Represents a list of JDBC database output objects which defines the output destination for a DataBrew recipe job to write to.
    public var databaseOutputs: [DataBrewClientTypes.DatabaseOutput]?
    /// The name of the dataset that this job processes.
    public var datasetName: Swift.String?
    /// The Amazon Resource Name (ARN) of an encryption key that is used to protect the job.
    public var encryptionKeyArn: Swift.String?
    /// The encryption mode for the job, which can be one of the following:
    ///
    /// * SSE-KMS - Server-side encryption with keys managed by KMS.
    ///
    /// * SSE-S3 - Server-side encryption with keys managed by Amazon S3.
    public var encryptionMode: DataBrewClientTypes.EncryptionMode?
    /// Enables or disables Amazon CloudWatch logging for the job. If logging is enabled, CloudWatch writes one log stream for each job run.
    public var logSubscription: DataBrewClientTypes.LogSubscription?
    /// The maximum number of nodes that DataBrew can consume when the job processes data.
    public var maxCapacity: Swift.Int?
    /// The maximum number of times to retry the job after a job run fails.
    public var maxRetries: Swift.Int?
    /// A unique name for the job. Valid characters are alphanumeric (A-Z, a-z, 0-9), hyphen (-), period (.), and space.
    /// This member is required.
    public var name: Swift.String?
    /// One or more artifacts that represent the output from running the job.
    public var outputs: [DataBrewClientTypes.Output]?
    /// Either the name of an existing project, or a combination of a recipe and a dataset to associate with the recipe.
    public var projectName: Swift.String?
    /// Represents the name and version of a DataBrew recipe.
    public var recipeReference: DataBrewClientTypes.RecipeReference?
    /// The Amazon Resource Name (ARN) of the Identity and Access Management (IAM) role to be assumed when DataBrew runs the job.
    /// This member is required.
    public var roleArn: Swift.String?
    /// Metadata tags to apply to this job.
    public var tags: [Swift.String: Swift.String]?
    /// The job's timeout in minutes. A job that attempts to run longer than this timeout period ends with a status of TIMEOUT.
    public var timeout: Swift.Int?

    public init(
        dataCatalogOutputs: [DataBrewClientTypes.DataCatalogOutput]? = nil,
        databaseOutputs: [DataBrewClientTypes.DatabaseOutput]? = nil,
        datasetName: Swift.String? = nil,
        encryptionKeyArn: Swift.String? = nil,
        encryptionMode: DataBrewClientTypes.EncryptionMode? = nil,
        logSubscription: DataBrewClientTypes.LogSubscription? = nil,
        maxCapacity: Swift.Int? = 0,
        maxRetries: Swift.Int? = 0,
        name: Swift.String? = nil,
        outputs: [DataBrewClientTypes.Output]? = nil,
        projectName: Swift.String? = nil,
        recipeReference: DataBrewClientTypes.RecipeReference? = nil,
        roleArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        timeout: Swift.Int? = 0
    )
    {
        self.dataCatalogOutputs = dataCatalogOutputs
        self.databaseOutputs = databaseOutputs
        self.datasetName = datasetName
        self.encryptionKeyArn = encryptionKeyArn
        self.encryptionMode = encryptionMode
        self.logSubscription = logSubscription
        self.maxCapacity = maxCapacity
        self.maxRetries = maxRetries
        self.name = name
        self.outputs = outputs
        self.projectName = projectName
        self.recipeReference = recipeReference
        self.roleArn = roleArn
        self.tags = tags
        self.timeout = timeout
    }
}

public struct CreateRecipeJobOutput: Swift.Sendable {
    /// The name of the job that you created.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

extension DataBrewClientTypes {

    public enum ThresholdType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case greaterThan
        case greaterThanOrEqual
        case lessThan
        case lessThanOrEqual
        case sdkUnknown(Swift.String)

        public static var allCases: [ThresholdType] {
            return [
                .greaterThan,
                .greaterThanOrEqual,
                .lessThan,
                .lessThanOrEqual
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .greaterThan: return "GREATER_THAN"
            case .greaterThanOrEqual: return "GREATER_THAN_OR_EQUAL"
            case .lessThan: return "LESS_THAN"
            case .lessThanOrEqual: return "LESS_THAN_OR_EQUAL"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataBrewClientTypes {

    public enum ThresholdUnit: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case count
        case percentage
        case sdkUnknown(Swift.String)

        public static var allCases: [ThresholdUnit] {
            return [
                .count,
                .percentage
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .count: return "COUNT"
            case .percentage: return "PERCENTAGE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataBrewClientTypes {

    /// The threshold used with a non-aggregate check expression. The non-aggregate check expression will be applied to each row in a specific column. Then the threshold will be used to determine whether the validation succeeds.
    public struct Threshold: Swift.Sendable {
        /// The type of a threshold. Used for comparison of an actual count of rows that satisfy the rule to the threshold value.
        public var type: DataBrewClientTypes.ThresholdType?
        /// Unit of threshold value. Can be either a COUNT or PERCENTAGE of the full sample size used for validation.
        public var unit: DataBrewClientTypes.ThresholdUnit?
        /// The value of a threshold.
        /// This member is required.
        public var value: Swift.Double

        public init(
            type: DataBrewClientTypes.ThresholdType? = nil,
            unit: DataBrewClientTypes.ThresholdUnit? = nil,
            value: Swift.Double = 0.0
        )
        {
            self.type = type
            self.unit = unit
            self.value = value
        }
    }
}

extension DataBrewClientTypes {

    /// Represents a single data quality requirement that should be validated in the scope of this dataset.
    public struct Rule: Swift.Sendable {
        /// The expression which includes column references, condition names followed by variable references, possibly grouped and combined with other conditions. For example, (:col1 starts_with :prefix1 or :col1 starts_with :prefix2) and (:col1 ends_with :suffix1 or :col1 ends_with :suffix2). Column and value references are substitution variables that should start with the ':' symbol. Depending on the context, substitution variables' values can be either an actual value or a column name. These values are defined in the SubstitutionMap. If a CheckExpression starts with a column reference, then ColumnSelectors in the rule should be null. If ColumnSelectors has been defined, then there should be no column reference in the left side of a condition, for example, is_between :val1 and :val2. For more information, see [Available checks](https://docs.aws.amazon.com/databrew/latest/dg/profile.data-quality-available-checks.html)
        /// This member is required.
        public var checkExpression: Swift.String?
        /// List of column selectors. Selectors can be used to select columns using a name or regular expression from the dataset. Rule will be applied to selected columns.
        public var columnSelectors: [DataBrewClientTypes.ColumnSelector]?
        /// A value that specifies whether the rule is disabled. Once a rule is disabled, a profile job will not validate it during a job run. Default value is false.
        public var disabled: Swift.Bool
        /// The name of the rule.
        /// This member is required.
        public var name: Swift.String?
        /// The map of substitution variable names to their values used in a check expression. Variable names should start with a ':' (colon). Variable values can either be actual values or column names. To differentiate between the two, column names should be enclosed in backticks, for example, ":col1": "`Column A`".
        public var substitutionMap: [Swift.String: Swift.String]?
        /// The threshold used with a non-aggregate check expression. Non-aggregate check expressions will be applied to each row in a specific column, and the threshold will be used to determine whether the validation succeeds.
        public var threshold: DataBrewClientTypes.Threshold?

        public init(
            checkExpression: Swift.String? = nil,
            columnSelectors: [DataBrewClientTypes.ColumnSelector]? = nil,
            disabled: Swift.Bool = false,
            name: Swift.String? = nil,
            substitutionMap: [Swift.String: Swift.String]? = nil,
            threshold: DataBrewClientTypes.Threshold? = nil
        )
        {
            self.checkExpression = checkExpression
            self.columnSelectors = columnSelectors
            self.disabled = disabled
            self.name = name
            self.substitutionMap = substitutionMap
            self.threshold = threshold
        }
    }
}

public struct CreateRulesetInput: Swift.Sendable {
    /// The description of the ruleset.
    public var description: Swift.String?
    /// The name of the ruleset to be created. Valid characters are alphanumeric (A-Z, a-z, 0-9), hyphen (-), period (.), and space.
    /// This member is required.
    public var name: Swift.String?
    /// A list of rules that are defined with the ruleset. A rule includes one or more checks to be validated on a DataBrew dataset.
    /// This member is required.
    public var rules: [DataBrewClientTypes.Rule]?
    /// Metadata tags to apply to the ruleset.
    public var tags: [Swift.String: Swift.String]?
    /// The Amazon Resource Name (ARN) of a resource (dataset) that the ruleset is associated with.
    /// This member is required.
    public var targetArn: Swift.String?

    public init(
        description: Swift.String? = nil,
        name: Swift.String? = nil,
        rules: [DataBrewClientTypes.Rule]? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        targetArn: Swift.String? = nil
    )
    {
        self.description = description
        self.name = name
        self.rules = rules
        self.tags = tags
        self.targetArn = targetArn
    }
}

public struct CreateRulesetOutput: Swift.Sendable {
    /// The unique name of the created ruleset.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct CreateScheduleInput: Swift.Sendable {
    /// The date or dates and time or times when the jobs are to be run. For more information, see [Cron expressions](https://docs.aws.amazon.com/databrew/latest/dg/jobs.cron.html) in the Glue DataBrew Developer Guide.
    /// This member is required.
    public var cronExpression: Swift.String?
    /// The name or names of one or more jobs to be run.
    public var jobNames: [Swift.String]?
    /// A unique name for the schedule. Valid characters are alphanumeric (A-Z, a-z, 0-9), hyphen (-), period (.), and space.
    /// This member is required.
    public var name: Swift.String?
    /// Metadata tags to apply to this schedule.
    public var tags: [Swift.String: Swift.String]?

    public init(
        cronExpression: Swift.String? = nil,
        jobNames: [Swift.String]? = nil,
        name: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.cronExpression = cronExpression
        self.jobNames = jobNames
        self.name = name
        self.tags = tags
    }
}

public struct CreateScheduleOutput: Swift.Sendable {
    /// The name of the schedule that was created.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct DeleteDatasetInput: Swift.Sendable {
    /// The name of the dataset to be deleted.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct DeleteDatasetOutput: Swift.Sendable {
    /// The name of the dataset that you deleted.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct DeleteJobInput: Swift.Sendable {
    /// The name of the job to be deleted.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct DeleteJobOutput: Swift.Sendable {
    /// The name of the job that you deleted.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct DeleteProjectInput: Swift.Sendable {
    /// The name of the project to be deleted.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct DeleteProjectOutput: Swift.Sendable {
    /// The name of the project that you deleted.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct DeleteRecipeVersionInput: Swift.Sendable {
    /// The name of the recipe.
    /// This member is required.
    public var name: Swift.String?
    /// The version of the recipe to be deleted. You can specify a numeric versions (X.Y) or LATEST_WORKING. LATEST_PUBLISHED is not supported.
    /// This member is required.
    public var recipeVersion: Swift.String?

    public init(
        name: Swift.String? = nil,
        recipeVersion: Swift.String? = nil
    )
    {
        self.name = name
        self.recipeVersion = recipeVersion
    }
}

public struct DeleteRecipeVersionOutput: Swift.Sendable {
    /// The name of the recipe that was deleted.
    /// This member is required.
    public var name: Swift.String?
    /// The version of the recipe that was deleted.
    /// This member is required.
    public var recipeVersion: Swift.String?

    public init(
        name: Swift.String? = nil,
        recipeVersion: Swift.String? = nil
    )
    {
        self.name = name
        self.recipeVersion = recipeVersion
    }
}

public struct DeleteRulesetInput: Swift.Sendable {
    /// The name of the ruleset to be deleted.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct DeleteRulesetOutput: Swift.Sendable {
    /// The name of the deleted ruleset.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct DeleteScheduleInput: Swift.Sendable {
    /// The name of the schedule to be deleted.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct DeleteScheduleOutput: Swift.Sendable {
    /// The name of the schedule that was deleted.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct DescribeDatasetInput: Swift.Sendable {
    /// The name of the dataset to be described.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

extension DataBrewClientTypes {

    public enum Source: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case database
        case datacatalog
        case s3
        case sdkUnknown(Swift.String)

        public static var allCases: [Source] {
            return [
                .database,
                .datacatalog,
                .s3
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .database: return "DATABASE"
            case .datacatalog: return "DATA-CATALOG"
            case .s3: return "S3"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeDatasetOutput: Swift.Sendable {
    /// The date and time that the dataset was created.
    public var createDate: Foundation.Date?
    /// The identifier (user name) of the user who created the dataset.
    public var createdBy: Swift.String?
    /// The file format of a dataset that is created from an Amazon S3 file or folder.
    public var format: DataBrewClientTypes.InputFormat?
    /// Represents a set of options that define the structure of either comma-separated value (CSV), Excel, or JSON input.
    public var formatOptions: DataBrewClientTypes.FormatOptions?
    /// Represents information on how DataBrew can find data, in either the Glue Data Catalog or Amazon S3.
    /// This member is required.
    public var input: DataBrewClientTypes.Input?
    /// The identifier (user name) of the user who last modified the dataset.
    public var lastModifiedBy: Swift.String?
    /// The date and time that the dataset was last modified.
    public var lastModifiedDate: Foundation.Date?
    /// The name of the dataset.
    /// This member is required.
    public var name: Swift.String?
    /// A set of options that defines how DataBrew interprets an Amazon S3 path of the dataset.
    public var pathOptions: DataBrewClientTypes.PathOptions?
    /// The Amazon Resource Name (ARN) of the dataset.
    public var resourceArn: Swift.String?
    /// The location of the data for this dataset, Amazon S3 or the Glue Data Catalog.
    public var source: DataBrewClientTypes.Source?
    /// Metadata tags associated with this dataset.
    public var tags: [Swift.String: Swift.String]?

    public init(
        createDate: Foundation.Date? = nil,
        createdBy: Swift.String? = nil,
        format: DataBrewClientTypes.InputFormat? = nil,
        formatOptions: DataBrewClientTypes.FormatOptions? = nil,
        input: DataBrewClientTypes.Input? = nil,
        lastModifiedBy: Swift.String? = nil,
        lastModifiedDate: Foundation.Date? = nil,
        name: Swift.String? = nil,
        pathOptions: DataBrewClientTypes.PathOptions? = nil,
        resourceArn: Swift.String? = nil,
        source: DataBrewClientTypes.Source? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.createDate = createDate
        self.createdBy = createdBy
        self.format = format
        self.formatOptions = formatOptions
        self.input = input
        self.lastModifiedBy = lastModifiedBy
        self.lastModifiedDate = lastModifiedDate
        self.name = name
        self.pathOptions = pathOptions
        self.resourceArn = resourceArn
        self.source = source
        self.tags = tags
    }
}

public struct DescribeJobInput: Swift.Sendable {
    /// The name of the job to be described.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

extension DataBrewClientTypes {

    public enum JobType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case profile
        case recipe
        case sdkUnknown(Swift.String)

        public static var allCases: [JobType] {
            return [
                .profile,
                .recipe
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .profile: return "PROFILE"
            case .recipe: return "RECIPE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeJobOutput: Swift.Sendable {
    /// The date and time that the job was created.
    public var createDate: Foundation.Date?
    /// The identifier (user name) of the user associated with the creation of the job.
    public var createdBy: Swift.String?
    /// One or more artifacts that represent the Glue Data Catalog output from running the job.
    public var dataCatalogOutputs: [DataBrewClientTypes.DataCatalogOutput]?
    /// Represents a list of JDBC database output objects which defines the output destination for a DataBrew recipe job to write into.
    public var databaseOutputs: [DataBrewClientTypes.DatabaseOutput]?
    /// The dataset that the job acts upon.
    public var datasetName: Swift.String?
    /// The Amazon Resource Name (ARN) of an encryption key that is used to protect the job.
    public var encryptionKeyArn: Swift.String?
    /// The encryption mode for the job, which can be one of the following:
    ///
    /// * SSE-KMS - Server-side encryption with keys managed by KMS.
    ///
    /// * SSE-S3 - Server-side encryption with keys managed by Amazon S3.
    public var encryptionMode: DataBrewClientTypes.EncryptionMode?
    /// Sample configuration for profile jobs only. Determines the number of rows on which the profile job will be executed.
    public var jobSample: DataBrewClientTypes.JobSample?
    /// The identifier (user name) of the user who last modified the job.
    public var lastModifiedBy: Swift.String?
    /// The date and time that the job was last modified.
    public var lastModifiedDate: Foundation.Date?
    /// Indicates whether Amazon CloudWatch logging is enabled for this job.
    public var logSubscription: DataBrewClientTypes.LogSubscription?
    /// The maximum number of compute nodes that DataBrew can consume when the job processes data.
    public var maxCapacity: Swift.Int
    /// The maximum number of times to retry the job after a job run fails.
    public var maxRetries: Swift.Int
    /// The name of the job.
    /// This member is required.
    public var name: Swift.String?
    /// One or more artifacts that represent the output from running the job.
    public var outputs: [DataBrewClientTypes.Output]?
    /// Configuration for profile jobs. Used to select columns, do evaluations, and override default parameters of evaluations. When configuration is null, the profile job will run with default settings.
    public var profileConfiguration: DataBrewClientTypes.ProfileConfiguration?
    /// The DataBrew project associated with this job.
    public var projectName: Swift.String?
    /// Represents the name and version of a DataBrew recipe.
    public var recipeReference: DataBrewClientTypes.RecipeReference?
    /// The Amazon Resource Name (ARN) of the job.
    public var resourceArn: Swift.String?
    /// The ARN of the Identity and Access Management (IAM) role to be assumed when DataBrew runs the job.
    public var roleArn: Swift.String?
    /// Metadata tags associated with this job.
    public var tags: [Swift.String: Swift.String]?
    /// The job's timeout in minutes. A job that attempts to run longer than this timeout period ends with a status of TIMEOUT.
    public var timeout: Swift.Int
    /// The job type, which must be one of the following:
    ///
    /// * PROFILE - The job analyzes the dataset to determine its size, data types, data distribution, and more.
    ///
    /// * RECIPE - The job applies one or more transformations to a dataset.
    public var type: DataBrewClientTypes.JobType?
    /// List of validation configurations that are applied to the profile job.
    public var validationConfigurations: [DataBrewClientTypes.ValidationConfiguration]?

    public init(
        createDate: Foundation.Date? = nil,
        createdBy: Swift.String? = nil,
        dataCatalogOutputs: [DataBrewClientTypes.DataCatalogOutput]? = nil,
        databaseOutputs: [DataBrewClientTypes.DatabaseOutput]? = nil,
        datasetName: Swift.String? = nil,
        encryptionKeyArn: Swift.String? = nil,
        encryptionMode: DataBrewClientTypes.EncryptionMode? = nil,
        jobSample: DataBrewClientTypes.JobSample? = nil,
        lastModifiedBy: Swift.String? = nil,
        lastModifiedDate: Foundation.Date? = nil,
        logSubscription: DataBrewClientTypes.LogSubscription? = nil,
        maxCapacity: Swift.Int = 0,
        maxRetries: Swift.Int = 0,
        name: Swift.String? = nil,
        outputs: [DataBrewClientTypes.Output]? = nil,
        profileConfiguration: DataBrewClientTypes.ProfileConfiguration? = nil,
        projectName: Swift.String? = nil,
        recipeReference: DataBrewClientTypes.RecipeReference? = nil,
        resourceArn: Swift.String? = nil,
        roleArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        timeout: Swift.Int = 0,
        type: DataBrewClientTypes.JobType? = nil,
        validationConfigurations: [DataBrewClientTypes.ValidationConfiguration]? = nil
    )
    {
        self.createDate = createDate
        self.createdBy = createdBy
        self.dataCatalogOutputs = dataCatalogOutputs
        self.databaseOutputs = databaseOutputs
        self.datasetName = datasetName
        self.encryptionKeyArn = encryptionKeyArn
        self.encryptionMode = encryptionMode
        self.jobSample = jobSample
        self.lastModifiedBy = lastModifiedBy
        self.lastModifiedDate = lastModifiedDate
        self.logSubscription = logSubscription
        self.maxCapacity = maxCapacity
        self.maxRetries = maxRetries
        self.name = name
        self.outputs = outputs
        self.profileConfiguration = profileConfiguration
        self.projectName = projectName
        self.recipeReference = recipeReference
        self.resourceArn = resourceArn
        self.roleArn = roleArn
        self.tags = tags
        self.timeout = timeout
        self.type = type
        self.validationConfigurations = validationConfigurations
    }
}

public struct DescribeJobRunInput: Swift.Sendable {
    /// The name of the job being processed during this run.
    /// This member is required.
    public var name: Swift.String?
    /// The unique identifier of the job run.
    /// This member is required.
    public var runId: Swift.String?

    public init(
        name: Swift.String? = nil,
        runId: Swift.String? = nil
    )
    {
        self.name = name
        self.runId = runId
    }
}

extension DataBrewClientTypes {

    public enum JobRunState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case running
        case starting
        case stopped
        case stopping
        case succeeded
        case timeout
        case sdkUnknown(Swift.String)

        public static var allCases: [JobRunState] {
            return [
                .failed,
                .running,
                .starting,
                .stopped,
                .stopping,
                .succeeded,
                .timeout
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .running: return "RUNNING"
            case .starting: return "STARTING"
            case .stopped: return "STOPPED"
            case .stopping: return "STOPPING"
            case .succeeded: return "SUCCEEDED"
            case .timeout: return "TIMEOUT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeJobRunOutput: Swift.Sendable {
    /// The number of times that DataBrew has attempted to run the job.
    public var attempt: Swift.Int
    /// The date and time when the job completed processing.
    public var completedOn: Foundation.Date?
    /// One or more artifacts that represent the Glue Data Catalog output from running the job.
    public var dataCatalogOutputs: [DataBrewClientTypes.DataCatalogOutput]?
    /// Represents a list of JDBC database output objects which defines the output destination for a DataBrew recipe job to write into.
    public var databaseOutputs: [DataBrewClientTypes.DatabaseOutput]?
    /// The name of the dataset for the job to process.
    public var datasetName: Swift.String?
    /// A message indicating an error (if any) that was encountered when the job ran.
    public var errorMessage: Swift.String?
    /// The amount of time, in seconds, during which the job run consumed resources.
    public var executionTime: Swift.Int
    /// The name of the job being processed during this run.
    /// This member is required.
    public var jobName: Swift.String?
    /// Sample configuration for profile jobs only. Determines the number of rows on which the profile job will be executed. If a JobSample value is not provided, the default value will be used. The default value is CUSTOM_ROWS for the mode parameter and 20000 for the size parameter.
    public var jobSample: DataBrewClientTypes.JobSample?
    /// The name of an Amazon CloudWatch log group, where the job writes diagnostic messages when it runs.
    public var logGroupName: Swift.String?
    /// The current status of Amazon CloudWatch logging for the job run.
    public var logSubscription: DataBrewClientTypes.LogSubscription?
    /// One or more output artifacts from a job run.
    public var outputs: [DataBrewClientTypes.Output]?
    /// Configuration for profile jobs. Used to select columns, do evaluations, and override default parameters of evaluations. When configuration is null, the profile job will run with default settings.
    public var profileConfiguration: DataBrewClientTypes.ProfileConfiguration?
    /// Represents the name and version of a DataBrew recipe.
    public var recipeReference: DataBrewClientTypes.RecipeReference?
    /// The unique identifier of the job run.
    public var runId: Swift.String?
    /// The Amazon Resource Name (ARN) of the user who started the job run.
    public var startedBy: Swift.String?
    /// The date and time when the job run began.
    public var startedOn: Foundation.Date?
    /// The current state of the job run entity itself.
    public var state: DataBrewClientTypes.JobRunState?
    /// List of validation configurations that are applied to the profile job.
    public var validationConfigurations: [DataBrewClientTypes.ValidationConfiguration]?

    public init(
        attempt: Swift.Int = 0,
        completedOn: Foundation.Date? = nil,
        dataCatalogOutputs: [DataBrewClientTypes.DataCatalogOutput]? = nil,
        databaseOutputs: [DataBrewClientTypes.DatabaseOutput]? = nil,
        datasetName: Swift.String? = nil,
        errorMessage: Swift.String? = nil,
        executionTime: Swift.Int = 0,
        jobName: Swift.String? = nil,
        jobSample: DataBrewClientTypes.JobSample? = nil,
        logGroupName: Swift.String? = nil,
        logSubscription: DataBrewClientTypes.LogSubscription? = nil,
        outputs: [DataBrewClientTypes.Output]? = nil,
        profileConfiguration: DataBrewClientTypes.ProfileConfiguration? = nil,
        recipeReference: DataBrewClientTypes.RecipeReference? = nil,
        runId: Swift.String? = nil,
        startedBy: Swift.String? = nil,
        startedOn: Foundation.Date? = nil,
        state: DataBrewClientTypes.JobRunState? = nil,
        validationConfigurations: [DataBrewClientTypes.ValidationConfiguration]? = nil
    )
    {
        self.attempt = attempt
        self.completedOn = completedOn
        self.dataCatalogOutputs = dataCatalogOutputs
        self.databaseOutputs = databaseOutputs
        self.datasetName = datasetName
        self.errorMessage = errorMessage
        self.executionTime = executionTime
        self.jobName = jobName
        self.jobSample = jobSample
        self.logGroupName = logGroupName
        self.logSubscription = logSubscription
        self.outputs = outputs
        self.profileConfiguration = profileConfiguration
        self.recipeReference = recipeReference
        self.runId = runId
        self.startedBy = startedBy
        self.startedOn = startedOn
        self.state = state
        self.validationConfigurations = validationConfigurations
    }
}

public struct DescribeProjectInput: Swift.Sendable {
    /// The name of the project to be described.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

extension DataBrewClientTypes {

    public enum SessionStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case assigned
        case failed
        case initializing
        case provisioning
        case ready
        case recycling
        case rotating
        case terminated
        case terminating
        case updating
        case sdkUnknown(Swift.String)

        public static var allCases: [SessionStatus] {
            return [
                .assigned,
                .failed,
                .initializing,
                .provisioning,
                .ready,
                .recycling,
                .rotating,
                .terminated,
                .terminating,
                .updating
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .assigned: return "ASSIGNED"
            case .failed: return "FAILED"
            case .initializing: return "INITIALIZING"
            case .provisioning: return "PROVISIONING"
            case .ready: return "READY"
            case .recycling: return "RECYCLING"
            case .rotating: return "ROTATING"
            case .terminated: return "TERMINATED"
            case .terminating: return "TERMINATING"
            case .updating: return "UPDATING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeProjectOutput: Swift.Sendable {
    /// The date and time that the project was created.
    public var createDate: Foundation.Date?
    /// The identifier (user name) of the user who created the project.
    public var createdBy: Swift.String?
    /// The dataset associated with the project.
    public var datasetName: Swift.String?
    /// The identifier (user name) of the user who last modified the project.
    public var lastModifiedBy: Swift.String?
    /// The date and time that the project was last modified.
    public var lastModifiedDate: Foundation.Date?
    /// The name of the project.
    /// This member is required.
    public var name: Swift.String?
    /// The date and time when the project was opened.
    public var openDate: Foundation.Date?
    /// The identifier (user name) of the user that opened the project for use.
    public var openedBy: Swift.String?
    /// The recipe associated with this job.
    public var recipeName: Swift.String?
    /// The Amazon Resource Name (ARN) of the project.
    public var resourceArn: Swift.String?
    /// The ARN of the Identity and Access Management (IAM) role to be assumed when DataBrew runs the job.
    public var roleArn: Swift.String?
    /// Represents the sample size and sampling type for DataBrew to use for interactive data analysis.
    public var sample: DataBrewClientTypes.Sample?
    /// Describes the current state of the session:
    ///
    /// * PROVISIONING - allocating resources for the session.
    ///
    /// * INITIALIZING - getting the session ready for first use.
    ///
    /// * ASSIGNED - the session is ready for use.
    public var sessionStatus: DataBrewClientTypes.SessionStatus?
    /// Metadata tags associated with this project.
    public var tags: [Swift.String: Swift.String]?

    public init(
        createDate: Foundation.Date? = nil,
        createdBy: Swift.String? = nil,
        datasetName: Swift.String? = nil,
        lastModifiedBy: Swift.String? = nil,
        lastModifiedDate: Foundation.Date? = nil,
        name: Swift.String? = nil,
        openDate: Foundation.Date? = nil,
        openedBy: Swift.String? = nil,
        recipeName: Swift.String? = nil,
        resourceArn: Swift.String? = nil,
        roleArn: Swift.String? = nil,
        sample: DataBrewClientTypes.Sample? = nil,
        sessionStatus: DataBrewClientTypes.SessionStatus? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.createDate = createDate
        self.createdBy = createdBy
        self.datasetName = datasetName
        self.lastModifiedBy = lastModifiedBy
        self.lastModifiedDate = lastModifiedDate
        self.name = name
        self.openDate = openDate
        self.openedBy = openedBy
        self.recipeName = recipeName
        self.resourceArn = resourceArn
        self.roleArn = roleArn
        self.sample = sample
        self.sessionStatus = sessionStatus
        self.tags = tags
    }
}

public struct DescribeRecipeInput: Swift.Sendable {
    /// The name of the recipe to be described.
    /// This member is required.
    public var name: Swift.String?
    /// The recipe version identifier. If this parameter isn't specified, then the latest published version is returned.
    public var recipeVersion: Swift.String?

    public init(
        name: Swift.String? = nil,
        recipeVersion: Swift.String? = nil
    )
    {
        self.name = name
        self.recipeVersion = recipeVersion
    }
}

public struct DescribeRecipeOutput: Swift.Sendable {
    /// The date and time that the recipe was created.
    public var createDate: Foundation.Date?
    /// The identifier (user name) of the user who created the recipe.
    public var createdBy: Swift.String?
    /// The description of the recipe.
    public var description: Swift.String?
    /// The identifier (user name) of the user who last modified the recipe.
    public var lastModifiedBy: Swift.String?
    /// The date and time that the recipe was last modified.
    public var lastModifiedDate: Foundation.Date?
    /// The name of the recipe.
    /// This member is required.
    public var name: Swift.String?
    /// The name of the project associated with this recipe.
    public var projectName: Swift.String?
    /// The identifier (user name) of the user who last published the recipe.
    public var publishedBy: Swift.String?
    /// The date and time when the recipe was last published.
    public var publishedDate: Foundation.Date?
    /// The recipe version identifier.
    public var recipeVersion: Swift.String?
    /// The ARN of the recipe.
    public var resourceArn: Swift.String?
    /// One or more steps to be performed by the recipe. Each step consists of an action, and the conditions under which the action should succeed.
    public var steps: [DataBrewClientTypes.RecipeStep]?
    /// Metadata tags associated with this project.
    public var tags: [Swift.String: Swift.String]?

    public init(
        createDate: Foundation.Date? = nil,
        createdBy: Swift.String? = nil,
        description: Swift.String? = nil,
        lastModifiedBy: Swift.String? = nil,
        lastModifiedDate: Foundation.Date? = nil,
        name: Swift.String? = nil,
        projectName: Swift.String? = nil,
        publishedBy: Swift.String? = nil,
        publishedDate: Foundation.Date? = nil,
        recipeVersion: Swift.String? = nil,
        resourceArn: Swift.String? = nil,
        steps: [DataBrewClientTypes.RecipeStep]? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.createDate = createDate
        self.createdBy = createdBy
        self.description = description
        self.lastModifiedBy = lastModifiedBy
        self.lastModifiedDate = lastModifiedDate
        self.name = name
        self.projectName = projectName
        self.publishedBy = publishedBy
        self.publishedDate = publishedDate
        self.recipeVersion = recipeVersion
        self.resourceArn = resourceArn
        self.steps = steps
        self.tags = tags
    }
}

public struct DescribeRulesetInput: Swift.Sendable {
    /// The name of the ruleset to be described.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct DescribeRulesetOutput: Swift.Sendable {
    /// The date and time that the ruleset was created.
    public var createDate: Foundation.Date?
    /// The Amazon Resource Name (ARN) of the user who created the ruleset.
    public var createdBy: Swift.String?
    /// The description of the ruleset.
    public var description: Swift.String?
    /// The Amazon Resource Name (ARN) of the user who last modified the ruleset.
    public var lastModifiedBy: Swift.String?
    /// The modification date and time of the ruleset.
    public var lastModifiedDate: Foundation.Date?
    /// The name of the ruleset.
    /// This member is required.
    public var name: Swift.String?
    /// The Amazon Resource Name (ARN) for the ruleset.
    public var resourceArn: Swift.String?
    /// A list of rules that are defined with the ruleset. A rule includes one or more checks to be validated on a DataBrew dataset.
    public var rules: [DataBrewClientTypes.Rule]?
    /// Metadata tags that have been applied to the ruleset.
    public var tags: [Swift.String: Swift.String]?
    /// The Amazon Resource Name (ARN) of a resource (dataset) that the ruleset is associated with.
    public var targetArn: Swift.String?

    public init(
        createDate: Foundation.Date? = nil,
        createdBy: Swift.String? = nil,
        description: Swift.String? = nil,
        lastModifiedBy: Swift.String? = nil,
        lastModifiedDate: Foundation.Date? = nil,
        name: Swift.String? = nil,
        resourceArn: Swift.String? = nil,
        rules: [DataBrewClientTypes.Rule]? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        targetArn: Swift.String? = nil
    )
    {
        self.createDate = createDate
        self.createdBy = createdBy
        self.description = description
        self.lastModifiedBy = lastModifiedBy
        self.lastModifiedDate = lastModifiedDate
        self.name = name
        self.resourceArn = resourceArn
        self.rules = rules
        self.tags = tags
        self.targetArn = targetArn
    }
}

public struct DescribeScheduleInput: Swift.Sendable {
    /// The name of the schedule to be described.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct DescribeScheduleOutput: Swift.Sendable {
    /// The date and time that the schedule was created.
    public var createDate: Foundation.Date?
    /// The identifier (user name) of the user who created the schedule.
    public var createdBy: Swift.String?
    /// The date or dates and time or times when the jobs are to be run for the schedule. For more information, see [Cron expressions](https://docs.aws.amazon.com/databrew/latest/dg/jobs.cron.html) in the Glue DataBrew Developer Guide.
    public var cronExpression: Swift.String?
    /// The name or names of one or more jobs to be run by using the schedule.
    public var jobNames: [Swift.String]?
    /// The identifier (user name) of the user who last modified the schedule.
    public var lastModifiedBy: Swift.String?
    /// The date and time that the schedule was last modified.
    public var lastModifiedDate: Foundation.Date?
    /// The name of the schedule.
    /// This member is required.
    public var name: Swift.String?
    /// The Amazon Resource Name (ARN) of the schedule.
    public var resourceArn: Swift.String?
    /// Metadata tags associated with this schedule.
    public var tags: [Swift.String: Swift.String]?

    public init(
        createDate: Foundation.Date? = nil,
        createdBy: Swift.String? = nil,
        cronExpression: Swift.String? = nil,
        jobNames: [Swift.String]? = nil,
        lastModifiedBy: Swift.String? = nil,
        lastModifiedDate: Foundation.Date? = nil,
        name: Swift.String? = nil,
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.createDate = createDate
        self.createdBy = createdBy
        self.cronExpression = cronExpression
        self.jobNames = jobNames
        self.lastModifiedBy = lastModifiedBy
        self.lastModifiedDate = lastModifiedDate
        self.name = name
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct ListDatasetsInput: Swift.Sendable {
    /// The maximum number of results to return in this request.
    public var maxResults: Swift.Int?
    /// The token returned by a previous call to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DataBrewClientTypes {

    /// Represents a dataset that can be processed by DataBrew.
    public struct Dataset: Swift.Sendable {
        /// The ID of the Amazon Web Services account that owns the dataset.
        public var accountId: Swift.String?
        /// The date and time that the dataset was created.
        public var createDate: Foundation.Date?
        /// The Amazon Resource Name (ARN) of the user who created the dataset.
        public var createdBy: Swift.String?
        /// The file format of a dataset that is created from an Amazon S3 file or folder.
        public var format: DataBrewClientTypes.InputFormat?
        /// A set of options that define how DataBrew interprets the data in the dataset.
        public var formatOptions: DataBrewClientTypes.FormatOptions?
        /// Information on how DataBrew can find the dataset, in either the Glue Data Catalog or Amazon S3.
        /// This member is required.
        public var input: DataBrewClientTypes.Input?
        /// The Amazon Resource Name (ARN) of the user who last modified the dataset.
        public var lastModifiedBy: Swift.String?
        /// The last modification date and time of the dataset.
        public var lastModifiedDate: Foundation.Date?
        /// The unique name of the dataset.
        /// This member is required.
        public var name: Swift.String?
        /// A set of options that defines how DataBrew interprets an Amazon S3 path of the dataset.
        public var pathOptions: DataBrewClientTypes.PathOptions?
        /// The unique Amazon Resource Name (ARN) for the dataset.
        public var resourceArn: Swift.String?
        /// The location of the data for the dataset, either Amazon S3 or the Glue Data Catalog.
        public var source: DataBrewClientTypes.Source?
        /// Metadata tags that have been applied to the dataset.
        public var tags: [Swift.String: Swift.String]?

        public init(
            accountId: Swift.String? = nil,
            createDate: Foundation.Date? = nil,
            createdBy: Swift.String? = nil,
            format: DataBrewClientTypes.InputFormat? = nil,
            formatOptions: DataBrewClientTypes.FormatOptions? = nil,
            input: DataBrewClientTypes.Input? = nil,
            lastModifiedBy: Swift.String? = nil,
            lastModifiedDate: Foundation.Date? = nil,
            name: Swift.String? = nil,
            pathOptions: DataBrewClientTypes.PathOptions? = nil,
            resourceArn: Swift.String? = nil,
            source: DataBrewClientTypes.Source? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.accountId = accountId
            self.createDate = createDate
            self.createdBy = createdBy
            self.format = format
            self.formatOptions = formatOptions
            self.input = input
            self.lastModifiedBy = lastModifiedBy
            self.lastModifiedDate = lastModifiedDate
            self.name = name
            self.pathOptions = pathOptions
            self.resourceArn = resourceArn
            self.source = source
            self.tags = tags
        }
    }
}

public struct ListDatasetsOutput: Swift.Sendable {
    /// A list of datasets that are defined.
    /// This member is required.
    public var datasets: [DataBrewClientTypes.Dataset]?
    /// A token that you can use in a subsequent call to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        datasets: [DataBrewClientTypes.Dataset]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.datasets = datasets
        self.nextToken = nextToken
    }
}

public struct ListJobRunsInput: Swift.Sendable {
    /// The maximum number of results to return in this request.
    public var maxResults: Swift.Int?
    /// The name of the job.
    /// This member is required.
    public var name: Swift.String?
    /// The token returned by a previous call to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        name: Swift.String? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.name = name
        self.nextToken = nextToken
    }
}

extension DataBrewClientTypes {

    /// Represents one run of a DataBrew job.
    public struct JobRun: Swift.Sendable {
        /// The number of times that DataBrew has attempted to run the job.
        public var attempt: Swift.Int
        /// The date and time when the job completed processing.
        public var completedOn: Foundation.Date?
        /// One or more artifacts that represent the Glue Data Catalog output from running the job.
        public var dataCatalogOutputs: [DataBrewClientTypes.DataCatalogOutput]?
        /// Represents a list of JDBC database output objects which defines the output destination for a DataBrew recipe job to write into.
        public var databaseOutputs: [DataBrewClientTypes.DatabaseOutput]?
        /// The name of the dataset for the job to process.
        public var datasetName: Swift.String?
        /// A message indicating an error (if any) that was encountered when the job ran.
        public var errorMessage: Swift.String?
        /// The amount of time, in seconds, during which a job run consumed resources.
        public var executionTime: Swift.Int
        /// The name of the job being processed during this run.
        public var jobName: Swift.String?
        /// A sample configuration for profile jobs only, which determines the number of rows on which the profile job is run. If a JobSample value isn't provided, the default is used. The default value is CUSTOM_ROWS for the mode parameter and 20,000 for the size parameter.
        public var jobSample: DataBrewClientTypes.JobSample?
        /// The name of an Amazon CloudWatch log group, where the job writes diagnostic messages when it runs.
        public var logGroupName: Swift.String?
        /// The current status of Amazon CloudWatch logging for the job run.
        public var logSubscription: DataBrewClientTypes.LogSubscription?
        /// One or more output artifacts from a job run.
        public var outputs: [DataBrewClientTypes.Output]?
        /// The set of steps processed by the job.
        public var recipeReference: DataBrewClientTypes.RecipeReference?
        /// The unique identifier of the job run.
        public var runId: Swift.String?
        /// The Amazon Resource Name (ARN) of the user who initiated the job run.
        public var startedBy: Swift.String?
        /// The date and time when the job run began.
        public var startedOn: Foundation.Date?
        /// The current state of the job run entity itself.
        public var state: DataBrewClientTypes.JobRunState?
        /// List of validation configurations that are applied to the profile job run.
        public var validationConfigurations: [DataBrewClientTypes.ValidationConfiguration]?

        public init(
            attempt: Swift.Int = 0,
            completedOn: Foundation.Date? = nil,
            dataCatalogOutputs: [DataBrewClientTypes.DataCatalogOutput]? = nil,
            databaseOutputs: [DataBrewClientTypes.DatabaseOutput]? = nil,
            datasetName: Swift.String? = nil,
            errorMessage: Swift.String? = nil,
            executionTime: Swift.Int = 0,
            jobName: Swift.String? = nil,
            jobSample: DataBrewClientTypes.JobSample? = nil,
            logGroupName: Swift.String? = nil,
            logSubscription: DataBrewClientTypes.LogSubscription? = nil,
            outputs: [DataBrewClientTypes.Output]? = nil,
            recipeReference: DataBrewClientTypes.RecipeReference? = nil,
            runId: Swift.String? = nil,
            startedBy: Swift.String? = nil,
            startedOn: Foundation.Date? = nil,
            state: DataBrewClientTypes.JobRunState? = nil,
            validationConfigurations: [DataBrewClientTypes.ValidationConfiguration]? = nil
        )
        {
            self.attempt = attempt
            self.completedOn = completedOn
            self.dataCatalogOutputs = dataCatalogOutputs
            self.databaseOutputs = databaseOutputs
            self.datasetName = datasetName
            self.errorMessage = errorMessage
            self.executionTime = executionTime
            self.jobName = jobName
            self.jobSample = jobSample
            self.logGroupName = logGroupName
            self.logSubscription = logSubscription
            self.outputs = outputs
            self.recipeReference = recipeReference
            self.runId = runId
            self.startedBy = startedBy
            self.startedOn = startedOn
            self.state = state
            self.validationConfigurations = validationConfigurations
        }
    }
}

public struct ListJobRunsOutput: Swift.Sendable {
    /// A list of job runs that have occurred for the specified job.
    /// This member is required.
    public var jobRuns: [DataBrewClientTypes.JobRun]?
    /// A token that you can use in a subsequent call to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        jobRuns: [DataBrewClientTypes.JobRun]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.jobRuns = jobRuns
        self.nextToken = nextToken
    }
}

public struct ListJobsInput: Swift.Sendable {
    /// The name of a dataset. Using this parameter indicates to return only those jobs that act on the specified dataset.
    public var datasetName: Swift.String?
    /// The maximum number of results to return in this request.
    public var maxResults: Swift.Int?
    /// A token generated by DataBrew that specifies where to continue pagination if a previous request was truncated. To get the next set of pages, pass in the NextToken value from the response object of the previous page call.
    public var nextToken: Swift.String?
    /// The name of a project. Using this parameter indicates to return only those jobs that are associated with the specified project.
    public var projectName: Swift.String?

    public init(
        datasetName: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        projectName: Swift.String? = nil
    )
    {
        self.datasetName = datasetName
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.projectName = projectName
    }
}

extension DataBrewClientTypes {

    /// Represents all of the attributes of a DataBrew job.
    public struct Job: Swift.Sendable {
        /// The ID of the Amazon Web Services account that owns the job.
        public var accountId: Swift.String?
        /// The date and time that the job was created.
        public var createDate: Foundation.Date?
        /// The Amazon Resource Name (ARN) of the user who created the job.
        public var createdBy: Swift.String?
        /// One or more artifacts that represent the Glue Data Catalog output from running the job.
        public var dataCatalogOutputs: [DataBrewClientTypes.DataCatalogOutput]?
        /// Represents a list of JDBC database output objects which defines the output destination for a DataBrew recipe job to write into.
        public var databaseOutputs: [DataBrewClientTypes.DatabaseOutput]?
        /// A dataset that the job is to process.
        public var datasetName: Swift.String?
        /// The Amazon Resource Name (ARN) of an encryption key that is used to protect the job output. For more information, see [Encrypting data written by DataBrew jobs](https://docs.aws.amazon.com/databrew/latest/dg/encryption-security-configuration.html)
        public var encryptionKeyArn: Swift.String?
        /// The encryption mode for the job, which can be one of the following:
        ///
        /// * SSE-KMS - Server-side encryption with keys managed by KMS.
        ///
        /// * SSE-S3 - Server-side encryption with keys managed by Amazon S3.
        public var encryptionMode: DataBrewClientTypes.EncryptionMode?
        /// A sample configuration for profile jobs only, which determines the number of rows on which the profile job is run. If a JobSample value isn't provided, the default value is used. The default value is CUSTOM_ROWS for the mode parameter and 20,000 for the size parameter.
        public var jobSample: DataBrewClientTypes.JobSample?
        /// The Amazon Resource Name (ARN) of the user who last modified the job.
        public var lastModifiedBy: Swift.String?
        /// The modification date and time of the job.
        public var lastModifiedDate: Foundation.Date?
        /// The current status of Amazon CloudWatch logging for the job.
        public var logSubscription: DataBrewClientTypes.LogSubscription?
        /// The maximum number of nodes that can be consumed when the job processes data.
        public var maxCapacity: Swift.Int
        /// The maximum number of times to retry the job after a job run fails.
        public var maxRetries: Swift.Int
        /// The unique name of the job.
        /// This member is required.
        public var name: Swift.String?
        /// One or more artifacts that represent output from running the job.
        public var outputs: [DataBrewClientTypes.Output]?
        /// The name of the project that the job is associated with.
        public var projectName: Swift.String?
        /// A set of steps that the job runs.
        public var recipeReference: DataBrewClientTypes.RecipeReference?
        /// The unique Amazon Resource Name (ARN) for the job.
        public var resourceArn: Swift.String?
        /// The Amazon Resource Name (ARN) of the role to be assumed for this job.
        public var roleArn: Swift.String?
        /// Metadata tags that have been applied to the job.
        public var tags: [Swift.String: Swift.String]?
        /// The job's timeout in minutes. A job that attempts to run longer than this timeout period ends with a status of TIMEOUT.
        public var timeout: Swift.Int
        /// The job type of the job, which must be one of the following:
        ///
        /// * PROFILE - A job to analyze a dataset, to determine its size, data types, data distribution, and more.
        ///
        /// * RECIPE - A job to apply one or more transformations to a dataset.
        public var type: DataBrewClientTypes.JobType?
        /// List of validation configurations that are applied to the profile job.
        public var validationConfigurations: [DataBrewClientTypes.ValidationConfiguration]?

        public init(
            accountId: Swift.String? = nil,
            createDate: Foundation.Date? = nil,
            createdBy: Swift.String? = nil,
            dataCatalogOutputs: [DataBrewClientTypes.DataCatalogOutput]? = nil,
            databaseOutputs: [DataBrewClientTypes.DatabaseOutput]? = nil,
            datasetName: Swift.String? = nil,
            encryptionKeyArn: Swift.String? = nil,
            encryptionMode: DataBrewClientTypes.EncryptionMode? = nil,
            jobSample: DataBrewClientTypes.JobSample? = nil,
            lastModifiedBy: Swift.String? = nil,
            lastModifiedDate: Foundation.Date? = nil,
            logSubscription: DataBrewClientTypes.LogSubscription? = nil,
            maxCapacity: Swift.Int = 0,
            maxRetries: Swift.Int = 0,
            name: Swift.String? = nil,
            outputs: [DataBrewClientTypes.Output]? = nil,
            projectName: Swift.String? = nil,
            recipeReference: DataBrewClientTypes.RecipeReference? = nil,
            resourceArn: Swift.String? = nil,
            roleArn: Swift.String? = nil,
            tags: [Swift.String: Swift.String]? = nil,
            timeout: Swift.Int = 0,
            type: DataBrewClientTypes.JobType? = nil,
            validationConfigurations: [DataBrewClientTypes.ValidationConfiguration]? = nil
        )
        {
            self.accountId = accountId
            self.createDate = createDate
            self.createdBy = createdBy
            self.dataCatalogOutputs = dataCatalogOutputs
            self.databaseOutputs = databaseOutputs
            self.datasetName = datasetName
            self.encryptionKeyArn = encryptionKeyArn
            self.encryptionMode = encryptionMode
            self.jobSample = jobSample
            self.lastModifiedBy = lastModifiedBy
            self.lastModifiedDate = lastModifiedDate
            self.logSubscription = logSubscription
            self.maxCapacity = maxCapacity
            self.maxRetries = maxRetries
            self.name = name
            self.outputs = outputs
            self.projectName = projectName
            self.recipeReference = recipeReference
            self.resourceArn = resourceArn
            self.roleArn = roleArn
            self.tags = tags
            self.timeout = timeout
            self.type = type
            self.validationConfigurations = validationConfigurations
        }
    }
}

public struct ListJobsOutput: Swift.Sendable {
    /// A list of jobs that are defined.
    /// This member is required.
    public var jobs: [DataBrewClientTypes.Job]?
    /// A token that you can use in a subsequent call to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        jobs: [DataBrewClientTypes.Job]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.jobs = jobs
        self.nextToken = nextToken
    }
}

public struct ListProjectsInput: Swift.Sendable {
    /// The maximum number of results to return in this request.
    public var maxResults: Swift.Int?
    /// The token returned by a previous call to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DataBrewClientTypes {

    /// Represents all of the attributes of a DataBrew project.
    public struct Project: Swift.Sendable {
        /// The ID of the Amazon Web Services account that owns the project.
        public var accountId: Swift.String?
        /// The date and time that the project was created.
        public var createDate: Foundation.Date?
        /// The Amazon Resource Name (ARN) of the user who crated the project.
        public var createdBy: Swift.String?
        /// The dataset that the project is to act upon.
        public var datasetName: Swift.String?
        /// The Amazon Resource Name (ARN) of the user who last modified the project.
        public var lastModifiedBy: Swift.String?
        /// The last modification date and time for the project.
        public var lastModifiedDate: Foundation.Date?
        /// The unique name of a project.
        /// This member is required.
        public var name: Swift.String?
        /// The date and time when the project was opened.
        public var openDate: Foundation.Date?
        /// The Amazon Resource Name (ARN) of the user that opened the project for use.
        public var openedBy: Swift.String?
        /// The name of a recipe that will be developed during a project session.
        /// This member is required.
        public var recipeName: Swift.String?
        /// The Amazon Resource Name (ARN) for the project.
        public var resourceArn: Swift.String?
        /// The Amazon Resource Name (ARN) of the role that will be assumed for this project.
        public var roleArn: Swift.String?
        /// The sample size and sampling type to apply to the data. If this parameter isn't specified, then the sample consists of the first 500 rows from the dataset.
        public var sample: DataBrewClientTypes.Sample?
        /// Metadata tags that have been applied to the project.
        public var tags: [Swift.String: Swift.String]?

        public init(
            accountId: Swift.String? = nil,
            createDate: Foundation.Date? = nil,
            createdBy: Swift.String? = nil,
            datasetName: Swift.String? = nil,
            lastModifiedBy: Swift.String? = nil,
            lastModifiedDate: Foundation.Date? = nil,
            name: Swift.String? = nil,
            openDate: Foundation.Date? = nil,
            openedBy: Swift.String? = nil,
            recipeName: Swift.String? = nil,
            resourceArn: Swift.String? = nil,
            roleArn: Swift.String? = nil,
            sample: DataBrewClientTypes.Sample? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.accountId = accountId
            self.createDate = createDate
            self.createdBy = createdBy
            self.datasetName = datasetName
            self.lastModifiedBy = lastModifiedBy
            self.lastModifiedDate = lastModifiedDate
            self.name = name
            self.openDate = openDate
            self.openedBy = openedBy
            self.recipeName = recipeName
            self.resourceArn = resourceArn
            self.roleArn = roleArn
            self.sample = sample
            self.tags = tags
        }
    }
}

public struct ListProjectsOutput: Swift.Sendable {
    /// A token that you can use in a subsequent call to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// A list of projects that are defined .
    /// This member is required.
    public var projects: [DataBrewClientTypes.Project]?

    public init(
        nextToken: Swift.String? = nil,
        projects: [DataBrewClientTypes.Project]? = nil
    )
    {
        self.nextToken = nextToken
        self.projects = projects
    }
}

public struct ListRecipesInput: Swift.Sendable {
    /// The maximum number of results to return in this request.
    public var maxResults: Swift.Int?
    /// The token returned by a previous call to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// Return only those recipes with a version identifier of LATEST_WORKING or LATEST_PUBLISHED. If RecipeVersion is omitted, ListRecipes returns all of the LATEST_PUBLISHED recipe versions. Valid values: LATEST_WORKING | LATEST_PUBLISHED
    public var recipeVersion: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        recipeVersion: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.recipeVersion = recipeVersion
    }
}

extension DataBrewClientTypes {

    /// Represents one or more actions to be performed on a DataBrew dataset.
    public struct Recipe: Swift.Sendable {
        /// The date and time that the recipe was created.
        public var createDate: Foundation.Date?
        /// The Amazon Resource Name (ARN) of the user who created the recipe.
        public var createdBy: Swift.String?
        /// The description of the recipe.
        public var description: Swift.String?
        /// The Amazon Resource Name (ARN) of the user who last modified the recipe.
        public var lastModifiedBy: Swift.String?
        /// The last modification date and time of the recipe.
        public var lastModifiedDate: Foundation.Date?
        /// The unique name for the recipe.
        /// This member is required.
        public var name: Swift.String?
        /// The name of the project that the recipe is associated with.
        public var projectName: Swift.String?
        /// The Amazon Resource Name (ARN) of the user who published the recipe.
        public var publishedBy: Swift.String?
        /// The date and time when the recipe was published.
        public var publishedDate: Foundation.Date?
        /// The identifier for the version for the recipe. Must be one of the following:
        ///
        /// * Numeric version (X.Y) - X and Y stand for major and minor version numbers. The maximum length of each is 6 digits, and neither can be negative values. Both X and Y are required, and "0.0" isn't a valid version.
        ///
        /// * LATEST_WORKING - the most recent valid version being developed in a DataBrew project.
        ///
        /// * LATEST_PUBLISHED - the most recent published version.
        public var recipeVersion: Swift.String?
        /// The Amazon Resource Name (ARN) for the recipe.
        public var resourceArn: Swift.String?
        /// A list of steps that are defined by the recipe.
        public var steps: [DataBrewClientTypes.RecipeStep]?
        /// Metadata tags that have been applied to the recipe.
        public var tags: [Swift.String: Swift.String]?

        public init(
            createDate: Foundation.Date? = nil,
            createdBy: Swift.String? = nil,
            description: Swift.String? = nil,
            lastModifiedBy: Swift.String? = nil,
            lastModifiedDate: Foundation.Date? = nil,
            name: Swift.String? = nil,
            projectName: Swift.String? = nil,
            publishedBy: Swift.String? = nil,
            publishedDate: Foundation.Date? = nil,
            recipeVersion: Swift.String? = nil,
            resourceArn: Swift.String? = nil,
            steps: [DataBrewClientTypes.RecipeStep]? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.createDate = createDate
            self.createdBy = createdBy
            self.description = description
            self.lastModifiedBy = lastModifiedBy
            self.lastModifiedDate = lastModifiedDate
            self.name = name
            self.projectName = projectName
            self.publishedBy = publishedBy
            self.publishedDate = publishedDate
            self.recipeVersion = recipeVersion
            self.resourceArn = resourceArn
            self.steps = steps
            self.tags = tags
        }
    }
}

public struct ListRecipesOutput: Swift.Sendable {
    /// A token that you can use in a subsequent call to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// A list of recipes that are defined.
    /// This member is required.
    public var recipes: [DataBrewClientTypes.Recipe]?

    public init(
        nextToken: Swift.String? = nil,
        recipes: [DataBrewClientTypes.Recipe]? = nil
    )
    {
        self.nextToken = nextToken
        self.recipes = recipes
    }
}

public struct ListRecipeVersionsInput: Swift.Sendable {
    /// The maximum number of results to return in this request.
    public var maxResults: Swift.Int?
    /// The name of the recipe for which to return version information.
    /// This member is required.
    public var name: Swift.String?
    /// The token returned by a previous call to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        name: Swift.String? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.name = name
        self.nextToken = nextToken
    }
}

public struct ListRecipeVersionsOutput: Swift.Sendable {
    /// A token that you can use in a subsequent call to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// A list of versions for the specified recipe.
    /// This member is required.
    public var recipes: [DataBrewClientTypes.Recipe]?

    public init(
        nextToken: Swift.String? = nil,
        recipes: [DataBrewClientTypes.Recipe]? = nil
    )
    {
        self.nextToken = nextToken
        self.recipes = recipes
    }
}

public struct ListRulesetsInput: Swift.Sendable {
    /// The maximum number of results to return in this request.
    public var maxResults: Swift.Int?
    /// A token generated by DataBrew that specifies where to continue pagination if a previous request was truncated. To get the next set of pages, pass in the NextToken value from the response object of the previous page call.
    public var nextToken: Swift.String?
    /// The Amazon Resource Name (ARN) of a resource (dataset). Using this parameter indicates to return only those rulesets that are associated with the specified resource.
    public var targetArn: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        targetArn: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.targetArn = targetArn
    }
}

extension DataBrewClientTypes {

    /// Contains metadata about the ruleset.
    public struct RulesetItem: Swift.Sendable {
        /// The ID of the Amazon Web Services account that owns the ruleset.
        public var accountId: Swift.String?
        /// The date and time that the ruleset was created.
        public var createDate: Foundation.Date?
        /// The Amazon Resource Name (ARN) of the user who created the ruleset.
        public var createdBy: Swift.String?
        /// The description of the ruleset.
        public var description: Swift.String?
        /// The Amazon Resource Name (ARN) of the user who last modified the ruleset.
        public var lastModifiedBy: Swift.String?
        /// The modification date and time of the ruleset.
        public var lastModifiedDate: Foundation.Date?
        /// The name of the ruleset.
        /// This member is required.
        public var name: Swift.String?
        /// The Amazon Resource Name (ARN) for the ruleset.
        public var resourceArn: Swift.String?
        /// The number of rules that are defined in the ruleset.
        public var ruleCount: Swift.Int
        /// Metadata tags that have been applied to the ruleset.
        public var tags: [Swift.String: Swift.String]?
        /// The Amazon Resource Name (ARN) of a resource (dataset) that the ruleset is associated with.
        /// This member is required.
        public var targetArn: Swift.String?

        public init(
            accountId: Swift.String? = nil,
            createDate: Foundation.Date? = nil,
            createdBy: Swift.String? = nil,
            description: Swift.String? = nil,
            lastModifiedBy: Swift.String? = nil,
            lastModifiedDate: Foundation.Date? = nil,
            name: Swift.String? = nil,
            resourceArn: Swift.String? = nil,
            ruleCount: Swift.Int = 0,
            tags: [Swift.String: Swift.String]? = nil,
            targetArn: Swift.String? = nil
        )
        {
            self.accountId = accountId
            self.createDate = createDate
            self.createdBy = createdBy
            self.description = description
            self.lastModifiedBy = lastModifiedBy
            self.lastModifiedDate = lastModifiedDate
            self.name = name
            self.resourceArn = resourceArn
            self.ruleCount = ruleCount
            self.tags = tags
            self.targetArn = targetArn
        }
    }
}

public struct ListRulesetsOutput: Swift.Sendable {
    /// A token that you can use in a subsequent call to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// A list of RulesetItem. RulesetItem contains meta data of a ruleset.
    /// This member is required.
    public var rulesets: [DataBrewClientTypes.RulesetItem]?

    public init(
        nextToken: Swift.String? = nil,
        rulesets: [DataBrewClientTypes.RulesetItem]? = nil
    )
    {
        self.nextToken = nextToken
        self.rulesets = rulesets
    }
}

public struct ListSchedulesInput: Swift.Sendable {
    /// The name of the job that these schedules apply to.
    public var jobName: Swift.String?
    /// The maximum number of results to return in this request.
    public var maxResults: Swift.Int?
    /// The token returned by a previous call to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        jobName: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.jobName = jobName
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DataBrewClientTypes {

    /// Represents one or more dates and times when a job is to run.
    public struct Schedule: Swift.Sendable {
        /// The ID of the Amazon Web Services account that owns the schedule.
        public var accountId: Swift.String?
        /// The date and time that the schedule was created.
        public var createDate: Foundation.Date?
        /// The Amazon Resource Name (ARN) of the user who created the schedule.
        public var createdBy: Swift.String?
        /// The dates and times when the job is to run. For more information, see [Cron expressions](https://docs.aws.amazon.com/databrew/latest/dg/jobs.cron.html) in the Glue DataBrew Developer Guide.
        public var cronExpression: Swift.String?
        /// A list of jobs to be run, according to the schedule.
        public var jobNames: [Swift.String]?
        /// The Amazon Resource Name (ARN) of the user who last modified the schedule.
        public var lastModifiedBy: Swift.String?
        /// The date and time when the schedule was last modified.
        public var lastModifiedDate: Foundation.Date?
        /// The name of the schedule.
        /// This member is required.
        public var name: Swift.String?
        /// The Amazon Resource Name (ARN) of the schedule.
        public var resourceArn: Swift.String?
        /// Metadata tags that have been applied to the schedule.
        public var tags: [Swift.String: Swift.String]?

        public init(
            accountId: Swift.String? = nil,
            createDate: Foundation.Date? = nil,
            createdBy: Swift.String? = nil,
            cronExpression: Swift.String? = nil,
            jobNames: [Swift.String]? = nil,
            lastModifiedBy: Swift.String? = nil,
            lastModifiedDate: Foundation.Date? = nil,
            name: Swift.String? = nil,
            resourceArn: Swift.String? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.accountId = accountId
            self.createDate = createDate
            self.createdBy = createdBy
            self.cronExpression = cronExpression
            self.jobNames = jobNames
            self.lastModifiedBy = lastModifiedBy
            self.lastModifiedDate = lastModifiedDate
            self.name = name
            self.resourceArn = resourceArn
            self.tags = tags
        }
    }
}

public struct ListSchedulesOutput: Swift.Sendable {
    /// A token that you can use in a subsequent call to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// A list of schedules that are defined.
    /// This member is required.
    public var schedules: [DataBrewClientTypes.Schedule]?

    public init(
        nextToken: Swift.String? = nil,
        schedules: [DataBrewClientTypes.Schedule]? = nil
    )
    {
        self.nextToken = nextToken
        self.schedules = schedules
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) string that uniquely identifies the DataBrew resource.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// A list of tags associated with the DataBrew resource.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

public struct PublishRecipeInput: Swift.Sendable {
    /// A description of the recipe to be published, for this version of the recipe.
    public var description: Swift.String?
    /// The name of the recipe to be published.
    /// This member is required.
    public var name: Swift.String?

    public init(
        description: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.description = description
        self.name = name
    }
}

public struct PublishRecipeOutput: Swift.Sendable {
    /// The name of the recipe that you published.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

extension DataBrewClientTypes {

    /// Represents the data being transformed during an action.
    public struct ViewFrame: Swift.Sendable {
        /// Controls if analytics computation is enabled or disabled. Enabled by default.
        public var analytics: DataBrewClientTypes.AnalyticsMode?
        /// The number of columns to include in the view frame, beginning with the StartColumnIndex value and ignoring any columns in the HiddenColumns list.
        public var columnRange: Swift.Int?
        /// A list of columns to hide in the view frame.
        public var hiddenColumns: [Swift.String]?
        /// The number of rows to include in the view frame, beginning with the StartRowIndex value.
        public var rowRange: Swift.Int?
        /// The starting index for the range of columns to return in the view frame.
        /// This member is required.
        public var startColumnIndex: Swift.Int?
        /// The starting index for the range of rows to return in the view frame.
        public var startRowIndex: Swift.Int?

        public init(
            analytics: DataBrewClientTypes.AnalyticsMode? = nil,
            columnRange: Swift.Int? = nil,
            hiddenColumns: [Swift.String]? = nil,
            rowRange: Swift.Int? = nil,
            startColumnIndex: Swift.Int? = nil,
            startRowIndex: Swift.Int? = nil
        )
        {
            self.analytics = analytics
            self.columnRange = columnRange
            self.hiddenColumns = hiddenColumns
            self.rowRange = rowRange
            self.startColumnIndex = startColumnIndex
            self.startRowIndex = startRowIndex
        }
    }
}

public struct SendProjectSessionActionInput: Swift.Sendable {
    /// A unique identifier for an interactive session that's currently open and ready for work. The action will be performed on this session.
    public var clientSessionId: Swift.String?
    /// The name of the project to apply the action to.
    /// This member is required.
    public var name: Swift.String?
    /// If true, the result of the recipe step will be returned, but not applied.
    public var preview: Swift.Bool?
    /// Represents a single step from a DataBrew recipe to be performed.
    public var recipeStep: DataBrewClientTypes.RecipeStep?
    /// The index from which to preview a step. This index is used to preview the result of steps that have already been applied, so that the resulting view frame is from earlier in the view frame stack.
    public var stepIndex: Swift.Int?
    /// Represents the data being transformed during an action.
    public var viewFrame: DataBrewClientTypes.ViewFrame?

    public init(
        clientSessionId: Swift.String? = nil,
        name: Swift.String? = nil,
        preview: Swift.Bool? = false,
        recipeStep: DataBrewClientTypes.RecipeStep? = nil,
        stepIndex: Swift.Int? = nil,
        viewFrame: DataBrewClientTypes.ViewFrame? = nil
    )
    {
        self.clientSessionId = clientSessionId
        self.name = name
        self.preview = preview
        self.recipeStep = recipeStep
        self.stepIndex = stepIndex
        self.viewFrame = viewFrame
    }
}

extension SendProjectSessionActionInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "SendProjectSessionActionInput(name: \(Swift.String(describing: name)), preview: \(Swift.String(describing: preview)), recipeStep: \(Swift.String(describing: recipeStep)), stepIndex: \(Swift.String(describing: stepIndex)), viewFrame: \(Swift.String(describing: viewFrame)), clientSessionId: \"CONTENT_REDACTED\")"}
}

public struct SendProjectSessionActionOutput: Swift.Sendable {
    /// A unique identifier for the action that was performed.
    public var actionId: Swift.Int?
    /// The name of the project that was affected by the action.
    /// This member is required.
    public var name: Swift.String?
    /// A message indicating the result of performing the action.
    public var result: Swift.String?

    public init(
        actionId: Swift.Int? = nil,
        name: Swift.String? = nil,
        result: Swift.String? = nil
    )
    {
        self.actionId = actionId
        self.name = name
        self.result = result
    }
}

public struct StartJobRunInput: Swift.Sendable {
    /// The name of the job to be run.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct StartJobRunOutput: Swift.Sendable {
    /// A system-generated identifier for this particular job run.
    /// This member is required.
    public var runId: Swift.String?

    public init(
        runId: Swift.String? = nil
    )
    {
        self.runId = runId
    }
}

public struct StartProjectSessionInput: Swift.Sendable {
    /// A value that, if true, enables you to take control of a session, even if a different client is currently accessing the project.
    public var assumeControl: Swift.Bool?
    /// The name of the project to act upon.
    /// This member is required.
    public var name: Swift.String?

    public init(
        assumeControl: Swift.Bool? = false,
        name: Swift.String? = nil
    )
    {
        self.assumeControl = assumeControl
        self.name = name
    }
}

public struct StartProjectSessionOutput: Swift.Sendable {
    /// A system-generated identifier for the session.
    public var clientSessionId: Swift.String?
    /// The name of the project to be acted upon.
    /// This member is required.
    public var name: Swift.String?

    public init(
        clientSessionId: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.clientSessionId = clientSessionId
        self.name = name
    }
}

extension StartProjectSessionOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "StartProjectSessionOutput(name: \(Swift.String(describing: name)), clientSessionId: \"CONTENT_REDACTED\")"}
}

public struct StopJobRunInput: Swift.Sendable {
    /// The name of the job to be stopped.
    /// This member is required.
    public var name: Swift.String?
    /// The ID of the job run to be stopped.
    /// This member is required.
    public var runId: Swift.String?

    public init(
        name: Swift.String? = nil,
        runId: Swift.String? = nil
    )
    {
        self.name = name
        self.runId = runId
    }
}

public struct StopJobRunOutput: Swift.Sendable {
    /// The ID of the job run that you stopped.
    /// This member is required.
    public var runId: Swift.String?

    public init(
        runId: Swift.String? = nil
    )
    {
        self.runId = runId
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The DataBrew resource to which tags should be added. The value for this parameter is an Amazon Resource Name (ARN). For DataBrew, you can tag a dataset, a job, a project, or a recipe.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// One or more tags to be assigned to the resource.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// A DataBrew resource from which you want to remove a tag or tags. The value for this parameter is an Amazon Resource Name (ARN).
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tag keys (names) of one or more tags to be removed.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateDatasetInput: Swift.Sendable {
    /// The file format of a dataset that is created from an Amazon S3 file or folder.
    public var format: DataBrewClientTypes.InputFormat?
    /// Represents a set of options that define the structure of either comma-separated value (CSV), Excel, or JSON input.
    public var formatOptions: DataBrewClientTypes.FormatOptions?
    /// Represents information on how DataBrew can find data, in either the Glue Data Catalog or Amazon S3.
    /// This member is required.
    public var input: DataBrewClientTypes.Input?
    /// The name of the dataset to be updated.
    /// This member is required.
    public var name: Swift.String?
    /// A set of options that defines how DataBrew interprets an Amazon S3 path of the dataset.
    public var pathOptions: DataBrewClientTypes.PathOptions?

    public init(
        format: DataBrewClientTypes.InputFormat? = nil,
        formatOptions: DataBrewClientTypes.FormatOptions? = nil,
        input: DataBrewClientTypes.Input? = nil,
        name: Swift.String? = nil,
        pathOptions: DataBrewClientTypes.PathOptions? = nil
    )
    {
        self.format = format
        self.formatOptions = formatOptions
        self.input = input
        self.name = name
        self.pathOptions = pathOptions
    }
}

public struct UpdateDatasetOutput: Swift.Sendable {
    /// The name of the dataset that you updated.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct UpdateProfileJobInput: Swift.Sendable {
    /// Configuration for profile jobs. Used to select columns, do evaluations, and override default parameters of evaluations. When configuration is null, the profile job will run with default settings.
    public var configuration: DataBrewClientTypes.ProfileConfiguration?
    /// The Amazon Resource Name (ARN) of an encryption key that is used to protect the job.
    public var encryptionKeyArn: Swift.String?
    /// The encryption mode for the job, which can be one of the following:
    ///
    /// * SSE-KMS - Server-side encryption with keys managed by KMS.
    ///
    /// * SSE-S3 - Server-side encryption with keys managed by Amazon S3.
    public var encryptionMode: DataBrewClientTypes.EncryptionMode?
    /// Sample configuration for Profile Jobs only. Determines the number of rows on which the Profile job will be executed. If a JobSample value is not provided for profile jobs, the default value will be used. The default value is CUSTOM_ROWS for the mode parameter and 20000 for the size parameter.
    public var jobSample: DataBrewClientTypes.JobSample?
    /// Enables or disables Amazon CloudWatch logging for the job. If logging is enabled, CloudWatch writes one log stream for each job run.
    public var logSubscription: DataBrewClientTypes.LogSubscription?
    /// The maximum number of compute nodes that DataBrew can use when the job processes data.
    public var maxCapacity: Swift.Int?
    /// The maximum number of times to retry the job after a job run fails.
    public var maxRetries: Swift.Int?
    /// The name of the job to be updated.
    /// This member is required.
    public var name: Swift.String?
    /// Represents an Amazon S3 location (bucket name, bucket owner, and object key) where DataBrew can read input data, or write output from a job.
    /// This member is required.
    public var outputLocation: DataBrewClientTypes.S3Location?
    /// The Amazon Resource Name (ARN) of the Identity and Access Management (IAM) role to be assumed when DataBrew runs the job.
    /// This member is required.
    public var roleArn: Swift.String?
    /// The job's timeout in minutes. A job that attempts to run longer than this timeout period ends with a status of TIMEOUT.
    public var timeout: Swift.Int?
    /// List of validation configurations that are applied to the profile job.
    public var validationConfigurations: [DataBrewClientTypes.ValidationConfiguration]?

    public init(
        configuration: DataBrewClientTypes.ProfileConfiguration? = nil,
        encryptionKeyArn: Swift.String? = nil,
        encryptionMode: DataBrewClientTypes.EncryptionMode? = nil,
        jobSample: DataBrewClientTypes.JobSample? = nil,
        logSubscription: DataBrewClientTypes.LogSubscription? = nil,
        maxCapacity: Swift.Int? = 0,
        maxRetries: Swift.Int? = 0,
        name: Swift.String? = nil,
        outputLocation: DataBrewClientTypes.S3Location? = nil,
        roleArn: Swift.String? = nil,
        timeout: Swift.Int? = 0,
        validationConfigurations: [DataBrewClientTypes.ValidationConfiguration]? = nil
    )
    {
        self.configuration = configuration
        self.encryptionKeyArn = encryptionKeyArn
        self.encryptionMode = encryptionMode
        self.jobSample = jobSample
        self.logSubscription = logSubscription
        self.maxCapacity = maxCapacity
        self.maxRetries = maxRetries
        self.name = name
        self.outputLocation = outputLocation
        self.roleArn = roleArn
        self.timeout = timeout
        self.validationConfigurations = validationConfigurations
    }
}

public struct UpdateProfileJobOutput: Swift.Sendable {
    /// The name of the job that was updated.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct UpdateProjectInput: Swift.Sendable {
    /// The name of the project to be updated.
    /// This member is required.
    public var name: Swift.String?
    /// The Amazon Resource Name (ARN) of the IAM role to be assumed for this request.
    /// This member is required.
    public var roleArn: Swift.String?
    /// Represents the sample size and sampling type for DataBrew to use for interactive data analysis.
    public var sample: DataBrewClientTypes.Sample?

    public init(
        name: Swift.String? = nil,
        roleArn: Swift.String? = nil,
        sample: DataBrewClientTypes.Sample? = nil
    )
    {
        self.name = name
        self.roleArn = roleArn
        self.sample = sample
    }
}

public struct UpdateProjectOutput: Swift.Sendable {
    /// The date and time that the project was last modified.
    public var lastModifiedDate: Foundation.Date?
    /// The name of the project that you updated.
    /// This member is required.
    public var name: Swift.String?

    public init(
        lastModifiedDate: Foundation.Date? = nil,
        name: Swift.String? = nil
    )
    {
        self.lastModifiedDate = lastModifiedDate
        self.name = name
    }
}

public struct UpdateRecipeInput: Swift.Sendable {
    /// A description of the recipe.
    public var description: Swift.String?
    /// The name of the recipe to be updated.
    /// This member is required.
    public var name: Swift.String?
    /// One or more steps to be performed by the recipe. Each step consists of an action, and the conditions under which the action should succeed.
    public var steps: [DataBrewClientTypes.RecipeStep]?

    public init(
        description: Swift.String? = nil,
        name: Swift.String? = nil,
        steps: [DataBrewClientTypes.RecipeStep]? = nil
    )
    {
        self.description = description
        self.name = name
        self.steps = steps
    }
}

public struct UpdateRecipeOutput: Swift.Sendable {
    /// The name of the recipe that was updated.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct UpdateRecipeJobInput: Swift.Sendable {
    /// One or more artifacts that represent the Glue Data Catalog output from running the job.
    public var dataCatalogOutputs: [DataBrewClientTypes.DataCatalogOutput]?
    /// Represents a list of JDBC database output objects which defines the output destination for a DataBrew recipe job to write into.
    public var databaseOutputs: [DataBrewClientTypes.DatabaseOutput]?
    /// The Amazon Resource Name (ARN) of an encryption key that is used to protect the job.
    public var encryptionKeyArn: Swift.String?
    /// The encryption mode for the job, which can be one of the following:
    ///
    /// * SSE-KMS - Server-side encryption with keys managed by KMS.
    ///
    /// * SSE-S3 - Server-side encryption with keys managed by Amazon S3.
    public var encryptionMode: DataBrewClientTypes.EncryptionMode?
    /// Enables or disables Amazon CloudWatch logging for the job. If logging is enabled, CloudWatch writes one log stream for each job run.
    public var logSubscription: DataBrewClientTypes.LogSubscription?
    /// The maximum number of nodes that DataBrew can consume when the job processes data.
    public var maxCapacity: Swift.Int?
    /// The maximum number of times to retry the job after a job run fails.
    public var maxRetries: Swift.Int?
    /// The name of the job to update.
    /// This member is required.
    public var name: Swift.String?
    /// One or more artifacts that represent the output from running the job.
    public var outputs: [DataBrewClientTypes.Output]?
    /// The Amazon Resource Name (ARN) of the Identity and Access Management (IAM) role to be assumed when DataBrew runs the job.
    /// This member is required.
    public var roleArn: Swift.String?
    /// The job's timeout in minutes. A job that attempts to run longer than this timeout period ends with a status of TIMEOUT.
    public var timeout: Swift.Int?

    public init(
        dataCatalogOutputs: [DataBrewClientTypes.DataCatalogOutput]? = nil,
        databaseOutputs: [DataBrewClientTypes.DatabaseOutput]? = nil,
        encryptionKeyArn: Swift.String? = nil,
        encryptionMode: DataBrewClientTypes.EncryptionMode? = nil,
        logSubscription: DataBrewClientTypes.LogSubscription? = nil,
        maxCapacity: Swift.Int? = 0,
        maxRetries: Swift.Int? = 0,
        name: Swift.String? = nil,
        outputs: [DataBrewClientTypes.Output]? = nil,
        roleArn: Swift.String? = nil,
        timeout: Swift.Int? = 0
    )
    {
        self.dataCatalogOutputs = dataCatalogOutputs
        self.databaseOutputs = databaseOutputs
        self.encryptionKeyArn = encryptionKeyArn
        self.encryptionMode = encryptionMode
        self.logSubscription = logSubscription
        self.maxCapacity = maxCapacity
        self.maxRetries = maxRetries
        self.name = name
        self.outputs = outputs
        self.roleArn = roleArn
        self.timeout = timeout
    }
}

public struct UpdateRecipeJobOutput: Swift.Sendable {
    /// The name of the job that you updated.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct UpdateRulesetInput: Swift.Sendable {
    /// The description of the ruleset.
    public var description: Swift.String?
    /// The name of the ruleset to be updated.
    /// This member is required.
    public var name: Swift.String?
    /// A list of rules that are defined with the ruleset. A rule includes one or more checks to be validated on a DataBrew dataset.
    /// This member is required.
    public var rules: [DataBrewClientTypes.Rule]?

    public init(
        description: Swift.String? = nil,
        name: Swift.String? = nil,
        rules: [DataBrewClientTypes.Rule]? = nil
    )
    {
        self.description = description
        self.name = name
        self.rules = rules
    }
}

public struct UpdateRulesetOutput: Swift.Sendable {
    /// The name of the updated ruleset.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

public struct UpdateScheduleInput: Swift.Sendable {
    /// The date or dates and time or times when the jobs are to be run. For more information, see [Cron expressions](https://docs.aws.amazon.com/databrew/latest/dg/jobs.cron.html) in the Glue DataBrew Developer Guide.
    /// This member is required.
    public var cronExpression: Swift.String?
    /// The name or names of one or more jobs to be run for this schedule.
    public var jobNames: [Swift.String]?
    /// The name of the schedule to update.
    /// This member is required.
    public var name: Swift.String?

    public init(
        cronExpression: Swift.String? = nil,
        jobNames: [Swift.String]? = nil,
        name: Swift.String? = nil
    )
    {
        self.cronExpression = cronExpression
        self.jobNames = jobNames
        self.name = name
    }
}

public struct UpdateScheduleOutput: Swift.Sendable {
    /// The name of the schedule that was updated.
    /// This member is required.
    public var name: Swift.String?

    public init(
        name: Swift.String? = nil
    )
    {
        self.name = name
    }
}

extension BatchDeleteRecipeVersionInput {

    static func urlPathProvider(_ value: BatchDeleteRecipeVersionInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/recipes/\(name.urlPercentEncoding())/batchDeleteRecipeVersion"
    }
}

extension CreateDatasetInput {

    static func urlPathProvider(_ value: CreateDatasetInput) -> Swift.String? {
        return "/datasets"
    }
}

extension CreateProfileJobInput {

    static func urlPathProvider(_ value: CreateProfileJobInput) -> Swift.String? {
        return "/profileJobs"
    }
}

extension CreateProjectInput {

    static func urlPathProvider(_ value: CreateProjectInput) -> Swift.String? {
        return "/projects"
    }
}

extension CreateRecipeInput {

    static func urlPathProvider(_ value: CreateRecipeInput) -> Swift.String? {
        return "/recipes"
    }
}

extension CreateRecipeJobInput {

    static func urlPathProvider(_ value: CreateRecipeJobInput) -> Swift.String? {
        return "/recipeJobs"
    }
}

extension CreateRulesetInput {

    static func urlPathProvider(_ value: CreateRulesetInput) -> Swift.String? {
        return "/rulesets"
    }
}

extension CreateScheduleInput {

    static func urlPathProvider(_ value: CreateScheduleInput) -> Swift.String? {
        return "/schedules"
    }
}

extension DeleteDatasetInput {

    static func urlPathProvider(_ value: DeleteDatasetInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/datasets/\(name.urlPercentEncoding())"
    }
}

extension DeleteJobInput {

    static func urlPathProvider(_ value: DeleteJobInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/jobs/\(name.urlPercentEncoding())"
    }
}

extension DeleteProjectInput {

    static func urlPathProvider(_ value: DeleteProjectInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/projects/\(name.urlPercentEncoding())"
    }
}

extension DeleteRecipeVersionInput {

    static func urlPathProvider(_ value: DeleteRecipeVersionInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        guard let recipeVersion = value.recipeVersion else {
            return nil
        }
        return "/recipes/\(name.urlPercentEncoding())/recipeVersion/\(recipeVersion.urlPercentEncoding())"
    }
}

extension DeleteRulesetInput {

    static func urlPathProvider(_ value: DeleteRulesetInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/rulesets/\(name.urlPercentEncoding())"
    }
}

extension DeleteScheduleInput {

    static func urlPathProvider(_ value: DeleteScheduleInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/schedules/\(name.urlPercentEncoding())"
    }
}

extension DescribeDatasetInput {

    static func urlPathProvider(_ value: DescribeDatasetInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/datasets/\(name.urlPercentEncoding())"
    }
}

extension DescribeJobInput {

    static func urlPathProvider(_ value: DescribeJobInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/jobs/\(name.urlPercentEncoding())"
    }
}

extension DescribeJobRunInput {

    static func urlPathProvider(_ value: DescribeJobRunInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        guard let runId = value.runId else {
            return nil
        }
        return "/jobs/\(name.urlPercentEncoding())/jobRun/\(runId.urlPercentEncoding())"
    }
}

extension DescribeProjectInput {

    static func urlPathProvider(_ value: DescribeProjectInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/projects/\(name.urlPercentEncoding())"
    }
}

extension DescribeRecipeInput {

    static func urlPathProvider(_ value: DescribeRecipeInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/recipes/\(name.urlPercentEncoding())"
    }
}

extension DescribeRecipeInput {

    static func queryItemProvider(_ value: DescribeRecipeInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let recipeVersion = value.recipeVersion {
            let recipeVersionQueryItem = Smithy.URIQueryItem(name: "recipeVersion".urlPercentEncoding(), value: Swift.String(recipeVersion).urlPercentEncoding())
            items.append(recipeVersionQueryItem)
        }
        return items
    }
}

extension DescribeRulesetInput {

    static func urlPathProvider(_ value: DescribeRulesetInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/rulesets/\(name.urlPercentEncoding())"
    }
}

extension DescribeScheduleInput {

    static func urlPathProvider(_ value: DescribeScheduleInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/schedules/\(name.urlPercentEncoding())"
    }
}

extension ListDatasetsInput {

    static func urlPathProvider(_ value: ListDatasetsInput) -> Swift.String? {
        return "/datasets"
    }
}

extension ListDatasetsInput {

    static func queryItemProvider(_ value: ListDatasetsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListJobRunsInput {

    static func urlPathProvider(_ value: ListJobRunsInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/jobs/\(name.urlPercentEncoding())/jobRuns"
    }
}

extension ListJobRunsInput {

    static func queryItemProvider(_ value: ListJobRunsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListJobsInput {

    static func urlPathProvider(_ value: ListJobsInput) -> Swift.String? {
        return "/jobs"
    }
}

extension ListJobsInput {

    static func queryItemProvider(_ value: ListJobsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let projectName = value.projectName {
            let projectNameQueryItem = Smithy.URIQueryItem(name: "projectName".urlPercentEncoding(), value: Swift.String(projectName).urlPercentEncoding())
            items.append(projectNameQueryItem)
        }
        if let datasetName = value.datasetName {
            let datasetNameQueryItem = Smithy.URIQueryItem(name: "datasetName".urlPercentEncoding(), value: Swift.String(datasetName).urlPercentEncoding())
            items.append(datasetNameQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListProjectsInput {

    static func urlPathProvider(_ value: ListProjectsInput) -> Swift.String? {
        return "/projects"
    }
}

extension ListProjectsInput {

    static func queryItemProvider(_ value: ListProjectsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListRecipesInput {

    static func urlPathProvider(_ value: ListRecipesInput) -> Swift.String? {
        return "/recipes"
    }
}

extension ListRecipesInput {

    static func queryItemProvider(_ value: ListRecipesInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let recipeVersion = value.recipeVersion {
            let recipeVersionQueryItem = Smithy.URIQueryItem(name: "recipeVersion".urlPercentEncoding(), value: Swift.String(recipeVersion).urlPercentEncoding())
            items.append(recipeVersionQueryItem)
        }
        return items
    }
}

extension ListRecipeVersionsInput {

    static func urlPathProvider(_ value: ListRecipeVersionsInput) -> Swift.String? {
        return "/recipeVersions"
    }
}

extension ListRecipeVersionsInput {

    static func queryItemProvider(_ value: ListRecipeVersionsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        guard let name = value.name else {
            let message = "Creating a URL Query Item failed. name is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let nameQueryItem = Smithy.URIQueryItem(name: "name".urlPercentEncoding(), value: Swift.String(name).urlPercentEncoding())
        items.append(nameQueryItem)
        return items
    }
}

extension ListRulesetsInput {

    static func urlPathProvider(_ value: ListRulesetsInput) -> Swift.String? {
        return "/rulesets"
    }
}

extension ListRulesetsInput {

    static func queryItemProvider(_ value: ListRulesetsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let targetArn = value.targetArn {
            let targetArnQueryItem = Smithy.URIQueryItem(name: "targetArn".urlPercentEncoding(), value: Swift.String(targetArn).urlPercentEncoding())
            items.append(targetArnQueryItem)
        }
        return items
    }
}

extension ListSchedulesInput {

    static func urlPathProvider(_ value: ListSchedulesInput) -> Swift.String? {
        return "/schedules"
    }
}

extension ListSchedulesInput {

    static func queryItemProvider(_ value: ListSchedulesInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let jobName = value.jobName {
            let jobNameQueryItem = Smithy.URIQueryItem(name: "jobName".urlPercentEncoding(), value: Swift.String(jobName).urlPercentEncoding())
            items.append(jobNameQueryItem)
        }
        return items
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension PublishRecipeInput {

    static func urlPathProvider(_ value: PublishRecipeInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/recipes/\(name.urlPercentEncoding())/publishRecipe"
    }
}

extension SendProjectSessionActionInput {

    static func urlPathProvider(_ value: SendProjectSessionActionInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/projects/\(name.urlPercentEncoding())/sendProjectSessionAction"
    }
}

extension StartJobRunInput {

    static func urlPathProvider(_ value: StartJobRunInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/jobs/\(name.urlPercentEncoding())/startJobRun"
    }
}

extension StartProjectSessionInput {

    static func urlPathProvider(_ value: StartProjectSessionInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/projects/\(name.urlPercentEncoding())/startProjectSession"
    }
}

extension StopJobRunInput {

    static func urlPathProvider(_ value: StopJobRunInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        guard let runId = value.runId else {
            return nil
        }
        return "/jobs/\(name.urlPercentEncoding())/jobRun/\(runId.urlPercentEncoding())/stopJobRun"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension UpdateDatasetInput {

    static func urlPathProvider(_ value: UpdateDatasetInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/datasets/\(name.urlPercentEncoding())"
    }
}

extension UpdateProfileJobInput {

    static func urlPathProvider(_ value: UpdateProfileJobInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/profileJobs/\(name.urlPercentEncoding())"
    }
}

extension UpdateProjectInput {

    static func urlPathProvider(_ value: UpdateProjectInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/projects/\(name.urlPercentEncoding())"
    }
}

extension UpdateRecipeInput {

    static func urlPathProvider(_ value: UpdateRecipeInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/recipes/\(name.urlPercentEncoding())"
    }
}

extension UpdateRecipeJobInput {

    static func urlPathProvider(_ value: UpdateRecipeJobInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/recipeJobs/\(name.urlPercentEncoding())"
    }
}

extension UpdateRulesetInput {

    static func urlPathProvider(_ value: UpdateRulesetInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/rulesets/\(name.urlPercentEncoding())"
    }
}

extension UpdateScheduleInput {

    static func urlPathProvider(_ value: UpdateScheduleInput) -> Swift.String? {
        guard let name = value.name else {
            return nil
        }
        return "/schedules/\(name.urlPercentEncoding())"
    }
}

extension BatchDeleteRecipeVersionInput {

    static func write(value: BatchDeleteRecipeVersionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["RecipeVersions"].writeList(value.recipeVersions, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateDatasetInput {

    static func write(value: CreateDatasetInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Format"].write(value.format)
        try writer["FormatOptions"].write(value.formatOptions, with: DataBrewClientTypes.FormatOptions.write(value:to:))
        try writer["Input"].write(value.input, with: DataBrewClientTypes.Input.write(value:to:))
        try writer["Name"].write(value.name)
        try writer["PathOptions"].write(value.pathOptions, with: DataBrewClientTypes.PathOptions.write(value:to:))
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CreateProfileJobInput {

    static func write(value: CreateProfileJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Configuration"].write(value.configuration, with: DataBrewClientTypes.ProfileConfiguration.write(value:to:))
        try writer["DatasetName"].write(value.datasetName)
        try writer["EncryptionKeyArn"].write(value.encryptionKeyArn)
        try writer["EncryptionMode"].write(value.encryptionMode)
        try writer["JobSample"].write(value.jobSample, with: DataBrewClientTypes.JobSample.write(value:to:))
        try writer["LogSubscription"].write(value.logSubscription)
        try writer["MaxCapacity"].write(value.maxCapacity)
        try writer["MaxRetries"].write(value.maxRetries)
        try writer["Name"].write(value.name)
        try writer["OutputLocation"].write(value.outputLocation, with: DataBrewClientTypes.S3Location.write(value:to:))
        try writer["RoleArn"].write(value.roleArn)
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["Timeout"].write(value.timeout)
        try writer["ValidationConfigurations"].writeList(value.validationConfigurations, memberWritingClosure: DataBrewClientTypes.ValidationConfiguration.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateProjectInput {

    static func write(value: CreateProjectInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DatasetName"].write(value.datasetName)
        try writer["Name"].write(value.name)
        try writer["RecipeName"].write(value.recipeName)
        try writer["RoleArn"].write(value.roleArn)
        try writer["Sample"].write(value.sample, with: DataBrewClientTypes.Sample.write(value:to:))
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CreateRecipeInput {

    static func write(value: CreateRecipeInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Description"].write(value.description)
        try writer["Name"].write(value.name)
        try writer["Steps"].writeList(value.steps, memberWritingClosure: DataBrewClientTypes.RecipeStep.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CreateRecipeJobInput {

    static func write(value: CreateRecipeJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DataCatalogOutputs"].writeList(value.dataCatalogOutputs, memberWritingClosure: DataBrewClientTypes.DataCatalogOutput.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["DatabaseOutputs"].writeList(value.databaseOutputs, memberWritingClosure: DataBrewClientTypes.DatabaseOutput.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["DatasetName"].write(value.datasetName)
        try writer["EncryptionKeyArn"].write(value.encryptionKeyArn)
        try writer["EncryptionMode"].write(value.encryptionMode)
        try writer["LogSubscription"].write(value.logSubscription)
        try writer["MaxCapacity"].write(value.maxCapacity)
        try writer["MaxRetries"].write(value.maxRetries)
        try writer["Name"].write(value.name)
        try writer["Outputs"].writeList(value.outputs, memberWritingClosure: DataBrewClientTypes.Output.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ProjectName"].write(value.projectName)
        try writer["RecipeReference"].write(value.recipeReference, with: DataBrewClientTypes.RecipeReference.write(value:to:))
        try writer["RoleArn"].write(value.roleArn)
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["Timeout"].write(value.timeout)
    }
}

extension CreateRulesetInput {

    static func write(value: CreateRulesetInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Description"].write(value.description)
        try writer["Name"].write(value.name)
        try writer["Rules"].writeList(value.rules, memberWritingClosure: DataBrewClientTypes.Rule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["TargetArn"].write(value.targetArn)
    }
}

extension CreateScheduleInput {

    static func write(value: CreateScheduleInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CronExpression"].write(value.cronExpression)
        try writer["JobNames"].writeList(value.jobNames, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Name"].write(value.name)
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension PublishRecipeInput {

    static func write(value: PublishRecipeInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Description"].write(value.description)
    }
}

extension SendProjectSessionActionInput {

    static func write(value: SendProjectSessionActionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientSessionId"].write(value.clientSessionId)
        try writer["Preview"].write(value.preview)
        try writer["RecipeStep"].write(value.recipeStep, with: DataBrewClientTypes.RecipeStep.write(value:to:))
        try writer["StepIndex"].write(value.stepIndex)
        try writer["ViewFrame"].write(value.viewFrame, with: DataBrewClientTypes.ViewFrame.write(value:to:))
    }
}

extension StartProjectSessionInput {

    static func write(value: StartProjectSessionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AssumeControl"].write(value.assumeControl)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension UpdateDatasetInput {

    static func write(value: UpdateDatasetInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Format"].write(value.format)
        try writer["FormatOptions"].write(value.formatOptions, with: DataBrewClientTypes.FormatOptions.write(value:to:))
        try writer["Input"].write(value.input, with: DataBrewClientTypes.Input.write(value:to:))
        try writer["PathOptions"].write(value.pathOptions, with: DataBrewClientTypes.PathOptions.write(value:to:))
    }
}

extension UpdateProfileJobInput {

    static func write(value: UpdateProfileJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Configuration"].write(value.configuration, with: DataBrewClientTypes.ProfileConfiguration.write(value:to:))
        try writer["EncryptionKeyArn"].write(value.encryptionKeyArn)
        try writer["EncryptionMode"].write(value.encryptionMode)
        try writer["JobSample"].write(value.jobSample, with: DataBrewClientTypes.JobSample.write(value:to:))
        try writer["LogSubscription"].write(value.logSubscription)
        try writer["MaxCapacity"].write(value.maxCapacity)
        try writer["MaxRetries"].write(value.maxRetries)
        try writer["OutputLocation"].write(value.outputLocation, with: DataBrewClientTypes.S3Location.write(value:to:))
        try writer["RoleArn"].write(value.roleArn)
        try writer["Timeout"].write(value.timeout)
        try writer["ValidationConfigurations"].writeList(value.validationConfigurations, memberWritingClosure: DataBrewClientTypes.ValidationConfiguration.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdateProjectInput {

    static func write(value: UpdateProjectInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["RoleArn"].write(value.roleArn)
        try writer["Sample"].write(value.sample, with: DataBrewClientTypes.Sample.write(value:to:))
    }
}

extension UpdateRecipeInput {

    static func write(value: UpdateRecipeInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Description"].write(value.description)
        try writer["Steps"].writeList(value.steps, memberWritingClosure: DataBrewClientTypes.RecipeStep.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdateRecipeJobInput {

    static func write(value: UpdateRecipeJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DataCatalogOutputs"].writeList(value.dataCatalogOutputs, memberWritingClosure: DataBrewClientTypes.DataCatalogOutput.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["DatabaseOutputs"].writeList(value.databaseOutputs, memberWritingClosure: DataBrewClientTypes.DatabaseOutput.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["EncryptionKeyArn"].write(value.encryptionKeyArn)
        try writer["EncryptionMode"].write(value.encryptionMode)
        try writer["LogSubscription"].write(value.logSubscription)
        try writer["MaxCapacity"].write(value.maxCapacity)
        try writer["MaxRetries"].write(value.maxRetries)
        try writer["Outputs"].writeList(value.outputs, memberWritingClosure: DataBrewClientTypes.Output.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["RoleArn"].write(value.roleArn)
        try writer["Timeout"].write(value.timeout)
    }
}

extension UpdateRulesetInput {

    static func write(value: UpdateRulesetInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Description"].write(value.description)
        try writer["Rules"].writeList(value.rules, memberWritingClosure: DataBrewClientTypes.Rule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdateScheduleInput {

    static func write(value: UpdateScheduleInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CronExpression"].write(value.cronExpression)
        try writer["JobNames"].writeList(value.jobNames, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension BatchDeleteRecipeVersionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> BatchDeleteRecipeVersionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = BatchDeleteRecipeVersionOutput()
        value.errors = try reader["Errors"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.RecipeVersionErrorDetail.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension CreateDatasetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateDatasetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateDatasetOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension CreateProfileJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateProfileJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateProfileJobOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension CreateProjectOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateProjectOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateProjectOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension CreateRecipeOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateRecipeOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateRecipeOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension CreateRecipeJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateRecipeJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateRecipeJobOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension CreateRulesetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateRulesetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateRulesetOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension CreateScheduleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateScheduleOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateScheduleOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension DeleteDatasetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteDatasetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteDatasetOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension DeleteJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteJobOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension DeleteProjectOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteProjectOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteProjectOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension DeleteRecipeVersionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteRecipeVersionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteRecipeVersionOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.recipeVersion = try reader["RecipeVersion"].readIfPresent() ?? ""
        return value
    }
}

extension DeleteRulesetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteRulesetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteRulesetOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension DeleteScheduleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteScheduleOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteScheduleOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension DescribeDatasetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDatasetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeDatasetOutput()
        value.createDate = try reader["CreateDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.createdBy = try reader["CreatedBy"].readIfPresent()
        value.format = try reader["Format"].readIfPresent()
        value.formatOptions = try reader["FormatOptions"].readIfPresent(with: DataBrewClientTypes.FormatOptions.read(from:))
        value.input = try reader["Input"].readIfPresent(with: DataBrewClientTypes.Input.read(from:))
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.pathOptions = try reader["PathOptions"].readIfPresent(with: DataBrewClientTypes.PathOptions.read(from:))
        value.resourceArn = try reader["ResourceArn"].readIfPresent()
        value.source = try reader["Source"].readIfPresent()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DescribeJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeJobOutput()
        value.createDate = try reader["CreateDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.createdBy = try reader["CreatedBy"].readIfPresent()
        value.dataCatalogOutputs = try reader["DataCatalogOutputs"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.DataCatalogOutput.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.databaseOutputs = try reader["DatabaseOutputs"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.DatabaseOutput.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.datasetName = try reader["DatasetName"].readIfPresent()
        value.encryptionKeyArn = try reader["EncryptionKeyArn"].readIfPresent()
        value.encryptionMode = try reader["EncryptionMode"].readIfPresent()
        value.jobSample = try reader["JobSample"].readIfPresent(with: DataBrewClientTypes.JobSample.read(from:))
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.logSubscription = try reader["LogSubscription"].readIfPresent()
        value.maxCapacity = try reader["MaxCapacity"].readIfPresent() ?? 0
        value.maxRetries = try reader["MaxRetries"].readIfPresent() ?? 0
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.outputs = try reader["Outputs"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.Output.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.profileConfiguration = try reader["ProfileConfiguration"].readIfPresent(with: DataBrewClientTypes.ProfileConfiguration.read(from:))
        value.projectName = try reader["ProjectName"].readIfPresent()
        value.recipeReference = try reader["RecipeReference"].readIfPresent(with: DataBrewClientTypes.RecipeReference.read(from:))
        value.resourceArn = try reader["ResourceArn"].readIfPresent()
        value.roleArn = try reader["RoleArn"].readIfPresent()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.timeout = try reader["Timeout"].readIfPresent() ?? 0
        value.type = try reader["Type"].readIfPresent()
        value.validationConfigurations = try reader["ValidationConfigurations"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.ValidationConfiguration.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeJobRunOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeJobRunOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeJobRunOutput()
        value.attempt = try reader["Attempt"].readIfPresent() ?? 0
        value.completedOn = try reader["CompletedOn"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.dataCatalogOutputs = try reader["DataCatalogOutputs"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.DataCatalogOutput.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.databaseOutputs = try reader["DatabaseOutputs"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.DatabaseOutput.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.datasetName = try reader["DatasetName"].readIfPresent()
        value.errorMessage = try reader["ErrorMessage"].readIfPresent()
        value.executionTime = try reader["ExecutionTime"].readIfPresent() ?? 0
        value.jobName = try reader["JobName"].readIfPresent() ?? ""
        value.jobSample = try reader["JobSample"].readIfPresent(with: DataBrewClientTypes.JobSample.read(from:))
        value.logGroupName = try reader["LogGroupName"].readIfPresent()
        value.logSubscription = try reader["LogSubscription"].readIfPresent()
        value.outputs = try reader["Outputs"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.Output.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.profileConfiguration = try reader["ProfileConfiguration"].readIfPresent(with: DataBrewClientTypes.ProfileConfiguration.read(from:))
        value.recipeReference = try reader["RecipeReference"].readIfPresent(with: DataBrewClientTypes.RecipeReference.read(from:))
        value.runId = try reader["RunId"].readIfPresent()
        value.startedBy = try reader["StartedBy"].readIfPresent()
        value.startedOn = try reader["StartedOn"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.state = try reader["State"].readIfPresent()
        value.validationConfigurations = try reader["ValidationConfigurations"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.ValidationConfiguration.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeProjectOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeProjectOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeProjectOutput()
        value.createDate = try reader["CreateDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.createdBy = try reader["CreatedBy"].readIfPresent()
        value.datasetName = try reader["DatasetName"].readIfPresent()
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.openDate = try reader["OpenDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.openedBy = try reader["OpenedBy"].readIfPresent()
        value.recipeName = try reader["RecipeName"].readIfPresent()
        value.resourceArn = try reader["ResourceArn"].readIfPresent()
        value.roleArn = try reader["RoleArn"].readIfPresent()
        value.sample = try reader["Sample"].readIfPresent(with: DataBrewClientTypes.Sample.read(from:))
        value.sessionStatus = try reader["SessionStatus"].readIfPresent()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DescribeRecipeOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeRecipeOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeRecipeOutput()
        value.createDate = try reader["CreateDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.createdBy = try reader["CreatedBy"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.projectName = try reader["ProjectName"].readIfPresent()
        value.publishedBy = try reader["PublishedBy"].readIfPresent()
        value.publishedDate = try reader["PublishedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.recipeVersion = try reader["RecipeVersion"].readIfPresent()
        value.resourceArn = try reader["ResourceArn"].readIfPresent()
        value.steps = try reader["Steps"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.RecipeStep.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DescribeRulesetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeRulesetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeRulesetOutput()
        value.createDate = try reader["CreateDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.createdBy = try reader["CreatedBy"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.resourceArn = try reader["ResourceArn"].readIfPresent()
        value.rules = try reader["Rules"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.Rule.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.targetArn = try reader["TargetArn"].readIfPresent()
        return value
    }
}

extension DescribeScheduleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeScheduleOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeScheduleOutput()
        value.createDate = try reader["CreateDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.createdBy = try reader["CreatedBy"].readIfPresent()
        value.cronExpression = try reader["CronExpression"].readIfPresent()
        value.jobNames = try reader["JobNames"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.resourceArn = try reader["ResourceArn"].readIfPresent()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension ListDatasetsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListDatasetsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListDatasetsOutput()
        value.datasets = try reader["Datasets"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.Dataset.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListJobRunsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListJobRunsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListJobRunsOutput()
        value.jobRuns = try reader["JobRuns"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.JobRun.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListJobsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListJobsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListJobsOutput()
        value.jobs = try reader["Jobs"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.Job.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListProjectsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListProjectsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListProjectsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.projects = try reader["Projects"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.Project.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ListRecipesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRecipesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRecipesOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.recipes = try reader["Recipes"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.Recipe.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ListRecipeVersionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRecipeVersionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRecipeVersionsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.recipes = try reader["Recipes"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.Recipe.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ListRulesetsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRulesetsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRulesetsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.rulesets = try reader["Rulesets"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.RulesetItem.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ListSchedulesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListSchedulesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListSchedulesOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.schedules = try reader["Schedules"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.Schedule.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension PublishRecipeOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PublishRecipeOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PublishRecipeOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension SendProjectSessionActionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SendProjectSessionActionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = SendProjectSessionActionOutput()
        value.actionId = try reader["ActionId"].readIfPresent()
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.result = try reader["Result"].readIfPresent()
        return value
    }
}

extension StartJobRunOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartJobRunOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartJobRunOutput()
        value.runId = try reader["RunId"].readIfPresent() ?? ""
        return value
    }
}

extension StartProjectSessionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartProjectSessionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartProjectSessionOutput()
        value.clientSessionId = try reader["ClientSessionId"].readIfPresent()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension StopJobRunOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopJobRunOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StopJobRunOutput()
        value.runId = try reader["RunId"].readIfPresent() ?? ""
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateDatasetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateDatasetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateDatasetOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension UpdateProfileJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateProfileJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateProfileJobOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension UpdateProjectOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateProjectOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateProjectOutput()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension UpdateRecipeOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateRecipeOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateRecipeOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension UpdateRecipeJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateRecipeJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateRecipeJobOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension UpdateRulesetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateRulesetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateRulesetOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension UpdateScheduleOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateScheduleOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateScheduleOutput()
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

enum BatchDeleteRecipeVersionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateDatasetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateProfileJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateProjectOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateRecipeOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateRecipeJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateRulesetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateScheduleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteDatasetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteProjectOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteRecipeVersionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteRulesetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteScheduleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDatasetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeJobRunOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeProjectOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeRecipeOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeRulesetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeScheduleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListDatasetsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListJobRunsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListJobsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListProjectsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRecipesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRecipeVersionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRulesetsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListSchedulesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PublishRecipeOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SendProjectSessionActionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartJobRunOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartProjectSessionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopJobRunOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateDatasetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateProfileJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateProjectOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateRecipeOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateRecipeJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateRulesetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateScheduleOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceQuotaExceededException {
        let reader = baseError.errorBodyReader
        var value = ServiceQuotaExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DataBrewClientTypes.RecipeVersionErrorDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.RecipeVersionErrorDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.RecipeVersionErrorDetail()
        value.errorCode = try reader["ErrorCode"].readIfPresent()
        value.errorMessage = try reader["ErrorMessage"].readIfPresent()
        value.recipeVersion = try reader["RecipeVersion"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.FormatOptions {

    static func write(value: DataBrewClientTypes.FormatOptions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Csv"].write(value.csv, with: DataBrewClientTypes.CsvOptions.write(value:to:))
        try writer["Excel"].write(value.excel, with: DataBrewClientTypes.ExcelOptions.write(value:to:))
        try writer["Json"].write(value.json, with: DataBrewClientTypes.JsonOptions.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.FormatOptions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.FormatOptions()
        value.json = try reader["Json"].readIfPresent(with: DataBrewClientTypes.JsonOptions.read(from:))
        value.excel = try reader["Excel"].readIfPresent(with: DataBrewClientTypes.ExcelOptions.read(from:))
        value.csv = try reader["Csv"].readIfPresent(with: DataBrewClientTypes.CsvOptions.read(from:))
        return value
    }
}

extension DataBrewClientTypes.CsvOptions {

    static func write(value: DataBrewClientTypes.CsvOptions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Delimiter"].write(value.delimiter)
        try writer["HeaderRow"].write(value.headerRow)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.CsvOptions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.CsvOptions()
        value.delimiter = try reader["Delimiter"].readIfPresent()
        value.headerRow = try reader["HeaderRow"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.ExcelOptions {

    static func write(value: DataBrewClientTypes.ExcelOptions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["HeaderRow"].write(value.headerRow)
        try writer["SheetIndexes"].writeList(value.sheetIndexes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeInt(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["SheetNames"].writeList(value.sheetNames, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.ExcelOptions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.ExcelOptions()
        value.sheetNames = try reader["SheetNames"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.sheetIndexes = try reader["SheetIndexes"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readInt(from:), memberNodeInfo: "member", isFlattened: false)
        value.headerRow = try reader["HeaderRow"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.JsonOptions {

    static func write(value: DataBrewClientTypes.JsonOptions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MultiLine"].write(value.multiLine)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.JsonOptions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.JsonOptions()
        value.multiLine = try reader["MultiLine"].readIfPresent() ?? false
        return value
    }
}

extension DataBrewClientTypes.Input {

    static func write(value: DataBrewClientTypes.Input?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DataCatalogInputDefinition"].write(value.dataCatalogInputDefinition, with: DataBrewClientTypes.DataCatalogInputDefinition.write(value:to:))
        try writer["DatabaseInputDefinition"].write(value.databaseInputDefinition, with: DataBrewClientTypes.DatabaseInputDefinition.write(value:to:))
        try writer["Metadata"].write(value.metadata, with: DataBrewClientTypes.Metadata.write(value:to:))
        try writer["S3InputDefinition"].write(value.s3InputDefinition, with: DataBrewClientTypes.S3Location.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.Input {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.Input()
        value.s3InputDefinition = try reader["S3InputDefinition"].readIfPresent(with: DataBrewClientTypes.S3Location.read(from:))
        value.dataCatalogInputDefinition = try reader["DataCatalogInputDefinition"].readIfPresent(with: DataBrewClientTypes.DataCatalogInputDefinition.read(from:))
        value.databaseInputDefinition = try reader["DatabaseInputDefinition"].readIfPresent(with: DataBrewClientTypes.DatabaseInputDefinition.read(from:))
        value.metadata = try reader["Metadata"].readIfPresent(with: DataBrewClientTypes.Metadata.read(from:))
        return value
    }
}

extension DataBrewClientTypes.Metadata {

    static func write(value: DataBrewClientTypes.Metadata?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["SourceArn"].write(value.sourceArn)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.Metadata {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.Metadata()
        value.sourceArn = try reader["SourceArn"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.DatabaseInputDefinition {

    static func write(value: DataBrewClientTypes.DatabaseInputDefinition?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DatabaseTableName"].write(value.databaseTableName)
        try writer["GlueConnectionName"].write(value.glueConnectionName)
        try writer["QueryString"].write(value.queryString)
        try writer["TempDirectory"].write(value.tempDirectory, with: DataBrewClientTypes.S3Location.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.DatabaseInputDefinition {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.DatabaseInputDefinition()
        value.glueConnectionName = try reader["GlueConnectionName"].readIfPresent() ?? ""
        value.databaseTableName = try reader["DatabaseTableName"].readIfPresent()
        value.tempDirectory = try reader["TempDirectory"].readIfPresent(with: DataBrewClientTypes.S3Location.read(from:))
        value.queryString = try reader["QueryString"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.S3Location {

    static func write(value: DataBrewClientTypes.S3Location?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Bucket"].write(value.bucket)
        try writer["BucketOwner"].write(value.bucketOwner)
        try writer["Key"].write(value.key)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.S3Location {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.S3Location()
        value.bucket = try reader["Bucket"].readIfPresent() ?? ""
        value.key = try reader["Key"].readIfPresent()
        value.bucketOwner = try reader["BucketOwner"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.DataCatalogInputDefinition {

    static func write(value: DataBrewClientTypes.DataCatalogInputDefinition?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CatalogId"].write(value.catalogId)
        try writer["DatabaseName"].write(value.databaseName)
        try writer["TableName"].write(value.tableName)
        try writer["TempDirectory"].write(value.tempDirectory, with: DataBrewClientTypes.S3Location.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.DataCatalogInputDefinition {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.DataCatalogInputDefinition()
        value.catalogId = try reader["CatalogId"].readIfPresent()
        value.databaseName = try reader["DatabaseName"].readIfPresent() ?? ""
        value.tableName = try reader["TableName"].readIfPresent() ?? ""
        value.tempDirectory = try reader["TempDirectory"].readIfPresent(with: DataBrewClientTypes.S3Location.read(from:))
        return value
    }
}

extension DataBrewClientTypes.PathOptions {

    static func write(value: DataBrewClientTypes.PathOptions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["FilesLimit"].write(value.filesLimit, with: DataBrewClientTypes.FilesLimit.write(value:to:))
        try writer["LastModifiedDateCondition"].write(value.lastModifiedDateCondition, with: DataBrewClientTypes.FilterExpression.write(value:to:))
        try writer["Parameters"].writeMap(value.parameters, valueWritingClosure: DataBrewClientTypes.DatasetParameter.write(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.PathOptions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.PathOptions()
        value.lastModifiedDateCondition = try reader["LastModifiedDateCondition"].readIfPresent(with: DataBrewClientTypes.FilterExpression.read(from:))
        value.filesLimit = try reader["FilesLimit"].readIfPresent(with: DataBrewClientTypes.FilesLimit.read(from:))
        value.parameters = try reader["Parameters"].readMapIfPresent(valueReadingClosure: DataBrewClientTypes.DatasetParameter.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DataBrewClientTypes.DatasetParameter {

    static func write(value: DataBrewClientTypes.DatasetParameter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CreateColumn"].write(value.createColumn)
        try writer["DatetimeOptions"].write(value.datetimeOptions, with: DataBrewClientTypes.DatetimeOptions.write(value:to:))
        try writer["Filter"].write(value.filter, with: DataBrewClientTypes.FilterExpression.write(value:to:))
        try writer["Name"].write(value.name)
        try writer["Type"].write(value.type)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.DatasetParameter {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.DatasetParameter()
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.type = try reader["Type"].readIfPresent() ?? .sdkUnknown("")
        value.datetimeOptions = try reader["DatetimeOptions"].readIfPresent(with: DataBrewClientTypes.DatetimeOptions.read(from:))
        value.createColumn = try reader["CreateColumn"].readIfPresent() ?? false
        value.filter = try reader["Filter"].readIfPresent(with: DataBrewClientTypes.FilterExpression.read(from:))
        return value
    }
}

extension DataBrewClientTypes.FilterExpression {

    static func write(value: DataBrewClientTypes.FilterExpression?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Expression"].write(value.expression)
        try writer["ValuesMap"].writeMap(value.valuesMap, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.FilterExpression {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.FilterExpression()
        value.expression = try reader["Expression"].readIfPresent() ?? ""
        value.valuesMap = try reader["ValuesMap"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false) ?? [:]
        return value
    }
}

extension DataBrewClientTypes.DatetimeOptions {

    static func write(value: DataBrewClientTypes.DatetimeOptions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Format"].write(value.format)
        try writer["LocaleCode"].write(value.localeCode)
        try writer["TimezoneOffset"].write(value.timezoneOffset)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.DatetimeOptions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.DatetimeOptions()
        value.format = try reader["Format"].readIfPresent() ?? ""
        value.timezoneOffset = try reader["TimezoneOffset"].readIfPresent()
        value.localeCode = try reader["LocaleCode"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.FilesLimit {

    static func write(value: DataBrewClientTypes.FilesLimit?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxFiles"].write(value.maxFiles)
        try writer["Order"].write(value.order)
        try writer["OrderedBy"].write(value.orderedBy)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.FilesLimit {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.FilesLimit()
        value.maxFiles = try reader["MaxFiles"].readIfPresent() ?? 0
        value.orderedBy = try reader["OrderedBy"].readIfPresent()
        value.order = try reader["Order"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.Output {

    static func write(value: DataBrewClientTypes.Output?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CompressionFormat"].write(value.compressionFormat)
        try writer["Format"].write(value.format)
        try writer["FormatOptions"].write(value.formatOptions, with: DataBrewClientTypes.OutputFormatOptions.write(value:to:))
        try writer["Location"].write(value.location, with: DataBrewClientTypes.S3Location.write(value:to:))
        try writer["MaxOutputFiles"].write(value.maxOutputFiles)
        try writer["Overwrite"].write(value.overwrite)
        try writer["PartitionColumns"].writeList(value.partitionColumns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.Output {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.Output()
        value.compressionFormat = try reader["CompressionFormat"].readIfPresent()
        value.format = try reader["Format"].readIfPresent()
        value.partitionColumns = try reader["PartitionColumns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.location = try reader["Location"].readIfPresent(with: DataBrewClientTypes.S3Location.read(from:))
        value.overwrite = try reader["Overwrite"].readIfPresent() ?? false
        value.formatOptions = try reader["FormatOptions"].readIfPresent(with: DataBrewClientTypes.OutputFormatOptions.read(from:))
        value.maxOutputFiles = try reader["MaxOutputFiles"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.OutputFormatOptions {

    static func write(value: DataBrewClientTypes.OutputFormatOptions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Csv"].write(value.csv, with: DataBrewClientTypes.CsvOutputOptions.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.OutputFormatOptions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.OutputFormatOptions()
        value.csv = try reader["Csv"].readIfPresent(with: DataBrewClientTypes.CsvOutputOptions.read(from:))
        return value
    }
}

extension DataBrewClientTypes.CsvOutputOptions {

    static func write(value: DataBrewClientTypes.CsvOutputOptions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Delimiter"].write(value.delimiter)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.CsvOutputOptions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.CsvOutputOptions()
        value.delimiter = try reader["Delimiter"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.DataCatalogOutput {

    static func write(value: DataBrewClientTypes.DataCatalogOutput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CatalogId"].write(value.catalogId)
        try writer["DatabaseName"].write(value.databaseName)
        try writer["DatabaseOptions"].write(value.databaseOptions, with: DataBrewClientTypes.DatabaseTableOutputOptions.write(value:to:))
        try writer["Overwrite"].write(value.overwrite)
        try writer["S3Options"].write(value.s3Options, with: DataBrewClientTypes.S3TableOutputOptions.write(value:to:))
        try writer["TableName"].write(value.tableName)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.DataCatalogOutput {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.DataCatalogOutput()
        value.catalogId = try reader["CatalogId"].readIfPresent()
        value.databaseName = try reader["DatabaseName"].readIfPresent() ?? ""
        value.tableName = try reader["TableName"].readIfPresent() ?? ""
        value.s3Options = try reader["S3Options"].readIfPresent(with: DataBrewClientTypes.S3TableOutputOptions.read(from:))
        value.databaseOptions = try reader["DatabaseOptions"].readIfPresent(with: DataBrewClientTypes.DatabaseTableOutputOptions.read(from:))
        value.overwrite = try reader["Overwrite"].readIfPresent() ?? false
        return value
    }
}

extension DataBrewClientTypes.DatabaseTableOutputOptions {

    static func write(value: DataBrewClientTypes.DatabaseTableOutputOptions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["TableName"].write(value.tableName)
        try writer["TempDirectory"].write(value.tempDirectory, with: DataBrewClientTypes.S3Location.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.DatabaseTableOutputOptions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.DatabaseTableOutputOptions()
        value.tempDirectory = try reader["TempDirectory"].readIfPresent(with: DataBrewClientTypes.S3Location.read(from:))
        value.tableName = try reader["TableName"].readIfPresent() ?? ""
        return value
    }
}

extension DataBrewClientTypes.S3TableOutputOptions {

    static func write(value: DataBrewClientTypes.S3TableOutputOptions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Location"].write(value.location, with: DataBrewClientTypes.S3Location.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.S3TableOutputOptions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.S3TableOutputOptions()
        value.location = try reader["Location"].readIfPresent(with: DataBrewClientTypes.S3Location.read(from:))
        return value
    }
}

extension DataBrewClientTypes.DatabaseOutput {

    static func write(value: DataBrewClientTypes.DatabaseOutput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DatabaseOptions"].write(value.databaseOptions, with: DataBrewClientTypes.DatabaseTableOutputOptions.write(value:to:))
        try writer["DatabaseOutputMode"].write(value.databaseOutputMode)
        try writer["GlueConnectionName"].write(value.glueConnectionName)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.DatabaseOutput {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.DatabaseOutput()
        value.glueConnectionName = try reader["GlueConnectionName"].readIfPresent() ?? ""
        value.databaseOptions = try reader["DatabaseOptions"].readIfPresent(with: DataBrewClientTypes.DatabaseTableOutputOptions.read(from:))
        value.databaseOutputMode = try reader["DatabaseOutputMode"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.ProfileConfiguration {

    static func write(value: DataBrewClientTypes.ProfileConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ColumnStatisticsConfigurations"].writeList(value.columnStatisticsConfigurations, memberWritingClosure: DataBrewClientTypes.ColumnStatisticsConfiguration.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["DatasetStatisticsConfiguration"].write(value.datasetStatisticsConfiguration, with: DataBrewClientTypes.StatisticsConfiguration.write(value:to:))
        try writer["EntityDetectorConfiguration"].write(value.entityDetectorConfiguration, with: DataBrewClientTypes.EntityDetectorConfiguration.write(value:to:))
        try writer["ProfileColumns"].writeList(value.profileColumns, memberWritingClosure: DataBrewClientTypes.ColumnSelector.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.ProfileConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.ProfileConfiguration()
        value.datasetStatisticsConfiguration = try reader["DatasetStatisticsConfiguration"].readIfPresent(with: DataBrewClientTypes.StatisticsConfiguration.read(from:))
        value.profileColumns = try reader["ProfileColumns"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.ColumnSelector.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.columnStatisticsConfigurations = try reader["ColumnStatisticsConfigurations"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.ColumnStatisticsConfiguration.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.entityDetectorConfiguration = try reader["EntityDetectorConfiguration"].readIfPresent(with: DataBrewClientTypes.EntityDetectorConfiguration.read(from:))
        return value
    }
}

extension DataBrewClientTypes.EntityDetectorConfiguration {

    static func write(value: DataBrewClientTypes.EntityDetectorConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AllowedStatistics"].writeList(value.allowedStatistics, memberWritingClosure: DataBrewClientTypes.AllowedStatistics.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["EntityTypes"].writeList(value.entityTypes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.EntityDetectorConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.EntityDetectorConfiguration()
        value.entityTypes = try reader["EntityTypes"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.allowedStatistics = try reader["AllowedStatistics"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.AllowedStatistics.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DataBrewClientTypes.AllowedStatistics {

    static func write(value: DataBrewClientTypes.AllowedStatistics?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Statistics"].writeList(value.statistics, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.AllowedStatistics {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.AllowedStatistics()
        value.statistics = try reader["Statistics"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DataBrewClientTypes.ColumnStatisticsConfiguration {

    static func write(value: DataBrewClientTypes.ColumnStatisticsConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Selectors"].writeList(value.selectors, memberWritingClosure: DataBrewClientTypes.ColumnSelector.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Statistics"].write(value.statistics, with: DataBrewClientTypes.StatisticsConfiguration.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.ColumnStatisticsConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.ColumnStatisticsConfiguration()
        value.selectors = try reader["Selectors"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.ColumnSelector.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.statistics = try reader["Statistics"].readIfPresent(with: DataBrewClientTypes.StatisticsConfiguration.read(from:))
        return value
    }
}

extension DataBrewClientTypes.StatisticsConfiguration {

    static func write(value: DataBrewClientTypes.StatisticsConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["IncludedStatistics"].writeList(value.includedStatistics, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Overrides"].writeList(value.overrides, memberWritingClosure: DataBrewClientTypes.StatisticOverride.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.StatisticsConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.StatisticsConfiguration()
        value.includedStatistics = try reader["IncludedStatistics"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.overrides = try reader["Overrides"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.StatisticOverride.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DataBrewClientTypes.StatisticOverride {

    static func write(value: DataBrewClientTypes.StatisticOverride?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Parameters"].writeMap(value.parameters, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["Statistic"].write(value.statistic)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.StatisticOverride {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.StatisticOverride()
        value.statistic = try reader["Statistic"].readIfPresent() ?? ""
        value.parameters = try reader["Parameters"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false) ?? [:]
        return value
    }
}

extension DataBrewClientTypes.ColumnSelector {

    static func write(value: DataBrewClientTypes.ColumnSelector?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
        try writer["Regex"].write(value.regex)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.ColumnSelector {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.ColumnSelector()
        value.regex = try reader["Regex"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.ValidationConfiguration {

    static func write(value: DataBrewClientTypes.ValidationConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["RulesetArn"].write(value.rulesetArn)
        try writer["ValidationMode"].write(value.validationMode)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.ValidationConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.ValidationConfiguration()
        value.rulesetArn = try reader["RulesetArn"].readIfPresent() ?? ""
        value.validationMode = try reader["ValidationMode"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.RecipeReference {

    static func write(value: DataBrewClientTypes.RecipeReference?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
        try writer["RecipeVersion"].write(value.recipeVersion)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.RecipeReference {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.RecipeReference()
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.recipeVersion = try reader["RecipeVersion"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.JobSample {

    static func write(value: DataBrewClientTypes.JobSample?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Mode"].write(value.mode)
        try writer["Size"].write(value.size)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.JobSample {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.JobSample()
        value.mode = try reader["Mode"].readIfPresent()
        value.size = try reader["Size"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.Sample {

    static func write(value: DataBrewClientTypes.Sample?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Size"].write(value.size)
        try writer["Type"].write(value.type)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.Sample {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.Sample()
        value.size = try reader["Size"].readIfPresent()
        value.type = try reader["Type"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension DataBrewClientTypes.RecipeStep {

    static func write(value: DataBrewClientTypes.RecipeStep?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Action"].write(value.action, with: DataBrewClientTypes.RecipeAction.write(value:to:))
        try writer["ConditionExpressions"].writeList(value.conditionExpressions, memberWritingClosure: DataBrewClientTypes.ConditionExpression.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.RecipeStep {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.RecipeStep()
        value.action = try reader["Action"].readIfPresent(with: DataBrewClientTypes.RecipeAction.read(from:))
        value.conditionExpressions = try reader["ConditionExpressions"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.ConditionExpression.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DataBrewClientTypes.ConditionExpression {

    static func write(value: DataBrewClientTypes.ConditionExpression?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Condition"].write(value.condition)
        try writer["TargetColumn"].write(value.targetColumn)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.ConditionExpression {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.ConditionExpression()
        value.condition = try reader["Condition"].readIfPresent() ?? ""
        value.value = try reader["Value"].readIfPresent()
        value.targetColumn = try reader["TargetColumn"].readIfPresent() ?? ""
        return value
    }
}

extension DataBrewClientTypes.RecipeAction {

    static func write(value: DataBrewClientTypes.RecipeAction?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Operation"].write(value.operation)
        try writer["Parameters"].writeMap(value.parameters, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.RecipeAction {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.RecipeAction()
        value.operation = try reader["Operation"].readIfPresent() ?? ""
        value.parameters = try reader["Parameters"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DataBrewClientTypes.Rule {

    static func write(value: DataBrewClientTypes.Rule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CheckExpression"].write(value.checkExpression)
        try writer["ColumnSelectors"].writeList(value.columnSelectors, memberWritingClosure: DataBrewClientTypes.ColumnSelector.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Disabled"].write(value.disabled)
        try writer["Name"].write(value.name)
        try writer["SubstitutionMap"].writeMap(value.substitutionMap, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["Threshold"].write(value.threshold, with: DataBrewClientTypes.Threshold.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.Rule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.Rule()
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.disabled = try reader["Disabled"].readIfPresent() ?? false
        value.checkExpression = try reader["CheckExpression"].readIfPresent() ?? ""
        value.substitutionMap = try reader["SubstitutionMap"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.threshold = try reader["Threshold"].readIfPresent(with: DataBrewClientTypes.Threshold.read(from:))
        value.columnSelectors = try reader["ColumnSelectors"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.ColumnSelector.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DataBrewClientTypes.Threshold {

    static func write(value: DataBrewClientTypes.Threshold?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Type"].write(value.type)
        try writer["Unit"].write(value.unit)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.Threshold {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.Threshold()
        value.value = try reader["Value"].readIfPresent() ?? 0
        value.type = try reader["Type"].readIfPresent()
        value.unit = try reader["Unit"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.Dataset {

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.Dataset {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.Dataset()
        value.accountId = try reader["AccountId"].readIfPresent()
        value.createdBy = try reader["CreatedBy"].readIfPresent()
        value.createDate = try reader["CreateDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.format = try reader["Format"].readIfPresent()
        value.formatOptions = try reader["FormatOptions"].readIfPresent(with: DataBrewClientTypes.FormatOptions.read(from:))
        value.input = try reader["Input"].readIfPresent(with: DataBrewClientTypes.Input.read(from:))
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.source = try reader["Source"].readIfPresent()
        value.pathOptions = try reader["PathOptions"].readIfPresent(with: DataBrewClientTypes.PathOptions.read(from:))
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.resourceArn = try reader["ResourceArn"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.JobRun {

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.JobRun {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.JobRun()
        value.attempt = try reader["Attempt"].readIfPresent() ?? 0
        value.completedOn = try reader["CompletedOn"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.datasetName = try reader["DatasetName"].readIfPresent()
        value.errorMessage = try reader["ErrorMessage"].readIfPresent()
        value.executionTime = try reader["ExecutionTime"].readIfPresent() ?? 0
        value.jobName = try reader["JobName"].readIfPresent()
        value.runId = try reader["RunId"].readIfPresent()
        value.state = try reader["State"].readIfPresent()
        value.logSubscription = try reader["LogSubscription"].readIfPresent()
        value.logGroupName = try reader["LogGroupName"].readIfPresent()
        value.outputs = try reader["Outputs"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.Output.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.dataCatalogOutputs = try reader["DataCatalogOutputs"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.DataCatalogOutput.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.databaseOutputs = try reader["DatabaseOutputs"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.DatabaseOutput.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.recipeReference = try reader["RecipeReference"].readIfPresent(with: DataBrewClientTypes.RecipeReference.read(from:))
        value.startedBy = try reader["StartedBy"].readIfPresent()
        value.startedOn = try reader["StartedOn"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.jobSample = try reader["JobSample"].readIfPresent(with: DataBrewClientTypes.JobSample.read(from:))
        value.validationConfigurations = try reader["ValidationConfigurations"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.ValidationConfiguration.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DataBrewClientTypes.Job {

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.Job {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.Job()
        value.accountId = try reader["AccountId"].readIfPresent()
        value.createdBy = try reader["CreatedBy"].readIfPresent()
        value.createDate = try reader["CreateDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.datasetName = try reader["DatasetName"].readIfPresent()
        value.encryptionKeyArn = try reader["EncryptionKeyArn"].readIfPresent()
        value.encryptionMode = try reader["EncryptionMode"].readIfPresent()
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.type = try reader["Type"].readIfPresent()
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.logSubscription = try reader["LogSubscription"].readIfPresent()
        value.maxCapacity = try reader["MaxCapacity"].readIfPresent() ?? 0
        value.maxRetries = try reader["MaxRetries"].readIfPresent() ?? 0
        value.outputs = try reader["Outputs"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.Output.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.dataCatalogOutputs = try reader["DataCatalogOutputs"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.DataCatalogOutput.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.databaseOutputs = try reader["DatabaseOutputs"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.DatabaseOutput.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.projectName = try reader["ProjectName"].readIfPresent()
        value.recipeReference = try reader["RecipeReference"].readIfPresent(with: DataBrewClientTypes.RecipeReference.read(from:))
        value.resourceArn = try reader["ResourceArn"].readIfPresent()
        value.roleArn = try reader["RoleArn"].readIfPresent()
        value.timeout = try reader["Timeout"].readIfPresent() ?? 0
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.jobSample = try reader["JobSample"].readIfPresent(with: DataBrewClientTypes.JobSample.read(from:))
        value.validationConfigurations = try reader["ValidationConfigurations"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.ValidationConfiguration.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DataBrewClientTypes.Project {

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.Project {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.Project()
        value.accountId = try reader["AccountId"].readIfPresent()
        value.createDate = try reader["CreateDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.createdBy = try reader["CreatedBy"].readIfPresent()
        value.datasetName = try reader["DatasetName"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.recipeName = try reader["RecipeName"].readIfPresent() ?? ""
        value.resourceArn = try reader["ResourceArn"].readIfPresent()
        value.sample = try reader["Sample"].readIfPresent(with: DataBrewClientTypes.Sample.read(from:))
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.roleArn = try reader["RoleArn"].readIfPresent()
        value.openedBy = try reader["OpenedBy"].readIfPresent()
        value.openDate = try reader["OpenDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension DataBrewClientTypes.Recipe {

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.Recipe {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.Recipe()
        value.createdBy = try reader["CreatedBy"].readIfPresent()
        value.createDate = try reader["CreateDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.projectName = try reader["ProjectName"].readIfPresent()
        value.publishedBy = try reader["PublishedBy"].readIfPresent()
        value.publishedDate = try reader["PublishedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.description = try reader["Description"].readIfPresent()
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.resourceArn = try reader["ResourceArn"].readIfPresent()
        value.steps = try reader["Steps"].readListIfPresent(memberReadingClosure: DataBrewClientTypes.RecipeStep.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.recipeVersion = try reader["RecipeVersion"].readIfPresent()
        return value
    }
}

extension DataBrewClientTypes.RulesetItem {

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.RulesetItem {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.RulesetItem()
        value.accountId = try reader["AccountId"].readIfPresent()
        value.createdBy = try reader["CreatedBy"].readIfPresent()
        value.createDate = try reader["CreateDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.description = try reader["Description"].readIfPresent()
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.resourceArn = try reader["ResourceArn"].readIfPresent()
        value.ruleCount = try reader["RuleCount"].readIfPresent() ?? 0
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.targetArn = try reader["TargetArn"].readIfPresent() ?? ""
        return value
    }
}

extension DataBrewClientTypes.Schedule {

    static func read(from reader: SmithyJSON.Reader) throws -> DataBrewClientTypes.Schedule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataBrewClientTypes.Schedule()
        value.accountId = try reader["AccountId"].readIfPresent()
        value.createdBy = try reader["CreatedBy"].readIfPresent()
        value.createDate = try reader["CreateDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.jobNames = try reader["JobNames"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.lastModifiedBy = try reader["LastModifiedBy"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.resourceArn = try reader["ResourceArn"].readIfPresent()
        value.cronExpression = try reader["CronExpression"].readIfPresent()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.name = try reader["Name"].readIfPresent() ?? ""
        return value
    }
}

extension DataBrewClientTypes.ViewFrame {

    static func write(value: DataBrewClientTypes.ViewFrame?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Analytics"].write(value.analytics)
        try writer["ColumnRange"].write(value.columnRange)
        try writer["HiddenColumns"].writeList(value.hiddenColumns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["RowRange"].write(value.rowRange)
        try writer["StartColumnIndex"].write(value.startColumnIndex)
        try writer["StartRowIndex"].write(value.startRowIndex)
    }
}

public enum DataBrewClientTypes {}
