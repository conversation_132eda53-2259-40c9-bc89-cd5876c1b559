//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.<PERSON>y<PERSON>eader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
@_spi(SmithyReadWrite) import struct SmithyReadWrite.ReadingClosureBox
@_spi(SmithyReadWrite) import struct SmithyReadWrite.WritingClosureBox

extension DLMClientTypes {

    /// [Event-based policies only] Specifies the encryption settings for cross-Region snapshot copies created by event-based policies.
    public struct EncryptionConfiguration: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the KMS key to use for EBS encryption. If this parameter is not specified, the default KMS key for the account is used.
        public var cmkArn: Swift.String?
        /// To encrypt a copy of an unencrypted snapshot when encryption by default is not enabled, enable encryption using this parameter. Copies of encrypted snapshots are encrypted, even if this parameter is false or when encryption by default is not enabled.
        /// This member is required.
        public var encrypted: Swift.Bool?

        public init(
            cmkArn: Swift.String? = nil,
            encrypted: Swift.Bool? = nil
        )
        {
            self.cmkArn = cmkArn
            self.encrypted = encrypted
        }
    }
}

extension DLMClientTypes {

    public enum RetentionIntervalUnitValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case days
        case months
        case weeks
        case years
        case sdkUnknown(Swift.String)

        public static var allCases: [RetentionIntervalUnitValues] {
            return [
                .days,
                .months,
                .weeks,
                .years
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .days: return "DAYS"
            case .months: return "MONTHS"
            case .weeks: return "WEEKS"
            case .years: return "YEARS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DLMClientTypes {

    /// Specifies a retention rule for cross-Region snapshot copies created by snapshot or event-based policies, or cross-Region AMI copies created by AMI policies. After the retention period expires, the cross-Region copy is deleted.
    public struct CrossRegionCopyRetainRule: Swift.Sendable {
        /// The amount of time to retain a cross-Region snapshot or AMI copy. The maximum is 100 years. This is equivalent to 1200 months, 5200 weeks, or 36500 days.
        public var interval: Swift.Int?
        /// The unit of time for time-based retention. For example, to retain a cross-Region copy for 3 months, specify Interval=3 and IntervalUnit=MONTHS.
        public var intervalUnit: DLMClientTypes.RetentionIntervalUnitValues?

        public init(
            interval: Swift.Int? = nil,
            intervalUnit: DLMClientTypes.RetentionIntervalUnitValues? = nil
        )
        {
            self.interval = interval
            self.intervalUnit = intervalUnit
        }
    }
}

extension DLMClientTypes {

    /// [Event-based policies only] Specifies a cross-Region copy action for event-based policies. To specify a cross-Region copy rule for snapshot and AMI policies, use [CrossRegionCopyRule](https://docs.aws.amazon.com/dlm/latest/APIReference/API_CrossRegionCopyRule.html).
    public struct CrossRegionCopyAction: Swift.Sendable {
        /// The encryption settings for the copied snapshot.
        /// This member is required.
        public var encryptionConfiguration: DLMClientTypes.EncryptionConfiguration?
        /// Specifies a retention rule for cross-Region snapshot copies created by snapshot or event-based policies, or cross-Region AMI copies created by AMI policies. After the retention period expires, the cross-Region copy is deleted.
        public var retainRule: DLMClientTypes.CrossRegionCopyRetainRule?
        /// The target Region.
        /// This member is required.
        public var target: Swift.String?

        public init(
            encryptionConfiguration: DLMClientTypes.EncryptionConfiguration? = nil,
            retainRule: DLMClientTypes.CrossRegionCopyRetainRule? = nil,
            target: Swift.String? = nil
        )
        {
            self.encryptionConfiguration = encryptionConfiguration
            self.retainRule = retainRule
            self.target = target
        }
    }
}

extension DLMClientTypes {

    /// [Event-based policies only] Specifies an action for an event-based policy.
    public struct Action: Swift.Sendable {
        /// The rule for copying shared snapshots across Regions.
        /// This member is required.
        public var crossRegionCopy: [DLMClientTypes.CrossRegionCopyAction]?
        /// A descriptive name for the action.
        /// This member is required.
        public var name: Swift.String?

        public init(
            crossRegionCopy: [DLMClientTypes.CrossRegionCopyAction]? = nil,
            name: Swift.String? = nil
        )
        {
            self.crossRegionCopy = crossRegionCopy
            self.name = name
        }
    }
}

extension DLMClientTypes {

    /// [Custom snapshot policies only] Describes the retention rule for archived snapshots. Once the archive retention threshold is met, the snapshots are permanently deleted from the archive tier. The archive retention rule must retain snapshots in the archive tier for a minimum of 90 days. For count-based schedules, you must specify Count. For age-based schedules, you must specify Interval and IntervalUnit. For more information about using snapshot archiving, see [Considerations for snapshot lifecycle policies](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/snapshot-ami-policy.html#dlm-archive).
    public struct RetentionArchiveTier: Swift.Sendable {
        /// The maximum number of snapshots to retain in the archive storage tier for each volume. The count must ensure that each snapshot remains in the archive tier for at least 90 days. For example, if the schedule creates snapshots every 30 days, you must specify a count of 3 or more to ensure that each snapshot is archived for at least 90 days.
        public var count: Swift.Int?
        /// Specifies the period of time to retain snapshots in the archive tier. After this period expires, the snapshot is permanently deleted.
        public var interval: Swift.Int?
        /// The unit of time in which to measure the Interval. For example, to retain a snapshots in the archive tier for 6 months, specify Interval=6 and IntervalUnit=MONTHS.
        public var intervalUnit: DLMClientTypes.RetentionIntervalUnitValues?

        public init(
            count: Swift.Int? = nil,
            interval: Swift.Int? = nil,
            intervalUnit: DLMClientTypes.RetentionIntervalUnitValues? = nil
        )
        {
            self.count = count
            self.interval = interval
            self.intervalUnit = intervalUnit
        }
    }
}

extension DLMClientTypes {

    /// [Custom snapshot policies only] Specifies information about the archive storage tier retention period.
    public struct ArchiveRetainRule: Swift.Sendable {
        /// Information about retention period in the Amazon EBS Snapshots Archive. For more information, see [Archive Amazon EBS snapshots](https://docs.aws.amazon.com/AWSEC2/latest/WindowsGuide/snapshot-archive.html).
        /// This member is required.
        public var retentionArchiveTier: DLMClientTypes.RetentionArchiveTier?

        public init(
            retentionArchiveTier: DLMClientTypes.RetentionArchiveTier? = nil
        )
        {
            self.retentionArchiveTier = retentionArchiveTier
        }
    }
}

extension DLMClientTypes {

    /// [Custom snapshot policies only] Specifies a snapshot archiving rule for a schedule.
    public struct ArchiveRule: Swift.Sendable {
        /// Information about the retention period for the snapshot archiving rule.
        /// This member is required.
        public var retainRule: DLMClientTypes.ArchiveRetainRule?

        public init(
            retainRule: DLMClientTypes.ArchiveRetainRule? = nil
        )
        {
            self.retainRule = retainRule
        }
    }
}

/// The service failed in an unexpected way.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var code: Swift.String? = nil
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        code: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.code = code
        self.properties.message = message
    }
}

/// Bad request. The request is missing required parameters or has invalid parameters.
public struct InvalidRequestException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var code: Swift.String? = nil
        public internal(set) var message: Swift.String? = nil
        /// The request included parameters that cannot be provided together.
        public internal(set) var mutuallyExclusiveParameters: [Swift.String]? = nil
        /// The request omitted one or more required parameters.
        public internal(set) var requiredParameters: [Swift.String]? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidRequestException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        code: Swift.String? = nil,
        message: Swift.String? = nil,
        mutuallyExclusiveParameters: [Swift.String]? = nil,
        requiredParameters: [Swift.String]? = nil
    )
    {
        self.properties.code = code
        self.properties.message = message
        self.properties.mutuallyExclusiveParameters = mutuallyExclusiveParameters
        self.properties.requiredParameters = requiredParameters
    }
}

/// The request failed because a limit was exceeded.
public struct LimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var code: Swift.String? = nil
        public internal(set) var message: Swift.String? = nil
        /// Value is the type of resource for which a limit was exceeded.
        public internal(set) var resourceType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        code: Swift.String? = nil,
        message: Swift.String? = nil,
        resourceType: Swift.String? = nil
    )
    {
        self.properties.code = code
        self.properties.message = message
        self.properties.resourceType = resourceType
    }
}

extension DLMClientTypes {

    /// [Default policies only] Specifies a destination Region for cross-Region copy actions.
    public struct CrossRegionCopyTarget: Swift.Sendable {
        /// The target Region, for example us-east-1.
        public var targetRegion: Swift.String?

        public init(
            targetRegion: Swift.String? = nil
        )
        {
            self.targetRegion = targetRegion
        }
    }
}

extension DLMClientTypes {

    public enum DefaultPolicyTypeValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case instance
        case volume
        case sdkUnknown(Swift.String)

        public static var allCases: [DefaultPolicyTypeValues] {
            return [
                .instance,
                .volume
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .instance: return "INSTANCE"
            case .volume: return "VOLUME"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DLMClientTypes {

    /// Specifies a tag for a resource.
    public struct Tag: Swift.Sendable {
        /// The tag key.
        /// This member is required.
        public var key: Swift.String?
        /// The tag value.
        /// This member is required.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

extension DLMClientTypes {

    /// [Default policies only] Specifies exclusion parameters for volumes or instances for which you do not want to create snapshots or AMIs. The policy will not create snapshots or AMIs for target resources that match any of the specified exclusion parameters.
    public struct Exclusions: Swift.Sendable {
        /// [Default policies for EBS snapshots only] Indicates whether to exclude volumes that are attached to instances as the boot volume. If you exclude boot volumes, only volumes attached as data (non-boot) volumes will be backed up by the policy. To exclude boot volumes, specify true.
        public var excludeBootVolumes: Swift.Bool?
        /// [Default policies for EBS-backed AMIs only] Specifies whether to exclude volumes that have specific tags.
        public var excludeTags: [DLMClientTypes.Tag]?
        /// [Default policies for EBS snapshots only] Specifies the volume types to exclude. Volumes of the specified types will not be targeted by the policy.
        public var excludeVolumeTypes: [Swift.String]?

        public init(
            excludeBootVolumes: Swift.Bool? = nil,
            excludeTags: [DLMClientTypes.Tag]? = nil,
            excludeVolumeTypes: [Swift.String]? = nil
        )
        {
            self.excludeBootVolumes = excludeBootVolumes
            self.excludeTags = excludeTags
            self.excludeVolumeTypes = excludeVolumeTypes
        }
    }
}

extension DLMClientTypes {

    public enum EventTypeValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case shareSnapshot
        case sdkUnknown(Swift.String)

        public static var allCases: [EventTypeValues] {
            return [
                .shareSnapshot
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .shareSnapshot: return "shareSnapshot"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DLMClientTypes {

    /// [Event-based policies only] Specifies an event that activates an event-based policy.
    public struct EventParameters: Swift.Sendable {
        /// The snapshot description that can trigger the policy. The description pattern is specified using a regular expression. The policy runs only if a snapshot with a description that matches the specified pattern is shared with your account. For example, specifying ^.*Created for policy: policy-1234567890abcdef0.*$ configures the policy to run only if snapshots created by policy policy-1234567890abcdef0 are shared with your account.
        /// This member is required.
        public var descriptionRegex: Swift.String?
        /// The type of event. Currently, only snapshot sharing events are supported.
        /// This member is required.
        public var eventType: DLMClientTypes.EventTypeValues?
        /// The IDs of the Amazon Web Services accounts that can trigger policy by sharing snapshots with your account. The policy only runs if one of the specified Amazon Web Services accounts shares a snapshot with your account.
        /// This member is required.
        public var snapshotOwner: [Swift.String]?

        public init(
            descriptionRegex: Swift.String? = nil,
            eventType: DLMClientTypes.EventTypeValues? = nil,
            snapshotOwner: [Swift.String]? = nil
        )
        {
            self.descriptionRegex = descriptionRegex
            self.eventType = eventType
            self.snapshotOwner = snapshotOwner
        }
    }
}

extension DLMClientTypes {

    public enum EventSourceValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case managedCwe
        case sdkUnknown(Swift.String)

        public static var allCases: [EventSourceValues] {
            return [
                .managedCwe
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .managedCwe: return "MANAGED_CWE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DLMClientTypes {

    /// [Event-based policies only] Specifies an event that activates an event-based policy.
    public struct EventSource: Swift.Sendable {
        /// Information about the event.
        public var parameters: DLMClientTypes.EventParameters?
        /// The source of the event. Currently only managed CloudWatch Events rules are supported.
        /// This member is required.
        public var type: DLMClientTypes.EventSourceValues?

        public init(
            parameters: DLMClientTypes.EventParameters? = nil,
            type: DLMClientTypes.EventSourceValues? = nil
        )
        {
            self.parameters = parameters
            self.type = type
        }
    }
}

extension DLMClientTypes {

    /// [Custom snapshot and AMI policies only] Specifies optional parameters for snapshot and AMI policies. The set of valid parameters depends on the combination of policy type and target resource type. If you choose to exclude boot volumes and you specify tags that consequently exclude all of the additional data volumes attached to an instance, then Amazon Data Lifecycle Manager will not create any snapshots for the affected instance, and it will emit a SnapshotsCreateFailed Amazon CloudWatch metric. For more information, see [Monitor your policies using Amazon CloudWatch](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/monitor-dlm-cw-metrics.html).
    public struct Parameters: Swift.Sendable {
        /// [Custom snapshot policies that target instances only] Indicates whether to exclude the root volume from multi-volume snapshot sets. The default is false. If you specify true, then the root volumes attached to targeted instances will be excluded from the multi-volume snapshot sets created by the policy.
        public var excludeBootVolume: Swift.Bool?
        /// [Custom snapshot policies that target instances only] The tags used to identify data (non-root) volumes to exclude from multi-volume snapshot sets. If you create a snapshot lifecycle policy that targets instances and you specify tags for this parameter, then data volumes with the specified tags that are attached to targeted instances will be excluded from the multi-volume snapshot sets created by the policy.
        public var excludeDataVolumeTags: [DLMClientTypes.Tag]?
        /// [Custom AMI policies only] Indicates whether targeted instances are rebooted when the lifecycle policy runs. true indicates that targeted instances are not rebooted when the policy runs. false indicates that target instances are rebooted when the policy runs. The default is true (instances are not rebooted).
        public var noReboot: Swift.Bool?

        public init(
            excludeBootVolume: Swift.Bool? = nil,
            excludeDataVolumeTags: [DLMClientTypes.Tag]? = nil,
            noReboot: Swift.Bool? = nil
        )
        {
            self.excludeBootVolume = excludeBootVolume
            self.excludeDataVolumeTags = excludeDataVolumeTags
            self.noReboot = noReboot
        }
    }
}

extension DLMClientTypes {

    public enum PolicyLanguageValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case simplified
        case standard
        case sdkUnknown(Swift.String)

        public static var allCases: [PolicyLanguageValues] {
            return [
                .simplified,
                .standard
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .simplified: return "SIMPLIFIED"
            case .standard: return "STANDARD"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DLMClientTypes {

    public enum PolicyTypeValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case ebsSnapshotManagement
        case eventBasedPolicy
        case imageManagement
        case sdkUnknown(Swift.String)

        public static var allCases: [PolicyTypeValues] {
            return [
                .ebsSnapshotManagement,
                .eventBasedPolicy,
                .imageManagement
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .ebsSnapshotManagement: return "EBS_SNAPSHOT_MANAGEMENT"
            case .eventBasedPolicy: return "EVENT_BASED_POLICY"
            case .imageManagement: return "IMAGE_MANAGEMENT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DLMClientTypes {

    public enum ResourceLocationValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cloud
        case localZone
        case outpost
        case sdkUnknown(Swift.String)

        public static var allCases: [ResourceLocationValues] {
            return [
                .cloud,
                .localZone,
                .outpost
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cloud: return "CLOUD"
            case .localZone: return "LOCAL_ZONE"
            case .outpost: return "OUTPOST"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DLMClientTypes {

    public enum ResourceTypeValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case instance
        case volume
        case sdkUnknown(Swift.String)

        public static var allCases: [ResourceTypeValues] {
            return [
                .instance,
                .volume
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .instance: return "INSTANCE"
            case .volume: return "VOLUME"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DLMClientTypes {

    public enum IntervalUnitValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case hours
        case sdkUnknown(Swift.String)

        public static var allCases: [IntervalUnitValues] {
            return [
                .hours
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .hours: return "HOURS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DLMClientTypes {

    public enum LocationValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cloud
        case localZone
        case outpostLocal
        case sdkUnknown(Swift.String)

        public static var allCases: [LocationValues] {
            return [
                .cloud,
                .localZone,
                .outpostLocal
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cloud: return "CLOUD"
            case .localZone: return "LOCAL_ZONE"
            case .outpostLocal: return "OUTPOST_LOCAL"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DLMClientTypes {

    public enum ExecutionHandlerServiceValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case awsSystemsManager
        case sdkUnknown(Swift.String)

        public static var allCases: [ExecutionHandlerServiceValues] {
            return [
                .awsSystemsManager
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .awsSystemsManager: return "AWS_SYSTEMS_MANAGER"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DLMClientTypes {

    public enum StageValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case post
        case pre
        case sdkUnknown(Swift.String)

        public static var allCases: [StageValues] {
            return [
                .post,
                .pre
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .post: return "POST"
            case .pre: return "PRE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DLMClientTypes {

    /// [Custom snapshot policies that target instances only] Information about pre and/or post scripts for a snapshot lifecycle policy that targets instances. For more information, see [ Automating application-consistent snapshots with pre and post scripts](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/automate-app-consistent-backups.html).
    public struct Script: Swift.Sendable {
        /// Indicates whether Amazon Data Lifecycle Manager should default to crash-consistent snapshots if the pre script fails.
        ///
        /// * To default to crash consistent snapshot if the pre script fails, specify true.
        ///
        /// * To skip the instance for snapshot creation if the pre script fails, specify false.
        ///
        ///
        /// This parameter is supported only if you run a pre script. If you run a post script only, omit this parameter. Default: true
        public var executeOperationOnScriptFailure: Swift.Bool?
        /// The SSM document that includes the pre and/or post scripts to run.
        ///
        /// * If you are automating VSS backups, specify AWS_VSS_BACKUP. In this case, Amazon Data Lifecycle Manager automatically uses the AWSEC2-CreateVssSnapshot SSM document.
        ///
        /// * If you are automating application-consistent snapshots for SAP HANA workloads, specify AWSSystemsManagerSAP-CreateDLMSnapshotForSAPHANA.
        ///
        /// * If you are using a custom SSM document that you own, specify either the name or ARN of the SSM document. If you are using a custom SSM document that is shared with you, specify the ARN of the SSM document.
        /// This member is required.
        public var executionHandler: Swift.String?
        /// Indicates the service used to execute the pre and/or post scripts.
        ///
        /// * If you are using custom SSM documents or automating application-consistent snapshots of SAP HANA workloads, specify AWS_SYSTEMS_MANAGER.
        ///
        /// * If you are automating VSS Backups, omit this parameter.
        ///
        ///
        /// Default: AWS_SYSTEMS_MANAGER
        public var executionHandlerService: DLMClientTypes.ExecutionHandlerServiceValues?
        /// Specifies a timeout period, in seconds, after which Amazon Data Lifecycle Manager fails the script run attempt if it has not completed. If a script does not complete within its timeout period, Amazon Data Lifecycle Manager fails the attempt. The timeout period applies to the pre and post scripts individually. If you are automating VSS Backups, omit this parameter. Default: 10
        public var executionTimeout: Swift.Int?
        /// Specifies the number of times Amazon Data Lifecycle Manager should retry scripts that fail.
        ///
        /// * If the pre script fails, Amazon Data Lifecycle Manager retries the entire snapshot creation process, including running the pre and post scripts.
        ///
        /// * If the post script fails, Amazon Data Lifecycle Manager retries the post script only; in this case, the pre script will have completed and the snapshot might have been created.
        ///
        ///
        /// If you do not want Amazon Data Lifecycle Manager to retry failed scripts, specify 0. Default: 0
        public var maximumRetryCount: Swift.Int?
        /// Indicate which scripts Amazon Data Lifecycle Manager should run on target instances. Pre scripts run before Amazon Data Lifecycle Manager initiates snapshot creation. Post scripts run after Amazon Data Lifecycle Manager initiates snapshot creation.
        ///
        /// * To run a pre script only, specify PRE. In this case, Amazon Data Lifecycle Manager calls the SSM document with the pre-script parameter before initiating snapshot creation.
        ///
        /// * To run a post script only, specify POST. In this case, Amazon Data Lifecycle Manager calls the SSM document with the post-script parameter after initiating snapshot creation.
        ///
        /// * To run both pre and post scripts, specify both PRE and POST. In this case, Amazon Data Lifecycle Manager calls the SSM document with the pre-script parameter before initiating snapshot creation, and then it calls the SSM document again with the post-script parameter after initiating snapshot creation.
        ///
        ///
        /// If you are automating VSS Backups, omit this parameter. Default: PRE and POST
        public var stages: [DLMClientTypes.StageValues]?

        public init(
            executeOperationOnScriptFailure: Swift.Bool? = nil,
            executionHandler: Swift.String? = nil,
            executionHandlerService: DLMClientTypes.ExecutionHandlerServiceValues? = nil,
            executionTimeout: Swift.Int? = nil,
            maximumRetryCount: Swift.Int? = nil,
            stages: [DLMClientTypes.StageValues]? = nil
        )
        {
            self.executeOperationOnScriptFailure = executeOperationOnScriptFailure
            self.executionHandler = executionHandler
            self.executionHandlerService = executionHandlerService
            self.executionTimeout = executionTimeout
            self.maximumRetryCount = maximumRetryCount
            self.stages = stages
        }
    }
}

extension DLMClientTypes {

    /// [Custom snapshot and AMI policies only] Specifies when the policy should create snapshots or AMIs.
    ///
    /// * You must specify either CronExpression, or Interval, IntervalUnit, and Times.
    ///
    /// * If you need to specify an [ArchiveRule](https://docs.aws.amazon.com/dlm/latest/APIReference/API_ArchiveRule.html) for the schedule, then you must specify a creation frequency of at least 28 days.
    public struct CreateRule: Swift.Sendable {
        /// The schedule, as a Cron expression. The schedule interval must be between 1 hour and 1 year. For more information, see the [Cron expressions reference](https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-cron-expressions.html) in the Amazon EventBridge User Guide.
        public var cronExpression: Swift.String?
        /// The interval between snapshots. The supported values are 1, 2, 3, 4, 6, 8, 12, and 24.
        public var interval: Swift.Int?
        /// The interval unit.
        public var intervalUnit: DLMClientTypes.IntervalUnitValues?
        /// [Custom snapshot policies only] Specifies the destination for snapshots created by the policy. The allowed destinations depend on the location of the targeted resources.
        ///
        /// * If the policy targets resources in a Region, then you must create snapshots in the same Region as the source resource.
        ///
        /// * If the policy targets resources in a Local Zone, you can create snapshots in the same Local Zone or in its parent Region.
        ///
        /// * If the policy targets resources on an Outpost, then you can create snapshots on the same Outpost or in its parent Region.
        ///
        ///
        /// Specify one of the following values:
        ///
        /// * To create snapshots in the same Region as the source resource, specify CLOUD.
        ///
        /// * To create snapshots in the same Local Zone as the source resource, specify LOCAL_ZONE.
        ///
        /// * To create snapshots on the same Outpost as the source resource, specify OUTPOST_LOCAL.
        ///
        ///
        /// Default: CLOUD
        public var location: DLMClientTypes.LocationValues?
        /// [Custom snapshot policies that target instances only] Specifies pre and/or post scripts for a snapshot lifecycle policy that targets instances. This is useful for creating application-consistent snapshots, or for performing specific administrative tasks before or after Amazon Data Lifecycle Manager initiates snapshot creation. For more information, see [Automating application-consistent snapshots with pre and post scripts](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/automate-app-consistent-backups.html).
        public var scripts: [DLMClientTypes.Script]?
        /// The time, in UTC, to start the operation. The supported format is hh:mm. The operation occurs within a one-hour window following the specified time. If you do not specify a time, Amazon Data Lifecycle Manager selects a time within the next 24 hours.
        public var times: [Swift.String]?

        public init(
            cronExpression: Swift.String? = nil,
            interval: Swift.Int? = nil,
            intervalUnit: DLMClientTypes.IntervalUnitValues? = nil,
            location: DLMClientTypes.LocationValues? = nil,
            scripts: [DLMClientTypes.Script]? = nil,
            times: [Swift.String]? = nil
        )
        {
            self.cronExpression = cronExpression
            self.interval = interval
            self.intervalUnit = intervalUnit
            self.location = location
            self.scripts = scripts
            self.times = times
        }
    }
}

extension DLMClientTypes {

    /// [Custom AMI policies only] Specifies an AMI deprecation rule for cross-Region AMI copies created by an AMI policy.
    public struct CrossRegionCopyDeprecateRule: Swift.Sendable {
        /// The period after which to deprecate the cross-Region AMI copies. The period must be less than or equal to the cross-Region AMI copy retention period, and it can't be greater than 10 years. This is equivalent to 120 months, 520 weeks, or 3650 days.
        public var interval: Swift.Int?
        /// The unit of time in which to measure the Interval. For example, to deprecate a cross-Region AMI copy after 3 months, specify Interval=3 and IntervalUnit=MONTHS.
        public var intervalUnit: DLMClientTypes.RetentionIntervalUnitValues?

        public init(
            interval: Swift.Int? = nil,
            intervalUnit: DLMClientTypes.RetentionIntervalUnitValues? = nil
        )
        {
            self.interval = interval
            self.intervalUnit = intervalUnit
        }
    }
}

extension DLMClientTypes {

    /// [Custom snapshot and AMI policies only] Specifies a cross-Region copy rule for a snapshot and AMI policies. To specify a cross-Region copy action for event-based polices, use [CrossRegionCopyAction](https://docs.aws.amazon.com/dlm/latest/APIReference/API_CrossRegionCopyAction.html).
    public struct CrossRegionCopyRule: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the KMS key to use for EBS encryption. If this parameter is not specified, the default KMS key for the account is used.
        public var cmkArn: Swift.String?
        /// Indicates whether to copy all user-defined tags from the source snapshot or AMI to the cross-Region copy.
        public var copyTags: Swift.Bool?
        /// [Custom AMI policies only] The AMI deprecation rule for cross-Region AMI copies created by the rule.
        public var deprecateRule: DLMClientTypes.CrossRegionCopyDeprecateRule?
        /// To encrypt a copy of an unencrypted snapshot if encryption by default is not enabled, enable encryption using this parameter. Copies of encrypted snapshots are encrypted, even if this parameter is false or if encryption by default is not enabled.
        /// This member is required.
        public var encrypted: Swift.Bool?
        /// The retention rule that indicates how long the cross-Region snapshot or AMI copies are to be retained in the destination Region.
        public var retainRule: DLMClientTypes.CrossRegionCopyRetainRule?
        /// Use this parameter for snapshot policies only. For AMI policies, use TargetRegion instead. [Custom snapshot policies only] The target Region or the Amazon Resource Name (ARN) of the target Outpost for the snapshot copies.
        public var target: Swift.String?
        /// Use this parameter for AMI policies only. For snapshot policies, use Target instead. For snapshot policies created before the Target parameter was introduced, this parameter indicates the target Region for snapshot copies. [Custom AMI policies only] The target Region or the Amazon Resource Name (ARN) of the target Outpost for the snapshot copies.
        public var targetRegion: Swift.String?

        public init(
            cmkArn: Swift.String? = nil,
            copyTags: Swift.Bool? = nil,
            deprecateRule: DLMClientTypes.CrossRegionCopyDeprecateRule? = nil,
            encrypted: Swift.Bool? = nil,
            retainRule: DLMClientTypes.CrossRegionCopyRetainRule? = nil,
            target: Swift.String? = nil,
            targetRegion: Swift.String? = nil
        )
        {
            self.cmkArn = cmkArn
            self.copyTags = copyTags
            self.deprecateRule = deprecateRule
            self.encrypted = encrypted
            self.retainRule = retainRule
            self.target = target
            self.targetRegion = targetRegion
        }
    }
}

extension DLMClientTypes {

    /// [Custom AMI policies only] Specifies an AMI deprecation rule for AMIs created by an AMI lifecycle policy. For age-based schedules, you must specify Interval and IntervalUnit. For count-based schedules, you must specify Count.
    public struct DeprecateRule: Swift.Sendable {
        /// If the schedule has a count-based retention rule, this parameter specifies the number of oldest AMIs to deprecate. The count must be less than or equal to the schedule's retention count, and it can't be greater than 1000.
        public var count: Swift.Int?
        /// If the schedule has an age-based retention rule, this parameter specifies the period after which to deprecate AMIs created by the schedule. The period must be less than or equal to the schedule's retention period, and it can't be greater than 10 years. This is equivalent to 120 months, 520 weeks, or 3650 days.
        public var interval: Swift.Int?
        /// The unit of time in which to measure the Interval.
        public var intervalUnit: DLMClientTypes.RetentionIntervalUnitValues?

        public init(
            count: Swift.Int? = nil,
            interval: Swift.Int? = nil,
            intervalUnit: DLMClientTypes.RetentionIntervalUnitValues? = nil
        )
        {
            self.count = count
            self.interval = interval
            self.intervalUnit = intervalUnit
        }
    }
}

extension DLMClientTypes {

    /// [Custom snapshot policies only] Specifies a rule for enabling fast snapshot restore for snapshots created by snapshot policies. You can enable fast snapshot restore based on either a count or a time interval.
    public struct FastRestoreRule: Swift.Sendable {
        /// The Availability Zones in which to enable fast snapshot restore.
        /// This member is required.
        public var availabilityZones: [Swift.String]?
        /// The number of snapshots to be enabled with fast snapshot restore.
        public var count: Swift.Int?
        /// The amount of time to enable fast snapshot restore. The maximum is 100 years. This is equivalent to 1200 months, 5200 weeks, or 36500 days.
        public var interval: Swift.Int?
        /// The unit of time for enabling fast snapshot restore.
        public var intervalUnit: DLMClientTypes.RetentionIntervalUnitValues?

        public init(
            availabilityZones: [Swift.String]? = nil,
            count: Swift.Int? = nil,
            interval: Swift.Int? = nil,
            intervalUnit: DLMClientTypes.RetentionIntervalUnitValues? = nil
        )
        {
            self.availabilityZones = availabilityZones
            self.count = count
            self.interval = interval
            self.intervalUnit = intervalUnit
        }
    }
}

extension DLMClientTypes {

    /// [Custom snapshot and AMI policies only] Specifies a retention rule for snapshots created by snapshot policies, or for AMIs created by AMI policies. For snapshot policies that have an [ArchiveRule](https://docs.aws.amazon.com/dlm/latest/APIReference/API_ArchiveRule.html), this retention rule applies to standard tier retention. When the retention threshold is met, snapshots are moved from the standard to the archive tier. For snapshot policies that do not have an ArchiveRule, snapshots are permanently deleted when this retention threshold is met. You can retain snapshots based on either a count or a time interval.
    ///
    /// * Count-based retention You must specify Count. If you specify an [ArchiveRule](https://docs.aws.amazon.com/dlm/latest/APIReference/API_ArchiveRule.html) for the schedule, then you can specify a retention count of 0 to archive snapshots immediately after creation. If you specify a [FastRestoreRule](https://docs.aws.amazon.com/dlm/latest/APIReference/API_FastRestoreRule.html), [ShareRule](https://docs.aws.amazon.com/dlm/latest/APIReference/API_ShareRule.html), or a [CrossRegionCopyRule](https://docs.aws.amazon.com/dlm/latest/APIReference/API_CrossRegionCopyRule.html), then you must specify a retention count of 1 or more.
    ///
    /// * Age-based retention You must specify Interval and IntervalUnit. If you specify an [ArchiveRule](https://docs.aws.amazon.com/dlm/latest/APIReference/API_ArchiveRule.html) for the schedule, then you can specify a retention interval of 0 days to archive snapshots immediately after creation. If you specify a [FastRestoreRule](https://docs.aws.amazon.com/dlm/latest/APIReference/API_FastRestoreRule.html), [ShareRule](https://docs.aws.amazon.com/dlm/latest/APIReference/API_ShareRule.html), or a [CrossRegionCopyRule](https://docs.aws.amazon.com/dlm/latest/APIReference/API_CrossRegionCopyRule.html), then you must specify a retention interval of 1 day or more.
    public struct RetainRule: Swift.Sendable {
        /// The number of snapshots to retain for each volume, up to a maximum of 1000. For example if you want to retain a maximum of three snapshots, specify 3. When the fourth snapshot is created, the oldest retained snapshot is deleted, or it is moved to the archive tier if you have specified an [ArchiveRule](https://docs.aws.amazon.com/dlm/latest/APIReference/API_ArchiveRule.html).
        public var count: Swift.Int?
        /// The amount of time to retain each snapshot. The maximum is 100 years. This is equivalent to 1200 months, 5200 weeks, or 36500 days.
        public var interval: Swift.Int?
        /// The unit of time for time-based retention. For example, to retain snapshots for 3 months, specify Interval=3 and IntervalUnit=MONTHS. Once the snapshot has been retained for 3 months, it is deleted, or it is moved to the archive tier if you have specified an [ArchiveRule](https://docs.aws.amazon.com/dlm/latest/APIReference/API_ArchiveRule.html).
        public var intervalUnit: DLMClientTypes.RetentionIntervalUnitValues?

        public init(
            count: Swift.Int? = nil,
            interval: Swift.Int? = nil,
            intervalUnit: DLMClientTypes.RetentionIntervalUnitValues? = nil
        )
        {
            self.count = count
            self.interval = interval
            self.intervalUnit = intervalUnit
        }
    }
}

extension DLMClientTypes {

    /// [Custom snapshot policies only] Specifies a rule for sharing snapshots across Amazon Web Services accounts.
    public struct ShareRule: Swift.Sendable {
        /// The IDs of the Amazon Web Services accounts with which to share the snapshots.
        /// This member is required.
        public var targetAccounts: [Swift.String]?
        /// The period after which snapshots that are shared with other Amazon Web Services accounts are automatically unshared.
        public var unshareInterval: Swift.Int?
        /// The unit of time for the automatic unsharing interval.
        public var unshareIntervalUnit: DLMClientTypes.RetentionIntervalUnitValues?

        public init(
            targetAccounts: [Swift.String]? = nil,
            unshareInterval: Swift.Int? = nil,
            unshareIntervalUnit: DLMClientTypes.RetentionIntervalUnitValues? = nil
        )
        {
            self.targetAccounts = targetAccounts
            self.unshareInterval = unshareInterval
            self.unshareIntervalUnit = unshareIntervalUnit
        }
    }
}

extension DLMClientTypes {

    /// [Custom snapshot and AMI policies only] Specifies a schedule for a snapshot or AMI lifecycle policy.
    public struct Schedule: Swift.Sendable {
        /// [Custom snapshot policies that target volumes only] The snapshot archiving rule for the schedule. When you specify an archiving rule, snapshots are automatically moved from the standard tier to the archive tier once the schedule's retention threshold is met. Snapshots are then retained in the archive tier for the archive retention period that you specify. For more information about using snapshot archiving, see [Considerations for snapshot lifecycle policies](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/snapshot-ami-policy.html#dlm-archive).
        public var archiveRule: DLMClientTypes.ArchiveRule?
        /// Copy all user-defined tags on a source volume to snapshots of the volume created by this policy.
        public var copyTags: Swift.Bool?
        /// The creation rule.
        public var createRule: DLMClientTypes.CreateRule?
        /// Specifies a rule for copying snapshots or AMIs across Regions. You can't specify cross-Region copy rules for policies that create snapshots on an Outpost or in a Local Zone. If the policy creates snapshots in a Region, then snapshots can be copied to up to three Regions or Outposts.
        public var crossRegionCopyRules: [DLMClientTypes.CrossRegionCopyRule]?
        /// [Custom AMI policies only] The AMI deprecation rule for the schedule.
        public var deprecateRule: DLMClientTypes.DeprecateRule?
        /// [Custom snapshot policies only] The rule for enabling fast snapshot restore.
        public var fastRestoreRule: DLMClientTypes.FastRestoreRule?
        /// The name of the schedule.
        public var name: Swift.String?
        /// The retention rule for snapshots or AMIs created by the policy.
        public var retainRule: DLMClientTypes.RetainRule?
        /// [Custom snapshot policies only] The rule for sharing snapshots with other Amazon Web Services accounts.
        public var shareRules: [DLMClientTypes.ShareRule]?
        /// The tags to apply to policy-created resources. These user-defined tags are in addition to the Amazon Web Services-added lifecycle tags.
        public var tagsToAdd: [DLMClientTypes.Tag]?
        /// [AMI policies and snapshot policies that target instances only] A collection of key/value pairs with values determined dynamically when the policy is executed. Keys may be any valid Amazon EC2 tag key. Values must be in one of the two following formats: $(instance-id) or $(timestamp). Variable tags are only valid for EBS Snapshot Management – Instance policies.
        public var variableTags: [DLMClientTypes.Tag]?

        public init(
            archiveRule: DLMClientTypes.ArchiveRule? = nil,
            copyTags: Swift.Bool? = nil,
            createRule: DLMClientTypes.CreateRule? = nil,
            crossRegionCopyRules: [DLMClientTypes.CrossRegionCopyRule]? = nil,
            deprecateRule: DLMClientTypes.DeprecateRule? = nil,
            fastRestoreRule: DLMClientTypes.FastRestoreRule? = nil,
            name: Swift.String? = nil,
            retainRule: DLMClientTypes.RetainRule? = nil,
            shareRules: [DLMClientTypes.ShareRule]? = nil,
            tagsToAdd: [DLMClientTypes.Tag]? = nil,
            variableTags: [DLMClientTypes.Tag]? = nil
        )
        {
            self.archiveRule = archiveRule
            self.copyTags = copyTags
            self.createRule = createRule
            self.crossRegionCopyRules = crossRegionCopyRules
            self.deprecateRule = deprecateRule
            self.fastRestoreRule = fastRestoreRule
            self.name = name
            self.retainRule = retainRule
            self.shareRules = shareRules
            self.tagsToAdd = tagsToAdd
            self.variableTags = variableTags
        }
    }
}

extension DLMClientTypes {

    /// Specifies the configuration of a lifecycle policy.
    public struct PolicyDetails: Swift.Sendable {
        /// [Event-based policies only] The actions to be performed when the event-based policy is activated. You can specify only one action per policy.
        public var actions: [DLMClientTypes.Action]?
        /// [Default policies only] Indicates whether the policy should copy tags from the source resource to the snapshot or AMI. If you do not specify a value, the default is false. Default: false
        public var copyTags: Swift.Bool?
        /// [Default policies only] Specifies how often the policy should run and create snapshots or AMIs. The creation frequency can range from 1 to 7 days. If you do not specify a value, the default is 1. Default: 1
        public var createInterval: Swift.Int?
        /// [Default policies only] Specifies destination Regions for snapshot or AMI copies. You can specify up to 3 destination Regions. If you do not want to create cross-Region copies, omit this parameter.
        public var crossRegionCopyTargets: [DLMClientTypes.CrossRegionCopyTarget]?
        /// [Event-based policies only] The event that activates the event-based policy.
        public var eventSource: DLMClientTypes.EventSource?
        /// [Default policies only] Specifies exclusion parameters for volumes or instances for which you do not want to create snapshots or AMIs. The policy will not create snapshots or AMIs for target resources that match any of the specified exclusion parameters.
        public var exclusions: DLMClientTypes.Exclusions?
        /// [Default policies only] Defines the snapshot or AMI retention behavior for the policy if the source volume or instance is deleted, or if the policy enters the error, disabled, or deleted state. By default (ExtendDeletion=false):
        ///
        /// * If a source resource is deleted, Amazon Data Lifecycle Manager will continue to delete previously created snapshots or AMIs, up to but not including the last one, based on the specified retention period. If you want Amazon Data Lifecycle Manager to delete all snapshots or AMIs, including the last one, specify true.
        ///
        /// * If a policy enters the error, disabled, or deleted state, Amazon Data Lifecycle Manager stops deleting snapshots and AMIs. If you want Amazon Data Lifecycle Manager to continue deleting snapshots or AMIs, including the last one, if the policy enters one of these states, specify true.
        ///
        ///
        /// If you enable extended deletion (ExtendDeletion=true), you override both default behaviors simultaneously. If you do not specify a value, the default is false. Default: false
        public var extendDeletion: Swift.Bool?
        /// [Custom snapshot and AMI policies only] A set of optional parameters for snapshot and AMI lifecycle policies. If you are modifying a policy that was created or previously modified using the Amazon Data Lifecycle Manager console, then you must include this parameter and specify either the default values or the new values that you require. You can't omit this parameter or set its values to null.
        public var parameters: DLMClientTypes.Parameters?
        /// The type of policy to create. Specify one of the following:
        ///
        /// * SIMPLIFIED To create a default policy.
        ///
        /// * STANDARD To create a custom policy.
        public var policyLanguage: DLMClientTypes.PolicyLanguageValues?
        /// The type of policy. Specify EBS_SNAPSHOT_MANAGEMENT to create a lifecycle policy that manages the lifecycle of Amazon EBS snapshots. Specify IMAGE_MANAGEMENT to create a lifecycle policy that manages the lifecycle of EBS-backed AMIs. Specify EVENT_BASED_POLICY  to create an event-based policy that performs specific actions when a defined event occurs in your Amazon Web Services account. The default is EBS_SNAPSHOT_MANAGEMENT.
        public var policyType: DLMClientTypes.PolicyTypeValues?
        /// [Custom snapshot and AMI policies only] The location of the resources to backup.
        ///
        /// * If the source resources are located in a Region, specify CLOUD. In this case, the policy targets all resources of the specified type with matching target tags across all Availability Zones in the Region.
        ///
        /// * [Custom snapshot policies only] If the source resources are located in a Local Zone, specify LOCAL_ZONE. In this case, the policy targets all resources of the specified type with matching target tags across all Local Zones in the Region.
        ///
        /// * If the source resources are located on an Outpost in your account, specify OUTPOST. In this case, the policy targets all resources of the specified type with matching target tags across all of the Outposts in your account.
        public var resourceLocations: [DLMClientTypes.ResourceLocationValues]?
        /// [Default policies only] Specify the type of default policy to create.
        ///
        /// * To create a default policy for EBS snapshots, that creates snapshots of all volumes in the Region that do not have recent backups, specify VOLUME.
        ///
        /// * To create a default policy for EBS-backed AMIs, that creates EBS-backed AMIs from all instances in the Region that do not have recent backups, specify INSTANCE.
        public var resourceType: DLMClientTypes.ResourceTypeValues?
        /// [Custom snapshot policies only] The target resource type for snapshot and AMI lifecycle policies. Use VOLUME to create snapshots of individual volumes or use INSTANCE to create multi-volume snapshots from the volumes for an instance.
        public var resourceTypes: [DLMClientTypes.ResourceTypeValues]?
        /// [Default policies only] Specifies how long the policy should retain snapshots or AMIs before deleting them. The retention period can range from 2 to 14 days, but it must be greater than the creation frequency to ensure that the policy retains at least 1 snapshot or AMI at any given time. If you do not specify a value, the default is 7. Default: 7
        public var retainInterval: Swift.Int?
        /// [Custom snapshot and AMI policies only] The schedules of policy-defined actions for snapshot and AMI lifecycle policies. A policy can have up to four schedules—one mandatory schedule and up to three optional schedules.
        public var schedules: [DLMClientTypes.Schedule]?
        /// [Custom snapshot and AMI policies only] The single tag that identifies targeted resources for this policy.
        public var targetTags: [DLMClientTypes.Tag]?

        public init(
            actions: [DLMClientTypes.Action]? = nil,
            copyTags: Swift.Bool? = nil,
            createInterval: Swift.Int? = nil,
            crossRegionCopyTargets: [DLMClientTypes.CrossRegionCopyTarget]? = nil,
            eventSource: DLMClientTypes.EventSource? = nil,
            exclusions: DLMClientTypes.Exclusions? = nil,
            extendDeletion: Swift.Bool? = nil,
            parameters: DLMClientTypes.Parameters? = nil,
            policyLanguage: DLMClientTypes.PolicyLanguageValues? = nil,
            policyType: DLMClientTypes.PolicyTypeValues? = nil,
            resourceLocations: [DLMClientTypes.ResourceLocationValues]? = nil,
            resourceType: DLMClientTypes.ResourceTypeValues? = nil,
            resourceTypes: [DLMClientTypes.ResourceTypeValues]? = nil,
            retainInterval: Swift.Int? = nil,
            schedules: [DLMClientTypes.Schedule]? = nil,
            targetTags: [DLMClientTypes.Tag]? = nil
        )
        {
            self.actions = actions
            self.copyTags = copyTags
            self.createInterval = createInterval
            self.crossRegionCopyTargets = crossRegionCopyTargets
            self.eventSource = eventSource
            self.exclusions = exclusions
            self.extendDeletion = extendDeletion
            self.parameters = parameters
            self.policyLanguage = policyLanguage
            self.policyType = policyType
            self.resourceLocations = resourceLocations
            self.resourceType = resourceType
            self.resourceTypes = resourceTypes
            self.retainInterval = retainInterval
            self.schedules = schedules
            self.targetTags = targetTags
        }
    }
}

extension DLMClientTypes {

    public enum SettablePolicyStateValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case enabled
        case sdkUnknown(Swift.String)

        public static var allCases: [SettablePolicyStateValues] {
            return [
                .disabled,
                .enabled
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "DISABLED"
            case .enabled: return "ENABLED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct CreateLifecyclePolicyInput: Swift.Sendable {
    /// [Default policies only] Indicates whether the policy should copy tags from the source resource to the snapshot or AMI. If you do not specify a value, the default is false. Default: false
    public var copyTags: Swift.Bool?
    /// [Default policies only] Specifies how often the policy should run and create snapshots or AMIs. The creation frequency can range from 1 to 7 days. If you do not specify a value, the default is 1. Default: 1
    public var createInterval: Swift.Int?
    /// [Default policies only] Specifies destination Regions for snapshot or AMI copies. You can specify up to 3 destination Regions. If you do not want to create cross-Region copies, omit this parameter.
    public var crossRegionCopyTargets: [DLMClientTypes.CrossRegionCopyTarget]?
    /// [Default policies only] Specify the type of default policy to create.
    ///
    /// * To create a default policy for EBS snapshots, that creates snapshots of all volumes in the Region that do not have recent backups, specify VOLUME.
    ///
    /// * To create a default policy for EBS-backed AMIs, that creates EBS-backed AMIs from all instances in the Region that do not have recent backups, specify INSTANCE.
    public var defaultPolicy: DLMClientTypes.DefaultPolicyTypeValues?
    /// A description of the lifecycle policy. The characters ^[0-9A-Za-z _-]+$ are supported.
    /// This member is required.
    public var description: Swift.String?
    /// [Default policies only] Specifies exclusion parameters for volumes or instances for which you do not want to create snapshots or AMIs. The policy will not create snapshots or AMIs for target resources that match any of the specified exclusion parameters.
    public var exclusions: DLMClientTypes.Exclusions?
    /// The Amazon Resource Name (ARN) of the IAM role used to run the operations specified by the lifecycle policy.
    /// This member is required.
    public var executionRoleArn: Swift.String?
    /// [Default policies only] Defines the snapshot or AMI retention behavior for the policy if the source volume or instance is deleted, or if the policy enters the error, disabled, or deleted state. By default (ExtendDeletion=false):
    ///
    /// * If a source resource is deleted, Amazon Data Lifecycle Manager will continue to delete previously created snapshots or AMIs, up to but not including the last one, based on the specified retention period. If you want Amazon Data Lifecycle Manager to delete all snapshots or AMIs, including the last one, specify true.
    ///
    /// * If a policy enters the error, disabled, or deleted state, Amazon Data Lifecycle Manager stops deleting snapshots and AMIs. If you want Amazon Data Lifecycle Manager to continue deleting snapshots or AMIs, including the last one, if the policy enters one of these states, specify true.
    ///
    ///
    /// If you enable extended deletion (ExtendDeletion=true), you override both default behaviors simultaneously. If you do not specify a value, the default is false. Default: false
    public var extendDeletion: Swift.Bool?
    /// The configuration details of the lifecycle policy. If you create a default policy, you can specify the request parameters either in the request body, or in the PolicyDetails request structure, but not both.
    public var policyDetails: DLMClientTypes.PolicyDetails?
    /// [Default policies only] Specifies how long the policy should retain snapshots or AMIs before deleting them. The retention period can range from 2 to 14 days, but it must be greater than the creation frequency to ensure that the policy retains at least 1 snapshot or AMI at any given time. If you do not specify a value, the default is 7. Default: 7
    public var retainInterval: Swift.Int?
    /// The activation state of the lifecycle policy after creation.
    /// This member is required.
    public var state: DLMClientTypes.SettablePolicyStateValues?
    /// The tags to apply to the lifecycle policy during creation.
    public var tags: [Swift.String: Swift.String]?

    public init(
        copyTags: Swift.Bool? = nil,
        createInterval: Swift.Int? = nil,
        crossRegionCopyTargets: [DLMClientTypes.CrossRegionCopyTarget]? = nil,
        defaultPolicy: DLMClientTypes.DefaultPolicyTypeValues? = nil,
        description: Swift.String? = nil,
        exclusions: DLMClientTypes.Exclusions? = nil,
        executionRoleArn: Swift.String? = nil,
        extendDeletion: Swift.Bool? = nil,
        policyDetails: DLMClientTypes.PolicyDetails? = nil,
        retainInterval: Swift.Int? = nil,
        state: DLMClientTypes.SettablePolicyStateValues? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.copyTags = copyTags
        self.createInterval = createInterval
        self.crossRegionCopyTargets = crossRegionCopyTargets
        self.defaultPolicy = defaultPolicy
        self.description = description
        self.exclusions = exclusions
        self.executionRoleArn = executionRoleArn
        self.extendDeletion = extendDeletion
        self.policyDetails = policyDetails
        self.retainInterval = retainInterval
        self.state = state
        self.tags = tags
    }
}

public struct CreateLifecyclePolicyOutput: Swift.Sendable {
    /// The identifier of the lifecycle policy.
    public var policyId: Swift.String?

    public init(
        policyId: Swift.String? = nil
    )
    {
        self.policyId = policyId
    }
}

extension DLMClientTypes {

    public enum DefaultPoliciesTypeValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case all
        case instance
        case volume
        case sdkUnknown(Swift.String)

        public static var allCases: [DefaultPoliciesTypeValues] {
            return [
                .all,
                .instance,
                .volume
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .all: return "ALL"
            case .instance: return "INSTANCE"
            case .volume: return "VOLUME"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// A requested resource was not found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var code: Swift.String? = nil
        public internal(set) var message: Swift.String? = nil
        /// Value is a list of resource IDs that were not found.
        public internal(set) var resourceIds: [Swift.String]? = nil
        /// Value is the type of resource that was not found.
        public internal(set) var resourceType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        code: Swift.String? = nil,
        message: Swift.String? = nil,
        resourceIds: [Swift.String]? = nil,
        resourceType: Swift.String? = nil
    )
    {
        self.properties.code = code
        self.properties.message = message
        self.properties.resourceIds = resourceIds
        self.properties.resourceType = resourceType
    }
}

public struct DeleteLifecyclePolicyInput: Swift.Sendable {
    /// The identifier of the lifecycle policy.
    /// This member is required.
    public var policyId: Swift.String?

    public init(
        policyId: Swift.String? = nil
    )
    {
        self.policyId = policyId
    }
}

public struct DeleteLifecyclePolicyOutput: Swift.Sendable {

    public init() { }
}

extension DLMClientTypes {

    public enum GettablePolicyStateValues: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case enabled
        case error
        case sdkUnknown(Swift.String)

        public static var allCases: [GettablePolicyStateValues] {
            return [
                .disabled,
                .enabled,
                .error
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "DISABLED"
            case .enabled: return "ENABLED"
            case .error: return "ERROR"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct GetLifecyclePoliciesInput: Swift.Sendable {
    /// [Default policies only] Specifies the type of default policy to get. Specify one of the following:
    ///
    /// * VOLUME - To get only the default policy for EBS snapshots
    ///
    /// * INSTANCE - To get only the default policy for EBS-backed AMIs
    ///
    /// * ALL - To get all default policies
    public var defaultPolicyType: DLMClientTypes.DefaultPoliciesTypeValues?
    /// The identifiers of the data lifecycle policies.
    public var policyIds: [Swift.String]?
    /// The resource type.
    public var resourceTypes: [DLMClientTypes.ResourceTypeValues]?
    /// The activation state.
    public var state: DLMClientTypes.GettablePolicyStateValues?
    /// The tags to add to objects created by the policy. Tags are strings in the format key=value. These user-defined tags are added in addition to the Amazon Web Services-added lifecycle tags.
    public var tagsToAdd: [Swift.String]?
    /// The target tag for a policy. Tags are strings in the format key=value.
    public var targetTags: [Swift.String]?

    public init(
        defaultPolicyType: DLMClientTypes.DefaultPoliciesTypeValues? = nil,
        policyIds: [Swift.String]? = nil,
        resourceTypes: [DLMClientTypes.ResourceTypeValues]? = nil,
        state: DLMClientTypes.GettablePolicyStateValues? = nil,
        tagsToAdd: [Swift.String]? = nil,
        targetTags: [Swift.String]? = nil
    )
    {
        self.defaultPolicyType = defaultPolicyType
        self.policyIds = policyIds
        self.resourceTypes = resourceTypes
        self.state = state
        self.tagsToAdd = tagsToAdd
        self.targetTags = targetTags
    }
}

extension DLMClientTypes {

    /// Summary information about a lifecycle policy.
    public struct LifecyclePolicySummary: Swift.Sendable {
        /// [Default policies only] The type of default policy. Values include:
        ///
        /// * VOLUME - Default policy for EBS snapshots
        ///
        /// * INSTANCE - Default policy for EBS-backed AMIs
        public var defaultPolicy: Swift.Bool?
        /// The description of the lifecycle policy.
        public var description: Swift.String?
        /// The identifier of the lifecycle policy.
        public var policyId: Swift.String?
        /// The type of policy. EBS_SNAPSHOT_MANAGEMENT indicates that the policy manages the lifecycle of Amazon EBS snapshots. IMAGE_MANAGEMENT indicates that the policy manages the lifecycle of EBS-backed AMIs. EVENT_BASED_POLICY indicates that the policy automates cross-account snapshot copies for snapshots that are shared with your account.
        public var policyType: DLMClientTypes.PolicyTypeValues?
        /// The activation state of the lifecycle policy.
        public var state: DLMClientTypes.GettablePolicyStateValues?
        /// The tags.
        public var tags: [Swift.String: Swift.String]?

        public init(
            defaultPolicy: Swift.Bool? = nil,
            description: Swift.String? = nil,
            policyId: Swift.String? = nil,
            policyType: DLMClientTypes.PolicyTypeValues? = nil,
            state: DLMClientTypes.GettablePolicyStateValues? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.defaultPolicy = defaultPolicy
            self.description = description
            self.policyId = policyId
            self.policyType = policyType
            self.state = state
            self.tags = tags
        }
    }
}

public struct GetLifecyclePoliciesOutput: Swift.Sendable {
    /// Summary information about the lifecycle policies.
    public var policies: [DLMClientTypes.LifecyclePolicySummary]?

    public init(
        policies: [DLMClientTypes.LifecyclePolicySummary]? = nil
    )
    {
        self.policies = policies
    }
}

public struct GetLifecyclePolicyInput: Swift.Sendable {
    /// The identifier of the lifecycle policy.
    /// This member is required.
    public var policyId: Swift.String?

    public init(
        policyId: Swift.String? = nil
    )
    {
        self.policyId = policyId
    }
}

extension DLMClientTypes {

    /// Information about a lifecycle policy.
    public struct LifecyclePolicy: Swift.Sendable {
        /// The local date and time when the lifecycle policy was created.
        public var dateCreated: Foundation.Date?
        /// The local date and time when the lifecycle policy was last modified.
        public var dateModified: Foundation.Date?
        /// Indicates whether the policy is a default lifecycle policy or a custom lifecycle policy.
        ///
        /// * true - the policy is a default policy.
        ///
        /// * false - the policy is a custom policy.
        public var defaultPolicy: Swift.Bool?
        /// The description of the lifecycle policy.
        public var description: Swift.String?
        /// The Amazon Resource Name (ARN) of the IAM role used to run the operations specified by the lifecycle policy.
        public var executionRoleArn: Swift.String?
        /// The Amazon Resource Name (ARN) of the policy.
        public var policyArn: Swift.String?
        /// The configuration of the lifecycle policy
        public var policyDetails: DLMClientTypes.PolicyDetails?
        /// The identifier of the lifecycle policy.
        public var policyId: Swift.String?
        /// The activation state of the lifecycle policy.
        public var state: DLMClientTypes.GettablePolicyStateValues?
        /// The description of the status.
        public var statusMessage: Swift.String?
        /// The tags.
        public var tags: [Swift.String: Swift.String]?

        public init(
            dateCreated: Foundation.Date? = nil,
            dateModified: Foundation.Date? = nil,
            defaultPolicy: Swift.Bool? = nil,
            description: Swift.String? = nil,
            executionRoleArn: Swift.String? = nil,
            policyArn: Swift.String? = nil,
            policyDetails: DLMClientTypes.PolicyDetails? = nil,
            policyId: Swift.String? = nil,
            state: DLMClientTypes.GettablePolicyStateValues? = nil,
            statusMessage: Swift.String? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.dateCreated = dateCreated
            self.dateModified = dateModified
            self.defaultPolicy = defaultPolicy
            self.description = description
            self.executionRoleArn = executionRoleArn
            self.policyArn = policyArn
            self.policyDetails = policyDetails
            self.policyId = policyId
            self.state = state
            self.statusMessage = statusMessage
            self.tags = tags
        }
    }
}

public struct GetLifecyclePolicyOutput: Swift.Sendable {
    /// Detailed information about the lifecycle policy.
    public var policy: DLMClientTypes.LifecyclePolicy?

    public init(
        policy: DLMClientTypes.LifecyclePolicy? = nil
    )
    {
        self.policy = policy
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// Information about the tags.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// One or more tags.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tag keys.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateLifecyclePolicyInput: Swift.Sendable {
    /// [Default policies only] Indicates whether the policy should copy tags from the source resource to the snapshot or AMI.
    public var copyTags: Swift.Bool?
    /// [Default policies only] Specifies how often the policy should run and create snapshots or AMIs. The creation frequency can range from 1 to 7 days.
    public var createInterval: Swift.Int?
    /// [Default policies only] Specifies destination Regions for snapshot or AMI copies. You can specify up to 3 destination Regions. If you do not want to create cross-Region copies, omit this parameter.
    public var crossRegionCopyTargets: [DLMClientTypes.CrossRegionCopyTarget]?
    /// A description of the lifecycle policy.
    public var description: Swift.String?
    /// [Default policies only] Specifies exclusion parameters for volumes or instances for which you do not want to create snapshots or AMIs. The policy will not create snapshots or AMIs for target resources that match any of the specified exclusion parameters.
    public var exclusions: DLMClientTypes.Exclusions?
    /// The Amazon Resource Name (ARN) of the IAM role used to run the operations specified by the lifecycle policy.
    public var executionRoleArn: Swift.String?
    /// [Default policies only] Defines the snapshot or AMI retention behavior for the policy if the source volume or instance is deleted, or if the policy enters the error, disabled, or deleted state. By default (ExtendDeletion=false):
    ///
    /// * If a source resource is deleted, Amazon Data Lifecycle Manager will continue to delete previously created snapshots or AMIs, up to but not including the last one, based on the specified retention period. If you want Amazon Data Lifecycle Manager to delete all snapshots or AMIs, including the last one, specify true.
    ///
    /// * If a policy enters the error, disabled, or deleted state, Amazon Data Lifecycle Manager stops deleting snapshots and AMIs. If you want Amazon Data Lifecycle Manager to continue deleting snapshots or AMIs, including the last one, if the policy enters one of these states, specify true.
    ///
    ///
    /// If you enable extended deletion (ExtendDeletion=true), you override both default behaviors simultaneously. Default: false
    public var extendDeletion: Swift.Bool?
    /// The configuration of the lifecycle policy. You cannot update the policy type or the resource type.
    public var policyDetails: DLMClientTypes.PolicyDetails?
    /// The identifier of the lifecycle policy.
    /// This member is required.
    public var policyId: Swift.String?
    /// [Default policies only] Specifies how long the policy should retain snapshots or AMIs before deleting them. The retention period can range from 2 to 14 days, but it must be greater than the creation frequency to ensure that the policy retains at least 1 snapshot or AMI at any given time.
    public var retainInterval: Swift.Int?
    /// The desired activation state of the lifecycle policy after creation.
    public var state: DLMClientTypes.SettablePolicyStateValues?

    public init(
        copyTags: Swift.Bool? = nil,
        createInterval: Swift.Int? = nil,
        crossRegionCopyTargets: [DLMClientTypes.CrossRegionCopyTarget]? = nil,
        description: Swift.String? = nil,
        exclusions: DLMClientTypes.Exclusions? = nil,
        executionRoleArn: Swift.String? = nil,
        extendDeletion: Swift.Bool? = nil,
        policyDetails: DLMClientTypes.PolicyDetails? = nil,
        policyId: Swift.String? = nil,
        retainInterval: Swift.Int? = nil,
        state: DLMClientTypes.SettablePolicyStateValues? = nil
    )
    {
        self.copyTags = copyTags
        self.createInterval = createInterval
        self.crossRegionCopyTargets = crossRegionCopyTargets
        self.description = description
        self.exclusions = exclusions
        self.executionRoleArn = executionRoleArn
        self.extendDeletion = extendDeletion
        self.policyDetails = policyDetails
        self.policyId = policyId
        self.retainInterval = retainInterval
        self.state = state
    }
}

public struct UpdateLifecyclePolicyOutput: Swift.Sendable {

    public init() { }
}

extension CreateLifecyclePolicyInput {

    static func urlPathProvider(_ value: CreateLifecyclePolicyInput) -> Swift.String? {
        return "/policies"
    }
}

extension DeleteLifecyclePolicyInput {

    static func urlPathProvider(_ value: DeleteLifecyclePolicyInput) -> Swift.String? {
        guard let policyId = value.policyId else {
            return nil
        }
        return "/policies/\(policyId.urlPercentEncoding())"
    }
}

extension GetLifecyclePoliciesInput {

    static func urlPathProvider(_ value: GetLifecyclePoliciesInput) -> Swift.String? {
        return "/policies"
    }
}

extension GetLifecyclePoliciesInput {

    static func queryItemProvider(_ value: GetLifecyclePoliciesInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let resourceTypes = value.resourceTypes {
            resourceTypes.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "resourceTypes".urlPercentEncoding(), value: Swift.String(queryItemValue.rawValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        if let tagsToAdd = value.tagsToAdd {
            tagsToAdd.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "tagsToAdd".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        if let state = value.state {
            let stateQueryItem = Smithy.URIQueryItem(name: "state".urlPercentEncoding(), value: Swift.String(state.rawValue).urlPercentEncoding())
            items.append(stateQueryItem)
        }
        if let policyIds = value.policyIds {
            policyIds.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "policyIds".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        if let targetTags = value.targetTags {
            targetTags.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "targetTags".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        if let defaultPolicyType = value.defaultPolicyType {
            let defaultPolicyTypeQueryItem = Smithy.URIQueryItem(name: "defaultPolicyType".urlPercentEncoding(), value: Swift.String(defaultPolicyType.rawValue).urlPercentEncoding())
            items.append(defaultPolicyTypeQueryItem)
        }
        return items
    }
}

extension GetLifecyclePolicyInput {

    static func urlPathProvider(_ value: GetLifecyclePolicyInput) -> Swift.String? {
        guard let policyId = value.policyId else {
            return nil
        }
        return "/policies/\(policyId.urlPercentEncoding())"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension UpdateLifecyclePolicyInput {

    static func urlPathProvider(_ value: UpdateLifecyclePolicyInput) -> Swift.String? {
        guard let policyId = value.policyId else {
            return nil
        }
        return "/policies/\(policyId.urlPercentEncoding())"
    }
}

extension CreateLifecyclePolicyInput {

    static func write(value: CreateLifecyclePolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CopyTags"].write(value.copyTags)
        try writer["CreateInterval"].write(value.createInterval)
        try writer["CrossRegionCopyTargets"].writeList(value.crossRegionCopyTargets, memberWritingClosure: DLMClientTypes.CrossRegionCopyTarget.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["DefaultPolicy"].write(value.defaultPolicy)
        try writer["Description"].write(value.description)
        try writer["Exclusions"].write(value.exclusions, with: DLMClientTypes.Exclusions.write(value:to:))
        try writer["ExecutionRoleArn"].write(value.executionRoleArn)
        try writer["ExtendDeletion"].write(value.extendDeletion)
        try writer["PolicyDetails"].write(value.policyDetails, with: DLMClientTypes.PolicyDetails.write(value:to:))
        try writer["RetainInterval"].write(value.retainInterval)
        try writer["State"].write(value.state)
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension UpdateLifecyclePolicyInput {

    static func write(value: UpdateLifecyclePolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CopyTags"].write(value.copyTags)
        try writer["CreateInterval"].write(value.createInterval)
        try writer["CrossRegionCopyTargets"].writeList(value.crossRegionCopyTargets, memberWritingClosure: DLMClientTypes.CrossRegionCopyTarget.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Description"].write(value.description)
        try writer["Exclusions"].write(value.exclusions, with: DLMClientTypes.Exclusions.write(value:to:))
        try writer["ExecutionRoleArn"].write(value.executionRoleArn)
        try writer["ExtendDeletion"].write(value.extendDeletion)
        try writer["PolicyDetails"].write(value.policyDetails, with: DLMClientTypes.PolicyDetails.write(value:to:))
        try writer["RetainInterval"].write(value.retainInterval)
        try writer["State"].write(value.state)
    }
}

extension CreateLifecyclePolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLifecyclePolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateLifecyclePolicyOutput()
        value.policyId = try reader["PolicyId"].readIfPresent()
        return value
    }
}

extension DeleteLifecyclePolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteLifecyclePolicyOutput {
        return DeleteLifecyclePolicyOutput()
    }
}

extension GetLifecyclePoliciesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetLifecyclePoliciesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetLifecyclePoliciesOutput()
        value.policies = try reader["Policies"].readListIfPresent(memberReadingClosure: DLMClientTypes.LifecyclePolicySummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension GetLifecyclePolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetLifecyclePolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetLifecyclePolicyOutput()
        value.policy = try reader["Policy"].readIfPresent(with: DLMClientTypes.LifecyclePolicy.read(from:))
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateLifecyclePolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLifecyclePolicyOutput {
        return UpdateLifecyclePolicyOutput()
    }
}

enum CreateLifecyclePolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteLifecyclePolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetLifecyclePoliciesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetLifecyclePolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLifecyclePolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension InvalidRequestException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InvalidRequestException {
        let reader = baseError.errorBodyReader
        var value = InvalidRequestException()
        value.properties.code = try reader["Code"].readIfPresent()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.mutuallyExclusiveParameters = try reader["MutuallyExclusiveParameters"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.properties.requiredParameters = try reader["RequiredParameters"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.code = try reader["Code"].readIfPresent()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LimitExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> LimitExceededException {
        let reader = baseError.errorBodyReader
        var value = LimitExceededException()
        value.properties.code = try reader["Code"].readIfPresent()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.resourceType = try reader["ResourceType"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.code = try reader["Code"].readIfPresent()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.resourceIds = try reader["ResourceIds"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.properties.resourceType = try reader["ResourceType"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DLMClientTypes.LifecyclePolicySummary {

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.LifecyclePolicySummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.LifecyclePolicySummary()
        value.policyId = try reader["PolicyId"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.state = try reader["State"].readIfPresent()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.policyType = try reader["PolicyType"].readIfPresent()
        value.defaultPolicy = try reader["DefaultPolicy"].readIfPresent()
        return value
    }
}

extension DLMClientTypes.LifecyclePolicy {

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.LifecyclePolicy {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.LifecyclePolicy()
        value.policyId = try reader["PolicyId"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.state = try reader["State"].readIfPresent()
        value.statusMessage = try reader["StatusMessage"].readIfPresent()
        value.executionRoleArn = try reader["ExecutionRoleArn"].readIfPresent()
        value.dateCreated = try reader["DateCreated"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dateModified = try reader["DateModified"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.policyDetails = try reader["PolicyDetails"].readIfPresent(with: DLMClientTypes.PolicyDetails.read(from:))
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.policyArn = try reader["PolicyArn"].readIfPresent()
        value.defaultPolicy = try reader["DefaultPolicy"].readIfPresent()
        return value
    }
}

extension DLMClientTypes.PolicyDetails {

    static func write(value: DLMClientTypes.PolicyDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Actions"].writeList(value.actions, memberWritingClosure: DLMClientTypes.Action.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["CopyTags"].write(value.copyTags)
        try writer["CreateInterval"].write(value.createInterval)
        try writer["CrossRegionCopyTargets"].writeList(value.crossRegionCopyTargets, memberWritingClosure: DLMClientTypes.CrossRegionCopyTarget.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["EventSource"].write(value.eventSource, with: DLMClientTypes.EventSource.write(value:to:))
        try writer["Exclusions"].write(value.exclusions, with: DLMClientTypes.Exclusions.write(value:to:))
        try writer["ExtendDeletion"].write(value.extendDeletion)
        try writer["Parameters"].write(value.parameters, with: DLMClientTypes.Parameters.write(value:to:))
        try writer["PolicyLanguage"].write(value.policyLanguage)
        try writer["PolicyType"].write(value.policyType)
        try writer["ResourceLocations"].writeList(value.resourceLocations, memberWritingClosure: SmithyReadWrite.WritingClosureBox<DLMClientTypes.ResourceLocationValues>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ResourceType"].write(value.resourceType)
        try writer["ResourceTypes"].writeList(value.resourceTypes, memberWritingClosure: SmithyReadWrite.WritingClosureBox<DLMClientTypes.ResourceTypeValues>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["RetainInterval"].write(value.retainInterval)
        try writer["Schedules"].writeList(value.schedules, memberWritingClosure: DLMClientTypes.Schedule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["TargetTags"].writeList(value.targetTags, memberWritingClosure: DLMClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.PolicyDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.PolicyDetails()
        value.policyType = try reader["PolicyType"].readIfPresent()
        value.resourceTypes = try reader["ResourceTypes"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<DLMClientTypes.ResourceTypeValues>().read(from:), memberNodeInfo: "member", isFlattened: false)
        value.resourceLocations = try reader["ResourceLocations"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<DLMClientTypes.ResourceLocationValues>().read(from:), memberNodeInfo: "member", isFlattened: false)
        value.targetTags = try reader["TargetTags"].readListIfPresent(memberReadingClosure: DLMClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.schedules = try reader["Schedules"].readListIfPresent(memberReadingClosure: DLMClientTypes.Schedule.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.parameters = try reader["Parameters"].readIfPresent(with: DLMClientTypes.Parameters.read(from:))
        value.eventSource = try reader["EventSource"].readIfPresent(with: DLMClientTypes.EventSource.read(from:))
        value.actions = try reader["Actions"].readListIfPresent(memberReadingClosure: DLMClientTypes.Action.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.policyLanguage = try reader["PolicyLanguage"].readIfPresent()
        value.resourceType = try reader["ResourceType"].readIfPresent()
        value.createInterval = try reader["CreateInterval"].readIfPresent()
        value.retainInterval = try reader["RetainInterval"].readIfPresent()
        value.copyTags = try reader["CopyTags"].readIfPresent()
        value.crossRegionCopyTargets = try reader["CrossRegionCopyTargets"].readListIfPresent(memberReadingClosure: DLMClientTypes.CrossRegionCopyTarget.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.extendDeletion = try reader["ExtendDeletion"].readIfPresent()
        value.exclusions = try reader["Exclusions"].readIfPresent(with: DLMClientTypes.Exclusions.read(from:))
        return value
    }
}

extension DLMClientTypes.Exclusions {

    static func write(value: DLMClientTypes.Exclusions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ExcludeBootVolumes"].write(value.excludeBootVolumes)
        try writer["ExcludeTags"].writeList(value.excludeTags, memberWritingClosure: DLMClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ExcludeVolumeTypes"].writeList(value.excludeVolumeTypes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.Exclusions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.Exclusions()
        value.excludeBootVolumes = try reader["ExcludeBootVolumes"].readIfPresent()
        value.excludeVolumeTypes = try reader["ExcludeVolumeTypes"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.excludeTags = try reader["ExcludeTags"].readListIfPresent(memberReadingClosure: DLMClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DLMClientTypes.Tag {

    static func write(value: DLMClientTypes.Tag?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Key"].write(value.key)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.Tag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.Tag()
        value.key = try reader["Key"].readIfPresent() ?? ""
        value.value = try reader["Value"].readIfPresent() ?? ""
        return value
    }
}

extension DLMClientTypes.CrossRegionCopyTarget {

    static func write(value: DLMClientTypes.CrossRegionCopyTarget?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["TargetRegion"].write(value.targetRegion)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.CrossRegionCopyTarget {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.CrossRegionCopyTarget()
        value.targetRegion = try reader["TargetRegion"].readIfPresent()
        return value
    }
}

extension DLMClientTypes.Action {

    static func write(value: DLMClientTypes.Action?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CrossRegionCopy"].writeList(value.crossRegionCopy, memberWritingClosure: DLMClientTypes.CrossRegionCopyAction.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Name"].write(value.name)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.Action {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.Action()
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.crossRegionCopy = try reader["CrossRegionCopy"].readListIfPresent(memberReadingClosure: DLMClientTypes.CrossRegionCopyAction.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DLMClientTypes.CrossRegionCopyAction {

    static func write(value: DLMClientTypes.CrossRegionCopyAction?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["EncryptionConfiguration"].write(value.encryptionConfiguration, with: DLMClientTypes.EncryptionConfiguration.write(value:to:))
        try writer["RetainRule"].write(value.retainRule, with: DLMClientTypes.CrossRegionCopyRetainRule.write(value:to:))
        try writer["Target"].write(value.target)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.CrossRegionCopyAction {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.CrossRegionCopyAction()
        value.target = try reader["Target"].readIfPresent() ?? ""
        value.encryptionConfiguration = try reader["EncryptionConfiguration"].readIfPresent(with: DLMClientTypes.EncryptionConfiguration.read(from:))
        value.retainRule = try reader["RetainRule"].readIfPresent(with: DLMClientTypes.CrossRegionCopyRetainRule.read(from:))
        return value
    }
}

extension DLMClientTypes.CrossRegionCopyRetainRule {

    static func write(value: DLMClientTypes.CrossRegionCopyRetainRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Interval"].write(value.interval)
        try writer["IntervalUnit"].write(value.intervalUnit)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.CrossRegionCopyRetainRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.CrossRegionCopyRetainRule()
        value.interval = try reader["Interval"].readIfPresent()
        value.intervalUnit = try reader["IntervalUnit"].readIfPresent()
        return value
    }
}

extension DLMClientTypes.EncryptionConfiguration {

    static func write(value: DLMClientTypes.EncryptionConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CmkArn"].write(value.cmkArn)
        try writer["Encrypted"].write(value.encrypted)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.EncryptionConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.EncryptionConfiguration()
        value.encrypted = try reader["Encrypted"].readIfPresent() ?? false
        value.cmkArn = try reader["CmkArn"].readIfPresent()
        return value
    }
}

extension DLMClientTypes.EventSource {

    static func write(value: DLMClientTypes.EventSource?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Parameters"].write(value.parameters, with: DLMClientTypes.EventParameters.write(value:to:))
        try writer["Type"].write(value.type)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.EventSource {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.EventSource()
        value.type = try reader["Type"].readIfPresent() ?? .sdkUnknown("")
        value.parameters = try reader["Parameters"].readIfPresent(with: DLMClientTypes.EventParameters.read(from:))
        return value
    }
}

extension DLMClientTypes.EventParameters {

    static func write(value: DLMClientTypes.EventParameters?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DescriptionRegex"].write(value.descriptionRegex)
        try writer["EventType"].write(value.eventType)
        try writer["SnapshotOwner"].writeList(value.snapshotOwner, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.EventParameters {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.EventParameters()
        value.eventType = try reader["EventType"].readIfPresent() ?? .sdkUnknown("")
        value.snapshotOwner = try reader["SnapshotOwner"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.descriptionRegex = try reader["DescriptionRegex"].readIfPresent() ?? ""
        return value
    }
}

extension DLMClientTypes.Parameters {

    static func write(value: DLMClientTypes.Parameters?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ExcludeBootVolume"].write(value.excludeBootVolume)
        try writer["ExcludeDataVolumeTags"].writeList(value.excludeDataVolumeTags, memberWritingClosure: DLMClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["NoReboot"].write(value.noReboot)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.Parameters {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.Parameters()
        value.excludeBootVolume = try reader["ExcludeBootVolume"].readIfPresent()
        value.noReboot = try reader["NoReboot"].readIfPresent()
        value.excludeDataVolumeTags = try reader["ExcludeDataVolumeTags"].readListIfPresent(memberReadingClosure: DLMClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DLMClientTypes.Schedule {

    static func write(value: DLMClientTypes.Schedule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ArchiveRule"].write(value.archiveRule, with: DLMClientTypes.ArchiveRule.write(value:to:))
        try writer["CopyTags"].write(value.copyTags)
        try writer["CreateRule"].write(value.createRule, with: DLMClientTypes.CreateRule.write(value:to:))
        try writer["CrossRegionCopyRules"].writeList(value.crossRegionCopyRules, memberWritingClosure: DLMClientTypes.CrossRegionCopyRule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["DeprecateRule"].write(value.deprecateRule, with: DLMClientTypes.DeprecateRule.write(value:to:))
        try writer["FastRestoreRule"].write(value.fastRestoreRule, with: DLMClientTypes.FastRestoreRule.write(value:to:))
        try writer["Name"].write(value.name)
        try writer["RetainRule"].write(value.retainRule, with: DLMClientTypes.RetainRule.write(value:to:))
        try writer["ShareRules"].writeList(value.shareRules, memberWritingClosure: DLMClientTypes.ShareRule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["TagsToAdd"].writeList(value.tagsToAdd, memberWritingClosure: DLMClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["VariableTags"].writeList(value.variableTags, memberWritingClosure: DLMClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.Schedule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.Schedule()
        value.name = try reader["Name"].readIfPresent()
        value.copyTags = try reader["CopyTags"].readIfPresent()
        value.tagsToAdd = try reader["TagsToAdd"].readListIfPresent(memberReadingClosure: DLMClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.variableTags = try reader["VariableTags"].readListIfPresent(memberReadingClosure: DLMClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.createRule = try reader["CreateRule"].readIfPresent(with: DLMClientTypes.CreateRule.read(from:))
        value.retainRule = try reader["RetainRule"].readIfPresent(with: DLMClientTypes.RetainRule.read(from:))
        value.fastRestoreRule = try reader["FastRestoreRule"].readIfPresent(with: DLMClientTypes.FastRestoreRule.read(from:))
        value.crossRegionCopyRules = try reader["CrossRegionCopyRules"].readListIfPresent(memberReadingClosure: DLMClientTypes.CrossRegionCopyRule.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.shareRules = try reader["ShareRules"].readListIfPresent(memberReadingClosure: DLMClientTypes.ShareRule.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.deprecateRule = try reader["DeprecateRule"].readIfPresent(with: DLMClientTypes.DeprecateRule.read(from:))
        value.archiveRule = try reader["ArchiveRule"].readIfPresent(with: DLMClientTypes.ArchiveRule.read(from:))
        return value
    }
}

extension DLMClientTypes.ArchiveRule {

    static func write(value: DLMClientTypes.ArchiveRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["RetainRule"].write(value.retainRule, with: DLMClientTypes.ArchiveRetainRule.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.ArchiveRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.ArchiveRule()
        value.retainRule = try reader["RetainRule"].readIfPresent(with: DLMClientTypes.ArchiveRetainRule.read(from:))
        return value
    }
}

extension DLMClientTypes.ArchiveRetainRule {

    static func write(value: DLMClientTypes.ArchiveRetainRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["RetentionArchiveTier"].write(value.retentionArchiveTier, with: DLMClientTypes.RetentionArchiveTier.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.ArchiveRetainRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.ArchiveRetainRule()
        value.retentionArchiveTier = try reader["RetentionArchiveTier"].readIfPresent(with: DLMClientTypes.RetentionArchiveTier.read(from:))
        return value
    }
}

extension DLMClientTypes.RetentionArchiveTier {

    static func write(value: DLMClientTypes.RetentionArchiveTier?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Count"].write(value.count)
        try writer["Interval"].write(value.interval)
        try writer["IntervalUnit"].write(value.intervalUnit)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.RetentionArchiveTier {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.RetentionArchiveTier()
        value.count = try reader["Count"].readIfPresent()
        value.interval = try reader["Interval"].readIfPresent()
        value.intervalUnit = try reader["IntervalUnit"].readIfPresent()
        return value
    }
}

extension DLMClientTypes.DeprecateRule {

    static func write(value: DLMClientTypes.DeprecateRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Count"].write(value.count)
        try writer["Interval"].write(value.interval)
        try writer["IntervalUnit"].write(value.intervalUnit)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.DeprecateRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.DeprecateRule()
        value.count = try reader["Count"].readIfPresent()
        value.interval = try reader["Interval"].readIfPresent()
        value.intervalUnit = try reader["IntervalUnit"].readIfPresent()
        return value
    }
}

extension DLMClientTypes.ShareRule {

    static func write(value: DLMClientTypes.ShareRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["TargetAccounts"].writeList(value.targetAccounts, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["UnshareInterval"].write(value.unshareInterval)
        try writer["UnshareIntervalUnit"].write(value.unshareIntervalUnit)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.ShareRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.ShareRule()
        value.targetAccounts = try reader["TargetAccounts"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.unshareInterval = try reader["UnshareInterval"].readIfPresent()
        value.unshareIntervalUnit = try reader["UnshareIntervalUnit"].readIfPresent()
        return value
    }
}

extension DLMClientTypes.CrossRegionCopyRule {

    static func write(value: DLMClientTypes.CrossRegionCopyRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CmkArn"].write(value.cmkArn)
        try writer["CopyTags"].write(value.copyTags)
        try writer["DeprecateRule"].write(value.deprecateRule, with: DLMClientTypes.CrossRegionCopyDeprecateRule.write(value:to:))
        try writer["Encrypted"].write(value.encrypted)
        try writer["RetainRule"].write(value.retainRule, with: DLMClientTypes.CrossRegionCopyRetainRule.write(value:to:))
        try writer["Target"].write(value.target)
        try writer["TargetRegion"].write(value.targetRegion)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.CrossRegionCopyRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.CrossRegionCopyRule()
        value.targetRegion = try reader["TargetRegion"].readIfPresent()
        value.target = try reader["Target"].readIfPresent()
        value.encrypted = try reader["Encrypted"].readIfPresent() ?? false
        value.cmkArn = try reader["CmkArn"].readIfPresent()
        value.copyTags = try reader["CopyTags"].readIfPresent()
        value.retainRule = try reader["RetainRule"].readIfPresent(with: DLMClientTypes.CrossRegionCopyRetainRule.read(from:))
        value.deprecateRule = try reader["DeprecateRule"].readIfPresent(with: DLMClientTypes.CrossRegionCopyDeprecateRule.read(from:))
        return value
    }
}

extension DLMClientTypes.CrossRegionCopyDeprecateRule {

    static func write(value: DLMClientTypes.CrossRegionCopyDeprecateRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Interval"].write(value.interval)
        try writer["IntervalUnit"].write(value.intervalUnit)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.CrossRegionCopyDeprecateRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.CrossRegionCopyDeprecateRule()
        value.interval = try reader["Interval"].readIfPresent()
        value.intervalUnit = try reader["IntervalUnit"].readIfPresent()
        return value
    }
}

extension DLMClientTypes.FastRestoreRule {

    static func write(value: DLMClientTypes.FastRestoreRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AvailabilityZones"].writeList(value.availabilityZones, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Count"].write(value.count)
        try writer["Interval"].write(value.interval)
        try writer["IntervalUnit"].write(value.intervalUnit)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.FastRestoreRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.FastRestoreRule()
        value.count = try reader["Count"].readIfPresent()
        value.interval = try reader["Interval"].readIfPresent()
        value.intervalUnit = try reader["IntervalUnit"].readIfPresent()
        value.availabilityZones = try reader["AvailabilityZones"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DLMClientTypes.RetainRule {

    static func write(value: DLMClientTypes.RetainRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Count"].write(value.count)
        try writer["Interval"].write(value.interval)
        try writer["IntervalUnit"].write(value.intervalUnit)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.RetainRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.RetainRule()
        value.count = try reader["Count"].readIfPresent()
        value.interval = try reader["Interval"].readIfPresent()
        value.intervalUnit = try reader["IntervalUnit"].readIfPresent()
        return value
    }
}

extension DLMClientTypes.CreateRule {

    static func write(value: DLMClientTypes.CreateRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CronExpression"].write(value.cronExpression)
        try writer["Interval"].write(value.interval)
        try writer["IntervalUnit"].write(value.intervalUnit)
        try writer["Location"].write(value.location)
        try writer["Scripts"].writeList(value.scripts, memberWritingClosure: DLMClientTypes.Script.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Times"].writeList(value.times, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.CreateRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.CreateRule()
        value.location = try reader["Location"].readIfPresent()
        value.interval = try reader["Interval"].readIfPresent()
        value.intervalUnit = try reader["IntervalUnit"].readIfPresent()
        value.times = try reader["Times"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.cronExpression = try reader["CronExpression"].readIfPresent()
        value.scripts = try reader["Scripts"].readListIfPresent(memberReadingClosure: DLMClientTypes.Script.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DLMClientTypes.Script {

    static func write(value: DLMClientTypes.Script?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ExecuteOperationOnScriptFailure"].write(value.executeOperationOnScriptFailure)
        try writer["ExecutionHandler"].write(value.executionHandler)
        try writer["ExecutionHandlerService"].write(value.executionHandlerService)
        try writer["ExecutionTimeout"].write(value.executionTimeout)
        try writer["MaximumRetryCount"].write(value.maximumRetryCount)
        try writer["Stages"].writeList(value.stages, memberWritingClosure: SmithyReadWrite.WritingClosureBox<DLMClientTypes.StageValues>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DLMClientTypes.Script {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DLMClientTypes.Script()
        value.stages = try reader["Stages"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<DLMClientTypes.StageValues>().read(from:), memberNodeInfo: "member", isFlattened: false)
        value.executionHandlerService = try reader["ExecutionHandlerService"].readIfPresent()
        value.executionHandler = try reader["ExecutionHandler"].readIfPresent() ?? ""
        value.executeOperationOnScriptFailure = try reader["ExecuteOperationOnScriptFailure"].readIfPresent()
        value.executionTimeout = try reader["ExecutionTimeout"].readIfPresent()
        value.maximumRetryCount = try reader["MaximumRetryCount"].readIfPresent()
        return value
    }
}

public enum DLMClientTypes {}
