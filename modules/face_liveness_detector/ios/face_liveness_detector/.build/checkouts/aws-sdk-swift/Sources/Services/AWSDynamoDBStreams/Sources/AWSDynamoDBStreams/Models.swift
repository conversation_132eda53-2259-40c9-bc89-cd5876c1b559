//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class Smithy<PERSON><PERSON><PERSON>I.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.AWSJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError

/// An error occurred on the server side.
public struct InternalServerError: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The server encountered an internal error trying to fulfill the request.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerError" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The operation tried to access a nonexistent table or index. The resource might not be specified correctly, or its status might not be ACTIVE.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The resource which is being requested does not exist.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input of a DescribeStream operation.
public struct DescribeStreamInput: Swift.Sendable {
    /// The shard ID of the first item that this operation will evaluate. Use the value that was returned for LastEvaluatedShardId in the previous operation.
    public var exclusiveStartShardId: Swift.String?
    /// The maximum number of shard objects to return. The upper limit is 100.
    public var limit: Swift.Int?
    /// The Amazon Resource Name (ARN) for the stream.
    /// This member is required.
    public var streamArn: Swift.String?

    public init(
        exclusiveStartShardId: Swift.String? = nil,
        limit: Swift.Int? = nil,
        streamArn: Swift.String? = nil
    )
    {
        self.exclusiveStartShardId = exclusiveStartShardId
        self.limit = limit
        self.streamArn = streamArn
    }
}

extension DynamoDBStreamsClientTypes {

    public enum KeyType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case hash
        case range
        case sdkUnknown(Swift.String)

        public static var allCases: [KeyType] {
            return [
                .hash,
                .range
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .hash: return "HASH"
            case .range: return "RANGE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DynamoDBStreamsClientTypes {

    /// Represents a single element of a key schema. A key schema specifies the attributes that make up the primary key of a table, or the key attributes of an index. A KeySchemaElement represents exactly one attribute of the primary key. For example, a simple primary key would be represented by one KeySchemaElement (for the partition key). A composite primary key would require one KeySchemaElement for the partition key, and another KeySchemaElement for the sort key. A KeySchemaElement must be a scalar, top-level attribute (not a nested attribute). The data type must be one of String, Number, or Binary. The attribute cannot be nested within a List or a Map.
    public struct KeySchemaElement: Swift.Sendable {
        /// The name of a key attribute.
        /// This member is required.
        public var attributeName: Swift.String?
        /// The role that this key attribute will assume:
        ///
        /// * HASH - partition key
        ///
        /// * RANGE - sort key
        ///
        ///
        /// The partition key of an item is also known as its hash attribute. The term "hash attribute" derives from DynamoDB's usage of an internal hash function to evenly distribute data items across partitions, based on their partition key values. The sort key of an item is also known as its range attribute. The term "range attribute" derives from the way DynamoDB stores items with the same partition key physically close together, in sorted order by the sort key value.
        /// This member is required.
        public var keyType: DynamoDBStreamsClientTypes.KeyType?

        public init(
            attributeName: Swift.String? = nil,
            keyType: DynamoDBStreamsClientTypes.KeyType? = nil
        )
        {
            self.attributeName = attributeName
            self.keyType = keyType
        }
    }
}

extension DynamoDBStreamsClientTypes {

    /// The beginning and ending sequence numbers for the stream records contained within a shard.
    public struct SequenceNumberRange: Swift.Sendable {
        /// The last sequence number for the stream records contained within a shard. String contains numeric characters only.
        public var endingSequenceNumber: Swift.String?
        /// The first sequence number for the stream records contained within a shard. String contains numeric characters only.
        public var startingSequenceNumber: Swift.String?

        public init(
            endingSequenceNumber: Swift.String? = nil,
            startingSequenceNumber: Swift.String? = nil
        )
        {
            self.endingSequenceNumber = endingSequenceNumber
            self.startingSequenceNumber = startingSequenceNumber
        }
    }
}

extension DynamoDBStreamsClientTypes {

    /// A uniquely identified group of stream records within a stream.
    public struct Shard: Swift.Sendable {
        /// The shard ID of the current shard's parent.
        public var parentShardId: Swift.String?
        /// The range of possible sequence numbers for the shard.
        public var sequenceNumberRange: DynamoDBStreamsClientTypes.SequenceNumberRange?
        /// The system-generated identifier for this shard.
        public var shardId: Swift.String?

        public init(
            parentShardId: Swift.String? = nil,
            sequenceNumberRange: DynamoDBStreamsClientTypes.SequenceNumberRange? = nil,
            shardId: Swift.String? = nil
        )
        {
            self.parentShardId = parentShardId
            self.sequenceNumberRange = sequenceNumberRange
            self.shardId = shardId
        }
    }
}

extension DynamoDBStreamsClientTypes {

    public enum StreamStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case disabling
        case enabled
        case enabling
        case sdkUnknown(Swift.String)

        public static var allCases: [StreamStatus] {
            return [
                .disabled,
                .disabling,
                .enabled,
                .enabling
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "DISABLED"
            case .disabling: return "DISABLING"
            case .enabled: return "ENABLED"
            case .enabling: return "ENABLING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DynamoDBStreamsClientTypes {

    public enum StreamViewType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case keysOnly
        case newAndOldImages
        case newImage
        case oldImage
        case sdkUnknown(Swift.String)

        public static var allCases: [StreamViewType] {
            return [
                .keysOnly,
                .newAndOldImages,
                .newImage,
                .oldImage
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .keysOnly: return "KEYS_ONLY"
            case .newAndOldImages: return "NEW_AND_OLD_IMAGES"
            case .newImage: return "NEW_IMAGE"
            case .oldImage: return "OLD_IMAGE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DynamoDBStreamsClientTypes {

    /// Represents all of the data describing a particular stream.
    public struct StreamDescription: Swift.Sendable {
        /// The date and time when the request to create this stream was issued.
        public var creationRequestDateTime: Foundation.Date?
        /// The key attribute(s) of the stream's DynamoDB table.
        public var keySchema: [DynamoDBStreamsClientTypes.KeySchemaElement]?
        /// The shard ID of the item where the operation stopped, inclusive of the previous result set. Use this value to start a new operation, excluding this value in the new request. If LastEvaluatedShardId is empty, then the "last page" of results has been processed and there is currently no more data to be retrieved. If LastEvaluatedShardId is not empty, it does not necessarily mean that there is more data in the result set. The only way to know when you have reached the end of the result set is when LastEvaluatedShardId is empty.
        public var lastEvaluatedShardId: Swift.String?
        /// The shards that comprise the stream.
        public var shards: [DynamoDBStreamsClientTypes.Shard]?
        /// The Amazon Resource Name (ARN) for the stream.
        public var streamArn: Swift.String?
        /// A timestamp, in ISO 8601 format, for this stream. Note that LatestStreamLabel is not a unique identifier for the stream, because it is possible that a stream from another table might have the same timestamp. However, the combination of the following three elements is guaranteed to be unique:
        ///
        /// * the Amazon Web Services customer ID.
        ///
        /// * the table name
        ///
        /// * the StreamLabel
        public var streamLabel: Swift.String?
        /// Indicates the current status of the stream:
        ///
        /// * ENABLING - Streams is currently being enabled on the DynamoDB table.
        ///
        /// * ENABLED - the stream is enabled.
        ///
        /// * DISABLING - Streams is currently being disabled on the DynamoDB table.
        ///
        /// * DISABLED - the stream is disabled.
        public var streamStatus: DynamoDBStreamsClientTypes.StreamStatus?
        /// Indicates the format of the records within this stream:
        ///
        /// * KEYS_ONLY - only the key attributes of items that were modified in the DynamoDB table.
        ///
        /// * NEW_IMAGE - entire items from the table, as they appeared after they were modified.
        ///
        /// * OLD_IMAGE - entire items from the table, as they appeared before they were modified.
        ///
        /// * NEW_AND_OLD_IMAGES - both the new and the old images of the items from the table.
        public var streamViewType: DynamoDBStreamsClientTypes.StreamViewType?
        /// The DynamoDB table with which the stream is associated.
        public var tableName: Swift.String?

        public init(
            creationRequestDateTime: Foundation.Date? = nil,
            keySchema: [DynamoDBStreamsClientTypes.KeySchemaElement]? = nil,
            lastEvaluatedShardId: Swift.String? = nil,
            shards: [DynamoDBStreamsClientTypes.Shard]? = nil,
            streamArn: Swift.String? = nil,
            streamLabel: Swift.String? = nil,
            streamStatus: DynamoDBStreamsClientTypes.StreamStatus? = nil,
            streamViewType: DynamoDBStreamsClientTypes.StreamViewType? = nil,
            tableName: Swift.String? = nil
        )
        {
            self.creationRequestDateTime = creationRequestDateTime
            self.keySchema = keySchema
            self.lastEvaluatedShardId = lastEvaluatedShardId
            self.shards = shards
            self.streamArn = streamArn
            self.streamLabel = streamLabel
            self.streamStatus = streamStatus
            self.streamViewType = streamViewType
            self.tableName = tableName
        }
    }
}

/// Represents the output of a DescribeStream operation.
public struct DescribeStreamOutput: Swift.Sendable {
    /// A complete description of the stream, including its creation date and time, the DynamoDB table associated with the stream, the shard IDs within the stream, and the beginning and ending sequence numbers of stream records within the shards.
    public var streamDescription: DynamoDBStreamsClientTypes.StreamDescription?

    public init(
        streamDescription: DynamoDBStreamsClientTypes.StreamDescription? = nil
    )
    {
        self.streamDescription = streamDescription
    }
}

/// The shard iterator has expired and can no longer be used to retrieve stream records. A shard iterator expires 15 minutes after it is retrieved using the GetShardIterator action.
public struct ExpiredIteratorException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The provided iterator exceeds the maximum age allowed.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ExpiredIteratorException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// There is no limit to the number of daily on-demand backups that can be taken. For most purposes, up to 500 simultaneous table operations are allowed per account. These operations include CreateTable, UpdateTable, DeleteTable,UpdateTimeToLive, RestoreTableFromBackup, and RestoreTableToPointInTime. When you are creating a table with one or more secondary indexes, you can have up to 250 such requests running at a time. However, if the table or index specifications are complex, then DynamoDB might temporarily reduce the number of concurrent operations. When importing into DynamoDB, up to 50 simultaneous import table operations are allowed per account. There is a soft account quota of 2,500 tables. GetRecords was called with a value of more than 1000 for the limit request parameter. More than 2 processes are reading from the same streams shard at the same time. Exceeding this limit may result in request throttling.
public struct LimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Too many operations for a given subscriber.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The operation attempted to read past the oldest stream record in a shard. In DynamoDB Streams, there is a 24 hour limit on data retention. Stream records whose age exceeds this limit are subject to removal (trimming) from the stream. You might receive a TrimmedDataAccessException if:
///
/// * You request a shard iterator with a sequence number older than the trim point (24 hours).
///
/// * You obtain a shard iterator, but before you use the iterator in a GetRecords request, a stream record in the shard exceeds the 24 hour period and is trimmed. This causes the iterator to access a record that no longer exists.
public struct TrimmedDataAccessException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// "The data you are trying to access has been trimmed.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TrimmedDataAccessException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input of a GetRecords operation.
public struct GetRecordsInput: Swift.Sendable {
    /// The maximum number of records to return from the shard. The upper limit is 1000.
    public var limit: Swift.Int?
    /// A shard iterator that was retrieved from a previous GetShardIterator operation. This iterator can be used to access the stream records in this shard.
    /// This member is required.
    public var shardIterator: Swift.String?

    public init(
        limit: Swift.Int? = nil,
        shardIterator: Swift.String? = nil
    )
    {
        self.limit = limit
        self.shardIterator = shardIterator
    }
}

extension DynamoDBStreamsClientTypes {

    public enum OperationType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case insert
        case modify
        case remove
        case sdkUnknown(Swift.String)

        public static var allCases: [OperationType] {
            return [
                .insert,
                .modify,
                .remove
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .insert: return "INSERT"
            case .modify: return "MODIFY"
            case .remove: return "REMOVE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DynamoDBStreamsClientTypes {

    /// Contains details about the type of identity that made the request.
    public struct Identity: Swift.Sendable {
        /// A unique identifier for the entity that made the call. For Time To Live, the principalId is "dynamodb.amazonaws.com".
        public var principalId: Swift.String?
        /// The type of the identity. For Time To Live, the type is "Service".
        public var type: Swift.String?

        public init(
            principalId: Swift.String? = nil,
            type: Swift.String? = nil
        )
        {
            self.principalId = principalId
            self.type = type
        }
    }
}

extension DynamoDBStreamsClientTypes {

    public enum ShardIteratorType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case afterSequenceNumber
        case atSequenceNumber
        case latest
        case trimHorizon
        case sdkUnknown(Swift.String)

        public static var allCases: [ShardIteratorType] {
            return [
                .afterSequenceNumber,
                .atSequenceNumber,
                .latest,
                .trimHorizon
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .afterSequenceNumber: return "AFTER_SEQUENCE_NUMBER"
            case .atSequenceNumber: return "AT_SEQUENCE_NUMBER"
            case .latest: return "LATEST"
            case .trimHorizon: return "TRIM_HORIZON"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Represents the input of a GetShardIterator operation.
public struct GetShardIteratorInput: Swift.Sendable {
    /// The sequence number of a stream record in the shard from which to start reading.
    public var sequenceNumber: Swift.String?
    /// The identifier of the shard. The iterator will be returned for this shard ID.
    /// This member is required.
    public var shardId: Swift.String?
    /// Determines how the shard iterator is used to start reading stream records from the shard:
    ///
    /// * AT_SEQUENCE_NUMBER - Start reading exactly from the position denoted by a specific sequence number.
    ///
    /// * AFTER_SEQUENCE_NUMBER - Start reading right after the position denoted by a specific sequence number.
    ///
    /// * TRIM_HORIZON - Start reading at the last (untrimmed) stream record, which is the oldest record in the shard. In DynamoDB Streams, there is a 24 hour limit on data retention. Stream records whose age exceeds this limit are subject to removal (trimming) from the stream.
    ///
    /// * LATEST - Start reading just after the most recent stream record in the shard, so that you always read the most recent data in the shard.
    /// This member is required.
    public var shardIteratorType: DynamoDBStreamsClientTypes.ShardIteratorType?
    /// The Amazon Resource Name (ARN) for the stream.
    /// This member is required.
    public var streamArn: Swift.String?

    public init(
        sequenceNumber: Swift.String? = nil,
        shardId: Swift.String? = nil,
        shardIteratorType: DynamoDBStreamsClientTypes.ShardIteratorType? = nil,
        streamArn: Swift.String? = nil
    )
    {
        self.sequenceNumber = sequenceNumber
        self.shardId = shardId
        self.shardIteratorType = shardIteratorType
        self.streamArn = streamArn
    }
}

/// Represents the output of a GetShardIterator operation.
public struct GetShardIteratorOutput: Swift.Sendable {
    /// The position in the shard from which to start reading stream records sequentially. A shard iterator specifies this position using the sequence number of a stream record in a shard.
    public var shardIterator: Swift.String?

    public init(
        shardIterator: Swift.String? = nil
    )
    {
        self.shardIterator = shardIterator
    }
}

/// Represents the input of a ListStreams operation.
public struct ListStreamsInput: Swift.Sendable {
    /// The ARN (Amazon Resource Name) of the first item that this operation will evaluate. Use the value that was returned for LastEvaluatedStreamArn in the previous operation.
    public var exclusiveStartStreamArn: Swift.String?
    /// The maximum number of streams to return. The upper limit is 100.
    public var limit: Swift.Int?
    /// If this parameter is provided, then only the streams associated with this table name are returned.
    public var tableName: Swift.String?

    public init(
        exclusiveStartStreamArn: Swift.String? = nil,
        limit: Swift.Int? = nil,
        tableName: Swift.String? = nil
    )
    {
        self.exclusiveStartStreamArn = exclusiveStartStreamArn
        self.limit = limit
        self.tableName = tableName
    }
}

extension DynamoDBStreamsClientTypes {

    /// Represents all of the data describing a particular stream.
    public struct Stream: Swift.Sendable {
        /// The Amazon Resource Name (ARN) for the stream.
        public var streamArn: Swift.String?
        /// A timestamp, in ISO 8601 format, for this stream. Note that LatestStreamLabel is not a unique identifier for the stream, because it is possible that a stream from another table might have the same timestamp. However, the combination of the following three elements is guaranteed to be unique:
        ///
        /// * the Amazon Web Services customer ID.
        ///
        /// * the table name
        ///
        /// * the StreamLabel
        public var streamLabel: Swift.String?
        /// The DynamoDB table with which the stream is associated.
        public var tableName: Swift.String?

        public init(
            streamArn: Swift.String? = nil,
            streamLabel: Swift.String? = nil,
            tableName: Swift.String? = nil
        )
        {
            self.streamArn = streamArn
            self.streamLabel = streamLabel
            self.tableName = tableName
        }
    }
}

/// Represents the output of a ListStreams operation.
public struct ListStreamsOutput: Swift.Sendable {
    /// The stream ARN of the item where the operation stopped, inclusive of the previous result set. Use this value to start a new operation, excluding this value in the new request. If LastEvaluatedStreamArn is empty, then the "last page" of results has been processed and there is no more data to be retrieved. If LastEvaluatedStreamArn is not empty, it does not necessarily mean that there is more data in the result set. The only way to know when you have reached the end of the result set is when LastEvaluatedStreamArn is empty.
    public var lastEvaluatedStreamArn: Swift.String?
    /// A list of stream descriptors associated with the current account and endpoint.
    public var streams: [DynamoDBStreamsClientTypes.Stream]?

    public init(
        lastEvaluatedStreamArn: Swift.String? = nil,
        streams: [DynamoDBStreamsClientTypes.Stream]? = nil
    )
    {
        self.lastEvaluatedStreamArn = lastEvaluatedStreamArn
        self.streams = streams
    }
}

extension DynamoDBStreamsClientTypes {

    /// Represents the data for an attribute. Each attribute value is described as a name-value pair. The name is the data type, and the value is the data itself. For more information, see [Data Types](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/HowItWorks.NamingRulesDataTypes.html#HowItWorks.DataTypes) in the Amazon DynamoDB Developer Guide.
    public indirect enum AttributeValue: Swift.Sendable {
        /// An attribute of type String. For example: "S": "Hello"
        case s(Swift.String)
        /// An attribute of type Number. For example: "N": "123.45" Numbers are sent across the network to DynamoDB as strings, to maximize compatibility across languages and libraries. However, DynamoDB treats them as number type attributes for mathematical operations.
        case n(Swift.String)
        /// An attribute of type Binary. For example: "B": "dGhpcyB0ZXh0IGlzIGJhc2U2NC1lbmNvZGVk"
        case b(Foundation.Data)
        /// An attribute of type String Set. For example: "SS": ["Giraffe", "Hippo" ,"Zebra"]
        case ss([Swift.String])
        /// An attribute of type Number Set. For example: "NS": ["42.2", "-19", "7.5", "3.14"] Numbers are sent across the network to DynamoDB as strings, to maximize compatibility across languages and libraries. However, DynamoDB treats them as number type attributes for mathematical operations.
        case ns([Swift.String])
        /// An attribute of type Binary Set. For example: "BS": ["U3Vubnk=", "UmFpbnk=", "U25vd3k="]
        case bs([Foundation.Data])
        /// An attribute of type Map. For example: "M": {"Name": {"S": "Joe"}, "Age": {"N": "35"}}
        case m([Swift.String: DynamoDBStreamsClientTypes.AttributeValue])
        /// An attribute of type List. For example: "L": [ {"S": "Cookies"} , {"S": "Coffee"}, {"N": "3.14159"}]
        case l([DynamoDBStreamsClientTypes.AttributeValue])
        /// An attribute of type Null. For example: "NULL": true
        case null(Swift.Bool)
        /// An attribute of type Boolean. For example: "BOOL": true
        case bool(Swift.Bool)
        case sdkUnknown(Swift.String)
    }
}

extension DynamoDBStreamsClientTypes {

    /// A description of a single data modification that was performed on an item in a DynamoDB table.
    public struct StreamRecord: Swift.Sendable {
        /// The approximate date and time when the stream record was created, in [UNIX epoch time](http://www.epochconverter.com/) format and rounded down to the closest second.
        public var approximateCreationDateTime: Foundation.Date?
        /// The primary key attribute(s) for the DynamoDB item that was modified.
        public var keys: [Swift.String: DynamoDBStreamsClientTypes.AttributeValue]?
        /// The item in the DynamoDB table as it appeared after it was modified.
        public var newImage: [Swift.String: DynamoDBStreamsClientTypes.AttributeValue]?
        /// The item in the DynamoDB table as it appeared before it was modified.
        public var oldImage: [Swift.String: DynamoDBStreamsClientTypes.AttributeValue]?
        /// The sequence number of the stream record.
        public var sequenceNumber: Swift.String?
        /// The size of the stream record, in bytes.
        public var sizeBytes: Swift.Int?
        /// The type of data from the modified DynamoDB item that was captured in this stream record:
        ///
        /// * KEYS_ONLY - only the key attributes of the modified item.
        ///
        /// * NEW_IMAGE - the entire item, as it appeared after it was modified.
        ///
        /// * OLD_IMAGE - the entire item, as it appeared before it was modified.
        ///
        /// * NEW_AND_OLD_IMAGES - both the new and the old item images of the item.
        public var streamViewType: DynamoDBStreamsClientTypes.StreamViewType?

        public init(
            approximateCreationDateTime: Foundation.Date? = nil,
            keys: [Swift.String: DynamoDBStreamsClientTypes.AttributeValue]? = nil,
            newImage: [Swift.String: DynamoDBStreamsClientTypes.AttributeValue]? = nil,
            oldImage: [Swift.String: DynamoDBStreamsClientTypes.AttributeValue]? = nil,
            sequenceNumber: Swift.String? = nil,
            sizeBytes: Swift.Int? = nil,
            streamViewType: DynamoDBStreamsClientTypes.StreamViewType? = nil
        )
        {
            self.approximateCreationDateTime = approximateCreationDateTime
            self.keys = keys
            self.newImage = newImage
            self.oldImage = oldImage
            self.sequenceNumber = sequenceNumber
            self.sizeBytes = sizeBytes
            self.streamViewType = streamViewType
        }
    }
}

extension DynamoDBStreamsClientTypes {

    /// A description of a unique event within a stream.
    public struct Record: Swift.Sendable {
        /// The region in which the GetRecords request was received.
        public var awsRegion: Swift.String?
        /// The main body of the stream record, containing all of the DynamoDB-specific fields.
        public var dynamodb: DynamoDBStreamsClientTypes.StreamRecord?
        /// A globally unique identifier for the event that was recorded in this stream record.
        public var eventID: Swift.String?
        /// The type of data modification that was performed on the DynamoDB table:
        ///
        /// * INSERT - a new item was added to the table.
        ///
        /// * MODIFY - one or more of an existing item's attributes were modified.
        ///
        /// * REMOVE - the item was deleted from the table
        public var eventName: DynamoDBStreamsClientTypes.OperationType?
        /// The Amazon Web Services service from which the stream record originated. For DynamoDB Streams, this is aws:dynamodb.
        public var eventSource: Swift.String?
        /// The version number of the stream record format. This number is updated whenever the structure of Record is modified. Client applications must not assume that eventVersion will remain at a particular value, as this number is subject to change at any time. In general, eventVersion will only increase as the low-level DynamoDB Streams API evolves.
        public var eventVersion: Swift.String?
        /// Items that are deleted by the Time to Live process after expiration have the following fields:
        ///
        /// * Records[].userIdentity.type "Service"
        ///
        /// * Records[].userIdentity.principalId "dynamodb.amazonaws.com"
        public var userIdentity: DynamoDBStreamsClientTypes.Identity?

        public init(
            awsRegion: Swift.String? = nil,
            dynamodb: DynamoDBStreamsClientTypes.StreamRecord? = nil,
            eventID: Swift.String? = nil,
            eventName: DynamoDBStreamsClientTypes.OperationType? = nil,
            eventSource: Swift.String? = nil,
            eventVersion: Swift.String? = nil,
            userIdentity: DynamoDBStreamsClientTypes.Identity? = nil
        )
        {
            self.awsRegion = awsRegion
            self.dynamodb = dynamodb
            self.eventID = eventID
            self.eventName = eventName
            self.eventSource = eventSource
            self.eventVersion = eventVersion
            self.userIdentity = userIdentity
        }
    }
}

/// Represents the output of a GetRecords operation.
public struct GetRecordsOutput: Swift.Sendable {
    /// The next position in the shard from which to start sequentially reading stream records. If set to null, the shard has been closed and the requested iterator will not return any more data.
    public var nextShardIterator: Swift.String?
    /// The stream records from the shard, which were retrieved using the shard iterator.
    public var records: [DynamoDBStreamsClientTypes.Record]?

    public init(
        nextShardIterator: Swift.String? = nil,
        records: [DynamoDBStreamsClientTypes.Record]? = nil
    )
    {
        self.nextShardIterator = nextShardIterator
        self.records = records
    }
}

extension DescribeStreamInput {

    static func urlPathProvider(_ value: DescribeStreamInput) -> Swift.String? {
        return "/"
    }
}

extension GetRecordsInput {

    static func urlPathProvider(_ value: GetRecordsInput) -> Swift.String? {
        return "/"
    }
}

extension GetShardIteratorInput {

    static func urlPathProvider(_ value: GetShardIteratorInput) -> Swift.String? {
        return "/"
    }
}

extension ListStreamsInput {

    static func urlPathProvider(_ value: ListStreamsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeStreamInput {

    static func write(value: DescribeStreamInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ExclusiveStartShardId"].write(value.exclusiveStartShardId)
        try writer["Limit"].write(value.limit)
        try writer["StreamArn"].write(value.streamArn)
    }
}

extension GetRecordsInput {

    static func write(value: GetRecordsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Limit"].write(value.limit)
        try writer["ShardIterator"].write(value.shardIterator)
    }
}

extension GetShardIteratorInput {

    static func write(value: GetShardIteratorInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["SequenceNumber"].write(value.sequenceNumber)
        try writer["ShardId"].write(value.shardId)
        try writer["ShardIteratorType"].write(value.shardIteratorType)
        try writer["StreamArn"].write(value.streamArn)
    }
}

extension ListStreamsInput {

    static func write(value: ListStreamsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ExclusiveStartStreamArn"].write(value.exclusiveStartStreamArn)
        try writer["Limit"].write(value.limit)
        try writer["TableName"].write(value.tableName)
    }
}

extension DescribeStreamOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeStreamOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeStreamOutput()
        value.streamDescription = try reader["StreamDescription"].readIfPresent(with: DynamoDBStreamsClientTypes.StreamDescription.read(from:))
        return value
    }
}

extension GetRecordsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetRecordsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetRecordsOutput()
        value.nextShardIterator = try reader["NextShardIterator"].readIfPresent()
        value.records = try reader["Records"].readListIfPresent(memberReadingClosure: DynamoDBStreamsClientTypes.Record.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension GetShardIteratorOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetShardIteratorOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetShardIteratorOutput()
        value.shardIterator = try reader["ShardIterator"].readIfPresent()
        return value
    }
}

extension ListStreamsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListStreamsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListStreamsOutput()
        value.lastEvaluatedStreamArn = try reader["LastEvaluatedStreamArn"].readIfPresent()
        value.streams = try reader["Streams"].readListIfPresent(memberReadingClosure: DynamoDBStreamsClientTypes.Stream.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

enum DescribeStreamOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetRecordsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ExpiredIteratorException": return try ExpiredIteratorException.makeError(baseError: baseError)
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TrimmedDataAccessException": return try TrimmedDataAccessException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetShardIteratorOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TrimmedDataAccessException": return try TrimmedDataAccessException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListStreamsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerError": return try InternalServerError.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerError {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InternalServerError {
        let reader = baseError.errorBodyReader
        var value = InternalServerError()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LimitExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LimitExceededException {
        let reader = baseError.errorBodyReader
        var value = LimitExceededException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TrimmedDataAccessException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> TrimmedDataAccessException {
        let reader = baseError.errorBodyReader
        var value = TrimmedDataAccessException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ExpiredIteratorException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ExpiredIteratorException {
        let reader = baseError.errorBodyReader
        var value = ExpiredIteratorException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DynamoDBStreamsClientTypes.StreamDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> DynamoDBStreamsClientTypes.StreamDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DynamoDBStreamsClientTypes.StreamDescription()
        value.streamArn = try reader["StreamArn"].readIfPresent()
        value.streamLabel = try reader["StreamLabel"].readIfPresent()
        value.streamStatus = try reader["StreamStatus"].readIfPresent()
        value.streamViewType = try reader["StreamViewType"].readIfPresent()
        value.creationRequestDateTime = try reader["CreationRequestDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.tableName = try reader["TableName"].readIfPresent()
        value.keySchema = try reader["KeySchema"].readListIfPresent(memberReadingClosure: DynamoDBStreamsClientTypes.KeySchemaElement.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.shards = try reader["Shards"].readListIfPresent(memberReadingClosure: DynamoDBStreamsClientTypes.Shard.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.lastEvaluatedShardId = try reader["LastEvaluatedShardId"].readIfPresent()
        return value
    }
}

extension DynamoDBStreamsClientTypes.Shard {

    static func read(from reader: SmithyJSON.Reader) throws -> DynamoDBStreamsClientTypes.Shard {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DynamoDBStreamsClientTypes.Shard()
        value.shardId = try reader["ShardId"].readIfPresent()
        value.sequenceNumberRange = try reader["SequenceNumberRange"].readIfPresent(with: DynamoDBStreamsClientTypes.SequenceNumberRange.read(from:))
        value.parentShardId = try reader["ParentShardId"].readIfPresent()
        return value
    }
}

extension DynamoDBStreamsClientTypes.SequenceNumberRange {

    static func read(from reader: SmithyJSON.Reader) throws -> DynamoDBStreamsClientTypes.SequenceNumberRange {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DynamoDBStreamsClientTypes.SequenceNumberRange()
        value.startingSequenceNumber = try reader["StartingSequenceNumber"].readIfPresent()
        value.endingSequenceNumber = try reader["EndingSequenceNumber"].readIfPresent()
        return value
    }
}

extension DynamoDBStreamsClientTypes.KeySchemaElement {

    static func read(from reader: SmithyJSON.Reader) throws -> DynamoDBStreamsClientTypes.KeySchemaElement {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DynamoDBStreamsClientTypes.KeySchemaElement()
        value.attributeName = try reader["AttributeName"].readIfPresent() ?? ""
        value.keyType = try reader["KeyType"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension DynamoDBStreamsClientTypes.Record {

    static func read(from reader: SmithyJSON.Reader) throws -> DynamoDBStreamsClientTypes.Record {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DynamoDBStreamsClientTypes.Record()
        value.eventID = try reader["eventID"].readIfPresent()
        value.eventName = try reader["eventName"].readIfPresent()
        value.eventVersion = try reader["eventVersion"].readIfPresent()
        value.eventSource = try reader["eventSource"].readIfPresent()
        value.awsRegion = try reader["awsRegion"].readIfPresent()
        value.dynamodb = try reader["dynamodb"].readIfPresent(with: DynamoDBStreamsClientTypes.StreamRecord.read(from:))
        value.userIdentity = try reader["userIdentity"].readIfPresent(with: DynamoDBStreamsClientTypes.Identity.read(from:))
        return value
    }
}

extension DynamoDBStreamsClientTypes.Identity {

    static func read(from reader: SmithyJSON.Reader) throws -> DynamoDBStreamsClientTypes.Identity {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DynamoDBStreamsClientTypes.Identity()
        value.principalId = try reader["PrincipalId"].readIfPresent()
        value.type = try reader["Type"].readIfPresent()
        return value
    }
}

extension DynamoDBStreamsClientTypes.StreamRecord {

    static func read(from reader: SmithyJSON.Reader) throws -> DynamoDBStreamsClientTypes.StreamRecord {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DynamoDBStreamsClientTypes.StreamRecord()
        value.approximateCreationDateTime = try reader["ApproximateCreationDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.keys = try reader["Keys"].readMapIfPresent(valueReadingClosure: DynamoDBStreamsClientTypes.AttributeValue.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.newImage = try reader["NewImage"].readMapIfPresent(valueReadingClosure: DynamoDBStreamsClientTypes.AttributeValue.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.oldImage = try reader["OldImage"].readMapIfPresent(valueReadingClosure: DynamoDBStreamsClientTypes.AttributeValue.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.sequenceNumber = try reader["SequenceNumber"].readIfPresent()
        value.sizeBytes = try reader["SizeBytes"].readIfPresent()
        value.streamViewType = try reader["StreamViewType"].readIfPresent()
        return value
    }
}

extension DynamoDBStreamsClientTypes.AttributeValue {

    static func read(from reader: SmithyJSON.Reader) throws -> DynamoDBStreamsClientTypes.AttributeValue {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "S":
                return .s(try reader["S"].read())
            case "N":
                return .n(try reader["N"].read())
            case "B":
                return .b(try reader["B"].read())
            case "SS":
                return .ss(try reader["SS"].readList(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false))
            case "NS":
                return .ns(try reader["NS"].readList(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false))
            case "BS":
                return .bs(try reader["BS"].readList(memberReadingClosure: SmithyReadWrite.ReadingClosures.readData(from:), memberNodeInfo: "member", isFlattened: false))
            case "M":
                return .m(try reader["M"].readMap(valueReadingClosure: DynamoDBStreamsClientTypes.AttributeValue.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false))
            case "L":
                return .l(try reader["L"].readList(memberReadingClosure: DynamoDBStreamsClientTypes.AttributeValue.read(from:), memberNodeInfo: "member", isFlattened: false))
            case "NULL":
                return .null(try reader["NULL"].read())
            case "BOOL":
                return .bool(try reader["BOOL"].read())
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension DynamoDBStreamsClientTypes.Stream {

    static func read(from reader: SmithyJSON.Reader) throws -> DynamoDBStreamsClientTypes.Stream {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DynamoDBStreamsClientTypes.Stream()
        value.streamArn = try reader["StreamArn"].readIfPresent()
        value.tableName = try reader["TableName"].readIfPresent()
        value.streamLabel = try reader["StreamLabel"].readIfPresent()
        return value
    }
}

public enum DynamoDBStreamsClientTypes {}
