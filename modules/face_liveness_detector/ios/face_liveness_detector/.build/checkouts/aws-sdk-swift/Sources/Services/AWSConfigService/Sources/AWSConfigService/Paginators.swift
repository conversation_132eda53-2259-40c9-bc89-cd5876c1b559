//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import Foundation
import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension ConfigClient {
    /// Paginate over `[DescribeAggregateComplianceByConfigRulesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeAggregateComplianceByConfigRulesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeAggregateComplianceByConfigRulesOutput`
    public func describeAggregateComplianceByConfigRulesPaginated(input: DescribeAggregateComplianceByConfigRulesInput) -> ClientRuntime.PaginatorSequence<DescribeAggregateComplianceByConfigRulesInput, DescribeAggregateComplianceByConfigRulesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeAggregateComplianceByConfigRulesInput, DescribeAggregateComplianceByConfigRulesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeAggregateComplianceByConfigRules(input:))
    }
}

extension DescribeAggregateComplianceByConfigRulesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeAggregateComplianceByConfigRulesInput {
        return DescribeAggregateComplianceByConfigRulesInput(
            configurationAggregatorName: self.configurationAggregatorName,
            filters: self.filters,
            limit: self.limit,
            nextToken: token
        )}
}
extension ConfigClient {
    /// Paginate over `[DescribeAggregateComplianceByConformancePacksOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeAggregateComplianceByConformancePacksInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeAggregateComplianceByConformancePacksOutput`
    public func describeAggregateComplianceByConformancePacksPaginated(input: DescribeAggregateComplianceByConformancePacksInput) -> ClientRuntime.PaginatorSequence<DescribeAggregateComplianceByConformancePacksInput, DescribeAggregateComplianceByConformancePacksOutput> {
        return ClientRuntime.PaginatorSequence<DescribeAggregateComplianceByConformancePacksInput, DescribeAggregateComplianceByConformancePacksOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeAggregateComplianceByConformancePacks(input:))
    }
}

extension DescribeAggregateComplianceByConformancePacksInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeAggregateComplianceByConformancePacksInput {
        return DescribeAggregateComplianceByConformancePacksInput(
            configurationAggregatorName: self.configurationAggregatorName,
            filters: self.filters,
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeAggregateComplianceByConformancePacksInput, OperationStackOutput == DescribeAggregateComplianceByConformancePacksOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeAggregateComplianceByConformancePacksPaginated`
    /// to access the nested member `[ConfigClientTypes.AggregateComplianceByConformancePack]`
    /// - Returns: `[ConfigClientTypes.AggregateComplianceByConformancePack]`
    public func aggregateComplianceByConformancePacks() async throws -> [ConfigClientTypes.AggregateComplianceByConformancePack] {
        return try await self.asyncCompactMap { item in item.aggregateComplianceByConformancePacks }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeAggregationAuthorizationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeAggregationAuthorizationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeAggregationAuthorizationsOutput`
    public func describeAggregationAuthorizationsPaginated(input: DescribeAggregationAuthorizationsInput) -> ClientRuntime.PaginatorSequence<DescribeAggregationAuthorizationsInput, DescribeAggregationAuthorizationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeAggregationAuthorizationsInput, DescribeAggregationAuthorizationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeAggregationAuthorizations(input:))
    }
}

extension DescribeAggregationAuthorizationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeAggregationAuthorizationsInput {
        return DescribeAggregationAuthorizationsInput(
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeAggregationAuthorizationsInput, OperationStackOutput == DescribeAggregationAuthorizationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeAggregationAuthorizationsPaginated`
    /// to access the nested member `[ConfigClientTypes.AggregationAuthorization]`
    /// - Returns: `[ConfigClientTypes.AggregationAuthorization]`
    public func aggregationAuthorizations() async throws -> [ConfigClientTypes.AggregationAuthorization] {
        return try await self.asyncCompactMap { item in item.aggregationAuthorizations }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeComplianceByConfigRuleOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeComplianceByConfigRuleInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeComplianceByConfigRuleOutput`
    public func describeComplianceByConfigRulePaginated(input: DescribeComplianceByConfigRuleInput) -> ClientRuntime.PaginatorSequence<DescribeComplianceByConfigRuleInput, DescribeComplianceByConfigRuleOutput> {
        return ClientRuntime.PaginatorSequence<DescribeComplianceByConfigRuleInput, DescribeComplianceByConfigRuleOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeComplianceByConfigRule(input:))
    }
}

extension DescribeComplianceByConfigRuleInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeComplianceByConfigRuleInput {
        return DescribeComplianceByConfigRuleInput(
            complianceTypes: self.complianceTypes,
            configRuleNames: self.configRuleNames,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeComplianceByConfigRuleInput, OperationStackOutput == DescribeComplianceByConfigRuleOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeComplianceByConfigRulePaginated`
    /// to access the nested member `[ConfigClientTypes.ComplianceByConfigRule]`
    /// - Returns: `[ConfigClientTypes.ComplianceByConfigRule]`
    public func complianceByConfigRules() async throws -> [ConfigClientTypes.ComplianceByConfigRule] {
        return try await self.asyncCompactMap { item in item.complianceByConfigRules }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeComplianceByResourceOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeComplianceByResourceInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeComplianceByResourceOutput`
    public func describeComplianceByResourcePaginated(input: DescribeComplianceByResourceInput) -> ClientRuntime.PaginatorSequence<DescribeComplianceByResourceInput, DescribeComplianceByResourceOutput> {
        return ClientRuntime.PaginatorSequence<DescribeComplianceByResourceInput, DescribeComplianceByResourceOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeComplianceByResource(input:))
    }
}

extension DescribeComplianceByResourceInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeComplianceByResourceInput {
        return DescribeComplianceByResourceInput(
            complianceTypes: self.complianceTypes,
            limit: self.limit,
            nextToken: token,
            resourceId: self.resourceId,
            resourceType: self.resourceType
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeComplianceByResourceInput, OperationStackOutput == DescribeComplianceByResourceOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeComplianceByResourcePaginated`
    /// to access the nested member `[ConfigClientTypes.ComplianceByResource]`
    /// - Returns: `[ConfigClientTypes.ComplianceByResource]`
    public func complianceByResources() async throws -> [ConfigClientTypes.ComplianceByResource] {
        return try await self.asyncCompactMap { item in item.complianceByResources }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeConfigRuleEvaluationStatusOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeConfigRuleEvaluationStatusInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeConfigRuleEvaluationStatusOutput`
    public func describeConfigRuleEvaluationStatusPaginated(input: DescribeConfigRuleEvaluationStatusInput) -> ClientRuntime.PaginatorSequence<DescribeConfigRuleEvaluationStatusInput, DescribeConfigRuleEvaluationStatusOutput> {
        return ClientRuntime.PaginatorSequence<DescribeConfigRuleEvaluationStatusInput, DescribeConfigRuleEvaluationStatusOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeConfigRuleEvaluationStatus(input:))
    }
}

extension DescribeConfigRuleEvaluationStatusInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeConfigRuleEvaluationStatusInput {
        return DescribeConfigRuleEvaluationStatusInput(
            configRuleNames: self.configRuleNames,
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeConfigRuleEvaluationStatusInput, OperationStackOutput == DescribeConfigRuleEvaluationStatusOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeConfigRuleEvaluationStatusPaginated`
    /// to access the nested member `[ConfigClientTypes.ConfigRuleEvaluationStatus]`
    /// - Returns: `[ConfigClientTypes.ConfigRuleEvaluationStatus]`
    public func configRulesEvaluationStatus() async throws -> [ConfigClientTypes.ConfigRuleEvaluationStatus] {
        return try await self.asyncCompactMap { item in item.configRulesEvaluationStatus }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeConfigRulesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeConfigRulesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeConfigRulesOutput`
    public func describeConfigRulesPaginated(input: DescribeConfigRulesInput) -> ClientRuntime.PaginatorSequence<DescribeConfigRulesInput, DescribeConfigRulesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeConfigRulesInput, DescribeConfigRulesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeConfigRules(input:))
    }
}

extension DescribeConfigRulesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeConfigRulesInput {
        return DescribeConfigRulesInput(
            configRuleNames: self.configRuleNames,
            filters: self.filters,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeConfigRulesInput, OperationStackOutput == DescribeConfigRulesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeConfigRulesPaginated`
    /// to access the nested member `[ConfigClientTypes.ConfigRule]`
    /// - Returns: `[ConfigClientTypes.ConfigRule]`
    public func configRules() async throws -> [ConfigClientTypes.ConfigRule] {
        return try await self.asyncCompactMap { item in item.configRules }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeConfigurationAggregatorsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeConfigurationAggregatorsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeConfigurationAggregatorsOutput`
    public func describeConfigurationAggregatorsPaginated(input: DescribeConfigurationAggregatorsInput) -> ClientRuntime.PaginatorSequence<DescribeConfigurationAggregatorsInput, DescribeConfigurationAggregatorsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeConfigurationAggregatorsInput, DescribeConfigurationAggregatorsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeConfigurationAggregators(input:))
    }
}

extension DescribeConfigurationAggregatorsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeConfigurationAggregatorsInput {
        return DescribeConfigurationAggregatorsInput(
            configurationAggregatorNames: self.configurationAggregatorNames,
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeConfigurationAggregatorsInput, OperationStackOutput == DescribeConfigurationAggregatorsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeConfigurationAggregatorsPaginated`
    /// to access the nested member `[ConfigClientTypes.ConfigurationAggregator]`
    /// - Returns: `[ConfigClientTypes.ConfigurationAggregator]`
    public func configurationAggregators() async throws -> [ConfigClientTypes.ConfigurationAggregator] {
        return try await self.asyncCompactMap { item in item.configurationAggregators }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeConfigurationAggregatorSourcesStatusOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeConfigurationAggregatorSourcesStatusInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeConfigurationAggregatorSourcesStatusOutput`
    public func describeConfigurationAggregatorSourcesStatusPaginated(input: DescribeConfigurationAggregatorSourcesStatusInput) -> ClientRuntime.PaginatorSequence<DescribeConfigurationAggregatorSourcesStatusInput, DescribeConfigurationAggregatorSourcesStatusOutput> {
        return ClientRuntime.PaginatorSequence<DescribeConfigurationAggregatorSourcesStatusInput, DescribeConfigurationAggregatorSourcesStatusOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeConfigurationAggregatorSourcesStatus(input:))
    }
}

extension DescribeConfigurationAggregatorSourcesStatusInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeConfigurationAggregatorSourcesStatusInput {
        return DescribeConfigurationAggregatorSourcesStatusInput(
            configurationAggregatorName: self.configurationAggregatorName,
            limit: self.limit,
            nextToken: token,
            updateStatus: self.updateStatus
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeConfigurationAggregatorSourcesStatusInput, OperationStackOutput == DescribeConfigurationAggregatorSourcesStatusOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeConfigurationAggregatorSourcesStatusPaginated`
    /// to access the nested member `[ConfigClientTypes.AggregatedSourceStatus]`
    /// - Returns: `[ConfigClientTypes.AggregatedSourceStatus]`
    public func aggregatedSourceStatusList() async throws -> [ConfigClientTypes.AggregatedSourceStatus] {
        return try await self.asyncCompactMap { item in item.aggregatedSourceStatusList }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeConformancePackComplianceOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeConformancePackComplianceInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeConformancePackComplianceOutput`
    public func describeConformancePackCompliancePaginated(input: DescribeConformancePackComplianceInput) -> ClientRuntime.PaginatorSequence<DescribeConformancePackComplianceInput, DescribeConformancePackComplianceOutput> {
        return ClientRuntime.PaginatorSequence<DescribeConformancePackComplianceInput, DescribeConformancePackComplianceOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeConformancePackCompliance(input:))
    }
}

extension DescribeConformancePackComplianceInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeConformancePackComplianceInput {
        return DescribeConformancePackComplianceInput(
            conformancePackName: self.conformancePackName,
            filters: self.filters,
            limit: self.limit,
            nextToken: token
        )}
}
extension ConfigClient {
    /// Paginate over `[DescribeConformancePacksOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeConformancePacksInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeConformancePacksOutput`
    public func describeConformancePacksPaginated(input: DescribeConformancePacksInput) -> ClientRuntime.PaginatorSequence<DescribeConformancePacksInput, DescribeConformancePacksOutput> {
        return ClientRuntime.PaginatorSequence<DescribeConformancePacksInput, DescribeConformancePacksOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeConformancePacks(input:))
    }
}

extension DescribeConformancePacksInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeConformancePacksInput {
        return DescribeConformancePacksInput(
            conformancePackNames: self.conformancePackNames,
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeConformancePacksInput, OperationStackOutput == DescribeConformancePacksOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeConformancePacksPaginated`
    /// to access the nested member `[ConfigClientTypes.ConformancePackDetail]`
    /// - Returns: `[ConfigClientTypes.ConformancePackDetail]`
    public func conformancePackDetails() async throws -> [ConfigClientTypes.ConformancePackDetail] {
        return try await self.asyncCompactMap { item in item.conformancePackDetails }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeConformancePackStatusOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeConformancePackStatusInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeConformancePackStatusOutput`
    public func describeConformancePackStatusPaginated(input: DescribeConformancePackStatusInput) -> ClientRuntime.PaginatorSequence<DescribeConformancePackStatusInput, DescribeConformancePackStatusOutput> {
        return ClientRuntime.PaginatorSequence<DescribeConformancePackStatusInput, DescribeConformancePackStatusOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeConformancePackStatus(input:))
    }
}

extension DescribeConformancePackStatusInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeConformancePackStatusInput {
        return DescribeConformancePackStatusInput(
            conformancePackNames: self.conformancePackNames,
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeConformancePackStatusInput, OperationStackOutput == DescribeConformancePackStatusOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeConformancePackStatusPaginated`
    /// to access the nested member `[ConfigClientTypes.ConformancePackStatusDetail]`
    /// - Returns: `[ConfigClientTypes.ConformancePackStatusDetail]`
    public func conformancePackStatusDetails() async throws -> [ConfigClientTypes.ConformancePackStatusDetail] {
        return try await self.asyncCompactMap { item in item.conformancePackStatusDetails }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeOrganizationConfigRulesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeOrganizationConfigRulesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeOrganizationConfigRulesOutput`
    public func describeOrganizationConfigRulesPaginated(input: DescribeOrganizationConfigRulesInput) -> ClientRuntime.PaginatorSequence<DescribeOrganizationConfigRulesInput, DescribeOrganizationConfigRulesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeOrganizationConfigRulesInput, DescribeOrganizationConfigRulesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeOrganizationConfigRules(input:))
    }
}

extension DescribeOrganizationConfigRulesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeOrganizationConfigRulesInput {
        return DescribeOrganizationConfigRulesInput(
            limit: self.limit,
            nextToken: token,
            organizationConfigRuleNames: self.organizationConfigRuleNames
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeOrganizationConfigRulesInput, OperationStackOutput == DescribeOrganizationConfigRulesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeOrganizationConfigRulesPaginated`
    /// to access the nested member `[ConfigClientTypes.OrganizationConfigRule]`
    /// - Returns: `[ConfigClientTypes.OrganizationConfigRule]`
    public func organizationConfigRules() async throws -> [ConfigClientTypes.OrganizationConfigRule] {
        return try await self.asyncCompactMap { item in item.organizationConfigRules }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeOrganizationConfigRuleStatusesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeOrganizationConfigRuleStatusesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeOrganizationConfigRuleStatusesOutput`
    public func describeOrganizationConfigRuleStatusesPaginated(input: DescribeOrganizationConfigRuleStatusesInput) -> ClientRuntime.PaginatorSequence<DescribeOrganizationConfigRuleStatusesInput, DescribeOrganizationConfigRuleStatusesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeOrganizationConfigRuleStatusesInput, DescribeOrganizationConfigRuleStatusesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeOrganizationConfigRuleStatuses(input:))
    }
}

extension DescribeOrganizationConfigRuleStatusesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeOrganizationConfigRuleStatusesInput {
        return DescribeOrganizationConfigRuleStatusesInput(
            limit: self.limit,
            nextToken: token,
            organizationConfigRuleNames: self.organizationConfigRuleNames
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeOrganizationConfigRuleStatusesInput, OperationStackOutput == DescribeOrganizationConfigRuleStatusesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeOrganizationConfigRuleStatusesPaginated`
    /// to access the nested member `[ConfigClientTypes.OrganizationConfigRuleStatus]`
    /// - Returns: `[ConfigClientTypes.OrganizationConfigRuleStatus]`
    public func organizationConfigRuleStatuses() async throws -> [ConfigClientTypes.OrganizationConfigRuleStatus] {
        return try await self.asyncCompactMap { item in item.organizationConfigRuleStatuses }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeOrganizationConformancePacksOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeOrganizationConformancePacksInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeOrganizationConformancePacksOutput`
    public func describeOrganizationConformancePacksPaginated(input: DescribeOrganizationConformancePacksInput) -> ClientRuntime.PaginatorSequence<DescribeOrganizationConformancePacksInput, DescribeOrganizationConformancePacksOutput> {
        return ClientRuntime.PaginatorSequence<DescribeOrganizationConformancePacksInput, DescribeOrganizationConformancePacksOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeOrganizationConformancePacks(input:))
    }
}

extension DescribeOrganizationConformancePacksInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeOrganizationConformancePacksInput {
        return DescribeOrganizationConformancePacksInput(
            limit: self.limit,
            nextToken: token,
            organizationConformancePackNames: self.organizationConformancePackNames
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeOrganizationConformancePacksInput, OperationStackOutput == DescribeOrganizationConformancePacksOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeOrganizationConformancePacksPaginated`
    /// to access the nested member `[ConfigClientTypes.OrganizationConformancePack]`
    /// - Returns: `[ConfigClientTypes.OrganizationConformancePack]`
    public func organizationConformancePacks() async throws -> [ConfigClientTypes.OrganizationConformancePack] {
        return try await self.asyncCompactMap { item in item.organizationConformancePacks }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeOrganizationConformancePackStatusesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeOrganizationConformancePackStatusesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeOrganizationConformancePackStatusesOutput`
    public func describeOrganizationConformancePackStatusesPaginated(input: DescribeOrganizationConformancePackStatusesInput) -> ClientRuntime.PaginatorSequence<DescribeOrganizationConformancePackStatusesInput, DescribeOrganizationConformancePackStatusesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeOrganizationConformancePackStatusesInput, DescribeOrganizationConformancePackStatusesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeOrganizationConformancePackStatuses(input:))
    }
}

extension DescribeOrganizationConformancePackStatusesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeOrganizationConformancePackStatusesInput {
        return DescribeOrganizationConformancePackStatusesInput(
            limit: self.limit,
            nextToken: token,
            organizationConformancePackNames: self.organizationConformancePackNames
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeOrganizationConformancePackStatusesInput, OperationStackOutput == DescribeOrganizationConformancePackStatusesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeOrganizationConformancePackStatusesPaginated`
    /// to access the nested member `[ConfigClientTypes.OrganizationConformancePackStatus]`
    /// - Returns: `[ConfigClientTypes.OrganizationConformancePackStatus]`
    public func organizationConformancePackStatuses() async throws -> [ConfigClientTypes.OrganizationConformancePackStatus] {
        return try await self.asyncCompactMap { item in item.organizationConformancePackStatuses }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribePendingAggregationRequestsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribePendingAggregationRequestsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribePendingAggregationRequestsOutput`
    public func describePendingAggregationRequestsPaginated(input: DescribePendingAggregationRequestsInput) -> ClientRuntime.PaginatorSequence<DescribePendingAggregationRequestsInput, DescribePendingAggregationRequestsOutput> {
        return ClientRuntime.PaginatorSequence<DescribePendingAggregationRequestsInput, DescribePendingAggregationRequestsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describePendingAggregationRequests(input:))
    }
}

extension DescribePendingAggregationRequestsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribePendingAggregationRequestsInput {
        return DescribePendingAggregationRequestsInput(
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribePendingAggregationRequestsInput, OperationStackOutput == DescribePendingAggregationRequestsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describePendingAggregationRequestsPaginated`
    /// to access the nested member `[ConfigClientTypes.PendingAggregationRequest]`
    /// - Returns: `[ConfigClientTypes.PendingAggregationRequest]`
    public func pendingAggregationRequests() async throws -> [ConfigClientTypes.PendingAggregationRequest] {
        return try await self.asyncCompactMap { item in item.pendingAggregationRequests }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeRemediationExceptionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeRemediationExceptionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeRemediationExceptionsOutput`
    public func describeRemediationExceptionsPaginated(input: DescribeRemediationExceptionsInput) -> ClientRuntime.PaginatorSequence<DescribeRemediationExceptionsInput, DescribeRemediationExceptionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeRemediationExceptionsInput, DescribeRemediationExceptionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeRemediationExceptions(input:))
    }
}

extension DescribeRemediationExceptionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeRemediationExceptionsInput {
        return DescribeRemediationExceptionsInput(
            configRuleName: self.configRuleName,
            limit: self.limit,
            nextToken: token,
            resourceKeys: self.resourceKeys
        )}
}
extension ConfigClient {
    /// Paginate over `[DescribeRemediationExecutionStatusOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeRemediationExecutionStatusInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeRemediationExecutionStatusOutput`
    public func describeRemediationExecutionStatusPaginated(input: DescribeRemediationExecutionStatusInput) -> ClientRuntime.PaginatorSequence<DescribeRemediationExecutionStatusInput, DescribeRemediationExecutionStatusOutput> {
        return ClientRuntime.PaginatorSequence<DescribeRemediationExecutionStatusInput, DescribeRemediationExecutionStatusOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeRemediationExecutionStatus(input:))
    }
}

extension DescribeRemediationExecutionStatusInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeRemediationExecutionStatusInput {
        return DescribeRemediationExecutionStatusInput(
            configRuleName: self.configRuleName,
            limit: self.limit,
            nextToken: token,
            resourceKeys: self.resourceKeys
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeRemediationExecutionStatusInput, OperationStackOutput == DescribeRemediationExecutionStatusOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeRemediationExecutionStatusPaginated`
    /// to access the nested member `[ConfigClientTypes.RemediationExecutionStatus]`
    /// - Returns: `[ConfigClientTypes.RemediationExecutionStatus]`
    public func remediationExecutionStatuses() async throws -> [ConfigClientTypes.RemediationExecutionStatus] {
        return try await self.asyncCompactMap { item in item.remediationExecutionStatuses }
    }
}
extension ConfigClient {
    /// Paginate over `[DescribeRetentionConfigurationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeRetentionConfigurationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeRetentionConfigurationsOutput`
    public func describeRetentionConfigurationsPaginated(input: DescribeRetentionConfigurationsInput) -> ClientRuntime.PaginatorSequence<DescribeRetentionConfigurationsInput, DescribeRetentionConfigurationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeRetentionConfigurationsInput, DescribeRetentionConfigurationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeRetentionConfigurations(input:))
    }
}

extension DescribeRetentionConfigurationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeRetentionConfigurationsInput {
        return DescribeRetentionConfigurationsInput(
            nextToken: token,
            retentionConfigurationNames: self.retentionConfigurationNames
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeRetentionConfigurationsInput, OperationStackOutput == DescribeRetentionConfigurationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeRetentionConfigurationsPaginated`
    /// to access the nested member `[ConfigClientTypes.RetentionConfiguration]`
    /// - Returns: `[ConfigClientTypes.RetentionConfiguration]`
    public func retentionConfigurations() async throws -> [ConfigClientTypes.RetentionConfiguration] {
        return try await self.asyncCompactMap { item in item.retentionConfigurations }
    }
}
extension ConfigClient {
    /// Paginate over `[GetAggregateComplianceDetailsByConfigRuleOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetAggregateComplianceDetailsByConfigRuleInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetAggregateComplianceDetailsByConfigRuleOutput`
    public func getAggregateComplianceDetailsByConfigRulePaginated(input: GetAggregateComplianceDetailsByConfigRuleInput) -> ClientRuntime.PaginatorSequence<GetAggregateComplianceDetailsByConfigRuleInput, GetAggregateComplianceDetailsByConfigRuleOutput> {
        return ClientRuntime.PaginatorSequence<GetAggregateComplianceDetailsByConfigRuleInput, GetAggregateComplianceDetailsByConfigRuleOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getAggregateComplianceDetailsByConfigRule(input:))
    }
}

extension GetAggregateComplianceDetailsByConfigRuleInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetAggregateComplianceDetailsByConfigRuleInput {
        return GetAggregateComplianceDetailsByConfigRuleInput(
            accountId: self.accountId,
            awsRegion: self.awsRegion,
            complianceType: self.complianceType,
            configRuleName: self.configRuleName,
            configurationAggregatorName: self.configurationAggregatorName,
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == GetAggregateComplianceDetailsByConfigRuleInput, OperationStackOutput == GetAggregateComplianceDetailsByConfigRuleOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getAggregateComplianceDetailsByConfigRulePaginated`
    /// to access the nested member `[ConfigClientTypes.AggregateEvaluationResult]`
    /// - Returns: `[ConfigClientTypes.AggregateEvaluationResult]`
    public func aggregateEvaluationResults() async throws -> [ConfigClientTypes.AggregateEvaluationResult] {
        return try await self.asyncCompactMap { item in item.aggregateEvaluationResults }
    }
}
extension ConfigClient {
    /// Paginate over `[GetAggregateConfigRuleComplianceSummaryOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetAggregateConfigRuleComplianceSummaryInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetAggregateConfigRuleComplianceSummaryOutput`
    public func getAggregateConfigRuleComplianceSummaryPaginated(input: GetAggregateConfigRuleComplianceSummaryInput) -> ClientRuntime.PaginatorSequence<GetAggregateConfigRuleComplianceSummaryInput, GetAggregateConfigRuleComplianceSummaryOutput> {
        return ClientRuntime.PaginatorSequence<GetAggregateConfigRuleComplianceSummaryInput, GetAggregateConfigRuleComplianceSummaryOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getAggregateConfigRuleComplianceSummary(input:))
    }
}

extension GetAggregateConfigRuleComplianceSummaryInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetAggregateConfigRuleComplianceSummaryInput {
        return GetAggregateConfigRuleComplianceSummaryInput(
            configurationAggregatorName: self.configurationAggregatorName,
            filters: self.filters,
            groupByKey: self.groupByKey,
            limit: self.limit,
            nextToken: token
        )}
}
extension ConfigClient {
    /// Paginate over `[GetAggregateConformancePackComplianceSummaryOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetAggregateConformancePackComplianceSummaryInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetAggregateConformancePackComplianceSummaryOutput`
    public func getAggregateConformancePackComplianceSummaryPaginated(input: GetAggregateConformancePackComplianceSummaryInput) -> ClientRuntime.PaginatorSequence<GetAggregateConformancePackComplianceSummaryInput, GetAggregateConformancePackComplianceSummaryOutput> {
        return ClientRuntime.PaginatorSequence<GetAggregateConformancePackComplianceSummaryInput, GetAggregateConformancePackComplianceSummaryOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getAggregateConformancePackComplianceSummary(input:))
    }
}

extension GetAggregateConformancePackComplianceSummaryInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetAggregateConformancePackComplianceSummaryInput {
        return GetAggregateConformancePackComplianceSummaryInput(
            configurationAggregatorName: self.configurationAggregatorName,
            filters: self.filters,
            groupByKey: self.groupByKey,
            limit: self.limit,
            nextToken: token
        )}
}
extension ConfigClient {
    /// Paginate over `[GetAggregateDiscoveredResourceCountsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetAggregateDiscoveredResourceCountsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetAggregateDiscoveredResourceCountsOutput`
    public func getAggregateDiscoveredResourceCountsPaginated(input: GetAggregateDiscoveredResourceCountsInput) -> ClientRuntime.PaginatorSequence<GetAggregateDiscoveredResourceCountsInput, GetAggregateDiscoveredResourceCountsOutput> {
        return ClientRuntime.PaginatorSequence<GetAggregateDiscoveredResourceCountsInput, GetAggregateDiscoveredResourceCountsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getAggregateDiscoveredResourceCounts(input:))
    }
}

extension GetAggregateDiscoveredResourceCountsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetAggregateDiscoveredResourceCountsInput {
        return GetAggregateDiscoveredResourceCountsInput(
            configurationAggregatorName: self.configurationAggregatorName,
            filters: self.filters,
            groupByKey: self.groupByKey,
            limit: self.limit,
            nextToken: token
        )}
}
extension ConfigClient {
    /// Paginate over `[GetComplianceDetailsByConfigRuleOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetComplianceDetailsByConfigRuleInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetComplianceDetailsByConfigRuleOutput`
    public func getComplianceDetailsByConfigRulePaginated(input: GetComplianceDetailsByConfigRuleInput) -> ClientRuntime.PaginatorSequence<GetComplianceDetailsByConfigRuleInput, GetComplianceDetailsByConfigRuleOutput> {
        return ClientRuntime.PaginatorSequence<GetComplianceDetailsByConfigRuleInput, GetComplianceDetailsByConfigRuleOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getComplianceDetailsByConfigRule(input:))
    }
}

extension GetComplianceDetailsByConfigRuleInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetComplianceDetailsByConfigRuleInput {
        return GetComplianceDetailsByConfigRuleInput(
            complianceTypes: self.complianceTypes,
            configRuleName: self.configRuleName,
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == GetComplianceDetailsByConfigRuleInput, OperationStackOutput == GetComplianceDetailsByConfigRuleOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getComplianceDetailsByConfigRulePaginated`
    /// to access the nested member `[ConfigClientTypes.EvaluationResult]`
    /// - Returns: `[ConfigClientTypes.EvaluationResult]`
    public func evaluationResults() async throws -> [ConfigClientTypes.EvaluationResult] {
        return try await self.asyncCompactMap { item in item.evaluationResults }
    }
}
extension ConfigClient {
    /// Paginate over `[GetComplianceDetailsByResourceOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetComplianceDetailsByResourceInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetComplianceDetailsByResourceOutput`
    public func getComplianceDetailsByResourcePaginated(input: GetComplianceDetailsByResourceInput) -> ClientRuntime.PaginatorSequence<GetComplianceDetailsByResourceInput, GetComplianceDetailsByResourceOutput> {
        return ClientRuntime.PaginatorSequence<GetComplianceDetailsByResourceInput, GetComplianceDetailsByResourceOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getComplianceDetailsByResource(input:))
    }
}

extension GetComplianceDetailsByResourceInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetComplianceDetailsByResourceInput {
        return GetComplianceDetailsByResourceInput(
            complianceTypes: self.complianceTypes,
            nextToken: token,
            resourceEvaluationId: self.resourceEvaluationId,
            resourceId: self.resourceId,
            resourceType: self.resourceType
        )}
}

extension PaginatorSequence where OperationStackInput == GetComplianceDetailsByResourceInput, OperationStackOutput == GetComplianceDetailsByResourceOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getComplianceDetailsByResourcePaginated`
    /// to access the nested member `[ConfigClientTypes.EvaluationResult]`
    /// - Returns: `[ConfigClientTypes.EvaluationResult]`
    public func evaluationResults() async throws -> [ConfigClientTypes.EvaluationResult] {
        return try await self.asyncCompactMap { item in item.evaluationResults }
    }
}
extension ConfigClient {
    /// Paginate over `[GetConformancePackComplianceDetailsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetConformancePackComplianceDetailsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetConformancePackComplianceDetailsOutput`
    public func getConformancePackComplianceDetailsPaginated(input: GetConformancePackComplianceDetailsInput) -> ClientRuntime.PaginatorSequence<GetConformancePackComplianceDetailsInput, GetConformancePackComplianceDetailsOutput> {
        return ClientRuntime.PaginatorSequence<GetConformancePackComplianceDetailsInput, GetConformancePackComplianceDetailsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getConformancePackComplianceDetails(input:))
    }
}

extension GetConformancePackComplianceDetailsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetConformancePackComplianceDetailsInput {
        return GetConformancePackComplianceDetailsInput(
            conformancePackName: self.conformancePackName,
            filters: self.filters,
            limit: self.limit,
            nextToken: token
        )}
}
extension ConfigClient {
    /// Paginate over `[GetConformancePackComplianceSummaryOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetConformancePackComplianceSummaryInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetConformancePackComplianceSummaryOutput`
    public func getConformancePackComplianceSummaryPaginated(input: GetConformancePackComplianceSummaryInput) -> ClientRuntime.PaginatorSequence<GetConformancePackComplianceSummaryInput, GetConformancePackComplianceSummaryOutput> {
        return ClientRuntime.PaginatorSequence<GetConformancePackComplianceSummaryInput, GetConformancePackComplianceSummaryOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getConformancePackComplianceSummary(input:))
    }
}

extension GetConformancePackComplianceSummaryInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetConformancePackComplianceSummaryInput {
        return GetConformancePackComplianceSummaryInput(
            conformancePackNames: self.conformancePackNames,
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == GetConformancePackComplianceSummaryInput, OperationStackOutput == GetConformancePackComplianceSummaryOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getConformancePackComplianceSummaryPaginated`
    /// to access the nested member `[ConfigClientTypes.ConformancePackComplianceSummary]`
    /// - Returns: `[ConfigClientTypes.ConformancePackComplianceSummary]`
    public func conformancePackComplianceSummaryList() async throws -> [ConfigClientTypes.ConformancePackComplianceSummary] {
        return try await self.asyncCompactMap { item in item.conformancePackComplianceSummaryList }
    }
}
extension ConfigClient {
    /// Paginate over `[GetDiscoveredResourceCountsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetDiscoveredResourceCountsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetDiscoveredResourceCountsOutput`
    public func getDiscoveredResourceCountsPaginated(input: GetDiscoveredResourceCountsInput) -> ClientRuntime.PaginatorSequence<GetDiscoveredResourceCountsInput, GetDiscoveredResourceCountsOutput> {
        return ClientRuntime.PaginatorSequence<GetDiscoveredResourceCountsInput, GetDiscoveredResourceCountsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getDiscoveredResourceCounts(input:))
    }
}

extension GetDiscoveredResourceCountsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetDiscoveredResourceCountsInput {
        return GetDiscoveredResourceCountsInput(
            limit: self.limit,
            nextToken: token,
            resourceTypes: self.resourceTypes
        )}
}
extension ConfigClient {
    /// Paginate over `[GetOrganizationConfigRuleDetailedStatusOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetOrganizationConfigRuleDetailedStatusInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetOrganizationConfigRuleDetailedStatusOutput`
    public func getOrganizationConfigRuleDetailedStatusPaginated(input: GetOrganizationConfigRuleDetailedStatusInput) -> ClientRuntime.PaginatorSequence<GetOrganizationConfigRuleDetailedStatusInput, GetOrganizationConfigRuleDetailedStatusOutput> {
        return ClientRuntime.PaginatorSequence<GetOrganizationConfigRuleDetailedStatusInput, GetOrganizationConfigRuleDetailedStatusOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getOrganizationConfigRuleDetailedStatus(input:))
    }
}

extension GetOrganizationConfigRuleDetailedStatusInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetOrganizationConfigRuleDetailedStatusInput {
        return GetOrganizationConfigRuleDetailedStatusInput(
            filters: self.filters,
            limit: self.limit,
            nextToken: token,
            organizationConfigRuleName: self.organizationConfigRuleName
        )}
}

extension PaginatorSequence where OperationStackInput == GetOrganizationConfigRuleDetailedStatusInput, OperationStackOutput == GetOrganizationConfigRuleDetailedStatusOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getOrganizationConfigRuleDetailedStatusPaginated`
    /// to access the nested member `[ConfigClientTypes.MemberAccountStatus]`
    /// - Returns: `[ConfigClientTypes.MemberAccountStatus]`
    public func organizationConfigRuleDetailedStatus() async throws -> [ConfigClientTypes.MemberAccountStatus] {
        return try await self.asyncCompactMap { item in item.organizationConfigRuleDetailedStatus }
    }
}
extension ConfigClient {
    /// Paginate over `[GetOrganizationConformancePackDetailedStatusOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetOrganizationConformancePackDetailedStatusInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetOrganizationConformancePackDetailedStatusOutput`
    public func getOrganizationConformancePackDetailedStatusPaginated(input: GetOrganizationConformancePackDetailedStatusInput) -> ClientRuntime.PaginatorSequence<GetOrganizationConformancePackDetailedStatusInput, GetOrganizationConformancePackDetailedStatusOutput> {
        return ClientRuntime.PaginatorSequence<GetOrganizationConformancePackDetailedStatusInput, GetOrganizationConformancePackDetailedStatusOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getOrganizationConformancePackDetailedStatus(input:))
    }
}

extension GetOrganizationConformancePackDetailedStatusInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetOrganizationConformancePackDetailedStatusInput {
        return GetOrganizationConformancePackDetailedStatusInput(
            filters: self.filters,
            limit: self.limit,
            nextToken: token,
            organizationConformancePackName: self.organizationConformancePackName
        )}
}

extension PaginatorSequence where OperationStackInput == GetOrganizationConformancePackDetailedStatusInput, OperationStackOutput == GetOrganizationConformancePackDetailedStatusOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getOrganizationConformancePackDetailedStatusPaginated`
    /// to access the nested member `[ConfigClientTypes.OrganizationConformancePackDetailedStatus]`
    /// - Returns: `[ConfigClientTypes.OrganizationConformancePackDetailedStatus]`
    public func organizationConformancePackDetailedStatuses() async throws -> [ConfigClientTypes.OrganizationConformancePackDetailedStatus] {
        return try await self.asyncCompactMap { item in item.organizationConformancePackDetailedStatuses }
    }
}
extension ConfigClient {
    /// Paginate over `[GetResourceConfigHistoryOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetResourceConfigHistoryInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetResourceConfigHistoryOutput`
    public func getResourceConfigHistoryPaginated(input: GetResourceConfigHistoryInput) -> ClientRuntime.PaginatorSequence<GetResourceConfigHistoryInput, GetResourceConfigHistoryOutput> {
        return ClientRuntime.PaginatorSequence<GetResourceConfigHistoryInput, GetResourceConfigHistoryOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getResourceConfigHistory(input:))
    }
}

extension GetResourceConfigHistoryInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetResourceConfigHistoryInput {
        return GetResourceConfigHistoryInput(
            chronologicalOrder: self.chronologicalOrder,
            earlierTime: self.earlierTime,
            laterTime: self.laterTime,
            limit: self.limit,
            nextToken: token,
            resourceId: self.resourceId,
            resourceType: self.resourceType
        )}
}

extension PaginatorSequence where OperationStackInput == GetResourceConfigHistoryInput, OperationStackOutput == GetResourceConfigHistoryOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getResourceConfigHistoryPaginated`
    /// to access the nested member `[ConfigClientTypes.ConfigurationItem]`
    /// - Returns: `[ConfigClientTypes.ConfigurationItem]`
    public func configurationItems() async throws -> [ConfigClientTypes.ConfigurationItem] {
        return try await self.asyncCompactMap { item in item.configurationItems }
    }
}
extension ConfigClient {
    /// Paginate over `[ListAggregateDiscoveredResourcesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListAggregateDiscoveredResourcesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListAggregateDiscoveredResourcesOutput`
    public func listAggregateDiscoveredResourcesPaginated(input: ListAggregateDiscoveredResourcesInput) -> ClientRuntime.PaginatorSequence<ListAggregateDiscoveredResourcesInput, ListAggregateDiscoveredResourcesOutput> {
        return ClientRuntime.PaginatorSequence<ListAggregateDiscoveredResourcesInput, ListAggregateDiscoveredResourcesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listAggregateDiscoveredResources(input:))
    }
}

extension ListAggregateDiscoveredResourcesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListAggregateDiscoveredResourcesInput {
        return ListAggregateDiscoveredResourcesInput(
            configurationAggregatorName: self.configurationAggregatorName,
            filters: self.filters,
            limit: self.limit,
            nextToken: token,
            resourceType: self.resourceType
        )}
}

extension PaginatorSequence where OperationStackInput == ListAggregateDiscoveredResourcesInput, OperationStackOutput == ListAggregateDiscoveredResourcesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listAggregateDiscoveredResourcesPaginated`
    /// to access the nested member `[ConfigClientTypes.AggregateResourceIdentifier]`
    /// - Returns: `[ConfigClientTypes.AggregateResourceIdentifier]`
    public func resourceIdentifiers() async throws -> [ConfigClientTypes.AggregateResourceIdentifier] {
        return try await self.asyncCompactMap { item in item.resourceIdentifiers }
    }
}
extension ConfigClient {
    /// Paginate over `[ListConfigurationRecordersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListConfigurationRecordersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListConfigurationRecordersOutput`
    public func listConfigurationRecordersPaginated(input: ListConfigurationRecordersInput) -> ClientRuntime.PaginatorSequence<ListConfigurationRecordersInput, ListConfigurationRecordersOutput> {
        return ClientRuntime.PaginatorSequence<ListConfigurationRecordersInput, ListConfigurationRecordersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listConfigurationRecorders(input:))
    }
}

extension ListConfigurationRecordersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListConfigurationRecordersInput {
        return ListConfigurationRecordersInput(
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListConfigurationRecordersInput, OperationStackOutput == ListConfigurationRecordersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listConfigurationRecordersPaginated`
    /// to access the nested member `[ConfigClientTypes.ConfigurationRecorderSummary]`
    /// - Returns: `[ConfigClientTypes.ConfigurationRecorderSummary]`
    public func configurationRecorderSummaries() async throws -> [ConfigClientTypes.ConfigurationRecorderSummary] {
        return try await self.asyncCompactMap { item in item.configurationRecorderSummaries }
    }
}
extension ConfigClient {
    /// Paginate over `[ListConformancePackComplianceScoresOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListConformancePackComplianceScoresInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListConformancePackComplianceScoresOutput`
    public func listConformancePackComplianceScoresPaginated(input: ListConformancePackComplianceScoresInput) -> ClientRuntime.PaginatorSequence<ListConformancePackComplianceScoresInput, ListConformancePackComplianceScoresOutput> {
        return ClientRuntime.PaginatorSequence<ListConformancePackComplianceScoresInput, ListConformancePackComplianceScoresOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listConformancePackComplianceScores(input:))
    }
}

extension ListConformancePackComplianceScoresInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListConformancePackComplianceScoresInput {
        return ListConformancePackComplianceScoresInput(
            filters: self.filters,
            limit: self.limit,
            nextToken: token,
            sortBy: self.sortBy,
            sortOrder: self.sortOrder
        )}
}
extension ConfigClient {
    /// Paginate over `[ListDiscoveredResourcesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListDiscoveredResourcesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListDiscoveredResourcesOutput`
    public func listDiscoveredResourcesPaginated(input: ListDiscoveredResourcesInput) -> ClientRuntime.PaginatorSequence<ListDiscoveredResourcesInput, ListDiscoveredResourcesOutput> {
        return ClientRuntime.PaginatorSequence<ListDiscoveredResourcesInput, ListDiscoveredResourcesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listDiscoveredResources(input:))
    }
}

extension ListDiscoveredResourcesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListDiscoveredResourcesInput {
        return ListDiscoveredResourcesInput(
            includeDeletedResources: self.includeDeletedResources,
            limit: self.limit,
            nextToken: token,
            resourceIds: self.resourceIds,
            resourceName: self.resourceName,
            resourceType: self.resourceType
        )}
}

extension PaginatorSequence where OperationStackInput == ListDiscoveredResourcesInput, OperationStackOutput == ListDiscoveredResourcesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listDiscoveredResourcesPaginated`
    /// to access the nested member `[ConfigClientTypes.ResourceIdentifier]`
    /// - Returns: `[ConfigClientTypes.ResourceIdentifier]`
    public func resourceIdentifiers() async throws -> [ConfigClientTypes.ResourceIdentifier] {
        return try await self.asyncCompactMap { item in item.resourceIdentifiers }
    }
}
extension ConfigClient {
    /// Paginate over `[ListResourceEvaluationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListResourceEvaluationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListResourceEvaluationsOutput`
    public func listResourceEvaluationsPaginated(input: ListResourceEvaluationsInput) -> ClientRuntime.PaginatorSequence<ListResourceEvaluationsInput, ListResourceEvaluationsOutput> {
        return ClientRuntime.PaginatorSequence<ListResourceEvaluationsInput, ListResourceEvaluationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listResourceEvaluations(input:))
    }
}

extension ListResourceEvaluationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListResourceEvaluationsInput {
        return ListResourceEvaluationsInput(
            filters: self.filters,
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListResourceEvaluationsInput, OperationStackOutput == ListResourceEvaluationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listResourceEvaluationsPaginated`
    /// to access the nested member `[ConfigClientTypes.ResourceEvaluation]`
    /// - Returns: `[ConfigClientTypes.ResourceEvaluation]`
    public func resourceEvaluations() async throws -> [ConfigClientTypes.ResourceEvaluation] {
        return try await self.asyncCompactMap { item in item.resourceEvaluations }
    }
}
extension ConfigClient {
    /// Paginate over `[ListStoredQueriesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListStoredQueriesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListStoredQueriesOutput`
    public func listStoredQueriesPaginated(input: ListStoredQueriesInput) -> ClientRuntime.PaginatorSequence<ListStoredQueriesInput, ListStoredQueriesOutput> {
        return ClientRuntime.PaginatorSequence<ListStoredQueriesInput, ListStoredQueriesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listStoredQueries(input:))
    }
}

extension ListStoredQueriesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListStoredQueriesInput {
        return ListStoredQueriesInput(
            maxResults: self.maxResults,
            nextToken: token
        )}
}
extension ConfigClient {
    /// Paginate over `[ListTagsForResourceOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListTagsForResourceInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListTagsForResourceOutput`
    public func listTagsForResourcePaginated(input: ListTagsForResourceInput) -> ClientRuntime.PaginatorSequence<ListTagsForResourceInput, ListTagsForResourceOutput> {
        return ClientRuntime.PaginatorSequence<ListTagsForResourceInput, ListTagsForResourceOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listTagsForResource(input:))
    }
}

extension ListTagsForResourceInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListTagsForResourceInput {
        return ListTagsForResourceInput(
            limit: self.limit,
            nextToken: token,
            resourceArn: self.resourceArn
        )}
}

extension PaginatorSequence where OperationStackInput == ListTagsForResourceInput, OperationStackOutput == ListTagsForResourceOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listTagsForResourcePaginated`
    /// to access the nested member `[ConfigClientTypes.Tag]`
    /// - Returns: `[ConfigClientTypes.Tag]`
    public func tags() async throws -> [ConfigClientTypes.Tag] {
        return try await self.asyncCompactMap { item in item.tags }
    }
}
extension ConfigClient {
    /// Paginate over `[SelectAggregateResourceConfigOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SelectAggregateResourceConfigInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SelectAggregateResourceConfigOutput`
    public func selectAggregateResourceConfigPaginated(input: SelectAggregateResourceConfigInput) -> ClientRuntime.PaginatorSequence<SelectAggregateResourceConfigInput, SelectAggregateResourceConfigOutput> {
        return ClientRuntime.PaginatorSequence<SelectAggregateResourceConfigInput, SelectAggregateResourceConfigOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.selectAggregateResourceConfig(input:))
    }
}

extension SelectAggregateResourceConfigInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SelectAggregateResourceConfigInput {
        return SelectAggregateResourceConfigInput(
            configurationAggregatorName: self.configurationAggregatorName,
            expression: self.expression,
            limit: self.limit,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == SelectAggregateResourceConfigInput, OperationStackOutput == SelectAggregateResourceConfigOutput {
    /// This paginator transforms the `AsyncSequence` returned by `selectAggregateResourceConfigPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func results() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.results }
    }
}
extension ConfigClient {
    /// Paginate over `[SelectResourceConfigOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SelectResourceConfigInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SelectResourceConfigOutput`
    public func selectResourceConfigPaginated(input: SelectResourceConfigInput) -> ClientRuntime.PaginatorSequence<SelectResourceConfigInput, SelectResourceConfigOutput> {
        return ClientRuntime.PaginatorSequence<SelectResourceConfigInput, SelectResourceConfigOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.selectResourceConfig(input:))
    }
}

extension SelectResourceConfigInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SelectResourceConfigInput {
        return SelectResourceConfigInput(
            expression: self.expression,
            limit: self.limit,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == SelectResourceConfigInput, OperationStackOutput == SelectResourceConfigOutput {
    /// This paginator transforms the `AsyncSequence` returned by `selectResourceConfigPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func results() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.results }
    }
}
