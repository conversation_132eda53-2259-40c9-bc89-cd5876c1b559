//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import Foundation
import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension EC2Client {
    /// Paginate over `[DescribeAddressesAttributeOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeAddressesAttributeInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeAddressesAttributeOutput`
    public func describeAddressesAttributePaginated(input: DescribeAddressesAttributeInput) -> ClientRuntime.PaginatorSequence<DescribeAddressesAttributeInput, DescribeAddressesAttributeOutput> {
        return ClientRuntime.PaginatorSequence<DescribeAddressesAttributeInput, DescribeAddressesAttributeOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeAddressesAttribute(input:))
    }
}

extension DescribeAddressesAttributeInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeAddressesAttributeInput {
        return DescribeAddressesAttributeInput(
            allocationIds: self.allocationIds,
            attribute: self.attribute,
            dryRun: self.dryRun,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeAddressesAttributeInput, OperationStackOutput == DescribeAddressesAttributeOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeAddressesAttributePaginated`
    /// to access the nested member `[EC2ClientTypes.AddressAttribute]`
    /// - Returns: `[EC2ClientTypes.AddressAttribute]`
    public func addresses() async throws -> [EC2ClientTypes.AddressAttribute] {
        return try await self.asyncCompactMap { item in item.addresses }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeAddressTransfersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeAddressTransfersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeAddressTransfersOutput`
    public func describeAddressTransfersPaginated(input: DescribeAddressTransfersInput) -> ClientRuntime.PaginatorSequence<DescribeAddressTransfersInput, DescribeAddressTransfersOutput> {
        return ClientRuntime.PaginatorSequence<DescribeAddressTransfersInput, DescribeAddressTransfersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeAddressTransfers(input:))
    }
}

extension DescribeAddressTransfersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeAddressTransfersInput {
        return DescribeAddressTransfersInput(
            allocationIds: self.allocationIds,
            dryRun: self.dryRun,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeAddressTransfersInput, OperationStackOutput == DescribeAddressTransfersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeAddressTransfersPaginated`
    /// to access the nested member `[EC2ClientTypes.AddressTransfer]`
    /// - Returns: `[EC2ClientTypes.AddressTransfer]`
    public func addressTransfers() async throws -> [EC2ClientTypes.AddressTransfer] {
        return try await self.asyncCompactMap { item in item.addressTransfers }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeAwsNetworkPerformanceMetricSubscriptionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeAwsNetworkPerformanceMetricSubscriptionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeAwsNetworkPerformanceMetricSubscriptionsOutput`
    public func describeAwsNetworkPerformanceMetricSubscriptionsPaginated(input: DescribeAwsNetworkPerformanceMetricSubscriptionsInput) -> ClientRuntime.PaginatorSequence<DescribeAwsNetworkPerformanceMetricSubscriptionsInput, DescribeAwsNetworkPerformanceMetricSubscriptionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeAwsNetworkPerformanceMetricSubscriptionsInput, DescribeAwsNetworkPerformanceMetricSubscriptionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeAwsNetworkPerformanceMetricSubscriptions(input:))
    }
}

extension DescribeAwsNetworkPerformanceMetricSubscriptionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeAwsNetworkPerformanceMetricSubscriptionsInput {
        return DescribeAwsNetworkPerformanceMetricSubscriptionsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeAwsNetworkPerformanceMetricSubscriptionsInput, OperationStackOutput == DescribeAwsNetworkPerformanceMetricSubscriptionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeAwsNetworkPerformanceMetricSubscriptionsPaginated`
    /// to access the nested member `[EC2ClientTypes.Subscription]`
    /// - Returns: `[EC2ClientTypes.Subscription]`
    public func subscriptions() async throws -> [EC2ClientTypes.Subscription] {
        return try await self.asyncCompactMap { item in item.subscriptions }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeByoipCidrsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeByoipCidrsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeByoipCidrsOutput`
    public func describeByoipCidrsPaginated(input: DescribeByoipCidrsInput) -> ClientRuntime.PaginatorSequence<DescribeByoipCidrsInput, DescribeByoipCidrsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeByoipCidrsInput, DescribeByoipCidrsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeByoipCidrs(input:))
    }
}

extension DescribeByoipCidrsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeByoipCidrsInput {
        return DescribeByoipCidrsInput(
            dryRun: self.dryRun,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeByoipCidrsInput, OperationStackOutput == DescribeByoipCidrsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeByoipCidrsPaginated`
    /// to access the nested member `[EC2ClientTypes.ByoipCidr]`
    /// - Returns: `[EC2ClientTypes.ByoipCidr]`
    public func byoipCidrs() async throws -> [EC2ClientTypes.ByoipCidr] {
        return try await self.asyncCompactMap { item in item.byoipCidrs }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeCapacityBlockExtensionHistoryOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCapacityBlockExtensionHistoryInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCapacityBlockExtensionHistoryOutput`
    public func describeCapacityBlockExtensionHistoryPaginated(input: DescribeCapacityBlockExtensionHistoryInput) -> ClientRuntime.PaginatorSequence<DescribeCapacityBlockExtensionHistoryInput, DescribeCapacityBlockExtensionHistoryOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCapacityBlockExtensionHistoryInput, DescribeCapacityBlockExtensionHistoryOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeCapacityBlockExtensionHistory(input:))
    }
}

extension DescribeCapacityBlockExtensionHistoryInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCapacityBlockExtensionHistoryInput {
        return DescribeCapacityBlockExtensionHistoryInput(
            capacityReservationIds: self.capacityReservationIds,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCapacityBlockExtensionHistoryInput, OperationStackOutput == DescribeCapacityBlockExtensionHistoryOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCapacityBlockExtensionHistoryPaginated`
    /// to access the nested member `[EC2ClientTypes.CapacityBlockExtension]`
    /// - Returns: `[EC2ClientTypes.CapacityBlockExtension]`
    public func capacityBlockExtensions() async throws -> [EC2ClientTypes.CapacityBlockExtension] {
        return try await self.asyncCompactMap { item in item.capacityBlockExtensions }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeCapacityBlockExtensionOfferingsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCapacityBlockExtensionOfferingsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCapacityBlockExtensionOfferingsOutput`
    public func describeCapacityBlockExtensionOfferingsPaginated(input: DescribeCapacityBlockExtensionOfferingsInput) -> ClientRuntime.PaginatorSequence<DescribeCapacityBlockExtensionOfferingsInput, DescribeCapacityBlockExtensionOfferingsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCapacityBlockExtensionOfferingsInput, DescribeCapacityBlockExtensionOfferingsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeCapacityBlockExtensionOfferings(input:))
    }
}

extension DescribeCapacityBlockExtensionOfferingsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCapacityBlockExtensionOfferingsInput {
        return DescribeCapacityBlockExtensionOfferingsInput(
            capacityBlockExtensionDurationHours: self.capacityBlockExtensionDurationHours,
            capacityReservationId: self.capacityReservationId,
            dryRun: self.dryRun,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCapacityBlockExtensionOfferingsInput, OperationStackOutput == DescribeCapacityBlockExtensionOfferingsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCapacityBlockExtensionOfferingsPaginated`
    /// to access the nested member `[EC2ClientTypes.CapacityBlockExtensionOffering]`
    /// - Returns: `[EC2ClientTypes.CapacityBlockExtensionOffering]`
    public func capacityBlockExtensionOfferings() async throws -> [EC2ClientTypes.CapacityBlockExtensionOffering] {
        return try await self.asyncCompactMap { item in item.capacityBlockExtensionOfferings }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeCapacityBlockOfferingsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCapacityBlockOfferingsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCapacityBlockOfferingsOutput`
    public func describeCapacityBlockOfferingsPaginated(input: DescribeCapacityBlockOfferingsInput) -> ClientRuntime.PaginatorSequence<DescribeCapacityBlockOfferingsInput, DescribeCapacityBlockOfferingsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCapacityBlockOfferingsInput, DescribeCapacityBlockOfferingsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeCapacityBlockOfferings(input:))
    }
}

extension DescribeCapacityBlockOfferingsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCapacityBlockOfferingsInput {
        return DescribeCapacityBlockOfferingsInput(
            capacityDurationHours: self.capacityDurationHours,
            dryRun: self.dryRun,
            endDateRange: self.endDateRange,
            instanceCount: self.instanceCount,
            instanceType: self.instanceType,
            maxResults: self.maxResults,
            nextToken: token,
            startDateRange: self.startDateRange
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCapacityBlockOfferingsInput, OperationStackOutput == DescribeCapacityBlockOfferingsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCapacityBlockOfferingsPaginated`
    /// to access the nested member `[EC2ClientTypes.CapacityBlockOffering]`
    /// - Returns: `[EC2ClientTypes.CapacityBlockOffering]`
    public func capacityBlockOfferings() async throws -> [EC2ClientTypes.CapacityBlockOffering] {
        return try await self.asyncCompactMap { item in item.capacityBlockOfferings }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeCapacityReservationBillingRequestsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCapacityReservationBillingRequestsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCapacityReservationBillingRequestsOutput`
    public func describeCapacityReservationBillingRequestsPaginated(input: DescribeCapacityReservationBillingRequestsInput) -> ClientRuntime.PaginatorSequence<DescribeCapacityReservationBillingRequestsInput, DescribeCapacityReservationBillingRequestsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCapacityReservationBillingRequestsInput, DescribeCapacityReservationBillingRequestsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeCapacityReservationBillingRequests(input:))
    }
}

extension DescribeCapacityReservationBillingRequestsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCapacityReservationBillingRequestsInput {
        return DescribeCapacityReservationBillingRequestsInput(
            capacityReservationIds: self.capacityReservationIds,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            role: self.role
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCapacityReservationBillingRequestsInput, OperationStackOutput == DescribeCapacityReservationBillingRequestsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCapacityReservationBillingRequestsPaginated`
    /// to access the nested member `[EC2ClientTypes.CapacityReservationBillingRequest]`
    /// - Returns: `[EC2ClientTypes.CapacityReservationBillingRequest]`
    public func capacityReservationBillingRequests() async throws -> [EC2ClientTypes.CapacityReservationBillingRequest] {
        return try await self.asyncCompactMap { item in item.capacityReservationBillingRequests }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeCapacityReservationFleetsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCapacityReservationFleetsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCapacityReservationFleetsOutput`
    public func describeCapacityReservationFleetsPaginated(input: DescribeCapacityReservationFleetsInput) -> ClientRuntime.PaginatorSequence<DescribeCapacityReservationFleetsInput, DescribeCapacityReservationFleetsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCapacityReservationFleetsInput, DescribeCapacityReservationFleetsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeCapacityReservationFleets(input:))
    }
}

extension DescribeCapacityReservationFleetsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCapacityReservationFleetsInput {
        return DescribeCapacityReservationFleetsInput(
            capacityReservationFleetIds: self.capacityReservationFleetIds,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCapacityReservationFleetsInput, OperationStackOutput == DescribeCapacityReservationFleetsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCapacityReservationFleetsPaginated`
    /// to access the nested member `[EC2ClientTypes.CapacityReservationFleet]`
    /// - Returns: `[EC2ClientTypes.CapacityReservationFleet]`
    public func capacityReservationFleets() async throws -> [EC2ClientTypes.CapacityReservationFleet] {
        return try await self.asyncCompactMap { item in item.capacityReservationFleets }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeCapacityReservationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCapacityReservationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCapacityReservationsOutput`
    public func describeCapacityReservationsPaginated(input: DescribeCapacityReservationsInput) -> ClientRuntime.PaginatorSequence<DescribeCapacityReservationsInput, DescribeCapacityReservationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCapacityReservationsInput, DescribeCapacityReservationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeCapacityReservations(input:))
    }
}

extension DescribeCapacityReservationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCapacityReservationsInput {
        return DescribeCapacityReservationsInput(
            capacityReservationIds: self.capacityReservationIds,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCapacityReservationsInput, OperationStackOutput == DescribeCapacityReservationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCapacityReservationsPaginated`
    /// to access the nested member `[EC2ClientTypes.CapacityReservation]`
    /// - Returns: `[EC2ClientTypes.CapacityReservation]`
    public func capacityReservations() async throws -> [EC2ClientTypes.CapacityReservation] {
        return try await self.asyncCompactMap { item in item.capacityReservations }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeCarrierGatewaysOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCarrierGatewaysInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCarrierGatewaysOutput`
    public func describeCarrierGatewaysPaginated(input: DescribeCarrierGatewaysInput) -> ClientRuntime.PaginatorSequence<DescribeCarrierGatewaysInput, DescribeCarrierGatewaysOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCarrierGatewaysInput, DescribeCarrierGatewaysOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeCarrierGateways(input:))
    }
}

extension DescribeCarrierGatewaysInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCarrierGatewaysInput {
        return DescribeCarrierGatewaysInput(
            carrierGatewayIds: self.carrierGatewayIds,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCarrierGatewaysInput, OperationStackOutput == DescribeCarrierGatewaysOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCarrierGatewaysPaginated`
    /// to access the nested member `[EC2ClientTypes.CarrierGateway]`
    /// - Returns: `[EC2ClientTypes.CarrierGateway]`
    public func carrierGateways() async throws -> [EC2ClientTypes.CarrierGateway] {
        return try await self.asyncCompactMap { item in item.carrierGateways }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeClassicLinkInstancesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeClassicLinkInstancesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeClassicLinkInstancesOutput`
    public func describeClassicLinkInstancesPaginated(input: DescribeClassicLinkInstancesInput) -> ClientRuntime.PaginatorSequence<DescribeClassicLinkInstancesInput, DescribeClassicLinkInstancesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeClassicLinkInstancesInput, DescribeClassicLinkInstancesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeClassicLinkInstances(input:))
    }
}

extension DescribeClassicLinkInstancesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeClassicLinkInstancesInput {
        return DescribeClassicLinkInstancesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            instanceIds: self.instanceIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeClassicLinkInstancesInput, OperationStackOutput == DescribeClassicLinkInstancesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeClassicLinkInstancesPaginated`
    /// to access the nested member `[EC2ClientTypes.ClassicLinkInstance]`
    /// - Returns: `[EC2ClientTypes.ClassicLinkInstance]`
    public func instances() async throws -> [EC2ClientTypes.ClassicLinkInstance] {
        return try await self.asyncCompactMap { item in item.instances }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeClientVpnAuthorizationRulesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeClientVpnAuthorizationRulesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeClientVpnAuthorizationRulesOutput`
    public func describeClientVpnAuthorizationRulesPaginated(input: DescribeClientVpnAuthorizationRulesInput) -> ClientRuntime.PaginatorSequence<DescribeClientVpnAuthorizationRulesInput, DescribeClientVpnAuthorizationRulesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeClientVpnAuthorizationRulesInput, DescribeClientVpnAuthorizationRulesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeClientVpnAuthorizationRules(input:))
    }
}

extension DescribeClientVpnAuthorizationRulesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeClientVpnAuthorizationRulesInput {
        return DescribeClientVpnAuthorizationRulesInput(
            clientVpnEndpointId: self.clientVpnEndpointId,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeClientVpnAuthorizationRulesInput, OperationStackOutput == DescribeClientVpnAuthorizationRulesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeClientVpnAuthorizationRulesPaginated`
    /// to access the nested member `[EC2ClientTypes.AuthorizationRule]`
    /// - Returns: `[EC2ClientTypes.AuthorizationRule]`
    public func authorizationRules() async throws -> [EC2ClientTypes.AuthorizationRule] {
        return try await self.asyncCompactMap { item in item.authorizationRules }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeClientVpnConnectionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeClientVpnConnectionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeClientVpnConnectionsOutput`
    public func describeClientVpnConnectionsPaginated(input: DescribeClientVpnConnectionsInput) -> ClientRuntime.PaginatorSequence<DescribeClientVpnConnectionsInput, DescribeClientVpnConnectionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeClientVpnConnectionsInput, DescribeClientVpnConnectionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeClientVpnConnections(input:))
    }
}

extension DescribeClientVpnConnectionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeClientVpnConnectionsInput {
        return DescribeClientVpnConnectionsInput(
            clientVpnEndpointId: self.clientVpnEndpointId,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeClientVpnConnectionsInput, OperationStackOutput == DescribeClientVpnConnectionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeClientVpnConnectionsPaginated`
    /// to access the nested member `[EC2ClientTypes.ClientVpnConnection]`
    /// - Returns: `[EC2ClientTypes.ClientVpnConnection]`
    public func connections() async throws -> [EC2ClientTypes.ClientVpnConnection] {
        return try await self.asyncCompactMap { item in item.connections }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeClientVpnEndpointsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeClientVpnEndpointsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeClientVpnEndpointsOutput`
    public func describeClientVpnEndpointsPaginated(input: DescribeClientVpnEndpointsInput) -> ClientRuntime.PaginatorSequence<DescribeClientVpnEndpointsInput, DescribeClientVpnEndpointsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeClientVpnEndpointsInput, DescribeClientVpnEndpointsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeClientVpnEndpoints(input:))
    }
}

extension DescribeClientVpnEndpointsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeClientVpnEndpointsInput {
        return DescribeClientVpnEndpointsInput(
            clientVpnEndpointIds: self.clientVpnEndpointIds,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeClientVpnEndpointsInput, OperationStackOutput == DescribeClientVpnEndpointsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeClientVpnEndpointsPaginated`
    /// to access the nested member `[EC2ClientTypes.ClientVpnEndpoint]`
    /// - Returns: `[EC2ClientTypes.ClientVpnEndpoint]`
    public func clientVpnEndpoints() async throws -> [EC2ClientTypes.ClientVpnEndpoint] {
        return try await self.asyncCompactMap { item in item.clientVpnEndpoints }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeClientVpnRoutesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeClientVpnRoutesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeClientVpnRoutesOutput`
    public func describeClientVpnRoutesPaginated(input: DescribeClientVpnRoutesInput) -> ClientRuntime.PaginatorSequence<DescribeClientVpnRoutesInput, DescribeClientVpnRoutesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeClientVpnRoutesInput, DescribeClientVpnRoutesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeClientVpnRoutes(input:))
    }
}

extension DescribeClientVpnRoutesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeClientVpnRoutesInput {
        return DescribeClientVpnRoutesInput(
            clientVpnEndpointId: self.clientVpnEndpointId,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeClientVpnRoutesInput, OperationStackOutput == DescribeClientVpnRoutesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeClientVpnRoutesPaginated`
    /// to access the nested member `[EC2ClientTypes.ClientVpnRoute]`
    /// - Returns: `[EC2ClientTypes.ClientVpnRoute]`
    public func routes() async throws -> [EC2ClientTypes.ClientVpnRoute] {
        return try await self.asyncCompactMap { item in item.routes }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeClientVpnTargetNetworksOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeClientVpnTargetNetworksInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeClientVpnTargetNetworksOutput`
    public func describeClientVpnTargetNetworksPaginated(input: DescribeClientVpnTargetNetworksInput) -> ClientRuntime.PaginatorSequence<DescribeClientVpnTargetNetworksInput, DescribeClientVpnTargetNetworksOutput> {
        return ClientRuntime.PaginatorSequence<DescribeClientVpnTargetNetworksInput, DescribeClientVpnTargetNetworksOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeClientVpnTargetNetworks(input:))
    }
}

extension DescribeClientVpnTargetNetworksInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeClientVpnTargetNetworksInput {
        return DescribeClientVpnTargetNetworksInput(
            associationIds: self.associationIds,
            clientVpnEndpointId: self.clientVpnEndpointId,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeClientVpnTargetNetworksInput, OperationStackOutput == DescribeClientVpnTargetNetworksOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeClientVpnTargetNetworksPaginated`
    /// to access the nested member `[EC2ClientTypes.TargetNetwork]`
    /// - Returns: `[EC2ClientTypes.TargetNetwork]`
    public func clientVpnTargetNetworks() async throws -> [EC2ClientTypes.TargetNetwork] {
        return try await self.asyncCompactMap { item in item.clientVpnTargetNetworks }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeCoipPoolsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCoipPoolsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCoipPoolsOutput`
    public func describeCoipPoolsPaginated(input: DescribeCoipPoolsInput) -> ClientRuntime.PaginatorSequence<DescribeCoipPoolsInput, DescribeCoipPoolsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCoipPoolsInput, DescribeCoipPoolsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeCoipPools(input:))
    }
}

extension DescribeCoipPoolsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCoipPoolsInput {
        return DescribeCoipPoolsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            poolIds: self.poolIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCoipPoolsInput, OperationStackOutput == DescribeCoipPoolsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCoipPoolsPaginated`
    /// to access the nested member `[EC2ClientTypes.CoipPool]`
    /// - Returns: `[EC2ClientTypes.CoipPool]`
    public func coipPools() async throws -> [EC2ClientTypes.CoipPool] {
        return try await self.asyncCompactMap { item in item.coipPools }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeDhcpOptionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeDhcpOptionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeDhcpOptionsOutput`
    public func describeDhcpOptionsPaginated(input: DescribeDhcpOptionsInput) -> ClientRuntime.PaginatorSequence<DescribeDhcpOptionsInput, DescribeDhcpOptionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeDhcpOptionsInput, DescribeDhcpOptionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeDhcpOptions(input:))
    }
}

extension DescribeDhcpOptionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeDhcpOptionsInput {
        return DescribeDhcpOptionsInput(
            dhcpOptionsIds: self.dhcpOptionsIds,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeDhcpOptionsInput, OperationStackOutput == DescribeDhcpOptionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeDhcpOptionsPaginated`
    /// to access the nested member `[EC2ClientTypes.DhcpOptions]`
    /// - Returns: `[EC2ClientTypes.DhcpOptions]`
    public func dhcpOptions() async throws -> [EC2ClientTypes.DhcpOptions] {
        return try await self.asyncCompactMap { item in item.dhcpOptions }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeEgressOnlyInternetGatewaysOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeEgressOnlyInternetGatewaysInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeEgressOnlyInternetGatewaysOutput`
    public func describeEgressOnlyInternetGatewaysPaginated(input: DescribeEgressOnlyInternetGatewaysInput) -> ClientRuntime.PaginatorSequence<DescribeEgressOnlyInternetGatewaysInput, DescribeEgressOnlyInternetGatewaysOutput> {
        return ClientRuntime.PaginatorSequence<DescribeEgressOnlyInternetGatewaysInput, DescribeEgressOnlyInternetGatewaysOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeEgressOnlyInternetGateways(input:))
    }
}

extension DescribeEgressOnlyInternetGatewaysInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeEgressOnlyInternetGatewaysInput {
        return DescribeEgressOnlyInternetGatewaysInput(
            dryRun: self.dryRun,
            egressOnlyInternetGatewayIds: self.egressOnlyInternetGatewayIds,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeEgressOnlyInternetGatewaysInput, OperationStackOutput == DescribeEgressOnlyInternetGatewaysOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeEgressOnlyInternetGatewaysPaginated`
    /// to access the nested member `[EC2ClientTypes.EgressOnlyInternetGateway]`
    /// - Returns: `[EC2ClientTypes.EgressOnlyInternetGateway]`
    public func egressOnlyInternetGateways() async throws -> [EC2ClientTypes.EgressOnlyInternetGateway] {
        return try await self.asyncCompactMap { item in item.egressOnlyInternetGateways }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeExportImageTasksOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeExportImageTasksInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeExportImageTasksOutput`
    public func describeExportImageTasksPaginated(input: DescribeExportImageTasksInput) -> ClientRuntime.PaginatorSequence<DescribeExportImageTasksInput, DescribeExportImageTasksOutput> {
        return ClientRuntime.PaginatorSequence<DescribeExportImageTasksInput, DescribeExportImageTasksOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeExportImageTasks(input:))
    }
}

extension DescribeExportImageTasksInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeExportImageTasksInput {
        return DescribeExportImageTasksInput(
            dryRun: self.dryRun,
            exportImageTaskIds: self.exportImageTaskIds,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeExportImageTasksInput, OperationStackOutput == DescribeExportImageTasksOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeExportImageTasksPaginated`
    /// to access the nested member `[EC2ClientTypes.ExportImageTask]`
    /// - Returns: `[EC2ClientTypes.ExportImageTask]`
    public func exportImageTasks() async throws -> [EC2ClientTypes.ExportImageTask] {
        return try await self.asyncCompactMap { item in item.exportImageTasks }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeFastLaunchImagesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeFastLaunchImagesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeFastLaunchImagesOutput`
    public func describeFastLaunchImagesPaginated(input: DescribeFastLaunchImagesInput) -> ClientRuntime.PaginatorSequence<DescribeFastLaunchImagesInput, DescribeFastLaunchImagesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeFastLaunchImagesInput, DescribeFastLaunchImagesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeFastLaunchImages(input:))
    }
}

extension DescribeFastLaunchImagesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeFastLaunchImagesInput {
        return DescribeFastLaunchImagesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            imageIds: self.imageIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeFastLaunchImagesInput, OperationStackOutput == DescribeFastLaunchImagesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeFastLaunchImagesPaginated`
    /// to access the nested member `[EC2ClientTypes.DescribeFastLaunchImagesSuccessItem]`
    /// - Returns: `[EC2ClientTypes.DescribeFastLaunchImagesSuccessItem]`
    public func fastLaunchImages() async throws -> [EC2ClientTypes.DescribeFastLaunchImagesSuccessItem] {
        return try await self.asyncCompactMap { item in item.fastLaunchImages }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeFastSnapshotRestoresOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeFastSnapshotRestoresInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeFastSnapshotRestoresOutput`
    public func describeFastSnapshotRestoresPaginated(input: DescribeFastSnapshotRestoresInput) -> ClientRuntime.PaginatorSequence<DescribeFastSnapshotRestoresInput, DescribeFastSnapshotRestoresOutput> {
        return ClientRuntime.PaginatorSequence<DescribeFastSnapshotRestoresInput, DescribeFastSnapshotRestoresOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeFastSnapshotRestores(input:))
    }
}

extension DescribeFastSnapshotRestoresInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeFastSnapshotRestoresInput {
        return DescribeFastSnapshotRestoresInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeFastSnapshotRestoresInput, OperationStackOutput == DescribeFastSnapshotRestoresOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeFastSnapshotRestoresPaginated`
    /// to access the nested member `[EC2ClientTypes.DescribeFastSnapshotRestoreSuccessItem]`
    /// - Returns: `[EC2ClientTypes.DescribeFastSnapshotRestoreSuccessItem]`
    public func fastSnapshotRestores() async throws -> [EC2ClientTypes.DescribeFastSnapshotRestoreSuccessItem] {
        return try await self.asyncCompactMap { item in item.fastSnapshotRestores }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeFleetsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeFleetsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeFleetsOutput`
    public func describeFleetsPaginated(input: DescribeFleetsInput) -> ClientRuntime.PaginatorSequence<DescribeFleetsInput, DescribeFleetsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeFleetsInput, DescribeFleetsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeFleets(input:))
    }
}

extension DescribeFleetsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeFleetsInput {
        return DescribeFleetsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            fleetIds: self.fleetIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeFleetsInput, OperationStackOutput == DescribeFleetsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeFleetsPaginated`
    /// to access the nested member `[EC2ClientTypes.FleetData]`
    /// - Returns: `[EC2ClientTypes.FleetData]`
    public func fleets() async throws -> [EC2ClientTypes.FleetData] {
        return try await self.asyncCompactMap { item in item.fleets }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeFlowLogsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeFlowLogsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeFlowLogsOutput`
    public func describeFlowLogsPaginated(input: DescribeFlowLogsInput) -> ClientRuntime.PaginatorSequence<DescribeFlowLogsInput, DescribeFlowLogsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeFlowLogsInput, DescribeFlowLogsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeFlowLogs(input:))
    }
}

extension DescribeFlowLogsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeFlowLogsInput {
        return DescribeFlowLogsInput(
            dryRun: self.dryRun,
            filter: self.filter,
            flowLogIds: self.flowLogIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeFlowLogsInput, OperationStackOutput == DescribeFlowLogsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeFlowLogsPaginated`
    /// to access the nested member `[EC2ClientTypes.FlowLog]`
    /// - Returns: `[EC2ClientTypes.FlowLog]`
    public func flowLogs() async throws -> [EC2ClientTypes.FlowLog] {
        return try await self.asyncCompactMap { item in item.flowLogs }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeFpgaImagesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeFpgaImagesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeFpgaImagesOutput`
    public func describeFpgaImagesPaginated(input: DescribeFpgaImagesInput) -> ClientRuntime.PaginatorSequence<DescribeFpgaImagesInput, DescribeFpgaImagesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeFpgaImagesInput, DescribeFpgaImagesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeFpgaImages(input:))
    }
}

extension DescribeFpgaImagesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeFpgaImagesInput {
        return DescribeFpgaImagesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            fpgaImageIds: self.fpgaImageIds,
            maxResults: self.maxResults,
            nextToken: token,
            owners: self.owners
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeFpgaImagesInput, OperationStackOutput == DescribeFpgaImagesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeFpgaImagesPaginated`
    /// to access the nested member `[EC2ClientTypes.FpgaImage]`
    /// - Returns: `[EC2ClientTypes.FpgaImage]`
    public func fpgaImages() async throws -> [EC2ClientTypes.FpgaImage] {
        return try await self.asyncCompactMap { item in item.fpgaImages }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeHostReservationOfferingsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeHostReservationOfferingsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeHostReservationOfferingsOutput`
    public func describeHostReservationOfferingsPaginated(input: DescribeHostReservationOfferingsInput) -> ClientRuntime.PaginatorSequence<DescribeHostReservationOfferingsInput, DescribeHostReservationOfferingsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeHostReservationOfferingsInput, DescribeHostReservationOfferingsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeHostReservationOfferings(input:))
    }
}

extension DescribeHostReservationOfferingsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeHostReservationOfferingsInput {
        return DescribeHostReservationOfferingsInput(
            filter: self.filter,
            maxDuration: self.maxDuration,
            maxResults: self.maxResults,
            minDuration: self.minDuration,
            nextToken: token,
            offeringId: self.offeringId
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeHostReservationOfferingsInput, OperationStackOutput == DescribeHostReservationOfferingsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeHostReservationOfferingsPaginated`
    /// to access the nested member `[EC2ClientTypes.HostOffering]`
    /// - Returns: `[EC2ClientTypes.HostOffering]`
    public func offeringSet() async throws -> [EC2ClientTypes.HostOffering] {
        return try await self.asyncCompactMap { item in item.offeringSet }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeHostReservationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeHostReservationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeHostReservationsOutput`
    public func describeHostReservationsPaginated(input: DescribeHostReservationsInput) -> ClientRuntime.PaginatorSequence<DescribeHostReservationsInput, DescribeHostReservationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeHostReservationsInput, DescribeHostReservationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeHostReservations(input:))
    }
}

extension DescribeHostReservationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeHostReservationsInput {
        return DescribeHostReservationsInput(
            filter: self.filter,
            hostReservationIdSet: self.hostReservationIdSet,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeHostReservationsInput, OperationStackOutput == DescribeHostReservationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeHostReservationsPaginated`
    /// to access the nested member `[EC2ClientTypes.HostReservation]`
    /// - Returns: `[EC2ClientTypes.HostReservation]`
    public func hostReservationSet() async throws -> [EC2ClientTypes.HostReservation] {
        return try await self.asyncCompactMap { item in item.hostReservationSet }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeHostsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeHostsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeHostsOutput`
    public func describeHostsPaginated(input: DescribeHostsInput) -> ClientRuntime.PaginatorSequence<DescribeHostsInput, DescribeHostsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeHostsInput, DescribeHostsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeHosts(input:))
    }
}

extension DescribeHostsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeHostsInput {
        return DescribeHostsInput(
            filter: self.filter,
            hostIds: self.hostIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeHostsInput, OperationStackOutput == DescribeHostsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeHostsPaginated`
    /// to access the nested member `[EC2ClientTypes.Host]`
    /// - Returns: `[EC2ClientTypes.Host]`
    public func hosts() async throws -> [EC2ClientTypes.Host] {
        return try await self.asyncCompactMap { item in item.hosts }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeIamInstanceProfileAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeIamInstanceProfileAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeIamInstanceProfileAssociationsOutput`
    public func describeIamInstanceProfileAssociationsPaginated(input: DescribeIamInstanceProfileAssociationsInput) -> ClientRuntime.PaginatorSequence<DescribeIamInstanceProfileAssociationsInput, DescribeIamInstanceProfileAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeIamInstanceProfileAssociationsInput, DescribeIamInstanceProfileAssociationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeIamInstanceProfileAssociations(input:))
    }
}

extension DescribeIamInstanceProfileAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeIamInstanceProfileAssociationsInput {
        return DescribeIamInstanceProfileAssociationsInput(
            associationIds: self.associationIds,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeIamInstanceProfileAssociationsInput, OperationStackOutput == DescribeIamInstanceProfileAssociationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeIamInstanceProfileAssociationsPaginated`
    /// to access the nested member `[EC2ClientTypes.IamInstanceProfileAssociation]`
    /// - Returns: `[EC2ClientTypes.IamInstanceProfileAssociation]`
    public func iamInstanceProfileAssociations() async throws -> [EC2ClientTypes.IamInstanceProfileAssociation] {
        return try await self.asyncCompactMap { item in item.iamInstanceProfileAssociations }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeImagesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeImagesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeImagesOutput`
    public func describeImagesPaginated(input: DescribeImagesInput) -> ClientRuntime.PaginatorSequence<DescribeImagesInput, DescribeImagesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeImagesInput, DescribeImagesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeImages(input:))
    }
}

extension DescribeImagesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeImagesInput {
        return DescribeImagesInput(
            dryRun: self.dryRun,
            executableUsers: self.executableUsers,
            filters: self.filters,
            imageIds: self.imageIds,
            includeDeprecated: self.includeDeprecated,
            includeDisabled: self.includeDisabled,
            maxResults: self.maxResults,
            nextToken: token,
            owners: self.owners
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeImagesInput, OperationStackOutput == DescribeImagesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeImagesPaginated`
    /// to access the nested member `[EC2ClientTypes.Image]`
    /// - Returns: `[EC2ClientTypes.Image]`
    public func images() async throws -> [EC2ClientTypes.Image] {
        return try await self.asyncCompactMap { item in item.images }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeImportImageTasksOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeImportImageTasksInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeImportImageTasksOutput`
    public func describeImportImageTasksPaginated(input: DescribeImportImageTasksInput) -> ClientRuntime.PaginatorSequence<DescribeImportImageTasksInput, DescribeImportImageTasksOutput> {
        return ClientRuntime.PaginatorSequence<DescribeImportImageTasksInput, DescribeImportImageTasksOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeImportImageTasks(input:))
    }
}

extension DescribeImportImageTasksInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeImportImageTasksInput {
        return DescribeImportImageTasksInput(
            dryRun: self.dryRun,
            filters: self.filters,
            importTaskIds: self.importTaskIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeImportImageTasksInput, OperationStackOutput == DescribeImportImageTasksOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeImportImageTasksPaginated`
    /// to access the nested member `[EC2ClientTypes.ImportImageTask]`
    /// - Returns: `[EC2ClientTypes.ImportImageTask]`
    public func importImageTasks() async throws -> [EC2ClientTypes.ImportImageTask] {
        return try await self.asyncCompactMap { item in item.importImageTasks }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeImportSnapshotTasksOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeImportSnapshotTasksInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeImportSnapshotTasksOutput`
    public func describeImportSnapshotTasksPaginated(input: DescribeImportSnapshotTasksInput) -> ClientRuntime.PaginatorSequence<DescribeImportSnapshotTasksInput, DescribeImportSnapshotTasksOutput> {
        return ClientRuntime.PaginatorSequence<DescribeImportSnapshotTasksInput, DescribeImportSnapshotTasksOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeImportSnapshotTasks(input:))
    }
}

extension DescribeImportSnapshotTasksInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeImportSnapshotTasksInput {
        return DescribeImportSnapshotTasksInput(
            dryRun: self.dryRun,
            filters: self.filters,
            importTaskIds: self.importTaskIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeImportSnapshotTasksInput, OperationStackOutput == DescribeImportSnapshotTasksOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeImportSnapshotTasksPaginated`
    /// to access the nested member `[EC2ClientTypes.ImportSnapshotTask]`
    /// - Returns: `[EC2ClientTypes.ImportSnapshotTask]`
    public func importSnapshotTasks() async throws -> [EC2ClientTypes.ImportSnapshotTask] {
        return try await self.asyncCompactMap { item in item.importSnapshotTasks }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeInstanceConnectEndpointsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeInstanceConnectEndpointsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeInstanceConnectEndpointsOutput`
    public func describeInstanceConnectEndpointsPaginated(input: DescribeInstanceConnectEndpointsInput) -> ClientRuntime.PaginatorSequence<DescribeInstanceConnectEndpointsInput, DescribeInstanceConnectEndpointsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeInstanceConnectEndpointsInput, DescribeInstanceConnectEndpointsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeInstanceConnectEndpoints(input:))
    }
}

extension DescribeInstanceConnectEndpointsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeInstanceConnectEndpointsInput {
        return DescribeInstanceConnectEndpointsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            instanceConnectEndpointIds: self.instanceConnectEndpointIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeInstanceConnectEndpointsInput, OperationStackOutput == DescribeInstanceConnectEndpointsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeInstanceConnectEndpointsPaginated`
    /// to access the nested member `[EC2ClientTypes.Ec2InstanceConnectEndpoint]`
    /// - Returns: `[EC2ClientTypes.Ec2InstanceConnectEndpoint]`
    public func instanceConnectEndpoints() async throws -> [EC2ClientTypes.Ec2InstanceConnectEndpoint] {
        return try await self.asyncCompactMap { item in item.instanceConnectEndpoints }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeInstanceCreditSpecificationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeInstanceCreditSpecificationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeInstanceCreditSpecificationsOutput`
    public func describeInstanceCreditSpecificationsPaginated(input: DescribeInstanceCreditSpecificationsInput) -> ClientRuntime.PaginatorSequence<DescribeInstanceCreditSpecificationsInput, DescribeInstanceCreditSpecificationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeInstanceCreditSpecificationsInput, DescribeInstanceCreditSpecificationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeInstanceCreditSpecifications(input:))
    }
}

extension DescribeInstanceCreditSpecificationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeInstanceCreditSpecificationsInput {
        return DescribeInstanceCreditSpecificationsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            instanceIds: self.instanceIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeInstanceCreditSpecificationsInput, OperationStackOutput == DescribeInstanceCreditSpecificationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeInstanceCreditSpecificationsPaginated`
    /// to access the nested member `[EC2ClientTypes.InstanceCreditSpecification]`
    /// - Returns: `[EC2ClientTypes.InstanceCreditSpecification]`
    public func instanceCreditSpecifications() async throws -> [EC2ClientTypes.InstanceCreditSpecification] {
        return try await self.asyncCompactMap { item in item.instanceCreditSpecifications }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeInstanceEventWindowsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeInstanceEventWindowsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeInstanceEventWindowsOutput`
    public func describeInstanceEventWindowsPaginated(input: DescribeInstanceEventWindowsInput) -> ClientRuntime.PaginatorSequence<DescribeInstanceEventWindowsInput, DescribeInstanceEventWindowsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeInstanceEventWindowsInput, DescribeInstanceEventWindowsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeInstanceEventWindows(input:))
    }
}

extension DescribeInstanceEventWindowsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeInstanceEventWindowsInput {
        return DescribeInstanceEventWindowsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            instanceEventWindowIds: self.instanceEventWindowIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeInstanceEventWindowsInput, OperationStackOutput == DescribeInstanceEventWindowsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeInstanceEventWindowsPaginated`
    /// to access the nested member `[EC2ClientTypes.InstanceEventWindow]`
    /// - Returns: `[EC2ClientTypes.InstanceEventWindow]`
    public func instanceEventWindows() async throws -> [EC2ClientTypes.InstanceEventWindow] {
        return try await self.asyncCompactMap { item in item.instanceEventWindows }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeInstanceImageMetadataOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeInstanceImageMetadataInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeInstanceImageMetadataOutput`
    public func describeInstanceImageMetadataPaginated(input: DescribeInstanceImageMetadataInput) -> ClientRuntime.PaginatorSequence<DescribeInstanceImageMetadataInput, DescribeInstanceImageMetadataOutput> {
        return ClientRuntime.PaginatorSequence<DescribeInstanceImageMetadataInput, DescribeInstanceImageMetadataOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeInstanceImageMetadata(input:))
    }
}

extension DescribeInstanceImageMetadataInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeInstanceImageMetadataInput {
        return DescribeInstanceImageMetadataInput(
            dryRun: self.dryRun,
            filters: self.filters,
            instanceIds: self.instanceIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeInstanceImageMetadataInput, OperationStackOutput == DescribeInstanceImageMetadataOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeInstanceImageMetadataPaginated`
    /// to access the nested member `[EC2ClientTypes.InstanceImageMetadata]`
    /// - Returns: `[EC2ClientTypes.InstanceImageMetadata]`
    public func instanceImageMetadata() async throws -> [EC2ClientTypes.InstanceImageMetadata] {
        return try await self.asyncCompactMap { item in item.instanceImageMetadata }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeInstancesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeInstancesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeInstancesOutput`
    public func describeInstancesPaginated(input: DescribeInstancesInput) -> ClientRuntime.PaginatorSequence<DescribeInstancesInput, DescribeInstancesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeInstancesInput, DescribeInstancesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeInstances(input:))
    }
}

extension DescribeInstancesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeInstancesInput {
        return DescribeInstancesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            instanceIds: self.instanceIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeInstancesInput, OperationStackOutput == DescribeInstancesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeInstancesPaginated`
    /// to access the nested member `[EC2ClientTypes.Reservation]`
    /// - Returns: `[EC2ClientTypes.Reservation]`
    public func reservations() async throws -> [EC2ClientTypes.Reservation] {
        return try await self.asyncCompactMap { item in item.reservations }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeInstanceStatusOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeInstanceStatusInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeInstanceStatusOutput`
    public func describeInstanceStatusPaginated(input: DescribeInstanceStatusInput) -> ClientRuntime.PaginatorSequence<DescribeInstanceStatusInput, DescribeInstanceStatusOutput> {
        return ClientRuntime.PaginatorSequence<DescribeInstanceStatusInput, DescribeInstanceStatusOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeInstanceStatus(input:))
    }
}

extension DescribeInstanceStatusInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeInstanceStatusInput {
        return DescribeInstanceStatusInput(
            dryRun: self.dryRun,
            filters: self.filters,
            includeAllInstances: self.includeAllInstances,
            instanceIds: self.instanceIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeInstanceStatusInput, OperationStackOutput == DescribeInstanceStatusOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeInstanceStatusPaginated`
    /// to access the nested member `[EC2ClientTypes.InstanceStatus]`
    /// - Returns: `[EC2ClientTypes.InstanceStatus]`
    public func instanceStatuses() async throws -> [EC2ClientTypes.InstanceStatus] {
        return try await self.asyncCompactMap { item in item.instanceStatuses }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeInstanceTopologyOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeInstanceTopologyInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeInstanceTopologyOutput`
    public func describeInstanceTopologyPaginated(input: DescribeInstanceTopologyInput) -> ClientRuntime.PaginatorSequence<DescribeInstanceTopologyInput, DescribeInstanceTopologyOutput> {
        return ClientRuntime.PaginatorSequence<DescribeInstanceTopologyInput, DescribeInstanceTopologyOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeInstanceTopology(input:))
    }
}

extension DescribeInstanceTopologyInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeInstanceTopologyInput {
        return DescribeInstanceTopologyInput(
            dryRun: self.dryRun,
            filters: self.filters,
            groupNames: self.groupNames,
            instanceIds: self.instanceIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeInstanceTopologyInput, OperationStackOutput == DescribeInstanceTopologyOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeInstanceTopologyPaginated`
    /// to access the nested member `[EC2ClientTypes.InstanceTopology]`
    /// - Returns: `[EC2ClientTypes.InstanceTopology]`
    public func instances() async throws -> [EC2ClientTypes.InstanceTopology] {
        return try await self.asyncCompactMap { item in item.instances }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeInstanceTypeOfferingsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeInstanceTypeOfferingsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeInstanceTypeOfferingsOutput`
    public func describeInstanceTypeOfferingsPaginated(input: DescribeInstanceTypeOfferingsInput) -> ClientRuntime.PaginatorSequence<DescribeInstanceTypeOfferingsInput, DescribeInstanceTypeOfferingsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeInstanceTypeOfferingsInput, DescribeInstanceTypeOfferingsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeInstanceTypeOfferings(input:))
    }
}

extension DescribeInstanceTypeOfferingsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeInstanceTypeOfferingsInput {
        return DescribeInstanceTypeOfferingsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            locationType: self.locationType,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeInstanceTypeOfferingsInput, OperationStackOutput == DescribeInstanceTypeOfferingsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeInstanceTypeOfferingsPaginated`
    /// to access the nested member `[EC2ClientTypes.InstanceTypeOffering]`
    /// - Returns: `[EC2ClientTypes.InstanceTypeOffering]`
    public func instanceTypeOfferings() async throws -> [EC2ClientTypes.InstanceTypeOffering] {
        return try await self.asyncCompactMap { item in item.instanceTypeOfferings }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeInstanceTypesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeInstanceTypesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeInstanceTypesOutput`
    public func describeInstanceTypesPaginated(input: DescribeInstanceTypesInput) -> ClientRuntime.PaginatorSequence<DescribeInstanceTypesInput, DescribeInstanceTypesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeInstanceTypesInput, DescribeInstanceTypesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeInstanceTypes(input:))
    }
}

extension DescribeInstanceTypesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeInstanceTypesInput {
        return DescribeInstanceTypesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            instanceTypes: self.instanceTypes,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeInstanceTypesInput, OperationStackOutput == DescribeInstanceTypesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeInstanceTypesPaginated`
    /// to access the nested member `[EC2ClientTypes.InstanceTypeInfo]`
    /// - Returns: `[EC2ClientTypes.InstanceTypeInfo]`
    public func instanceTypes() async throws -> [EC2ClientTypes.InstanceTypeInfo] {
        return try await self.asyncCompactMap { item in item.instanceTypes }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeInternetGatewaysOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeInternetGatewaysInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeInternetGatewaysOutput`
    public func describeInternetGatewaysPaginated(input: DescribeInternetGatewaysInput) -> ClientRuntime.PaginatorSequence<DescribeInternetGatewaysInput, DescribeInternetGatewaysOutput> {
        return ClientRuntime.PaginatorSequence<DescribeInternetGatewaysInput, DescribeInternetGatewaysOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeInternetGateways(input:))
    }
}

extension DescribeInternetGatewaysInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeInternetGatewaysInput {
        return DescribeInternetGatewaysInput(
            dryRun: self.dryRun,
            filters: self.filters,
            internetGatewayIds: self.internetGatewayIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeInternetGatewaysInput, OperationStackOutput == DescribeInternetGatewaysOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeInternetGatewaysPaginated`
    /// to access the nested member `[EC2ClientTypes.InternetGateway]`
    /// - Returns: `[EC2ClientTypes.InternetGateway]`
    public func internetGateways() async throws -> [EC2ClientTypes.InternetGateway] {
        return try await self.asyncCompactMap { item in item.internetGateways }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeIpamPoolsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeIpamPoolsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeIpamPoolsOutput`
    public func describeIpamPoolsPaginated(input: DescribeIpamPoolsInput) -> ClientRuntime.PaginatorSequence<DescribeIpamPoolsInput, DescribeIpamPoolsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeIpamPoolsInput, DescribeIpamPoolsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeIpamPools(input:))
    }
}

extension DescribeIpamPoolsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeIpamPoolsInput {
        return DescribeIpamPoolsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            ipamPoolIds: self.ipamPoolIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeIpamPoolsInput, OperationStackOutput == DescribeIpamPoolsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeIpamPoolsPaginated`
    /// to access the nested member `[EC2ClientTypes.IpamPool]`
    /// - Returns: `[EC2ClientTypes.IpamPool]`
    public func ipamPools() async throws -> [EC2ClientTypes.IpamPool] {
        return try await self.asyncCompactMap { item in item.ipamPools }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeIpamResourceDiscoveriesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeIpamResourceDiscoveriesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeIpamResourceDiscoveriesOutput`
    public func describeIpamResourceDiscoveriesPaginated(input: DescribeIpamResourceDiscoveriesInput) -> ClientRuntime.PaginatorSequence<DescribeIpamResourceDiscoveriesInput, DescribeIpamResourceDiscoveriesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeIpamResourceDiscoveriesInput, DescribeIpamResourceDiscoveriesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeIpamResourceDiscoveries(input:))
    }
}

extension DescribeIpamResourceDiscoveriesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeIpamResourceDiscoveriesInput {
        return DescribeIpamResourceDiscoveriesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            ipamResourceDiscoveryIds: self.ipamResourceDiscoveryIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeIpamResourceDiscoveriesInput, OperationStackOutput == DescribeIpamResourceDiscoveriesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeIpamResourceDiscoveriesPaginated`
    /// to access the nested member `[EC2ClientTypes.IpamResourceDiscovery]`
    /// - Returns: `[EC2ClientTypes.IpamResourceDiscovery]`
    public func ipamResourceDiscoveries() async throws -> [EC2ClientTypes.IpamResourceDiscovery] {
        return try await self.asyncCompactMap { item in item.ipamResourceDiscoveries }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeIpamResourceDiscoveryAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeIpamResourceDiscoveryAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeIpamResourceDiscoveryAssociationsOutput`
    public func describeIpamResourceDiscoveryAssociationsPaginated(input: DescribeIpamResourceDiscoveryAssociationsInput) -> ClientRuntime.PaginatorSequence<DescribeIpamResourceDiscoveryAssociationsInput, DescribeIpamResourceDiscoveryAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeIpamResourceDiscoveryAssociationsInput, DescribeIpamResourceDiscoveryAssociationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeIpamResourceDiscoveryAssociations(input:))
    }
}

extension DescribeIpamResourceDiscoveryAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeIpamResourceDiscoveryAssociationsInput {
        return DescribeIpamResourceDiscoveryAssociationsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            ipamResourceDiscoveryAssociationIds: self.ipamResourceDiscoveryAssociationIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeIpamResourceDiscoveryAssociationsInput, OperationStackOutput == DescribeIpamResourceDiscoveryAssociationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeIpamResourceDiscoveryAssociationsPaginated`
    /// to access the nested member `[EC2ClientTypes.IpamResourceDiscoveryAssociation]`
    /// - Returns: `[EC2ClientTypes.IpamResourceDiscoveryAssociation]`
    public func ipamResourceDiscoveryAssociations() async throws -> [EC2ClientTypes.IpamResourceDiscoveryAssociation] {
        return try await self.asyncCompactMap { item in item.ipamResourceDiscoveryAssociations }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeIpamsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeIpamsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeIpamsOutput`
    public func describeIpamsPaginated(input: DescribeIpamsInput) -> ClientRuntime.PaginatorSequence<DescribeIpamsInput, DescribeIpamsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeIpamsInput, DescribeIpamsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeIpams(input:))
    }
}

extension DescribeIpamsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeIpamsInput {
        return DescribeIpamsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            ipamIds: self.ipamIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeIpamsInput, OperationStackOutput == DescribeIpamsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeIpamsPaginated`
    /// to access the nested member `[EC2ClientTypes.Ipam]`
    /// - Returns: `[EC2ClientTypes.Ipam]`
    public func ipams() async throws -> [EC2ClientTypes.Ipam] {
        return try await self.asyncCompactMap { item in item.ipams }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeIpamScopesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeIpamScopesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeIpamScopesOutput`
    public func describeIpamScopesPaginated(input: DescribeIpamScopesInput) -> ClientRuntime.PaginatorSequence<DescribeIpamScopesInput, DescribeIpamScopesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeIpamScopesInput, DescribeIpamScopesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeIpamScopes(input:))
    }
}

extension DescribeIpamScopesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeIpamScopesInput {
        return DescribeIpamScopesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            ipamScopeIds: self.ipamScopeIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeIpamScopesInput, OperationStackOutput == DescribeIpamScopesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeIpamScopesPaginated`
    /// to access the nested member `[EC2ClientTypes.IpamScope]`
    /// - Returns: `[EC2ClientTypes.IpamScope]`
    public func ipamScopes() async throws -> [EC2ClientTypes.IpamScope] {
        return try await self.asyncCompactMap { item in item.ipamScopes }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeIpv6PoolsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeIpv6PoolsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeIpv6PoolsOutput`
    public func describeIpv6PoolsPaginated(input: DescribeIpv6PoolsInput) -> ClientRuntime.PaginatorSequence<DescribeIpv6PoolsInput, DescribeIpv6PoolsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeIpv6PoolsInput, DescribeIpv6PoolsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeIpv6Pools(input:))
    }
}

extension DescribeIpv6PoolsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeIpv6PoolsInput {
        return DescribeIpv6PoolsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            poolIds: self.poolIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeIpv6PoolsInput, OperationStackOutput == DescribeIpv6PoolsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeIpv6PoolsPaginated`
    /// to access the nested member `[EC2ClientTypes.Ipv6Pool]`
    /// - Returns: `[EC2ClientTypes.Ipv6Pool]`
    public func ipv6Pools() async throws -> [EC2ClientTypes.Ipv6Pool] {
        return try await self.asyncCompactMap { item in item.ipv6Pools }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeLaunchTemplatesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeLaunchTemplatesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeLaunchTemplatesOutput`
    public func describeLaunchTemplatesPaginated(input: DescribeLaunchTemplatesInput) -> ClientRuntime.PaginatorSequence<DescribeLaunchTemplatesInput, DescribeLaunchTemplatesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeLaunchTemplatesInput, DescribeLaunchTemplatesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeLaunchTemplates(input:))
    }
}

extension DescribeLaunchTemplatesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeLaunchTemplatesInput {
        return DescribeLaunchTemplatesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            launchTemplateIds: self.launchTemplateIds,
            launchTemplateNames: self.launchTemplateNames,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeLaunchTemplatesInput, OperationStackOutput == DescribeLaunchTemplatesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeLaunchTemplatesPaginated`
    /// to access the nested member `[EC2ClientTypes.LaunchTemplate]`
    /// - Returns: `[EC2ClientTypes.LaunchTemplate]`
    public func launchTemplates() async throws -> [EC2ClientTypes.LaunchTemplate] {
        return try await self.asyncCompactMap { item in item.launchTemplates }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeLaunchTemplateVersionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeLaunchTemplateVersionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeLaunchTemplateVersionsOutput`
    public func describeLaunchTemplateVersionsPaginated(input: DescribeLaunchTemplateVersionsInput) -> ClientRuntime.PaginatorSequence<DescribeLaunchTemplateVersionsInput, DescribeLaunchTemplateVersionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeLaunchTemplateVersionsInput, DescribeLaunchTemplateVersionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeLaunchTemplateVersions(input:))
    }
}

extension DescribeLaunchTemplateVersionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeLaunchTemplateVersionsInput {
        return DescribeLaunchTemplateVersionsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            launchTemplateId: self.launchTemplateId,
            launchTemplateName: self.launchTemplateName,
            maxResults: self.maxResults,
            maxVersion: self.maxVersion,
            minVersion: self.minVersion,
            nextToken: token,
            resolveAlias: self.resolveAlias,
            versions: self.versions
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeLaunchTemplateVersionsInput, OperationStackOutput == DescribeLaunchTemplateVersionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeLaunchTemplateVersionsPaginated`
    /// to access the nested member `[EC2ClientTypes.LaunchTemplateVersion]`
    /// - Returns: `[EC2ClientTypes.LaunchTemplateVersion]`
    public func launchTemplateVersions() async throws -> [EC2ClientTypes.LaunchTemplateVersion] {
        return try await self.asyncCompactMap { item in item.launchTemplateVersions }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeLocalGatewayRouteTablesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeLocalGatewayRouteTablesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeLocalGatewayRouteTablesOutput`
    public func describeLocalGatewayRouteTablesPaginated(input: DescribeLocalGatewayRouteTablesInput) -> ClientRuntime.PaginatorSequence<DescribeLocalGatewayRouteTablesInput, DescribeLocalGatewayRouteTablesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeLocalGatewayRouteTablesInput, DescribeLocalGatewayRouteTablesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeLocalGatewayRouteTables(input:))
    }
}

extension DescribeLocalGatewayRouteTablesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeLocalGatewayRouteTablesInput {
        return DescribeLocalGatewayRouteTablesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            localGatewayRouteTableIds: self.localGatewayRouteTableIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeLocalGatewayRouteTablesInput, OperationStackOutput == DescribeLocalGatewayRouteTablesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeLocalGatewayRouteTablesPaginated`
    /// to access the nested member `[EC2ClientTypes.LocalGatewayRouteTable]`
    /// - Returns: `[EC2ClientTypes.LocalGatewayRouteTable]`
    public func localGatewayRouteTables() async throws -> [EC2ClientTypes.LocalGatewayRouteTable] {
        return try await self.asyncCompactMap { item in item.localGatewayRouteTables }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsOutput`
    public func describeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsPaginated(input: DescribeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsInput) -> ClientRuntime.PaginatorSequence<DescribeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsInput, DescribeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsInput, DescribeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeLocalGatewayRouteTableVirtualInterfaceGroupAssociations(input:))
    }
}

extension DescribeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsInput {
        return DescribeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            localGatewayRouteTableVirtualInterfaceGroupAssociationIds: self.localGatewayRouteTableVirtualInterfaceGroupAssociationIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsInput, OperationStackOutput == DescribeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeLocalGatewayRouteTableVirtualInterfaceGroupAssociationsPaginated`
    /// to access the nested member `[EC2ClientTypes.LocalGatewayRouteTableVirtualInterfaceGroupAssociation]`
    /// - Returns: `[EC2ClientTypes.LocalGatewayRouteTableVirtualInterfaceGroupAssociation]`
    public func localGatewayRouteTableVirtualInterfaceGroupAssociations() async throws -> [EC2ClientTypes.LocalGatewayRouteTableVirtualInterfaceGroupAssociation] {
        return try await self.asyncCompactMap { item in item.localGatewayRouteTableVirtualInterfaceGroupAssociations }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeLocalGatewayRouteTableVpcAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeLocalGatewayRouteTableVpcAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeLocalGatewayRouteTableVpcAssociationsOutput`
    public func describeLocalGatewayRouteTableVpcAssociationsPaginated(input: DescribeLocalGatewayRouteTableVpcAssociationsInput) -> ClientRuntime.PaginatorSequence<DescribeLocalGatewayRouteTableVpcAssociationsInput, DescribeLocalGatewayRouteTableVpcAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeLocalGatewayRouteTableVpcAssociationsInput, DescribeLocalGatewayRouteTableVpcAssociationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeLocalGatewayRouteTableVpcAssociations(input:))
    }
}

extension DescribeLocalGatewayRouteTableVpcAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeLocalGatewayRouteTableVpcAssociationsInput {
        return DescribeLocalGatewayRouteTableVpcAssociationsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            localGatewayRouteTableVpcAssociationIds: self.localGatewayRouteTableVpcAssociationIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeLocalGatewayRouteTableVpcAssociationsInput, OperationStackOutput == DescribeLocalGatewayRouteTableVpcAssociationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeLocalGatewayRouteTableVpcAssociationsPaginated`
    /// to access the nested member `[EC2ClientTypes.LocalGatewayRouteTableVpcAssociation]`
    /// - Returns: `[EC2ClientTypes.LocalGatewayRouteTableVpcAssociation]`
    public func localGatewayRouteTableVpcAssociations() async throws -> [EC2ClientTypes.LocalGatewayRouteTableVpcAssociation] {
        return try await self.asyncCompactMap { item in item.localGatewayRouteTableVpcAssociations }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeLocalGatewaysOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeLocalGatewaysInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeLocalGatewaysOutput`
    public func describeLocalGatewaysPaginated(input: DescribeLocalGatewaysInput) -> ClientRuntime.PaginatorSequence<DescribeLocalGatewaysInput, DescribeLocalGatewaysOutput> {
        return ClientRuntime.PaginatorSequence<DescribeLocalGatewaysInput, DescribeLocalGatewaysOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeLocalGateways(input:))
    }
}

extension DescribeLocalGatewaysInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeLocalGatewaysInput {
        return DescribeLocalGatewaysInput(
            dryRun: self.dryRun,
            filters: self.filters,
            localGatewayIds: self.localGatewayIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeLocalGatewaysInput, OperationStackOutput == DescribeLocalGatewaysOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeLocalGatewaysPaginated`
    /// to access the nested member `[EC2ClientTypes.LocalGateway]`
    /// - Returns: `[EC2ClientTypes.LocalGateway]`
    public func localGateways() async throws -> [EC2ClientTypes.LocalGateway] {
        return try await self.asyncCompactMap { item in item.localGateways }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeLocalGatewayVirtualInterfaceGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeLocalGatewayVirtualInterfaceGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeLocalGatewayVirtualInterfaceGroupsOutput`
    public func describeLocalGatewayVirtualInterfaceGroupsPaginated(input: DescribeLocalGatewayVirtualInterfaceGroupsInput) -> ClientRuntime.PaginatorSequence<DescribeLocalGatewayVirtualInterfaceGroupsInput, DescribeLocalGatewayVirtualInterfaceGroupsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeLocalGatewayVirtualInterfaceGroupsInput, DescribeLocalGatewayVirtualInterfaceGroupsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeLocalGatewayVirtualInterfaceGroups(input:))
    }
}

extension DescribeLocalGatewayVirtualInterfaceGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeLocalGatewayVirtualInterfaceGroupsInput {
        return DescribeLocalGatewayVirtualInterfaceGroupsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            localGatewayVirtualInterfaceGroupIds: self.localGatewayVirtualInterfaceGroupIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeLocalGatewayVirtualInterfaceGroupsInput, OperationStackOutput == DescribeLocalGatewayVirtualInterfaceGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeLocalGatewayVirtualInterfaceGroupsPaginated`
    /// to access the nested member `[EC2ClientTypes.LocalGatewayVirtualInterfaceGroup]`
    /// - Returns: `[EC2ClientTypes.LocalGatewayVirtualInterfaceGroup]`
    public func localGatewayVirtualInterfaceGroups() async throws -> [EC2ClientTypes.LocalGatewayVirtualInterfaceGroup] {
        return try await self.asyncCompactMap { item in item.localGatewayVirtualInterfaceGroups }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeLocalGatewayVirtualInterfacesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeLocalGatewayVirtualInterfacesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeLocalGatewayVirtualInterfacesOutput`
    public func describeLocalGatewayVirtualInterfacesPaginated(input: DescribeLocalGatewayVirtualInterfacesInput) -> ClientRuntime.PaginatorSequence<DescribeLocalGatewayVirtualInterfacesInput, DescribeLocalGatewayVirtualInterfacesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeLocalGatewayVirtualInterfacesInput, DescribeLocalGatewayVirtualInterfacesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeLocalGatewayVirtualInterfaces(input:))
    }
}

extension DescribeLocalGatewayVirtualInterfacesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeLocalGatewayVirtualInterfacesInput {
        return DescribeLocalGatewayVirtualInterfacesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            localGatewayVirtualInterfaceIds: self.localGatewayVirtualInterfaceIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeLocalGatewayVirtualInterfacesInput, OperationStackOutput == DescribeLocalGatewayVirtualInterfacesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeLocalGatewayVirtualInterfacesPaginated`
    /// to access the nested member `[EC2ClientTypes.LocalGatewayVirtualInterface]`
    /// - Returns: `[EC2ClientTypes.LocalGatewayVirtualInterface]`
    public func localGatewayVirtualInterfaces() async throws -> [EC2ClientTypes.LocalGatewayVirtualInterface] {
        return try await self.asyncCompactMap { item in item.localGatewayVirtualInterfaces }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeMacHostsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeMacHostsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeMacHostsOutput`
    public func describeMacHostsPaginated(input: DescribeMacHostsInput) -> ClientRuntime.PaginatorSequence<DescribeMacHostsInput, DescribeMacHostsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeMacHostsInput, DescribeMacHostsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeMacHosts(input:))
    }
}

extension DescribeMacHostsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeMacHostsInput {
        return DescribeMacHostsInput(
            filters: self.filters,
            hostIds: self.hostIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeMacHostsInput, OperationStackOutput == DescribeMacHostsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeMacHostsPaginated`
    /// to access the nested member `[EC2ClientTypes.MacHost]`
    /// - Returns: `[EC2ClientTypes.MacHost]`
    public func macHosts() async throws -> [EC2ClientTypes.MacHost] {
        return try await self.asyncCompactMap { item in item.macHosts }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeManagedPrefixListsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeManagedPrefixListsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeManagedPrefixListsOutput`
    public func describeManagedPrefixListsPaginated(input: DescribeManagedPrefixListsInput) -> ClientRuntime.PaginatorSequence<DescribeManagedPrefixListsInput, DescribeManagedPrefixListsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeManagedPrefixListsInput, DescribeManagedPrefixListsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeManagedPrefixLists(input:))
    }
}

extension DescribeManagedPrefixListsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeManagedPrefixListsInput {
        return DescribeManagedPrefixListsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            prefixListIds: self.prefixListIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeManagedPrefixListsInput, OperationStackOutput == DescribeManagedPrefixListsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeManagedPrefixListsPaginated`
    /// to access the nested member `[EC2ClientTypes.ManagedPrefixList]`
    /// - Returns: `[EC2ClientTypes.ManagedPrefixList]`
    public func prefixLists() async throws -> [EC2ClientTypes.ManagedPrefixList] {
        return try await self.asyncCompactMap { item in item.prefixLists }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeMovingAddressesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeMovingAddressesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeMovingAddressesOutput`
    public func describeMovingAddressesPaginated(input: DescribeMovingAddressesInput) -> ClientRuntime.PaginatorSequence<DescribeMovingAddressesInput, DescribeMovingAddressesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeMovingAddressesInput, DescribeMovingAddressesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeMovingAddresses(input:))
    }
}

extension DescribeMovingAddressesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeMovingAddressesInput {
        return DescribeMovingAddressesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            publicIps: self.publicIps
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeMovingAddressesInput, OperationStackOutput == DescribeMovingAddressesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeMovingAddressesPaginated`
    /// to access the nested member `[EC2ClientTypes.MovingAddressStatus]`
    /// - Returns: `[EC2ClientTypes.MovingAddressStatus]`
    public func movingAddressStatuses() async throws -> [EC2ClientTypes.MovingAddressStatus] {
        return try await self.asyncCompactMap { item in item.movingAddressStatuses }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeNatGatewaysOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeNatGatewaysInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeNatGatewaysOutput`
    public func describeNatGatewaysPaginated(input: DescribeNatGatewaysInput) -> ClientRuntime.PaginatorSequence<DescribeNatGatewaysInput, DescribeNatGatewaysOutput> {
        return ClientRuntime.PaginatorSequence<DescribeNatGatewaysInput, DescribeNatGatewaysOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeNatGateways(input:))
    }
}

extension DescribeNatGatewaysInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeNatGatewaysInput {
        return DescribeNatGatewaysInput(
            dryRun: self.dryRun,
            filter: self.filter,
            maxResults: self.maxResults,
            natGatewayIds: self.natGatewayIds,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeNatGatewaysInput, OperationStackOutput == DescribeNatGatewaysOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeNatGatewaysPaginated`
    /// to access the nested member `[EC2ClientTypes.NatGateway]`
    /// - Returns: `[EC2ClientTypes.NatGateway]`
    public func natGateways() async throws -> [EC2ClientTypes.NatGateway] {
        return try await self.asyncCompactMap { item in item.natGateways }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeNetworkAclsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeNetworkAclsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeNetworkAclsOutput`
    public func describeNetworkAclsPaginated(input: DescribeNetworkAclsInput) -> ClientRuntime.PaginatorSequence<DescribeNetworkAclsInput, DescribeNetworkAclsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeNetworkAclsInput, DescribeNetworkAclsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeNetworkAcls(input:))
    }
}

extension DescribeNetworkAclsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeNetworkAclsInput {
        return DescribeNetworkAclsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            networkAclIds: self.networkAclIds,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeNetworkAclsInput, OperationStackOutput == DescribeNetworkAclsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeNetworkAclsPaginated`
    /// to access the nested member `[EC2ClientTypes.NetworkAcl]`
    /// - Returns: `[EC2ClientTypes.NetworkAcl]`
    public func networkAcls() async throws -> [EC2ClientTypes.NetworkAcl] {
        return try await self.asyncCompactMap { item in item.networkAcls }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeNetworkInsightsAccessScopeAnalysesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeNetworkInsightsAccessScopeAnalysesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeNetworkInsightsAccessScopeAnalysesOutput`
    public func describeNetworkInsightsAccessScopeAnalysesPaginated(input: DescribeNetworkInsightsAccessScopeAnalysesInput) -> ClientRuntime.PaginatorSequence<DescribeNetworkInsightsAccessScopeAnalysesInput, DescribeNetworkInsightsAccessScopeAnalysesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeNetworkInsightsAccessScopeAnalysesInput, DescribeNetworkInsightsAccessScopeAnalysesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeNetworkInsightsAccessScopeAnalyses(input:))
    }
}

extension DescribeNetworkInsightsAccessScopeAnalysesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeNetworkInsightsAccessScopeAnalysesInput {
        return DescribeNetworkInsightsAccessScopeAnalysesInput(
            analysisStartTimeBegin: self.analysisStartTimeBegin,
            analysisStartTimeEnd: self.analysisStartTimeEnd,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            networkInsightsAccessScopeAnalysisIds: self.networkInsightsAccessScopeAnalysisIds,
            networkInsightsAccessScopeId: self.networkInsightsAccessScopeId,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeNetworkInsightsAccessScopeAnalysesInput, OperationStackOutput == DescribeNetworkInsightsAccessScopeAnalysesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeNetworkInsightsAccessScopeAnalysesPaginated`
    /// to access the nested member `[EC2ClientTypes.NetworkInsightsAccessScopeAnalysis]`
    /// - Returns: `[EC2ClientTypes.NetworkInsightsAccessScopeAnalysis]`
    public func networkInsightsAccessScopeAnalyses() async throws -> [EC2ClientTypes.NetworkInsightsAccessScopeAnalysis] {
        return try await self.asyncCompactMap { item in item.networkInsightsAccessScopeAnalyses }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeNetworkInsightsAccessScopesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeNetworkInsightsAccessScopesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeNetworkInsightsAccessScopesOutput`
    public func describeNetworkInsightsAccessScopesPaginated(input: DescribeNetworkInsightsAccessScopesInput) -> ClientRuntime.PaginatorSequence<DescribeNetworkInsightsAccessScopesInput, DescribeNetworkInsightsAccessScopesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeNetworkInsightsAccessScopesInput, DescribeNetworkInsightsAccessScopesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeNetworkInsightsAccessScopes(input:))
    }
}

extension DescribeNetworkInsightsAccessScopesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeNetworkInsightsAccessScopesInput {
        return DescribeNetworkInsightsAccessScopesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            networkInsightsAccessScopeIds: self.networkInsightsAccessScopeIds,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeNetworkInsightsAccessScopesInput, OperationStackOutput == DescribeNetworkInsightsAccessScopesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeNetworkInsightsAccessScopesPaginated`
    /// to access the nested member `[EC2ClientTypes.NetworkInsightsAccessScope]`
    /// - Returns: `[EC2ClientTypes.NetworkInsightsAccessScope]`
    public func networkInsightsAccessScopes() async throws -> [EC2ClientTypes.NetworkInsightsAccessScope] {
        return try await self.asyncCompactMap { item in item.networkInsightsAccessScopes }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeNetworkInsightsAnalysesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeNetworkInsightsAnalysesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeNetworkInsightsAnalysesOutput`
    public func describeNetworkInsightsAnalysesPaginated(input: DescribeNetworkInsightsAnalysesInput) -> ClientRuntime.PaginatorSequence<DescribeNetworkInsightsAnalysesInput, DescribeNetworkInsightsAnalysesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeNetworkInsightsAnalysesInput, DescribeNetworkInsightsAnalysesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeNetworkInsightsAnalyses(input:))
    }
}

extension DescribeNetworkInsightsAnalysesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeNetworkInsightsAnalysesInput {
        return DescribeNetworkInsightsAnalysesInput(
            analysisEndTime: self.analysisEndTime,
            analysisStartTime: self.analysisStartTime,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            networkInsightsAnalysisIds: self.networkInsightsAnalysisIds,
            networkInsightsPathId: self.networkInsightsPathId,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeNetworkInsightsAnalysesInput, OperationStackOutput == DescribeNetworkInsightsAnalysesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeNetworkInsightsAnalysesPaginated`
    /// to access the nested member `[EC2ClientTypes.NetworkInsightsAnalysis]`
    /// - Returns: `[EC2ClientTypes.NetworkInsightsAnalysis]`
    public func networkInsightsAnalyses() async throws -> [EC2ClientTypes.NetworkInsightsAnalysis] {
        return try await self.asyncCompactMap { item in item.networkInsightsAnalyses }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeNetworkInsightsPathsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeNetworkInsightsPathsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeNetworkInsightsPathsOutput`
    public func describeNetworkInsightsPathsPaginated(input: DescribeNetworkInsightsPathsInput) -> ClientRuntime.PaginatorSequence<DescribeNetworkInsightsPathsInput, DescribeNetworkInsightsPathsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeNetworkInsightsPathsInput, DescribeNetworkInsightsPathsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeNetworkInsightsPaths(input:))
    }
}

extension DescribeNetworkInsightsPathsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeNetworkInsightsPathsInput {
        return DescribeNetworkInsightsPathsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            networkInsightsPathIds: self.networkInsightsPathIds,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeNetworkInsightsPathsInput, OperationStackOutput == DescribeNetworkInsightsPathsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeNetworkInsightsPathsPaginated`
    /// to access the nested member `[EC2ClientTypes.NetworkInsightsPath]`
    /// - Returns: `[EC2ClientTypes.NetworkInsightsPath]`
    public func networkInsightsPaths() async throws -> [EC2ClientTypes.NetworkInsightsPath] {
        return try await self.asyncCompactMap { item in item.networkInsightsPaths }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeNetworkInterfacePermissionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeNetworkInterfacePermissionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeNetworkInterfacePermissionsOutput`
    public func describeNetworkInterfacePermissionsPaginated(input: DescribeNetworkInterfacePermissionsInput) -> ClientRuntime.PaginatorSequence<DescribeNetworkInterfacePermissionsInput, DescribeNetworkInterfacePermissionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeNetworkInterfacePermissionsInput, DescribeNetworkInterfacePermissionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeNetworkInterfacePermissions(input:))
    }
}

extension DescribeNetworkInterfacePermissionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeNetworkInterfacePermissionsInput {
        return DescribeNetworkInterfacePermissionsInput(
            filters: self.filters,
            maxResults: self.maxResults,
            networkInterfacePermissionIds: self.networkInterfacePermissionIds,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeNetworkInterfacePermissionsInput, OperationStackOutput == DescribeNetworkInterfacePermissionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeNetworkInterfacePermissionsPaginated`
    /// to access the nested member `[EC2ClientTypes.NetworkInterfacePermission]`
    /// - Returns: `[EC2ClientTypes.NetworkInterfacePermission]`
    public func networkInterfacePermissions() async throws -> [EC2ClientTypes.NetworkInterfacePermission] {
        return try await self.asyncCompactMap { item in item.networkInterfacePermissions }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeNetworkInterfacesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeNetworkInterfacesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeNetworkInterfacesOutput`
    public func describeNetworkInterfacesPaginated(input: DescribeNetworkInterfacesInput) -> ClientRuntime.PaginatorSequence<DescribeNetworkInterfacesInput, DescribeNetworkInterfacesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeNetworkInterfacesInput, DescribeNetworkInterfacesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeNetworkInterfaces(input:))
    }
}

extension DescribeNetworkInterfacesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeNetworkInterfacesInput {
        return DescribeNetworkInterfacesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            networkInterfaceIds: self.networkInterfaceIds,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeNetworkInterfacesInput, OperationStackOutput == DescribeNetworkInterfacesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeNetworkInterfacesPaginated`
    /// to access the nested member `[EC2ClientTypes.NetworkInterface]`
    /// - Returns: `[EC2ClientTypes.NetworkInterface]`
    public func networkInterfaces() async throws -> [EC2ClientTypes.NetworkInterface] {
        return try await self.asyncCompactMap { item in item.networkInterfaces }
    }
}
extension EC2Client {
    /// Paginate over `[DescribePrefixListsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribePrefixListsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribePrefixListsOutput`
    public func describePrefixListsPaginated(input: DescribePrefixListsInput) -> ClientRuntime.PaginatorSequence<DescribePrefixListsInput, DescribePrefixListsOutput> {
        return ClientRuntime.PaginatorSequence<DescribePrefixListsInput, DescribePrefixListsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describePrefixLists(input:))
    }
}

extension DescribePrefixListsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribePrefixListsInput {
        return DescribePrefixListsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            prefixListIds: self.prefixListIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribePrefixListsInput, OperationStackOutput == DescribePrefixListsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describePrefixListsPaginated`
    /// to access the nested member `[EC2ClientTypes.PrefixList]`
    /// - Returns: `[EC2ClientTypes.PrefixList]`
    public func prefixLists() async throws -> [EC2ClientTypes.PrefixList] {
        return try await self.asyncCompactMap { item in item.prefixLists }
    }
}
extension EC2Client {
    /// Paginate over `[DescribePrincipalIdFormatOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribePrincipalIdFormatInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribePrincipalIdFormatOutput`
    public func describePrincipalIdFormatPaginated(input: DescribePrincipalIdFormatInput) -> ClientRuntime.PaginatorSequence<DescribePrincipalIdFormatInput, DescribePrincipalIdFormatOutput> {
        return ClientRuntime.PaginatorSequence<DescribePrincipalIdFormatInput, DescribePrincipalIdFormatOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describePrincipalIdFormat(input:))
    }
}

extension DescribePrincipalIdFormatInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribePrincipalIdFormatInput {
        return DescribePrincipalIdFormatInput(
            dryRun: self.dryRun,
            maxResults: self.maxResults,
            nextToken: token,
            resources: self.resources
        )}
}

extension PaginatorSequence where OperationStackInput == DescribePrincipalIdFormatInput, OperationStackOutput == DescribePrincipalIdFormatOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describePrincipalIdFormatPaginated`
    /// to access the nested member `[EC2ClientTypes.PrincipalIdFormat]`
    /// - Returns: `[EC2ClientTypes.PrincipalIdFormat]`
    public func principals() async throws -> [EC2ClientTypes.PrincipalIdFormat] {
        return try await self.asyncCompactMap { item in item.principals }
    }
}
extension EC2Client {
    /// Paginate over `[DescribePublicIpv4PoolsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribePublicIpv4PoolsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribePublicIpv4PoolsOutput`
    public func describePublicIpv4PoolsPaginated(input: DescribePublicIpv4PoolsInput) -> ClientRuntime.PaginatorSequence<DescribePublicIpv4PoolsInput, DescribePublicIpv4PoolsOutput> {
        return ClientRuntime.PaginatorSequence<DescribePublicIpv4PoolsInput, DescribePublicIpv4PoolsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describePublicIpv4Pools(input:))
    }
}

extension DescribePublicIpv4PoolsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribePublicIpv4PoolsInput {
        return DescribePublicIpv4PoolsInput(
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            poolIds: self.poolIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribePublicIpv4PoolsInput, OperationStackOutput == DescribePublicIpv4PoolsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describePublicIpv4PoolsPaginated`
    /// to access the nested member `[EC2ClientTypes.PublicIpv4Pool]`
    /// - Returns: `[EC2ClientTypes.PublicIpv4Pool]`
    public func publicIpv4Pools() async throws -> [EC2ClientTypes.PublicIpv4Pool] {
        return try await self.asyncCompactMap { item in item.publicIpv4Pools }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeReplaceRootVolumeTasksOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReplaceRootVolumeTasksInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReplaceRootVolumeTasksOutput`
    public func describeReplaceRootVolumeTasksPaginated(input: DescribeReplaceRootVolumeTasksInput) -> ClientRuntime.PaginatorSequence<DescribeReplaceRootVolumeTasksInput, DescribeReplaceRootVolumeTasksOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReplaceRootVolumeTasksInput, DescribeReplaceRootVolumeTasksOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeReplaceRootVolumeTasks(input:))
    }
}

extension DescribeReplaceRootVolumeTasksInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReplaceRootVolumeTasksInput {
        return DescribeReplaceRootVolumeTasksInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            replaceRootVolumeTaskIds: self.replaceRootVolumeTaskIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeReplaceRootVolumeTasksInput, OperationStackOutput == DescribeReplaceRootVolumeTasksOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeReplaceRootVolumeTasksPaginated`
    /// to access the nested member `[EC2ClientTypes.ReplaceRootVolumeTask]`
    /// - Returns: `[EC2ClientTypes.ReplaceRootVolumeTask]`
    public func replaceRootVolumeTasks() async throws -> [EC2ClientTypes.ReplaceRootVolumeTask] {
        return try await self.asyncCompactMap { item in item.replaceRootVolumeTasks }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeReservedInstancesModificationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReservedInstancesModificationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReservedInstancesModificationsOutput`
    public func describeReservedInstancesModificationsPaginated(input: DescribeReservedInstancesModificationsInput) -> ClientRuntime.PaginatorSequence<DescribeReservedInstancesModificationsInput, DescribeReservedInstancesModificationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReservedInstancesModificationsInput, DescribeReservedInstancesModificationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeReservedInstancesModifications(input:))
    }
}

extension DescribeReservedInstancesModificationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReservedInstancesModificationsInput {
        return DescribeReservedInstancesModificationsInput(
            filters: self.filters,
            nextToken: token,
            reservedInstancesModificationIds: self.reservedInstancesModificationIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeReservedInstancesModificationsInput, OperationStackOutput == DescribeReservedInstancesModificationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeReservedInstancesModificationsPaginated`
    /// to access the nested member `[EC2ClientTypes.ReservedInstancesModification]`
    /// - Returns: `[EC2ClientTypes.ReservedInstancesModification]`
    public func reservedInstancesModifications() async throws -> [EC2ClientTypes.ReservedInstancesModification] {
        return try await self.asyncCompactMap { item in item.reservedInstancesModifications }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeReservedInstancesOfferingsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReservedInstancesOfferingsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReservedInstancesOfferingsOutput`
    public func describeReservedInstancesOfferingsPaginated(input: DescribeReservedInstancesOfferingsInput) -> ClientRuntime.PaginatorSequence<DescribeReservedInstancesOfferingsInput, DescribeReservedInstancesOfferingsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReservedInstancesOfferingsInput, DescribeReservedInstancesOfferingsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeReservedInstancesOfferings(input:))
    }
}

extension DescribeReservedInstancesOfferingsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReservedInstancesOfferingsInput {
        return DescribeReservedInstancesOfferingsInput(
            availabilityZone: self.availabilityZone,
            dryRun: self.dryRun,
            filters: self.filters,
            includeMarketplace: self.includeMarketplace,
            instanceTenancy: self.instanceTenancy,
            instanceType: self.instanceType,
            maxDuration: self.maxDuration,
            maxInstanceCount: self.maxInstanceCount,
            maxResults: self.maxResults,
            minDuration: self.minDuration,
            nextToken: token,
            offeringClass: self.offeringClass,
            offeringType: self.offeringType,
            productDescription: self.productDescription,
            reservedInstancesOfferingIds: self.reservedInstancesOfferingIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeReservedInstancesOfferingsInput, OperationStackOutput == DescribeReservedInstancesOfferingsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeReservedInstancesOfferingsPaginated`
    /// to access the nested member `[EC2ClientTypes.ReservedInstancesOffering]`
    /// - Returns: `[EC2ClientTypes.ReservedInstancesOffering]`
    public func reservedInstancesOfferings() async throws -> [EC2ClientTypes.ReservedInstancesOffering] {
        return try await self.asyncCompactMap { item in item.reservedInstancesOfferings }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeRouteTablesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeRouteTablesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeRouteTablesOutput`
    public func describeRouteTablesPaginated(input: DescribeRouteTablesInput) -> ClientRuntime.PaginatorSequence<DescribeRouteTablesInput, DescribeRouteTablesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeRouteTablesInput, DescribeRouteTablesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeRouteTables(input:))
    }
}

extension DescribeRouteTablesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeRouteTablesInput {
        return DescribeRouteTablesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            routeTableIds: self.routeTableIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeRouteTablesInput, OperationStackOutput == DescribeRouteTablesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeRouteTablesPaginated`
    /// to access the nested member `[EC2ClientTypes.RouteTable]`
    /// - Returns: `[EC2ClientTypes.RouteTable]`
    public func routeTables() async throws -> [EC2ClientTypes.RouteTable] {
        return try await self.asyncCompactMap { item in item.routeTables }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeScheduledInstanceAvailabilityOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeScheduledInstanceAvailabilityInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeScheduledInstanceAvailabilityOutput`
    public func describeScheduledInstanceAvailabilityPaginated(input: DescribeScheduledInstanceAvailabilityInput) -> ClientRuntime.PaginatorSequence<DescribeScheduledInstanceAvailabilityInput, DescribeScheduledInstanceAvailabilityOutput> {
        return ClientRuntime.PaginatorSequence<DescribeScheduledInstanceAvailabilityInput, DescribeScheduledInstanceAvailabilityOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeScheduledInstanceAvailability(input:))
    }
}

extension DescribeScheduledInstanceAvailabilityInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeScheduledInstanceAvailabilityInput {
        return DescribeScheduledInstanceAvailabilityInput(
            dryRun: self.dryRun,
            filters: self.filters,
            firstSlotStartTimeRange: self.firstSlotStartTimeRange,
            maxResults: self.maxResults,
            maxSlotDurationInHours: self.maxSlotDurationInHours,
            minSlotDurationInHours: self.minSlotDurationInHours,
            nextToken: token,
            recurrence: self.recurrence
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeScheduledInstanceAvailabilityInput, OperationStackOutput == DescribeScheduledInstanceAvailabilityOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeScheduledInstanceAvailabilityPaginated`
    /// to access the nested member `[EC2ClientTypes.ScheduledInstanceAvailability]`
    /// - Returns: `[EC2ClientTypes.ScheduledInstanceAvailability]`
    public func scheduledInstanceAvailabilitySet() async throws -> [EC2ClientTypes.ScheduledInstanceAvailability] {
        return try await self.asyncCompactMap { item in item.scheduledInstanceAvailabilitySet }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeScheduledInstancesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeScheduledInstancesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeScheduledInstancesOutput`
    public func describeScheduledInstancesPaginated(input: DescribeScheduledInstancesInput) -> ClientRuntime.PaginatorSequence<DescribeScheduledInstancesInput, DescribeScheduledInstancesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeScheduledInstancesInput, DescribeScheduledInstancesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeScheduledInstances(input:))
    }
}

extension DescribeScheduledInstancesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeScheduledInstancesInput {
        return DescribeScheduledInstancesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            scheduledInstanceIds: self.scheduledInstanceIds,
            slotStartTimeRange: self.slotStartTimeRange
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeScheduledInstancesInput, OperationStackOutput == DescribeScheduledInstancesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeScheduledInstancesPaginated`
    /// to access the nested member `[EC2ClientTypes.ScheduledInstance]`
    /// - Returns: `[EC2ClientTypes.ScheduledInstance]`
    public func scheduledInstanceSet() async throws -> [EC2ClientTypes.ScheduledInstance] {
        return try await self.asyncCompactMap { item in item.scheduledInstanceSet }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeSecurityGroupRulesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeSecurityGroupRulesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeSecurityGroupRulesOutput`
    public func describeSecurityGroupRulesPaginated(input: DescribeSecurityGroupRulesInput) -> ClientRuntime.PaginatorSequence<DescribeSecurityGroupRulesInput, DescribeSecurityGroupRulesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeSecurityGroupRulesInput, DescribeSecurityGroupRulesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeSecurityGroupRules(input:))
    }
}

extension DescribeSecurityGroupRulesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeSecurityGroupRulesInput {
        return DescribeSecurityGroupRulesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            securityGroupRuleIds: self.securityGroupRuleIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeSecurityGroupRulesInput, OperationStackOutput == DescribeSecurityGroupRulesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeSecurityGroupRulesPaginated`
    /// to access the nested member `[EC2ClientTypes.SecurityGroupRule]`
    /// - Returns: `[EC2ClientTypes.SecurityGroupRule]`
    public func securityGroupRules() async throws -> [EC2ClientTypes.SecurityGroupRule] {
        return try await self.asyncCompactMap { item in item.securityGroupRules }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeSecurityGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeSecurityGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeSecurityGroupsOutput`
    public func describeSecurityGroupsPaginated(input: DescribeSecurityGroupsInput) -> ClientRuntime.PaginatorSequence<DescribeSecurityGroupsInput, DescribeSecurityGroupsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeSecurityGroupsInput, DescribeSecurityGroupsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeSecurityGroups(input:))
    }
}

extension DescribeSecurityGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeSecurityGroupsInput {
        return DescribeSecurityGroupsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            groupIds: self.groupIds,
            groupNames: self.groupNames,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeSecurityGroupsInput, OperationStackOutput == DescribeSecurityGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeSecurityGroupsPaginated`
    /// to access the nested member `[EC2ClientTypes.SecurityGroup]`
    /// - Returns: `[EC2ClientTypes.SecurityGroup]`
    public func securityGroups() async throws -> [EC2ClientTypes.SecurityGroup] {
        return try await self.asyncCompactMap { item in item.securityGroups }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeSecurityGroupVpcAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeSecurityGroupVpcAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeSecurityGroupVpcAssociationsOutput`
    public func describeSecurityGroupVpcAssociationsPaginated(input: DescribeSecurityGroupVpcAssociationsInput) -> ClientRuntime.PaginatorSequence<DescribeSecurityGroupVpcAssociationsInput, DescribeSecurityGroupVpcAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeSecurityGroupVpcAssociationsInput, DescribeSecurityGroupVpcAssociationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeSecurityGroupVpcAssociations(input:))
    }
}

extension DescribeSecurityGroupVpcAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeSecurityGroupVpcAssociationsInput {
        return DescribeSecurityGroupVpcAssociationsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeSecurityGroupVpcAssociationsInput, OperationStackOutput == DescribeSecurityGroupVpcAssociationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeSecurityGroupVpcAssociationsPaginated`
    /// to access the nested member `[EC2ClientTypes.SecurityGroupVpcAssociation]`
    /// - Returns: `[EC2ClientTypes.SecurityGroupVpcAssociation]`
    public func securityGroupVpcAssociations() async throws -> [EC2ClientTypes.SecurityGroupVpcAssociation] {
        return try await self.asyncCompactMap { item in item.securityGroupVpcAssociations }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeSnapshotsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeSnapshotsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeSnapshotsOutput`
    public func describeSnapshotsPaginated(input: DescribeSnapshotsInput) -> ClientRuntime.PaginatorSequence<DescribeSnapshotsInput, DescribeSnapshotsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeSnapshotsInput, DescribeSnapshotsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeSnapshots(input:))
    }
}

extension DescribeSnapshotsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeSnapshotsInput {
        return DescribeSnapshotsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            ownerIds: self.ownerIds,
            restorableByUserIds: self.restorableByUserIds,
            snapshotIds: self.snapshotIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeSnapshotsInput, OperationStackOutput == DescribeSnapshotsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeSnapshotsPaginated`
    /// to access the nested member `[EC2ClientTypes.Snapshot]`
    /// - Returns: `[EC2ClientTypes.Snapshot]`
    public func snapshots() async throws -> [EC2ClientTypes.Snapshot] {
        return try await self.asyncCompactMap { item in item.snapshots }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeSnapshotTierStatusOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeSnapshotTierStatusInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeSnapshotTierStatusOutput`
    public func describeSnapshotTierStatusPaginated(input: DescribeSnapshotTierStatusInput) -> ClientRuntime.PaginatorSequence<DescribeSnapshotTierStatusInput, DescribeSnapshotTierStatusOutput> {
        return ClientRuntime.PaginatorSequence<DescribeSnapshotTierStatusInput, DescribeSnapshotTierStatusOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeSnapshotTierStatus(input:))
    }
}

extension DescribeSnapshotTierStatusInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeSnapshotTierStatusInput {
        return DescribeSnapshotTierStatusInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeSnapshotTierStatusInput, OperationStackOutput == DescribeSnapshotTierStatusOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeSnapshotTierStatusPaginated`
    /// to access the nested member `[EC2ClientTypes.SnapshotTierStatus]`
    /// - Returns: `[EC2ClientTypes.SnapshotTierStatus]`
    public func snapshotTierStatuses() async throws -> [EC2ClientTypes.SnapshotTierStatus] {
        return try await self.asyncCompactMap { item in item.snapshotTierStatuses }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeSpotFleetRequestsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeSpotFleetRequestsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeSpotFleetRequestsOutput`
    public func describeSpotFleetRequestsPaginated(input: DescribeSpotFleetRequestsInput) -> ClientRuntime.PaginatorSequence<DescribeSpotFleetRequestsInput, DescribeSpotFleetRequestsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeSpotFleetRequestsInput, DescribeSpotFleetRequestsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeSpotFleetRequests(input:))
    }
}

extension DescribeSpotFleetRequestsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeSpotFleetRequestsInput {
        return DescribeSpotFleetRequestsInput(
            dryRun: self.dryRun,
            maxResults: self.maxResults,
            nextToken: token,
            spotFleetRequestIds: self.spotFleetRequestIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeSpotFleetRequestsInput, OperationStackOutput == DescribeSpotFleetRequestsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeSpotFleetRequestsPaginated`
    /// to access the nested member `[EC2ClientTypes.SpotFleetRequestConfig]`
    /// - Returns: `[EC2ClientTypes.SpotFleetRequestConfig]`
    public func spotFleetRequestConfigs() async throws -> [EC2ClientTypes.SpotFleetRequestConfig] {
        return try await self.asyncCompactMap { item in item.spotFleetRequestConfigs }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeSpotInstanceRequestsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeSpotInstanceRequestsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeSpotInstanceRequestsOutput`
    public func describeSpotInstanceRequestsPaginated(input: DescribeSpotInstanceRequestsInput) -> ClientRuntime.PaginatorSequence<DescribeSpotInstanceRequestsInput, DescribeSpotInstanceRequestsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeSpotInstanceRequestsInput, DescribeSpotInstanceRequestsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeSpotInstanceRequests(input:))
    }
}

extension DescribeSpotInstanceRequestsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeSpotInstanceRequestsInput {
        return DescribeSpotInstanceRequestsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            spotInstanceRequestIds: self.spotInstanceRequestIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeSpotInstanceRequestsInput, OperationStackOutput == DescribeSpotInstanceRequestsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeSpotInstanceRequestsPaginated`
    /// to access the nested member `[EC2ClientTypes.SpotInstanceRequest]`
    /// - Returns: `[EC2ClientTypes.SpotInstanceRequest]`
    public func spotInstanceRequests() async throws -> [EC2ClientTypes.SpotInstanceRequest] {
        return try await self.asyncCompactMap { item in item.spotInstanceRequests }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeSpotPriceHistoryOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeSpotPriceHistoryInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeSpotPriceHistoryOutput`
    public func describeSpotPriceHistoryPaginated(input: DescribeSpotPriceHistoryInput) -> ClientRuntime.PaginatorSequence<DescribeSpotPriceHistoryInput, DescribeSpotPriceHistoryOutput> {
        return ClientRuntime.PaginatorSequence<DescribeSpotPriceHistoryInput, DescribeSpotPriceHistoryOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeSpotPriceHistory(input:))
    }
}

extension DescribeSpotPriceHistoryInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeSpotPriceHistoryInput {
        return DescribeSpotPriceHistoryInput(
            availabilityZone: self.availabilityZone,
            dryRun: self.dryRun,
            endTime: self.endTime,
            filters: self.filters,
            instanceTypes: self.instanceTypes,
            maxResults: self.maxResults,
            nextToken: token,
            productDescriptions: self.productDescriptions,
            startTime: self.startTime
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeSpotPriceHistoryInput, OperationStackOutput == DescribeSpotPriceHistoryOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeSpotPriceHistoryPaginated`
    /// to access the nested member `[EC2ClientTypes.SpotPrice]`
    /// - Returns: `[EC2ClientTypes.SpotPrice]`
    public func spotPriceHistory() async throws -> [EC2ClientTypes.SpotPrice] {
        return try await self.asyncCompactMap { item in item.spotPriceHistory }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeStaleSecurityGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeStaleSecurityGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeStaleSecurityGroupsOutput`
    public func describeStaleSecurityGroupsPaginated(input: DescribeStaleSecurityGroupsInput) -> ClientRuntime.PaginatorSequence<DescribeStaleSecurityGroupsInput, DescribeStaleSecurityGroupsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeStaleSecurityGroupsInput, DescribeStaleSecurityGroupsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeStaleSecurityGroups(input:))
    }
}

extension DescribeStaleSecurityGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeStaleSecurityGroupsInput {
        return DescribeStaleSecurityGroupsInput(
            dryRun: self.dryRun,
            maxResults: self.maxResults,
            nextToken: token,
            vpcId: self.vpcId
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeStaleSecurityGroupsInput, OperationStackOutput == DescribeStaleSecurityGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeStaleSecurityGroupsPaginated`
    /// to access the nested member `[EC2ClientTypes.StaleSecurityGroup]`
    /// - Returns: `[EC2ClientTypes.StaleSecurityGroup]`
    public func staleSecurityGroupSet() async throws -> [EC2ClientTypes.StaleSecurityGroup] {
        return try await self.asyncCompactMap { item in item.staleSecurityGroupSet }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeStoreImageTasksOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeStoreImageTasksInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeStoreImageTasksOutput`
    public func describeStoreImageTasksPaginated(input: DescribeStoreImageTasksInput) -> ClientRuntime.PaginatorSequence<DescribeStoreImageTasksInput, DescribeStoreImageTasksOutput> {
        return ClientRuntime.PaginatorSequence<DescribeStoreImageTasksInput, DescribeStoreImageTasksOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeStoreImageTasks(input:))
    }
}

extension DescribeStoreImageTasksInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeStoreImageTasksInput {
        return DescribeStoreImageTasksInput(
            dryRun: self.dryRun,
            filters: self.filters,
            imageIds: self.imageIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeStoreImageTasksInput, OperationStackOutput == DescribeStoreImageTasksOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeStoreImageTasksPaginated`
    /// to access the nested member `[EC2ClientTypes.StoreImageTaskResult]`
    /// - Returns: `[EC2ClientTypes.StoreImageTaskResult]`
    public func storeImageTaskResults() async throws -> [EC2ClientTypes.StoreImageTaskResult] {
        return try await self.asyncCompactMap { item in item.storeImageTaskResults }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeSubnetsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeSubnetsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeSubnetsOutput`
    public func describeSubnetsPaginated(input: DescribeSubnetsInput) -> ClientRuntime.PaginatorSequence<DescribeSubnetsInput, DescribeSubnetsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeSubnetsInput, DescribeSubnetsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeSubnets(input:))
    }
}

extension DescribeSubnetsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeSubnetsInput {
        return DescribeSubnetsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            subnetIds: self.subnetIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeSubnetsInput, OperationStackOutput == DescribeSubnetsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeSubnetsPaginated`
    /// to access the nested member `[EC2ClientTypes.Subnet]`
    /// - Returns: `[EC2ClientTypes.Subnet]`
    public func subnets() async throws -> [EC2ClientTypes.Subnet] {
        return try await self.asyncCompactMap { item in item.subnets }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTagsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTagsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTagsOutput`
    public func describeTagsPaginated(input: DescribeTagsInput) -> ClientRuntime.PaginatorSequence<DescribeTagsInput, DescribeTagsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTagsInput, DescribeTagsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTags(input:))
    }
}

extension DescribeTagsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTagsInput {
        return DescribeTagsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTagsInput, OperationStackOutput == DescribeTagsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTagsPaginated`
    /// to access the nested member `[EC2ClientTypes.TagDescription]`
    /// - Returns: `[EC2ClientTypes.TagDescription]`
    public func tags() async throws -> [EC2ClientTypes.TagDescription] {
        return try await self.asyncCompactMap { item in item.tags }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTrafficMirrorFiltersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTrafficMirrorFiltersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTrafficMirrorFiltersOutput`
    public func describeTrafficMirrorFiltersPaginated(input: DescribeTrafficMirrorFiltersInput) -> ClientRuntime.PaginatorSequence<DescribeTrafficMirrorFiltersInput, DescribeTrafficMirrorFiltersOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTrafficMirrorFiltersInput, DescribeTrafficMirrorFiltersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTrafficMirrorFilters(input:))
    }
}

extension DescribeTrafficMirrorFiltersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTrafficMirrorFiltersInput {
        return DescribeTrafficMirrorFiltersInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            trafficMirrorFilterIds: self.trafficMirrorFilterIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTrafficMirrorFiltersInput, OperationStackOutput == DescribeTrafficMirrorFiltersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTrafficMirrorFiltersPaginated`
    /// to access the nested member `[EC2ClientTypes.TrafficMirrorFilter]`
    /// - Returns: `[EC2ClientTypes.TrafficMirrorFilter]`
    public func trafficMirrorFilters() async throws -> [EC2ClientTypes.TrafficMirrorFilter] {
        return try await self.asyncCompactMap { item in item.trafficMirrorFilters }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTrafficMirrorSessionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTrafficMirrorSessionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTrafficMirrorSessionsOutput`
    public func describeTrafficMirrorSessionsPaginated(input: DescribeTrafficMirrorSessionsInput) -> ClientRuntime.PaginatorSequence<DescribeTrafficMirrorSessionsInput, DescribeTrafficMirrorSessionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTrafficMirrorSessionsInput, DescribeTrafficMirrorSessionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTrafficMirrorSessions(input:))
    }
}

extension DescribeTrafficMirrorSessionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTrafficMirrorSessionsInput {
        return DescribeTrafficMirrorSessionsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            trafficMirrorSessionIds: self.trafficMirrorSessionIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTrafficMirrorSessionsInput, OperationStackOutput == DescribeTrafficMirrorSessionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTrafficMirrorSessionsPaginated`
    /// to access the nested member `[EC2ClientTypes.TrafficMirrorSession]`
    /// - Returns: `[EC2ClientTypes.TrafficMirrorSession]`
    public func trafficMirrorSessions() async throws -> [EC2ClientTypes.TrafficMirrorSession] {
        return try await self.asyncCompactMap { item in item.trafficMirrorSessions }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTrafficMirrorTargetsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTrafficMirrorTargetsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTrafficMirrorTargetsOutput`
    public func describeTrafficMirrorTargetsPaginated(input: DescribeTrafficMirrorTargetsInput) -> ClientRuntime.PaginatorSequence<DescribeTrafficMirrorTargetsInput, DescribeTrafficMirrorTargetsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTrafficMirrorTargetsInput, DescribeTrafficMirrorTargetsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTrafficMirrorTargets(input:))
    }
}

extension DescribeTrafficMirrorTargetsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTrafficMirrorTargetsInput {
        return DescribeTrafficMirrorTargetsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            trafficMirrorTargetIds: self.trafficMirrorTargetIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTrafficMirrorTargetsInput, OperationStackOutput == DescribeTrafficMirrorTargetsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTrafficMirrorTargetsPaginated`
    /// to access the nested member `[EC2ClientTypes.TrafficMirrorTarget]`
    /// - Returns: `[EC2ClientTypes.TrafficMirrorTarget]`
    public func trafficMirrorTargets() async throws -> [EC2ClientTypes.TrafficMirrorTarget] {
        return try await self.asyncCompactMap { item in item.trafficMirrorTargets }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTransitGatewayAttachmentsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTransitGatewayAttachmentsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTransitGatewayAttachmentsOutput`
    public func describeTransitGatewayAttachmentsPaginated(input: DescribeTransitGatewayAttachmentsInput) -> ClientRuntime.PaginatorSequence<DescribeTransitGatewayAttachmentsInput, DescribeTransitGatewayAttachmentsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTransitGatewayAttachmentsInput, DescribeTransitGatewayAttachmentsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTransitGatewayAttachments(input:))
    }
}

extension DescribeTransitGatewayAttachmentsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTransitGatewayAttachmentsInput {
        return DescribeTransitGatewayAttachmentsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayAttachmentIds: self.transitGatewayAttachmentIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTransitGatewayAttachmentsInput, OperationStackOutput == DescribeTransitGatewayAttachmentsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTransitGatewayAttachmentsPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayAttachment]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayAttachment]`
    public func transitGatewayAttachments() async throws -> [EC2ClientTypes.TransitGatewayAttachment] {
        return try await self.asyncCompactMap { item in item.transitGatewayAttachments }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTransitGatewayConnectPeersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTransitGatewayConnectPeersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTransitGatewayConnectPeersOutput`
    public func describeTransitGatewayConnectPeersPaginated(input: DescribeTransitGatewayConnectPeersInput) -> ClientRuntime.PaginatorSequence<DescribeTransitGatewayConnectPeersInput, DescribeTransitGatewayConnectPeersOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTransitGatewayConnectPeersInput, DescribeTransitGatewayConnectPeersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTransitGatewayConnectPeers(input:))
    }
}

extension DescribeTransitGatewayConnectPeersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTransitGatewayConnectPeersInput {
        return DescribeTransitGatewayConnectPeersInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayConnectPeerIds: self.transitGatewayConnectPeerIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTransitGatewayConnectPeersInput, OperationStackOutput == DescribeTransitGatewayConnectPeersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTransitGatewayConnectPeersPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayConnectPeer]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayConnectPeer]`
    public func transitGatewayConnectPeers() async throws -> [EC2ClientTypes.TransitGatewayConnectPeer] {
        return try await self.asyncCompactMap { item in item.transitGatewayConnectPeers }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTransitGatewayConnectsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTransitGatewayConnectsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTransitGatewayConnectsOutput`
    public func describeTransitGatewayConnectsPaginated(input: DescribeTransitGatewayConnectsInput) -> ClientRuntime.PaginatorSequence<DescribeTransitGatewayConnectsInput, DescribeTransitGatewayConnectsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTransitGatewayConnectsInput, DescribeTransitGatewayConnectsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTransitGatewayConnects(input:))
    }
}

extension DescribeTransitGatewayConnectsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTransitGatewayConnectsInput {
        return DescribeTransitGatewayConnectsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayAttachmentIds: self.transitGatewayAttachmentIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTransitGatewayConnectsInput, OperationStackOutput == DescribeTransitGatewayConnectsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTransitGatewayConnectsPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayConnect]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayConnect]`
    public func transitGatewayConnects() async throws -> [EC2ClientTypes.TransitGatewayConnect] {
        return try await self.asyncCompactMap { item in item.transitGatewayConnects }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTransitGatewayMulticastDomainsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTransitGatewayMulticastDomainsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTransitGatewayMulticastDomainsOutput`
    public func describeTransitGatewayMulticastDomainsPaginated(input: DescribeTransitGatewayMulticastDomainsInput) -> ClientRuntime.PaginatorSequence<DescribeTransitGatewayMulticastDomainsInput, DescribeTransitGatewayMulticastDomainsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTransitGatewayMulticastDomainsInput, DescribeTransitGatewayMulticastDomainsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTransitGatewayMulticastDomains(input:))
    }
}

extension DescribeTransitGatewayMulticastDomainsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTransitGatewayMulticastDomainsInput {
        return DescribeTransitGatewayMulticastDomainsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayMulticastDomainIds: self.transitGatewayMulticastDomainIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTransitGatewayMulticastDomainsInput, OperationStackOutput == DescribeTransitGatewayMulticastDomainsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTransitGatewayMulticastDomainsPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayMulticastDomain]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayMulticastDomain]`
    public func transitGatewayMulticastDomains() async throws -> [EC2ClientTypes.TransitGatewayMulticastDomain] {
        return try await self.asyncCompactMap { item in item.transitGatewayMulticastDomains }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTransitGatewayPeeringAttachmentsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTransitGatewayPeeringAttachmentsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTransitGatewayPeeringAttachmentsOutput`
    public func describeTransitGatewayPeeringAttachmentsPaginated(input: DescribeTransitGatewayPeeringAttachmentsInput) -> ClientRuntime.PaginatorSequence<DescribeTransitGatewayPeeringAttachmentsInput, DescribeTransitGatewayPeeringAttachmentsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTransitGatewayPeeringAttachmentsInput, DescribeTransitGatewayPeeringAttachmentsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTransitGatewayPeeringAttachments(input:))
    }
}

extension DescribeTransitGatewayPeeringAttachmentsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTransitGatewayPeeringAttachmentsInput {
        return DescribeTransitGatewayPeeringAttachmentsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayAttachmentIds: self.transitGatewayAttachmentIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTransitGatewayPeeringAttachmentsInput, OperationStackOutput == DescribeTransitGatewayPeeringAttachmentsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTransitGatewayPeeringAttachmentsPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayPeeringAttachment]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayPeeringAttachment]`
    public func transitGatewayPeeringAttachments() async throws -> [EC2ClientTypes.TransitGatewayPeeringAttachment] {
        return try await self.asyncCompactMap { item in item.transitGatewayPeeringAttachments }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTransitGatewayPolicyTablesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTransitGatewayPolicyTablesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTransitGatewayPolicyTablesOutput`
    public func describeTransitGatewayPolicyTablesPaginated(input: DescribeTransitGatewayPolicyTablesInput) -> ClientRuntime.PaginatorSequence<DescribeTransitGatewayPolicyTablesInput, DescribeTransitGatewayPolicyTablesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTransitGatewayPolicyTablesInput, DescribeTransitGatewayPolicyTablesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTransitGatewayPolicyTables(input:))
    }
}

extension DescribeTransitGatewayPolicyTablesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTransitGatewayPolicyTablesInput {
        return DescribeTransitGatewayPolicyTablesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayPolicyTableIds: self.transitGatewayPolicyTableIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTransitGatewayPolicyTablesInput, OperationStackOutput == DescribeTransitGatewayPolicyTablesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTransitGatewayPolicyTablesPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayPolicyTable]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayPolicyTable]`
    public func transitGatewayPolicyTables() async throws -> [EC2ClientTypes.TransitGatewayPolicyTable] {
        return try await self.asyncCompactMap { item in item.transitGatewayPolicyTables }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTransitGatewayRouteTableAnnouncementsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTransitGatewayRouteTableAnnouncementsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTransitGatewayRouteTableAnnouncementsOutput`
    public func describeTransitGatewayRouteTableAnnouncementsPaginated(input: DescribeTransitGatewayRouteTableAnnouncementsInput) -> ClientRuntime.PaginatorSequence<DescribeTransitGatewayRouteTableAnnouncementsInput, DescribeTransitGatewayRouteTableAnnouncementsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTransitGatewayRouteTableAnnouncementsInput, DescribeTransitGatewayRouteTableAnnouncementsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTransitGatewayRouteTableAnnouncements(input:))
    }
}

extension DescribeTransitGatewayRouteTableAnnouncementsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTransitGatewayRouteTableAnnouncementsInput {
        return DescribeTransitGatewayRouteTableAnnouncementsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayRouteTableAnnouncementIds: self.transitGatewayRouteTableAnnouncementIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTransitGatewayRouteTableAnnouncementsInput, OperationStackOutput == DescribeTransitGatewayRouteTableAnnouncementsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTransitGatewayRouteTableAnnouncementsPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayRouteTableAnnouncement]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayRouteTableAnnouncement]`
    public func transitGatewayRouteTableAnnouncements() async throws -> [EC2ClientTypes.TransitGatewayRouteTableAnnouncement] {
        return try await self.asyncCompactMap { item in item.transitGatewayRouteTableAnnouncements }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTransitGatewayRouteTablesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTransitGatewayRouteTablesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTransitGatewayRouteTablesOutput`
    public func describeTransitGatewayRouteTablesPaginated(input: DescribeTransitGatewayRouteTablesInput) -> ClientRuntime.PaginatorSequence<DescribeTransitGatewayRouteTablesInput, DescribeTransitGatewayRouteTablesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTransitGatewayRouteTablesInput, DescribeTransitGatewayRouteTablesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTransitGatewayRouteTables(input:))
    }
}

extension DescribeTransitGatewayRouteTablesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTransitGatewayRouteTablesInput {
        return DescribeTransitGatewayRouteTablesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayRouteTableIds: self.transitGatewayRouteTableIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTransitGatewayRouteTablesInput, OperationStackOutput == DescribeTransitGatewayRouteTablesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTransitGatewayRouteTablesPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayRouteTable]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayRouteTable]`
    public func transitGatewayRouteTables() async throws -> [EC2ClientTypes.TransitGatewayRouteTable] {
        return try await self.asyncCompactMap { item in item.transitGatewayRouteTables }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTransitGatewaysOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTransitGatewaysInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTransitGatewaysOutput`
    public func describeTransitGatewaysPaginated(input: DescribeTransitGatewaysInput) -> ClientRuntime.PaginatorSequence<DescribeTransitGatewaysInput, DescribeTransitGatewaysOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTransitGatewaysInput, DescribeTransitGatewaysOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTransitGateways(input:))
    }
}

extension DescribeTransitGatewaysInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTransitGatewaysInput {
        return DescribeTransitGatewaysInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayIds: self.transitGatewayIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTransitGatewaysInput, OperationStackOutput == DescribeTransitGatewaysOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTransitGatewaysPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGateway]`
    /// - Returns: `[EC2ClientTypes.TransitGateway]`
    public func transitGateways() async throws -> [EC2ClientTypes.TransitGateway] {
        return try await self.asyncCompactMap { item in item.transitGateways }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTransitGatewayVpcAttachmentsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTransitGatewayVpcAttachmentsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTransitGatewayVpcAttachmentsOutput`
    public func describeTransitGatewayVpcAttachmentsPaginated(input: DescribeTransitGatewayVpcAttachmentsInput) -> ClientRuntime.PaginatorSequence<DescribeTransitGatewayVpcAttachmentsInput, DescribeTransitGatewayVpcAttachmentsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTransitGatewayVpcAttachmentsInput, DescribeTransitGatewayVpcAttachmentsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTransitGatewayVpcAttachments(input:))
    }
}

extension DescribeTransitGatewayVpcAttachmentsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTransitGatewayVpcAttachmentsInput {
        return DescribeTransitGatewayVpcAttachmentsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayAttachmentIds: self.transitGatewayAttachmentIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTransitGatewayVpcAttachmentsInput, OperationStackOutput == DescribeTransitGatewayVpcAttachmentsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTransitGatewayVpcAttachmentsPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayVpcAttachment]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayVpcAttachment]`
    public func transitGatewayVpcAttachments() async throws -> [EC2ClientTypes.TransitGatewayVpcAttachment] {
        return try await self.asyncCompactMap { item in item.transitGatewayVpcAttachments }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeTrunkInterfaceAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTrunkInterfaceAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTrunkInterfaceAssociationsOutput`
    public func describeTrunkInterfaceAssociationsPaginated(input: DescribeTrunkInterfaceAssociationsInput) -> ClientRuntime.PaginatorSequence<DescribeTrunkInterfaceAssociationsInput, DescribeTrunkInterfaceAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTrunkInterfaceAssociationsInput, DescribeTrunkInterfaceAssociationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeTrunkInterfaceAssociations(input:))
    }
}

extension DescribeTrunkInterfaceAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTrunkInterfaceAssociationsInput {
        return DescribeTrunkInterfaceAssociationsInput(
            associationIds: self.associationIds,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeTrunkInterfaceAssociationsInput, OperationStackOutput == DescribeTrunkInterfaceAssociationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeTrunkInterfaceAssociationsPaginated`
    /// to access the nested member `[EC2ClientTypes.TrunkInterfaceAssociation]`
    /// - Returns: `[EC2ClientTypes.TrunkInterfaceAssociation]`
    public func interfaceAssociations() async throws -> [EC2ClientTypes.TrunkInterfaceAssociation] {
        return try await self.asyncCompactMap { item in item.interfaceAssociations }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVerifiedAccessEndpointsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVerifiedAccessEndpointsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVerifiedAccessEndpointsOutput`
    public func describeVerifiedAccessEndpointsPaginated(input: DescribeVerifiedAccessEndpointsInput) -> ClientRuntime.PaginatorSequence<DescribeVerifiedAccessEndpointsInput, DescribeVerifiedAccessEndpointsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVerifiedAccessEndpointsInput, DescribeVerifiedAccessEndpointsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVerifiedAccessEndpoints(input:))
    }
}

extension DescribeVerifiedAccessEndpointsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVerifiedAccessEndpointsInput {
        return DescribeVerifiedAccessEndpointsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            verifiedAccessEndpointIds: self.verifiedAccessEndpointIds,
            verifiedAccessGroupId: self.verifiedAccessGroupId,
            verifiedAccessInstanceId: self.verifiedAccessInstanceId
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVerifiedAccessEndpointsInput, OperationStackOutput == DescribeVerifiedAccessEndpointsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVerifiedAccessEndpointsPaginated`
    /// to access the nested member `[EC2ClientTypes.VerifiedAccessEndpoint]`
    /// - Returns: `[EC2ClientTypes.VerifiedAccessEndpoint]`
    public func verifiedAccessEndpoints() async throws -> [EC2ClientTypes.VerifiedAccessEndpoint] {
        return try await self.asyncCompactMap { item in item.verifiedAccessEndpoints }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVerifiedAccessGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVerifiedAccessGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVerifiedAccessGroupsOutput`
    public func describeVerifiedAccessGroupsPaginated(input: DescribeVerifiedAccessGroupsInput) -> ClientRuntime.PaginatorSequence<DescribeVerifiedAccessGroupsInput, DescribeVerifiedAccessGroupsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVerifiedAccessGroupsInput, DescribeVerifiedAccessGroupsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVerifiedAccessGroups(input:))
    }
}

extension DescribeVerifiedAccessGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVerifiedAccessGroupsInput {
        return DescribeVerifiedAccessGroupsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            verifiedAccessGroupIds: self.verifiedAccessGroupIds,
            verifiedAccessInstanceId: self.verifiedAccessInstanceId
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVerifiedAccessGroupsInput, OperationStackOutput == DescribeVerifiedAccessGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVerifiedAccessGroupsPaginated`
    /// to access the nested member `[EC2ClientTypes.VerifiedAccessGroup]`
    /// - Returns: `[EC2ClientTypes.VerifiedAccessGroup]`
    public func verifiedAccessGroups() async throws -> [EC2ClientTypes.VerifiedAccessGroup] {
        return try await self.asyncCompactMap { item in item.verifiedAccessGroups }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVerifiedAccessInstanceLoggingConfigurationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVerifiedAccessInstanceLoggingConfigurationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVerifiedAccessInstanceLoggingConfigurationsOutput`
    public func describeVerifiedAccessInstanceLoggingConfigurationsPaginated(input: DescribeVerifiedAccessInstanceLoggingConfigurationsInput) -> ClientRuntime.PaginatorSequence<DescribeVerifiedAccessInstanceLoggingConfigurationsInput, DescribeVerifiedAccessInstanceLoggingConfigurationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVerifiedAccessInstanceLoggingConfigurationsInput, DescribeVerifiedAccessInstanceLoggingConfigurationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVerifiedAccessInstanceLoggingConfigurations(input:))
    }
}

extension DescribeVerifiedAccessInstanceLoggingConfigurationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVerifiedAccessInstanceLoggingConfigurationsInput {
        return DescribeVerifiedAccessInstanceLoggingConfigurationsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            verifiedAccessInstanceIds: self.verifiedAccessInstanceIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVerifiedAccessInstanceLoggingConfigurationsInput, OperationStackOutput == DescribeVerifiedAccessInstanceLoggingConfigurationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVerifiedAccessInstanceLoggingConfigurationsPaginated`
    /// to access the nested member `[EC2ClientTypes.VerifiedAccessInstanceLoggingConfiguration]`
    /// - Returns: `[EC2ClientTypes.VerifiedAccessInstanceLoggingConfiguration]`
    public func loggingConfigurations() async throws -> [EC2ClientTypes.VerifiedAccessInstanceLoggingConfiguration] {
        return try await self.asyncCompactMap { item in item.loggingConfigurations }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVerifiedAccessInstancesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVerifiedAccessInstancesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVerifiedAccessInstancesOutput`
    public func describeVerifiedAccessInstancesPaginated(input: DescribeVerifiedAccessInstancesInput) -> ClientRuntime.PaginatorSequence<DescribeVerifiedAccessInstancesInput, DescribeVerifiedAccessInstancesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVerifiedAccessInstancesInput, DescribeVerifiedAccessInstancesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVerifiedAccessInstances(input:))
    }
}

extension DescribeVerifiedAccessInstancesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVerifiedAccessInstancesInput {
        return DescribeVerifiedAccessInstancesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            verifiedAccessInstanceIds: self.verifiedAccessInstanceIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVerifiedAccessInstancesInput, OperationStackOutput == DescribeVerifiedAccessInstancesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVerifiedAccessInstancesPaginated`
    /// to access the nested member `[EC2ClientTypes.VerifiedAccessInstance]`
    /// - Returns: `[EC2ClientTypes.VerifiedAccessInstance]`
    public func verifiedAccessInstances() async throws -> [EC2ClientTypes.VerifiedAccessInstance] {
        return try await self.asyncCompactMap { item in item.verifiedAccessInstances }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVerifiedAccessTrustProvidersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVerifiedAccessTrustProvidersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVerifiedAccessTrustProvidersOutput`
    public func describeVerifiedAccessTrustProvidersPaginated(input: DescribeVerifiedAccessTrustProvidersInput) -> ClientRuntime.PaginatorSequence<DescribeVerifiedAccessTrustProvidersInput, DescribeVerifiedAccessTrustProvidersOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVerifiedAccessTrustProvidersInput, DescribeVerifiedAccessTrustProvidersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVerifiedAccessTrustProviders(input:))
    }
}

extension DescribeVerifiedAccessTrustProvidersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVerifiedAccessTrustProvidersInput {
        return DescribeVerifiedAccessTrustProvidersInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            verifiedAccessTrustProviderIds: self.verifiedAccessTrustProviderIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVerifiedAccessTrustProvidersInput, OperationStackOutput == DescribeVerifiedAccessTrustProvidersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVerifiedAccessTrustProvidersPaginated`
    /// to access the nested member `[EC2ClientTypes.VerifiedAccessTrustProvider]`
    /// - Returns: `[EC2ClientTypes.VerifiedAccessTrustProvider]`
    public func verifiedAccessTrustProviders() async throws -> [EC2ClientTypes.VerifiedAccessTrustProvider] {
        return try await self.asyncCompactMap { item in item.verifiedAccessTrustProviders }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVolumesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVolumesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVolumesOutput`
    public func describeVolumesPaginated(input: DescribeVolumesInput) -> ClientRuntime.PaginatorSequence<DescribeVolumesInput, DescribeVolumesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVolumesInput, DescribeVolumesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVolumes(input:))
    }
}

extension DescribeVolumesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVolumesInput {
        return DescribeVolumesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            volumeIds: self.volumeIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVolumesInput, OperationStackOutput == DescribeVolumesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVolumesPaginated`
    /// to access the nested member `[EC2ClientTypes.Volume]`
    /// - Returns: `[EC2ClientTypes.Volume]`
    public func volumes() async throws -> [EC2ClientTypes.Volume] {
        return try await self.asyncCompactMap { item in item.volumes }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVolumesModificationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVolumesModificationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVolumesModificationsOutput`
    public func describeVolumesModificationsPaginated(input: DescribeVolumesModificationsInput) -> ClientRuntime.PaginatorSequence<DescribeVolumesModificationsInput, DescribeVolumesModificationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVolumesModificationsInput, DescribeVolumesModificationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVolumesModifications(input:))
    }
}

extension DescribeVolumesModificationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVolumesModificationsInput {
        return DescribeVolumesModificationsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            volumeIds: self.volumeIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVolumesModificationsInput, OperationStackOutput == DescribeVolumesModificationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVolumesModificationsPaginated`
    /// to access the nested member `[EC2ClientTypes.VolumeModification]`
    /// - Returns: `[EC2ClientTypes.VolumeModification]`
    public func volumesModifications() async throws -> [EC2ClientTypes.VolumeModification] {
        return try await self.asyncCompactMap { item in item.volumesModifications }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVolumeStatusOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVolumeStatusInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVolumeStatusOutput`
    public func describeVolumeStatusPaginated(input: DescribeVolumeStatusInput) -> ClientRuntime.PaginatorSequence<DescribeVolumeStatusInput, DescribeVolumeStatusOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVolumeStatusInput, DescribeVolumeStatusOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVolumeStatus(input:))
    }
}

extension DescribeVolumeStatusInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVolumeStatusInput {
        return DescribeVolumeStatusInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            volumeIds: self.volumeIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVolumeStatusInput, OperationStackOutput == DescribeVolumeStatusOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVolumeStatusPaginated`
    /// to access the nested member `[EC2ClientTypes.VolumeStatusItem]`
    /// - Returns: `[EC2ClientTypes.VolumeStatusItem]`
    public func volumeStatuses() async throws -> [EC2ClientTypes.VolumeStatusItem] {
        return try await self.asyncCompactMap { item in item.volumeStatuses }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVpcClassicLinkDnsSupportOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVpcClassicLinkDnsSupportInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVpcClassicLinkDnsSupportOutput`
    public func describeVpcClassicLinkDnsSupportPaginated(input: DescribeVpcClassicLinkDnsSupportInput) -> ClientRuntime.PaginatorSequence<DescribeVpcClassicLinkDnsSupportInput, DescribeVpcClassicLinkDnsSupportOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVpcClassicLinkDnsSupportInput, DescribeVpcClassicLinkDnsSupportOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVpcClassicLinkDnsSupport(input:))
    }
}

extension DescribeVpcClassicLinkDnsSupportInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVpcClassicLinkDnsSupportInput {
        return DescribeVpcClassicLinkDnsSupportInput(
            maxResults: self.maxResults,
            nextToken: token,
            vpcIds: self.vpcIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVpcClassicLinkDnsSupportInput, OperationStackOutput == DescribeVpcClassicLinkDnsSupportOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVpcClassicLinkDnsSupportPaginated`
    /// to access the nested member `[EC2ClientTypes.ClassicLinkDnsSupport]`
    /// - Returns: `[EC2ClientTypes.ClassicLinkDnsSupport]`
    public func vpcs() async throws -> [EC2ClientTypes.ClassicLinkDnsSupport] {
        return try await self.asyncCompactMap { item in item.vpcs }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVpcEndpointConnectionNotificationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVpcEndpointConnectionNotificationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVpcEndpointConnectionNotificationsOutput`
    public func describeVpcEndpointConnectionNotificationsPaginated(input: DescribeVpcEndpointConnectionNotificationsInput) -> ClientRuntime.PaginatorSequence<DescribeVpcEndpointConnectionNotificationsInput, DescribeVpcEndpointConnectionNotificationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVpcEndpointConnectionNotificationsInput, DescribeVpcEndpointConnectionNotificationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVpcEndpointConnectionNotifications(input:))
    }
}

extension DescribeVpcEndpointConnectionNotificationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVpcEndpointConnectionNotificationsInput {
        return DescribeVpcEndpointConnectionNotificationsInput(
            connectionNotificationId: self.connectionNotificationId,
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVpcEndpointConnectionNotificationsInput, OperationStackOutput == DescribeVpcEndpointConnectionNotificationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVpcEndpointConnectionNotificationsPaginated`
    /// to access the nested member `[EC2ClientTypes.ConnectionNotification]`
    /// - Returns: `[EC2ClientTypes.ConnectionNotification]`
    public func connectionNotificationSet() async throws -> [EC2ClientTypes.ConnectionNotification] {
        return try await self.asyncCompactMap { item in item.connectionNotificationSet }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVpcEndpointConnectionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVpcEndpointConnectionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVpcEndpointConnectionsOutput`
    public func describeVpcEndpointConnectionsPaginated(input: DescribeVpcEndpointConnectionsInput) -> ClientRuntime.PaginatorSequence<DescribeVpcEndpointConnectionsInput, DescribeVpcEndpointConnectionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVpcEndpointConnectionsInput, DescribeVpcEndpointConnectionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVpcEndpointConnections(input:))
    }
}

extension DescribeVpcEndpointConnectionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVpcEndpointConnectionsInput {
        return DescribeVpcEndpointConnectionsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVpcEndpointConnectionsInput, OperationStackOutput == DescribeVpcEndpointConnectionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVpcEndpointConnectionsPaginated`
    /// to access the nested member `[EC2ClientTypes.VpcEndpointConnection]`
    /// - Returns: `[EC2ClientTypes.VpcEndpointConnection]`
    public func vpcEndpointConnections() async throws -> [EC2ClientTypes.VpcEndpointConnection] {
        return try await self.asyncCompactMap { item in item.vpcEndpointConnections }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVpcEndpointsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVpcEndpointsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVpcEndpointsOutput`
    public func describeVpcEndpointsPaginated(input: DescribeVpcEndpointsInput) -> ClientRuntime.PaginatorSequence<DescribeVpcEndpointsInput, DescribeVpcEndpointsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVpcEndpointsInput, DescribeVpcEndpointsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVpcEndpoints(input:))
    }
}

extension DescribeVpcEndpointsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVpcEndpointsInput {
        return DescribeVpcEndpointsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            vpcEndpointIds: self.vpcEndpointIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVpcEndpointsInput, OperationStackOutput == DescribeVpcEndpointsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVpcEndpointsPaginated`
    /// to access the nested member `[EC2ClientTypes.VpcEndpoint]`
    /// - Returns: `[EC2ClientTypes.VpcEndpoint]`
    public func vpcEndpoints() async throws -> [EC2ClientTypes.VpcEndpoint] {
        return try await self.asyncCompactMap { item in item.vpcEndpoints }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVpcEndpointServiceConfigurationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVpcEndpointServiceConfigurationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVpcEndpointServiceConfigurationsOutput`
    public func describeVpcEndpointServiceConfigurationsPaginated(input: DescribeVpcEndpointServiceConfigurationsInput) -> ClientRuntime.PaginatorSequence<DescribeVpcEndpointServiceConfigurationsInput, DescribeVpcEndpointServiceConfigurationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVpcEndpointServiceConfigurationsInput, DescribeVpcEndpointServiceConfigurationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVpcEndpointServiceConfigurations(input:))
    }
}

extension DescribeVpcEndpointServiceConfigurationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVpcEndpointServiceConfigurationsInput {
        return DescribeVpcEndpointServiceConfigurationsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            serviceIds: self.serviceIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVpcEndpointServiceConfigurationsInput, OperationStackOutput == DescribeVpcEndpointServiceConfigurationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVpcEndpointServiceConfigurationsPaginated`
    /// to access the nested member `[EC2ClientTypes.ServiceConfiguration]`
    /// - Returns: `[EC2ClientTypes.ServiceConfiguration]`
    public func serviceConfigurations() async throws -> [EC2ClientTypes.ServiceConfiguration] {
        return try await self.asyncCompactMap { item in item.serviceConfigurations }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVpcEndpointServicePermissionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVpcEndpointServicePermissionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVpcEndpointServicePermissionsOutput`
    public func describeVpcEndpointServicePermissionsPaginated(input: DescribeVpcEndpointServicePermissionsInput) -> ClientRuntime.PaginatorSequence<DescribeVpcEndpointServicePermissionsInput, DescribeVpcEndpointServicePermissionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVpcEndpointServicePermissionsInput, DescribeVpcEndpointServicePermissionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVpcEndpointServicePermissions(input:))
    }
}

extension DescribeVpcEndpointServicePermissionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVpcEndpointServicePermissionsInput {
        return DescribeVpcEndpointServicePermissionsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            serviceId: self.serviceId
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVpcEndpointServicePermissionsInput, OperationStackOutput == DescribeVpcEndpointServicePermissionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVpcEndpointServicePermissionsPaginated`
    /// to access the nested member `[EC2ClientTypes.AllowedPrincipal]`
    /// - Returns: `[EC2ClientTypes.AllowedPrincipal]`
    public func allowedPrincipals() async throws -> [EC2ClientTypes.AllowedPrincipal] {
        return try await self.asyncCompactMap { item in item.allowedPrincipals }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVpcPeeringConnectionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVpcPeeringConnectionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVpcPeeringConnectionsOutput`
    public func describeVpcPeeringConnectionsPaginated(input: DescribeVpcPeeringConnectionsInput) -> ClientRuntime.PaginatorSequence<DescribeVpcPeeringConnectionsInput, DescribeVpcPeeringConnectionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVpcPeeringConnectionsInput, DescribeVpcPeeringConnectionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVpcPeeringConnections(input:))
    }
}

extension DescribeVpcPeeringConnectionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVpcPeeringConnectionsInput {
        return DescribeVpcPeeringConnectionsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            vpcPeeringConnectionIds: self.vpcPeeringConnectionIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVpcPeeringConnectionsInput, OperationStackOutput == DescribeVpcPeeringConnectionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVpcPeeringConnectionsPaginated`
    /// to access the nested member `[EC2ClientTypes.VpcPeeringConnection]`
    /// - Returns: `[EC2ClientTypes.VpcPeeringConnection]`
    public func vpcPeeringConnections() async throws -> [EC2ClientTypes.VpcPeeringConnection] {
        return try await self.asyncCompactMap { item in item.vpcPeeringConnections }
    }
}
extension EC2Client {
    /// Paginate over `[DescribeVpcsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeVpcsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeVpcsOutput`
    public func describeVpcsPaginated(input: DescribeVpcsInput) -> ClientRuntime.PaginatorSequence<DescribeVpcsInput, DescribeVpcsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeVpcsInput, DescribeVpcsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeVpcs(input:))
    }
}

extension DescribeVpcsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeVpcsInput {
        return DescribeVpcsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            vpcIds: self.vpcIds
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeVpcsInput, OperationStackOutput == DescribeVpcsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeVpcsPaginated`
    /// to access the nested member `[EC2ClientTypes.Vpc]`
    /// - Returns: `[EC2ClientTypes.Vpc]`
    public func vpcs() async throws -> [EC2ClientTypes.Vpc] {
        return try await self.asyncCompactMap { item in item.vpcs }
    }
}
extension EC2Client {
    /// Paginate over `[GetAssociatedIpv6PoolCidrsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetAssociatedIpv6PoolCidrsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetAssociatedIpv6PoolCidrsOutput`
    public func getAssociatedIpv6PoolCidrsPaginated(input: GetAssociatedIpv6PoolCidrsInput) -> ClientRuntime.PaginatorSequence<GetAssociatedIpv6PoolCidrsInput, GetAssociatedIpv6PoolCidrsOutput> {
        return ClientRuntime.PaginatorSequence<GetAssociatedIpv6PoolCidrsInput, GetAssociatedIpv6PoolCidrsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getAssociatedIpv6PoolCidrs(input:))
    }
}

extension GetAssociatedIpv6PoolCidrsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetAssociatedIpv6PoolCidrsInput {
        return GetAssociatedIpv6PoolCidrsInput(
            dryRun: self.dryRun,
            maxResults: self.maxResults,
            nextToken: token,
            poolId: self.poolId
        )}
}

extension PaginatorSequence where OperationStackInput == GetAssociatedIpv6PoolCidrsInput, OperationStackOutput == GetAssociatedIpv6PoolCidrsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getAssociatedIpv6PoolCidrsPaginated`
    /// to access the nested member `[EC2ClientTypes.Ipv6CidrAssociation]`
    /// - Returns: `[EC2ClientTypes.Ipv6CidrAssociation]`
    public func ipv6CidrAssociations() async throws -> [EC2ClientTypes.Ipv6CidrAssociation] {
        return try await self.asyncCompactMap { item in item.ipv6CidrAssociations }
    }
}
extension EC2Client {
    /// Paginate over `[GetAwsNetworkPerformanceDataOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetAwsNetworkPerformanceDataInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetAwsNetworkPerformanceDataOutput`
    public func getAwsNetworkPerformanceDataPaginated(input: GetAwsNetworkPerformanceDataInput) -> ClientRuntime.PaginatorSequence<GetAwsNetworkPerformanceDataInput, GetAwsNetworkPerformanceDataOutput> {
        return ClientRuntime.PaginatorSequence<GetAwsNetworkPerformanceDataInput, GetAwsNetworkPerformanceDataOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getAwsNetworkPerformanceData(input:))
    }
}

extension GetAwsNetworkPerformanceDataInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetAwsNetworkPerformanceDataInput {
        return GetAwsNetworkPerformanceDataInput(
            dataQueries: self.dataQueries,
            dryRun: self.dryRun,
            endTime: self.endTime,
            maxResults: self.maxResults,
            nextToken: token,
            startTime: self.startTime
        )}
}

extension PaginatorSequence where OperationStackInput == GetAwsNetworkPerformanceDataInput, OperationStackOutput == GetAwsNetworkPerformanceDataOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getAwsNetworkPerformanceDataPaginated`
    /// to access the nested member `[EC2ClientTypes.DataResponse]`
    /// - Returns: `[EC2ClientTypes.DataResponse]`
    public func dataResponses() async throws -> [EC2ClientTypes.DataResponse] {
        return try await self.asyncCompactMap { item in item.dataResponses }
    }
}
extension EC2Client {
    /// Paginate over `[GetGroupsForCapacityReservationOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetGroupsForCapacityReservationInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetGroupsForCapacityReservationOutput`
    public func getGroupsForCapacityReservationPaginated(input: GetGroupsForCapacityReservationInput) -> ClientRuntime.PaginatorSequence<GetGroupsForCapacityReservationInput, GetGroupsForCapacityReservationOutput> {
        return ClientRuntime.PaginatorSequence<GetGroupsForCapacityReservationInput, GetGroupsForCapacityReservationOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getGroupsForCapacityReservation(input:))
    }
}

extension GetGroupsForCapacityReservationInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetGroupsForCapacityReservationInput {
        return GetGroupsForCapacityReservationInput(
            capacityReservationId: self.capacityReservationId,
            dryRun: self.dryRun,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == GetGroupsForCapacityReservationInput, OperationStackOutput == GetGroupsForCapacityReservationOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getGroupsForCapacityReservationPaginated`
    /// to access the nested member `[EC2ClientTypes.CapacityReservationGroup]`
    /// - Returns: `[EC2ClientTypes.CapacityReservationGroup]`
    public func capacityReservationGroups() async throws -> [EC2ClientTypes.CapacityReservationGroup] {
        return try await self.asyncCompactMap { item in item.capacityReservationGroups }
    }
}
extension EC2Client {
    /// Paginate over `[GetInstanceTypesFromInstanceRequirementsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetInstanceTypesFromInstanceRequirementsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetInstanceTypesFromInstanceRequirementsOutput`
    public func getInstanceTypesFromInstanceRequirementsPaginated(input: GetInstanceTypesFromInstanceRequirementsInput) -> ClientRuntime.PaginatorSequence<GetInstanceTypesFromInstanceRequirementsInput, GetInstanceTypesFromInstanceRequirementsOutput> {
        return ClientRuntime.PaginatorSequence<GetInstanceTypesFromInstanceRequirementsInput, GetInstanceTypesFromInstanceRequirementsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getInstanceTypesFromInstanceRequirements(input:))
    }
}

extension GetInstanceTypesFromInstanceRequirementsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetInstanceTypesFromInstanceRequirementsInput {
        return GetInstanceTypesFromInstanceRequirementsInput(
            architectureTypes: self.architectureTypes,
            dryRun: self.dryRun,
            instanceRequirements: self.instanceRequirements,
            maxResults: self.maxResults,
            nextToken: token,
            virtualizationTypes: self.virtualizationTypes
        )}
}

extension PaginatorSequence where OperationStackInput == GetInstanceTypesFromInstanceRequirementsInput, OperationStackOutput == GetInstanceTypesFromInstanceRequirementsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getInstanceTypesFromInstanceRequirementsPaginated`
    /// to access the nested member `[EC2ClientTypes.InstanceTypeInfoFromInstanceRequirements]`
    /// - Returns: `[EC2ClientTypes.InstanceTypeInfoFromInstanceRequirements]`
    public func instanceTypes() async throws -> [EC2ClientTypes.InstanceTypeInfoFromInstanceRequirements] {
        return try await self.asyncCompactMap { item in item.instanceTypes }
    }
}
extension EC2Client {
    /// Paginate over `[GetIpamAddressHistoryOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetIpamAddressHistoryInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetIpamAddressHistoryOutput`
    public func getIpamAddressHistoryPaginated(input: GetIpamAddressHistoryInput) -> ClientRuntime.PaginatorSequence<GetIpamAddressHistoryInput, GetIpamAddressHistoryOutput> {
        return ClientRuntime.PaginatorSequence<GetIpamAddressHistoryInput, GetIpamAddressHistoryOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getIpamAddressHistory(input:))
    }
}

extension GetIpamAddressHistoryInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetIpamAddressHistoryInput {
        return GetIpamAddressHistoryInput(
            cidr: self.cidr,
            dryRun: self.dryRun,
            endTime: self.endTime,
            ipamScopeId: self.ipamScopeId,
            maxResults: self.maxResults,
            nextToken: token,
            startTime: self.startTime,
            vpcId: self.vpcId
        )}
}

extension PaginatorSequence where OperationStackInput == GetIpamAddressHistoryInput, OperationStackOutput == GetIpamAddressHistoryOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getIpamAddressHistoryPaginated`
    /// to access the nested member `[EC2ClientTypes.IpamAddressHistoryRecord]`
    /// - Returns: `[EC2ClientTypes.IpamAddressHistoryRecord]`
    public func historyRecords() async throws -> [EC2ClientTypes.IpamAddressHistoryRecord] {
        return try await self.asyncCompactMap { item in item.historyRecords }
    }
}
extension EC2Client {
    /// Paginate over `[GetIpamDiscoveredAccountsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetIpamDiscoveredAccountsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetIpamDiscoveredAccountsOutput`
    public func getIpamDiscoveredAccountsPaginated(input: GetIpamDiscoveredAccountsInput) -> ClientRuntime.PaginatorSequence<GetIpamDiscoveredAccountsInput, GetIpamDiscoveredAccountsOutput> {
        return ClientRuntime.PaginatorSequence<GetIpamDiscoveredAccountsInput, GetIpamDiscoveredAccountsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getIpamDiscoveredAccounts(input:))
    }
}

extension GetIpamDiscoveredAccountsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetIpamDiscoveredAccountsInput {
        return GetIpamDiscoveredAccountsInput(
            discoveryRegion: self.discoveryRegion,
            dryRun: self.dryRun,
            filters: self.filters,
            ipamResourceDiscoveryId: self.ipamResourceDiscoveryId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == GetIpamDiscoveredAccountsInput, OperationStackOutput == GetIpamDiscoveredAccountsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getIpamDiscoveredAccountsPaginated`
    /// to access the nested member `[EC2ClientTypes.IpamDiscoveredAccount]`
    /// - Returns: `[EC2ClientTypes.IpamDiscoveredAccount]`
    public func ipamDiscoveredAccounts() async throws -> [EC2ClientTypes.IpamDiscoveredAccount] {
        return try await self.asyncCompactMap { item in item.ipamDiscoveredAccounts }
    }
}
extension EC2Client {
    /// Paginate over `[GetIpamDiscoveredResourceCidrsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetIpamDiscoveredResourceCidrsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetIpamDiscoveredResourceCidrsOutput`
    public func getIpamDiscoveredResourceCidrsPaginated(input: GetIpamDiscoveredResourceCidrsInput) -> ClientRuntime.PaginatorSequence<GetIpamDiscoveredResourceCidrsInput, GetIpamDiscoveredResourceCidrsOutput> {
        return ClientRuntime.PaginatorSequence<GetIpamDiscoveredResourceCidrsInput, GetIpamDiscoveredResourceCidrsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getIpamDiscoveredResourceCidrs(input:))
    }
}

extension GetIpamDiscoveredResourceCidrsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetIpamDiscoveredResourceCidrsInput {
        return GetIpamDiscoveredResourceCidrsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            ipamResourceDiscoveryId: self.ipamResourceDiscoveryId,
            maxResults: self.maxResults,
            nextToken: token,
            resourceRegion: self.resourceRegion
        )}
}

extension PaginatorSequence where OperationStackInput == GetIpamDiscoveredResourceCidrsInput, OperationStackOutput == GetIpamDiscoveredResourceCidrsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getIpamDiscoveredResourceCidrsPaginated`
    /// to access the nested member `[EC2ClientTypes.IpamDiscoveredResourceCidr]`
    /// - Returns: `[EC2ClientTypes.IpamDiscoveredResourceCidr]`
    public func ipamDiscoveredResourceCidrs() async throws -> [EC2ClientTypes.IpamDiscoveredResourceCidr] {
        return try await self.asyncCompactMap { item in item.ipamDiscoveredResourceCidrs }
    }
}
extension EC2Client {
    /// Paginate over `[GetIpamPoolAllocationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetIpamPoolAllocationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetIpamPoolAllocationsOutput`
    public func getIpamPoolAllocationsPaginated(input: GetIpamPoolAllocationsInput) -> ClientRuntime.PaginatorSequence<GetIpamPoolAllocationsInput, GetIpamPoolAllocationsOutput> {
        return ClientRuntime.PaginatorSequence<GetIpamPoolAllocationsInput, GetIpamPoolAllocationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getIpamPoolAllocations(input:))
    }
}

extension GetIpamPoolAllocationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetIpamPoolAllocationsInput {
        return GetIpamPoolAllocationsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            ipamPoolAllocationId: self.ipamPoolAllocationId,
            ipamPoolId: self.ipamPoolId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == GetIpamPoolAllocationsInput, OperationStackOutput == GetIpamPoolAllocationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getIpamPoolAllocationsPaginated`
    /// to access the nested member `[EC2ClientTypes.IpamPoolAllocation]`
    /// - Returns: `[EC2ClientTypes.IpamPoolAllocation]`
    public func ipamPoolAllocations() async throws -> [EC2ClientTypes.IpamPoolAllocation] {
        return try await self.asyncCompactMap { item in item.ipamPoolAllocations }
    }
}
extension EC2Client {
    /// Paginate over `[GetIpamPoolCidrsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetIpamPoolCidrsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetIpamPoolCidrsOutput`
    public func getIpamPoolCidrsPaginated(input: GetIpamPoolCidrsInput) -> ClientRuntime.PaginatorSequence<GetIpamPoolCidrsInput, GetIpamPoolCidrsOutput> {
        return ClientRuntime.PaginatorSequence<GetIpamPoolCidrsInput, GetIpamPoolCidrsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getIpamPoolCidrs(input:))
    }
}

extension GetIpamPoolCidrsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetIpamPoolCidrsInput {
        return GetIpamPoolCidrsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            ipamPoolId: self.ipamPoolId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == GetIpamPoolCidrsInput, OperationStackOutput == GetIpamPoolCidrsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getIpamPoolCidrsPaginated`
    /// to access the nested member `[EC2ClientTypes.IpamPoolCidr]`
    /// - Returns: `[EC2ClientTypes.IpamPoolCidr]`
    public func ipamPoolCidrs() async throws -> [EC2ClientTypes.IpamPoolCidr] {
        return try await self.asyncCompactMap { item in item.ipamPoolCidrs }
    }
}
extension EC2Client {
    /// Paginate over `[GetIpamResourceCidrsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetIpamResourceCidrsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetIpamResourceCidrsOutput`
    public func getIpamResourceCidrsPaginated(input: GetIpamResourceCidrsInput) -> ClientRuntime.PaginatorSequence<GetIpamResourceCidrsInput, GetIpamResourceCidrsOutput> {
        return ClientRuntime.PaginatorSequence<GetIpamResourceCidrsInput, GetIpamResourceCidrsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getIpamResourceCidrs(input:))
    }
}

extension GetIpamResourceCidrsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetIpamResourceCidrsInput {
        return GetIpamResourceCidrsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            ipamPoolId: self.ipamPoolId,
            ipamScopeId: self.ipamScopeId,
            maxResults: self.maxResults,
            nextToken: token,
            resourceId: self.resourceId,
            resourceOwner: self.resourceOwner,
            resourceTag: self.resourceTag,
            resourceType: self.resourceType
        )}
}

extension PaginatorSequence where OperationStackInput == GetIpamResourceCidrsInput, OperationStackOutput == GetIpamResourceCidrsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getIpamResourceCidrsPaginated`
    /// to access the nested member `[EC2ClientTypes.IpamResourceCidr]`
    /// - Returns: `[EC2ClientTypes.IpamResourceCidr]`
    public func ipamResourceCidrs() async throws -> [EC2ClientTypes.IpamResourceCidr] {
        return try await self.asyncCompactMap { item in item.ipamResourceCidrs }
    }
}
extension EC2Client {
    /// Paginate over `[GetManagedPrefixListAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetManagedPrefixListAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetManagedPrefixListAssociationsOutput`
    public func getManagedPrefixListAssociationsPaginated(input: GetManagedPrefixListAssociationsInput) -> ClientRuntime.PaginatorSequence<GetManagedPrefixListAssociationsInput, GetManagedPrefixListAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<GetManagedPrefixListAssociationsInput, GetManagedPrefixListAssociationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getManagedPrefixListAssociations(input:))
    }
}

extension GetManagedPrefixListAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetManagedPrefixListAssociationsInput {
        return GetManagedPrefixListAssociationsInput(
            dryRun: self.dryRun,
            maxResults: self.maxResults,
            nextToken: token,
            prefixListId: self.prefixListId
        )}
}

extension PaginatorSequence where OperationStackInput == GetManagedPrefixListAssociationsInput, OperationStackOutput == GetManagedPrefixListAssociationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getManagedPrefixListAssociationsPaginated`
    /// to access the nested member `[EC2ClientTypes.PrefixListAssociation]`
    /// - Returns: `[EC2ClientTypes.PrefixListAssociation]`
    public func prefixListAssociations() async throws -> [EC2ClientTypes.PrefixListAssociation] {
        return try await self.asyncCompactMap { item in item.prefixListAssociations }
    }
}
extension EC2Client {
    /// Paginate over `[GetManagedPrefixListEntriesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetManagedPrefixListEntriesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetManagedPrefixListEntriesOutput`
    public func getManagedPrefixListEntriesPaginated(input: GetManagedPrefixListEntriesInput) -> ClientRuntime.PaginatorSequence<GetManagedPrefixListEntriesInput, GetManagedPrefixListEntriesOutput> {
        return ClientRuntime.PaginatorSequence<GetManagedPrefixListEntriesInput, GetManagedPrefixListEntriesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getManagedPrefixListEntries(input:))
    }
}

extension GetManagedPrefixListEntriesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetManagedPrefixListEntriesInput {
        return GetManagedPrefixListEntriesInput(
            dryRun: self.dryRun,
            maxResults: self.maxResults,
            nextToken: token,
            prefixListId: self.prefixListId,
            targetVersion: self.targetVersion
        )}
}

extension PaginatorSequence where OperationStackInput == GetManagedPrefixListEntriesInput, OperationStackOutput == GetManagedPrefixListEntriesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getManagedPrefixListEntriesPaginated`
    /// to access the nested member `[EC2ClientTypes.PrefixListEntry]`
    /// - Returns: `[EC2ClientTypes.PrefixListEntry]`
    public func entries() async throws -> [EC2ClientTypes.PrefixListEntry] {
        return try await self.asyncCompactMap { item in item.entries }
    }
}
extension EC2Client {
    /// Paginate over `[GetNetworkInsightsAccessScopeAnalysisFindingsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetNetworkInsightsAccessScopeAnalysisFindingsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetNetworkInsightsAccessScopeAnalysisFindingsOutput`
    public func getNetworkInsightsAccessScopeAnalysisFindingsPaginated(input: GetNetworkInsightsAccessScopeAnalysisFindingsInput) -> ClientRuntime.PaginatorSequence<GetNetworkInsightsAccessScopeAnalysisFindingsInput, GetNetworkInsightsAccessScopeAnalysisFindingsOutput> {
        return ClientRuntime.PaginatorSequence<GetNetworkInsightsAccessScopeAnalysisFindingsInput, GetNetworkInsightsAccessScopeAnalysisFindingsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getNetworkInsightsAccessScopeAnalysisFindings(input:))
    }
}

extension GetNetworkInsightsAccessScopeAnalysisFindingsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetNetworkInsightsAccessScopeAnalysisFindingsInput {
        return GetNetworkInsightsAccessScopeAnalysisFindingsInput(
            dryRun: self.dryRun,
            maxResults: self.maxResults,
            networkInsightsAccessScopeAnalysisId: self.networkInsightsAccessScopeAnalysisId,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == GetNetworkInsightsAccessScopeAnalysisFindingsInput, OperationStackOutput == GetNetworkInsightsAccessScopeAnalysisFindingsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getNetworkInsightsAccessScopeAnalysisFindingsPaginated`
    /// to access the nested member `[EC2ClientTypes.AccessScopeAnalysisFinding]`
    /// - Returns: `[EC2ClientTypes.AccessScopeAnalysisFinding]`
    public func analysisFindings() async throws -> [EC2ClientTypes.AccessScopeAnalysisFinding] {
        return try await self.asyncCompactMap { item in item.analysisFindings }
    }
}
extension EC2Client {
    /// Paginate over `[GetSecurityGroupsForVpcOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetSecurityGroupsForVpcInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetSecurityGroupsForVpcOutput`
    public func getSecurityGroupsForVpcPaginated(input: GetSecurityGroupsForVpcInput) -> ClientRuntime.PaginatorSequence<GetSecurityGroupsForVpcInput, GetSecurityGroupsForVpcOutput> {
        return ClientRuntime.PaginatorSequence<GetSecurityGroupsForVpcInput, GetSecurityGroupsForVpcOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getSecurityGroupsForVpc(input:))
    }
}

extension GetSecurityGroupsForVpcInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetSecurityGroupsForVpcInput {
        return GetSecurityGroupsForVpcInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            vpcId: self.vpcId
        )}
}

extension PaginatorSequence where OperationStackInput == GetSecurityGroupsForVpcInput, OperationStackOutput == GetSecurityGroupsForVpcOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getSecurityGroupsForVpcPaginated`
    /// to access the nested member `[EC2ClientTypes.SecurityGroupForVpc]`
    /// - Returns: `[EC2ClientTypes.SecurityGroupForVpc]`
    public func securityGroupForVpcs() async throws -> [EC2ClientTypes.SecurityGroupForVpc] {
        return try await self.asyncCompactMap { item in item.securityGroupForVpcs }
    }
}
extension EC2Client {
    /// Paginate over `[GetSpotPlacementScoresOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetSpotPlacementScoresInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetSpotPlacementScoresOutput`
    public func getSpotPlacementScoresPaginated(input: GetSpotPlacementScoresInput) -> ClientRuntime.PaginatorSequence<GetSpotPlacementScoresInput, GetSpotPlacementScoresOutput> {
        return ClientRuntime.PaginatorSequence<GetSpotPlacementScoresInput, GetSpotPlacementScoresOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getSpotPlacementScores(input:))
    }
}

extension GetSpotPlacementScoresInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetSpotPlacementScoresInput {
        return GetSpotPlacementScoresInput(
            dryRun: self.dryRun,
            instanceRequirementsWithMetadata: self.instanceRequirementsWithMetadata,
            instanceTypes: self.instanceTypes,
            maxResults: self.maxResults,
            nextToken: token,
            regionNames: self.regionNames,
            singleAvailabilityZone: self.singleAvailabilityZone,
            targetCapacity: self.targetCapacity,
            targetCapacityUnitType: self.targetCapacityUnitType
        )}
}

extension PaginatorSequence where OperationStackInput == GetSpotPlacementScoresInput, OperationStackOutput == GetSpotPlacementScoresOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getSpotPlacementScoresPaginated`
    /// to access the nested member `[EC2ClientTypes.SpotPlacementScore]`
    /// - Returns: `[EC2ClientTypes.SpotPlacementScore]`
    public func spotPlacementScores() async throws -> [EC2ClientTypes.SpotPlacementScore] {
        return try await self.asyncCompactMap { item in item.spotPlacementScores }
    }
}
extension EC2Client {
    /// Paginate over `[GetTransitGatewayAttachmentPropagationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetTransitGatewayAttachmentPropagationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetTransitGatewayAttachmentPropagationsOutput`
    public func getTransitGatewayAttachmentPropagationsPaginated(input: GetTransitGatewayAttachmentPropagationsInput) -> ClientRuntime.PaginatorSequence<GetTransitGatewayAttachmentPropagationsInput, GetTransitGatewayAttachmentPropagationsOutput> {
        return ClientRuntime.PaginatorSequence<GetTransitGatewayAttachmentPropagationsInput, GetTransitGatewayAttachmentPropagationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getTransitGatewayAttachmentPropagations(input:))
    }
}

extension GetTransitGatewayAttachmentPropagationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetTransitGatewayAttachmentPropagationsInput {
        return GetTransitGatewayAttachmentPropagationsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayAttachmentId: self.transitGatewayAttachmentId
        )}
}

extension PaginatorSequence where OperationStackInput == GetTransitGatewayAttachmentPropagationsInput, OperationStackOutput == GetTransitGatewayAttachmentPropagationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getTransitGatewayAttachmentPropagationsPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayAttachmentPropagation]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayAttachmentPropagation]`
    public func transitGatewayAttachmentPropagations() async throws -> [EC2ClientTypes.TransitGatewayAttachmentPropagation] {
        return try await self.asyncCompactMap { item in item.transitGatewayAttachmentPropagations }
    }
}
extension EC2Client {
    /// Paginate over `[GetTransitGatewayMulticastDomainAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetTransitGatewayMulticastDomainAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetTransitGatewayMulticastDomainAssociationsOutput`
    public func getTransitGatewayMulticastDomainAssociationsPaginated(input: GetTransitGatewayMulticastDomainAssociationsInput) -> ClientRuntime.PaginatorSequence<GetTransitGatewayMulticastDomainAssociationsInput, GetTransitGatewayMulticastDomainAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<GetTransitGatewayMulticastDomainAssociationsInput, GetTransitGatewayMulticastDomainAssociationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getTransitGatewayMulticastDomainAssociations(input:))
    }
}

extension GetTransitGatewayMulticastDomainAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetTransitGatewayMulticastDomainAssociationsInput {
        return GetTransitGatewayMulticastDomainAssociationsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayMulticastDomainId: self.transitGatewayMulticastDomainId
        )}
}

extension PaginatorSequence where OperationStackInput == GetTransitGatewayMulticastDomainAssociationsInput, OperationStackOutput == GetTransitGatewayMulticastDomainAssociationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getTransitGatewayMulticastDomainAssociationsPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayMulticastDomainAssociation]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayMulticastDomainAssociation]`
    public func multicastDomainAssociations() async throws -> [EC2ClientTypes.TransitGatewayMulticastDomainAssociation] {
        return try await self.asyncCompactMap { item in item.multicastDomainAssociations }
    }
}
extension EC2Client {
    /// Paginate over `[GetTransitGatewayPolicyTableAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetTransitGatewayPolicyTableAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetTransitGatewayPolicyTableAssociationsOutput`
    public func getTransitGatewayPolicyTableAssociationsPaginated(input: GetTransitGatewayPolicyTableAssociationsInput) -> ClientRuntime.PaginatorSequence<GetTransitGatewayPolicyTableAssociationsInput, GetTransitGatewayPolicyTableAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<GetTransitGatewayPolicyTableAssociationsInput, GetTransitGatewayPolicyTableAssociationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getTransitGatewayPolicyTableAssociations(input:))
    }
}

extension GetTransitGatewayPolicyTableAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetTransitGatewayPolicyTableAssociationsInput {
        return GetTransitGatewayPolicyTableAssociationsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayPolicyTableId: self.transitGatewayPolicyTableId
        )}
}

extension PaginatorSequence where OperationStackInput == GetTransitGatewayPolicyTableAssociationsInput, OperationStackOutput == GetTransitGatewayPolicyTableAssociationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getTransitGatewayPolicyTableAssociationsPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayPolicyTableAssociation]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayPolicyTableAssociation]`
    public func associations() async throws -> [EC2ClientTypes.TransitGatewayPolicyTableAssociation] {
        return try await self.asyncCompactMap { item in item.associations }
    }
}
extension EC2Client {
    /// Paginate over `[GetTransitGatewayPrefixListReferencesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetTransitGatewayPrefixListReferencesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetTransitGatewayPrefixListReferencesOutput`
    public func getTransitGatewayPrefixListReferencesPaginated(input: GetTransitGatewayPrefixListReferencesInput) -> ClientRuntime.PaginatorSequence<GetTransitGatewayPrefixListReferencesInput, GetTransitGatewayPrefixListReferencesOutput> {
        return ClientRuntime.PaginatorSequence<GetTransitGatewayPrefixListReferencesInput, GetTransitGatewayPrefixListReferencesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getTransitGatewayPrefixListReferences(input:))
    }
}

extension GetTransitGatewayPrefixListReferencesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetTransitGatewayPrefixListReferencesInput {
        return GetTransitGatewayPrefixListReferencesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayRouteTableId: self.transitGatewayRouteTableId
        )}
}

extension PaginatorSequence where OperationStackInput == GetTransitGatewayPrefixListReferencesInput, OperationStackOutput == GetTransitGatewayPrefixListReferencesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getTransitGatewayPrefixListReferencesPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayPrefixListReference]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayPrefixListReference]`
    public func transitGatewayPrefixListReferences() async throws -> [EC2ClientTypes.TransitGatewayPrefixListReference] {
        return try await self.asyncCompactMap { item in item.transitGatewayPrefixListReferences }
    }
}
extension EC2Client {
    /// Paginate over `[GetTransitGatewayRouteTableAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetTransitGatewayRouteTableAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetTransitGatewayRouteTableAssociationsOutput`
    public func getTransitGatewayRouteTableAssociationsPaginated(input: GetTransitGatewayRouteTableAssociationsInput) -> ClientRuntime.PaginatorSequence<GetTransitGatewayRouteTableAssociationsInput, GetTransitGatewayRouteTableAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<GetTransitGatewayRouteTableAssociationsInput, GetTransitGatewayRouteTableAssociationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getTransitGatewayRouteTableAssociations(input:))
    }
}

extension GetTransitGatewayRouteTableAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetTransitGatewayRouteTableAssociationsInput {
        return GetTransitGatewayRouteTableAssociationsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayRouteTableId: self.transitGatewayRouteTableId
        )}
}

extension PaginatorSequence where OperationStackInput == GetTransitGatewayRouteTableAssociationsInput, OperationStackOutput == GetTransitGatewayRouteTableAssociationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getTransitGatewayRouteTableAssociationsPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayRouteTableAssociation]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayRouteTableAssociation]`
    public func associations() async throws -> [EC2ClientTypes.TransitGatewayRouteTableAssociation] {
        return try await self.asyncCompactMap { item in item.associations }
    }
}
extension EC2Client {
    /// Paginate over `[GetTransitGatewayRouteTablePropagationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetTransitGatewayRouteTablePropagationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetTransitGatewayRouteTablePropagationsOutput`
    public func getTransitGatewayRouteTablePropagationsPaginated(input: GetTransitGatewayRouteTablePropagationsInput) -> ClientRuntime.PaginatorSequence<GetTransitGatewayRouteTablePropagationsInput, GetTransitGatewayRouteTablePropagationsOutput> {
        return ClientRuntime.PaginatorSequence<GetTransitGatewayRouteTablePropagationsInput, GetTransitGatewayRouteTablePropagationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getTransitGatewayRouteTablePropagations(input:))
    }
}

extension GetTransitGatewayRouteTablePropagationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetTransitGatewayRouteTablePropagationsInput {
        return GetTransitGatewayRouteTablePropagationsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayRouteTableId: self.transitGatewayRouteTableId
        )}
}

extension PaginatorSequence where OperationStackInput == GetTransitGatewayRouteTablePropagationsInput, OperationStackOutput == GetTransitGatewayRouteTablePropagationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getTransitGatewayRouteTablePropagationsPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayRouteTablePropagation]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayRouteTablePropagation]`
    public func transitGatewayRouteTablePropagations() async throws -> [EC2ClientTypes.TransitGatewayRouteTablePropagation] {
        return try await self.asyncCompactMap { item in item.transitGatewayRouteTablePropagations }
    }
}
extension EC2Client {
    /// Paginate over `[GetVpnConnectionDeviceTypesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetVpnConnectionDeviceTypesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetVpnConnectionDeviceTypesOutput`
    public func getVpnConnectionDeviceTypesPaginated(input: GetVpnConnectionDeviceTypesInput) -> ClientRuntime.PaginatorSequence<GetVpnConnectionDeviceTypesInput, GetVpnConnectionDeviceTypesOutput> {
        return ClientRuntime.PaginatorSequence<GetVpnConnectionDeviceTypesInput, GetVpnConnectionDeviceTypesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getVpnConnectionDeviceTypes(input:))
    }
}

extension GetVpnConnectionDeviceTypesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetVpnConnectionDeviceTypesInput {
        return GetVpnConnectionDeviceTypesInput(
            dryRun: self.dryRun,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == GetVpnConnectionDeviceTypesInput, OperationStackOutput == GetVpnConnectionDeviceTypesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `getVpnConnectionDeviceTypesPaginated`
    /// to access the nested member `[EC2ClientTypes.VpnConnectionDeviceType]`
    /// - Returns: `[EC2ClientTypes.VpnConnectionDeviceType]`
    public func vpnConnectionDeviceTypes() async throws -> [EC2ClientTypes.VpnConnectionDeviceType] {
        return try await self.asyncCompactMap { item in item.vpnConnectionDeviceTypes }
    }
}
extension EC2Client {
    /// Paginate over `[ListImagesInRecycleBinOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListImagesInRecycleBinInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListImagesInRecycleBinOutput`
    public func listImagesInRecycleBinPaginated(input: ListImagesInRecycleBinInput) -> ClientRuntime.PaginatorSequence<ListImagesInRecycleBinInput, ListImagesInRecycleBinOutput> {
        return ClientRuntime.PaginatorSequence<ListImagesInRecycleBinInput, ListImagesInRecycleBinOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listImagesInRecycleBin(input:))
    }
}

extension ListImagesInRecycleBinInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListImagesInRecycleBinInput {
        return ListImagesInRecycleBinInput(
            dryRun: self.dryRun,
            imageIds: self.imageIds,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListImagesInRecycleBinInput, OperationStackOutput == ListImagesInRecycleBinOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listImagesInRecycleBinPaginated`
    /// to access the nested member `[EC2ClientTypes.ImageRecycleBinInfo]`
    /// - Returns: `[EC2ClientTypes.ImageRecycleBinInfo]`
    public func images() async throws -> [EC2ClientTypes.ImageRecycleBinInfo] {
        return try await self.asyncCompactMap { item in item.images }
    }
}
extension EC2Client {
    /// Paginate over `[ListSnapshotsInRecycleBinOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListSnapshotsInRecycleBinInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListSnapshotsInRecycleBinOutput`
    public func listSnapshotsInRecycleBinPaginated(input: ListSnapshotsInRecycleBinInput) -> ClientRuntime.PaginatorSequence<ListSnapshotsInRecycleBinInput, ListSnapshotsInRecycleBinOutput> {
        return ClientRuntime.PaginatorSequence<ListSnapshotsInRecycleBinInput, ListSnapshotsInRecycleBinOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listSnapshotsInRecycleBin(input:))
    }
}

extension ListSnapshotsInRecycleBinInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListSnapshotsInRecycleBinInput {
        return ListSnapshotsInRecycleBinInput(
            dryRun: self.dryRun,
            maxResults: self.maxResults,
            nextToken: token,
            snapshotIds: self.snapshotIds
        )}
}

extension PaginatorSequence where OperationStackInput == ListSnapshotsInRecycleBinInput, OperationStackOutput == ListSnapshotsInRecycleBinOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listSnapshotsInRecycleBinPaginated`
    /// to access the nested member `[EC2ClientTypes.SnapshotRecycleBinInfo]`
    /// - Returns: `[EC2ClientTypes.SnapshotRecycleBinInfo]`
    public func snapshots() async throws -> [EC2ClientTypes.SnapshotRecycleBinInfo] {
        return try await self.asyncCompactMap { item in item.snapshots }
    }
}
extension EC2Client {
    /// Paginate over `[SearchLocalGatewayRoutesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchLocalGatewayRoutesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchLocalGatewayRoutesOutput`
    public func searchLocalGatewayRoutesPaginated(input: SearchLocalGatewayRoutesInput) -> ClientRuntime.PaginatorSequence<SearchLocalGatewayRoutesInput, SearchLocalGatewayRoutesOutput> {
        return ClientRuntime.PaginatorSequence<SearchLocalGatewayRoutesInput, SearchLocalGatewayRoutesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchLocalGatewayRoutes(input:))
    }
}

extension SearchLocalGatewayRoutesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchLocalGatewayRoutesInput {
        return SearchLocalGatewayRoutesInput(
            dryRun: self.dryRun,
            filters: self.filters,
            localGatewayRouteTableId: self.localGatewayRouteTableId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == SearchLocalGatewayRoutesInput, OperationStackOutput == SearchLocalGatewayRoutesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchLocalGatewayRoutesPaginated`
    /// to access the nested member `[EC2ClientTypes.LocalGatewayRoute]`
    /// - Returns: `[EC2ClientTypes.LocalGatewayRoute]`
    public func routes() async throws -> [EC2ClientTypes.LocalGatewayRoute] {
        return try await self.asyncCompactMap { item in item.routes }
    }
}
extension EC2Client {
    /// Paginate over `[SearchTransitGatewayMulticastGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchTransitGatewayMulticastGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchTransitGatewayMulticastGroupsOutput`
    public func searchTransitGatewayMulticastGroupsPaginated(input: SearchTransitGatewayMulticastGroupsInput) -> ClientRuntime.PaginatorSequence<SearchTransitGatewayMulticastGroupsInput, SearchTransitGatewayMulticastGroupsOutput> {
        return ClientRuntime.PaginatorSequence<SearchTransitGatewayMulticastGroupsInput, SearchTransitGatewayMulticastGroupsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchTransitGatewayMulticastGroups(input:))
    }
}

extension SearchTransitGatewayMulticastGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchTransitGatewayMulticastGroupsInput {
        return SearchTransitGatewayMulticastGroupsInput(
            dryRun: self.dryRun,
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token,
            transitGatewayMulticastDomainId: self.transitGatewayMulticastDomainId
        )}
}

extension PaginatorSequence where OperationStackInput == SearchTransitGatewayMulticastGroupsInput, OperationStackOutput == SearchTransitGatewayMulticastGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchTransitGatewayMulticastGroupsPaginated`
    /// to access the nested member `[EC2ClientTypes.TransitGatewayMulticastGroup]`
    /// - Returns: `[EC2ClientTypes.TransitGatewayMulticastGroup]`
    public func multicastGroups() async throws -> [EC2ClientTypes.TransitGatewayMulticastGroup] {
        return try await self.asyncCompactMap { item in item.multicastGroups }
    }
}
