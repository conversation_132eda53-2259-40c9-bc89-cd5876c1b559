//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import class SmithyWaitersAPI.Waiter
import enum SmithyWaitersAPI.JMESUtils
import struct SmithyWaitersAPI.WaiterConfiguration
import struct SmithyWaitersAPI.WaiterOptions
import struct Smithy<PERSON>aitersAPI.WaiterOutcome

extension DeadlineClient {

    static func queueFleetAssociationStoppedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<GetQueueFleetAssociationInput, GetQueueFleetAssociationOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<GetQueueFleetAssociationInput, GetQueueFleetAssociationOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: GetQueueFleetAssociationInput, result: Swift.Result<GetQueueFleetAssociationOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "status"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "STOPPED"
                guard case .success(let output) = result else { return false }
                let status = output.status
                return SmithyWaitersAPI.JMESUtils.compare(status, ==, "STOPPED")
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<GetQueueFleetAssociationInput, GetQueueFleetAssociationOutput>(acceptors: acceptors, minDelay: 10.0, maxDelay: 600.0)
    }

    /// Initiates waiting for the QueueFleetAssociationStopped event on the getQueueFleetAssociation operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `GetQueueFleetAssociationInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilQueueFleetAssociationStopped(options: SmithyWaitersAPI.WaiterOptions, input: GetQueueFleetAssociationInput) async throws -> SmithyWaitersAPI.WaiterOutcome<GetQueueFleetAssociationOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.queueFleetAssociationStoppedWaiterConfig(), operation: self.getQueueFleetAssociation(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }
}
