//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension CostandUsageReportClient {
    /// Paginate over `[DescribeReportDefinitionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReportDefinitionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReportDefinitionsOutput`
    public func describeReportDefinitionsPaginated(input: DescribeReportDefinitionsInput) -> ClientRuntime.PaginatorSequence<DescribeReportDefinitionsInput, DescribeReportDefinitionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReportDefinitionsInput, DescribeReportDefinitionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeReportDefinitions(input:))
    }
}

extension DescribeReportDefinitionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReportDefinitionsInput {
        return DescribeReportDefinitionsInput(
            maxResults: self.maxResults,
            nextToken: token
        )}
}
