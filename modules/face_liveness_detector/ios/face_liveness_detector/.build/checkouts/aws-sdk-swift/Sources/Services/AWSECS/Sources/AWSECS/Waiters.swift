//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import class SmithyWaitersAPI.Waiter
import enum SmithyWaitersAPI.JMESUtils
import struct SmithyWaitersAPI.WaiterConfiguration
import struct SmithyWaitersAPI.WaiterOptions
import struct SmithyWaitersAPI.WaiterOutcome

extension ECSClient {

    static func servicesInactiveWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeServicesInput, DescribeServicesOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeServicesInput, DescribeServicesOutput>.Acceptor] = [
            .init(state: .failure, matcher: { (input: DescribeServicesInput, result: Swift.Result<DescribeServicesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "failures[].reason"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "MISSING"
                guard case .success(let output) = result else { return false }
                let failures = output.failures
                let projection: [Swift.String]? = failures?.compactMap { original in
                    let reason = original.reason
                    return reason
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "MISSING") }) ?? false
            }),
            .init(state: .success, matcher: { (input: DescribeServicesInput, result: Swift.Result<DescribeServicesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "services[].status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "INACTIVE"
                guard case .success(let output) = result else { return false }
                let services = output.services
                let projection: [Swift.String]? = services?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "INACTIVE") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeServicesInput, DescribeServicesOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ServicesInactive event on the describeServices operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeServicesInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilServicesInactive(options: SmithyWaitersAPI.WaiterOptions, input: DescribeServicesInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeServicesOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.servicesInactiveWaiterConfig(), operation: self.describeServices(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func servicesStableWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeServicesInput, DescribeServicesOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeServicesInput, DescribeServicesOutput>.Acceptor] = [
            .init(state: .failure, matcher: { (input: DescribeServicesInput, result: Swift.Result<DescribeServicesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "failures[].reason"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "MISSING"
                guard case .success(let output) = result else { return false }
                let failures = output.failures
                let projection: [Swift.String]? = failures?.compactMap { original in
                    let reason = original.reason
                    return reason
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "MISSING") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeServicesInput, result: Swift.Result<DescribeServicesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "services[].status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "DRAINING"
                guard case .success(let output) = result else { return false }
                let services = output.services
                let projection: [Swift.String]? = services?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "DRAINING") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeServicesInput, result: Swift.Result<DescribeServicesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "services[].status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "INACTIVE"
                guard case .success(let output) = result else { return false }
                let services = output.services
                let projection: [Swift.String]? = services?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "INACTIVE") }) ?? false
            }),
            .init(state: .success, matcher: { (input: DescribeServicesInput, result: Swift.Result<DescribeServicesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "length(services[?!(length(deployments) == `1` && runningCount == desiredCount)]) == `0`"
                // JMESPath comparator: "booleanEquals"
                // JMESPath expected value: "true"
                guard case .success(let output) = result else { return false }
                let services = output.services
                let servicesFiltered: [ECSClientTypes.Service]? = services?.filter { original in
                    let deployments = original.deployments
                    let count = Double(deployments?.count ?? 0)
                    let number = Double(1.0)
                    let comparison = SmithyWaitersAPI.JMESUtils.compare(count, ==, number)
                    let runningCount = original.runningCount
                    let desiredCount = original.desiredCount
                    let comparison2 = SmithyWaitersAPI.JMESUtils.compare(runningCount, ==, desiredCount)
                    let andResult = comparison && comparison2
                    let negated = !andResult
                    return negated
                }
                let count = Double(servicesFiltered?.count ?? 0)
                let number = Double(0.0)
                let comparison = SmithyWaitersAPI.JMESUtils.compare(count, ==, number)
                return SmithyWaitersAPI.JMESUtils.compare(comparison, ==, true)
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeServicesInput, DescribeServicesOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ServicesStable event on the describeServices operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeServicesInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilServicesStable(options: SmithyWaitersAPI.WaiterOptions, input: DescribeServicesInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeServicesOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.servicesStableWaiterConfig(), operation: self.describeServices(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func tasksRunningWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeTasksInput, DescribeTasksOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeTasksInput, DescribeTasksOutput>.Acceptor] = [
            .init(state: .failure, matcher: { (input: DescribeTasksInput, result: Swift.Result<DescribeTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "tasks[].lastStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "STOPPED"
                guard case .success(let output) = result else { return false }
                let tasks = output.tasks
                let projection: [Swift.String]? = tasks?.compactMap { original in
                    let lastStatus = original.lastStatus
                    return lastStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "STOPPED") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeTasksInput, result: Swift.Result<DescribeTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "failures[].reason"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "MISSING"
                guard case .success(let output) = result else { return false }
                let failures = output.failures
                let projection: [Swift.String]? = failures?.compactMap { original in
                    let reason = original.reason
                    return reason
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "MISSING") }) ?? false
            }),
            .init(state: .success, matcher: { (input: DescribeTasksInput, result: Swift.Result<DescribeTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "tasks[].lastStatus"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "RUNNING"
                guard case .success(let output) = result else { return false }
                let tasks = output.tasks
                let projection: [Swift.String]? = tasks?.compactMap { original in
                    let lastStatus = original.lastStatus
                    return lastStatus
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "RUNNING") } ?? false)
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeTasksInput, DescribeTasksOutput>(acceptors: acceptors, minDelay: 6.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the TasksRunning event on the describeTasks operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeTasksInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilTasksRunning(options: SmithyWaitersAPI.WaiterOptions, input: DescribeTasksInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeTasksOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.tasksRunningWaiterConfig(), operation: self.describeTasks(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func tasksStoppedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeTasksInput, DescribeTasksOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeTasksInput, DescribeTasksOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeTasksInput, result: Swift.Result<DescribeTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "tasks[].lastStatus"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "STOPPED"
                guard case .success(let output) = result else { return false }
                let tasks = output.tasks
                let projection: [Swift.String]? = tasks?.compactMap { original in
                    let lastStatus = original.lastStatus
                    return lastStatus
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "STOPPED") } ?? false)
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeTasksInput, DescribeTasksOutput>(acceptors: acceptors, minDelay: 6.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the TasksStopped event on the describeTasks operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeTasksInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilTasksStopped(options: SmithyWaitersAPI.WaiterOptions, input: DescribeTasksInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeTasksOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.tasksStoppedWaiterConfig(), operation: self.describeTasks(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }
}
