//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension ConnectContactLensClient {
    /// Paginate over `[ListRealtimeContactAnalysisSegmentsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListRealtimeContactAnalysisSegmentsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListRealtimeContactAnalysisSegmentsOutput`
    public func listRealtimeContactAnalysisSegmentsPaginated(input: ListRealtimeContactAnalysisSegmentsInput) -> ClientRuntime.PaginatorSequence<ListRealtimeContactAnalysisSegmentsInput, ListRealtimeContactAnalysisSegmentsOutput> {
        return ClientRuntime.PaginatorSequence<ListRealtimeContactAnalysisSegmentsInput, ListRealtimeContactAnalysisSegmentsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listRealtimeContactAnalysisSegments(input:))
    }
}

extension ListRealtimeContactAnalysisSegmentsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListRealtimeContactAnalysisSegmentsInput {
        return ListRealtimeContactAnalysisSegmentsInput(
            contactId: self.contactId,
            instanceId: self.instanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}
