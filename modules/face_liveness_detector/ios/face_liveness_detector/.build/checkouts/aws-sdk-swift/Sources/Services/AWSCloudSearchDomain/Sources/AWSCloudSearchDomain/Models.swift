//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ByteStream
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import func SmithyReadWrite.listReadingClosure
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.<PERSON>y<PERSON>eader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
import struct SmithyHTTPAPI.Header
import struct SmithyHTTPAPI.Headers

/// Information about any problems encountered while processing a search request.
public struct SearchException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// A description of the error returned by the search service.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SearchException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CloudSearchDomainClientTypes {

    public enum QueryParser: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case dismax
        case lucene
        case simple
        case structured
        case sdkUnknown(Swift.String)

        public static var allCases: [QueryParser] {
            return [
                .dismax,
                .lucene,
                .simple,
                .structured
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .dismax: return "dismax"
            case .lucene: return "lucene"
            case .simple: return "simple"
            case .structured: return "structured"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Container for the parameters to the Search request.
public struct SearchInput: Swift.Sendable {
    /// Retrieves a cursor value you can use to page through large result sets. Use the size parameter to control the number of hits to include in each response. You can specify either the cursor or start parameter in a request; they are mutually exclusive. To get the first cursor, set the cursor value to initial. In subsequent requests, specify the cursor value returned in the hits section of the response. For more information, see [Paginating Results](http://docs.aws.amazon.com/cloudsearch/latest/developerguide/paginating-results.html) in the Amazon CloudSearch Developer Guide.
    public var cursor: Swift.String?
    /// Defines one or more numeric expressions that can be used to sort results or specify search or filter criteria. You can also specify expressions as return fields. You specify the expressions in JSON using the form {"EXPRESSIONNAME":"EXPRESSION"}. You can define and use multiple expressions in a search request. For example:  {"expression1":"_score*rating", "expression2":"(1/rank)*year"}  For information about the variables, operators, and functions you can use in expressions, see [Writing Expressions](http://docs.aws.amazon.com/cloudsearch/latest/developerguide/configuring-expressions.html#writing-expressions) in the Amazon CloudSearch Developer Guide.
    public var expr: Swift.String?
    /// Specifies one or more fields for which to get facet information, and options that control how the facet information is returned. Each specified field must be facet-enabled in the domain configuration. The fields and options are specified in JSON using the form {"FIELD":{"OPTION":VALUE,"OPTION:"STRING"},"FIELD":{"OPTION":VALUE,"OPTION":"STRING"}}. You can specify the following faceting options:
    ///
    /// * buckets specifies an array of the facet values or ranges to count. Ranges are specified using the same syntax that you use to search for a range of values. For more information, see [ Searching for a Range of Values](http://docs.aws.amazon.com/cloudsearch/latest/developerguide/searching-ranges.html) in the Amazon CloudSearch Developer Guide. Buckets are returned in the order they are specified in the request. The sort and size options are not valid if you specify buckets.
    ///
    /// * size specifies the maximum number of facets to include in the results. By default, Amazon CloudSearch returns counts for the top 10. The size parameter is only valid when you specify the sort option; it cannot be used in conjunction with buckets.
    ///
    /// * sort specifies how you want to sort the facets in the results: bucket or count. Specify bucket to sort alphabetically or numerically by facet value (in ascending order). Specify count to sort by the facet counts computed for each facet value (in descending order). To retrieve facet counts for particular values or ranges of values, use the buckets option instead of sort.
    ///
    ///
    /// If no facet options are specified, facet counts are computed for all field values, the facets are sorted by facet count, and the top 10 facets are returned in the results. To count particular buckets of values, use the buckets option. For example, the following request uses the buckets option to calculate and return facet counts by decade.  {"year":{"buckets":["[1970,1979]","[1980,1989]","[1990,1999]","[2000,2009]","[2010,}"]}}  To sort facets by facet count, use the count option. For example, the following request sets the sort option to count to sort the facet values by facet count, with the facet values that have the most matching documents listed first. Setting the size option to 3 returns only the top three facet values.  {"year":{"sort":"count","size":3}}  To sort the facets by value, use the bucket option. For example, the following request sets the sort option to bucket to sort the facet values numerically by year, with earliest year listed first.  {"year":{"sort":"bucket"}}  For more information, see [Getting and Using Facet Information](http://docs.aws.amazon.com/cloudsearch/latest/developerguide/faceting.html) in the Amazon CloudSearch Developer Guide.
    public var facet: Swift.String?
    /// Specifies a structured query that filters the results of a search without affecting how the results are scored and sorted. You use filterQuery in conjunction with the query parameter to filter the documents that match the constraints specified in the query parameter. Specifying a filter controls only which matching documents are included in the results, it has no effect on how they are scored and sorted. The filterQuery parameter supports the full structured query syntax. For more information about using filters, see [Filtering Matching Documents](http://docs.aws.amazon.com/cloudsearch/latest/developerguide/filtering-results.html) in the Amazon CloudSearch Developer Guide.
    public var filterQuery: Swift.String?
    /// Retrieves highlights for matches in the specified text or text-array fields. Each specified field must be highlight enabled in the domain configuration. The fields and options are specified in JSON using the form {"FIELD":{"OPTION":VALUE,"OPTION:"STRING"},"FIELD":{"OPTION":VALUE,"OPTION":"STRING"}}. You can specify the following highlight options:
    ///
    /// * format: specifies the format of the data in the text field: text or html. When data is returned as HTML, all non-alphanumeric characters are encoded. The default is html.
    ///
    /// * max_phrases: specifies the maximum number of occurrences of the search term(s) you want to highlight. By default, the first occurrence is highlighted.
    ///
    /// * pre_tag: specifies the string to prepend to an occurrence of a search term. The default for HTML highlights is <em>. The default for text highlights is *.
    ///
    /// * post_tag: specifies the string to append to an occurrence of a search term. The default for HTML highlights is </em>. The default for text highlights is *.
    ///
    ///
    /// If no highlight options are specified for a field, the returned field text is treated as HTML and the first match is highlighted with emphasis tags: <em>search-term</em>. For example, the following request retrieves highlights for the actors and title fields. { "actors": {}, "title": {"format": "text","max_phrases": 2,"pre_tag": "","post_tag": ""} }
    public var highlight: Swift.String?
    /// Enables partial results to be returned if one or more index partitions are unavailable. When your search index is partitioned across multiple search instances, by default Amazon CloudSearch only returns results if every partition can be queried. This means that the failure of a single search instance can result in 5xx (internal server) errors. When you enable partial results, Amazon CloudSearch returns whatever results are available and includes the percentage of documents searched in the search results (percent-searched). This enables you to more gracefully degrade your users' search experience. For example, rather than displaying no results, you could display the partial results and a message indicating that the results might be incomplete due to a temporary system outage.
    public var partial: Swift.Bool?
    /// Specifies the search criteria for the request. How you specify the search criteria depends on the query parser used for the request and the parser options specified in the queryOptions parameter. By default, the simple query parser is used to process requests. To use the structured, lucene, or dismax query parser, you must also specify the queryParser parameter. For more information about specifying search criteria, see [Searching Your Data](http://docs.aws.amazon.com/cloudsearch/latest/developerguide/searching.html) in the Amazon CloudSearch Developer Guide.
    /// This member is required.
    public var query: Swift.String?
    /// Configures options for the query parser specified in the queryParser parameter. You specify the options in JSON using the following form {"OPTION1":"VALUE1","OPTION2":VALUE2"..."OPTIONN":"VALUEN"}. The options you can configure vary according to which parser you use:
    ///
    /// * defaultOperator: The default operator used to combine individual terms in the search string. For example: defaultOperator: 'or'. For the dismax parser, you specify a percentage that represents the percentage of terms in the search string (rounded down) that must match, rather than a default operator. A value of 0% is the equivalent to OR, and a value of 100% is equivalent to AND. The percentage must be specified as a value in the range 0-100 followed by the percent (%) symbol. For example, defaultOperator: 50%. Valid values: and, or, a percentage in the range 0%-100% (dismax). Default: and (simple, structured, lucene) or 100 (dismax). Valid for: simple, structured, lucene, and dismax.
    ///
    /// * fields: An array of the fields to search when no fields are specified in a search. If no fields are specified in a search and this option is not specified, all text and text-array fields are searched. You can specify a weight for each field to control the relative importance of each field when Amazon CloudSearch calculates relevance scores. To specify a field weight, append a caret (^) symbol and the weight to the field name. For example, to boost the importance of the title field over the description field you could specify: "fields":["title^5","description"]. Valid values: The name of any configured field and an optional numeric value greater than zero. Default: All text and text-array fields. Valid for: simple, structured, lucene, and dismax.
    ///
    /// * operators: An array of the operators or special characters you want to disable for the simple query parser. If you disable the and, or, or not operators, the corresponding operators (+, |, -) have no special meaning and are dropped from the search string. Similarly, disabling prefix disables the wildcard operator (*) and disabling phrase disables the ability to search for phrases by enclosing phrases in double quotes. Disabling precedence disables the ability to control order of precedence using parentheses. Disabling near disables the ability to use the ~ operator to perform a sloppy phrase search. Disabling the fuzzy operator disables the ability to use the ~ operator to perform a fuzzy search. escape disables the ability to use a backslash (</code>) to escape special characters within the search string. Disabling whitespace is an advanced option that prevents the parser from tokenizing on whitespace, which can be useful for Vietnamese. (It prevents Vietnamese words from being split incorrectly.) For example, you could disable all operators other than the phrase operator to support just simple term and phrase queries: "operators":["and","not","or", "prefix"]. Valid values: and, escape, fuzzy, near, not, or, phrase, precedence, prefix, whitespace. Default: All operators and special characters are enabled. Valid for: simple.
    ///
    /// * phraseFields: An array of the text or text-array fields you want to use for phrase searches. When the terms in the search string appear in close proximity within a field, the field scores higher. You can specify a weight for each field to boost that score. The phraseSlop option controls how much the matches can deviate from the search string and still be boosted. To specify a field weight, append a caret (^) symbol and the weight to the field name. For example, to boost phrase matches in the title field over the abstract field, you could specify: "phraseFields":["title^3", "plot"] Valid values: The name of any text or text-array field and an optional numeric value greater than zero. Default: No fields. If you don't specify any fields with phraseFields, proximity scoring is disabled even if phraseSlop is specified. Valid for: dismax.
    ///
    /// * phraseSlop: An integer value that specifies how much matches can deviate from the search phrase and still be boosted according to the weights specified in the phraseFields option; for example, phraseSlop: 2. You must also specify phraseFields to enable proximity scoring. Valid values: positive integers. Default: 0. Valid for: dismax.
    ///
    /// * explicitPhraseSlop: An integer value that specifies how much a match can deviate from the search phrase when the phrase is enclosed in double quotes in the search string. (Phrases that exceed this proximity distance are not considered a match.) For example, to specify a slop of three for dismax phrase queries, you would specify "explicitPhraseSlop":3. Valid values: positive integers. Default: 0. Valid for: dismax.
    ///
    /// * tieBreaker: When a term in the search string is found in a document's field, a score is calculated for that field based on how common the word is in that field compared to other documents. If the term occurs in multiple fields within a document, by default only the highest scoring field contributes to the document's overall score. You can specify a tieBreaker value to enable the matches in lower-scoring fields to contribute to the document's score. That way, if two documents have the same max field score for a particular term, the score for the document that has matches in more fields will be higher. The formula for calculating the score with a tieBreaker is (max field score) + (tieBreaker) * (sum of the scores for the rest of the matching fields). Set tieBreaker to 0 to disregard all but the highest scoring field (pure max): "tieBreaker":0. Set to 1 to sum the scores from all fields (pure sum): "tieBreaker":1. Valid values: 0.0 to 1.0. Default: 0.0. Valid for: dismax.
    public var queryOptions: Swift.String?
    /// Specifies which query parser to use to process the request. If queryParser is not specified, Amazon CloudSearch uses the simple query parser. Amazon CloudSearch supports four query parsers:
    ///
    /// * simple: perform simple searches of text and text-array fields. By default, the simple query parser searches all text and text-array fields. You can specify which fields to search by with the queryOptions parameter. If you prefix a search term with a plus sign (+) documents must contain the term to be considered a match. (This is the default, unless you configure the default operator with the queryOptions parameter.) You can use the - (NOT), | (OR), and * (wildcard) operators to exclude particular terms, find results that match any of the specified terms, or search for a prefix. To search for a phrase rather than individual terms, enclose the phrase in double quotes. For more information, see [Searching for Text](http://docs.aws.amazon.com/cloudsearch/latest/developerguide/searching-text.html) in the Amazon CloudSearch Developer Guide.
    ///
    /// * structured: perform advanced searches by combining multiple expressions to define the search criteria. You can also search within particular fields, search for values and ranges of values, and use advanced options such as term boosting, matchall, and near. For more information, see [Constructing Compound Queries](http://docs.aws.amazon.com/cloudsearch/latest/developerguide/searching-compound-queries.html) in the Amazon CloudSearch Developer Guide.
    ///
    /// * lucene: search using the Apache Lucene query parser syntax. For more information, see [Apache Lucene Query Parser Syntax](http://lucene.apache.org/core/4_6_0/queryparser/org/apache/lucene/queryparser/classic/package-summary.html#package_description).
    ///
    /// * dismax: search using the simplified subset of the Apache Lucene query parser syntax defined by the DisMax query parser. For more information, see [DisMax Query Parser Syntax](http://wiki.apache.org/solr/DisMaxQParserPlugin#Query_Syntax).
    public var queryParser: CloudSearchDomainClientTypes.QueryParser?
    /// Specifies the field and expression values to include in the response. Multiple fields or expressions are specified as a comma-separated list. By default, a search response includes all return enabled fields (_all_fields). To return only the document IDs for the matching documents, specify _no_fields. To retrieve the relevance score calculated for each document, specify _score.
    public var `return`: Swift.String?
    /// Specifies the maximum number of search hits to include in the response.
    public var size: Swift.Int?
    /// Specifies the fields or custom expressions to use to sort the search results. Multiple fields or expressions are specified as a comma-separated list. You must specify the sort direction (asc or desc) for each field; for example, year desc,title asc. To use a field to sort results, the field must be sort-enabled in the domain configuration. Array type fields cannot be used for sorting. If no sort parameter is specified, results are sorted by their default relevance scores in descending order: _score desc. You can also sort by document ID (_id asc) and version (_version desc). For more information, see [Sorting Results](http://docs.aws.amazon.com/cloudsearch/latest/developerguide/sorting-results.html) in the Amazon CloudSearch Developer Guide.
    public var sort: Swift.String?
    /// Specifies the offset of the first search hit you want to return. Note that the result set is zero-based; the first result is at index 0. You can specify either the start or cursor parameter in a request, they are mutually exclusive. For more information, see [Paginating Results](http://docs.aws.amazon.com/cloudsearch/latest/developerguide/paginating-results.html) in the Amazon CloudSearch Developer Guide.
    public var start: Swift.Int?
    /// Specifies one or more fields for which to get statistics information. Each specified field must be facet-enabled in the domain configuration. The fields are specified in JSON using the form: {"FIELD-A":{},"FIELD-B":{}} There are currently no options supported for statistics.
    public var stats: Swift.String?

    public init(
        cursor: Swift.String? = nil,
        expr: Swift.String? = nil,
        facet: Swift.String? = nil,
        filterQuery: Swift.String? = nil,
        highlight: Swift.String? = nil,
        partial: Swift.Bool? = false,
        query: Swift.String? = nil,
        queryOptions: Swift.String? = nil,
        queryParser: CloudSearchDomainClientTypes.QueryParser? = nil,
        `return`: Swift.String? = nil,
        size: Swift.Int? = 0,
        sort: Swift.String? = nil,
        start: Swift.Int? = 0,
        stats: Swift.String? = nil
    )
    {
        self.cursor = cursor
        self.expr = expr
        self.facet = facet
        self.filterQuery = filterQuery
        self.highlight = highlight
        self.partial = partial
        self.query = query
        self.queryOptions = queryOptions
        self.queryParser = queryParser
        self.`return` = `return`
        self.size = size
        self.sort = sort
        self.start = start
        self.stats = stats
    }
}

extension CloudSearchDomainClientTypes {

    /// A container for facet information.
    public struct Bucket: Swift.Sendable {
        /// The number of hits that contain the facet value in the specified facet field.
        public var count: Swift.Int
        /// The facet value being counted.
        public var value: Swift.String?

        public init(
            count: Swift.Int = 0,
            value: Swift.String? = nil
        )
        {
            self.count = count
            self.value = value
        }
    }
}

extension CloudSearchDomainClientTypes {

    /// A container for the calculated facet values and counts.
    public struct BucketInfo: Swift.Sendable {
        /// A list of the calculated facet values and counts.
        public var buckets: [CloudSearchDomainClientTypes.Bucket]?

        public init(
            buckets: [CloudSearchDomainClientTypes.Bucket]? = nil
        )
        {
            self.buckets = buckets
        }
    }
}

extension CloudSearchDomainClientTypes {

    /// Information about a document that matches the search request.
    public struct Hit: Swift.Sendable {
        /// The expressions returned from a document that matches the search request.
        public var exprs: [Swift.String: Swift.String]?
        /// The fields returned from a document that matches the search request.
        public var fields: [Swift.String: [Swift.String]]?
        /// The highlights returned from a document that matches the search request.
        public var highlights: [Swift.String: Swift.String]?
        /// The document ID of a document that matches the search request.
        public var id: Swift.String?

        public init(
            exprs: [Swift.String: Swift.String]? = nil,
            fields: [Swift.String: [Swift.String]]? = nil,
            highlights: [Swift.String: Swift.String]? = nil,
            id: Swift.String? = nil
        )
        {
            self.exprs = exprs
            self.fields = fields
            self.highlights = highlights
            self.id = id
        }
    }
}

extension CloudSearchDomainClientTypes {

    /// The collection of documents that match the search request.
    public struct Hits: Swift.Sendable {
        /// A cursor that can be used to retrieve the next set of matching documents when you want to page through a large result set.
        public var cursor: Swift.String?
        /// The total number of documents that match the search request.
        public var found: Swift.Int
        /// A document that matches the search request.
        public var hit: [CloudSearchDomainClientTypes.Hit]?
        /// The index of the first matching document.
        public var start: Swift.Int

        public init(
            cursor: Swift.String? = nil,
            found: Swift.Int = 0,
            hit: [CloudSearchDomainClientTypes.Hit]? = nil,
            start: Swift.Int = 0
        )
        {
            self.cursor = cursor
            self.found = found
            self.hit = hit
            self.start = start
        }
    }
}

extension CloudSearchDomainClientTypes {

    /// The statistics for a field calculated in the request.
    public struct FieldStats: Swift.Sendable {
        /// The number of documents that contain a value in the specified field in the result set.
        public var count: Swift.Int
        /// The maximum value found in the specified field in the result set. If the field is numeric (int, int-array, double, or double-array), max is the string representation of a double-precision 64-bit floating point value. If the field is date or date-array, max is the string representation of a date with the format specified in [IETF RFC3339](http://tools.ietf.org/html/rfc3339): yyyy-mm-ddTHH:mm:ss.SSSZ.
        public var max: Swift.String?
        /// The average of the values found in the specified field in the result set. If the field is numeric (int, int-array, double, or double-array), mean is the string representation of a double-precision 64-bit floating point value. If the field is date or date-array, mean is the string representation of a date with the format specified in [IETF RFC3339](http://tools.ietf.org/html/rfc3339): yyyy-mm-ddTHH:mm:ss.SSSZ.
        public var mean: Swift.String?
        /// The minimum value found in the specified field in the result set. If the field is numeric (int, int-array, double, or double-array), min is the string representation of a double-precision 64-bit floating point value. If the field is date or date-array, min is the string representation of a date with the format specified in [IETF RFC3339](http://tools.ietf.org/html/rfc3339): yyyy-mm-ddTHH:mm:ss.SSSZ.
        public var min: Swift.String?
        /// The number of documents that do not contain a value in the specified field in the result set.
        public var missing: Swift.Int
        /// The standard deviation of the values in the specified field in the result set.
        public var stddev: Swift.Double
        /// The sum of the field values across the documents in the result set. null for date fields.
        public var sum: Swift.Double
        /// The sum of all field values in the result set squared.
        public var sumOfSquares: Swift.Double

        public init(
            count: Swift.Int = 0,
            max: Swift.String? = nil,
            mean: Swift.String? = nil,
            min: Swift.String? = nil,
            missing: Swift.Int = 0,
            stddev: Swift.Double = 0.0,
            sum: Swift.Double = 0.0,
            sumOfSquares: Swift.Double = 0.0
        )
        {
            self.count = count
            self.max = max
            self.mean = mean
            self.min = min
            self.missing = missing
            self.stddev = stddev
            self.sum = sum
            self.sumOfSquares = sumOfSquares
        }
    }
}

extension CloudSearchDomainClientTypes {

    /// Contains the resource id (rid) and the time it took to process the request (timems).
    public struct SearchStatus: Swift.Sendable {
        /// The encrypted resource ID for the request.
        public var rid: Swift.String?
        /// How long it took to process the request, in milliseconds.
        public var timems: Swift.Int

        public init(
            rid: Swift.String? = nil,
            timems: Swift.Int = 0
        )
        {
            self.rid = rid
            self.timems = timems
        }
    }
}

/// The result of a Search request. Contains the documents that match the specified search criteria and any requested fields, highlights, and facet information.
public struct SearchOutput: Swift.Sendable {
    /// The requested facet information.
    public var facets: [Swift.String: CloudSearchDomainClientTypes.BucketInfo]?
    /// The documents that match the search criteria.
    public var hits: CloudSearchDomainClientTypes.Hits?
    /// The requested field statistics information.
    public var stats: [Swift.String: CloudSearchDomainClientTypes.FieldStats]?
    /// The status information returned for the search request.
    public var status: CloudSearchDomainClientTypes.SearchStatus?

    public init(
        facets: [Swift.String: CloudSearchDomainClientTypes.BucketInfo]? = nil,
        hits: CloudSearchDomainClientTypes.Hits? = nil,
        stats: [Swift.String: CloudSearchDomainClientTypes.FieldStats]? = nil,
        status: CloudSearchDomainClientTypes.SearchStatus? = nil
    )
    {
        self.facets = facets
        self.hits = hits
        self.stats = stats
        self.status = status
    }
}

/// Container for the parameters to the Suggest request.
public struct SuggestInput: Swift.Sendable {
    /// Specifies the string for which you want to get suggestions.
    /// This member is required.
    public var query: Swift.String?
    /// Specifies the maximum number of suggestions to return.
    public var size: Swift.Int?
    /// Specifies the name of the suggester to use to find suggested matches.
    /// This member is required.
    public var suggester: Swift.String?

    public init(
        query: Swift.String? = nil,
        size: Swift.Int? = 0,
        suggester: Swift.String? = nil
    )
    {
        self.query = query
        self.size = size
        self.suggester = suggester
    }
}

extension CloudSearchDomainClientTypes {

    /// Contains the resource id (rid) and the time it took to process the request (timems).
    public struct SuggestStatus: Swift.Sendable {
        /// The encrypted resource ID for the request.
        public var rid: Swift.String?
        /// How long it took to process the request, in milliseconds.
        public var timems: Swift.Int

        public init(
            rid: Swift.String? = nil,
            timems: Swift.Int = 0
        )
        {
            self.rid = rid
            self.timems = timems
        }
    }
}

extension CloudSearchDomainClientTypes {

    /// An autocomplete suggestion that matches the query string specified in a SuggestRequest.
    public struct SuggestionMatch: Swift.Sendable {
        /// The document ID of the suggested document.
        public var id: Swift.String?
        /// The relevance score of a suggested match.
        public var score: Swift.Int
        /// The string that matches the query string specified in the SuggestRequest.
        public var suggestion: Swift.String?

        public init(
            id: Swift.String? = nil,
            score: Swift.Int = 0,
            suggestion: Swift.String? = nil
        )
        {
            self.id = id
            self.score = score
            self.suggestion = suggestion
        }
    }
}

extension CloudSearchDomainClientTypes {

    /// Container for the suggestion information returned in a SuggestResponse.
    public struct SuggestModel: Swift.Sendable {
        /// The number of documents that were found to match the query string.
        public var found: Swift.Int
        /// The query string specified in the suggest request.
        public var query: Swift.String?
        /// The documents that match the query string.
        public var suggestions: [CloudSearchDomainClientTypes.SuggestionMatch]?

        public init(
            found: Swift.Int = 0,
            query: Swift.String? = nil,
            suggestions: [CloudSearchDomainClientTypes.SuggestionMatch]? = nil
        )
        {
            self.found = found
            self.query = query
            self.suggestions = suggestions
        }
    }
}

/// Contains the response to a Suggest request.
public struct SuggestOutput: Swift.Sendable {
    /// The status of a SuggestRequest. Contains the resource ID (rid) and how long it took to process the request (timems).
    public var status: CloudSearchDomainClientTypes.SuggestStatus?
    /// Container for the matching search suggestion information.
    public var suggest: CloudSearchDomainClientTypes.SuggestModel?

    public init(
        status: CloudSearchDomainClientTypes.SuggestStatus? = nil,
        suggest: CloudSearchDomainClientTypes.SuggestModel? = nil
    )
    {
        self.status = status
        self.suggest = suggest
    }
}

/// Information about any problems encountered while processing an upload request.
public struct DocumentServiceException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The description of the errors returned by the document service.
        public internal(set) var message: Swift.String? = nil
        /// The return status of a document upload request, error or success.
        public internal(set) var status: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DocumentServiceException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        status: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.status = status
    }
}

extension CloudSearchDomainClientTypes {

    public enum ContentType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case applicationJson
        case applicationXml
        case sdkUnknown(Swift.String)

        public static var allCases: [ContentType] {
            return [
                .applicationJson,
                .applicationXml
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .applicationJson: return "application/json"
            case .applicationXml: return "application/xml"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Container for the parameters to the UploadDocuments request.
public struct UploadDocumentsInput: Swift.Sendable {
    /// The format of the batch you are uploading. Amazon CloudSearch supports two document batch formats:
    ///
    /// * application/json
    ///
    /// * application/xml
    /// This member is required.
    public var contentType: CloudSearchDomainClientTypes.ContentType?
    /// A batch of documents formatted in JSON or HTML.
    /// This member is required.
    public var documents: Smithy.ByteStream?

    public init(
        contentType: CloudSearchDomainClientTypes.ContentType? = nil,
        documents: Smithy.ByteStream? = nil
    )
    {
        self.contentType = contentType
        self.documents = documents
    }
}

extension CloudSearchDomainClientTypes {

    /// A warning returned by the document service when an issue is discovered while processing an upload request.
    public struct DocumentServiceWarning: Swift.Sendable {
        /// The description for a warning returned by the document service.
        public var message: Swift.String?

        public init(
            message: Swift.String? = nil
        )
        {
            self.message = message
        }
    }
}

/// Contains the response to an UploadDocuments request.
public struct UploadDocumentsOutput: Swift.Sendable {
    /// The number of documents that were added to the search domain.
    public var adds: Swift.Int
    /// The number of documents that were deleted from the search domain.
    public var deletes: Swift.Int
    /// The status of an UploadDocumentsRequest.
    public var status: Swift.String?
    /// Any warnings returned by the document service about the documents being uploaded.
    public var warnings: [CloudSearchDomainClientTypes.DocumentServiceWarning]?

    public init(
        adds: Swift.Int = 0,
        deletes: Swift.Int = 0,
        status: Swift.String? = nil,
        warnings: [CloudSearchDomainClientTypes.DocumentServiceWarning]? = nil
    )
    {
        self.adds = adds
        self.deletes = deletes
        self.status = status
        self.warnings = warnings
    }
}

extension SearchInput {

    static func urlPathProvider(_ value: SearchInput) -> Swift.String? {
        return "/2013-01-01/search"
    }
}

extension SearchInput {

    static func queryItemProvider(_ value: SearchInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        items.append(Smithy.URIQueryItem(name: "format", value: "sdk"))
        items.append(Smithy.URIQueryItem(name: "pretty", value: "true"))
        if let cursor = value.cursor {
            let cursorQueryItem = Smithy.URIQueryItem(name: "cursor".urlPercentEncoding(), value: Swift.String(cursor).urlPercentEncoding())
            items.append(cursorQueryItem)
        }
        if let queryOptions = value.queryOptions {
            let queryOptionsQueryItem = Smithy.URIQueryItem(name: "q.options".urlPercentEncoding(), value: Swift.String(queryOptions).urlPercentEncoding())
            items.append(queryOptionsQueryItem)
        }
        guard let query = value.query else {
            let message = "Creating a URL Query Item failed. query is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let queryQueryItem = Smithy.URIQueryItem(name: "q".urlPercentEncoding(), value: Swift.String(query).urlPercentEncoding())
        items.append(queryQueryItem)
        if let start = value.start {
            let startQueryItem = Smithy.URIQueryItem(name: "start".urlPercentEncoding(), value: Swift.String(start).urlPercentEncoding())
            items.append(startQueryItem)
        }
        if let sort = value.sort {
            let sortQueryItem = Smithy.URIQueryItem(name: "sort".urlPercentEncoding(), value: Swift.String(sort).urlPercentEncoding())
            items.append(sortQueryItem)
        }
        if let queryParser = value.queryParser {
            let queryParserQueryItem = Smithy.URIQueryItem(name: "q.parser".urlPercentEncoding(), value: Swift.String(queryParser.rawValue).urlPercentEncoding())
            items.append(queryParserQueryItem)
        }
        if let highlight = value.highlight {
            let highlightQueryItem = Smithy.URIQueryItem(name: "highlight".urlPercentEncoding(), value: Swift.String(highlight).urlPercentEncoding())
            items.append(highlightQueryItem)
        }
        if let size = value.size {
            let sizeQueryItem = Smithy.URIQueryItem(name: "size".urlPercentEncoding(), value: Swift.String(size).urlPercentEncoding())
            items.append(sizeQueryItem)
        }
        if let stats = value.stats {
            let statsQueryItem = Smithy.URIQueryItem(name: "stats".urlPercentEncoding(), value: Swift.String(stats).urlPercentEncoding())
            items.append(statsQueryItem)
        }
        if let expr = value.expr {
            let exprQueryItem = Smithy.URIQueryItem(name: "expr".urlPercentEncoding(), value: Swift.String(expr).urlPercentEncoding())
            items.append(exprQueryItem)
        }
        if let facet = value.facet {
            let facetQueryItem = Smithy.URIQueryItem(name: "facet".urlPercentEncoding(), value: Swift.String(facet).urlPercentEncoding())
            items.append(facetQueryItem)
        }
        if let partial = value.partial {
            let partialQueryItem = Smithy.URIQueryItem(name: "partial".urlPercentEncoding(), value: Swift.String(partial).urlPercentEncoding())
            items.append(partialQueryItem)
        }
        if let `return` = value.`return` {
            let returnQueryItem = Smithy.URIQueryItem(name: "return".urlPercentEncoding(), value: Swift.String(`return`).urlPercentEncoding())
            items.append(returnQueryItem)
        }
        if let filterQuery = value.filterQuery {
            let filterQueryQueryItem = Smithy.URIQueryItem(name: "fq".urlPercentEncoding(), value: Swift.String(filterQuery).urlPercentEncoding())
            items.append(filterQueryQueryItem)
        }
        return items
    }
}

extension SuggestInput {

    static func urlPathProvider(_ value: SuggestInput) -> Swift.String? {
        return "/2013-01-01/suggest"
    }
}

extension SuggestInput {

    static func queryItemProvider(_ value: SuggestInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        items.append(Smithy.URIQueryItem(name: "format", value: "sdk"))
        items.append(Smithy.URIQueryItem(name: "pretty", value: "true"))
        guard let suggester = value.suggester else {
            let message = "Creating a URL Query Item failed. suggester is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let suggesterQueryItem = Smithy.URIQueryItem(name: "suggester".urlPercentEncoding(), value: Swift.String(suggester).urlPercentEncoding())
        items.append(suggesterQueryItem)
        if let size = value.size {
            let sizeQueryItem = Smithy.URIQueryItem(name: "size".urlPercentEncoding(), value: Swift.String(size).urlPercentEncoding())
            items.append(sizeQueryItem)
        }
        guard let query = value.query else {
            let message = "Creating a URL Query Item failed. query is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let queryQueryItem = Smithy.URIQueryItem(name: "q".urlPercentEncoding(), value: Swift.String(query).urlPercentEncoding())
        items.append(queryQueryItem)
        return items
    }
}

extension UploadDocumentsInput {

    static func urlPathProvider(_ value: UploadDocumentsInput) -> Swift.String? {
        return "/2013-01-01/documents/batch"
    }
}

extension UploadDocumentsInput {

    static func headerProvider(_ value: UploadDocumentsInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let contentType = value.contentType {
            items.add(SmithyHTTPAPI.Header(name: "Content-Type", value: Swift.String(contentType.rawValue)))
        }
        return items
    }
}

extension UploadDocumentsInput {

    static func queryItemProvider(_ value: UploadDocumentsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        items.append(Smithy.URIQueryItem(name: "format", value: "sdk"))
        return items
    }
}

extension UploadDocumentsInput {

    static func write(value: UploadDocumentsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["documents"].write(value.documents)
    }
}

extension SearchOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SearchOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = SearchOutput()
        value.facets = try reader["facets"].readMapIfPresent(valueReadingClosure: CloudSearchDomainClientTypes.BucketInfo.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.hits = try reader["hits"].readIfPresent(with: CloudSearchDomainClientTypes.Hits.read(from:))
        value.stats = try reader["stats"].readMapIfPresent(valueReadingClosure: CloudSearchDomainClientTypes.FieldStats.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.status = try reader["status"].readIfPresent(with: CloudSearchDomainClientTypes.SearchStatus.read(from:))
        return value
    }
}

extension SuggestOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SuggestOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = SuggestOutput()
        value.status = try reader["status"].readIfPresent(with: CloudSearchDomainClientTypes.SuggestStatus.read(from:))
        value.suggest = try reader["suggest"].readIfPresent(with: CloudSearchDomainClientTypes.SuggestModel.read(from:))
        return value
    }
}

extension UploadDocumentsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UploadDocumentsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UploadDocumentsOutput()
        value.adds = try reader["adds"].readIfPresent() ?? 0
        value.deletes = try reader["deletes"].readIfPresent() ?? 0
        value.status = try reader["status"].readIfPresent()
        value.warnings = try reader["warnings"].readListIfPresent(memberReadingClosure: CloudSearchDomainClientTypes.DocumentServiceWarning.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

enum SearchOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "SearchException": return try SearchException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SuggestOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "SearchException": return try SearchException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UploadDocumentsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DocumentServiceException": return try DocumentServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension SearchException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> SearchException {
        let reader = baseError.errorBodyReader
        var value = SearchException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DocumentServiceException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> DocumentServiceException {
        let reader = baseError.errorBodyReader
        var value = DocumentServiceException()
        value.properties.message = try reader["message"].readIfPresent()
        value.properties.status = try reader["status"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CloudSearchDomainClientTypes.SearchStatus {

    static func read(from reader: SmithyJSON.Reader) throws -> CloudSearchDomainClientTypes.SearchStatus {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CloudSearchDomainClientTypes.SearchStatus()
        value.timems = try reader["timems"].readIfPresent() ?? 0
        value.rid = try reader["rid"].readIfPresent()
        return value
    }
}

extension CloudSearchDomainClientTypes.Hits {

    static func read(from reader: SmithyJSON.Reader) throws -> CloudSearchDomainClientTypes.Hits {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CloudSearchDomainClientTypes.Hits()
        value.found = try reader["found"].readIfPresent() ?? 0
        value.start = try reader["start"].readIfPresent() ?? 0
        value.cursor = try reader["cursor"].readIfPresent()
        value.hit = try reader["hit"].readListIfPresent(memberReadingClosure: CloudSearchDomainClientTypes.Hit.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension CloudSearchDomainClientTypes.Hit {

    static func read(from reader: SmithyJSON.Reader) throws -> CloudSearchDomainClientTypes.Hit {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CloudSearchDomainClientTypes.Hit()
        value.id = try reader["id"].readIfPresent()
        value.fields = try reader["fields"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.listReadingClosure(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.exprs = try reader["exprs"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.highlights = try reader["highlights"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension CloudSearchDomainClientTypes.BucketInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> CloudSearchDomainClientTypes.BucketInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CloudSearchDomainClientTypes.BucketInfo()
        value.buckets = try reader["buckets"].readListIfPresent(memberReadingClosure: CloudSearchDomainClientTypes.Bucket.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension CloudSearchDomainClientTypes.Bucket {

    static func read(from reader: SmithyJSON.Reader) throws -> CloudSearchDomainClientTypes.Bucket {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CloudSearchDomainClientTypes.Bucket()
        value.value = try reader["value"].readIfPresent()
        value.count = try reader["count"].readIfPresent() ?? 0
        return value
    }
}

extension CloudSearchDomainClientTypes.FieldStats {

    static func read(from reader: SmithyJSON.Reader) throws -> CloudSearchDomainClientTypes.FieldStats {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CloudSearchDomainClientTypes.FieldStats()
        value.min = try reader["min"].readIfPresent()
        value.max = try reader["max"].readIfPresent()
        value.count = try reader["count"].readIfPresent() ?? 0
        value.missing = try reader["missing"].readIfPresent() ?? 0
        value.sum = try reader["sum"].readIfPresent() ?? 0
        value.sumOfSquares = try reader["sumOfSquares"].readIfPresent() ?? 0
        value.mean = try reader["mean"].readIfPresent()
        value.stddev = try reader["stddev"].readIfPresent() ?? 0
        return value
    }
}

extension CloudSearchDomainClientTypes.SuggestStatus {

    static func read(from reader: SmithyJSON.Reader) throws -> CloudSearchDomainClientTypes.SuggestStatus {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CloudSearchDomainClientTypes.SuggestStatus()
        value.timems = try reader["timems"].readIfPresent() ?? 0
        value.rid = try reader["rid"].readIfPresent()
        return value
    }
}

extension CloudSearchDomainClientTypes.SuggestModel {

    static func read(from reader: SmithyJSON.Reader) throws -> CloudSearchDomainClientTypes.SuggestModel {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CloudSearchDomainClientTypes.SuggestModel()
        value.query = try reader["query"].readIfPresent()
        value.found = try reader["found"].readIfPresent() ?? 0
        value.suggestions = try reader["suggestions"].readListIfPresent(memberReadingClosure: CloudSearchDomainClientTypes.SuggestionMatch.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension CloudSearchDomainClientTypes.SuggestionMatch {

    static func read(from reader: SmithyJSON.Reader) throws -> CloudSearchDomainClientTypes.SuggestionMatch {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CloudSearchDomainClientTypes.SuggestionMatch()
        value.suggestion = try reader["suggestion"].readIfPresent()
        value.score = try reader["score"].readIfPresent() ?? 0
        value.id = try reader["id"].readIfPresent()
        return value
    }
}

extension CloudSearchDomainClientTypes.DocumentServiceWarning {

    static func read(from reader: SmithyJSON.Reader) throws -> CloudSearchDomainClientTypes.DocumentServiceWarning {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CloudSearchDomainClientTypes.DocumentServiceWarning()
        value.message = try reader["message"].readIfPresent()
        return value
    }
}

public enum CloudSearchDomainClientTypes {}
