//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension DocDBElasticClient {
    /// Paginate over `[ListClustersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListClustersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListClustersOutput`
    public func listClustersPaginated(input: ListClustersInput) -> ClientRuntime.PaginatorSequence<ListClustersInput, ListClustersOutput> {
        return ClientRuntime.PaginatorSequence<ListClustersInput, ListClustersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listClusters(input:))
    }
}

extension ListClustersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListClustersInput {
        return ListClustersInput(
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListClustersInput, OperationStackOutput == ListClustersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listClustersPaginated`
    /// to access the nested member `[DocDBElasticClientTypes.ClusterInList]`
    /// - Returns: `[DocDBElasticClientTypes.ClusterInList]`
    public func clusters() async throws -> [DocDBElasticClientTypes.ClusterInList] {
        return try await self.asyncCompactMap { item in item.clusters }
    }
}
extension DocDBElasticClient {
    /// Paginate over `[ListClusterSnapshotsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListClusterSnapshotsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListClusterSnapshotsOutput`
    public func listClusterSnapshotsPaginated(input: ListClusterSnapshotsInput) -> ClientRuntime.PaginatorSequence<ListClusterSnapshotsInput, ListClusterSnapshotsOutput> {
        return ClientRuntime.PaginatorSequence<ListClusterSnapshotsInput, ListClusterSnapshotsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listClusterSnapshots(input:))
    }
}

extension ListClusterSnapshotsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListClusterSnapshotsInput {
        return ListClusterSnapshotsInput(
            clusterArn: self.clusterArn,
            maxResults: self.maxResults,
            nextToken: token,
            snapshotType: self.snapshotType
        )}
}

extension PaginatorSequence where OperationStackInput == ListClusterSnapshotsInput, OperationStackOutput == ListClusterSnapshotsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listClusterSnapshotsPaginated`
    /// to access the nested member `[DocDBElasticClientTypes.ClusterSnapshotInList]`
    /// - Returns: `[DocDBElasticClientTypes.ClusterSnapshotInList]`
    public func snapshots() async throws -> [DocDBElasticClientTypes.ClusterSnapshotInList] {
        return try await self.asyncCompactMap { item in item.snapshots }
    }
}
extension DocDBElasticClient {
    /// Paginate over `[ListPendingMaintenanceActionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListPendingMaintenanceActionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListPendingMaintenanceActionsOutput`
    public func listPendingMaintenanceActionsPaginated(input: ListPendingMaintenanceActionsInput) -> ClientRuntime.PaginatorSequence<ListPendingMaintenanceActionsInput, ListPendingMaintenanceActionsOutput> {
        return ClientRuntime.PaginatorSequence<ListPendingMaintenanceActionsInput, ListPendingMaintenanceActionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listPendingMaintenanceActions(input:))
    }
}

extension ListPendingMaintenanceActionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListPendingMaintenanceActionsInput {
        return ListPendingMaintenanceActionsInput(
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListPendingMaintenanceActionsInput, OperationStackOutput == ListPendingMaintenanceActionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listPendingMaintenanceActionsPaginated`
    /// to access the nested member `[DocDBElasticClientTypes.ResourcePendingMaintenanceAction]`
    /// - Returns: `[DocDBElasticClientTypes.ResourcePendingMaintenanceAction]`
    public func resourcePendingMaintenanceActions() async throws -> [DocDBElasticClientTypes.ResourcePendingMaintenanceAction] {
        return try await self.asyncCompactMap { item in item.resourcePendingMaintenanceActions }
    }
}
