//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
@_spi(SmithyReadWrite) import func SmithyReadWrite.mapReadingClosure
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
@_spi(SmithyReadWrite) import struct SmithyReadWrite.ReadingClosureBox
@_spi(SmithyReadWrite) import struct SmithyReadWrite.WritingClosureBox


public struct AcceptInvitationOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteGraphOutput: Swift.Sendable {

    public init() { }
}

public struct DisableOrganizationAdminAccountInput: Swift.Sendable {

    public init() { }
}

public struct DisableOrganizationAdminAccountOutput: Swift.Sendable {

    public init() { }
}

public struct DisassociateMembershipOutput: Swift.Sendable {

    public init() { }
}

public struct EnableOrganizationAdminAccountOutput: Swift.Sendable {

    public init() { }
}

public struct RejectInvitationOutput: Swift.Sendable {

    public init() { }
}

public struct StartMonitoringMemberOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateDatasourcePackagesOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateInvestigationStateOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateOrganizationConfigurationOutput: Swift.Sendable {

    public init() { }
}

extension DetectiveClientTypes {

    public enum ErrorCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case internalerror
        case invalidgrapharn
        case invalidrequestbody
        case sdkUnknown(Swift.String)

        public static var allCases: [ErrorCode] {
            return [
                .internalerror,
                .invalidgrapharn,
                .invalidrequestbody
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .internalerror: return "INTERNAL_ERROR"
            case .invalidgrapharn: return "INVALID_GRAPH_ARN"
            case .invalidrequestbody: return "INVALID_REQUEST_BODY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The request issuer does not have permission to access this resource or perform this operation.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The SDK default error code associated with the access denied exception.
        public internal(set) var errorCode: DetectiveClientTypes.ErrorCode? = nil
        /// The SDK default explanation of why access was denied.
        public internal(set) var errorCodeReason: Swift.String? = nil
        public internal(set) var message: Swift.String? = nil
        /// The error code associated with the access denied exception.
        public internal(set) var subErrorCode: DetectiveClientTypes.ErrorCode? = nil
        /// An explanation of why access was denied.
        public internal(set) var subErrorCodeReason: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: DetectiveClientTypes.ErrorCode? = nil,
        errorCodeReason: Swift.String? = nil,
        message: Swift.String? = nil,
        subErrorCode: DetectiveClientTypes.ErrorCode? = nil,
        subErrorCodeReason: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.errorCodeReason = errorCodeReason
        self.properties.message = message
        self.properties.subErrorCode = subErrorCode
        self.properties.subErrorCodeReason = subErrorCodeReason
    }
}

/// The request attempted an invalid action.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request was valid but failed because of a problem with the service.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request refers to a nonexistent resource.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request parameters are invalid.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The error code associated with the validation failure.
        public internal(set) var errorCode: DetectiveClientTypes.ErrorCode? = nil
        /// An explanation of why validation failed.
        public internal(set) var errorCodeReason: Swift.String? = nil
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        errorCode: DetectiveClientTypes.ErrorCode? = nil,
        errorCodeReason: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.errorCode = errorCode
        self.properties.errorCodeReason = errorCodeReason
        self.properties.message = message
    }
}

public struct AcceptInvitationInput: Swift.Sendable {
    /// The ARN of the behavior graph that the member account is accepting the invitation for. The member account status in the behavior graph must be INVITED.
    /// This member is required.
    public var graphArn: Swift.String?

    public init(
        graphArn: Swift.String? = nil
    )
    {
        self.graphArn = graphArn
    }
}

extension DetectiveClientTypes {

    /// An Amazon Web Services account that is the administrator account of or a member of a behavior graph.
    public struct Account: Swift.Sendable {
        /// The account identifier of the Amazon Web Services account.
        /// This member is required.
        public var accountId: Swift.String?
        /// The Amazon Web Services account root user email address for the Amazon Web Services account.
        /// This member is required.
        public var emailAddress: Swift.String?

        public init(
            accountId: Swift.String? = nil,
            emailAddress: Swift.String? = nil
        )
        {
            self.accountId = accountId
            self.emailAddress = emailAddress
        }
    }
}

extension DetectiveClientTypes.Account: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "Account(accountId: \(Swift.String(describing: accountId)), emailAddress: \"CONTENT_REDACTED\")"}
}

extension DetectiveClientTypes {

    /// Information about the Detective administrator account for an organization.
    public struct Administrator: Swift.Sendable {
        /// The Amazon Web Services account identifier of the Detective administrator account for the organization.
        public var accountId: Swift.String?
        /// The date and time when the Detective administrator account was enabled. The value is an ISO8601 formatted string. For example, 2021-08-18T16:35:56.284Z.
        public var delegationTime: Foundation.Date?
        /// The ARN of the organization behavior graph.
        public var graphArn: Swift.String?

        public init(
            accountId: Swift.String? = nil,
            delegationTime: Foundation.Date? = nil,
            graphArn: Swift.String? = nil
        )
        {
            self.accountId = accountId
            self.delegationTime = delegationTime
            self.graphArn = graphArn
        }
    }
}

public struct BatchGetGraphMemberDatasourcesInput: Swift.Sendable {
    /// The list of Amazon Web Services accounts to get data source package information on.
    /// This member is required.
    public var accountIds: [Swift.String]?
    /// The ARN of the behavior graph.
    /// This member is required.
    public var graphArn: Swift.String?

    public init(
        accountIds: [Swift.String]? = nil,
        graphArn: Swift.String? = nil
    )
    {
        self.accountIds = accountIds
        self.graphArn = graphArn
    }
}

extension DetectiveClientTypes {

    public enum DatasourcePackage: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case asffSecurityhubFinding
        case detectiveCore
        case eksAudit
        case sdkUnknown(Swift.String)

        public static var allCases: [DatasourcePackage] {
            return [
                .asffSecurityhubFinding,
                .detectiveCore,
                .eksAudit
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .asffSecurityhubFinding: return "ASFF_SECURITYHUB_FINDING"
            case .detectiveCore: return "DETECTIVE_CORE"
            case .eksAudit: return "EKS_AUDIT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DetectiveClientTypes {

    public enum DatasourcePackageIngestState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case started
        case stopped
        case sdkUnknown(Swift.String)

        public static var allCases: [DatasourcePackageIngestState] {
            return [
                .disabled,
                .started,
                .stopped
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "DISABLED"
            case .started: return "STARTED"
            case .stopped: return "STOPPED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DetectiveClientTypes {

    /// Details on when data collection began for a source package.
    public struct TimestampForCollection: Swift.Sendable {
        /// The data and time when data collection began for a source package. The value is an ISO8601 formatted string. For example, 2021-08-18T16:35:56.284Z.
        public var timestamp: Foundation.Date?

        public init(
            timestamp: Foundation.Date? = nil
        )
        {
            self.timestamp = timestamp
        }
    }
}

extension DetectiveClientTypes {

    /// Details on data source packages for members of the behavior graph.
    public struct MembershipDatasources: Swift.Sendable {
        /// The account identifier of the Amazon Web Services account.
        public var accountId: Swift.String?
        /// Details on when a data source package was added to a behavior graph.
        public var datasourcePackageIngestHistory: [Swift.String: [Swift.String: DetectiveClientTypes.TimestampForCollection]]?
        /// The ARN of the organization behavior graph.
        public var graphArn: Swift.String?

        public init(
            accountId: Swift.String? = nil,
            datasourcePackageIngestHistory: [Swift.String: [Swift.String: DetectiveClientTypes.TimestampForCollection]]? = nil,
            graphArn: Swift.String? = nil
        )
        {
            self.accountId = accountId
            self.datasourcePackageIngestHistory = datasourcePackageIngestHistory
            self.graphArn = graphArn
        }
    }
}

extension DetectiveClientTypes {

    /// A member account that was included in a request but for which the request could not be processed.
    public struct UnprocessedAccount: Swift.Sendable {
        /// The Amazon Web Services account identifier of the member account that was not processed.
        public var accountId: Swift.String?
        /// The reason that the member account request could not be processed.
        public var reason: Swift.String?

        public init(
            accountId: Swift.String? = nil,
            reason: Swift.String? = nil
        )
        {
            self.accountId = accountId
            self.reason = reason
        }
    }
}

public struct BatchGetGraphMemberDatasourcesOutput: Swift.Sendable {
    /// Details on the status of data source packages for members of the behavior graph.
    public var memberDatasources: [DetectiveClientTypes.MembershipDatasources]?
    /// Accounts that data source package information could not be retrieved for.
    public var unprocessedAccounts: [DetectiveClientTypes.UnprocessedAccount]?

    public init(
        memberDatasources: [DetectiveClientTypes.MembershipDatasources]? = nil,
        unprocessedAccounts: [DetectiveClientTypes.UnprocessedAccount]? = nil
    )
    {
        self.memberDatasources = memberDatasources
        self.unprocessedAccounts = unprocessedAccounts
    }
}

public struct BatchGetMembershipDatasourcesInput: Swift.Sendable {
    /// The ARN of the behavior graph.
    /// This member is required.
    public var graphArns: [Swift.String]?

    public init(
        graphArns: [Swift.String]? = nil
    )
    {
        self.graphArns = graphArns
    }
}

extension DetectiveClientTypes {

    /// Behavior graphs that could not be processed in the request.
    public struct UnprocessedGraph: Swift.Sendable {
        /// The ARN of the organization behavior graph.
        public var graphArn: Swift.String?
        /// The reason data source package information could not be processed for a behavior graph.
        public var reason: Swift.String?

        public init(
            graphArn: Swift.String? = nil,
            reason: Swift.String? = nil
        )
        {
            self.graphArn = graphArn
            self.reason = reason
        }
    }
}

public struct BatchGetMembershipDatasourcesOutput: Swift.Sendable {
    /// Details on the data source package history for an member of the behavior graph.
    public var membershipDatasources: [DetectiveClientTypes.MembershipDatasources]?
    /// Graphs that data source package information could not be retrieved for.
    public var unprocessedGraphs: [DetectiveClientTypes.UnprocessedGraph]?

    public init(
        membershipDatasources: [DetectiveClientTypes.MembershipDatasources]? = nil,
        unprocessedGraphs: [DetectiveClientTypes.UnprocessedGraph]? = nil
    )
    {
        self.membershipDatasources = membershipDatasources
        self.unprocessedGraphs = unprocessedGraphs
    }
}

/// This request cannot be completed for one of the following reasons.
///
/// * This request cannot be completed if it would cause the number of member accounts in the behavior graph to exceed the maximum allowed. A behavior graph cannot have more than 1,200 member accounts.
///
/// * This request cannot be completed if the current volume ingested is above the limit of 10 TB per day. Detective will not allow you to add additional member accounts.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
        /// The type of resource that has exceeded the service quota.
        public internal(set) var resources: [Swift.String]? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceQuotaExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        resources: [Swift.String]? = nil
    )
    {
        self.properties.message = message
        self.properties.resources = resources
    }
}

public struct CreateGraphInput: Swift.Sendable {
    /// The tags to assign to the new behavior graph. You can add up to 50 tags. For each tag, you provide the tag key and the tag value. Each tag key can contain up to 128 characters. Each tag value can contain up to 256 characters.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

public struct CreateGraphOutput: Swift.Sendable {
    /// The ARN of the new behavior graph.
    public var graphArn: Swift.String?

    public init(
        graphArn: Swift.String? = nil
    )
    {
        self.graphArn = graphArn
    }
}

public struct CreateMembersInput: Swift.Sendable {
    /// The list of Amazon Web Services accounts to invite or to enable. You can invite or enable up to 50 accounts at a time. For each invited account, the account list contains the account identifier and the Amazon Web Services account root user email address. For organization accounts in the organization behavior graph, the email address is not required.
    /// This member is required.
    public var accounts: [DetectiveClientTypes.Account]?
    /// if set to true, then the invited accounts do not receive email notifications. By default, this is set to false, and the invited accounts receive email notifications. Organization accounts in the organization behavior graph do not receive email notifications.
    public var disableEmailNotification: Swift.Bool?
    /// The ARN of the behavior graph.
    /// This member is required.
    public var graphArn: Swift.String?
    /// Customized message text to include in the invitation email message to the invited member accounts.
    public var message: Swift.String?

    public init(
        accounts: [DetectiveClientTypes.Account]? = nil,
        disableEmailNotification: Swift.Bool? = false,
        graphArn: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.accounts = accounts
        self.disableEmailNotification = disableEmailNotification
        self.graphArn = graphArn
        self.message = message
    }
}

extension CreateMembersInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateMembersInput(accounts: \(Swift.String(describing: accounts)), disableEmailNotification: \(Swift.String(describing: disableEmailNotification)), graphArn: \(Swift.String(describing: graphArn)), message: \"CONTENT_REDACTED\")"}
}

extension DetectiveClientTypes {

    public enum MemberDisabledReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case volumeTooHigh
        case volumeUnknown
        case sdkUnknown(Swift.String)

        public static var allCases: [MemberDisabledReason] {
            return [
                .volumeTooHigh,
                .volumeUnknown
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .volumeTooHigh: return "VOLUME_TOO_HIGH"
            case .volumeUnknown: return "VOLUME_UNKNOWN"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DetectiveClientTypes {

    public enum InvitationType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case invitation
        case organization
        case sdkUnknown(Swift.String)

        public static var allCases: [InvitationType] {
            return [
                .invitation,
                .organization
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .invitation: return "INVITATION"
            case .organization: return "ORGANIZATION"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DetectiveClientTypes {

    public enum MemberStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case acceptedButDisabled
        case enabled
        case invited
        case verificationFailed
        case verificationInProgress
        case sdkUnknown(Swift.String)

        public static var allCases: [MemberStatus] {
            return [
                .acceptedButDisabled,
                .enabled,
                .invited,
                .verificationFailed,
                .verificationInProgress
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .acceptedButDisabled: return "ACCEPTED_BUT_DISABLED"
            case .enabled: return "ENABLED"
            case .invited: return "INVITED"
            case .verificationFailed: return "VERIFICATION_FAILED"
            case .verificationInProgress: return "VERIFICATION_IN_PROGRESS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DetectiveClientTypes {

    /// Information on the usage of a data source package in the behavior graph.
    public struct DatasourcePackageUsageInfo: Swift.Sendable {
        /// Total volume of data in bytes per day ingested for a given data source package.
        public var volumeUsageInBytes: Swift.Int?
        /// The data and time when the member account data volume was last updated. The value is an ISO8601 formatted string. For example, 2021-08-18T16:35:56.284Z.
        public var volumeUsageUpdateTime: Foundation.Date?

        public init(
            volumeUsageInBytes: Swift.Int? = nil,
            volumeUsageUpdateTime: Foundation.Date? = nil
        )
        {
            self.volumeUsageInBytes = volumeUsageInBytes
            self.volumeUsageUpdateTime = volumeUsageUpdateTime
        }
    }
}

extension DetectiveClientTypes {

    /// Details about a member account in a behavior graph.
    public struct MemberDetail: Swift.Sendable {
        /// The Amazon Web Services account identifier for the member account.
        public var accountId: Swift.String?
        /// The Amazon Web Services account identifier of the administrator account for the behavior graph.
        public var administratorId: Swift.String?
        /// The state of a data source package for the behavior graph.
        public var datasourcePackageIngestStates: [Swift.String: DetectiveClientTypes.DatasourcePackageIngestState]?
        /// For member accounts with a status of ACCEPTED_BUT_DISABLED, the reason that the member account is not enabled. The reason can have one of the following values:
        ///
        /// * VOLUME_TOO_HIGH - Indicates that adding the member account would cause the data volume for the behavior graph to be too high.
        ///
        /// * VOLUME_UNKNOWN - Indicates that Detective is unable to verify the data volume for the member account. This is usually because the member account is not enrolled in Amazon GuardDuty.
        public var disabledReason: DetectiveClientTypes.MemberDisabledReason?
        /// The Amazon Web Services account root user email address for the member account.
        public var emailAddress: Swift.String?
        /// The ARN of the behavior graph.
        public var graphArn: Swift.String?
        /// The type of behavior graph membership. For an organization account in the organization behavior graph, the type is ORGANIZATION. For an account that was invited to a behavior graph, the type is INVITATION.
        public var invitationType: DetectiveClientTypes.InvitationType?
        /// For invited accounts, the date and time that Detective sent the invitation to the account. The value is an ISO8601 formatted string. For example, 2021-08-18T16:35:56.284Z.
        public var invitedTime: Foundation.Date?
        /// The Amazon Web Services account identifier of the administrator account for the behavior graph.
        @available(*, deprecated, message: "This property is deprecated. Use AdministratorId instead.")
        public var masterId: Swift.String?
        /// The member account data volume as a percentage of the maximum allowed data volume. 0 indicates 0 percent, and 100 indicates 100 percent. Note that this is not the percentage of the behavior graph data volume. For example, the data volume for the behavior graph is 80 GB per day. The maximum data volume is 160 GB per day. If the data volume for the member account is 40 GB per day, then PercentOfGraphUtilization is 25. It represents 25% of the maximum allowed data volume.
        @available(*, deprecated, message: "This property is deprecated. Use VolumeUsageByDatasourcePackage instead.")
        public var percentOfGraphUtilization: Swift.Double?
        /// The date and time when the graph utilization percentage was last updated. The value is an ISO8601 formatted string. For example, 2021-08-18T16:35:56.284Z.
        @available(*, deprecated, message: "This property is deprecated. Use VolumeUsageByDatasourcePackage instead.")
        public var percentOfGraphUtilizationUpdatedTime: Foundation.Date?
        /// The current membership status of the member account. The status can have one of the following values:
        ///
        /// * INVITED - For invited accounts only. Indicates that the member was sent an invitation but has not yet responded.
        ///
        /// * VERIFICATION_IN_PROGRESS - For invited accounts only, indicates that Detective is verifying that the account identifier and email address provided for the member account match. If they do match, then Detective sends the invitation. If the email address and account identifier don't match, then the member cannot be added to the behavior graph. For organization accounts in the organization behavior graph, indicates that Detective is verifying that the account belongs to the organization.
        ///
        /// * VERIFICATION_FAILED - For invited accounts only. Indicates that the account and email address provided for the member account do not match, and Detective did not send an invitation to the account.
        ///
        /// * ENABLED - Indicates that the member account currently contributes data to the behavior graph. For invited accounts, the member account accepted the invitation. For organization accounts in the organization behavior graph, the Detective administrator account enabled the organization account as a member account.
        ///
        /// * ACCEPTED_BUT_DISABLED - The account accepted the invitation, or was enabled by the Detective administrator account, but is prevented from contributing data to the behavior graph. DisabledReason provides the reason why the member account is not enabled.
        ///
        ///
        /// Invited accounts that declined an invitation or that were removed from the behavior graph are not included. In the organization behavior graph, organization accounts that the Detective administrator account did not enable are not included.
        public var status: DetectiveClientTypes.MemberStatus?
        /// The date and time that the member account was last updated. The value is an ISO8601 formatted string. For example, 2021-08-18T16:35:56.284Z.
        public var updatedTime: Foundation.Date?
        /// Details on the volume of usage for each data source package in a behavior graph.
        public var volumeUsageByDatasourcePackage: [Swift.String: DetectiveClientTypes.DatasourcePackageUsageInfo]?
        /// The data volume in bytes per day for the member account.
        @available(*, deprecated, message: "This property is deprecated. Use VolumeUsageByDatasourcePackage instead.")
        public var volumeUsageInBytes: Swift.Int?
        /// The data and time when the member account data volume was last updated. The value is an ISO8601 formatted string. For example, 2021-08-18T16:35:56.284Z.
        @available(*, deprecated, message: "This property is deprecated. Use VolumeUsageByDatasourcePackage instead.")
        public var volumeUsageUpdatedTime: Foundation.Date?

        public init(
            accountId: Swift.String? = nil,
            administratorId: Swift.String? = nil,
            datasourcePackageIngestStates: [Swift.String: DetectiveClientTypes.DatasourcePackageIngestState]? = nil,
            disabledReason: DetectiveClientTypes.MemberDisabledReason? = nil,
            emailAddress: Swift.String? = nil,
            graphArn: Swift.String? = nil,
            invitationType: DetectiveClientTypes.InvitationType? = nil,
            invitedTime: Foundation.Date? = nil,
            masterId: Swift.String? = nil,
            percentOfGraphUtilization: Swift.Double? = nil,
            percentOfGraphUtilizationUpdatedTime: Foundation.Date? = nil,
            status: DetectiveClientTypes.MemberStatus? = nil,
            updatedTime: Foundation.Date? = nil,
            volumeUsageByDatasourcePackage: [Swift.String: DetectiveClientTypes.DatasourcePackageUsageInfo]? = nil,
            volumeUsageInBytes: Swift.Int? = nil,
            volumeUsageUpdatedTime: Foundation.Date? = nil
        )
        {
            self.accountId = accountId
            self.administratorId = administratorId
            self.datasourcePackageIngestStates = datasourcePackageIngestStates
            self.disabledReason = disabledReason
            self.emailAddress = emailAddress
            self.graphArn = graphArn
            self.invitationType = invitationType
            self.invitedTime = invitedTime
            self.masterId = masterId
            self.percentOfGraphUtilization = percentOfGraphUtilization
            self.percentOfGraphUtilizationUpdatedTime = percentOfGraphUtilizationUpdatedTime
            self.status = status
            self.updatedTime = updatedTime
            self.volumeUsageByDatasourcePackage = volumeUsageByDatasourcePackage
            self.volumeUsageInBytes = volumeUsageInBytes
            self.volumeUsageUpdatedTime = volumeUsageUpdatedTime
        }
    }
}

extension DetectiveClientTypes.MemberDetail: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "MemberDetail(accountId: \(Swift.String(describing: accountId)), administratorId: \(Swift.String(describing: administratorId)), datasourcePackageIngestStates: \(Swift.String(describing: datasourcePackageIngestStates)), disabledReason: \(Swift.String(describing: disabledReason)), graphArn: \(Swift.String(describing: graphArn)), invitationType: \(Swift.String(describing: invitationType)), invitedTime: \(Swift.String(describing: invitedTime)), masterId: \(Swift.String(describing: masterId)), percentOfGraphUtilization: \(Swift.String(describing: percentOfGraphUtilization)), percentOfGraphUtilizationUpdatedTime: \(Swift.String(describing: percentOfGraphUtilizationUpdatedTime)), status: \(Swift.String(describing: status)), updatedTime: \(Swift.String(describing: updatedTime)), volumeUsageByDatasourcePackage: \(Swift.String(describing: volumeUsageByDatasourcePackage)), volumeUsageInBytes: \(Swift.String(describing: volumeUsageInBytes)), volumeUsageUpdatedTime: \(Swift.String(describing: volumeUsageUpdatedTime)), emailAddress: \"CONTENT_REDACTED\")"}
}

public struct CreateMembersOutput: Swift.Sendable {
    /// The set of member account invitation or enablement requests that Detective was able to process. This includes accounts that are being verified, that failed verification, and that passed verification and are being sent an invitation or are being enabled.
    public var members: [DetectiveClientTypes.MemberDetail]?
    /// The list of accounts for which Detective was unable to process the invitation or enablement request. For each account, the list provides the reason why the request could not be processed. The list includes accounts that are already member accounts in the behavior graph.
    public var unprocessedAccounts: [DetectiveClientTypes.UnprocessedAccount]?

    public init(
        members: [DetectiveClientTypes.MemberDetail]? = nil,
        unprocessedAccounts: [DetectiveClientTypes.UnprocessedAccount]? = nil
    )
    {
        self.members = members
        self.unprocessedAccounts = unprocessedAccounts
    }
}

public struct DeleteGraphInput: Swift.Sendable {
    /// The ARN of the behavior graph to disable.
    /// This member is required.
    public var graphArn: Swift.String?

    public init(
        graphArn: Swift.String? = nil
    )
    {
        self.graphArn = graphArn
    }
}

public struct DeleteMembersInput: Swift.Sendable {
    /// The list of Amazon Web Services account identifiers for the member accounts to remove from the behavior graph. You can remove up to 50 member accounts at a time.
    /// This member is required.
    public var accountIds: [Swift.String]?
    /// The ARN of the behavior graph to remove members from.
    /// This member is required.
    public var graphArn: Swift.String?

    public init(
        accountIds: [Swift.String]? = nil,
        graphArn: Swift.String? = nil
    )
    {
        self.accountIds = accountIds
        self.graphArn = graphArn
    }
}

public struct DeleteMembersOutput: Swift.Sendable {
    /// The list of Amazon Web Services account identifiers for the member accounts that Detective successfully removed from the behavior graph.
    public var accountIds: [Swift.String]?
    /// The list of member accounts that Detective was not able to remove from the behavior graph. For each member account, provides the reason that the deletion could not be processed.
    public var unprocessedAccounts: [DetectiveClientTypes.UnprocessedAccount]?

    public init(
        accountIds: [Swift.String]? = nil,
        unprocessedAccounts: [DetectiveClientTypes.UnprocessedAccount]? = nil
    )
    {
        self.accountIds = accountIds
        self.unprocessedAccounts = unprocessedAccounts
    }
}

/// The request cannot be completed because too many other requests are occurring at the same time.
public struct TooManyRequestsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TooManyRequestsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DescribeOrganizationConfigurationInput: Swift.Sendable {
    /// The ARN of the organization behavior graph.
    /// This member is required.
    public var graphArn: Swift.String?

    public init(
        graphArn: Swift.String? = nil
    )
    {
        self.graphArn = graphArn
    }
}

public struct DescribeOrganizationConfigurationOutput: Swift.Sendable {
    /// Indicates whether to automatically enable new organization accounts as member accounts in the organization behavior graph.
    public var autoEnable: Swift.Bool

    public init(
        autoEnable: Swift.Bool = false
    )
    {
        self.autoEnable = autoEnable
    }
}

public struct DisassociateMembershipInput: Swift.Sendable {
    /// The ARN of the behavior graph to remove the member account from. The member account's member status in the behavior graph must be ENABLED.
    /// This member is required.
    public var graphArn: Swift.String?

    public init(
        graphArn: Swift.String? = nil
    )
    {
        self.graphArn = graphArn
    }
}

public struct EnableOrganizationAdminAccountInput: Swift.Sendable {
    /// The Amazon Web Services account identifier of the account to designate as the Detective administrator account for the organization.
    /// This member is required.
    public var accountId: Swift.String?

    public init(
        accountId: Swift.String? = nil
    )
    {
        self.accountId = accountId
    }
}

public struct GetInvestigationInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the behavior graph.
    /// This member is required.
    public var graphArn: Swift.String?
    /// The investigation ID of the investigation report.
    /// This member is required.
    public var investigationId: Swift.String?

    public init(
        graphArn: Swift.String? = nil,
        investigationId: Swift.String? = nil
    )
    {
        self.graphArn = graphArn
        self.investigationId = investigationId
    }
}

extension DetectiveClientTypes {

    public enum EntityType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case iamRole
        case iamUser
        case sdkUnknown(Swift.String)

        public static var allCases: [EntityType] {
            return [
                .iamRole,
                .iamUser
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .iamRole: return "IAM_ROLE"
            case .iamUser: return "IAM_USER"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DetectiveClientTypes {

    public enum Severity: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case critical
        case high
        case informational
        case low
        case medium
        case sdkUnknown(Swift.String)

        public static var allCases: [Severity] {
            return [
                .critical,
                .high,
                .informational,
                .low,
                .medium
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .critical: return "CRITICAL"
            case .high: return "HIGH"
            case .informational: return "INFORMATIONAL"
            case .low: return "LOW"
            case .medium: return "MEDIUM"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DetectiveClientTypes {

    public enum State: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case active
        case archived
        case sdkUnknown(Swift.String)

        public static var allCases: [State] {
            return [
                .active,
                .archived
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .active: return "ACTIVE"
            case .archived: return "ARCHIVED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DetectiveClientTypes {

    public enum Status: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case running
        case successful
        case sdkUnknown(Swift.String)

        public static var allCases: [Status] {
            return [
                .failed,
                .running,
                .successful
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .running: return "RUNNING"
            case .successful: return "SUCCESSFUL"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct GetInvestigationOutput: Swift.Sendable {
    /// The creation time of the investigation report in UTC time stamp format.
    public var createdTime: Foundation.Date?
    /// The unique Amazon Resource Name (ARN). Detective supports IAM user ARNs and IAM role ARNs.
    public var entityArn: Swift.String?
    /// Type of entity. For example, Amazon Web Services accounts, such as an IAM user and/or IAM role.
    public var entityType: DetectiveClientTypes.EntityType?
    /// The Amazon Resource Name (ARN) of the behavior graph.
    public var graphArn: Swift.String?
    /// The investigation ID of the investigation report.
    public var investigationId: Swift.String?
    /// The data and time when the investigation began. The value is an UTC ISO8601 formatted string. For example, 2021-08-18T16:35:56.284Z.
    public var scopeEndTime: Foundation.Date?
    /// The start date and time used to set the scope time within which you want to generate the investigation report. The value is an UTC ISO8601 formatted string. For example, 2021-08-18T16:35:56.284Z.
    public var scopeStartTime: Foundation.Date?
    /// The severity assigned is based on the likelihood and impact of the indicators of compromise discovered in the investigation.
    public var severity: DetectiveClientTypes.Severity?
    /// The current state of the investigation. An archived investigation indicates that you have completed reviewing the investigation.
    public var state: DetectiveClientTypes.State?
    /// The status based on the completion status of the investigation.
    public var status: DetectiveClientTypes.Status?

    public init(
        createdTime: Foundation.Date? = nil,
        entityArn: Swift.String? = nil,
        entityType: DetectiveClientTypes.EntityType? = nil,
        graphArn: Swift.String? = nil,
        investigationId: Swift.String? = nil,
        scopeEndTime: Foundation.Date? = nil,
        scopeStartTime: Foundation.Date? = nil,
        severity: DetectiveClientTypes.Severity? = nil,
        state: DetectiveClientTypes.State? = nil,
        status: DetectiveClientTypes.Status? = nil
    )
    {
        self.createdTime = createdTime
        self.entityArn = entityArn
        self.entityType = entityType
        self.graphArn = graphArn
        self.investigationId = investigationId
        self.scopeEndTime = scopeEndTime
        self.scopeStartTime = scopeStartTime
        self.severity = severity
        self.state = state
        self.status = status
    }
}

public struct GetMembersInput: Swift.Sendable {
    /// The list of Amazon Web Services account identifiers for the member account for which to return member details. You can request details for up to 50 member accounts at a time. You cannot use GetMembers to retrieve information about member accounts that were removed from the behavior graph.
    /// This member is required.
    public var accountIds: [Swift.String]?
    /// The ARN of the behavior graph for which to request the member details.
    /// This member is required.
    public var graphArn: Swift.String?

    public init(
        accountIds: [Swift.String]? = nil,
        graphArn: Swift.String? = nil
    )
    {
        self.accountIds = accountIds
        self.graphArn = graphArn
    }
}

public struct GetMembersOutput: Swift.Sendable {
    /// The member account details that Detective is returning in response to the request.
    public var memberDetails: [DetectiveClientTypes.MemberDetail]?
    /// The requested member accounts for which Detective was unable to return member details. For each account, provides the reason why the request could not be processed.
    public var unprocessedAccounts: [DetectiveClientTypes.UnprocessedAccount]?

    public init(
        memberDetails: [DetectiveClientTypes.MemberDetail]? = nil,
        unprocessedAccounts: [DetectiveClientTypes.UnprocessedAccount]? = nil
    )
    {
        self.memberDetails = memberDetails
        self.unprocessedAccounts = unprocessedAccounts
    }
}

public struct ListDatasourcePackagesInput: Swift.Sendable {
    /// The ARN of the behavior graph.
    /// This member is required.
    public var graphArn: Swift.String?
    /// The maximum number of results to return.
    public var maxResults: Swift.Int?
    /// For requests to get the next page of results, the pagination token that was returned with the previous set of results. The initial request does not include a pagination token.
    public var nextToken: Swift.String?

    public init(
        graphArn: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.graphArn = graphArn
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DetectiveClientTypes {

    /// Details about the data source packages ingested by your behavior graph.
    public struct DatasourcePackageIngestDetail: Swift.Sendable {
        /// Details on which data source packages are ingested for a member account.
        public var datasourcePackageIngestState: DetectiveClientTypes.DatasourcePackageIngestState?
        /// The date a data source package was enabled for this account
        public var lastIngestStateChange: [Swift.String: DetectiveClientTypes.TimestampForCollection]?

        public init(
            datasourcePackageIngestState: DetectiveClientTypes.DatasourcePackageIngestState? = nil,
            lastIngestStateChange: [Swift.String: DetectiveClientTypes.TimestampForCollection]? = nil
        )
        {
            self.datasourcePackageIngestState = datasourcePackageIngestState
            self.lastIngestStateChange = lastIngestStateChange
        }
    }
}

public struct ListDatasourcePackagesOutput: Swift.Sendable {
    /// Details on the data source packages active in the behavior graph.
    public var datasourcePackages: [Swift.String: DetectiveClientTypes.DatasourcePackageIngestDetail]?
    /// For requests to get the next page of results, the pagination token that was returned with the previous set of results. The initial request does not include a pagination token.
    public var nextToken: Swift.String?

    public init(
        datasourcePackages: [Swift.String: DetectiveClientTypes.DatasourcePackageIngestDetail]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.datasourcePackages = datasourcePackages
        self.nextToken = nextToken
    }
}

public struct ListGraphsInput: Swift.Sendable {
    /// The maximum number of graphs to return at a time. The total must be less than the overall limit on the number of results to return, which is currently 200.
    public var maxResults: Swift.Int?
    /// For requests to get the next page of results, the pagination token that was returned with the previous set of results. The initial request does not include a pagination token.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DetectiveClientTypes {

    /// A behavior graph in Detective.
    public struct Graph: Swift.Sendable {
        /// The ARN of the behavior graph.
        public var arn: Swift.String?
        /// The date and time that the behavior graph was created. The value is an ISO8601 formatted string. For example, 2021-08-18T16:35:56.284Z.
        public var createdTime: Foundation.Date?

        public init(
            arn: Swift.String? = nil,
            createdTime: Foundation.Date? = nil
        )
        {
            self.arn = arn
            self.createdTime = createdTime
        }
    }
}

public struct ListGraphsOutput: Swift.Sendable {
    /// A list of behavior graphs that the account is an administrator account for.
    public var graphList: [DetectiveClientTypes.Graph]?
    /// If there are more behavior graphs remaining in the results, then this is the pagination token to use to request the next page of behavior graphs.
    public var nextToken: Swift.String?

    public init(
        graphList: [DetectiveClientTypes.Graph]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.graphList = graphList
        self.nextToken = nextToken
    }
}

extension DetectiveClientTypes {

    public enum IndicatorType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case flaggedIpAddress
        case impossibleTravel
        case newAso
        case newGeolocation
        case newUserAgent
        case relatedFinding
        case relatedFindingGroup
        case ttpObserved
        case sdkUnknown(Swift.String)

        public static var allCases: [IndicatorType] {
            return [
                .flaggedIpAddress,
                .impossibleTravel,
                .newAso,
                .newGeolocation,
                .newUserAgent,
                .relatedFinding,
                .relatedFindingGroup,
                .ttpObserved
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .flaggedIpAddress: return "FLAGGED_IP_ADDRESS"
            case .impossibleTravel: return "IMPOSSIBLE_TRAVEL"
            case .newAso: return "NEW_ASO"
            case .newGeolocation: return "NEW_GEOLOCATION"
            case .newUserAgent: return "NEW_USER_AGENT"
            case .relatedFinding: return "RELATED_FINDING"
            case .relatedFindingGroup: return "RELATED_FINDING_GROUP"
            case .ttpObserved: return "TTP_OBSERVED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct ListIndicatorsInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the behavior graph.
    /// This member is required.
    public var graphArn: Swift.String?
    /// For the list of indicators of compromise that are generated by Detective investigations, see [Detective investigations](https://docs.aws.amazon.com/detective/latest/userguide/detective-investigations.html).
    public var indicatorType: DetectiveClientTypes.IndicatorType?
    /// The investigation ID of the investigation report.
    /// This member is required.
    public var investigationId: Swift.String?
    /// Lists the maximum number of indicators in a page.
    public var maxResults: Swift.Int?
    /// Lists if there are more results available. The value of nextToken is a unique pagination token for each page. Repeat the call using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return a Validation Exception error.
    public var nextToken: Swift.String?

    public init(
        graphArn: Swift.String? = nil,
        indicatorType: DetectiveClientTypes.IndicatorType? = nil,
        investigationId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.graphArn = graphArn
        self.indicatorType = indicatorType
        self.investigationId = investigationId
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DetectiveClientTypes {

    public enum Reason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case awsThreatIntelligence
        case sdkUnknown(Swift.String)

        public static var allCases: [Reason] {
            return [
                .awsThreatIntelligence
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .awsThreatIntelligence: return "AWS_THREAT_INTELLIGENCE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DetectiveClientTypes {

    /// Contains information on suspicious IP addresses identified as indicators of compromise. This indicator is derived from Amazon Web Services threat intelligence.
    public struct FlaggedIpAddressDetail: Swift.Sendable {
        /// IP address of the suspicious entity.
        public var ipAddress: Swift.String?
        /// Details the reason the IP address was flagged as suspicious.
        public var reason: DetectiveClientTypes.Reason?

        public init(
            ipAddress: Swift.String? = nil,
            reason: DetectiveClientTypes.Reason? = nil
        )
        {
            self.ipAddress = ipAddress
            self.reason = reason
        }
    }
}

extension DetectiveClientTypes {

    /// Contains information on unusual and impossible travel in an account.
    public struct ImpossibleTravelDetail: Swift.Sendable {
        /// IP address where the resource was last used in the impossible travel.
        public var endingIpAddress: Swift.String?
        /// Location where the resource was last used in the impossible travel.
        public var endingLocation: Swift.String?
        /// Returns the time difference between the first and last timestamp the resource was used.
        public var hourlyTimeDelta: Swift.Int?
        /// IP address where the resource was first used in the impossible travel.
        public var startingIpAddress: Swift.String?
        /// Location where the resource was first used in the impossible travel.
        public var startingLocation: Swift.String?

        public init(
            endingIpAddress: Swift.String? = nil,
            endingLocation: Swift.String? = nil,
            hourlyTimeDelta: Swift.Int? = nil,
            startingIpAddress: Swift.String? = nil,
            startingLocation: Swift.String? = nil
        )
        {
            self.endingIpAddress = endingIpAddress
            self.endingLocation = endingLocation
            self.hourlyTimeDelta = hourlyTimeDelta
            self.startingIpAddress = startingIpAddress
            self.startingLocation = startingLocation
        }
    }
}

extension DetectiveClientTypes {

    /// Details new Autonomous System Organizations (ASOs) used either at the resource or account level.
    public struct NewAsoDetail: Swift.Sendable {
        /// Details about the new Autonomous System Organization (ASO).
        public var aso: Swift.String?
        /// Checks if the Autonomous System Organization (ASO) is new for the entire account.
        public var isNewForEntireAccount: Swift.Bool

        public init(
            aso: Swift.String? = nil,
            isNewForEntireAccount: Swift.Bool = false
        )
        {
            self.aso = aso
            self.isNewForEntireAccount = isNewForEntireAccount
        }
    }
}

extension DetectiveClientTypes {

    /// Details new geolocations used either at the resource or account level. For example, lists an observed geolocation that is an infrequent or unused location based on previous user activity.
    public struct NewGeolocationDetail: Swift.Sendable {
        /// IP address using which the resource was accessed.
        public var ipAddress: Swift.String?
        /// Checks if the geolocation is new for the entire account.
        public var isNewForEntireAccount: Swift.Bool
        /// Location where the resource was accessed.
        public var location: Swift.String?

        public init(
            ipAddress: Swift.String? = nil,
            isNewForEntireAccount: Swift.Bool = false,
            location: Swift.String? = nil
        )
        {
            self.ipAddress = ipAddress
            self.isNewForEntireAccount = isNewForEntireAccount
            self.location = location
        }
    }
}

extension DetectiveClientTypes {

    /// Details new user agents used either at the resource or account level.
    public struct NewUserAgentDetail: Swift.Sendable {
        /// Checks if the user agent is new for the entire account.
        public var isNewForEntireAccount: Swift.Bool
        /// New user agent which accessed the resource.
        public var userAgent: Swift.String?

        public init(
            isNewForEntireAccount: Swift.Bool = false,
            userAgent: Swift.String? = nil
        )
        {
            self.isNewForEntireAccount = isNewForEntireAccount
            self.userAgent = userAgent
        }
    }
}

extension DetectiveClientTypes {

    /// Details related activities associated with a potential security event. Lists all distinct categories of evidence that are connected to the resource or the finding group.
    public struct RelatedFindingDetail: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the related finding.
        public var arn: Swift.String?
        /// The IP address of the finding.
        public var ipAddress: Swift.String?
        /// The type of finding.
        public var type: Swift.String?

        public init(
            arn: Swift.String? = nil,
            ipAddress: Swift.String? = nil,
            type: Swift.String? = nil
        )
        {
            self.arn = arn
            self.ipAddress = ipAddress
            self.type = type
        }
    }
}

extension DetectiveClientTypes {

    /// Details multiple activities as they related to a potential security event. Detective uses graph analysis technique that infers relationships between findings and entities, and groups them together as a finding group.
    public struct RelatedFindingGroupDetail: Swift.Sendable {
        /// The unique identifier for the finding group.
        public var id: Swift.String?

        public init(
            id: Swift.String? = nil
        )
        {
            self.id = id
        }
    }
}

extension DetectiveClientTypes {

    /// Details tactics, techniques, and procedures (TTPs) used in a potential security event. Tactics are based on [MITRE ATT&CK Matrix for Enterprise](https://attack.mitre.org/matrices/enterprise/).
    public struct TTPsObservedDetail: Swift.Sendable {
        /// The total number of failed API requests.
        public var apiFailureCount: Swift.Int
        /// The name of the API where the tactics, techniques, and procedure (TTP) was observed.
        public var apiName: Swift.String?
        /// The total number of successful API requests.
        public var apiSuccessCount: Swift.Int
        /// The IP address where the tactics, techniques, and procedure (TTP) was observed.
        public var ipAddress: Swift.String?
        /// The procedure used, identified by the investigation.
        public var procedure: Swift.String?
        /// The tactic used, identified by the investigation.
        public var tactic: Swift.String?
        /// The technique used, identified by the investigation.
        public var technique: Swift.String?

        public init(
            apiFailureCount: Swift.Int = 0,
            apiName: Swift.String? = nil,
            apiSuccessCount: Swift.Int = 0,
            ipAddress: Swift.String? = nil,
            procedure: Swift.String? = nil,
            tactic: Swift.String? = nil,
            technique: Swift.String? = nil
        )
        {
            self.apiFailureCount = apiFailureCount
            self.apiName = apiName
            self.apiSuccessCount = apiSuccessCount
            self.ipAddress = ipAddress
            self.procedure = procedure
            self.tactic = tactic
            self.technique = technique
        }
    }
}

extension DetectiveClientTypes {

    /// Details about the indicators of compromise which are used to determine if a resource is involved in a security incident. An indicator of compromise (IOC) is an artifact observed in or on a network, system, or environment that can (with a high level of confidence) identify malicious activity or a security incident. For the list of indicators of compromise that are generated by Detective investigations, see [Detective investigations](https://docs.aws.amazon.com/detective/latest/userguide/detective-investigations.html).
    public struct IndicatorDetail: Swift.Sendable {
        /// Suspicious IP addresses that are flagged, which indicates critical or severe threats based on threat intelligence by Detective. This indicator is derived from Amazon Web Services threat intelligence.
        public var flaggedIpAddressDetail: DetectiveClientTypes.FlaggedIpAddressDetail?
        /// Identifies unusual and impossible user activity for an account.
        public var impossibleTravelDetail: DetectiveClientTypes.ImpossibleTravelDetail?
        /// Contains details about the new Autonomous System Organization (ASO).
        public var newAsoDetail: DetectiveClientTypes.NewAsoDetail?
        /// Contains details about the new geographic location.
        public var newGeolocationDetail: DetectiveClientTypes.NewGeolocationDetail?
        /// Contains details about the new user agent.
        public var newUserAgentDetail: DetectiveClientTypes.NewUserAgentDetail?
        /// Contains details about related findings.
        public var relatedFindingDetail: DetectiveClientTypes.RelatedFindingDetail?
        /// Contains details about related finding groups.
        public var relatedFindingGroupDetail: DetectiveClientTypes.RelatedFindingGroupDetail?
        /// Details about the indicator of compromise.
        public var ttPsObservedDetail: DetectiveClientTypes.TTPsObservedDetail?

        public init(
            flaggedIpAddressDetail: DetectiveClientTypes.FlaggedIpAddressDetail? = nil,
            impossibleTravelDetail: DetectiveClientTypes.ImpossibleTravelDetail? = nil,
            newAsoDetail: DetectiveClientTypes.NewAsoDetail? = nil,
            newGeolocationDetail: DetectiveClientTypes.NewGeolocationDetail? = nil,
            newUserAgentDetail: DetectiveClientTypes.NewUserAgentDetail? = nil,
            relatedFindingDetail: DetectiveClientTypes.RelatedFindingDetail? = nil,
            relatedFindingGroupDetail: DetectiveClientTypes.RelatedFindingGroupDetail? = nil,
            ttPsObservedDetail: DetectiveClientTypes.TTPsObservedDetail? = nil
        )
        {
            self.flaggedIpAddressDetail = flaggedIpAddressDetail
            self.impossibleTravelDetail = impossibleTravelDetail
            self.newAsoDetail = newAsoDetail
            self.newGeolocationDetail = newGeolocationDetail
            self.newUserAgentDetail = newUserAgentDetail
            self.relatedFindingDetail = relatedFindingDetail
            self.relatedFindingGroupDetail = relatedFindingGroupDetail
            self.ttPsObservedDetail = ttPsObservedDetail
        }
    }
}

extension DetectiveClientTypes {

    /// Detective investigations triages indicators of compromises such as a finding and surfaces only the most critical and suspicious issues, so you can focus on high-level investigations. An Indicator lets you determine if an Amazon Web Services resource is involved in unusual activity that could indicate malicious behavior and its impact.
    public struct Indicator: Swift.Sendable {
        /// Details about the indicators of compromise that are used to determine if a resource is involved in a security incident. An indicator of compromise (IOC) is an artifact observed in or on a network, system, or environment that can (with a high level of confidence) identify malicious activity or a security incident.
        public var indicatorDetail: DetectiveClientTypes.IndicatorDetail?
        /// The type of indicator.
        public var indicatorType: DetectiveClientTypes.IndicatorType?

        public init(
            indicatorDetail: DetectiveClientTypes.IndicatorDetail? = nil,
            indicatorType: DetectiveClientTypes.IndicatorType? = nil
        )
        {
            self.indicatorDetail = indicatorDetail
            self.indicatorType = indicatorType
        }
    }
}

public struct ListIndicatorsOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the behavior graph.
    public var graphArn: Swift.String?
    /// Lists the indicators of compromise.
    public var indicators: [DetectiveClientTypes.Indicator]?
    /// The investigation ID of the investigation report.
    public var investigationId: Swift.String?
    /// Lists if there are more results available. The value of nextToken is a unique pagination token for each page. Repeat the call using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return a Validation Exception error.
    public var nextToken: Swift.String?

    public init(
        graphArn: Swift.String? = nil,
        indicators: [DetectiveClientTypes.Indicator]? = nil,
        investigationId: Swift.String? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.graphArn = graphArn
        self.indicators = indicators
        self.investigationId = investigationId
        self.nextToken = nextToken
    }
}

extension DetectiveClientTypes {

    /// Contains details on the time range used to filter data.
    public struct DateFilter: Swift.Sendable {
        /// A timestamp representing the end date of the time period until when data is filtered, including the end date.
        /// This member is required.
        public var endInclusive: Foundation.Date?
        /// A timestamp representing the start of the time period from when data is filtered, including the start date.
        /// This member is required.
        public var startInclusive: Foundation.Date?

        public init(
            endInclusive: Foundation.Date? = nil,
            startInclusive: Foundation.Date? = nil
        )
        {
            self.endInclusive = endInclusive
            self.startInclusive = startInclusive
        }
    }
}

extension DetectiveClientTypes {

    /// A string for filtering Detective investigations.
    public struct StringFilter: Swift.Sendable {
        /// The string filter value.
        /// This member is required.
        public var value: Swift.String?

        public init(
            value: Swift.String? = nil
        )
        {
            self.value = value
        }
    }
}

extension DetectiveClientTypes {

    /// Details on the criteria used to define the filter for investigation results.
    public struct FilterCriteria: Swift.Sendable {
        /// Filter the investigation results based on when the investigation was created.
        public var createdTime: DetectiveClientTypes.DateFilter?
        /// Filter the investigation results based on the Amazon Resource Name (ARN) of the entity.
        public var entityArn: DetectiveClientTypes.StringFilter?
        /// Filter the investigation results based on the severity.
        public var severity: DetectiveClientTypes.StringFilter?
        /// Filter the investigation results based on the state.
        public var state: DetectiveClientTypes.StringFilter?
        /// Filter the investigation results based on the status.
        public var status: DetectiveClientTypes.StringFilter?

        public init(
            createdTime: DetectiveClientTypes.DateFilter? = nil,
            entityArn: DetectiveClientTypes.StringFilter? = nil,
            severity: DetectiveClientTypes.StringFilter? = nil,
            state: DetectiveClientTypes.StringFilter? = nil,
            status: DetectiveClientTypes.StringFilter? = nil
        )
        {
            self.createdTime = createdTime
            self.entityArn = entityArn
            self.severity = severity
            self.state = state
            self.status = status
        }
    }
}

extension DetectiveClientTypes {

    public enum Field: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case createdTime
        case severity
        case status
        case sdkUnknown(Swift.String)

        public static var allCases: [Field] {
            return [
                .createdTime,
                .severity,
                .status
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .createdTime: return "CREATED_TIME"
            case .severity: return "SEVERITY"
            case .status: return "STATUS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DetectiveClientTypes {

    public enum SortOrder: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case asc
        case desc
        case sdkUnknown(Swift.String)

        public static var allCases: [SortOrder] {
            return [
                .asc,
                .desc
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .asc: return "ASC"
            case .desc: return "DESC"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DetectiveClientTypes {

    /// Details about the criteria used for sorting investigations.
    public struct SortCriteria: Swift.Sendable {
        /// Represents the Field attribute to sort investigations.
        public var field: DetectiveClientTypes.Field?
        /// The order by which the sorted findings are displayed.
        public var sortOrder: DetectiveClientTypes.SortOrder?

        public init(
            field: DetectiveClientTypes.Field? = nil,
            sortOrder: DetectiveClientTypes.SortOrder? = nil
        )
        {
            self.field = field
            self.sortOrder = sortOrder
        }
    }
}

public struct ListInvestigationsInput: Swift.Sendable {
    /// Filters the investigation results based on a criteria.
    public var filterCriteria: DetectiveClientTypes.FilterCriteria?
    /// The Amazon Resource Name (ARN) of the behavior graph.
    /// This member is required.
    public var graphArn: Swift.String?
    /// Lists the maximum number of investigations in a page.
    public var maxResults: Swift.Int?
    /// Lists if there are more results available. The value of nextToken is a unique pagination token for each page. Repeat the call using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours. Using an expired pagination token will return a Validation Exception error.
    public var nextToken: Swift.String?
    /// Sorts the investigation results based on a criteria.
    public var sortCriteria: DetectiveClientTypes.SortCriteria?

    public init(
        filterCriteria: DetectiveClientTypes.FilterCriteria? = nil,
        graphArn: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        sortCriteria: DetectiveClientTypes.SortCriteria? = nil
    )
    {
        self.filterCriteria = filterCriteria
        self.graphArn = graphArn
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.sortCriteria = sortCriteria
    }
}

extension DetectiveClientTypes {

    /// Details about the investigation related to a potential security event identified by Detective.
    public struct InvestigationDetail: Swift.Sendable {
        /// The time stamp of the creation time of the investigation report. The value is an UTC ISO8601 formatted string. For example, 2021-08-18T16:35:56.284Z.
        public var createdTime: Foundation.Date?
        /// The unique Amazon Resource Name (ARN) of the IAM user and IAM role.
        public var entityArn: Swift.String?
        /// Type of entity. For example, Amazon Web Services accounts, such as IAM user and role.
        public var entityType: DetectiveClientTypes.EntityType?
        /// The investigation ID of the investigation report.
        public var investigationId: Swift.String?
        /// Severity based on the likelihood and impact of the indicators of compromise discovered in the investigation.
        public var severity: DetectiveClientTypes.Severity?
        /// The current state of the investigation. An archived investigation indicates you have completed reviewing the investigation.
        public var state: DetectiveClientTypes.State?
        /// Status based on the completion status of the investigation.
        public var status: DetectiveClientTypes.Status?

        public init(
            createdTime: Foundation.Date? = nil,
            entityArn: Swift.String? = nil,
            entityType: DetectiveClientTypes.EntityType? = nil,
            investigationId: Swift.String? = nil,
            severity: DetectiveClientTypes.Severity? = nil,
            state: DetectiveClientTypes.State? = nil,
            status: DetectiveClientTypes.Status? = nil
        )
        {
            self.createdTime = createdTime
            self.entityArn = entityArn
            self.entityType = entityType
            self.investigationId = investigationId
            self.severity = severity
            self.state = state
            self.status = status
        }
    }
}

public struct ListInvestigationsOutput: Swift.Sendable {
    /// Lists the summary of uncommon behavior or malicious activity which indicates a compromise.
    public var investigationDetails: [DetectiveClientTypes.InvestigationDetail]?
    /// Lists if there are more results available. The value of nextToken is a unique pagination token for each page. Repeat the call using the returned token to retrieve the next page. Keep all other arguments unchanged. Each pagination token expires after 24 hours.
    public var nextToken: Swift.String?

    public init(
        investigationDetails: [DetectiveClientTypes.InvestigationDetail]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.investigationDetails = investigationDetails
        self.nextToken = nextToken
    }
}

public struct ListInvitationsInput: Swift.Sendable {
    /// The maximum number of behavior graph invitations to return in the response. The total must be less than the overall limit on the number of results to return, which is currently 200.
    public var maxResults: Swift.Int?
    /// For requests to retrieve the next page of results, the pagination token that was returned with the previous page of results. The initial request does not include a pagination token.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListInvitationsOutput: Swift.Sendable {
    /// The list of behavior graphs for which the member account has open or accepted invitations.
    public var invitations: [DetectiveClientTypes.MemberDetail]?
    /// If there are more behavior graphs remaining in the results, then this is the pagination token to use to request the next page of behavior graphs.
    public var nextToken: Swift.String?

    public init(
        invitations: [DetectiveClientTypes.MemberDetail]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.invitations = invitations
        self.nextToken = nextToken
    }
}

public struct ListMembersInput: Swift.Sendable {
    /// The ARN of the behavior graph for which to retrieve the list of member accounts.
    /// This member is required.
    public var graphArn: Swift.String?
    /// The maximum number of member accounts to include in the response. The total must be less than the overall limit on the number of results to return, which is currently 200.
    public var maxResults: Swift.Int?
    /// For requests to retrieve the next page of member account results, the pagination token that was returned with the previous page of results. The initial request does not include a pagination token.
    public var nextToken: Swift.String?

    public init(
        graphArn: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.graphArn = graphArn
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListMembersOutput: Swift.Sendable {
    /// The list of member accounts in the behavior graph. For invited accounts, the results include member accounts that did not pass verification and member accounts that have not yet accepted the invitation to the behavior graph. The results do not include member accounts that were removed from the behavior graph. For the organization behavior graph, the results do not include organization accounts that the Detective administrator account has not enabled as member accounts.
    public var memberDetails: [DetectiveClientTypes.MemberDetail]?
    /// If there are more member accounts remaining in the results, then use this pagination token to request the next page of member accounts.
    public var nextToken: Swift.String?

    public init(
        memberDetails: [DetectiveClientTypes.MemberDetail]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.memberDetails = memberDetails
        self.nextToken = nextToken
    }
}

public struct ListOrganizationAdminAccountsInput: Swift.Sendable {
    /// The maximum number of results to return.
    public var maxResults: Swift.Int?
    /// For requests to get the next page of results, the pagination token that was returned with the previous set of results. The initial request does not include a pagination token.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListOrganizationAdminAccountsOutput: Swift.Sendable {
    /// The list of Detective administrator accounts.
    public var administrators: [DetectiveClientTypes.Administrator]?
    /// If there are more accounts remaining in the results, then this is the pagination token to use to request the next page of accounts.
    public var nextToken: Swift.String?

    public init(
        administrators: [DetectiveClientTypes.Administrator]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.administrators = administrators
        self.nextToken = nextToken
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The ARN of the behavior graph for which to retrieve the tag values.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// The tag values that are assigned to the behavior graph. The request returns up to 50 tag values.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

public struct RejectInvitationInput: Swift.Sendable {
    /// The ARN of the behavior graph to reject the invitation to. The member account's current member status in the behavior graph must be INVITED.
    /// This member is required.
    public var graphArn: Swift.String?

    public init(
        graphArn: Swift.String? = nil
    )
    {
        self.graphArn = graphArn
    }
}

public struct StartInvestigationInput: Swift.Sendable {
    /// The unique Amazon Resource Name (ARN) of the IAM user and IAM role.
    /// This member is required.
    public var entityArn: Swift.String?
    /// The Amazon Resource Name (ARN) of the behavior graph.
    /// This member is required.
    public var graphArn: Swift.String?
    /// The data and time when the investigation ended. The value is an UTC ISO8601 formatted string. For example, 2021-08-18T16:35:56.284Z.
    /// This member is required.
    public var scopeEndTime: Foundation.Date?
    /// The data and time when the investigation began. The value is an UTC ISO8601 formatted string. For example, 2021-08-18T16:35:56.284Z.
    /// This member is required.
    public var scopeStartTime: Foundation.Date?

    public init(
        entityArn: Swift.String? = nil,
        graphArn: Swift.String? = nil,
        scopeEndTime: Foundation.Date? = nil,
        scopeStartTime: Foundation.Date? = nil
    )
    {
        self.entityArn = entityArn
        self.graphArn = graphArn
        self.scopeEndTime = scopeEndTime
        self.scopeStartTime = scopeStartTime
    }
}

public struct StartInvestigationOutput: Swift.Sendable {
    /// The investigation ID of the investigation report.
    public var investigationId: Swift.String?

    public init(
        investigationId: Swift.String? = nil
    )
    {
        self.investigationId = investigationId
    }
}

public struct StartMonitoringMemberInput: Swift.Sendable {
    /// The account ID of the member account to try to enable. The account must be an invited member account with a status of ACCEPTED_BUT_DISABLED.
    /// This member is required.
    public var accountId: Swift.String?
    /// The ARN of the behavior graph.
    /// This member is required.
    public var graphArn: Swift.String?

    public init(
        accountId: Swift.String? = nil,
        graphArn: Swift.String? = nil
    )
    {
        self.accountId = accountId
        self.graphArn = graphArn
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The ARN of the behavior graph to assign the tags to.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tags to assign to the behavior graph. You can add up to 50 tags. For each tag, you provide the tag key and the tag value. Each tag key can contain up to 128 characters. Each tag value can contain up to 256 characters.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The ARN of the behavior graph to remove the tags from.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tag keys of the tags to remove from the behavior graph. You can remove up to 50 tags at a time.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateDatasourcePackagesInput: Swift.Sendable {
    /// The data source package start for the behavior graph.
    /// This member is required.
    public var datasourcePackages: [DetectiveClientTypes.DatasourcePackage]?
    /// The ARN of the behavior graph.
    /// This member is required.
    public var graphArn: Swift.String?

    public init(
        datasourcePackages: [DetectiveClientTypes.DatasourcePackage]? = nil,
        graphArn: Swift.String? = nil
    )
    {
        self.datasourcePackages = datasourcePackages
        self.graphArn = graphArn
    }
}

public struct UpdateInvestigationStateInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the behavior graph.
    /// This member is required.
    public var graphArn: Swift.String?
    /// The investigation ID of the investigation report.
    /// This member is required.
    public var investigationId: Swift.String?
    /// The current state of the investigation. An archived investigation indicates you have completed reviewing the investigation.
    /// This member is required.
    public var state: DetectiveClientTypes.State?

    public init(
        graphArn: Swift.String? = nil,
        investigationId: Swift.String? = nil,
        state: DetectiveClientTypes.State? = nil
    )
    {
        self.graphArn = graphArn
        self.investigationId = investigationId
        self.state = state
    }
}

public struct UpdateOrganizationConfigurationInput: Swift.Sendable {
    /// Indicates whether to automatically enable new organization accounts as member accounts in the organization behavior graph.
    public var autoEnable: Swift.Bool?
    /// The ARN of the organization behavior graph.
    /// This member is required.
    public var graphArn: Swift.String?

    public init(
        autoEnable: Swift.Bool? = false,
        graphArn: Swift.String? = nil
    )
    {
        self.autoEnable = autoEnable
        self.graphArn = graphArn
    }
}

extension AcceptInvitationInput {

    static func urlPathProvider(_ value: AcceptInvitationInput) -> Swift.String? {
        return "/invitation"
    }
}

extension BatchGetGraphMemberDatasourcesInput {

    static func urlPathProvider(_ value: BatchGetGraphMemberDatasourcesInput) -> Swift.String? {
        return "/graph/datasources/get"
    }
}

extension BatchGetMembershipDatasourcesInput {

    static func urlPathProvider(_ value: BatchGetMembershipDatasourcesInput) -> Swift.String? {
        return "/membership/datasources/get"
    }
}

extension CreateGraphInput {

    static func urlPathProvider(_ value: CreateGraphInput) -> Swift.String? {
        return "/graph"
    }
}

extension CreateMembersInput {

    static func urlPathProvider(_ value: CreateMembersInput) -> Swift.String? {
        return "/graph/members"
    }
}

extension DeleteGraphInput {

    static func urlPathProvider(_ value: DeleteGraphInput) -> Swift.String? {
        return "/graph/removal"
    }
}

extension DeleteMembersInput {

    static func urlPathProvider(_ value: DeleteMembersInput) -> Swift.String? {
        return "/graph/members/removal"
    }
}

extension DescribeOrganizationConfigurationInput {

    static func urlPathProvider(_ value: DescribeOrganizationConfigurationInput) -> Swift.String? {
        return "/orgs/describeOrganizationConfiguration"
    }
}

extension DisableOrganizationAdminAccountInput {

    static func urlPathProvider(_ value: DisableOrganizationAdminAccountInput) -> Swift.String? {
        return "/orgs/disableAdminAccount"
    }
}

extension DisassociateMembershipInput {

    static func urlPathProvider(_ value: DisassociateMembershipInput) -> Swift.String? {
        return "/membership/removal"
    }
}

extension EnableOrganizationAdminAccountInput {

    static func urlPathProvider(_ value: EnableOrganizationAdminAccountInput) -> Swift.String? {
        return "/orgs/enableAdminAccount"
    }
}

extension GetInvestigationInput {

    static func urlPathProvider(_ value: GetInvestigationInput) -> Swift.String? {
        return "/investigations/getInvestigation"
    }
}

extension GetMembersInput {

    static func urlPathProvider(_ value: GetMembersInput) -> Swift.String? {
        return "/graph/members/get"
    }
}

extension ListDatasourcePackagesInput {

    static func urlPathProvider(_ value: ListDatasourcePackagesInput) -> Swift.String? {
        return "/graph/datasources/list"
    }
}

extension ListGraphsInput {

    static func urlPathProvider(_ value: ListGraphsInput) -> Swift.String? {
        return "/graphs/list"
    }
}

extension ListIndicatorsInput {

    static func urlPathProvider(_ value: ListIndicatorsInput) -> Swift.String? {
        return "/investigations/listIndicators"
    }
}

extension ListInvestigationsInput {

    static func urlPathProvider(_ value: ListInvestigationsInput) -> Swift.String? {
        return "/investigations/listInvestigations"
    }
}

extension ListInvitationsInput {

    static func urlPathProvider(_ value: ListInvitationsInput) -> Swift.String? {
        return "/invitations/list"
    }
}

extension ListMembersInput {

    static func urlPathProvider(_ value: ListMembersInput) -> Swift.String? {
        return "/graph/members/list"
    }
}

extension ListOrganizationAdminAccountsInput {

    static func urlPathProvider(_ value: ListOrganizationAdminAccountsInput) -> Swift.String? {
        return "/orgs/adminAccountslist"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension RejectInvitationInput {

    static func urlPathProvider(_ value: RejectInvitationInput) -> Swift.String? {
        return "/invitation/removal"
    }
}

extension StartInvestigationInput {

    static func urlPathProvider(_ value: StartInvestigationInput) -> Swift.String? {
        return "/investigations/startInvestigation"
    }
}

extension StartMonitoringMemberInput {

    static func urlPathProvider(_ value: StartMonitoringMemberInput) -> Swift.String? {
        return "/graph/member/monitoringstate"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension UpdateDatasourcePackagesInput {

    static func urlPathProvider(_ value: UpdateDatasourcePackagesInput) -> Swift.String? {
        return "/graph/datasources/update"
    }
}

extension UpdateInvestigationStateInput {

    static func urlPathProvider(_ value: UpdateInvestigationStateInput) -> Swift.String? {
        return "/investigations/updateInvestigationState"
    }
}

extension UpdateOrganizationConfigurationInput {

    static func urlPathProvider(_ value: UpdateOrganizationConfigurationInput) -> Swift.String? {
        return "/orgs/updateOrganizationConfiguration"
    }
}

extension AcceptInvitationInput {

    static func write(value: AcceptInvitationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["GraphArn"].write(value.graphArn)
    }
}

extension BatchGetGraphMemberDatasourcesInput {

    static func write(value: BatchGetGraphMemberDatasourcesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AccountIds"].writeList(value.accountIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["GraphArn"].write(value.graphArn)
    }
}

extension BatchGetMembershipDatasourcesInput {

    static func write(value: BatchGetMembershipDatasourcesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["GraphArns"].writeList(value.graphArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateGraphInput {

    static func write(value: CreateGraphInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CreateMembersInput {

    static func write(value: CreateMembersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Accounts"].writeList(value.accounts, memberWritingClosure: DetectiveClientTypes.Account.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["DisableEmailNotification"].write(value.disableEmailNotification)
        try writer["GraphArn"].write(value.graphArn)
        try writer["Message"].write(value.message)
    }
}

extension DeleteGraphInput {

    static func write(value: DeleteGraphInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["GraphArn"].write(value.graphArn)
    }
}

extension DeleteMembersInput {

    static func write(value: DeleteMembersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AccountIds"].writeList(value.accountIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["GraphArn"].write(value.graphArn)
    }
}

extension DescribeOrganizationConfigurationInput {

    static func write(value: DescribeOrganizationConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["GraphArn"].write(value.graphArn)
    }
}

extension DisassociateMembershipInput {

    static func write(value: DisassociateMembershipInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["GraphArn"].write(value.graphArn)
    }
}

extension EnableOrganizationAdminAccountInput {

    static func write(value: EnableOrganizationAdminAccountInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AccountId"].write(value.accountId)
    }
}

extension GetInvestigationInput {

    static func write(value: GetInvestigationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["GraphArn"].write(value.graphArn)
        try writer["InvestigationId"].write(value.investigationId)
    }
}

extension GetMembersInput {

    static func write(value: GetMembersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AccountIds"].writeList(value.accountIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["GraphArn"].write(value.graphArn)
    }
}

extension ListDatasourcePackagesInput {

    static func write(value: ListDatasourcePackagesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["GraphArn"].write(value.graphArn)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListGraphsInput {

    static func write(value: ListGraphsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListIndicatorsInput {

    static func write(value: ListIndicatorsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["GraphArn"].write(value.graphArn)
        try writer["IndicatorType"].write(value.indicatorType)
        try writer["InvestigationId"].write(value.investigationId)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListInvestigationsInput {

    static func write(value: ListInvestigationsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["FilterCriteria"].write(value.filterCriteria, with: DetectiveClientTypes.FilterCriteria.write(value:to:))
        try writer["GraphArn"].write(value.graphArn)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["SortCriteria"].write(value.sortCriteria, with: DetectiveClientTypes.SortCriteria.write(value:to:))
    }
}

extension ListInvitationsInput {

    static func write(value: ListInvitationsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListMembersInput {

    static func write(value: ListMembersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["GraphArn"].write(value.graphArn)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListOrganizationAdminAccountsInput {

    static func write(value: ListOrganizationAdminAccountsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension RejectInvitationInput {

    static func write(value: RejectInvitationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["GraphArn"].write(value.graphArn)
    }
}

extension StartInvestigationInput {

    static func write(value: StartInvestigationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["EntityArn"].write(value.entityArn)
        try writer["GraphArn"].write(value.graphArn)
        try writer["ScopeEndTime"].writeTimestamp(value.scopeEndTime, format: SmithyTimestamps.TimestampFormat.dateTime)
        try writer["ScopeStartTime"].writeTimestamp(value.scopeStartTime, format: SmithyTimestamps.TimestampFormat.dateTime)
    }
}

extension StartMonitoringMemberInput {

    static func write(value: StartMonitoringMemberInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AccountId"].write(value.accountId)
        try writer["GraphArn"].write(value.graphArn)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension UpdateDatasourcePackagesInput {

    static func write(value: UpdateDatasourcePackagesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DatasourcePackages"].writeList(value.datasourcePackages, memberWritingClosure: SmithyReadWrite.WritingClosureBox<DetectiveClientTypes.DatasourcePackage>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["GraphArn"].write(value.graphArn)
    }
}

extension UpdateInvestigationStateInput {

    static func write(value: UpdateInvestigationStateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["GraphArn"].write(value.graphArn)
        try writer["InvestigationId"].write(value.investigationId)
        try writer["State"].write(value.state)
    }
}

extension UpdateOrganizationConfigurationInput {

    static func write(value: UpdateOrganizationConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AutoEnable"].write(value.autoEnable)
        try writer["GraphArn"].write(value.graphArn)
    }
}

extension AcceptInvitationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AcceptInvitationOutput {
        return AcceptInvitationOutput()
    }
}

extension BatchGetGraphMemberDatasourcesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> BatchGetGraphMemberDatasourcesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = BatchGetGraphMemberDatasourcesOutput()
        value.memberDatasources = try reader["MemberDatasources"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.MembershipDatasources.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.unprocessedAccounts = try reader["UnprocessedAccounts"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.UnprocessedAccount.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension BatchGetMembershipDatasourcesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> BatchGetMembershipDatasourcesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = BatchGetMembershipDatasourcesOutput()
        value.membershipDatasources = try reader["MembershipDatasources"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.MembershipDatasources.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.unprocessedGraphs = try reader["UnprocessedGraphs"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.UnprocessedGraph.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension CreateGraphOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateGraphOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateGraphOutput()
        value.graphArn = try reader["GraphArn"].readIfPresent()
        return value
    }
}

extension CreateMembersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateMembersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateMembersOutput()
        value.members = try reader["Members"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.MemberDetail.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.unprocessedAccounts = try reader["UnprocessedAccounts"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.UnprocessedAccount.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DeleteGraphOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteGraphOutput {
        return DeleteGraphOutput()
    }
}

extension DeleteMembersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteMembersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteMembersOutput()
        value.accountIds = try reader["AccountIds"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.unprocessedAccounts = try reader["UnprocessedAccounts"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.UnprocessedAccount.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeOrganizationConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeOrganizationConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeOrganizationConfigurationOutput()
        value.autoEnable = try reader["AutoEnable"].readIfPresent() ?? false
        return value
    }
}

extension DisableOrganizationAdminAccountOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisableOrganizationAdminAccountOutput {
        return DisableOrganizationAdminAccountOutput()
    }
}

extension DisassociateMembershipOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisassociateMembershipOutput {
        return DisassociateMembershipOutput()
    }
}

extension EnableOrganizationAdminAccountOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> EnableOrganizationAdminAccountOutput {
        return EnableOrganizationAdminAccountOutput()
    }
}

extension GetInvestigationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetInvestigationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetInvestigationOutput()
        value.createdTime = try reader["CreatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.entityArn = try reader["EntityArn"].readIfPresent()
        value.entityType = try reader["EntityType"].readIfPresent()
        value.graphArn = try reader["GraphArn"].readIfPresent()
        value.investigationId = try reader["InvestigationId"].readIfPresent()
        value.scopeEndTime = try reader["ScopeEndTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.scopeStartTime = try reader["ScopeStartTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.severity = try reader["Severity"].readIfPresent()
        value.state = try reader["State"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        return value
    }
}

extension GetMembersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetMembersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetMembersOutput()
        value.memberDetails = try reader["MemberDetails"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.MemberDetail.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.unprocessedAccounts = try reader["UnprocessedAccounts"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.UnprocessedAccount.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListDatasourcePackagesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListDatasourcePackagesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListDatasourcePackagesOutput()
        value.datasourcePackages = try reader["DatasourcePackages"].readMapIfPresent(valueReadingClosure: DetectiveClientTypes.DatasourcePackageIngestDetail.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListGraphsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListGraphsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListGraphsOutput()
        value.graphList = try reader["GraphList"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.Graph.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListIndicatorsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListIndicatorsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListIndicatorsOutput()
        value.graphArn = try reader["GraphArn"].readIfPresent()
        value.indicators = try reader["Indicators"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.Indicator.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.investigationId = try reader["InvestigationId"].readIfPresent()
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListInvestigationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListInvestigationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListInvestigationsOutput()
        value.investigationDetails = try reader["InvestigationDetails"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.InvestigationDetail.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListInvitationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListInvitationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListInvitationsOutput()
        value.invitations = try reader["Invitations"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.MemberDetail.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListMembersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListMembersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListMembersOutput()
        value.memberDetails = try reader["MemberDetails"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.MemberDetail.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListOrganizationAdminAccountsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListOrganizationAdminAccountsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListOrganizationAdminAccountsOutput()
        value.administrators = try reader["Administrators"].readListIfPresent(memberReadingClosure: DetectiveClientTypes.Administrator.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension RejectInvitationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RejectInvitationOutput {
        return RejectInvitationOutput()
    }
}

extension StartInvestigationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartInvestigationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartInvestigationOutput()
        value.investigationId = try reader["InvestigationId"].readIfPresent()
        return value
    }
}

extension StartMonitoringMemberOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartMonitoringMemberOutput {
        return StartMonitoringMemberOutput()
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateDatasourcePackagesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateDatasourcePackagesOutput {
        return UpdateDatasourcePackagesOutput()
    }
}

extension UpdateInvestigationStateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateInvestigationStateOutput {
        return UpdateInvestigationStateOutput()
    }
}

extension UpdateOrganizationConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateOrganizationConfigurationOutput {
        return UpdateOrganizationConfigurationOutput()
    }
}

enum AcceptInvitationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum BatchGetGraphMemberDatasourcesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum BatchGetMembershipDatasourcesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateGraphOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateMembersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteGraphOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteMembersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeOrganizationConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisableOrganizationAdminAccountOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisassociateMembershipOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum EnableOrganizationAdminAccountOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetInvestigationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetMembersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListDatasourcePackagesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListGraphsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListIndicatorsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListInvestigationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListInvitationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListMembersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListOrganizationAdminAccountsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RejectInvitationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartInvestigationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartMonitoringMemberOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateDatasourcePackagesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateInvestigationStateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateOrganizationConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent()
        value.properties.errorCodeReason = try reader["ErrorCodeReason"].readIfPresent()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.errorCode = try reader["ErrorCode"].readIfPresent()
        value.properties.errorCodeReason = try reader["ErrorCodeReason"].readIfPresent()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.subErrorCode = try reader["SubErrorCode"].readIfPresent()
        value.properties.subErrorCodeReason = try reader["SubErrorCodeReason"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceQuotaExceededException {
        let reader = baseError.errorBodyReader
        var value = ServiceQuotaExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.resources = try reader["Resources"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TooManyRequestsException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> TooManyRequestsException {
        let reader = baseError.errorBodyReader
        var value = TooManyRequestsException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DetectiveClientTypes.MembershipDatasources {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.MembershipDatasources {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.MembershipDatasources()
        value.accountId = try reader["AccountId"].readIfPresent()
        value.graphArn = try reader["GraphArn"].readIfPresent()
        value.datasourcePackageIngestHistory = try reader["DatasourcePackageIngestHistory"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.mapReadingClosure(valueReadingClosure: DetectiveClientTypes.TimestampForCollection.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DetectiveClientTypes.TimestampForCollection {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.TimestampForCollection {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.TimestampForCollection()
        value.timestamp = try reader["Timestamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension DetectiveClientTypes.UnprocessedAccount {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.UnprocessedAccount {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.UnprocessedAccount()
        value.accountId = try reader["AccountId"].readIfPresent()
        value.reason = try reader["Reason"].readIfPresent()
        return value
    }
}

extension DetectiveClientTypes.UnprocessedGraph {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.UnprocessedGraph {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.UnprocessedGraph()
        value.graphArn = try reader["GraphArn"].readIfPresent()
        value.reason = try reader["Reason"].readIfPresent()
        return value
    }
}

extension DetectiveClientTypes.MemberDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.MemberDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.MemberDetail()
        value.accountId = try reader["AccountId"].readIfPresent()
        value.emailAddress = try reader["EmailAddress"].readIfPresent()
        value.graphArn = try reader["GraphArn"].readIfPresent()
        value.masterId = try reader["MasterId"].readIfPresent()
        value.administratorId = try reader["AdministratorId"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.disabledReason = try reader["DisabledReason"].readIfPresent()
        value.invitedTime = try reader["InvitedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.updatedTime = try reader["UpdatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.volumeUsageInBytes = try reader["VolumeUsageInBytes"].readIfPresent()
        value.volumeUsageUpdatedTime = try reader["VolumeUsageUpdatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.percentOfGraphUtilization = try reader["PercentOfGraphUtilization"].readIfPresent()
        value.percentOfGraphUtilizationUpdatedTime = try reader["PercentOfGraphUtilizationUpdatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.invitationType = try reader["InvitationType"].readIfPresent()
        value.volumeUsageByDatasourcePackage = try reader["VolumeUsageByDatasourcePackage"].readMapIfPresent(valueReadingClosure: DetectiveClientTypes.DatasourcePackageUsageInfo.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.datasourcePackageIngestStates = try reader["DatasourcePackageIngestStates"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosureBox<DetectiveClientTypes.DatasourcePackageIngestState>().read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DetectiveClientTypes.DatasourcePackageUsageInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.DatasourcePackageUsageInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.DatasourcePackageUsageInfo()
        value.volumeUsageInBytes = try reader["VolumeUsageInBytes"].readIfPresent()
        value.volumeUsageUpdateTime = try reader["VolumeUsageUpdateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension DetectiveClientTypes.DatasourcePackageIngestDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.DatasourcePackageIngestDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.DatasourcePackageIngestDetail()
        value.datasourcePackageIngestState = try reader["DatasourcePackageIngestState"].readIfPresent()
        value.lastIngestStateChange = try reader["LastIngestStateChange"].readMapIfPresent(valueReadingClosure: DetectiveClientTypes.TimestampForCollection.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DetectiveClientTypes.Graph {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.Graph {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.Graph()
        value.arn = try reader["Arn"].readIfPresent()
        value.createdTime = try reader["CreatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension DetectiveClientTypes.Indicator {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.Indicator {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.Indicator()
        value.indicatorType = try reader["IndicatorType"].readIfPresent()
        value.indicatorDetail = try reader["IndicatorDetail"].readIfPresent(with: DetectiveClientTypes.IndicatorDetail.read(from:))
        return value
    }
}

extension DetectiveClientTypes.IndicatorDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.IndicatorDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.IndicatorDetail()
        value.ttPsObservedDetail = try reader["TTPsObservedDetail"].readIfPresent(with: DetectiveClientTypes.TTPsObservedDetail.read(from:))
        value.impossibleTravelDetail = try reader["ImpossibleTravelDetail"].readIfPresent(with: DetectiveClientTypes.ImpossibleTravelDetail.read(from:))
        value.flaggedIpAddressDetail = try reader["FlaggedIpAddressDetail"].readIfPresent(with: DetectiveClientTypes.FlaggedIpAddressDetail.read(from:))
        value.newGeolocationDetail = try reader["NewGeolocationDetail"].readIfPresent(with: DetectiveClientTypes.NewGeolocationDetail.read(from:))
        value.newAsoDetail = try reader["NewAsoDetail"].readIfPresent(with: DetectiveClientTypes.NewAsoDetail.read(from:))
        value.newUserAgentDetail = try reader["NewUserAgentDetail"].readIfPresent(with: DetectiveClientTypes.NewUserAgentDetail.read(from:))
        value.relatedFindingDetail = try reader["RelatedFindingDetail"].readIfPresent(with: DetectiveClientTypes.RelatedFindingDetail.read(from:))
        value.relatedFindingGroupDetail = try reader["RelatedFindingGroupDetail"].readIfPresent(with: DetectiveClientTypes.RelatedFindingGroupDetail.read(from:))
        return value
    }
}

extension DetectiveClientTypes.RelatedFindingGroupDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.RelatedFindingGroupDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.RelatedFindingGroupDetail()
        value.id = try reader["Id"].readIfPresent()
        return value
    }
}

extension DetectiveClientTypes.RelatedFindingDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.RelatedFindingDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.RelatedFindingDetail()
        value.arn = try reader["Arn"].readIfPresent()
        value.type = try reader["Type"].readIfPresent()
        value.ipAddress = try reader["IpAddress"].readIfPresent()
        return value
    }
}

extension DetectiveClientTypes.NewUserAgentDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.NewUserAgentDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.NewUserAgentDetail()
        value.userAgent = try reader["UserAgent"].readIfPresent()
        value.isNewForEntireAccount = try reader["IsNewForEntireAccount"].readIfPresent() ?? false
        return value
    }
}

extension DetectiveClientTypes.NewAsoDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.NewAsoDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.NewAsoDetail()
        value.aso = try reader["Aso"].readIfPresent()
        value.isNewForEntireAccount = try reader["IsNewForEntireAccount"].readIfPresent() ?? false
        return value
    }
}

extension DetectiveClientTypes.NewGeolocationDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.NewGeolocationDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.NewGeolocationDetail()
        value.location = try reader["Location"].readIfPresent()
        value.ipAddress = try reader["IpAddress"].readIfPresent()
        value.isNewForEntireAccount = try reader["IsNewForEntireAccount"].readIfPresent() ?? false
        return value
    }
}

extension DetectiveClientTypes.FlaggedIpAddressDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.FlaggedIpAddressDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.FlaggedIpAddressDetail()
        value.ipAddress = try reader["IpAddress"].readIfPresent()
        value.reason = try reader["Reason"].readIfPresent()
        return value
    }
}

extension DetectiveClientTypes.ImpossibleTravelDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.ImpossibleTravelDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.ImpossibleTravelDetail()
        value.startingIpAddress = try reader["StartingIpAddress"].readIfPresent()
        value.endingIpAddress = try reader["EndingIpAddress"].readIfPresent()
        value.startingLocation = try reader["StartingLocation"].readIfPresent()
        value.endingLocation = try reader["EndingLocation"].readIfPresent()
        value.hourlyTimeDelta = try reader["HourlyTimeDelta"].readIfPresent()
        return value
    }
}

extension DetectiveClientTypes.TTPsObservedDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.TTPsObservedDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.TTPsObservedDetail()
        value.tactic = try reader["Tactic"].readIfPresent()
        value.technique = try reader["Technique"].readIfPresent()
        value.procedure = try reader["Procedure"].readIfPresent()
        value.ipAddress = try reader["IpAddress"].readIfPresent()
        value.apiName = try reader["APIName"].readIfPresent()
        value.apiSuccessCount = try reader["APISuccessCount"].readIfPresent() ?? 0
        value.apiFailureCount = try reader["APIFailureCount"].readIfPresent() ?? 0
        return value
    }
}

extension DetectiveClientTypes.InvestigationDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.InvestigationDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.InvestigationDetail()
        value.investigationId = try reader["InvestigationId"].readIfPresent()
        value.severity = try reader["Severity"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.state = try reader["State"].readIfPresent()
        value.createdTime = try reader["CreatedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.entityArn = try reader["EntityArn"].readIfPresent()
        value.entityType = try reader["EntityType"].readIfPresent()
        return value
    }
}

extension DetectiveClientTypes.Administrator {

    static func read(from reader: SmithyJSON.Reader) throws -> DetectiveClientTypes.Administrator {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DetectiveClientTypes.Administrator()
        value.accountId = try reader["AccountId"].readIfPresent()
        value.graphArn = try reader["GraphArn"].readIfPresent()
        value.delegationTime = try reader["DelegationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension DetectiveClientTypes.Account {

    static func write(value: DetectiveClientTypes.Account?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AccountId"].write(value.accountId)
        try writer["EmailAddress"].write(value.emailAddress)
    }
}

extension DetectiveClientTypes.FilterCriteria {

    static func write(value: DetectiveClientTypes.FilterCriteria?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CreatedTime"].write(value.createdTime, with: DetectiveClientTypes.DateFilter.write(value:to:))
        try writer["EntityArn"].write(value.entityArn, with: DetectiveClientTypes.StringFilter.write(value:to:))
        try writer["Severity"].write(value.severity, with: DetectiveClientTypes.StringFilter.write(value:to:))
        try writer["State"].write(value.state, with: DetectiveClientTypes.StringFilter.write(value:to:))
        try writer["Status"].write(value.status, with: DetectiveClientTypes.StringFilter.write(value:to:))
    }
}

extension DetectiveClientTypes.DateFilter {

    static func write(value: DetectiveClientTypes.DateFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["EndInclusive"].writeTimestamp(value.endInclusive, format: SmithyTimestamps.TimestampFormat.dateTime)
        try writer["StartInclusive"].writeTimestamp(value.startInclusive, format: SmithyTimestamps.TimestampFormat.dateTime)
    }
}

extension DetectiveClientTypes.StringFilter {

    static func write(value: DetectiveClientTypes.StringFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Value"].write(value.value)
    }
}

extension DetectiveClientTypes.SortCriteria {

    static func write(value: DetectiveClientTypes.SortCriteria?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Field"].write(value.field)
        try writer["SortOrder"].write(value.sortOrder)
    }
}

public enum DetectiveClientTypes {}
