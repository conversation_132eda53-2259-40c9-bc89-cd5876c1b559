//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHT<PERSON>API.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.AWSJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError

/// You already have a DAX cluster with the given identifier.
public struct ClusterAlreadyExistsFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ClusterAlreadyExists" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// You have attempted to exceed the maximum number of DAX clusters for your AWS account.
public struct ClusterQuotaForCustomerExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ClusterQuotaForCustomerExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// There are not enough system resources to create the cluster you requested (or to resize an already-existing cluster).
public struct InsufficientClusterCapacityFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InsufficientClusterCapacity" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The requested DAX cluster is not in the available state.
public struct InvalidClusterStateFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidClusterState" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Two or more incompatible parameters were specified.
public struct InvalidParameterCombinationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidParameterCombination" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// One or more parameters in a parameter group are in an invalid state.
public struct InvalidParameterGroupStateFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidParameterGroupState" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The value for a parameter is invalid.
public struct InvalidParameterValueException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidParameterValue" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The VPC network is in an invalid state.
public struct InvalidVPCNetworkStateFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidVPCNetworkStateFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// You have attempted to exceed the maximum number of nodes for a DAX cluster.
public struct NodeQuotaForClusterExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "NodeQuotaForClusterExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// You have attempted to exceed the maximum number of nodes for your AWS account.
public struct NodeQuotaForCustomerExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "NodeQuotaForCustomerExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified parameter group does not exist.
public struct ParameterGroupNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ParameterGroupNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified service linked role (SLR) was not found.
public struct ServiceLinkedRoleNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceLinkedRoleNotFoundFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// You have reached the maximum number of x509 certificates that can be created for encrypted clusters in a 30 day period. Contact AWS customer support to discuss options for continuing to create encrypted clusters.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {
    public static var typeName: Swift.String { "ServiceQuotaExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init() { }
}

/// The requested subnet group name does not refer to an existing subnet group.
public struct SubnetGroupNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SubnetGroupNotFoundFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// You have exceeded the maximum number of tags for this DAX cluster.
public struct TagQuotaPerResourceExceeded: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TagQuotaPerResourceExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension DAXClientTypes {

    public enum ClusterEndpointEncryptionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case `none`
        case tls
        case sdkUnknown(Swift.String)

        public static var allCases: [ClusterEndpointEncryptionType] {
            return [
                .none,
                .tls
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .none: return "NONE"
            case .tls: return "TLS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DAXClientTypes {

    /// Represents the settings used to enable server-side encryption.
    public struct SSESpecification: Swift.Sendable {
        /// Indicates whether server-side encryption is enabled (true) or disabled (false) on the cluster.
        /// This member is required.
        public var enabled: Swift.Bool?

        public init(
            enabled: Swift.Bool? = nil
        )
        {
            self.enabled = enabled
        }
    }
}

extension DAXClientTypes {

    /// A description of a tag. Every tag is a key-value pair. You can add up to 50 tags to a single DAX cluster. AWS-assigned tag names and values are automatically assigned the aws: prefix, which the user cannot assign. AWS-assigned tag names do not count towards the tag limit of 50. User-assigned tag names have the prefix user:. You cannot backdate the application of a tag.
    public struct Tag: Swift.Sendable {
        /// The key for the tag. Tag keys are case sensitive. Every DAX cluster can only have one tag with the same key. If you try to add an existing tag (same key), the existing tag value will be updated to the new value.
        public var key: Swift.String?
        /// The value of the tag. Tag values are case-sensitive and can be null.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

public struct CreateClusterInput: Swift.Sendable {
    /// The Availability Zones (AZs) in which the cluster nodes will reside after the cluster has been created or updated. If provided, the length of this list must equal the ReplicationFactor parameter. If you omit this parameter, DAX will spread the nodes across Availability Zones for the highest availability.
    public var availabilityZones: [Swift.String]?
    /// The type of encryption the cluster's endpoint should support. Values are:
    ///
    /// * NONE for no encryption
    ///
    /// * TLS for Transport Layer Security
    public var clusterEndpointEncryptionType: DAXClientTypes.ClusterEndpointEncryptionType?
    /// The cluster identifier. This parameter is stored as a lowercase string. Constraints:
    ///
    /// * A name must contain from 1 to 20 alphanumeric characters or hyphens.
    ///
    /// * The first character must be a letter.
    ///
    /// * A name cannot end with a hyphen or contain two consecutive hyphens.
    /// This member is required.
    public var clusterName: Swift.String?
    /// A description of the cluster.
    public var description: Swift.String?
    /// A valid Amazon Resource Name (ARN) that identifies an IAM role. At runtime, DAX will assume this role and use the role's permissions to access DynamoDB on your behalf.
    /// This member is required.
    public var iamRoleArn: Swift.String?
    /// The compute and memory capacity of the nodes in the cluster.
    /// This member is required.
    public var nodeType: Swift.String?
    /// The Amazon Resource Name (ARN) of the Amazon SNS topic to which notifications will be sent. The Amazon SNS topic owner must be same as the DAX cluster owner.
    public var notificationTopicArn: Swift.String?
    /// The parameter group to be associated with the DAX cluster.
    public var parameterGroupName: Swift.String?
    /// Specifies the weekly time range during which maintenance on the DAX cluster is performed. It is specified as a range in the format ddd:hh24:mi-ddd:hh24:mi (24H Clock UTC). The minimum maintenance window is a 60 minute period. Valid values for ddd are:
    ///
    /// * sun
    ///
    /// * mon
    ///
    /// * tue
    ///
    /// * wed
    ///
    /// * thu
    ///
    /// * fri
    ///
    /// * sat
    ///
    ///
    /// Example: sun:05:00-sun:09:00 If you don't specify a preferred maintenance window when you create or modify a cache cluster, DAX assigns a 60-minute maintenance window on a randomly selected day of the week.
    public var preferredMaintenanceWindow: Swift.String?
    /// The number of nodes in the DAX cluster. A replication factor of 1 will create a single-node cluster, without any read replicas. For additional fault tolerance, you can create a multiple node cluster with one or more read replicas. To do this, set ReplicationFactor to a number between 3 (one primary and two read replicas) and 10 (one primary and nine read replicas). If the AvailabilityZones parameter is provided, its length must equal the ReplicationFactor. AWS recommends that you have at least two read replicas per cluster.
    /// This member is required.
    public var replicationFactor: Swift.Int?
    /// A list of security group IDs to be assigned to each node in the DAX cluster. (Each of the security group ID is system-generated.) If this parameter is not specified, DAX assigns the default VPC security group to each node.
    public var securityGroupIds: [Swift.String]?
    /// Represents the settings used to enable server-side encryption on the cluster.
    public var sseSpecification: DAXClientTypes.SSESpecification?
    /// The name of the subnet group to be used for the replication group. DAX clusters can only run in an Amazon VPC environment. All of the subnets that you specify in a subnet group must exist in the same VPC.
    public var subnetGroupName: Swift.String?
    /// A set of tags to associate with the DAX cluster.
    public var tags: [DAXClientTypes.Tag]?

    public init(
        availabilityZones: [Swift.String]? = nil,
        clusterEndpointEncryptionType: DAXClientTypes.ClusterEndpointEncryptionType? = nil,
        clusterName: Swift.String? = nil,
        description: Swift.String? = nil,
        iamRoleArn: Swift.String? = nil,
        nodeType: Swift.String? = nil,
        notificationTopicArn: Swift.String? = nil,
        parameterGroupName: Swift.String? = nil,
        preferredMaintenanceWindow: Swift.String? = nil,
        replicationFactor: Swift.Int? = 0,
        securityGroupIds: [Swift.String]? = nil,
        sseSpecification: DAXClientTypes.SSESpecification? = nil,
        subnetGroupName: Swift.String? = nil,
        tags: [DAXClientTypes.Tag]? = nil
    )
    {
        self.availabilityZones = availabilityZones
        self.clusterEndpointEncryptionType = clusterEndpointEncryptionType
        self.clusterName = clusterName
        self.description = description
        self.iamRoleArn = iamRoleArn
        self.nodeType = nodeType
        self.notificationTopicArn = notificationTopicArn
        self.parameterGroupName = parameterGroupName
        self.preferredMaintenanceWindow = preferredMaintenanceWindow
        self.replicationFactor = replicationFactor
        self.securityGroupIds = securityGroupIds
        self.sseSpecification = sseSpecification
        self.subnetGroupName = subnetGroupName
        self.tags = tags
    }
}

extension DAXClientTypes {

    /// Represents the information required for client programs to connect to the endpoint for a DAX cluster.
    public struct Endpoint: Swift.Sendable {
        /// The DNS hostname of the endpoint.
        public var address: Swift.String?
        /// The port number that applications should use to connect to the endpoint.
        public var port: Swift.Int
        /// The URL that applications should use to connect to the endpoint. The default ports are 8111 for the "dax" protocol and 9111 for the "daxs" protocol.
        public var url: Swift.String?

        public init(
            address: Swift.String? = nil,
            port: Swift.Int = 0,
            url: Swift.String? = nil
        )
        {
            self.address = address
            self.port = port
            self.url = url
        }
    }
}

extension DAXClientTypes {

    /// Represents an individual node within a DAX cluster.
    public struct Node: Swift.Sendable {
        /// The Availability Zone (AZ) in which the node has been deployed.
        public var availabilityZone: Swift.String?
        /// The endpoint for the node, consisting of a DNS name and a port number. Client applications can connect directly to a node endpoint, if desired (as an alternative to allowing DAX client software to intelligently route requests and responses to nodes in the DAX cluster.
        public var endpoint: DAXClientTypes.Endpoint?
        /// The date and time (in UNIX epoch format) when the node was launched.
        public var nodeCreateTime: Foundation.Date?
        /// A system-generated identifier for the node.
        public var nodeId: Swift.String?
        /// The current status of the node. For example: available.
        public var nodeStatus: Swift.String?
        /// The status of the parameter group associated with this node. For example, in-sync.
        public var parameterGroupStatus: Swift.String?

        public init(
            availabilityZone: Swift.String? = nil,
            endpoint: DAXClientTypes.Endpoint? = nil,
            nodeCreateTime: Foundation.Date? = nil,
            nodeId: Swift.String? = nil,
            nodeStatus: Swift.String? = nil,
            parameterGroupStatus: Swift.String? = nil
        )
        {
            self.availabilityZone = availabilityZone
            self.endpoint = endpoint
            self.nodeCreateTime = nodeCreateTime
            self.nodeId = nodeId
            self.nodeStatus = nodeStatus
            self.parameterGroupStatus = parameterGroupStatus
        }
    }
}

extension DAXClientTypes {

    /// Describes a notification topic and its status. Notification topics are used for publishing DAX events to subscribers using Amazon Simple Notification Service (SNS).
    public struct NotificationConfiguration: Swift.Sendable {
        /// The Amazon Resource Name (ARN) that identifies the topic.
        public var topicArn: Swift.String?
        /// The current state of the topic. A value of “active” means that notifications will be sent to the topic. A value of “inactive” means that notifications will not be sent to the topic.
        public var topicStatus: Swift.String?

        public init(
            topicArn: Swift.String? = nil,
            topicStatus: Swift.String? = nil
        )
        {
            self.topicArn = topicArn
            self.topicStatus = topicStatus
        }
    }
}

extension DAXClientTypes {

    /// The status of a parameter group.
    public struct ParameterGroupStatus: Swift.Sendable {
        /// The node IDs of one or more nodes to be rebooted.
        public var nodeIdsToReboot: [Swift.String]?
        /// The status of parameter updates.
        public var parameterApplyStatus: Swift.String?
        /// The name of the parameter group.
        public var parameterGroupName: Swift.String?

        public init(
            nodeIdsToReboot: [Swift.String]? = nil,
            parameterApplyStatus: Swift.String? = nil,
            parameterGroupName: Swift.String? = nil
        )
        {
            self.nodeIdsToReboot = nodeIdsToReboot
            self.parameterApplyStatus = parameterApplyStatus
            self.parameterGroupName = parameterGroupName
        }
    }
}

extension DAXClientTypes {

    /// An individual VPC security group and its status.
    public struct SecurityGroupMembership: Swift.Sendable {
        /// The unique ID for this security group.
        public var securityGroupIdentifier: Swift.String?
        /// The status of this security group.
        public var status: Swift.String?

        public init(
            securityGroupIdentifier: Swift.String? = nil,
            status: Swift.String? = nil
        )
        {
            self.securityGroupIdentifier = securityGroupIdentifier
            self.status = status
        }
    }
}

extension DAXClientTypes {

    public enum SSEStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case disabling
        case enabled
        case enabling
        case sdkUnknown(Swift.String)

        public static var allCases: [SSEStatus] {
            return [
                .disabled,
                .disabling,
                .enabled,
                .enabling
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "DISABLED"
            case .disabling: return "DISABLING"
            case .enabled: return "ENABLED"
            case .enabling: return "ENABLING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DAXClientTypes {

    /// The description of the server-side encryption status on the specified DAX cluster.
    public struct SSEDescription: Swift.Sendable {
        /// The current state of server-side encryption:
        ///
        /// * ENABLING - Server-side encryption is being enabled.
        ///
        /// * ENABLED - Server-side encryption is enabled.
        ///
        /// * DISABLING - Server-side encryption is being disabled.
        ///
        /// * DISABLED - Server-side encryption is disabled.
        public var status: DAXClientTypes.SSEStatus?

        public init(
            status: DAXClientTypes.SSEStatus? = nil
        )
        {
            self.status = status
        }
    }
}

extension DAXClientTypes {

    /// Contains all of the attributes of a specific DAX cluster.
    public struct Cluster: Swift.Sendable {
        /// The number of nodes in the cluster that are active (i.e., capable of serving requests).
        public var activeNodes: Swift.Int?
        /// The Amazon Resource Name (ARN) that uniquely identifies the cluster.
        public var clusterArn: Swift.String?
        /// The endpoint for this DAX cluster, consisting of a DNS name, a port number, and a URL. Applications should use the URL to configure the DAX client to find their cluster.
        public var clusterDiscoveryEndpoint: DAXClientTypes.Endpoint?
        /// The type of encryption supported by the cluster's endpoint. Values are:
        ///
        /// * NONE for no encryption TLS for Transport Layer Security
        public var clusterEndpointEncryptionType: DAXClientTypes.ClusterEndpointEncryptionType?
        /// The name of the DAX cluster.
        public var clusterName: Swift.String?
        /// The description of the cluster.
        public var description: Swift.String?
        /// A valid Amazon Resource Name (ARN) that identifies an IAM role. At runtime, DAX will assume this role and use the role's permissions to access DynamoDB on your behalf.
        public var iamRoleArn: Swift.String?
        /// A list of nodes to be removed from the cluster.
        public var nodeIdsToRemove: [Swift.String]?
        /// The node type for the nodes in the cluster. (All nodes in a DAX cluster are of the same type.)
        public var nodeType: Swift.String?
        /// A list of nodes that are currently in the cluster.
        public var nodes: [DAXClientTypes.Node]?
        /// Describes a notification topic and its status. Notification topics are used for publishing DAX events to subscribers using Amazon Simple Notification Service (SNS).
        public var notificationConfiguration: DAXClientTypes.NotificationConfiguration?
        /// The parameter group being used by nodes in the cluster.
        public var parameterGroup: DAXClientTypes.ParameterGroupStatus?
        /// A range of time when maintenance of DAX cluster software will be performed. For example: sun:01:00-sun:09:00. Cluster maintenance normally takes less than 30 minutes, and is performed automatically within the maintenance window.
        public var preferredMaintenanceWindow: Swift.String?
        /// A list of security groups, and the status of each, for the nodes in the cluster.
        public var securityGroups: [DAXClientTypes.SecurityGroupMembership]?
        /// The description of the server-side encryption status on the specified DAX cluster.
        public var sseDescription: DAXClientTypes.SSEDescription?
        /// The current status of the cluster.
        public var status: Swift.String?
        /// The subnet group where the DAX cluster is running.
        public var subnetGroup: Swift.String?
        /// The total number of nodes in the cluster.
        public var totalNodes: Swift.Int?

        public init(
            activeNodes: Swift.Int? = nil,
            clusterArn: Swift.String? = nil,
            clusterDiscoveryEndpoint: DAXClientTypes.Endpoint? = nil,
            clusterEndpointEncryptionType: DAXClientTypes.ClusterEndpointEncryptionType? = nil,
            clusterName: Swift.String? = nil,
            description: Swift.String? = nil,
            iamRoleArn: Swift.String? = nil,
            nodeIdsToRemove: [Swift.String]? = nil,
            nodeType: Swift.String? = nil,
            nodes: [DAXClientTypes.Node]? = nil,
            notificationConfiguration: DAXClientTypes.NotificationConfiguration? = nil,
            parameterGroup: DAXClientTypes.ParameterGroupStatus? = nil,
            preferredMaintenanceWindow: Swift.String? = nil,
            securityGroups: [DAXClientTypes.SecurityGroupMembership]? = nil,
            sseDescription: DAXClientTypes.SSEDescription? = nil,
            status: Swift.String? = nil,
            subnetGroup: Swift.String? = nil,
            totalNodes: Swift.Int? = nil
        )
        {
            self.activeNodes = activeNodes
            self.clusterArn = clusterArn
            self.clusterDiscoveryEndpoint = clusterDiscoveryEndpoint
            self.clusterEndpointEncryptionType = clusterEndpointEncryptionType
            self.clusterName = clusterName
            self.description = description
            self.iamRoleArn = iamRoleArn
            self.nodeIdsToRemove = nodeIdsToRemove
            self.nodeType = nodeType
            self.nodes = nodes
            self.notificationConfiguration = notificationConfiguration
            self.parameterGroup = parameterGroup
            self.preferredMaintenanceWindow = preferredMaintenanceWindow
            self.securityGroups = securityGroups
            self.sseDescription = sseDescription
            self.status = status
            self.subnetGroup = subnetGroup
            self.totalNodes = totalNodes
        }
    }
}

public struct CreateClusterOutput: Swift.Sendable {
    /// A description of the DAX cluster that you have created.
    public var cluster: DAXClientTypes.Cluster?

    public init(
        cluster: DAXClientTypes.Cluster? = nil
    )
    {
        self.cluster = cluster
    }
}

/// The specified parameter group already exists.
public struct ParameterGroupAlreadyExistsFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ParameterGroupAlreadyExists" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// You have attempted to exceed the maximum number of parameter groups.
public struct ParameterGroupQuotaExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ParameterGroupQuotaExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct CreateParameterGroupInput: Swift.Sendable {
    /// A description of the parameter group.
    public var description: Swift.String?
    /// The name of the parameter group to apply to all of the clusters in this replication group.
    /// This member is required.
    public var parameterGroupName: Swift.String?

    public init(
        description: Swift.String? = nil,
        parameterGroupName: Swift.String? = nil
    )
    {
        self.description = description
        self.parameterGroupName = parameterGroupName
    }
}

extension DAXClientTypes {

    /// A named set of parameters that are applied to all of the nodes in a DAX cluster.
    public struct ParameterGroup: Swift.Sendable {
        /// A description of the parameter group.
        public var description: Swift.String?
        /// The name of the parameter group.
        public var parameterGroupName: Swift.String?

        public init(
            description: Swift.String? = nil,
            parameterGroupName: Swift.String? = nil
        )
        {
            self.description = description
            self.parameterGroupName = parameterGroupName
        }
    }
}

public struct CreateParameterGroupOutput: Swift.Sendable {
    /// Represents the output of a CreateParameterGroup action.
    public var parameterGroup: DAXClientTypes.ParameterGroup?

    public init(
        parameterGroup: DAXClientTypes.ParameterGroup? = nil
    )
    {
        self.parameterGroup = parameterGroup
    }
}

/// An invalid subnet identifier was specified.
public struct InvalidSubnet: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidSubnet" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified subnet group already exists.
public struct SubnetGroupAlreadyExistsFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SubnetGroupAlreadyExists" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request cannot be processed because it would exceed the allowed number of subnets in a subnet group.
public struct SubnetGroupQuotaExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SubnetGroupQuotaExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request cannot be processed because it would exceed the allowed number of subnets in a subnet group.
public struct SubnetQuotaExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SubnetQuotaExceededFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct CreateSubnetGroupInput: Swift.Sendable {
    /// A description for the subnet group
    public var description: Swift.String?
    /// A name for the subnet group. This value is stored as a lowercase string.
    /// This member is required.
    public var subnetGroupName: Swift.String?
    /// A list of VPC subnet IDs for the subnet group.
    /// This member is required.
    public var subnetIds: [Swift.String]?

    public init(
        description: Swift.String? = nil,
        subnetGroupName: Swift.String? = nil,
        subnetIds: [Swift.String]? = nil
    )
    {
        self.description = description
        self.subnetGroupName = subnetGroupName
        self.subnetIds = subnetIds
    }
}

extension DAXClientTypes {

    /// Represents the subnet associated with a DAX cluster. This parameter refers to subnets defined in Amazon Virtual Private Cloud (Amazon VPC) and used with DAX.
    public struct Subnet: Swift.Sendable {
        /// The Availability Zone (AZ) for the subnet.
        public var subnetAvailabilityZone: Swift.String?
        /// The system-assigned identifier for the subnet.
        public var subnetIdentifier: Swift.String?

        public init(
            subnetAvailabilityZone: Swift.String? = nil,
            subnetIdentifier: Swift.String? = nil
        )
        {
            self.subnetAvailabilityZone = subnetAvailabilityZone
            self.subnetIdentifier = subnetIdentifier
        }
    }
}

extension DAXClientTypes {

    /// Represents the output of one of the following actions:
    ///
    /// * CreateSubnetGroup
    ///
    /// * ModifySubnetGroup
    public struct SubnetGroup: Swift.Sendable {
        /// The description of the subnet group.
        public var description: Swift.String?
        /// The name of the subnet group.
        public var subnetGroupName: Swift.String?
        /// A list of subnets associated with the subnet group.
        public var subnets: [DAXClientTypes.Subnet]?
        /// The Amazon Virtual Private Cloud identifier (VPC ID) of the subnet group.
        public var vpcId: Swift.String?

        public init(
            description: Swift.String? = nil,
            subnetGroupName: Swift.String? = nil,
            subnets: [DAXClientTypes.Subnet]? = nil,
            vpcId: Swift.String? = nil
        )
        {
            self.description = description
            self.subnetGroupName = subnetGroupName
            self.subnets = subnets
            self.vpcId = vpcId
        }
    }
}

public struct CreateSubnetGroupOutput: Swift.Sendable {
    /// Represents the output of a CreateSubnetGroup operation.
    public var subnetGroup: DAXClientTypes.SubnetGroup?

    public init(
        subnetGroup: DAXClientTypes.SubnetGroup? = nil
    )
    {
        self.subnetGroup = subnetGroup
    }
}

/// The requested cluster ID does not refer to an existing DAX cluster.
public struct ClusterNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ClusterNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// None of the nodes in the cluster have the given node ID.
public struct NodeNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "NodeNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DecreaseReplicationFactorInput: Swift.Sendable {
    /// The Availability Zone(s) from which to remove nodes.
    public var availabilityZones: [Swift.String]?
    /// The name of the DAX cluster from which you want to remove nodes.
    /// This member is required.
    public var clusterName: Swift.String?
    /// The new number of nodes for the DAX cluster.
    /// This member is required.
    public var newReplicationFactor: Swift.Int?
    /// The unique identifiers of the nodes to be removed from the cluster.
    public var nodeIdsToRemove: [Swift.String]?

    public init(
        availabilityZones: [Swift.String]? = nil,
        clusterName: Swift.String? = nil,
        newReplicationFactor: Swift.Int? = 0,
        nodeIdsToRemove: [Swift.String]? = nil
    )
    {
        self.availabilityZones = availabilityZones
        self.clusterName = clusterName
        self.newReplicationFactor = newReplicationFactor
        self.nodeIdsToRemove = nodeIdsToRemove
    }
}

public struct DecreaseReplicationFactorOutput: Swift.Sendable {
    /// A description of the DAX cluster, after you have decreased its replication factor.
    public var cluster: DAXClientTypes.Cluster?

    public init(
        cluster: DAXClientTypes.Cluster? = nil
    )
    {
        self.cluster = cluster
    }
}

public struct DeleteClusterInput: Swift.Sendable {
    /// The name of the cluster to be deleted.
    /// This member is required.
    public var clusterName: Swift.String?

    public init(
        clusterName: Swift.String? = nil
    )
    {
        self.clusterName = clusterName
    }
}

public struct DeleteClusterOutput: Swift.Sendable {
    /// A description of the DAX cluster that is being deleted.
    public var cluster: DAXClientTypes.Cluster?

    public init(
        cluster: DAXClientTypes.Cluster? = nil
    )
    {
        self.cluster = cluster
    }
}

public struct DeleteParameterGroupInput: Swift.Sendable {
    /// The name of the parameter group to delete.
    /// This member is required.
    public var parameterGroupName: Swift.String?

    public init(
        parameterGroupName: Swift.String? = nil
    )
    {
        self.parameterGroupName = parameterGroupName
    }
}

public struct DeleteParameterGroupOutput: Swift.Sendable {
    /// A user-specified message for this action (i.e., a reason for deleting the parameter group).
    public var deletionMessage: Swift.String?

    public init(
        deletionMessage: Swift.String? = nil
    )
    {
        self.deletionMessage = deletionMessage
    }
}

/// The specified subnet group is currently in use.
public struct SubnetGroupInUseFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SubnetGroupInUse" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DeleteSubnetGroupInput: Swift.Sendable {
    /// The name of the subnet group to delete.
    /// This member is required.
    public var subnetGroupName: Swift.String?

    public init(
        subnetGroupName: Swift.String? = nil
    )
    {
        self.subnetGroupName = subnetGroupName
    }
}

public struct DeleteSubnetGroupOutput: Swift.Sendable {
    /// A user-specified message for this action (i.e., a reason for deleting the subnet group).
    public var deletionMessage: Swift.String?

    public init(
        deletionMessage: Swift.String? = nil
    )
    {
        self.deletionMessage = deletionMessage
    }
}

public struct DescribeClustersInput: Swift.Sendable {
    /// The names of the DAX clusters being described.
    public var clusterNames: [Swift.String]?
    /// The maximum number of results to include in the response. If more results exist than the specified MaxResults value, a token is included in the response so that the remaining results can be retrieved. The value for MaxResults must be between 20 and 100.
    public var maxResults: Swift.Int?
    /// An optional token returned from a prior request. Use this token for pagination of results from this action. If this parameter is specified, the response includes only results beyond the token, up to the value specified by MaxResults.
    public var nextToken: Swift.String?

    public init(
        clusterNames: [Swift.String]? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.clusterNames = clusterNames
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct DescribeClustersOutput: Swift.Sendable {
    /// The descriptions of your DAX clusters, in response to a DescribeClusters request.
    public var clusters: [DAXClientTypes.Cluster]?
    /// Provides an identifier to allow retrieval of paginated results.
    public var nextToken: Swift.String?

    public init(
        clusters: [DAXClientTypes.Cluster]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.clusters = clusters
        self.nextToken = nextToken
    }
}

public struct DescribeDefaultParametersInput: Swift.Sendable {
    /// The maximum number of results to include in the response. If more results exist than the specified MaxResults value, a token is included in the response so that the remaining results can be retrieved. The value for MaxResults must be between 20 and 100.
    public var maxResults: Swift.Int?
    /// An optional token returned from a prior request. Use this token for pagination of results from this action. If this parameter is specified, the response includes only results beyond the token, up to the value specified by MaxResults.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DAXClientTypes {

    public enum ChangeType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case immediate
        case requiresReboot
        case sdkUnknown(Swift.String)

        public static var allCases: [ChangeType] {
            return [
                .immediate,
                .requiresReboot
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .immediate: return "IMMEDIATE"
            case .requiresReboot: return "REQUIRES_REBOOT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DAXClientTypes {

    public enum IsModifiable: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case conditional
        case `false`
        case `true`
        case sdkUnknown(Swift.String)

        public static var allCases: [IsModifiable] {
            return [
                .conditional,
                .false,
                .true
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .conditional: return "CONDITIONAL"
            case .false: return "FALSE"
            case .true: return "TRUE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DAXClientTypes {

    /// Represents a parameter value that is applicable to a particular node type.
    public struct NodeTypeSpecificValue: Swift.Sendable {
        /// A node type to which the parameter value applies.
        public var nodeType: Swift.String?
        /// The parameter value for this node type.
        public var value: Swift.String?

        public init(
            nodeType: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.nodeType = nodeType
            self.value = value
        }
    }
}

extension DAXClientTypes {

    public enum ParameterType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case `default`
        case nodeTypeSpecific
        case sdkUnknown(Swift.String)

        public static var allCases: [ParameterType] {
            return [
                .default,
                .nodeTypeSpecific
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .default: return "DEFAULT"
            case .nodeTypeSpecific: return "NODE_TYPE_SPECIFIC"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DAXClientTypes {

    /// Describes an individual setting that controls some aspect of DAX behavior.
    public struct Parameter: Swift.Sendable {
        /// A range of values within which the parameter can be set.
        public var allowedValues: Swift.String?
        /// The conditions under which changes to this parameter can be applied. For example, requires-reboot indicates that a new value for this parameter will only take effect if a node is rebooted.
        public var changeType: DAXClientTypes.ChangeType?
        /// The data type of the parameter. For example, integer:
        public var dataType: Swift.String?
        /// A description of the parameter
        public var description: Swift.String?
        /// Whether the customer is allowed to modify the parameter.
        public var isModifiable: DAXClientTypes.IsModifiable?
        /// A list of node types, and specific parameter values for each node.
        public var nodeTypeSpecificValues: [DAXClientTypes.NodeTypeSpecificValue]?
        /// The name of the parameter.
        public var parameterName: Swift.String?
        /// Determines whether the parameter can be applied to any nodes, or only nodes of a particular type.
        public var parameterType: DAXClientTypes.ParameterType?
        /// The value for the parameter.
        public var parameterValue: Swift.String?
        /// How the parameter is defined. For example, system denotes a system-defined parameter.
        public var source: Swift.String?

        public init(
            allowedValues: Swift.String? = nil,
            changeType: DAXClientTypes.ChangeType? = nil,
            dataType: Swift.String? = nil,
            description: Swift.String? = nil,
            isModifiable: DAXClientTypes.IsModifiable? = nil,
            nodeTypeSpecificValues: [DAXClientTypes.NodeTypeSpecificValue]? = nil,
            parameterName: Swift.String? = nil,
            parameterType: DAXClientTypes.ParameterType? = nil,
            parameterValue: Swift.String? = nil,
            source: Swift.String? = nil
        )
        {
            self.allowedValues = allowedValues
            self.changeType = changeType
            self.dataType = dataType
            self.description = description
            self.isModifiable = isModifiable
            self.nodeTypeSpecificValues = nodeTypeSpecificValues
            self.parameterName = parameterName
            self.parameterType = parameterType
            self.parameterValue = parameterValue
            self.source = source
        }
    }
}

public struct DescribeDefaultParametersOutput: Swift.Sendable {
    /// Provides an identifier to allow retrieval of paginated results.
    public var nextToken: Swift.String?
    /// A list of parameters. Each element in the list represents one parameter.
    public var parameters: [DAXClientTypes.Parameter]?

    public init(
        nextToken: Swift.String? = nil,
        parameters: [DAXClientTypes.Parameter]? = nil
    )
    {
        self.nextToken = nextToken
        self.parameters = parameters
    }
}

extension DAXClientTypes {

    public enum SourceType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cluster
        case parameterGroup
        case subnetGroup
        case sdkUnknown(Swift.String)

        public static var allCases: [SourceType] {
            return [
                .cluster,
                .parameterGroup,
                .subnetGroup
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cluster: return "CLUSTER"
            case .parameterGroup: return "PARAMETER_GROUP"
            case .subnetGroup: return "SUBNET_GROUP"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeEventsInput: Swift.Sendable {
    /// The number of minutes' worth of events to retrieve.
    public var duration: Swift.Int?
    /// The end of the time interval for which to retrieve events, specified in ISO 8601 format.
    public var endTime: Foundation.Date?
    /// The maximum number of results to include in the response. If more results exist than the specified MaxResults value, a token is included in the response so that the remaining results can be retrieved. The value for MaxResults must be between 20 and 100.
    public var maxResults: Swift.Int?
    /// An optional token returned from a prior request. Use this token for pagination of results from this action. If this parameter is specified, the response includes only results beyond the token, up to the value specified by MaxResults.
    public var nextToken: Swift.String?
    /// The identifier of the event source for which events will be returned. If not specified, then all sources are included in the response.
    public var sourceName: Swift.String?
    /// The event source to retrieve events for. If no value is specified, all events are returned.
    public var sourceType: DAXClientTypes.SourceType?
    /// The beginning of the time interval to retrieve events for, specified in ISO 8601 format.
    public var startTime: Foundation.Date?

    public init(
        duration: Swift.Int? = nil,
        endTime: Foundation.Date? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        sourceName: Swift.String? = nil,
        sourceType: DAXClientTypes.SourceType? = nil,
        startTime: Foundation.Date? = nil
    )
    {
        self.duration = duration
        self.endTime = endTime
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.sourceName = sourceName
        self.sourceType = sourceType
        self.startTime = startTime
    }
}

extension DAXClientTypes {

    /// Represents a single occurrence of something interesting within the system. Some examples of events are creating a DAX cluster, adding or removing a node, or rebooting a node.
    public struct Event: Swift.Sendable {
        /// The date and time when the event occurred.
        public var date: Foundation.Date?
        /// A user-defined message associated with the event.
        public var message: Swift.String?
        /// The source of the event. For example, if the event occurred at the node level, the source would be the node ID.
        public var sourceName: Swift.String?
        /// Specifies the origin of this event - a cluster, a parameter group, a node ID, etc.
        public var sourceType: DAXClientTypes.SourceType?

        public init(
            date: Foundation.Date? = nil,
            message: Swift.String? = nil,
            sourceName: Swift.String? = nil,
            sourceType: DAXClientTypes.SourceType? = nil
        )
        {
            self.date = date
            self.message = message
            self.sourceName = sourceName
            self.sourceType = sourceType
        }
    }
}

public struct DescribeEventsOutput: Swift.Sendable {
    /// An array of events. Each element in the array represents one event.
    public var events: [DAXClientTypes.Event]?
    /// Provides an identifier to allow retrieval of paginated results.
    public var nextToken: Swift.String?

    public init(
        events: [DAXClientTypes.Event]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.events = events
        self.nextToken = nextToken
    }
}

public struct DescribeParameterGroupsInput: Swift.Sendable {
    /// The maximum number of results to include in the response. If more results exist than the specified MaxResults value, a token is included in the response so that the remaining results can be retrieved. The value for MaxResults must be between 20 and 100.
    public var maxResults: Swift.Int?
    /// An optional token returned from a prior request. Use this token for pagination of results from this action. If this parameter is specified, the response includes only results beyond the token, up to the value specified by MaxResults.
    public var nextToken: Swift.String?
    /// The names of the parameter groups.
    public var parameterGroupNames: [Swift.String]?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        parameterGroupNames: [Swift.String]? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.parameterGroupNames = parameterGroupNames
    }
}

public struct DescribeParameterGroupsOutput: Swift.Sendable {
    /// Provides an identifier to allow retrieval of paginated results.
    public var nextToken: Swift.String?
    /// An array of parameter groups. Each element in the array represents one parameter group.
    public var parameterGroups: [DAXClientTypes.ParameterGroup]?

    public init(
        nextToken: Swift.String? = nil,
        parameterGroups: [DAXClientTypes.ParameterGroup]? = nil
    )
    {
        self.nextToken = nextToken
        self.parameterGroups = parameterGroups
    }
}

public struct DescribeParametersInput: Swift.Sendable {
    /// The maximum number of results to include in the response. If more results exist than the specified MaxResults value, a token is included in the response so that the remaining results can be retrieved. The value for MaxResults must be between 20 and 100.
    public var maxResults: Swift.Int?
    /// An optional token returned from a prior request. Use this token for pagination of results from this action. If this parameter is specified, the response includes only results beyond the token, up to the value specified by MaxResults.
    public var nextToken: Swift.String?
    /// The name of the parameter group.
    /// This member is required.
    public var parameterGroupName: Swift.String?
    /// How the parameter is defined. For example, system denotes a system-defined parameter.
    public var source: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        parameterGroupName: Swift.String? = nil,
        source: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.parameterGroupName = parameterGroupName
        self.source = source
    }
}

public struct DescribeParametersOutput: Swift.Sendable {
    /// Provides an identifier to allow retrieval of paginated results.
    public var nextToken: Swift.String?
    /// A list of parameters within a parameter group. Each element in the list represents one parameter.
    public var parameters: [DAXClientTypes.Parameter]?

    public init(
        nextToken: Swift.String? = nil,
        parameters: [DAXClientTypes.Parameter]? = nil
    )
    {
        self.nextToken = nextToken
        self.parameters = parameters
    }
}

public struct DescribeSubnetGroupsInput: Swift.Sendable {
    /// The maximum number of results to include in the response. If more results exist than the specified MaxResults value, a token is included in the response so that the remaining results can be retrieved. The value for MaxResults must be between 20 and 100.
    public var maxResults: Swift.Int?
    /// An optional token returned from a prior request. Use this token for pagination of results from this action. If this parameter is specified, the response includes only results beyond the token, up to the value specified by MaxResults.
    public var nextToken: Swift.String?
    /// The name of the subnet group.
    public var subnetGroupNames: [Swift.String]?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        subnetGroupNames: [Swift.String]? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.subnetGroupNames = subnetGroupNames
    }
}

public struct DescribeSubnetGroupsOutput: Swift.Sendable {
    /// Provides an identifier to allow retrieval of paginated results.
    public var nextToken: Swift.String?
    /// An array of subnet groups. Each element in the array represents a single subnet group.
    public var subnetGroups: [DAXClientTypes.SubnetGroup]?

    public init(
        nextToken: Swift.String? = nil,
        subnetGroups: [DAXClientTypes.SubnetGroup]? = nil
    )
    {
        self.nextToken = nextToken
        self.subnetGroups = subnetGroups
    }
}

public struct IncreaseReplicationFactorInput: Swift.Sendable {
    /// The Availability Zones (AZs) in which the cluster nodes will be created. All nodes belonging to the cluster are placed in these Availability Zones. Use this parameter if you want to distribute the nodes across multiple AZs.
    public var availabilityZones: [Swift.String]?
    /// The name of the DAX cluster that will receive additional nodes.
    /// This member is required.
    public var clusterName: Swift.String?
    /// The new number of nodes for the DAX cluster.
    /// This member is required.
    public var newReplicationFactor: Swift.Int?

    public init(
        availabilityZones: [Swift.String]? = nil,
        clusterName: Swift.String? = nil,
        newReplicationFactor: Swift.Int? = 0
    )
    {
        self.availabilityZones = availabilityZones
        self.clusterName = clusterName
        self.newReplicationFactor = newReplicationFactor
    }
}

public struct IncreaseReplicationFactorOutput: Swift.Sendable {
    /// A description of the DAX cluster. with its new replication factor.
    public var cluster: DAXClientTypes.Cluster?

    public init(
        cluster: DAXClientTypes.Cluster? = nil
    )
    {
        self.cluster = cluster
    }
}

/// The Amazon Resource Name (ARN) supplied in the request is not valid.
public struct InvalidARNFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidARN" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct ListTagsInput: Swift.Sendable {
    /// An optional token returned from a prior request. Use this token for pagination of results from this action. If this parameter is specified, the response includes only results beyond the token.
    public var nextToken: Swift.String?
    /// The name of the DAX resource to which the tags belong.
    /// This member is required.
    public var resourceName: Swift.String?

    public init(
        nextToken: Swift.String? = nil,
        resourceName: Swift.String? = nil
    )
    {
        self.nextToken = nextToken
        self.resourceName = resourceName
    }
}

public struct ListTagsOutput: Swift.Sendable {
    /// If this value is present, there are additional results to be displayed. To retrieve them, call ListTags again, with NextToken set to this value.
    public var nextToken: Swift.String?
    /// A list of tags currently associated with the DAX cluster.
    public var tags: [DAXClientTypes.Tag]?

    public init(
        nextToken: Swift.String? = nil,
        tags: [DAXClientTypes.Tag]? = nil
    )
    {
        self.nextToken = nextToken
        self.tags = tags
    }
}

public struct RebootNodeInput: Swift.Sendable {
    /// The name of the DAX cluster containing the node to be rebooted.
    /// This member is required.
    public var clusterName: Swift.String?
    /// The system-assigned ID of the node to be rebooted.
    /// This member is required.
    public var nodeId: Swift.String?

    public init(
        clusterName: Swift.String? = nil,
        nodeId: Swift.String? = nil
    )
    {
        self.clusterName = clusterName
        self.nodeId = nodeId
    }
}

public struct RebootNodeOutput: Swift.Sendable {
    /// A description of the DAX cluster after a node has been rebooted.
    public var cluster: DAXClientTypes.Cluster?

    public init(
        cluster: DAXClientTypes.Cluster? = nil
    )
    {
        self.cluster = cluster
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The name of the DAX resource to which tags should be added.
    /// This member is required.
    public var resourceName: Swift.String?
    /// The tags to be assigned to the DAX resource.
    /// This member is required.
    public var tags: [DAXClientTypes.Tag]?

    public init(
        resourceName: Swift.String? = nil,
        tags: [DAXClientTypes.Tag]? = nil
    )
    {
        self.resourceName = resourceName
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {
    /// The list of tags that are associated with the DAX resource.
    public var tags: [DAXClientTypes.Tag]?

    public init(
        tags: [DAXClientTypes.Tag]? = nil
    )
    {
        self.tags = tags
    }
}

/// The tag does not exist.
public struct TagNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TagNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The name of the DAX resource from which the tags should be removed.
    /// This member is required.
    public var resourceName: Swift.String?
    /// A list of tag keys. If the DAX cluster has any tags with these keys, then the tags are removed from the cluster.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceName: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceName = resourceName
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {
    /// The tag keys that have been removed from the cluster.
    public var tags: [DAXClientTypes.Tag]?

    public init(
        tags: [DAXClientTypes.Tag]? = nil
    )
    {
        self.tags = tags
    }
}

public struct UpdateClusterInput: Swift.Sendable {
    /// The name of the DAX cluster to be modified.
    /// This member is required.
    public var clusterName: Swift.String?
    /// A description of the changes being made to the cluster.
    public var description: Swift.String?
    /// The Amazon Resource Name (ARN) that identifies the topic.
    public var notificationTopicArn: Swift.String?
    /// The current state of the topic. A value of “active” means that notifications will be sent to the topic. A value of “inactive” means that notifications will not be sent to the topic.
    public var notificationTopicStatus: Swift.String?
    /// The name of a parameter group for this cluster.
    public var parameterGroupName: Swift.String?
    /// A range of time when maintenance of DAX cluster software will be performed. For example: sun:01:00-sun:09:00. Cluster maintenance normally takes less than 30 minutes, and is performed automatically within the maintenance window.
    public var preferredMaintenanceWindow: Swift.String?
    /// A list of user-specified security group IDs to be assigned to each node in the DAX cluster. If this parameter is not specified, DAX assigns the default VPC security group to each node.
    public var securityGroupIds: [Swift.String]?

    public init(
        clusterName: Swift.String? = nil,
        description: Swift.String? = nil,
        notificationTopicArn: Swift.String? = nil,
        notificationTopicStatus: Swift.String? = nil,
        parameterGroupName: Swift.String? = nil,
        preferredMaintenanceWindow: Swift.String? = nil,
        securityGroupIds: [Swift.String]? = nil
    )
    {
        self.clusterName = clusterName
        self.description = description
        self.notificationTopicArn = notificationTopicArn
        self.notificationTopicStatus = notificationTopicStatus
        self.parameterGroupName = parameterGroupName
        self.preferredMaintenanceWindow = preferredMaintenanceWindow
        self.securityGroupIds = securityGroupIds
    }
}

public struct UpdateClusterOutput: Swift.Sendable {
    /// A description of the DAX cluster, after it has been modified.
    public var cluster: DAXClientTypes.Cluster?

    public init(
        cluster: DAXClientTypes.Cluster? = nil
    )
    {
        self.cluster = cluster
    }
}

extension DAXClientTypes {

    /// An individual DAX parameter.
    public struct ParameterNameValue: Swift.Sendable {
        /// The name of the parameter.
        public var parameterName: Swift.String?
        /// The value of the parameter.
        public var parameterValue: Swift.String?

        public init(
            parameterName: Swift.String? = nil,
            parameterValue: Swift.String? = nil
        )
        {
            self.parameterName = parameterName
            self.parameterValue = parameterValue
        }
    }
}

public struct UpdateParameterGroupInput: Swift.Sendable {
    /// The name of the parameter group.
    /// This member is required.
    public var parameterGroupName: Swift.String?
    /// An array of name-value pairs for the parameters in the group. Each element in the array represents a single parameter. record-ttl-millis and query-ttl-millis are the only supported parameter names. For more details, see [Configuring TTL Settings](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/DAX.cluster-management.html#DAX.cluster-management.custom-settings.ttl).
    /// This member is required.
    public var parameterNameValues: [DAXClientTypes.ParameterNameValue]?

    public init(
        parameterGroupName: Swift.String? = nil,
        parameterNameValues: [DAXClientTypes.ParameterNameValue]? = nil
    )
    {
        self.parameterGroupName = parameterGroupName
        self.parameterNameValues = parameterNameValues
    }
}

public struct UpdateParameterGroupOutput: Swift.Sendable {
    /// The parameter group that has been modified.
    public var parameterGroup: DAXClientTypes.ParameterGroup?

    public init(
        parameterGroup: DAXClientTypes.ParameterGroup? = nil
    )
    {
        self.parameterGroup = parameterGroup
    }
}

/// The requested subnet is being used by another subnet group.
public struct SubnetInUse: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SubnetInUse" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct UpdateSubnetGroupInput: Swift.Sendable {
    /// A description of the subnet group.
    public var description: Swift.String?
    /// The name of the subnet group.
    /// This member is required.
    public var subnetGroupName: Swift.String?
    /// A list of subnet IDs in the subnet group.
    public var subnetIds: [Swift.String]?

    public init(
        description: Swift.String? = nil,
        subnetGroupName: Swift.String? = nil,
        subnetIds: [Swift.String]? = nil
    )
    {
        self.description = description
        self.subnetGroupName = subnetGroupName
        self.subnetIds = subnetIds
    }
}

public struct UpdateSubnetGroupOutput: Swift.Sendable {
    /// The subnet group that has been modified.
    public var subnetGroup: DAXClientTypes.SubnetGroup?

    public init(
        subnetGroup: DAXClientTypes.SubnetGroup? = nil
    )
    {
        self.subnetGroup = subnetGroup
    }
}

extension CreateClusterInput {

    static func urlPathProvider(_ value: CreateClusterInput) -> Swift.String? {
        return "/"
    }
}

extension CreateParameterGroupInput {

    static func urlPathProvider(_ value: CreateParameterGroupInput) -> Swift.String? {
        return "/"
    }
}

extension CreateSubnetGroupInput {

    static func urlPathProvider(_ value: CreateSubnetGroupInput) -> Swift.String? {
        return "/"
    }
}

extension DecreaseReplicationFactorInput {

    static func urlPathProvider(_ value: DecreaseReplicationFactorInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteClusterInput {

    static func urlPathProvider(_ value: DeleteClusterInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteParameterGroupInput {

    static func urlPathProvider(_ value: DeleteParameterGroupInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteSubnetGroupInput {

    static func urlPathProvider(_ value: DeleteSubnetGroupInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeClustersInput {

    static func urlPathProvider(_ value: DescribeClustersInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeDefaultParametersInput {

    static func urlPathProvider(_ value: DescribeDefaultParametersInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeEventsInput {

    static func urlPathProvider(_ value: DescribeEventsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeParameterGroupsInput {

    static func urlPathProvider(_ value: DescribeParameterGroupsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeParametersInput {

    static func urlPathProvider(_ value: DescribeParametersInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeSubnetGroupsInput {

    static func urlPathProvider(_ value: DescribeSubnetGroupsInput) -> Swift.String? {
        return "/"
    }
}

extension IncreaseReplicationFactorInput {

    static func urlPathProvider(_ value: IncreaseReplicationFactorInput) -> Swift.String? {
        return "/"
    }
}

extension ListTagsInput {

    static func urlPathProvider(_ value: ListTagsInput) -> Swift.String? {
        return "/"
    }
}

extension RebootNodeInput {

    static func urlPathProvider(_ value: RebootNodeInput) -> Swift.String? {
        return "/"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        return "/"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateClusterInput {

    static func urlPathProvider(_ value: UpdateClusterInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateParameterGroupInput {

    static func urlPathProvider(_ value: UpdateParameterGroupInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateSubnetGroupInput {

    static func urlPathProvider(_ value: UpdateSubnetGroupInput) -> Swift.String? {
        return "/"
    }
}

extension CreateClusterInput {

    static func write(value: CreateClusterInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AvailabilityZones"].writeList(value.availabilityZones, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ClusterEndpointEncryptionType"].write(value.clusterEndpointEncryptionType)
        try writer["ClusterName"].write(value.clusterName)
        try writer["Description"].write(value.description)
        try writer["IamRoleArn"].write(value.iamRoleArn)
        try writer["NodeType"].write(value.nodeType)
        try writer["NotificationTopicArn"].write(value.notificationTopicArn)
        try writer["ParameterGroupName"].write(value.parameterGroupName)
        try writer["PreferredMaintenanceWindow"].write(value.preferredMaintenanceWindow)
        try writer["ReplicationFactor"].write(value.replicationFactor)
        try writer["SSESpecification"].write(value.sseSpecification, with: DAXClientTypes.SSESpecification.write(value:to:))
        try writer["SecurityGroupIds"].writeList(value.securityGroupIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["SubnetGroupName"].write(value.subnetGroupName)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DAXClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateParameterGroupInput {

    static func write(value: CreateParameterGroupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Description"].write(value.description)
        try writer["ParameterGroupName"].write(value.parameterGroupName)
    }
}

extension CreateSubnetGroupInput {

    static func write(value: CreateSubnetGroupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Description"].write(value.description)
        try writer["SubnetGroupName"].write(value.subnetGroupName)
        try writer["SubnetIds"].writeList(value.subnetIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DecreaseReplicationFactorInput {

    static func write(value: DecreaseReplicationFactorInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AvailabilityZones"].writeList(value.availabilityZones, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ClusterName"].write(value.clusterName)
        try writer["NewReplicationFactor"].write(value.newReplicationFactor)
        try writer["NodeIdsToRemove"].writeList(value.nodeIdsToRemove, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DeleteClusterInput {

    static func write(value: DeleteClusterInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClusterName"].write(value.clusterName)
    }
}

extension DeleteParameterGroupInput {

    static func write(value: DeleteParameterGroupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ParameterGroupName"].write(value.parameterGroupName)
    }
}

extension DeleteSubnetGroupInput {

    static func write(value: DeleteSubnetGroupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["SubnetGroupName"].write(value.subnetGroupName)
    }
}

extension DescribeClustersInput {

    static func write(value: DescribeClustersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClusterNames"].writeList(value.clusterNames, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension DescribeDefaultParametersInput {

    static func write(value: DescribeDefaultParametersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension DescribeEventsInput {

    static func write(value: DescribeEventsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Duration"].write(value.duration)
        try writer["EndTime"].writeTimestamp(value.endTime, format: SmithyTimestamps.TimestampFormat.epochSeconds)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["SourceName"].write(value.sourceName)
        try writer["SourceType"].write(value.sourceType)
        try writer["StartTime"].writeTimestamp(value.startTime, format: SmithyTimestamps.TimestampFormat.epochSeconds)
    }
}

extension DescribeParameterGroupsInput {

    static func write(value: DescribeParameterGroupsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["ParameterGroupNames"].writeList(value.parameterGroupNames, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DescribeParametersInput {

    static func write(value: DescribeParametersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["ParameterGroupName"].write(value.parameterGroupName)
        try writer["Source"].write(value.source)
    }
}

extension DescribeSubnetGroupsInput {

    static func write(value: DescribeSubnetGroupsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["SubnetGroupNames"].writeList(value.subnetGroupNames, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension IncreaseReplicationFactorInput {

    static func write(value: IncreaseReplicationFactorInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AvailabilityZones"].writeList(value.availabilityZones, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ClusterName"].write(value.clusterName)
        try writer["NewReplicationFactor"].write(value.newReplicationFactor)
    }
}

extension ListTagsInput {

    static func write(value: ListTagsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["NextToken"].write(value.nextToken)
        try writer["ResourceName"].write(value.resourceName)
    }
}

extension RebootNodeInput {

    static func write(value: RebootNodeInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClusterName"].write(value.clusterName)
        try writer["NodeId"].write(value.nodeId)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceName"].write(value.resourceName)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DAXClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UntagResourceInput {

    static func write(value: UntagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceName"].write(value.resourceName)
        try writer["TagKeys"].writeList(value.tagKeys, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdateClusterInput {

    static func write(value: UpdateClusterInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClusterName"].write(value.clusterName)
        try writer["Description"].write(value.description)
        try writer["NotificationTopicArn"].write(value.notificationTopicArn)
        try writer["NotificationTopicStatus"].write(value.notificationTopicStatus)
        try writer["ParameterGroupName"].write(value.parameterGroupName)
        try writer["PreferredMaintenanceWindow"].write(value.preferredMaintenanceWindow)
        try writer["SecurityGroupIds"].writeList(value.securityGroupIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdateParameterGroupInput {

    static func write(value: UpdateParameterGroupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ParameterGroupName"].write(value.parameterGroupName)
        try writer["ParameterNameValues"].writeList(value.parameterNameValues, memberWritingClosure: DAXClientTypes.ParameterNameValue.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdateSubnetGroupInput {

    static func write(value: UpdateSubnetGroupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Description"].write(value.description)
        try writer["SubnetGroupName"].write(value.subnetGroupName)
        try writer["SubnetIds"].writeList(value.subnetIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateClusterOutput()
        value.cluster = try reader["Cluster"].readIfPresent(with: DAXClientTypes.Cluster.read(from:))
        return value
    }
}

extension CreateParameterGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateParameterGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateParameterGroupOutput()
        value.parameterGroup = try reader["ParameterGroup"].readIfPresent(with: DAXClientTypes.ParameterGroup.read(from:))
        return value
    }
}

extension CreateSubnetGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateSubnetGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateSubnetGroupOutput()
        value.subnetGroup = try reader["SubnetGroup"].readIfPresent(with: DAXClientTypes.SubnetGroup.read(from:))
        return value
    }
}

extension DecreaseReplicationFactorOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DecreaseReplicationFactorOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DecreaseReplicationFactorOutput()
        value.cluster = try reader["Cluster"].readIfPresent(with: DAXClientTypes.Cluster.read(from:))
        return value
    }
}

extension DeleteClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteClusterOutput()
        value.cluster = try reader["Cluster"].readIfPresent(with: DAXClientTypes.Cluster.read(from:))
        return value
    }
}

extension DeleteParameterGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteParameterGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteParameterGroupOutput()
        value.deletionMessage = try reader["DeletionMessage"].readIfPresent()
        return value
    }
}

extension DeleteSubnetGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteSubnetGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteSubnetGroupOutput()
        value.deletionMessage = try reader["DeletionMessage"].readIfPresent()
        return value
    }
}

extension DescribeClustersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeClustersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeClustersOutput()
        value.clusters = try reader["Clusters"].readListIfPresent(memberReadingClosure: DAXClientTypes.Cluster.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension DescribeDefaultParametersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDefaultParametersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeDefaultParametersOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.parameters = try reader["Parameters"].readListIfPresent(memberReadingClosure: DAXClientTypes.Parameter.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeEventsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeEventsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeEventsOutput()
        value.events = try reader["Events"].readListIfPresent(memberReadingClosure: DAXClientTypes.Event.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension DescribeParameterGroupsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeParameterGroupsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeParameterGroupsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.parameterGroups = try reader["ParameterGroups"].readListIfPresent(memberReadingClosure: DAXClientTypes.ParameterGroup.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeParametersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeParametersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeParametersOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.parameters = try reader["Parameters"].readListIfPresent(memberReadingClosure: DAXClientTypes.Parameter.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeSubnetGroupsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeSubnetGroupsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeSubnetGroupsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.subnetGroups = try reader["SubnetGroups"].readListIfPresent(memberReadingClosure: DAXClientTypes.SubnetGroup.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension IncreaseReplicationFactorOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> IncreaseReplicationFactorOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = IncreaseReplicationFactorOutput()
        value.cluster = try reader["Cluster"].readIfPresent(with: DAXClientTypes.Cluster.read(from:))
        return value
    }
}

extension ListTagsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: DAXClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension RebootNodeOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RebootNodeOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = RebootNodeOutput()
        value.cluster = try reader["Cluster"].readIfPresent(with: DAXClientTypes.Cluster.read(from:))
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = TagResourceOutput()
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: DAXClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UntagResourceOutput()
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: DAXClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension UpdateClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateClusterOutput()
        value.cluster = try reader["Cluster"].readIfPresent(with: DAXClientTypes.Cluster.read(from:))
        return value
    }
}

extension UpdateParameterGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateParameterGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateParameterGroupOutput()
        value.parameterGroup = try reader["ParameterGroup"].readIfPresent(with: DAXClientTypes.ParameterGroup.read(from:))
        return value
    }
}

extension UpdateSubnetGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateSubnetGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateSubnetGroupOutput()
        value.subnetGroup = try reader["SubnetGroup"].readIfPresent(with: DAXClientTypes.SubnetGroup.read(from:))
        return value
    }
}

enum CreateClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClusterAlreadyExists": return try ClusterAlreadyExistsFault.makeError(baseError: baseError)
            case "ClusterQuotaForCustomerExceeded": return try ClusterQuotaForCustomerExceededFault.makeError(baseError: baseError)
            case "InsufficientClusterCapacity": return try InsufficientClusterCapacityFault.makeError(baseError: baseError)
            case "InvalidClusterState": return try InvalidClusterStateFault.makeError(baseError: baseError)
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterGroupState": return try InvalidParameterGroupStateFault.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "InvalidVPCNetworkStateFault": return try InvalidVPCNetworkStateFault.makeError(baseError: baseError)
            case "NodeQuotaForClusterExceeded": return try NodeQuotaForClusterExceededFault.makeError(baseError: baseError)
            case "NodeQuotaForCustomerExceeded": return try NodeQuotaForCustomerExceededFault.makeError(baseError: baseError)
            case "ParameterGroupNotFound": return try ParameterGroupNotFoundFault.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            case "ServiceQuotaExceeded": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "SubnetGroupNotFoundFault": return try SubnetGroupNotFoundFault.makeError(baseError: baseError)
            case "TagQuotaPerResourceExceeded": return try TagQuotaPerResourceExceeded.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateParameterGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterGroupState": return try InvalidParameterGroupStateFault.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "ParameterGroupAlreadyExists": return try ParameterGroupAlreadyExistsFault.makeError(baseError: baseError)
            case "ParameterGroupQuotaExceeded": return try ParameterGroupQuotaExceededFault.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateSubnetGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidSubnet": return try InvalidSubnet.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            case "SubnetGroupAlreadyExists": return try SubnetGroupAlreadyExistsFault.makeError(baseError: baseError)
            case "SubnetGroupQuotaExceeded": return try SubnetGroupQuotaExceededFault.makeError(baseError: baseError)
            case "SubnetQuotaExceededFault": return try SubnetQuotaExceededFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DecreaseReplicationFactorOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClusterNotFound": return try ClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidClusterState": return try InvalidClusterStateFault.makeError(baseError: baseError)
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "NodeNotFound": return try NodeNotFoundFault.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClusterNotFound": return try ClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidClusterState": return try InvalidClusterStateFault.makeError(baseError: baseError)
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteParameterGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterGroupState": return try InvalidParameterGroupStateFault.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "ParameterGroupNotFound": return try ParameterGroupNotFoundFault.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteSubnetGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            case "SubnetGroupInUse": return try SubnetGroupInUseFault.makeError(baseError: baseError)
            case "SubnetGroupNotFoundFault": return try SubnetGroupNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeClustersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClusterNotFound": return try ClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDefaultParametersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeEventsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeParameterGroupsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "ParameterGroupNotFound": return try ParameterGroupNotFoundFault.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeParametersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "ParameterGroupNotFound": return try ParameterGroupNotFoundFault.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeSubnetGroupsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            case "SubnetGroupNotFoundFault": return try SubnetGroupNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum IncreaseReplicationFactorOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClusterNotFound": return try ClusterNotFoundFault.makeError(baseError: baseError)
            case "InsufficientClusterCapacity": return try InsufficientClusterCapacityFault.makeError(baseError: baseError)
            case "InvalidClusterState": return try InvalidClusterStateFault.makeError(baseError: baseError)
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "InvalidVPCNetworkStateFault": return try InvalidVPCNetworkStateFault.makeError(baseError: baseError)
            case "NodeQuotaForClusterExceeded": return try NodeQuotaForClusterExceededFault.makeError(baseError: baseError)
            case "NodeQuotaForCustomerExceeded": return try NodeQuotaForCustomerExceededFault.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClusterNotFound": return try ClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidARN": return try InvalidARNFault.makeError(baseError: baseError)
            case "InvalidClusterState": return try InvalidClusterStateFault.makeError(baseError: baseError)
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RebootNodeOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClusterNotFound": return try ClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidClusterState": return try InvalidClusterStateFault.makeError(baseError: baseError)
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "NodeNotFound": return try NodeNotFoundFault.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClusterNotFound": return try ClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidARN": return try InvalidARNFault.makeError(baseError: baseError)
            case "InvalidClusterState": return try InvalidClusterStateFault.makeError(baseError: baseError)
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            case "TagQuotaPerResourceExceeded": return try TagQuotaPerResourceExceeded.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClusterNotFound": return try ClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidARN": return try InvalidARNFault.makeError(baseError: baseError)
            case "InvalidClusterState": return try InvalidClusterStateFault.makeError(baseError: baseError)
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            case "TagNotFound": return try TagNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClusterNotFound": return try ClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidClusterState": return try InvalidClusterStateFault.makeError(baseError: baseError)
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterGroupState": return try InvalidParameterGroupStateFault.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "ParameterGroupNotFound": return try ParameterGroupNotFoundFault.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateParameterGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterCombination": return try InvalidParameterCombinationException.makeError(baseError: baseError)
            case "InvalidParameterGroupState": return try InvalidParameterGroupStateFault.makeError(baseError: baseError)
            case "InvalidParameterValue": return try InvalidParameterValueException.makeError(baseError: baseError)
            case "ParameterGroupNotFound": return try ParameterGroupNotFoundFault.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateSubnetGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidSubnet": return try InvalidSubnet.makeError(baseError: baseError)
            case "ServiceLinkedRoleNotFoundFault": return try ServiceLinkedRoleNotFoundFault.makeError(baseError: baseError)
            case "SubnetGroupNotFoundFault": return try SubnetGroupNotFoundFault.makeError(baseError: baseError)
            case "SubnetInUse": return try SubnetInUse.makeError(baseError: baseError)
            case "SubnetQuotaExceededFault": return try SubnetQuotaExceededFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ParameterGroupNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ParameterGroupNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = ParameterGroupNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension NodeQuotaForCustomerExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> NodeQuotaForCustomerExceededFault {
        let reader = baseError.errorBodyReader
        var value = NodeQuotaForCustomerExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ClusterAlreadyExistsFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ClusterAlreadyExistsFault {
        let reader = baseError.errorBodyReader
        var value = ClusterAlreadyExistsFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InsufficientClusterCapacityFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InsufficientClusterCapacityFault {
        let reader = baseError.errorBodyReader
        var value = InsufficientClusterCapacityFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidVPCNetworkStateFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidVPCNetworkStateFault {
        let reader = baseError.errorBodyReader
        var value = InvalidVPCNetworkStateFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ServiceQuotaExceededException {
        var value = ServiceQuotaExceededException()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ClusterQuotaForCustomerExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ClusterQuotaForCustomerExceededFault {
        let reader = baseError.errorBodyReader
        var value = ClusterQuotaForCustomerExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidClusterStateFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidClusterStateFault {
        let reader = baseError.errorBodyReader
        var value = InvalidClusterStateFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension NodeQuotaForClusterExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> NodeQuotaForClusterExceededFault {
        let reader = baseError.errorBodyReader
        var value = NodeQuotaForClusterExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceLinkedRoleNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ServiceLinkedRoleNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = ServiceLinkedRoleNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidParameterGroupStateFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidParameterGroupStateFault {
        let reader = baseError.errorBodyReader
        var value = InvalidParameterGroupStateFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidParameterCombinationException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidParameterCombinationException {
        let reader = baseError.errorBodyReader
        var value = InvalidParameterCombinationException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidParameterValueException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidParameterValueException {
        let reader = baseError.errorBodyReader
        var value = InvalidParameterValueException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SubnetGroupNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> SubnetGroupNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = SubnetGroupNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TagQuotaPerResourceExceeded {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> TagQuotaPerResourceExceeded {
        let reader = baseError.errorBodyReader
        var value = TagQuotaPerResourceExceeded()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ParameterGroupQuotaExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ParameterGroupQuotaExceededFault {
        let reader = baseError.errorBodyReader
        var value = ParameterGroupQuotaExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ParameterGroupAlreadyExistsFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ParameterGroupAlreadyExistsFault {
        let reader = baseError.errorBodyReader
        var value = ParameterGroupAlreadyExistsFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SubnetGroupQuotaExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> SubnetGroupQuotaExceededFault {
        let reader = baseError.errorBodyReader
        var value = SubnetGroupQuotaExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SubnetGroupAlreadyExistsFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> SubnetGroupAlreadyExistsFault {
        let reader = baseError.errorBodyReader
        var value = SubnetGroupAlreadyExistsFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidSubnet {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidSubnet {
        let reader = baseError.errorBodyReader
        var value = InvalidSubnet()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SubnetQuotaExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> SubnetQuotaExceededFault {
        let reader = baseError.errorBodyReader
        var value = SubnetQuotaExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension NodeNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> NodeNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = NodeNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ClusterNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ClusterNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = ClusterNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SubnetGroupInUseFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> SubnetGroupInUseFault {
        let reader = baseError.errorBodyReader
        var value = SubnetGroupInUseFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidARNFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidARNFault {
        let reader = baseError.errorBodyReader
        var value = InvalidARNFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TagNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> TagNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = TagNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SubnetInUse {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> SubnetInUse {
        let reader = baseError.errorBodyReader
        var value = SubnetInUse()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DAXClientTypes.Cluster {

    static func read(from reader: SmithyJSON.Reader) throws -> DAXClientTypes.Cluster {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DAXClientTypes.Cluster()
        value.clusterName = try reader["ClusterName"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.clusterArn = try reader["ClusterArn"].readIfPresent()
        value.totalNodes = try reader["TotalNodes"].readIfPresent()
        value.activeNodes = try reader["ActiveNodes"].readIfPresent()
        value.nodeType = try reader["NodeType"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.clusterDiscoveryEndpoint = try reader["ClusterDiscoveryEndpoint"].readIfPresent(with: DAXClientTypes.Endpoint.read(from:))
        value.nodeIdsToRemove = try reader["NodeIdsToRemove"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.nodes = try reader["Nodes"].readListIfPresent(memberReadingClosure: DAXClientTypes.Node.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.preferredMaintenanceWindow = try reader["PreferredMaintenanceWindow"].readIfPresent()
        value.notificationConfiguration = try reader["NotificationConfiguration"].readIfPresent(with: DAXClientTypes.NotificationConfiguration.read(from:))
        value.subnetGroup = try reader["SubnetGroup"].readIfPresent()
        value.securityGroups = try reader["SecurityGroups"].readListIfPresent(memberReadingClosure: DAXClientTypes.SecurityGroupMembership.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.iamRoleArn = try reader["IamRoleArn"].readIfPresent()
        value.parameterGroup = try reader["ParameterGroup"].readIfPresent(with: DAXClientTypes.ParameterGroupStatus.read(from:))
        value.sseDescription = try reader["SSEDescription"].readIfPresent(with: DAXClientTypes.SSEDescription.read(from:))
        value.clusterEndpointEncryptionType = try reader["ClusterEndpointEncryptionType"].readIfPresent()
        return value
    }
}

extension DAXClientTypes.SSEDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> DAXClientTypes.SSEDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DAXClientTypes.SSEDescription()
        value.status = try reader["Status"].readIfPresent()
        return value
    }
}

extension DAXClientTypes.ParameterGroupStatus {

    static func read(from reader: SmithyJSON.Reader) throws -> DAXClientTypes.ParameterGroupStatus {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DAXClientTypes.ParameterGroupStatus()
        value.parameterGroupName = try reader["ParameterGroupName"].readIfPresent()
        value.parameterApplyStatus = try reader["ParameterApplyStatus"].readIfPresent()
        value.nodeIdsToReboot = try reader["NodeIdsToReboot"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DAXClientTypes.SecurityGroupMembership {

    static func read(from reader: SmithyJSON.Reader) throws -> DAXClientTypes.SecurityGroupMembership {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DAXClientTypes.SecurityGroupMembership()
        value.securityGroupIdentifier = try reader["SecurityGroupIdentifier"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        return value
    }
}

extension DAXClientTypes.NotificationConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> DAXClientTypes.NotificationConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DAXClientTypes.NotificationConfiguration()
        value.topicArn = try reader["TopicArn"].readIfPresent()
        value.topicStatus = try reader["TopicStatus"].readIfPresent()
        return value
    }
}

extension DAXClientTypes.Node {

    static func read(from reader: SmithyJSON.Reader) throws -> DAXClientTypes.Node {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DAXClientTypes.Node()
        value.nodeId = try reader["NodeId"].readIfPresent()
        value.endpoint = try reader["Endpoint"].readIfPresent(with: DAXClientTypes.Endpoint.read(from:))
        value.nodeCreateTime = try reader["NodeCreateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.availabilityZone = try reader["AvailabilityZone"].readIfPresent()
        value.nodeStatus = try reader["NodeStatus"].readIfPresent()
        value.parameterGroupStatus = try reader["ParameterGroupStatus"].readIfPresent()
        return value
    }
}

extension DAXClientTypes.Endpoint {

    static func read(from reader: SmithyJSON.Reader) throws -> DAXClientTypes.Endpoint {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DAXClientTypes.Endpoint()
        value.address = try reader["Address"].readIfPresent()
        value.port = try reader["Port"].readIfPresent() ?? 0
        value.url = try reader["URL"].readIfPresent()
        return value
    }
}

extension DAXClientTypes.ParameterGroup {

    static func read(from reader: SmithyJSON.Reader) throws -> DAXClientTypes.ParameterGroup {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DAXClientTypes.ParameterGroup()
        value.parameterGroupName = try reader["ParameterGroupName"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        return value
    }
}

extension DAXClientTypes.SubnetGroup {

    static func read(from reader: SmithyJSON.Reader) throws -> DAXClientTypes.SubnetGroup {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DAXClientTypes.SubnetGroup()
        value.subnetGroupName = try reader["SubnetGroupName"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.vpcId = try reader["VpcId"].readIfPresent()
        value.subnets = try reader["Subnets"].readListIfPresent(memberReadingClosure: DAXClientTypes.Subnet.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DAXClientTypes.Subnet {

    static func read(from reader: SmithyJSON.Reader) throws -> DAXClientTypes.Subnet {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DAXClientTypes.Subnet()
        value.subnetIdentifier = try reader["SubnetIdentifier"].readIfPresent()
        value.subnetAvailabilityZone = try reader["SubnetAvailabilityZone"].readIfPresent()
        return value
    }
}

extension DAXClientTypes.Parameter {

    static func read(from reader: SmithyJSON.Reader) throws -> DAXClientTypes.Parameter {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DAXClientTypes.Parameter()
        value.parameterName = try reader["ParameterName"].readIfPresent()
        value.parameterType = try reader["ParameterType"].readIfPresent()
        value.parameterValue = try reader["ParameterValue"].readIfPresent()
        value.nodeTypeSpecificValues = try reader["NodeTypeSpecificValues"].readListIfPresent(memberReadingClosure: DAXClientTypes.NodeTypeSpecificValue.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.description = try reader["Description"].readIfPresent()
        value.source = try reader["Source"].readIfPresent()
        value.dataType = try reader["DataType"].readIfPresent()
        value.allowedValues = try reader["AllowedValues"].readIfPresent()
        value.isModifiable = try reader["IsModifiable"].readIfPresent()
        value.changeType = try reader["ChangeType"].readIfPresent()
        return value
    }
}

extension DAXClientTypes.NodeTypeSpecificValue {

    static func read(from reader: SmithyJSON.Reader) throws -> DAXClientTypes.NodeTypeSpecificValue {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DAXClientTypes.NodeTypeSpecificValue()
        value.nodeType = try reader["NodeType"].readIfPresent()
        value.value = try reader["Value"].readIfPresent()
        return value
    }
}

extension DAXClientTypes.Event {

    static func read(from reader: SmithyJSON.Reader) throws -> DAXClientTypes.Event {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DAXClientTypes.Event()
        value.sourceName = try reader["SourceName"].readIfPresent()
        value.sourceType = try reader["SourceType"].readIfPresent()
        value.message = try reader["Message"].readIfPresent()
        value.date = try reader["Date"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension DAXClientTypes.Tag {

    static func write(value: DAXClientTypes.Tag?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Key"].write(value.key)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DAXClientTypes.Tag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DAXClientTypes.Tag()
        value.key = try reader["Key"].readIfPresent()
        value.value = try reader["Value"].readIfPresent()
        return value
    }
}

extension DAXClientTypes.SSESpecification {

    static func write(value: DAXClientTypes.SSESpecification?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Enabled"].write(value.enabled)
    }
}

extension DAXClientTypes.ParameterNameValue {

    static func write(value: DAXClientTypes.ParameterNameValue?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ParameterName"].write(value.parameterName)
        try writer["ParameterValue"].write(value.parameterValue)
    }
}

public enum DAXClientTypes {}
