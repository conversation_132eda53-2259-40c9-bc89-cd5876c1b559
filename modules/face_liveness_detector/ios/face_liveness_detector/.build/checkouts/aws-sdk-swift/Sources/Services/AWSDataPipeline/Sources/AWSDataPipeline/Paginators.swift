//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension DataPipelineClient {
    /// Paginate over `[DescribeObjectsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeObjectsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeObjectsOutput`
    public func describeObjectsPaginated(input: DescribeObjectsInput) -> ClientRuntime.PaginatorSequence<DescribeObjectsInput, DescribeObjectsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeObjectsInput, DescribeObjectsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeObjects(input:))
    }
}

extension DescribeObjectsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeObjectsInput {
        return DescribeObjectsInput(
            evaluateExpressions: self.evaluateExpressions,
            marker: token,
            objectIds: self.objectIds,
            pipelineId: self.pipelineId
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeObjectsInput, OperationStackOutput == DescribeObjectsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeObjectsPaginated`
    /// to access the nested member `[DataPipelineClientTypes.PipelineObject]`
    /// - Returns: `[DataPipelineClientTypes.PipelineObject]`
    public func pipelineObjects() async throws -> [DataPipelineClientTypes.PipelineObject] {
        return try await self.asyncCompactMap { item in item.pipelineObjects }
    }
}
extension DataPipelineClient {
    /// Paginate over `[ListPipelinesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListPipelinesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListPipelinesOutput`
    public func listPipelinesPaginated(input: ListPipelinesInput) -> ClientRuntime.PaginatorSequence<ListPipelinesInput, ListPipelinesOutput> {
        return ClientRuntime.PaginatorSequence<ListPipelinesInput, ListPipelinesOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.listPipelines(input:))
    }
}

extension ListPipelinesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListPipelinesInput {
        return ListPipelinesInput(
            marker: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListPipelinesInput, OperationStackOutput == ListPipelinesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listPipelinesPaginated`
    /// to access the nested member `[DataPipelineClientTypes.PipelineIdName]`
    /// - Returns: `[DataPipelineClientTypes.PipelineIdName]`
    public func pipelineIdList() async throws -> [DataPipelineClientTypes.PipelineIdName] {
        return try await self.asyncCompactMap { item in item.pipelineIdList }
    }
}
extension DataPipelineClient {
    /// Paginate over `[QueryObjectsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[QueryObjectsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `QueryObjectsOutput`
    public func queryObjectsPaginated(input: QueryObjectsInput) -> ClientRuntime.PaginatorSequence<QueryObjectsInput, QueryObjectsOutput> {
        return ClientRuntime.PaginatorSequence<QueryObjectsInput, QueryObjectsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.queryObjects(input:))
    }
}

extension QueryObjectsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> QueryObjectsInput {
        return QueryObjectsInput(
            limit: self.limit,
            marker: token,
            pipelineId: self.pipelineId,
            query: self.query,
            sphere: self.sphere
        )}
}

extension PaginatorSequence where OperationStackInput == QueryObjectsInput, OperationStackOutput == QueryObjectsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `queryObjectsPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func ids() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.ids }
    }
}
