//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHT<PERSON>API.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.AWSJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
@_spi(SmithyReadWrite) import struct SmithyReadWrite.ReadingClosureBox

/// A client exception has occurred.
public struct ClientException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ClientException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// The specified directory has already been shared with this Amazon Web Services account.
public struct DirectoryAlreadySharedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DirectoryAlreadySharedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// The specified entity could not be found.
public struct EntityDoesNotExistException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "EntityDoesNotExistException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// One or more parameters are not valid.
public struct InvalidParameterException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidParameterException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// An exception has occurred in Directory Service.
public struct ServiceException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

public struct AcceptSharedDirectoryInput: Swift.Sendable {
    /// Identifier of the shared directory in the directory consumer account. This identifier is different for each directory owner account.
    /// This member is required.
    public var sharedDirectoryId: Swift.String?

    public init(
        sharedDirectoryId: Swift.String? = nil
    )
    {
        self.sharedDirectoryId = sharedDirectoryId
    }
}

extension DirectoryClientTypes {

    public enum ShareMethod: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case handshake
        case organizations
        case sdkUnknown(Swift.String)

        public static var allCases: [ShareMethod] {
            return [
                .handshake,
                .organizations
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .handshake: return "HANDSHAKE"
            case .organizations: return "ORGANIZATIONS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    public enum ShareStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case deleted
        case deleting
        case pendingAcceptance
        case rejected
        case rejecting
        case rejectFailed
        case shared
        case shareFailed
        case sharing
        case sdkUnknown(Swift.String)

        public static var allCases: [ShareStatus] {
            return [
                .deleted,
                .deleting,
                .pendingAcceptance,
                .rejected,
                .rejecting,
                .rejectFailed,
                .shared,
                .shareFailed,
                .sharing
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .deleted: return "Deleted"
            case .deleting: return "Deleting"
            case .pendingAcceptance: return "PendingAcceptance"
            case .rejected: return "Rejected"
            case .rejecting: return "Rejecting"
            case .rejectFailed: return "RejectFailed"
            case .shared: return "Shared"
            case .shareFailed: return "ShareFailed"
            case .sharing: return "Sharing"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Details about the shared directory in the directory owner account for which the share request in the directory consumer account has been accepted.
    public struct SharedDirectory: Swift.Sendable {
        /// The date and time that the shared directory was created.
        public var createdDateTime: Foundation.Date?
        /// The date and time that the shared directory was last updated.
        public var lastUpdatedDateTime: Foundation.Date?
        /// Identifier of the directory owner account, which contains the directory that has been shared to the consumer account.
        public var ownerAccountId: Swift.String?
        /// Identifier of the directory in the directory owner account.
        public var ownerDirectoryId: Swift.String?
        /// The method used when sharing a directory to determine whether the directory should be shared within your Amazon Web Services organization (ORGANIZATIONS) or with any Amazon Web Services account by sending a shared directory request (HANDSHAKE).
        public var shareMethod: DirectoryClientTypes.ShareMethod?
        /// A directory share request that is sent by the directory owner to the directory consumer. The request includes a typed message to help the directory consumer administrator determine whether to approve or reject the share invitation.
        public var shareNotes: Swift.String?
        /// Current directory status of the shared Managed Microsoft AD directory.
        public var shareStatus: DirectoryClientTypes.ShareStatus?
        /// Identifier of the directory consumer account that has access to the shared directory (OwnerDirectoryId) in the directory owner account.
        public var sharedAccountId: Swift.String?
        /// Identifier of the shared directory in the directory consumer account. This identifier is different for each directory owner account.
        public var sharedDirectoryId: Swift.String?

        public init(
            createdDateTime: Foundation.Date? = nil,
            lastUpdatedDateTime: Foundation.Date? = nil,
            ownerAccountId: Swift.String? = nil,
            ownerDirectoryId: Swift.String? = nil,
            shareMethod: DirectoryClientTypes.ShareMethod? = nil,
            shareNotes: Swift.String? = nil,
            shareStatus: DirectoryClientTypes.ShareStatus? = nil,
            sharedAccountId: Swift.String? = nil,
            sharedDirectoryId: Swift.String? = nil
        )
        {
            self.createdDateTime = createdDateTime
            self.lastUpdatedDateTime = lastUpdatedDateTime
            self.ownerAccountId = ownerAccountId
            self.ownerDirectoryId = ownerDirectoryId
            self.shareMethod = shareMethod
            self.shareNotes = shareNotes
            self.shareStatus = shareStatus
            self.sharedAccountId = sharedAccountId
            self.sharedDirectoryId = sharedDirectoryId
        }
    }
}

extension DirectoryClientTypes.SharedDirectory: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "SharedDirectory(createdDateTime: \(Swift.String(describing: createdDateTime)), lastUpdatedDateTime: \(Swift.String(describing: lastUpdatedDateTime)), ownerAccountId: \(Swift.String(describing: ownerAccountId)), ownerDirectoryId: \(Swift.String(describing: ownerDirectoryId)), shareMethod: \(Swift.String(describing: shareMethod)), shareStatus: \(Swift.String(describing: shareStatus)), sharedAccountId: \(Swift.String(describing: sharedAccountId)), sharedDirectoryId: \(Swift.String(describing: sharedDirectoryId)), shareNotes: \"CONTENT_REDACTED\")"}
}

public struct AcceptSharedDirectoryOutput: Swift.Sendable {
    /// The shared directory in the directory consumer account.
    public var sharedDirectory: DirectoryClientTypes.SharedDirectory?

    public init(
        sharedDirectory: DirectoryClientTypes.SharedDirectory? = nil
    )
    {
        self.sharedDirectory = sharedDirectory
    }
}

/// You do not have sufficient access to perform this action.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// The specified directory is unavailable.
public struct DirectoryUnavailableException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DirectoryUnavailableException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// The specified entity already exists.
public struct EntityAlreadyExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "EntityAlreadyExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// The maximum allowed number of IP addresses was exceeded. The default limit is 100 IP address blocks.
public struct IpRouteLimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "IpRouteLimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

extension DirectoryClientTypes {

    /// IP address block. This is often the address block of the DNS server used for your self-managed domain.
    public struct IpRoute: Swift.Sendable {
        /// IP address block using CIDR format, for example 10.0.0.0/24. This is often the address block of the DNS server used for your self-managed domain. For a single IP address use a CIDR address block with /32. For example 10.0.0.0/32.
        public var cidrIp: Swift.String?
        /// Description of the address block.
        public var description: Swift.String?

        public init(
            cidrIp: Swift.String? = nil,
            description: Swift.String? = nil
        )
        {
            self.cidrIp = cidrIp
            self.description = description
        }
    }
}

public struct AddIpRoutesInput: Swift.Sendable {
    /// Identifier (ID) of the directory to which to add the address block.
    /// This member is required.
    public var directoryId: Swift.String?
    /// IP address blocks, using CIDR format, of the traffic to route. This is often the IP address block of the DNS server used for your self-managed domain.
    /// This member is required.
    public var ipRoutes: [DirectoryClientTypes.IpRoute]?
    /// If set to true, updates the inbound and outbound rules of the security group that has the description: "Amazon Web Services created security group for directory ID directory controllers." Following are the new rules: Inbound:
    ///
    /// * Type: Custom UDP Rule, Protocol: UDP, Range: 88, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: Custom UDP Rule, Protocol: UDP, Range: 123, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: Custom UDP Rule, Protocol: UDP, Range: 138, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: Custom UDP Rule, Protocol: UDP, Range: 389, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: Custom UDP Rule, Protocol: UDP, Range: 464, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: Custom UDP Rule, Protocol: UDP, Range: 445, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: Custom TCP Rule, Protocol: TCP, Range: 88, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: Custom TCP Rule, Protocol: TCP, Range: 135, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: Custom TCP Rule, Protocol: TCP, Range: 445, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: Custom TCP Rule, Protocol: TCP, Range: 464, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: Custom TCP Rule, Protocol: TCP, Range: 636, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: Custom TCP Rule, Protocol: TCP, Range: 1024-65535, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: Custom TCP Rule, Protocol: TCP, Range: 3268-33269, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: DNS (UDP), Protocol: UDP, Range: 53, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: DNS (TCP), Protocol: TCP, Range: 53, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: LDAP, Protocol: TCP, Range: 389, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    /// * Type: All ICMP, Protocol: All, Range: N/A, Source: Managed Microsoft AD VPC IPv4 CIDR
    ///
    ///
    /// Outbound:
    ///
    /// * Type: All traffic, Protocol: All, Range: All, Destination: 0.0.0.0/0
    ///
    ///
    /// These security rules impact an internal network interface that is not exposed publicly.
    public var updateSecurityGroupForDirectoryControllers: Swift.Bool?

    public init(
        directoryId: Swift.String? = nil,
        ipRoutes: [DirectoryClientTypes.IpRoute]? = nil,
        updateSecurityGroupForDirectoryControllers: Swift.Bool? = false
    )
    {
        self.directoryId = directoryId
        self.ipRoutes = ipRoutes
        self.updateSecurityGroupForDirectoryControllers = updateSecurityGroupForDirectoryControllers
    }
}

public struct AddIpRoutesOutput: Swift.Sendable {

    public init() { }
}

/// The Region you specified is the same Region where the Managed Microsoft AD directory was created. Specify a different Region and try again.
public struct DirectoryAlreadyInRegionException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DirectoryAlreadyInRegionException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// The specified directory does not exist in the system.
public struct DirectoryDoesNotExistException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DirectoryDoesNotExistException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// You have reached the limit for maximum number of simultaneous Region replications per directory.
public struct RegionLimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RegionLimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// The operation is not supported.
public struct UnsupportedOperationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UnsupportedOperationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

extension DirectoryClientTypes {

    /// Contains VPC information for the [CreateDirectory] or [CreateMicrosoftAD] operation.
    public struct DirectoryVpcSettings: Swift.Sendable {
        /// The identifiers of the subnets for the directory servers. The two subnets must be in different Availability Zones. Directory Service creates a directory server and a DNS server in each of these subnets.
        /// This member is required.
        public var subnetIds: [Swift.String]?
        /// The identifier of the VPC in which to create the directory.
        /// This member is required.
        public var vpcId: Swift.String?

        public init(
            subnetIds: [Swift.String]? = nil,
            vpcId: Swift.String? = nil
        )
        {
            self.subnetIds = subnetIds
            self.vpcId = vpcId
        }
    }
}

public struct AddRegionInput: Swift.Sendable {
    /// The identifier of the directory to which you want to add Region replication.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The name of the Region where you want to add domain controllers for replication. For example, us-east-1.
    /// This member is required.
    public var regionName: Swift.String?
    /// Contains VPC information for the [CreateDirectory] or [CreateMicrosoftAD] operation.
    /// This member is required.
    public var vpcSettings: DirectoryClientTypes.DirectoryVpcSettings?

    public init(
        directoryId: Swift.String? = nil,
        regionName: Swift.String? = nil,
        vpcSettings: DirectoryClientTypes.DirectoryVpcSettings? = nil
    )
    {
        self.directoryId = directoryId
        self.regionName = regionName
        self.vpcSettings = vpcSettings
    }
}

public struct AddRegionOutput: Swift.Sendable {

    public init() { }
}

/// The maximum allowed number of tags was exceeded.
public struct TagLimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TagLimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

extension DirectoryClientTypes {

    /// Metadata assigned to a directory consisting of a key-value pair.
    public struct Tag: Swift.Sendable {
        /// Required name of the tag. The string value can be Unicode characters and cannot be prefixed with "aws:". The string can contain only the set of Unicode letters, digits, white-space, '_', '.', '/', '=', '+', '-', ':', '@'(Java regex: "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-]*)$").
        /// This member is required.
        public var key: Swift.String?
        /// The optional value of the tag. The string value can be Unicode characters. The string can contain only the set of Unicode letters, digits, white-space, '_', '.', '/', '=', '+', '-', ':', '@' (Java regex: "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-]*)$").
        /// This member is required.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

public struct AddTagsToResourceInput: Swift.Sendable {
    /// Identifier (ID) for the directory to which to add the tag.
    /// This member is required.
    public var resourceId: Swift.String?
    /// The tags to be assigned to the directory.
    /// This member is required.
    public var tags: [DirectoryClientTypes.Tag]?

    public init(
        resourceId: Swift.String? = nil,
        tags: [DirectoryClientTypes.Tag]? = nil
    )
    {
        self.resourceId = resourceId
        self.tags = tags
    }
}

public struct AddTagsToResourceOutput: Swift.Sendable {

    public init() { }
}

extension DirectoryClientTypes {

    /// Represents a named directory attribute.
    public struct Attribute: Swift.Sendable {
        /// The name of the attribute.
        public var name: Swift.String?
        /// The value of the attribute.
        public var value: Swift.String?

        public init(
            name: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.name = name
            self.value = value
        }
    }
}

/// An authentication error occurred.
public struct AuthenticationFailedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The textual message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The identifier of the request that caused the exception.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AuthenticationFailedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

public struct CancelSchemaExtensionInput: Swift.Sendable {
    /// The identifier of the directory whose schema extension will be canceled.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The identifier of the schema extension that will be canceled.
    /// This member is required.
    public var schemaExtensionId: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        schemaExtensionId: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.schemaExtensionId = schemaExtensionId
    }
}

public struct CancelSchemaExtensionOutput: Swift.Sendable {

    public init() { }
}

extension DirectoryClientTypes {

    /// Contains information about the client certificate authentication settings for the RegisterCertificate and DescribeCertificate operations.
    public struct ClientCertAuthSettings: Swift.Sendable {
        /// Specifies the URL of the default OCSP server used to check for revocation status. A secondary value to any OCSP address found in the AIA extension of the user certificate.
        public var ocspUrl: Swift.String?

        public init(
            ocspUrl: Swift.String? = nil
        )
        {
            self.ocspUrl = ocspUrl
        }
    }
}

extension DirectoryClientTypes {

    public enum CertificateState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case deregistered
        case deregistering
        case deregisterFailed
        case registered
        case registering
        case registerFailed
        case sdkUnknown(Swift.String)

        public static var allCases: [CertificateState] {
            return [
                .deregistered,
                .deregistering,
                .deregisterFailed,
                .registered,
                .registering,
                .registerFailed
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .deregistered: return "Deregistered"
            case .deregistering: return "Deregistering"
            case .deregisterFailed: return "DeregisterFailed"
            case .registered: return "Registered"
            case .registering: return "Registering"
            case .registerFailed: return "RegisterFailed"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    public enum CertificateType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case clientCertAuth
        case clientLdaps
        case sdkUnknown(Swift.String)

        public static var allCases: [CertificateType] {
            return [
                .clientCertAuth,
                .clientLdaps
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .clientCertAuth: return "ClientCertAuth"
            case .clientLdaps: return "ClientLDAPS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Information about the certificate.
    public struct Certificate: Swift.Sendable {
        /// The identifier of the certificate.
        public var certificateId: Swift.String?
        /// A ClientCertAuthSettings object that contains client certificate authentication settings.
        public var clientCertAuthSettings: DirectoryClientTypes.ClientCertAuthSettings?
        /// The common name for the certificate.
        public var commonName: Swift.String?
        /// The date and time when the certificate will expire.
        public var expiryDateTime: Foundation.Date?
        /// The date and time that the certificate was registered.
        public var registeredDateTime: Foundation.Date?
        /// The state of the certificate.
        public var state: DirectoryClientTypes.CertificateState?
        /// Describes a state change for the certificate.
        public var stateReason: Swift.String?
        /// The function that the registered certificate performs. Valid values include ClientLDAPS or ClientCertAuth. The default value is ClientLDAPS.
        public var type: DirectoryClientTypes.CertificateType?

        public init(
            certificateId: Swift.String? = nil,
            clientCertAuthSettings: DirectoryClientTypes.ClientCertAuthSettings? = nil,
            commonName: Swift.String? = nil,
            expiryDateTime: Foundation.Date? = nil,
            registeredDateTime: Foundation.Date? = nil,
            state: DirectoryClientTypes.CertificateState? = nil,
            stateReason: Swift.String? = nil,
            type: DirectoryClientTypes.CertificateType? = nil
        )
        {
            self.certificateId = certificateId
            self.clientCertAuthSettings = clientCertAuthSettings
            self.commonName = commonName
            self.expiryDateTime = expiryDateTime
            self.registeredDateTime = registeredDateTime
            self.state = state
            self.stateReason = stateReason
            self.type = type
        }
    }
}

/// The certificate has already been registered into the system.
public struct CertificateAlreadyExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "CertificateAlreadyExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// The certificate is not present in the system for describe or deregister activities.
public struct CertificateDoesNotExistException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "CertificateDoesNotExistException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

extension DirectoryClientTypes {

    /// Contains general information about a certificate.
    public struct CertificateInfo: Swift.Sendable {
        /// The identifier of the certificate.
        public var certificateId: Swift.String?
        /// The common name for the certificate.
        public var commonName: Swift.String?
        /// The date and time when the certificate will expire.
        public var expiryDateTime: Foundation.Date?
        /// The state of the certificate.
        public var state: DirectoryClientTypes.CertificateState?
        /// The function that the registered certificate performs. Valid values include ClientLDAPS or ClientCertAuth. The default value is ClientLDAPS.
        public var type: DirectoryClientTypes.CertificateType?

        public init(
            certificateId: Swift.String? = nil,
            commonName: Swift.String? = nil,
            expiryDateTime: Foundation.Date? = nil,
            state: DirectoryClientTypes.CertificateState? = nil,
            type: DirectoryClientTypes.CertificateType? = nil
        )
        {
            self.certificateId = certificateId
            self.commonName = commonName
            self.expiryDateTime = expiryDateTime
            self.state = state
            self.type = type
        }
    }
}

/// The certificate is being used for the LDAP security connection and cannot be removed without disabling LDAP security.
public struct CertificateInUseException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "CertificateInUseException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// The certificate could not be added because the certificate limit has been reached.
public struct CertificateLimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "CertificateLimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

extension DirectoryClientTypes {

    public enum ClientAuthenticationStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case enabled
        case sdkUnknown(Swift.String)

        public static var allCases: [ClientAuthenticationStatus] {
            return [
                .disabled,
                .enabled
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "Disabled"
            case .enabled: return "Enabled"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    public enum ClientAuthenticationType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case smartCard
        case smartCardOrPassword
        case sdkUnknown(Swift.String)

        public static var allCases: [ClientAuthenticationType] {
            return [
                .smartCard,
                .smartCardOrPassword
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .smartCard: return "SmartCard"
            case .smartCardOrPassword: return "SmartCardOrPassword"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Contains information about a client authentication method for a directory.
    public struct ClientAuthenticationSettingInfo: Swift.Sendable {
        /// The date and time when the status of the client authentication type was last updated.
        public var lastUpdatedDateTime: Foundation.Date?
        /// Whether the client authentication type is enabled or disabled for the specified directory.
        public var status: DirectoryClientTypes.ClientAuthenticationStatus?
        /// The type of client authentication for the specified directory. If no type is specified, a list of all client authentication types that are supported for the directory is retrieved.
        public var type: DirectoryClientTypes.ClientAuthenticationType?

        public init(
            lastUpdatedDateTime: Foundation.Date? = nil,
            status: DirectoryClientTypes.ClientAuthenticationStatus? = nil,
            type: DirectoryClientTypes.ClientAuthenticationType? = nil
        )
        {
            self.lastUpdatedDateTime = lastUpdatedDateTime
            self.status = status
            self.type = type
        }
    }
}

extension DirectoryClientTypes {

    /// Contains information about a computer account in a directory.
    public struct Computer: Swift.Sendable {
        /// An array of [Attribute] objects containing the LDAP attributes that belong to the computer account.
        public var computerAttributes: [DirectoryClientTypes.Attribute]?
        /// The identifier of the computer.
        public var computerId: Swift.String?
        /// The computer name.
        public var computerName: Swift.String?

        public init(
            computerAttributes: [DirectoryClientTypes.Attribute]? = nil,
            computerId: Swift.String? = nil,
            computerName: Swift.String? = nil
        )
        {
            self.computerAttributes = computerAttributes
            self.computerId = computerId
            self.computerName = computerName
        }
    }
}

extension DirectoryClientTypes {

    public enum ReplicationScope: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case domain
        case sdkUnknown(Swift.String)

        public static var allCases: [ReplicationScope] {
            return [
                .domain
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .domain: return "Domain"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Points to a remote domain with which you are setting up a trust relationship. Conditional forwarders are required in order to set up a trust relationship with another domain.
    public struct ConditionalForwarder: Swift.Sendable {
        /// The IP addresses of the remote DNS server associated with RemoteDomainName. This is the IP address of the DNS server that your conditional forwarder points to.
        public var dnsIpAddrs: [Swift.String]?
        /// The fully qualified domain name (FQDN) of the remote domains pointed to by the conditional forwarder.
        public var remoteDomainName: Swift.String?
        /// The replication scope of the conditional forwarder. The only allowed value is Domain, which will replicate the conditional forwarder to all of the domain controllers for your Amazon Web Services directory.
        public var replicationScope: DirectoryClientTypes.ReplicationScope?

        public init(
            dnsIpAddrs: [Swift.String]? = nil,
            remoteDomainName: Swift.String? = nil,
            replicationScope: DirectoryClientTypes.ReplicationScope? = nil
        )
        {
            self.dnsIpAddrs = dnsIpAddrs
            self.remoteDomainName = remoteDomainName
            self.replicationScope = replicationScope
        }
    }
}

/// The maximum number of directories in the region has been reached. You can use the [GetDirectoryLimits] operation to determine your directory limits in the region.
public struct DirectoryLimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DirectoryLimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

extension DirectoryClientTypes {

    /// Contains information for the [ConnectDirectory] operation when an AD Connector directory is being created.
    public struct DirectoryConnectSettings: Swift.Sendable {
        /// A list of one or more IP addresses of DNS servers or domain controllers in your self-managed directory.
        /// This member is required.
        public var customerDnsIps: [Swift.String]?
        /// The user name of an account in your self-managed directory that is used to connect to the directory. This account must have the following permissions:
        ///
        /// * Read users and groups
        ///
        /// * Create computer objects
        ///
        /// * Join computers to the domain
        /// This member is required.
        public var customerUserName: Swift.String?
        /// A list of subnet identifiers in the VPC in which the AD Connector is created.
        /// This member is required.
        public var subnetIds: [Swift.String]?
        /// The identifier of the VPC in which the AD Connector is created.
        /// This member is required.
        public var vpcId: Swift.String?

        public init(
            customerDnsIps: [Swift.String]? = nil,
            customerUserName: Swift.String? = nil,
            subnetIds: [Swift.String]? = nil,
            vpcId: Swift.String? = nil
        )
        {
            self.customerDnsIps = customerDnsIps
            self.customerUserName = customerUserName
            self.subnetIds = subnetIds
            self.vpcId = vpcId
        }
    }
}

extension DirectoryClientTypes {

    public enum DirectorySize: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case large
        case small
        case sdkUnknown(Swift.String)

        public static var allCases: [DirectorySize] {
            return [
                .large,
                .small
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .large: return "Large"
            case .small: return "Small"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Contains the inputs for the [ConnectDirectory] operation.
public struct ConnectDirectoryInput: Swift.Sendable {
    /// A [DirectoryConnectSettings] object that contains additional information for the operation.
    /// This member is required.
    public var connectSettings: DirectoryClientTypes.DirectoryConnectSettings?
    /// A description for the directory.
    public var description: Swift.String?
    /// The fully qualified name of your self-managed directory, such as corp.example.com.
    /// This member is required.
    public var name: Swift.String?
    /// The password for your self-managed user account.
    /// This member is required.
    public var password: Swift.String?
    /// The NetBIOS name of your self-managed directory, such as CORP.
    public var shortName: Swift.String?
    /// The size of the directory.
    /// This member is required.
    public var size: DirectoryClientTypes.DirectorySize?
    /// The tags to be assigned to AD Connector.
    public var tags: [DirectoryClientTypes.Tag]?

    public init(
        connectSettings: DirectoryClientTypes.DirectoryConnectSettings? = nil,
        description: Swift.String? = nil,
        name: Swift.String? = nil,
        password: Swift.String? = nil,
        shortName: Swift.String? = nil,
        size: DirectoryClientTypes.DirectorySize? = nil,
        tags: [DirectoryClientTypes.Tag]? = nil
    )
    {
        self.connectSettings = connectSettings
        self.description = description
        self.name = name
        self.password = password
        self.shortName = shortName
        self.size = size
        self.tags = tags
    }
}

extension ConnectDirectoryInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ConnectDirectoryInput(connectSettings: \(Swift.String(describing: connectSettings)), description: \(Swift.String(describing: description)), name: \(Swift.String(describing: name)), shortName: \(Swift.String(describing: shortName)), size: \(Swift.String(describing: size)), tags: \(Swift.String(describing: tags)), password: \"CONTENT_REDACTED\")"}
}

/// Contains the results of the [ConnectDirectory] operation.
public struct ConnectDirectoryOutput: Swift.Sendable {
    /// The identifier of the new directory.
    public var directoryId: Swift.String?

    public init(
        directoryId: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
    }
}

/// Contains the inputs for the [CreateAlias] operation.
public struct CreateAliasInput: Swift.Sendable {
    /// The requested alias. The alias must be unique amongst all aliases in Amazon Web Services. This operation throws an EntityAlreadyExistsException error if the alias already exists.
    /// This member is required.
    public var alias: Swift.String?
    /// The identifier of the directory for which to create the alias.
    /// This member is required.
    public var directoryId: Swift.String?

    public init(
        alias: Swift.String? = nil,
        directoryId: Swift.String? = nil
    )
    {
        self.alias = alias
        self.directoryId = directoryId
    }
}

/// Contains the results of the [CreateAlias] operation.
public struct CreateAliasOutput: Swift.Sendable {
    /// The alias for the directory.
    public var alias: Swift.String?
    /// The identifier of the directory.
    public var directoryId: Swift.String?

    public init(
        alias: Swift.String? = nil,
        directoryId: Swift.String? = nil
    )
    {
        self.alias = alias
        self.directoryId = directoryId
    }
}

/// Contains the inputs for the [CreateComputer] operation.
public struct CreateComputerInput: Swift.Sendable {
    /// An array of [Attribute] objects that contain any LDAP attributes to apply to the computer account.
    public var computerAttributes: [DirectoryClientTypes.Attribute]?
    /// The name of the computer account.
    /// This member is required.
    public var computerName: Swift.String?
    /// The identifier of the directory in which to create the computer account.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The fully-qualified distinguished name of the organizational unit to place the computer account in.
    public var organizationalUnitDistinguishedName: Swift.String?
    /// A one-time password that is used to join the computer to the directory. You should generate a random, strong password to use for this parameter.
    /// This member is required.
    public var password: Swift.String?

    public init(
        computerAttributes: [DirectoryClientTypes.Attribute]? = nil,
        computerName: Swift.String? = nil,
        directoryId: Swift.String? = nil,
        organizationalUnitDistinguishedName: Swift.String? = nil,
        password: Swift.String? = nil
    )
    {
        self.computerAttributes = computerAttributes
        self.computerName = computerName
        self.directoryId = directoryId
        self.organizationalUnitDistinguishedName = organizationalUnitDistinguishedName
        self.password = password
    }
}

extension CreateComputerInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateComputerInput(computerAttributes: \(Swift.String(describing: computerAttributes)), computerName: \(Swift.String(describing: computerName)), directoryId: \(Swift.String(describing: directoryId)), organizationalUnitDistinguishedName: \(Swift.String(describing: organizationalUnitDistinguishedName)), password: \"CONTENT_REDACTED\")"}
}

/// Contains the results for the [CreateComputer] operation.
public struct CreateComputerOutput: Swift.Sendable {
    /// A [Computer] object that represents the computer account.
    public var computer: DirectoryClientTypes.Computer?

    public init(
        computer: DirectoryClientTypes.Computer? = nil
    )
    {
        self.computer = computer
    }
}

/// Initiates the creation of a conditional forwarder for your Directory Service for Microsoft Active Directory. Conditional forwarders are required in order to set up a trust relationship with another domain.
public struct CreateConditionalForwarderInput: Swift.Sendable {
    /// The directory ID of the Amazon Web Services directory for which you are creating the conditional forwarder.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The IP addresses of the remote DNS server associated with RemoteDomainName.
    /// This member is required.
    public var dnsIpAddrs: [Swift.String]?
    /// The fully qualified domain name (FQDN) of the remote domain with which you will set up a trust relationship.
    /// This member is required.
    public var remoteDomainName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        dnsIpAddrs: [Swift.String]? = nil,
        remoteDomainName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.dnsIpAddrs = dnsIpAddrs
        self.remoteDomainName = remoteDomainName
    }
}

/// The result of a CreateConditinalForwarder request.
public struct CreateConditionalForwarderOutput: Swift.Sendable {

    public init() { }
}

/// Contains the inputs for the [CreateDirectory] operation.
public struct CreateDirectoryInput: Swift.Sendable {
    /// A description for the directory.
    public var description: Swift.String?
    /// The fully qualified name for the directory, such as corp.example.com.
    /// This member is required.
    public var name: Swift.String?
    /// The password for the directory administrator. The directory creation process creates a directory administrator account with the user name Administrator and this password. If you need to change the password for the administrator account, you can use the [ResetUserPassword] API call. The regex pattern for this string is made up of the following conditions:
    ///
    /// * Length (?=^.{8,64}$) – Must be between 8 and 64 characters
    ///
    ///
    /// AND any 3 of the following password complexity rules required by Active Directory:
    ///
    /// * Numbers and upper case and lowercase (?=.*\d)(?=.*[A-Z])(?=.*[a-z])
    ///
    /// * Numbers and special characters and lower case (?=.*\d)(?=.*[^A-Za-z0-9\s])(?=.*[a-z])
    ///
    /// * Special characters and upper case and lower case (?=.*[^A-Za-z0-9\s])(?=.*[A-Z])(?=.*[a-z])
    ///
    /// * Numbers and upper case and special characters (?=.*\d)(?=.*[A-Z])(?=.*[^A-Za-z0-9\s])
    ///
    ///
    /// For additional information about how Active Directory passwords are enforced, see [Password must meet complexity requirements](https://docs.microsoft.com/en-us/windows/security/threat-protection/security-policy-settings/password-must-meet-complexity-requirements) on the Microsoft website.
    /// This member is required.
    public var password: Swift.String?
    /// The NetBIOS name of the directory, such as CORP.
    public var shortName: Swift.String?
    /// The size of the directory.
    /// This member is required.
    public var size: DirectoryClientTypes.DirectorySize?
    /// The tags to be assigned to the Simple AD directory.
    public var tags: [DirectoryClientTypes.Tag]?
    /// A [DirectoryVpcSettings] object that contains additional information for the operation.
    public var vpcSettings: DirectoryClientTypes.DirectoryVpcSettings?

    public init(
        description: Swift.String? = nil,
        name: Swift.String? = nil,
        password: Swift.String? = nil,
        shortName: Swift.String? = nil,
        size: DirectoryClientTypes.DirectorySize? = nil,
        tags: [DirectoryClientTypes.Tag]? = nil,
        vpcSettings: DirectoryClientTypes.DirectoryVpcSettings? = nil
    )
    {
        self.description = description
        self.name = name
        self.password = password
        self.shortName = shortName
        self.size = size
        self.tags = tags
        self.vpcSettings = vpcSettings
    }
}

extension CreateDirectoryInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateDirectoryInput(description: \(Swift.String(describing: description)), name: \(Swift.String(describing: name)), shortName: \(Swift.String(describing: shortName)), size: \(Swift.String(describing: size)), tags: \(Swift.String(describing: tags)), vpcSettings: \(Swift.String(describing: vpcSettings)), password: \"CONTENT_REDACTED\")"}
}

/// Contains the results of the [CreateDirectory] operation.
public struct CreateDirectoryOutput: Swift.Sendable {
    /// The identifier of the directory that was created.
    public var directoryId: Swift.String?

    public init(
        directoryId: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
    }
}

/// The account does not have sufficient permission to perform the operation.
public struct InsufficientPermissionsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InsufficientPermissionsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

public struct CreateLogSubscriptionInput: Swift.Sendable {
    /// Identifier of the directory to which you want to subscribe and receive real-time logs to your specified CloudWatch log group.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The name of the CloudWatch log group where the real-time domain controller logs are forwarded.
    /// This member is required.
    public var logGroupName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        logGroupName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.logGroupName = logGroupName
    }
}

public struct CreateLogSubscriptionOutput: Swift.Sendable {

    public init() { }
}

extension DirectoryClientTypes {

    public enum DirectoryEdition: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case enterprise
        case standard
        case sdkUnknown(Swift.String)

        public static var allCases: [DirectoryEdition] {
            return [
                .enterprise,
                .standard
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .enterprise: return "Enterprise"
            case .standard: return "Standard"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Creates an Managed Microsoft AD directory.
public struct CreateMicrosoftADInput: Swift.Sendable {
    /// A description for the directory. This label will appear on the Amazon Web Services console Directory Details page after the directory is created.
    public var description: Swift.String?
    /// Managed Microsoft AD is available in two editions: Standard and Enterprise. Enterprise is the default.
    public var edition: DirectoryClientTypes.DirectoryEdition?
    /// The fully qualified domain name for the Managed Microsoft AD directory, such as corp.example.com. This name will resolve inside your VPC only. It does not need to be publicly resolvable.
    /// This member is required.
    public var name: Swift.String?
    /// The password for the default administrative user named Admin. If you need to change the password for the administrator account, you can use the [ResetUserPassword] API call.
    /// This member is required.
    public var password: Swift.String?
    /// The NetBIOS name for your domain, such as CORP. If you don't specify a NetBIOS name, it will default to the first part of your directory DNS. For example, CORP for the directory DNS corp.example.com.
    public var shortName: Swift.String?
    /// The tags to be assigned to the Managed Microsoft AD directory.
    public var tags: [DirectoryClientTypes.Tag]?
    /// Contains VPC information for the [CreateDirectory] or [CreateMicrosoftAD] operation.
    /// This member is required.
    public var vpcSettings: DirectoryClientTypes.DirectoryVpcSettings?

    public init(
        description: Swift.String? = nil,
        edition: DirectoryClientTypes.DirectoryEdition? = nil,
        name: Swift.String? = nil,
        password: Swift.String? = nil,
        shortName: Swift.String? = nil,
        tags: [DirectoryClientTypes.Tag]? = nil,
        vpcSettings: DirectoryClientTypes.DirectoryVpcSettings? = nil
    )
    {
        self.description = description
        self.edition = edition
        self.name = name
        self.password = password
        self.shortName = shortName
        self.tags = tags
        self.vpcSettings = vpcSettings
    }
}

extension CreateMicrosoftADInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateMicrosoftADInput(description: \(Swift.String(describing: description)), edition: \(Swift.String(describing: edition)), name: \(Swift.String(describing: name)), shortName: \(Swift.String(describing: shortName)), tags: \(Swift.String(describing: tags)), vpcSettings: \(Swift.String(describing: vpcSettings)), password: \"CONTENT_REDACTED\")"}
}

/// Result of a CreateMicrosoftAD request.
public struct CreateMicrosoftADOutput: Swift.Sendable {
    /// The identifier of the directory that was created.
    public var directoryId: Swift.String?

    public init(
        directoryId: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
    }
}

/// The maximum number of manual snapshots for the directory has been reached. You can use the [GetSnapshotLimits] operation to determine the snapshot limits for a directory.
public struct SnapshotLimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SnapshotLimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// Contains the inputs for the [CreateSnapshot] operation.
public struct CreateSnapshotInput: Swift.Sendable {
    /// The identifier of the directory of which to take a snapshot.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The descriptive name to apply to the snapshot.
    public var name: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.name = name
    }
}

/// Contains the results of the [CreateSnapshot] operation.
public struct CreateSnapshotOutput: Swift.Sendable {
    /// The identifier of the snapshot that was created.
    public var snapshotId: Swift.String?

    public init(
        snapshotId: Swift.String? = nil
    )
    {
        self.snapshotId = snapshotId
    }
}

extension DirectoryClientTypes {

    public enum SelectiveAuth: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case enabled
        case sdkUnknown(Swift.String)

        public static var allCases: [SelectiveAuth] {
            return [
                .disabled,
                .enabled
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "Disabled"
            case .enabled: return "Enabled"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    public enum TrustDirection: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case oneWayIncoming
        case oneWayOutgoing
        case twoWay
        case sdkUnknown(Swift.String)

        public static var allCases: [TrustDirection] {
            return [
                .oneWayIncoming,
                .oneWayOutgoing,
                .twoWay
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .oneWayIncoming: return "One-Way: Incoming"
            case .oneWayOutgoing: return "One-Way: Outgoing"
            case .twoWay: return "Two-Way"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    public enum TrustType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case external
        case forest
        case sdkUnknown(Swift.String)

        public static var allCases: [TrustType] {
            return [
                .external,
                .forest
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .external: return "External"
            case .forest: return "Forest"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Directory Service for Microsoft Active Directory allows you to configure trust relationships. For example, you can establish a trust between your Managed Microsoft AD directory, and your existing self-managed Microsoft Active Directory. This would allow you to provide users and groups access to resources in either domain, with a single set of credentials. This action initiates the creation of the Amazon Web Services side of a trust relationship between an Managed Microsoft AD directory and an external domain.
public struct CreateTrustInput: Swift.Sendable {
    /// The IP addresses of the remote DNS server associated with RemoteDomainName.
    public var conditionalForwarderIpAddrs: [Swift.String]?
    /// The Directory ID of the Managed Microsoft AD directory for which to establish the trust relationship.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The Fully Qualified Domain Name (FQDN) of the external domain for which to create the trust relationship.
    /// This member is required.
    public var remoteDomainName: Swift.String?
    /// Optional parameter to enable selective authentication for the trust.
    public var selectiveAuth: DirectoryClientTypes.SelectiveAuth?
    /// The direction of the trust relationship.
    /// This member is required.
    public var trustDirection: DirectoryClientTypes.TrustDirection?
    /// The trust password. The trust password must be the same password that was used when creating the trust relationship on the external domain.
    /// This member is required.
    public var trustPassword: Swift.String?
    /// The trust relationship type. Forest is the default.
    public var trustType: DirectoryClientTypes.TrustType?

    public init(
        conditionalForwarderIpAddrs: [Swift.String]? = nil,
        directoryId: Swift.String? = nil,
        remoteDomainName: Swift.String? = nil,
        selectiveAuth: DirectoryClientTypes.SelectiveAuth? = nil,
        trustDirection: DirectoryClientTypes.TrustDirection? = nil,
        trustPassword: Swift.String? = nil,
        trustType: DirectoryClientTypes.TrustType? = nil
    )
    {
        self.conditionalForwarderIpAddrs = conditionalForwarderIpAddrs
        self.directoryId = directoryId
        self.remoteDomainName = remoteDomainName
        self.selectiveAuth = selectiveAuth
        self.trustDirection = trustDirection
        self.trustPassword = trustPassword
        self.trustType = trustType
    }
}

extension CreateTrustInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateTrustInput(conditionalForwarderIpAddrs: \(Swift.String(describing: conditionalForwarderIpAddrs)), directoryId: \(Swift.String(describing: directoryId)), remoteDomainName: \(Swift.String(describing: remoteDomainName)), selectiveAuth: \(Swift.String(describing: selectiveAuth)), trustDirection: \(Swift.String(describing: trustDirection)), trustType: \(Swift.String(describing: trustType)), trustPassword: \"CONTENT_REDACTED\")"}
}

/// The result of a CreateTrust request.
public struct CreateTrustOutput: Swift.Sendable {
    /// A unique identifier for the trust relationship that was created.
    public var trustId: Swift.String?

    public init(
        trustId: Swift.String? = nil
    )
    {
        self.trustId = trustId
    }
}

extension DirectoryClientTypes {

    public enum DataAccessStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case disabling
        case enabled
        case enabling
        case failed
        case sdkUnknown(Swift.String)

        public static var allCases: [DataAccessStatus] {
            return [
                .disabled,
                .disabling,
                .enabled,
                .enabling,
                .failed
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "Disabled"
            case .disabling: return "Disabling"
            case .enabled: return "Enabled"
            case .enabling: return "Enabling"
            case .failed: return "Failed"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Deletes a conditional forwarder.
public struct DeleteConditionalForwarderInput: Swift.Sendable {
    /// The directory ID for which you are deleting the conditional forwarder.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The fully qualified domain name (FQDN) of the remote domain with which you are deleting the conditional forwarder.
    /// This member is required.
    public var remoteDomainName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        remoteDomainName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.remoteDomainName = remoteDomainName
    }
}

/// The result of a DeleteConditionalForwarder request.
public struct DeleteConditionalForwarderOutput: Swift.Sendable {

    public init() { }
}

/// Contains the inputs for the [DeleteDirectory] operation.
public struct DeleteDirectoryInput: Swift.Sendable {
    /// The identifier of the directory to delete.
    /// This member is required.
    public var directoryId: Swift.String?

    public init(
        directoryId: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
    }
}

/// Contains the results of the [DeleteDirectory] operation.
public struct DeleteDirectoryOutput: Swift.Sendable {
    /// The directory identifier.
    public var directoryId: Swift.String?

    public init(
        directoryId: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
    }
}

public struct DeleteLogSubscriptionInput: Swift.Sendable {
    /// Identifier of the directory whose log subscription you want to delete.
    /// This member is required.
    public var directoryId: Swift.String?

    public init(
        directoryId: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
    }
}

public struct DeleteLogSubscriptionOutput: Swift.Sendable {

    public init() { }
}

/// Contains the inputs for the [DeleteSnapshot] operation.
public struct DeleteSnapshotInput: Swift.Sendable {
    /// The identifier of the directory snapshot to be deleted.
    /// This member is required.
    public var snapshotId: Swift.String?

    public init(
        snapshotId: Swift.String? = nil
    )
    {
        self.snapshotId = snapshotId
    }
}

/// Contains the results of the [DeleteSnapshot] operation.
public struct DeleteSnapshotOutput: Swift.Sendable {
    /// The identifier of the directory snapshot that was deleted.
    public var snapshotId: Swift.String?

    public init(
        snapshotId: Swift.String? = nil
    )
    {
        self.snapshotId = snapshotId
    }
}

/// Deletes the local side of an existing trust relationship between the Managed Microsoft AD directory and the external domain.
public struct DeleteTrustInput: Swift.Sendable {
    /// Delete a conditional forwarder as part of a DeleteTrustRequest.
    public var deleteAssociatedConditionalForwarder: Swift.Bool?
    /// The Trust ID of the trust relationship to be deleted.
    /// This member is required.
    public var trustId: Swift.String?

    public init(
        deleteAssociatedConditionalForwarder: Swift.Bool? = false,
        trustId: Swift.String? = nil
    )
    {
        self.deleteAssociatedConditionalForwarder = deleteAssociatedConditionalForwarder
        self.trustId = trustId
    }
}

/// The result of a DeleteTrust request.
public struct DeleteTrustOutput: Swift.Sendable {
    /// The Trust ID of the trust relationship that was deleted.
    public var trustId: Swift.String?

    public init(
        trustId: Swift.String? = nil
    )
    {
        self.trustId = trustId
    }
}

public struct DeregisterCertificateInput: Swift.Sendable {
    /// The identifier of the certificate.
    /// This member is required.
    public var certificateId: Swift.String?
    /// The identifier of the directory.
    /// This member is required.
    public var directoryId: Swift.String?

    public init(
        certificateId: Swift.String? = nil,
        directoryId: Swift.String? = nil
    )
    {
        self.certificateId = certificateId
        self.directoryId = directoryId
    }
}

public struct DeregisterCertificateOutput: Swift.Sendable {

    public init() { }
}

/// Removes the specified directory as a publisher to the specified Amazon SNS topic.
public struct DeregisterEventTopicInput: Swift.Sendable {
    /// The Directory ID to remove as a publisher. This directory will no longer send messages to the specified Amazon SNS topic.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The name of the Amazon SNS topic from which to remove the directory as a publisher.
    /// This member is required.
    public var topicName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        topicName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.topicName = topicName
    }
}

/// The result of a DeregisterEventTopic request.
public struct DeregisterEventTopicOutput: Swift.Sendable {

    public init() { }
}

public struct DescribeCertificateInput: Swift.Sendable {
    /// The identifier of the certificate.
    /// This member is required.
    public var certificateId: Swift.String?
    /// The identifier of the directory.
    /// This member is required.
    public var directoryId: Swift.String?

    public init(
        certificateId: Swift.String? = nil,
        directoryId: Swift.String? = nil
    )
    {
        self.certificateId = certificateId
        self.directoryId = directoryId
    }
}

public struct DescribeCertificateOutput: Swift.Sendable {
    /// Information about the certificate, including registered date time, certificate state, the reason for the state, expiration date time, and certificate common name.
    public var certificate: DirectoryClientTypes.Certificate?

    public init(
        certificate: DirectoryClientTypes.Certificate? = nil
    )
    {
        self.certificate = certificate
    }
}

public struct DescribeClientAuthenticationSettingsInput: Swift.Sendable {
    /// The identifier of the directory for which to retrieve information.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The maximum number of items to return. If this value is zero, the maximum number of items is specified by the limitations of the operation.
    public var limit: Swift.Int?
    /// The DescribeClientAuthenticationSettingsResult.NextToken value from a previous call to [DescribeClientAuthenticationSettings]. Pass null if this is the first call.
    public var nextToken: Swift.String?
    /// The type of client authentication for which to retrieve information. If no type is specified, a list of all client authentication types that are supported for the specified directory is retrieved.
    public var type: DirectoryClientTypes.ClientAuthenticationType?

    public init(
        directoryId: Swift.String? = nil,
        limit: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        type: DirectoryClientTypes.ClientAuthenticationType? = nil
    )
    {
        self.directoryId = directoryId
        self.limit = limit
        self.nextToken = nextToken
        self.type = type
    }
}

public struct DescribeClientAuthenticationSettingsOutput: Swift.Sendable {
    /// Information about the type of client authentication for the specified directory. The following information is retrieved: The date and time when the status of the client authentication type was last updated, whether the client authentication type is enabled or disabled, and the type of client authentication.
    public var clientAuthenticationSettingsInfo: [DirectoryClientTypes.ClientAuthenticationSettingInfo]?
    /// The next token used to retrieve the client authentication settings if the number of setting types exceeds page limit and there is another page.
    public var nextToken: Swift.String?

    public init(
        clientAuthenticationSettingsInfo: [DirectoryClientTypes.ClientAuthenticationSettingInfo]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.clientAuthenticationSettingsInfo = clientAuthenticationSettingsInfo
        self.nextToken = nextToken
    }
}

/// Describes a conditional forwarder.
public struct DescribeConditionalForwardersInput: Swift.Sendable {
    /// The directory ID for which to get the list of associated conditional forwarders.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The fully qualified domain names (FQDN) of the remote domains for which to get the list of associated conditional forwarders. If this member is null, all conditional forwarders are returned.
    public var remoteDomainNames: [Swift.String]?

    public init(
        directoryId: Swift.String? = nil,
        remoteDomainNames: [Swift.String]? = nil
    )
    {
        self.directoryId = directoryId
        self.remoteDomainNames = remoteDomainNames
    }
}

/// The result of a DescribeConditionalForwarder request.
public struct DescribeConditionalForwardersOutput: Swift.Sendable {
    /// The list of conditional forwarders that have been created.
    public var conditionalForwarders: [DirectoryClientTypes.ConditionalForwarder]?

    public init(
        conditionalForwarders: [DirectoryClientTypes.ConditionalForwarder]? = nil
    )
    {
        self.conditionalForwarders = conditionalForwarders
    }
}

/// The NextToken value is not valid.
public struct InvalidNextTokenException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidNextTokenException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// Contains the inputs for the [DescribeDirectories] operation.
public struct DescribeDirectoriesInput: Swift.Sendable {
    /// A list of identifiers of the directories for which to obtain the information. If this member is null, all directories that belong to the current account are returned. An empty list results in an InvalidParameterException being thrown.
    public var directoryIds: [Swift.String]?
    /// The maximum number of items to return. If this value is zero, the maximum number of items is specified by the limitations of the operation.
    public var limit: Swift.Int?
    /// The DescribeDirectoriesResult.NextToken value from a previous call to [DescribeDirectories]. Pass null if this is the first call.
    public var nextToken: Swift.String?

    public init(
        directoryIds: [Swift.String]? = nil,
        limit: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.directoryIds = directoryIds
        self.limit = limit
        self.nextToken = nextToken
    }
}

extension DirectoryClientTypes {

    /// Contains information about an AD Connector directory.
    public struct DirectoryConnectSettingsDescription: Swift.Sendable {
        /// A list of the Availability Zones that the directory is in.
        public var availabilityZones: [Swift.String]?
        /// The IP addresses of the AD Connector servers.
        public var connectIps: [Swift.String]?
        /// The user name of the service account in your self-managed directory.
        public var customerUserName: Swift.String?
        /// The security group identifier for the AD Connector directory.
        public var securityGroupId: Swift.String?
        /// A list of subnet identifiers in the VPC that the AD Connector is in.
        public var subnetIds: [Swift.String]?
        /// The identifier of the VPC that the AD Connector is in.
        public var vpcId: Swift.String?

        public init(
            availabilityZones: [Swift.String]? = nil,
            connectIps: [Swift.String]? = nil,
            customerUserName: Swift.String? = nil,
            securityGroupId: Swift.String? = nil,
            subnetIds: [Swift.String]? = nil,
            vpcId: Swift.String? = nil
        )
        {
            self.availabilityZones = availabilityZones
            self.connectIps = connectIps
            self.customerUserName = customerUserName
            self.securityGroupId = securityGroupId
            self.subnetIds = subnetIds
            self.vpcId = vpcId
        }
    }
}

extension DirectoryClientTypes {

    public enum OSVersion: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case version2012
        case version2019
        case sdkUnknown(Swift.String)

        public static var allCases: [OSVersion] {
            return [
                .version2012,
                .version2019
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .version2012: return "SERVER_2012"
            case .version2019: return "SERVER_2019"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    public enum RadiusAuthenticationProtocol: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case chap
        case mschapv1
        case mschapv2
        case pap
        case sdkUnknown(Swift.String)

        public static var allCases: [RadiusAuthenticationProtocol] {
            return [
                .chap,
                .mschapv1,
                .mschapv2,
                .pap
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .chap: return "CHAP"
            case .mschapv1: return "MS-CHAPv1"
            case .mschapv2: return "MS-CHAPv2"
            case .pap: return "PAP"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Contains information about a Remote Authentication Dial In User Service (RADIUS) server.
    public struct RadiusSettings: Swift.Sendable {
        /// The protocol specified for your RADIUS endpoints.
        public var authenticationProtocol: DirectoryClientTypes.RadiusAuthenticationProtocol?
        /// Not currently used.
        public var displayLabel: Swift.String?
        /// The port that your RADIUS server is using for communications. Your self-managed network must allow inbound traffic over this port from the Directory Service servers.
        public var radiusPort: Swift.Int?
        /// The maximum number of times that communication with the RADIUS server is retried after the initial attempt.
        public var radiusRetries: Swift.Int
        /// An array of strings that contains the fully qualified domain name (FQDN) or IP addresses of the RADIUS server endpoints, or the FQDN or IP addresses of your RADIUS server load balancer.
        public var radiusServers: [Swift.String]?
        /// The amount of time, in seconds, to wait for the RADIUS server to respond.
        public var radiusTimeout: Swift.Int?
        /// Required for enabling RADIUS on the directory.
        public var sharedSecret: Swift.String?
        /// Not currently used.
        public var useSameUsername: Swift.Bool

        public init(
            authenticationProtocol: DirectoryClientTypes.RadiusAuthenticationProtocol? = nil,
            displayLabel: Swift.String? = nil,
            radiusPort: Swift.Int? = nil,
            radiusRetries: Swift.Int = 0,
            radiusServers: [Swift.String]? = nil,
            radiusTimeout: Swift.Int? = nil,
            sharedSecret: Swift.String? = nil,
            useSameUsername: Swift.Bool = false
        )
        {
            self.authenticationProtocol = authenticationProtocol
            self.displayLabel = displayLabel
            self.radiusPort = radiusPort
            self.radiusRetries = radiusRetries
            self.radiusServers = radiusServers
            self.radiusTimeout = radiusTimeout
            self.sharedSecret = sharedSecret
            self.useSameUsername = useSameUsername
        }
    }
}

extension DirectoryClientTypes.RadiusSettings: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "RadiusSettings(authenticationProtocol: \(Swift.String(describing: authenticationProtocol)), displayLabel: \(Swift.String(describing: displayLabel)), radiusPort: \(Swift.String(describing: radiusPort)), radiusRetries: \(Swift.String(describing: radiusRetries)), radiusServers: \(Swift.String(describing: radiusServers)), radiusTimeout: \(Swift.String(describing: radiusTimeout)), useSameUsername: \(Swift.String(describing: useSameUsername)), sharedSecret: \"CONTENT_REDACTED\")"}
}

extension DirectoryClientTypes {

    public enum RadiusStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case completed
        case creating
        case failed
        case sdkUnknown(Swift.String)

        public static var allCases: [RadiusStatus] {
            return [
                .completed,
                .creating,
                .failed
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .completed: return "Completed"
            case .creating: return "Creating"
            case .failed: return "Failed"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Contains information about the directory.
    public struct DirectoryVpcSettingsDescription: Swift.Sendable {
        /// The list of Availability Zones that the directory is in.
        public var availabilityZones: [Swift.String]?
        /// The domain controller security group identifier for the directory.
        public var securityGroupId: Swift.String?
        /// The identifiers of the subnets for the directory servers.
        public var subnetIds: [Swift.String]?
        /// The identifier of the VPC that the directory is in.
        public var vpcId: Swift.String?

        public init(
            availabilityZones: [Swift.String]? = nil,
            securityGroupId: Swift.String? = nil,
            subnetIds: [Swift.String]? = nil,
            vpcId: Swift.String? = nil
        )
        {
            self.availabilityZones = availabilityZones
            self.securityGroupId = securityGroupId
            self.subnetIds = subnetIds
            self.vpcId = vpcId
        }
    }
}

extension DirectoryClientTypes {

    /// Describes the directory owner account details that have been shared to the directory consumer account.
    public struct OwnerDirectoryDescription: Swift.Sendable {
        /// Identifier of the directory owner account.
        public var accountId: Swift.String?
        /// Identifier of the Managed Microsoft AD directory in the directory owner account.
        public var directoryId: Swift.String?
        /// IP address of the directory’s domain controllers.
        public var dnsIpAddrs: [Swift.String]?
        /// A [RadiusSettings] object that contains information about the RADIUS server.
        public var radiusSettings: DirectoryClientTypes.RadiusSettings?
        /// Information about the status of the RADIUS server.
        public var radiusStatus: DirectoryClientTypes.RadiusStatus?
        /// Information about the VPC settings for the directory.
        public var vpcSettings: DirectoryClientTypes.DirectoryVpcSettingsDescription?

        public init(
            accountId: Swift.String? = nil,
            directoryId: Swift.String? = nil,
            dnsIpAddrs: [Swift.String]? = nil,
            radiusSettings: DirectoryClientTypes.RadiusSettings? = nil,
            radiusStatus: DirectoryClientTypes.RadiusStatus? = nil,
            vpcSettings: DirectoryClientTypes.DirectoryVpcSettingsDescription? = nil
        )
        {
            self.accountId = accountId
            self.directoryId = directoryId
            self.dnsIpAddrs = dnsIpAddrs
            self.radiusSettings = radiusSettings
            self.radiusStatus = radiusStatus
            self.vpcSettings = vpcSettings
        }
    }
}

extension DirectoryClientTypes {

    /// Provides information about the Regions that are configured for multi-Region replication.
    public struct RegionsInfo: Swift.Sendable {
        /// Lists the Regions where the directory has been replicated, excluding the primary Region.
        public var additionalRegions: [Swift.String]?
        /// The Region where the Managed Microsoft AD directory was originally created.
        public var primaryRegion: Swift.String?

        public init(
            additionalRegions: [Swift.String]? = nil,
            primaryRegion: Swift.String? = nil
        )
        {
            self.additionalRegions = additionalRegions
            self.primaryRegion = primaryRegion
        }
    }
}

extension DirectoryClientTypes {

    public enum DirectoryStage: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case active
        case created
        case creating
        case deleted
        case deleting
        case failed
        case impaired
        case inoperable
        case requested
        case restorefailed
        case restoring
        case updating
        case sdkUnknown(Swift.String)

        public static var allCases: [DirectoryStage] {
            return [
                .active,
                .created,
                .creating,
                .deleted,
                .deleting,
                .failed,
                .impaired,
                .inoperable,
                .requested,
                .restorefailed,
                .restoring,
                .updating
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .active: return "Active"
            case .created: return "Created"
            case .creating: return "Creating"
            case .deleted: return "Deleted"
            case .deleting: return "Deleting"
            case .failed: return "Failed"
            case .impaired: return "Impaired"
            case .inoperable: return "Inoperable"
            case .requested: return "Requested"
            case .restorefailed: return "RestoreFailed"
            case .restoring: return "Restoring"
            case .updating: return "Updating"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    public enum DirectoryType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case adConnector
        case microsoftAd
        case sharedMicrosoftAd
        case simpleAd
        case sdkUnknown(Swift.String)

        public static var allCases: [DirectoryType] {
            return [
                .adConnector,
                .microsoftAd,
                .sharedMicrosoftAd,
                .simpleAd
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .adConnector: return "ADConnector"
            case .microsoftAd: return "MicrosoftAD"
            case .sharedMicrosoftAd: return "SharedMicrosoftAD"
            case .simpleAd: return "SimpleAD"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Contains information about an Directory Service directory.
    public struct DirectoryDescription: Swift.Sendable {
        /// The access URL for the directory, such as http://.awsapps.com. If no alias has been created for the directory,  is the directory identifier, such as d-XXXXXXXXXX.
        public var accessUrl: Swift.String?
        /// The alias for the directory. If no alias has been created for the directory, the alias is the directory identifier, such as d-XXXXXXXXXX.
        public var alias: Swift.String?
        /// A [DirectoryConnectSettingsDescription] object that contains additional information about an AD Connector directory. This member is only present if the directory is an AD Connector directory.
        public var connectSettings: DirectoryClientTypes.DirectoryConnectSettingsDescription?
        /// The description for the directory.
        public var description: Swift.String?
        /// The desired number of domain controllers in the directory if the directory is Microsoft AD.
        public var desiredNumberOfDomainControllers: Swift.Int?
        /// The directory identifier.
        public var directoryId: Swift.String?
        /// The IP addresses of the DNS servers for the directory. For a Simple AD or Microsoft AD directory, these are the IP addresses of the Simple AD or Microsoft AD directory servers. For an AD Connector directory, these are the IP addresses of the DNS servers or domain controllers in your self-managed directory to which the AD Connector is connected.
        public var dnsIpAddrs: [Swift.String]?
        /// The edition associated with this directory.
        public var edition: DirectoryClientTypes.DirectoryEdition?
        /// Specifies when the directory was created.
        public var launchTime: Foundation.Date?
        /// The fully qualified name of the directory.
        public var name: Swift.String?
        /// The operating system (OS) version of the directory.
        public var osVersion: DirectoryClientTypes.OSVersion?
        /// Describes the Managed Microsoft AD directory in the directory owner account.
        public var ownerDirectoryDescription: DirectoryClientTypes.OwnerDirectoryDescription?
        /// A [RadiusSettings] object that contains information about the RADIUS server configured for this directory.
        public var radiusSettings: DirectoryClientTypes.RadiusSettings?
        /// The status of the RADIUS MFA server connection.
        public var radiusStatus: DirectoryClientTypes.RadiusStatus?
        /// Lists the Regions where the directory has replicated.
        public var regionsInfo: DirectoryClientTypes.RegionsInfo?
        /// The method used when sharing a directory to determine whether the directory should be shared within your Amazon Web Services organization (ORGANIZATIONS) or with any Amazon Web Services account by sending a shared directory request (HANDSHAKE).
        public var shareMethod: DirectoryClientTypes.ShareMethod?
        /// A directory share request that is sent by the directory owner to the directory consumer. The request includes a typed message to help the directory consumer administrator determine whether to approve or reject the share invitation.
        public var shareNotes: Swift.String?
        /// Current directory status of the shared Managed Microsoft AD directory.
        public var shareStatus: DirectoryClientTypes.ShareStatus?
        /// The short name of the directory.
        public var shortName: Swift.String?
        /// The directory size.
        public var size: DirectoryClientTypes.DirectorySize?
        /// Indicates if single sign-on is enabled for the directory. For more information, see [EnableSso] and [DisableSso].
        public var ssoEnabled: Swift.Bool
        /// The current stage of the directory.
        public var stage: DirectoryClientTypes.DirectoryStage?
        /// The date and time that the stage was last updated.
        public var stageLastUpdatedDateTime: Foundation.Date?
        /// Additional information about the directory stage.
        public var stageReason: Swift.String?
        /// The directory type.
        public var type: DirectoryClientTypes.DirectoryType?
        /// A [DirectoryVpcSettingsDescription] object that contains additional information about a directory. This member is only present if the directory is a Simple AD or Managed Microsoft AD directory.
        public var vpcSettings: DirectoryClientTypes.DirectoryVpcSettingsDescription?

        public init(
            accessUrl: Swift.String? = nil,
            alias: Swift.String? = nil,
            connectSettings: DirectoryClientTypes.DirectoryConnectSettingsDescription? = nil,
            description: Swift.String? = nil,
            desiredNumberOfDomainControllers: Swift.Int? = nil,
            directoryId: Swift.String? = nil,
            dnsIpAddrs: [Swift.String]? = nil,
            edition: DirectoryClientTypes.DirectoryEdition? = nil,
            launchTime: Foundation.Date? = nil,
            name: Swift.String? = nil,
            osVersion: DirectoryClientTypes.OSVersion? = nil,
            ownerDirectoryDescription: DirectoryClientTypes.OwnerDirectoryDescription? = nil,
            radiusSettings: DirectoryClientTypes.RadiusSettings? = nil,
            radiusStatus: DirectoryClientTypes.RadiusStatus? = nil,
            regionsInfo: DirectoryClientTypes.RegionsInfo? = nil,
            shareMethod: DirectoryClientTypes.ShareMethod? = nil,
            shareNotes: Swift.String? = nil,
            shareStatus: DirectoryClientTypes.ShareStatus? = nil,
            shortName: Swift.String? = nil,
            size: DirectoryClientTypes.DirectorySize? = nil,
            ssoEnabled: Swift.Bool = false,
            stage: DirectoryClientTypes.DirectoryStage? = nil,
            stageLastUpdatedDateTime: Foundation.Date? = nil,
            stageReason: Swift.String? = nil,
            type: DirectoryClientTypes.DirectoryType? = nil,
            vpcSettings: DirectoryClientTypes.DirectoryVpcSettingsDescription? = nil
        )
        {
            self.accessUrl = accessUrl
            self.alias = alias
            self.connectSettings = connectSettings
            self.description = description
            self.desiredNumberOfDomainControllers = desiredNumberOfDomainControllers
            self.directoryId = directoryId
            self.dnsIpAddrs = dnsIpAddrs
            self.edition = edition
            self.launchTime = launchTime
            self.name = name
            self.osVersion = osVersion
            self.ownerDirectoryDescription = ownerDirectoryDescription
            self.radiusSettings = radiusSettings
            self.radiusStatus = radiusStatus
            self.regionsInfo = regionsInfo
            self.shareMethod = shareMethod
            self.shareNotes = shareNotes
            self.shareStatus = shareStatus
            self.shortName = shortName
            self.size = size
            self.ssoEnabled = ssoEnabled
            self.stage = stage
            self.stageLastUpdatedDateTime = stageLastUpdatedDateTime
            self.stageReason = stageReason
            self.type = type
            self.vpcSettings = vpcSettings
        }
    }
}

extension DirectoryClientTypes.DirectoryDescription: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "DirectoryDescription(accessUrl: \(Swift.String(describing: accessUrl)), alias: \(Swift.String(describing: alias)), connectSettings: \(Swift.String(describing: connectSettings)), description: \(Swift.String(describing: description)), desiredNumberOfDomainControllers: \(Swift.String(describing: desiredNumberOfDomainControllers)), directoryId: \(Swift.String(describing: directoryId)), dnsIpAddrs: \(Swift.String(describing: dnsIpAddrs)), edition: \(Swift.String(describing: edition)), launchTime: \(Swift.String(describing: launchTime)), name: \(Swift.String(describing: name)), osVersion: \(Swift.String(describing: osVersion)), ownerDirectoryDescription: \(Swift.String(describing: ownerDirectoryDescription)), radiusSettings: \(Swift.String(describing: radiusSettings)), radiusStatus: \(Swift.String(describing: radiusStatus)), regionsInfo: \(Swift.String(describing: regionsInfo)), shareMethod: \(Swift.String(describing: shareMethod)), shareStatus: \(Swift.String(describing: shareStatus)), shortName: \(Swift.String(describing: shortName)), size: \(Swift.String(describing: size)), ssoEnabled: \(Swift.String(describing: ssoEnabled)), stage: \(Swift.String(describing: stage)), stageLastUpdatedDateTime: \(Swift.String(describing: stageLastUpdatedDateTime)), stageReason: \(Swift.String(describing: stageReason)), type: \(Swift.String(describing: type)), vpcSettings: \(Swift.String(describing: vpcSettings)), shareNotes: \"CONTENT_REDACTED\")"}
}

/// Contains the results of the [DescribeDirectories] operation.
public struct DescribeDirectoriesOutput: Swift.Sendable {
    /// The list of [DirectoryDescription] objects that were retrieved. It is possible that this list contains less than the number of items specified in the Limit member of the request. This occurs if there are less than the requested number of items left to retrieve, or if the limitations of the operation have been exceeded.
    public var directoryDescriptions: [DirectoryClientTypes.DirectoryDescription]?
    /// If not null, more results are available. Pass this value for the NextToken parameter in a subsequent call to [DescribeDirectories] to retrieve the next set of items.
    public var nextToken: Swift.String?

    public init(
        directoryDescriptions: [DirectoryClientTypes.DirectoryDescription]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.directoryDescriptions = directoryDescriptions
        self.nextToken = nextToken
    }
}

public struct DescribeDirectoryDataAccessInput: Swift.Sendable {
    /// The directory identifier.
    /// This member is required.
    public var directoryId: Swift.String?

    public init(
        directoryId: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
    }
}

public struct DescribeDirectoryDataAccessOutput: Swift.Sendable {
    /// The current status of data access through the Directory Service Data API.
    public var dataAccessStatus: DirectoryClientTypes.DataAccessStatus?

    public init(
        dataAccessStatus: DirectoryClientTypes.DataAccessStatus? = nil
    )
    {
        self.dataAccessStatus = dataAccessStatus
    }
}

public struct DescribeDomainControllersInput: Swift.Sendable {
    /// Identifier of the directory for which to retrieve the domain controller information.
    /// This member is required.
    public var directoryId: Swift.String?
    /// A list of identifiers for the domain controllers whose information will be provided.
    public var domainControllerIds: [Swift.String]?
    /// The maximum number of items to return.
    public var limit: Swift.Int?
    /// The DescribeDomainControllers.NextToken value from a previous call to [DescribeDomainControllers]. Pass null if this is the first call.
    public var nextToken: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        domainControllerIds: [Swift.String]? = nil,
        limit: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.domainControllerIds = domainControllerIds
        self.limit = limit
        self.nextToken = nextToken
    }
}

extension DirectoryClientTypes {

    public enum DomainControllerStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case active
        case creating
        case deleted
        case deleting
        case failed
        case impaired
        case restoring
        case updating
        case sdkUnknown(Swift.String)

        public static var allCases: [DomainControllerStatus] {
            return [
                .active,
                .creating,
                .deleted,
                .deleting,
                .failed,
                .impaired,
                .restoring,
                .updating
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .active: return "Active"
            case .creating: return "Creating"
            case .deleted: return "Deleted"
            case .deleting: return "Deleting"
            case .failed: return "Failed"
            case .impaired: return "Impaired"
            case .restoring: return "Restoring"
            case .updating: return "Updating"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Contains information about the domain controllers for a specified directory.
    public struct DomainController: Swift.Sendable {
        /// The Availability Zone where the domain controller is located.
        public var availabilityZone: Swift.String?
        /// Identifier of the directory where the domain controller resides.
        public var directoryId: Swift.String?
        /// The IP address of the domain controller.
        public var dnsIpAddr: Swift.String?
        /// Identifies a specific domain controller in the directory.
        public var domainControllerId: Swift.String?
        /// Specifies when the domain controller was created.
        public var launchTime: Foundation.Date?
        /// The status of the domain controller.
        public var status: DirectoryClientTypes.DomainControllerStatus?
        /// The date and time that the status was last updated.
        public var statusLastUpdatedDateTime: Foundation.Date?
        /// A description of the domain controller state.
        public var statusReason: Swift.String?
        /// Identifier of the subnet in the VPC that contains the domain controller.
        public var subnetId: Swift.String?
        /// The identifier of the VPC that contains the domain controller.
        public var vpcId: Swift.String?

        public init(
            availabilityZone: Swift.String? = nil,
            directoryId: Swift.String? = nil,
            dnsIpAddr: Swift.String? = nil,
            domainControllerId: Swift.String? = nil,
            launchTime: Foundation.Date? = nil,
            status: DirectoryClientTypes.DomainControllerStatus? = nil,
            statusLastUpdatedDateTime: Foundation.Date? = nil,
            statusReason: Swift.String? = nil,
            subnetId: Swift.String? = nil,
            vpcId: Swift.String? = nil
        )
        {
            self.availabilityZone = availabilityZone
            self.directoryId = directoryId
            self.dnsIpAddr = dnsIpAddr
            self.domainControllerId = domainControllerId
            self.launchTime = launchTime
            self.status = status
            self.statusLastUpdatedDateTime = statusLastUpdatedDateTime
            self.statusReason = statusReason
            self.subnetId = subnetId
            self.vpcId = vpcId
        }
    }
}

public struct DescribeDomainControllersOutput: Swift.Sendable {
    /// List of the [DomainController] objects that were retrieved.
    public var domainControllers: [DirectoryClientTypes.DomainController]?
    /// If not null, more results are available. Pass this value for the NextToken parameter in a subsequent call to [DescribeDomainControllers] retrieve the next set of items.
    public var nextToken: Swift.String?

    public init(
        domainControllers: [DirectoryClientTypes.DomainController]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.domainControllers = domainControllers
        self.nextToken = nextToken
    }
}

/// Describes event topics.
public struct DescribeEventTopicsInput: Swift.Sendable {
    /// The Directory ID for which to get the list of associated Amazon SNS topics. If this member is null, associations for all Directory IDs are returned.
    public var directoryId: Swift.String?
    /// A list of Amazon SNS topic names for which to obtain the information. If this member is null, all associations for the specified Directory ID are returned. An empty list results in an InvalidParameterException being thrown.
    public var topicNames: [Swift.String]?

    public init(
        directoryId: Swift.String? = nil,
        topicNames: [Swift.String]? = nil
    )
    {
        self.directoryId = directoryId
        self.topicNames = topicNames
    }
}

extension DirectoryClientTypes {

    public enum TopicStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case deleted
        case failed
        case registered
        case topicNotFound
        case sdkUnknown(Swift.String)

        public static var allCases: [TopicStatus] {
            return [
                .deleted,
                .failed,
                .registered,
                .topicNotFound
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .deleted: return "Deleted"
            case .failed: return "Failed"
            case .registered: return "Registered"
            case .topicNotFound: return "Topic not found"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Information about Amazon SNS topic and Directory Service directory associations.
    public struct EventTopic: Swift.Sendable {
        /// The date and time of when you associated your directory with the Amazon SNS topic.
        public var createdDateTime: Foundation.Date?
        /// The Directory ID of an Directory Service directory that will publish status messages to an Amazon SNS topic.
        public var directoryId: Swift.String?
        /// The topic registration status.
        public var status: DirectoryClientTypes.TopicStatus?
        /// The Amazon SNS topic ARN (Amazon Resource Name).
        public var topicArn: Swift.String?
        /// The name of an Amazon SNS topic the receives status messages from the directory.
        public var topicName: Swift.String?

        public init(
            createdDateTime: Foundation.Date? = nil,
            directoryId: Swift.String? = nil,
            status: DirectoryClientTypes.TopicStatus? = nil,
            topicArn: Swift.String? = nil,
            topicName: Swift.String? = nil
        )
        {
            self.createdDateTime = createdDateTime
            self.directoryId = directoryId
            self.status = status
            self.topicArn = topicArn
            self.topicName = topicName
        }
    }
}

/// The result of a DescribeEventTopic request.
public struct DescribeEventTopicsOutput: Swift.Sendable {
    /// A list of Amazon SNS topic names that receive status messages from the specified Directory ID.
    public var eventTopics: [DirectoryClientTypes.EventTopic]?

    public init(
        eventTopics: [DirectoryClientTypes.EventTopic]? = nil
    )
    {
        self.eventTopics = eventTopics
    }
}

extension DirectoryClientTypes {

    public enum LDAPSType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case client
        case sdkUnknown(Swift.String)

        public static var allCases: [LDAPSType] {
            return [
                .client
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .client: return "Client"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeLDAPSSettingsInput: Swift.Sendable {
    /// The identifier of the directory.
    /// This member is required.
    public var directoryId: Swift.String?
    /// Specifies the number of items that should be displayed on one page.
    public var limit: Swift.Int?
    /// The type of next token used for pagination.
    public var nextToken: Swift.String?
    /// The type of LDAP security to enable. Currently only the value Client is supported.
    public var type: DirectoryClientTypes.LDAPSType?

    public init(
        directoryId: Swift.String? = nil,
        limit: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        type: DirectoryClientTypes.LDAPSType? = nil
    )
    {
        self.directoryId = directoryId
        self.limit = limit
        self.nextToken = nextToken
        self.type = type
    }
}

extension DirectoryClientTypes {

    public enum LDAPSStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case enabled
        case enableFailed
        case enabling
        case sdkUnknown(Swift.String)

        public static var allCases: [LDAPSStatus] {
            return [
                .disabled,
                .enabled,
                .enableFailed,
                .enabling
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "Disabled"
            case .enabled: return "Enabled"
            case .enableFailed: return "EnableFailed"
            case .enabling: return "Enabling"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Contains general information about the LDAPS settings.
    public struct LDAPSSettingInfo: Swift.Sendable {
        /// The date and time when the LDAPS settings were last updated.
        public var lastUpdatedDateTime: Foundation.Date?
        /// The state of the LDAPS settings.
        public var ldapsStatus: DirectoryClientTypes.LDAPSStatus?
        /// Describes a state change for LDAPS.
        public var ldapsStatusReason: Swift.String?

        public init(
            lastUpdatedDateTime: Foundation.Date? = nil,
            ldapsStatus: DirectoryClientTypes.LDAPSStatus? = nil,
            ldapsStatusReason: Swift.String? = nil
        )
        {
            self.lastUpdatedDateTime = lastUpdatedDateTime
            self.ldapsStatus = ldapsStatus
            self.ldapsStatusReason = ldapsStatusReason
        }
    }
}

public struct DescribeLDAPSSettingsOutput: Swift.Sendable {
    /// Information about LDAP security for the specified directory, including status of enablement, state last updated date time, and the reason for the state.
    public var ldapsSettingsInfo: [DirectoryClientTypes.LDAPSSettingInfo]?
    /// The next token used to retrieve the LDAPS settings if the number of setting types exceeds page limit and there is another page.
    public var nextToken: Swift.String?

    public init(
        ldapsSettingsInfo: [DirectoryClientTypes.LDAPSSettingInfo]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.ldapsSettingsInfo = ldapsSettingsInfo
        self.nextToken = nextToken
    }
}

public struct DescribeRegionsInput: Swift.Sendable {
    /// The identifier of the directory.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The DescribeRegionsResult.NextToken value from a previous call to [DescribeRegions]. Pass null if this is the first call.
    public var nextToken: Swift.String?
    /// The name of the Region. For example, us-east-1.
    public var regionName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        regionName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.nextToken = nextToken
        self.regionName = regionName
    }
}

extension DirectoryClientTypes {

    public enum RegionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case additional
        case primary
        case sdkUnknown(Swift.String)

        public static var allCases: [RegionType] {
            return [
                .additional,
                .primary
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .additional: return "Additional"
            case .primary: return "Primary"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// The replicated Region information for a directory.
    public struct RegionDescription: Swift.Sendable {
        /// The desired number of domain controllers in the specified Region for the specified directory.
        public var desiredNumberOfDomainControllers: Swift.Int?
        /// The identifier of the directory.
        public var directoryId: Swift.String?
        /// The date and time that the Region description was last updated.
        public var lastUpdatedDateTime: Foundation.Date?
        /// Specifies when the Region replication began.
        public var launchTime: Foundation.Date?
        /// The name of the Region. For example, us-east-1.
        public var regionName: Swift.String?
        /// Specifies whether the Region is the primary Region or an additional Region.
        public var regionType: DirectoryClientTypes.RegionType?
        /// The status of the replication process for the specified Region.
        public var status: DirectoryClientTypes.DirectoryStage?
        /// The date and time that the Region status was last updated.
        public var statusLastUpdatedDateTime: Foundation.Date?
        /// Contains VPC information for the [CreateDirectory] or [CreateMicrosoftAD] operation.
        public var vpcSettings: DirectoryClientTypes.DirectoryVpcSettings?

        public init(
            desiredNumberOfDomainControllers: Swift.Int? = nil,
            directoryId: Swift.String? = nil,
            lastUpdatedDateTime: Foundation.Date? = nil,
            launchTime: Foundation.Date? = nil,
            regionName: Swift.String? = nil,
            regionType: DirectoryClientTypes.RegionType? = nil,
            status: DirectoryClientTypes.DirectoryStage? = nil,
            statusLastUpdatedDateTime: Foundation.Date? = nil,
            vpcSettings: DirectoryClientTypes.DirectoryVpcSettings? = nil
        )
        {
            self.desiredNumberOfDomainControllers = desiredNumberOfDomainControllers
            self.directoryId = directoryId
            self.lastUpdatedDateTime = lastUpdatedDateTime
            self.launchTime = launchTime
            self.regionName = regionName
            self.regionType = regionType
            self.status = status
            self.statusLastUpdatedDateTime = statusLastUpdatedDateTime
            self.vpcSettings = vpcSettings
        }
    }
}

public struct DescribeRegionsOutput: Swift.Sendable {
    /// If not null, more results are available. Pass this value for the NextToken parameter in a subsequent call to [DescribeRegions] to retrieve the next set of items.
    public var nextToken: Swift.String?
    /// List of Region information related to the directory for each replicated Region.
    public var regionsDescription: [DirectoryClientTypes.RegionDescription]?

    public init(
        nextToken: Swift.String? = nil,
        regionsDescription: [DirectoryClientTypes.RegionDescription]? = nil
    )
    {
        self.nextToken = nextToken
        self.regionsDescription = regionsDescription
    }
}

extension DirectoryClientTypes {

    public enum DirectoryConfigurationStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case `default`
        case failed
        case requested
        case updated
        case updating
        case sdkUnknown(Swift.String)

        public static var allCases: [DirectoryConfigurationStatus] {
            return [
                .default,
                .failed,
                .requested,
                .updated,
                .updating
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .default: return "Default"
            case .failed: return "Failed"
            case .requested: return "Requested"
            case .updated: return "Updated"
            case .updating: return "Updating"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeSettingsInput: Swift.Sendable {
    /// The identifier of the directory for which to retrieve information.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The DescribeSettingsResult.NextToken value from a previous call to [DescribeSettings]. Pass null if this is the first call.
    public var nextToken: Swift.String?
    /// The status of the directory settings for which to retrieve information.
    public var status: DirectoryClientTypes.DirectoryConfigurationStatus?

    public init(
        directoryId: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        status: DirectoryClientTypes.DirectoryConfigurationStatus? = nil
    )
    {
        self.directoryId = directoryId
        self.nextToken = nextToken
        self.status = status
    }
}

extension DirectoryClientTypes {

    /// Contains information about the specified configurable setting for a directory.
    public struct SettingEntry: Swift.Sendable {
        /// The valid range of values for the directory setting. These values depend on the DataType of your directory.
        public var allowedValues: Swift.String?
        /// The value of the directory setting that is applied to the directory.
        public var appliedValue: Swift.String?
        /// The data type of a directory setting. This is used to define the AllowedValues of a setting. For example a data type can be Boolean, DurationInSeconds, or Enum.
        public var dataType: Swift.String?
        /// The date and time when the request to update a directory setting was last submitted.
        public var lastRequestedDateTime: Foundation.Date?
        /// The date and time when the directory setting was last updated.
        public var lastUpdatedDateTime: Foundation.Date?
        /// The name of the directory setting. For example: TLS_1_0
        public var name: Swift.String?
        /// Details about the status of the request to update the directory setting. If the directory setting is deployed in more than one region, status is returned for the request in each region where the setting is deployed.
        public var requestDetailedStatus: [Swift.String: DirectoryClientTypes.DirectoryConfigurationStatus]?
        /// The overall status of the request to update the directory setting request. If the directory setting is deployed in more than one region, and the request fails in any region, the overall status is Failed.
        public var requestStatus: DirectoryClientTypes.DirectoryConfigurationStatus?
        /// The last status message for the directory status request.
        public var requestStatusMessage: Swift.String?
        /// The value that was last requested for the directory setting.
        public var requestedValue: Swift.String?
        /// The type, or category, of a directory setting. Similar settings have the same type. For example, Protocol, Cipher, or Certificate-Based Authentication.
        public var type: Swift.String?

        public init(
            allowedValues: Swift.String? = nil,
            appliedValue: Swift.String? = nil,
            dataType: Swift.String? = nil,
            lastRequestedDateTime: Foundation.Date? = nil,
            lastUpdatedDateTime: Foundation.Date? = nil,
            name: Swift.String? = nil,
            requestDetailedStatus: [Swift.String: DirectoryClientTypes.DirectoryConfigurationStatus]? = nil,
            requestStatus: DirectoryClientTypes.DirectoryConfigurationStatus? = nil,
            requestStatusMessage: Swift.String? = nil,
            requestedValue: Swift.String? = nil,
            type: Swift.String? = nil
        )
        {
            self.allowedValues = allowedValues
            self.appliedValue = appliedValue
            self.dataType = dataType
            self.lastRequestedDateTime = lastRequestedDateTime
            self.lastUpdatedDateTime = lastUpdatedDateTime
            self.name = name
            self.requestDetailedStatus = requestDetailedStatus
            self.requestStatus = requestStatus
            self.requestStatusMessage = requestStatusMessage
            self.requestedValue = requestedValue
            self.type = type
        }
    }
}

public struct DescribeSettingsOutput: Swift.Sendable {
    /// The identifier of the directory.
    public var directoryId: Swift.String?
    /// If not null, token that indicates that more results are available. Pass this value for the NextToken parameter in a subsequent call to DescribeSettings to retrieve the next set of items.
    public var nextToken: Swift.String?
    /// The list of [SettingEntry] objects that were retrieved. It is possible that this list contains less than the number of items specified in the Limit member of the request. This occurs if there are less than the requested number of items left to retrieve, or if the limitations of the operation have been exceeded.
    public var settingEntries: [DirectoryClientTypes.SettingEntry]?

    public init(
        directoryId: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        settingEntries: [DirectoryClientTypes.SettingEntry]? = nil
    )
    {
        self.directoryId = directoryId
        self.nextToken = nextToken
        self.settingEntries = settingEntries
    }
}

public struct DescribeSharedDirectoriesInput: Swift.Sendable {
    /// The number of shared directories to return in the response object.
    public var limit: Swift.Int?
    /// The DescribeSharedDirectoriesResult.NextToken value from a previous call to [DescribeSharedDirectories]. Pass null if this is the first call.
    public var nextToken: Swift.String?
    /// Returns the identifier of the directory in the directory owner account.
    /// This member is required.
    public var ownerDirectoryId: Swift.String?
    /// A list of identifiers of all shared directories in your account.
    public var sharedDirectoryIds: [Swift.String]?

    public init(
        limit: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        ownerDirectoryId: Swift.String? = nil,
        sharedDirectoryIds: [Swift.String]? = nil
    )
    {
        self.limit = limit
        self.nextToken = nextToken
        self.ownerDirectoryId = ownerDirectoryId
        self.sharedDirectoryIds = sharedDirectoryIds
    }
}

public struct DescribeSharedDirectoriesOutput: Swift.Sendable {
    /// If not null, token that indicates that more results are available. Pass this value for the NextToken parameter in a subsequent call to [DescribeSharedDirectories] to retrieve the next set of items.
    public var nextToken: Swift.String?
    /// A list of all shared directories in your account.
    public var sharedDirectories: [DirectoryClientTypes.SharedDirectory]?

    public init(
        nextToken: Swift.String? = nil,
        sharedDirectories: [DirectoryClientTypes.SharedDirectory]? = nil
    )
    {
        self.nextToken = nextToken
        self.sharedDirectories = sharedDirectories
    }
}

/// Contains the inputs for the [DescribeSnapshots] operation.
public struct DescribeSnapshotsInput: Swift.Sendable {
    /// The identifier of the directory for which to retrieve snapshot information.
    public var directoryId: Swift.String?
    /// The maximum number of objects to return.
    public var limit: Swift.Int?
    /// The DescribeSnapshotsResult.NextToken value from a previous call to [DescribeSnapshots]. Pass null if this is the first call.
    public var nextToken: Swift.String?
    /// A list of identifiers of the snapshots to obtain the information for. If this member is null or empty, all snapshots are returned using the Limit and NextToken members.
    public var snapshotIds: [Swift.String]?

    public init(
        directoryId: Swift.String? = nil,
        limit: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        snapshotIds: [Swift.String]? = nil
    )
    {
        self.directoryId = directoryId
        self.limit = limit
        self.nextToken = nextToken
        self.snapshotIds = snapshotIds
    }
}

extension DirectoryClientTypes {

    public enum SnapshotStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case completed
        case creating
        case failed
        case sdkUnknown(Swift.String)

        public static var allCases: [SnapshotStatus] {
            return [
                .completed,
                .creating,
                .failed
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .completed: return "Completed"
            case .creating: return "Creating"
            case .failed: return "Failed"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    public enum SnapshotType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case auto
        case manual
        case sdkUnknown(Swift.String)

        public static var allCases: [SnapshotType] {
            return [
                .auto,
                .manual
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .auto: return "Auto"
            case .manual: return "Manual"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Describes a directory snapshot.
    public struct Snapshot: Swift.Sendable {
        /// The directory identifier.
        public var directoryId: Swift.String?
        /// The descriptive name of the snapshot.
        public var name: Swift.String?
        /// The snapshot identifier.
        public var snapshotId: Swift.String?
        /// The date and time that the snapshot was taken.
        public var startTime: Foundation.Date?
        /// The snapshot status.
        public var status: DirectoryClientTypes.SnapshotStatus?
        /// The snapshot type.
        public var type: DirectoryClientTypes.SnapshotType?

        public init(
            directoryId: Swift.String? = nil,
            name: Swift.String? = nil,
            snapshotId: Swift.String? = nil,
            startTime: Foundation.Date? = nil,
            status: DirectoryClientTypes.SnapshotStatus? = nil,
            type: DirectoryClientTypes.SnapshotType? = nil
        )
        {
            self.directoryId = directoryId
            self.name = name
            self.snapshotId = snapshotId
            self.startTime = startTime
            self.status = status
            self.type = type
        }
    }
}

/// Contains the results of the [DescribeSnapshots] operation.
public struct DescribeSnapshotsOutput: Swift.Sendable {
    /// If not null, more results are available. Pass this value in the NextToken member of a subsequent call to [DescribeSnapshots].
    public var nextToken: Swift.String?
    /// The list of [Snapshot] objects that were retrieved. It is possible that this list contains less than the number of items specified in the Limit member of the request. This occurs if there are less than the requested number of items left to retrieve, or if the limitations of the operation have been exceeded.
    public var snapshots: [DirectoryClientTypes.Snapshot]?

    public init(
        nextToken: Swift.String? = nil,
        snapshots: [DirectoryClientTypes.Snapshot]? = nil
    )
    {
        self.nextToken = nextToken
        self.snapshots = snapshots
    }
}

/// Describes the trust relationships for a particular Managed Microsoft AD directory. If no input parameters are provided, such as directory ID or trust ID, this request describes all the trust relationships.
public struct DescribeTrustsInput: Swift.Sendable {
    /// The Directory ID of the Amazon Web Services directory that is a part of the requested trust relationship.
    public var directoryId: Swift.String?
    /// The maximum number of objects to return.
    public var limit: Swift.Int?
    /// The DescribeTrustsResult.NextToken value from a previous call to [DescribeTrusts]. Pass null if this is the first call.
    public var nextToken: Swift.String?
    /// A list of identifiers of the trust relationships for which to obtain the information. If this member is null, all trust relationships that belong to the current account are returned. An empty list results in an InvalidParameterException being thrown.
    public var trustIds: [Swift.String]?

    public init(
        directoryId: Swift.String? = nil,
        limit: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        trustIds: [Swift.String]? = nil
    )
    {
        self.directoryId = directoryId
        self.limit = limit
        self.nextToken = nextToken
        self.trustIds = trustIds
    }
}

extension DirectoryClientTypes {

    public enum TrustState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case created
        case creating
        case deleted
        case deleting
        case failed
        case updated
        case updateFailed
        case updating
        case verified
        case verifying
        case verifyFailed
        case sdkUnknown(Swift.String)

        public static var allCases: [TrustState] {
            return [
                .created,
                .creating,
                .deleted,
                .deleting,
                .failed,
                .updated,
                .updateFailed,
                .updating,
                .verified,
                .verifying,
                .verifyFailed
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .created: return "Created"
            case .creating: return "Creating"
            case .deleted: return "Deleted"
            case .deleting: return "Deleting"
            case .failed: return "Failed"
            case .updated: return "Updated"
            case .updateFailed: return "UpdateFailed"
            case .updating: return "Updating"
            case .verified: return "Verified"
            case .verifying: return "Verifying"
            case .verifyFailed: return "VerifyFailed"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Describes a trust relationship between an Managed Microsoft AD directory and an external domain.
    public struct Trust: Swift.Sendable {
        /// The date and time that the trust relationship was created.
        public var createdDateTime: Foundation.Date?
        /// The Directory ID of the Amazon Web Services directory involved in the trust relationship.
        public var directoryId: Swift.String?
        /// The date and time that the trust relationship was last updated.
        public var lastUpdatedDateTime: Foundation.Date?
        /// The Fully Qualified Domain Name (FQDN) of the external domain involved in the trust relationship.
        public var remoteDomainName: Swift.String?
        /// Current state of selective authentication for the trust.
        public var selectiveAuth: DirectoryClientTypes.SelectiveAuth?
        /// The date and time that the TrustState was last updated.
        public var stateLastUpdatedDateTime: Foundation.Date?
        /// The trust relationship direction.
        public var trustDirection: DirectoryClientTypes.TrustDirection?
        /// The unique ID of the trust relationship.
        public var trustId: Swift.String?
        /// The trust relationship state.
        public var trustState: DirectoryClientTypes.TrustState?
        /// The reason for the TrustState.
        public var trustStateReason: Swift.String?
        /// The trust relationship type. Forest is the default.
        public var trustType: DirectoryClientTypes.TrustType?

        public init(
            createdDateTime: Foundation.Date? = nil,
            directoryId: Swift.String? = nil,
            lastUpdatedDateTime: Foundation.Date? = nil,
            remoteDomainName: Swift.String? = nil,
            selectiveAuth: DirectoryClientTypes.SelectiveAuth? = nil,
            stateLastUpdatedDateTime: Foundation.Date? = nil,
            trustDirection: DirectoryClientTypes.TrustDirection? = nil,
            trustId: Swift.String? = nil,
            trustState: DirectoryClientTypes.TrustState? = nil,
            trustStateReason: Swift.String? = nil,
            trustType: DirectoryClientTypes.TrustType? = nil
        )
        {
            self.createdDateTime = createdDateTime
            self.directoryId = directoryId
            self.lastUpdatedDateTime = lastUpdatedDateTime
            self.remoteDomainName = remoteDomainName
            self.selectiveAuth = selectiveAuth
            self.stateLastUpdatedDateTime = stateLastUpdatedDateTime
            self.trustDirection = trustDirection
            self.trustId = trustId
            self.trustState = trustState
            self.trustStateReason = trustStateReason
            self.trustType = trustType
        }
    }
}

/// The result of a DescribeTrust request.
public struct DescribeTrustsOutput: Swift.Sendable {
    /// If not null, more results are available. Pass this value for the NextToken parameter in a subsequent call to [DescribeTrusts] to retrieve the next set of items.
    public var nextToken: Swift.String?
    /// The list of Trust objects that were retrieved. It is possible that this list contains less than the number of items specified in the Limit member of the request. This occurs if there are less than the requested number of items left to retrieve, or if the limitations of the operation have been exceeded.
    public var trusts: [DirectoryClientTypes.Trust]?

    public init(
        nextToken: Swift.String? = nil,
        trusts: [DirectoryClientTypes.Trust]? = nil
    )
    {
        self.nextToken = nextToken
        self.trusts = trusts
    }
}

extension DirectoryClientTypes {

    public enum UpdateType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case os
        case sdkUnknown(Swift.String)

        public static var allCases: [UpdateType] {
            return [
                .os
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .os: return "OS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeUpdateDirectoryInput: Swift.Sendable {
    /// The unique identifier of the directory.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The DescribeUpdateDirectoryResult. NextToken value from a previous call to [DescribeUpdateDirectory]. Pass null if this is the first call.
    public var nextToken: Swift.String?
    /// The name of the Region.
    public var regionName: Swift.String?
    /// The type of updates you want to describe for the directory.
    /// This member is required.
    public var updateType: DirectoryClientTypes.UpdateType?

    public init(
        directoryId: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        regionName: Swift.String? = nil,
        updateType: DirectoryClientTypes.UpdateType? = nil
    )
    {
        self.directoryId = directoryId
        self.nextToken = nextToken
        self.regionName = regionName
        self.updateType = updateType
    }
}

extension DirectoryClientTypes {

    /// OS version that the directory needs to be updated to.
    public struct OSUpdateSettings: Swift.Sendable {
        /// OS version that the directory needs to be updated to.
        public var osVersion: DirectoryClientTypes.OSVersion?

        public init(
            osVersion: DirectoryClientTypes.OSVersion? = nil
        )
        {
            self.osVersion = osVersion
        }
    }
}

extension DirectoryClientTypes {

    /// The value for a given type of UpdateSettings.
    public struct UpdateValue: Swift.Sendable {
        /// The OS update related settings.
        public var osUpdateSettings: DirectoryClientTypes.OSUpdateSettings?

        public init(
            osUpdateSettings: DirectoryClientTypes.OSUpdateSettings? = nil
        )
        {
            self.osUpdateSettings = osUpdateSettings
        }
    }
}

extension DirectoryClientTypes {

    public enum UpdateStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case updated
        case updateFailed
        case updating
        case sdkUnknown(Swift.String)

        public static var allCases: [UpdateStatus] {
            return [
                .updated,
                .updateFailed,
                .updating
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .updated: return "Updated"
            case .updateFailed: return "UpdateFailed"
            case .updating: return "Updating"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// An entry of update information related to a requested update type.
    public struct UpdateInfoEntry: Swift.Sendable {
        /// This specifies if the update was initiated by the customer or by the service team.
        public var initiatedBy: Swift.String?
        /// The last updated date and time of a particular directory setting.
        public var lastUpdatedDateTime: Foundation.Date?
        /// The new value of the target setting.
        public var newValue: DirectoryClientTypes.UpdateValue?
        /// The old value of the target setting.
        public var previousValue: DirectoryClientTypes.UpdateValue?
        /// The name of the Region.
        public var region: Swift.String?
        /// The start time of the UpdateDirectorySetup for the particular type.
        public var startTime: Foundation.Date?
        /// The status of the update performed on the directory.
        public var status: DirectoryClientTypes.UpdateStatus?
        /// The reason for the current status of the update type activity.
        public var statusReason: Swift.String?

        public init(
            initiatedBy: Swift.String? = nil,
            lastUpdatedDateTime: Foundation.Date? = nil,
            newValue: DirectoryClientTypes.UpdateValue? = nil,
            previousValue: DirectoryClientTypes.UpdateValue? = nil,
            region: Swift.String? = nil,
            startTime: Foundation.Date? = nil,
            status: DirectoryClientTypes.UpdateStatus? = nil,
            statusReason: Swift.String? = nil
        )
        {
            self.initiatedBy = initiatedBy
            self.lastUpdatedDateTime = lastUpdatedDateTime
            self.newValue = newValue
            self.previousValue = previousValue
            self.region = region
            self.startTime = startTime
            self.status = status
            self.statusReason = statusReason
        }
    }
}

public struct DescribeUpdateDirectoryOutput: Swift.Sendable {
    /// If not null, more results are available. Pass this value for the NextToken parameter.
    public var nextToken: Swift.String?
    /// The list of update activities on a directory for the requested update type.
    public var updateActivities: [DirectoryClientTypes.UpdateInfoEntry]?

    public init(
        nextToken: Swift.String? = nil,
        updateActivities: [DirectoryClientTypes.UpdateInfoEntry]? = nil
    )
    {
        self.nextToken = nextToken
        self.updateActivities = updateActivities
    }
}

/// The directory is already updated to desired update type settings.
public struct DirectoryInDesiredStateException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DirectoryInDesiredStateException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

extension DirectoryClientTypes {

    /// Contains directory limit information for a Region.
    public struct DirectoryLimits: Swift.Sendable {
        /// The current number of cloud directories in the Region.
        public var cloudOnlyDirectoriesCurrentCount: Swift.Int?
        /// The maximum number of cloud directories allowed in the Region.
        public var cloudOnlyDirectoriesLimit: Swift.Int?
        /// Indicates if the cloud directory limit has been reached.
        public var cloudOnlyDirectoriesLimitReached: Swift.Bool
        /// The current number of Managed Microsoft AD directories in the region.
        public var cloudOnlyMicrosoftADCurrentCount: Swift.Int?
        /// The maximum number of Managed Microsoft AD directories allowed in the region.
        public var cloudOnlyMicrosoftADLimit: Swift.Int?
        /// Indicates if the Managed Microsoft AD directory limit has been reached.
        public var cloudOnlyMicrosoftADLimitReached: Swift.Bool
        /// The current number of connected directories in the Region.
        public var connectedDirectoriesCurrentCount: Swift.Int?
        /// The maximum number of connected directories allowed in the Region.
        public var connectedDirectoriesLimit: Swift.Int?
        /// Indicates if the connected directory limit has been reached.
        public var connectedDirectoriesLimitReached: Swift.Bool

        public init(
            cloudOnlyDirectoriesCurrentCount: Swift.Int? = nil,
            cloudOnlyDirectoriesLimit: Swift.Int? = nil,
            cloudOnlyDirectoriesLimitReached: Swift.Bool = false,
            cloudOnlyMicrosoftADCurrentCount: Swift.Int? = nil,
            cloudOnlyMicrosoftADLimit: Swift.Int? = nil,
            cloudOnlyMicrosoftADLimitReached: Swift.Bool = false,
            connectedDirectoriesCurrentCount: Swift.Int? = nil,
            connectedDirectoriesLimit: Swift.Int? = nil,
            connectedDirectoriesLimitReached: Swift.Bool = false
        )
        {
            self.cloudOnlyDirectoriesCurrentCount = cloudOnlyDirectoriesCurrentCount
            self.cloudOnlyDirectoriesLimit = cloudOnlyDirectoriesLimit
            self.cloudOnlyDirectoriesLimitReached = cloudOnlyDirectoriesLimitReached
            self.cloudOnlyMicrosoftADCurrentCount = cloudOnlyMicrosoftADCurrentCount
            self.cloudOnlyMicrosoftADLimit = cloudOnlyMicrosoftADLimit
            self.cloudOnlyMicrosoftADLimitReached = cloudOnlyMicrosoftADLimitReached
            self.connectedDirectoriesCurrentCount = connectedDirectoriesCurrentCount
            self.connectedDirectoriesLimit = connectedDirectoriesLimit
            self.connectedDirectoriesLimitReached = connectedDirectoriesLimitReached
        }
    }
}

/// The specified directory has not been shared with this Amazon Web Services account.
public struct DirectoryNotSharedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DirectoryNotSharedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// Client authentication is already enabled.
public struct InvalidClientAuthStatusException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidClientAuthStatusException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

public struct DisableClientAuthenticationInput: Swift.Sendable {
    /// The identifier of the directory
    /// This member is required.
    public var directoryId: Swift.String?
    /// The type of client authentication to disable. Currently the only parameter "SmartCard" is supported.
    /// This member is required.
    public var type: DirectoryClientTypes.ClientAuthenticationType?

    public init(
        directoryId: Swift.String? = nil,
        type: DirectoryClientTypes.ClientAuthenticationType? = nil
    )
    {
        self.directoryId = directoryId
        self.type = type
    }
}

public struct DisableClientAuthenticationOutput: Swift.Sendable {

    public init() { }
}

public struct DisableDirectoryDataAccessInput: Swift.Sendable {
    /// The directory identifier.
    /// This member is required.
    public var directoryId: Swift.String?

    public init(
        directoryId: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
    }
}

public struct DisableDirectoryDataAccessOutput: Swift.Sendable {

    public init() { }
}

/// The LDAP activities could not be performed because they are limited by the LDAPS status.
public struct InvalidLDAPSStatusException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidLDAPSStatusException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

public struct DisableLDAPSInput: Swift.Sendable {
    /// The identifier of the directory.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The type of LDAP security to enable. Currently only the value Client is supported.
    /// This member is required.
    public var type: DirectoryClientTypes.LDAPSType?

    public init(
        directoryId: Swift.String? = nil,
        type: DirectoryClientTypes.LDAPSType? = nil
    )
    {
        self.directoryId = directoryId
        self.type = type
    }
}

public struct DisableLDAPSOutput: Swift.Sendable {

    public init() { }
}

/// Contains the inputs for the [DisableRadius] operation.
public struct DisableRadiusInput: Swift.Sendable {
    /// The identifier of the directory for which to disable MFA.
    /// This member is required.
    public var directoryId: Swift.String?

    public init(
        directoryId: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
    }
}

/// Contains the results of the [DisableRadius] operation.
public struct DisableRadiusOutput: Swift.Sendable {

    public init() { }
}

/// Contains the inputs for the [DisableSso] operation.
public struct DisableSsoInput: Swift.Sendable {
    /// The identifier of the directory for which to disable single-sign on.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The password of an alternate account to use to disable single-sign on. This is only used for AD Connector directories. For more information, see the UserName parameter.
    public var password: Swift.String?
    /// The username of an alternate account to use to disable single-sign on. This is only used for AD Connector directories. This account must have privileges to remove a service principal name. If the AD Connector service account does not have privileges to remove a service principal name, you can specify an alternate account with the UserName and Password parameters. These credentials are only used to disable single sign-on and are not stored by the service. The AD Connector service account is not changed.
    public var userName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        password: Swift.String? = nil,
        userName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.password = password
        self.userName = userName
    }
}

extension DisableSsoInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "DisableSsoInput(directoryId: \(Swift.String(describing: directoryId)), userName: \(Swift.String(describing: userName)), password: \"CONTENT_REDACTED\")"}
}

/// Contains the results of the [DisableSso] operation.
public struct DisableSsoOutput: Swift.Sendable {

    public init() { }
}

/// Client authentication setup could not be completed because at least one valid certificate must be registered in the system.
public struct NoAvailableCertificateException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "NoAvailableCertificateException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

public struct EnableClientAuthenticationInput: Swift.Sendable {
    /// The identifier of the specified directory.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The type of client authentication to enable. Currently only the value SmartCard is supported. Smart card authentication in AD Connector requires that you enable Kerberos Constrained Delegation for the Service User to the LDAP service in your self-managed AD.
    /// This member is required.
    public var type: DirectoryClientTypes.ClientAuthenticationType?

    public init(
        directoryId: Swift.String? = nil,
        type: DirectoryClientTypes.ClientAuthenticationType? = nil
    )
    {
        self.directoryId = directoryId
        self.type = type
    }
}

public struct EnableClientAuthenticationOutput: Swift.Sendable {

    public init() { }
}

public struct EnableDirectoryDataAccessInput: Swift.Sendable {
    /// The directory identifier.
    /// This member is required.
    public var directoryId: Swift.String?

    public init(
        directoryId: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
    }
}

public struct EnableDirectoryDataAccessOutput: Swift.Sendable {

    public init() { }
}

public struct EnableLDAPSInput: Swift.Sendable {
    /// The identifier of the directory.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The type of LDAP security to enable. Currently only the value Client is supported.
    /// This member is required.
    public var type: DirectoryClientTypes.LDAPSType?

    public init(
        directoryId: Swift.String? = nil,
        type: DirectoryClientTypes.LDAPSType? = nil
    )
    {
        self.directoryId = directoryId
        self.type = type
    }
}

public struct EnableLDAPSOutput: Swift.Sendable {

    public init() { }
}

/// Contains the inputs for the [EnableRadius] operation.
public struct EnableRadiusInput: Swift.Sendable {
    /// The identifier of the directory for which to enable MFA.
    /// This member is required.
    public var directoryId: Swift.String?
    /// A [RadiusSettings] object that contains information about the RADIUS server.
    /// This member is required.
    public var radiusSettings: DirectoryClientTypes.RadiusSettings?

    public init(
        directoryId: Swift.String? = nil,
        radiusSettings: DirectoryClientTypes.RadiusSettings? = nil
    )
    {
        self.directoryId = directoryId
        self.radiusSettings = radiusSettings
    }
}

/// Contains the results of the [EnableRadius] operation.
public struct EnableRadiusOutput: Swift.Sendable {

    public init() { }
}

/// Contains the inputs for the [EnableSso] operation.
public struct EnableSsoInput: Swift.Sendable {
    /// The identifier of the directory for which to enable single-sign on.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The password of an alternate account to use to enable single-sign on. This is only used for AD Connector directories. For more information, see the UserName parameter.
    public var password: Swift.String?
    /// The username of an alternate account to use to enable single-sign on. This is only used for AD Connector directories. This account must have privileges to add a service principal name. If the AD Connector service account does not have privileges to add a service principal name, you can specify an alternate account with the UserName and Password parameters. These credentials are only used to enable single sign-on and are not stored by the service. The AD Connector service account is not changed.
    public var userName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        password: Swift.String? = nil,
        userName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.password = password
        self.userName = userName
    }
}

extension EnableSsoInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "EnableSsoInput(directoryId: \(Swift.String(describing: directoryId)), userName: \(Swift.String(describing: userName)), password: \"CONTENT_REDACTED\")"}
}

/// Contains the results of the [EnableSso] operation.
public struct EnableSsoOutput: Swift.Sendable {

    public init() { }
}

/// Contains the inputs for the [GetDirectoryLimits] operation.
public struct GetDirectoryLimitsInput: Swift.Sendable {

    public init() { }
}

/// Contains the results of the [GetDirectoryLimits] operation.
public struct GetDirectoryLimitsOutput: Swift.Sendable {
    /// A [DirectoryLimits] object that contains the directory limits for the current Region.
    public var directoryLimits: DirectoryClientTypes.DirectoryLimits?

    public init(
        directoryLimits: DirectoryClientTypes.DirectoryLimits? = nil
    )
    {
        self.directoryLimits = directoryLimits
    }
}

/// Contains the inputs for the [GetSnapshotLimits] operation.
public struct GetSnapshotLimitsInput: Swift.Sendable {
    /// Contains the identifier of the directory to obtain the limits for.
    /// This member is required.
    public var directoryId: Swift.String?

    public init(
        directoryId: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
    }
}

extension DirectoryClientTypes {

    /// Contains manual snapshot limit information for a directory.
    public struct SnapshotLimits: Swift.Sendable {
        /// The current number of manual snapshots of the directory.
        public var manualSnapshotsCurrentCount: Swift.Int?
        /// The maximum number of manual snapshots allowed.
        public var manualSnapshotsLimit: Swift.Int?
        /// Indicates if the manual snapshot limit has been reached.
        public var manualSnapshotsLimitReached: Swift.Bool

        public init(
            manualSnapshotsCurrentCount: Swift.Int? = nil,
            manualSnapshotsLimit: Swift.Int? = nil,
            manualSnapshotsLimitReached: Swift.Bool = false
        )
        {
            self.manualSnapshotsCurrentCount = manualSnapshotsCurrentCount
            self.manualSnapshotsLimit = manualSnapshotsLimit
            self.manualSnapshotsLimitReached = manualSnapshotsLimitReached
        }
    }
}

/// Contains the results of the [GetSnapshotLimits] operation.
public struct GetSnapshotLimitsOutput: Swift.Sendable {
    /// A [SnapshotLimits] object that contains the manual snapshot limits for the specified directory.
    public var snapshotLimits: DirectoryClientTypes.SnapshotLimits?

    public init(
        snapshotLimits: DirectoryClientTypes.SnapshotLimits? = nil
    )
    {
        self.snapshotLimits = snapshotLimits
    }
}

public struct ListCertificatesInput: Swift.Sendable {
    /// The identifier of the directory.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The number of items that should show up on one page
    public var limit: Swift.Int?
    /// A token for requesting another page of certificates if the NextToken response element indicates that more certificates are available. Use the value of the returned NextToken element in your request until the token comes back as null. Pass null if this is the first call.
    public var nextToken: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        limit: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.limit = limit
        self.nextToken = nextToken
    }
}

public struct ListCertificatesOutput: Swift.Sendable {
    /// A list of certificates with basic details including certificate ID, certificate common name, certificate state.
    public var certificatesInfo: [DirectoryClientTypes.CertificateInfo]?
    /// Indicates whether another page of certificates is available when the number of available certificates exceeds the page limit.
    public var nextToken: Swift.String?

    public init(
        certificatesInfo: [DirectoryClientTypes.CertificateInfo]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.certificatesInfo = certificatesInfo
        self.nextToken = nextToken
    }
}

public struct ListIpRoutesInput: Swift.Sendable {
    /// Identifier (ID) of the directory for which you want to retrieve the IP addresses.
    /// This member is required.
    public var directoryId: Swift.String?
    /// Maximum number of items to return. If this value is zero, the maximum number of items is specified by the limitations of the operation.
    public var limit: Swift.Int?
    /// The ListIpRoutes.NextToken value from a previous call to [ListIpRoutes]. Pass null if this is the first call.
    public var nextToken: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        limit: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.limit = limit
        self.nextToken = nextToken
    }
}

extension DirectoryClientTypes {

    public enum IpRouteStatusMsg: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case added
        case adding
        case addFailed
        case removed
        case removeFailed
        case removing
        case sdkUnknown(Swift.String)

        public static var allCases: [IpRouteStatusMsg] {
            return [
                .added,
                .adding,
                .addFailed,
                .removed,
                .removeFailed,
                .removing
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .added: return "Added"
            case .adding: return "Adding"
            case .addFailed: return "AddFailed"
            case .removed: return "Removed"
            case .removeFailed: return "RemoveFailed"
            case .removing: return "Removing"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Information about one or more IP address blocks.
    public struct IpRouteInfo: Swift.Sendable {
        /// The date and time the address block was added to the directory.
        public var addedDateTime: Foundation.Date?
        /// IP address block in the [IpRoute].
        public var cidrIp: Swift.String?
        /// Description of the [IpRouteInfo].
        public var description: Swift.String?
        /// Identifier (ID) of the directory associated with the IP addresses.
        public var directoryId: Swift.String?
        /// The status of the IP address block.
        public var ipRouteStatusMsg: DirectoryClientTypes.IpRouteStatusMsg?
        /// The reason for the IpRouteStatusMsg.
        public var ipRouteStatusReason: Swift.String?

        public init(
            addedDateTime: Foundation.Date? = nil,
            cidrIp: Swift.String? = nil,
            description: Swift.String? = nil,
            directoryId: Swift.String? = nil,
            ipRouteStatusMsg: DirectoryClientTypes.IpRouteStatusMsg? = nil,
            ipRouteStatusReason: Swift.String? = nil
        )
        {
            self.addedDateTime = addedDateTime
            self.cidrIp = cidrIp
            self.description = description
            self.directoryId = directoryId
            self.ipRouteStatusMsg = ipRouteStatusMsg
            self.ipRouteStatusReason = ipRouteStatusReason
        }
    }
}

public struct ListIpRoutesOutput: Swift.Sendable {
    /// A list of [IpRoute]s.
    public var ipRoutesInfo: [DirectoryClientTypes.IpRouteInfo]?
    /// If not null, more results are available. Pass this value for the NextToken parameter in a subsequent call to [ListIpRoutes] to retrieve the next set of items.
    public var nextToken: Swift.String?

    public init(
        ipRoutesInfo: [DirectoryClientTypes.IpRouteInfo]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.ipRoutesInfo = ipRoutesInfo
        self.nextToken = nextToken
    }
}

public struct ListLogSubscriptionsInput: Swift.Sendable {
    /// If a DirectoryID is provided, lists only the log subscription associated with that directory. If no DirectoryId is provided, lists all log subscriptions associated with your Amazon Web Services account. If there are no log subscriptions for the Amazon Web Services account or the directory, an empty list will be returned.
    public var directoryId: Swift.String?
    /// The maximum number of items returned.
    public var limit: Swift.Int?
    /// The token for the next set of items to return.
    public var nextToken: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        limit: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.limit = limit
        self.nextToken = nextToken
    }
}

extension DirectoryClientTypes {

    /// Represents a log subscription, which tracks real-time data from a chosen log group to a specified destination.
    public struct LogSubscription: Swift.Sendable {
        /// Identifier (ID) of the directory that you want to associate with the log subscription.
        public var directoryId: Swift.String?
        /// The name of the log group.
        public var logGroupName: Swift.String?
        /// The date and time that the log subscription was created.
        public var subscriptionCreatedDateTime: Foundation.Date?

        public init(
            directoryId: Swift.String? = nil,
            logGroupName: Swift.String? = nil,
            subscriptionCreatedDateTime: Foundation.Date? = nil
        )
        {
            self.directoryId = directoryId
            self.logGroupName = logGroupName
            self.subscriptionCreatedDateTime = subscriptionCreatedDateTime
        }
    }
}

public struct ListLogSubscriptionsOutput: Swift.Sendable {
    /// A list of active [LogSubscription] objects for calling the Amazon Web Services account.
    public var logSubscriptions: [DirectoryClientTypes.LogSubscription]?
    /// The token for the next set of items to return.
    public var nextToken: Swift.String?

    public init(
        logSubscriptions: [DirectoryClientTypes.LogSubscription]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.logSubscriptions = logSubscriptions
        self.nextToken = nextToken
    }
}

public struct ListSchemaExtensionsInput: Swift.Sendable {
    /// The identifier of the directory from which to retrieve the schema extension information.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The maximum number of items to return.
    public var limit: Swift.Int?
    /// The ListSchemaExtensions.NextToken value from a previous call to ListSchemaExtensions. Pass null if this is the first call.
    public var nextToken: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        limit: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.limit = limit
        self.nextToken = nextToken
    }
}

extension DirectoryClientTypes {

    public enum SchemaExtensionStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cancelled
        case cancelInProgress
        case completed
        case creatingSnapshot
        case failed
        case initializing
        case replicating
        case rollbackInProgress
        case updatingSchema
        case sdkUnknown(Swift.String)

        public static var allCases: [SchemaExtensionStatus] {
            return [
                .cancelled,
                .cancelInProgress,
                .completed,
                .creatingSnapshot,
                .failed,
                .initializing,
                .replicating,
                .rollbackInProgress,
                .updatingSchema
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cancelled: return "Cancelled"
            case .cancelInProgress: return "CancelInProgress"
            case .completed: return "Completed"
            case .creatingSnapshot: return "CreatingSnapshot"
            case .failed: return "Failed"
            case .initializing: return "Initializing"
            case .replicating: return "Replicating"
            case .rollbackInProgress: return "RollbackInProgress"
            case .updatingSchema: return "UpdatingSchema"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Information about a schema extension.
    public struct SchemaExtensionInfo: Swift.Sendable {
        /// A description of the schema extension.
        public var description: Swift.String?
        /// The identifier of the directory to which the schema extension is applied.
        public var directoryId: Swift.String?
        /// The date and time that the schema extension was completed.
        public var endDateTime: Foundation.Date?
        /// The identifier of the schema extension.
        public var schemaExtensionId: Swift.String?
        /// The current status of the schema extension.
        public var schemaExtensionStatus: DirectoryClientTypes.SchemaExtensionStatus?
        /// The reason for the SchemaExtensionStatus.
        public var schemaExtensionStatusReason: Swift.String?
        /// The date and time that the schema extension started being applied to the directory.
        public var startDateTime: Foundation.Date?

        public init(
            description: Swift.String? = nil,
            directoryId: Swift.String? = nil,
            endDateTime: Foundation.Date? = nil,
            schemaExtensionId: Swift.String? = nil,
            schemaExtensionStatus: DirectoryClientTypes.SchemaExtensionStatus? = nil,
            schemaExtensionStatusReason: Swift.String? = nil,
            startDateTime: Foundation.Date? = nil
        )
        {
            self.description = description
            self.directoryId = directoryId
            self.endDateTime = endDateTime
            self.schemaExtensionId = schemaExtensionId
            self.schemaExtensionStatus = schemaExtensionStatus
            self.schemaExtensionStatusReason = schemaExtensionStatusReason
            self.startDateTime = startDateTime
        }
    }
}

public struct ListSchemaExtensionsOutput: Swift.Sendable {
    /// If not null, more results are available. Pass this value for the NextToken parameter in a subsequent call to ListSchemaExtensions to retrieve the next set of items.
    public var nextToken: Swift.String?
    /// Information about the schema extensions applied to the directory.
    public var schemaExtensionsInfo: [DirectoryClientTypes.SchemaExtensionInfo]?

    public init(
        nextToken: Swift.String? = nil,
        schemaExtensionsInfo: [DirectoryClientTypes.SchemaExtensionInfo]? = nil
    )
    {
        self.nextToken = nextToken
        self.schemaExtensionsInfo = schemaExtensionsInfo
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// Reserved for future use.
    public var limit: Swift.Int?
    /// Reserved for future use.
    public var nextToken: Swift.String?
    /// Identifier (ID) of the directory for which you want to retrieve tags.
    /// This member is required.
    public var resourceId: Swift.String?

    public init(
        limit: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        resourceId: Swift.String? = nil
    )
    {
        self.limit = limit
        self.nextToken = nextToken
        self.resourceId = resourceId
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// Reserved for future use.
    public var nextToken: Swift.String?
    /// List of tags returned by the ListTagsForResource operation.
    public var tags: [DirectoryClientTypes.Tag]?

    public init(
        nextToken: Swift.String? = nil,
        tags: [DirectoryClientTypes.Tag]? = nil
    )
    {
        self.nextToken = nextToken
        self.tags = tags
    }
}

/// The certificate PEM that was provided has incorrect encoding.
public struct InvalidCertificateException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidCertificateException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

public struct RegisterCertificateInput: Swift.Sendable {
    /// The certificate PEM string that needs to be registered.
    /// This member is required.
    public var certificateData: Swift.String?
    /// A ClientCertAuthSettings object that contains client certificate authentication settings.
    public var clientCertAuthSettings: DirectoryClientTypes.ClientCertAuthSettings?
    /// The identifier of the directory.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The function that the registered certificate performs. Valid values include ClientLDAPS or ClientCertAuth. The default value is ClientLDAPS.
    public var type: DirectoryClientTypes.CertificateType?

    public init(
        certificateData: Swift.String? = nil,
        clientCertAuthSettings: DirectoryClientTypes.ClientCertAuthSettings? = nil,
        directoryId: Swift.String? = nil,
        type: DirectoryClientTypes.CertificateType? = nil
    )
    {
        self.certificateData = certificateData
        self.clientCertAuthSettings = clientCertAuthSettings
        self.directoryId = directoryId
        self.type = type
    }
}

public struct RegisterCertificateOutput: Swift.Sendable {
    /// The identifier of the certificate.
    public var certificateId: Swift.String?

    public init(
        certificateId: Swift.String? = nil
    )
    {
        self.certificateId = certificateId
    }
}

/// Registers a new event topic.
public struct RegisterEventTopicInput: Swift.Sendable {
    /// The Directory ID that will publish status messages to the Amazon SNS topic.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The Amazon SNS topic name to which the directory will publish status messages. This Amazon SNS topic must be in the same region as the specified Directory ID.
    /// This member is required.
    public var topicName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        topicName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.topicName = topicName
    }
}

/// The result of a RegisterEventTopic request.
public struct RegisterEventTopicOutput: Swift.Sendable {

    public init() { }
}

public struct RejectSharedDirectoryInput: Swift.Sendable {
    /// Identifier of the shared directory in the directory consumer account. This identifier is different for each directory owner account.
    /// This member is required.
    public var sharedDirectoryId: Swift.String?

    public init(
        sharedDirectoryId: Swift.String? = nil
    )
    {
        self.sharedDirectoryId = sharedDirectoryId
    }
}

public struct RejectSharedDirectoryOutput: Swift.Sendable {
    /// Identifier of the shared directory in the directory consumer account.
    public var sharedDirectoryId: Swift.String?

    public init(
        sharedDirectoryId: Swift.String? = nil
    )
    {
        self.sharedDirectoryId = sharedDirectoryId
    }
}

public struct RemoveIpRoutesInput: Swift.Sendable {
    /// IP address blocks that you want to remove.
    /// This member is required.
    public var cidrIps: [Swift.String]?
    /// Identifier (ID) of the directory from which you want to remove the IP addresses.
    /// This member is required.
    public var directoryId: Swift.String?

    public init(
        cidrIps: [Swift.String]? = nil,
        directoryId: Swift.String? = nil
    )
    {
        self.cidrIps = cidrIps
        self.directoryId = directoryId
    }
}

public struct RemoveIpRoutesOutput: Swift.Sendable {

    public init() { }
}

public struct RemoveRegionInput: Swift.Sendable {
    /// The identifier of the directory for which you want to remove Region replication.
    /// This member is required.
    public var directoryId: Swift.String?

    public init(
        directoryId: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
    }
}

public struct RemoveRegionOutput: Swift.Sendable {

    public init() { }
}

public struct RemoveTagsFromResourceInput: Swift.Sendable {
    /// Identifier (ID) of the directory from which to remove the tag.
    /// This member is required.
    public var resourceId: Swift.String?
    /// The tag key (name) of the tag to be removed.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceId: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceId = resourceId
        self.tagKeys = tagKeys
    }
}

public struct RemoveTagsFromResourceOutput: Swift.Sendable {

    public init() { }
}

/// The new password provided by the user does not meet the password complexity requirements defined in your directory.
public struct InvalidPasswordException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidPasswordException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// The user provided a username that does not exist in your directory.
public struct UserDoesNotExistException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UserDoesNotExistException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

public struct ResetUserPasswordInput: Swift.Sendable {
    /// Identifier of the Managed Microsoft AD or Simple AD directory in which the user resides.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The new password that will be reset.
    /// This member is required.
    public var newPassword: Swift.String?
    /// The user name of the user whose password will be reset.
    /// This member is required.
    public var userName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        newPassword: Swift.String? = nil,
        userName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.newPassword = newPassword
        self.userName = userName
    }
}

extension ResetUserPasswordInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ResetUserPasswordInput(directoryId: \(Swift.String(describing: directoryId)), userName: \(Swift.String(describing: userName)), newPassword: \"CONTENT_REDACTED\")"}
}

public struct ResetUserPasswordOutput: Swift.Sendable {

    public init() { }
}

/// An object representing the inputs for the [RestoreFromSnapshot] operation.
public struct RestoreFromSnapshotInput: Swift.Sendable {
    /// The identifier of the snapshot to restore from.
    /// This member is required.
    public var snapshotId: Swift.String?

    public init(
        snapshotId: Swift.String? = nil
    )
    {
        self.snapshotId = snapshotId
    }
}

/// Contains the results of the [RestoreFromSnapshot] operation.
public struct RestoreFromSnapshotOutput: Swift.Sendable {

    public init() { }
}

/// The specified shared target is not valid.
public struct InvalidTargetException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidTargetException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// Exception encountered while trying to access your Amazon Web Services organization.
public struct OrganizationsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "OrganizationsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// The maximum number of Amazon Web Services accounts that you can share with this directory has been reached.
public struct ShareLimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ShareLimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

extension DirectoryClientTypes {

    public enum TargetType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case account
        case sdkUnknown(Swift.String)

        public static var allCases: [TargetType] {
            return [
                .account
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .account: return "ACCOUNT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DirectoryClientTypes {

    /// Identifier that contains details about the directory consumer account.
    public struct ShareTarget: Swift.Sendable {
        /// Identifier of the directory consumer account.
        /// This member is required.
        public var id: Swift.String?
        /// Type of identifier to be used in the Id field.
        /// This member is required.
        public var type: DirectoryClientTypes.TargetType?

        public init(
            id: Swift.String? = nil,
            type: DirectoryClientTypes.TargetType? = nil
        )
        {
            self.id = id
            self.type = type
        }
    }
}

public struct ShareDirectoryInput: Swift.Sendable {
    /// Identifier of the Managed Microsoft AD directory that you want to share with other Amazon Web Services accounts.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The method used when sharing a directory to determine whether the directory should be shared within your Amazon Web Services organization (ORGANIZATIONS) or with any Amazon Web Services account by sending a directory sharing request (HANDSHAKE).
    /// This member is required.
    public var shareMethod: DirectoryClientTypes.ShareMethod?
    /// A directory share request that is sent by the directory owner to the directory consumer. The request includes a typed message to help the directory consumer administrator determine whether to approve or reject the share invitation.
    public var shareNotes: Swift.String?
    /// Identifier for the directory consumer account with whom the directory is to be shared.
    /// This member is required.
    public var shareTarget: DirectoryClientTypes.ShareTarget?

    public init(
        directoryId: Swift.String? = nil,
        shareMethod: DirectoryClientTypes.ShareMethod? = nil,
        shareNotes: Swift.String? = nil,
        shareTarget: DirectoryClientTypes.ShareTarget? = nil
    )
    {
        self.directoryId = directoryId
        self.shareMethod = shareMethod
        self.shareNotes = shareNotes
        self.shareTarget = shareTarget
    }
}

extension ShareDirectoryInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ShareDirectoryInput(directoryId: \(Swift.String(describing: directoryId)), shareMethod: \(Swift.String(describing: shareMethod)), shareTarget: \(Swift.String(describing: shareTarget)), shareNotes: \"CONTENT_REDACTED\")"}
}

public struct ShareDirectoryOutput: Swift.Sendable {
    /// Identifier of the directory that is stored in the directory consumer account that is shared from the specified directory (DirectoryId).
    public var sharedDirectoryId: Swift.String?

    public init(
        sharedDirectoryId: Swift.String? = nil
    )
    {
        self.sharedDirectoryId = sharedDirectoryId
    }
}

public struct StartSchemaExtensionInput: Swift.Sendable {
    /// If true, creates a snapshot of the directory before applying the schema extension.
    /// This member is required.
    public var createSnapshotBeforeSchemaExtension: Swift.Bool?
    /// A description of the schema extension.
    /// This member is required.
    public var description: Swift.String?
    /// The identifier of the directory for which the schema extension will be applied to.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The LDIF file represented as a string. To construct the LdifContent string, precede each line as it would be formatted in an ldif file with \n. See the example request below for more details. The file size can be no larger than 1MB.
    /// This member is required.
    public var ldifContent: Swift.String?

    public init(
        createSnapshotBeforeSchemaExtension: Swift.Bool? = false,
        description: Swift.String? = nil,
        directoryId: Swift.String? = nil,
        ldifContent: Swift.String? = nil
    )
    {
        self.createSnapshotBeforeSchemaExtension = createSnapshotBeforeSchemaExtension
        self.description = description
        self.directoryId = directoryId
        self.ldifContent = ldifContent
    }
}

public struct StartSchemaExtensionOutput: Swift.Sendable {
    /// The identifier of the schema extension that will be applied.
    public var schemaExtensionId: Swift.String?

    public init(
        schemaExtensionId: Swift.String? = nil
    )
    {
        self.schemaExtensionId = schemaExtensionId
    }
}

extension DirectoryClientTypes {

    /// Identifier that contains details about the directory consumer account with whom the directory is being unshared.
    public struct UnshareTarget: Swift.Sendable {
        /// Identifier of the directory consumer account.
        /// This member is required.
        public var id: Swift.String?
        /// Type of identifier to be used in the Id field.
        /// This member is required.
        public var type: DirectoryClientTypes.TargetType?

        public init(
            id: Swift.String? = nil,
            type: DirectoryClientTypes.TargetType? = nil
        )
        {
            self.id = id
            self.type = type
        }
    }
}

public struct UnshareDirectoryInput: Swift.Sendable {
    /// The identifier of the Managed Microsoft AD directory that you want to stop sharing.
    /// This member is required.
    public var directoryId: Swift.String?
    /// Identifier for the directory consumer account with whom the directory has to be unshared.
    /// This member is required.
    public var unshareTarget: DirectoryClientTypes.UnshareTarget?

    public init(
        directoryId: Swift.String? = nil,
        unshareTarget: DirectoryClientTypes.UnshareTarget? = nil
    )
    {
        self.directoryId = directoryId
        self.unshareTarget = unshareTarget
    }
}

public struct UnshareDirectoryOutput: Swift.Sendable {
    /// Identifier of the directory stored in the directory consumer account that is to be unshared from the specified directory (DirectoryId).
    public var sharedDirectoryId: Swift.String?

    public init(
        sharedDirectoryId: Swift.String? = nil
    )
    {
        self.sharedDirectoryId = sharedDirectoryId
    }
}

/// Updates a conditional forwarder.
public struct UpdateConditionalForwarderInput: Swift.Sendable {
    /// The directory ID of the Amazon Web Services directory for which to update the conditional forwarder.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The updated IP addresses of the remote DNS server associated with the conditional forwarder.
    /// This member is required.
    public var dnsIpAddrs: [Swift.String]?
    /// The fully qualified domain name (FQDN) of the remote domain with which you will set up a trust relationship.
    /// This member is required.
    public var remoteDomainName: Swift.String?

    public init(
        directoryId: Swift.String? = nil,
        dnsIpAddrs: [Swift.String]? = nil,
        remoteDomainName: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
        self.dnsIpAddrs = dnsIpAddrs
        self.remoteDomainName = remoteDomainName
    }
}

/// The result of an UpdateConditionalForwarder request.
public struct UpdateConditionalForwarderOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateDirectorySetupInput: Swift.Sendable {
    /// The boolean that specifies if a snapshot for the directory needs to be taken before updating the directory.
    public var createSnapshotBeforeUpdate: Swift.Bool?
    /// The identifier of the directory on which you want to perform the update.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The settings for the OS update that needs to be performed on the directory.
    public var osUpdateSettings: DirectoryClientTypes.OSUpdateSettings?
    /// The type of update that needs to be performed on the directory. For example, OS.
    /// This member is required.
    public var updateType: DirectoryClientTypes.UpdateType?

    public init(
        createSnapshotBeforeUpdate: Swift.Bool? = false,
        directoryId: Swift.String? = nil,
        osUpdateSettings: DirectoryClientTypes.OSUpdateSettings? = nil,
        updateType: DirectoryClientTypes.UpdateType? = nil
    )
    {
        self.createSnapshotBeforeUpdate = createSnapshotBeforeUpdate
        self.directoryId = directoryId
        self.osUpdateSettings = osUpdateSettings
        self.updateType = updateType
    }
}

public struct UpdateDirectorySetupOutput: Swift.Sendable {

    public init() { }
}

/// The maximum allowed number of domain controllers per directory was exceeded. The default limit per directory is 20 domain controllers.
public struct DomainControllerLimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DomainControllerLimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

public struct UpdateNumberOfDomainControllersInput: Swift.Sendable {
    /// The number of domain controllers desired in the directory.
    /// This member is required.
    public var desiredNumber: Swift.Int?
    /// Identifier of the directory to which the domain controllers will be added or removed.
    /// This member is required.
    public var directoryId: Swift.String?

    public init(
        desiredNumber: Swift.Int? = nil,
        directoryId: Swift.String? = nil
    )
    {
        self.desiredNumber = desiredNumber
        self.directoryId = directoryId
    }
}

public struct UpdateNumberOfDomainControllersOutput: Swift.Sendable {

    public init() { }
}

/// Contains the inputs for the [UpdateRadius] operation.
public struct UpdateRadiusInput: Swift.Sendable {
    /// The identifier of the directory for which to update the RADIUS server information.
    /// This member is required.
    public var directoryId: Swift.String?
    /// A [RadiusSettings] object that contains information about the RADIUS server.
    /// This member is required.
    public var radiusSettings: DirectoryClientTypes.RadiusSettings?

    public init(
        directoryId: Swift.String? = nil,
        radiusSettings: DirectoryClientTypes.RadiusSettings? = nil
    )
    {
        self.directoryId = directoryId
        self.radiusSettings = radiusSettings
    }
}

/// Contains the results of the [UpdateRadius] operation.
public struct UpdateRadiusOutput: Swift.Sendable {

    public init() { }
}

/// The specified directory setting is not compatible with other settings.
public struct IncompatibleSettingsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "IncompatibleSettingsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

/// The specified directory setting is not supported.
public struct UnsupportedSettingsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The descriptive message for the exception.
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services request identifier.
        public internal(set) var requestId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UnsupportedSettingsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        requestId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.requestId = requestId
    }
}

extension DirectoryClientTypes {

    /// Contains information about the configurable settings for a directory.
    public struct Setting: Swift.Sendable {
        /// The name of the directory setting. For example: TLS_1_0
        /// This member is required.
        public var name: Swift.String?
        /// The value of the directory setting for which to retrieve information. For example, for TLS_1_0, the valid values are: Enable and Disable.
        /// This member is required.
        public var value: Swift.String?

        public init(
            name: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.name = name
            self.value = value
        }
    }
}

public struct UpdateSettingsInput: Swift.Sendable {
    /// The identifier of the directory for which to update settings.
    /// This member is required.
    public var directoryId: Swift.String?
    /// The list of [Setting] objects.
    /// This member is required.
    public var settings: [DirectoryClientTypes.Setting]?

    public init(
        directoryId: Swift.String? = nil,
        settings: [DirectoryClientTypes.Setting]? = nil
    )
    {
        self.directoryId = directoryId
        self.settings = settings
    }
}

public struct UpdateSettingsOutput: Swift.Sendable {
    /// The identifier of the directory.
    public var directoryId: Swift.String?

    public init(
        directoryId: Swift.String? = nil
    )
    {
        self.directoryId = directoryId
    }
}

public struct UpdateTrustInput: Swift.Sendable {
    /// Updates selective authentication for the trust.
    public var selectiveAuth: DirectoryClientTypes.SelectiveAuth?
    /// Identifier of the trust relationship.
    /// This member is required.
    public var trustId: Swift.String?

    public init(
        selectiveAuth: DirectoryClientTypes.SelectiveAuth? = nil,
        trustId: Swift.String? = nil
    )
    {
        self.selectiveAuth = selectiveAuth
        self.trustId = trustId
    }
}

public struct UpdateTrustOutput: Swift.Sendable {
    /// The Amazon Web Services request identifier.
    public var requestId: Swift.String?
    /// Identifier of the trust relationship.
    public var trustId: Swift.String?

    public init(
        requestId: Swift.String? = nil,
        trustId: Swift.String? = nil
    )
    {
        self.requestId = requestId
        self.trustId = trustId
    }
}

/// Initiates the verification of an existing trust relationship between an Managed Microsoft AD directory and an external domain.
public struct VerifyTrustInput: Swift.Sendable {
    /// The unique Trust ID of the trust relationship to verify.
    /// This member is required.
    public var trustId: Swift.String?

    public init(
        trustId: Swift.String? = nil
    )
    {
        self.trustId = trustId
    }
}

/// Result of a VerifyTrust request.
public struct VerifyTrustOutput: Swift.Sendable {
    /// The unique Trust ID of the trust relationship that was verified.
    public var trustId: Swift.String?

    public init(
        trustId: Swift.String? = nil
    )
    {
        self.trustId = trustId
    }
}

extension AcceptSharedDirectoryInput {

    static func urlPathProvider(_ value: AcceptSharedDirectoryInput) -> Swift.String? {
        return "/"
    }
}

extension AddIpRoutesInput {

    static func urlPathProvider(_ value: AddIpRoutesInput) -> Swift.String? {
        return "/"
    }
}

extension AddRegionInput {

    static func urlPathProvider(_ value: AddRegionInput) -> Swift.String? {
        return "/"
    }
}

extension AddTagsToResourceInput {

    static func urlPathProvider(_ value: AddTagsToResourceInput) -> Swift.String? {
        return "/"
    }
}

extension CancelSchemaExtensionInput {

    static func urlPathProvider(_ value: CancelSchemaExtensionInput) -> Swift.String? {
        return "/"
    }
}

extension ConnectDirectoryInput {

    static func urlPathProvider(_ value: ConnectDirectoryInput) -> Swift.String? {
        return "/"
    }
}

extension CreateAliasInput {

    static func urlPathProvider(_ value: CreateAliasInput) -> Swift.String? {
        return "/"
    }
}

extension CreateComputerInput {

    static func urlPathProvider(_ value: CreateComputerInput) -> Swift.String? {
        return "/"
    }
}

extension CreateConditionalForwarderInput {

    static func urlPathProvider(_ value: CreateConditionalForwarderInput) -> Swift.String? {
        return "/"
    }
}

extension CreateDirectoryInput {

    static func urlPathProvider(_ value: CreateDirectoryInput) -> Swift.String? {
        return "/"
    }
}

extension CreateLogSubscriptionInput {

    static func urlPathProvider(_ value: CreateLogSubscriptionInput) -> Swift.String? {
        return "/"
    }
}

extension CreateMicrosoftADInput {

    static func urlPathProvider(_ value: CreateMicrosoftADInput) -> Swift.String? {
        return "/"
    }
}

extension CreateSnapshotInput {

    static func urlPathProvider(_ value: CreateSnapshotInput) -> Swift.String? {
        return "/"
    }
}

extension CreateTrustInput {

    static func urlPathProvider(_ value: CreateTrustInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteConditionalForwarderInput {

    static func urlPathProvider(_ value: DeleteConditionalForwarderInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteDirectoryInput {

    static func urlPathProvider(_ value: DeleteDirectoryInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteLogSubscriptionInput {

    static func urlPathProvider(_ value: DeleteLogSubscriptionInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteSnapshotInput {

    static func urlPathProvider(_ value: DeleteSnapshotInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteTrustInput {

    static func urlPathProvider(_ value: DeleteTrustInput) -> Swift.String? {
        return "/"
    }
}

extension DeregisterCertificateInput {

    static func urlPathProvider(_ value: DeregisterCertificateInput) -> Swift.String? {
        return "/"
    }
}

extension DeregisterEventTopicInput {

    static func urlPathProvider(_ value: DeregisterEventTopicInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeCertificateInput {

    static func urlPathProvider(_ value: DescribeCertificateInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeClientAuthenticationSettingsInput {

    static func urlPathProvider(_ value: DescribeClientAuthenticationSettingsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeConditionalForwardersInput {

    static func urlPathProvider(_ value: DescribeConditionalForwardersInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeDirectoriesInput {

    static func urlPathProvider(_ value: DescribeDirectoriesInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeDirectoryDataAccessInput {

    static func urlPathProvider(_ value: DescribeDirectoryDataAccessInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeDomainControllersInput {

    static func urlPathProvider(_ value: DescribeDomainControllersInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeEventTopicsInput {

    static func urlPathProvider(_ value: DescribeEventTopicsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeLDAPSSettingsInput {

    static func urlPathProvider(_ value: DescribeLDAPSSettingsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeRegionsInput {

    static func urlPathProvider(_ value: DescribeRegionsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeSettingsInput {

    static func urlPathProvider(_ value: DescribeSettingsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeSharedDirectoriesInput {

    static func urlPathProvider(_ value: DescribeSharedDirectoriesInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeSnapshotsInput {

    static func urlPathProvider(_ value: DescribeSnapshotsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeTrustsInput {

    static func urlPathProvider(_ value: DescribeTrustsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeUpdateDirectoryInput {

    static func urlPathProvider(_ value: DescribeUpdateDirectoryInput) -> Swift.String? {
        return "/"
    }
}

extension DisableClientAuthenticationInput {

    static func urlPathProvider(_ value: DisableClientAuthenticationInput) -> Swift.String? {
        return "/"
    }
}

extension DisableDirectoryDataAccessInput {

    static func urlPathProvider(_ value: DisableDirectoryDataAccessInput) -> Swift.String? {
        return "/"
    }
}

extension DisableLDAPSInput {

    static func urlPathProvider(_ value: DisableLDAPSInput) -> Swift.String? {
        return "/"
    }
}

extension DisableRadiusInput {

    static func urlPathProvider(_ value: DisableRadiusInput) -> Swift.String? {
        return "/"
    }
}

extension DisableSsoInput {

    static func urlPathProvider(_ value: DisableSsoInput) -> Swift.String? {
        return "/"
    }
}

extension EnableClientAuthenticationInput {

    static func urlPathProvider(_ value: EnableClientAuthenticationInput) -> Swift.String? {
        return "/"
    }
}

extension EnableDirectoryDataAccessInput {

    static func urlPathProvider(_ value: EnableDirectoryDataAccessInput) -> Swift.String? {
        return "/"
    }
}

extension EnableLDAPSInput {

    static func urlPathProvider(_ value: EnableLDAPSInput) -> Swift.String? {
        return "/"
    }
}

extension EnableRadiusInput {

    static func urlPathProvider(_ value: EnableRadiusInput) -> Swift.String? {
        return "/"
    }
}

extension EnableSsoInput {

    static func urlPathProvider(_ value: EnableSsoInput) -> Swift.String? {
        return "/"
    }
}

extension GetDirectoryLimitsInput {

    static func urlPathProvider(_ value: GetDirectoryLimitsInput) -> Swift.String? {
        return "/"
    }
}

extension GetSnapshotLimitsInput {

    static func urlPathProvider(_ value: GetSnapshotLimitsInput) -> Swift.String? {
        return "/"
    }
}

extension ListCertificatesInput {

    static func urlPathProvider(_ value: ListCertificatesInput) -> Swift.String? {
        return "/"
    }
}

extension ListIpRoutesInput {

    static func urlPathProvider(_ value: ListIpRoutesInput) -> Swift.String? {
        return "/"
    }
}

extension ListLogSubscriptionsInput {

    static func urlPathProvider(_ value: ListLogSubscriptionsInput) -> Swift.String? {
        return "/"
    }
}

extension ListSchemaExtensionsInput {

    static func urlPathProvider(_ value: ListSchemaExtensionsInput) -> Swift.String? {
        return "/"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        return "/"
    }
}

extension RegisterCertificateInput {

    static func urlPathProvider(_ value: RegisterCertificateInput) -> Swift.String? {
        return "/"
    }
}

extension RegisterEventTopicInput {

    static func urlPathProvider(_ value: RegisterEventTopicInput) -> Swift.String? {
        return "/"
    }
}

extension RejectSharedDirectoryInput {

    static func urlPathProvider(_ value: RejectSharedDirectoryInput) -> Swift.String? {
        return "/"
    }
}

extension RemoveIpRoutesInput {

    static func urlPathProvider(_ value: RemoveIpRoutesInput) -> Swift.String? {
        return "/"
    }
}

extension RemoveRegionInput {

    static func urlPathProvider(_ value: RemoveRegionInput) -> Swift.String? {
        return "/"
    }
}

extension RemoveTagsFromResourceInput {

    static func urlPathProvider(_ value: RemoveTagsFromResourceInput) -> Swift.String? {
        return "/"
    }
}

extension ResetUserPasswordInput {

    static func urlPathProvider(_ value: ResetUserPasswordInput) -> Swift.String? {
        return "/"
    }
}

extension RestoreFromSnapshotInput {

    static func urlPathProvider(_ value: RestoreFromSnapshotInput) -> Swift.String? {
        return "/"
    }
}

extension ShareDirectoryInput {

    static func urlPathProvider(_ value: ShareDirectoryInput) -> Swift.String? {
        return "/"
    }
}

extension StartSchemaExtensionInput {

    static func urlPathProvider(_ value: StartSchemaExtensionInput) -> Swift.String? {
        return "/"
    }
}

extension UnshareDirectoryInput {

    static func urlPathProvider(_ value: UnshareDirectoryInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateConditionalForwarderInput {

    static func urlPathProvider(_ value: UpdateConditionalForwarderInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateDirectorySetupInput {

    static func urlPathProvider(_ value: UpdateDirectorySetupInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateNumberOfDomainControllersInput {

    static func urlPathProvider(_ value: UpdateNumberOfDomainControllersInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateRadiusInput {

    static func urlPathProvider(_ value: UpdateRadiusInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateSettingsInput {

    static func urlPathProvider(_ value: UpdateSettingsInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateTrustInput {

    static func urlPathProvider(_ value: UpdateTrustInput) -> Swift.String? {
        return "/"
    }
}

extension VerifyTrustInput {

    static func urlPathProvider(_ value: VerifyTrustInput) -> Swift.String? {
        return "/"
    }
}

extension AcceptSharedDirectoryInput {

    static func write(value: AcceptSharedDirectoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["SharedDirectoryId"].write(value.sharedDirectoryId)
    }
}

extension AddIpRoutesInput {

    static func write(value: AddIpRoutesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["IpRoutes"].writeList(value.ipRoutes, memberWritingClosure: DirectoryClientTypes.IpRoute.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["UpdateSecurityGroupForDirectoryControllers"].write(value.updateSecurityGroupForDirectoryControllers)
    }
}

extension AddRegionInput {

    static func write(value: AddRegionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["RegionName"].write(value.regionName)
        try writer["VPCSettings"].write(value.vpcSettings, with: DirectoryClientTypes.DirectoryVpcSettings.write(value:to:))
    }
}

extension AddTagsToResourceInput {

    static func write(value: AddTagsToResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceId"].write(value.resourceId)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DirectoryClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CancelSchemaExtensionInput {

    static func write(value: CancelSchemaExtensionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["SchemaExtensionId"].write(value.schemaExtensionId)
    }
}

extension ConnectDirectoryInput {

    static func write(value: ConnectDirectoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ConnectSettings"].write(value.connectSettings, with: DirectoryClientTypes.DirectoryConnectSettings.write(value:to:))
        try writer["Description"].write(value.description)
        try writer["Name"].write(value.name)
        try writer["Password"].write(value.password)
        try writer["ShortName"].write(value.shortName)
        try writer["Size"].write(value.size)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DirectoryClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateAliasInput {

    static func write(value: CreateAliasInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Alias"].write(value.alias)
        try writer["DirectoryId"].write(value.directoryId)
    }
}

extension CreateComputerInput {

    static func write(value: CreateComputerInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ComputerAttributes"].writeList(value.computerAttributes, memberWritingClosure: DirectoryClientTypes.Attribute.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ComputerName"].write(value.computerName)
        try writer["DirectoryId"].write(value.directoryId)
        try writer["OrganizationalUnitDistinguishedName"].write(value.organizationalUnitDistinguishedName)
        try writer["Password"].write(value.password)
    }
}

extension CreateConditionalForwarderInput {

    static func write(value: CreateConditionalForwarderInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["DnsIpAddrs"].writeList(value.dnsIpAddrs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["RemoteDomainName"].write(value.remoteDomainName)
    }
}

extension CreateDirectoryInput {

    static func write(value: CreateDirectoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Description"].write(value.description)
        try writer["Name"].write(value.name)
        try writer["Password"].write(value.password)
        try writer["ShortName"].write(value.shortName)
        try writer["Size"].write(value.size)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DirectoryClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["VpcSettings"].write(value.vpcSettings, with: DirectoryClientTypes.DirectoryVpcSettings.write(value:to:))
    }
}

extension CreateLogSubscriptionInput {

    static func write(value: CreateLogSubscriptionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["LogGroupName"].write(value.logGroupName)
    }
}

extension CreateMicrosoftADInput {

    static func write(value: CreateMicrosoftADInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Description"].write(value.description)
        try writer["Edition"].write(value.edition)
        try writer["Name"].write(value.name)
        try writer["Password"].write(value.password)
        try writer["ShortName"].write(value.shortName)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DirectoryClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["VpcSettings"].write(value.vpcSettings, with: DirectoryClientTypes.DirectoryVpcSettings.write(value:to:))
    }
}

extension CreateSnapshotInput {

    static func write(value: CreateSnapshotInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Name"].write(value.name)
    }
}

extension CreateTrustInput {

    static func write(value: CreateTrustInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ConditionalForwarderIpAddrs"].writeList(value.conditionalForwarderIpAddrs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["DirectoryId"].write(value.directoryId)
        try writer["RemoteDomainName"].write(value.remoteDomainName)
        try writer["SelectiveAuth"].write(value.selectiveAuth)
        try writer["TrustDirection"].write(value.trustDirection)
        try writer["TrustPassword"].write(value.trustPassword)
        try writer["TrustType"].write(value.trustType)
    }
}

extension DeleteConditionalForwarderInput {

    static func write(value: DeleteConditionalForwarderInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["RemoteDomainName"].write(value.remoteDomainName)
    }
}

extension DeleteDirectoryInput {

    static func write(value: DeleteDirectoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
    }
}

extension DeleteLogSubscriptionInput {

    static func write(value: DeleteLogSubscriptionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
    }
}

extension DeleteSnapshotInput {

    static func write(value: DeleteSnapshotInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["SnapshotId"].write(value.snapshotId)
    }
}

extension DeleteTrustInput {

    static func write(value: DeleteTrustInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DeleteAssociatedConditionalForwarder"].write(value.deleteAssociatedConditionalForwarder)
        try writer["TrustId"].write(value.trustId)
    }
}

extension DeregisterCertificateInput {

    static func write(value: DeregisterCertificateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CertificateId"].write(value.certificateId)
        try writer["DirectoryId"].write(value.directoryId)
    }
}

extension DeregisterEventTopicInput {

    static func write(value: DeregisterEventTopicInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["TopicName"].write(value.topicName)
    }
}

extension DescribeCertificateInput {

    static func write(value: DescribeCertificateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CertificateId"].write(value.certificateId)
        try writer["DirectoryId"].write(value.directoryId)
    }
}

extension DescribeClientAuthenticationSettingsInput {

    static func write(value: DescribeClientAuthenticationSettingsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Limit"].write(value.limit)
        try writer["NextToken"].write(value.nextToken)
        try writer["Type"].write(value.type)
    }
}

extension DescribeConditionalForwardersInput {

    static func write(value: DescribeConditionalForwardersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["RemoteDomainNames"].writeList(value.remoteDomainNames, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DescribeDirectoriesInput {

    static func write(value: DescribeDirectoriesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryIds"].writeList(value.directoryIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Limit"].write(value.limit)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension DescribeDirectoryDataAccessInput {

    static func write(value: DescribeDirectoryDataAccessInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
    }
}

extension DescribeDomainControllersInput {

    static func write(value: DescribeDomainControllersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["DomainControllerIds"].writeList(value.domainControllerIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Limit"].write(value.limit)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension DescribeEventTopicsInput {

    static func write(value: DescribeEventTopicsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["TopicNames"].writeList(value.topicNames, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DescribeLDAPSSettingsInput {

    static func write(value: DescribeLDAPSSettingsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Limit"].write(value.limit)
        try writer["NextToken"].write(value.nextToken)
        try writer["Type"].write(value.type)
    }
}

extension DescribeRegionsInput {

    static func write(value: DescribeRegionsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["NextToken"].write(value.nextToken)
        try writer["RegionName"].write(value.regionName)
    }
}

extension DescribeSettingsInput {

    static func write(value: DescribeSettingsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["NextToken"].write(value.nextToken)
        try writer["Status"].write(value.status)
    }
}

extension DescribeSharedDirectoriesInput {

    static func write(value: DescribeSharedDirectoriesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Limit"].write(value.limit)
        try writer["NextToken"].write(value.nextToken)
        try writer["OwnerDirectoryId"].write(value.ownerDirectoryId)
        try writer["SharedDirectoryIds"].writeList(value.sharedDirectoryIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DescribeSnapshotsInput {

    static func write(value: DescribeSnapshotsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Limit"].write(value.limit)
        try writer["NextToken"].write(value.nextToken)
        try writer["SnapshotIds"].writeList(value.snapshotIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DescribeTrustsInput {

    static func write(value: DescribeTrustsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Limit"].write(value.limit)
        try writer["NextToken"].write(value.nextToken)
        try writer["TrustIds"].writeList(value.trustIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DescribeUpdateDirectoryInput {

    static func write(value: DescribeUpdateDirectoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["NextToken"].write(value.nextToken)
        try writer["RegionName"].write(value.regionName)
        try writer["UpdateType"].write(value.updateType)
    }
}

extension DisableClientAuthenticationInput {

    static func write(value: DisableClientAuthenticationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Type"].write(value.type)
    }
}

extension DisableDirectoryDataAccessInput {

    static func write(value: DisableDirectoryDataAccessInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
    }
}

extension DisableLDAPSInput {

    static func write(value: DisableLDAPSInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Type"].write(value.type)
    }
}

extension DisableRadiusInput {

    static func write(value: DisableRadiusInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
    }
}

extension DisableSsoInput {

    static func write(value: DisableSsoInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Password"].write(value.password)
        try writer["UserName"].write(value.userName)
    }
}

extension EnableClientAuthenticationInput {

    static func write(value: EnableClientAuthenticationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Type"].write(value.type)
    }
}

extension EnableDirectoryDataAccessInput {

    static func write(value: EnableDirectoryDataAccessInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
    }
}

extension EnableLDAPSInput {

    static func write(value: EnableLDAPSInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Type"].write(value.type)
    }
}

extension EnableRadiusInput {

    static func write(value: EnableRadiusInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["RadiusSettings"].write(value.radiusSettings, with: DirectoryClientTypes.RadiusSettings.write(value:to:))
    }
}

extension EnableSsoInput {

    static func write(value: EnableSsoInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Password"].write(value.password)
        try writer["UserName"].write(value.userName)
    }
}

extension GetDirectoryLimitsInput {

    static func write(value: GetDirectoryLimitsInput?, to writer: SmithyJSON.Writer) throws {
        guard value != nil else { return }
        _ = writer[""]  // create an empty structure
    }
}

extension GetSnapshotLimitsInput {

    static func write(value: GetSnapshotLimitsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
    }
}

extension ListCertificatesInput {

    static func write(value: ListCertificatesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Limit"].write(value.limit)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListIpRoutesInput {

    static func write(value: ListIpRoutesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Limit"].write(value.limit)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListLogSubscriptionsInput {

    static func write(value: ListLogSubscriptionsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Limit"].write(value.limit)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListSchemaExtensionsInput {

    static func write(value: ListSchemaExtensionsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Limit"].write(value.limit)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListTagsForResourceInput {

    static func write(value: ListTagsForResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Limit"].write(value.limit)
        try writer["NextToken"].write(value.nextToken)
        try writer["ResourceId"].write(value.resourceId)
    }
}

extension RegisterCertificateInput {

    static func write(value: RegisterCertificateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CertificateData"].write(value.certificateData)
        try writer["ClientCertAuthSettings"].write(value.clientCertAuthSettings, with: DirectoryClientTypes.ClientCertAuthSettings.write(value:to:))
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Type"].write(value.type)
    }
}

extension RegisterEventTopicInput {

    static func write(value: RegisterEventTopicInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["TopicName"].write(value.topicName)
    }
}

extension RejectSharedDirectoryInput {

    static func write(value: RejectSharedDirectoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["SharedDirectoryId"].write(value.sharedDirectoryId)
    }
}

extension RemoveIpRoutesInput {

    static func write(value: RemoveIpRoutesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CidrIps"].writeList(value.cidrIps, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["DirectoryId"].write(value.directoryId)
    }
}

extension RemoveRegionInput {

    static func write(value: RemoveRegionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
    }
}

extension RemoveTagsFromResourceInput {

    static func write(value: RemoveTagsFromResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceId"].write(value.resourceId)
        try writer["TagKeys"].writeList(value.tagKeys, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ResetUserPasswordInput {

    static func write(value: ResetUserPasswordInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["NewPassword"].write(value.newPassword)
        try writer["UserName"].write(value.userName)
    }
}

extension RestoreFromSnapshotInput {

    static func write(value: RestoreFromSnapshotInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["SnapshotId"].write(value.snapshotId)
    }
}

extension ShareDirectoryInput {

    static func write(value: ShareDirectoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["ShareMethod"].write(value.shareMethod)
        try writer["ShareNotes"].write(value.shareNotes)
        try writer["ShareTarget"].write(value.shareTarget, with: DirectoryClientTypes.ShareTarget.write(value:to:))
    }
}

extension StartSchemaExtensionInput {

    static func write(value: StartSchemaExtensionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CreateSnapshotBeforeSchemaExtension"].write(value.createSnapshotBeforeSchemaExtension)
        try writer["Description"].write(value.description)
        try writer["DirectoryId"].write(value.directoryId)
        try writer["LdifContent"].write(value.ldifContent)
    }
}

extension UnshareDirectoryInput {

    static func write(value: UnshareDirectoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["UnshareTarget"].write(value.unshareTarget, with: DirectoryClientTypes.UnshareTarget.write(value:to:))
    }
}

extension UpdateConditionalForwarderInput {

    static func write(value: UpdateConditionalForwarderInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["DnsIpAddrs"].writeList(value.dnsIpAddrs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["RemoteDomainName"].write(value.remoteDomainName)
    }
}

extension UpdateDirectorySetupInput {

    static func write(value: UpdateDirectorySetupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CreateSnapshotBeforeUpdate"].write(value.createSnapshotBeforeUpdate)
        try writer["DirectoryId"].write(value.directoryId)
        try writer["OSUpdateSettings"].write(value.osUpdateSettings, with: DirectoryClientTypes.OSUpdateSettings.write(value:to:))
        try writer["UpdateType"].write(value.updateType)
    }
}

extension UpdateNumberOfDomainControllersInput {

    static func write(value: UpdateNumberOfDomainControllersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DesiredNumber"].write(value.desiredNumber)
        try writer["DirectoryId"].write(value.directoryId)
    }
}

extension UpdateRadiusInput {

    static func write(value: UpdateRadiusInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["RadiusSettings"].write(value.radiusSettings, with: DirectoryClientTypes.RadiusSettings.write(value:to:))
    }
}

extension UpdateSettingsInput {

    static func write(value: UpdateSettingsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DirectoryId"].write(value.directoryId)
        try writer["Settings"].writeList(value.settings, memberWritingClosure: DirectoryClientTypes.Setting.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdateTrustInput {

    static func write(value: UpdateTrustInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["SelectiveAuth"].write(value.selectiveAuth)
        try writer["TrustId"].write(value.trustId)
    }
}

extension VerifyTrustInput {

    static func write(value: VerifyTrustInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["TrustId"].write(value.trustId)
    }
}

extension AcceptSharedDirectoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AcceptSharedDirectoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = AcceptSharedDirectoryOutput()
        value.sharedDirectory = try reader["SharedDirectory"].readIfPresent(with: DirectoryClientTypes.SharedDirectory.read(from:))
        return value
    }
}

extension AddIpRoutesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AddIpRoutesOutput {
        return AddIpRoutesOutput()
    }
}

extension AddRegionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AddRegionOutput {
        return AddRegionOutput()
    }
}

extension AddTagsToResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AddTagsToResourceOutput {
        return AddTagsToResourceOutput()
    }
}

extension CancelSchemaExtensionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CancelSchemaExtensionOutput {
        return CancelSchemaExtensionOutput()
    }
}

extension ConnectDirectoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ConnectDirectoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ConnectDirectoryOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        return value
    }
}

extension CreateAliasOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateAliasOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateAliasOutput()
        value.alias = try reader["Alias"].readIfPresent()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        return value
    }
}

extension CreateComputerOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateComputerOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateComputerOutput()
        value.computer = try reader["Computer"].readIfPresent(with: DirectoryClientTypes.Computer.read(from:))
        return value
    }
}

extension CreateConditionalForwarderOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateConditionalForwarderOutput {
        return CreateConditionalForwarderOutput()
    }
}

extension CreateDirectoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateDirectoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateDirectoryOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        return value
    }
}

extension CreateLogSubscriptionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLogSubscriptionOutput {
        return CreateLogSubscriptionOutput()
    }
}

extension CreateMicrosoftADOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateMicrosoftADOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateMicrosoftADOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        return value
    }
}

extension CreateSnapshotOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateSnapshotOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateSnapshotOutput()
        value.snapshotId = try reader["SnapshotId"].readIfPresent()
        return value
    }
}

extension CreateTrustOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateTrustOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateTrustOutput()
        value.trustId = try reader["TrustId"].readIfPresent()
        return value
    }
}

extension DeleteConditionalForwarderOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteConditionalForwarderOutput {
        return DeleteConditionalForwarderOutput()
    }
}

extension DeleteDirectoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteDirectoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteDirectoryOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        return value
    }
}

extension DeleteLogSubscriptionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteLogSubscriptionOutput {
        return DeleteLogSubscriptionOutput()
    }
}

extension DeleteSnapshotOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteSnapshotOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteSnapshotOutput()
        value.snapshotId = try reader["SnapshotId"].readIfPresent()
        return value
    }
}

extension DeleteTrustOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteTrustOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteTrustOutput()
        value.trustId = try reader["TrustId"].readIfPresent()
        return value
    }
}

extension DeregisterCertificateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeregisterCertificateOutput {
        return DeregisterCertificateOutput()
    }
}

extension DeregisterEventTopicOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeregisterEventTopicOutput {
        return DeregisterEventTopicOutput()
    }
}

extension DescribeCertificateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeCertificateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeCertificateOutput()
        value.certificate = try reader["Certificate"].readIfPresent(with: DirectoryClientTypes.Certificate.read(from:))
        return value
    }
}

extension DescribeClientAuthenticationSettingsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeClientAuthenticationSettingsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeClientAuthenticationSettingsOutput()
        value.clientAuthenticationSettingsInfo = try reader["ClientAuthenticationSettingsInfo"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.ClientAuthenticationSettingInfo.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension DescribeConditionalForwardersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeConditionalForwardersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeConditionalForwardersOutput()
        value.conditionalForwarders = try reader["ConditionalForwarders"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.ConditionalForwarder.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeDirectoriesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDirectoriesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeDirectoriesOutput()
        value.directoryDescriptions = try reader["DirectoryDescriptions"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.DirectoryDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension DescribeDirectoryDataAccessOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDirectoryDataAccessOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeDirectoryDataAccessOutput()
        value.dataAccessStatus = try reader["DataAccessStatus"].readIfPresent()
        return value
    }
}

extension DescribeDomainControllersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDomainControllersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeDomainControllersOutput()
        value.domainControllers = try reader["DomainControllers"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.DomainController.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension DescribeEventTopicsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeEventTopicsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeEventTopicsOutput()
        value.eventTopics = try reader["EventTopics"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.EventTopic.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeLDAPSSettingsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeLDAPSSettingsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeLDAPSSettingsOutput()
        value.ldapsSettingsInfo = try reader["LDAPSSettingsInfo"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.LDAPSSettingInfo.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension DescribeRegionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeRegionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeRegionsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.regionsDescription = try reader["RegionsDescription"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.RegionDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeSettingsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeSettingsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeSettingsOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.settingEntries = try reader["SettingEntries"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.SettingEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeSharedDirectoriesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeSharedDirectoriesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeSharedDirectoriesOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.sharedDirectories = try reader["SharedDirectories"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.SharedDirectory.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeSnapshotsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeSnapshotsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeSnapshotsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.snapshots = try reader["Snapshots"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.Snapshot.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeTrustsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeTrustsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeTrustsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.trusts = try reader["Trusts"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.Trust.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeUpdateDirectoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeUpdateDirectoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeUpdateDirectoryOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.updateActivities = try reader["UpdateActivities"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.UpdateInfoEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DisableClientAuthenticationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisableClientAuthenticationOutput {
        return DisableClientAuthenticationOutput()
    }
}

extension DisableDirectoryDataAccessOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisableDirectoryDataAccessOutput {
        return DisableDirectoryDataAccessOutput()
    }
}

extension DisableLDAPSOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisableLDAPSOutput {
        return DisableLDAPSOutput()
    }
}

extension DisableRadiusOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisableRadiusOutput {
        return DisableRadiusOutput()
    }
}

extension DisableSsoOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisableSsoOutput {
        return DisableSsoOutput()
    }
}

extension EnableClientAuthenticationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> EnableClientAuthenticationOutput {
        return EnableClientAuthenticationOutput()
    }
}

extension EnableDirectoryDataAccessOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> EnableDirectoryDataAccessOutput {
        return EnableDirectoryDataAccessOutput()
    }
}

extension EnableLDAPSOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> EnableLDAPSOutput {
        return EnableLDAPSOutput()
    }
}

extension EnableRadiusOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> EnableRadiusOutput {
        return EnableRadiusOutput()
    }
}

extension EnableSsoOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> EnableSsoOutput {
        return EnableSsoOutput()
    }
}

extension GetDirectoryLimitsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetDirectoryLimitsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetDirectoryLimitsOutput()
        value.directoryLimits = try reader["DirectoryLimits"].readIfPresent(with: DirectoryClientTypes.DirectoryLimits.read(from:))
        return value
    }
}

extension GetSnapshotLimitsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetSnapshotLimitsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetSnapshotLimitsOutput()
        value.snapshotLimits = try reader["SnapshotLimits"].readIfPresent(with: DirectoryClientTypes.SnapshotLimits.read(from:))
        return value
    }
}

extension ListCertificatesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListCertificatesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListCertificatesOutput()
        value.certificatesInfo = try reader["CertificatesInfo"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.CertificateInfo.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListIpRoutesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListIpRoutesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListIpRoutesOutput()
        value.ipRoutesInfo = try reader["IpRoutesInfo"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.IpRouteInfo.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListLogSubscriptionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListLogSubscriptionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListLogSubscriptionsOutput()
        value.logSubscriptions = try reader["LogSubscriptions"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.LogSubscription.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListSchemaExtensionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListSchemaExtensionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListSchemaExtensionsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.schemaExtensionsInfo = try reader["SchemaExtensionsInfo"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.SchemaExtensionInfo.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension RegisterCertificateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RegisterCertificateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = RegisterCertificateOutput()
        value.certificateId = try reader["CertificateId"].readIfPresent()
        return value
    }
}

extension RegisterEventTopicOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RegisterEventTopicOutput {
        return RegisterEventTopicOutput()
    }
}

extension RejectSharedDirectoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RejectSharedDirectoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = RejectSharedDirectoryOutput()
        value.sharedDirectoryId = try reader["SharedDirectoryId"].readIfPresent()
        return value
    }
}

extension RemoveIpRoutesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RemoveIpRoutesOutput {
        return RemoveIpRoutesOutput()
    }
}

extension RemoveRegionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RemoveRegionOutput {
        return RemoveRegionOutput()
    }
}

extension RemoveTagsFromResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RemoveTagsFromResourceOutput {
        return RemoveTagsFromResourceOutput()
    }
}

extension ResetUserPasswordOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ResetUserPasswordOutput {
        return ResetUserPasswordOutput()
    }
}

extension RestoreFromSnapshotOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RestoreFromSnapshotOutput {
        return RestoreFromSnapshotOutput()
    }
}

extension ShareDirectoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ShareDirectoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ShareDirectoryOutput()
        value.sharedDirectoryId = try reader["SharedDirectoryId"].readIfPresent()
        return value
    }
}

extension StartSchemaExtensionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartSchemaExtensionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartSchemaExtensionOutput()
        value.schemaExtensionId = try reader["SchemaExtensionId"].readIfPresent()
        return value
    }
}

extension UnshareDirectoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UnshareDirectoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UnshareDirectoryOutput()
        value.sharedDirectoryId = try reader["SharedDirectoryId"].readIfPresent()
        return value
    }
}

extension UpdateConditionalForwarderOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateConditionalForwarderOutput {
        return UpdateConditionalForwarderOutput()
    }
}

extension UpdateDirectorySetupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateDirectorySetupOutput {
        return UpdateDirectorySetupOutput()
    }
}

extension UpdateNumberOfDomainControllersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateNumberOfDomainControllersOutput {
        return UpdateNumberOfDomainControllersOutput()
    }
}

extension UpdateRadiusOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateRadiusOutput {
        return UpdateRadiusOutput()
    }
}

extension UpdateSettingsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateSettingsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateSettingsOutput()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        return value
    }
}

extension UpdateTrustOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateTrustOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateTrustOutput()
        value.requestId = try reader["RequestId"].readIfPresent()
        value.trustId = try reader["TrustId"].readIfPresent()
        return value
    }
}

extension VerifyTrustOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> VerifyTrustOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = VerifyTrustOutput()
        value.trustId = try reader["TrustId"].readIfPresent()
        return value
    }
}

enum AcceptSharedDirectoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryAlreadySharedException": return try DirectoryAlreadySharedException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum AddIpRoutesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "EntityAlreadyExistsException": return try EntityAlreadyExistsException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "IpRouteLimitExceededException": return try IpRouteLimitExceededException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum AddRegionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryAlreadyInRegionException": return try DirectoryAlreadyInRegionException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RegionLimitExceededException": return try RegionLimitExceededException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum AddTagsToResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "TagLimitExceededException": return try TagLimitExceededException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CancelSchemaExtensionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ConnectDirectoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryLimitExceededException": return try DirectoryLimitExceededException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateAliasOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityAlreadyExistsException": return try EntityAlreadyExistsException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateComputerOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AuthenticationFailedException": return try AuthenticationFailedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "EntityAlreadyExistsException": return try EntityAlreadyExistsException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateConditionalForwarderOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "EntityAlreadyExistsException": return try EntityAlreadyExistsException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateDirectoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryLimitExceededException": return try DirectoryLimitExceededException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateLogSubscriptionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityAlreadyExistsException": return try EntityAlreadyExistsException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InsufficientPermissionsException": return try InsufficientPermissionsException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateMicrosoftADOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryLimitExceededException": return try DirectoryLimitExceededException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateSnapshotOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "SnapshotLimitExceededException": return try SnapshotLimitExceededException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateTrustOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityAlreadyExistsException": return try EntityAlreadyExistsException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteConditionalForwarderOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteDirectoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteLogSubscriptionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteSnapshotOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteTrustOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeregisterCertificateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "CertificateDoesNotExistException": return try CertificateDoesNotExistException.makeError(baseError: baseError)
            case "CertificateInUseException": return try CertificateInUseException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeregisterEventTopicOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeCertificateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "CertificateDoesNotExistException": return try CertificateDoesNotExistException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeClientAuthenticationSettingsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeConditionalForwardersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDirectoriesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDirectoryDataAccessOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDomainControllersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeEventTopicsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeLDAPSSettingsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeRegionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeSettingsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeSharedDirectoriesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeSnapshotsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeTrustsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeUpdateDirectoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisableClientAuthenticationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "InvalidClientAuthStatusException": return try InvalidClientAuthStatusException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisableDirectoryDataAccessOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "DirectoryInDesiredStateException": return try DirectoryInDesiredStateException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisableLDAPSOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InvalidLDAPSStatusException": return try InvalidLDAPSStatusException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisableRadiusOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisableSsoOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AuthenticationFailedException": return try AuthenticationFailedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InsufficientPermissionsException": return try InsufficientPermissionsException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum EnableClientAuthenticationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "InvalidClientAuthStatusException": return try InvalidClientAuthStatusException.makeError(baseError: baseError)
            case "NoAvailableCertificateException": return try NoAvailableCertificateException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum EnableDirectoryDataAccessOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "DirectoryInDesiredStateException": return try DirectoryInDesiredStateException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum EnableLDAPSOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InvalidLDAPSStatusException": return try InvalidLDAPSStatusException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NoAvailableCertificateException": return try NoAvailableCertificateException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum EnableRadiusOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityAlreadyExistsException": return try EntityAlreadyExistsException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum EnableSsoOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AuthenticationFailedException": return try AuthenticationFailedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InsufficientPermissionsException": return try InsufficientPermissionsException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetDirectoryLimitsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetSnapshotLimitsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListCertificatesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListIpRoutesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListLogSubscriptionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListSchemaExtensionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidNextTokenException": return try InvalidNextTokenException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RegisterCertificateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "CertificateAlreadyExistsException": return try CertificateAlreadyExistsException.makeError(baseError: baseError)
            case "CertificateLimitExceededException": return try CertificateLimitExceededException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InvalidCertificateException": return try InvalidCertificateException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RegisterEventTopicOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RejectSharedDirectoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryAlreadySharedException": return try DirectoryAlreadySharedException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RemoveIpRoutesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RemoveRegionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RemoveTagsFromResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ResetUserPasswordOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidPasswordException": return try InvalidPasswordException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            case "UserDoesNotExistException": return try UserDoesNotExistException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RestoreFromSnapshotOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ShareDirectoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryAlreadySharedException": return try DirectoryAlreadySharedException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "InvalidTargetException": return try InvalidTargetException.makeError(baseError: baseError)
            case "OrganizationsException": return try OrganizationsException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "ShareLimitExceededException": return try ShareLimitExceededException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartSchemaExtensionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "SnapshotLimitExceededException": return try SnapshotLimitExceededException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UnshareDirectoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryNotSharedException": return try DirectoryNotSharedException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidTargetException": return try InvalidTargetException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateConditionalForwarderOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateDirectorySetupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "DirectoryInDesiredStateException": return try DirectoryInDesiredStateException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "SnapshotLimitExceededException": return try SnapshotLimitExceededException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateNumberOfDomainControllersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "DomainControllerLimitExceededException": return try DomainControllerLimitExceededException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateRadiusOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateSettingsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "DirectoryDoesNotExistException": return try DirectoryDoesNotExistException.makeError(baseError: baseError)
            case "DirectoryUnavailableException": return try DirectoryUnavailableException.makeError(baseError: baseError)
            case "IncompatibleSettingsException": return try IncompatibleSettingsException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            case "UnsupportedSettingsException": return try UnsupportedSettingsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateTrustOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum VerifyTrustOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ClientException": return try ClientException.makeError(baseError: baseError)
            case "EntityDoesNotExistException": return try EntityDoesNotExistException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServiceException": return try ServiceException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension EntityDoesNotExistException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> EntityDoesNotExistException {
        let reader = baseError.errorBodyReader
        var value = EntityDoesNotExistException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ServiceException {
        let reader = baseError.errorBodyReader
        var value = ServiceException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DirectoryAlreadySharedException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> DirectoryAlreadySharedException {
        let reader = baseError.errorBodyReader
        var value = DirectoryAlreadySharedException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidParameterException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidParameterException {
        let reader = baseError.errorBodyReader
        var value = InvalidParameterException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ClientException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ClientException {
        let reader = baseError.errorBodyReader
        var value = ClientException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension IpRouteLimitExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> IpRouteLimitExceededException {
        let reader = baseError.errorBodyReader
        var value = IpRouteLimitExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension EntityAlreadyExistsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> EntityAlreadyExistsException {
        let reader = baseError.errorBodyReader
        var value = EntityAlreadyExistsException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DirectoryUnavailableException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> DirectoryUnavailableException {
        let reader = baseError.errorBodyReader
        var value = DirectoryUnavailableException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UnsupportedOperationException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UnsupportedOperationException {
        let reader = baseError.errorBodyReader
        var value = UnsupportedOperationException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DirectoryDoesNotExistException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> DirectoryDoesNotExistException {
        let reader = baseError.errorBodyReader
        var value = DirectoryDoesNotExistException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension RegionLimitExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> RegionLimitExceededException {
        let reader = baseError.errorBodyReader
        var value = RegionLimitExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DirectoryAlreadyInRegionException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> DirectoryAlreadyInRegionException {
        let reader = baseError.errorBodyReader
        var value = DirectoryAlreadyInRegionException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TagLimitExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> TagLimitExceededException {
        let reader = baseError.errorBodyReader
        var value = TagLimitExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DirectoryLimitExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> DirectoryLimitExceededException {
        let reader = baseError.errorBodyReader
        var value = DirectoryLimitExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AuthenticationFailedException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> AuthenticationFailedException {
        let reader = baseError.errorBodyReader
        var value = AuthenticationFailedException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InsufficientPermissionsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InsufficientPermissionsException {
        let reader = baseError.errorBodyReader
        var value = InsufficientPermissionsException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SnapshotLimitExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> SnapshotLimitExceededException {
        let reader = baseError.errorBodyReader
        var value = SnapshotLimitExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CertificateInUseException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> CertificateInUseException {
        let reader = baseError.errorBodyReader
        var value = CertificateInUseException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CertificateDoesNotExistException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> CertificateDoesNotExistException {
        let reader = baseError.errorBodyReader
        var value = CertificateDoesNotExistException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidNextTokenException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidNextTokenException {
        let reader = baseError.errorBodyReader
        var value = InvalidNextTokenException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidClientAuthStatusException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidClientAuthStatusException {
        let reader = baseError.errorBodyReader
        var value = InvalidClientAuthStatusException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DirectoryInDesiredStateException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> DirectoryInDesiredStateException {
        let reader = baseError.errorBodyReader
        var value = DirectoryInDesiredStateException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidLDAPSStatusException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidLDAPSStatusException {
        let reader = baseError.errorBodyReader
        var value = InvalidLDAPSStatusException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension NoAvailableCertificateException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> NoAvailableCertificateException {
        let reader = baseError.errorBodyReader
        var value = NoAvailableCertificateException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CertificateAlreadyExistsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> CertificateAlreadyExistsException {
        let reader = baseError.errorBodyReader
        var value = CertificateAlreadyExistsException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CertificateLimitExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> CertificateLimitExceededException {
        let reader = baseError.errorBodyReader
        var value = CertificateLimitExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidCertificateException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidCertificateException {
        let reader = baseError.errorBodyReader
        var value = InvalidCertificateException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UserDoesNotExistException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UserDoesNotExistException {
        let reader = baseError.errorBodyReader
        var value = UserDoesNotExistException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidPasswordException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidPasswordException {
        let reader = baseError.errorBodyReader
        var value = InvalidPasswordException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension OrganizationsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> OrganizationsException {
        let reader = baseError.errorBodyReader
        var value = OrganizationsException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ShareLimitExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ShareLimitExceededException {
        let reader = baseError.errorBodyReader
        var value = ShareLimitExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidTargetException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidTargetException {
        let reader = baseError.errorBodyReader
        var value = InvalidTargetException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DirectoryNotSharedException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> DirectoryNotSharedException {
        let reader = baseError.errorBodyReader
        var value = DirectoryNotSharedException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DomainControllerLimitExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> DomainControllerLimitExceededException {
        let reader = baseError.errorBodyReader
        var value = DomainControllerLimitExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension IncompatibleSettingsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> IncompatibleSettingsException {
        let reader = baseError.errorBodyReader
        var value = IncompatibleSettingsException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UnsupportedSettingsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UnsupportedSettingsException {
        let reader = baseError.errorBodyReader
        var value = UnsupportedSettingsException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.requestId = try reader["RequestId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DirectoryClientTypes.SharedDirectory {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.SharedDirectory {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.SharedDirectory()
        value.ownerAccountId = try reader["OwnerAccountId"].readIfPresent()
        value.ownerDirectoryId = try reader["OwnerDirectoryId"].readIfPresent()
        value.shareMethod = try reader["ShareMethod"].readIfPresent()
        value.sharedAccountId = try reader["SharedAccountId"].readIfPresent()
        value.sharedDirectoryId = try reader["SharedDirectoryId"].readIfPresent()
        value.shareStatus = try reader["ShareStatus"].readIfPresent()
        value.shareNotes = try reader["ShareNotes"].readIfPresent()
        value.createdDateTime = try reader["CreatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastUpdatedDateTime = try reader["LastUpdatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension DirectoryClientTypes.Computer {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.Computer {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.Computer()
        value.computerId = try reader["ComputerId"].readIfPresent()
        value.computerName = try reader["ComputerName"].readIfPresent()
        value.computerAttributes = try reader["ComputerAttributes"].readListIfPresent(memberReadingClosure: DirectoryClientTypes.Attribute.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DirectoryClientTypes.Attribute {

    static func write(value: DirectoryClientTypes.Attribute?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.Attribute {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.Attribute()
        value.name = try reader["Name"].readIfPresent()
        value.value = try reader["Value"].readIfPresent()
        return value
    }
}

extension DirectoryClientTypes.Certificate {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.Certificate {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.Certificate()
        value.certificateId = try reader["CertificateId"].readIfPresent()
        value.state = try reader["State"].readIfPresent()
        value.stateReason = try reader["StateReason"].readIfPresent()
        value.commonName = try reader["CommonName"].readIfPresent()
        value.registeredDateTime = try reader["RegisteredDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.expiryDateTime = try reader["ExpiryDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.type = try reader["Type"].readIfPresent()
        value.clientCertAuthSettings = try reader["ClientCertAuthSettings"].readIfPresent(with: DirectoryClientTypes.ClientCertAuthSettings.read(from:))
        return value
    }
}

extension DirectoryClientTypes.ClientCertAuthSettings {

    static func write(value: DirectoryClientTypes.ClientCertAuthSettings?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["OCSPUrl"].write(value.ocspUrl)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.ClientCertAuthSettings {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.ClientCertAuthSettings()
        value.ocspUrl = try reader["OCSPUrl"].readIfPresent()
        return value
    }
}

extension DirectoryClientTypes.ClientAuthenticationSettingInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.ClientAuthenticationSettingInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.ClientAuthenticationSettingInfo()
        value.type = try reader["Type"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.lastUpdatedDateTime = try reader["LastUpdatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension DirectoryClientTypes.ConditionalForwarder {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.ConditionalForwarder {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.ConditionalForwarder()
        value.remoteDomainName = try reader["RemoteDomainName"].readIfPresent()
        value.dnsIpAddrs = try reader["DnsIpAddrs"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.replicationScope = try reader["ReplicationScope"].readIfPresent()
        return value
    }
}

extension DirectoryClientTypes.DirectoryDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.DirectoryDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.DirectoryDescription()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.shortName = try reader["ShortName"].readIfPresent()
        value.size = try reader["Size"].readIfPresent()
        value.edition = try reader["Edition"].readIfPresent()
        value.alias = try reader["Alias"].readIfPresent()
        value.accessUrl = try reader["AccessUrl"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.dnsIpAddrs = try reader["DnsIpAddrs"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.stage = try reader["Stage"].readIfPresent()
        value.shareStatus = try reader["ShareStatus"].readIfPresent()
        value.shareMethod = try reader["ShareMethod"].readIfPresent()
        value.shareNotes = try reader["ShareNotes"].readIfPresent()
        value.launchTime = try reader["LaunchTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.stageLastUpdatedDateTime = try reader["StageLastUpdatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.type = try reader["Type"].readIfPresent()
        value.vpcSettings = try reader["VpcSettings"].readIfPresent(with: DirectoryClientTypes.DirectoryVpcSettingsDescription.read(from:))
        value.connectSettings = try reader["ConnectSettings"].readIfPresent(with: DirectoryClientTypes.DirectoryConnectSettingsDescription.read(from:))
        value.radiusSettings = try reader["RadiusSettings"].readIfPresent(with: DirectoryClientTypes.RadiusSettings.read(from:))
        value.radiusStatus = try reader["RadiusStatus"].readIfPresent()
        value.stageReason = try reader["StageReason"].readIfPresent()
        value.ssoEnabled = try reader["SsoEnabled"].readIfPresent() ?? false
        value.desiredNumberOfDomainControllers = try reader["DesiredNumberOfDomainControllers"].readIfPresent()
        value.ownerDirectoryDescription = try reader["OwnerDirectoryDescription"].readIfPresent(with: DirectoryClientTypes.OwnerDirectoryDescription.read(from:))
        value.regionsInfo = try reader["RegionsInfo"].readIfPresent(with: DirectoryClientTypes.RegionsInfo.read(from:))
        value.osVersion = try reader["OsVersion"].readIfPresent()
        return value
    }
}

extension DirectoryClientTypes.RegionsInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.RegionsInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.RegionsInfo()
        value.primaryRegion = try reader["PrimaryRegion"].readIfPresent()
        value.additionalRegions = try reader["AdditionalRegions"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DirectoryClientTypes.OwnerDirectoryDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.OwnerDirectoryDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.OwnerDirectoryDescription()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.accountId = try reader["AccountId"].readIfPresent()
        value.dnsIpAddrs = try reader["DnsIpAddrs"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.vpcSettings = try reader["VpcSettings"].readIfPresent(with: DirectoryClientTypes.DirectoryVpcSettingsDescription.read(from:))
        value.radiusSettings = try reader["RadiusSettings"].readIfPresent(with: DirectoryClientTypes.RadiusSettings.read(from:))
        value.radiusStatus = try reader["RadiusStatus"].readIfPresent()
        return value
    }
}

extension DirectoryClientTypes.RadiusSettings {

    static func write(value: DirectoryClientTypes.RadiusSettings?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AuthenticationProtocol"].write(value.authenticationProtocol)
        try writer["DisplayLabel"].write(value.displayLabel)
        try writer["RadiusPort"].write(value.radiusPort)
        try writer["RadiusRetries"].write(value.radiusRetries)
        try writer["RadiusServers"].writeList(value.radiusServers, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["RadiusTimeout"].write(value.radiusTimeout)
        try writer["SharedSecret"].write(value.sharedSecret)
        try writer["UseSameUsername"].write(value.useSameUsername)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.RadiusSettings {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.RadiusSettings()
        value.radiusServers = try reader["RadiusServers"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.radiusPort = try reader["RadiusPort"].readIfPresent()
        value.radiusTimeout = try reader["RadiusTimeout"].readIfPresent()
        value.radiusRetries = try reader["RadiusRetries"].readIfPresent() ?? 0
        value.sharedSecret = try reader["SharedSecret"].readIfPresent()
        value.authenticationProtocol = try reader["AuthenticationProtocol"].readIfPresent()
        value.displayLabel = try reader["DisplayLabel"].readIfPresent()
        value.useSameUsername = try reader["UseSameUsername"].readIfPresent() ?? false
        return value
    }
}

extension DirectoryClientTypes.DirectoryVpcSettingsDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.DirectoryVpcSettingsDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.DirectoryVpcSettingsDescription()
        value.vpcId = try reader["VpcId"].readIfPresent()
        value.subnetIds = try reader["SubnetIds"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.securityGroupId = try reader["SecurityGroupId"].readIfPresent()
        value.availabilityZones = try reader["AvailabilityZones"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DirectoryClientTypes.DirectoryConnectSettingsDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.DirectoryConnectSettingsDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.DirectoryConnectSettingsDescription()
        value.vpcId = try reader["VpcId"].readIfPresent()
        value.subnetIds = try reader["SubnetIds"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.customerUserName = try reader["CustomerUserName"].readIfPresent()
        value.securityGroupId = try reader["SecurityGroupId"].readIfPresent()
        value.availabilityZones = try reader["AvailabilityZones"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.connectIps = try reader["ConnectIps"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DirectoryClientTypes.DomainController {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.DomainController {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.DomainController()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.domainControllerId = try reader["DomainControllerId"].readIfPresent()
        value.dnsIpAddr = try reader["DnsIpAddr"].readIfPresent()
        value.vpcId = try reader["VpcId"].readIfPresent()
        value.subnetId = try reader["SubnetId"].readIfPresent()
        value.availabilityZone = try reader["AvailabilityZone"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.statusReason = try reader["StatusReason"].readIfPresent()
        value.launchTime = try reader["LaunchTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.statusLastUpdatedDateTime = try reader["StatusLastUpdatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension DirectoryClientTypes.EventTopic {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.EventTopic {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.EventTopic()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.topicName = try reader["TopicName"].readIfPresent()
        value.topicArn = try reader["TopicArn"].readIfPresent()
        value.createdDateTime = try reader["CreatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.status = try reader["Status"].readIfPresent()
        return value
    }
}

extension DirectoryClientTypes.LDAPSSettingInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.LDAPSSettingInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.LDAPSSettingInfo()
        value.ldapsStatus = try reader["LDAPSStatus"].readIfPresent()
        value.ldapsStatusReason = try reader["LDAPSStatusReason"].readIfPresent()
        value.lastUpdatedDateTime = try reader["LastUpdatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension DirectoryClientTypes.RegionDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.RegionDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.RegionDescription()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.regionName = try reader["RegionName"].readIfPresent()
        value.regionType = try reader["RegionType"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.vpcSettings = try reader["VpcSettings"].readIfPresent(with: DirectoryClientTypes.DirectoryVpcSettings.read(from:))
        value.desiredNumberOfDomainControllers = try reader["DesiredNumberOfDomainControllers"].readIfPresent()
        value.launchTime = try reader["LaunchTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.statusLastUpdatedDateTime = try reader["StatusLastUpdatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastUpdatedDateTime = try reader["LastUpdatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension DirectoryClientTypes.DirectoryVpcSettings {

    static func write(value: DirectoryClientTypes.DirectoryVpcSettings?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["SubnetIds"].writeList(value.subnetIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["VpcId"].write(value.vpcId)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.DirectoryVpcSettings {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.DirectoryVpcSettings()
        value.vpcId = try reader["VpcId"].readIfPresent() ?? ""
        value.subnetIds = try reader["SubnetIds"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DirectoryClientTypes.SettingEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.SettingEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.SettingEntry()
        value.type = try reader["Type"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.allowedValues = try reader["AllowedValues"].readIfPresent()
        value.appliedValue = try reader["AppliedValue"].readIfPresent()
        value.requestedValue = try reader["RequestedValue"].readIfPresent()
        value.requestStatus = try reader["RequestStatus"].readIfPresent()
        value.requestDetailedStatus = try reader["RequestDetailedStatus"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosureBox<DirectoryClientTypes.DirectoryConfigurationStatus>().read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.requestStatusMessage = try reader["RequestStatusMessage"].readIfPresent()
        value.lastUpdatedDateTime = try reader["LastUpdatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastRequestedDateTime = try reader["LastRequestedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.dataType = try reader["DataType"].readIfPresent()
        return value
    }
}

extension DirectoryClientTypes.Snapshot {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.Snapshot {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.Snapshot()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.snapshotId = try reader["SnapshotId"].readIfPresent()
        value.type = try reader["Type"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.startTime = try reader["StartTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension DirectoryClientTypes.Trust {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.Trust {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.Trust()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.trustId = try reader["TrustId"].readIfPresent()
        value.remoteDomainName = try reader["RemoteDomainName"].readIfPresent()
        value.trustType = try reader["TrustType"].readIfPresent()
        value.trustDirection = try reader["TrustDirection"].readIfPresent()
        value.trustState = try reader["TrustState"].readIfPresent()
        value.createdDateTime = try reader["CreatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastUpdatedDateTime = try reader["LastUpdatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.stateLastUpdatedDateTime = try reader["StateLastUpdatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.trustStateReason = try reader["TrustStateReason"].readIfPresent()
        value.selectiveAuth = try reader["SelectiveAuth"].readIfPresent()
        return value
    }
}

extension DirectoryClientTypes.UpdateInfoEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.UpdateInfoEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.UpdateInfoEntry()
        value.region = try reader["Region"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.statusReason = try reader["StatusReason"].readIfPresent()
        value.initiatedBy = try reader["InitiatedBy"].readIfPresent()
        value.newValue = try reader["NewValue"].readIfPresent(with: DirectoryClientTypes.UpdateValue.read(from:))
        value.previousValue = try reader["PreviousValue"].readIfPresent(with: DirectoryClientTypes.UpdateValue.read(from:))
        value.startTime = try reader["StartTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastUpdatedDateTime = try reader["LastUpdatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension DirectoryClientTypes.UpdateValue {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.UpdateValue {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.UpdateValue()
        value.osUpdateSettings = try reader["OSUpdateSettings"].readIfPresent(with: DirectoryClientTypes.OSUpdateSettings.read(from:))
        return value
    }
}

extension DirectoryClientTypes.OSUpdateSettings {

    static func write(value: DirectoryClientTypes.OSUpdateSettings?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["OSVersion"].write(value.osVersion)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.OSUpdateSettings {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.OSUpdateSettings()
        value.osVersion = try reader["OSVersion"].readIfPresent()
        return value
    }
}

extension DirectoryClientTypes.DirectoryLimits {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.DirectoryLimits {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.DirectoryLimits()
        value.cloudOnlyDirectoriesLimit = try reader["CloudOnlyDirectoriesLimit"].readIfPresent()
        value.cloudOnlyDirectoriesCurrentCount = try reader["CloudOnlyDirectoriesCurrentCount"].readIfPresent()
        value.cloudOnlyDirectoriesLimitReached = try reader["CloudOnlyDirectoriesLimitReached"].readIfPresent() ?? false
        value.cloudOnlyMicrosoftADLimit = try reader["CloudOnlyMicrosoftADLimit"].readIfPresent()
        value.cloudOnlyMicrosoftADCurrentCount = try reader["CloudOnlyMicrosoftADCurrentCount"].readIfPresent()
        value.cloudOnlyMicrosoftADLimitReached = try reader["CloudOnlyMicrosoftADLimitReached"].readIfPresent() ?? false
        value.connectedDirectoriesLimit = try reader["ConnectedDirectoriesLimit"].readIfPresent()
        value.connectedDirectoriesCurrentCount = try reader["ConnectedDirectoriesCurrentCount"].readIfPresent()
        value.connectedDirectoriesLimitReached = try reader["ConnectedDirectoriesLimitReached"].readIfPresent() ?? false
        return value
    }
}

extension DirectoryClientTypes.SnapshotLimits {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.SnapshotLimits {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.SnapshotLimits()
        value.manualSnapshotsLimit = try reader["ManualSnapshotsLimit"].readIfPresent()
        value.manualSnapshotsCurrentCount = try reader["ManualSnapshotsCurrentCount"].readIfPresent()
        value.manualSnapshotsLimitReached = try reader["ManualSnapshotsLimitReached"].readIfPresent() ?? false
        return value
    }
}

extension DirectoryClientTypes.CertificateInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.CertificateInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.CertificateInfo()
        value.certificateId = try reader["CertificateId"].readIfPresent()
        value.commonName = try reader["CommonName"].readIfPresent()
        value.state = try reader["State"].readIfPresent()
        value.expiryDateTime = try reader["ExpiryDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.type = try reader["Type"].readIfPresent()
        return value
    }
}

extension DirectoryClientTypes.IpRouteInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.IpRouteInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.IpRouteInfo()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.cidrIp = try reader["CidrIp"].readIfPresent()
        value.ipRouteStatusMsg = try reader["IpRouteStatusMsg"].readIfPresent()
        value.addedDateTime = try reader["AddedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.ipRouteStatusReason = try reader["IpRouteStatusReason"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        return value
    }
}

extension DirectoryClientTypes.LogSubscription {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.LogSubscription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.LogSubscription()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.logGroupName = try reader["LogGroupName"].readIfPresent()
        value.subscriptionCreatedDateTime = try reader["SubscriptionCreatedDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension DirectoryClientTypes.SchemaExtensionInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.SchemaExtensionInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.SchemaExtensionInfo()
        value.directoryId = try reader["DirectoryId"].readIfPresent()
        value.schemaExtensionId = try reader["SchemaExtensionId"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.schemaExtensionStatus = try reader["SchemaExtensionStatus"].readIfPresent()
        value.schemaExtensionStatusReason = try reader["SchemaExtensionStatusReason"].readIfPresent()
        value.startDateTime = try reader["StartDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.endDateTime = try reader["EndDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension DirectoryClientTypes.Tag {

    static func write(value: DirectoryClientTypes.Tag?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Key"].write(value.key)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DirectoryClientTypes.Tag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DirectoryClientTypes.Tag()
        value.key = try reader["Key"].readIfPresent() ?? ""
        value.value = try reader["Value"].readIfPresent() ?? ""
        return value
    }
}

extension DirectoryClientTypes.IpRoute {

    static func write(value: DirectoryClientTypes.IpRoute?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CidrIp"].write(value.cidrIp)
        try writer["Description"].write(value.description)
    }
}

extension DirectoryClientTypes.DirectoryConnectSettings {

    static func write(value: DirectoryClientTypes.DirectoryConnectSettings?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CustomerDnsIps"].writeList(value.customerDnsIps, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["CustomerUserName"].write(value.customerUserName)
        try writer["SubnetIds"].writeList(value.subnetIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["VpcId"].write(value.vpcId)
    }
}

extension DirectoryClientTypes.ShareTarget {

    static func write(value: DirectoryClientTypes.ShareTarget?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Id"].write(value.id)
        try writer["Type"].write(value.type)
    }
}

extension DirectoryClientTypes.UnshareTarget {

    static func write(value: DirectoryClientTypes.UnshareTarget?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Id"].write(value.id)
        try writer["Type"].write(value.type)
    }
}

extension DirectoryClientTypes.Setting {

    static func write(value: DirectoryClientTypes.Setting?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
        try writer["Value"].write(value.value)
    }
}

public enum DirectoryClientTypes {}
