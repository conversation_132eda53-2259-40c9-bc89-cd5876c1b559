//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
@_spi(SmithyReadWrite) import func SmithyReadWrite.listReadingClosure
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
import struct SmithyHTTPAPI.Header
import struct SmithyHTTPAPI.Headers
@_spi(SmithyReadWrite) import struct SmithyReadWrite.ReadingClosureBox
@_spi(SmithyReadWrite) import struct SmithyReadWrite.WritingClosureBox
@_spi(SmithyTimestamps) import struct SmithyTimestamps.TimestampFormatter

extension CodeGuruProfilerClientTypes {

    public enum ActionGroup: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// Permission group type for Agent APIs - ConfigureAgent, PostAgentProfile
        case agentPermissions
        case sdkUnknown(Swift.String)

        public static var allCases: [ActionGroup] {
            return [
                .agentPermissions
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .agentPermissions: return "agentPermissions"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The requested operation would cause a conflict with the current state of a service resource associated with the request. Resolve the conflict before retrying this request.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The server encountered an internal error and is unable to complete the request.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The resource specified in the request does not exist.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// You have exceeded your service quota. To perform the requested action, remove some of the relevant resources, or use [Service Quotas](https://docs.aws.amazon.com/servicequotas/latest/userguide/intro.html) to request a service quota increase.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceQuotaExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request was denied due to request throttling.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The parameter is not valid.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CodeGuruProfilerClientTypes {

    public enum EventPublisher: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// Notifications for Anomaly Detection
        case anomalyDetection
        case sdkUnknown(Swift.String)

        public static var allCases: [EventPublisher] {
            return [
                .anomalyDetection
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .anomalyDetection: return "AnomalyDetection"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruProfilerClientTypes {

    /// Notification medium for users to get alerted for events that occur in application profile. We support SNS topic as a notification channel.
    public struct Channel: Swift.Sendable {
        /// List of publishers for different type of events that may be detected in an application from the profile. Anomaly detection is the only event publisher in Profiler.
        /// This member is required.
        public var eventPublishers: [CodeGuruProfilerClientTypes.EventPublisher]?
        /// Unique identifier for each Channel in the notification configuration of a Profiling Group. A random UUID for channelId is used when adding a channel to the notification configuration if not specified in the request.
        public var id: Swift.String?
        /// Unique arn of the resource to be used for notifications. We support a valid SNS topic arn as a channel uri.
        /// This member is required.
        public var uri: Swift.String?

        public init(
            eventPublishers: [CodeGuruProfilerClientTypes.EventPublisher]? = nil,
            id: Swift.String? = nil,
            uri: Swift.String? = nil
        )
        {
            self.eventPublishers = eventPublishers
            self.id = id
            self.uri = uri
        }
    }
}

/// The structure representing the AddNotificationChannelsRequest.
public struct AddNotificationChannelsInput: Swift.Sendable {
    /// One or 2 channels to report to when anomalies are detected.
    /// This member is required.
    public var channels: [CodeGuruProfilerClientTypes.Channel]?
    /// The name of the profiling group that we are setting up notifications for.
    /// This member is required.
    public var profilingGroupName: Swift.String?

    public init(
        channels: [CodeGuruProfilerClientTypes.Channel]? = nil,
        profilingGroupName: Swift.String? = nil
    )
    {
        self.channels = channels
        self.profilingGroupName = profilingGroupName
    }
}

extension CodeGuruProfilerClientTypes {

    /// The configuration for notifications stored for each profiling group. This includes up to to two channels and a list of event publishers associated with each channel.
    public struct NotificationConfiguration: Swift.Sendable {
        /// List of up to two channels to be used for sending notifications for events detected from the application profile.
        public var channels: [CodeGuruProfilerClientTypes.Channel]?

        public init(
            channels: [CodeGuruProfilerClientTypes.Channel]? = nil
        )
        {
            self.channels = channels
        }
    }
}

/// The structure representing the AddNotificationChannelsResponse.
public struct AddNotificationChannelsOutput: Swift.Sendable {
    /// The new notification configuration for this profiling group.
    public var notificationConfiguration: CodeGuruProfilerClientTypes.NotificationConfiguration?

    public init(
        notificationConfiguration: CodeGuruProfilerClientTypes.NotificationConfiguration? = nil
    )
    {
        self.notificationConfiguration = notificationConfiguration
    }
}

extension CodeGuruProfilerClientTypes {

    public enum AgentParameterField: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// Maximum stack depth to be captured by the CodeGuru Profiler.
        case maxStackDepth
        /// Percentage of memory to be used by CodeGuru profiler. Minimum of 30MB is required for the agent.
        case memoryUsageLimitPercent
        /// Minimum time in milliseconds between sending reports.
        case minimumTimeForReportingInMilliseconds
        /// Reporting interval in milliseconds used to report profiles.
        case reportingIntervalInMilliseconds
        /// Sampling interval in milliseconds used to sample profiles.
        case samplingIntervalInMilliseconds
        case sdkUnknown(Swift.String)

        public static var allCases: [AgentParameterField] {
            return [
                .maxStackDepth,
                .memoryUsageLimitPercent,
                .minimumTimeForReportingInMilliseconds,
                .reportingIntervalInMilliseconds,
                .samplingIntervalInMilliseconds
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .maxStackDepth: return "MaxStackDepth"
            case .memoryUsageLimitPercent: return "MemoryUsageLimitPercent"
            case .minimumTimeForReportingInMilliseconds: return "MinimumTimeForReportingInMilliseconds"
            case .reportingIntervalInMilliseconds: return "ReportingIntervalInMilliseconds"
            case .samplingIntervalInMilliseconds: return "SamplingIntervalInMilliseconds"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruProfilerClientTypes {

    /// The response of [ConfigureAgent](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_ConfigureAgent.html) that specifies if an agent profiles or not and for how long to return profiling data.
    public struct AgentConfiguration: Swift.Sendable {
        /// Parameters used by the profiler. The valid parameters are:
        ///
        /// * MaxStackDepth - The maximum depth of the stacks in the code that is represented in the profile. For example, if CodeGuru Profiler finds a method A, which calls method B, which calls method C, which calls method D, then the depth is 4. If the maxDepth is set to 2, then the profiler evaluates A and B.
        ///
        /// * MemoryUsageLimitPercent - The percentage of memory that is used by the profiler.
        ///
        /// * MinimumTimeForReportingInMilliseconds - The minimum time in milliseconds between sending reports.
        ///
        /// * ReportingIntervalInMilliseconds - The reporting interval in milliseconds used to report profiles.
        ///
        /// * SamplingIntervalInMilliseconds - The sampling interval in milliseconds that is used to profile samples.
        public var agentParameters: [Swift.String: Swift.String]?
        /// How long a profiling agent should send profiling data using [ConfigureAgent](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_ConfigureAgent.html). For example, if this is set to 300, the profiling agent calls [ConfigureAgent](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_ConfigureAgent.html) every 5 minutes to submit the profiled data collected during that period.
        /// This member is required.
        public var periodInSeconds: Swift.Int?
        /// A Boolean that specifies whether the profiling agent collects profiling data or not. Set to true to enable profiling.
        /// This member is required.
        public var shouldProfile: Swift.Bool?

        public init(
            agentParameters: [Swift.String: Swift.String]? = nil,
            periodInSeconds: Swift.Int? = nil,
            shouldProfile: Swift.Bool? = nil
        )
        {
            self.agentParameters = agentParameters
            self.periodInSeconds = periodInSeconds
            self.shouldProfile = shouldProfile
        }
    }
}

extension CodeGuruProfilerClientTypes {

    /// Specifies whether profiling is enabled or disabled for a profiling group. It is used by [ConfigureAgent](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_ConfigureAgent.html) to enable or disable profiling for a profiling group.
    public struct AgentOrchestrationConfig: Swift.Sendable {
        /// A Boolean that specifies whether the profiling agent collects profiling data or not. Set to true to enable profiling.
        /// This member is required.
        public var profilingEnabled: Swift.Bool?

        public init(
            profilingEnabled: Swift.Bool? = nil
        )
        {
            self.profilingEnabled = profilingEnabled
        }
    }
}

extension CodeGuruProfilerClientTypes {

    public enum AggregationPeriod: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// Period of one day.
        case p1d
        /// Period of one hour.
        case pt1h
        /// Period of five minutes.
        case pt5m
        case sdkUnknown(Swift.String)

        public static var allCases: [AggregationPeriod] {
            return [
                .p1d,
                .pt1h,
                .pt5m
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .p1d: return "P1D"
            case .pt1h: return "PT1H"
            case .pt5m: return "PT5M"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruProfilerClientTypes {

    /// Specifies the aggregation period and aggregation start time for an aggregated profile. An aggregated profile is used to collect posted agent profiles during an aggregation period. There are three possible aggregation periods (1 day, 1 hour, or 5 minutes).
    public struct AggregatedProfileTime: Swift.Sendable {
        /// The aggregation period. This indicates the period during which an aggregation profile collects posted agent profiles for a profiling group. Use one of three valid durations that are specified using the ISO 8601 format.
        ///
        /// * P1D — 1 day
        ///
        /// * PT1H — 1 hour
        ///
        /// * PT5M — 5 minutes
        public var period: CodeGuruProfilerClientTypes.AggregationPeriod?
        /// The time that aggregation of posted agent profiles for a profiling group starts. The aggregation profile contains profiles posted by the agent starting at this time for an aggregation period specified by the period property of the AggregatedProfileTime object. Specify start using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
        public var start: Foundation.Date?

        public init(
            period: CodeGuruProfilerClientTypes.AggregationPeriod? = nil,
            start: Foundation.Date? = nil
        )
        {
            self.period = period
            self.start = start
        }
    }
}

extension CodeGuruProfilerClientTypes {

    public enum FeedbackType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// Profiler recommendation flagged as not useful.
        case negative
        /// Profiler recommendation flagged as useful.
        case positive
        case sdkUnknown(Swift.String)

        public static var allCases: [FeedbackType] {
            return [
                .negative,
                .positive
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .negative: return "Negative"
            case .positive: return "Positive"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruProfilerClientTypes {

    /// Feedback that can be submitted for each instance of an anomaly by the user. Feedback is be used for improvements in generating recommendations for the application.
    public struct UserFeedback: Swift.Sendable {
        /// Optional Positive or Negative feedback submitted by the user about whether the recommendation is useful or not.
        /// This member is required.
        public var type: CodeGuruProfilerClientTypes.FeedbackType?

        public init(
            type: CodeGuruProfilerClientTypes.FeedbackType? = nil
        )
        {
            self.type = type
        }
    }
}

extension CodeGuruProfilerClientTypes {

    /// The specific duration in which the metric is flagged as anomalous.
    public struct AnomalyInstance: Swift.Sendable {
        /// The end time of the period during which the metric is flagged as anomalous. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
        public var endTime: Foundation.Date?
        /// The universally unique identifier (UUID) of an instance of an anomaly in a metric.
        /// This member is required.
        public var id: Swift.String?
        /// The start time of the period during which the metric is flagged as anomalous. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
        /// This member is required.
        public var startTime: Foundation.Date?
        /// Feedback type on a specific instance of anomaly submitted by the user.
        public var userFeedback: CodeGuruProfilerClientTypes.UserFeedback?

        public init(
            endTime: Foundation.Date? = nil,
            id: Swift.String? = nil,
            startTime: Foundation.Date? = nil,
            userFeedback: CodeGuruProfilerClientTypes.UserFeedback? = nil
        )
        {
            self.endTime = endTime
            self.id = id
            self.startTime = startTime
            self.userFeedback = userFeedback
        }
    }
}

extension CodeGuruProfilerClientTypes {

    public enum MetricType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// Metric value aggregated for all instances of a frame name in a profile relative to the root frame.
        case aggregatedrelativetotaltime
        case sdkUnknown(Swift.String)

        public static var allCases: [MetricType] {
            return [
                .aggregatedrelativetotaltime
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .aggregatedrelativetotaltime: return "AggregatedRelativeTotalTime"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruProfilerClientTypes {

    /// Details about the metric that the analysis used when it detected the anomaly. The metric what is analyzed to create recommendations. It includes the name of the frame that was analyzed and the type and thread states used to derive the metric value for that frame.
    public struct Metric: Swift.Sendable {
        /// The name of the method that appears as a frame in any stack in a profile.
        /// This member is required.
        public var frameName: Swift.String?
        /// The list of application runtime thread states that is used to calculate the metric value for the frame.
        /// This member is required.
        public var threadStates: [Swift.String]?
        /// A type that specifies how a metric for a frame is analyzed. The supported value AggregatedRelativeTotalTime is an aggregation of the metric value for one frame that is calculated across the occurences of all frames in a profile.
        /// This member is required.
        public var type: CodeGuruProfilerClientTypes.MetricType?

        public init(
            frameName: Swift.String? = nil,
            threadStates: [Swift.String]? = nil,
            type: CodeGuruProfilerClientTypes.MetricType? = nil
        )
        {
            self.frameName = frameName
            self.threadStates = threadStates
            self.type = type
        }
    }
}

extension CodeGuruProfilerClientTypes {

    /// Details about an anomaly in a specific metric of application profile. The anomaly is detected using analysis of the metric data over a period of time.
    public struct Anomaly: Swift.Sendable {
        /// A list of the instances of the detected anomalies during the requested period.
        /// This member is required.
        public var instances: [CodeGuruProfilerClientTypes.AnomalyInstance]?
        /// Details about the metric that the analysis used when it detected the anomaly. The metric includes the name of the frame that was analyzed with the type and thread states used to derive the metric value for that frame.
        /// This member is required.
        public var metric: CodeGuruProfilerClientTypes.Metric?
        /// The reason for which metric was flagged as anomalous.
        /// This member is required.
        public var reason: Swift.String?

        public init(
            instances: [CodeGuruProfilerClientTypes.AnomalyInstance]? = nil,
            metric: CodeGuruProfilerClientTypes.Metric? = nil,
            reason: Swift.String? = nil
        )
        {
            self.instances = instances
            self.metric = metric
            self.reason = reason
        }
    }
}

extension CodeGuruProfilerClientTypes {

    /// The frame name, metric type, and thread states. These are used to derive the value of the metric for the frame.
    public struct FrameMetric: Swift.Sendable {
        /// Name of the method common across the multiple occurrences of a frame in an application profile.
        /// This member is required.
        public var frameName: Swift.String?
        /// List of application runtime thread states used to get the counts for a frame a derive a metric value.
        /// This member is required.
        public var threadStates: [Swift.String]?
        /// A type of aggregation that specifies how a metric for a frame is analyzed. The supported value AggregatedRelativeTotalTime is an aggregation of the metric value for one frame that is calculated across the occurrences of all frames in a profile.
        /// This member is required.
        public var type: CodeGuruProfilerClientTypes.MetricType?

        public init(
            frameName: Swift.String? = nil,
            threadStates: [Swift.String]? = nil,
            type: CodeGuruProfilerClientTypes.MetricType? = nil
        )
        {
            self.frameName = frameName
            self.threadStates = threadStates
            self.type = type
        }
    }
}

/// The structure representing the BatchGetFrameMetricDataRequest.
public struct BatchGetFrameMetricDataInput: Swift.Sendable {
    /// The end time of the time period for the returned time series values. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
    public var endTime: Foundation.Date?
    /// The details of the metrics that are used to request a time series of values. The metric includes the name of the frame, the aggregation type to calculate the metric value for the frame, and the thread states to use to get the count for the metric value of the frame.
    public var frameMetrics: [CodeGuruProfilerClientTypes.FrameMetric]?
    /// The duration of the frame metrics used to return the time series values. Specify using the ISO 8601 format. The maximum period duration is one day (PT24H or P1D).
    public var period: Swift.String?
    /// The name of the profiling group associated with the the frame metrics used to return the time series values.
    /// This member is required.
    public var profilingGroupName: Swift.String?
    /// The start time of the time period for the frame metrics used to return the time series values. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
    public var startTime: Foundation.Date?
    /// The requested resolution of time steps for the returned time series of values. If the requested target resolution is not available due to data not being retained we provide a best effort result by falling back to the most granular available resolution after the target resolution. There are 3 valid values.
    ///
    /// * P1D — 1 day
    ///
    /// * PT1H — 1 hour
    ///
    /// * PT5M — 5 minutes
    public var targetResolution: CodeGuruProfilerClientTypes.AggregationPeriod?

    public init(
        endTime: Foundation.Date? = nil,
        frameMetrics: [CodeGuruProfilerClientTypes.FrameMetric]? = nil,
        period: Swift.String? = nil,
        profilingGroupName: Swift.String? = nil,
        startTime: Foundation.Date? = nil,
        targetResolution: CodeGuruProfilerClientTypes.AggregationPeriod? = nil
    )
    {
        self.endTime = endTime
        self.frameMetrics = frameMetrics
        self.period = period
        self.profilingGroupName = profilingGroupName
        self.startTime = startTime
        self.targetResolution = targetResolution
    }
}

extension CodeGuruProfilerClientTypes {

    /// A data type that contains a Timestamp object. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
    public struct TimestampStructure: Swift.Sendable {
        /// A Timestamp. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
        /// This member is required.
        public var value: Foundation.Date?

        public init(
            value: Foundation.Date? = nil
        )
        {
            self.value = value
        }
    }
}

extension CodeGuruProfilerClientTypes {

    /// Information about a frame metric and its values.
    public struct FrameMetricDatum: Swift.Sendable {
        /// The frame name, metric type, and thread states. These are used to derive the value of the metric for the frame.
        /// This member is required.
        public var frameMetric: CodeGuruProfilerClientTypes.FrameMetric?
        /// A list of values that are associated with a frame metric.
        /// This member is required.
        public var values: [Swift.Double]?

        public init(
            frameMetric: CodeGuruProfilerClientTypes.FrameMetric? = nil,
            values: [Swift.Double]? = nil
        )
        {
            self.frameMetric = frameMetric
            self.values = values
        }
    }
}

/// The structure representing the BatchGetFrameMetricDataResponse.
public struct BatchGetFrameMetricDataOutput: Swift.Sendable {
    /// The end time of the time period for the returned time series values. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
    /// This member is required.
    public var endTime: Foundation.Date?
    /// List of instances, or time steps, in the time series. For example, if the period is one day (PT24H)), and the resolution is five minutes (PT5M), then there are 288 endTimes in the list that are each five minutes appart.
    /// This member is required.
    public var endTimes: [CodeGuruProfilerClientTypes.TimestampStructure]?
    /// Details of the metrics to request a time series of values. The metric includes the name of the frame, the aggregation type to calculate the metric value for the frame, and the thread states to use to get the count for the metric value of the frame.
    /// This member is required.
    public var frameMetricData: [CodeGuruProfilerClientTypes.FrameMetricDatum]?
    /// Resolution or granularity of the profile data used to generate the time series. This is the value used to jump through time steps in a time series. There are 3 valid values.
    ///
    /// * P1D — 1 day
    ///
    /// * PT1H — 1 hour
    ///
    /// * PT5M — 5 minutes
    /// This member is required.
    public var resolution: CodeGuruProfilerClientTypes.AggregationPeriod?
    /// The start time of the time period for the returned time series values. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
    /// This member is required.
    public var startTime: Foundation.Date?
    /// List of instances which remained unprocessed. This will create a missing time step in the list of end times.
    /// This member is required.
    public var unprocessedEndTimes: [Swift.String: [CodeGuruProfilerClientTypes.TimestampStructure]]?

    public init(
        endTime: Foundation.Date? = nil,
        endTimes: [CodeGuruProfilerClientTypes.TimestampStructure]? = nil,
        frameMetricData: [CodeGuruProfilerClientTypes.FrameMetricDatum]? = nil,
        resolution: CodeGuruProfilerClientTypes.AggregationPeriod? = nil,
        startTime: Foundation.Date? = nil,
        unprocessedEndTimes: [Swift.String: [CodeGuruProfilerClientTypes.TimestampStructure]]? = nil
    )
    {
        self.endTime = endTime
        self.endTimes = endTimes
        self.frameMetricData = frameMetricData
        self.resolution = resolution
        self.startTime = startTime
        self.unprocessedEndTimes = unprocessedEndTimes
    }
}

/// The structure representing the GetFindingsReportAccountSummaryRequest.
public struct GetFindingsReportAccountSummaryInput: Swift.Sendable {
    /// A Boolean value indicating whether to only return reports from daily profiles. If set to True, only analysis data from daily profiles is returned. If set to False, analysis data is returned from smaller time windows (for example, one hour).
    public var dailyReportsOnly: Swift.Bool?
    /// The maximum number of results returned by  GetFindingsReportAccountSummary in paginated output. When this parameter is used, GetFindingsReportAccountSummary only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another GetFindingsReportAccountSummary request with the returned nextToken value.
    public var maxResults: Swift.Int?
    /// The nextToken value returned from a previous paginated GetFindingsReportAccountSummary request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This token should be treated as an opaque identifier that is only used to retrieve the next items in a list and not for other programmatic purposes.
    public var nextToken: Swift.String?

    public init(
        dailyReportsOnly: Swift.Bool? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.dailyReportsOnly = dailyReportsOnly
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension CodeGuruProfilerClientTypes {

    /// Information about potential recommendations that might be created from the analysis of profiling data.
    public struct FindingsReportSummary: Swift.Sendable {
        /// The universally unique identifier (UUID) of the recommendation report.
        public var id: Swift.String?
        /// The end time of the period during which the metric is flagged as anomalous. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
        public var profileEndTime: Foundation.Date?
        /// The start time of the profile the analysis data is about. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
        public var profileStartTime: Foundation.Date?
        /// The name of the profiling group that is associated with the analysis data.
        public var profilingGroupName: Swift.String?
        /// The total number of different recommendations that were found by the analysis.
        public var totalNumberOfFindings: Swift.Int?

        public init(
            id: Swift.String? = nil,
            profileEndTime: Foundation.Date? = nil,
            profileStartTime: Foundation.Date? = nil,
            profilingGroupName: Swift.String? = nil,
            totalNumberOfFindings: Swift.Int? = nil
        )
        {
            self.id = id
            self.profileEndTime = profileEndTime
            self.profileStartTime = profileStartTime
            self.profilingGroupName = profilingGroupName
            self.totalNumberOfFindings = totalNumberOfFindings
        }
    }
}

/// The structure representing the GetFindingsReportAccountSummaryResponse.
public struct GetFindingsReportAccountSummaryOutput: Swift.Sendable {
    /// The nextToken value to include in a future GetFindingsReportAccountSummary request. When the results of a GetFindingsReportAccountSummary request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
    public var nextToken: Swift.String?
    /// The return list of [FindingsReportSummary](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_FindingsReportSummary.html) objects taht contain summaries of analysis results for all profiling groups in your AWS account.
    /// This member is required.
    public var reportSummaries: [CodeGuruProfilerClientTypes.FindingsReportSummary]?

    public init(
        nextToken: Swift.String? = nil,
        reportSummaries: [CodeGuruProfilerClientTypes.FindingsReportSummary]? = nil
    )
    {
        self.nextToken = nextToken
        self.reportSummaries = reportSummaries
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource that contains the tags to return.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// The list of tags assigned to the specified resource. This is the list of tags returned in the response.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

extension CodeGuruProfilerClientTypes {

    public enum MetadataField: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// Unique identifier for the agent instance.
        case agentId
        /// AWS requestId of the Lambda invocation.
        case awsRequestId
        /// Compute platform on which agent is running.
        case computePlatform
        /// Execution environment on which Lambda function is running.
        case executionEnvironment
        /// Function ARN that's used to invoke the Lambda function.
        case lambdaFunctionArn
        /// Memory allocated for the Lambda function.
        case lambdaMemoryLimitInMb
        /// Time in milliseconds for the previous Lambda invocation.
        case lambdaPreviousExecutionTimeInMilliseconds
        /// Time in milliseconds left before the execution times out.
        case lambdaRemainingTimeInMilliseconds
        /// Time in milliseconds between two invocations of the Lambda function.
        case lambdaTimeGapBetweenInvokesInMilliseconds
        case sdkUnknown(Swift.String)

        public static var allCases: [MetadataField] {
            return [
                .agentId,
                .awsRequestId,
                .computePlatform,
                .executionEnvironment,
                .lambdaFunctionArn,
                .lambdaMemoryLimitInMb,
                .lambdaPreviousExecutionTimeInMilliseconds,
                .lambdaRemainingTimeInMilliseconds,
                .lambdaTimeGapBetweenInvokesInMilliseconds
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .agentId: return "AgentId"
            case .awsRequestId: return "AwsRequestId"
            case .computePlatform: return "ComputePlatform"
            case .executionEnvironment: return "ExecutionEnvironment"
            case .lambdaFunctionArn: return "LambdaFunctionArn"
            case .lambdaMemoryLimitInMb: return "LambdaMemoryLimitInMB"
            case .lambdaPreviousExecutionTimeInMilliseconds: return "LambdaPreviousExecutionTimeInMilliseconds"
            case .lambdaRemainingTimeInMilliseconds: return "LambdaRemainingTimeInMilliseconds"
            case .lambdaTimeGapBetweenInvokesInMilliseconds: return "LambdaTimeGapBetweenInvokesInMilliseconds"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The structure representing the configureAgentRequest.
public struct ConfigureAgentInput: Swift.Sendable {
    /// A universally unique identifier (UUID) for a profiling instance. For example, if the profiling instance is an Amazon EC2 instance, it is the instance ID. If it is an AWS Fargate container, it is the container's task ID.
    public var fleetInstanceId: Swift.String?
    /// Metadata captured about the compute platform the agent is running on. It includes information about sampling and reporting. The valid fields are:
    ///
    /// * COMPUTE_PLATFORM - The compute platform on which the agent is running
    ///
    /// * AGENT_ID - The ID for an agent instance.
    ///
    /// * AWS_REQUEST_ID - The AWS request ID of a Lambda invocation.
    ///
    /// * EXECUTION_ENVIRONMENT - The execution environment a Lambda function is running on.
    ///
    /// * LAMBDA_FUNCTION_ARN - The Amazon Resource Name (ARN) that is used to invoke a Lambda function.
    ///
    /// * LAMBDA_MEMORY_LIMIT_IN_MB - The memory allocated to a Lambda function.
    ///
    /// * LAMBDA_REMAINING_TIME_IN_MILLISECONDS - The time in milliseconds before execution of a Lambda function times out.
    ///
    /// * LAMBDA_TIME_GAP_BETWEEN_INVOKES_IN_MILLISECONDS - The time in milliseconds between two invocations of a Lambda function.
    ///
    /// * LAMBDA_PREVIOUS_EXECUTION_TIME_IN_MILLISECONDS - The time in milliseconds for the previous Lambda invocation.
    public var metadata: [Swift.String: Swift.String]?
    /// The name of the profiling group for which the configured agent is collecting profiling data.
    /// This member is required.
    public var profilingGroupName: Swift.String?

    public init(
        fleetInstanceId: Swift.String? = nil,
        metadata: [Swift.String: Swift.String]? = nil,
        profilingGroupName: Swift.String? = nil
    )
    {
        self.fleetInstanceId = fleetInstanceId
        self.metadata = metadata
        self.profilingGroupName = profilingGroupName
    }
}

/// The structure representing the configureAgentResponse.
public struct ConfigureAgentOutput: Swift.Sendable {
    /// An [AgentConfiguration](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_AgentConfiguration.html) object that specifies if an agent profiles or not and for how long to return profiling data.
    /// This member is required.
    public var configuration: CodeGuruProfilerClientTypes.AgentConfiguration?

    public init(
        configuration: CodeGuruProfilerClientTypes.AgentConfiguration? = nil
    )
    {
        self.configuration = configuration
    }
}

extension CodeGuruProfilerClientTypes {

    public enum ComputePlatform: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// Compute platform meant to used for AWS Lambda.
        case awslambda
        /// Compute platform meant to used for all usecases (like EC2, Fargate, physical servers etc.) but AWS Lambda.
        case `default`
        case sdkUnknown(Swift.String)

        public static var allCases: [ComputePlatform] {
            return [
                .awslambda,
                .default
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .awslambda: return "AWSLambda"
            case .default: return "Default"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The structure representing the createProfiliingGroupRequest.
public struct CreateProfilingGroupInput: Swift.Sendable {
    /// Specifies whether profiling is enabled or disabled for the created profiling group.
    public var agentOrchestrationConfig: CodeGuruProfilerClientTypes.AgentOrchestrationConfig?
    /// Amazon CodeGuru Profiler uses this universally unique identifier (UUID) to prevent the accidental creation of duplicate profiling groups if there are failures and retries.
    /// This member is required.
    public var clientToken: Swift.String?
    /// The compute platform of the profiling group. Use AWSLambda if your application runs on AWS Lambda. Use Default if your application runs on a compute platform that is not AWS Lambda, such an Amazon EC2 instance, an on-premises server, or a different platform. If not specified, Default is used.
    public var computePlatform: CodeGuruProfilerClientTypes.ComputePlatform?
    /// The name of the profiling group to create.
    /// This member is required.
    public var profilingGroupName: Swift.String?
    /// A list of tags to add to the created profiling group.
    public var tags: [Swift.String: Swift.String]?

    public init(
        agentOrchestrationConfig: CodeGuruProfilerClientTypes.AgentOrchestrationConfig? = nil,
        clientToken: Swift.String? = nil,
        computePlatform: CodeGuruProfilerClientTypes.ComputePlatform? = nil,
        profilingGroupName: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.agentOrchestrationConfig = agentOrchestrationConfig
        self.clientToken = clientToken
        self.computePlatform = computePlatform
        self.profilingGroupName = profilingGroupName
        self.tags = tags
    }
}

extension CodeGuruProfilerClientTypes {

    /// Profiling status includes information about the last time a profile agent pinged back, the last time a profile was received, and the aggregation period and start time for the most recent aggregated profile.
    public struct ProfilingStatus: Swift.Sendable {
        /// The date and time when the profiling agent most recently pinged back. Specify using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
        public var latestAgentOrchestratedAt: Foundation.Date?
        /// The date and time when the most recent profile was received. Specify using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
        public var latestAgentProfileReportedAt: Foundation.Date?
        /// An [AggregatedProfileTime](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_AggregatedProfileTime.html) object that contains the aggregation period and start time for an aggregated profile.
        public var latestAggregatedProfile: CodeGuruProfilerClientTypes.AggregatedProfileTime?

        public init(
            latestAgentOrchestratedAt: Foundation.Date? = nil,
            latestAgentProfileReportedAt: Foundation.Date? = nil,
            latestAggregatedProfile: CodeGuruProfilerClientTypes.AggregatedProfileTime? = nil
        )
        {
            self.latestAgentOrchestratedAt = latestAgentOrchestratedAt
            self.latestAgentProfileReportedAt = latestAgentProfileReportedAt
            self.latestAggregatedProfile = latestAggregatedProfile
        }
    }
}

extension CodeGuruProfilerClientTypes {

    /// Contains information about a profiling group.
    public struct ProfilingGroupDescription: Swift.Sendable {
        /// An [AgentOrchestrationConfig](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_AgentOrchestrationConfig.html) object that indicates if the profiling group is enabled for profiled or not.
        public var agentOrchestrationConfig: CodeGuruProfilerClientTypes.AgentOrchestrationConfig?
        /// The Amazon Resource Name (ARN) identifying the profiling group resource.
        public var arn: Swift.String?
        /// The compute platform of the profiling group. If it is set to AWSLambda, then the profiled application runs on AWS Lambda. If it is set to Default, then the profiled application runs on a compute platform that is not AWS Lambda, such an Amazon EC2 instance, an on-premises server, or a different platform. The default is Default.
        public var computePlatform: CodeGuruProfilerClientTypes.ComputePlatform?
        /// The time when the profiling group was created. Specify using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
        public var createdAt: Foundation.Date?
        /// The name of the profiling group.
        public var name: Swift.String?
        /// A [ProfilingStatus](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_ProfilingStatus.html) object that includes information about the last time a profile agent pinged back, the last time a profile was received, and the aggregation period and start time for the most recent aggregated profile.
        public var profilingStatus: CodeGuruProfilerClientTypes.ProfilingStatus?
        /// A list of the tags that belong to this profiling group.
        public var tags: [Swift.String: Swift.String]?
        /// The date and time when the profiling group was last updated. Specify using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
        public var updatedAt: Foundation.Date?

        public init(
            agentOrchestrationConfig: CodeGuruProfilerClientTypes.AgentOrchestrationConfig? = nil,
            arn: Swift.String? = nil,
            computePlatform: CodeGuruProfilerClientTypes.ComputePlatform? = nil,
            createdAt: Foundation.Date? = nil,
            name: Swift.String? = nil,
            profilingStatus: CodeGuruProfilerClientTypes.ProfilingStatus? = nil,
            tags: [Swift.String: Swift.String]? = nil,
            updatedAt: Foundation.Date? = nil
        )
        {
            self.agentOrchestrationConfig = agentOrchestrationConfig
            self.arn = arn
            self.computePlatform = computePlatform
            self.createdAt = createdAt
            self.name = name
            self.profilingStatus = profilingStatus
            self.tags = tags
            self.updatedAt = updatedAt
        }
    }
}

/// The structure representing the createProfilingGroupResponse.
public struct CreateProfilingGroupOutput: Swift.Sendable {
    /// The returned [ProfilingGroupDescription](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_ProfilingGroupDescription.html) object that contains information about the created profiling group.
    /// This member is required.
    public var profilingGroup: CodeGuruProfilerClientTypes.ProfilingGroupDescription?

    public init(
        profilingGroup: CodeGuruProfilerClientTypes.ProfilingGroupDescription? = nil
    )
    {
        self.profilingGroup = profilingGroup
    }
}

/// The structure representing the deleteProfilingGroupRequest.
public struct DeleteProfilingGroupInput: Swift.Sendable {
    /// The name of the profiling group to delete.
    /// This member is required.
    public var profilingGroupName: Swift.String?

    public init(
        profilingGroupName: Swift.String? = nil
    )
    {
        self.profilingGroupName = profilingGroupName
    }
}

/// The structure representing the deleteProfilingGroupResponse.
public struct DeleteProfilingGroupOutput: Swift.Sendable {

    public init() { }
}

/// The structure representing the describeProfilingGroupRequest.
public struct DescribeProfilingGroupInput: Swift.Sendable {
    /// The name of the profiling group to get information about.
    /// This member is required.
    public var profilingGroupName: Swift.String?

    public init(
        profilingGroupName: Swift.String? = nil
    )
    {
        self.profilingGroupName = profilingGroupName
    }
}

/// The structure representing the describeProfilingGroupResponse.
public struct DescribeProfilingGroupOutput: Swift.Sendable {
    /// The returned [ProfilingGroupDescription](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_ProfilingGroupDescription.html) object that contains information about the requested profiling group.
    /// This member is required.
    public var profilingGroup: CodeGuruProfilerClientTypes.ProfilingGroupDescription?

    public init(
        profilingGroup: CodeGuruProfilerClientTypes.ProfilingGroupDescription? = nil
    )
    {
        self.profilingGroup = profilingGroup
    }
}

/// The structure representing the GetNotificationConfigurationRequest.
public struct GetNotificationConfigurationInput: Swift.Sendable {
    /// The name of the profiling group we want to get the notification configuration for.
    /// This member is required.
    public var profilingGroupName: Swift.String?

    public init(
        profilingGroupName: Swift.String? = nil
    )
    {
        self.profilingGroupName = profilingGroupName
    }
}

/// The structure representing the GetNotificationConfigurationResponse.
public struct GetNotificationConfigurationOutput: Swift.Sendable {
    /// The current notification configuration for this profiling group.
    /// This member is required.
    public var notificationConfiguration: CodeGuruProfilerClientTypes.NotificationConfiguration?

    public init(
        notificationConfiguration: CodeGuruProfilerClientTypes.NotificationConfiguration? = nil
    )
    {
        self.notificationConfiguration = notificationConfiguration
    }
}

/// The structure representing the getPolicyRequest.
public struct GetPolicyInput: Swift.Sendable {
    /// The name of the profiling group.
    /// This member is required.
    public var profilingGroupName: Swift.String?

    public init(
        profilingGroupName: Swift.String? = nil
    )
    {
        self.profilingGroupName = profilingGroupName
    }
}

/// The structure representing the getPolicyResponse.
public struct GetPolicyOutput: Swift.Sendable {
    /// The JSON-formatted resource-based policy attached to the ProfilingGroup.
    /// This member is required.
    public var policy: Swift.String?
    /// A unique identifier for the current revision of the returned policy.
    /// This member is required.
    public var revisionId: Swift.String?

    public init(
        policy: Swift.String? = nil,
        revisionId: Swift.String? = nil
    )
    {
        self.policy = policy
        self.revisionId = revisionId
    }
}

/// The structure representing the getProfileRequest.
public struct GetProfileInput: Swift.Sendable {
    /// The format of the returned profiling data. The format maps to the Accept and Content-Type headers of the HTTP request. You can specify one of the following: or the default .
    ///
    /// * application/json — standard JSON format
    ///
    /// * application/x-amzn-ion — the Amazon Ion data format. For more information, see [Amazon Ion](http://amzn.github.io/ion-docs/).
    public var accept: Swift.String?
    /// The end time of the requested profile. Specify using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC. If you specify endTime, then you must also specify period or startTime, but not both.
    public var endTime: Foundation.Date?
    /// The maximum depth of the stacks in the code that is represented in the aggregated profile. For example, if CodeGuru Profiler finds a method A, which calls method B, which calls method C, which calls method D, then the depth is 4. If the maxDepth is set to 2, then the aggregated profile contains representations of methods A and B.
    public var maxDepth: Swift.Int?
    /// Used with startTime or endTime to specify the time range for the returned aggregated profile. Specify using the ISO 8601 format. For example, P1DT1H1M1S. To get the latest aggregated profile, specify only period.
    public var period: Swift.String?
    /// The name of the profiling group to get.
    /// This member is required.
    public var profilingGroupName: Swift.String?
    /// The start time of the profile to get. Specify using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC. If you specify startTime, then you must also specify period or endTime, but not both.
    public var startTime: Foundation.Date?

    public init(
        accept: Swift.String? = nil,
        endTime: Foundation.Date? = nil,
        maxDepth: Swift.Int? = nil,
        period: Swift.String? = nil,
        profilingGroupName: Swift.String? = nil,
        startTime: Foundation.Date? = nil
    )
    {
        self.accept = accept
        self.endTime = endTime
        self.maxDepth = maxDepth
        self.period = period
        self.profilingGroupName = profilingGroupName
        self.startTime = startTime
    }
}

/// The structure representing the getProfileResponse.
public struct GetProfileOutput: Swift.Sendable {
    /// The content encoding of the profile.
    public var contentEncoding: Swift.String?
    /// The content type of the profile in the payload. It is either application/json or the default application/x-amzn-ion.
    /// This member is required.
    public var contentType: Swift.String?
    /// Information about the profile.
    /// This member is required.
    public var profile: Foundation.Data?

    public init(
        contentEncoding: Swift.String? = nil,
        contentType: Swift.String? = nil,
        profile: Foundation.Data? = nil
    )
    {
        self.contentEncoding = contentEncoding
        self.contentType = contentType
        self.profile = profile
    }
}

/// The structure representing the GetRecommendationsRequest.
public struct GetRecommendationsInput: Swift.Sendable {
    /// The start time of the profile to get analysis data about. You must specify startTime and endTime. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
    /// This member is required.
    public var endTime: Foundation.Date?
    /// The language used to provide analysis. Specify using a string that is one of the following BCP 47 language codes.
    ///
    /// * de-DE - German, Germany
    ///
    /// * en-GB - English, United Kingdom
    ///
    /// * en-US - English, United States
    ///
    /// * es-ES - Spanish, Spain
    ///
    /// * fr-FR - French, France
    ///
    /// * it-IT - Italian, Italy
    ///
    /// * ja-JP - Japanese, Japan
    ///
    /// * ko-KR - Korean, Republic of Korea
    ///
    /// * pt-BR - Portugese, Brazil
    ///
    /// * zh-CN - Chinese, China
    ///
    /// * zh-TW - Chinese, Taiwan
    public var locale: Swift.String?
    /// The name of the profiling group to get analysis data about.
    /// This member is required.
    public var profilingGroupName: Swift.String?
    /// The end time of the profile to get analysis data about. You must specify startTime and endTime. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
    /// This member is required.
    public var startTime: Foundation.Date?

    public init(
        endTime: Foundation.Date? = nil,
        locale: Swift.String? = nil,
        profilingGroupName: Swift.String? = nil,
        startTime: Foundation.Date? = nil
    )
    {
        self.endTime = endTime
        self.locale = locale
        self.profilingGroupName = profilingGroupName
        self.startTime = startTime
    }
}

extension CodeGuruProfilerClientTypes {

    /// A set of rules used to make a recommendation during an analysis.
    public struct Pattern: Swift.Sendable {
        /// A list of the different counters used to determine if there is a match.
        public var countersToAggregate: [Swift.String]?
        /// The description of the recommendation. This explains a potential inefficiency in a profiled application.
        public var description: Swift.String?
        /// The universally unique identifier (UUID) of this pattern.
        public var id: Swift.String?
        /// The name for this pattern.
        public var name: Swift.String?
        /// A string that contains the steps recommended to address the potential inefficiency.
        public var resolutionSteps: Swift.String?
        /// A list of frame names that were searched during the analysis that generated a recommendation.
        public var targetFrames: [[Swift.String]]?
        /// The percentage of time an application spends in one method that triggers a recommendation. The percentage of time is the same as the percentage of the total gathered sample counts during analysis.
        public var thresholdPercent: Swift.Double

        public init(
            countersToAggregate: [Swift.String]? = nil,
            description: Swift.String? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil,
            resolutionSteps: Swift.String? = nil,
            targetFrames: [[Swift.String]]? = nil,
            thresholdPercent: Swift.Double = 0.0
        )
        {
            self.countersToAggregate = countersToAggregate
            self.description = description
            self.id = id
            self.name = name
            self.resolutionSteps = resolutionSteps
            self.targetFrames = targetFrames
            self.thresholdPercent = thresholdPercent
        }
    }
}

extension CodeGuruProfilerClientTypes {

    /// The part of a profile that contains a recommendation found during analysis.
    public struct Match: Swift.Sendable {
        /// The location in the profiling graph that contains a recommendation found during analysis.
        public var frameAddress: Swift.String?
        /// The target frame that triggered a match.
        public var targetFramesIndex: Swift.Int?
        /// The value in the profile data that exceeded the recommendation threshold.
        public var thresholdBreachValue: Swift.Double?

        public init(
            frameAddress: Swift.String? = nil,
            targetFramesIndex: Swift.Int? = nil,
            thresholdBreachValue: Swift.Double? = nil
        )
        {
            self.frameAddress = frameAddress
            self.targetFramesIndex = targetFramesIndex
            self.thresholdBreachValue = thresholdBreachValue
        }
    }
}

extension CodeGuruProfilerClientTypes {

    /// A potential improvement that was found from analyzing the profiling data.
    public struct Recommendation: Swift.Sendable {
        /// How many different places in the profile graph triggered a match.
        /// This member is required.
        public var allMatchesCount: Swift.Int?
        /// How much of the total sample count is potentially affected.
        /// This member is required.
        public var allMatchesSum: Swift.Double?
        /// End time of the profile that was used by this analysis. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
        /// This member is required.
        public var endTime: Foundation.Date?
        /// The pattern that analysis recognized in the profile to make this recommendation.
        /// This member is required.
        public var pattern: CodeGuruProfilerClientTypes.Pattern?
        /// The start time of the profile that was used by this analysis. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
        /// This member is required.
        public var startTime: Foundation.Date?
        /// List of the matches with most impact.
        /// This member is required.
        public var topMatches: [CodeGuruProfilerClientTypes.Match]?

        public init(
            allMatchesCount: Swift.Int? = nil,
            allMatchesSum: Swift.Double? = nil,
            endTime: Foundation.Date? = nil,
            pattern: CodeGuruProfilerClientTypes.Pattern? = nil,
            startTime: Foundation.Date? = nil,
            topMatches: [CodeGuruProfilerClientTypes.Match]? = nil
        )
        {
            self.allMatchesCount = allMatchesCount
            self.allMatchesSum = allMatchesSum
            self.endTime = endTime
            self.pattern = pattern
            self.startTime = startTime
            self.topMatches = topMatches
        }
    }
}

/// The structure representing the GetRecommendationsResponse.
public struct GetRecommendationsOutput: Swift.Sendable {
    /// The list of anomalies that the analysis has found for this profile.
    /// This member is required.
    public var anomalies: [CodeGuruProfilerClientTypes.Anomaly]?
    /// The end time of the profile the analysis data is about. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
    /// This member is required.
    public var profileEndTime: Foundation.Date?
    /// The start time of the profile the analysis data is about. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
    /// This member is required.
    public var profileStartTime: Foundation.Date?
    /// The name of the profiling group the analysis data is about.
    /// This member is required.
    public var profilingGroupName: Swift.String?
    /// The list of recommendations that the analysis found for this profile.
    /// This member is required.
    public var recommendations: [CodeGuruProfilerClientTypes.Recommendation]?

    public init(
        anomalies: [CodeGuruProfilerClientTypes.Anomaly]? = nil,
        profileEndTime: Foundation.Date? = nil,
        profileStartTime: Foundation.Date? = nil,
        profilingGroupName: Swift.String? = nil,
        recommendations: [CodeGuruProfilerClientTypes.Recommendation]? = nil
    )
    {
        self.anomalies = anomalies
        self.profileEndTime = profileEndTime
        self.profileStartTime = profileStartTime
        self.profilingGroupName = profilingGroupName
        self.recommendations = recommendations
    }
}

/// The structure representing the ListFindingsReportsRequest.
public struct ListFindingsReportsInput: Swift.Sendable {
    /// A Boolean value indicating whether to only return reports from daily profiles. If set to True, only analysis data from daily profiles is returned. If set to False, analysis data is returned from smaller time windows (for example, one hour).
    public var dailyReportsOnly: Swift.Bool?
    /// The end time of the profile to get analysis data about. You must specify startTime and endTime. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
    /// This member is required.
    public var endTime: Foundation.Date?
    /// The maximum number of report results returned by ListFindingsReports in paginated output. When this parameter is used, ListFindingsReports only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another ListFindingsReports request with the returned nextToken value.
    public var maxResults: Swift.Int?
    /// The nextToken value returned from a previous paginated ListFindingsReportsRequest request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This token should be treated as an opaque identifier that is only used to retrieve the next items in a list and not for other programmatic purposes.
    public var nextToken: Swift.String?
    /// The name of the profiling group from which to search for analysis data.
    /// This member is required.
    public var profilingGroupName: Swift.String?
    /// The start time of the profile to get analysis data about. You must specify startTime and endTime. This is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
    /// This member is required.
    public var startTime: Foundation.Date?

    public init(
        dailyReportsOnly: Swift.Bool? = nil,
        endTime: Foundation.Date? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        profilingGroupName: Swift.String? = nil,
        startTime: Foundation.Date? = nil
    )
    {
        self.dailyReportsOnly = dailyReportsOnly
        self.endTime = endTime
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.profilingGroupName = profilingGroupName
        self.startTime = startTime
    }
}

/// The structure representing the ListFindingsReportsResponse.
public struct ListFindingsReportsOutput: Swift.Sendable {
    /// The list of analysis results summaries.
    /// This member is required.
    public var findingsReportSummaries: [CodeGuruProfilerClientTypes.FindingsReportSummary]?
    /// The nextToken value to include in a future ListFindingsReports request. When the results of a ListFindingsReports request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
    public var nextToken: Swift.String?

    public init(
        findingsReportSummaries: [CodeGuruProfilerClientTypes.FindingsReportSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.findingsReportSummaries = findingsReportSummaries
        self.nextToken = nextToken
    }
}

extension CodeGuruProfilerClientTypes {

    public enum OrderBy: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// Order by timestamp in ascending order.
        case timestampAscending
        /// Order by timestamp in descending order.
        case timestampDescending
        case sdkUnknown(Swift.String)

        public static var allCases: [OrderBy] {
            return [
                .timestampAscending,
                .timestampDescending
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .timestampAscending: return "TimestampAscending"
            case .timestampDescending: return "TimestampDescending"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The structure representing the listProfileTimesRequest.
public struct ListProfileTimesInput: Swift.Sendable {
    /// The end time of the time range from which to list the profiles.
    /// This member is required.
    public var endTime: Foundation.Date?
    /// The maximum number of profile time results returned by ListProfileTimes in paginated output. When this parameter is used, ListProfileTimes only returns maxResults results in a single page with a nextToken response element. The remaining results of the initial request can be seen by sending another ListProfileTimes request with the returned nextToken value.
    public var maxResults: Swift.Int?
    /// The nextToken value returned from a previous paginated ListProfileTimes request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This token should be treated as an opaque identifier that is only used to retrieve the next items in a list and not for other programmatic purposes.
    public var nextToken: Swift.String?
    /// The order (ascending or descending by start time of the profile) to use when listing profiles. Defaults to TIMESTAMP_DESCENDING.
    public var orderBy: CodeGuruProfilerClientTypes.OrderBy?
    /// The aggregation period. This specifies the period during which an aggregation profile collects posted agent profiles for a profiling group. There are 3 valid values.
    ///
    /// * P1D — 1 day
    ///
    /// * PT1H — 1 hour
    ///
    /// * PT5M — 5 minutes
    /// This member is required.
    public var period: CodeGuruProfilerClientTypes.AggregationPeriod?
    /// The name of the profiling group.
    /// This member is required.
    public var profilingGroupName: Swift.String?
    /// The start time of the time range from which to list the profiles.
    /// This member is required.
    public var startTime: Foundation.Date?

    public init(
        endTime: Foundation.Date? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        orderBy: CodeGuruProfilerClientTypes.OrderBy? = nil,
        period: CodeGuruProfilerClientTypes.AggregationPeriod? = nil,
        profilingGroupName: Swift.String? = nil,
        startTime: Foundation.Date? = nil
    )
    {
        self.endTime = endTime
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.orderBy = orderBy
        self.period = period
        self.profilingGroupName = profilingGroupName
        self.startTime = startTime
    }
}

extension CodeGuruProfilerClientTypes {

    /// Contains the start time of a profile.
    public struct ProfileTime: Swift.Sendable {
        /// The start time of a profile. It is specified using the ISO 8601 format. For example, 2020-06-01T13:15:02.001Z represents 1 millisecond past June 1, 2020 1:15:02 PM UTC.
        public var start: Foundation.Date?

        public init(
            start: Foundation.Date? = nil
        )
        {
            self.start = start
        }
    }
}

/// The structure representing the listProfileTimesResponse.
public struct ListProfileTimesOutput: Swift.Sendable {
    /// The nextToken value to include in a future ListProfileTimes request. When the results of a ListProfileTimes request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
    public var nextToken: Swift.String?
    /// The list of start times of the available profiles for the aggregation period in the specified time range.
    /// This member is required.
    public var profileTimes: [CodeGuruProfilerClientTypes.ProfileTime]?

    public init(
        nextToken: Swift.String? = nil,
        profileTimes: [CodeGuruProfilerClientTypes.ProfileTime]? = nil
    )
    {
        self.nextToken = nextToken
        self.profileTimes = profileTimes
    }
}

/// The structure representing the listProfilingGroupsRequest.
public struct ListProfilingGroupsInput: Swift.Sendable {
    /// A Boolean value indicating whether to include a description. If true, then a list of [ProfilingGroupDescription](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_ProfilingGroupDescription.html) objects that contain detailed information about profiling groups is returned. If false, then a list of profiling group names is returned.
    public var includeDescription: Swift.Bool?
    /// The maximum number of profiling groups results returned by ListProfilingGroups in paginated output. When this parameter is used, ListProfilingGroups only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another ListProfilingGroups request with the returned nextToken value.
    public var maxResults: Swift.Int?
    /// The nextToken value returned from a previous paginated ListProfilingGroups request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. This token should be treated as an opaque identifier that is only used to retrieve the next items in a list and not for other programmatic purposes.
    public var nextToken: Swift.String?

    public init(
        includeDescription: Swift.Bool? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.includeDescription = includeDescription
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

/// The structure representing the listProfilingGroupsResponse.
public struct ListProfilingGroupsOutput: Swift.Sendable {
    /// The nextToken value to include in a future ListProfilingGroups request. When the results of a ListProfilingGroups request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
    public var nextToken: Swift.String?
    /// A returned list of profiling group names. A list of the names is returned only if includeDescription is false, otherwise a list of [ProfilingGroupDescription](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_ProfilingGroupDescription.html) objects is returned.
    /// This member is required.
    public var profilingGroupNames: [Swift.String]?
    /// A returned list [ProfilingGroupDescription](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_ProfilingGroupDescription.html) objects. A list of [ProfilingGroupDescription](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_ProfilingGroupDescription.html) objects is returned only if includeDescription is true, otherwise a list of profiling group names is returned.
    public var profilingGroups: [CodeGuruProfilerClientTypes.ProfilingGroupDescription]?

    public init(
        nextToken: Swift.String? = nil,
        profilingGroupNames: [Swift.String]? = nil,
        profilingGroups: [CodeGuruProfilerClientTypes.ProfilingGroupDescription]? = nil
    )
    {
        self.nextToken = nextToken
        self.profilingGroupNames = profilingGroupNames
        self.profilingGroups = profilingGroups
    }
}

/// The structure representing the postAgentProfileRequest.
public struct PostAgentProfileInput: Swift.Sendable {
    /// The submitted profiling data.
    /// This member is required.
    public var agentProfile: Foundation.Data?
    /// The format of the submitted profiling data. The format maps to the Accept and Content-Type headers of the HTTP request. You can specify one of the following: or the default .
    ///
    /// * application/json — standard JSON format
    ///
    /// * application/x-amzn-ion — the Amazon Ion data format. For more information, see [Amazon Ion](http://amzn.github.io/ion-docs/).
    /// This member is required.
    public var contentType: Swift.String?
    /// Amazon CodeGuru Profiler uses this universally unique identifier (UUID) to prevent the accidental submission of duplicate profiling data if there are failures and retries.
    public var profileToken: Swift.String?
    /// The name of the profiling group with the aggregated profile that receives the submitted profiling data.
    /// This member is required.
    public var profilingGroupName: Swift.String?

    public init(
        agentProfile: Foundation.Data? = nil,
        contentType: Swift.String? = nil,
        profileToken: Swift.String? = nil,
        profilingGroupName: Swift.String? = nil
    )
    {
        self.agentProfile = agentProfile
        self.contentType = contentType
        self.profileToken = profileToken
        self.profilingGroupName = profilingGroupName
    }
}

/// The structure representing the postAgentProfileResponse.
public struct PostAgentProfileOutput: Swift.Sendable {

    public init() { }
}

/// The structure representing the putPermissionRequest.
public struct PutPermissionInput: Swift.Sendable {
    /// Specifies an action group that contains permissions to add to a profiling group resource. One action group is supported, agentPermissions, which grants permission to perform actions required by the profiling agent, ConfigureAgent and PostAgentProfile permissions.
    /// This member is required.
    public var actionGroup: CodeGuruProfilerClientTypes.ActionGroup?
    /// A list ARNs for the roles and users you want to grant access to the profiling group. Wildcards are not are supported in the ARNs.
    /// This member is required.
    public var principals: [Swift.String]?
    /// The name of the profiling group to grant access to.
    /// This member is required.
    public var profilingGroupName: Swift.String?
    /// A universally unique identifier (UUID) for the revision of the policy you are adding to the profiling group. Do not specify this when you add permissions to a profiling group for the first time. If a policy already exists on the profiling group, you must specify the revisionId.
    public var revisionId: Swift.String?

    public init(
        actionGroup: CodeGuruProfilerClientTypes.ActionGroup? = nil,
        principals: [Swift.String]? = nil,
        profilingGroupName: Swift.String? = nil,
        revisionId: Swift.String? = nil
    )
    {
        self.actionGroup = actionGroup
        self.principals = principals
        self.profilingGroupName = profilingGroupName
        self.revisionId = revisionId
    }
}

/// The structure representing the putPermissionResponse.
public struct PutPermissionOutput: Swift.Sendable {
    /// The JSON-formatted resource-based policy on the profiling group that includes the added permissions.
    /// This member is required.
    public var policy: Swift.String?
    /// A universally unique identifier (UUID) for the revision of the resource-based policy that includes the added permissions. The JSON-formatted policy is in the policy element of the response.
    /// This member is required.
    public var revisionId: Swift.String?

    public init(
        policy: Swift.String? = nil,
        revisionId: Swift.String? = nil
    )
    {
        self.policy = policy
        self.revisionId = revisionId
    }
}

/// The structure representing the RemoveNotificationChannelRequest.
public struct RemoveNotificationChannelInput: Swift.Sendable {
    /// The id of the channel that we want to stop receiving notifications.
    /// This member is required.
    public var channelId: Swift.String?
    /// The name of the profiling group we want to change notification configuration for.
    /// This member is required.
    public var profilingGroupName: Swift.String?

    public init(
        channelId: Swift.String? = nil,
        profilingGroupName: Swift.String? = nil
    )
    {
        self.channelId = channelId
        self.profilingGroupName = profilingGroupName
    }
}

/// The structure representing the RemoveNotificationChannelResponse.
public struct RemoveNotificationChannelOutput: Swift.Sendable {
    /// The new notification configuration for this profiling group.
    public var notificationConfiguration: CodeGuruProfilerClientTypes.NotificationConfiguration?

    public init(
        notificationConfiguration: CodeGuruProfilerClientTypes.NotificationConfiguration? = nil
    )
    {
        self.notificationConfiguration = notificationConfiguration
    }
}

/// The structure representing the removePermissionRequest.
public struct RemovePermissionInput: Swift.Sendable {
    /// Specifies an action group that contains the permissions to remove from a profiling group's resource-based policy. One action group is supported, agentPermissions, which grants ConfigureAgent and PostAgentProfile permissions.
    /// This member is required.
    public var actionGroup: CodeGuruProfilerClientTypes.ActionGroup?
    /// The name of the profiling group.
    /// This member is required.
    public var profilingGroupName: Swift.String?
    /// A universally unique identifier (UUID) for the revision of the resource-based policy from which you want to remove permissions.
    /// This member is required.
    public var revisionId: Swift.String?

    public init(
        actionGroup: CodeGuruProfilerClientTypes.ActionGroup? = nil,
        profilingGroupName: Swift.String? = nil,
        revisionId: Swift.String? = nil
    )
    {
        self.actionGroup = actionGroup
        self.profilingGroupName = profilingGroupName
        self.revisionId = revisionId
    }
}

/// The structure representing the removePermissionResponse.
public struct RemovePermissionOutput: Swift.Sendable {
    /// The JSON-formatted resource-based policy on the profiling group after the specified permissions were removed.
    /// This member is required.
    public var policy: Swift.String?
    /// A universally unique identifier (UUID) for the revision of the resource-based policy after the specified permissions were removed. The updated JSON-formatted policy is in the policy element of the response.
    /// This member is required.
    public var revisionId: Swift.String?

    public init(
        policy: Swift.String? = nil,
        revisionId: Swift.String? = nil
    )
    {
        self.policy = policy
        self.revisionId = revisionId
    }
}

/// The structure representing the SubmitFeedbackRequest.
public struct SubmitFeedbackInput: Swift.Sendable {
    /// The universally unique identifier (UUID) of the [AnomalyInstance](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_AnomalyInstance.html) object that is included in the analysis data.
    /// This member is required.
    public var anomalyInstanceId: Swift.String?
    /// Optional feedback about this anomaly.
    public var comment: Swift.String?
    /// The name of the profiling group that is associated with the analysis data.
    /// This member is required.
    public var profilingGroupName: Swift.String?
    /// The feedback tpye. Thee are two valid values, Positive and Negative.
    /// This member is required.
    public var type: CodeGuruProfilerClientTypes.FeedbackType?

    public init(
        anomalyInstanceId: Swift.String? = nil,
        comment: Swift.String? = nil,
        profilingGroupName: Swift.String? = nil,
        type: CodeGuruProfilerClientTypes.FeedbackType? = nil
    )
    {
        self.anomalyInstanceId = anomalyInstanceId
        self.comment = comment
        self.profilingGroupName = profilingGroupName
        self.type = type
    }
}

/// The structure representing the SubmitFeedbackResponse.
public struct SubmitFeedbackOutput: Swift.Sendable {

    public init() { }
}

/// The structure representing the updateProfilingGroupRequest.
public struct UpdateProfilingGroupInput: Swift.Sendable {
    /// Specifies whether profiling is enabled or disabled for a profiling group.
    /// This member is required.
    public var agentOrchestrationConfig: CodeGuruProfilerClientTypes.AgentOrchestrationConfig?
    /// The name of the profiling group to update.
    /// This member is required.
    public var profilingGroupName: Swift.String?

    public init(
        agentOrchestrationConfig: CodeGuruProfilerClientTypes.AgentOrchestrationConfig? = nil,
        profilingGroupName: Swift.String? = nil
    )
    {
        self.agentOrchestrationConfig = agentOrchestrationConfig
        self.profilingGroupName = profilingGroupName
    }
}

/// The structure representing the updateProfilingGroupResponse.
public struct UpdateProfilingGroupOutput: Swift.Sendable {
    /// A [ProfilingGroupDescription](https://docs.aws.amazon.com/codeguru/latest/profiler-api/API_ProfilingGroupDescription.html) that contains information about the returned updated profiling group.
    /// This member is required.
    public var profilingGroup: CodeGuruProfilerClientTypes.ProfilingGroupDescription?

    public init(
        profilingGroup: CodeGuruProfilerClientTypes.ProfilingGroupDescription? = nil
    )
    {
        self.profilingGroup = profilingGroup
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource that the tags are added to.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The list of tags that are added to the specified resource.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource that contains the tags to remove.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// A list of tag keys. Existing tags of resources with keys in this list are removed from the specified resource.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

extension AddNotificationChannelsInput {

    static func urlPathProvider(_ value: AddNotificationChannelsInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        return "/profilingGroups/\(profilingGroupName.urlPercentEncoding())/notificationConfiguration"
    }
}

extension BatchGetFrameMetricDataInput {

    static func urlPathProvider(_ value: BatchGetFrameMetricDataInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        return "/profilingGroups/\(profilingGroupName.urlPercentEncoding())/frames/-/metrics"
    }
}

extension BatchGetFrameMetricDataInput {

    static func queryItemProvider(_ value: BatchGetFrameMetricDataInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let period = value.period {
            let periodQueryItem = Smithy.URIQueryItem(name: "period".urlPercentEncoding(), value: Swift.String(period).urlPercentEncoding())
            items.append(periodQueryItem)
        }
        if let startTime = value.startTime {
            let startTimeQueryItem = Smithy.URIQueryItem(name: "startTime".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: startTime)).urlPercentEncoding())
            items.append(startTimeQueryItem)
        }
        if let endTime = value.endTime {
            let endTimeQueryItem = Smithy.URIQueryItem(name: "endTime".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: endTime)).urlPercentEncoding())
            items.append(endTimeQueryItem)
        }
        if let targetResolution = value.targetResolution {
            let targetResolutionQueryItem = Smithy.URIQueryItem(name: "targetResolution".urlPercentEncoding(), value: Swift.String(targetResolution.rawValue).urlPercentEncoding())
            items.append(targetResolutionQueryItem)
        }
        return items
    }
}

extension ConfigureAgentInput {

    static func urlPathProvider(_ value: ConfigureAgentInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        return "/profilingGroups/\(profilingGroupName.urlPercentEncoding())/configureAgent"
    }
}

extension CreateProfilingGroupInput {

    static func urlPathProvider(_ value: CreateProfilingGroupInput) -> Swift.String? {
        return "/profilingGroups"
    }
}

extension CreateProfilingGroupInput {

    static func queryItemProvider(_ value: CreateProfilingGroupInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let clientToken = value.clientToken else {
            let message = "Creating a URL Query Item failed. clientToken is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let clientTokenQueryItem = Smithy.URIQueryItem(name: "clientToken".urlPercentEncoding(), value: Swift.String(clientToken).urlPercentEncoding())
        items.append(clientTokenQueryItem)
        return items
    }
}

extension DeleteProfilingGroupInput {

    static func urlPathProvider(_ value: DeleteProfilingGroupInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        return "/profilingGroups/\(profilingGroupName.urlPercentEncoding())"
    }
}

extension DescribeProfilingGroupInput {

    static func urlPathProvider(_ value: DescribeProfilingGroupInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        return "/profilingGroups/\(profilingGroupName.urlPercentEncoding())"
    }
}

extension GetFindingsReportAccountSummaryInput {

    static func urlPathProvider(_ value: GetFindingsReportAccountSummaryInput) -> Swift.String? {
        return "/internal/findingsReports"
    }
}

extension GetFindingsReportAccountSummaryInput {

    static func queryItemProvider(_ value: GetFindingsReportAccountSummaryInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let dailyReportsOnly = value.dailyReportsOnly {
            let dailyReportsOnlyQueryItem = Smithy.URIQueryItem(name: "dailyReportsOnly".urlPercentEncoding(), value: Swift.String(dailyReportsOnly).urlPercentEncoding())
            items.append(dailyReportsOnlyQueryItem)
        }
        return items
    }
}

extension GetNotificationConfigurationInput {

    static func urlPathProvider(_ value: GetNotificationConfigurationInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        return "/profilingGroups/\(profilingGroupName.urlPercentEncoding())/notificationConfiguration"
    }
}

extension GetPolicyInput {

    static func urlPathProvider(_ value: GetPolicyInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        return "/profilingGroups/\(profilingGroupName.urlPercentEncoding())/policy"
    }
}

extension GetProfileInput {

    static func urlPathProvider(_ value: GetProfileInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        return "/profilingGroups/\(profilingGroupName.urlPercentEncoding())/profile"
    }
}

extension GetProfileInput {

    static func headerProvider(_ value: GetProfileInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let accept = value.accept {
            items.add(SmithyHTTPAPI.Header(name: "Accept", value: Swift.String(accept)))
        }
        return items
    }
}

extension GetProfileInput {

    static func queryItemProvider(_ value: GetProfileInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let maxDepth = value.maxDepth {
            let maxDepthQueryItem = Smithy.URIQueryItem(name: "maxDepth".urlPercentEncoding(), value: Swift.String(maxDepth).urlPercentEncoding())
            items.append(maxDepthQueryItem)
        }
        if let period = value.period {
            let periodQueryItem = Smithy.URIQueryItem(name: "period".urlPercentEncoding(), value: Swift.String(period).urlPercentEncoding())
            items.append(periodQueryItem)
        }
        if let startTime = value.startTime {
            let startTimeQueryItem = Smithy.URIQueryItem(name: "startTime".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: startTime)).urlPercentEncoding())
            items.append(startTimeQueryItem)
        }
        if let endTime = value.endTime {
            let endTimeQueryItem = Smithy.URIQueryItem(name: "endTime".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: endTime)).urlPercentEncoding())
            items.append(endTimeQueryItem)
        }
        return items
    }
}

extension GetRecommendationsInput {

    static func urlPathProvider(_ value: GetRecommendationsInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        return "/internal/profilingGroups/\(profilingGroupName.urlPercentEncoding())/recommendations"
    }
}

extension GetRecommendationsInput {

    static func queryItemProvider(_ value: GetRecommendationsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let startTime = value.startTime else {
            let message = "Creating a URL Query Item failed. startTime is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let startTimeQueryItem = Smithy.URIQueryItem(name: "startTime".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: startTime)).urlPercentEncoding())
        items.append(startTimeQueryItem)
        guard let endTime = value.endTime else {
            let message = "Creating a URL Query Item failed. endTime is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let endTimeQueryItem = Smithy.URIQueryItem(name: "endTime".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: endTime)).urlPercentEncoding())
        items.append(endTimeQueryItem)
        if let locale = value.locale {
            let localeQueryItem = Smithy.URIQueryItem(name: "locale".urlPercentEncoding(), value: Swift.String(locale).urlPercentEncoding())
            items.append(localeQueryItem)
        }
        return items
    }
}

extension ListFindingsReportsInput {

    static func urlPathProvider(_ value: ListFindingsReportsInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        return "/internal/profilingGroups/\(profilingGroupName.urlPercentEncoding())/findingsReports"
    }
}

extension ListFindingsReportsInput {

    static func queryItemProvider(_ value: ListFindingsReportsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let dailyReportsOnly = value.dailyReportsOnly {
            let dailyReportsOnlyQueryItem = Smithy.URIQueryItem(name: "dailyReportsOnly".urlPercentEncoding(), value: Swift.String(dailyReportsOnly).urlPercentEncoding())
            items.append(dailyReportsOnlyQueryItem)
        }
        guard let startTime = value.startTime else {
            let message = "Creating a URL Query Item failed. startTime is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let startTimeQueryItem = Smithy.URIQueryItem(name: "startTime".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: startTime)).urlPercentEncoding())
        items.append(startTimeQueryItem)
        guard let endTime = value.endTime else {
            let message = "Creating a URL Query Item failed. endTime is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let endTimeQueryItem = Smithy.URIQueryItem(name: "endTime".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: endTime)).urlPercentEncoding())
        items.append(endTimeQueryItem)
        return items
    }
}

extension ListProfileTimesInput {

    static func urlPathProvider(_ value: ListProfileTimesInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        return "/profilingGroups/\(profilingGroupName.urlPercentEncoding())/profileTimes"
    }
}

extension ListProfileTimesInput {

    static func queryItemProvider(_ value: ListProfileTimesInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let period = value.period else {
            let message = "Creating a URL Query Item failed. period is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let periodQueryItem = Smithy.URIQueryItem(name: "period".urlPercentEncoding(), value: Swift.String(period.rawValue).urlPercentEncoding())
        items.append(periodQueryItem)
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let orderBy = value.orderBy {
            let orderByQueryItem = Smithy.URIQueryItem(name: "orderBy".urlPercentEncoding(), value: Swift.String(orderBy.rawValue).urlPercentEncoding())
            items.append(orderByQueryItem)
        }
        guard let startTime = value.startTime else {
            let message = "Creating a URL Query Item failed. startTime is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let startTimeQueryItem = Smithy.URIQueryItem(name: "startTime".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: startTime)).urlPercentEncoding())
        items.append(startTimeQueryItem)
        guard let endTime = value.endTime else {
            let message = "Creating a URL Query Item failed. endTime is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let endTimeQueryItem = Smithy.URIQueryItem(name: "endTime".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: endTime)).urlPercentEncoding())
        items.append(endTimeQueryItem)
        return items
    }
}

extension ListProfilingGroupsInput {

    static func urlPathProvider(_ value: ListProfilingGroupsInput) -> Swift.String? {
        return "/profilingGroups"
    }
}

extension ListProfilingGroupsInput {

    static func queryItemProvider(_ value: ListProfilingGroupsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let includeDescription = value.includeDescription {
            let includeDescriptionQueryItem = Smithy.URIQueryItem(name: "includeDescription".urlPercentEncoding(), value: Swift.String(includeDescription).urlPercentEncoding())
            items.append(includeDescriptionQueryItem)
        }
        return items
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension PostAgentProfileInput {

    static func urlPathProvider(_ value: PostAgentProfileInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        return "/profilingGroups/\(profilingGroupName.urlPercentEncoding())/agentProfile"
    }
}

extension PostAgentProfileInput {

    static func headerProvider(_ value: PostAgentProfileInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let contentType = value.contentType {
            items.add(SmithyHTTPAPI.Header(name: "Content-Type", value: Swift.String(contentType)))
        }
        return items
    }
}

extension PostAgentProfileInput {

    static func queryItemProvider(_ value: PostAgentProfileInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let profileToken = value.profileToken {
            let profileTokenQueryItem = Smithy.URIQueryItem(name: "profileToken".urlPercentEncoding(), value: Swift.String(profileToken).urlPercentEncoding())
            items.append(profileTokenQueryItem)
        }
        return items
    }
}

extension PutPermissionInput {

    static func urlPathProvider(_ value: PutPermissionInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        guard let actionGroup = value.actionGroup else {
            return nil
        }
        return "/profilingGroups/\(profilingGroupName.urlPercentEncoding())/policy/\(actionGroup.rawValue.urlPercentEncoding())"
    }
}

extension RemoveNotificationChannelInput {

    static func urlPathProvider(_ value: RemoveNotificationChannelInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        guard let channelId = value.channelId else {
            return nil
        }
        return "/profilingGroups/\(profilingGroupName.urlPercentEncoding())/notificationConfiguration/\(channelId.urlPercentEncoding())"
    }
}

extension RemovePermissionInput {

    static func urlPathProvider(_ value: RemovePermissionInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        guard let actionGroup = value.actionGroup else {
            return nil
        }
        return "/profilingGroups/\(profilingGroupName.urlPercentEncoding())/policy/\(actionGroup.rawValue.urlPercentEncoding())"
    }
}

extension RemovePermissionInput {

    static func queryItemProvider(_ value: RemovePermissionInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let revisionId = value.revisionId else {
            let message = "Creating a URL Query Item failed. revisionId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let revisionIdQueryItem = Smithy.URIQueryItem(name: "revisionId".urlPercentEncoding(), value: Swift.String(revisionId).urlPercentEncoding())
        items.append(revisionIdQueryItem)
        return items
    }
}

extension SubmitFeedbackInput {

    static func urlPathProvider(_ value: SubmitFeedbackInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        guard let anomalyInstanceId = value.anomalyInstanceId else {
            return nil
        }
        return "/internal/profilingGroups/\(profilingGroupName.urlPercentEncoding())/anomalies/\(anomalyInstanceId.urlPercentEncoding())/feedback"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension UpdateProfilingGroupInput {

    static func urlPathProvider(_ value: UpdateProfilingGroupInput) -> Swift.String? {
        guard let profilingGroupName = value.profilingGroupName else {
            return nil
        }
        return "/profilingGroups/\(profilingGroupName.urlPercentEncoding())"
    }
}

extension AddNotificationChannelsInput {

    static func write(value: AddNotificationChannelsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["channels"].writeList(value.channels, memberWritingClosure: CodeGuruProfilerClientTypes.Channel.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension BatchGetFrameMetricDataInput {

    static func write(value: BatchGetFrameMetricDataInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["frameMetrics"].writeList(value.frameMetrics, memberWritingClosure: CodeGuruProfilerClientTypes.FrameMetric.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ConfigureAgentInput {

    static func write(value: ConfigureAgentInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["fleetInstanceId"].write(value.fleetInstanceId)
        try writer["metadata"].writeMap(value.metadata, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CreateProfilingGroupInput {

    static func write(value: CreateProfilingGroupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["agentOrchestrationConfig"].write(value.agentOrchestrationConfig, with: CodeGuruProfilerClientTypes.AgentOrchestrationConfig.write(value:to:))
        try writer["computePlatform"].write(value.computePlatform)
        try writer["profilingGroupName"].write(value.profilingGroupName)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension PostAgentProfileInput {

    static func write(value: PostAgentProfileInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["agentProfile"].write(value.agentProfile)
    }
}

extension PutPermissionInput {

    static func write(value: PutPermissionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["principals"].writeList(value.principals, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["revisionId"].write(value.revisionId)
    }
}

extension SubmitFeedbackInput {

    static func write(value: SubmitFeedbackInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["comment"].write(value.comment)
        try writer["type"].write(value.type)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension UpdateProfilingGroupInput {

    static func write(value: UpdateProfilingGroupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["agentOrchestrationConfig"].write(value.agentOrchestrationConfig, with: CodeGuruProfilerClientTypes.AgentOrchestrationConfig.write(value:to:))
    }
}

extension AddNotificationChannelsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AddNotificationChannelsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = AddNotificationChannelsOutput()
        value.notificationConfiguration = try reader["notificationConfiguration"].readIfPresent(with: CodeGuruProfilerClientTypes.NotificationConfiguration.read(from:))
        return value
    }
}

extension BatchGetFrameMetricDataOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> BatchGetFrameMetricDataOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = BatchGetFrameMetricDataOutput()
        value.endTime = try reader["endTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.endTimes = try reader["endTimes"].readListIfPresent(memberReadingClosure: CodeGuruProfilerClientTypes.TimestampStructure.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.frameMetricData = try reader["frameMetricData"].readListIfPresent(memberReadingClosure: CodeGuruProfilerClientTypes.FrameMetricDatum.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.resolution = try reader["resolution"].readIfPresent() ?? .sdkUnknown("")
        value.startTime = try reader["startTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.unprocessedEndTimes = try reader["unprocessedEndTimes"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.listReadingClosure(memberReadingClosure: CodeGuruProfilerClientTypes.TimestampStructure.read(from:), memberNodeInfo: "member", isFlattened: false), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false) ?? [:]
        return value
    }
}

extension ConfigureAgentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ConfigureAgentOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ConfigureAgentOutput()
        value.configuration = try reader.readIfPresent(with: CodeGuruProfilerClientTypes.AgentConfiguration.read(from:))
        return value
    }
}

extension CreateProfilingGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateProfilingGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateProfilingGroupOutput()
        value.profilingGroup = try reader.readIfPresent(with: CodeGuruProfilerClientTypes.ProfilingGroupDescription.read(from:))
        return value
    }
}

extension DeleteProfilingGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteProfilingGroupOutput {
        return DeleteProfilingGroupOutput()
    }
}

extension DescribeProfilingGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeProfilingGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeProfilingGroupOutput()
        value.profilingGroup = try reader.readIfPresent(with: CodeGuruProfilerClientTypes.ProfilingGroupDescription.read(from:))
        return value
    }
}

extension GetFindingsReportAccountSummaryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetFindingsReportAccountSummaryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetFindingsReportAccountSummaryOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.reportSummaries = try reader["reportSummaries"].readListIfPresent(memberReadingClosure: CodeGuruProfilerClientTypes.FindingsReportSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension GetNotificationConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetNotificationConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetNotificationConfigurationOutput()
        value.notificationConfiguration = try reader["notificationConfiguration"].readIfPresent(with: CodeGuruProfilerClientTypes.NotificationConfiguration.read(from:))
        return value
    }
}

extension GetPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetPolicyOutput()
        value.policy = try reader["policy"].readIfPresent() ?? ""
        value.revisionId = try reader["revisionId"].readIfPresent() ?? ""
        return value
    }
}

extension GetProfileOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetProfileOutput {
        var value = GetProfileOutput()
        if let contentEncodingHeaderValue = httpResponse.headers.value(for: "Content-Encoding") {
            value.contentEncoding = contentEncodingHeaderValue
        }
        if let contentTypeHeaderValue = httpResponse.headers.value(for: "Content-Type") {
            value.contentType = contentTypeHeaderValue
        }
        switch httpResponse.body {
        case .data(let data):
            value.profile = data
        case .stream(let stream):
            value.profile = try stream.readToEnd()
        case .noStream:
            value.profile = nil
        }
        return value
    }
}

extension GetRecommendationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetRecommendationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetRecommendationsOutput()
        value.anomalies = try reader["anomalies"].readListIfPresent(memberReadingClosure: CodeGuruProfilerClientTypes.Anomaly.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.profileEndTime = try reader["profileEndTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.profileStartTime = try reader["profileStartTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.profilingGroupName = try reader["profilingGroupName"].readIfPresent() ?? ""
        value.recommendations = try reader["recommendations"].readListIfPresent(memberReadingClosure: CodeGuruProfilerClientTypes.Recommendation.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ListFindingsReportsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListFindingsReportsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListFindingsReportsOutput()
        value.findingsReportSummaries = try reader["findingsReportSummaries"].readListIfPresent(memberReadingClosure: CodeGuruProfilerClientTypes.FindingsReportSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListProfileTimesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListProfileTimesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListProfileTimesOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.profileTimes = try reader["profileTimes"].readListIfPresent(memberReadingClosure: CodeGuruProfilerClientTypes.ProfileTime.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ListProfilingGroupsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListProfilingGroupsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListProfilingGroupsOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.profilingGroupNames = try reader["profilingGroupNames"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.profilingGroups = try reader["profilingGroups"].readListIfPresent(memberReadingClosure: CodeGuruProfilerClientTypes.ProfilingGroupDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension PostAgentProfileOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PostAgentProfileOutput {
        return PostAgentProfileOutput()
    }
}

extension PutPermissionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutPermissionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutPermissionOutput()
        value.policy = try reader["policy"].readIfPresent() ?? ""
        value.revisionId = try reader["revisionId"].readIfPresent() ?? ""
        return value
    }
}

extension RemoveNotificationChannelOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RemoveNotificationChannelOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = RemoveNotificationChannelOutput()
        value.notificationConfiguration = try reader["notificationConfiguration"].readIfPresent(with: CodeGuruProfilerClientTypes.NotificationConfiguration.read(from:))
        return value
    }
}

extension RemovePermissionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RemovePermissionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = RemovePermissionOutput()
        value.policy = try reader["policy"].readIfPresent() ?? ""
        value.revisionId = try reader["revisionId"].readIfPresent() ?? ""
        return value
    }
}

extension SubmitFeedbackOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SubmitFeedbackOutput {
        return SubmitFeedbackOutput()
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateProfilingGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateProfilingGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateProfilingGroupOutput()
        value.profilingGroup = try reader.readIfPresent(with: CodeGuruProfilerClientTypes.ProfilingGroupDescription.read(from:))
        return value
    }
}

enum AddNotificationChannelsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum BatchGetFrameMetricDataOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ConfigureAgentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateProfilingGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteProfilingGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeProfilingGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetFindingsReportAccountSummaryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetNotificationConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetProfileOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetRecommendationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListFindingsReportsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListProfileTimesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListProfilingGroupsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PostAgentProfileOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutPermissionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RemoveNotificationChannelOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RemovePermissionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SubmitFeedbackOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateProfilingGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceQuotaExceededException {
        let reader = baseError.errorBodyReader
        var value = ServiceQuotaExceededException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        var value = ThrottlingException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CodeGuruProfilerClientTypes.NotificationConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.NotificationConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.NotificationConfiguration()
        value.channels = try reader["channels"].readListIfPresent(memberReadingClosure: CodeGuruProfilerClientTypes.Channel.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension CodeGuruProfilerClientTypes.Channel {

    static func write(value: CodeGuruProfilerClientTypes.Channel?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["eventPublishers"].writeList(value.eventPublishers, memberWritingClosure: SmithyReadWrite.WritingClosureBox<CodeGuruProfilerClientTypes.EventPublisher>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["id"].write(value.id)
        try writer["uri"].write(value.uri)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.Channel {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.Channel()
        value.id = try reader["id"].readIfPresent()
        value.uri = try reader["uri"].readIfPresent() ?? ""
        value.eventPublishers = try reader["eventPublishers"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<CodeGuruProfilerClientTypes.EventPublisher>().read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension CodeGuruProfilerClientTypes.TimestampStructure {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.TimestampStructure {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.TimestampStructure()
        value.value = try reader["value"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension CodeGuruProfilerClientTypes.FrameMetricDatum {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.FrameMetricDatum {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.FrameMetricDatum()
        value.frameMetric = try reader["frameMetric"].readIfPresent(with: CodeGuruProfilerClientTypes.FrameMetric.read(from:))
        value.values = try reader["values"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readDouble(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension CodeGuruProfilerClientTypes.FrameMetric {

    static func write(value: CodeGuruProfilerClientTypes.FrameMetric?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["frameName"].write(value.frameName)
        try writer["threadStates"].writeList(value.threadStates, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["type"].write(value.type)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.FrameMetric {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.FrameMetric()
        value.frameName = try reader["frameName"].readIfPresent() ?? ""
        value.type = try reader["type"].readIfPresent() ?? .sdkUnknown("")
        value.threadStates = try reader["threadStates"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension CodeGuruProfilerClientTypes.AgentConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.AgentConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.AgentConfiguration()
        value.shouldProfile = try reader["shouldProfile"].readIfPresent() ?? false
        value.periodInSeconds = try reader["periodInSeconds"].readIfPresent() ?? 0
        value.agentParameters = try reader["agentParameters"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension CodeGuruProfilerClientTypes.ProfilingGroupDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.ProfilingGroupDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.ProfilingGroupDescription()
        value.name = try reader["name"].readIfPresent()
        value.agentOrchestrationConfig = try reader["agentOrchestrationConfig"].readIfPresent(with: CodeGuruProfilerClientTypes.AgentOrchestrationConfig.read(from:))
        value.arn = try reader["arn"].readIfPresent()
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.updatedAt = try reader["updatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.profilingStatus = try reader["profilingStatus"].readIfPresent(with: CodeGuruProfilerClientTypes.ProfilingStatus.read(from:))
        value.computePlatform = try reader["computePlatform"].readIfPresent()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension CodeGuruProfilerClientTypes.ProfilingStatus {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.ProfilingStatus {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.ProfilingStatus()
        value.latestAgentProfileReportedAt = try reader["latestAgentProfileReportedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.latestAggregatedProfile = try reader["latestAggregatedProfile"].readIfPresent(with: CodeGuruProfilerClientTypes.AggregatedProfileTime.read(from:))
        value.latestAgentOrchestratedAt = try reader["latestAgentOrchestratedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension CodeGuruProfilerClientTypes.AggregatedProfileTime {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.AggregatedProfileTime {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.AggregatedProfileTime()
        value.start = try reader["start"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.period = try reader["period"].readIfPresent()
        return value
    }
}

extension CodeGuruProfilerClientTypes.AgentOrchestrationConfig {

    static func write(value: CodeGuruProfilerClientTypes.AgentOrchestrationConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["profilingEnabled"].write(value.profilingEnabled)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.AgentOrchestrationConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.AgentOrchestrationConfig()
        value.profilingEnabled = try reader["profilingEnabled"].readIfPresent() ?? false
        return value
    }
}

extension CodeGuruProfilerClientTypes.FindingsReportSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.FindingsReportSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.FindingsReportSummary()
        value.id = try reader["id"].readIfPresent()
        value.profilingGroupName = try reader["profilingGroupName"].readIfPresent()
        value.profileStartTime = try reader["profileStartTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.profileEndTime = try reader["profileEndTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.totalNumberOfFindings = try reader["totalNumberOfFindings"].readIfPresent()
        return value
    }
}

extension CodeGuruProfilerClientTypes.Recommendation {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.Recommendation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.Recommendation()
        value.allMatchesCount = try reader["allMatchesCount"].readIfPresent() ?? 0
        value.allMatchesSum = try reader["allMatchesSum"].readIfPresent() ?? 0.0
        value.pattern = try reader["pattern"].readIfPresent(with: CodeGuruProfilerClientTypes.Pattern.read(from:))
        value.topMatches = try reader["topMatches"].readListIfPresent(memberReadingClosure: CodeGuruProfilerClientTypes.Match.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.startTime = try reader["startTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.endTime = try reader["endTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension CodeGuruProfilerClientTypes.Match {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.Match {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.Match()
        value.targetFramesIndex = try reader["targetFramesIndex"].readIfPresent()
        value.frameAddress = try reader["frameAddress"].readIfPresent()
        value.thresholdBreachValue = try reader["thresholdBreachValue"].readIfPresent()
        return value
    }
}

extension CodeGuruProfilerClientTypes.Pattern {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.Pattern {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.Pattern()
        value.id = try reader["id"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.description = try reader["description"].readIfPresent()
        value.resolutionSteps = try reader["resolutionSteps"].readIfPresent()
        value.targetFrames = try reader["targetFrames"].readListIfPresent(memberReadingClosure: SmithyReadWrite.listReadingClosure(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false), memberNodeInfo: "member", isFlattened: false)
        value.thresholdPercent = try reader["thresholdPercent"].readIfPresent() ?? 0
        value.countersToAggregate = try reader["countersToAggregate"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension CodeGuruProfilerClientTypes.Anomaly {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.Anomaly {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.Anomaly()
        value.metric = try reader["metric"].readIfPresent(with: CodeGuruProfilerClientTypes.Metric.read(from:))
        value.reason = try reader["reason"].readIfPresent() ?? ""
        value.instances = try reader["instances"].readListIfPresent(memberReadingClosure: CodeGuruProfilerClientTypes.AnomalyInstance.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension CodeGuruProfilerClientTypes.AnomalyInstance {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.AnomalyInstance {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.AnomalyInstance()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.startTime = try reader["startTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.endTime = try reader["endTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.userFeedback = try reader["userFeedback"].readIfPresent(with: CodeGuruProfilerClientTypes.UserFeedback.read(from:))
        return value
    }
}

extension CodeGuruProfilerClientTypes.UserFeedback {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.UserFeedback {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.UserFeedback()
        value.type = try reader["type"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension CodeGuruProfilerClientTypes.Metric {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.Metric {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.Metric()
        value.frameName = try reader["frameName"].readIfPresent() ?? ""
        value.type = try reader["type"].readIfPresent() ?? .sdkUnknown("")
        value.threadStates = try reader["threadStates"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension CodeGuruProfilerClientTypes.ProfileTime {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruProfilerClientTypes.ProfileTime {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruProfilerClientTypes.ProfileTime()
        value.start = try reader["start"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

public enum CodeGuruProfilerClientTypes {}
