//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHT<PERSON><PERSON>I.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.AWSJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError

extension ECRPUBLICClientTypes {

    /// An authorization token data object that corresponds to a public registry.
    public struct AuthorizationData: Swift.Sendable {
        /// A base64-encoded string that contains authorization data for a public Amazon ECR registry. When the string is decoded, it's presented in the format user:password for public registry authentication using docker login.
        public var authorizationToken: Swift.String?
        /// The Unix time in seconds and milliseconds when the authorization token expires. Authorization tokens are valid for 12 hours.
        public var expiresAt: Foundation.Date?

        public init(
            authorizationToken: Swift.String? = nil,
            expiresAt: Foundation.Date? = nil
        )
        {
            self.authorizationToken = authorizationToken
            self.expiresAt = expiresAt
        }
    }
}

/// The specified parameter is invalid. Review the available parameters for the API request.
public struct InvalidParameterException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidParameterException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The registry doesn't exist.
public struct RegistryNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RegistryNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified repository can't be found. Check the spelling of the specified repository and ensure that you're performing operations on the correct registry.
public struct RepositoryNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RepositoryNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// These errors are usually caused by a server-side issue.
public struct ServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The action isn't supported in this Region.
public struct UnsupportedCommandException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UnsupportedCommandException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct BatchCheckLayerAvailabilityInput: Swift.Sendable {
    /// The digests of the image layers to check.
    /// This member is required.
    public var layerDigests: [Swift.String]?
    /// The Amazon Web Services account ID, or registry alias, associated with the public registry that contains the image layers to check. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository that's associated with the image layers to check.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        layerDigests: [Swift.String]? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.layerDigests = layerDigests
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

extension ECRPUBLICClientTypes {

    public enum LayerFailureCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case invalidlayerdigest
        case missinglayerdigest
        case sdkUnknown(Swift.String)

        public static var allCases: [LayerFailureCode] {
            return [
                .invalidlayerdigest,
                .missinglayerdigest
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .invalidlayerdigest: return "InvalidLayerDigest"
            case .missinglayerdigest: return "MissingLayerDigest"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRPUBLICClientTypes {

    /// An object that represents an Amazon ECR image layer failure.
    public struct LayerFailure: Swift.Sendable {
        /// The failure code that's associated with the failure.
        public var failureCode: ECRPUBLICClientTypes.LayerFailureCode?
        /// The reason for the failure.
        public var failureReason: Swift.String?
        /// The layer digest that's associated with the failure.
        public var layerDigest: Swift.String?

        public init(
            failureCode: ECRPUBLICClientTypes.LayerFailureCode? = nil,
            failureReason: Swift.String? = nil,
            layerDigest: Swift.String? = nil
        )
        {
            self.failureCode = failureCode
            self.failureReason = failureReason
            self.layerDigest = layerDigest
        }
    }
}

extension ECRPUBLICClientTypes {

    public enum LayerAvailability: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case available
        case unavailable
        case sdkUnknown(Swift.String)

        public static var allCases: [LayerAvailability] {
            return [
                .available,
                .unavailable
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .available: return "AVAILABLE"
            case .unavailable: return "UNAVAILABLE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRPUBLICClientTypes {

    /// An object that represents an Amazon ECR image layer.
    public struct Layer: Swift.Sendable {
        /// The availability status of the image layer.
        public var layerAvailability: ECRPUBLICClientTypes.LayerAvailability?
        /// The sha256 digest of the image layer.
        public var layerDigest: Swift.String?
        /// The size, in bytes, of the image layer.
        public var layerSize: Swift.Int?
        /// The media type of the layer, such as application/vnd.docker.image.rootfs.diff.tar.gzip or application/vnd.oci.image.layer.v1.tar+gzip.
        public var mediaType: Swift.String?

        public init(
            layerAvailability: ECRPUBLICClientTypes.LayerAvailability? = nil,
            layerDigest: Swift.String? = nil,
            layerSize: Swift.Int? = nil,
            mediaType: Swift.String? = nil
        )
        {
            self.layerAvailability = layerAvailability
            self.layerDigest = layerDigest
            self.layerSize = layerSize
            self.mediaType = mediaType
        }
    }
}

public struct BatchCheckLayerAvailabilityOutput: Swift.Sendable {
    /// Any failures associated with the call.
    public var failures: [ECRPUBLICClientTypes.LayerFailure]?
    /// A list of image layer objects that correspond to the image layer references in the request.
    public var layers: [ECRPUBLICClientTypes.Layer]?

    public init(
        failures: [ECRPUBLICClientTypes.LayerFailure]? = nil,
        layers: [ECRPUBLICClientTypes.Layer]? = nil
    )
    {
        self.failures = failures
        self.layers = layers
    }
}

extension ECRPUBLICClientTypes {

    /// An object with identifying information for an Amazon ECR image.
    public struct ImageIdentifier: Swift.Sendable {
        /// The sha256 digest of the image manifest.
        public var imageDigest: Swift.String?
        /// The tag that's used for the image.
        public var imageTag: Swift.String?

        public init(
            imageDigest: Swift.String? = nil,
            imageTag: Swift.String? = nil
        )
        {
            self.imageDigest = imageDigest
            self.imageTag = imageTag
        }
    }
}

public struct BatchDeleteImageInput: Swift.Sendable {
    /// A list of image ID references that correspond to images to delete. The format of the imageIds reference is imageTag=tag or imageDigest=digest.
    /// This member is required.
    public var imageIds: [ECRPUBLICClientTypes.ImageIdentifier]?
    /// The Amazon Web Services account ID, or registry alias, that's associated with the registry that contains the image to delete. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// The repository in a public registry that contains the image to delete.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        imageIds: [ECRPUBLICClientTypes.ImageIdentifier]? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageIds = imageIds
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

extension ECRPUBLICClientTypes {

    public enum ImageFailureCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case imagenotfound
        case imagereferencedbymanifestlist
        case imagetagdoesnotmatchdigest
        case invalidimagedigest
        case invalidimagetag
        case kmserror
        case missingdigestandtag
        case sdkUnknown(Swift.String)

        public static var allCases: [ImageFailureCode] {
            return [
                .imagenotfound,
                .imagereferencedbymanifestlist,
                .imagetagdoesnotmatchdigest,
                .invalidimagedigest,
                .invalidimagetag,
                .kmserror,
                .missingdigestandtag
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .imagenotfound: return "ImageNotFound"
            case .imagereferencedbymanifestlist: return "ImageReferencedByManifestList"
            case .imagetagdoesnotmatchdigest: return "ImageTagDoesNotMatchDigest"
            case .invalidimagedigest: return "InvalidImageDigest"
            case .invalidimagetag: return "InvalidImageTag"
            case .kmserror: return "KmsError"
            case .missingdigestandtag: return "MissingDigestAndTag"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRPUBLICClientTypes {

    /// An object that represents an Amazon ECR image failure.
    public struct ImageFailure: Swift.Sendable {
        /// The code that's associated with the failure.
        public var failureCode: ECRPUBLICClientTypes.ImageFailureCode?
        /// The reason for the failure.
        public var failureReason: Swift.String?
        /// The image ID that's associated with the failure.
        public var imageId: ECRPUBLICClientTypes.ImageIdentifier?

        public init(
            failureCode: ECRPUBLICClientTypes.ImageFailureCode? = nil,
            failureReason: Swift.String? = nil,
            imageId: ECRPUBLICClientTypes.ImageIdentifier? = nil
        )
        {
            self.failureCode = failureCode
            self.failureReason = failureReason
            self.imageId = imageId
        }
    }
}

public struct BatchDeleteImageOutput: Swift.Sendable {
    /// Any failures associated with the call.
    public var failures: [ECRPUBLICClientTypes.ImageFailure]?
    /// The image IDs of the deleted images.
    public var imageIds: [ECRPUBLICClientTypes.ImageIdentifier]?

    public init(
        failures: [ECRPUBLICClientTypes.ImageFailure]? = nil,
        imageIds: [ECRPUBLICClientTypes.ImageIdentifier]? = nil
    )
    {
        self.failures = failures
        self.imageIds = imageIds
    }
}

/// The specified layer upload doesn't contain any layer parts.
public struct EmptyUploadException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "EmptyUploadException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The layer digest calculation performed by Amazon ECR when the image layer doesn't match the digest specified.
public struct InvalidLayerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidLayerException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The image layer already exists in the associated repository.
public struct LayerAlreadyExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LayerAlreadyExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Layer parts must be at least 5 MiB in size.
public struct LayerPartTooSmallException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LayerPartTooSmallException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The upload can't be found, or the specified upload ID isn't valid for this repository.
public struct UploadNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UploadNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct CompleteLayerUploadInput: Swift.Sendable {
    /// The sha256 digest of the image layer.
    /// This member is required.
    public var layerDigests: [Swift.String]?
    /// The Amazon Web Services account ID, or registry alias, associated with the registry where layers are uploaded. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository in a public registry to associate with the image layer.
    /// This member is required.
    public var repositoryName: Swift.String?
    /// The upload ID from a previous [InitiateLayerUpload] operation to associate with the image layer.
    /// This member is required.
    public var uploadId: Swift.String?

    public init(
        layerDigests: [Swift.String]? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil,
        uploadId: Swift.String? = nil
    )
    {
        self.layerDigests = layerDigests
        self.registryId = registryId
        self.repositoryName = repositoryName
        self.uploadId = uploadId
    }
}

public struct CompleteLayerUploadOutput: Swift.Sendable {
    /// The sha256 digest of the image layer.
    public var layerDigest: Swift.String?
    /// The public registry ID that's associated with the request.
    public var registryId: Swift.String?
    /// The repository name that's associated with the request.
    public var repositoryName: Swift.String?
    /// The upload ID that's associated with the layer.
    public var uploadId: Swift.String?

    public init(
        layerDigest: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil,
        uploadId: Swift.String? = nil
    )
    {
        self.layerDigest = layerDigest
        self.registryId = registryId
        self.repositoryName = repositoryName
        self.uploadId = uploadId
    }
}

/// An invalid parameter has been specified. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
public struct InvalidTagParameterException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidTagParameterException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The operation didn't succeed because it would have exceeded a service limit for your account. For more information, see [Amazon ECR Service Quotas](https://docs.aws.amazon.com/AmazonECR/latest/userguide/service-quotas.html) in the Amazon Elastic Container Registry User Guide.
public struct LimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified repository already exists in the specified registry.
public struct RepositoryAlreadyExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RepositoryAlreadyExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The list of tags on the repository is over the limit. The maximum number of tags that can be applied to a repository is 50.
public struct TooManyTagsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TooManyTagsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension ECRPUBLICClientTypes {

    /// An object that contains the catalog data for a repository. This data is publicly visible in the Amazon ECR Public Gallery.
    public struct RepositoryCatalogDataInput: Swift.Sendable {
        /// A detailed description of the contents of the repository. It's publicly visible in the Amazon ECR Public Gallery. The text must be in markdown format.
        public var aboutText: Swift.String?
        /// The system architecture that the images in the repository are compatible with. On the Amazon ECR Public Gallery, the following supported architectures appear as badges on the repository and are used as search filters. If an unsupported tag is added to your repository catalog data, it's associated with the repository and can be retrieved using the API but isn't discoverable in the Amazon ECR Public Gallery.
        ///
        /// * ARM
        ///
        /// * ARM 64
        ///
        /// * x86
        ///
        /// * x86-64
        public var architectures: [Swift.String]?
        /// A short description of the contents of the repository. This text appears in both the image details and also when searching for repositories on the Amazon ECR Public Gallery.
        public var description: Swift.String?
        /// The base64-encoded repository logo payload. The repository logo is only publicly visible in the Amazon ECR Public Gallery for verified accounts.
        public var logoImageBlob: Foundation.Data?
        /// The operating systems that the images in the repository are compatible with. On the Amazon ECR Public Gallery, the following supported operating systems appear as badges on the repository and are used as search filters. If an unsupported tag is added to your repository catalog data, it's associated with the repository and can be retrieved using the API but isn't discoverable in the Amazon ECR Public Gallery.
        ///
        /// * Linux
        ///
        /// * Windows
        public var operatingSystems: [Swift.String]?
        /// Detailed information about how to use the contents of the repository. It's publicly visible in the Amazon ECR Public Gallery. The usage text provides context, support information, and additional usage details for users of the repository. The text must be in markdown format.
        public var usageText: Swift.String?

        public init(
            aboutText: Swift.String? = nil,
            architectures: [Swift.String]? = nil,
            description: Swift.String? = nil,
            logoImageBlob: Foundation.Data? = nil,
            operatingSystems: [Swift.String]? = nil,
            usageText: Swift.String? = nil
        )
        {
            self.aboutText = aboutText
            self.architectures = architectures
            self.description = description
            self.logoImageBlob = logoImageBlob
            self.operatingSystems = operatingSystems
            self.usageText = usageText
        }
    }
}

extension ECRPUBLICClientTypes {

    /// The metadata that you apply to a resource to help you categorize and organize them. Each tag consists of a key and an optional value. You define both. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
    public struct Tag: Swift.Sendable {
        /// One part of a key-value pair that make up a tag. A key is a general label that acts like a category for more specific tag values.
        public var key: Swift.String?
        /// The optional part of a key-value pair that make up a tag. A value acts as a descriptor within a tag category (key).
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

public struct CreateRepositoryInput: Swift.Sendable {
    /// The details about the repository that are publicly visible in the Amazon ECR Public Gallery.
    public var catalogData: ECRPUBLICClientTypes.RepositoryCatalogDataInput?
    /// The name to use for the repository. This appears publicly in the Amazon ECR Public Gallery. The repository name can be specified on its own (for example nginx-web-app) or prepended with a namespace to group the repository into a category (for example project-a/nginx-web-app).
    /// This member is required.
    public var repositoryName: Swift.String?
    /// The metadata that you apply to each repository to help categorize and organize your repositories. Each tag consists of a key and an optional value. You define both of them. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
    public var tags: [ECRPUBLICClientTypes.Tag]?

    public init(
        catalogData: ECRPUBLICClientTypes.RepositoryCatalogDataInput? = nil,
        repositoryName: Swift.String? = nil,
        tags: [ECRPUBLICClientTypes.Tag]? = nil
    )
    {
        self.catalogData = catalogData
        self.repositoryName = repositoryName
        self.tags = tags
    }
}

extension ECRPUBLICClientTypes {

    /// The catalog data for a repository. This data is publicly visible in the Amazon ECR Public Gallery.
    public struct RepositoryCatalogData: Swift.Sendable {
        /// The longform description of the contents of the repository. This text appears in the repository details on the Amazon ECR Public Gallery.
        public var aboutText: Swift.String?
        /// The architecture tags that are associated with the repository. Only supported operating system tags appear publicly in the Amazon ECR Public Gallery. For more information, see [RepositoryCatalogDataInput].
        public var architectures: [Swift.String]?
        /// The short description of the repository.
        public var description: Swift.String?
        /// The URL that contains the logo that's associated with the repository.
        public var logoUrl: Swift.String?
        /// Indicates whether the repository is certified by Amazon Web Services Marketplace.
        public var marketplaceCertified: Swift.Bool?
        /// The operating system tags that are associated with the repository. Only supported operating system tags appear publicly in the Amazon ECR Public Gallery. For more information, see [RepositoryCatalogDataInput].
        public var operatingSystems: [Swift.String]?
        /// The longform usage details of the contents of the repository. The usage text provides context for users of the repository.
        public var usageText: Swift.String?

        public init(
            aboutText: Swift.String? = nil,
            architectures: [Swift.String]? = nil,
            description: Swift.String? = nil,
            logoUrl: Swift.String? = nil,
            marketplaceCertified: Swift.Bool? = nil,
            operatingSystems: [Swift.String]? = nil,
            usageText: Swift.String? = nil
        )
        {
            self.aboutText = aboutText
            self.architectures = architectures
            self.description = description
            self.logoUrl = logoUrl
            self.marketplaceCertified = marketplaceCertified
            self.operatingSystems = operatingSystems
            self.usageText = usageText
        }
    }
}

extension ECRPUBLICClientTypes {

    /// An object representing a repository.
    public struct Repository: Swift.Sendable {
        /// The date and time, in JavaScript date format, when the repository was created.
        public var createdAt: Foundation.Date?
        /// The Amazon Web Services account ID that's associated with the public registry that contains the repository.
        public var registryId: Swift.String?
        /// The Amazon Resource Name (ARN) that identifies the repository. The ARN contains the arn:aws:ecr namespace, followed by the region of the repository, Amazon Web Services account ID of the repository owner, repository namespace, and repository name. For example, arn:aws:ecr:region:************:repository/test.
        public var repositoryArn: Swift.String?
        /// The name of the repository.
        public var repositoryName: Swift.String?
        /// The URI for the repository. You can use this URI for container image push and pull operations.
        public var repositoryUri: Swift.String?

        public init(
            createdAt: Foundation.Date? = nil,
            registryId: Swift.String? = nil,
            repositoryArn: Swift.String? = nil,
            repositoryName: Swift.String? = nil,
            repositoryUri: Swift.String? = nil
        )
        {
            self.createdAt = createdAt
            self.registryId = registryId
            self.repositoryArn = repositoryArn
            self.repositoryName = repositoryName
            self.repositoryUri = repositoryUri
        }
    }
}

public struct CreateRepositoryOutput: Swift.Sendable {
    /// The catalog data for a repository. This data is publicly visible in the Amazon ECR Public Gallery.
    public var catalogData: ECRPUBLICClientTypes.RepositoryCatalogData?
    /// The repository that was created.
    public var repository: ECRPUBLICClientTypes.Repository?

    public init(
        catalogData: ECRPUBLICClientTypes.RepositoryCatalogData? = nil,
        repository: ECRPUBLICClientTypes.Repository? = nil
    )
    {
        self.catalogData = catalogData
        self.repository = repository
    }
}

/// The specified repository contains images. To delete a repository that contains images, you must force the deletion with the force parameter.
public struct RepositoryNotEmptyException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RepositoryNotEmptyException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DeleteRepositoryInput: Swift.Sendable {
    /// The force option can be used to delete a repository that contains images. If the force option is not used, the repository must be empty prior to deletion.
    public var force: Swift.Bool?
    /// The Amazon Web Services account ID that's associated with the public registry that contains the repository to delete. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository to delete.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        force: Swift.Bool? = false,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.force = force
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct DeleteRepositoryOutput: Swift.Sendable {
    /// The repository that was deleted.
    public var repository: ECRPUBLICClientTypes.Repository?

    public init(
        repository: ECRPUBLICClientTypes.Repository? = nil
    )
    {
        self.repository = repository
    }
}

/// The specified repository and registry combination doesn't have an associated repository policy.
public struct RepositoryPolicyNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RepositoryPolicyNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DeleteRepositoryPolicyInput: Swift.Sendable {
    /// The Amazon Web Services account ID that's associated with the public registry that contains the repository policy to delete. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository that's associated with the repository policy to delete.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct DeleteRepositoryPolicyOutput: Swift.Sendable {
    /// The JSON repository policy that was deleted from the repository.
    public var policyText: Swift.String?
    /// The registry ID that's associated with the request.
    public var registryId: Swift.String?
    /// The repository name that's associated with the request.
    public var repositoryName: Swift.String?

    public init(
        policyText: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.policyText = policyText
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

/// The image requested doesn't exist in the specified repository.
public struct ImageNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ImageNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DescribeImagesInput: Swift.Sendable {
    /// The list of image IDs for the requested repository.
    public var imageIds: [ECRPUBLICClientTypes.ImageIdentifier]?
    /// The maximum number of repository results that's returned by DescribeImages in paginated output. When this parameter is used, DescribeImages only returns maxResults results in a single page along with a nextToken response element. You can see the remaining results of the initial request by sending another DescribeImages request with the returned nextToken value. This value can be between 1 and 1000. If this parameter isn't used, then DescribeImages returns up to 100 results and a nextToken value, if applicable. If you specify images with imageIds, you can't use this option.
    public var maxResults: Swift.Int?
    /// The nextToken value that's returned from a previous paginated DescribeImages request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. If there are no more results to return, this value is null. If you specify images with imageIds, you can't use this option.
    public var nextToken: Swift.String?
    /// The Amazon Web Services account ID that's associated with the public registry that contains the repository where images are described. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// The repository that contains the images to describe.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        imageIds: [ECRPUBLICClientTypes.ImageIdentifier]? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageIds = imageIds
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

extension ECRPUBLICClientTypes {

    /// An object that describes an image that's returned by a [DescribeImages] operation.
    public struct ImageDetail: Swift.Sendable {
        /// The artifact media type of the image.
        public var artifactMediaType: Swift.String?
        /// The sha256 digest of the image manifest.
        public var imageDigest: Swift.String?
        /// The media type of the image manifest.
        public var imageManifestMediaType: Swift.String?
        /// The date and time, expressed in standard JavaScript date format, that the current image was pushed to the repository at.
        public var imagePushedAt: Foundation.Date?
        /// The size, in bytes, of the image in the repository. If the image is a manifest list, this is the max size of all manifests in the list. Beginning with Docker version 1.9, the Docker client compresses image layers before pushing them to a V2 Docker registry. The output of the docker images command shows the uncompressed image size, so it might return a larger image size than the image sizes that are returned by [DescribeImages].
        public var imageSizeInBytes: Swift.Int?
        /// The list of tags that's associated with this image.
        public var imageTags: [Swift.String]?
        /// The Amazon Web Services account ID that's associated with the public registry where this image belongs.
        public var registryId: Swift.String?
        /// The name of the repository where this image belongs.
        public var repositoryName: Swift.String?

        public init(
            artifactMediaType: Swift.String? = nil,
            imageDigest: Swift.String? = nil,
            imageManifestMediaType: Swift.String? = nil,
            imagePushedAt: Foundation.Date? = nil,
            imageSizeInBytes: Swift.Int? = nil,
            imageTags: [Swift.String]? = nil,
            registryId: Swift.String? = nil,
            repositoryName: Swift.String? = nil
        )
        {
            self.artifactMediaType = artifactMediaType
            self.imageDigest = imageDigest
            self.imageManifestMediaType = imageManifestMediaType
            self.imagePushedAt = imagePushedAt
            self.imageSizeInBytes = imageSizeInBytes
            self.imageTags = imageTags
            self.registryId = registryId
            self.repositoryName = repositoryName
        }
    }
}

public struct DescribeImagesOutput: Swift.Sendable {
    /// A list of [ImageDetail] objects that contain data about the image.
    public var imageDetails: [ECRPUBLICClientTypes.ImageDetail]?
    /// The nextToken value to include in a future DescribeImages request. When the results of a DescribeImages request exceed maxResults, you can use this value to retrieve the next page of results. If there are no more results to return, this value is null.
    public var nextToken: Swift.String?

    public init(
        imageDetails: [ECRPUBLICClientTypes.ImageDetail]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.imageDetails = imageDetails
        self.nextToken = nextToken
    }
}

public struct DescribeImageTagsInput: Swift.Sendable {
    /// The maximum number of repository results that's returned by DescribeImageTags in paginated output. When this parameter is used, DescribeImageTags only returns maxResults results in a single page along with a nextToken response element. You can see the remaining results of the initial request by sending another DescribeImageTags request with the returned nextToken value. This value can be between 1 and 1000. If this parameter isn't used, then DescribeImageTags returns up to 100 results and a nextToken value, if applicable. If you specify images with imageIds, you can't use this option.
    public var maxResults: Swift.Int?
    /// The nextToken value that's returned from a previous paginated DescribeImageTags request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. If there are no more results to return, this value is null. If you specify images with imageIds, you can't use this option.
    public var nextToken: Swift.String?
    /// The Amazon Web Services account ID that's associated with the public registry that contains the repository where images are described. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository that contains the image tag details to describe.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

extension ECRPUBLICClientTypes {

    /// An object that describes the image tag details that are returned by a [DescribeImageTags] action.
    public struct ReferencedImageDetail: Swift.Sendable {
        /// The artifact media type of the image.
        public var artifactMediaType: Swift.String?
        /// The sha256 digest of the image manifest.
        public var imageDigest: Swift.String?
        /// The media type of the image manifest.
        public var imageManifestMediaType: Swift.String?
        /// The date and time, expressed in standard JavaScript date format, which the current image tag was pushed to the repository at.
        public var imagePushedAt: Foundation.Date?
        /// The size, in bytes, of the image in the repository. If the image is a manifest list, this is the max size of all manifests in the list. Beginning with Docker version 1.9, the Docker client compresses image layers before pushing them to a V2 Docker registry. The output of the docker images command shows the uncompressed image size, so it might return a larger image size than the image sizes that are returned by [DescribeImages].
        public var imageSizeInBytes: Swift.Int?

        public init(
            artifactMediaType: Swift.String? = nil,
            imageDigest: Swift.String? = nil,
            imageManifestMediaType: Swift.String? = nil,
            imagePushedAt: Foundation.Date? = nil,
            imageSizeInBytes: Swift.Int? = nil
        )
        {
            self.artifactMediaType = artifactMediaType
            self.imageDigest = imageDigest
            self.imageManifestMediaType = imageManifestMediaType
            self.imagePushedAt = imagePushedAt
            self.imageSizeInBytes = imageSizeInBytes
        }
    }
}

extension ECRPUBLICClientTypes {

    /// An object that represents the image tag details for an image.
    public struct ImageTagDetail: Swift.Sendable {
        /// The time stamp that indicates when the image tag was created.
        public var createdAt: Foundation.Date?
        /// An object that describes the details of an image.
        public var imageDetail: ECRPUBLICClientTypes.ReferencedImageDetail?
        /// The tag that's associated with the image.
        public var imageTag: Swift.String?

        public init(
            createdAt: Foundation.Date? = nil,
            imageDetail: ECRPUBLICClientTypes.ReferencedImageDetail? = nil,
            imageTag: Swift.String? = nil
        )
        {
            self.createdAt = createdAt
            self.imageDetail = imageDetail
            self.imageTag = imageTag
        }
    }
}

public struct DescribeImageTagsOutput: Swift.Sendable {
    /// The image tag details for the images in the requested repository.
    public var imageTagDetails: [ECRPUBLICClientTypes.ImageTagDetail]?
    /// The nextToken value to include in a future DescribeImageTags request. When the results of a DescribeImageTags request exceed maxResults, you can use this value to retrieve the next page of results. If there are no more results to return, this value is null.
    public var nextToken: Swift.String?

    public init(
        imageTagDetails: [ECRPUBLICClientTypes.ImageTagDetail]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.imageTagDetails = imageTagDetails
        self.nextToken = nextToken
    }
}

public struct DescribeRegistriesInput: Swift.Sendable {
    /// The maximum number of repository results that's returned by DescribeRegistries in paginated output. When this parameter is used, DescribeRegistries only returns maxResults results in a single page along with a nextToken response element. The remaining results of the initial request can be seen by sending another DescribeRegistries request with the returned nextToken value. This value can be between 1 and 1000. If this parameter isn't used, then DescribeRegistries returns up to 100 results and a nextToken value, if applicable.
    public var maxResults: Swift.Int?
    /// The nextToken value that's returned from a previous paginated DescribeRegistries request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. If there are no more results to return, this value is null. This token should be treated as an opaque identifier that is only used to retrieve the next items in a list and not for other programmatic purposes.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension ECRPUBLICClientTypes {

    public enum RegistryAliasStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case active
        case pending
        case rejected
        case sdkUnknown(Swift.String)

        public static var allCases: [RegistryAliasStatus] {
            return [
                .active,
                .pending,
                .rejected
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .active: return "ACTIVE"
            case .pending: return "PENDING"
            case .rejected: return "REJECTED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ECRPUBLICClientTypes {

    /// An object representing the aliases for a public registry. A public registry is given an alias when it's created. However, a custom alias can be set using the Amazon ECR console. For more information, see [Registries](https://docs.aws.amazon.com/AmazonECR/latest/userguide/Registries.html) in the Amazon Elastic Container Registry User Guide.
    public struct RegistryAlias: Swift.Sendable {
        /// Indicates whether the registry alias is the default alias for the registry. When the first public repository is created, your public registry is assigned a default registry alias.
        /// This member is required.
        public var defaultRegistryAlias: Swift.Bool
        /// The name of the registry alias.
        /// This member is required.
        public var name: Swift.String?
        /// Indicates whether the registry alias is the primary alias for the registry. If true, the alias is the primary registry alias and is displayed in both the repository URL and the image URI used in the docker pull commands on the Amazon ECR Public Gallery. A registry alias that isn't the primary registry alias can be used in the repository URI in a docker pull command.
        /// This member is required.
        public var primaryRegistryAlias: Swift.Bool
        /// The status of the registry alias.
        /// This member is required.
        public var status: ECRPUBLICClientTypes.RegistryAliasStatus?

        public init(
            defaultRegistryAlias: Swift.Bool = false,
            name: Swift.String? = nil,
            primaryRegistryAlias: Swift.Bool = false,
            status: ECRPUBLICClientTypes.RegistryAliasStatus? = nil
        )
        {
            self.defaultRegistryAlias = defaultRegistryAlias
            self.name = name
            self.primaryRegistryAlias = primaryRegistryAlias
            self.status = status
        }
    }
}

extension ECRPUBLICClientTypes {

    /// The details of a public registry.
    public struct Registry: Swift.Sendable {
        /// An array of objects that represents the aliases for a public registry.
        /// This member is required.
        public var aliases: [ECRPUBLICClientTypes.RegistryAlias]?
        /// The Amazon Resource Name (ARN) of the public registry.
        /// This member is required.
        public var registryArn: Swift.String?
        /// The Amazon Web Services account ID that's associated with the registry. If you do not specify a registry, the default public registry is assumed.
        /// This member is required.
        public var registryId: Swift.String?
        /// The URI of a public registry. The URI contains a universal prefix and the registry alias.
        /// This member is required.
        public var registryUri: Swift.String?
        /// Indicates whether the account is a verified Amazon Web Services Marketplace vendor. If an account is verified, each public repository receives a verified account badge on the Amazon ECR Public Gallery.
        /// This member is required.
        public var verified: Swift.Bool?

        public init(
            aliases: [ECRPUBLICClientTypes.RegistryAlias]? = nil,
            registryArn: Swift.String? = nil,
            registryId: Swift.String? = nil,
            registryUri: Swift.String? = nil,
            verified: Swift.Bool? = nil
        )
        {
            self.aliases = aliases
            self.registryArn = registryArn
            self.registryId = registryId
            self.registryUri = registryUri
            self.verified = verified
        }
    }
}

public struct DescribeRegistriesOutput: Swift.Sendable {
    /// The nextToken value to include in a future DescribeRepositories request. If the results of a DescribeRepositories request exceed maxResults, you can use this value to retrieve the next page of results. If there are no more results, this value is null.
    public var nextToken: Swift.String?
    /// An object that contains the details for a public registry.
    /// This member is required.
    public var registries: [ECRPUBLICClientTypes.Registry]?

    public init(
        nextToken: Swift.String? = nil,
        registries: [ECRPUBLICClientTypes.Registry]? = nil
    )
    {
        self.nextToken = nextToken
        self.registries = registries
    }
}

public struct DescribeRepositoriesInput: Swift.Sendable {
    /// The maximum number of repository results that's returned by DescribeRepositories in paginated output. When this parameter is used, DescribeRepositories only returns maxResults results in a single page along with a nextToken response element. You can see the remaining results of the initial request by sending another DescribeRepositories request with the returned nextToken value. This value can be between 1 and 1000. If this parameter isn't used, then DescribeRepositories returns up to 100 results and a nextToken value, if applicable. If you specify repositories with repositoryNames, you can't use this option.
    public var maxResults: Swift.Int?
    /// The nextToken value that's returned from a previous paginated DescribeRepositories request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. If there are no more results to return, this value is null. If you specify repositories with repositoryNames, you can't use this option. This token should be treated as an opaque identifier that is only used to retrieve the next items in a list and not for other programmatic purposes.
    public var nextToken: Swift.String?
    /// The Amazon Web Services account ID that's associated with the registry that contains the repositories to be described. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// A list of repositories to describe. If this parameter is omitted, then all repositories in a registry are described.
    public var repositoryNames: [Swift.String]?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryNames: [Swift.String]? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.registryId = registryId
        self.repositoryNames = repositoryNames
    }
}

public struct DescribeRepositoriesOutput: Swift.Sendable {
    /// The nextToken value to include in a future DescribeRepositories request. When the results of a DescribeRepositories request exceed maxResults, this value can be used to retrieve the next page of results. If there are no more results to return, this value is null.
    public var nextToken: Swift.String?
    /// A list of repository objects corresponding to valid repositories.
    public var repositories: [ECRPUBLICClientTypes.Repository]?

    public init(
        nextToken: Swift.String? = nil,
        repositories: [ECRPUBLICClientTypes.Repository]? = nil
    )
    {
        self.nextToken = nextToken
        self.repositories = repositories
    }
}

public struct GetAuthorizationTokenInput: Swift.Sendable {

    public init() { }
}

public struct GetAuthorizationTokenOutput: Swift.Sendable {
    /// An authorization token data object that corresponds to a public registry.
    public var authorizationData: ECRPUBLICClientTypes.AuthorizationData?

    public init(
        authorizationData: ECRPUBLICClientTypes.AuthorizationData? = nil
    )
    {
        self.authorizationData = authorizationData
    }
}

public struct GetRegistryCatalogDataInput: Swift.Sendable {

    public init() { }
}

extension ECRPUBLICClientTypes {

    /// The metadata for a public registry.
    public struct RegistryCatalogData: Swift.Sendable {
        /// The display name for a public registry. This appears on the Amazon ECR Public Gallery. Only accounts that have the verified account badge can have a registry display name.
        public var displayName: Swift.String?

        public init(
            displayName: Swift.String? = nil
        )
        {
            self.displayName = displayName
        }
    }
}

public struct GetRegistryCatalogDataOutput: Swift.Sendable {
    /// The catalog metadata for the public registry.
    /// This member is required.
    public var registryCatalogData: ECRPUBLICClientTypes.RegistryCatalogData?

    public init(
        registryCatalogData: ECRPUBLICClientTypes.RegistryCatalogData? = nil
    )
    {
        self.registryCatalogData = registryCatalogData
    }
}

/// The repository catalog data doesn't exist.
public struct RepositoryCatalogDataNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RepositoryCatalogDataNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct GetRepositoryCatalogDataInput: Swift.Sendable {
    /// The Amazon Web Services account ID that's associated with the registry that contains the repositories to be described. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository to retrieve the catalog metadata for.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct GetRepositoryCatalogDataOutput: Swift.Sendable {
    /// The catalog metadata for the repository.
    public var catalogData: ECRPUBLICClientTypes.RepositoryCatalogData?

    public init(
        catalogData: ECRPUBLICClientTypes.RepositoryCatalogData? = nil
    )
    {
        self.catalogData = catalogData
    }
}

public struct GetRepositoryPolicyInput: Swift.Sendable {
    /// The Amazon Web Services account ID that's associated with the public registry that contains the repository. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository with the policy to retrieve.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct GetRepositoryPolicyOutput: Swift.Sendable {
    /// The repository policy text that's associated with the repository. The policy text will be in JSON format.
    public var policyText: Swift.String?
    /// The registry ID that's associated with the request.
    public var registryId: Swift.String?
    /// The repository name that's associated with the request.
    public var repositoryName: Swift.String?

    public init(
        policyText: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.policyText = policyText
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

extension ECRPUBLICClientTypes {

    /// An object that represents an Amazon ECR image.
    public struct Image: Swift.Sendable {
        /// An object that contains the image tag and image digest associated with an image.
        public var imageId: ECRPUBLICClientTypes.ImageIdentifier?
        /// The image manifest that's associated with the image.
        public var imageManifest: Swift.String?
        /// The manifest media type of the image.
        public var imageManifestMediaType: Swift.String?
        /// The Amazon Web Services account ID that's associated with the registry containing the image.
        public var registryId: Swift.String?
        /// The name of the repository that's associated with the image.
        public var repositoryName: Swift.String?

        public init(
            imageId: ECRPUBLICClientTypes.ImageIdentifier? = nil,
            imageManifest: Swift.String? = nil,
            imageManifestMediaType: Swift.String? = nil,
            registryId: Swift.String? = nil,
            repositoryName: Swift.String? = nil
        )
        {
            self.imageId = imageId
            self.imageManifest = imageManifest
            self.imageManifestMediaType = imageManifestMediaType
            self.registryId = registryId
            self.repositoryName = repositoryName
        }
    }
}

/// The specified image has already been pushed, and there were no changes to the manifest or image tag after the last push.
public struct ImageAlreadyExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ImageAlreadyExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified image digest doesn't match the digest that Amazon ECR calculated for the image.
public struct ImageDigestDoesNotMatchException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ImageDigestDoesNotMatchException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified image is tagged with a tag that already exists. The repository is configured for tag immutability.
public struct ImageTagAlreadyExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ImageTagAlreadyExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct InitiateLayerUploadInput: Swift.Sendable {
    /// The Amazon Web Services account ID, or registry alias, that's associated with the registry to which you intend to upload layers. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository that you want to upload layers to.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct InitiateLayerUploadOutput: Swift.Sendable {
    /// The size, in bytes, that Amazon ECR expects future layer part uploads to be.
    public var partSize: Swift.Int?
    /// The upload ID for the layer upload. This parameter is passed to further [UploadLayerPart] and [CompleteLayerUpload] operations.
    public var uploadId: Swift.String?

    public init(
        partSize: Swift.Int? = nil,
        uploadId: Swift.String? = nil
    )
    {
        self.partSize = partSize
        self.uploadId = uploadId
    }
}

/// The layer part size isn't valid, or the first byte specified isn't consecutive to the last byte of a previous layer part upload.
public struct InvalidLayerPartException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The position of the last byte of the layer part.
        public internal(set) var lastValidByteReceived: Swift.Int? = nil
        public internal(set) var message: Swift.String? = nil
        /// The Amazon Web Services account ID that's associated with the layer part.
        public internal(set) var registryId: Swift.String? = nil
        /// The name of the repository.
        public internal(set) var repositoryName: Swift.String? = nil
        /// The upload ID that's associated with the layer part.
        public internal(set) var uploadId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidLayerPartException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        lastValidByteReceived: Swift.Int? = nil,
        message: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil,
        uploadId: Swift.String? = nil
    )
    {
        self.properties.lastValidByteReceived = lastValidByteReceived
        self.properties.message = message
        self.properties.registryId = registryId
        self.properties.repositoryName = repositoryName
        self.properties.uploadId = uploadId
    }
}

/// The specified layers can't be found, or the specified layer isn't valid for this repository.
public struct LayersNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LayersNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) that identifies the resource to list the tags for. Currently, the supported resource is an Amazon ECR Public repository.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// The tags for the resource.
    public var tags: [ECRPUBLICClientTypes.Tag]?

    public init(
        tags: [ECRPUBLICClientTypes.Tag]? = nil
    )
    {
        self.tags = tags
    }
}

/// The manifest list is referencing an image that doesn't exist.
public struct ReferencedImagesNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ReferencedImagesNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct PutImageInput: Swift.Sendable {
    /// The image digest of the image manifest that corresponds to the image.
    public var imageDigest: Swift.String?
    /// The image manifest that corresponds to the image to be uploaded.
    /// This member is required.
    public var imageManifest: Swift.String?
    /// The media type of the image manifest. If you push an image manifest that doesn't contain the mediaType field, you must specify the imageManifestMediaType in the request.
    public var imageManifestMediaType: Swift.String?
    /// The tag to associate with the image. This parameter is required for images that use the Docker Image Manifest V2 Schema 2 or Open Container Initiative (OCI) formats.
    public var imageTag: Swift.String?
    /// The Amazon Web Services account ID, or registry alias, that's associated with the public registry that contains the repository where the image is put. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository where the image is put.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        imageDigest: Swift.String? = nil,
        imageManifest: Swift.String? = nil,
        imageManifestMediaType: Swift.String? = nil,
        imageTag: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.imageDigest = imageDigest
        self.imageManifest = imageManifest
        self.imageManifestMediaType = imageManifestMediaType
        self.imageTag = imageTag
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct PutImageOutput: Swift.Sendable {
    /// Details of the image uploaded.
    public var image: ECRPUBLICClientTypes.Image?

    public init(
        image: ECRPUBLICClientTypes.Image? = nil
    )
    {
        self.image = image
    }
}

public struct PutRegistryCatalogDataInput: Swift.Sendable {
    /// The display name for a public registry. The display name is shown as the repository author in the Amazon ECR Public Gallery. The registry display name is only publicly visible in the Amazon ECR Public Gallery for verified accounts.
    public var displayName: Swift.String?

    public init(
        displayName: Swift.String? = nil
    )
    {
        self.displayName = displayName
    }
}

public struct PutRegistryCatalogDataOutput: Swift.Sendable {
    /// The catalog data for the public registry.
    /// This member is required.
    public var registryCatalogData: ECRPUBLICClientTypes.RegistryCatalogData?

    public init(
        registryCatalogData: ECRPUBLICClientTypes.RegistryCatalogData? = nil
    )
    {
        self.registryCatalogData = registryCatalogData
    }
}

public struct PutRepositoryCatalogDataInput: Swift.Sendable {
    /// An object containing the catalog data for a repository. This data is publicly visible in the Amazon ECR Public Gallery.
    /// This member is required.
    public var catalogData: ECRPUBLICClientTypes.RepositoryCatalogDataInput?
    /// The Amazon Web Services account ID that's associated with the public registry the repository is in. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository to create or update the catalog data for.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        catalogData: ECRPUBLICClientTypes.RepositoryCatalogDataInput? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.catalogData = catalogData
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct PutRepositoryCatalogDataOutput: Swift.Sendable {
    /// The catalog data for the repository.
    public var catalogData: ECRPUBLICClientTypes.RepositoryCatalogData?

    public init(
        catalogData: ECRPUBLICClientTypes.RepositoryCatalogData? = nil
    )
    {
        self.catalogData = catalogData
    }
}

public struct SetRepositoryPolicyInput: Swift.Sendable {
    /// If the policy that you want to set on a repository policy would prevent you from setting another policy in the future, you must force the [SetRepositoryPolicy] operation. This prevents accidental repository lockouts.
    public var force: Swift.Bool?
    /// The JSON repository policy text to apply to the repository. For more information, see [Amazon ECR Repository Policies](https://docs.aws.amazon.com/AmazonECR/latest/userguide/repository-policy-examples.html) in the Amazon Elastic Container Registry User Guide.
    /// This member is required.
    public var policyText: Swift.String?
    /// The Amazon Web Services account ID that's associated with the registry that contains the repository. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository to receive the policy.
    /// This member is required.
    public var repositoryName: Swift.String?

    public init(
        force: Swift.Bool? = false,
        policyText: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.force = force
        self.policyText = policyText
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct SetRepositoryPolicyOutput: Swift.Sendable {
    /// The JSON repository policy text that's applied to the repository.
    public var policyText: Swift.String?
    /// The registry ID that's associated with the request.
    public var registryId: Swift.String?
    /// The repository name that's associated with the request.
    public var repositoryName: Swift.String?

    public init(
        policyText: Swift.String? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil
    )
    {
        self.policyText = policyText
        self.registryId = registryId
        self.repositoryName = repositoryName
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource to add tags to. Currently, the supported resource is an Amazon ECR Public repository.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tags to add to the resource. A tag is an array of key-value pairs. Tag keys can have a maximum character length of 128 characters, and tag values can have a maximum length of 256 characters.
    /// This member is required.
    public var tags: [ECRPUBLICClientTypes.Tag]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [ECRPUBLICClientTypes.Tag]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource to delete tags from. Currently, the supported resource is an Amazon ECR Public repository.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The keys of the tags to be removed.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UploadLayerPartInput: Swift.Sendable {
    /// The base64-encoded layer part payload.
    /// This member is required.
    public var layerPartBlob: Foundation.Data?
    /// The position of the first byte of the layer part witin the overall image layer.
    /// This member is required.
    public var partFirstByte: Swift.Int?
    /// The position of the last byte of the layer part within the overall image layer.
    /// This member is required.
    public var partLastByte: Swift.Int?
    /// The Amazon Web Services account ID, or registry alias, that's associated with the registry that you're uploading layer parts to. If you do not specify a registry, the default public registry is assumed.
    public var registryId: Swift.String?
    /// The name of the repository that you're uploading layer parts to.
    /// This member is required.
    public var repositoryName: Swift.String?
    /// The upload ID from a previous [InitiateLayerUpload] operation to associate with the layer part upload.
    /// This member is required.
    public var uploadId: Swift.String?

    public init(
        layerPartBlob: Foundation.Data? = nil,
        partFirstByte: Swift.Int? = nil,
        partLastByte: Swift.Int? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil,
        uploadId: Swift.String? = nil
    )
    {
        self.layerPartBlob = layerPartBlob
        self.partFirstByte = partFirstByte
        self.partLastByte = partLastByte
        self.registryId = registryId
        self.repositoryName = repositoryName
        self.uploadId = uploadId
    }
}

public struct UploadLayerPartOutput: Swift.Sendable {
    /// The integer value of the last byte that's received in the request.
    public var lastByteReceived: Swift.Int?
    /// The registry ID that's associated with the request.
    public var registryId: Swift.String?
    /// The repository name that's associated with the request.
    public var repositoryName: Swift.String?
    /// The upload ID that's associated with the request.
    public var uploadId: Swift.String?

    public init(
        lastByteReceived: Swift.Int? = nil,
        registryId: Swift.String? = nil,
        repositoryName: Swift.String? = nil,
        uploadId: Swift.String? = nil
    )
    {
        self.lastByteReceived = lastByteReceived
        self.registryId = registryId
        self.repositoryName = repositoryName
        self.uploadId = uploadId
    }
}

extension BatchCheckLayerAvailabilityInput {

    static func urlPathProvider(_ value: BatchCheckLayerAvailabilityInput) -> Swift.String? {
        return "/"
    }
}

extension BatchDeleteImageInput {

    static func urlPathProvider(_ value: BatchDeleteImageInput) -> Swift.String? {
        return "/"
    }
}

extension CompleteLayerUploadInput {

    static func urlPathProvider(_ value: CompleteLayerUploadInput) -> Swift.String? {
        return "/"
    }
}

extension CreateRepositoryInput {

    static func urlPathProvider(_ value: CreateRepositoryInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteRepositoryInput {

    static func urlPathProvider(_ value: DeleteRepositoryInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteRepositoryPolicyInput {

    static func urlPathProvider(_ value: DeleteRepositoryPolicyInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeImagesInput {

    static func urlPathProvider(_ value: DescribeImagesInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeImageTagsInput {

    static func urlPathProvider(_ value: DescribeImageTagsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeRegistriesInput {

    static func urlPathProvider(_ value: DescribeRegistriesInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeRepositoriesInput {

    static func urlPathProvider(_ value: DescribeRepositoriesInput) -> Swift.String? {
        return "/"
    }
}

extension GetAuthorizationTokenInput {

    static func urlPathProvider(_ value: GetAuthorizationTokenInput) -> Swift.String? {
        return "/"
    }
}

extension GetRegistryCatalogDataInput {

    static func urlPathProvider(_ value: GetRegistryCatalogDataInput) -> Swift.String? {
        return "/"
    }
}

extension GetRepositoryCatalogDataInput {

    static func urlPathProvider(_ value: GetRepositoryCatalogDataInput) -> Swift.String? {
        return "/"
    }
}

extension GetRepositoryPolicyInput {

    static func urlPathProvider(_ value: GetRepositoryPolicyInput) -> Swift.String? {
        return "/"
    }
}

extension InitiateLayerUploadInput {

    static func urlPathProvider(_ value: InitiateLayerUploadInput) -> Swift.String? {
        return "/"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        return "/"
    }
}

extension PutImageInput {

    static func urlPathProvider(_ value: PutImageInput) -> Swift.String? {
        return "/"
    }
}

extension PutRegistryCatalogDataInput {

    static func urlPathProvider(_ value: PutRegistryCatalogDataInput) -> Swift.String? {
        return "/"
    }
}

extension PutRepositoryCatalogDataInput {

    static func urlPathProvider(_ value: PutRepositoryCatalogDataInput) -> Swift.String? {
        return "/"
    }
}

extension SetRepositoryPolicyInput {

    static func urlPathProvider(_ value: SetRepositoryPolicyInput) -> Swift.String? {
        return "/"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        return "/"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        return "/"
    }
}

extension UploadLayerPartInput {

    static func urlPathProvider(_ value: UploadLayerPartInput) -> Swift.String? {
        return "/"
    }
}

extension BatchCheckLayerAvailabilityInput {

    static func write(value: BatchCheckLayerAvailabilityInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["layerDigests"].writeList(value.layerDigests, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension BatchDeleteImageInput {

    static func write(value: BatchDeleteImageInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["imageIds"].writeList(value.imageIds, memberWritingClosure: ECRPUBLICClientTypes.ImageIdentifier.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension CompleteLayerUploadInput {

    static func write(value: CompleteLayerUploadInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["layerDigests"].writeList(value.layerDigests, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
        try writer["uploadId"].write(value.uploadId)
    }
}

extension CreateRepositoryInput {

    static func write(value: CreateRepositoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["catalogData"].write(value.catalogData, with: ECRPUBLICClientTypes.RepositoryCatalogDataInput.write(value:to:))
        try writer["repositoryName"].write(value.repositoryName)
        try writer["tags"].writeList(value.tags, memberWritingClosure: ECRPUBLICClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DeleteRepositoryInput {

    static func write(value: DeleteRepositoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["force"].write(value.force)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension DeleteRepositoryPolicyInput {

    static func write(value: DeleteRepositoryPolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension DescribeImagesInput {

    static func write(value: DescribeImagesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["imageIds"].writeList(value.imageIds, memberWritingClosure: ECRPUBLICClientTypes.ImageIdentifier.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension DescribeImageTagsInput {

    static func write(value: DescribeImageTagsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension DescribeRegistriesInput {

    static func write(value: DescribeRegistriesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension DescribeRepositoriesInput {

    static func write(value: DescribeRepositoriesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryNames"].writeList(value.repositoryNames, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension GetAuthorizationTokenInput {

    static func write(value: GetAuthorizationTokenInput?, to writer: SmithyJSON.Writer) throws {
        guard value != nil else { return }
        _ = writer[""]  // create an empty structure
    }
}

extension GetRegistryCatalogDataInput {

    static func write(value: GetRegistryCatalogDataInput?, to writer: SmithyJSON.Writer) throws {
        guard value != nil else { return }
        _ = writer[""]  // create an empty structure
    }
}

extension GetRepositoryCatalogDataInput {

    static func write(value: GetRepositoryCatalogDataInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension GetRepositoryPolicyInput {

    static func write(value: GetRepositoryPolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension InitiateLayerUploadInput {

    static func write(value: InitiateLayerUploadInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension ListTagsForResourceInput {

    static func write(value: ListTagsForResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["resourceArn"].write(value.resourceArn)
    }
}

extension PutImageInput {

    static func write(value: PutImageInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["imageDigest"].write(value.imageDigest)
        try writer["imageManifest"].write(value.imageManifest)
        try writer["imageManifestMediaType"].write(value.imageManifestMediaType)
        try writer["imageTag"].write(value.imageTag)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension PutRegistryCatalogDataInput {

    static func write(value: PutRegistryCatalogDataInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["displayName"].write(value.displayName)
    }
}

extension PutRepositoryCatalogDataInput {

    static func write(value: PutRepositoryCatalogDataInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["catalogData"].write(value.catalogData, with: ECRPUBLICClientTypes.RepositoryCatalogDataInput.write(value:to:))
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension SetRepositoryPolicyInput {

    static func write(value: SetRepositoryPolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["force"].write(value.force)
        try writer["policyText"].write(value.policyText)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["resourceArn"].write(value.resourceArn)
        try writer["tags"].writeList(value.tags, memberWritingClosure: ECRPUBLICClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UntagResourceInput {

    static func write(value: UntagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["resourceArn"].write(value.resourceArn)
        try writer["tagKeys"].writeList(value.tagKeys, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UploadLayerPartInput {

    static func write(value: UploadLayerPartInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["layerPartBlob"].write(value.layerPartBlob)
        try writer["partFirstByte"].write(value.partFirstByte)
        try writer["partLastByte"].write(value.partLastByte)
        try writer["registryId"].write(value.registryId)
        try writer["repositoryName"].write(value.repositoryName)
        try writer["uploadId"].write(value.uploadId)
    }
}

extension BatchCheckLayerAvailabilityOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> BatchCheckLayerAvailabilityOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = BatchCheckLayerAvailabilityOutput()
        value.failures = try reader["failures"].readListIfPresent(memberReadingClosure: ECRPUBLICClientTypes.LayerFailure.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.layers = try reader["layers"].readListIfPresent(memberReadingClosure: ECRPUBLICClientTypes.Layer.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension BatchDeleteImageOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> BatchDeleteImageOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = BatchDeleteImageOutput()
        value.failures = try reader["failures"].readListIfPresent(memberReadingClosure: ECRPUBLICClientTypes.ImageFailure.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.imageIds = try reader["imageIds"].readListIfPresent(memberReadingClosure: ECRPUBLICClientTypes.ImageIdentifier.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension CompleteLayerUploadOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CompleteLayerUploadOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CompleteLayerUploadOutput()
        value.layerDigest = try reader["layerDigest"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.uploadId = try reader["uploadId"].readIfPresent()
        return value
    }
}

extension CreateRepositoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateRepositoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateRepositoryOutput()
        value.catalogData = try reader["catalogData"].readIfPresent(with: ECRPUBLICClientTypes.RepositoryCatalogData.read(from:))
        value.repository = try reader["repository"].readIfPresent(with: ECRPUBLICClientTypes.Repository.read(from:))
        return value
    }
}

extension DeleteRepositoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteRepositoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteRepositoryOutput()
        value.repository = try reader["repository"].readIfPresent(with: ECRPUBLICClientTypes.Repository.read(from:))
        return value
    }
}

extension DeleteRepositoryPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteRepositoryPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteRepositoryPolicyOutput()
        value.policyText = try reader["policyText"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension DescribeImagesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeImagesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeImagesOutput()
        value.imageDetails = try reader["imageDetails"].readListIfPresent(memberReadingClosure: ECRPUBLICClientTypes.ImageDetail.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension DescribeImageTagsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeImageTagsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeImageTagsOutput()
        value.imageTagDetails = try reader["imageTagDetails"].readListIfPresent(memberReadingClosure: ECRPUBLICClientTypes.ImageTagDetail.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension DescribeRegistriesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeRegistriesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeRegistriesOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.registries = try reader["registries"].readListIfPresent(memberReadingClosure: ECRPUBLICClientTypes.Registry.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DescribeRepositoriesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeRepositoriesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeRepositoriesOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.repositories = try reader["repositories"].readListIfPresent(memberReadingClosure: ECRPUBLICClientTypes.Repository.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension GetAuthorizationTokenOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetAuthorizationTokenOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetAuthorizationTokenOutput()
        value.authorizationData = try reader["authorizationData"].readIfPresent(with: ECRPUBLICClientTypes.AuthorizationData.read(from:))
        return value
    }
}

extension GetRegistryCatalogDataOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetRegistryCatalogDataOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetRegistryCatalogDataOutput()
        value.registryCatalogData = try reader["registryCatalogData"].readIfPresent(with: ECRPUBLICClientTypes.RegistryCatalogData.read(from:))
        return value
    }
}

extension GetRepositoryCatalogDataOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetRepositoryCatalogDataOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetRepositoryCatalogDataOutput()
        value.catalogData = try reader["catalogData"].readIfPresent(with: ECRPUBLICClientTypes.RepositoryCatalogData.read(from:))
        return value
    }
}

extension GetRepositoryPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetRepositoryPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetRepositoryPolicyOutput()
        value.policyText = try reader["policyText"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension InitiateLayerUploadOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> InitiateLayerUploadOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = InitiateLayerUploadOutput()
        value.partSize = try reader["partSize"].readIfPresent()
        value.uploadId = try reader["uploadId"].readIfPresent()
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["tags"].readListIfPresent(memberReadingClosure: ECRPUBLICClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension PutImageOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutImageOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutImageOutput()
        value.image = try reader["image"].readIfPresent(with: ECRPUBLICClientTypes.Image.read(from:))
        return value
    }
}

extension PutRegistryCatalogDataOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutRegistryCatalogDataOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutRegistryCatalogDataOutput()
        value.registryCatalogData = try reader["registryCatalogData"].readIfPresent(with: ECRPUBLICClientTypes.RegistryCatalogData.read(from:))
        return value
    }
}

extension PutRepositoryCatalogDataOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutRepositoryCatalogDataOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutRepositoryCatalogDataOutput()
        value.catalogData = try reader["catalogData"].readIfPresent(with: ECRPUBLICClientTypes.RepositoryCatalogData.read(from:))
        return value
    }
}

extension SetRepositoryPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SetRepositoryPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = SetRepositoryPolicyOutput()
        value.policyText = try reader["policyText"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UploadLayerPartOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UploadLayerPartOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UploadLayerPartOutput()
        value.lastByteReceived = try reader["lastByteReceived"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.uploadId = try reader["uploadId"].readIfPresent()
        return value
    }
}

enum BatchCheckLayerAvailabilityOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RegistryNotFoundException": return try RegistryNotFoundException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum BatchDeleteImageOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CompleteLayerUploadOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "EmptyUploadException": return try EmptyUploadException.makeError(baseError: baseError)
            case "InvalidLayerException": return try InvalidLayerException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LayerAlreadyExistsException": return try LayerAlreadyExistsException.makeError(baseError: baseError)
            case "LayerPartTooSmallException": return try LayerPartTooSmallException.makeError(baseError: baseError)
            case "RegistryNotFoundException": return try RegistryNotFoundException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            case "UploadNotFoundException": return try UploadNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateRepositoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "InvalidTagParameterException": return try InvalidTagParameterException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "RepositoryAlreadyExistsException": return try RepositoryAlreadyExistsException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "TooManyTagsException": return try TooManyTagsException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteRepositoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotEmptyException": return try RepositoryNotEmptyException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteRepositoryPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "RepositoryPolicyNotFoundException": return try RepositoryPolicyNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeImagesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ImageNotFoundException": return try ImageNotFoundException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeImageTagsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeRegistriesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeRepositoriesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetAuthorizationTokenOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetRegistryCatalogDataOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetRepositoryCatalogDataOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryCatalogDataNotFoundException": return try RepositoryCatalogDataNotFoundException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetRepositoryPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "RepositoryPolicyNotFoundException": return try RepositoryPolicyNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum InitiateLayerUploadOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RegistryNotFoundException": return try RegistryNotFoundException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutImageOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ImageAlreadyExistsException": return try ImageAlreadyExistsException.makeError(baseError: baseError)
            case "ImageDigestDoesNotMatchException": return try ImageDigestDoesNotMatchException.makeError(baseError: baseError)
            case "ImageTagAlreadyExistsException": return try ImageTagAlreadyExistsException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LayersNotFoundException": return try LayersNotFoundException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ReferencedImagesNotFoundException": return try ReferencedImagesNotFoundException.makeError(baseError: baseError)
            case "RegistryNotFoundException": return try RegistryNotFoundException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutRegistryCatalogDataOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutRepositoryCatalogDataOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SetRepositoryPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "InvalidTagParameterException": return try InvalidTagParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "TooManyTagsException": return try TooManyTagsException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "InvalidTagParameterException": return try InvalidTagParameterException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "TooManyTagsException": return try TooManyTagsException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UploadLayerPartOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidLayerPartException": return try InvalidLayerPartException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "RegistryNotFoundException": return try RegistryNotFoundException.makeError(baseError: baseError)
            case "RepositoryNotFoundException": return try RepositoryNotFoundException.makeError(baseError: baseError)
            case "ServerException": return try ServerException.makeError(baseError: baseError)
            case "UnsupportedCommandException": return try UnsupportedCommandException.makeError(baseError: baseError)
            case "UploadNotFoundException": return try UploadNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension RepositoryNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> RepositoryNotFoundException {
        let reader = baseError.errorBodyReader
        var value = RepositoryNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UnsupportedCommandException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UnsupportedCommandException {
        let reader = baseError.errorBodyReader
        var value = UnsupportedCommandException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServerException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ServerException {
        let reader = baseError.errorBodyReader
        var value = ServerException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidParameterException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidParameterException {
        let reader = baseError.errorBodyReader
        var value = InvalidParameterException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension RegistryNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> RegistryNotFoundException {
        let reader = baseError.errorBodyReader
        var value = RegistryNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LayerAlreadyExistsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LayerAlreadyExistsException {
        let reader = baseError.errorBodyReader
        var value = LayerAlreadyExistsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension EmptyUploadException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> EmptyUploadException {
        let reader = baseError.errorBodyReader
        var value = EmptyUploadException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidLayerException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidLayerException {
        let reader = baseError.errorBodyReader
        var value = InvalidLayerException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LayerPartTooSmallException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LayerPartTooSmallException {
        let reader = baseError.errorBodyReader
        var value = LayerPartTooSmallException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UploadNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UploadNotFoundException {
        let reader = baseError.errorBodyReader
        var value = UploadNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LimitExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LimitExceededException {
        let reader = baseError.errorBodyReader
        var value = LimitExceededException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidTagParameterException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidTagParameterException {
        let reader = baseError.errorBodyReader
        var value = InvalidTagParameterException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TooManyTagsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> TooManyTagsException {
        let reader = baseError.errorBodyReader
        var value = TooManyTagsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension RepositoryAlreadyExistsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> RepositoryAlreadyExistsException {
        let reader = baseError.errorBodyReader
        var value = RepositoryAlreadyExistsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension RepositoryNotEmptyException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> RepositoryNotEmptyException {
        let reader = baseError.errorBodyReader
        var value = RepositoryNotEmptyException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension RepositoryPolicyNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> RepositoryPolicyNotFoundException {
        let reader = baseError.errorBodyReader
        var value = RepositoryPolicyNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ImageNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ImageNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ImageNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension RepositoryCatalogDataNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> RepositoryCatalogDataNotFoundException {
        let reader = baseError.errorBodyReader
        var value = RepositoryCatalogDataNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ImageDigestDoesNotMatchException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ImageDigestDoesNotMatchException {
        let reader = baseError.errorBodyReader
        var value = ImageDigestDoesNotMatchException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ImageTagAlreadyExistsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ImageTagAlreadyExistsException {
        let reader = baseError.errorBodyReader
        var value = ImageTagAlreadyExistsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ReferencedImagesNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ReferencedImagesNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ReferencedImagesNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LayersNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LayersNotFoundException {
        let reader = baseError.errorBodyReader
        var value = LayersNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ImageAlreadyExistsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ImageAlreadyExistsException {
        let reader = baseError.errorBodyReader
        var value = ImageAlreadyExistsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidLayerPartException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidLayerPartException {
        let reader = baseError.errorBodyReader
        var value = InvalidLayerPartException()
        value.properties.lastValidByteReceived = try reader["lastValidByteReceived"].readIfPresent()
        value.properties.message = try reader["message"].readIfPresent()
        value.properties.registryId = try reader["registryId"].readIfPresent()
        value.properties.repositoryName = try reader["repositoryName"].readIfPresent()
        value.properties.uploadId = try reader["uploadId"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ECRPUBLICClientTypes.Layer {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.Layer {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.Layer()
        value.layerDigest = try reader["layerDigest"].readIfPresent()
        value.layerAvailability = try reader["layerAvailability"].readIfPresent()
        value.layerSize = try reader["layerSize"].readIfPresent()
        value.mediaType = try reader["mediaType"].readIfPresent()
        return value
    }
}

extension ECRPUBLICClientTypes.LayerFailure {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.LayerFailure {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.LayerFailure()
        value.layerDigest = try reader["layerDigest"].readIfPresent()
        value.failureCode = try reader["failureCode"].readIfPresent()
        value.failureReason = try reader["failureReason"].readIfPresent()
        return value
    }
}

extension ECRPUBLICClientTypes.ImageIdentifier {

    static func write(value: ECRPUBLICClientTypes.ImageIdentifier?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["imageDigest"].write(value.imageDigest)
        try writer["imageTag"].write(value.imageTag)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.ImageIdentifier {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.ImageIdentifier()
        value.imageDigest = try reader["imageDigest"].readIfPresent()
        value.imageTag = try reader["imageTag"].readIfPresent()
        return value
    }
}

extension ECRPUBLICClientTypes.ImageFailure {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.ImageFailure {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.ImageFailure()
        value.imageId = try reader["imageId"].readIfPresent(with: ECRPUBLICClientTypes.ImageIdentifier.read(from:))
        value.failureCode = try reader["failureCode"].readIfPresent()
        value.failureReason = try reader["failureReason"].readIfPresent()
        return value
    }
}

extension ECRPUBLICClientTypes.Repository {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.Repository {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.Repository()
        value.repositoryArn = try reader["repositoryArn"].readIfPresent()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.repositoryUri = try reader["repositoryUri"].readIfPresent()
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension ECRPUBLICClientTypes.RepositoryCatalogData {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.RepositoryCatalogData {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.RepositoryCatalogData()
        value.description = try reader["description"].readIfPresent()
        value.architectures = try reader["architectures"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.operatingSystems = try reader["operatingSystems"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.logoUrl = try reader["logoUrl"].readIfPresent()
        value.aboutText = try reader["aboutText"].readIfPresent()
        value.usageText = try reader["usageText"].readIfPresent()
        value.marketplaceCertified = try reader["marketplaceCertified"].readIfPresent()
        return value
    }
}

extension ECRPUBLICClientTypes.ImageDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.ImageDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.ImageDetail()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.imageDigest = try reader["imageDigest"].readIfPresent()
        value.imageTags = try reader["imageTags"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.imageSizeInBytes = try reader["imageSizeInBytes"].readIfPresent()
        value.imagePushedAt = try reader["imagePushedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.imageManifestMediaType = try reader["imageManifestMediaType"].readIfPresent()
        value.artifactMediaType = try reader["artifactMediaType"].readIfPresent()
        return value
    }
}

extension ECRPUBLICClientTypes.ImageTagDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.ImageTagDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.ImageTagDetail()
        value.imageTag = try reader["imageTag"].readIfPresent()
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.imageDetail = try reader["imageDetail"].readIfPresent(with: ECRPUBLICClientTypes.ReferencedImageDetail.read(from:))
        return value
    }
}

extension ECRPUBLICClientTypes.ReferencedImageDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.ReferencedImageDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.ReferencedImageDetail()
        value.imageDigest = try reader["imageDigest"].readIfPresent()
        value.imageSizeInBytes = try reader["imageSizeInBytes"].readIfPresent()
        value.imagePushedAt = try reader["imagePushedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.imageManifestMediaType = try reader["imageManifestMediaType"].readIfPresent()
        value.artifactMediaType = try reader["artifactMediaType"].readIfPresent()
        return value
    }
}

extension ECRPUBLICClientTypes.Registry {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.Registry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.Registry()
        value.registryId = try reader["registryId"].readIfPresent() ?? ""
        value.registryArn = try reader["registryArn"].readIfPresent() ?? ""
        value.registryUri = try reader["registryUri"].readIfPresent() ?? ""
        value.verified = try reader["verified"].readIfPresent() ?? false
        value.aliases = try reader["aliases"].readListIfPresent(memberReadingClosure: ECRPUBLICClientTypes.RegistryAlias.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ECRPUBLICClientTypes.RegistryAlias {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.RegistryAlias {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.RegistryAlias()
        value.name = try reader["name"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        value.primaryRegistryAlias = try reader["primaryRegistryAlias"].readIfPresent() ?? false
        value.defaultRegistryAlias = try reader["defaultRegistryAlias"].readIfPresent() ?? false
        return value
    }
}

extension ECRPUBLICClientTypes.AuthorizationData {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.AuthorizationData {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.AuthorizationData()
        value.authorizationToken = try reader["authorizationToken"].readIfPresent()
        value.expiresAt = try reader["expiresAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension ECRPUBLICClientTypes.RegistryCatalogData {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.RegistryCatalogData {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.RegistryCatalogData()
        value.displayName = try reader["displayName"].readIfPresent()
        return value
    }
}

extension ECRPUBLICClientTypes.Tag {

    static func write(value: ECRPUBLICClientTypes.Tag?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Key"].write(value.key)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.Tag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.Tag()
        value.key = try reader["Key"].readIfPresent()
        value.value = try reader["Value"].readIfPresent()
        return value
    }
}

extension ECRPUBLICClientTypes.Image {

    static func read(from reader: SmithyJSON.Reader) throws -> ECRPUBLICClientTypes.Image {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ECRPUBLICClientTypes.Image()
        value.registryId = try reader["registryId"].readIfPresent()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.imageId = try reader["imageId"].readIfPresent(with: ECRPUBLICClientTypes.ImageIdentifier.read(from:))
        value.imageManifest = try reader["imageManifest"].readIfPresent()
        value.imageManifestMediaType = try reader["imageManifestMediaType"].readIfPresent()
        return value
    }
}

extension ECRPUBLICClientTypes.RepositoryCatalogDataInput {

    static func write(value: ECRPUBLICClientTypes.RepositoryCatalogDataInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["aboutText"].write(value.aboutText)
        try writer["architectures"].writeList(value.architectures, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["description"].write(value.description)
        try writer["logoImageBlob"].write(value.logoImageBlob)
        try writer["operatingSystems"].writeList(value.operatingSystems, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["usageText"].write(value.usageText)
    }
}

public enum ECRPUBLICClientTypes {}
