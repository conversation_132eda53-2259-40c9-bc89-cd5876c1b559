//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ByteStream
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
import struct SmithyHTTPAPI.Header
import struct SmithyHTTPAPI.Headers

extension EBSClientTypes {

    public enum AccessDeniedExceptionReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case dependencyAccessDenied
        case unauthorizedAccount
        case sdkUnknown(Swift.String)

        public static var allCases: [AccessDeniedExceptionReason] {
            return [
                .dependencyAccessDenied,
                .unauthorizedAccount
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .dependencyAccessDenied: return "DEPENDENCY_ACCESS_DENIED"
            case .unauthorizedAccount: return "UNAUTHORIZED_ACCOUNT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// You do not have sufficient access to perform this action.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
        /// The reason for the exception.
        /// This member is required.
        public internal(set) var reason: EBSClientTypes.AccessDeniedExceptionReason? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        reason: EBSClientTypes.AccessDeniedExceptionReason? = nil
    )
    {
        self.properties.message = message
        self.properties.reason = reason
    }
}

extension EBSClientTypes {

    /// A block of data in an Amazon Elastic Block Store snapshot.
    public struct Block: Swift.Sendable {
        /// The block index.
        public var blockIndex: Swift.Int?
        /// The block token for the block index.
        public var blockToken: Swift.String?

        public init(
            blockIndex: Swift.Int? = nil,
            blockToken: Swift.String? = nil
        )
        {
            self.blockIndex = blockIndex
            self.blockToken = blockToken
        }
    }
}

extension EBSClientTypes {

    /// A block of data in an Amazon Elastic Block Store snapshot that is different from another snapshot of the same volume/snapshot lineage.
    public struct ChangedBlock: Swift.Sendable {
        /// The block index.
        public var blockIndex: Swift.Int?
        /// The block token for the block index of the FirstSnapshotId specified in the ListChangedBlocks operation. This value is absent if the first snapshot does not have the changed block that is on the second snapshot.
        public var firstBlockToken: Swift.String?
        /// The block token for the block index of the SecondSnapshotId specified in the ListChangedBlocks operation.
        public var secondBlockToken: Swift.String?

        public init(
            blockIndex: Swift.Int? = nil,
            firstBlockToken: Swift.String? = nil,
            secondBlockToken: Swift.String? = nil
        )
        {
            self.blockIndex = blockIndex
            self.firstBlockToken = firstBlockToken
            self.secondBlockToken = secondBlockToken
        }
    }
}

extension EBSClientTypes.ChangedBlock: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CONTENT_REDACTED"
    }
}

extension EBSClientTypes {

    public enum ChecksumAggregationMethod: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case checksumAggregationLinear
        case sdkUnknown(Swift.String)

        public static var allCases: [ChecksumAggregationMethod] {
            return [
                .checksumAggregationLinear
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .checksumAggregationLinear: return "LINEAR"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EBSClientTypes {

    public enum ChecksumAlgorithm: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case checksumAlgorithmSha256
        case sdkUnknown(Swift.String)

        public static var allCases: [ChecksumAlgorithm] {
            return [
                .checksumAlgorithmSha256
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .checksumAlgorithmSha256: return "SHA256"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// An internal error has occurred. For more information see [Error retries](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/error-retries.html).
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension EBSClientTypes {

    public enum RequestThrottledExceptionReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case accountThrottled
        case dependencyRequestThrottled
        case resourceLevelThrottle
        case sdkUnknown(Swift.String)

        public static var allCases: [RequestThrottledExceptionReason] {
            return [
                .accountThrottled,
                .dependencyRequestThrottled,
                .resourceLevelThrottle
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .accountThrottled: return "ACCOUNT_THROTTLED"
            case .dependencyRequestThrottled: return "DEPENDENCY_REQUEST_THROTTLED"
            case .resourceLevelThrottle: return "RESOURCE_LEVEL_THROTTLE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The number of API requests has exceeded the maximum allowed API request throttling limit for the snapshot. For more information see [Error retries](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/error-retries.html).
public struct RequestThrottledException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
        /// The reason for the exception.
        public internal(set) var reason: EBSClientTypes.RequestThrottledExceptionReason? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RequestThrottledException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        reason: EBSClientTypes.RequestThrottledExceptionReason? = nil
    )
    {
        self.properties.message = message
        self.properties.reason = reason
    }
}

extension EBSClientTypes {

    public enum ResourceNotFoundExceptionReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case dependencyResourceNotFound
        case grantNotFound
        case imageNotFound
        case snapshotNotFound
        case sdkUnknown(Swift.String)

        public static var allCases: [ResourceNotFoundExceptionReason] {
            return [
                .dependencyResourceNotFound,
                .grantNotFound,
                .imageNotFound,
                .snapshotNotFound
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .dependencyResourceNotFound: return "DEPENDENCY_RESOURCE_NOT_FOUND"
            case .grantNotFound: return "GRANT_NOT_FOUND"
            case .imageNotFound: return "IMAGE_NOT_FOUND"
            case .snapshotNotFound: return "SNAPSHOT_NOT_FOUND"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The specified resource does not exist.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
        /// The reason for the exception.
        public internal(set) var reason: EBSClientTypes.ResourceNotFoundExceptionReason? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        reason: EBSClientTypes.ResourceNotFoundExceptionReason? = nil
    )
    {
        self.properties.message = message
        self.properties.reason = reason
    }
}

extension EBSClientTypes {

    public enum ServiceQuotaExceededExceptionReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case dependencyServiceQuotaExceeded
        case sdkUnknown(Swift.String)

        public static var allCases: [ServiceQuotaExceededExceptionReason] {
            return [
                .dependencyServiceQuotaExceeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .dependencyServiceQuotaExceeded: return "DEPENDENCY_SERVICE_QUOTA_EXCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Your current service quotas do not allow you to perform this action.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
        /// The reason for the exception.
        public internal(set) var reason: EBSClientTypes.ServiceQuotaExceededExceptionReason? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceQuotaExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        reason: EBSClientTypes.ServiceQuotaExceededExceptionReason? = nil
    )
    {
        self.properties.message = message
        self.properties.reason = reason
    }
}

extension EBSClientTypes {

    public enum ValidationExceptionReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case conflictingBlockUpdate
        case invalidBlock
        case invalidBlockToken
        case invalidContentEncoding
        case invalidCustomerKey
        case invalidDependencyRequest
        case invalidGrantToken
        case invalidImageId
        case invalidPageToken
        case invalidParameterValue
        case invalidSnapshotId
        case invalidTag
        case invalidVolumeSize
        case unrelatedSnapshots
        case writeRequestTimeout
        case sdkUnknown(Swift.String)

        public static var allCases: [ValidationExceptionReason] {
            return [
                .conflictingBlockUpdate,
                .invalidBlock,
                .invalidBlockToken,
                .invalidContentEncoding,
                .invalidCustomerKey,
                .invalidDependencyRequest,
                .invalidGrantToken,
                .invalidImageId,
                .invalidPageToken,
                .invalidParameterValue,
                .invalidSnapshotId,
                .invalidTag,
                .invalidVolumeSize,
                .unrelatedSnapshots,
                .writeRequestTimeout
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .conflictingBlockUpdate: return "CONFLICTING_BLOCK_UPDATE"
            case .invalidBlock: return "INVALID_BLOCK"
            case .invalidBlockToken: return "INVALID_BLOCK_TOKEN"
            case .invalidContentEncoding: return "INVALID_CONTENT_ENCODING"
            case .invalidCustomerKey: return "INVALID_CUSTOMER_KEY"
            case .invalidDependencyRequest: return "INVALID_DEPENDENCY_REQUEST"
            case .invalidGrantToken: return "INVALID_GRANT_TOKEN"
            case .invalidImageId: return "INVALID_IMAGE_ID"
            case .invalidPageToken: return "INVALID_PAGE_TOKEN"
            case .invalidParameterValue: return "INVALID_PARAMETER_VALUE"
            case .invalidSnapshotId: return "INVALID_SNAPSHOT_ID"
            case .invalidTag: return "INVALID_TAG"
            case .invalidVolumeSize: return "INVALID_VOLUME_SIZE"
            case .unrelatedSnapshots: return "UNRELATED_SNAPSHOTS"
            case .writeRequestTimeout: return "WRITE_REQUEST_TIMEOUT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The input fails to satisfy the constraints of the EBS direct APIs.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
        /// The reason for the validation exception.
        public internal(set) var reason: EBSClientTypes.ValidationExceptionReason? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        reason: EBSClientTypes.ValidationExceptionReason? = nil
    )
    {
        self.properties.message = message
        self.properties.reason = reason
    }
}

public struct CompleteSnapshotInput: Swift.Sendable {
    /// The number of blocks that were written to the snapshot.
    /// This member is required.
    public var changedBlocksCount: Swift.Int?
    /// An aggregated Base-64 SHA256 checksum based on the checksums of each written block. To generate the aggregated checksum using the linear aggregation method, arrange the checksums for each written block in ascending order of their block index, concatenate them to form a single string, and then generate the checksum on the entire string using the SHA256 algorithm.
    public var checksum: Swift.String?
    /// The aggregation method used to generate the checksum. Currently, the only supported aggregation method is LINEAR.
    public var checksumAggregationMethod: EBSClientTypes.ChecksumAggregationMethod?
    /// The algorithm used to generate the checksum. Currently, the only supported algorithm is SHA256.
    public var checksumAlgorithm: EBSClientTypes.ChecksumAlgorithm?
    /// The ID of the snapshot.
    /// This member is required.
    public var snapshotId: Swift.String?

    public init(
        changedBlocksCount: Swift.Int? = nil,
        checksum: Swift.String? = nil,
        checksumAggregationMethod: EBSClientTypes.ChecksumAggregationMethod? = nil,
        checksumAlgorithm: EBSClientTypes.ChecksumAlgorithm? = nil,
        snapshotId: Swift.String? = nil
    )
    {
        self.changedBlocksCount = changedBlocksCount
        self.checksum = checksum
        self.checksumAggregationMethod = checksumAggregationMethod
        self.checksumAlgorithm = checksumAlgorithm
        self.snapshotId = snapshotId
    }
}

extension EBSClientTypes {

    public enum Status: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case completed
        case error
        case pending
        case sdkUnknown(Swift.String)

        public static var allCases: [Status] {
            return [
                .completed,
                .error,
                .pending
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .completed: return "completed"
            case .error: return "error"
            case .pending: return "pending"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct CompleteSnapshotOutput: Swift.Sendable {
    /// The status of the snapshot.
    public var status: EBSClientTypes.Status?

    public init(
        status: EBSClientTypes.Status? = nil
    )
    {
        self.status = status
    }
}

/// You have reached the limit for concurrent API requests. For more information, see [Optimizing performance of the EBS direct APIs](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ebs-accessing-snapshot.html#ebsapi-performance) in the Amazon Elastic Compute Cloud User Guide.
public struct ConcurrentLimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConcurrentLimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request uses the same client token as a previous, but non-identical request.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct GetSnapshotBlockInput: Swift.Sendable {
    /// The block index of the block in which to read the data. A block index is a logical index in units of 512 KiB blocks. To identify the block index, divide the logical offset of the data in the logical volume by the block size (logical offset of data/524288). The logical offset of the data must be 512 KiB aligned.
    /// This member is required.
    public var blockIndex: Swift.Int?
    /// The block token of the block from which to get data. You can obtain the BlockToken by running the ListChangedBlocks or ListSnapshotBlocks operations.
    /// This member is required.
    public var blockToken: Swift.String?
    /// The ID of the snapshot containing the block from which to get data. If the specified snapshot is encrypted, you must have permission to use the KMS key that was used to encrypt the snapshot. For more information, see [ Using encryption](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ebsapis-using-encryption.html) in the Amazon Elastic Compute Cloud User Guide.
    /// This member is required.
    public var snapshotId: Swift.String?

    public init(
        blockIndex: Swift.Int? = nil,
        blockToken: Swift.String? = nil,
        snapshotId: Swift.String? = nil
    )
    {
        self.blockIndex = blockIndex
        self.blockToken = blockToken
        self.snapshotId = snapshotId
    }
}

public struct GetSnapshotBlockOutput: Swift.Sendable {
    /// The data content of the block.
    public var blockData: Smithy.ByteStream?
    /// The checksum generated for the block, which is Base64 encoded.
    public var checksum: Swift.String?
    /// The algorithm used to generate the checksum for the block, such as SHA256.
    public var checksumAlgorithm: EBSClientTypes.ChecksumAlgorithm?
    /// The size of the data in the block.
    public var dataLength: Swift.Int?

    public init(
        blockData: Smithy.ByteStream? = Smithy.ByteStream.data(Foundation.Data(base64Encoded: "")),
        checksum: Swift.String? = nil,
        checksumAlgorithm: EBSClientTypes.ChecksumAlgorithm? = nil,
        dataLength: Swift.Int? = nil
    )
    {
        self.blockData = blockData
        self.checksum = checksum
        self.checksumAlgorithm = checksumAlgorithm
        self.dataLength = dataLength
    }
}

extension GetSnapshotBlockOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "GetSnapshotBlockOutput(checksum: \(Swift.String(describing: checksum)), checksumAlgorithm: \(Swift.String(describing: checksumAlgorithm)), dataLength: \(Swift.String(describing: dataLength)), blockData: \"CONTENT_REDACTED\")"}
}

public struct ListChangedBlocksInput: Swift.Sendable {
    /// The ID of the first snapshot to use for the comparison. The FirstSnapshotID parameter must be specified with a SecondSnapshotId parameter; otherwise, an error occurs.
    public var firstSnapshotId: Swift.String?
    /// The maximum number of blocks to be returned by the request. Even if additional blocks can be retrieved from the snapshot, the request can return less blocks than MaxResults or an empty array of blocks. To retrieve the next set of blocks from the snapshot, make another request with the returned NextToken value. The value of NextToken is null when there are no more blocks to return.
    public var maxResults: Swift.Int?
    /// The token to request the next page of results. If you specify NextToken, then StartingBlockIndex is ignored.
    public var nextToken: Swift.String?
    /// The ID of the second snapshot to use for the comparison. The SecondSnapshotId parameter must be specified with a FirstSnapshotID parameter; otherwise, an error occurs.
    /// This member is required.
    public var secondSnapshotId: Swift.String?
    /// The block index from which the comparison should start. The list in the response will start from this block index or the next valid block index in the snapshots. If you specify NextToken, then StartingBlockIndex is ignored.
    public var startingBlockIndex: Swift.Int?

    public init(
        firstSnapshotId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        secondSnapshotId: Swift.String? = nil,
        startingBlockIndex: Swift.Int? = nil
    )
    {
        self.firstSnapshotId = firstSnapshotId
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.secondSnapshotId = secondSnapshotId
        self.startingBlockIndex = startingBlockIndex
    }
}

public struct ListChangedBlocksOutput: Swift.Sendable {
    /// The size of the blocks in the snapshot, in bytes.
    public var blockSize: Swift.Int?
    /// An array of objects containing information about the changed blocks.
    public var changedBlocks: [EBSClientTypes.ChangedBlock]?
    /// The time when the BlockToken expires.
    public var expiryTime: Foundation.Date?
    /// The token to use to retrieve the next page of results. This value is null when there are no more results to return.
    public var nextToken: Swift.String?
    /// The size of the volume in GB.
    public var volumeSize: Swift.Int?

    public init(
        blockSize: Swift.Int? = nil,
        changedBlocks: [EBSClientTypes.ChangedBlock]? = nil,
        expiryTime: Foundation.Date? = nil,
        nextToken: Swift.String? = nil,
        volumeSize: Swift.Int? = nil
    )
    {
        self.blockSize = blockSize
        self.changedBlocks = changedBlocks
        self.expiryTime = expiryTime
        self.nextToken = nextToken
        self.volumeSize = volumeSize
    }
}

extension ListChangedBlocksOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ListChangedBlocksOutput(blockSize: \(Swift.String(describing: blockSize)), expiryTime: \(Swift.String(describing: expiryTime)), nextToken: \(Swift.String(describing: nextToken)), volumeSize: \(Swift.String(describing: volumeSize)), changedBlocks: \"CONTENT_REDACTED\")"}
}

public struct ListSnapshotBlocksInput: Swift.Sendable {
    /// The maximum number of blocks to be returned by the request. Even if additional blocks can be retrieved from the snapshot, the request can return less blocks than MaxResults or an empty array of blocks. To retrieve the next set of blocks from the snapshot, make another request with the returned NextToken value. The value of NextToken is null when there are no more blocks to return.
    public var maxResults: Swift.Int?
    /// The token to request the next page of results. If you specify NextToken, then StartingBlockIndex is ignored.
    public var nextToken: Swift.String?
    /// The ID of the snapshot from which to get block indexes and block tokens.
    /// This member is required.
    public var snapshotId: Swift.String?
    /// The block index from which the list should start. The list in the response will start from this block index or the next valid block index in the snapshot. If you specify NextToken, then StartingBlockIndex is ignored.
    public var startingBlockIndex: Swift.Int?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        snapshotId: Swift.String? = nil,
        startingBlockIndex: Swift.Int? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.snapshotId = snapshotId
        self.startingBlockIndex = startingBlockIndex
    }
}

public struct ListSnapshotBlocksOutput: Swift.Sendable {
    /// The size of the blocks in the snapshot, in bytes.
    public var blockSize: Swift.Int?
    /// An array of objects containing information about the blocks.
    public var blocks: [EBSClientTypes.Block]?
    /// The time when the BlockToken expires.
    public var expiryTime: Foundation.Date?
    /// The token to use to retrieve the next page of results. This value is null when there are no more results to return.
    public var nextToken: Swift.String?
    /// The size of the volume in GB.
    public var volumeSize: Swift.Int?

    public init(
        blockSize: Swift.Int? = nil,
        blocks: [EBSClientTypes.Block]? = nil,
        expiryTime: Foundation.Date? = nil,
        nextToken: Swift.String? = nil,
        volumeSize: Swift.Int? = nil
    )
    {
        self.blockSize = blockSize
        self.blocks = blocks
        self.expiryTime = expiryTime
        self.nextToken = nextToken
        self.volumeSize = volumeSize
    }
}

extension ListSnapshotBlocksOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ListSnapshotBlocksOutput(blockSize: \(Swift.String(describing: blockSize)), expiryTime: \(Swift.String(describing: expiryTime)), nextToken: \(Swift.String(describing: nextToken)), volumeSize: \(Swift.String(describing: volumeSize)), blocks: \"CONTENT_REDACTED\")"}
}

public struct PutSnapshotBlockInput: Swift.Sendable {
    /// The data to write to the block. The block data is not signed as part of the Signature Version 4 signing process. As a result, you must generate and provide a Base64-encoded SHA256 checksum for the block data using the x-amz-Checksum header. Also, you must specify the checksum algorithm using the x-amz-Checksum-Algorithm header. The checksum that you provide is part of the Signature Version 4 signing process. It is validated against a checksum generated by Amazon EBS to ensure the validity and authenticity of the data. If the checksums do not correspond, the request fails. For more information, see [ Using checksums with the EBS direct APIs](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ebs-accessing-snapshot.html#ebsapis-using-checksums) in the Amazon Elastic Compute Cloud User Guide.
    /// This member is required.
    public var blockData: Smithy.ByteStream?
    /// The block index of the block in which to write the data. A block index is a logical index in units of 512 KiB blocks. To identify the block index, divide the logical offset of the data in the logical volume by the block size (logical offset of data/524288). The logical offset of the data must be 512 KiB aligned.
    /// This member is required.
    public var blockIndex: Swift.Int?
    /// A Base64-encoded SHA256 checksum of the data. Only SHA256 checksums are supported.
    /// This member is required.
    public var checksum: Swift.String?
    /// The algorithm used to generate the checksum. Currently, the only supported algorithm is SHA256.
    /// This member is required.
    public var checksumAlgorithm: EBSClientTypes.ChecksumAlgorithm?
    /// The size of the data to write to the block, in bytes. Currently, the only supported size is 524288 bytes. Valid values: 524288
    /// This member is required.
    public var dataLength: Swift.Int?
    /// The progress of the write process, as a percentage.
    public var progress: Swift.Int?
    /// The ID of the snapshot. If the specified snapshot is encrypted, you must have permission to use the KMS key that was used to encrypt the snapshot. For more information, see [ Using encryption](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ebsapis-using-encryption.html) in the Amazon Elastic Compute Cloud User Guide..
    /// This member is required.
    public var snapshotId: Swift.String?

    public init(
        blockData: Smithy.ByteStream? = nil,
        blockIndex: Swift.Int? = nil,
        checksum: Swift.String? = nil,
        checksumAlgorithm: EBSClientTypes.ChecksumAlgorithm? = nil,
        dataLength: Swift.Int? = nil,
        progress: Swift.Int? = nil,
        snapshotId: Swift.String? = nil
    )
    {
        self.blockData = blockData
        self.blockIndex = blockIndex
        self.checksum = checksum
        self.checksumAlgorithm = checksumAlgorithm
        self.dataLength = dataLength
        self.progress = progress
        self.snapshotId = snapshotId
    }
}

extension PutSnapshotBlockInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "PutSnapshotBlockInput(blockIndex: \(Swift.String(describing: blockIndex)), checksum: \(Swift.String(describing: checksum)), checksumAlgorithm: \(Swift.String(describing: checksumAlgorithm)), dataLength: \(Swift.String(describing: dataLength)), progress: \(Swift.String(describing: progress)), snapshotId: \(Swift.String(describing: snapshotId)), blockData: \"CONTENT_REDACTED\")"}
}

public struct PutSnapshotBlockOutput: Swift.Sendable {
    /// The SHA256 checksum generated for the block data by Amazon EBS.
    public var checksum: Swift.String?
    /// The algorithm used by Amazon EBS to generate the checksum.
    public var checksumAlgorithm: EBSClientTypes.ChecksumAlgorithm?

    public init(
        checksum: Swift.String? = nil,
        checksumAlgorithm: EBSClientTypes.ChecksumAlgorithm? = nil
    )
    {
        self.checksum = checksum
        self.checksumAlgorithm = checksumAlgorithm
    }
}

extension EBSClientTypes {

    /// Describes a tag.
    public struct Tag: Swift.Sendable {
        /// The key of the tag.
        public var key: Swift.String?
        /// The value of the tag.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

public struct StartSnapshotInput: Swift.Sendable {
    /// A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully. The subsequent retries with the same client token return the result from the original successful request and they have no additional effect. If you do not specify a client token, one is automatically generated by the Amazon Web Services SDK. For more information, see [ Idempotency for StartSnapshot API](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ebs-direct-api-idempotency.html) in the Amazon Elastic Compute Cloud User Guide.
    public var clientToken: Swift.String?
    /// A description for the snapshot.
    public var description: Swift.String?
    /// Indicates whether to encrypt the snapshot. You can't specify Encrypted and ParentSnapshotId in the same request. If you specify both parameters, the request fails with ValidationException. The encryption status of the snapshot depends on the values that you specify for Encrypted, KmsKeyArn, and ParentSnapshotId, and whether your Amazon Web Services account is enabled for [ encryption by default](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/EBSEncryption.html#encryption-by-default). For more information, see [ Using encryption](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ebsapis-using-encryption.html) in the Amazon Elastic Compute Cloud User Guide. To create an encrypted snapshot, you must have permission to use the KMS key. For more information, see [ Permissions to use Key Management Service keys](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ebsapi-permissions.html#ebsapi-kms-permissions) in the Amazon Elastic Compute Cloud User Guide.
    public var encrypted: Swift.Bool?
    /// The Amazon Resource Name (ARN) of the Key Management Service (KMS) key to be used to encrypt the snapshot. The encryption status of the snapshot depends on the values that you specify for Encrypted, KmsKeyArn, and ParentSnapshotId, and whether your Amazon Web Services account is enabled for [ encryption by default](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/EBSEncryption.html#encryption-by-default). For more information, see [ Using encryption](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ebsapis-using-encryption.html) in the Amazon Elastic Compute Cloud User Guide. To create an encrypted snapshot, you must have permission to use the KMS key. For more information, see [ Permissions to use Key Management Service keys](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ebsapi-permissions.html#ebsapi-kms-permissions) in the Amazon Elastic Compute Cloud User Guide.
    public var kmsKeyArn: Swift.String?
    /// The ID of the parent snapshot. If there is no parent snapshot, or if you are creating the first snapshot for an on-premises volume, omit this parameter. You can't specify ParentSnapshotId and Encrypted in the same request. If you specify both parameters, the request fails with ValidationException. The encryption status of the snapshot depends on the values that you specify for Encrypted, KmsKeyArn, and ParentSnapshotId, and whether your Amazon Web Services account is enabled for [ encryption by default](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/EBSEncryption.html#encryption-by-default). For more information, see [ Using encryption](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ebsapis-using-encryption.html) in the Amazon Elastic Compute Cloud User Guide. If you specify an encrypted parent snapshot, you must have permission to use the KMS key that was used to encrypt the parent snapshot. For more information, see [ Permissions to use Key Management Service keys](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ebsapi-permissions.html#ebsapi-kms-permissions) in the Amazon Elastic Compute Cloud User Guide.
    public var parentSnapshotId: Swift.String?
    /// The tags to apply to the snapshot.
    public var tags: [EBSClientTypes.Tag]?
    /// The amount of time (in minutes) after which the snapshot is automatically cancelled if:
    ///
    /// * No blocks are written to the snapshot.
    ///
    /// * The snapshot is not completed after writing the last block of data.
    ///
    ///
    /// If no value is specified, the timeout defaults to 60 minutes.
    public var timeout: Swift.Int?
    /// The size of the volume, in GiB. The maximum size is 65536 GiB (64 TiB).
    /// This member is required.
    public var volumeSize: Swift.Int?

    public init(
        clientToken: Swift.String? = nil,
        description: Swift.String? = nil,
        encrypted: Swift.Bool? = nil,
        kmsKeyArn: Swift.String? = nil,
        parentSnapshotId: Swift.String? = nil,
        tags: [EBSClientTypes.Tag]? = nil,
        timeout: Swift.Int? = nil,
        volumeSize: Swift.Int? = nil
    )
    {
        self.clientToken = clientToken
        self.description = description
        self.encrypted = encrypted
        self.kmsKeyArn = kmsKeyArn
        self.parentSnapshotId = parentSnapshotId
        self.tags = tags
        self.timeout = timeout
        self.volumeSize = volumeSize
    }
}

extension StartSnapshotInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "StartSnapshotInput(clientToken: \(Swift.String(describing: clientToken)), description: \(Swift.String(describing: description)), encrypted: \(Swift.String(describing: encrypted)), parentSnapshotId: \(Swift.String(describing: parentSnapshotId)), tags: \(Swift.String(describing: tags)), timeout: \(Swift.String(describing: timeout)), volumeSize: \(Swift.String(describing: volumeSize)), kmsKeyArn: \"CONTENT_REDACTED\")"}
}

extension EBSClientTypes {

    public enum SSEType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case `none`
        case sseEbs
        case sseKms
        case sdkUnknown(Swift.String)

        public static var allCases: [SSEType] {
            return [
                .none,
                .sseEbs,
                .sseKms
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .none: return "none"
            case .sseEbs: return "sse-ebs"
            case .sseKms: return "sse-kms"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct StartSnapshotOutput: Swift.Sendable {
    /// The size of the blocks in the snapshot, in bytes.
    public var blockSize: Swift.Int?
    /// The description of the snapshot.
    public var description: Swift.String?
    /// The Amazon Resource Name (ARN) of the Key Management Service (KMS) key used to encrypt the snapshot.
    public var kmsKeyArn: Swift.String?
    /// The Amazon Web Services account ID of the snapshot owner.
    public var ownerId: Swift.String?
    /// The ID of the parent snapshot.
    public var parentSnapshotId: Swift.String?
    /// The ID of the snapshot.
    public var snapshotId: Swift.String?
    /// Reserved for future use.
    public var sseType: EBSClientTypes.SSEType?
    /// The timestamp when the snapshot was created.
    public var startTime: Foundation.Date?
    /// The status of the snapshot.
    public var status: EBSClientTypes.Status?
    /// The tags applied to the snapshot. You can specify up to 50 tags per snapshot. For more information, see [ Tagging your Amazon EC2 resources](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Using_Tags.html) in the Amazon Elastic Compute Cloud User Guide.
    public var tags: [EBSClientTypes.Tag]?
    /// The size of the volume, in GiB.
    public var volumeSize: Swift.Int?

    public init(
        blockSize: Swift.Int? = nil,
        description: Swift.String? = nil,
        kmsKeyArn: Swift.String? = nil,
        ownerId: Swift.String? = nil,
        parentSnapshotId: Swift.String? = nil,
        snapshotId: Swift.String? = nil,
        sseType: EBSClientTypes.SSEType? = nil,
        startTime: Foundation.Date? = nil,
        status: EBSClientTypes.Status? = nil,
        tags: [EBSClientTypes.Tag]? = nil,
        volumeSize: Swift.Int? = nil
    )
    {
        self.blockSize = blockSize
        self.description = description
        self.kmsKeyArn = kmsKeyArn
        self.ownerId = ownerId
        self.parentSnapshotId = parentSnapshotId
        self.snapshotId = snapshotId
        self.sseType = sseType
        self.startTime = startTime
        self.status = status
        self.tags = tags
        self.volumeSize = volumeSize
    }
}

extension StartSnapshotOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "StartSnapshotOutput(blockSize: \(Swift.String(describing: blockSize)), description: \(Swift.String(describing: description)), ownerId: \(Swift.String(describing: ownerId)), parentSnapshotId: \(Swift.String(describing: parentSnapshotId)), snapshotId: \(Swift.String(describing: snapshotId)), sseType: \(Swift.String(describing: sseType)), startTime: \(Swift.String(describing: startTime)), status: \(Swift.String(describing: status)), tags: \(Swift.String(describing: tags)), volumeSize: \(Swift.String(describing: volumeSize)), kmsKeyArn: \"CONTENT_REDACTED\")"}
}

extension CompleteSnapshotInput {

    static func urlPathProvider(_ value: CompleteSnapshotInput) -> Swift.String? {
        guard let snapshotId = value.snapshotId else {
            return nil
        }
        return "/snapshots/completion/\(snapshotId.urlPercentEncoding())"
    }
}

extension CompleteSnapshotInput {

    static func headerProvider(_ value: CompleteSnapshotInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let changedBlocksCount = value.changedBlocksCount {
            items.add(SmithyHTTPAPI.Header(name: "x-amz-ChangedBlocksCount", value: Swift.String(changedBlocksCount)))
        }
        if let checksum = value.checksum {
            items.add(SmithyHTTPAPI.Header(name: "x-amz-Checksum", value: Swift.String(checksum)))
        }
        if let checksumAggregationMethod = value.checksumAggregationMethod {
            items.add(SmithyHTTPAPI.Header(name: "x-amz-Checksum-Aggregation-Method", value: Swift.String(checksumAggregationMethod.rawValue)))
        }
        if let checksumAlgorithm = value.checksumAlgorithm {
            items.add(SmithyHTTPAPI.Header(name: "x-amz-Checksum-Algorithm", value: Swift.String(checksumAlgorithm.rawValue)))
        }
        return items
    }
}

extension GetSnapshotBlockInput {

    static func urlPathProvider(_ value: GetSnapshotBlockInput) -> Swift.String? {
        guard let snapshotId = value.snapshotId else {
            return nil
        }
        guard let blockIndex = value.blockIndex else {
            return nil
        }
        return "/snapshots/\(snapshotId.urlPercentEncoding())/blocks/\(blockIndex)"
    }
}

extension GetSnapshotBlockInput {

    static func queryItemProvider(_ value: GetSnapshotBlockInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let blockToken = value.blockToken else {
            let message = "Creating a URL Query Item failed. blockToken is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let blockTokenQueryItem = Smithy.URIQueryItem(name: "blockToken".urlPercentEncoding(), value: Swift.String(blockToken).urlPercentEncoding())
        items.append(blockTokenQueryItem)
        return items
    }
}

extension ListChangedBlocksInput {

    static func urlPathProvider(_ value: ListChangedBlocksInput) -> Swift.String? {
        guard let secondSnapshotId = value.secondSnapshotId else {
            return nil
        }
        return "/snapshots/\(secondSnapshotId.urlPercentEncoding())/changedblocks"
    }
}

extension ListChangedBlocksInput {

    static func queryItemProvider(_ value: ListChangedBlocksInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let firstSnapshotId = value.firstSnapshotId {
            let firstSnapshotIdQueryItem = Smithy.URIQueryItem(name: "firstSnapshotId".urlPercentEncoding(), value: Swift.String(firstSnapshotId).urlPercentEncoding())
            items.append(firstSnapshotIdQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "pageToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let startingBlockIndex = value.startingBlockIndex {
            let startingBlockIndexQueryItem = Smithy.URIQueryItem(name: "startingBlockIndex".urlPercentEncoding(), value: Swift.String(startingBlockIndex).urlPercentEncoding())
            items.append(startingBlockIndexQueryItem)
        }
        return items
    }
}

extension ListSnapshotBlocksInput {

    static func urlPathProvider(_ value: ListSnapshotBlocksInput) -> Swift.String? {
        guard let snapshotId = value.snapshotId else {
            return nil
        }
        return "/snapshots/\(snapshotId.urlPercentEncoding())/blocks"
    }
}

extension ListSnapshotBlocksInput {

    static func queryItemProvider(_ value: ListSnapshotBlocksInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "pageToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let startingBlockIndex = value.startingBlockIndex {
            let startingBlockIndexQueryItem = Smithy.URIQueryItem(name: "startingBlockIndex".urlPercentEncoding(), value: Swift.String(startingBlockIndex).urlPercentEncoding())
            items.append(startingBlockIndexQueryItem)
        }
        return items
    }
}

extension PutSnapshotBlockInput {

    static func urlPathProvider(_ value: PutSnapshotBlockInput) -> Swift.String? {
        guard let snapshotId = value.snapshotId else {
            return nil
        }
        guard let blockIndex = value.blockIndex else {
            return nil
        }
        return "/snapshots/\(snapshotId.urlPercentEncoding())/blocks/\(blockIndex)"
    }
}

extension PutSnapshotBlockInput {

    static func headerProvider(_ value: PutSnapshotBlockInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let checksum = value.checksum {
            items.add(SmithyHTTPAPI.Header(name: "x-amz-Checksum", value: Swift.String(checksum)))
        }
        if let checksumAlgorithm = value.checksumAlgorithm {
            items.add(SmithyHTTPAPI.Header(name: "x-amz-Checksum-Algorithm", value: Swift.String(checksumAlgorithm.rawValue)))
        }
        if let dataLength = value.dataLength {
            items.add(SmithyHTTPAPI.Header(name: "x-amz-Data-Length", value: Swift.String(dataLength)))
        }
        if let progress = value.progress {
            items.add(SmithyHTTPAPI.Header(name: "x-amz-Progress", value: Swift.String(progress)))
        }
        return items
    }
}

extension StartSnapshotInput {

    static func urlPathProvider(_ value: StartSnapshotInput) -> Swift.String? {
        return "/snapshots"
    }
}

extension PutSnapshotBlockInput {

    static func write(value: PutSnapshotBlockInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["BlockData"].write(value.blockData)
    }
}

extension StartSnapshotInput {

    static func write(value: StartSnapshotInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["Description"].write(value.description)
        try writer["Encrypted"].write(value.encrypted)
        try writer["KmsKeyArn"].write(value.kmsKeyArn)
        try writer["ParentSnapshotId"].write(value.parentSnapshotId)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: EBSClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Timeout"].write(value.timeout)
        try writer["VolumeSize"].write(value.volumeSize)
    }
}

extension CompleteSnapshotOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CompleteSnapshotOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CompleteSnapshotOutput()
        value.status = try reader["Status"].readIfPresent()
        return value
    }
}

extension GetSnapshotBlockOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetSnapshotBlockOutput {
        var value = GetSnapshotBlockOutput()
        if let checksumHeaderValue = httpResponse.headers.value(for: "x-amz-Checksum") {
            value.checksum = checksumHeaderValue
        }
        if let checksumAlgorithmHeaderValue = httpResponse.headers.value(for: "x-amz-Checksum-Algorithm") {
            value.checksumAlgorithm = EBSClientTypes.ChecksumAlgorithm(rawValue: checksumAlgorithmHeaderValue)
        }
        if let dataLengthHeaderValue = httpResponse.headers.value(for: "x-amz-Data-Length") {
            value.dataLength = Swift.Int(dataLengthHeaderValue) ?? 0
        }
        switch httpResponse.body {
        case .data(let data):
            value.blockData = .data(data)
        case .stream(let stream):
            value.blockData = .stream(stream)
        case .noStream:
            value.blockData = nil
        }
        return value
    }
}

extension ListChangedBlocksOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListChangedBlocksOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListChangedBlocksOutput()
        value.blockSize = try reader["BlockSize"].readIfPresent()
        value.changedBlocks = try reader["ChangedBlocks"].readListIfPresent(memberReadingClosure: EBSClientTypes.ChangedBlock.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.expiryTime = try reader["ExpiryTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.volumeSize = try reader["VolumeSize"].readIfPresent()
        return value
    }
}

extension ListSnapshotBlocksOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListSnapshotBlocksOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListSnapshotBlocksOutput()
        value.blockSize = try reader["BlockSize"].readIfPresent()
        value.blocks = try reader["Blocks"].readListIfPresent(memberReadingClosure: EBSClientTypes.Block.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.expiryTime = try reader["ExpiryTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.volumeSize = try reader["VolumeSize"].readIfPresent()
        return value
    }
}

extension PutSnapshotBlockOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutSnapshotBlockOutput {
        var value = PutSnapshotBlockOutput()
        if let checksumHeaderValue = httpResponse.headers.value(for: "x-amz-Checksum") {
            value.checksum = checksumHeaderValue
        }
        if let checksumAlgorithmHeaderValue = httpResponse.headers.value(for: "x-amz-Checksum-Algorithm") {
            value.checksumAlgorithm = EBSClientTypes.ChecksumAlgorithm(rawValue: checksumAlgorithmHeaderValue)
        }
        return value
    }
}

extension StartSnapshotOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartSnapshotOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartSnapshotOutput()
        value.blockSize = try reader["BlockSize"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.kmsKeyArn = try reader["KmsKeyArn"].readIfPresent()
        value.ownerId = try reader["OwnerId"].readIfPresent()
        value.parentSnapshotId = try reader["ParentSnapshotId"].readIfPresent()
        value.snapshotId = try reader["SnapshotId"].readIfPresent()
        value.sseType = try reader["SseType"].readIfPresent()
        value.startTime = try reader["StartTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.status = try reader["Status"].readIfPresent()
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: EBSClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.volumeSize = try reader["VolumeSize"].readIfPresent()
        return value
    }
}

enum CompleteSnapshotOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "RequestThrottledException": return try RequestThrottledException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetSnapshotBlockOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "RequestThrottledException": return try RequestThrottledException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListChangedBlocksOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "RequestThrottledException": return try RequestThrottledException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListSnapshotBlocksOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "RequestThrottledException": return try RequestThrottledException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutSnapshotBlockOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "RequestThrottledException": return try RequestThrottledException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartSnapshotOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConcurrentLimitExceededException": return try ConcurrentLimitExceededException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "RequestThrottledException": return try RequestThrottledException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.reason = try reader["Reason"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.reason = try reader["Reason"].readIfPresent() ?? .sdkUnknown("")
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.reason = try reader["Reason"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension RequestThrottledException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> RequestThrottledException {
        let reader = baseError.errorBodyReader
        var value = RequestThrottledException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.reason = try reader["Reason"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceQuotaExceededException {
        let reader = baseError.errorBodyReader
        var value = ServiceQuotaExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.reason = try reader["Reason"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConcurrentLimitExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConcurrentLimitExceededException {
        let reader = baseError.errorBodyReader
        var value = ConcurrentLimitExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension EBSClientTypes.ChangedBlock {

    static func read(from reader: SmithyJSON.Reader) throws -> EBSClientTypes.ChangedBlock {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EBSClientTypes.ChangedBlock()
        value.blockIndex = try reader["BlockIndex"].readIfPresent()
        value.firstBlockToken = try reader["FirstBlockToken"].readIfPresent()
        value.secondBlockToken = try reader["SecondBlockToken"].readIfPresent()
        return value
    }
}

extension EBSClientTypes.Block {

    static func read(from reader: SmithyJSON.Reader) throws -> EBSClientTypes.Block {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EBSClientTypes.Block()
        value.blockIndex = try reader["BlockIndex"].readIfPresent()
        value.blockToken = try reader["BlockToken"].readIfPresent()
        return value
    }
}

extension EBSClientTypes.Tag {

    static func write(value: EBSClientTypes.Tag?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Key"].write(value.key)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EBSClientTypes.Tag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EBSClientTypes.Tag()
        value.key = try reader["Key"].readIfPresent()
        value.value = try reader["Value"].readIfPresent()
        return value
    }
}

public enum EBSClientTypes {}
