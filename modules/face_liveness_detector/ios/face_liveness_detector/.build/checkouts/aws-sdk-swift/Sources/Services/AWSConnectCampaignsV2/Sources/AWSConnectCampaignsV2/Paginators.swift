//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension ConnectCampaignsV2Client {
    /// Paginate over `[ListCampaignsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListCampaignsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListCampaignsOutput`
    public func listCampaignsPaginated(input: ListCampaignsInput) -> ClientRuntime.PaginatorSequence<ListCampaignsInput, ListCampaignsOutput> {
        return ClientRuntime.PaginatorSequence<ListCampaignsInput, ListCampaignsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listCampaigns(input:))
    }
}

extension ListCampaignsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListCampaignsInput {
        return ListCampaignsInput(
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListCampaignsInput, OperationStackOutput == ListCampaignsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listCampaignsPaginated`
    /// to access the nested member `[ConnectCampaignsV2ClientTypes.CampaignSummary]`
    /// - Returns: `[ConnectCampaignsV2ClientTypes.CampaignSummary]`
    public func campaignSummaryList() async throws -> [ConnectCampaignsV2ClientTypes.CampaignSummary] {
        return try await self.asyncCompactMap { item in item.campaignSummaryList }
    }
}
extension ConnectCampaignsV2Client {
    /// Paginate over `[ListConnectInstanceIntegrationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListConnectInstanceIntegrationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListConnectInstanceIntegrationsOutput`
    public func listConnectInstanceIntegrationsPaginated(input: ListConnectInstanceIntegrationsInput) -> ClientRuntime.PaginatorSequence<ListConnectInstanceIntegrationsInput, ListConnectInstanceIntegrationsOutput> {
        return ClientRuntime.PaginatorSequence<ListConnectInstanceIntegrationsInput, ListConnectInstanceIntegrationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listConnectInstanceIntegrations(input:))
    }
}

extension ListConnectInstanceIntegrationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListConnectInstanceIntegrationsInput {
        return ListConnectInstanceIntegrationsInput(
            connectInstanceId: self.connectInstanceId,
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListConnectInstanceIntegrationsInput, OperationStackOutput == ListConnectInstanceIntegrationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listConnectInstanceIntegrationsPaginated`
    /// to access the nested member `[ConnectCampaignsV2ClientTypes.IntegrationSummary]`
    /// - Returns: `[ConnectCampaignsV2ClientTypes.IntegrationSummary]`
    public func integrationSummaryList() async throws -> [ConnectCampaignsV2ClientTypes.IntegrationSummary] {
        return try await self.asyncCompactMap { item in item.integrationSummaryList }
    }
}
