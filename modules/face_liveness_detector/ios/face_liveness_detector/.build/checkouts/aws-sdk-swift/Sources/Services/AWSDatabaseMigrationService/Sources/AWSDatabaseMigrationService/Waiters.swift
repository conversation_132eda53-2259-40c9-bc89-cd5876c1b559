//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import class SmithyWaitersAPI.Waiter
import enum SmithyWaitersAPI.JMESUtils
import protocol ClientRuntime.ServiceError
import struct SmithyWaitersAPI.WaiterConfiguration
import struct SmithyWaitersAPI.WaiterOptions
import struct SmithyWaitersAPI.WaiterOutcome

extension DatabaseMigrationClient {

    static func testConnectionSucceedsWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeConnectionsInput, DescribeConnectionsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeConnectionsInput, DescribeConnectionsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeConnectionsInput, result: Swift.Result<DescribeConnectionsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Connections[].Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "successful"
                guard case .success(let output) = result else { return false }
                let connections = output.connections
                let projection: [Swift.String]? = connections?.compactMap { original in
                    let status = original.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "successful") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeConnectionsInput, result: Swift.Result<DescribeConnectionsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Connections[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "failed"
                guard case .success(let output) = result else { return false }
                let connections = output.connections
                let projection: [Swift.String]? = connections?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "failed") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeConnectionsInput, DescribeConnectionsOutput>(acceptors: acceptors, minDelay: 5.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the TestConnectionSucceeds event on the describeConnections operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeConnectionsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilTestConnectionSucceeds(options: SmithyWaitersAPI.WaiterOptions, input: DescribeConnectionsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeConnectionsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.testConnectionSucceedsWaiterConfig(), operation: self.describeConnections(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func endpointDeletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeEndpointsInput, DescribeEndpointsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeEndpointsInput, DescribeEndpointsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeEndpointsInput, result: Swift.Result<DescribeEndpointsOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "ResourceNotFoundFault"
            }),
            .init(state: .failure, matcher: { (input: DescribeEndpointsInput, result: Swift.Result<DescribeEndpointsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Endpoints[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "active"
                guard case .success(let output) = result else { return false }
                let endpoints = output.endpoints
                let projection: [Swift.String]? = endpoints?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "active") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeEndpointsInput, result: Swift.Result<DescribeEndpointsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Endpoints[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "creating"
                guard case .success(let output) = result else { return false }
                let endpoints = output.endpoints
                let projection: [Swift.String]? = endpoints?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "creating") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeEndpointsInput, DescribeEndpointsOutput>(acceptors: acceptors, minDelay: 5.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the EndpointDeleted event on the describeEndpoints operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeEndpointsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilEndpointDeleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeEndpointsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeEndpointsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.endpointDeletedWaiterConfig(), operation: self.describeEndpoints(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func replicationInstanceAvailableWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationInstancesInput, DescribeReplicationInstancesOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationInstancesInput, DescribeReplicationInstancesOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeReplicationInstancesInput, result: Swift.Result<DescribeReplicationInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationInstances[].ReplicationInstanceStatus"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "available"
                guard case .success(let output) = result else { return false }
                let replicationInstances = output.replicationInstances
                let projection: [Swift.String]? = replicationInstances?.compactMap { original in
                    let replicationInstanceStatus = original.replicationInstanceStatus
                    return replicationInstanceStatus
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "available") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationInstancesInput, result: Swift.Result<DescribeReplicationInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationInstances[].ReplicationInstanceStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleting"
                guard case .success(let output) = result else { return false }
                let replicationInstances = output.replicationInstances
                let projection: [Swift.String]? = replicationInstances?.compactMap { original in
                    let replicationInstanceStatus = original.replicationInstanceStatus
                    return replicationInstanceStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleting") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationInstancesInput, result: Swift.Result<DescribeReplicationInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationInstances[].ReplicationInstanceStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "incompatible-credentials"
                guard case .success(let output) = result else { return false }
                let replicationInstances = output.replicationInstances
                let projection: [Swift.String]? = replicationInstances?.compactMap { original in
                    let replicationInstanceStatus = original.replicationInstanceStatus
                    return replicationInstanceStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "incompatible-credentials") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationInstancesInput, result: Swift.Result<DescribeReplicationInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationInstances[].ReplicationInstanceStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "incompatible-network"
                guard case .success(let output) = result else { return false }
                let replicationInstances = output.replicationInstances
                let projection: [Swift.String]? = replicationInstances?.compactMap { original in
                    let replicationInstanceStatus = original.replicationInstanceStatus
                    return replicationInstanceStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "incompatible-network") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationInstancesInput, result: Swift.Result<DescribeReplicationInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationInstances[].ReplicationInstanceStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "inaccessible-encryption-credentials"
                guard case .success(let output) = result else { return false }
                let replicationInstances = output.replicationInstances
                let projection: [Swift.String]? = replicationInstances?.compactMap { original in
                    let replicationInstanceStatus = original.replicationInstanceStatus
                    return replicationInstanceStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "inaccessible-encryption-credentials") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationInstancesInput, DescribeReplicationInstancesOutput>(acceptors: acceptors, minDelay: 60.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ReplicationInstanceAvailable event on the describeReplicationInstances operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeReplicationInstancesInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilReplicationInstanceAvailable(options: SmithyWaitersAPI.WaiterOptions, input: DescribeReplicationInstancesInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeReplicationInstancesOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.replicationInstanceAvailableWaiterConfig(), operation: self.describeReplicationInstances(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func replicationInstanceDeletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationInstancesInput, DescribeReplicationInstancesOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationInstancesInput, DescribeReplicationInstancesOutput>.Acceptor] = [
            .init(state: .failure, matcher: { (input: DescribeReplicationInstancesInput, result: Swift.Result<DescribeReplicationInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationInstances[].ReplicationInstanceStatus"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "available"
                guard case .success(let output) = result else { return false }
                let replicationInstances = output.replicationInstances
                let projection: [Swift.String]? = replicationInstances?.compactMap { original in
                    let replicationInstanceStatus = original.replicationInstanceStatus
                    return replicationInstanceStatus
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "available") }) ?? false
            }),
            .init(state: .success, matcher: { (input: DescribeReplicationInstancesInput, result: Swift.Result<DescribeReplicationInstancesOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "ResourceNotFoundFault"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationInstancesInput, DescribeReplicationInstancesOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ReplicationInstanceDeleted event on the describeReplicationInstances operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeReplicationInstancesInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilReplicationInstanceDeleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeReplicationInstancesInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeReplicationInstancesOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.replicationInstanceDeletedWaiterConfig(), operation: self.describeReplicationInstances(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func replicationTaskDeletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationTasksInput, DescribeReplicationTasksOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationTasksInput, DescribeReplicationTasksOutput>.Acceptor] = [
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "ready"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "ready") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "creating"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "creating") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "stopped"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "stopped") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "running"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "running") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "failed"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "failed") }) ?? false
            }),
            .init(state: .success, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "ResourceNotFoundFault"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationTasksInput, DescribeReplicationTasksOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ReplicationTaskDeleted event on the describeReplicationTasks operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeReplicationTasksInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilReplicationTaskDeleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeReplicationTasksInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeReplicationTasksOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.replicationTaskDeletedWaiterConfig(), operation: self.describeReplicationTasks(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func replicationTaskReadyWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationTasksInput, DescribeReplicationTasksOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationTasksInput, DescribeReplicationTasksOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "ready"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "ready") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "starting"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "starting") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "running"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "running") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "stopping"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "stopping") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "stopped"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "stopped") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "failed"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "failed") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "modifying"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "modifying") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "testing"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "testing") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleting"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleting") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationTasksInput, DescribeReplicationTasksOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ReplicationTaskReady event on the describeReplicationTasks operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeReplicationTasksInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilReplicationTaskReady(options: SmithyWaitersAPI.WaiterOptions, input: DescribeReplicationTasksInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeReplicationTasksOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.replicationTaskReadyWaiterConfig(), operation: self.describeReplicationTasks(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func replicationTaskRunningWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationTasksInput, DescribeReplicationTasksOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationTasksInput, DescribeReplicationTasksOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "running"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "running") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "ready"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "ready") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "creating"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "creating") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "stopping"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "stopping") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "stopped"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "stopped") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "failed"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "failed") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "modifying"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "modifying") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "testing"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "testing") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleting"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleting") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationTasksInput, DescribeReplicationTasksOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ReplicationTaskRunning event on the describeReplicationTasks operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeReplicationTasksInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilReplicationTaskRunning(options: SmithyWaitersAPI.WaiterOptions, input: DescribeReplicationTasksInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeReplicationTasksOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.replicationTaskRunningWaiterConfig(), operation: self.describeReplicationTasks(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func replicationTaskStoppedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationTasksInput, DescribeReplicationTasksOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationTasksInput, DescribeReplicationTasksOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "stopped"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "stopped") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "ready"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "ready") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "creating"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "creating") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "starting"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "starting") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "failed"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "failed") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "modifying"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "modifying") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "testing"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "testing") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeReplicationTasksInput, result: Swift.Result<DescribeReplicationTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ReplicationTasks[].Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleting"
                guard case .success(let output) = result else { return false }
                let replicationTasks = output.replicationTasks
                let projection: [Swift.String]? = replicationTasks?.compactMap { original in
                    let status = original.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleting") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeReplicationTasksInput, DescribeReplicationTasksOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ReplicationTaskStopped event on the describeReplicationTasks operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeReplicationTasksInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilReplicationTaskStopped(options: SmithyWaitersAPI.WaiterOptions, input: DescribeReplicationTasksInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeReplicationTasksOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.replicationTaskStoppedWaiterConfig(), operation: self.describeReplicationTasks(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }
}
