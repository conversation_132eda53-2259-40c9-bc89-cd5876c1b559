//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHT<PERSON><PERSON>I.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.AWSJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
@_spi(SmithyReadWrite) import struct SmithyReadWrite.WritingClosureBox

/// You are not authorized to use this operation with the given parameters.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CostOptimizationHubClientTypes {

    public enum EnrollmentStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case active
        case inactive
        case sdkUnknown(Swift.String)

        public static var allCases: [EnrollmentStatus] {
            return [
                .active,
                .inactive
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .active: return "Active"
            case .inactive: return "Inactive"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Describes the enrollment status of an organization's member accounts in Cost Optimization Hub.
    public struct AccountEnrollmentStatus: Swift.Sendable {
        /// The Amazon Web Services account ID.
        public var accountId: Swift.String?
        /// The time when the account enrollment status was created.
        public var createdTimestamp: Foundation.Date?
        /// The time when the account enrollment status was last updated.
        public var lastUpdatedTimestamp: Foundation.Date?
        /// The account enrollment status.
        public var status: CostOptimizationHubClientTypes.EnrollmentStatus?

        public init(
            accountId: Swift.String? = nil,
            createdTimestamp: Foundation.Date? = nil,
            lastUpdatedTimestamp: Foundation.Date? = nil,
            status: CostOptimizationHubClientTypes.EnrollmentStatus? = nil
        )
        {
            self.accountId = accountId
            self.createdTimestamp = createdTimestamp
            self.lastUpdatedTimestamp = lastUpdatedTimestamp
            self.status = status
        }
    }
}

extension CostOptimizationHubClientTypes {

    public enum ActionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case delete
        case migrateToGraviton
        case purchaseReservedInstances
        case purchaseSavingsPlans
        case rightsize
        case stop
        case upgrade
        case sdkUnknown(Swift.String)

        public static var allCases: [ActionType] {
            return [
                .delete,
                .migrateToGraviton,
                .purchaseReservedInstances,
                .purchaseSavingsPlans,
                .rightsize,
                .stop,
                .upgrade
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .delete: return "Delete"
            case .migrateToGraviton: return "MigrateToGraviton"
            case .purchaseReservedInstances: return "PurchaseReservedInstances"
            case .purchaseSavingsPlans: return "PurchaseSavingsPlans"
            case .rightsize: return "Rightsize"
            case .stop: return "Stop"
            case .upgrade: return "Upgrade"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Describes the Amazon Elastic Block Store performance configuration of the current and recommended resource configuration for a recommendation.
    public struct BlockStoragePerformanceConfiguration: Swift.Sendable {
        /// The number of I/O operations per second.
        public var iops: Swift.Double?
        /// The throughput that the volume supports.
        public var throughput: Swift.Double?

        public init(
            iops: Swift.Double? = nil,
            throughput: Swift.Double? = nil
        )
        {
            self.iops = iops
            self.throughput = throughput
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Describes the performance configuration for compute services such as Amazon EC2, Lambda, and ECS.
    public struct ComputeConfiguration: Swift.Sendable {
        /// The architecture of the resource.
        public var architecture: Swift.String?
        /// The memory size of the resource.
        public var memorySizeInMB: Swift.Int?
        /// The platform of the resource. The platform is the specific combination of operating system, license model, and software on an instance.
        public var platform: Swift.String?
        /// The number of vCPU cores in the resource.
        public var vCpu: Swift.Double?

        public init(
            architecture: Swift.String? = nil,
            memorySizeInMB: Swift.Int? = nil,
            platform: Swift.String? = nil,
            vCpu: Swift.Double? = nil
        )
        {
            self.architecture = architecture
            self.memorySizeInMB = memorySizeInMB
            self.platform = platform
            self.vCpu = vCpu
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The Compute Savings Plans configuration used for recommendations.
    public struct ComputeSavingsPlansConfiguration: Swift.Sendable {
        /// The account scope that you want your recommendations for. Amazon Web Services calculates recommendations including the management account and member accounts if the value is set to PAYER. If the value is LINKED, recommendations are calculated for individual member accounts only.
        public var accountScope: Swift.String?
        /// The hourly commitment for the Savings Plans type.
        public var hourlyCommitment: Swift.String?
        /// The payment option for the commitment.
        public var paymentOption: Swift.String?
        /// The Savings Plans recommendation term in years.
        public var term: Swift.String?

        public init(
            accountScope: Swift.String? = nil,
            hourlyCommitment: Swift.String? = nil,
            paymentOption: Swift.String? = nil,
            term: Swift.String? = nil
        )
        {
            self.accountScope = accountScope
            self.hourlyCommitment = hourlyCommitment
            self.paymentOption = paymentOption
            self.term = term
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Pricing information about a Savings Plan.
    public struct SavingsPlansPricing: Swift.Sendable {
        /// Estimated monthly commitment for the Savings Plan.
        public var estimatedMonthlyCommitment: Swift.Double?
        /// Estimated On-Demand cost you will pay after buying the Savings Plan.
        public var estimatedOnDemandCost: Swift.Double?
        /// The cost of paying for the recommended Savings Plan monthly.
        public var monthlySavingsPlansEligibleCost: Swift.Double?
        /// Estimated savings as a percentage of your overall costs after buying the Savings Plan.
        public var savingsPercentage: Swift.Double?

        public init(
            estimatedMonthlyCommitment: Swift.Double? = nil,
            estimatedOnDemandCost: Swift.Double? = nil,
            monthlySavingsPlansEligibleCost: Swift.Double? = nil,
            savingsPercentage: Swift.Double? = nil
        )
        {
            self.estimatedMonthlyCommitment = estimatedMonthlyCommitment
            self.estimatedOnDemandCost = estimatedOnDemandCost
            self.monthlySavingsPlansEligibleCost = monthlySavingsPlansEligibleCost
            self.savingsPercentage = savingsPercentage
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Cost impact of the purchase recommendation.
    public struct SavingsPlansCostCalculation: Swift.Sendable {
        /// Pricing details of the purchase recommendation.
        public var pricing: CostOptimizationHubClientTypes.SavingsPlansPricing?

        public init(
            pricing: CostOptimizationHubClientTypes.SavingsPlansPricing? = nil
        )
        {
            self.pricing = pricing
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The Compute Savings Plans recommendation details.
    public struct ComputeSavingsPlans: Swift.Sendable {
        /// Configuration details of the Compute Savings Plans to purchase.
        public var configuration: CostOptimizationHubClientTypes.ComputeSavingsPlansConfiguration?
        /// Cost impact of the Savings Plans purchase recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.SavingsPlansCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.ComputeSavingsPlansConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.SavingsPlansCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

/// An error on the server occurred during the processing of your request. Try again later.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request was denied due to request throttling.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CostOptimizationHubClientTypes {

    /// The input failed to meet the constraints specified by the Amazon Web Services service in a specified field.
    public struct ValidationExceptionDetail: Swift.Sendable {
        /// The field name where the invalid entry was detected.
        /// This member is required.
        public var fieldName: Swift.String?
        /// A message with the reason for the validation exception error.
        /// This member is required.
        public var message: Swift.String?

        public init(
            fieldName: Swift.String? = nil,
            message: Swift.String? = nil
        )
        {
            self.fieldName = fieldName
            self.message = message
        }
    }
}

extension CostOptimizationHubClientTypes {

    public enum ValidationExceptionReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case fieldValidationFailed
        case other
        case sdkUnknown(Swift.String)

        public static var allCases: [ValidationExceptionReason] {
            return [
                .fieldValidationFailed,
                .other
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .fieldValidationFailed: return "FieldValidationFailed"
            case .other: return "Other"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The input fails to satisfy the constraints specified by an Amazon Web Services service.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The list of fields that are invalid.
        public internal(set) var fields: [CostOptimizationHubClientTypes.ValidationExceptionDetail]? = nil
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The reason for the validation exception.
        public internal(set) var reason: CostOptimizationHubClientTypes.ValidationExceptionReason? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        fields: [CostOptimizationHubClientTypes.ValidationExceptionDetail]? = nil,
        message: Swift.String? = nil,
        reason: CostOptimizationHubClientTypes.ValidationExceptionReason? = nil
    )
    {
        self.properties.fields = fields
        self.properties.message = message
        self.properties.reason = reason
    }
}

public struct GetPreferencesInput: Swift.Sendable {

    public init() { }
}

extension CostOptimizationHubClientTypes {

    public enum MemberAccountDiscountVisibility: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case all
        case `none`
        case sdkUnknown(Swift.String)

        public static var allCases: [MemberAccountDiscountVisibility] {
            return [
                .all,
                .none
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .all: return "All"
            case .none: return "None"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CostOptimizationHubClientTypes {

    public enum SavingsEstimationMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case afterDiscounts
        case beforeDiscounts
        case sdkUnknown(Swift.String)

        public static var allCases: [SavingsEstimationMode] {
            return [
                .afterDiscounts,
                .beforeDiscounts
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .afterDiscounts: return "AfterDiscounts"
            case .beforeDiscounts: return "BeforeDiscounts"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct GetPreferencesOutput: Swift.Sendable {
    /// Retrieves the status of the "member account discount visibility" preference.
    public var memberAccountDiscountVisibility: CostOptimizationHubClientTypes.MemberAccountDiscountVisibility?
    /// Retrieves the status of the "savings estimation mode" preference.
    public var savingsEstimationMode: CostOptimizationHubClientTypes.SavingsEstimationMode?

    public init(
        memberAccountDiscountVisibility: CostOptimizationHubClientTypes.MemberAccountDiscountVisibility? = nil,
        savingsEstimationMode: CostOptimizationHubClientTypes.SavingsEstimationMode? = nil
    )
    {
        self.memberAccountDiscountVisibility = memberAccountDiscountVisibility
        self.savingsEstimationMode = savingsEstimationMode
    }
}

/// The specified Amazon Resource Name (ARN) in the request doesn't exist.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The identifier of the resource that was not found.
        /// This member is required.
        public internal(set) var resourceId: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        resourceId: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.resourceId = resourceId
    }
}

public struct GetRecommendationInput: Swift.Sendable {
    /// The ID for the recommendation.
    /// This member is required.
    public var recommendationId: Swift.String?

    public init(
        recommendationId: Swift.String? = nil
    )
    {
        self.recommendationId = recommendationId
    }
}

extension CostOptimizationHubClientTypes {

    /// The storage configuration used for recommendations.
    public struct StorageConfiguration: Swift.Sendable {
        /// The storage volume.
        public var sizeInGb: Swift.Double?
        /// The storage type.
        public var type: Swift.String?

        public init(
            sizeInGb: Swift.Double? = nil,
            type: Swift.String? = nil
        )
        {
            self.sizeInGb = sizeInGb
            self.type = type
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The Amazon Elastic Block Store volume configuration used for recommendations.
    public struct EbsVolumeConfiguration: Swift.Sendable {
        /// The Amazon Elastic Block Store attachment state.
        public var attachmentState: Swift.String?
        /// The Amazon Elastic Block Store performance configuration.
        public var performance: CostOptimizationHubClientTypes.BlockStoragePerformanceConfiguration?
        /// The disk storage of the Amazon Elastic Block Store volume.
        public var storage: CostOptimizationHubClientTypes.StorageConfiguration?

        public init(
            attachmentState: Swift.String? = nil,
            performance: CostOptimizationHubClientTypes.BlockStoragePerformanceConfiguration? = nil,
            storage: CostOptimizationHubClientTypes.StorageConfiguration? = nil
        )
        {
            self.attachmentState = attachmentState
            self.performance = performance
            self.storage = storage
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Estimated discount details of the current and recommended resource configuration for a recommendation.
    public struct EstimatedDiscounts: Swift.Sendable {
        /// Estimated other discounts include all discounts that are not itemized. Itemized discounts include reservedInstanceDiscount and savingsPlansDiscount.
        public var otherDiscount: Swift.Double?
        /// Estimated reserved instance discounts.
        public var reservedInstancesDiscount: Swift.Double?
        /// Estimated Savings Plans discounts.
        public var savingsPlansDiscount: Swift.Double?

        public init(
            otherDiscount: Swift.Double? = nil,
            reservedInstancesDiscount: Swift.Double? = nil,
            savingsPlansDiscount: Swift.Double? = nil
        )
        {
            self.otherDiscount = otherDiscount
            self.reservedInstancesDiscount = reservedInstancesDiscount
            self.savingsPlansDiscount = savingsPlansDiscount
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Contains pricing information about the specified resource.
    public struct ResourcePricing: Swift.Sendable {
        /// The savings estimate incorporating all discounts with Amazon Web Services, such as Reserved Instances and Savings Plans.
        public var estimatedCostAfterDiscounts: Swift.Double?
        /// The savings estimate using Amazon Web Services public pricing without incorporating any discounts.
        public var estimatedCostBeforeDiscounts: Swift.Double?
        /// The estimated discounts for a recommendation.
        public var estimatedDiscounts: CostOptimizationHubClientTypes.EstimatedDiscounts?
        /// The estimated net unused amortized commitment for the recommendation.
        public var estimatedNetUnusedAmortizedCommitments: Swift.Double?

        public init(
            estimatedCostAfterDiscounts: Swift.Double? = nil,
            estimatedCostBeforeDiscounts: Swift.Double? = nil,
            estimatedDiscounts: CostOptimizationHubClientTypes.EstimatedDiscounts? = nil,
            estimatedNetUnusedAmortizedCommitments: Swift.Double? = nil
        )
        {
            self.estimatedCostAfterDiscounts = estimatedCostAfterDiscounts
            self.estimatedCostBeforeDiscounts = estimatedCostBeforeDiscounts
            self.estimatedDiscounts = estimatedDiscounts
            self.estimatedNetUnusedAmortizedCommitments = estimatedNetUnusedAmortizedCommitments
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Details about the usage.
    public struct Usage: Swift.Sendable {
        /// The operation value.
        public var operation: Swift.String?
        /// The product code.
        public var productCode: Swift.String?
        /// The usage unit.
        public var unit: Swift.String?
        /// The usage amount.
        public var usageAmount: Swift.Double?
        /// The usage type.
        public var usageType: Swift.String?

        public init(
            operation: Swift.String? = nil,
            productCode: Swift.String? = nil,
            unit: Swift.String? = nil,
            usageAmount: Swift.Double? = nil,
            usageType: Swift.String? = nil
        )
        {
            self.operation = operation
            self.productCode = productCode
            self.unit = unit
            self.usageAmount = usageAmount
            self.usageType = usageType
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Cost impact of the resource recommendation.
    public struct ResourceCostCalculation: Swift.Sendable {
        /// Pricing details of the resource recommendation.
        public var pricing: CostOptimizationHubClientTypes.ResourcePricing?
        /// Usage details of the resource recommendation.
        public var usages: [CostOptimizationHubClientTypes.Usage]?

        public init(
            pricing: CostOptimizationHubClientTypes.ResourcePricing? = nil,
            usages: [CostOptimizationHubClientTypes.Usage]? = nil
        )
        {
            self.pricing = pricing
            self.usages = usages
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Describes the Amazon Elastic Block Store volume configuration of the current and recommended resource configuration for a recommendation.
    public struct EbsVolume: Swift.Sendable {
        /// The Amazon Elastic Block Store volume configuration used for recommendations.
        public var configuration: CostOptimizationHubClientTypes.EbsVolumeConfiguration?
        /// Cost impact of the recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.ResourceCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.EbsVolumeConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.ResourceCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The Instance configuration used for recommendations.
    public struct InstanceConfiguration: Swift.Sendable {
        /// Details about the type.
        public var type: Swift.String?

        public init(
            type: Swift.String? = nil
        )
        {
            self.type = type
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The EC2 auto scaling group configuration used for recommendations.
    public struct Ec2AutoScalingGroupConfiguration: Swift.Sendable {
        /// Details about the instance.
        public var instance: CostOptimizationHubClientTypes.InstanceConfiguration?

        public init(
            instance: CostOptimizationHubClientTypes.InstanceConfiguration? = nil
        )
        {
            self.instance = instance
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The EC2 Auto Scaling group recommendation details.
    public struct Ec2AutoScalingGroup: Swift.Sendable {
        /// The EC2 Auto Scaling group configuration used for recommendations.
        public var configuration: CostOptimizationHubClientTypes.Ec2AutoScalingGroupConfiguration?
        /// Cost impact of the recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.ResourceCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.Ec2AutoScalingGroupConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.ResourceCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The EC2 instance configuration used for recommendations.
    public struct Ec2InstanceConfiguration: Swift.Sendable {
        /// Details about the instance.
        public var instance: CostOptimizationHubClientTypes.InstanceConfiguration?

        public init(
            instance: CostOptimizationHubClientTypes.InstanceConfiguration? = nil
        )
        {
            self.instance = instance
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Describes the EC2 instance configuration of the current and recommended resource configuration for a recommendation.
    public struct Ec2Instance: Swift.Sendable {
        /// The EC2 instance configuration used for recommendations.
        public var configuration: CostOptimizationHubClientTypes.Ec2InstanceConfiguration?
        /// Cost impact of the recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.ResourceCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.Ec2InstanceConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.ResourceCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The EC2 instance Savings Plans configuration used for recommendations.
    public struct Ec2InstanceSavingsPlansConfiguration: Swift.Sendable {
        /// The account scope that you want your recommendations for.
        public var accountScope: Swift.String?
        /// The hourly commitment for the Savings Plans type.
        public var hourlyCommitment: Swift.String?
        /// The instance family of the recommended Savings Plan.
        public var instanceFamily: Swift.String?
        /// The payment option for the commitment.
        public var paymentOption: Swift.String?
        /// The Amazon Web Services Region of the commitment.
        public var savingsPlansRegion: Swift.String?
        /// The Savings Plans recommendation term in years.
        public var term: Swift.String?

        public init(
            accountScope: Swift.String? = nil,
            hourlyCommitment: Swift.String? = nil,
            instanceFamily: Swift.String? = nil,
            paymentOption: Swift.String? = nil,
            savingsPlansRegion: Swift.String? = nil,
            term: Swift.String? = nil
        )
        {
            self.accountScope = accountScope
            self.hourlyCommitment = hourlyCommitment
            self.instanceFamily = instanceFamily
            self.paymentOption = paymentOption
            self.savingsPlansRegion = savingsPlansRegion
            self.term = term
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The EC2 instance Savings Plans recommendation details.
    public struct Ec2InstanceSavingsPlans: Swift.Sendable {
        /// The EC2 instance Savings Plans configuration used for recommendations.
        public var configuration: CostOptimizationHubClientTypes.Ec2InstanceSavingsPlansConfiguration?
        /// Cost impact of the Savings Plans purchase recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.SavingsPlansCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.Ec2InstanceSavingsPlansConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.SavingsPlansCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The EC2 reserved instances configuration used for recommendations.
    public struct Ec2ReservedInstancesConfiguration: Swift.Sendable {
        /// The account scope that you want your recommendations for.
        public var accountScope: Swift.String?
        /// Determines whether the recommendation is for a current generation instance.
        public var currentGeneration: Swift.String?
        /// The instance family of the recommended reservation.
        public var instanceFamily: Swift.String?
        /// The type of instance that Amazon Web Services recommends.
        public var instanceType: Swift.String?
        /// How much purchasing reserved instances costs you on a monthly basis.
        public var monthlyRecurringCost: Swift.String?
        /// The number of normalized units that Amazon Web Services recommends that you purchase.
        public var normalizedUnitsToPurchase: Swift.String?
        /// The number of instances that Amazon Web Services recommends that you purchase.
        public var numberOfInstancesToPurchase: Swift.String?
        /// Indicates whether the recommendation is for standard or convertible reservations.
        public var offeringClass: Swift.String?
        /// The payment option for the commitment.
        public var paymentOption: Swift.String?
        /// The platform of the recommended reservation. The platform is the specific combination of operating system, license model, and software on an instance.
        public var platform: Swift.String?
        /// The Amazon Web Services Region of the commitment.
        public var reservedInstancesRegion: Swift.String?
        /// The service that you want your recommendations for.
        public var service: Swift.String?
        /// Determines whether the recommendation is size flexible.
        public var sizeFlexEligible: Swift.Bool?
        /// Determines whether the recommended reservation is dedicated or shared.
        public var tenancy: Swift.String?
        /// The reserved instances recommendation term in years.
        public var term: Swift.String?
        /// How much purchasing this instance costs you upfront.
        public var upfrontCost: Swift.String?

        public init(
            accountScope: Swift.String? = nil,
            currentGeneration: Swift.String? = nil,
            instanceFamily: Swift.String? = nil,
            instanceType: Swift.String? = nil,
            monthlyRecurringCost: Swift.String? = nil,
            normalizedUnitsToPurchase: Swift.String? = nil,
            numberOfInstancesToPurchase: Swift.String? = nil,
            offeringClass: Swift.String? = nil,
            paymentOption: Swift.String? = nil,
            platform: Swift.String? = nil,
            reservedInstancesRegion: Swift.String? = nil,
            service: Swift.String? = nil,
            sizeFlexEligible: Swift.Bool? = nil,
            tenancy: Swift.String? = nil,
            term: Swift.String? = nil,
            upfrontCost: Swift.String? = nil
        )
        {
            self.accountScope = accountScope
            self.currentGeneration = currentGeneration
            self.instanceFamily = instanceFamily
            self.instanceType = instanceType
            self.monthlyRecurringCost = monthlyRecurringCost
            self.normalizedUnitsToPurchase = normalizedUnitsToPurchase
            self.numberOfInstancesToPurchase = numberOfInstancesToPurchase
            self.offeringClass = offeringClass
            self.paymentOption = paymentOption
            self.platform = platform
            self.reservedInstancesRegion = reservedInstancesRegion
            self.service = service
            self.sizeFlexEligible = sizeFlexEligible
            self.tenancy = tenancy
            self.term = term
            self.upfrontCost = upfrontCost
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Pricing details for your recommended reserved instance.
    public struct ReservedInstancesPricing: Swift.Sendable {
        /// The estimated cost of your recurring monthly fees for the recommended reserved instance across the month.
        public var estimatedMonthlyAmortizedReservationCost: Swift.Double?
        /// The remaining On-Demand cost estimated to not be covered by the recommended reserved instance, over the length of the lookback period.
        public var estimatedOnDemandCost: Swift.Double?
        /// The cost of paying for the recommended reserved instance monthly.
        public var monthlyReservationEligibleCost: Swift.Double?
        /// The savings percentage relative to the total On-Demand costs that are associated with this instance.
        public var savingsPercentage: Swift.Double?

        public init(
            estimatedMonthlyAmortizedReservationCost: Swift.Double? = nil,
            estimatedOnDemandCost: Swift.Double? = nil,
            monthlyReservationEligibleCost: Swift.Double? = nil,
            savingsPercentage: Swift.Double? = nil
        )
        {
            self.estimatedMonthlyAmortizedReservationCost = estimatedMonthlyAmortizedReservationCost
            self.estimatedOnDemandCost = estimatedOnDemandCost
            self.monthlyReservationEligibleCost = monthlyReservationEligibleCost
            self.savingsPercentage = savingsPercentage
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Cost impact of the purchase recommendation.
    public struct ReservedInstancesCostCalculation: Swift.Sendable {
        /// Pricing details of the purchase recommendation.
        public var pricing: CostOptimizationHubClientTypes.ReservedInstancesPricing?

        public init(
            pricing: CostOptimizationHubClientTypes.ReservedInstancesPricing? = nil
        )
        {
            self.pricing = pricing
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The EC2 reserved instances recommendation details.
    public struct Ec2ReservedInstances: Swift.Sendable {
        /// The EC2 reserved instances configuration used for recommendations.
        public var configuration: CostOptimizationHubClientTypes.Ec2ReservedInstancesConfiguration?
        /// Cost impact of the purchase recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.Ec2ReservedInstancesConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The ECS service configuration used for recommendations.
    public struct EcsServiceConfiguration: Swift.Sendable {
        /// Details about the compute configuration.
        public var compute: CostOptimizationHubClientTypes.ComputeConfiguration?

        public init(
            compute: CostOptimizationHubClientTypes.ComputeConfiguration? = nil
        )
        {
            self.compute = compute
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The ECS service recommendation details.
    public struct EcsService: Swift.Sendable {
        /// The ECS service configuration used for recommendations.
        public var configuration: CostOptimizationHubClientTypes.EcsServiceConfiguration?
        /// Cost impact of the recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.ResourceCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.EcsServiceConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.ResourceCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The ElastiCache reserved instances configuration used for recommendations.
    public struct ElastiCacheReservedInstancesConfiguration: Swift.Sendable {
        /// The account scope that you want your recommendations for.
        public var accountScope: Swift.String?
        /// Determines whether the recommendation is for a current generation instance.
        public var currentGeneration: Swift.String?
        /// The instance family of the recommended reservation.
        public var instanceFamily: Swift.String?
        /// The type of instance that Amazon Web Services recommends.
        public var instanceType: Swift.String?
        /// How much purchasing reserved instances costs you on a monthly basis.
        public var monthlyRecurringCost: Swift.String?
        /// The number of normalized units that Amazon Web Services recommends that you purchase.
        public var normalizedUnitsToPurchase: Swift.String?
        /// The number of instances that Amazon Web Services recommends that you purchase.
        public var numberOfInstancesToPurchase: Swift.String?
        /// The payment option for the commitment.
        public var paymentOption: Swift.String?
        /// The Amazon Web Services Region of the commitment.
        public var reservedInstancesRegion: Swift.String?
        /// The service that you want your recommendations for.
        public var service: Swift.String?
        /// Determines whether the recommendation is size flexible.
        public var sizeFlexEligible: Swift.Bool?
        /// The reserved instances recommendation term in years.
        public var term: Swift.String?
        /// How much purchasing this instance costs you upfront.
        public var upfrontCost: Swift.String?

        public init(
            accountScope: Swift.String? = nil,
            currentGeneration: Swift.String? = nil,
            instanceFamily: Swift.String? = nil,
            instanceType: Swift.String? = nil,
            monthlyRecurringCost: Swift.String? = nil,
            normalizedUnitsToPurchase: Swift.String? = nil,
            numberOfInstancesToPurchase: Swift.String? = nil,
            paymentOption: Swift.String? = nil,
            reservedInstancesRegion: Swift.String? = nil,
            service: Swift.String? = nil,
            sizeFlexEligible: Swift.Bool? = nil,
            term: Swift.String? = nil,
            upfrontCost: Swift.String? = nil
        )
        {
            self.accountScope = accountScope
            self.currentGeneration = currentGeneration
            self.instanceFamily = instanceFamily
            self.instanceType = instanceType
            self.monthlyRecurringCost = monthlyRecurringCost
            self.normalizedUnitsToPurchase = normalizedUnitsToPurchase
            self.numberOfInstancesToPurchase = numberOfInstancesToPurchase
            self.paymentOption = paymentOption
            self.reservedInstancesRegion = reservedInstancesRegion
            self.service = service
            self.sizeFlexEligible = sizeFlexEligible
            self.term = term
            self.upfrontCost = upfrontCost
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The ElastiCache reserved instances recommendation details.
    public struct ElastiCacheReservedInstances: Swift.Sendable {
        /// The ElastiCache reserved instances configuration used for recommendations.
        public var configuration: CostOptimizationHubClientTypes.ElastiCacheReservedInstancesConfiguration?
        /// Cost impact of the purchase recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.ElastiCacheReservedInstancesConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The Lambda function configuration used for recommendations.
    public struct LambdaFunctionConfiguration: Swift.Sendable {
        /// Details about the compute configuration.
        public var compute: CostOptimizationHubClientTypes.ComputeConfiguration?

        public init(
            compute: CostOptimizationHubClientTypes.ComputeConfiguration? = nil
        )
        {
            self.compute = compute
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The Lambda function recommendation details.
    public struct LambdaFunction: Swift.Sendable {
        /// The Lambda function configuration used for recommendations.
        public var configuration: CostOptimizationHubClientTypes.LambdaFunctionConfiguration?
        /// Cost impact of the recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.ResourceCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.LambdaFunctionConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.ResourceCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The OpenSearch reserved instances configuration used for recommendations.
    public struct OpenSearchReservedInstancesConfiguration: Swift.Sendable {
        /// The account scope that you want your recommendations for.
        public var accountScope: Swift.String?
        /// Determines whether the recommendation is for a current generation instance.
        public var currentGeneration: Swift.String?
        /// The type of instance that Amazon Web Services recommends.
        public var instanceType: Swift.String?
        /// How much purchasing reserved instances costs you on a monthly basis.
        public var monthlyRecurringCost: Swift.String?
        /// The number of normalized units that Amazon Web Services recommends that you purchase.
        public var normalizedUnitsToPurchase: Swift.String?
        /// The number of instances that Amazon Web Services recommends that you purchase.
        public var numberOfInstancesToPurchase: Swift.String?
        /// The payment option for the commitment.
        public var paymentOption: Swift.String?
        /// The Amazon Web Services Region of the commitment.
        public var reservedInstancesRegion: Swift.String?
        /// The service that you want your recommendations for.
        public var service: Swift.String?
        /// Determines whether the recommendation is size flexible.
        public var sizeFlexEligible: Swift.Bool?
        /// The reserved instances recommendation term in years.
        public var term: Swift.String?
        /// How much purchasing this instance costs you upfront.
        public var upfrontCost: Swift.String?

        public init(
            accountScope: Swift.String? = nil,
            currentGeneration: Swift.String? = nil,
            instanceType: Swift.String? = nil,
            monthlyRecurringCost: Swift.String? = nil,
            normalizedUnitsToPurchase: Swift.String? = nil,
            numberOfInstancesToPurchase: Swift.String? = nil,
            paymentOption: Swift.String? = nil,
            reservedInstancesRegion: Swift.String? = nil,
            service: Swift.String? = nil,
            sizeFlexEligible: Swift.Bool? = nil,
            term: Swift.String? = nil,
            upfrontCost: Swift.String? = nil
        )
        {
            self.accountScope = accountScope
            self.currentGeneration = currentGeneration
            self.instanceType = instanceType
            self.monthlyRecurringCost = monthlyRecurringCost
            self.normalizedUnitsToPurchase = normalizedUnitsToPurchase
            self.numberOfInstancesToPurchase = numberOfInstancesToPurchase
            self.paymentOption = paymentOption
            self.reservedInstancesRegion = reservedInstancesRegion
            self.service = service
            self.sizeFlexEligible = sizeFlexEligible
            self.term = term
            self.upfrontCost = upfrontCost
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The OpenSearch reserved instances recommendation details.
    public struct OpenSearchReservedInstances: Swift.Sendable {
        /// The OpenSearch reserved instances configuration used for recommendations.
        public var configuration: CostOptimizationHubClientTypes.OpenSearchReservedInstancesConfiguration?
        /// Cost impact of the purchase recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.OpenSearchReservedInstancesConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The DB instance configuration used for recommendations.
    public struct DbInstanceConfiguration: Swift.Sendable {
        /// The DB instance class of the DB instance.
        public var dbInstanceClass: Swift.String?

        public init(
            dbInstanceClass: Swift.String? = nil
        )
        {
            self.dbInstanceClass = dbInstanceClass
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The Amazon RDS DB instance configuration used for recommendations.
    public struct RdsDbInstanceConfiguration: Swift.Sendable {
        /// Details about the instance configuration.
        public var instance: CostOptimizationHubClientTypes.DbInstanceConfiguration?

        public init(
            instance: CostOptimizationHubClientTypes.DbInstanceConfiguration? = nil
        )
        {
            self.instance = instance
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Contains the details of an Amazon RDS DB instance.
    public struct RdsDbInstance: Swift.Sendable {
        /// The Amazon RDS DB instance configuration used for recommendations.
        public var configuration: CostOptimizationHubClientTypes.RdsDbInstanceConfiguration?
        /// Cost impact of the resource recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.ResourceCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.RdsDbInstanceConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.ResourceCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The Amazon RDS DB instance storage configuration used for recommendations.
    public struct RdsDbInstanceStorageConfiguration: Swift.Sendable {
        /// The new amount of storage in GB to allocate for the DB instance.
        public var allocatedStorageInGb: Swift.Double?
        /// The amount of Provisioned IOPS (input/output operations per second) to be initially allocated for the DB instance.
        public var iops: Swift.Double?
        /// The storage throughput for the DB instance.
        public var storageThroughput: Swift.Double?
        /// The storage type to associate with the DB instance.
        public var storageType: Swift.String?

        public init(
            allocatedStorageInGb: Swift.Double? = nil,
            iops: Swift.Double? = nil,
            storageThroughput: Swift.Double? = nil,
            storageType: Swift.String? = nil
        )
        {
            self.allocatedStorageInGb = allocatedStorageInGb
            self.iops = iops
            self.storageThroughput = storageThroughput
            self.storageType = storageType
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Contains the details of an Amazon RDS DB instance storage.
    public struct RdsDbInstanceStorage: Swift.Sendable {
        /// The Amazon RDS DB instance storage configuration used for recommendations.
        public var configuration: CostOptimizationHubClientTypes.RdsDbInstanceStorageConfiguration?
        /// Cost impact of the resource recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.ResourceCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.RdsDbInstanceStorageConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.ResourceCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The RDS reserved instances configuration used for recommendations.
    public struct RdsReservedInstancesConfiguration: Swift.Sendable {
        /// The account scope that you want your recommendations for.
        public var accountScope: Swift.String?
        /// Determines whether the recommendation is for a current generation instance.
        public var currentGeneration: Swift.String?
        /// The database edition that the recommended reservation supports.
        public var databaseEdition: Swift.String?
        /// The database engine that the recommended reservation supports.
        public var databaseEngine: Swift.String?
        /// Determines whether the recommendation is for a reservation in a single Availability Zone or a reservation with a backup in a second Availability Zone.
        public var deploymentOption: Swift.String?
        /// The instance family of the recommended reservation.
        public var instanceFamily: Swift.String?
        /// The type of instance that Amazon Web Services recommends.
        public var instanceType: Swift.String?
        /// The license model that the recommended reservation supports.
        public var licenseModel: Swift.String?
        /// How much purchasing this instance costs you on a monthly basis.
        public var monthlyRecurringCost: Swift.String?
        /// The number of normalized units that Amazon Web Services recommends that you purchase.
        public var normalizedUnitsToPurchase: Swift.String?
        /// The number of instances that Amazon Web Services recommends that you purchase.
        public var numberOfInstancesToPurchase: Swift.String?
        /// The payment option for the commitment.
        public var paymentOption: Swift.String?
        /// The Amazon Web Services Region of the commitment.
        public var reservedInstancesRegion: Swift.String?
        /// The service that you want your recommendations for.
        public var service: Swift.String?
        /// Determines whether the recommendation is size flexible.
        public var sizeFlexEligible: Swift.Bool?
        /// The reserved instances recommendation term in years.
        public var term: Swift.String?
        /// How much purchasing this instance costs you upfront.
        public var upfrontCost: Swift.String?

        public init(
            accountScope: Swift.String? = nil,
            currentGeneration: Swift.String? = nil,
            databaseEdition: Swift.String? = nil,
            databaseEngine: Swift.String? = nil,
            deploymentOption: Swift.String? = nil,
            instanceFamily: Swift.String? = nil,
            instanceType: Swift.String? = nil,
            licenseModel: Swift.String? = nil,
            monthlyRecurringCost: Swift.String? = nil,
            normalizedUnitsToPurchase: Swift.String? = nil,
            numberOfInstancesToPurchase: Swift.String? = nil,
            paymentOption: Swift.String? = nil,
            reservedInstancesRegion: Swift.String? = nil,
            service: Swift.String? = nil,
            sizeFlexEligible: Swift.Bool? = nil,
            term: Swift.String? = nil,
            upfrontCost: Swift.String? = nil
        )
        {
            self.accountScope = accountScope
            self.currentGeneration = currentGeneration
            self.databaseEdition = databaseEdition
            self.databaseEngine = databaseEngine
            self.deploymentOption = deploymentOption
            self.instanceFamily = instanceFamily
            self.instanceType = instanceType
            self.licenseModel = licenseModel
            self.monthlyRecurringCost = monthlyRecurringCost
            self.normalizedUnitsToPurchase = normalizedUnitsToPurchase
            self.numberOfInstancesToPurchase = numberOfInstancesToPurchase
            self.paymentOption = paymentOption
            self.reservedInstancesRegion = reservedInstancesRegion
            self.service = service
            self.sizeFlexEligible = sizeFlexEligible
            self.term = term
            self.upfrontCost = upfrontCost
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The RDS reserved instances recommendation details.
    public struct RdsReservedInstances: Swift.Sendable {
        /// The RDS reserved instances configuration used for recommendations.
        public var configuration: CostOptimizationHubClientTypes.RdsReservedInstancesConfiguration?
        /// Cost impact of the purchase recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.RdsReservedInstancesConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The Redshift reserved instances configuration used for recommendations.
    public struct RedshiftReservedInstancesConfiguration: Swift.Sendable {
        /// The account scope that you want your recommendations for.
        public var accountScope: Swift.String?
        /// Determines whether the recommendation is for a current generation instance.
        public var currentGeneration: Swift.String?
        /// The instance family of the recommended reservation.
        public var instanceFamily: Swift.String?
        /// The type of instance that Amazon Web Services recommends.
        public var instanceType: Swift.String?
        /// How much purchasing reserved instances costs you on a monthly basis.
        public var monthlyRecurringCost: Swift.String?
        /// The number of normalized units that Amazon Web Services recommends that you purchase.
        public var normalizedUnitsToPurchase: Swift.String?
        /// The number of instances that Amazon Web Services recommends that you purchase.
        public var numberOfInstancesToPurchase: Swift.String?
        /// The payment option for the commitment.
        public var paymentOption: Swift.String?
        /// The Amazon Web Services Region of the commitment.
        public var reservedInstancesRegion: Swift.String?
        /// The service that you want your recommendations for.
        public var service: Swift.String?
        /// Determines whether the recommendation is size flexible.
        public var sizeFlexEligible: Swift.Bool?
        /// The reserved instances recommendation term in years.
        public var term: Swift.String?
        /// How much purchasing this instance costs you upfront.
        public var upfrontCost: Swift.String?

        public init(
            accountScope: Swift.String? = nil,
            currentGeneration: Swift.String? = nil,
            instanceFamily: Swift.String? = nil,
            instanceType: Swift.String? = nil,
            monthlyRecurringCost: Swift.String? = nil,
            normalizedUnitsToPurchase: Swift.String? = nil,
            numberOfInstancesToPurchase: Swift.String? = nil,
            paymentOption: Swift.String? = nil,
            reservedInstancesRegion: Swift.String? = nil,
            service: Swift.String? = nil,
            sizeFlexEligible: Swift.Bool? = nil,
            term: Swift.String? = nil,
            upfrontCost: Swift.String? = nil
        )
        {
            self.accountScope = accountScope
            self.currentGeneration = currentGeneration
            self.instanceFamily = instanceFamily
            self.instanceType = instanceType
            self.monthlyRecurringCost = monthlyRecurringCost
            self.normalizedUnitsToPurchase = normalizedUnitsToPurchase
            self.numberOfInstancesToPurchase = numberOfInstancesToPurchase
            self.paymentOption = paymentOption
            self.reservedInstancesRegion = reservedInstancesRegion
            self.service = service
            self.sizeFlexEligible = sizeFlexEligible
            self.term = term
            self.upfrontCost = upfrontCost
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The Redshift reserved instances recommendation details.
    public struct RedshiftReservedInstances: Swift.Sendable {
        /// The Redshift reserved instances configuration used for recommendations.
        public var configuration: CostOptimizationHubClientTypes.RedshiftReservedInstancesConfiguration?
        /// Cost impact of the purchase recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.RedshiftReservedInstancesConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The SageMaker Savings Plans configuration used for recommendations.
    public struct SageMakerSavingsPlansConfiguration: Swift.Sendable {
        /// The account scope that you want your recommendations for.
        public var accountScope: Swift.String?
        /// The hourly commitment for the Savings Plans type.
        public var hourlyCommitment: Swift.String?
        /// The payment option for the commitment.
        public var paymentOption: Swift.String?
        /// The Savings Plans recommendation term in years.
        public var term: Swift.String?

        public init(
            accountScope: Swift.String? = nil,
            hourlyCommitment: Swift.String? = nil,
            paymentOption: Swift.String? = nil,
            term: Swift.String? = nil
        )
        {
            self.accountScope = accountScope
            self.hourlyCommitment = hourlyCommitment
            self.paymentOption = paymentOption
            self.term = term
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The SageMaker Savings Plans recommendation details.
    public struct SageMakerSavingsPlans: Swift.Sendable {
        /// The SageMaker Savings Plans configuration used for recommendations.
        public var configuration: CostOptimizationHubClientTypes.SageMakerSavingsPlansConfiguration?
        /// Cost impact of the Savings Plans purchase recommendation.
        public var costCalculation: CostOptimizationHubClientTypes.SavingsPlansCostCalculation?

        public init(
            configuration: CostOptimizationHubClientTypes.SageMakerSavingsPlansConfiguration? = nil,
            costCalculation: CostOptimizationHubClientTypes.SavingsPlansCostCalculation? = nil
        )
        {
            self.configuration = configuration
            self.costCalculation = costCalculation
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Contains detailed information about the specified resource.
    public enum ResourceDetails: Swift.Sendable {
        /// The Lambda function recommendation details.
        case lambdafunction(CostOptimizationHubClientTypes.LambdaFunction)
        /// The ECS service recommendation details.
        case ecsservice(CostOptimizationHubClientTypes.EcsService)
        /// The EC2 instance recommendation details.
        case ec2instance(CostOptimizationHubClientTypes.Ec2Instance)
        /// The Amazon Elastic Block Store volume recommendation details.
        case ebsvolume(CostOptimizationHubClientTypes.EbsVolume)
        /// The EC2 Auto Scaling group recommendation details.
        case ec2autoscalinggroup(CostOptimizationHubClientTypes.Ec2AutoScalingGroup)
        /// The EC2 reserved instances recommendation details.
        case ec2reservedinstances(CostOptimizationHubClientTypes.Ec2ReservedInstances)
        /// The RDS reserved instances recommendation details.
        case rdsreservedinstances(CostOptimizationHubClientTypes.RdsReservedInstances)
        /// The ElastiCache reserved instances recommendation details.
        case elasticachereservedinstances(CostOptimizationHubClientTypes.ElastiCacheReservedInstances)
        /// The OpenSearch reserved instances recommendation details.
        case opensearchreservedinstances(CostOptimizationHubClientTypes.OpenSearchReservedInstances)
        /// The Redshift reserved instances recommendation details.
        case redshiftreservedinstances(CostOptimizationHubClientTypes.RedshiftReservedInstances)
        /// The EC2 instance Savings Plans recommendation details.
        case ec2instancesavingsplans(CostOptimizationHubClientTypes.Ec2InstanceSavingsPlans)
        /// The Compute Savings Plans recommendation details.
        case computesavingsplans(CostOptimizationHubClientTypes.ComputeSavingsPlans)
        /// The SageMaker Savings Plans recommendation details.
        case sagemakersavingsplans(CostOptimizationHubClientTypes.SageMakerSavingsPlans)
        /// The DB instance recommendation details.
        case rdsdbinstance(CostOptimizationHubClientTypes.RdsDbInstance)
        /// The DB instance storage recommendation details.
        case rdsdbinstancestorage(CostOptimizationHubClientTypes.RdsDbInstanceStorage)
        case sdkUnknown(Swift.String)
    }
}

extension CostOptimizationHubClientTypes {

    public enum ResourceType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case computeSavingsPlans
        case ebsVolume
        case ec2AutoScalingGroup
        case ec2Instance
        case ec2InstanceSavingsPlans
        case ec2ReservedInstances
        case ecsService
        case elastiCacheReservedInstances
        case lambdaFunction
        case openSearchReservedInstances
        case rdsDbInstance
        case rdsDbInstanceStorage
        case rdsReservedInstances
        case redshiftReservedInstances
        case sageMakerSavingsPlans
        case sdkUnknown(Swift.String)

        public static var allCases: [ResourceType] {
            return [
                .computeSavingsPlans,
                .ebsVolume,
                .ec2AutoScalingGroup,
                .ec2Instance,
                .ec2InstanceSavingsPlans,
                .ec2ReservedInstances,
                .ecsService,
                .elastiCacheReservedInstances,
                .lambdaFunction,
                .openSearchReservedInstances,
                .rdsDbInstance,
                .rdsDbInstanceStorage,
                .rdsReservedInstances,
                .redshiftReservedInstances,
                .sageMakerSavingsPlans
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .computeSavingsPlans: return "ComputeSavingsPlans"
            case .ebsVolume: return "EbsVolume"
            case .ec2AutoScalingGroup: return "Ec2AutoScalingGroup"
            case .ec2Instance: return "Ec2Instance"
            case .ec2InstanceSavingsPlans: return "Ec2InstanceSavingsPlans"
            case .ec2ReservedInstances: return "Ec2ReservedInstances"
            case .ecsService: return "EcsService"
            case .elastiCacheReservedInstances: return "ElastiCacheReservedInstances"
            case .lambdaFunction: return "LambdaFunction"
            case .openSearchReservedInstances: return "OpenSearchReservedInstances"
            case .rdsDbInstance: return "RdsDbInstance"
            case .rdsDbInstanceStorage: return "RdsDbInstanceStorage"
            case .rdsReservedInstances: return "RdsReservedInstances"
            case .redshiftReservedInstances: return "RedshiftReservedInstances"
            case .sageMakerSavingsPlans: return "SageMakerSavingsPlans"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CostOptimizationHubClientTypes {

    public enum ImplementationEffort: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case high
        case low
        case medium
        case veryHigh
        case veryLow
        case sdkUnknown(Swift.String)

        public static var allCases: [ImplementationEffort] {
            return [
                .high,
                .low,
                .medium,
                .veryHigh,
                .veryLow
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .high: return "High"
            case .low: return "Low"
            case .medium: return "Medium"
            case .veryHigh: return "VeryHigh"
            case .veryLow: return "VeryLow"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CostOptimizationHubClientTypes {

    public enum Source: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case computeOptimizer
        case costExplorer
        case sdkUnknown(Swift.String)

        public static var allCases: [Source] {
            return [
                .computeOptimizer,
                .costExplorer
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .computeOptimizer: return "ComputeOptimizer"
            case .costExplorer: return "CostExplorer"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The tag structure that contains a tag key and value.
    public struct Tag: Swift.Sendable {
        /// The key that's associated with the tag.
        public var key: Swift.String?
        /// The value that's associated with the tag.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

public struct GetRecommendationOutput: Swift.Sendable {
    /// The account that the recommendation is for.
    public var accountId: Swift.String?
    /// The type of action you can take by adopting the recommendation.
    public var actionType: CostOptimizationHubClientTypes.ActionType?
    /// The lookback period used to calculate cost impact for a recommendation.
    public var costCalculationLookbackPeriodInDays: Swift.Int?
    /// The currency code used for the recommendation.
    public var currencyCode: Swift.String?
    /// The details for the resource.
    public var currentResourceDetails: CostOptimizationHubClientTypes.ResourceDetails?
    /// The type of resource.
    public var currentResourceType: CostOptimizationHubClientTypes.ResourceType?
    /// The estimated monthly cost of the current resource. For Reserved Instances and Savings Plans, it refers to the cost for eligible usage.
    public var estimatedMonthlyCost: Swift.Double?
    /// The estimated monthly savings amount for the recommendation.
    public var estimatedMonthlySavings: Swift.Double?
    /// The estimated savings amount over the lookback period used to calculate cost impact for a recommendation.
    public var estimatedSavingsOverCostCalculationLookbackPeriod: Swift.Double?
    /// The estimated savings percentage relative to the total cost over the cost calculation lookback period.
    public var estimatedSavingsPercentage: Swift.Double?
    /// The effort required to implement the recommendation.
    public var implementationEffort: CostOptimizationHubClientTypes.ImplementationEffort?
    /// The time when the recommendation was last generated.
    public var lastRefreshTimestamp: Foundation.Date?
    /// The ID for the recommendation.
    public var recommendationId: Swift.String?
    /// The lookback period that's used to generate the recommendation.
    public var recommendationLookbackPeriodInDays: Swift.Int?
    /// The details about the recommended resource.
    public var recommendedResourceDetails: CostOptimizationHubClientTypes.ResourceDetails?
    /// The resource type of the recommendation.
    public var recommendedResourceType: CostOptimizationHubClientTypes.ResourceType?
    /// The Amazon Web Services Region of the resource.
    public var region: Swift.String?
    /// The Amazon Resource Name (ARN) of the resource.
    public var resourceArn: Swift.String?
    /// The unique identifier for the resource. This is the same as the Amazon Resource Name (ARN), if available.
    public var resourceId: Swift.String?
    /// Whether or not implementing the recommendation requires a restart.
    public var restartNeeded: Swift.Bool?
    /// Whether or not implementing the recommendation can be rolled back.
    public var rollbackPossible: Swift.Bool?
    /// The source of the recommendation.
    public var source: CostOptimizationHubClientTypes.Source?
    /// A list of tags associated with the resource for which the recommendation exists.
    public var tags: [CostOptimizationHubClientTypes.Tag]?

    public init(
        accountId: Swift.String? = nil,
        actionType: CostOptimizationHubClientTypes.ActionType? = nil,
        costCalculationLookbackPeriodInDays: Swift.Int? = nil,
        currencyCode: Swift.String? = nil,
        currentResourceDetails: CostOptimizationHubClientTypes.ResourceDetails? = nil,
        currentResourceType: CostOptimizationHubClientTypes.ResourceType? = nil,
        estimatedMonthlyCost: Swift.Double? = nil,
        estimatedMonthlySavings: Swift.Double? = nil,
        estimatedSavingsOverCostCalculationLookbackPeriod: Swift.Double? = nil,
        estimatedSavingsPercentage: Swift.Double? = nil,
        implementationEffort: CostOptimizationHubClientTypes.ImplementationEffort? = nil,
        lastRefreshTimestamp: Foundation.Date? = nil,
        recommendationId: Swift.String? = nil,
        recommendationLookbackPeriodInDays: Swift.Int? = nil,
        recommendedResourceDetails: CostOptimizationHubClientTypes.ResourceDetails? = nil,
        recommendedResourceType: CostOptimizationHubClientTypes.ResourceType? = nil,
        region: Swift.String? = nil,
        resourceArn: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        restartNeeded: Swift.Bool? = nil,
        rollbackPossible: Swift.Bool? = nil,
        source: CostOptimizationHubClientTypes.Source? = nil,
        tags: [CostOptimizationHubClientTypes.Tag]? = nil
    )
    {
        self.accountId = accountId
        self.actionType = actionType
        self.costCalculationLookbackPeriodInDays = costCalculationLookbackPeriodInDays
        self.currencyCode = currencyCode
        self.currentResourceDetails = currentResourceDetails
        self.currentResourceType = currentResourceType
        self.estimatedMonthlyCost = estimatedMonthlyCost
        self.estimatedMonthlySavings = estimatedMonthlySavings
        self.estimatedSavingsOverCostCalculationLookbackPeriod = estimatedSavingsOverCostCalculationLookbackPeriod
        self.estimatedSavingsPercentage = estimatedSavingsPercentage
        self.implementationEffort = implementationEffort
        self.lastRefreshTimestamp = lastRefreshTimestamp
        self.recommendationId = recommendationId
        self.recommendationLookbackPeriodInDays = recommendationLookbackPeriodInDays
        self.recommendedResourceDetails = recommendedResourceDetails
        self.recommendedResourceType = recommendedResourceType
        self.region = region
        self.resourceArn = resourceArn
        self.resourceId = resourceId
        self.restartNeeded = restartNeeded
        self.rollbackPossible = rollbackPossible
        self.source = source
        self.tags = tags
    }
}

public struct ListEnrollmentStatusesInput: Swift.Sendable {
    /// The account ID of a member account in the organization.
    public var accountId: Swift.String?
    /// Indicates whether to return the enrollment status for the organization.
    public var includeOrganizationInfo: Swift.Bool?
    /// The maximum number of objects that are returned for the request.
    public var maxResults: Swift.Int?
    /// The token to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        accountId: Swift.String? = nil,
        includeOrganizationInfo: Swift.Bool? = false,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.accountId = accountId
        self.includeOrganizationInfo = includeOrganizationInfo
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListEnrollmentStatusesOutput: Swift.Sendable {
    /// The enrollment status of all member accounts in the organization if the account is the management account or delegated administrator.
    public var includeMemberAccounts: Swift.Bool?
    /// The enrollment status of a specific account ID, including creation and last updated timestamps.
    public var items: [CostOptimizationHubClientTypes.AccountEnrollmentStatus]?
    /// The token to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        includeMemberAccounts: Swift.Bool? = nil,
        items: [CostOptimizationHubClientTypes.AccountEnrollmentStatus]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.includeMemberAccounts = includeMemberAccounts
        self.items = items
        self.nextToken = nextToken
    }
}

extension CostOptimizationHubClientTypes {

    /// Describes a filter that returns a more specific list of recommendations. Filters recommendations by different dimensions.
    public struct Filter: Swift.Sendable {
        /// The account that the recommendation is for.
        public var accountIds: [Swift.String]?
        /// The type of action you can take by adopting the recommendation.
        public var actionTypes: [CostOptimizationHubClientTypes.ActionType]?
        /// The effort required to implement the recommendation.
        public var implementationEfforts: [CostOptimizationHubClientTypes.ImplementationEffort]?
        /// The IDs for the recommendations.
        public var recommendationIds: [Swift.String]?
        /// The Amazon Web Services Region of the resource.
        public var regions: [Swift.String]?
        /// The Amazon Resource Name (ARN) of the recommendation.
        public var resourceArns: [Swift.String]?
        /// The resource ID of the recommendation.
        public var resourceIds: [Swift.String]?
        /// The resource type of the recommendation.
        public var resourceTypes: [CostOptimizationHubClientTypes.ResourceType]?
        /// Whether or not implementing the recommendation requires a restart.
        public var restartNeeded: Swift.Bool?
        /// Whether or not implementing the recommendation can be rolled back.
        public var rollbackPossible: Swift.Bool?
        /// A list of tags assigned to the recommendation.
        public var tags: [CostOptimizationHubClientTypes.Tag]?

        public init(
            accountIds: [Swift.String]? = nil,
            actionTypes: [CostOptimizationHubClientTypes.ActionType]? = nil,
            implementationEfforts: [CostOptimizationHubClientTypes.ImplementationEffort]? = nil,
            recommendationIds: [Swift.String]? = nil,
            regions: [Swift.String]? = nil,
            resourceArns: [Swift.String]? = nil,
            resourceIds: [Swift.String]? = nil,
            resourceTypes: [CostOptimizationHubClientTypes.ResourceType]? = nil,
            restartNeeded: Swift.Bool? = nil,
            rollbackPossible: Swift.Bool? = nil,
            tags: [CostOptimizationHubClientTypes.Tag]? = nil
        )
        {
            self.accountIds = accountIds
            self.actionTypes = actionTypes
            self.implementationEfforts = implementationEfforts
            self.recommendationIds = recommendationIds
            self.regions = regions
            self.resourceArns = resourceArns
            self.resourceIds = resourceIds
            self.resourceTypes = resourceTypes
            self.restartNeeded = restartNeeded
            self.rollbackPossible = rollbackPossible
            self.tags = tags
        }
    }
}

extension CostOptimizationHubClientTypes {

    public enum Order: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case asc
        case desc
        case sdkUnknown(Swift.String)

        public static var allCases: [Order] {
            return [
                .asc,
                .desc
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .asc: return "Asc"
            case .desc: return "Desc"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// Defines how rows will be sorted in the response.
    public struct OrderBy: Swift.Sendable {
        /// Sorts by dimension values.
        public var dimension: Swift.String?
        /// The order that's used to sort the data.
        public var order: CostOptimizationHubClientTypes.Order?

        public init(
            dimension: Swift.String? = nil,
            order: CostOptimizationHubClientTypes.Order? = nil
        )
        {
            self.dimension = dimension
            self.order = order
        }
    }
}

public struct ListRecommendationsInput: Swift.Sendable {
    /// The constraints that you want all returned recommendations to match.
    public var filter: CostOptimizationHubClientTypes.Filter?
    /// List of all recommendations for a resource, or a single recommendation if de-duped by resourceId.
    public var includeAllRecommendations: Swift.Bool?
    /// The maximum number of recommendations that are returned for the request.
    public var maxResults: Swift.Int?
    /// The token to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// The ordering of recommendations by a dimension.
    public var orderBy: CostOptimizationHubClientTypes.OrderBy?

    public init(
        filter: CostOptimizationHubClientTypes.Filter? = nil,
        includeAllRecommendations: Swift.Bool? = false,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        orderBy: CostOptimizationHubClientTypes.OrderBy? = nil
    )
    {
        self.filter = filter
        self.includeAllRecommendations = includeAllRecommendations
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.orderBy = orderBy
    }
}

extension CostOptimizationHubClientTypes {

    /// Describes a recommendation.
    public struct Recommendation: Swift.Sendable {
        /// The account that the recommendation is for.
        public var accountId: Swift.String?
        /// The type of tasks that can be carried out by this action.
        public var actionType: Swift.String?
        /// The currency code used for the recommendation.
        public var currencyCode: Swift.String?
        /// Describes the current resource.
        public var currentResourceSummary: Swift.String?
        /// The current resource type.
        public var currentResourceType: Swift.String?
        /// The estimated monthly cost of the current resource. For Reserved Instances and Savings Plans, it refers to the cost for eligible usage.
        public var estimatedMonthlyCost: Swift.Double?
        /// The estimated monthly savings amount for the recommendation.
        public var estimatedMonthlySavings: Swift.Double?
        /// The estimated savings percentage relative to the total cost over the cost calculation lookback period.
        public var estimatedSavingsPercentage: Swift.Double?
        /// The effort required to implement the recommendation.
        public var implementationEffort: Swift.String?
        /// The time when the recommendation was last generated.
        public var lastRefreshTimestamp: Foundation.Date?
        /// The ID for the recommendation.
        public var recommendationId: Swift.String?
        /// The lookback period that's used to generate the recommendation.
        public var recommendationLookbackPeriodInDays: Swift.Int?
        /// Describes the recommended resource.
        public var recommendedResourceSummary: Swift.String?
        /// The recommended resource type.
        public var recommendedResourceType: Swift.String?
        /// The Amazon Web Services Region of the resource.
        public var region: Swift.String?
        /// The Amazon Resource Name (ARN) for the recommendation.
        public var resourceArn: Swift.String?
        /// The resource ID for the recommendation.
        public var resourceId: Swift.String?
        /// Whether or not implementing the recommendation requires a restart.
        public var restartNeeded: Swift.Bool?
        /// Whether or not implementing the recommendation can be rolled back.
        public var rollbackPossible: Swift.Bool?
        /// The source of the recommendation.
        public var source: CostOptimizationHubClientTypes.Source?
        /// A list of tags assigned to the recommendation.
        public var tags: [CostOptimizationHubClientTypes.Tag]?

        public init(
            accountId: Swift.String? = nil,
            actionType: Swift.String? = nil,
            currencyCode: Swift.String? = nil,
            currentResourceSummary: Swift.String? = nil,
            currentResourceType: Swift.String? = nil,
            estimatedMonthlyCost: Swift.Double? = nil,
            estimatedMonthlySavings: Swift.Double? = nil,
            estimatedSavingsPercentage: Swift.Double? = nil,
            implementationEffort: Swift.String? = nil,
            lastRefreshTimestamp: Foundation.Date? = nil,
            recommendationId: Swift.String? = nil,
            recommendationLookbackPeriodInDays: Swift.Int? = nil,
            recommendedResourceSummary: Swift.String? = nil,
            recommendedResourceType: Swift.String? = nil,
            region: Swift.String? = nil,
            resourceArn: Swift.String? = nil,
            resourceId: Swift.String? = nil,
            restartNeeded: Swift.Bool? = nil,
            rollbackPossible: Swift.Bool? = nil,
            source: CostOptimizationHubClientTypes.Source? = nil,
            tags: [CostOptimizationHubClientTypes.Tag]? = nil
        )
        {
            self.accountId = accountId
            self.actionType = actionType
            self.currencyCode = currencyCode
            self.currentResourceSummary = currentResourceSummary
            self.currentResourceType = currentResourceType
            self.estimatedMonthlyCost = estimatedMonthlyCost
            self.estimatedMonthlySavings = estimatedMonthlySavings
            self.estimatedSavingsPercentage = estimatedSavingsPercentage
            self.implementationEffort = implementationEffort
            self.lastRefreshTimestamp = lastRefreshTimestamp
            self.recommendationId = recommendationId
            self.recommendationLookbackPeriodInDays = recommendationLookbackPeriodInDays
            self.recommendedResourceSummary = recommendedResourceSummary
            self.recommendedResourceType = recommendedResourceType
            self.region = region
            self.resourceArn = resourceArn
            self.resourceId = resourceId
            self.restartNeeded = restartNeeded
            self.rollbackPossible = rollbackPossible
            self.source = source
            self.tags = tags
        }
    }
}

public struct ListRecommendationsOutput: Swift.Sendable {
    /// List of all savings recommendations.
    public var items: [CostOptimizationHubClientTypes.Recommendation]?
    /// The token to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        items: [CostOptimizationHubClientTypes.Recommendation]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

extension CostOptimizationHubClientTypes {

    public enum SummaryMetrics: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case savingsPercentage
        case sdkUnknown(Swift.String)

        public static var allCases: [SummaryMetrics] {
            return [
                .savingsPercentage
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .savingsPercentage: return "SavingsPercentage"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct ListRecommendationSummariesInput: Swift.Sendable {
    /// Describes a filter that returns a more specific list of recommendations. Filters recommendations by different dimensions.
    public var filter: CostOptimizationHubClientTypes.Filter?
    /// The grouping of recommendations by a dimension.
    /// This member is required.
    public var groupBy: Swift.String?
    /// The maximum number of recommendations to be returned for the request.
    public var maxResults: Swift.Int?
    /// Additional metrics to be returned for the request. The only valid value is savingsPercentage.
    public var metrics: [CostOptimizationHubClientTypes.SummaryMetrics]?
    /// The token to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        filter: CostOptimizationHubClientTypes.Filter? = nil,
        groupBy: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        metrics: [CostOptimizationHubClientTypes.SummaryMetrics]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filter = filter
        self.groupBy = groupBy
        self.maxResults = maxResults
        self.metrics = metrics
        self.nextToken = nextToken
    }
}

extension CostOptimizationHubClientTypes {

    /// The summary of rightsizing recommendations, including de-duped savings from all types of recommendations.
    public struct RecommendationSummary: Swift.Sendable {
        /// The estimated total savings resulting from modifications, on a monthly basis.
        public var estimatedMonthlySavings: Swift.Double?
        /// The grouping of recommendations.
        public var group: Swift.String?
        /// The total number of instance recommendations.
        public var recommendationCount: Swift.Int?

        public init(
            estimatedMonthlySavings: Swift.Double? = nil,
            group: Swift.String? = nil,
            recommendationCount: Swift.Int? = nil
        )
        {
            self.estimatedMonthlySavings = estimatedMonthlySavings
            self.group = group
            self.recommendationCount = recommendationCount
        }
    }
}

extension CostOptimizationHubClientTypes {

    /// The results or descriptions for the additional metrics, based on whether the metrics were or were not requested.
    public struct SummaryMetricsResult: Swift.Sendable {
        /// The savings percentage based on your Amazon Web Services spend over the past 30 days. Savings percentage is only supported when filtering by Region, account ID, or tags.
        public var savingsPercentage: Swift.String?

        public init(
            savingsPercentage: Swift.String? = nil
        )
        {
            self.savingsPercentage = savingsPercentage
        }
    }
}

public struct ListRecommendationSummariesOutput: Swift.Sendable {
    /// The currency code used for the recommendation.
    public var currencyCode: Swift.String?
    /// The total overall savings for the aggregated view.
    public var estimatedTotalDedupedSavings: Swift.Double?
    /// The dimension used to group the recommendations by.
    public var groupBy: Swift.String?
    /// A list of all savings recommendations.
    public var items: [CostOptimizationHubClientTypes.RecommendationSummary]?
    /// The results or descriptions for the additional metrics, based on whether the metrics were or were not requested.
    public var metrics: CostOptimizationHubClientTypes.SummaryMetricsResult?
    /// The token to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        currencyCode: Swift.String? = nil,
        estimatedTotalDedupedSavings: Swift.Double? = nil,
        groupBy: Swift.String? = nil,
        items: [CostOptimizationHubClientTypes.RecommendationSummary]? = nil,
        metrics: CostOptimizationHubClientTypes.SummaryMetricsResult? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.currencyCode = currencyCode
        self.estimatedTotalDedupedSavings = estimatedTotalDedupedSavings
        self.groupBy = groupBy
        self.items = items
        self.metrics = metrics
        self.nextToken = nextToken
    }
}

public struct UpdateEnrollmentStatusInput: Swift.Sendable {
    /// Indicates whether to enroll member accounts of the organization if the account is the management account or delegated administrator.
    public var includeMemberAccounts: Swift.Bool?
    /// Sets the account status.
    /// This member is required.
    public var status: CostOptimizationHubClientTypes.EnrollmentStatus?

    public init(
        includeMemberAccounts: Swift.Bool? = nil,
        status: CostOptimizationHubClientTypes.EnrollmentStatus? = nil
    )
    {
        self.includeMemberAccounts = includeMemberAccounts
        self.status = status
    }
}

public struct UpdateEnrollmentStatusOutput: Swift.Sendable {
    /// The enrollment status of the account.
    public var status: Swift.String?

    public init(
        status: Swift.String? = nil
    )
    {
        self.status = status
    }
}

public struct UpdatePreferencesInput: Swift.Sendable {
    /// Sets the "member account discount visibility" preference.
    public var memberAccountDiscountVisibility: CostOptimizationHubClientTypes.MemberAccountDiscountVisibility?
    /// Sets the "savings estimation mode" preference.
    public var savingsEstimationMode: CostOptimizationHubClientTypes.SavingsEstimationMode?

    public init(
        memberAccountDiscountVisibility: CostOptimizationHubClientTypes.MemberAccountDiscountVisibility? = nil,
        savingsEstimationMode: CostOptimizationHubClientTypes.SavingsEstimationMode? = nil
    )
    {
        self.memberAccountDiscountVisibility = memberAccountDiscountVisibility
        self.savingsEstimationMode = savingsEstimationMode
    }
}

public struct UpdatePreferencesOutput: Swift.Sendable {
    /// Shows the status of the "member account discount visibility" preference.
    public var memberAccountDiscountVisibility: CostOptimizationHubClientTypes.MemberAccountDiscountVisibility?
    /// Shows the status of the "savings estimation mode" preference.
    public var savingsEstimationMode: CostOptimizationHubClientTypes.SavingsEstimationMode?

    public init(
        memberAccountDiscountVisibility: CostOptimizationHubClientTypes.MemberAccountDiscountVisibility? = nil,
        savingsEstimationMode: CostOptimizationHubClientTypes.SavingsEstimationMode? = nil
    )
    {
        self.memberAccountDiscountVisibility = memberAccountDiscountVisibility
        self.savingsEstimationMode = savingsEstimationMode
    }
}

extension GetPreferencesInput {

    static func urlPathProvider(_ value: GetPreferencesInput) -> Swift.String? {
        return "/"
    }
}

extension GetRecommendationInput {

    static func urlPathProvider(_ value: GetRecommendationInput) -> Swift.String? {
        return "/"
    }
}

extension ListEnrollmentStatusesInput {

    static func urlPathProvider(_ value: ListEnrollmentStatusesInput) -> Swift.String? {
        return "/"
    }
}

extension ListRecommendationsInput {

    static func urlPathProvider(_ value: ListRecommendationsInput) -> Swift.String? {
        return "/"
    }
}

extension ListRecommendationSummariesInput {

    static func urlPathProvider(_ value: ListRecommendationSummariesInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateEnrollmentStatusInput {

    static func urlPathProvider(_ value: UpdateEnrollmentStatusInput) -> Swift.String? {
        return "/"
    }
}

extension UpdatePreferencesInput {

    static func urlPathProvider(_ value: UpdatePreferencesInput) -> Swift.String? {
        return "/"
    }
}

extension GetPreferencesInput {

    static func write(value: GetPreferencesInput?, to writer: SmithyJSON.Writer) throws {
        guard value != nil else { return }
        _ = writer[""]  // create an empty structure
    }
}

extension GetRecommendationInput {

    static func write(value: GetRecommendationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["recommendationId"].write(value.recommendationId)
    }
}

extension ListEnrollmentStatusesInput {

    static func write(value: ListEnrollmentStatusesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["accountId"].write(value.accountId)
        try writer["includeOrganizationInfo"].write(value.includeOrganizationInfo)
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension ListRecommendationsInput {

    static func write(value: ListRecommendationsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filter"].write(value.filter, with: CostOptimizationHubClientTypes.Filter.write(value:to:))
        try writer["includeAllRecommendations"].write(value.includeAllRecommendations)
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["orderBy"].write(value.orderBy, with: CostOptimizationHubClientTypes.OrderBy.write(value:to:))
    }
}

extension ListRecommendationSummariesInput {

    static func write(value: ListRecommendationSummariesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filter"].write(value.filter, with: CostOptimizationHubClientTypes.Filter.write(value:to:))
        try writer["groupBy"].write(value.groupBy)
        try writer["maxResults"].write(value.maxResults)
        try writer["metrics"].writeList(value.metrics, memberWritingClosure: SmithyReadWrite.WritingClosureBox<CostOptimizationHubClientTypes.SummaryMetrics>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension UpdateEnrollmentStatusInput {

    static func write(value: UpdateEnrollmentStatusInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["includeMemberAccounts"].write(value.includeMemberAccounts)
        try writer["status"].write(value.status)
    }
}

extension UpdatePreferencesInput {

    static func write(value: UpdatePreferencesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["memberAccountDiscountVisibility"].write(value.memberAccountDiscountVisibility)
        try writer["savingsEstimationMode"].write(value.savingsEstimationMode)
    }
}

extension GetPreferencesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetPreferencesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetPreferencesOutput()
        value.memberAccountDiscountVisibility = try reader["memberAccountDiscountVisibility"].readIfPresent()
        value.savingsEstimationMode = try reader["savingsEstimationMode"].readIfPresent()
        return value
    }
}

extension GetRecommendationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetRecommendationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetRecommendationOutput()
        value.accountId = try reader["accountId"].readIfPresent()
        value.actionType = try reader["actionType"].readIfPresent()
        value.costCalculationLookbackPeriodInDays = try reader["costCalculationLookbackPeriodInDays"].readIfPresent()
        value.currencyCode = try reader["currencyCode"].readIfPresent()
        value.currentResourceDetails = try reader["currentResourceDetails"].readIfPresent(with: CostOptimizationHubClientTypes.ResourceDetails.read(from:))
        value.currentResourceType = try reader["currentResourceType"].readIfPresent()
        value.estimatedMonthlyCost = try reader["estimatedMonthlyCost"].readIfPresent()
        value.estimatedMonthlySavings = try reader["estimatedMonthlySavings"].readIfPresent()
        value.estimatedSavingsOverCostCalculationLookbackPeriod = try reader["estimatedSavingsOverCostCalculationLookbackPeriod"].readIfPresent()
        value.estimatedSavingsPercentage = try reader["estimatedSavingsPercentage"].readIfPresent()
        value.implementationEffort = try reader["implementationEffort"].readIfPresent()
        value.lastRefreshTimestamp = try reader["lastRefreshTimestamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.recommendationId = try reader["recommendationId"].readIfPresent()
        value.recommendationLookbackPeriodInDays = try reader["recommendationLookbackPeriodInDays"].readIfPresent()
        value.recommendedResourceDetails = try reader["recommendedResourceDetails"].readIfPresent(with: CostOptimizationHubClientTypes.ResourceDetails.read(from:))
        value.recommendedResourceType = try reader["recommendedResourceType"].readIfPresent()
        value.region = try reader["region"].readIfPresent()
        value.resourceArn = try reader["resourceArn"].readIfPresent()
        value.resourceId = try reader["resourceId"].readIfPresent()
        value.restartNeeded = try reader["restartNeeded"].readIfPresent()
        value.rollbackPossible = try reader["rollbackPossible"].readIfPresent()
        value.source = try reader["source"].readIfPresent()
        value.tags = try reader["tags"].readListIfPresent(memberReadingClosure: CostOptimizationHubClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListEnrollmentStatusesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListEnrollmentStatusesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListEnrollmentStatusesOutput()
        value.includeMemberAccounts = try reader["includeMemberAccounts"].readIfPresent()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: CostOptimizationHubClientTypes.AccountEnrollmentStatus.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListRecommendationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRecommendationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRecommendationsOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: CostOptimizationHubClientTypes.Recommendation.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListRecommendationSummariesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRecommendationSummariesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRecommendationSummariesOutput()
        value.currencyCode = try reader["currencyCode"].readIfPresent()
        value.estimatedTotalDedupedSavings = try reader["estimatedTotalDedupedSavings"].readIfPresent()
        value.groupBy = try reader["groupBy"].readIfPresent()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: CostOptimizationHubClientTypes.RecommendationSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.metrics = try reader["metrics"].readIfPresent(with: CostOptimizationHubClientTypes.SummaryMetricsResult.read(from:))
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension UpdateEnrollmentStatusOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateEnrollmentStatusOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateEnrollmentStatusOutput()
        value.status = try reader["status"].readIfPresent()
        return value
    }
}

extension UpdatePreferencesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdatePreferencesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdatePreferencesOutput()
        value.memberAccountDiscountVisibility = try reader["memberAccountDiscountVisibility"].readIfPresent()
        value.savingsEstimationMode = try reader["savingsEstimationMode"].readIfPresent()
        return value
    }
}

enum GetPreferencesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetRecommendationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListEnrollmentStatusesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRecommendationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRecommendationSummariesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateEnrollmentStatusOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdatePreferencesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        var value = ThrottlingException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.fields = try reader["fields"].readListIfPresent(memberReadingClosure: CostOptimizationHubClientTypes.ValidationExceptionDetail.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.reason = try reader["reason"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.resourceId = try reader["resourceId"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CostOptimizationHubClientTypes.ResourceDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.ResourceDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "lambdaFunction":
                return .lambdafunction(try reader["lambdaFunction"].read(with: CostOptimizationHubClientTypes.LambdaFunction.read(from:)))
            case "ecsService":
                return .ecsservice(try reader["ecsService"].read(with: CostOptimizationHubClientTypes.EcsService.read(from:)))
            case "ec2Instance":
                return .ec2instance(try reader["ec2Instance"].read(with: CostOptimizationHubClientTypes.Ec2Instance.read(from:)))
            case "ebsVolume":
                return .ebsvolume(try reader["ebsVolume"].read(with: CostOptimizationHubClientTypes.EbsVolume.read(from:)))
            case "ec2AutoScalingGroup":
                return .ec2autoscalinggroup(try reader["ec2AutoScalingGroup"].read(with: CostOptimizationHubClientTypes.Ec2AutoScalingGroup.read(from:)))
            case "ec2ReservedInstances":
                return .ec2reservedinstances(try reader["ec2ReservedInstances"].read(with: CostOptimizationHubClientTypes.Ec2ReservedInstances.read(from:)))
            case "rdsReservedInstances":
                return .rdsreservedinstances(try reader["rdsReservedInstances"].read(with: CostOptimizationHubClientTypes.RdsReservedInstances.read(from:)))
            case "elastiCacheReservedInstances":
                return .elasticachereservedinstances(try reader["elastiCacheReservedInstances"].read(with: CostOptimizationHubClientTypes.ElastiCacheReservedInstances.read(from:)))
            case "openSearchReservedInstances":
                return .opensearchreservedinstances(try reader["openSearchReservedInstances"].read(with: CostOptimizationHubClientTypes.OpenSearchReservedInstances.read(from:)))
            case "redshiftReservedInstances":
                return .redshiftreservedinstances(try reader["redshiftReservedInstances"].read(with: CostOptimizationHubClientTypes.RedshiftReservedInstances.read(from:)))
            case "ec2InstanceSavingsPlans":
                return .ec2instancesavingsplans(try reader["ec2InstanceSavingsPlans"].read(with: CostOptimizationHubClientTypes.Ec2InstanceSavingsPlans.read(from:)))
            case "computeSavingsPlans":
                return .computesavingsplans(try reader["computeSavingsPlans"].read(with: CostOptimizationHubClientTypes.ComputeSavingsPlans.read(from:)))
            case "sageMakerSavingsPlans":
                return .sagemakersavingsplans(try reader["sageMakerSavingsPlans"].read(with: CostOptimizationHubClientTypes.SageMakerSavingsPlans.read(from:)))
            case "rdsDbInstance":
                return .rdsdbinstance(try reader["rdsDbInstance"].read(with: CostOptimizationHubClientTypes.RdsDbInstance.read(from:)))
            case "rdsDbInstanceStorage":
                return .rdsdbinstancestorage(try reader["rdsDbInstanceStorage"].read(with: CostOptimizationHubClientTypes.RdsDbInstanceStorage.read(from:)))
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension CostOptimizationHubClientTypes.RdsDbInstanceStorage {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.RdsDbInstanceStorage {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.RdsDbInstanceStorage()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.RdsDbInstanceStorageConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.ResourceCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.ResourceCostCalculation {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.ResourceCostCalculation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.ResourceCostCalculation()
        value.usages = try reader["usages"].readListIfPresent(memberReadingClosure: CostOptimizationHubClientTypes.Usage.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.pricing = try reader["pricing"].readIfPresent(with: CostOptimizationHubClientTypes.ResourcePricing.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.ResourcePricing {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.ResourcePricing {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.ResourcePricing()
        value.estimatedCostBeforeDiscounts = try reader["estimatedCostBeforeDiscounts"].readIfPresent()
        value.estimatedNetUnusedAmortizedCommitments = try reader["estimatedNetUnusedAmortizedCommitments"].readIfPresent()
        value.estimatedDiscounts = try reader["estimatedDiscounts"].readIfPresent(with: CostOptimizationHubClientTypes.EstimatedDiscounts.read(from:))
        value.estimatedCostAfterDiscounts = try reader["estimatedCostAfterDiscounts"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.EstimatedDiscounts {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.EstimatedDiscounts {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.EstimatedDiscounts()
        value.savingsPlansDiscount = try reader["savingsPlansDiscount"].readIfPresent()
        value.reservedInstancesDiscount = try reader["reservedInstancesDiscount"].readIfPresent()
        value.otherDiscount = try reader["otherDiscount"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.Usage {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.Usage {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.Usage()
        value.usageType = try reader["usageType"].readIfPresent()
        value.usageAmount = try reader["usageAmount"].readIfPresent()
        value.operation = try reader["operation"].readIfPresent()
        value.productCode = try reader["productCode"].readIfPresent()
        value.unit = try reader["unit"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.RdsDbInstanceStorageConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.RdsDbInstanceStorageConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.RdsDbInstanceStorageConfiguration()
        value.storageType = try reader["storageType"].readIfPresent()
        value.allocatedStorageInGb = try reader["allocatedStorageInGb"].readIfPresent()
        value.iops = try reader["iops"].readIfPresent()
        value.storageThroughput = try reader["storageThroughput"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.RdsDbInstance {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.RdsDbInstance {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.RdsDbInstance()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.RdsDbInstanceConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.ResourceCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.RdsDbInstanceConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.RdsDbInstanceConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.RdsDbInstanceConfiguration()
        value.instance = try reader["instance"].readIfPresent(with: CostOptimizationHubClientTypes.DbInstanceConfiguration.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.DbInstanceConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.DbInstanceConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.DbInstanceConfiguration()
        value.dbInstanceClass = try reader["dbInstanceClass"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.SageMakerSavingsPlans {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.SageMakerSavingsPlans {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.SageMakerSavingsPlans()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.SageMakerSavingsPlansConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.SavingsPlansCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.SavingsPlansCostCalculation {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.SavingsPlansCostCalculation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.SavingsPlansCostCalculation()
        value.pricing = try reader["pricing"].readIfPresent(with: CostOptimizationHubClientTypes.SavingsPlansPricing.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.SavingsPlansPricing {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.SavingsPlansPricing {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.SavingsPlansPricing()
        value.monthlySavingsPlansEligibleCost = try reader["monthlySavingsPlansEligibleCost"].readIfPresent()
        value.estimatedMonthlyCommitment = try reader["estimatedMonthlyCommitment"].readIfPresent()
        value.savingsPercentage = try reader["savingsPercentage"].readIfPresent()
        value.estimatedOnDemandCost = try reader["estimatedOnDemandCost"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.SageMakerSavingsPlansConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.SageMakerSavingsPlansConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.SageMakerSavingsPlansConfiguration()
        value.accountScope = try reader["accountScope"].readIfPresent()
        value.term = try reader["term"].readIfPresent()
        value.paymentOption = try reader["paymentOption"].readIfPresent()
        value.hourlyCommitment = try reader["hourlyCommitment"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.ComputeSavingsPlans {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.ComputeSavingsPlans {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.ComputeSavingsPlans()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.ComputeSavingsPlansConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.SavingsPlansCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.ComputeSavingsPlansConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.ComputeSavingsPlansConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.ComputeSavingsPlansConfiguration()
        value.accountScope = try reader["accountScope"].readIfPresent()
        value.term = try reader["term"].readIfPresent()
        value.paymentOption = try reader["paymentOption"].readIfPresent()
        value.hourlyCommitment = try reader["hourlyCommitment"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.Ec2InstanceSavingsPlans {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.Ec2InstanceSavingsPlans {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.Ec2InstanceSavingsPlans()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.Ec2InstanceSavingsPlansConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.SavingsPlansCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.Ec2InstanceSavingsPlansConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.Ec2InstanceSavingsPlansConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.Ec2InstanceSavingsPlansConfiguration()
        value.accountScope = try reader["accountScope"].readIfPresent()
        value.term = try reader["term"].readIfPresent()
        value.paymentOption = try reader["paymentOption"].readIfPresent()
        value.hourlyCommitment = try reader["hourlyCommitment"].readIfPresent()
        value.instanceFamily = try reader["instanceFamily"].readIfPresent()
        value.savingsPlansRegion = try reader["savingsPlansRegion"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.RedshiftReservedInstances {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.RedshiftReservedInstances {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.RedshiftReservedInstances()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.RedshiftReservedInstancesConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.ReservedInstancesCostCalculation {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.ReservedInstancesCostCalculation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.ReservedInstancesCostCalculation()
        value.pricing = try reader["pricing"].readIfPresent(with: CostOptimizationHubClientTypes.ReservedInstancesPricing.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.ReservedInstancesPricing {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.ReservedInstancesPricing {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.ReservedInstancesPricing()
        value.estimatedOnDemandCost = try reader["estimatedOnDemandCost"].readIfPresent()
        value.monthlyReservationEligibleCost = try reader["monthlyReservationEligibleCost"].readIfPresent()
        value.savingsPercentage = try reader["savingsPercentage"].readIfPresent()
        value.estimatedMonthlyAmortizedReservationCost = try reader["estimatedMonthlyAmortizedReservationCost"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.RedshiftReservedInstancesConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.RedshiftReservedInstancesConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.RedshiftReservedInstancesConfiguration()
        value.accountScope = try reader["accountScope"].readIfPresent()
        value.service = try reader["service"].readIfPresent()
        value.normalizedUnitsToPurchase = try reader["normalizedUnitsToPurchase"].readIfPresent()
        value.term = try reader["term"].readIfPresent()
        value.paymentOption = try reader["paymentOption"].readIfPresent()
        value.numberOfInstancesToPurchase = try reader["numberOfInstancesToPurchase"].readIfPresent()
        value.instanceFamily = try reader["instanceFamily"].readIfPresent()
        value.instanceType = try reader["instanceType"].readIfPresent()
        value.reservedInstancesRegion = try reader["reservedInstancesRegion"].readIfPresent()
        value.sizeFlexEligible = try reader["sizeFlexEligible"].readIfPresent()
        value.currentGeneration = try reader["currentGeneration"].readIfPresent()
        value.upfrontCost = try reader["upfrontCost"].readIfPresent()
        value.monthlyRecurringCost = try reader["monthlyRecurringCost"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.OpenSearchReservedInstances {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.OpenSearchReservedInstances {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.OpenSearchReservedInstances()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.OpenSearchReservedInstancesConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.OpenSearchReservedInstancesConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.OpenSearchReservedInstancesConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.OpenSearchReservedInstancesConfiguration()
        value.accountScope = try reader["accountScope"].readIfPresent()
        value.service = try reader["service"].readIfPresent()
        value.normalizedUnitsToPurchase = try reader["normalizedUnitsToPurchase"].readIfPresent()
        value.term = try reader["term"].readIfPresent()
        value.paymentOption = try reader["paymentOption"].readIfPresent()
        value.numberOfInstancesToPurchase = try reader["numberOfInstancesToPurchase"].readIfPresent()
        value.instanceType = try reader["instanceType"].readIfPresent()
        value.reservedInstancesRegion = try reader["reservedInstancesRegion"].readIfPresent()
        value.currentGeneration = try reader["currentGeneration"].readIfPresent()
        value.sizeFlexEligible = try reader["sizeFlexEligible"].readIfPresent()
        value.upfrontCost = try reader["upfrontCost"].readIfPresent()
        value.monthlyRecurringCost = try reader["monthlyRecurringCost"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.ElastiCacheReservedInstances {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.ElastiCacheReservedInstances {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.ElastiCacheReservedInstances()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.ElastiCacheReservedInstancesConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.ElastiCacheReservedInstancesConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.ElastiCacheReservedInstancesConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.ElastiCacheReservedInstancesConfiguration()
        value.accountScope = try reader["accountScope"].readIfPresent()
        value.service = try reader["service"].readIfPresent()
        value.normalizedUnitsToPurchase = try reader["normalizedUnitsToPurchase"].readIfPresent()
        value.term = try reader["term"].readIfPresent()
        value.paymentOption = try reader["paymentOption"].readIfPresent()
        value.numberOfInstancesToPurchase = try reader["numberOfInstancesToPurchase"].readIfPresent()
        value.instanceFamily = try reader["instanceFamily"].readIfPresent()
        value.instanceType = try reader["instanceType"].readIfPresent()
        value.reservedInstancesRegion = try reader["reservedInstancesRegion"].readIfPresent()
        value.currentGeneration = try reader["currentGeneration"].readIfPresent()
        value.sizeFlexEligible = try reader["sizeFlexEligible"].readIfPresent()
        value.upfrontCost = try reader["upfrontCost"].readIfPresent()
        value.monthlyRecurringCost = try reader["monthlyRecurringCost"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.RdsReservedInstances {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.RdsReservedInstances {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.RdsReservedInstances()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.RdsReservedInstancesConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.RdsReservedInstancesConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.RdsReservedInstancesConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.RdsReservedInstancesConfiguration()
        value.accountScope = try reader["accountScope"].readIfPresent()
        value.service = try reader["service"].readIfPresent()
        value.normalizedUnitsToPurchase = try reader["normalizedUnitsToPurchase"].readIfPresent()
        value.term = try reader["term"].readIfPresent()
        value.paymentOption = try reader["paymentOption"].readIfPresent()
        value.numberOfInstancesToPurchase = try reader["numberOfInstancesToPurchase"].readIfPresent()
        value.instanceFamily = try reader["instanceFamily"].readIfPresent()
        value.instanceType = try reader["instanceType"].readIfPresent()
        value.reservedInstancesRegion = try reader["reservedInstancesRegion"].readIfPresent()
        value.sizeFlexEligible = try reader["sizeFlexEligible"].readIfPresent()
        value.currentGeneration = try reader["currentGeneration"].readIfPresent()
        value.upfrontCost = try reader["upfrontCost"].readIfPresent()
        value.monthlyRecurringCost = try reader["monthlyRecurringCost"].readIfPresent()
        value.licenseModel = try reader["licenseModel"].readIfPresent()
        value.databaseEdition = try reader["databaseEdition"].readIfPresent()
        value.databaseEngine = try reader["databaseEngine"].readIfPresent()
        value.deploymentOption = try reader["deploymentOption"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.Ec2ReservedInstances {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.Ec2ReservedInstances {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.Ec2ReservedInstances()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.Ec2ReservedInstancesConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.ReservedInstancesCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.Ec2ReservedInstancesConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.Ec2ReservedInstancesConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.Ec2ReservedInstancesConfiguration()
        value.accountScope = try reader["accountScope"].readIfPresent()
        value.service = try reader["service"].readIfPresent()
        value.normalizedUnitsToPurchase = try reader["normalizedUnitsToPurchase"].readIfPresent()
        value.term = try reader["term"].readIfPresent()
        value.paymentOption = try reader["paymentOption"].readIfPresent()
        value.numberOfInstancesToPurchase = try reader["numberOfInstancesToPurchase"].readIfPresent()
        value.offeringClass = try reader["offeringClass"].readIfPresent()
        value.instanceFamily = try reader["instanceFamily"].readIfPresent()
        value.instanceType = try reader["instanceType"].readIfPresent()
        value.reservedInstancesRegion = try reader["reservedInstancesRegion"].readIfPresent()
        value.currentGeneration = try reader["currentGeneration"].readIfPresent()
        value.platform = try reader["platform"].readIfPresent()
        value.tenancy = try reader["tenancy"].readIfPresent()
        value.sizeFlexEligible = try reader["sizeFlexEligible"].readIfPresent()
        value.upfrontCost = try reader["upfrontCost"].readIfPresent()
        value.monthlyRecurringCost = try reader["monthlyRecurringCost"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.Ec2AutoScalingGroup {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.Ec2AutoScalingGroup {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.Ec2AutoScalingGroup()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.Ec2AutoScalingGroupConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.ResourceCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.Ec2AutoScalingGroupConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.Ec2AutoScalingGroupConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.Ec2AutoScalingGroupConfiguration()
        value.instance = try reader["instance"].readIfPresent(with: CostOptimizationHubClientTypes.InstanceConfiguration.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.InstanceConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.InstanceConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.InstanceConfiguration()
        value.type = try reader["type"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.EbsVolume {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.EbsVolume {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.EbsVolume()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.EbsVolumeConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.ResourceCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.EbsVolumeConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.EbsVolumeConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.EbsVolumeConfiguration()
        value.storage = try reader["storage"].readIfPresent(with: CostOptimizationHubClientTypes.StorageConfiguration.read(from:))
        value.performance = try reader["performance"].readIfPresent(with: CostOptimizationHubClientTypes.BlockStoragePerformanceConfiguration.read(from:))
        value.attachmentState = try reader["attachmentState"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.BlockStoragePerformanceConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.BlockStoragePerformanceConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.BlockStoragePerformanceConfiguration()
        value.iops = try reader["iops"].readIfPresent()
        value.throughput = try reader["throughput"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.StorageConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.StorageConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.StorageConfiguration()
        value.type = try reader["type"].readIfPresent()
        value.sizeInGb = try reader["sizeInGb"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.Ec2Instance {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.Ec2Instance {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.Ec2Instance()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.Ec2InstanceConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.ResourceCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.Ec2InstanceConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.Ec2InstanceConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.Ec2InstanceConfiguration()
        value.instance = try reader["instance"].readIfPresent(with: CostOptimizationHubClientTypes.InstanceConfiguration.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.EcsService {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.EcsService {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.EcsService()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.EcsServiceConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.ResourceCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.EcsServiceConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.EcsServiceConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.EcsServiceConfiguration()
        value.compute = try reader["compute"].readIfPresent(with: CostOptimizationHubClientTypes.ComputeConfiguration.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.ComputeConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.ComputeConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.ComputeConfiguration()
        value.vCpu = try reader["vCpu"].readIfPresent()
        value.memorySizeInMB = try reader["memorySizeInMB"].readIfPresent()
        value.architecture = try reader["architecture"].readIfPresent()
        value.platform = try reader["platform"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.LambdaFunction {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.LambdaFunction {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.LambdaFunction()
        value.configuration = try reader["configuration"].readIfPresent(with: CostOptimizationHubClientTypes.LambdaFunctionConfiguration.read(from:))
        value.costCalculation = try reader["costCalculation"].readIfPresent(with: CostOptimizationHubClientTypes.ResourceCostCalculation.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.LambdaFunctionConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.LambdaFunctionConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.LambdaFunctionConfiguration()
        value.compute = try reader["compute"].readIfPresent(with: CostOptimizationHubClientTypes.ComputeConfiguration.read(from:))
        return value
    }
}

extension CostOptimizationHubClientTypes.Tag {

    static func write(value: CostOptimizationHubClientTypes.Tag?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["key"].write(value.key)
        try writer["value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.Tag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.Tag()
        value.key = try reader["key"].readIfPresent()
        value.value = try reader["value"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.AccountEnrollmentStatus {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.AccountEnrollmentStatus {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.AccountEnrollmentStatus()
        value.accountId = try reader["accountId"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        value.lastUpdatedTimestamp = try reader["lastUpdatedTimestamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.createdTimestamp = try reader["createdTimestamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension CostOptimizationHubClientTypes.Recommendation {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.Recommendation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.Recommendation()
        value.recommendationId = try reader["recommendationId"].readIfPresent()
        value.accountId = try reader["accountId"].readIfPresent()
        value.region = try reader["region"].readIfPresent()
        value.resourceId = try reader["resourceId"].readIfPresent()
        value.resourceArn = try reader["resourceArn"].readIfPresent()
        value.currentResourceType = try reader["currentResourceType"].readIfPresent()
        value.recommendedResourceType = try reader["recommendedResourceType"].readIfPresent()
        value.estimatedMonthlySavings = try reader["estimatedMonthlySavings"].readIfPresent()
        value.estimatedSavingsPercentage = try reader["estimatedSavingsPercentage"].readIfPresent()
        value.estimatedMonthlyCost = try reader["estimatedMonthlyCost"].readIfPresent()
        value.currencyCode = try reader["currencyCode"].readIfPresent()
        value.implementationEffort = try reader["implementationEffort"].readIfPresent()
        value.restartNeeded = try reader["restartNeeded"].readIfPresent()
        value.actionType = try reader["actionType"].readIfPresent()
        value.rollbackPossible = try reader["rollbackPossible"].readIfPresent()
        value.currentResourceSummary = try reader["currentResourceSummary"].readIfPresent()
        value.recommendedResourceSummary = try reader["recommendedResourceSummary"].readIfPresent()
        value.lastRefreshTimestamp = try reader["lastRefreshTimestamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.recommendationLookbackPeriodInDays = try reader["recommendationLookbackPeriodInDays"].readIfPresent()
        value.source = try reader["source"].readIfPresent()
        value.tags = try reader["tags"].readListIfPresent(memberReadingClosure: CostOptimizationHubClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension CostOptimizationHubClientTypes.RecommendationSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.RecommendationSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.RecommendationSummary()
        value.group = try reader["group"].readIfPresent()
        value.estimatedMonthlySavings = try reader["estimatedMonthlySavings"].readIfPresent()
        value.recommendationCount = try reader["recommendationCount"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.SummaryMetricsResult {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.SummaryMetricsResult {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.SummaryMetricsResult()
        value.savingsPercentage = try reader["savingsPercentage"].readIfPresent()
        return value
    }
}

extension CostOptimizationHubClientTypes.ValidationExceptionDetail {

    static func read(from reader: SmithyJSON.Reader) throws -> CostOptimizationHubClientTypes.ValidationExceptionDetail {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostOptimizationHubClientTypes.ValidationExceptionDetail()
        value.fieldName = try reader["fieldName"].readIfPresent() ?? ""
        value.message = try reader["message"].readIfPresent() ?? ""
        return value
    }
}

extension CostOptimizationHubClientTypes.Filter {

    static func write(value: CostOptimizationHubClientTypes.Filter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["accountIds"].writeList(value.accountIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["actionTypes"].writeList(value.actionTypes, memberWritingClosure: SmithyReadWrite.WritingClosureBox<CostOptimizationHubClientTypes.ActionType>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["implementationEfforts"].writeList(value.implementationEfforts, memberWritingClosure: SmithyReadWrite.WritingClosureBox<CostOptimizationHubClientTypes.ImplementationEffort>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["recommendationIds"].writeList(value.recommendationIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["regions"].writeList(value.regions, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["resourceArns"].writeList(value.resourceArns, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["resourceIds"].writeList(value.resourceIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["resourceTypes"].writeList(value.resourceTypes, memberWritingClosure: SmithyReadWrite.WritingClosureBox<CostOptimizationHubClientTypes.ResourceType>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["restartNeeded"].write(value.restartNeeded)
        try writer["rollbackPossible"].write(value.rollbackPossible)
        try writer["tags"].writeList(value.tags, memberWritingClosure: CostOptimizationHubClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CostOptimizationHubClientTypes.OrderBy {

    static func write(value: CostOptimizationHubClientTypes.OrderBy?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["dimension"].write(value.dimension)
        try writer["order"].write(value.order)
    }
}

public enum CostOptimizationHubClientTypes {}
