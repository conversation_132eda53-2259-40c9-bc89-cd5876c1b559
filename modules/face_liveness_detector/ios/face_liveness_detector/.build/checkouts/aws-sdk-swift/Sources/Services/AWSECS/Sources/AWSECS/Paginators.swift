//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension ECSClient {
    /// Paginate over `[ListAccountSettingsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListAccountSettingsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListAccountSettingsOutput`
    public func listAccountSettingsPaginated(input: ListAccountSettingsInput) -> ClientRuntime.PaginatorSequence<ListAccountSettingsInput, ListAccountSettingsOutput> {
        return ClientRuntime.PaginatorSequence<ListAccountSettingsInput, ListAccountSettingsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listAccountSettings(input:))
    }
}

extension ListAccountSettingsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListAccountSettingsInput {
        return ListAccountSettingsInput(
            effectiveSettings: self.effectiveSettings,
            maxResults: self.maxResults,
            name: self.name,
            nextToken: token,
            principalArn: self.principalArn,
            value: self.value
        )}
}

extension PaginatorSequence where OperationStackInput == ListAccountSettingsInput, OperationStackOutput == ListAccountSettingsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listAccountSettingsPaginated`
    /// to access the nested member `[ECSClientTypes.Setting]`
    /// - Returns: `[ECSClientTypes.Setting]`
    public func settings() async throws -> [ECSClientTypes.Setting] {
        return try await self.asyncCompactMap { item in item.settings }
    }
}
extension ECSClient {
    /// Paginate over `[ListAttributesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListAttributesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListAttributesOutput`
    public func listAttributesPaginated(input: ListAttributesInput) -> ClientRuntime.PaginatorSequence<ListAttributesInput, ListAttributesOutput> {
        return ClientRuntime.PaginatorSequence<ListAttributesInput, ListAttributesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listAttributes(input:))
    }
}

extension ListAttributesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListAttributesInput {
        return ListAttributesInput(
            attributeName: self.attributeName,
            attributeValue: self.attributeValue,
            cluster: self.cluster,
            maxResults: self.maxResults,
            nextToken: token,
            targetType: self.targetType
        )}
}

extension PaginatorSequence where OperationStackInput == ListAttributesInput, OperationStackOutput == ListAttributesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listAttributesPaginated`
    /// to access the nested member `[ECSClientTypes.Attribute]`
    /// - Returns: `[ECSClientTypes.Attribute]`
    public func attributes() async throws -> [ECSClientTypes.Attribute] {
        return try await self.asyncCompactMap { item in item.attributes }
    }
}
extension ECSClient {
    /// Paginate over `[ListClustersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListClustersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListClustersOutput`
    public func listClustersPaginated(input: ListClustersInput) -> ClientRuntime.PaginatorSequence<ListClustersInput, ListClustersOutput> {
        return ClientRuntime.PaginatorSequence<ListClustersInput, ListClustersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listClusters(input:))
    }
}

extension ListClustersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListClustersInput {
        return ListClustersInput(
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListClustersInput, OperationStackOutput == ListClustersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listClustersPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func clusterArns() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.clusterArns }
    }
}
extension ECSClient {
    /// Paginate over `[ListContainerInstancesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListContainerInstancesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListContainerInstancesOutput`
    public func listContainerInstancesPaginated(input: ListContainerInstancesInput) -> ClientRuntime.PaginatorSequence<ListContainerInstancesInput, ListContainerInstancesOutput> {
        return ClientRuntime.PaginatorSequence<ListContainerInstancesInput, ListContainerInstancesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listContainerInstances(input:))
    }
}

extension ListContainerInstancesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListContainerInstancesInput {
        return ListContainerInstancesInput(
            cluster: self.cluster,
            filter: self.filter,
            maxResults: self.maxResults,
            nextToken: token,
            status: self.status
        )}
}

extension PaginatorSequence where OperationStackInput == ListContainerInstancesInput, OperationStackOutput == ListContainerInstancesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listContainerInstancesPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func containerInstanceArns() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.containerInstanceArns }
    }
}
extension ECSClient {
    /// Paginate over `[ListServicesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListServicesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListServicesOutput`
    public func listServicesPaginated(input: ListServicesInput) -> ClientRuntime.PaginatorSequence<ListServicesInput, ListServicesOutput> {
        return ClientRuntime.PaginatorSequence<ListServicesInput, ListServicesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listServices(input:))
    }
}

extension ListServicesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListServicesInput {
        return ListServicesInput(
            cluster: self.cluster,
            launchType: self.launchType,
            maxResults: self.maxResults,
            nextToken: token,
            schedulingStrategy: self.schedulingStrategy
        )}
}

extension PaginatorSequence where OperationStackInput == ListServicesInput, OperationStackOutput == ListServicesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listServicesPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func serviceArns() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.serviceArns }
    }
}
extension ECSClient {
    /// Paginate over `[ListServicesByNamespaceOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListServicesByNamespaceInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListServicesByNamespaceOutput`
    public func listServicesByNamespacePaginated(input: ListServicesByNamespaceInput) -> ClientRuntime.PaginatorSequence<ListServicesByNamespaceInput, ListServicesByNamespaceOutput> {
        return ClientRuntime.PaginatorSequence<ListServicesByNamespaceInput, ListServicesByNamespaceOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listServicesByNamespace(input:))
    }
}

extension ListServicesByNamespaceInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListServicesByNamespaceInput {
        return ListServicesByNamespaceInput(
            maxResults: self.maxResults,
            namespace: self.namespace,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListServicesByNamespaceInput, OperationStackOutput == ListServicesByNamespaceOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listServicesByNamespacePaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func serviceArns() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.serviceArns }
    }
}
extension ECSClient {
    /// Paginate over `[ListTaskDefinitionFamiliesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListTaskDefinitionFamiliesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListTaskDefinitionFamiliesOutput`
    public func listTaskDefinitionFamiliesPaginated(input: ListTaskDefinitionFamiliesInput) -> ClientRuntime.PaginatorSequence<ListTaskDefinitionFamiliesInput, ListTaskDefinitionFamiliesOutput> {
        return ClientRuntime.PaginatorSequence<ListTaskDefinitionFamiliesInput, ListTaskDefinitionFamiliesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listTaskDefinitionFamilies(input:))
    }
}

extension ListTaskDefinitionFamiliesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListTaskDefinitionFamiliesInput {
        return ListTaskDefinitionFamiliesInput(
            familyPrefix: self.familyPrefix,
            maxResults: self.maxResults,
            nextToken: token,
            status: self.status
        )}
}

extension PaginatorSequence where OperationStackInput == ListTaskDefinitionFamiliesInput, OperationStackOutput == ListTaskDefinitionFamiliesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listTaskDefinitionFamiliesPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func families() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.families }
    }
}
extension ECSClient {
    /// Paginate over `[ListTaskDefinitionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListTaskDefinitionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListTaskDefinitionsOutput`
    public func listTaskDefinitionsPaginated(input: ListTaskDefinitionsInput) -> ClientRuntime.PaginatorSequence<ListTaskDefinitionsInput, ListTaskDefinitionsOutput> {
        return ClientRuntime.PaginatorSequence<ListTaskDefinitionsInput, ListTaskDefinitionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listTaskDefinitions(input:))
    }
}

extension ListTaskDefinitionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListTaskDefinitionsInput {
        return ListTaskDefinitionsInput(
            familyPrefix: self.familyPrefix,
            maxResults: self.maxResults,
            nextToken: token,
            sort: self.sort,
            status: self.status
        )}
}

extension PaginatorSequence where OperationStackInput == ListTaskDefinitionsInput, OperationStackOutput == ListTaskDefinitionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listTaskDefinitionsPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func taskDefinitionArns() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.taskDefinitionArns }
    }
}
extension ECSClient {
    /// Paginate over `[ListTasksOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListTasksInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListTasksOutput`
    public func listTasksPaginated(input: ListTasksInput) -> ClientRuntime.PaginatorSequence<ListTasksInput, ListTasksOutput> {
        return ClientRuntime.PaginatorSequence<ListTasksInput, ListTasksOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listTasks(input:))
    }
}

extension ListTasksInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListTasksInput {
        return ListTasksInput(
            cluster: self.cluster,
            containerInstance: self.containerInstance,
            desiredStatus: self.desiredStatus,
            family: self.family,
            launchType: self.launchType,
            maxResults: self.maxResults,
            nextToken: token,
            serviceName: self.serviceName,
            startedBy: self.startedBy
        )}
}

extension PaginatorSequence where OperationStackInput == ListTasksInput, OperationStackOutput == ListTasksOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listTasksPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func taskArns() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.taskArns }
    }
}
