//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ByteStream
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
@_spi(SmithyReadWrite) import func SmithyReadWrite.listReadingClosure
@_spi(SmithyReadWrite) import func SmithyReadWrite.mapReadingClosure
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
import struct SmithyHTTPAPI.Header
import struct SmithyHTTPAPI.Headers
@_spi(SmithyReadWrite) import struct SmithyReadWrite.WritingClosureBox

/// The operation did not succeed because of an unauthorized access attempt.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CodeartifactClientTypes {

    public enum AllowPublish: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case allow
        case block
        case sdkUnknown(Swift.String)

        public static var allCases: [AllowPublish] {
            return [
                .allow,
                .block
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .allow: return "ALLOW"
            case .block: return "BLOCK"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeartifactClientTypes {

    public enum AllowUpstream: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case allow
        case block
        case sdkUnknown(Swift.String)

        public static var allCases: [AllowUpstream] {
            return [
                .allow,
                .block
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .allow: return "ALLOW"
            case .block: return "BLOCK"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeartifactClientTypes {

    public enum HashAlgorithm: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case md5
        case sha1
        case sha256
        case sha512
        case sdkUnknown(Swift.String)

        public static var allCases: [HashAlgorithm] {
            return [
                .md5,
                .sha1,
                .sha256,
                .sha512
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .md5: return "MD5"
            case .sha1: return "SHA-1"
            case .sha256: return "SHA-256"
            case .sha512: return "SHA-512"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeartifactClientTypes {

    /// Contains details about a package version asset.
    public struct AssetSummary: Swift.Sendable {
        /// The hashes of the asset.
        public var hashes: [Swift.String: Swift.String]?
        /// The name of the asset.
        /// This member is required.
        public var name: Swift.String?
        /// The size of the asset.
        public var size: Swift.Int?

        public init(
            hashes: [Swift.String: Swift.String]? = nil,
            name: Swift.String? = nil,
            size: Swift.Int? = nil
        )
        {
            self.hashes = hashes
            self.name = name
            self.size = size
        }
    }
}

extension CodeartifactClientTypes {

    public enum PackageGroupAssociationType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case strong
        case `weak`
        case sdkUnknown(Swift.String)

        public static var allCases: [PackageGroupAssociationType] {
            return [
                .strong,
                .weak
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .strong: return "STRONG"
            case .weak: return "WEAK"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeartifactClientTypes {

    public enum PackageFormat: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cargo
        case generic
        case maven
        case npm
        case nuget
        case pypi
        case ruby
        case swift
        case sdkUnknown(Swift.String)

        public static var allCases: [PackageFormat] {
            return [
                .cargo,
                .generic,
                .maven,
                .npm,
                .nuget,
                .pypi,
                .ruby,
                .swift
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cargo: return "cargo"
            case .generic: return "generic"
            case .maven: return "maven"
            case .npm: return "npm"
            case .nuget: return "nuget"
            case .pypi: return "pypi"
            case .ruby: return "ruby"
            case .swift: return "swift"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeartifactClientTypes {

    /// A package associated with a package group.
    public struct AssociatedPackage: Swift.Sendable {
        /// Describes the strength of the association between the package and package group. A strong match can be thought of as an exact match, and a weak match can be thought of as a variation match, for example, the package name matches a variation of the package group pattern. For more information about package group pattern matching, including strong and weak matches, see [Package group definition syntax and matching behavior](https://docs.aws.amazon.com/codeartifact/latest/ug/package-group-definition-syntax-matching-behavior.html) in the CodeArtifact User Guide.
        public var associationType: CodeartifactClientTypes.PackageGroupAssociationType?
        /// A format that specifies the type of the associated package.
        public var format: CodeartifactClientTypes.PackageFormat?
        /// The namespace of the associated package. The package component that specifies its namespace depends on its type. For example:
        ///
        /// * The namespace of a Maven package version is its groupId.
        ///
        /// * The namespace of an npm or Swift package version is its scope.
        ///
        /// * The namespace of a generic package is its namespace.
        ///
        /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
        public var namespace: Swift.String?
        /// The name of the associated package.
        public var package: Swift.String?

        public init(
            associationType: CodeartifactClientTypes.PackageGroupAssociationType? = nil,
            format: CodeartifactClientTypes.PackageFormat? = nil,
            namespace: Swift.String? = nil,
            package: Swift.String? = nil
        )
        {
            self.associationType = associationType
            self.format = format
            self.namespace = namespace
            self.package = package
        }
    }
}

extension CodeartifactClientTypes {

    public enum ResourceType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case asset
        case domain
        case package
        case packageVersion
        case repository
        case sdkUnknown(Swift.String)

        public static var allCases: [ResourceType] {
            return [
                .asset,
                .domain,
                .package,
                .packageVersion,
                .repository
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .asset: return "asset"
            case .domain: return "domain"
            case .package: return "package"
            case .packageVersion: return "package-version"
            case .repository: return "repository"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The operation did not succeed because prerequisites are not met.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The ID of the resource.
        public internal(set) var resourceId: Swift.String? = nil
        /// The type of Amazon Web Services resource.
        public internal(set) var resourceType: CodeartifactClientTypes.ResourceType? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: CodeartifactClientTypes.ResourceType? = nil
    )
    {
        self.properties.message = message
        self.properties.resourceId = resourceId
        self.properties.resourceType = resourceType
    }
}

/// The operation did not succeed because of an error that occurred inside CodeArtifact.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The operation did not succeed because the resource requested is not found in the service.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The ID of the resource.
        public internal(set) var resourceId: Swift.String? = nil
        /// The type of Amazon Web Services resource.
        public internal(set) var resourceType: CodeartifactClientTypes.ResourceType? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: CodeartifactClientTypes.ResourceType? = nil
    )
    {
        self.properties.message = message
        self.properties.resourceId = resourceId
        self.properties.resourceType = resourceType
    }
}

/// The operation did not succeed because it would have exceeded a service limit for your account.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The ID of the resource.
        public internal(set) var resourceId: Swift.String? = nil
        /// The type of Amazon Web Services resource.
        public internal(set) var resourceType: CodeartifactClientTypes.ResourceType? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceQuotaExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: CodeartifactClientTypes.ResourceType? = nil
    )
    {
        self.properties.message = message
        self.properties.resourceId = resourceId
        self.properties.resourceType = resourceType
    }
}

/// The operation did not succeed because too many requests are sent to the service.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The time period, in seconds, to wait before retrying the request.
        public internal(set) var retryAfterSeconds: Swift.Int? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        retryAfterSeconds: Swift.Int? = nil
    )
    {
        self.properties.message = message
        self.properties.retryAfterSeconds = retryAfterSeconds
    }
}

extension CodeartifactClientTypes {

    public enum ValidationExceptionReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cannotParse
        case encryptionKeyError
        case fieldValidationFailed
        case other
        case unknownOperation
        case sdkUnknown(Swift.String)

        public static var allCases: [ValidationExceptionReason] {
            return [
                .cannotParse,
                .encryptionKeyError,
                .fieldValidationFailed,
                .other,
                .unknownOperation
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cannotParse: return "CANNOT_PARSE"
            case .encryptionKeyError: return "ENCRYPTION_KEY_ERROR"
            case .fieldValidationFailed: return "FIELD_VALIDATION_FAILED"
            case .other: return "OTHER"
            case .unknownOperation: return "UNKNOWN_OPERATION"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The operation did not succeed because a parameter in the request was sent with an invalid value.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        ///
        public internal(set) var reason: CodeartifactClientTypes.ValidationExceptionReason? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        reason: CodeartifactClientTypes.ValidationExceptionReason? = nil
    )
    {
        self.properties.message = message
        self.properties.reason = reason
    }
}

public struct AssociateExternalConnectionInput: Swift.Sendable {
    /// The name of the domain that contains the repository.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The name of the external connection to add to the repository. The following values are supported:
    ///
    /// * public:npmjs - for the npm public repository.
    ///
    /// * public:nuget-org - for the NuGet Gallery.
    ///
    /// * public:pypi - for the Python Package Index.
    ///
    /// * public:maven-central - for Maven Central.
    ///
    /// * public:maven-googleandroid - for the Google Android repository.
    ///
    /// * public:maven-gradleplugins - for the Gradle plugins repository.
    ///
    /// * public:maven-commonsware - for the CommonsWare Android repository.
    ///
    /// * public:maven-clojars - for the Clojars repository.
    ///
    /// * public:ruby-gems-org - for RubyGems.org.
    ///
    /// * public:crates-io - for Crates.io.
    /// This member is required.
    public var externalConnection: Swift.String?
    /// The name of the repository to which the external connection is added.
    /// This member is required.
    public var repository: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        externalConnection: Swift.String? = nil,
        repository: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.externalConnection = externalConnection
        self.repository = repository
    }
}

extension CodeartifactClientTypes {

    public enum ExternalConnectionStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case available
        case sdkUnknown(Swift.String)

        public static var allCases: [ExternalConnectionStatus] {
            return [
                .available
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .available: return "Available"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeartifactClientTypes {

    /// Contains information about the external connection of a repository.
    public struct RepositoryExternalConnectionInfo: Swift.Sendable {
        /// The name of the external connection associated with a repository.
        public var externalConnectionName: Swift.String?
        /// The package format associated with a repository's external connection. The valid package formats are:
        ///
        /// * npm: A Node Package Manager (npm) package.
        ///
        /// * pypi: A Python Package Index (PyPI) package.
        ///
        /// * maven: A Maven package that contains compiled code in a distributable format, such as a JAR file.
        ///
        /// * nuget: A NuGet package.
        ///
        /// * generic: A generic package.
        ///
        /// * ruby: A Ruby package.
        ///
        /// * swift: A Swift package.
        ///
        /// * cargo: A Cargo package.
        public var packageFormat: CodeartifactClientTypes.PackageFormat?
        /// The status of the external connection of a repository. There is one valid value, Available.
        public var status: CodeartifactClientTypes.ExternalConnectionStatus?

        public init(
            externalConnectionName: Swift.String? = nil,
            packageFormat: CodeartifactClientTypes.PackageFormat? = nil,
            status: CodeartifactClientTypes.ExternalConnectionStatus? = nil
        )
        {
            self.externalConnectionName = externalConnectionName
            self.packageFormat = packageFormat
            self.status = status
        }
    }
}

extension CodeartifactClientTypes {

    /// Information about an upstream repository.
    public struct UpstreamRepositoryInfo: Swift.Sendable {
        /// The name of an upstream repository.
        public var repositoryName: Swift.String?

        public init(
            repositoryName: Swift.String? = nil
        )
        {
            self.repositoryName = repositoryName
        }
    }
}

extension CodeartifactClientTypes {

    /// The details of a repository stored in CodeArtifact. A CodeArtifact repository contains a set of package versions, each of which maps to a set of assets. Repositories are polyglot—a single repository can contain packages of any supported type. Each repository exposes endpoints for fetching and publishing packages using tools like the npm CLI, the Maven CLI (mvn), and pip. You can create up to 100 repositories per Amazon Web Services account.
    public struct RepositoryDescription: Swift.Sendable {
        /// The 12-digit account number of the Amazon Web Services account that manages the repository.
        public var administratorAccount: Swift.String?
        /// The Amazon Resource Name (ARN) of the repository.
        public var arn: Swift.String?
        /// A timestamp that represents the date and time the repository was created.
        public var createdTime: Foundation.Date?
        /// A text description of the repository.
        public var description: Swift.String?
        /// The name of the domain that contains the repository.
        public var domainName: Swift.String?
        /// The 12-digit account number of the Amazon Web Services account that owns the domain that contains the repository. It does not include dashes or spaces.
        public var domainOwner: Swift.String?
        /// An array of external connections associated with the repository.
        public var externalConnections: [CodeartifactClientTypes.RepositoryExternalConnectionInfo]?
        /// The name of the repository.
        public var name: Swift.String?
        /// A list of upstream repositories to associate with the repository. The order of the upstream repositories in the list determines their priority order when CodeArtifact looks for a requested package version. For more information, see [Working with upstream repositories](https://docs.aws.amazon.com/codeartifact/latest/ug/repos-upstream.html).
        public var upstreams: [CodeartifactClientTypes.UpstreamRepositoryInfo]?

        public init(
            administratorAccount: Swift.String? = nil,
            arn: Swift.String? = nil,
            createdTime: Foundation.Date? = nil,
            description: Swift.String? = nil,
            domainName: Swift.String? = nil,
            domainOwner: Swift.String? = nil,
            externalConnections: [CodeartifactClientTypes.RepositoryExternalConnectionInfo]? = nil,
            name: Swift.String? = nil,
            upstreams: [CodeartifactClientTypes.UpstreamRepositoryInfo]? = nil
        )
        {
            self.administratorAccount = administratorAccount
            self.arn = arn
            self.createdTime = createdTime
            self.description = description
            self.domainName = domainName
            self.domainOwner = domainOwner
            self.externalConnections = externalConnections
            self.name = name
            self.upstreams = upstreams
        }
    }
}

public struct AssociateExternalConnectionOutput: Swift.Sendable {
    /// Information about the connected repository after processing the request.
    public var repository: CodeartifactClientTypes.RepositoryDescription?

    public init(
        repository: CodeartifactClientTypes.RepositoryDescription? = nil
    )
    {
        self.repository = repository
    }
}

public struct CopyPackageVersionsInput: Swift.Sendable {
    /// Set to true to overwrite a package version that already exists in the destination repository. If set to false and the package version already exists in the destination repository, the package version is returned in the failedVersions field of the response with an ALREADY_EXISTS error code.
    public var allowOverwrite: Swift.Bool?
    /// The name of the repository into which package versions are copied.
    /// This member is required.
    public var destinationRepository: Swift.String?
    /// The name of the domain that contains the source and destination repositories.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The format of the package versions to be copied.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// Set to true to copy packages from repositories that are upstream from the source repository to the destination repository. The default setting is false. For more information, see [Working with upstream repositories](https://docs.aws.amazon.com/codeartifact/latest/ug/repos-upstream.html).
    public var includeFromUpstream: Swift.Bool?
    /// The namespace of the package versions to be copied. The package component that specifies its namespace depends on its type. For example: The namespace is required when copying package versions of the following formats:
    ///
    /// * Maven
    ///
    /// * Swift
    ///
    /// * generic
    ///
    ///
    ///
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The name of the package that contains the versions to be copied.
    /// This member is required.
    public var package: Swift.String?
    /// The name of the repository that contains the package versions to be copied.
    /// This member is required.
    public var sourceRepository: Swift.String?
    /// A list of key-value pairs. The keys are package versions and the values are package version revisions. A CopyPackageVersion operation succeeds if the specified versions in the source repository match the specified package version revision. You must specify versions or versionRevisions. You cannot specify both.
    public var versionRevisions: [Swift.String: Swift.String]?
    /// The versions of the package to be copied. You must specify versions or versionRevisions. You cannot specify both.
    public var versions: [Swift.String]?

    public init(
        allowOverwrite: Swift.Bool? = nil,
        destinationRepository: Swift.String? = nil,
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        includeFromUpstream: Swift.Bool? = nil,
        namespace: Swift.String? = nil,
        package: Swift.String? = nil,
        sourceRepository: Swift.String? = nil,
        versionRevisions: [Swift.String: Swift.String]? = nil,
        versions: [Swift.String]? = nil
    )
    {
        self.allowOverwrite = allowOverwrite
        self.destinationRepository = destinationRepository
        self.domain = domain
        self.domainOwner = domainOwner
        self.format = format
        self.includeFromUpstream = includeFromUpstream
        self.namespace = namespace
        self.package = package
        self.sourceRepository = sourceRepository
        self.versionRevisions = versionRevisions
        self.versions = versions
    }
}

extension CodeartifactClientTypes {

    public enum PackageVersionErrorCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case alreadyExists
        case mismatchedRevision
        case mismatchedStatus
        case notAllowed
        case notFound
        case skipped
        case sdkUnknown(Swift.String)

        public static var allCases: [PackageVersionErrorCode] {
            return [
                .alreadyExists,
                .mismatchedRevision,
                .mismatchedStatus,
                .notAllowed,
                .notFound,
                .skipped
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .alreadyExists: return "ALREADY_EXISTS"
            case .mismatchedRevision: return "MISMATCHED_REVISION"
            case .mismatchedStatus: return "MISMATCHED_STATUS"
            case .notAllowed: return "NOT_ALLOWED"
            case .notFound: return "NOT_FOUND"
            case .skipped: return "SKIPPED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeartifactClientTypes {

    /// l An error associated with package.
    public struct PackageVersionError: Swift.Sendable {
        /// The error code associated with the error. Valid error codes are:
        ///
        /// * ALREADY_EXISTS
        ///
        /// * MISMATCHED_REVISION
        ///
        /// * MISMATCHED_STATUS
        ///
        /// * NOT_ALLOWED
        ///
        /// * NOT_FOUND
        ///
        /// * SKIPPED
        public var errorCode: CodeartifactClientTypes.PackageVersionErrorCode?
        /// The error message associated with the error.
        public var errorMessage: Swift.String?

        public init(
            errorCode: CodeartifactClientTypes.PackageVersionErrorCode? = nil,
            errorMessage: Swift.String? = nil
        )
        {
            self.errorCode = errorCode
            self.errorMessage = errorMessage
        }
    }
}

extension CodeartifactClientTypes {

    public enum PackageVersionStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case archived
        case deleted
        case disposed
        case published
        case unfinished
        case unlisted
        case sdkUnknown(Swift.String)

        public static var allCases: [PackageVersionStatus] {
            return [
                .archived,
                .deleted,
                .disposed,
                .published,
                .unfinished,
                .unlisted
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .archived: return "Archived"
            case .deleted: return "Deleted"
            case .disposed: return "Disposed"
            case .published: return "Published"
            case .unfinished: return "Unfinished"
            case .unlisted: return "Unlisted"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeartifactClientTypes {

    /// Contains the revision and status of a package version.
    public struct SuccessfulPackageVersionInfo: Swift.Sendable {
        /// The revision of a package version.
        public var revision: Swift.String?
        /// The status of a package version.
        public var status: CodeartifactClientTypes.PackageVersionStatus?

        public init(
            revision: Swift.String? = nil,
            status: CodeartifactClientTypes.PackageVersionStatus? = nil
        )
        {
            self.revision = revision
            self.status = status
        }
    }
}

public struct CopyPackageVersionsOutput: Swift.Sendable {
    /// A map of package versions that failed to copy and their error codes. The possible error codes are in the PackageVersionError data type. They are:
    ///
    /// * ALREADY_EXISTS
    ///
    /// * MISMATCHED_REVISION
    ///
    /// * MISMATCHED_STATUS
    ///
    /// * NOT_ALLOWED
    ///
    /// * NOT_FOUND
    ///
    /// * SKIPPED
    public var failedVersions: [Swift.String: CodeartifactClientTypes.PackageVersionError]?
    /// A list of the package versions that were successfully copied to your repository.
    public var successfulVersions: [Swift.String: CodeartifactClientTypes.SuccessfulPackageVersionInfo]?

    public init(
        failedVersions: [Swift.String: CodeartifactClientTypes.PackageVersionError]? = nil,
        successfulVersions: [Swift.String: CodeartifactClientTypes.SuccessfulPackageVersionInfo]? = nil
    )
    {
        self.failedVersions = failedVersions
        self.successfulVersions = successfulVersions
    }
}

extension CodeartifactClientTypes {

    /// A tag is a key-value pair that can be used to manage, search for, or filter resources in CodeArtifact.
    public struct Tag: Swift.Sendable {
        /// The tag key.
        /// This member is required.
        public var key: Swift.String?
        /// The tag value.
        /// This member is required.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

public struct CreateDomainInput: Swift.Sendable {
    /// The name of the domain to create. All domain names in an Amazon Web Services Region that are in the same Amazon Web Services account must be unique. The domain name is used as the prefix in DNS hostnames. Do not use sensitive information in a domain name because it is publicly discoverable.
    /// This member is required.
    public var domain: Swift.String?
    /// The encryption key for the domain. This is used to encrypt content stored in a domain. An encryption key can be a key ID, a key Amazon Resource Name (ARN), a key alias, or a key alias ARN. To specify an encryptionKey, your IAM role must have kms:DescribeKey and kms:CreateGrant permissions on the encryption key that is used. For more information, see [DescribeKey](https://docs.aws.amazon.com/kms/latest/APIReference/API_DescribeKey.html#API_DescribeKey_RequestSyntax) in the Key Management Service API Reference and [Key Management Service API Permissions Reference](https://docs.aws.amazon.com/kms/latest/developerguide/kms-api-permissions-reference.html) in the Key Management Service Developer Guide. CodeArtifact supports only symmetric CMKs. Do not associate an asymmetric CMK with your domain. For more information, see [Using symmetric and asymmetric keys](https://docs.aws.amazon.com/kms/latest/developerguide/symmetric-asymmetric.html) in the Key Management Service Developer Guide.
    public var encryptionKey: Swift.String?
    /// One or more tag key-value pairs for the domain.
    public var tags: [CodeartifactClientTypes.Tag]?

    public init(
        domain: Swift.String? = nil,
        encryptionKey: Swift.String? = nil,
        tags: [CodeartifactClientTypes.Tag]? = nil
    )
    {
        self.domain = domain
        self.encryptionKey = encryptionKey
        self.tags = tags
    }
}

extension CodeartifactClientTypes {

    public enum DomainStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case active
        case deleted
        case sdkUnknown(Swift.String)

        public static var allCases: [DomainStatus] {
            return [
                .active,
                .deleted
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .active: return "Active"
            case .deleted: return "Deleted"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeartifactClientTypes {

    /// Information about a domain. A domain is a container for repositories. When you create a domain, it is empty until you add one or more repositories.
    public struct DomainDescription: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the domain.
        public var arn: Swift.String?
        /// The total size of all assets in the domain.
        public var assetSizeBytes: Swift.Int
        /// A timestamp that represents the date and time the domain was created.
        public var createdTime: Foundation.Date?
        /// The ARN of an Key Management Service (KMS) key associated with a domain.
        public var encryptionKey: Swift.String?
        /// The name of the domain.
        public var name: Swift.String?
        /// The Amazon Web Services account ID that owns the domain.
        public var owner: Swift.String?
        /// The number of repositories in the domain.
        public var repositoryCount: Swift.Int
        /// The Amazon Resource Name (ARN) of the Amazon S3 bucket that is used to store package assets in the domain.
        public var s3BucketArn: Swift.String?
        /// The current status of a domain.
        public var status: CodeartifactClientTypes.DomainStatus?

        public init(
            arn: Swift.String? = nil,
            assetSizeBytes: Swift.Int = 0,
            createdTime: Foundation.Date? = nil,
            encryptionKey: Swift.String? = nil,
            name: Swift.String? = nil,
            owner: Swift.String? = nil,
            repositoryCount: Swift.Int = 0,
            s3BucketArn: Swift.String? = nil,
            status: CodeartifactClientTypes.DomainStatus? = nil
        )
        {
            self.arn = arn
            self.assetSizeBytes = assetSizeBytes
            self.createdTime = createdTime
            self.encryptionKey = encryptionKey
            self.name = name
            self.owner = owner
            self.repositoryCount = repositoryCount
            self.s3BucketArn = s3BucketArn
            self.status = status
        }
    }
}

public struct CreateDomainOutput: Swift.Sendable {
    /// Contains information about the created domain after processing the request.
    public var domain: CodeartifactClientTypes.DomainDescription?

    public init(
        domain: CodeartifactClientTypes.DomainDescription? = nil
    )
    {
        self.domain = domain
    }
}

public struct CreatePackageGroupInput: Swift.Sendable {
    /// The contact information for the created package group.
    public var contactInfo: Swift.String?
    /// A description of the package group.
    public var description: Swift.String?
    /// The name of the domain in which you want to create a package group.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The pattern of the package group to create. The pattern is also the identifier of the package group.
    /// This member is required.
    public var packageGroup: Swift.String?
    /// One or more tag key-value pairs for the package group.
    public var tags: [CodeartifactClientTypes.Tag]?

    public init(
        contactInfo: Swift.String? = nil,
        description: Swift.String? = nil,
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        packageGroup: Swift.String? = nil,
        tags: [CodeartifactClientTypes.Tag]? = nil
    )
    {
        self.contactInfo = contactInfo
        self.description = description
        self.domain = domain
        self.domainOwner = domainOwner
        self.packageGroup = packageGroup
        self.tags = tags
    }
}

extension CodeartifactClientTypes {

    public enum PackageGroupOriginRestrictionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case externalUpstream
        case internalUpstream
        case publish
        case sdkUnknown(Swift.String)

        public static var allCases: [PackageGroupOriginRestrictionType] {
            return [
                .externalUpstream,
                .internalUpstream,
                .publish
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .externalUpstream: return "EXTERNAL_UPSTREAM"
            case .internalUpstream: return "INTERNAL_UPSTREAM"
            case .publish: return "PUBLISH"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeartifactClientTypes {

    public enum PackageGroupOriginRestrictionMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case allow
        case allowSpecificRepositories
        case block
        case inherit
        case sdkUnknown(Swift.String)

        public static var allCases: [PackageGroupOriginRestrictionMode] {
            return [
                .allow,
                .allowSpecificRepositories,
                .block,
                .inherit
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .allow: return "ALLOW"
            case .allowSpecificRepositories: return "ALLOW_SPECIFIC_REPOSITORIES"
            case .block: return "BLOCK"
            case .inherit: return "INHERIT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeartifactClientTypes {

    /// Information about the identifiers of a package group.
    public struct PackageGroupReference: Swift.Sendable {
        /// The ARN of the package group.
        public var arn: Swift.String?
        /// The pattern of the package group. The pattern determines which packages are associated with the package group, and is also the identifier of the package group.
        public var pattern: Swift.String?

        public init(
            arn: Swift.String? = nil,
            pattern: Swift.String? = nil
        )
        {
            self.arn = arn
            self.pattern = pattern
        }
    }
}

extension CodeartifactClientTypes {

    /// Contains information about the configured restrictions of the origin controls of a package group.
    public struct PackageGroupOriginRestriction: Swift.Sendable {
        /// The effective package group origin restriction setting. If the value of mode is ALLOW, ALLOW_SPECIFIC_REPOSITORIES, or BLOCK, then the value of effectiveMode is the same. Otherwise, when the value of mode is INHERIT, then the value of effectiveMode is the value of mode of the first parent group which does not have a value of INHERIT.
        public var effectiveMode: CodeartifactClientTypes.PackageGroupOriginRestrictionMode?
        /// The parent package group that the package group origin restrictions are inherited from.
        public var inheritedFrom: CodeartifactClientTypes.PackageGroupReference?
        /// The package group origin restriction setting. If the value of mode is ALLOW, ALLOW_SPECIFIC_REPOSITORIES, or BLOCK, then the value of effectiveMode is the same. Otherwise, when the value is INHERIT, then the value of effectiveMode is the value of mode of the first parent group which does not have a value of INHERIT.
        public var mode: CodeartifactClientTypes.PackageGroupOriginRestrictionMode?
        /// The number of repositories in the allowed repository list.
        public var repositoriesCount: Swift.Int?

        public init(
            effectiveMode: CodeartifactClientTypes.PackageGroupOriginRestrictionMode? = nil,
            inheritedFrom: CodeartifactClientTypes.PackageGroupReference? = nil,
            mode: CodeartifactClientTypes.PackageGroupOriginRestrictionMode? = nil,
            repositoriesCount: Swift.Int? = nil
        )
        {
            self.effectiveMode = effectiveMode
            self.inheritedFrom = inheritedFrom
            self.mode = mode
            self.repositoriesCount = repositoriesCount
        }
    }
}

extension CodeartifactClientTypes {

    /// The package group origin configuration that determines how package versions can enter repositories.
    public struct PackageGroupOriginConfiguration: Swift.Sendable {
        /// The origin configuration settings that determine how package versions can enter repositories.
        public var restrictions: [Swift.String: CodeartifactClientTypes.PackageGroupOriginRestriction]?

        public init(
            restrictions: [Swift.String: CodeartifactClientTypes.PackageGroupOriginRestriction]? = nil
        )
        {
            self.restrictions = restrictions
        }
    }
}

extension CodeartifactClientTypes {

    /// The description of the package group.
    public struct PackageGroupDescription: Swift.Sendable {
        /// The ARN of the package group.
        public var arn: Swift.String?
        /// The contact information of the package group.
        public var contactInfo: Swift.String?
        /// A timestamp that represents the date and time the package group was created.
        public var createdTime: Foundation.Date?
        /// The description of the package group.
        public var description: Swift.String?
        /// The name of the domain that contains the package group.
        public var domainName: Swift.String?
        /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
        public var domainOwner: Swift.String?
        /// The package group origin configuration that determines how package versions can enter repositories.
        public var originConfiguration: CodeartifactClientTypes.PackageGroupOriginConfiguration?
        /// The direct parent package group of the package group.
        public var parent: CodeartifactClientTypes.PackageGroupReference?
        /// The pattern of the package group. The pattern determines which packages are associated with the package group.
        public var pattern: Swift.String?

        public init(
            arn: Swift.String? = nil,
            contactInfo: Swift.String? = nil,
            createdTime: Foundation.Date? = nil,
            description: Swift.String? = nil,
            domainName: Swift.String? = nil,
            domainOwner: Swift.String? = nil,
            originConfiguration: CodeartifactClientTypes.PackageGroupOriginConfiguration? = nil,
            parent: CodeartifactClientTypes.PackageGroupReference? = nil,
            pattern: Swift.String? = nil
        )
        {
            self.arn = arn
            self.contactInfo = contactInfo
            self.createdTime = createdTime
            self.description = description
            self.domainName = domainName
            self.domainOwner = domainOwner
            self.originConfiguration = originConfiguration
            self.parent = parent
            self.pattern = pattern
        }
    }
}

public struct CreatePackageGroupOutput: Swift.Sendable {
    /// Information about the created package group after processing the request.
    public var packageGroup: CodeartifactClientTypes.PackageGroupDescription?

    public init(
        packageGroup: CodeartifactClientTypes.PackageGroupDescription? = nil
    )
    {
        self.packageGroup = packageGroup
    }
}

extension CodeartifactClientTypes {

    /// Information about an upstream repository. A list of UpstreamRepository objects is an input parameter to [CreateRepository](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_CreateRepository.html) and [UpdateRepository](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_UpdateRepository.html).
    public struct UpstreamRepository: Swift.Sendable {
        /// The name of an upstream repository.
        /// This member is required.
        public var repositoryName: Swift.String?

        public init(
            repositoryName: Swift.String? = nil
        )
        {
            self.repositoryName = repositoryName
        }
    }
}

public struct CreateRepositoryInput: Swift.Sendable {
    /// A description of the created repository.
    public var description: Swift.String?
    /// The name of the domain that contains the created repository.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The name of the repository to create.
    /// This member is required.
    public var repository: Swift.String?
    /// One or more tag key-value pairs for the repository.
    public var tags: [CodeartifactClientTypes.Tag]?
    /// A list of upstream repositories to associate with the repository. The order of the upstream repositories in the list determines their priority order when CodeArtifact looks for a requested package version. For more information, see [Working with upstream repositories](https://docs.aws.amazon.com/codeartifact/latest/ug/repos-upstream.html).
    public var upstreams: [CodeartifactClientTypes.UpstreamRepository]?

    public init(
        description: Swift.String? = nil,
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        repository: Swift.String? = nil,
        tags: [CodeartifactClientTypes.Tag]? = nil,
        upstreams: [CodeartifactClientTypes.UpstreamRepository]? = nil
    )
    {
        self.description = description
        self.domain = domain
        self.domainOwner = domainOwner
        self.repository = repository
        self.tags = tags
        self.upstreams = upstreams
    }
}

public struct CreateRepositoryOutput: Swift.Sendable {
    /// Information about the created repository after processing the request.
    public var repository: CodeartifactClientTypes.RepositoryDescription?

    public init(
        repository: CodeartifactClientTypes.RepositoryDescription? = nil
    )
    {
        self.repository = repository
    }
}

public struct DeleteDomainInput: Swift.Sendable {
    /// The name of the domain to delete.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
    }
}

public struct DeleteDomainOutput: Swift.Sendable {
    /// Contains information about the deleted domain after processing the request.
    public var domain: CodeartifactClientTypes.DomainDescription?

    public init(
        domain: CodeartifactClientTypes.DomainDescription? = nil
    )
    {
        self.domain = domain
    }
}

public struct DeleteDomainPermissionsPolicyInput: Swift.Sendable {
    /// The name of the domain associated with the resource policy to be deleted.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The current revision of the resource policy to be deleted. This revision is used for optimistic locking, which prevents others from overwriting your changes to the domain's resource policy.
    public var policyRevision: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        policyRevision: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.policyRevision = policyRevision
    }
}

extension CodeartifactClientTypes {

    /// An CodeArtifact resource policy that contains a resource ARN, document details, and a revision.
    public struct ResourcePolicy: Swift.Sendable {
        /// The resource policy formatted in JSON.
        public var document: Swift.String?
        /// The ARN of the resource associated with the resource policy
        public var resourceArn: Swift.String?
        /// The current revision of the resource policy.
        public var revision: Swift.String?

        public init(
            document: Swift.String? = nil,
            resourceArn: Swift.String? = nil,
            revision: Swift.String? = nil
        )
        {
            self.document = document
            self.resourceArn = resourceArn
            self.revision = revision
        }
    }
}

public struct DeleteDomainPermissionsPolicyOutput: Swift.Sendable {
    /// Information about the deleted resource policy after processing the request.
    public var policy: CodeartifactClientTypes.ResourcePolicy?

    public init(
        policy: CodeartifactClientTypes.ResourcePolicy? = nil
    )
    {
        self.policy = policy
    }
}

public struct DeletePackageInput: Swift.Sendable {
    /// The name of the domain that contains the package to delete.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The format of the requested package to delete.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package to delete. The package component that specifies its namespace depends on its type. For example: The namespace is required when deleting packages of the following formats:
    ///
    /// * Maven
    ///
    /// * Swift
    ///
    /// * generic
    ///
    ///
    ///
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The name of the package to delete.
    /// This member is required.
    public var package: Swift.String?
    /// The name of the repository that contains the package to delete.
    /// This member is required.
    public var repository: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        package: Swift.String? = nil,
        repository: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.format = format
        self.namespace = namespace
        self.package = package
        self.repository = repository
    }
}

extension CodeartifactClientTypes {

    /// Details about the origin restrictions set on the package. The package origin restrictions determine how new versions of a package can be added to a specific repository.
    public struct PackageOriginRestrictions: Swift.Sendable {
        /// The package origin configuration that determines if new versions of the package can be published directly to the repository.
        /// This member is required.
        public var publish: CodeartifactClientTypes.AllowPublish?
        /// The package origin configuration that determines if new versions of the package can be added to the repository from an external connection or upstream source.
        /// This member is required.
        public var upstream: CodeartifactClientTypes.AllowUpstream?

        public init(
            publish: CodeartifactClientTypes.AllowPublish? = nil,
            upstream: CodeartifactClientTypes.AllowUpstream? = nil
        )
        {
            self.publish = publish
            self.upstream = upstream
        }
    }
}

extension CodeartifactClientTypes {

    /// Details about the package origin configuration of a package.
    public struct PackageOriginConfiguration: Swift.Sendable {
        /// A PackageOriginRestrictions object that contains information about the upstream and publish package origin configuration for the package.
        public var restrictions: CodeartifactClientTypes.PackageOriginRestrictions?

        public init(
            restrictions: CodeartifactClientTypes.PackageOriginRestrictions? = nil
        )
        {
            self.restrictions = restrictions
        }
    }
}

extension CodeartifactClientTypes {

    /// Details about a package, including its format, namespace, and name.
    public struct PackageSummary: Swift.Sendable {
        /// The format of the package.
        public var format: CodeartifactClientTypes.PackageFormat?
        /// The namespace of the package. The package component that specifies its namespace depends on its type. For example:
        ///
        /// * The namespace of a Maven package version is its groupId.
        ///
        /// * The namespace of an npm or Swift package version is its scope.
        ///
        /// * The namespace of a generic package is its namespace.
        ///
        /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
        public var namespace: Swift.String?
        /// A [PackageOriginConfiguration](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageOriginConfiguration.html) object that contains a [PackageOriginRestrictions](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageOriginRestrictions.html) object that contains information about the upstream and publish package origin restrictions.
        public var originConfiguration: CodeartifactClientTypes.PackageOriginConfiguration?
        /// The name of the package.
        public var package: Swift.String?

        public init(
            format: CodeartifactClientTypes.PackageFormat? = nil,
            namespace: Swift.String? = nil,
            originConfiguration: CodeartifactClientTypes.PackageOriginConfiguration? = nil,
            package: Swift.String? = nil
        )
        {
            self.format = format
            self.namespace = namespace
            self.originConfiguration = originConfiguration
            self.package = package
        }
    }
}

public struct DeletePackageOutput: Swift.Sendable {
    /// Details about a package, including its format, namespace, and name.
    public var deletedPackage: CodeartifactClientTypes.PackageSummary?

    public init(
        deletedPackage: CodeartifactClientTypes.PackageSummary? = nil
    )
    {
        self.deletedPackage = deletedPackage
    }
}

public struct DeletePackageGroupInput: Swift.Sendable {
    /// The domain that contains the package group to be deleted.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The pattern of the package group to be deleted.
    /// This member is required.
    public var packageGroup: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        packageGroup: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.packageGroup = packageGroup
    }
}

public struct DeletePackageGroupOutput: Swift.Sendable {
    /// Information about the deleted package group after processing the request.
    public var packageGroup: CodeartifactClientTypes.PackageGroupDescription?

    public init(
        packageGroup: CodeartifactClientTypes.PackageGroupDescription? = nil
    )
    {
        self.packageGroup = packageGroup
    }
}

public struct DeletePackageVersionsInput: Swift.Sendable {
    /// The name of the domain that contains the package to delete.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The expected status of the package version to delete.
    public var expectedStatus: CodeartifactClientTypes.PackageVersionStatus?
    /// The format of the package versions to delete.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package versions to be deleted. The package component that specifies its namespace depends on its type. For example: The namespace is required when deleting package versions of the following formats:
    ///
    /// * Maven
    ///
    /// * Swift
    ///
    /// * generic
    ///
    ///
    ///
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The name of the package with the versions to delete.
    /// This member is required.
    public var package: Swift.String?
    /// The name of the repository that contains the package versions to delete.
    /// This member is required.
    public var repository: Swift.String?
    /// An array of strings that specify the versions of the package to delete.
    /// This member is required.
    public var versions: [Swift.String]?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        expectedStatus: CodeartifactClientTypes.PackageVersionStatus? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        package: Swift.String? = nil,
        repository: Swift.String? = nil,
        versions: [Swift.String]? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.expectedStatus = expectedStatus
        self.format = format
        self.namespace = namespace
        self.package = package
        self.repository = repository
        self.versions = versions
    }
}

public struct DeletePackageVersionsOutput: Swift.Sendable {
    /// A PackageVersionError object that contains a map of errors codes for the deleted package that failed. The possible error codes are:
    ///
    /// * ALREADY_EXISTS
    ///
    /// * MISMATCHED_REVISION
    ///
    /// * MISMATCHED_STATUS
    ///
    /// * NOT_ALLOWED
    ///
    /// * NOT_FOUND
    ///
    /// * SKIPPED
    public var failedVersions: [Swift.String: CodeartifactClientTypes.PackageVersionError]?
    /// A list of the package versions that were successfully deleted. The status of every successful version will be Deleted.
    public var successfulVersions: [Swift.String: CodeartifactClientTypes.SuccessfulPackageVersionInfo]?

    public init(
        failedVersions: [Swift.String: CodeartifactClientTypes.PackageVersionError]? = nil,
        successfulVersions: [Swift.String: CodeartifactClientTypes.SuccessfulPackageVersionInfo]? = nil
    )
    {
        self.failedVersions = failedVersions
        self.successfulVersions = successfulVersions
    }
}

public struct DeleteRepositoryInput: Swift.Sendable {
    /// The name of the domain that contains the repository to delete.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The name of the repository to delete.
    /// This member is required.
    public var repository: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        repository: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.repository = repository
    }
}

public struct DeleteRepositoryOutput: Swift.Sendable {
    /// Information about the deleted repository after processing the request.
    public var repository: CodeartifactClientTypes.RepositoryDescription?

    public init(
        repository: CodeartifactClientTypes.RepositoryDescription? = nil
    )
    {
        self.repository = repository
    }
}

public struct DeleteRepositoryPermissionsPolicyInput: Swift.Sendable {
    /// The name of the domain that contains the repository associated with the resource policy to be deleted.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The revision of the repository's resource policy to be deleted. This revision is used for optimistic locking, which prevents others from accidentally overwriting your changes to the repository's resource policy.
    public var policyRevision: Swift.String?
    /// The name of the repository that is associated with the resource policy to be deleted
    /// This member is required.
    public var repository: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        policyRevision: Swift.String? = nil,
        repository: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.policyRevision = policyRevision
        self.repository = repository
    }
}

public struct DeleteRepositoryPermissionsPolicyOutput: Swift.Sendable {
    /// Information about the deleted policy after processing the request.
    public var policy: CodeartifactClientTypes.ResourcePolicy?

    public init(
        policy: CodeartifactClientTypes.ResourcePolicy? = nil
    )
    {
        self.policy = policy
    }
}

public struct DescribeDomainInput: Swift.Sendable {
    /// A string that specifies the name of the requested domain.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
    }
}

public struct DescribeDomainOutput: Swift.Sendable {
    /// Information about a domain. A domain is a container for repositories. When you create a domain, it is empty until you add one or more repositories.
    public var domain: CodeartifactClientTypes.DomainDescription?

    public init(
        domain: CodeartifactClientTypes.DomainDescription? = nil
    )
    {
        self.domain = domain
    }
}

public struct DescribePackageInput: Swift.Sendable {
    /// The name of the domain that contains the repository that contains the package.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// A format that specifies the type of the requested package.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the requested package. The package component that specifies its namespace depends on its type. For example: The namespace is required when requesting packages of the following formats:
    ///
    /// * Maven
    ///
    /// * Swift
    ///
    /// * generic
    ///
    ///
    ///
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The name of the requested package.
    /// This member is required.
    public var package: Swift.String?
    /// The name of the repository that contains the requested package.
    /// This member is required.
    public var repository: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        package: Swift.String? = nil,
        repository: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.format = format
        self.namespace = namespace
        self.package = package
        self.repository = repository
    }
}

extension CodeartifactClientTypes {

    /// Details about a package.
    public struct PackageDescription: Swift.Sendable {
        /// A format that specifies the type of the package.
        public var format: CodeartifactClientTypes.PackageFormat?
        /// The name of the package.
        public var name: Swift.String?
        /// The namespace of the package. The package component that specifies its namespace depends on its type. For example:
        ///
        /// * The namespace of a Maven package version is its groupId.
        ///
        /// * The namespace of an npm or Swift package version is its scope.
        ///
        /// * The namespace of a generic package is its namespace.
        ///
        /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
        public var namespace: Swift.String?
        /// The package origin configuration for the package.
        public var originConfiguration: CodeartifactClientTypes.PackageOriginConfiguration?

        public init(
            format: CodeartifactClientTypes.PackageFormat? = nil,
            name: Swift.String? = nil,
            namespace: Swift.String? = nil,
            originConfiguration: CodeartifactClientTypes.PackageOriginConfiguration? = nil
        )
        {
            self.format = format
            self.name = name
            self.namespace = namespace
            self.originConfiguration = originConfiguration
        }
    }
}

public struct DescribePackageOutput: Swift.Sendable {
    /// A [PackageDescription](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageDescription.html) object that contains information about the requested package.
    /// This member is required.
    public var package: CodeartifactClientTypes.PackageDescription?

    public init(
        package: CodeartifactClientTypes.PackageDescription? = nil
    )
    {
        self.package = package
    }
}

public struct DescribePackageGroupInput: Swift.Sendable {
    /// The name of the domain that contains the package group.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The pattern of the requested package group.
    /// This member is required.
    public var packageGroup: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        packageGroup: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.packageGroup = packageGroup
    }
}

public struct DescribePackageGroupOutput: Swift.Sendable {
    /// A [PackageGroupDescription](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageGroupDescription.html) object that contains information about the requested package group.
    public var packageGroup: CodeartifactClientTypes.PackageGroupDescription?

    public init(
        packageGroup: CodeartifactClientTypes.PackageGroupDescription? = nil
    )
    {
        self.packageGroup = packageGroup
    }
}

public struct DescribePackageVersionInput: Swift.Sendable {
    /// The name of the domain that contains the repository that contains the package version.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// A format that specifies the type of the requested package version.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the requested package version. The package component that specifies its namespace depends on its type. For example: The namespace is required when requesting package versions of the following formats:
    ///
    /// * Maven
    ///
    /// * Swift
    ///
    /// * generic
    ///
    ///
    ///
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The name of the requested package version.
    /// This member is required.
    public var package: Swift.String?
    /// A string that contains the package version (for example, 3.5.2).
    /// This member is required.
    public var packageVersion: Swift.String?
    /// The name of the repository that contains the package version.
    /// This member is required.
    public var repository: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        package: Swift.String? = nil,
        packageVersion: Swift.String? = nil,
        repository: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.format = format
        self.namespace = namespace
        self.package = package
        self.packageVersion = packageVersion
        self.repository = repository
    }
}

extension CodeartifactClientTypes {

    /// Details of the license data.
    public struct LicenseInfo: Swift.Sendable {
        /// Name of the license.
        public var name: Swift.String?
        /// The URL for license data.
        public var url: Swift.String?

        public init(
            name: Swift.String? = nil,
            url: Swift.String? = nil
        )
        {
            self.name = name
            self.url = url
        }
    }
}

extension CodeartifactClientTypes {

    /// Information about how a package originally entered the CodeArtifact domain. For packages published directly to CodeArtifact, the entry point is the repository it was published to. For packages ingested from an external repository, the entry point is the external connection that it was ingested from. An external connection is a CodeArtifact repository that is connected to an external repository such as the npm registry or NuGet gallery. If a package version exists in a repository and is updated, for example if a package of the same version is added with additional assets, the package version's DomainEntryPoint will not change from the original package version's value.
    public struct DomainEntryPoint: Swift.Sendable {
        /// The name of the external connection that a package was ingested from.
        public var externalConnectionName: Swift.String?
        /// The name of the repository that a package was originally published to.
        public var repositoryName: Swift.String?

        public init(
            externalConnectionName: Swift.String? = nil,
            repositoryName: Swift.String? = nil
        )
        {
            self.externalConnectionName = externalConnectionName
            self.repositoryName = repositoryName
        }
    }
}

extension CodeartifactClientTypes {

    public enum PackageVersionOriginType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case external
        case `internal`
        case unknown
        case sdkUnknown(Swift.String)

        public static var allCases: [PackageVersionOriginType] {
            return [
                .external,
                .internal,
                .unknown
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .external: return "EXTERNAL"
            case .internal: return "INTERNAL"
            case .unknown: return "UNKNOWN"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeartifactClientTypes {

    /// Information about how a package version was added to a repository.
    public struct PackageVersionOrigin: Swift.Sendable {
        /// A [DomainEntryPoint](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_DomainEntryPoint.html) object that contains information about from which repository or external connection the package version was added to the domain.
        public var domainEntryPoint: CodeartifactClientTypes.DomainEntryPoint?
        /// Describes how the package version was originally added to the domain. An INTERNAL origin type means the package version was published directly to a repository in the domain. An EXTERNAL origin type means the package version was ingested from an external connection.
        public var originType: CodeartifactClientTypes.PackageVersionOriginType?

        public init(
            domainEntryPoint: CodeartifactClientTypes.DomainEntryPoint? = nil,
            originType: CodeartifactClientTypes.PackageVersionOriginType? = nil
        )
        {
            self.domainEntryPoint = domainEntryPoint
            self.originType = originType
        }
    }
}

extension CodeartifactClientTypes {

    /// Details about a package version.
    public struct PackageVersionDescription: Swift.Sendable {
        /// The name of the package that is displayed. The displayName varies depending on the package version's format. For example, if an npm package is named ui, is in the namespace vue, and has the format npm, then the displayName is @vue/ui.
        public var displayName: Swift.String?
        /// The format of the package version.
        public var format: CodeartifactClientTypes.PackageFormat?
        /// The homepage associated with the package.
        public var homePage: Swift.String?
        /// Information about licenses associated with the package version.
        public var licenses: [CodeartifactClientTypes.LicenseInfo]?
        /// The namespace of the package version. The package component that specifies its namespace depends on its type. For example:
        ///
        /// * The namespace of a Maven package version is its groupId.
        ///
        /// * The namespace of an npm or Swift package version is its scope.
        ///
        /// * The namespace of a generic package is its namespace.
        ///
        /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
        public var namespace: Swift.String?
        /// A [PackageVersionOrigin](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageVersionOrigin.html) object that contains information about how the package version was added to the repository.
        public var origin: CodeartifactClientTypes.PackageVersionOrigin?
        /// The name of the requested package.
        public var packageName: Swift.String?
        /// A timestamp that contains the date and time the package version was published.
        public var publishedTime: Foundation.Date?
        /// The revision of the package version.
        public var revision: Swift.String?
        /// The repository for the source code in the package version, or the source code used to build it.
        public var sourceCodeRepository: Swift.String?
        /// A string that contains the status of the package version.
        public var status: CodeartifactClientTypes.PackageVersionStatus?
        /// A summary of the package version. The summary is extracted from the package. The information in and detail level of the summary depends on the package version's format.
        public var summary: Swift.String?
        /// The version of the package.
        public var version: Swift.String?

        public init(
            displayName: Swift.String? = nil,
            format: CodeartifactClientTypes.PackageFormat? = nil,
            homePage: Swift.String? = nil,
            licenses: [CodeartifactClientTypes.LicenseInfo]? = nil,
            namespace: Swift.String? = nil,
            origin: CodeartifactClientTypes.PackageVersionOrigin? = nil,
            packageName: Swift.String? = nil,
            publishedTime: Foundation.Date? = nil,
            revision: Swift.String? = nil,
            sourceCodeRepository: Swift.String? = nil,
            status: CodeartifactClientTypes.PackageVersionStatus? = nil,
            summary: Swift.String? = nil,
            version: Swift.String? = nil
        )
        {
            self.displayName = displayName
            self.format = format
            self.homePage = homePage
            self.licenses = licenses
            self.namespace = namespace
            self.origin = origin
            self.packageName = packageName
            self.publishedTime = publishedTime
            self.revision = revision
            self.sourceCodeRepository = sourceCodeRepository
            self.status = status
            self.summary = summary
            self.version = version
        }
    }
}

public struct DescribePackageVersionOutput: Swift.Sendable {
    /// A [PackageVersionDescription](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageVersionDescription.html) object that contains information about the requested package version.
    /// This member is required.
    public var packageVersion: CodeartifactClientTypes.PackageVersionDescription?

    public init(
        packageVersion: CodeartifactClientTypes.PackageVersionDescription? = nil
    )
    {
        self.packageVersion = packageVersion
    }
}

public struct DescribeRepositoryInput: Swift.Sendable {
    /// The name of the domain that contains the repository to describe.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// A string that specifies the name of the requested repository.
    /// This member is required.
    public var repository: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        repository: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.repository = repository
    }
}

public struct DescribeRepositoryOutput: Swift.Sendable {
    /// A RepositoryDescription object that contains the requested repository information.
    public var repository: CodeartifactClientTypes.RepositoryDescription?

    public init(
        repository: CodeartifactClientTypes.RepositoryDescription? = nil
    )
    {
        self.repository = repository
    }
}

public struct DisassociateExternalConnectionInput: Swift.Sendable {
    /// The name of the domain that contains the repository from which to remove the external repository.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The name of the external connection to be removed from the repository.
    /// This member is required.
    public var externalConnection: Swift.String?
    /// The name of the repository from which the external connection will be removed.
    /// This member is required.
    public var repository: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        externalConnection: Swift.String? = nil,
        repository: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.externalConnection = externalConnection
        self.repository = repository
    }
}

public struct DisassociateExternalConnectionOutput: Swift.Sendable {
    /// The repository associated with the removed external connection.
    public var repository: CodeartifactClientTypes.RepositoryDescription?

    public init(
        repository: CodeartifactClientTypes.RepositoryDescription? = nil
    )
    {
        self.repository = repository
    }
}

public struct DisposePackageVersionsInput: Swift.Sendable {
    /// The name of the domain that contains the repository you want to dispose.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The expected status of the package version to dispose.
    public var expectedStatus: CodeartifactClientTypes.PackageVersionStatus?
    /// A format that specifies the type of package versions you want to dispose.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package versions to be disposed. The package component that specifies its namespace depends on its type. For example: The namespace is required when disposing package versions of the following formats:
    ///
    /// * Maven
    ///
    /// * Swift
    ///
    /// * generic
    ///
    ///
    ///
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The name of the package with the versions you want to dispose.
    /// This member is required.
    public var package: Swift.String?
    /// The name of the repository that contains the package versions you want to dispose.
    /// This member is required.
    public var repository: Swift.String?
    /// The revisions of the package versions you want to dispose.
    public var versionRevisions: [Swift.String: Swift.String]?
    /// The versions of the package you want to dispose.
    /// This member is required.
    public var versions: [Swift.String]?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        expectedStatus: CodeartifactClientTypes.PackageVersionStatus? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        package: Swift.String? = nil,
        repository: Swift.String? = nil,
        versionRevisions: [Swift.String: Swift.String]? = nil,
        versions: [Swift.String]? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.expectedStatus = expectedStatus
        self.format = format
        self.namespace = namespace
        self.package = package
        self.repository = repository
        self.versionRevisions = versionRevisions
        self.versions = versions
    }
}

public struct DisposePackageVersionsOutput: Swift.Sendable {
    /// A PackageVersionError object that contains a map of errors codes for the disposed package versions that failed. The possible error codes are:
    ///
    /// * ALREADY_EXISTS
    ///
    /// * MISMATCHED_REVISION
    ///
    /// * MISMATCHED_STATUS
    ///
    /// * NOT_ALLOWED
    ///
    /// * NOT_FOUND
    ///
    /// * SKIPPED
    public var failedVersions: [Swift.String: CodeartifactClientTypes.PackageVersionError]?
    /// A list of the package versions that were successfully disposed.
    public var successfulVersions: [Swift.String: CodeartifactClientTypes.SuccessfulPackageVersionInfo]?

    public init(
        failedVersions: [Swift.String: CodeartifactClientTypes.PackageVersionError]? = nil,
        successfulVersions: [Swift.String: CodeartifactClientTypes.SuccessfulPackageVersionInfo]? = nil
    )
    {
        self.failedVersions = failedVersions
        self.successfulVersions = successfulVersions
    }
}

public struct GetAssociatedPackageGroupInput: Swift.Sendable {
    /// The name of the domain that contains the package from which to get the associated package group.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The format of the package from which to get the associated package group.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package from which to get the associated package group. The package component that specifies its namespace depends on its type. For example: The namespace is required when getting associated package groups from packages of the following formats:
    ///
    /// * Maven
    ///
    /// * Swift
    ///
    /// * generic
    ///
    ///
    ///
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The package from which to get the associated package group.
    /// This member is required.
    public var package: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        package: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.format = format
        self.namespace = namespace
        self.package = package
    }
}

public struct GetAssociatedPackageGroupOutput: Swift.Sendable {
    /// Describes the strength of the association between the package and package group. A strong match is also known as an exact match, and a weak match is known as a relative match.
    public var associationType: CodeartifactClientTypes.PackageGroupAssociationType?
    /// The package group that is associated with the requested package.
    public var packageGroup: CodeartifactClientTypes.PackageGroupDescription?

    public init(
        associationType: CodeartifactClientTypes.PackageGroupAssociationType? = nil,
        packageGroup: CodeartifactClientTypes.PackageGroupDescription? = nil
    )
    {
        self.associationType = associationType
        self.packageGroup = packageGroup
    }
}

public struct GetAuthorizationTokenInput: Swift.Sendable {
    /// The name of the domain that is in scope for the generated authorization token.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The time, in seconds, that the generated authorization token is valid. Valid values are 0 and any number between 900 (15 minutes) and 43200 (12 hours). A value of 0 will set the expiration of the authorization token to the same expiration of the user's role's temporary credentials.
    public var durationSeconds: Swift.Int?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        durationSeconds: Swift.Int? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.durationSeconds = durationSeconds
    }
}

public struct GetAuthorizationTokenOutput: Swift.Sendable {
    /// The returned authentication token.
    public var authorizationToken: Swift.String?
    /// A timestamp that specifies the date and time the authorization token expires.
    public var expiration: Foundation.Date?

    public init(
        authorizationToken: Swift.String? = nil,
        expiration: Foundation.Date? = nil
    )
    {
        self.authorizationToken = authorizationToken
        self.expiration = expiration
    }
}

extension GetAuthorizationTokenOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CONTENT_REDACTED"
    }
}

public struct GetDomainPermissionsPolicyInput: Swift.Sendable {
    /// The name of the domain to which the resource policy is attached.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
    }
}

public struct GetDomainPermissionsPolicyOutput: Swift.Sendable {
    /// The returned resource policy.
    public var policy: CodeartifactClientTypes.ResourcePolicy?

    public init(
        policy: CodeartifactClientTypes.ResourcePolicy? = nil
    )
    {
        self.policy = policy
    }
}

public struct GetPackageVersionAssetInput: Swift.Sendable {
    /// The name of the requested asset.
    /// This member is required.
    public var asset: Swift.String?
    /// The name of the domain that contains the repository that contains the package version with the requested asset.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// A format that specifies the type of the package version with the requested asset file.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package version with the requested asset file. The package component that specifies its namespace depends on its type. For example: The namespace is required when requesting assets from package versions of the following formats:
    ///
    /// * Maven
    ///
    /// * Swift
    ///
    /// * generic
    ///
    ///
    ///
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The name of the package that contains the requested asset.
    /// This member is required.
    public var package: Swift.String?
    /// A string that contains the package version (for example, 3.5.2).
    /// This member is required.
    public var packageVersion: Swift.String?
    /// The name of the package version revision that contains the requested asset.
    public var packageVersionRevision: Swift.String?
    /// The repository that contains the package version with the requested asset.
    /// This member is required.
    public var repository: Swift.String?

    public init(
        asset: Swift.String? = nil,
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        package: Swift.String? = nil,
        packageVersion: Swift.String? = nil,
        packageVersionRevision: Swift.String? = nil,
        repository: Swift.String? = nil
    )
    {
        self.asset = asset
        self.domain = domain
        self.domainOwner = domainOwner
        self.format = format
        self.namespace = namespace
        self.package = package
        self.packageVersion = packageVersion
        self.packageVersionRevision = packageVersionRevision
        self.repository = repository
    }
}

public struct GetPackageVersionAssetOutput: Swift.Sendable {
    /// The binary file, or asset, that is downloaded.
    public var asset: Smithy.ByteStream?
    /// The name of the asset that is downloaded.
    public var assetName: Swift.String?
    /// A string that contains the package version (for example, 3.5.2).
    public var packageVersion: Swift.String?
    /// The name of the package version revision that contains the downloaded asset.
    public var packageVersionRevision: Swift.String?

    public init(
        asset: Smithy.ByteStream? = Smithy.ByteStream.data(Foundation.Data(base64Encoded: "")),
        assetName: Swift.String? = nil,
        packageVersion: Swift.String? = nil,
        packageVersionRevision: Swift.String? = nil
    )
    {
        self.asset = asset
        self.assetName = assetName
        self.packageVersion = packageVersion
        self.packageVersionRevision = packageVersionRevision
    }
}

public struct GetPackageVersionReadmeInput: Swift.Sendable {
    /// The name of the domain that contains the repository that contains the package version with the requested readme file.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// A format that specifies the type of the package version with the requested readme file.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package version with the requested readme file. The package component that specifies its namespace depends on its type. For example: The namespace is required when requesting the readme from package versions of the following formats:
    ///
    /// * Maven
    ///
    /// * Swift
    ///
    /// * generic
    ///
    ///
    ///
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The name of the package version that contains the requested readme file.
    /// This member is required.
    public var package: Swift.String?
    /// A string that contains the package version (for example, 3.5.2).
    /// This member is required.
    public var packageVersion: Swift.String?
    /// The repository that contains the package with the requested readme file.
    /// This member is required.
    public var repository: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        package: Swift.String? = nil,
        packageVersion: Swift.String? = nil,
        repository: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.format = format
        self.namespace = namespace
        self.package = package
        self.packageVersion = packageVersion
        self.repository = repository
    }
}

public struct GetPackageVersionReadmeOutput: Swift.Sendable {
    /// The format of the package with the requested readme file.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package version with the requested readme file. The package component that specifies its namespace depends on its type. For example:
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The name of the package that contains the returned readme file.
    public var package: Swift.String?
    /// The text of the returned readme file.
    public var readme: Swift.String?
    /// The version of the package with the requested readme file.
    public var version: Swift.String?
    /// The current revision associated with the package version.
    public var versionRevision: Swift.String?

    public init(
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        package: Swift.String? = nil,
        readme: Swift.String? = nil,
        version: Swift.String? = nil,
        versionRevision: Swift.String? = nil
    )
    {
        self.format = format
        self.namespace = namespace
        self.package = package
        self.readme = readme
        self.version = version
        self.versionRevision = versionRevision
    }
}

extension CodeartifactClientTypes {

    public enum EndpointType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case dualstack
        case ipv4
        case sdkUnknown(Swift.String)

        public static var allCases: [EndpointType] {
            return [
                .dualstack,
                .ipv4
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .dualstack: return "dualstack"
            case .ipv4: return "ipv4"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct GetRepositoryEndpointInput: Swift.Sendable {
    /// The name of the domain that contains the repository.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain that contains the repository. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// A string that specifies the type of endpoint.
    public var endpointType: CodeartifactClientTypes.EndpointType?
    /// Returns which endpoint of a repository to return. A repository has one endpoint for each package format.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The name of the repository.
    /// This member is required.
    public var repository: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        endpointType: CodeartifactClientTypes.EndpointType? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        repository: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.endpointType = endpointType
        self.format = format
        self.repository = repository
    }
}

public struct GetRepositoryEndpointOutput: Swift.Sendable {
    /// A string that specifies the URL of the returned endpoint.
    public var repositoryEndpoint: Swift.String?

    public init(
        repositoryEndpoint: Swift.String? = nil
    )
    {
        self.repositoryEndpoint = repositoryEndpoint
    }
}

public struct GetRepositoryPermissionsPolicyInput: Swift.Sendable {
    /// The name of the domain containing the repository whose associated resource policy is to be retrieved.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The name of the repository whose associated resource policy is to be retrieved.
    /// This member is required.
    public var repository: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        repository: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.repository = repository
    }
}

public struct GetRepositoryPermissionsPolicyOutput: Swift.Sendable {
    /// The returned resource policy.
    public var policy: CodeartifactClientTypes.ResourcePolicy?

    public init(
        policy: CodeartifactClientTypes.ResourcePolicy? = nil
    )
    {
        self.policy = policy
    }
}

public struct ListAllowedRepositoriesForGroupInput: Swift.Sendable {
    /// The name of the domain that contains the package group from which to list allowed repositories.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The maximum number of results to return per page.
    public var maxResults: Swift.Int?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// The origin configuration restriction type of which to list allowed repositories.
    /// This member is required.
    public var originRestrictionType: CodeartifactClientTypes.PackageGroupOriginRestrictionType?
    /// The pattern of the package group from which to list allowed repositories.
    /// This member is required.
    public var packageGroup: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        originRestrictionType: CodeartifactClientTypes.PackageGroupOriginRestrictionType? = nil,
        packageGroup: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.originRestrictionType = originRestrictionType
        self.packageGroup = packageGroup
    }
}

public struct ListAllowedRepositoriesForGroupOutput: Swift.Sendable {
    /// The list of allowed repositories for the package group and origin configuration restriction type.
    public var allowedRepositories: [Swift.String]?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        allowedRepositories: [Swift.String]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.allowedRepositories = allowedRepositories
        self.nextToken = nextToken
    }
}

public struct ListAssociatedPackagesInput: Swift.Sendable {
    /// The name of the domain that contains the package group from which to list associated packages.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The maximum number of results to return per page.
    public var maxResults: Swift.Int?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// The pattern of the package group from which to list associated packages.
    /// This member is required.
    public var packageGroup: Swift.String?
    /// When this flag is included, ListAssociatedPackages will return a list of packages that would be associated with a package group, even if it does not exist.
    public var preview: Swift.Bool?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        packageGroup: Swift.String? = nil,
        preview: Swift.Bool? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.packageGroup = packageGroup
        self.preview = preview
    }
}

public struct ListAssociatedPackagesOutput: Swift.Sendable {
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// The list of packages associated with the requested package group.
    public var packages: [CodeartifactClientTypes.AssociatedPackage]?

    public init(
        nextToken: Swift.String? = nil,
        packages: [CodeartifactClientTypes.AssociatedPackage]? = nil
    )
    {
        self.nextToken = nextToken
        self.packages = packages
    }
}

public struct ListDomainsInput: Swift.Sendable {
    /// The maximum number of results to return per page.
    public var maxResults: Swift.Int?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension CodeartifactClientTypes {

    /// Information about a domain, including its name, Amazon Resource Name (ARN), and status. The [ListDomains](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_ListDomains.html) operation returns a list of DomainSummary objects.
    public struct DomainSummary: Swift.Sendable {
        /// The ARN of the domain.
        public var arn: Swift.String?
        /// A timestamp that contains the date and time the domain was created.
        public var createdTime: Foundation.Date?
        /// The key used to encrypt the domain.
        public var encryptionKey: Swift.String?
        /// The name of the domain.
        public var name: Swift.String?
        /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
        public var owner: Swift.String?
        /// A string that contains the status of the domain.
        public var status: CodeartifactClientTypes.DomainStatus?

        public init(
            arn: Swift.String? = nil,
            createdTime: Foundation.Date? = nil,
            encryptionKey: Swift.String? = nil,
            name: Swift.String? = nil,
            owner: Swift.String? = nil,
            status: CodeartifactClientTypes.DomainStatus? = nil
        )
        {
            self.arn = arn
            self.createdTime = createdTime
            self.encryptionKey = encryptionKey
            self.name = name
            self.owner = owner
            self.status = status
        }
    }
}

public struct ListDomainsOutput: Swift.Sendable {
    /// The returned list of [DomainSummary](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_DomainSummary.html) objects.
    public var domains: [CodeartifactClientTypes.DomainSummary]?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?

    public init(
        domains: [CodeartifactClientTypes.DomainSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.domains = domains
        self.nextToken = nextToken
    }
}

public struct ListPackageGroupsInput: Swift.Sendable {
    /// The domain for which you want to list package groups.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The maximum number of results to return per page.
    public var maxResults: Swift.Int?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// A prefix for which to search package groups. When included, ListPackageGroups will return only package groups with patterns that match the prefix.
    public var `prefix`: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        `prefix`: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.`prefix` = `prefix`
    }
}

extension CodeartifactClientTypes {

    /// Details about a package group.
    public struct PackageGroupSummary: Swift.Sendable {
        /// The ARN of the package group.
        public var arn: Swift.String?
        /// The contact information of the package group.
        public var contactInfo: Swift.String?
        /// A timestamp that represents the date and time the repository was created.
        public var createdTime: Foundation.Date?
        /// The description of the package group.
        public var description: Swift.String?
        /// The domain that contains the package group.
        public var domainName: Swift.String?
        /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
        public var domainOwner: Swift.String?
        /// Details about the package origin configuration of a package group.
        public var originConfiguration: CodeartifactClientTypes.PackageGroupOriginConfiguration?
        /// The direct parent package group of the package group.
        public var parent: CodeartifactClientTypes.PackageGroupReference?
        /// The pattern of the package group. The pattern determines which packages are associated with the package group.
        public var pattern: Swift.String?

        public init(
            arn: Swift.String? = nil,
            contactInfo: Swift.String? = nil,
            createdTime: Foundation.Date? = nil,
            description: Swift.String? = nil,
            domainName: Swift.String? = nil,
            domainOwner: Swift.String? = nil,
            originConfiguration: CodeartifactClientTypes.PackageGroupOriginConfiguration? = nil,
            parent: CodeartifactClientTypes.PackageGroupReference? = nil,
            pattern: Swift.String? = nil
        )
        {
            self.arn = arn
            self.contactInfo = contactInfo
            self.createdTime = createdTime
            self.description = description
            self.domainName = domainName
            self.domainOwner = domainOwner
            self.originConfiguration = originConfiguration
            self.parent = parent
            self.pattern = pattern
        }
    }
}

public struct ListPackageGroupsOutput: Swift.Sendable {
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// The list of package groups in the requested domain.
    public var packageGroups: [CodeartifactClientTypes.PackageGroupSummary]?

    public init(
        nextToken: Swift.String? = nil,
        packageGroups: [CodeartifactClientTypes.PackageGroupSummary]? = nil
    )
    {
        self.nextToken = nextToken
        self.packageGroups = packageGroups
    }
}

public struct ListPackagesInput: Swift.Sendable {
    /// The name of the domain that contains the repository that contains the requested packages.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The format used to filter requested packages. Only packages from the provided format will be returned.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The maximum number of results to return per page.
    public var maxResults: Swift.Int?
    /// The namespace prefix used to filter requested packages. Only packages with a namespace that starts with the provided string value are returned. Note that although this option is called --namespace and not --namespace-prefix, it has prefix-matching behavior. Each package format uses namespace as follows:
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// A prefix used to filter requested packages. Only packages with names that start with packagePrefix are returned.
    public var packagePrefix: Swift.String?
    /// The value of the Publish package origin control restriction used to filter requested packages. Only packages with the provided restriction are returned. For more information, see [PackageOriginRestrictions](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageOriginRestrictions.html).
    public var publish: CodeartifactClientTypes.AllowPublish?
    /// The name of the repository that contains the requested packages.
    /// This member is required.
    public var repository: Swift.String?
    /// The value of the Upstream package origin control restriction used to filter requested packages. Only packages with the provided restriction are returned. For more information, see [PackageOriginRestrictions](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageOriginRestrictions.html).
    public var upstream: CodeartifactClientTypes.AllowUpstream?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        maxResults: Swift.Int? = nil,
        namespace: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        packagePrefix: Swift.String? = nil,
        publish: CodeartifactClientTypes.AllowPublish? = nil,
        repository: Swift.String? = nil,
        upstream: CodeartifactClientTypes.AllowUpstream? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.format = format
        self.maxResults = maxResults
        self.namespace = namespace
        self.nextToken = nextToken
        self.packagePrefix = packagePrefix
        self.publish = publish
        self.repository = repository
        self.upstream = upstream
    }
}

public struct ListPackagesOutput: Swift.Sendable {
    /// If there are additional results, this is the token for the next set of results.
    public var nextToken: Swift.String?
    /// The list of returned [PackageSummary](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageSummary.html) objects.
    public var packages: [CodeartifactClientTypes.PackageSummary]?

    public init(
        nextToken: Swift.String? = nil,
        packages: [CodeartifactClientTypes.PackageSummary]? = nil
    )
    {
        self.nextToken = nextToken
        self.packages = packages
    }
}

public struct ListPackageVersionAssetsInput: Swift.Sendable {
    /// The name of the domain that contains the repository associated with the package version assets.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The format of the package that contains the requested package version assets.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The maximum number of results to return per page.
    public var maxResults: Swift.Int?
    /// The namespace of the package version that contains the requested package version assets. The package component that specifies its namespace depends on its type. For example: The namespace is required requesting assets from package versions of the following formats:
    ///
    /// * Maven
    ///
    /// * Swift
    ///
    /// * generic
    ///
    ///
    ///
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// The name of the package that contains the requested package version assets.
    /// This member is required.
    public var package: Swift.String?
    /// A string that contains the package version (for example, 3.5.2).
    /// This member is required.
    public var packageVersion: Swift.String?
    /// The name of the repository that contains the package that contains the requested package version assets.
    /// This member is required.
    public var repository: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        maxResults: Swift.Int? = nil,
        namespace: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        package: Swift.String? = nil,
        packageVersion: Swift.String? = nil,
        repository: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.format = format
        self.maxResults = maxResults
        self.namespace = namespace
        self.nextToken = nextToken
        self.package = package
        self.packageVersion = packageVersion
        self.repository = repository
    }
}

public struct ListPackageVersionAssetsOutput: Swift.Sendable {
    /// The returned list of [AssetSummary](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_AssetSummary.html) objects.
    public var assets: [CodeartifactClientTypes.AssetSummary]?
    /// The format of the package that contains the requested package version assets.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package version that contains the requested package version assets. The package component that specifies its namespace depends on its type. For example:
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// If there are additional results, this is the token for the next set of results.
    public var nextToken: Swift.String?
    /// The name of the package that contains the requested package version assets.
    public var package: Swift.String?
    /// The version of the package associated with the requested assets.
    public var version: Swift.String?
    /// The current revision associated with the package version.
    public var versionRevision: Swift.String?

    public init(
        assets: [CodeartifactClientTypes.AssetSummary]? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        package: Swift.String? = nil,
        version: Swift.String? = nil,
        versionRevision: Swift.String? = nil
    )
    {
        self.assets = assets
        self.format = format
        self.namespace = namespace
        self.nextToken = nextToken
        self.package = package
        self.version = version
        self.versionRevision = versionRevision
    }
}

public struct ListPackageVersionDependenciesInput: Swift.Sendable {
    /// The name of the domain that contains the repository that contains the requested package version dependencies.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The format of the package with the requested dependencies.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package version with the requested dependencies. The package component that specifies its namespace depends on its type. For example: The namespace is required when listing dependencies from package versions of the following formats:
    ///
    /// * Maven
    ///
    ///
    ///
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm package version is its scope.
    ///
    /// * Python and NuGet package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// The name of the package versions' package.
    /// This member is required.
    public var package: Swift.String?
    /// A string that contains the package version (for example, 3.5.2).
    /// This member is required.
    public var packageVersion: Swift.String?
    /// The name of the repository that contains the requested package version.
    /// This member is required.
    public var repository: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        package: Swift.String? = nil,
        packageVersion: Swift.String? = nil,
        repository: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.format = format
        self.namespace = namespace
        self.nextToken = nextToken
        self.package = package
        self.packageVersion = packageVersion
        self.repository = repository
    }
}

extension CodeartifactClientTypes {

    /// Details about a package dependency.
    public struct PackageDependency: Swift.Sendable {
        /// The type of a package dependency. The possible values depend on the package type.
        ///
        /// * npm: regular, dev, peer, optional
        ///
        /// * maven: optional, parent, compile, runtime, test, system, provided. Note that parent is not a regular Maven dependency type; instead this is extracted from the  element if one is defined in the package version's POM file.
        ///
        /// * nuget: The dependencyType field is never set for NuGet packages.
        ///
        /// * pypi: Requires-Dist
        public var dependencyType: Swift.String?
        /// The namespace of the package that this package depends on. The package component that specifies its namespace depends on its type. For example:
        ///
        /// * The namespace of a Maven package version is its groupId.
        ///
        /// * The namespace of an npm or Swift package version is its scope.
        ///
        /// * The namespace of a generic package is its namespace.
        ///
        /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
        public var namespace: Swift.String?
        /// The name of the package that this package depends on.
        public var package: Swift.String?
        /// The required version, or version range, of the package that this package depends on. The version format is specific to the package type. For example, the following are possible valid required versions: 1.2.3, ^2.3.4, or 4.x.
        public var versionRequirement: Swift.String?

        public init(
            dependencyType: Swift.String? = nil,
            namespace: Swift.String? = nil,
            package: Swift.String? = nil,
            versionRequirement: Swift.String? = nil
        )
        {
            self.dependencyType = dependencyType
            self.namespace = namespace
            self.package = package
            self.versionRequirement = versionRequirement
        }
    }
}

public struct ListPackageVersionDependenciesOutput: Swift.Sendable {
    /// The returned list of [PackageDependency](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageDependency.html) objects.
    public var dependencies: [CodeartifactClientTypes.PackageDependency]?
    /// A format that specifies the type of the package that contains the returned dependencies.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package version that contains the returned dependencies. The package component that specifies its namespace depends on its type. For example: The namespace is required when listing dependencies from package versions of the following formats:
    ///
    /// * Maven
    ///
    ///
    ///
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm package version is its scope.
    ///
    /// * Python and NuGet package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// The name of the package that contains the returned package versions dependencies.
    public var package: Swift.String?
    /// The version of the package that is specified in the request.
    public var version: Swift.String?
    /// The current revision associated with the package version.
    public var versionRevision: Swift.String?

    public init(
        dependencies: [CodeartifactClientTypes.PackageDependency]? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        package: Swift.String? = nil,
        version: Swift.String? = nil,
        versionRevision: Swift.String? = nil
    )
    {
        self.dependencies = dependencies
        self.format = format
        self.namespace = namespace
        self.nextToken = nextToken
        self.package = package
        self.version = version
        self.versionRevision = versionRevision
    }
}

extension CodeartifactClientTypes {

    public enum PackageVersionSortType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case publishedTime
        case sdkUnknown(Swift.String)

        public static var allCases: [PackageVersionSortType] {
            return [
                .publishedTime
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .publishedTime: return "PUBLISHED_TIME"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct ListPackageVersionsInput: Swift.Sendable {
    /// The name of the domain that contains the repository that contains the requested package versions.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The format of the package versions you want to list.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The maximum number of results to return per page.
    public var maxResults: Swift.Int?
    /// The namespace of the package that contains the requested package versions. The package component that specifies its namespace depends on its type. For example: The namespace is required when deleting package versions of the following formats:
    ///
    /// * Maven
    ///
    /// * Swift
    ///
    /// * generic
    ///
    ///
    ///
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// The originType used to filter package versions. Only package versions with the provided originType will be returned.
    public var originType: CodeartifactClientTypes.PackageVersionOriginType?
    /// The name of the package for which you want to request package versions.
    /// This member is required.
    public var package: Swift.String?
    /// The name of the repository that contains the requested package versions.
    /// This member is required.
    public var repository: Swift.String?
    /// How to sort the requested list of package versions.
    public var sortBy: CodeartifactClientTypes.PackageVersionSortType?
    /// A string that filters the requested package versions by status.
    public var status: CodeartifactClientTypes.PackageVersionStatus?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        maxResults: Swift.Int? = nil,
        namespace: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        originType: CodeartifactClientTypes.PackageVersionOriginType? = nil,
        package: Swift.String? = nil,
        repository: Swift.String? = nil,
        sortBy: CodeartifactClientTypes.PackageVersionSortType? = nil,
        status: CodeartifactClientTypes.PackageVersionStatus? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.format = format
        self.maxResults = maxResults
        self.namespace = namespace
        self.nextToken = nextToken
        self.originType = originType
        self.package = package
        self.repository = repository
        self.sortBy = sortBy
        self.status = status
    }
}

extension CodeartifactClientTypes {

    /// Details about a package version, including its status, version, and revision. The [ListPackageVersions](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_ListPackageVersions.html) operation returns a list of PackageVersionSummary objects.
    public struct PackageVersionSummary: Swift.Sendable {
        /// A [PackageVersionOrigin](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageVersionOrigin.html) object that contains information about how the package version was added to the repository.
        public var origin: CodeartifactClientTypes.PackageVersionOrigin?
        /// The revision associated with a package version.
        public var revision: Swift.String?
        /// A string that contains the status of the package version. It can be one of the following:
        /// This member is required.
        public var status: CodeartifactClientTypes.PackageVersionStatus?
        /// Information about a package version.
        /// This member is required.
        public var version: Swift.String?

        public init(
            origin: CodeartifactClientTypes.PackageVersionOrigin? = nil,
            revision: Swift.String? = nil,
            status: CodeartifactClientTypes.PackageVersionStatus? = nil,
            version: Swift.String? = nil
        )
        {
            self.origin = origin
            self.revision = revision
            self.status = status
            self.version = version
        }
    }
}

public struct ListPackageVersionsOutput: Swift.Sendable {
    /// The default package version to display. This depends on the package format:
    ///
    /// * For Maven and PyPI packages, it's the most recently published package version.
    ///
    /// * For npm packages, it's the version referenced by the latest tag. If the latest tag is not set, it's the most recently published package version.
    public var defaultDisplayVersion: Swift.String?
    /// A format of the package.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package that contains the requested package versions. The package component that specifies its namespace depends on its type. For example:
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// If there are additional results, this is the token for the next set of results.
    public var nextToken: Swift.String?
    /// The name of the package.
    public var package: Swift.String?
    /// The returned list of [PackageVersionSummary](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageVersionSummary.html) objects.
    public var versions: [CodeartifactClientTypes.PackageVersionSummary]?

    public init(
        defaultDisplayVersion: Swift.String? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        package: Swift.String? = nil,
        versions: [CodeartifactClientTypes.PackageVersionSummary]? = nil
    )
    {
        self.defaultDisplayVersion = defaultDisplayVersion
        self.format = format
        self.namespace = namespace
        self.nextToken = nextToken
        self.package = package
        self.versions = versions
    }
}

public struct ListRepositoriesInput: Swift.Sendable {
    /// The maximum number of results to return per page.
    public var maxResults: Swift.Int?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// A prefix used to filter returned repositories. Only repositories with names that start with repositoryPrefix are returned.
    public var repositoryPrefix: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        repositoryPrefix: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.repositoryPrefix = repositoryPrefix
    }
}

extension CodeartifactClientTypes {

    /// Details about a repository, including its Amazon Resource Name (ARN), description, and domain information. The [ListRepositories](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_ListRepositories.html) operation returns a list of RepositorySummary objects.
    public struct RepositorySummary: Swift.Sendable {
        /// The Amazon Web Services account ID that manages the repository.
        public var administratorAccount: Swift.String?
        /// The ARN of the repository.
        public var arn: Swift.String?
        /// A timestamp that represents the date and time the repository was created.
        public var createdTime: Foundation.Date?
        /// The description of the repository.
        public var description: Swift.String?
        /// The name of the domain that contains the repository.
        public var domainName: Swift.String?
        /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
        public var domainOwner: Swift.String?
        /// The name of the repository.
        public var name: Swift.String?

        public init(
            administratorAccount: Swift.String? = nil,
            arn: Swift.String? = nil,
            createdTime: Foundation.Date? = nil,
            description: Swift.String? = nil,
            domainName: Swift.String? = nil,
            domainOwner: Swift.String? = nil,
            name: Swift.String? = nil
        )
        {
            self.administratorAccount = administratorAccount
            self.arn = arn
            self.createdTime = createdTime
            self.description = description
            self.domainName = domainName
            self.domainOwner = domainOwner
            self.name = name
        }
    }
}

public struct ListRepositoriesOutput: Swift.Sendable {
    /// If there are additional results, this is the token for the next set of results.
    public var nextToken: Swift.String?
    /// The returned list of [RepositorySummary](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_RepositorySummary.html) objects.
    public var repositories: [CodeartifactClientTypes.RepositorySummary]?

    public init(
        nextToken: Swift.String? = nil,
        repositories: [CodeartifactClientTypes.RepositorySummary]? = nil
    )
    {
        self.nextToken = nextToken
        self.repositories = repositories
    }
}

public struct ListRepositoriesInDomainInput: Swift.Sendable {
    /// Filter the list of repositories to only include those that are managed by the Amazon Web Services account ID.
    public var administratorAccount: Swift.String?
    /// The name of the domain that contains the returned list of repositories.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The maximum number of results to return per page.
    public var maxResults: Swift.Int?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// A prefix used to filter returned repositories. Only repositories with names that start with repositoryPrefix are returned.
    public var repositoryPrefix: Swift.String?

    public init(
        administratorAccount: Swift.String? = nil,
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        repositoryPrefix: Swift.String? = nil
    )
    {
        self.administratorAccount = administratorAccount
        self.domain = domain
        self.domainOwner = domainOwner
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.repositoryPrefix = repositoryPrefix
    }
}

public struct ListRepositoriesInDomainOutput: Swift.Sendable {
    /// If there are additional results, this is the token for the next set of results.
    public var nextToken: Swift.String?
    /// The returned list of repositories.
    public var repositories: [CodeartifactClientTypes.RepositorySummary]?

    public init(
        nextToken: Swift.String? = nil,
        repositories: [CodeartifactClientTypes.RepositorySummary]? = nil
    )
    {
        self.nextToken = nextToken
        self.repositories = repositories
    }
}

public struct ListSubPackageGroupsInput: Swift.Sendable {
    /// The name of the domain which contains the package group from which to list sub package groups.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The maximum number of results to return per page.
    public var maxResults: Swift.Int?
    /// The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// The pattern of the package group from which to list sub package groups.
    /// This member is required.
    public var packageGroup: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        packageGroup: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.packageGroup = packageGroup
    }
}

public struct ListSubPackageGroupsOutput: Swift.Sendable {
    /// If there are additional results, this is the token for the next set of results.
    public var nextToken: Swift.String?
    /// A list of sub package groups for the requested package group.
    public var packageGroups: [CodeartifactClientTypes.PackageGroupSummary]?

    public init(
        nextToken: Swift.String? = nil,
        packageGroups: [CodeartifactClientTypes.PackageGroupSummary]? = nil
    )
    {
        self.nextToken = nextToken
        self.packageGroups = packageGroups
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource to get tags for.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// A list of tag key and value pairs associated with the specified resource.
    public var tags: [CodeartifactClientTypes.Tag]?

    public init(
        tags: [CodeartifactClientTypes.Tag]? = nil
    )
    {
        self.tags = tags
    }
}

public struct PublishPackageVersionInput: Swift.Sendable {
    /// The content of the asset to publish.
    /// This member is required.
    public var assetContent: Smithy.ByteStream?
    /// The name of the asset to publish. Asset names can include Unicode letters and numbers, and the following special characters: ~ ! @ ^ & ( ) - ` _ + [ ] { } ; , . `
    /// This member is required.
    public var assetName: Swift.String?
    /// The SHA256 hash of the assetContent to publish. This value must be calculated by the caller and provided with the request (see [Publishing a generic package](https://docs.aws.amazon.com/codeartifact/latest/ug/using-generic.html#publishing-generic-packages) in the CodeArtifact User Guide). This value is used as an integrity check to verify that the assetContent has not changed after it was originally sent.
    /// This member is required.
    public var assetSHA256: Swift.String?
    /// The name of the domain that contains the repository that contains the package version to publish.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the AWS account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// A format that specifies the type of the package version with the requested asset file. The only supported value is generic.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package version to publish.
    public var namespace: Swift.String?
    /// The name of the package version to publish.
    /// This member is required.
    public var package: Swift.String?
    /// The package version to publish (for example, 3.5.2).
    /// This member is required.
    public var packageVersion: Swift.String?
    /// The name of the repository that the package version will be published to.
    /// This member is required.
    public var repository: Swift.String?
    /// Specifies whether the package version should remain in the unfinished state. If omitted, the package version status will be set to Published (see [Package version status](https://docs.aws.amazon.com/codeartifact/latest/ug/packages-overview.html#package-version-status) in the CodeArtifact User Guide). Valid values: unfinished
    public var unfinished: Swift.Bool?

    public init(
        assetContent: Smithy.ByteStream? = nil,
        assetName: Swift.String? = nil,
        assetSHA256: Swift.String? = nil,
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        package: Swift.String? = nil,
        packageVersion: Swift.String? = nil,
        repository: Swift.String? = nil,
        unfinished: Swift.Bool? = nil
    )
    {
        self.assetContent = assetContent
        self.assetName = assetName
        self.assetSHA256 = assetSHA256
        self.domain = domain
        self.domainOwner = domainOwner
        self.format = format
        self.namespace = namespace
        self.package = package
        self.packageVersion = packageVersion
        self.repository = repository
        self.unfinished = unfinished
    }
}

public struct PublishPackageVersionOutput: Swift.Sendable {
    /// An [AssetSummary](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_AssetSummary.html) for the published asset.
    public var asset: CodeartifactClientTypes.AssetSummary?
    /// The format of the package version.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package version.
    public var namespace: Swift.String?
    /// The name of the package.
    public var package: Swift.String?
    /// A string that contains the status of the package version. For more information, see [Package version status](https://docs.aws.amazon.com/codeartifact/latest/ug/packages-overview.html#package-version-status.html#package-version-status) in the CodeArtifact User Guide.
    public var status: CodeartifactClientTypes.PackageVersionStatus?
    /// The version of the package.
    public var version: Swift.String?
    /// The revision of the package version.
    public var versionRevision: Swift.String?

    public init(
        asset: CodeartifactClientTypes.AssetSummary? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        package: Swift.String? = nil,
        status: CodeartifactClientTypes.PackageVersionStatus? = nil,
        version: Swift.String? = nil,
        versionRevision: Swift.String? = nil
    )
    {
        self.asset = asset
        self.format = format
        self.namespace = namespace
        self.package = package
        self.status = status
        self.version = version
        self.versionRevision = versionRevision
    }
}

public struct PutDomainPermissionsPolicyInput: Swift.Sendable {
    /// The name of the domain on which to set the resource policy.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// A valid displayable JSON Aspen policy string to be set as the access control resource policy on the provided domain.
    /// This member is required.
    public var policyDocument: Swift.String?
    /// The current revision of the resource policy to be set. This revision is used for optimistic locking, which prevents others from overwriting your changes to the domain's resource policy.
    public var policyRevision: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        policyDocument: Swift.String? = nil,
        policyRevision: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.policyDocument = policyDocument
        self.policyRevision = policyRevision
    }
}

public struct PutDomainPermissionsPolicyOutput: Swift.Sendable {
    /// The resource policy that was set after processing the request.
    public var policy: CodeartifactClientTypes.ResourcePolicy?

    public init(
        policy: CodeartifactClientTypes.ResourcePolicy? = nil
    )
    {
        self.policy = policy
    }
}

public struct PutPackageOriginConfigurationInput: Swift.Sendable {
    /// The name of the domain that contains the repository that contains the package.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// A format that specifies the type of the package to be updated.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package to be updated. The package component that specifies its namespace depends on its type. For example:
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The name of the package to be updated.
    /// This member is required.
    public var package: Swift.String?
    /// The name of the repository that contains the package.
    /// This member is required.
    public var repository: Swift.String?
    /// A [PackageOriginRestrictions](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageOriginRestrictions.html) object that contains information about the upstream and publish package origin restrictions. The upstream restriction determines if new package versions can be ingested or retained from external connections or upstream repositories. The publish restriction determines if new package versions can be published directly to the repository. You must include both the desired upstream and publish restrictions.
    /// This member is required.
    public var restrictions: CodeartifactClientTypes.PackageOriginRestrictions?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        package: Swift.String? = nil,
        repository: Swift.String? = nil,
        restrictions: CodeartifactClientTypes.PackageOriginRestrictions? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.format = format
        self.namespace = namespace
        self.package = package
        self.repository = repository
        self.restrictions = restrictions
    }
}

public struct PutPackageOriginConfigurationOutput: Swift.Sendable {
    /// A [PackageOriginConfiguration](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageOriginConfiguration.html) object that describes the origin configuration set for the package. It contains a [PackageOriginRestrictions](https://docs.aws.amazon.com/codeartifact/latest/APIReference/API_PackageOriginRestrictions.html) object that describes how new versions of the package can be introduced to the repository.
    public var originConfiguration: CodeartifactClientTypes.PackageOriginConfiguration?

    public init(
        originConfiguration: CodeartifactClientTypes.PackageOriginConfiguration? = nil
    )
    {
        self.originConfiguration = originConfiguration
    }
}

public struct PutRepositoryPermissionsPolicyInput: Swift.Sendable {
    /// The name of the domain containing the repository to set the resource policy on.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// A valid displayable JSON Aspen policy string to be set as the access control resource policy on the provided repository.
    /// This member is required.
    public var policyDocument: Swift.String?
    /// Sets the revision of the resource policy that specifies permissions to access the repository. This revision is used for optimistic locking, which prevents others from overwriting your changes to the repository's resource policy.
    public var policyRevision: Swift.String?
    /// The name of the repository to set the resource policy on.
    /// This member is required.
    public var repository: Swift.String?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        policyDocument: Swift.String? = nil,
        policyRevision: Swift.String? = nil,
        repository: Swift.String? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.policyDocument = policyDocument
        self.policyRevision = policyRevision
        self.repository = repository
    }
}

public struct PutRepositoryPermissionsPolicyOutput: Swift.Sendable {
    /// The resource policy that was set after processing the request.
    public var policy: CodeartifactClientTypes.ResourcePolicy?

    public init(
        policy: CodeartifactClientTypes.ResourcePolicy? = nil
    )
    {
        self.policy = policy
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource that you want to add or update tags for.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tags you want to modify or add to the resource.
    /// This member is required.
    public var tags: [CodeartifactClientTypes.Tag]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [CodeartifactClientTypes.Tag]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource that you want to remove tags from.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tag key for each tag that you want to remove from the resource.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UpdatePackageGroupInput: Swift.Sendable {
    /// Contact information which you want to update the requested package group with.
    public var contactInfo: Swift.String?
    /// The description you want to update the requested package group with.
    public var description: Swift.String?
    /// The name of the domain which contains the package group to be updated.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The pattern of the package group to be updated.
    /// This member is required.
    public var packageGroup: Swift.String?

    public init(
        contactInfo: Swift.String? = nil,
        description: Swift.String? = nil,
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        packageGroup: Swift.String? = nil
    )
    {
        self.contactInfo = contactInfo
        self.description = description
        self.domain = domain
        self.domainOwner = domainOwner
        self.packageGroup = packageGroup
    }
}

public struct UpdatePackageGroupOutput: Swift.Sendable {
    /// The package group and information about it after the request has been processed.
    public var packageGroup: CodeartifactClientTypes.PackageGroupDescription?

    public init(
        packageGroup: CodeartifactClientTypes.PackageGroupDescription? = nil
    )
    {
        self.packageGroup = packageGroup
    }
}

extension CodeartifactClientTypes {

    /// Details about an allowed repository for a package group, including its name and origin configuration.
    public struct PackageGroupAllowedRepository: Swift.Sendable {
        /// The origin configuration restriction type of the allowed repository.
        public var originRestrictionType: CodeartifactClientTypes.PackageGroupOriginRestrictionType?
        /// The name of the allowed repository.
        public var repositoryName: Swift.String?

        public init(
            originRestrictionType: CodeartifactClientTypes.PackageGroupOriginRestrictionType? = nil,
            repositoryName: Swift.String? = nil
        )
        {
            self.originRestrictionType = originRestrictionType
            self.repositoryName = repositoryName
        }
    }
}

public struct UpdatePackageGroupOriginConfigurationInput: Swift.Sendable {
    /// The repository name and restrictions to add to the allowed repository list of the specified package group.
    public var addAllowedRepositories: [CodeartifactClientTypes.PackageGroupAllowedRepository]?
    /// The name of the domain which contains the package group for which to update the origin configuration.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The pattern of the package group for which to update the origin configuration.
    /// This member is required.
    public var packageGroup: Swift.String?
    /// The repository name and restrictions to remove from the allowed repository list of the specified package group.
    public var removeAllowedRepositories: [CodeartifactClientTypes.PackageGroupAllowedRepository]?
    /// The origin configuration settings that determine how package versions can enter repositories.
    public var restrictions: [Swift.String: CodeartifactClientTypes.PackageGroupOriginRestrictionMode]?

    public init(
        addAllowedRepositories: [CodeartifactClientTypes.PackageGroupAllowedRepository]? = nil,
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        packageGroup: Swift.String? = nil,
        removeAllowedRepositories: [CodeartifactClientTypes.PackageGroupAllowedRepository]? = nil,
        restrictions: [Swift.String: CodeartifactClientTypes.PackageGroupOriginRestrictionMode]? = nil
    )
    {
        self.addAllowedRepositories = addAllowedRepositories
        self.domain = domain
        self.domainOwner = domainOwner
        self.packageGroup = packageGroup
        self.removeAllowedRepositories = removeAllowedRepositories
        self.restrictions = restrictions
    }
}

extension CodeartifactClientTypes {

    public enum PackageGroupAllowedRepositoryUpdateType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case added
        case removed
        case sdkUnknown(Swift.String)

        public static var allCases: [PackageGroupAllowedRepositoryUpdateType] {
            return [
                .added,
                .removed
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .added: return "ADDED"
            case .removed: return "REMOVED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct UpdatePackageGroupOriginConfigurationOutput: Swift.Sendable {
    /// Information about the updated allowed repositories after processing the request.
    public var allowedRepositoryUpdates: [Swift.String: [Swift.String: [Swift.String]]]?
    /// The package group and information about it after processing the request.
    public var packageGroup: CodeartifactClientTypes.PackageGroupDescription?

    public init(
        allowedRepositoryUpdates: [Swift.String: [Swift.String: [Swift.String]]]? = nil,
        packageGroup: CodeartifactClientTypes.PackageGroupDescription? = nil
    )
    {
        self.allowedRepositoryUpdates = allowedRepositoryUpdates
        self.packageGroup = packageGroup
    }
}

public struct UpdatePackageVersionsStatusInput: Swift.Sendable {
    /// The name of the domain that contains the repository that contains the package versions with a status to be updated.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The package version’s expected status before it is updated. If expectedStatus is provided, the package version's status is updated only if its status at the time UpdatePackageVersionsStatus is called matches expectedStatus.
    public var expectedStatus: CodeartifactClientTypes.PackageVersionStatus?
    /// A format that specifies the type of the package with the statuses to update.
    /// This member is required.
    public var format: CodeartifactClientTypes.PackageFormat?
    /// The namespace of the package version to be updated. The package component that specifies its namespace depends on its type. For example:
    ///
    /// * The namespace of a Maven package version is its groupId.
    ///
    /// * The namespace of an npm or Swift package version is its scope.
    ///
    /// * The namespace of a generic package is its namespace.
    ///
    /// * Python, NuGet, Ruby, and Cargo package versions do not contain a corresponding component, package versions of those formats do not have a namespace.
    public var namespace: Swift.String?
    /// The name of the package with the version statuses to update.
    /// This member is required.
    public var package: Swift.String?
    /// The repository that contains the package versions with the status you want to update.
    /// This member is required.
    public var repository: Swift.String?
    /// The status you want to change the package version status to.
    /// This member is required.
    public var targetStatus: CodeartifactClientTypes.PackageVersionStatus?
    /// A map of package versions and package version revisions. The map key is the package version (for example, 3.5.2), and the map value is the package version revision.
    public var versionRevisions: [Swift.String: Swift.String]?
    /// An array of strings that specify the versions of the package with the statuses to update.
    /// This member is required.
    public var versions: [Swift.String]?

    public init(
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        expectedStatus: CodeartifactClientTypes.PackageVersionStatus? = nil,
        format: CodeartifactClientTypes.PackageFormat? = nil,
        namespace: Swift.String? = nil,
        package: Swift.String? = nil,
        repository: Swift.String? = nil,
        targetStatus: CodeartifactClientTypes.PackageVersionStatus? = nil,
        versionRevisions: [Swift.String: Swift.String]? = nil,
        versions: [Swift.String]? = nil
    )
    {
        self.domain = domain
        self.domainOwner = domainOwner
        self.expectedStatus = expectedStatus
        self.format = format
        self.namespace = namespace
        self.package = package
        self.repository = repository
        self.targetStatus = targetStatus
        self.versionRevisions = versionRevisions
        self.versions = versions
    }
}

public struct UpdatePackageVersionsStatusOutput: Swift.Sendable {
    /// A list of SuccessfulPackageVersionInfo objects, one for each package version with a status that successfully updated.
    public var failedVersions: [Swift.String: CodeartifactClientTypes.PackageVersionError]?
    /// A list of PackageVersionError objects, one for each package version with a status that failed to update.
    public var successfulVersions: [Swift.String: CodeartifactClientTypes.SuccessfulPackageVersionInfo]?

    public init(
        failedVersions: [Swift.String: CodeartifactClientTypes.PackageVersionError]? = nil,
        successfulVersions: [Swift.String: CodeartifactClientTypes.SuccessfulPackageVersionInfo]? = nil
    )
    {
        self.failedVersions = failedVersions
        self.successfulVersions = successfulVersions
    }
}

public struct UpdateRepositoryInput: Swift.Sendable {
    /// An updated repository description.
    public var description: Swift.String?
    /// The name of the domain associated with the repository to update.
    /// This member is required.
    public var domain: Swift.String?
    /// The 12-digit account number of the Amazon Web Services account that owns the domain. It does not include dashes or spaces.
    public var domainOwner: Swift.String?
    /// The name of the repository to update.
    /// This member is required.
    public var repository: Swift.String?
    /// A list of upstream repositories to associate with the repository. The order of the upstream repositories in the list determines their priority order when CodeArtifact looks for a requested package version. For more information, see [Working with upstream repositories](https://docs.aws.amazon.com/codeartifact/latest/ug/repos-upstream.html).
    public var upstreams: [CodeartifactClientTypes.UpstreamRepository]?

    public init(
        description: Swift.String? = nil,
        domain: Swift.String? = nil,
        domainOwner: Swift.String? = nil,
        repository: Swift.String? = nil,
        upstreams: [CodeartifactClientTypes.UpstreamRepository]? = nil
    )
    {
        self.description = description
        self.domain = domain
        self.domainOwner = domainOwner
        self.repository = repository
        self.upstreams = upstreams
    }
}

public struct UpdateRepositoryOutput: Swift.Sendable {
    /// The updated repository.
    public var repository: CodeartifactClientTypes.RepositoryDescription?

    public init(
        repository: CodeartifactClientTypes.RepositoryDescription? = nil
    )
    {
        self.repository = repository
    }
}

extension AssociateExternalConnectionInput {

    static func urlPathProvider(_ value: AssociateExternalConnectionInput) -> Swift.String? {
        return "/v1/repository/external-connection"
    }
}

extension AssociateExternalConnectionInput {

    static func queryItemProvider(_ value: AssociateExternalConnectionInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let externalConnection = value.externalConnection else {
            let message = "Creating a URL Query Item failed. externalConnection is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let externalConnectionQueryItem = Smithy.URIQueryItem(name: "external-connection".urlPercentEncoding(), value: Swift.String(externalConnection).urlPercentEncoding())
        items.append(externalConnectionQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension CopyPackageVersionsInput {

    static func urlPathProvider(_ value: CopyPackageVersionsInput) -> Swift.String? {
        return "/v1/package/versions/copy"
    }
}

extension CopyPackageVersionsInput {

    static func queryItemProvider(_ value: CopyPackageVersionsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let sourceRepository = value.sourceRepository else {
            let message = "Creating a URL Query Item failed. sourceRepository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let sourceRepositoryQueryItem = Smithy.URIQueryItem(name: "source-repository".urlPercentEncoding(), value: Swift.String(sourceRepository).urlPercentEncoding())
        items.append(sourceRepositoryQueryItem)
        guard let destinationRepository = value.destinationRepository else {
            let message = "Creating a URL Query Item failed. destinationRepository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let destinationRepositoryQueryItem = Smithy.URIQueryItem(name: "destination-repository".urlPercentEncoding(), value: Swift.String(destinationRepository).urlPercentEncoding())
        items.append(destinationRepositoryQueryItem)
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        return items
    }
}

extension CreateDomainInput {

    static func urlPathProvider(_ value: CreateDomainInput) -> Swift.String? {
        return "/v1/domain"
    }
}

extension CreateDomainInput {

    static func queryItemProvider(_ value: CreateDomainInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        return items
    }
}

extension CreatePackageGroupInput {

    static func urlPathProvider(_ value: CreatePackageGroupInput) -> Swift.String? {
        return "/v1/package-group"
    }
}

extension CreatePackageGroupInput {

    static func queryItemProvider(_ value: CreatePackageGroupInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        return items
    }
}

extension CreateRepositoryInput {

    static func urlPathProvider(_ value: CreateRepositoryInput) -> Swift.String? {
        return "/v1/repository"
    }
}

extension CreateRepositoryInput {

    static func queryItemProvider(_ value: CreateRepositoryInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension DeleteDomainInput {

    static func urlPathProvider(_ value: DeleteDomainInput) -> Swift.String? {
        return "/v1/domain"
    }
}

extension DeleteDomainInput {

    static func queryItemProvider(_ value: DeleteDomainInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        return items
    }
}

extension DeleteDomainPermissionsPolicyInput {

    static func urlPathProvider(_ value: DeleteDomainPermissionsPolicyInput) -> Swift.String? {
        return "/v1/domain/permissions/policy"
    }
}

extension DeleteDomainPermissionsPolicyInput {

    static func queryItemProvider(_ value: DeleteDomainPermissionsPolicyInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        if let policyRevision = value.policyRevision {
            let policyRevisionQueryItem = Smithy.URIQueryItem(name: "policy-revision".urlPercentEncoding(), value: Swift.String(policyRevision).urlPercentEncoding())
            items.append(policyRevisionQueryItem)
        }
        return items
    }
}

extension DeletePackageInput {

    static func urlPathProvider(_ value: DeletePackageInput) -> Swift.String? {
        return "/v1/package"
    }
}

extension DeletePackageInput {

    static func queryItemProvider(_ value: DeletePackageInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension DeletePackageGroupInput {

    static func urlPathProvider(_ value: DeletePackageGroupInput) -> Swift.String? {
        return "/v1/package-group"
    }
}

extension DeletePackageGroupInput {

    static func queryItemProvider(_ value: DeletePackageGroupInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let packageGroup = value.packageGroup else {
            let message = "Creating a URL Query Item failed. packageGroup is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageGroupQueryItem = Smithy.URIQueryItem(name: "package-group".urlPercentEncoding(), value: Swift.String(packageGroup).urlPercentEncoding())
        items.append(packageGroupQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        return items
    }
}

extension DeletePackageVersionsInput {

    static func urlPathProvider(_ value: DeletePackageVersionsInput) -> Swift.String? {
        return "/v1/package/versions/delete"
    }
}

extension DeletePackageVersionsInput {

    static func queryItemProvider(_ value: DeletePackageVersionsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension DeleteRepositoryInput {

    static func urlPathProvider(_ value: DeleteRepositoryInput) -> Swift.String? {
        return "/v1/repository"
    }
}

extension DeleteRepositoryInput {

    static func queryItemProvider(_ value: DeleteRepositoryInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension DeleteRepositoryPermissionsPolicyInput {

    static func urlPathProvider(_ value: DeleteRepositoryPermissionsPolicyInput) -> Swift.String? {
        return "/v1/repository/permissions/policies"
    }
}

extension DeleteRepositoryPermissionsPolicyInput {

    static func queryItemProvider(_ value: DeleteRepositoryPermissionsPolicyInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        if let policyRevision = value.policyRevision {
            let policyRevisionQueryItem = Smithy.URIQueryItem(name: "policy-revision".urlPercentEncoding(), value: Swift.String(policyRevision).urlPercentEncoding())
            items.append(policyRevisionQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension DescribeDomainInput {

    static func urlPathProvider(_ value: DescribeDomainInput) -> Swift.String? {
        return "/v1/domain"
    }
}

extension DescribeDomainInput {

    static func queryItemProvider(_ value: DescribeDomainInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        return items
    }
}

extension DescribePackageInput {

    static func urlPathProvider(_ value: DescribePackageInput) -> Swift.String? {
        return "/v1/package"
    }
}

extension DescribePackageInput {

    static func queryItemProvider(_ value: DescribePackageInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension DescribePackageGroupInput {

    static func urlPathProvider(_ value: DescribePackageGroupInput) -> Swift.String? {
        return "/v1/package-group"
    }
}

extension DescribePackageGroupInput {

    static func queryItemProvider(_ value: DescribePackageGroupInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let packageGroup = value.packageGroup else {
            let message = "Creating a URL Query Item failed. packageGroup is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageGroupQueryItem = Smithy.URIQueryItem(name: "package-group".urlPercentEncoding(), value: Swift.String(packageGroup).urlPercentEncoding())
        items.append(packageGroupQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        return items
    }
}

extension DescribePackageVersionInput {

    static func urlPathProvider(_ value: DescribePackageVersionInput) -> Swift.String? {
        return "/v1/package/version"
    }
}

extension DescribePackageVersionInput {

    static func queryItemProvider(_ value: DescribePackageVersionInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        guard let packageVersion = value.packageVersion else {
            let message = "Creating a URL Query Item failed. packageVersion is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageVersionQueryItem = Smithy.URIQueryItem(name: "version".urlPercentEncoding(), value: Swift.String(packageVersion).urlPercentEncoding())
        items.append(packageVersionQueryItem)
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension DescribeRepositoryInput {

    static func urlPathProvider(_ value: DescribeRepositoryInput) -> Swift.String? {
        return "/v1/repository"
    }
}

extension DescribeRepositoryInput {

    static func queryItemProvider(_ value: DescribeRepositoryInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension DisassociateExternalConnectionInput {

    static func urlPathProvider(_ value: DisassociateExternalConnectionInput) -> Swift.String? {
        return "/v1/repository/external-connection"
    }
}

extension DisassociateExternalConnectionInput {

    static func queryItemProvider(_ value: DisassociateExternalConnectionInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let externalConnection = value.externalConnection else {
            let message = "Creating a URL Query Item failed. externalConnection is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let externalConnectionQueryItem = Smithy.URIQueryItem(name: "external-connection".urlPercentEncoding(), value: Swift.String(externalConnection).urlPercentEncoding())
        items.append(externalConnectionQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension DisposePackageVersionsInput {

    static func urlPathProvider(_ value: DisposePackageVersionsInput) -> Swift.String? {
        return "/v1/package/versions/dispose"
    }
}

extension DisposePackageVersionsInput {

    static func queryItemProvider(_ value: DisposePackageVersionsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension GetAssociatedPackageGroupInput {

    static func urlPathProvider(_ value: GetAssociatedPackageGroupInput) -> Swift.String? {
        return "/v1/get-associated-package-group"
    }
}

extension GetAssociatedPackageGroupInput {

    static func queryItemProvider(_ value: GetAssociatedPackageGroupInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        return items
    }
}

extension GetAuthorizationTokenInput {

    static func urlPathProvider(_ value: GetAuthorizationTokenInput) -> Swift.String? {
        return "/v1/authorization-token"
    }
}

extension GetAuthorizationTokenInput {

    static func queryItemProvider(_ value: GetAuthorizationTokenInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let durationSeconds = value.durationSeconds {
            let durationSecondsQueryItem = Smithy.URIQueryItem(name: "duration".urlPercentEncoding(), value: Swift.String(durationSeconds).urlPercentEncoding())
            items.append(durationSecondsQueryItem)
        }
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        return items
    }
}

extension GetDomainPermissionsPolicyInput {

    static func urlPathProvider(_ value: GetDomainPermissionsPolicyInput) -> Swift.String? {
        return "/v1/domain/permissions/policy"
    }
}

extension GetDomainPermissionsPolicyInput {

    static func queryItemProvider(_ value: GetDomainPermissionsPolicyInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        return items
    }
}

extension GetPackageVersionAssetInput {

    static func urlPathProvider(_ value: GetPackageVersionAssetInput) -> Swift.String? {
        return "/v1/package/version/asset"
    }
}

extension GetPackageVersionAssetInput {

    static func queryItemProvider(_ value: GetPackageVersionAssetInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        guard let packageVersion = value.packageVersion else {
            let message = "Creating a URL Query Item failed. packageVersion is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageVersionQueryItem = Smithy.URIQueryItem(name: "version".urlPercentEncoding(), value: Swift.String(packageVersion).urlPercentEncoding())
        items.append(packageVersionQueryItem)
        if let packageVersionRevision = value.packageVersionRevision {
            let packageVersionRevisionQueryItem = Smithy.URIQueryItem(name: "revision".urlPercentEncoding(), value: Swift.String(packageVersionRevision).urlPercentEncoding())
            items.append(packageVersionRevisionQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        guard let asset = value.asset else {
            let message = "Creating a URL Query Item failed. asset is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let assetQueryItem = Smithy.URIQueryItem(name: "asset".urlPercentEncoding(), value: Swift.String(asset).urlPercentEncoding())
        items.append(assetQueryItem)
        return items
    }
}

extension GetPackageVersionReadmeInput {

    static func urlPathProvider(_ value: GetPackageVersionReadmeInput) -> Swift.String? {
        return "/v1/package/version/readme"
    }
}

extension GetPackageVersionReadmeInput {

    static func queryItemProvider(_ value: GetPackageVersionReadmeInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        guard let packageVersion = value.packageVersion else {
            let message = "Creating a URL Query Item failed. packageVersion is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageVersionQueryItem = Smithy.URIQueryItem(name: "version".urlPercentEncoding(), value: Swift.String(packageVersion).urlPercentEncoding())
        items.append(packageVersionQueryItem)
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension GetRepositoryEndpointInput {

    static func urlPathProvider(_ value: GetRepositoryEndpointInput) -> Swift.String? {
        return "/v1/repository/endpoint"
    }
}

extension GetRepositoryEndpointInput {

    static func queryItemProvider(_ value: GetRepositoryEndpointInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let endpointType = value.endpointType {
            let endpointTypeQueryItem = Smithy.URIQueryItem(name: "endpointType".urlPercentEncoding(), value: Swift.String(endpointType.rawValue).urlPercentEncoding())
            items.append(endpointTypeQueryItem)
        }
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension GetRepositoryPermissionsPolicyInput {

    static func urlPathProvider(_ value: GetRepositoryPermissionsPolicyInput) -> Swift.String? {
        return "/v1/repository/permissions/policy"
    }
}

extension GetRepositoryPermissionsPolicyInput {

    static func queryItemProvider(_ value: GetRepositoryPermissionsPolicyInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension ListAllowedRepositoriesForGroupInput {

    static func urlPathProvider(_ value: ListAllowedRepositoriesForGroupInput) -> Swift.String? {
        return "/v1/package-group-allowed-repositories"
    }
}

extension ListAllowedRepositoriesForGroupInput {

    static func queryItemProvider(_ value: ListAllowedRepositoriesForGroupInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let packageGroup = value.packageGroup else {
            let message = "Creating a URL Query Item failed. packageGroup is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageGroupQueryItem = Smithy.URIQueryItem(name: "package-group".urlPercentEncoding(), value: Swift.String(packageGroup).urlPercentEncoding())
        items.append(packageGroupQueryItem)
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "max-results".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "next-token".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let originRestrictionType = value.originRestrictionType else {
            let message = "Creating a URL Query Item failed. originRestrictionType is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let originRestrictionTypeQueryItem = Smithy.URIQueryItem(name: "originRestrictionType".urlPercentEncoding(), value: Swift.String(originRestrictionType.rawValue).urlPercentEncoding())
        items.append(originRestrictionTypeQueryItem)
        return items
    }
}

extension ListAssociatedPackagesInput {

    static func urlPathProvider(_ value: ListAssociatedPackagesInput) -> Swift.String? {
        return "/v1/list-associated-packages"
    }
}

extension ListAssociatedPackagesInput {

    static func queryItemProvider(_ value: ListAssociatedPackagesInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let preview = value.preview {
            let previewQueryItem = Smithy.URIQueryItem(name: "preview".urlPercentEncoding(), value: Swift.String(preview).urlPercentEncoding())
            items.append(previewQueryItem)
        }
        guard let packageGroup = value.packageGroup else {
            let message = "Creating a URL Query Item failed. packageGroup is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageGroupQueryItem = Smithy.URIQueryItem(name: "package-group".urlPercentEncoding(), value: Swift.String(packageGroup).urlPercentEncoding())
        items.append(packageGroupQueryItem)
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "max-results".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "next-token".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        return items
    }
}

extension ListDomainsInput {

    static func urlPathProvider(_ value: ListDomainsInput) -> Swift.String? {
        return "/v1/domains"
    }
}

extension ListPackageGroupsInput {

    static func urlPathProvider(_ value: ListPackageGroupsInput) -> Swift.String? {
        return "/v1/package-groups"
    }
}

extension ListPackageGroupsInput {

    static func queryItemProvider(_ value: ListPackageGroupsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "max-results".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "next-token".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let `prefix` = value.`prefix` {
            let prefixQueryItem = Smithy.URIQueryItem(name: "prefix".urlPercentEncoding(), value: Swift.String(`prefix`).urlPercentEncoding())
            items.append(prefixQueryItem)
        }
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        return items
    }
}

extension ListPackagesInput {

    static func urlPathProvider(_ value: ListPackagesInput) -> Swift.String? {
        return "/v1/packages"
    }
}

extension ListPackagesInput {

    static func queryItemProvider(_ value: ListPackagesInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let upstream = value.upstream {
            let upstreamQueryItem = Smithy.URIQueryItem(name: "upstream".urlPercentEncoding(), value: Swift.String(upstream.rawValue).urlPercentEncoding())
            items.append(upstreamQueryItem)
        }
        if let packagePrefix = value.packagePrefix {
            let packagePrefixQueryItem = Smithy.URIQueryItem(name: "package-prefix".urlPercentEncoding(), value: Swift.String(packagePrefix).urlPercentEncoding())
            items.append(packagePrefixQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "max-results".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "next-token".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let publish = value.publish {
            let publishQueryItem = Smithy.URIQueryItem(name: "publish".urlPercentEncoding(), value: Swift.String(publish.rawValue).urlPercentEncoding())
            items.append(publishQueryItem)
        }
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        if let format = value.format {
            let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
            items.append(formatQueryItem)
        }
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension ListPackageVersionAssetsInput {

    static func urlPathProvider(_ value: ListPackageVersionAssetsInput) -> Swift.String? {
        return "/v1/package/version/assets"
    }
}

extension ListPackageVersionAssetsInput {

    static func queryItemProvider(_ value: ListPackageVersionAssetsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "max-results".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "next-token".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        guard let packageVersion = value.packageVersion else {
            let message = "Creating a URL Query Item failed. packageVersion is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageVersionQueryItem = Smithy.URIQueryItem(name: "version".urlPercentEncoding(), value: Swift.String(packageVersion).urlPercentEncoding())
        items.append(packageVersionQueryItem)
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension ListPackageVersionDependenciesInput {

    static func urlPathProvider(_ value: ListPackageVersionDependenciesInput) -> Swift.String? {
        return "/v1/package/version/dependencies"
    }
}

extension ListPackageVersionDependenciesInput {

    static func queryItemProvider(_ value: ListPackageVersionDependenciesInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "next-token".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        guard let packageVersion = value.packageVersion else {
            let message = "Creating a URL Query Item failed. packageVersion is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageVersionQueryItem = Smithy.URIQueryItem(name: "version".urlPercentEncoding(), value: Swift.String(packageVersion).urlPercentEncoding())
        items.append(packageVersionQueryItem)
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension ListPackageVersionsInput {

    static func urlPathProvider(_ value: ListPackageVersionsInput) -> Swift.String? {
        return "/v1/package/versions"
    }
}

extension ListPackageVersionsInput {

    static func queryItemProvider(_ value: ListPackageVersionsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let originType = value.originType {
            let originTypeQueryItem = Smithy.URIQueryItem(name: "originType".urlPercentEncoding(), value: Swift.String(originType.rawValue).urlPercentEncoding())
            items.append(originTypeQueryItem)
        }
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "max-results".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "next-token".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        if let sortBy = value.sortBy {
            let sortByQueryItem = Smithy.URIQueryItem(name: "sortBy".urlPercentEncoding(), value: Swift.String(sortBy.rawValue).urlPercentEncoding())
            items.append(sortByQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        if let status = value.status {
            let statusQueryItem = Smithy.URIQueryItem(name: "status".urlPercentEncoding(), value: Swift.String(status.rawValue).urlPercentEncoding())
            items.append(statusQueryItem)
        }
        return items
    }
}

extension ListRepositoriesInput {

    static func urlPathProvider(_ value: ListRepositoriesInput) -> Swift.String? {
        return "/v1/repositories"
    }
}

extension ListRepositoriesInput {

    static func queryItemProvider(_ value: ListRepositoriesInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let repositoryPrefix = value.repositoryPrefix {
            let repositoryPrefixQueryItem = Smithy.URIQueryItem(name: "repository-prefix".urlPercentEncoding(), value: Swift.String(repositoryPrefix).urlPercentEncoding())
            items.append(repositoryPrefixQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "max-results".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "next-token".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        return items
    }
}

extension ListRepositoriesInDomainInput {

    static func urlPathProvider(_ value: ListRepositoriesInDomainInput) -> Swift.String? {
        return "/v1/domain/repositories"
    }
}

extension ListRepositoriesInDomainInput {

    static func queryItemProvider(_ value: ListRepositoriesInDomainInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let repositoryPrefix = value.repositoryPrefix {
            let repositoryPrefixQueryItem = Smithy.URIQueryItem(name: "repository-prefix".urlPercentEncoding(), value: Swift.String(repositoryPrefix).urlPercentEncoding())
            items.append(repositoryPrefixQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "max-results".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "next-token".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        if let administratorAccount = value.administratorAccount {
            let administratorAccountQueryItem = Smithy.URIQueryItem(name: "administrator-account".urlPercentEncoding(), value: Swift.String(administratorAccount).urlPercentEncoding())
            items.append(administratorAccountQueryItem)
        }
        return items
    }
}

extension ListSubPackageGroupsInput {

    static func urlPathProvider(_ value: ListSubPackageGroupsInput) -> Swift.String? {
        return "/v1/package-groups/sub-groups"
    }
}

extension ListSubPackageGroupsInput {

    static func queryItemProvider(_ value: ListSubPackageGroupsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let packageGroup = value.packageGroup else {
            let message = "Creating a URL Query Item failed. packageGroup is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageGroupQueryItem = Smithy.URIQueryItem(name: "package-group".urlPercentEncoding(), value: Swift.String(packageGroup).urlPercentEncoding())
        items.append(packageGroupQueryItem)
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "max-results".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "next-token".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        return items
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        return "/v1/tags"
    }
}

extension ListTagsForResourceInput {

    static func queryItemProvider(_ value: ListTagsForResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let resourceArn = value.resourceArn else {
            let message = "Creating a URL Query Item failed. resourceArn is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let resourceArnQueryItem = Smithy.URIQueryItem(name: "resourceArn".urlPercentEncoding(), value: Swift.String(resourceArn).urlPercentEncoding())
        items.append(resourceArnQueryItem)
        return items
    }
}

extension PublishPackageVersionInput {

    static func urlPathProvider(_ value: PublishPackageVersionInput) -> Swift.String? {
        return "/v1/package/version/publish"
    }
}

extension PublishPackageVersionInput {

    static func headerProvider(_ value: PublishPackageVersionInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let assetSHA256 = value.assetSHA256 {
            items.add(SmithyHTTPAPI.Header(name: "x-amz-content-sha256", value: Swift.String(assetSHA256)))
        }
        return items
    }
}

extension PublishPackageVersionInput {

    static func queryItemProvider(_ value: PublishPackageVersionInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        guard let assetName = value.assetName else {
            let message = "Creating a URL Query Item failed. assetName is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let assetNameQueryItem = Smithy.URIQueryItem(name: "asset".urlPercentEncoding(), value: Swift.String(assetName).urlPercentEncoding())
        items.append(assetNameQueryItem)
        guard let packageVersion = value.packageVersion else {
            let message = "Creating a URL Query Item failed. packageVersion is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageVersionQueryItem = Smithy.URIQueryItem(name: "version".urlPercentEncoding(), value: Swift.String(packageVersion).urlPercentEncoding())
        items.append(packageVersionQueryItem)
        if let unfinished = value.unfinished {
            let unfinishedQueryItem = Smithy.URIQueryItem(name: "unfinished".urlPercentEncoding(), value: Swift.String(unfinished).urlPercentEncoding())
            items.append(unfinishedQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension PutDomainPermissionsPolicyInput {

    static func urlPathProvider(_ value: PutDomainPermissionsPolicyInput) -> Swift.String? {
        return "/v1/domain/permissions/policy"
    }
}

extension PutPackageOriginConfigurationInput {

    static func urlPathProvider(_ value: PutPackageOriginConfigurationInput) -> Swift.String? {
        return "/v1/package"
    }
}

extension PutPackageOriginConfigurationInput {

    static func queryItemProvider(_ value: PutPackageOriginConfigurationInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension PutRepositoryPermissionsPolicyInput {

    static func urlPathProvider(_ value: PutRepositoryPermissionsPolicyInput) -> Swift.String? {
        return "/v1/repository/permissions/policy"
    }
}

extension PutRepositoryPermissionsPolicyInput {

    static func queryItemProvider(_ value: PutRepositoryPermissionsPolicyInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        return "/v1/tag"
    }
}

extension TagResourceInput {

    static func queryItemProvider(_ value: TagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let resourceArn = value.resourceArn else {
            let message = "Creating a URL Query Item failed. resourceArn is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let resourceArnQueryItem = Smithy.URIQueryItem(name: "resourceArn".urlPercentEncoding(), value: Swift.String(resourceArn).urlPercentEncoding())
        items.append(resourceArnQueryItem)
        return items
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        return "/v1/untag"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let resourceArn = value.resourceArn else {
            let message = "Creating a URL Query Item failed. resourceArn is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let resourceArnQueryItem = Smithy.URIQueryItem(name: "resourceArn".urlPercentEncoding(), value: Swift.String(resourceArn).urlPercentEncoding())
        items.append(resourceArnQueryItem)
        return items
    }
}

extension UpdatePackageGroupInput {

    static func urlPathProvider(_ value: UpdatePackageGroupInput) -> Swift.String? {
        return "/v1/package-group"
    }
}

extension UpdatePackageGroupInput {

    static func queryItemProvider(_ value: UpdatePackageGroupInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        return items
    }
}

extension UpdatePackageGroupOriginConfigurationInput {

    static func urlPathProvider(_ value: UpdatePackageGroupOriginConfigurationInput) -> Swift.String? {
        return "/v1/package-group-origin-configuration"
    }
}

extension UpdatePackageGroupOriginConfigurationInput {

    static func queryItemProvider(_ value: UpdatePackageGroupOriginConfigurationInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let packageGroup = value.packageGroup else {
            let message = "Creating a URL Query Item failed. packageGroup is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageGroupQueryItem = Smithy.URIQueryItem(name: "package-group".urlPercentEncoding(), value: Swift.String(packageGroup).urlPercentEncoding())
        items.append(packageGroupQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        return items
    }
}

extension UpdatePackageVersionsStatusInput {

    static func urlPathProvider(_ value: UpdatePackageVersionsStatusInput) -> Swift.String? {
        return "/v1/package/versions/update_status"
    }
}

extension UpdatePackageVersionsStatusInput {

    static func queryItemProvider(_ value: UpdatePackageVersionsStatusInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let package = value.package else {
            let message = "Creating a URL Query Item failed. package is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let packageQueryItem = Smithy.URIQueryItem(name: "package".urlPercentEncoding(), value: Swift.String(package).urlPercentEncoding())
        items.append(packageQueryItem)
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let format = value.format else {
            let message = "Creating a URL Query Item failed. format is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let formatQueryItem = Smithy.URIQueryItem(name: "format".urlPercentEncoding(), value: Swift.String(format.rawValue).urlPercentEncoding())
        items.append(formatQueryItem)
        if let namespace = value.namespace {
            let namespaceQueryItem = Smithy.URIQueryItem(name: "namespace".urlPercentEncoding(), value: Swift.String(namespace).urlPercentEncoding())
            items.append(namespaceQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension UpdateRepositoryInput {

    static func urlPathProvider(_ value: UpdateRepositoryInput) -> Swift.String? {
        return "/v1/repository"
    }
}

extension UpdateRepositoryInput {

    static func queryItemProvider(_ value: UpdateRepositoryInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let domain = value.domain else {
            let message = "Creating a URL Query Item failed. domain is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let domainQueryItem = Smithy.URIQueryItem(name: "domain".urlPercentEncoding(), value: Swift.String(domain).urlPercentEncoding())
        items.append(domainQueryItem)
        if let domainOwner = value.domainOwner {
            let domainOwnerQueryItem = Smithy.URIQueryItem(name: "domain-owner".urlPercentEncoding(), value: Swift.String(domainOwner).urlPercentEncoding())
            items.append(domainOwnerQueryItem)
        }
        guard let repository = value.repository else {
            let message = "Creating a URL Query Item failed. repository is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let repositoryQueryItem = Smithy.URIQueryItem(name: "repository".urlPercentEncoding(), value: Swift.String(repository).urlPercentEncoding())
        items.append(repositoryQueryItem)
        return items
    }
}

extension CopyPackageVersionsInput {

    static func write(value: CopyPackageVersionsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["allowOverwrite"].write(value.allowOverwrite)
        try writer["includeFromUpstream"].write(value.includeFromUpstream)
        try writer["versionRevisions"].writeMap(value.versionRevisions, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["versions"].writeList(value.versions, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateDomainInput {

    static func write(value: CreateDomainInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["encryptionKey"].write(value.encryptionKey)
        try writer["tags"].writeList(value.tags, memberWritingClosure: CodeartifactClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreatePackageGroupInput {

    static func write(value: CreatePackageGroupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["contactInfo"].write(value.contactInfo)
        try writer["description"].write(value.description)
        try writer["packageGroup"].write(value.packageGroup)
        try writer["tags"].writeList(value.tags, memberWritingClosure: CodeartifactClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateRepositoryInput {

    static func write(value: CreateRepositoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["description"].write(value.description)
        try writer["tags"].writeList(value.tags, memberWritingClosure: CodeartifactClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["upstreams"].writeList(value.upstreams, memberWritingClosure: CodeartifactClientTypes.UpstreamRepository.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DeletePackageVersionsInput {

    static func write(value: DeletePackageVersionsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["expectedStatus"].write(value.expectedStatus)
        try writer["versions"].writeList(value.versions, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DisposePackageVersionsInput {

    static func write(value: DisposePackageVersionsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["expectedStatus"].write(value.expectedStatus)
        try writer["versionRevisions"].writeMap(value.versionRevisions, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["versions"].writeList(value.versions, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ListDomainsInput {

    static func write(value: ListDomainsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension PublishPackageVersionInput {

    static func write(value: PublishPackageVersionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["assetContent"].write(value.assetContent)
    }
}

extension PutDomainPermissionsPolicyInput {

    static func write(value: PutDomainPermissionsPolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["domain"].write(value.domain)
        try writer["domainOwner"].write(value.domainOwner)
        try writer["policyDocument"].write(value.policyDocument)
        try writer["policyRevision"].write(value.policyRevision)
    }
}

extension PutPackageOriginConfigurationInput {

    static func write(value: PutPackageOriginConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["restrictions"].write(value.restrictions, with: CodeartifactClientTypes.PackageOriginRestrictions.write(value:to:))
    }
}

extension PutRepositoryPermissionsPolicyInput {

    static func write(value: PutRepositoryPermissionsPolicyInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["policyDocument"].write(value.policyDocument)
        try writer["policyRevision"].write(value.policyRevision)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tags"].writeList(value.tags, memberWritingClosure: CodeartifactClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UntagResourceInput {

    static func write(value: UntagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tagKeys"].writeList(value.tagKeys, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdatePackageGroupInput {

    static func write(value: UpdatePackageGroupInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["contactInfo"].write(value.contactInfo)
        try writer["description"].write(value.description)
        try writer["packageGroup"].write(value.packageGroup)
    }
}

extension UpdatePackageGroupOriginConfigurationInput {

    static func write(value: UpdatePackageGroupOriginConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["addAllowedRepositories"].writeList(value.addAllowedRepositories, memberWritingClosure: CodeartifactClientTypes.PackageGroupAllowedRepository.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["removeAllowedRepositories"].writeList(value.removeAllowedRepositories, memberWritingClosure: CodeartifactClientTypes.PackageGroupAllowedRepository.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["restrictions"].writeMap(value.restrictions, valueWritingClosure: SmithyReadWrite.WritingClosureBox<CodeartifactClientTypes.PackageGroupOriginRestrictionMode>().write(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension UpdatePackageVersionsStatusInput {

    static func write(value: UpdatePackageVersionsStatusInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["expectedStatus"].write(value.expectedStatus)
        try writer["targetStatus"].write(value.targetStatus)
        try writer["versionRevisions"].writeMap(value.versionRevisions, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["versions"].writeList(value.versions, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdateRepositoryInput {

    static func write(value: UpdateRepositoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["description"].write(value.description)
        try writer["upstreams"].writeList(value.upstreams, memberWritingClosure: CodeartifactClientTypes.UpstreamRepository.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension AssociateExternalConnectionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AssociateExternalConnectionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = AssociateExternalConnectionOutput()
        value.repository = try reader["repository"].readIfPresent(with: CodeartifactClientTypes.RepositoryDescription.read(from:))
        return value
    }
}

extension CopyPackageVersionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CopyPackageVersionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CopyPackageVersionsOutput()
        value.failedVersions = try reader["failedVersions"].readMapIfPresent(valueReadingClosure: CodeartifactClientTypes.PackageVersionError.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.successfulVersions = try reader["successfulVersions"].readMapIfPresent(valueReadingClosure: CodeartifactClientTypes.SuccessfulPackageVersionInfo.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension CreateDomainOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateDomainOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateDomainOutput()
        value.domain = try reader["domain"].readIfPresent(with: CodeartifactClientTypes.DomainDescription.read(from:))
        return value
    }
}

extension CreatePackageGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreatePackageGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreatePackageGroupOutput()
        value.packageGroup = try reader["packageGroup"].readIfPresent(with: CodeartifactClientTypes.PackageGroupDescription.read(from:))
        return value
    }
}

extension CreateRepositoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateRepositoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateRepositoryOutput()
        value.repository = try reader["repository"].readIfPresent(with: CodeartifactClientTypes.RepositoryDescription.read(from:))
        return value
    }
}

extension DeleteDomainOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteDomainOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteDomainOutput()
        value.domain = try reader["domain"].readIfPresent(with: CodeartifactClientTypes.DomainDescription.read(from:))
        return value
    }
}

extension DeleteDomainPermissionsPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteDomainPermissionsPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteDomainPermissionsPolicyOutput()
        value.policy = try reader["policy"].readIfPresent(with: CodeartifactClientTypes.ResourcePolicy.read(from:))
        return value
    }
}

extension DeletePackageOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeletePackageOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeletePackageOutput()
        value.deletedPackage = try reader["deletedPackage"].readIfPresent(with: CodeartifactClientTypes.PackageSummary.read(from:))
        return value
    }
}

extension DeletePackageGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeletePackageGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeletePackageGroupOutput()
        value.packageGroup = try reader["packageGroup"].readIfPresent(with: CodeartifactClientTypes.PackageGroupDescription.read(from:))
        return value
    }
}

extension DeletePackageVersionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeletePackageVersionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeletePackageVersionsOutput()
        value.failedVersions = try reader["failedVersions"].readMapIfPresent(valueReadingClosure: CodeartifactClientTypes.PackageVersionError.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.successfulVersions = try reader["successfulVersions"].readMapIfPresent(valueReadingClosure: CodeartifactClientTypes.SuccessfulPackageVersionInfo.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DeleteRepositoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteRepositoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteRepositoryOutput()
        value.repository = try reader["repository"].readIfPresent(with: CodeartifactClientTypes.RepositoryDescription.read(from:))
        return value
    }
}

extension DeleteRepositoryPermissionsPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteRepositoryPermissionsPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteRepositoryPermissionsPolicyOutput()
        value.policy = try reader["policy"].readIfPresent(with: CodeartifactClientTypes.ResourcePolicy.read(from:))
        return value
    }
}

extension DescribeDomainOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDomainOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeDomainOutput()
        value.domain = try reader["domain"].readIfPresent(with: CodeartifactClientTypes.DomainDescription.read(from:))
        return value
    }
}

extension DescribePackageOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribePackageOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribePackageOutput()
        value.package = try reader["package"].readIfPresent(with: CodeartifactClientTypes.PackageDescription.read(from:))
        return value
    }
}

extension DescribePackageGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribePackageGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribePackageGroupOutput()
        value.packageGroup = try reader["packageGroup"].readIfPresent(with: CodeartifactClientTypes.PackageGroupDescription.read(from:))
        return value
    }
}

extension DescribePackageVersionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribePackageVersionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribePackageVersionOutput()
        value.packageVersion = try reader["packageVersion"].readIfPresent(with: CodeartifactClientTypes.PackageVersionDescription.read(from:))
        return value
    }
}

extension DescribeRepositoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeRepositoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeRepositoryOutput()
        value.repository = try reader["repository"].readIfPresent(with: CodeartifactClientTypes.RepositoryDescription.read(from:))
        return value
    }
}

extension DisassociateExternalConnectionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisassociateExternalConnectionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DisassociateExternalConnectionOutput()
        value.repository = try reader["repository"].readIfPresent(with: CodeartifactClientTypes.RepositoryDescription.read(from:))
        return value
    }
}

extension DisposePackageVersionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisposePackageVersionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DisposePackageVersionsOutput()
        value.failedVersions = try reader["failedVersions"].readMapIfPresent(valueReadingClosure: CodeartifactClientTypes.PackageVersionError.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.successfulVersions = try reader["successfulVersions"].readMapIfPresent(valueReadingClosure: CodeartifactClientTypes.SuccessfulPackageVersionInfo.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension GetAssociatedPackageGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetAssociatedPackageGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetAssociatedPackageGroupOutput()
        value.associationType = try reader["associationType"].readIfPresent()
        value.packageGroup = try reader["packageGroup"].readIfPresent(with: CodeartifactClientTypes.PackageGroupDescription.read(from:))
        return value
    }
}

extension GetAuthorizationTokenOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetAuthorizationTokenOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetAuthorizationTokenOutput()
        value.authorizationToken = try reader["authorizationToken"].readIfPresent()
        value.expiration = try reader["expiration"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension GetDomainPermissionsPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetDomainPermissionsPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetDomainPermissionsPolicyOutput()
        value.policy = try reader["policy"].readIfPresent(with: CodeartifactClientTypes.ResourcePolicy.read(from:))
        return value
    }
}

extension GetPackageVersionAssetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetPackageVersionAssetOutput {
        var value = GetPackageVersionAssetOutput()
        if let assetNameHeaderValue = httpResponse.headers.value(for: "X-AssetName") {
            value.assetName = assetNameHeaderValue
        }
        if let packageVersionHeaderValue = httpResponse.headers.value(for: "X-PackageVersion") {
            value.packageVersion = packageVersionHeaderValue
        }
        if let packageVersionRevisionHeaderValue = httpResponse.headers.value(for: "X-PackageVersionRevision") {
            value.packageVersionRevision = packageVersionRevisionHeaderValue
        }
        switch httpResponse.body {
        case .data(let data):
            value.asset = .data(data)
        case .stream(let stream):
            value.asset = .stream(stream)
        case .noStream:
            value.asset = nil
        }
        return value
    }
}

extension GetPackageVersionReadmeOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetPackageVersionReadmeOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetPackageVersionReadmeOutput()
        value.format = try reader["format"].readIfPresent()
        value.namespace = try reader["namespace"].readIfPresent()
        value.package = try reader["package"].readIfPresent()
        value.readme = try reader["readme"].readIfPresent()
        value.version = try reader["version"].readIfPresent()
        value.versionRevision = try reader["versionRevision"].readIfPresent()
        return value
    }
}

extension GetRepositoryEndpointOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetRepositoryEndpointOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetRepositoryEndpointOutput()
        value.repositoryEndpoint = try reader["repositoryEndpoint"].readIfPresent()
        return value
    }
}

extension GetRepositoryPermissionsPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetRepositoryPermissionsPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetRepositoryPermissionsPolicyOutput()
        value.policy = try reader["policy"].readIfPresent(with: CodeartifactClientTypes.ResourcePolicy.read(from:))
        return value
    }
}

extension ListAllowedRepositoriesForGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListAllowedRepositoriesForGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListAllowedRepositoriesForGroupOutput()
        value.allowedRepositories = try reader["allowedRepositories"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListAssociatedPackagesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListAssociatedPackagesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListAssociatedPackagesOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.packages = try reader["packages"].readListIfPresent(memberReadingClosure: CodeartifactClientTypes.AssociatedPackage.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListDomainsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListDomainsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListDomainsOutput()
        value.domains = try reader["domains"].readListIfPresent(memberReadingClosure: CodeartifactClientTypes.DomainSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListPackageGroupsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListPackageGroupsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListPackageGroupsOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.packageGroups = try reader["packageGroups"].readListIfPresent(memberReadingClosure: CodeartifactClientTypes.PackageGroupSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListPackagesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListPackagesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListPackagesOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.packages = try reader["packages"].readListIfPresent(memberReadingClosure: CodeartifactClientTypes.PackageSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListPackageVersionAssetsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListPackageVersionAssetsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListPackageVersionAssetsOutput()
        value.assets = try reader["assets"].readListIfPresent(memberReadingClosure: CodeartifactClientTypes.AssetSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.format = try reader["format"].readIfPresent()
        value.namespace = try reader["namespace"].readIfPresent()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.package = try reader["package"].readIfPresent()
        value.version = try reader["version"].readIfPresent()
        value.versionRevision = try reader["versionRevision"].readIfPresent()
        return value
    }
}

extension ListPackageVersionDependenciesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListPackageVersionDependenciesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListPackageVersionDependenciesOutput()
        value.dependencies = try reader["dependencies"].readListIfPresent(memberReadingClosure: CodeartifactClientTypes.PackageDependency.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.format = try reader["format"].readIfPresent()
        value.namespace = try reader["namespace"].readIfPresent()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.package = try reader["package"].readIfPresent()
        value.version = try reader["version"].readIfPresent()
        value.versionRevision = try reader["versionRevision"].readIfPresent()
        return value
    }
}

extension ListPackageVersionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListPackageVersionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListPackageVersionsOutput()
        value.defaultDisplayVersion = try reader["defaultDisplayVersion"].readIfPresent()
        value.format = try reader["format"].readIfPresent()
        value.namespace = try reader["namespace"].readIfPresent()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.package = try reader["package"].readIfPresent()
        value.versions = try reader["versions"].readListIfPresent(memberReadingClosure: CodeartifactClientTypes.PackageVersionSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListRepositoriesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRepositoriesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRepositoriesOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.repositories = try reader["repositories"].readListIfPresent(memberReadingClosure: CodeartifactClientTypes.RepositorySummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListRepositoriesInDomainOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRepositoriesInDomainOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRepositoriesInDomainOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.repositories = try reader["repositories"].readListIfPresent(memberReadingClosure: CodeartifactClientTypes.RepositorySummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListSubPackageGroupsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListSubPackageGroupsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListSubPackageGroupsOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.packageGroups = try reader["packageGroups"].readListIfPresent(memberReadingClosure: CodeartifactClientTypes.PackageGroupSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["tags"].readListIfPresent(memberReadingClosure: CodeartifactClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension PublishPackageVersionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PublishPackageVersionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PublishPackageVersionOutput()
        value.asset = try reader["asset"].readIfPresent(with: CodeartifactClientTypes.AssetSummary.read(from:))
        value.format = try reader["format"].readIfPresent()
        value.namespace = try reader["namespace"].readIfPresent()
        value.package = try reader["package"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        value.version = try reader["version"].readIfPresent()
        value.versionRevision = try reader["versionRevision"].readIfPresent()
        return value
    }
}

extension PutDomainPermissionsPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutDomainPermissionsPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutDomainPermissionsPolicyOutput()
        value.policy = try reader["policy"].readIfPresent(with: CodeartifactClientTypes.ResourcePolicy.read(from:))
        return value
    }
}

extension PutPackageOriginConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutPackageOriginConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutPackageOriginConfigurationOutput()
        value.originConfiguration = try reader["originConfiguration"].readIfPresent(with: CodeartifactClientTypes.PackageOriginConfiguration.read(from:))
        return value
    }
}

extension PutRepositoryPermissionsPolicyOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutRepositoryPermissionsPolicyOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutRepositoryPermissionsPolicyOutput()
        value.policy = try reader["policy"].readIfPresent(with: CodeartifactClientTypes.ResourcePolicy.read(from:))
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdatePackageGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdatePackageGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdatePackageGroupOutput()
        value.packageGroup = try reader["packageGroup"].readIfPresent(with: CodeartifactClientTypes.PackageGroupDescription.read(from:))
        return value
    }
}

extension UpdatePackageGroupOriginConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdatePackageGroupOriginConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdatePackageGroupOriginConfigurationOutput()
        value.allowedRepositoryUpdates = try reader["allowedRepositoryUpdates"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.mapReadingClosure(valueReadingClosure: SmithyReadWrite.listReadingClosure(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.packageGroup = try reader["packageGroup"].readIfPresent(with: CodeartifactClientTypes.PackageGroupDescription.read(from:))
        return value
    }
}

extension UpdatePackageVersionsStatusOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdatePackageVersionsStatusOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdatePackageVersionsStatusOutput()
        value.failedVersions = try reader["failedVersions"].readMapIfPresent(valueReadingClosure: CodeartifactClientTypes.PackageVersionError.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.successfulVersions = try reader["successfulVersions"].readMapIfPresent(valueReadingClosure: CodeartifactClientTypes.SuccessfulPackageVersionInfo.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension UpdateRepositoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateRepositoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateRepositoryOutput()
        value.repository = try reader["repository"].readIfPresent(with: CodeartifactClientTypes.RepositoryDescription.read(from:))
        return value
    }
}

enum AssociateExternalConnectionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CopyPackageVersionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateDomainOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreatePackageGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateRepositoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteDomainOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteDomainPermissionsPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeletePackageOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeletePackageGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeletePackageVersionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteRepositoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteRepositoryPermissionsPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDomainOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribePackageOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribePackageGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribePackageVersionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeRepositoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisassociateExternalConnectionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisposePackageVersionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetAssociatedPackageGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetAuthorizationTokenOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetDomainPermissionsPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetPackageVersionAssetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetPackageVersionReadmeOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetRepositoryEndpointOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetRepositoryPermissionsPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListAllowedRepositoriesForGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListAssociatedPackagesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListDomainsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListPackageGroupsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListPackagesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListPackageVersionAssetsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListPackageVersionDependenciesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListPackageVersionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRepositoriesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRepositoriesInDomainOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListSubPackageGroupsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PublishPackageVersionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutDomainPermissionsPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutPackageOriginConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutRepositoryPermissionsPolicyOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdatePackageGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdatePackageGroupOriginConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdatePackageVersionsStatusOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateRepositoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.resourceId = try reader["resourceId"].readIfPresent()
        value.properties.resourceType = try reader["resourceType"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.reason = try reader["reason"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ThrottlingException()
        if let retryAfterSecondsHeaderValue = httpResponse.headers.value(for: "Retry-After") {
            value.properties.retryAfterSeconds = Swift.Int(retryAfterSecondsHeaderValue) ?? 0
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.resourceId = try reader["resourceId"].readIfPresent()
        value.properties.resourceType = try reader["resourceType"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceQuotaExceededException {
        let reader = baseError.errorBodyReader
        var value = ServiceQuotaExceededException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.resourceId = try reader["resourceId"].readIfPresent()
        value.properties.resourceType = try reader["resourceType"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CodeartifactClientTypes.RepositoryDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.RepositoryDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.RepositoryDescription()
        value.name = try reader["name"].readIfPresent()
        value.administratorAccount = try reader["administratorAccount"].readIfPresent()
        value.domainName = try reader["domainName"].readIfPresent()
        value.domainOwner = try reader["domainOwner"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.description = try reader["description"].readIfPresent()
        value.upstreams = try reader["upstreams"].readListIfPresent(memberReadingClosure: CodeartifactClientTypes.UpstreamRepositoryInfo.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.externalConnections = try reader["externalConnections"].readListIfPresent(memberReadingClosure: CodeartifactClientTypes.RepositoryExternalConnectionInfo.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.createdTime = try reader["createdTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension CodeartifactClientTypes.RepositoryExternalConnectionInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.RepositoryExternalConnectionInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.RepositoryExternalConnectionInfo()
        value.externalConnectionName = try reader["externalConnectionName"].readIfPresent()
        value.packageFormat = try reader["packageFormat"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        return value
    }
}

extension CodeartifactClientTypes.UpstreamRepositoryInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.UpstreamRepositoryInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.UpstreamRepositoryInfo()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        return value
    }
}

extension CodeartifactClientTypes.SuccessfulPackageVersionInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.SuccessfulPackageVersionInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.SuccessfulPackageVersionInfo()
        value.revision = try reader["revision"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        return value
    }
}

extension CodeartifactClientTypes.PackageVersionError {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.PackageVersionError {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.PackageVersionError()
        value.errorCode = try reader["errorCode"].readIfPresent()
        value.errorMessage = try reader["errorMessage"].readIfPresent()
        return value
    }
}

extension CodeartifactClientTypes.DomainDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.DomainDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.DomainDescription()
        value.name = try reader["name"].readIfPresent()
        value.owner = try reader["owner"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        value.createdTime = try reader["createdTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.encryptionKey = try reader["encryptionKey"].readIfPresent()
        value.repositoryCount = try reader["repositoryCount"].readIfPresent() ?? 0
        value.assetSizeBytes = try reader["assetSizeBytes"].readIfPresent() ?? 0
        value.s3BucketArn = try reader["s3BucketArn"].readIfPresent()
        return value
    }
}

extension CodeartifactClientTypes.PackageGroupDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.PackageGroupDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.PackageGroupDescription()
        value.arn = try reader["arn"].readIfPresent()
        value.pattern = try reader["pattern"].readIfPresent()
        value.domainName = try reader["domainName"].readIfPresent()
        value.domainOwner = try reader["domainOwner"].readIfPresent()
        value.createdTime = try reader["createdTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.contactInfo = try reader["contactInfo"].readIfPresent()
        value.description = try reader["description"].readIfPresent()
        value.originConfiguration = try reader["originConfiguration"].readIfPresent(with: CodeartifactClientTypes.PackageGroupOriginConfiguration.read(from:))
        value.parent = try reader["parent"].readIfPresent(with: CodeartifactClientTypes.PackageGroupReference.read(from:))
        return value
    }
}

extension CodeartifactClientTypes.PackageGroupReference {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.PackageGroupReference {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.PackageGroupReference()
        value.arn = try reader["arn"].readIfPresent()
        value.pattern = try reader["pattern"].readIfPresent()
        return value
    }
}

extension CodeartifactClientTypes.PackageGroupOriginConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.PackageGroupOriginConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.PackageGroupOriginConfiguration()
        value.restrictions = try reader["restrictions"].readMapIfPresent(valueReadingClosure: CodeartifactClientTypes.PackageGroupOriginRestriction.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension CodeartifactClientTypes.PackageGroupOriginRestriction {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.PackageGroupOriginRestriction {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.PackageGroupOriginRestriction()
        value.mode = try reader["mode"].readIfPresent()
        value.effectiveMode = try reader["effectiveMode"].readIfPresent()
        value.inheritedFrom = try reader["inheritedFrom"].readIfPresent(with: CodeartifactClientTypes.PackageGroupReference.read(from:))
        value.repositoriesCount = try reader["repositoriesCount"].readIfPresent()
        return value
    }
}

extension CodeartifactClientTypes.ResourcePolicy {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.ResourcePolicy {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.ResourcePolicy()
        value.resourceArn = try reader["resourceArn"].readIfPresent()
        value.revision = try reader["revision"].readIfPresent()
        value.document = try reader["document"].readIfPresent()
        return value
    }
}

extension CodeartifactClientTypes.PackageSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.PackageSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.PackageSummary()
        value.format = try reader["format"].readIfPresent()
        value.namespace = try reader["namespace"].readIfPresent()
        value.package = try reader["package"].readIfPresent()
        value.originConfiguration = try reader["originConfiguration"].readIfPresent(with: CodeartifactClientTypes.PackageOriginConfiguration.read(from:))
        return value
    }
}

extension CodeartifactClientTypes.PackageOriginConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.PackageOriginConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.PackageOriginConfiguration()
        value.restrictions = try reader["restrictions"].readIfPresent(with: CodeartifactClientTypes.PackageOriginRestrictions.read(from:))
        return value
    }
}

extension CodeartifactClientTypes.PackageOriginRestrictions {

    static func write(value: CodeartifactClientTypes.PackageOriginRestrictions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["publish"].write(value.publish)
        try writer["upstream"].write(value.upstream)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.PackageOriginRestrictions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.PackageOriginRestrictions()
        value.publish = try reader["publish"].readIfPresent() ?? .sdkUnknown("")
        value.upstream = try reader["upstream"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension CodeartifactClientTypes.PackageDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.PackageDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.PackageDescription()
        value.format = try reader["format"].readIfPresent()
        value.namespace = try reader["namespace"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.originConfiguration = try reader["originConfiguration"].readIfPresent(with: CodeartifactClientTypes.PackageOriginConfiguration.read(from:))
        return value
    }
}

extension CodeartifactClientTypes.PackageVersionDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.PackageVersionDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.PackageVersionDescription()
        value.format = try reader["format"].readIfPresent()
        value.namespace = try reader["namespace"].readIfPresent()
        value.packageName = try reader["packageName"].readIfPresent()
        value.displayName = try reader["displayName"].readIfPresent()
        value.version = try reader["version"].readIfPresent()
        value.summary = try reader["summary"].readIfPresent()
        value.homePage = try reader["homePage"].readIfPresent()
        value.sourceCodeRepository = try reader["sourceCodeRepository"].readIfPresent()
        value.publishedTime = try reader["publishedTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.licenses = try reader["licenses"].readListIfPresent(memberReadingClosure: CodeartifactClientTypes.LicenseInfo.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.revision = try reader["revision"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        value.origin = try reader["origin"].readIfPresent(with: CodeartifactClientTypes.PackageVersionOrigin.read(from:))
        return value
    }
}

extension CodeartifactClientTypes.PackageVersionOrigin {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.PackageVersionOrigin {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.PackageVersionOrigin()
        value.domainEntryPoint = try reader["domainEntryPoint"].readIfPresent(with: CodeartifactClientTypes.DomainEntryPoint.read(from:))
        value.originType = try reader["originType"].readIfPresent()
        return value
    }
}

extension CodeartifactClientTypes.DomainEntryPoint {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.DomainEntryPoint {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.DomainEntryPoint()
        value.repositoryName = try reader["repositoryName"].readIfPresent()
        value.externalConnectionName = try reader["externalConnectionName"].readIfPresent()
        return value
    }
}

extension CodeartifactClientTypes.LicenseInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.LicenseInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.LicenseInfo()
        value.name = try reader["name"].readIfPresent()
        value.url = try reader["url"].readIfPresent()
        return value
    }
}

extension CodeartifactClientTypes.AssociatedPackage {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.AssociatedPackage {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.AssociatedPackage()
        value.format = try reader["format"].readIfPresent()
        value.namespace = try reader["namespace"].readIfPresent()
        value.package = try reader["package"].readIfPresent()
        value.associationType = try reader["associationType"].readIfPresent()
        return value
    }
}

extension CodeartifactClientTypes.DomainSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.DomainSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.DomainSummary()
        value.name = try reader["name"].readIfPresent()
        value.owner = try reader["owner"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        value.createdTime = try reader["createdTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.encryptionKey = try reader["encryptionKey"].readIfPresent()
        return value
    }
}

extension CodeartifactClientTypes.PackageGroupSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.PackageGroupSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.PackageGroupSummary()
        value.arn = try reader["arn"].readIfPresent()
        value.pattern = try reader["pattern"].readIfPresent()
        value.domainName = try reader["domainName"].readIfPresent()
        value.domainOwner = try reader["domainOwner"].readIfPresent()
        value.createdTime = try reader["createdTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.contactInfo = try reader["contactInfo"].readIfPresent()
        value.description = try reader["description"].readIfPresent()
        value.originConfiguration = try reader["originConfiguration"].readIfPresent(with: CodeartifactClientTypes.PackageGroupOriginConfiguration.read(from:))
        value.parent = try reader["parent"].readIfPresent(with: CodeartifactClientTypes.PackageGroupReference.read(from:))
        return value
    }
}

extension CodeartifactClientTypes.AssetSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.AssetSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.AssetSummary()
        value.name = try reader["name"].readIfPresent() ?? ""
        value.size = try reader["size"].readIfPresent()
        value.hashes = try reader["hashes"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension CodeartifactClientTypes.PackageDependency {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.PackageDependency {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.PackageDependency()
        value.namespace = try reader["namespace"].readIfPresent()
        value.package = try reader["package"].readIfPresent()
        value.dependencyType = try reader["dependencyType"].readIfPresent()
        value.versionRequirement = try reader["versionRequirement"].readIfPresent()
        return value
    }
}

extension CodeartifactClientTypes.PackageVersionSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.PackageVersionSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.PackageVersionSummary()
        value.version = try reader["version"].readIfPresent() ?? ""
        value.revision = try reader["revision"].readIfPresent()
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        value.origin = try reader["origin"].readIfPresent(with: CodeartifactClientTypes.PackageVersionOrigin.read(from:))
        return value
    }
}

extension CodeartifactClientTypes.RepositorySummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.RepositorySummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.RepositorySummary()
        value.name = try reader["name"].readIfPresent()
        value.administratorAccount = try reader["administratorAccount"].readIfPresent()
        value.domainName = try reader["domainName"].readIfPresent()
        value.domainOwner = try reader["domainOwner"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.description = try reader["description"].readIfPresent()
        value.createdTime = try reader["createdTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension CodeartifactClientTypes.Tag {

    static func write(value: CodeartifactClientTypes.Tag?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["key"].write(value.key)
        try writer["value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeartifactClientTypes.Tag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeartifactClientTypes.Tag()
        value.key = try reader["key"].readIfPresent() ?? ""
        value.value = try reader["value"].readIfPresent() ?? ""
        return value
    }
}

extension CodeartifactClientTypes.UpstreamRepository {

    static func write(value: CodeartifactClientTypes.UpstreamRepository?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["repositoryName"].write(value.repositoryName)
    }
}

extension CodeartifactClientTypes.PackageGroupAllowedRepository {

    static func write(value: CodeartifactClientTypes.PackageGroupAllowedRepository?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["originRestrictionType"].write(value.originRestrictionType)
        try writer["repositoryName"].write(value.repositoryName)
    }
}

public enum CodeartifactClientTypes {}
