//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHT<PERSON><PERSON>I.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
@_spi(SmithyTimestamps) import struct SmithyTimestamps.TimestampFormatter

/// You don't have permissions to perform the requested operation. The IAM principal making the request must have at least one IAM permissions policy attached that grants the required permissions. For more information, see [Access management](https://docs.aws.amazon.com/IAM/latest/UserGuide/access.html) in the IAM User Guide.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension EKSAuthClientTypes {

    /// An object with the permanent IAM role identity and the temporary session name.
    public struct AssumedRoleUser: Swift.Sendable {
        /// The ARN of the IAM role that the temporary credentials authenticate to.
        /// This member is required.
        public var arn: Swift.String?
        /// The session name of the temporary session requested to STS. The value is a unique identifier that contains the role ID, a colon (:), and the role session name of the role that is being assumed. The role ID is generated by IAM when the role is created. The role session name part of the value follows this format: eks-clustername-podname-random UUID
        /// This member is required.
        public var assumeRoleId: Swift.String?

        public init(
            arn: Swift.String? = nil,
            assumeRoleId: Swift.String? = nil
        )
        {
            self.arn = arn
            self.assumeRoleId = assumeRoleId
        }
    }
}

/// The specified Kubernetes service account token is expired.
public struct ExpiredTokenException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ExpiredTokenException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// These errors are usually caused by a server-side issue.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified parameter is invalid. Review the available parameters for the API request.
public struct InvalidParameterException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidParameterException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// This exception is thrown if the request contains a semantic error. The precise meaning will depend on the API, and will be documented in the error message.
public struct InvalidRequestException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidRequestException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified Kubernetes service account token is invalid.
public struct InvalidTokenException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidTokenException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified resource could not be found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The service is unavailable. Back off and retry the operation.
public struct ServiceUnavailableException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceUnavailableException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request was denied because your request rate is too high. Reduce the frequency of requests.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct AssumeRoleForPodIdentityInput: Swift.Sendable {
    /// The name of the cluster for the request.
    /// This member is required.
    public var clusterName: Swift.String?
    /// The token of the Kubernetes service account for the pod.
    /// This member is required.
    public var token: Swift.String?

    public init(
        clusterName: Swift.String? = nil,
        token: Swift.String? = nil
    )
    {
        self.clusterName = clusterName
        self.token = token
    }
}

extension AssumeRoleForPodIdentityInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "AssumeRoleForPodIdentityInput(clusterName: \(Swift.String(describing: clusterName)), token: \"CONTENT_REDACTED\")"}
}

extension EKSAuthClientTypes {

    /// The Amazon Web Services Signature Version 4 type of temporary credentials.
    public struct Credentials: Swift.Sendable {
        /// The access key ID that identifies the temporary security credentials.
        /// This member is required.
        public var accessKeyId: Swift.String?
        /// The Unix epoch timestamp in seconds when the current credentials expire.
        /// This member is required.
        public var expiration: Foundation.Date?
        /// The secret access key that applications inside the pods use to sign requests.
        /// This member is required.
        public var secretAccessKey: Swift.String?
        /// The token that applications inside the pods must pass to any service API to use the temporary credentials.
        /// This member is required.
        public var sessionToken: Swift.String?

        public init(
            accessKeyId: Swift.String? = nil,
            expiration: Foundation.Date? = nil,
            secretAccessKey: Swift.String? = nil,
            sessionToken: Swift.String? = nil
        )
        {
            self.accessKeyId = accessKeyId
            self.expiration = expiration
            self.secretAccessKey = secretAccessKey
            self.sessionToken = sessionToken
        }
    }
}

extension EKSAuthClientTypes.Credentials: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CONTENT_REDACTED"
    }
}

extension EKSAuthClientTypes {

    /// Amazon EKS Pod Identity associations provide the ability to manage credentials for your applications, similar to the way that Amazon EC2 instance profiles provide credentials to Amazon EC2 instances.
    public struct PodIdentityAssociation: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the EKS Pod Identity association.
        /// This member is required.
        public var associationArn: Swift.String?
        /// The ID of the association.
        /// This member is required.
        public var associationId: Swift.String?

        public init(
            associationArn: Swift.String? = nil,
            associationId: Swift.String? = nil
        )
        {
            self.associationArn = associationArn
            self.associationId = associationId
        }
    }
}

extension EKSAuthClientTypes {

    /// An object containing the name of the Kubernetes service account inside the cluster to associate the IAM credentials with.
    public struct Subject: Swift.Sendable {
        /// The name of the Kubernetes namespace inside the cluster to create the association in. The service account and the pods that use the service account must be in this namespace.
        /// This member is required.
        public var namespace: Swift.String?
        /// The name of the Kubernetes service account inside the cluster to associate the IAM credentials with.
        /// This member is required.
        public var serviceAccount: Swift.String?

        public init(
            namespace: Swift.String? = nil,
            serviceAccount: Swift.String? = nil
        )
        {
            self.namespace = namespace
            self.serviceAccount = serviceAccount
        }
    }
}

public struct AssumeRoleForPodIdentityOutput: Swift.Sendable {
    /// An object with the permanent IAM role identity and the temporary session name. The ARN of the IAM role that the temporary credentials authenticate to. The session name of the temporary session requested to STS. The value is a unique identifier that contains the role ID, a colon (:), and the role session name of the role that is being assumed. The role ID is generated by IAM when the role is created. The role session name part of the value follows this format: eks-clustername-podname-random UUID
    /// This member is required.
    public var assumedRoleUser: EKSAuthClientTypes.AssumedRoleUser?
    /// The identity that is allowed to use the credentials. This value is always pods.eks.amazonaws.com.
    /// This member is required.
    public var audience: Swift.String?
    /// The Amazon Web Services Signature Version 4 type of temporary credentials.
    /// This member is required.
    public var credentials: EKSAuthClientTypes.Credentials?
    /// The Amazon Resource Name (ARN) and ID of the EKS Pod Identity association.
    /// This member is required.
    public var podIdentityAssociation: EKSAuthClientTypes.PodIdentityAssociation?
    /// The name of the Kubernetes service account inside the cluster to associate the IAM credentials with.
    /// This member is required.
    public var subject: EKSAuthClientTypes.Subject?

    public init(
        assumedRoleUser: EKSAuthClientTypes.AssumedRoleUser? = nil,
        audience: Swift.String? = nil,
        credentials: EKSAuthClientTypes.Credentials? = nil,
        podIdentityAssociation: EKSAuthClientTypes.PodIdentityAssociation? = nil,
        subject: EKSAuthClientTypes.Subject? = nil
    )
    {
        self.assumedRoleUser = assumedRoleUser
        self.audience = audience
        self.credentials = credentials
        self.podIdentityAssociation = podIdentityAssociation
        self.subject = subject
    }
}

extension AssumeRoleForPodIdentityOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "AssumeRoleForPodIdentityOutput(assumedRoleUser: \(Swift.String(describing: assumedRoleUser)), audience: \(Swift.String(describing: audience)), podIdentityAssociation: \(Swift.String(describing: podIdentityAssociation)), subject: \(Swift.String(describing: subject)), credentials: \"CONTENT_REDACTED\")"}
}

extension AssumeRoleForPodIdentityInput {

    static func urlPathProvider(_ value: AssumeRoleForPodIdentityInput) -> Swift.String? {
        guard let clusterName = value.clusterName else {
            return nil
        }
        return "/clusters/\(clusterName.urlPercentEncoding())/assume-role-for-pod-identity"
    }
}

extension AssumeRoleForPodIdentityInput {

    static func write(value: AssumeRoleForPodIdentityInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["token"].write(value.token)
    }
}

extension AssumeRoleForPodIdentityOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AssumeRoleForPodIdentityOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = AssumeRoleForPodIdentityOutput()
        value.assumedRoleUser = try reader["assumedRoleUser"].readIfPresent(with: EKSAuthClientTypes.AssumedRoleUser.read(from:))
        value.audience = try reader["audience"].readIfPresent() ?? ""
        value.credentials = try reader["credentials"].readIfPresent(with: EKSAuthClientTypes.Credentials.read(from:))
        value.podIdentityAssociation = try reader["podIdentityAssociation"].readIfPresent(with: EKSAuthClientTypes.PodIdentityAssociation.read(from:))
        value.subject = try reader["subject"].readIfPresent(with: EKSAuthClientTypes.Subject.read(from:))
        return value
    }
}

enum AssumeRoleForPodIdentityOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ExpiredTokenException": return try ExpiredTokenException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "InvalidTokenException": return try InvalidTokenException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceUnavailableException": return try ServiceUnavailableException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ExpiredTokenException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ExpiredTokenException {
        let reader = baseError.errorBodyReader
        var value = ExpiredTokenException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceUnavailableException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceUnavailableException {
        let reader = baseError.errorBodyReader
        var value = ServiceUnavailableException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidRequestException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InvalidRequestException {
        let reader = baseError.errorBodyReader
        var value = InvalidRequestException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidTokenException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InvalidTokenException {
        let reader = baseError.errorBodyReader
        var value = InvalidTokenException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidParameterException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InvalidParameterException {
        let reader = baseError.errorBodyReader
        var value = InvalidParameterException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        var value = ThrottlingException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension EKSAuthClientTypes.Subject {

    static func read(from reader: SmithyJSON.Reader) throws -> EKSAuthClientTypes.Subject {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EKSAuthClientTypes.Subject()
        value.namespace = try reader["namespace"].readIfPresent() ?? ""
        value.serviceAccount = try reader["serviceAccount"].readIfPresent() ?? ""
        return value
    }
}

extension EKSAuthClientTypes.PodIdentityAssociation {

    static func read(from reader: SmithyJSON.Reader) throws -> EKSAuthClientTypes.PodIdentityAssociation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EKSAuthClientTypes.PodIdentityAssociation()
        value.associationArn = try reader["associationArn"].readIfPresent() ?? ""
        value.associationId = try reader["associationId"].readIfPresent() ?? ""
        return value
    }
}

extension EKSAuthClientTypes.AssumedRoleUser {

    static func read(from reader: SmithyJSON.Reader) throws -> EKSAuthClientTypes.AssumedRoleUser {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EKSAuthClientTypes.AssumedRoleUser()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.assumeRoleId = try reader["assumeRoleId"].readIfPresent() ?? ""
        return value
    }
}

extension EKSAuthClientTypes.Credentials {

    static func read(from reader: SmithyJSON.Reader) throws -> EKSAuthClientTypes.Credentials {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EKSAuthClientTypes.Credentials()
        value.sessionToken = try reader["sessionToken"].readIfPresent() ?? ""
        value.secretAccessKey = try reader["secretAccessKey"].readIfPresent() ?? ""
        value.accessKeyId = try reader["accessKeyId"].readIfPresent() ?? ""
        value.expiration = try reader["expiration"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

public enum EKSAuthClientTypes {}
