//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.<PERSON>yReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
@_spi(SmithyTimestamps) import struct SmithyTimestamps.TimestampFormatter

extension EMRcontainersClientTypes {

    public enum CertificateProviderType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case pem
        case sdkUnknown(Swift.String)

        public static var allCases: [CertificateProviderType] {
            return [
                .pem
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .pem: return "PEM"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EMRcontainersClientTypes {

    /// Configurations related to the TLS certificate for the security configuration.
    public struct TLSCertificateConfiguration: Swift.Sendable {
        /// The TLS certificate type. Acceptable values: PEM or Custom.
        public var certificateProviderType: EMRcontainersClientTypes.CertificateProviderType?
        /// Secrets Manager ARN that contains the private TLS certificate contents, used for communication between the user job and the system job.
        public var privateCertificateSecretArn: Swift.String?
        /// Secrets Manager ARN that contains the public TLS certificate contents, used for communication between the user job and the system job.
        public var publicCertificateSecretArn: Swift.String?

        public init(
            certificateProviderType: EMRcontainersClientTypes.CertificateProviderType? = nil,
            privateCertificateSecretArn: Swift.String? = nil,
            publicCertificateSecretArn: Swift.String? = nil
        )
        {
            self.certificateProviderType = certificateProviderType
            self.privateCertificateSecretArn = privateCertificateSecretArn
            self.publicCertificateSecretArn = publicCertificateSecretArn
        }
    }
}

extension EMRcontainersClientTypes {

    /// Configurations related to in-transit encryption for the security configuration.
    public struct InTransitEncryptionConfiguration: Swift.Sendable {
        /// TLS certificate-related configuration input for the security configuration.
        public var tlsCertificateConfiguration: EMRcontainersClientTypes.TLSCertificateConfiguration?

        public init(
            tlsCertificateConfiguration: EMRcontainersClientTypes.TLSCertificateConfiguration? = nil
        )
        {
            self.tlsCertificateConfiguration = tlsCertificateConfiguration
        }
    }
}

extension EMRcontainersClientTypes {

    /// Configurations related to encryption for the security configuration.
    public struct EncryptionConfiguration: Swift.Sendable {
        /// In-transit encryption-related input for the security configuration.
        public var inTransitEncryptionConfiguration: EMRcontainersClientTypes.InTransitEncryptionConfiguration?

        public init(
            inTransitEncryptionConfiguration: EMRcontainersClientTypes.InTransitEncryptionConfiguration? = nil
        )
        {
            self.inTransitEncryptionConfiguration = inTransitEncryptionConfiguration
        }
    }
}

extension EMRcontainersClientTypes {

    /// Namespace inputs for the system job.
    public struct SecureNamespaceInfo: Swift.Sendable {
        /// The ID of the Amazon EKS cluster where Amazon EMR on EKS jobs run.
        public var clusterId: Swift.String?
        /// The namespace of the Amazon EKS cluster where the system jobs run.
        public var namespace: Swift.String?

        public init(
            clusterId: Swift.String? = nil,
            namespace: Swift.String? = nil
        )
        {
            self.clusterId = clusterId
            self.namespace = namespace
        }
    }
}

extension EMRcontainersClientTypes {

    /// Lake Formation related configuration inputs for the security configuration.
    public struct LakeFormationConfiguration: Swift.Sendable {
        /// The session tag to authorize Amazon EMR on EKS for API calls to Lake Formation.
        public var authorizedSessionTagValue: Swift.String?
        /// The query engine IAM role ARN that is tied to the secure Spark job. The QueryEngine role assumes the JobExecutionRole to execute all the Lake Formation calls.
        public var queryEngineRoleArn: Swift.String?
        /// The namespace input of the system job.
        public var secureNamespaceInfo: EMRcontainersClientTypes.SecureNamespaceInfo?

        public init(
            authorizedSessionTagValue: Swift.String? = nil,
            queryEngineRoleArn: Swift.String? = nil,
            secureNamespaceInfo: EMRcontainersClientTypes.SecureNamespaceInfo? = nil
        )
        {
            self.authorizedSessionTagValue = authorizedSessionTagValue
            self.queryEngineRoleArn = queryEngineRoleArn
            self.secureNamespaceInfo = secureNamespaceInfo
        }
    }
}

extension EMRcontainersClientTypes {

    /// Authorization-related configuration inputs for the security configuration.
    public struct AuthorizationConfiguration: Swift.Sendable {
        /// Encryption-related configuration input for the security configuration.
        public var encryptionConfiguration: EMRcontainersClientTypes.EncryptionConfiguration?
        /// Lake Formation related configuration inputs for the security configuration.
        public var lakeFormationConfiguration: EMRcontainersClientTypes.LakeFormationConfiguration?

        public init(
            encryptionConfiguration: EMRcontainersClientTypes.EncryptionConfiguration? = nil,
            lakeFormationConfiguration: EMRcontainersClientTypes.LakeFormationConfiguration? = nil
        )
        {
            self.encryptionConfiguration = encryptionConfiguration
            self.lakeFormationConfiguration = lakeFormationConfiguration
        }
    }
}

/// This is an internal server exception.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// There are invalid parameters in the client request.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct CancelJobRunInput: Swift.Sendable {
    /// The ID of the job run to cancel.
    /// This member is required.
    public var id: Swift.String?
    /// The ID of the virtual cluster for which the job run will be canceled.
    /// This member is required.
    public var virtualClusterId: Swift.String?

    public init(
        id: Swift.String? = nil,
        virtualClusterId: Swift.String? = nil
    )
    {
        self.id = id
        self.virtualClusterId = virtualClusterId
    }
}

public struct CancelJobRunOutput: Swift.Sendable {
    /// The output contains the ID of the cancelled job run.
    public var id: Swift.String?
    /// The output contains the virtual cluster ID for which the job run is cancelled.
    public var virtualClusterId: Swift.String?

    public init(
        id: Swift.String? = nil,
        virtualClusterId: Swift.String? = nil
    )
    {
        self.id = id
        self.virtualClusterId = virtualClusterId
    }
}

/// The specified resource was not found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension EMRcontainersClientTypes {

    /// A configuration for CloudWatch monitoring. You can configure your jobs to send log information to CloudWatch Logs. This data type allows job template parameters to be specified within.
    public struct ParametricCloudWatchMonitoringConfiguration: Swift.Sendable {
        /// The name of the log group for log publishing.
        public var logGroupName: Swift.String?
        /// The specified name prefix for log streams.
        public var logStreamNamePrefix: Swift.String?

        public init(
            logGroupName: Swift.String? = nil,
            logStreamNamePrefix: Swift.String? = nil
        )
        {
            self.logGroupName = logGroupName
            self.logStreamNamePrefix = logStreamNamePrefix
        }
    }
}

extension EMRcontainersClientTypes {

    /// Amazon S3 configuration for monitoring log publishing. You can configure your jobs to send log information to Amazon S3. This data type allows job template parameters to be specified within.
    public struct ParametricS3MonitoringConfiguration: Swift.Sendable {
        /// Amazon S3 destination URI for log publishing.
        public var logUri: Swift.String?

        public init(
            logUri: Swift.String? = nil
        )
        {
            self.logUri = logUri
        }
    }
}

extension EMRcontainersClientTypes {

    /// Configuration setting for monitoring. This data type allows job template parameters to be specified within.
    public struct ParametricMonitoringConfiguration: Swift.Sendable {
        /// Monitoring configurations for CloudWatch.
        public var cloudWatchMonitoringConfiguration: EMRcontainersClientTypes.ParametricCloudWatchMonitoringConfiguration?
        /// Monitoring configurations for the persistent application UI.
        public var persistentAppUI: Swift.String?
        /// Amazon S3 configuration for monitoring log publishing.
        public var s3MonitoringConfiguration: EMRcontainersClientTypes.ParametricS3MonitoringConfiguration?

        public init(
            cloudWatchMonitoringConfiguration: EMRcontainersClientTypes.ParametricCloudWatchMonitoringConfiguration? = nil,
            persistentAppUI: Swift.String? = nil,
            s3MonitoringConfiguration: EMRcontainersClientTypes.ParametricS3MonitoringConfiguration? = nil
        )
        {
            self.cloudWatchMonitoringConfiguration = cloudWatchMonitoringConfiguration
            self.persistentAppUI = persistentAppUI
            self.s3MonitoringConfiguration = s3MonitoringConfiguration
        }
    }
}

extension EMRcontainersClientTypes {

    /// The job driver for job type.
    public struct SparkSqlJobDriver: Swift.Sendable {
        /// The SQL file to be executed.
        public var entryPoint: Swift.String?
        /// The Spark parameters to be included in the Spark SQL command.
        public var sparkSqlParameters: Swift.String?

        public init(
            entryPoint: Swift.String? = nil,
            sparkSqlParameters: Swift.String? = nil
        )
        {
            self.entryPoint = entryPoint
            self.sparkSqlParameters = sparkSqlParameters
        }
    }
}

extension EMRcontainersClientTypes.SparkSqlJobDriver: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "SparkSqlJobDriver(entryPoint: \"CONTENT_REDACTED\", sparkSqlParameters: \"CONTENT_REDACTED\")"}
}

extension EMRcontainersClientTypes {

    /// The information about job driver for Spark submit.
    public struct SparkSubmitJobDriver: Swift.Sendable {
        /// The entry point of job application.
        /// This member is required.
        public var entryPoint: Swift.String?
        /// The arguments for job application.
        public var entryPointArguments: [Swift.String]?
        /// The Spark submit parameters that are used for job runs.
        public var sparkSubmitParameters: Swift.String?

        public init(
            entryPoint: Swift.String? = nil,
            entryPointArguments: [Swift.String]? = nil,
            sparkSubmitParameters: Swift.String? = nil
        )
        {
            self.entryPoint = entryPoint
            self.entryPointArguments = entryPointArguments
            self.sparkSubmitParameters = sparkSubmitParameters
        }
    }
}

extension EMRcontainersClientTypes.SparkSubmitJobDriver: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "SparkSubmitJobDriver(entryPoint: \"CONTENT_REDACTED\", entryPointArguments: \"CONTENT_REDACTED\", sparkSubmitParameters: \"CONTENT_REDACTED\")"}
}

extension EMRcontainersClientTypes {

    /// Specify the driver that the job runs on. Exactly one of the two available job drivers is required, either sparkSqlJobDriver or sparkSubmitJobDriver.
    public struct JobDriver: Swift.Sendable {
        /// The job driver for job type.
        public var sparkSqlJobDriver: EMRcontainersClientTypes.SparkSqlJobDriver?
        /// The job driver parameters specified for spark submit.
        public var sparkSubmitJobDriver: EMRcontainersClientTypes.SparkSubmitJobDriver?

        public init(
            sparkSqlJobDriver: EMRcontainersClientTypes.SparkSqlJobDriver? = nil,
            sparkSubmitJobDriver: EMRcontainersClientTypes.SparkSubmitJobDriver? = nil
        )
        {
            self.sparkSqlJobDriver = sparkSqlJobDriver
            self.sparkSubmitJobDriver = sparkSubmitJobDriver
        }
    }
}

extension EMRcontainersClientTypes {

    public enum TemplateParameterDataType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case number
        case string
        case sdkUnknown(Swift.String)

        public static var allCases: [TemplateParameterDataType] {
            return [
                .number,
                .string
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .number: return "NUMBER"
            case .string: return "STRING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EMRcontainersClientTypes {

    /// The configuration of a job template parameter.
    public struct TemplateParameterConfiguration: Swift.Sendable {
        /// The default value for the job template parameter.
        public var defaultValue: Swift.String?
        /// The type of the job template parameter. Allowed values are: ‘STRING’, ‘NUMBER’.
        public var type: EMRcontainersClientTypes.TemplateParameterDataType?

        public init(
            defaultValue: Swift.String? = nil,
            type: EMRcontainersClientTypes.TemplateParameterDataType? = nil
        )
        {
            self.defaultValue = defaultValue
            self.type = type
        }
    }
}

public struct CreateJobTemplateOutput: Swift.Sendable {
    /// This output display the ARN of the created job template.
    public var arn: Swift.String?
    /// This output displays the date and time when the job template was created.
    public var createdAt: Foundation.Date?
    /// This output display the created job template ID.
    public var id: Swift.String?
    /// This output displays the name of the created job template.
    public var name: Swift.String?

    public init(
        arn: Swift.String? = nil,
        createdAt: Foundation.Date? = nil,
        id: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.arn = arn
        self.createdAt = createdAt
        self.id = id
        self.name = name
    }
}

extension EMRcontainersClientTypes {

    /// A configuration for CloudWatch monitoring. You can configure your jobs to send log information to CloudWatch Logs.
    public struct CloudWatchMonitoringConfiguration: Swift.Sendable {
        /// The name of the log group for log publishing.
        /// This member is required.
        public var logGroupName: Swift.String?
        /// The specified name prefix for log streams.
        public var logStreamNamePrefix: Swift.String?

        public init(
            logGroupName: Swift.String? = nil,
            logStreamNamePrefix: Swift.String? = nil
        )
        {
            self.logGroupName = logGroupName
            self.logStreamNamePrefix = logStreamNamePrefix
        }
    }
}

extension EMRcontainersClientTypes {

    /// The settings for container log rotation.
    public struct ContainerLogRotationConfiguration: Swift.Sendable {
        /// The number of files to keep in container after rotation.
        /// This member is required.
        public var maxFilesToKeep: Swift.Int?
        /// The file size at which to rotate logs. Minimum of 2KB, Maximum of 2GB.
        /// This member is required.
        public var rotationSize: Swift.String?

        public init(
            maxFilesToKeep: Swift.Int? = nil,
            rotationSize: Swift.String? = nil
        )
        {
            self.maxFilesToKeep = maxFilesToKeep
            self.rotationSize = rotationSize
        }
    }
}

extension EMRcontainersClientTypes {

    public enum PersistentAppUI: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case enabled
        case sdkUnknown(Swift.String)

        public static var allCases: [PersistentAppUI] {
            return [
                .disabled,
                .enabled
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "DISABLED"
            case .enabled: return "ENABLED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EMRcontainersClientTypes {

    /// Amazon S3 configuration for monitoring log publishing. You can configure your jobs to send log information to Amazon S3.
    public struct S3MonitoringConfiguration: Swift.Sendable {
        /// Amazon S3 destination URI for log publishing.
        /// This member is required.
        public var logUri: Swift.String?

        public init(
            logUri: Swift.String? = nil
        )
        {
            self.logUri = logUri
        }
    }
}

extension EMRcontainersClientTypes {

    /// Configuration setting for monitoring.
    public struct MonitoringConfiguration: Swift.Sendable {
        /// Monitoring configurations for CloudWatch.
        public var cloudWatchMonitoringConfiguration: EMRcontainersClientTypes.CloudWatchMonitoringConfiguration?
        /// Enable or disable container log rotation.
        public var containerLogRotationConfiguration: EMRcontainersClientTypes.ContainerLogRotationConfiguration?
        /// Monitoring configurations for the persistent application UI.
        public var persistentAppUI: EMRcontainersClientTypes.PersistentAppUI?
        /// Amazon S3 configuration for monitoring log publishing.
        public var s3MonitoringConfiguration: EMRcontainersClientTypes.S3MonitoringConfiguration?

        public init(
            cloudWatchMonitoringConfiguration: EMRcontainersClientTypes.CloudWatchMonitoringConfiguration? = nil,
            containerLogRotationConfiguration: EMRcontainersClientTypes.ContainerLogRotationConfiguration? = nil,
            persistentAppUI: EMRcontainersClientTypes.PersistentAppUI? = nil,
            s3MonitoringConfiguration: EMRcontainersClientTypes.S3MonitoringConfiguration? = nil
        )
        {
            self.cloudWatchMonitoringConfiguration = cloudWatchMonitoringConfiguration
            self.containerLogRotationConfiguration = containerLogRotationConfiguration
            self.persistentAppUI = persistentAppUI
            self.s3MonitoringConfiguration = s3MonitoringConfiguration
        }
    }
}

public struct CreateManagedEndpointOutput: Swift.Sendable {
    /// The output contains the ARN of the managed endpoint.
    public var arn: Swift.String?
    /// The output contains the ID of the managed endpoint.
    public var id: Swift.String?
    /// The output contains the name of the managed endpoint.
    public var name: Swift.String?
    /// The output contains the ID of the virtual cluster.
    public var virtualClusterId: Swift.String?

    public init(
        arn: Swift.String? = nil,
        id: Swift.String? = nil,
        name: Swift.String? = nil,
        virtualClusterId: Swift.String? = nil
    )
    {
        self.arn = arn
        self.id = id
        self.name = name
        self.virtualClusterId = virtualClusterId
    }
}

extension EMRcontainersClientTypes {

    /// Configurations related to the security configuration for the request.
    public struct SecurityConfigurationData: Swift.Sendable {
        /// Authorization-related configuration input for the security configuration.
        public var authorizationConfiguration: EMRcontainersClientTypes.AuthorizationConfiguration?

        public init(
            authorizationConfiguration: EMRcontainersClientTypes.AuthorizationConfiguration? = nil
        )
        {
            self.authorizationConfiguration = authorizationConfiguration
        }
    }
}

public struct CreateSecurityConfigurationInput: Swift.Sendable {
    /// The client idempotency token to use when creating the security configuration.
    /// This member is required.
    public var clientToken: Swift.String?
    /// The name of the security configuration.
    /// This member is required.
    public var name: Swift.String?
    /// Security configuration input for the request.
    /// This member is required.
    public var securityConfigurationData: EMRcontainersClientTypes.SecurityConfigurationData?
    /// The tags to add to the security configuration.
    public var tags: [Swift.String: Swift.String]?

    public init(
        clientToken: Swift.String? = nil,
        name: Swift.String? = nil,
        securityConfigurationData: EMRcontainersClientTypes.SecurityConfigurationData? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.clientToken = clientToken
        self.name = name
        self.securityConfigurationData = securityConfigurationData
        self.tags = tags
    }
}

public struct CreateSecurityConfigurationOutput: Swift.Sendable {
    /// The ARN (Amazon Resource Name) of the security configuration.
    public var arn: Swift.String?
    /// The ID of the security configuration.
    public var id: Swift.String?
    /// The name of the security configuration.
    public var name: Swift.String?

    public init(
        arn: Swift.String? = nil,
        id: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.arn = arn
        self.id = id
        self.name = name
    }
}

/// The request exceeded the Amazon EKS API operation limits.
public struct EKSRequestThrottledException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "EKSRequestThrottledException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension EMRcontainersClientTypes {

    /// The information about the Amazon EKS cluster.
    public struct EksInfo: Swift.Sendable {
        /// The namespaces of the Amazon EKS cluster.
        public var namespace: Swift.String?

        public init(
            namespace: Swift.String? = nil
        )
        {
            self.namespace = namespace
        }
    }
}

extension EMRcontainersClientTypes {

    /// The information about the container used for a job run or a managed endpoint.
    public enum ContainerInfo: Swift.Sendable {
        /// The information about the Amazon EKS cluster.
        case eksinfo(EMRcontainersClientTypes.EksInfo)
        case sdkUnknown(Swift.String)
    }
}

extension EMRcontainersClientTypes {

    public enum ContainerProviderType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case eks
        case sdkUnknown(Swift.String)

        public static var allCases: [ContainerProviderType] {
            return [
                .eks
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .eks: return "EKS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EMRcontainersClientTypes {

    /// The information about the container provider.
    public struct ContainerProvider: Swift.Sendable {
        /// The ID of the container cluster.
        /// This member is required.
        public var id: Swift.String?
        /// The information about the container cluster.
        public var info: EMRcontainersClientTypes.ContainerInfo?
        /// The type of the container provider. Amazon EKS is the only supported type as of now.
        /// This member is required.
        public var type: EMRcontainersClientTypes.ContainerProviderType?

        public init(
            id: Swift.String? = nil,
            info: EMRcontainersClientTypes.ContainerInfo? = nil,
            type: EMRcontainersClientTypes.ContainerProviderType? = nil
        )
        {
            self.id = id
            self.info = info
            self.type = type
        }
    }
}

public struct CreateVirtualClusterInput: Swift.Sendable {
    /// The client token of the virtual cluster.
    /// This member is required.
    public var clientToken: Swift.String?
    /// The container provider of the virtual cluster.
    /// This member is required.
    public var containerProvider: EMRcontainersClientTypes.ContainerProvider?
    /// The specified name of the virtual cluster.
    /// This member is required.
    public var name: Swift.String?
    /// The ID of the security configuration.
    public var securityConfigurationId: Swift.String?
    /// The tags assigned to the virtual cluster.
    public var tags: [Swift.String: Swift.String]?

    public init(
        clientToken: Swift.String? = nil,
        containerProvider: EMRcontainersClientTypes.ContainerProvider? = nil,
        name: Swift.String? = nil,
        securityConfigurationId: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.clientToken = clientToken
        self.containerProvider = containerProvider
        self.name = name
        self.securityConfigurationId = securityConfigurationId
        self.tags = tags
    }
}

public struct CreateVirtualClusterOutput: Swift.Sendable {
    /// This output contains the ARN of virtual cluster.
    public var arn: Swift.String?
    /// This output contains the virtual cluster ID.
    public var id: Swift.String?
    /// This output contains the name of the virtual cluster.
    public var name: Swift.String?

    public init(
        arn: Swift.String? = nil,
        id: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.arn = arn
        self.id = id
        self.name = name
    }
}

public struct DeleteJobTemplateInput: Swift.Sendable {
    /// The ID of the job template that will be deleted.
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

public struct DeleteJobTemplateOutput: Swift.Sendable {
    /// This output contains the ID of the job template that was deleted.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

public struct DeleteManagedEndpointInput: Swift.Sendable {
    /// The ID of the managed endpoint.
    /// This member is required.
    public var id: Swift.String?
    /// The ID of the endpoint's virtual cluster.
    /// This member is required.
    public var virtualClusterId: Swift.String?

    public init(
        id: Swift.String? = nil,
        virtualClusterId: Swift.String? = nil
    )
    {
        self.id = id
        self.virtualClusterId = virtualClusterId
    }
}

public struct DeleteManagedEndpointOutput: Swift.Sendable {
    /// The output displays the ID of the managed endpoint.
    public var id: Swift.String?
    /// The output displays the ID of the endpoint's virtual cluster.
    public var virtualClusterId: Swift.String?

    public init(
        id: Swift.String? = nil,
        virtualClusterId: Swift.String? = nil
    )
    {
        self.id = id
        self.virtualClusterId = virtualClusterId
    }
}

public struct DeleteVirtualClusterInput: Swift.Sendable {
    /// The ID of the virtual cluster that will be deleted.
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

public struct DeleteVirtualClusterOutput: Swift.Sendable {
    /// This output contains the ID of the virtual cluster that will be deleted.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

public struct DescribeJobRunInput: Swift.Sendable {
    /// The ID of the job run request.
    /// This member is required.
    public var id: Swift.String?
    /// The ID of the virtual cluster for which the job run is submitted.
    /// This member is required.
    public var virtualClusterId: Swift.String?

    public init(
        id: Swift.String? = nil,
        virtualClusterId: Swift.String? = nil
    )
    {
        self.id = id
        self.virtualClusterId = virtualClusterId
    }
}

extension EMRcontainersClientTypes {

    public enum FailureReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case clusterUnavailable
        case internalError
        case userError
        case validationError
        case sdkUnknown(Swift.String)

        public static var allCases: [FailureReason] {
            return [
                .clusterUnavailable,
                .internalError,
                .userError,
                .validationError
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .clusterUnavailable: return "CLUSTER_UNAVAILABLE"
            case .internalError: return "INTERNAL_ERROR"
            case .userError: return "USER_ERROR"
            case .validationError: return "VALIDATION_ERROR"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EMRcontainersClientTypes {

    /// The configuration of the retry policy that the job runs on.
    public struct RetryPolicyConfiguration: Swift.Sendable {
        /// The maximum number of attempts on the job's driver.
        /// This member is required.
        public var maxAttempts: Swift.Int?

        public init(
            maxAttempts: Swift.Int? = nil
        )
        {
            self.maxAttempts = maxAttempts
        }
    }
}

extension EMRcontainersClientTypes {

    /// The current status of the retry policy executed on the job.
    public struct RetryPolicyExecution: Swift.Sendable {
        /// The current number of attempts made on the driver of the job.
        /// This member is required.
        public var currentAttemptCount: Swift.Int?

        public init(
            currentAttemptCount: Swift.Int? = nil
        )
        {
            self.currentAttemptCount = currentAttemptCount
        }
    }
}

extension EMRcontainersClientTypes {

    public enum JobRunState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cancelled
        case cancelPending
        case completed
        case failed
        case pending
        case running
        case submitted
        case sdkUnknown(Swift.String)

        public static var allCases: [JobRunState] {
            return [
                .cancelled,
                .cancelPending,
                .completed,
                .failed,
                .pending,
                .running,
                .submitted
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cancelled: return "CANCELLED"
            case .cancelPending: return "CANCEL_PENDING"
            case .completed: return "COMPLETED"
            case .failed: return "FAILED"
            case .pending: return "PENDING"
            case .running: return "RUNNING"
            case .submitted: return "SUBMITTED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeJobTemplateInput: Swift.Sendable {
    /// The ID of the job template that will be described.
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

public struct DescribeManagedEndpointInput: Swift.Sendable {
    /// This output displays ID of the managed endpoint.
    /// This member is required.
    public var id: Swift.String?
    /// The ID of the endpoint's virtual cluster.
    /// This member is required.
    public var virtualClusterId: Swift.String?

    public init(
        id: Swift.String? = nil,
        virtualClusterId: Swift.String? = nil
    )
    {
        self.id = id
        self.virtualClusterId = virtualClusterId
    }
}

extension EMRcontainersClientTypes {

    /// The entity representing certificate data generated for managed endpoint.
    public struct Certificate: Swift.Sendable {
        /// The ARN of the certificate generated for managed endpoint.
        public var certificateArn: Swift.String?
        /// The base64 encoded PEM certificate data generated for managed endpoint.
        public var certificateData: Swift.String?

        public init(
            certificateArn: Swift.String? = nil,
            certificateData: Swift.String? = nil
        )
        {
            self.certificateArn = certificateArn
            self.certificateData = certificateData
        }
    }
}

extension EMRcontainersClientTypes {

    public enum EndpointState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case active
        case creating
        case terminated
        case terminatedWithErrors
        case terminating
        case sdkUnknown(Swift.String)

        public static var allCases: [EndpointState] {
            return [
                .active,
                .creating,
                .terminated,
                .terminatedWithErrors,
                .terminating
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .active: return "ACTIVE"
            case .creating: return "CREATING"
            case .terminated: return "TERMINATED"
            case .terminatedWithErrors: return "TERMINATED_WITH_ERRORS"
            case .terminating: return "TERMINATING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeSecurityConfigurationInput: Swift.Sendable {
    /// The ID of the security configuration.
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

extension EMRcontainersClientTypes {

    /// Inputs related to the security configuration. Security configurations in Amazon EMR on EKS are templates for different security setups. You can use security configurations to configure the Lake Formation integration setup. You can also create a security configuration to re-use a security setup each time you create a virtual cluster.
    public struct SecurityConfiguration: Swift.Sendable {
        /// The ARN (Amazon Resource Name) of the security configuration.
        public var arn: Swift.String?
        /// The date and time that the job run was created.
        public var createdAt: Foundation.Date?
        /// The user who created the job run.
        public var createdBy: Swift.String?
        /// The ID of the security configuration.
        public var id: Swift.String?
        /// The name of the security configuration.
        public var name: Swift.String?
        /// Security configuration inputs for the request.
        public var securityConfigurationData: EMRcontainersClientTypes.SecurityConfigurationData?
        /// The tags to assign to the security configuration.
        public var tags: [Swift.String: Swift.String]?

        public init(
            arn: Swift.String? = nil,
            createdAt: Foundation.Date? = nil,
            createdBy: Swift.String? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil,
            securityConfigurationData: EMRcontainersClientTypes.SecurityConfigurationData? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.arn = arn
            self.createdAt = createdAt
            self.createdBy = createdBy
            self.id = id
            self.name = name
            self.securityConfigurationData = securityConfigurationData
            self.tags = tags
        }
    }
}

public struct DescribeSecurityConfigurationOutput: Swift.Sendable {
    /// Details of the security configuration.
    public var securityConfiguration: EMRcontainersClientTypes.SecurityConfiguration?

    public init(
        securityConfiguration: EMRcontainersClientTypes.SecurityConfiguration? = nil
    )
    {
        self.securityConfiguration = securityConfiguration
    }
}

public struct DescribeVirtualClusterInput: Swift.Sendable {
    /// The ID of the virtual cluster that will be described.
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

extension EMRcontainersClientTypes {

    public enum VirtualClusterState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case arrested
        case running
        case terminated
        case terminating
        case sdkUnknown(Swift.String)

        public static var allCases: [VirtualClusterState] {
            return [
                .arrested,
                .running,
                .terminated,
                .terminating
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .arrested: return "ARRESTED"
            case .running: return "RUNNING"
            case .terminated: return "TERMINATED"
            case .terminating: return "TERMINATING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension EMRcontainersClientTypes {

    /// This entity describes a virtual cluster. A virtual cluster is a Kubernetes namespace that Amazon EMR is registered with. Amazon EMR uses virtual clusters to run jobs and host endpoints. Multiple virtual clusters can be backed by the same physical cluster. However, each virtual cluster maps to one namespace on an Amazon EKS cluster. Virtual clusters do not create any active resources that contribute to your bill or that require lifecycle management outside the service.
    public struct VirtualCluster: Swift.Sendable {
        /// The ARN of the virtual cluster.
        public var arn: Swift.String?
        /// The container provider of the virtual cluster.
        public var containerProvider: EMRcontainersClientTypes.ContainerProvider?
        /// The date and time when the virtual cluster is created.
        public var createdAt: Foundation.Date?
        /// The ID of the virtual cluster.
        public var id: Swift.String?
        /// The name of the virtual cluster.
        public var name: Swift.String?
        /// The ID of the security configuration.
        public var securityConfigurationId: Swift.String?
        /// The state of the virtual cluster.
        public var state: EMRcontainersClientTypes.VirtualClusterState?
        /// The assigned tags of the virtual cluster.
        public var tags: [Swift.String: Swift.String]?

        public init(
            arn: Swift.String? = nil,
            containerProvider: EMRcontainersClientTypes.ContainerProvider? = nil,
            createdAt: Foundation.Date? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil,
            securityConfigurationId: Swift.String? = nil,
            state: EMRcontainersClientTypes.VirtualClusterState? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.arn = arn
            self.containerProvider = containerProvider
            self.createdAt = createdAt
            self.id = id
            self.name = name
            self.securityConfigurationId = securityConfigurationId
            self.state = state
            self.tags = tags
        }
    }
}

public struct DescribeVirtualClusterOutput: Swift.Sendable {
    /// This output displays information about the specified virtual cluster.
    public var virtualCluster: EMRcontainersClientTypes.VirtualCluster?

    public init(
        virtualCluster: EMRcontainersClientTypes.VirtualCluster? = nil
    )
    {
        self.virtualCluster = virtualCluster
    }
}

/// The request throttled.
public struct RequestThrottledException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RequestThrottledException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct GetManagedEndpointSessionCredentialsInput: Swift.Sendable {
    /// The client idempotency token of the job run request.
    public var clientToken: Swift.String?
    /// Type of the token requested. Currently supported and default value of this field is “TOKEN.”
    /// This member is required.
    public var credentialType: Swift.String?
    /// Duration in seconds for which the session token is valid. The default duration is 15 minutes and the maximum is 12 hours.
    public var durationInSeconds: Swift.Int?
    /// The ARN of the managed endpoint for which the request is submitted.
    /// This member is required.
    public var endpointIdentifier: Swift.String?
    /// The IAM Execution Role ARN that will be used by the job run.
    /// This member is required.
    public var executionRoleArn: Swift.String?
    /// String identifier used to separate sections of the execution logs uploaded to S3.
    public var logContext: Swift.String?
    /// The ARN of the Virtual Cluster which the Managed Endpoint belongs to.
    /// This member is required.
    public var virtualClusterIdentifier: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        credentialType: Swift.String? = nil,
        durationInSeconds: Swift.Int? = nil,
        endpointIdentifier: Swift.String? = nil,
        executionRoleArn: Swift.String? = nil,
        logContext: Swift.String? = nil,
        virtualClusterIdentifier: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.credentialType = credentialType
        self.durationInSeconds = durationInSeconds
        self.endpointIdentifier = endpointIdentifier
        self.executionRoleArn = executionRoleArn
        self.logContext = logContext
        self.virtualClusterIdentifier = virtualClusterIdentifier
    }
}

extension EMRcontainersClientTypes {

    /// The structure containing the session token being returned.
    public enum Credentials: Swift.Sendable {
        /// The actual session token being returned.
        case token(Swift.String)
        case sdkUnknown(Swift.String)
    }
}

public struct GetManagedEndpointSessionCredentialsOutput: Swift.Sendable {
    /// The structure containing the session credentials.
    public var credentials: EMRcontainersClientTypes.Credentials?
    /// The date and time when the session token will expire.
    public var expiresAt: Foundation.Date?
    /// The identifier of the session token returned.
    public var id: Swift.String?

    public init(
        credentials: EMRcontainersClientTypes.Credentials? = nil,
        expiresAt: Foundation.Date? = nil,
        id: Swift.String? = nil
    )
    {
        self.credentials = credentials
        self.expiresAt = expiresAt
        self.id = id
    }
}

public struct ListJobRunsInput: Swift.Sendable {
    /// The date and time after which the job runs were submitted.
    public var createdAfter: Foundation.Date?
    /// The date and time before which the job runs were submitted.
    public var createdBefore: Foundation.Date?
    /// The maximum number of job runs that can be listed.
    public var maxResults: Swift.Int?
    /// The name of the job run.
    public var name: Swift.String?
    /// The token for the next set of job runs to return.
    public var nextToken: Swift.String?
    /// The states of the job run.
    public var states: [EMRcontainersClientTypes.JobRunState]?
    /// The ID of the virtual cluster for which to list the job run.
    /// This member is required.
    public var virtualClusterId: Swift.String?

    public init(
        createdAfter: Foundation.Date? = nil,
        createdBefore: Foundation.Date? = nil,
        maxResults: Swift.Int? = nil,
        name: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        states: [EMRcontainersClientTypes.JobRunState]? = nil,
        virtualClusterId: Swift.String? = nil
    )
    {
        self.createdAfter = createdAfter
        self.createdBefore = createdBefore
        self.maxResults = maxResults
        self.name = name
        self.nextToken = nextToken
        self.states = states
        self.virtualClusterId = virtualClusterId
    }
}

public struct ListJobTemplatesInput: Swift.Sendable {
    /// The date and time after which the job templates were created.
    public var createdAfter: Foundation.Date?
    /// The date and time before which the job templates were created.
    public var createdBefore: Foundation.Date?
    /// The maximum number of job templates that can be listed.
    public var maxResults: Swift.Int?
    /// The token for the next set of job templates to return.
    public var nextToken: Swift.String?

    public init(
        createdAfter: Foundation.Date? = nil,
        createdBefore: Foundation.Date? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.createdAfter = createdAfter
        self.createdBefore = createdBefore
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListManagedEndpointsInput: Swift.Sendable {
    /// The date and time after which the endpoints are created.
    public var createdAfter: Foundation.Date?
    /// The date and time before which the endpoints are created.
    public var createdBefore: Foundation.Date?
    /// The maximum number of managed endpoints that can be listed.
    public var maxResults: Swift.Int?
    /// The token for the next set of managed endpoints to return.
    public var nextToken: Swift.String?
    /// The states of the managed endpoints.
    public var states: [EMRcontainersClientTypes.EndpointState]?
    /// The types of the managed endpoints.
    public var types: [Swift.String]?
    /// The ID of the virtual cluster.
    /// This member is required.
    public var virtualClusterId: Swift.String?

    public init(
        createdAfter: Foundation.Date? = nil,
        createdBefore: Foundation.Date? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        states: [EMRcontainersClientTypes.EndpointState]? = nil,
        types: [Swift.String]? = nil,
        virtualClusterId: Swift.String? = nil
    )
    {
        self.createdAfter = createdAfter
        self.createdBefore = createdBefore
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.states = states
        self.types = types
        self.virtualClusterId = virtualClusterId
    }
}

public struct ListSecurityConfigurationsInput: Swift.Sendable {
    /// The date and time after which the security configuration was created.
    public var createdAfter: Foundation.Date?
    /// The date and time before which the security configuration was created.
    public var createdBefore: Foundation.Date?
    /// The maximum number of security configurations the operation can list.
    public var maxResults: Swift.Int?
    /// The token for the next set of security configurations to return.
    public var nextToken: Swift.String?

    public init(
        createdAfter: Foundation.Date? = nil,
        createdBefore: Foundation.Date? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.createdAfter = createdAfter
        self.createdBefore = createdBefore
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListSecurityConfigurationsOutput: Swift.Sendable {
    /// The token for the next set of security configurations to return.
    public var nextToken: Swift.String?
    /// The list of returned security configurations.
    public var securityConfigurations: [EMRcontainersClientTypes.SecurityConfiguration]?

    public init(
        nextToken: Swift.String? = nil,
        securityConfigurations: [EMRcontainersClientTypes.SecurityConfiguration]? = nil
    )
    {
        self.nextToken = nextToken
        self.securityConfigurations = securityConfigurations
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The ARN of tagged resources.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// The tags assigned to resources.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

public struct ListVirtualClustersInput: Swift.Sendable {
    /// The container provider ID of the virtual cluster.
    public var containerProviderId: Swift.String?
    /// The container provider type of the virtual cluster. Amazon EKS is the only supported type as of now.
    public var containerProviderType: EMRcontainersClientTypes.ContainerProviderType?
    /// The date and time after which the virtual clusters are created.
    public var createdAfter: Foundation.Date?
    /// The date and time before which the virtual clusters are created.
    public var createdBefore: Foundation.Date?
    /// Optional Boolean that specifies whether the operation should return the virtual clusters that have the access entry integration enabled or disabled. If not specified, the operation returns all applicable virtual clusters.
    public var eksAccessEntryIntegrated: Swift.Bool?
    /// The maximum number of virtual clusters that can be listed.
    public var maxResults: Swift.Int?
    /// The token for the next set of virtual clusters to return.
    public var nextToken: Swift.String?
    /// The states of the requested virtual clusters.
    public var states: [EMRcontainersClientTypes.VirtualClusterState]?

    public init(
        containerProviderId: Swift.String? = nil,
        containerProviderType: EMRcontainersClientTypes.ContainerProviderType? = nil,
        createdAfter: Foundation.Date? = nil,
        createdBefore: Foundation.Date? = nil,
        eksAccessEntryIntegrated: Swift.Bool? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        states: [EMRcontainersClientTypes.VirtualClusterState]? = nil
    )
    {
        self.containerProviderId = containerProviderId
        self.containerProviderType = containerProviderType
        self.createdAfter = createdAfter
        self.createdBefore = createdBefore
        self.eksAccessEntryIntegrated = eksAccessEntryIntegrated
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.states = states
    }
}

public struct ListVirtualClustersOutput: Swift.Sendable {
    /// This output displays the token for the next set of virtual clusters.
    public var nextToken: Swift.String?
    /// This output lists the specified virtual clusters.
    public var virtualClusters: [EMRcontainersClientTypes.VirtualCluster]?

    public init(
        nextToken: Swift.String? = nil,
        virtualClusters: [EMRcontainersClientTypes.VirtualCluster]? = nil
    )
    {
        self.nextToken = nextToken
        self.virtualClusters = virtualClusters
    }
}

public struct StartJobRunOutput: Swift.Sendable {
    /// This output lists the ARN of job run.
    public var arn: Swift.String?
    /// This output displays the started job run ID.
    public var id: Swift.String?
    /// This output displays the name of the started job run.
    public var name: Swift.String?
    /// This output displays the virtual cluster ID for which the job run was submitted.
    public var virtualClusterId: Swift.String?

    public init(
        arn: Swift.String? = nil,
        id: Swift.String? = nil,
        name: Swift.String? = nil,
        virtualClusterId: Swift.String? = nil
    )
    {
        self.arn = arn
        self.id = id
        self.name = name
        self.virtualClusterId = virtualClusterId
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The ARN of resources.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tags assigned to resources.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The ARN of resources.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tag keys of the resources.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

extension EMRcontainersClientTypes {

    /// A configuration specification to be used when provisioning virtual clusters, which can include configurations for applications and software bundled with Amazon EMR on EKS. A configuration consists of a classification, properties, and optional nested configurations. A classification refers to an application-specific configuration file. Properties are the settings you want to change in that file.
    public struct Configuration: Swift.Sendable {
        /// The classification within a configuration.
        /// This member is required.
        public var classification: Swift.String?
        /// A list of additional configurations to apply within a configuration object.
        public var configurations: [EMRcontainersClientTypes.Configuration]?
        /// A set of properties specified within a configuration classification.
        public var properties: [Swift.String: Swift.String]?

        public init(
            classification: Swift.String? = nil,
            configurations: [EMRcontainersClientTypes.Configuration]? = nil,
            properties: [Swift.String: Swift.String]? = nil
        )
        {
            self.classification = classification
            self.configurations = configurations
            self.properties = properties
        }
    }
}

extension EMRcontainersClientTypes.Configuration: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "Configuration(classification: \(Swift.String(describing: classification)), configurations: \(Swift.String(describing: configurations)), properties: \"CONTENT_REDACTED\")"}
}

extension EMRcontainersClientTypes {

    /// A configuration specification to be used to override existing configurations.
    public struct ConfigurationOverrides: Swift.Sendable {
        /// The configurations for the application running by the job run.
        public var applicationConfiguration: [EMRcontainersClientTypes.Configuration]?
        /// The configurations for monitoring.
        public var monitoringConfiguration: EMRcontainersClientTypes.MonitoringConfiguration?

        public init(
            applicationConfiguration: [EMRcontainersClientTypes.Configuration]? = nil,
            monitoringConfiguration: EMRcontainersClientTypes.MonitoringConfiguration? = nil
        )
        {
            self.applicationConfiguration = applicationConfiguration
            self.monitoringConfiguration = monitoringConfiguration
        }
    }
}

extension EMRcontainersClientTypes {

    /// A configuration specification to be used to override existing configurations. This data type allows job template parameters to be specified within.
    public struct ParametricConfigurationOverrides: Swift.Sendable {
        /// The configurations for the application running by the job run.
        public var applicationConfiguration: [EMRcontainersClientTypes.Configuration]?
        /// The configurations for monitoring.
        public var monitoringConfiguration: EMRcontainersClientTypes.ParametricMonitoringConfiguration?

        public init(
            applicationConfiguration: [EMRcontainersClientTypes.Configuration]? = nil,
            monitoringConfiguration: EMRcontainersClientTypes.ParametricMonitoringConfiguration? = nil
        )
        {
            self.applicationConfiguration = applicationConfiguration
            self.monitoringConfiguration = monitoringConfiguration
        }
    }
}

extension EMRcontainersClientTypes {

    /// This entity represents the endpoint that is managed by Amazon EMR on EKS.
    public struct Endpoint: Swift.Sendable {
        /// The ARN of the endpoint.
        public var arn: Swift.String?
        /// The certificate ARN of the endpoint. This field is under deprecation and will be removed in future.
        @available(*, deprecated, message: "Customer provided certificate-arn is deprecated and would be removed in future.")
        public var certificateArn: Swift.String?
        /// The certificate generated by emr control plane on customer behalf to secure the managed endpoint.
        public var certificateAuthority: EMRcontainersClientTypes.Certificate?
        /// The configuration settings that are used to override existing configurations for endpoints.
        public var configurationOverrides: EMRcontainersClientTypes.ConfigurationOverrides?
        /// The date and time when the endpoint was created.
        public var createdAt: Foundation.Date?
        /// The execution role ARN of the endpoint.
        public var executionRoleArn: Swift.String?
        /// The reasons why the endpoint has failed.
        public var failureReason: EMRcontainersClientTypes.FailureReason?
        /// The ID of the endpoint.
        public var id: Swift.String?
        /// The name of the endpoint.
        public var name: Swift.String?
        /// The EMR release version to be used for the endpoint.
        public var releaseLabel: Swift.String?
        /// The security group configuration of the endpoint.
        public var securityGroup: Swift.String?
        /// The server URL of the endpoint.
        public var serverUrl: Swift.String?
        /// The state of the endpoint.
        public var state: EMRcontainersClientTypes.EndpointState?
        /// Additional details of the endpoint state.
        public var stateDetails: Swift.String?
        /// The subnet IDs of the endpoint.
        public var subnetIds: [Swift.String]?
        /// The tags of the endpoint.
        public var tags: [Swift.String: Swift.String]?
        /// The type of the endpoint.
        public var type: Swift.String?
        /// The ID of the endpoint's virtual cluster.
        public var virtualClusterId: Swift.String?

        public init(
            arn: Swift.String? = nil,
            certificateArn: Swift.String? = nil,
            certificateAuthority: EMRcontainersClientTypes.Certificate? = nil,
            configurationOverrides: EMRcontainersClientTypes.ConfigurationOverrides? = nil,
            createdAt: Foundation.Date? = nil,
            executionRoleArn: Swift.String? = nil,
            failureReason: EMRcontainersClientTypes.FailureReason? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil,
            releaseLabel: Swift.String? = nil,
            securityGroup: Swift.String? = nil,
            serverUrl: Swift.String? = nil,
            state: EMRcontainersClientTypes.EndpointState? = nil,
            stateDetails: Swift.String? = nil,
            subnetIds: [Swift.String]? = nil,
            tags: [Swift.String: Swift.String]? = nil,
            type: Swift.String? = nil,
            virtualClusterId: Swift.String? = nil
        )
        {
            self.arn = arn
            self.certificateArn = certificateArn
            self.certificateAuthority = certificateAuthority
            self.configurationOverrides = configurationOverrides
            self.createdAt = createdAt
            self.executionRoleArn = executionRoleArn
            self.failureReason = failureReason
            self.id = id
            self.name = name
            self.releaseLabel = releaseLabel
            self.securityGroup = securityGroup
            self.serverUrl = serverUrl
            self.state = state
            self.stateDetails = stateDetails
            self.subnetIds = subnetIds
            self.tags = tags
            self.type = type
            self.virtualClusterId = virtualClusterId
        }
    }
}

extension EMRcontainersClientTypes {

    /// This entity describes a job run. A job run is a unit of work, such as a Spark jar, PySpark script, or SparkSQL query, that you submit to Amazon EMR on EKS.
    public struct JobRun: Swift.Sendable {
        /// The ARN of job run.
        public var arn: Swift.String?
        /// The client token used to start a job run.
        public var clientToken: Swift.String?
        /// The configuration settings that are used to override default configuration.
        public var configurationOverrides: EMRcontainersClientTypes.ConfigurationOverrides?
        /// The date and time when the job run was created.
        public var createdAt: Foundation.Date?
        /// The user who created the job run.
        public var createdBy: Swift.String?
        /// The execution role ARN of the job run.
        public var executionRoleArn: Swift.String?
        /// The reasons why the job run has failed.
        public var failureReason: EMRcontainersClientTypes.FailureReason?
        /// The date and time when the job run has finished.
        public var finishedAt: Foundation.Date?
        /// The ID of the job run.
        public var id: Swift.String?
        /// Parameters of job driver for the job run.
        public var jobDriver: EMRcontainersClientTypes.JobDriver?
        /// The name of the job run.
        public var name: Swift.String?
        /// The release version of Amazon EMR.
        public var releaseLabel: Swift.String?
        /// The configuration of the retry policy that the job runs on.
        public var retryPolicyConfiguration: EMRcontainersClientTypes.RetryPolicyConfiguration?
        /// The current status of the retry policy executed on the job.
        public var retryPolicyExecution: EMRcontainersClientTypes.RetryPolicyExecution?
        /// The state of the job run.
        public var state: EMRcontainersClientTypes.JobRunState?
        /// Additional details of the job run state.
        public var stateDetails: Swift.String?
        /// The assigned tags of the job run.
        public var tags: [Swift.String: Swift.String]?
        /// The ID of the job run's virtual cluster.
        public var virtualClusterId: Swift.String?

        public init(
            arn: Swift.String? = nil,
            clientToken: Swift.String? = nil,
            configurationOverrides: EMRcontainersClientTypes.ConfigurationOverrides? = nil,
            createdAt: Foundation.Date? = nil,
            createdBy: Swift.String? = nil,
            executionRoleArn: Swift.String? = nil,
            failureReason: EMRcontainersClientTypes.FailureReason? = nil,
            finishedAt: Foundation.Date? = nil,
            id: Swift.String? = nil,
            jobDriver: EMRcontainersClientTypes.JobDriver? = nil,
            name: Swift.String? = nil,
            releaseLabel: Swift.String? = nil,
            retryPolicyConfiguration: EMRcontainersClientTypes.RetryPolicyConfiguration? = nil,
            retryPolicyExecution: EMRcontainersClientTypes.RetryPolicyExecution? = nil,
            state: EMRcontainersClientTypes.JobRunState? = nil,
            stateDetails: Swift.String? = nil,
            tags: [Swift.String: Swift.String]? = nil,
            virtualClusterId: Swift.String? = nil
        )
        {
            self.arn = arn
            self.clientToken = clientToken
            self.configurationOverrides = configurationOverrides
            self.createdAt = createdAt
            self.createdBy = createdBy
            self.executionRoleArn = executionRoleArn
            self.failureReason = failureReason
            self.finishedAt = finishedAt
            self.id = id
            self.jobDriver = jobDriver
            self.name = name
            self.releaseLabel = releaseLabel
            self.retryPolicyConfiguration = retryPolicyConfiguration
            self.retryPolicyExecution = retryPolicyExecution
            self.state = state
            self.stateDetails = stateDetails
            self.tags = tags
            self.virtualClusterId = virtualClusterId
        }
    }
}

extension EMRcontainersClientTypes {

    /// The values of StartJobRun API requests used in job runs started using the job template.
    public struct JobTemplateData: Swift.Sendable {
        /// The configuration settings that are used to override defaults configuration.
        public var configurationOverrides: EMRcontainersClientTypes.ParametricConfigurationOverrides?
        /// The execution role ARN of the job run.
        /// This member is required.
        public var executionRoleArn: Swift.String?
        /// Specify the driver that the job runs on. Exactly one of the two available job drivers is required, either sparkSqlJobDriver or sparkSubmitJobDriver.
        /// This member is required.
        public var jobDriver: EMRcontainersClientTypes.JobDriver?
        /// The tags assigned to jobs started using the job template.
        public var jobTags: [Swift.String: Swift.String]?
        /// The configuration of parameters existing in the job template.
        public var parameterConfiguration: [Swift.String: EMRcontainersClientTypes.TemplateParameterConfiguration]?
        /// The release version of Amazon EMR.
        /// This member is required.
        public var releaseLabel: Swift.String?

        public init(
            configurationOverrides: EMRcontainersClientTypes.ParametricConfigurationOverrides? = nil,
            executionRoleArn: Swift.String? = nil,
            jobDriver: EMRcontainersClientTypes.JobDriver? = nil,
            jobTags: [Swift.String: Swift.String]? = nil,
            parameterConfiguration: [Swift.String: EMRcontainersClientTypes.TemplateParameterConfiguration]? = nil,
            releaseLabel: Swift.String? = nil
        )
        {
            self.configurationOverrides = configurationOverrides
            self.executionRoleArn = executionRoleArn
            self.jobDriver = jobDriver
            self.jobTags = jobTags
            self.parameterConfiguration = parameterConfiguration
            self.releaseLabel = releaseLabel
        }
    }
}

public struct CreateManagedEndpointInput: Swift.Sendable {
    /// The certificate ARN provided by users for the managed endpoint. This field is under deprecation and will be removed in future releases.
    @available(*, deprecated, message: "Customer provided certificate-arn is deprecated and would be removed in future.")
    public var certificateArn: Swift.String?
    /// The client idempotency token for this create call.
    /// This member is required.
    public var clientToken: Swift.String?
    /// The configuration settings that will be used to override existing configurations.
    public var configurationOverrides: EMRcontainersClientTypes.ConfigurationOverrides?
    /// The ARN of the execution role.
    /// This member is required.
    public var executionRoleArn: Swift.String?
    /// The name of the managed endpoint.
    /// This member is required.
    public var name: Swift.String?
    /// The Amazon EMR release version.
    /// This member is required.
    public var releaseLabel: Swift.String?
    /// The tags of the managed endpoint.
    public var tags: [Swift.String: Swift.String]?
    /// The type of the managed endpoint.
    /// This member is required.
    public var type: Swift.String?
    /// The ID of the virtual cluster for which a managed endpoint is created.
    /// This member is required.
    public var virtualClusterId: Swift.String?

    public init(
        certificateArn: Swift.String? = nil,
        clientToken: Swift.String? = nil,
        configurationOverrides: EMRcontainersClientTypes.ConfigurationOverrides? = nil,
        executionRoleArn: Swift.String? = nil,
        name: Swift.String? = nil,
        releaseLabel: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        type: Swift.String? = nil,
        virtualClusterId: Swift.String? = nil
    )
    {
        self.certificateArn = certificateArn
        self.clientToken = clientToken
        self.configurationOverrides = configurationOverrides
        self.executionRoleArn = executionRoleArn
        self.name = name
        self.releaseLabel = releaseLabel
        self.tags = tags
        self.type = type
        self.virtualClusterId = virtualClusterId
    }
}

public struct StartJobRunInput: Swift.Sendable {
    /// The client idempotency token of the job run request.
    /// This member is required.
    public var clientToken: Swift.String?
    /// The configuration overrides for the job run.
    public var configurationOverrides: EMRcontainersClientTypes.ConfigurationOverrides?
    /// The execution role ARN for the job run.
    public var executionRoleArn: Swift.String?
    /// The job driver for the job run.
    public var jobDriver: EMRcontainersClientTypes.JobDriver?
    /// The job template ID to be used to start the job run.
    public var jobTemplateId: Swift.String?
    /// The values of job template parameters to start a job run.
    public var jobTemplateParameters: [Swift.String: Swift.String]?
    /// The name of the job run.
    public var name: Swift.String?
    /// The Amazon EMR release version to use for the job run.
    public var releaseLabel: Swift.String?
    /// The retry policy configuration for the job run.
    public var retryPolicyConfiguration: EMRcontainersClientTypes.RetryPolicyConfiguration?
    /// The tags assigned to job runs.
    public var tags: [Swift.String: Swift.String]?
    /// The virtual cluster ID for which the job run request is submitted.
    /// This member is required.
    public var virtualClusterId: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        configurationOverrides: EMRcontainersClientTypes.ConfigurationOverrides? = nil,
        executionRoleArn: Swift.String? = nil,
        jobDriver: EMRcontainersClientTypes.JobDriver? = nil,
        jobTemplateId: Swift.String? = nil,
        jobTemplateParameters: [Swift.String: Swift.String]? = nil,
        name: Swift.String? = nil,
        releaseLabel: Swift.String? = nil,
        retryPolicyConfiguration: EMRcontainersClientTypes.RetryPolicyConfiguration? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        virtualClusterId: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.configurationOverrides = configurationOverrides
        self.executionRoleArn = executionRoleArn
        self.jobDriver = jobDriver
        self.jobTemplateId = jobTemplateId
        self.jobTemplateParameters = jobTemplateParameters
        self.name = name
        self.releaseLabel = releaseLabel
        self.retryPolicyConfiguration = retryPolicyConfiguration
        self.tags = tags
        self.virtualClusterId = virtualClusterId
    }
}

extension EMRcontainersClientTypes {

    /// This entity describes a job template. Job template stores values of StartJobRun API request in a template and can be used to start a job run. Job template allows two use cases: avoid repeating recurring StartJobRun API request values, enforcing certain values in StartJobRun API request.
    public struct JobTemplate: Swift.Sendable {
        /// The ARN of the job template.
        public var arn: Swift.String?
        /// The date and time when the job template was created.
        public var createdAt: Foundation.Date?
        /// The user who created the job template.
        public var createdBy: Swift.String?
        /// The error message in case the decryption of job template fails.
        public var decryptionError: Swift.String?
        /// The ID of the job template.
        public var id: Swift.String?
        /// The job template data which holds values of StartJobRun API request.
        /// This member is required.
        public var jobTemplateData: EMRcontainersClientTypes.JobTemplateData?
        /// The KMS key ARN used to encrypt the job template.
        public var kmsKeyArn: Swift.String?
        /// The name of the job template.
        public var name: Swift.String?
        /// The tags assigned to the job template.
        public var tags: [Swift.String: Swift.String]?

        public init(
            arn: Swift.String? = nil,
            createdAt: Foundation.Date? = nil,
            createdBy: Swift.String? = nil,
            decryptionError: Swift.String? = nil,
            id: Swift.String? = nil,
            jobTemplateData: EMRcontainersClientTypes.JobTemplateData? = nil,
            kmsKeyArn: Swift.String? = nil,
            name: Swift.String? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.arn = arn
            self.createdAt = createdAt
            self.createdBy = createdBy
            self.decryptionError = decryptionError
            self.id = id
            self.jobTemplateData = jobTemplateData
            self.kmsKeyArn = kmsKeyArn
            self.name = name
            self.tags = tags
        }
    }
}

public struct CreateJobTemplateInput: Swift.Sendable {
    /// The client token of the job template.
    /// This member is required.
    public var clientToken: Swift.String?
    /// The job template data which holds values of StartJobRun API request.
    /// This member is required.
    public var jobTemplateData: EMRcontainersClientTypes.JobTemplateData?
    /// The KMS key ARN used to encrypt the job template.
    public var kmsKeyArn: Swift.String?
    /// The specified name of the job template.
    /// This member is required.
    public var name: Swift.String?
    /// The tags that are associated with the job template.
    public var tags: [Swift.String: Swift.String]?

    public init(
        clientToken: Swift.String? = nil,
        jobTemplateData: EMRcontainersClientTypes.JobTemplateData? = nil,
        kmsKeyArn: Swift.String? = nil,
        name: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.clientToken = clientToken
        self.jobTemplateData = jobTemplateData
        self.kmsKeyArn = kmsKeyArn
        self.name = name
        self.tags = tags
    }
}

public struct DescribeJobRunOutput: Swift.Sendable {
    /// The output displays information about a job run.
    public var jobRun: EMRcontainersClientTypes.JobRun?

    public init(
        jobRun: EMRcontainersClientTypes.JobRun? = nil
    )
    {
        self.jobRun = jobRun
    }
}

public struct DescribeManagedEndpointOutput: Swift.Sendable {
    /// This output displays information about a managed endpoint.
    public var endpoint: EMRcontainersClientTypes.Endpoint?

    public init(
        endpoint: EMRcontainersClientTypes.Endpoint? = nil
    )
    {
        self.endpoint = endpoint
    }
}

public struct DescribeJobTemplateOutput: Swift.Sendable {
    /// This output displays information about the specified job template.
    public var jobTemplate: EMRcontainersClientTypes.JobTemplate?

    public init(
        jobTemplate: EMRcontainersClientTypes.JobTemplate? = nil
    )
    {
        self.jobTemplate = jobTemplate
    }
}

public struct ListJobRunsOutput: Swift.Sendable {
    /// This output lists information about the specified job runs.
    public var jobRuns: [EMRcontainersClientTypes.JobRun]?
    /// This output displays the token for the next set of job runs.
    public var nextToken: Swift.String?

    public init(
        jobRuns: [EMRcontainersClientTypes.JobRun]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.jobRuns = jobRuns
        self.nextToken = nextToken
    }
}

public struct ListManagedEndpointsOutput: Swift.Sendable {
    /// The managed endpoints to be listed.
    public var endpoints: [EMRcontainersClientTypes.Endpoint]?
    /// The token for the next set of endpoints to return.
    public var nextToken: Swift.String?

    public init(
        endpoints: [EMRcontainersClientTypes.Endpoint]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.endpoints = endpoints
        self.nextToken = nextToken
    }
}

public struct ListJobTemplatesOutput: Swift.Sendable {
    /// This output displays the token for the next set of job templates.
    public var nextToken: Swift.String?
    /// This output lists information about the specified job templates.
    public var templates: [EMRcontainersClientTypes.JobTemplate]?

    public init(
        nextToken: Swift.String? = nil,
        templates: [EMRcontainersClientTypes.JobTemplate]? = nil
    )
    {
        self.nextToken = nextToken
        self.templates = templates
    }
}

extension CancelJobRunInput {

    static func urlPathProvider(_ value: CancelJobRunInput) -> Swift.String? {
        guard let virtualClusterId = value.virtualClusterId else {
            return nil
        }
        guard let id = value.id else {
            return nil
        }
        return "/virtualclusters/\(virtualClusterId.urlPercentEncoding())/jobruns/\(id.urlPercentEncoding())"
    }
}

extension CreateJobTemplateInput {

    static func urlPathProvider(_ value: CreateJobTemplateInput) -> Swift.String? {
        return "/jobtemplates"
    }
}

extension CreateManagedEndpointInput {

    static func urlPathProvider(_ value: CreateManagedEndpointInput) -> Swift.String? {
        guard let virtualClusterId = value.virtualClusterId else {
            return nil
        }
        return "/virtualclusters/\(virtualClusterId.urlPercentEncoding())/endpoints"
    }
}

extension CreateSecurityConfigurationInput {

    static func urlPathProvider(_ value: CreateSecurityConfigurationInput) -> Swift.String? {
        return "/securityconfigurations"
    }
}

extension CreateVirtualClusterInput {

    static func urlPathProvider(_ value: CreateVirtualClusterInput) -> Swift.String? {
        return "/virtualclusters"
    }
}

extension DeleteJobTemplateInput {

    static func urlPathProvider(_ value: DeleteJobTemplateInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/jobtemplates/\(id.urlPercentEncoding())"
    }
}

extension DeleteManagedEndpointInput {

    static func urlPathProvider(_ value: DeleteManagedEndpointInput) -> Swift.String? {
        guard let virtualClusterId = value.virtualClusterId else {
            return nil
        }
        guard let id = value.id else {
            return nil
        }
        return "/virtualclusters/\(virtualClusterId.urlPercentEncoding())/endpoints/\(id.urlPercentEncoding())"
    }
}

extension DeleteVirtualClusterInput {

    static func urlPathProvider(_ value: DeleteVirtualClusterInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/virtualclusters/\(id.urlPercentEncoding())"
    }
}

extension DescribeJobRunInput {

    static func urlPathProvider(_ value: DescribeJobRunInput) -> Swift.String? {
        guard let virtualClusterId = value.virtualClusterId else {
            return nil
        }
        guard let id = value.id else {
            return nil
        }
        return "/virtualclusters/\(virtualClusterId.urlPercentEncoding())/jobruns/\(id.urlPercentEncoding())"
    }
}

extension DescribeJobTemplateInput {

    static func urlPathProvider(_ value: DescribeJobTemplateInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/jobtemplates/\(id.urlPercentEncoding())"
    }
}

extension DescribeManagedEndpointInput {

    static func urlPathProvider(_ value: DescribeManagedEndpointInput) -> Swift.String? {
        guard let virtualClusterId = value.virtualClusterId else {
            return nil
        }
        guard let id = value.id else {
            return nil
        }
        return "/virtualclusters/\(virtualClusterId.urlPercentEncoding())/endpoints/\(id.urlPercentEncoding())"
    }
}

extension DescribeSecurityConfigurationInput {

    static func urlPathProvider(_ value: DescribeSecurityConfigurationInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/securityconfigurations/\(id.urlPercentEncoding())"
    }
}

extension DescribeVirtualClusterInput {

    static func urlPathProvider(_ value: DescribeVirtualClusterInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/virtualclusters/\(id.urlPercentEncoding())"
    }
}

extension GetManagedEndpointSessionCredentialsInput {

    static func urlPathProvider(_ value: GetManagedEndpointSessionCredentialsInput) -> Swift.String? {
        guard let virtualClusterIdentifier = value.virtualClusterIdentifier else {
            return nil
        }
        guard let endpointIdentifier = value.endpointIdentifier else {
            return nil
        }
        return "/virtualclusters/\(virtualClusterIdentifier.urlPercentEncoding())/endpoints/\(endpointIdentifier.urlPercentEncoding())/credentials"
    }
}

extension ListJobRunsInput {

    static func urlPathProvider(_ value: ListJobRunsInput) -> Swift.String? {
        guard let virtualClusterId = value.virtualClusterId else {
            return nil
        }
        return "/virtualclusters/\(virtualClusterId.urlPercentEncoding())/jobruns"
    }
}

extension ListJobRunsInput {

    static func queryItemProvider(_ value: ListJobRunsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let name = value.name {
            let nameQueryItem = Smithy.URIQueryItem(name: "name".urlPercentEncoding(), value: Swift.String(name).urlPercentEncoding())
            items.append(nameQueryItem)
        }
        if let createdBefore = value.createdBefore {
            let createdBeforeQueryItem = Smithy.URIQueryItem(name: "createdBefore".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: createdBefore)).urlPercentEncoding())
            items.append(createdBeforeQueryItem)
        }
        if let createdAfter = value.createdAfter {
            let createdAfterQueryItem = Smithy.URIQueryItem(name: "createdAfter".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: createdAfter)).urlPercentEncoding())
            items.append(createdAfterQueryItem)
        }
        if let states = value.states {
            states.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "states".urlPercentEncoding(), value: Swift.String(queryItemValue.rawValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        return items
    }
}

extension ListJobTemplatesInput {

    static func urlPathProvider(_ value: ListJobTemplatesInput) -> Swift.String? {
        return "/jobtemplates"
    }
}

extension ListJobTemplatesInput {

    static func queryItemProvider(_ value: ListJobTemplatesInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let createdBefore = value.createdBefore {
            let createdBeforeQueryItem = Smithy.URIQueryItem(name: "createdBefore".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: createdBefore)).urlPercentEncoding())
            items.append(createdBeforeQueryItem)
        }
        if let createdAfter = value.createdAfter {
            let createdAfterQueryItem = Smithy.URIQueryItem(name: "createdAfter".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: createdAfter)).urlPercentEncoding())
            items.append(createdAfterQueryItem)
        }
        return items
    }
}

extension ListManagedEndpointsInput {

    static func urlPathProvider(_ value: ListManagedEndpointsInput) -> Swift.String? {
        guard let virtualClusterId = value.virtualClusterId else {
            return nil
        }
        return "/virtualclusters/\(virtualClusterId.urlPercentEncoding())/endpoints"
    }
}

extension ListManagedEndpointsInput {

    static func queryItemProvider(_ value: ListManagedEndpointsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let types = value.types {
            types.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "types".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let createdBefore = value.createdBefore {
            let createdBeforeQueryItem = Smithy.URIQueryItem(name: "createdBefore".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: createdBefore)).urlPercentEncoding())
            items.append(createdBeforeQueryItem)
        }
        if let createdAfter = value.createdAfter {
            let createdAfterQueryItem = Smithy.URIQueryItem(name: "createdAfter".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: createdAfter)).urlPercentEncoding())
            items.append(createdAfterQueryItem)
        }
        if let states = value.states {
            states.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "states".urlPercentEncoding(), value: Swift.String(queryItemValue.rawValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        return items
    }
}

extension ListSecurityConfigurationsInput {

    static func urlPathProvider(_ value: ListSecurityConfigurationsInput) -> Swift.String? {
        return "/securityconfigurations"
    }
}

extension ListSecurityConfigurationsInput {

    static func queryItemProvider(_ value: ListSecurityConfigurationsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let createdBefore = value.createdBefore {
            let createdBeforeQueryItem = Smithy.URIQueryItem(name: "createdBefore".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: createdBefore)).urlPercentEncoding())
            items.append(createdBeforeQueryItem)
        }
        if let createdAfter = value.createdAfter {
            let createdAfterQueryItem = Smithy.URIQueryItem(name: "createdAfter".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: createdAfter)).urlPercentEncoding())
            items.append(createdAfterQueryItem)
        }
        return items
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension ListVirtualClustersInput {

    static func urlPathProvider(_ value: ListVirtualClustersInput) -> Swift.String? {
        return "/virtualclusters"
    }
}

extension ListVirtualClustersInput {

    static func queryItemProvider(_ value: ListVirtualClustersInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let containerProviderId = value.containerProviderId {
            let containerProviderIdQueryItem = Smithy.URIQueryItem(name: "containerProviderId".urlPercentEncoding(), value: Swift.String(containerProviderId).urlPercentEncoding())
            items.append(containerProviderIdQueryItem)
        }
        if let eksAccessEntryIntegrated = value.eksAccessEntryIntegrated {
            let eksAccessEntryIntegratedQueryItem = Smithy.URIQueryItem(name: "eksAccessEntryIntegrated".urlPercentEncoding(), value: Swift.String(eksAccessEntryIntegrated).urlPercentEncoding())
            items.append(eksAccessEntryIntegratedQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let containerProviderType = value.containerProviderType {
            let containerProviderTypeQueryItem = Smithy.URIQueryItem(name: "containerProviderType".urlPercentEncoding(), value: Swift.String(containerProviderType.rawValue).urlPercentEncoding())
            items.append(containerProviderTypeQueryItem)
        }
        if let createdBefore = value.createdBefore {
            let createdBeforeQueryItem = Smithy.URIQueryItem(name: "createdBefore".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: createdBefore)).urlPercentEncoding())
            items.append(createdBeforeQueryItem)
        }
        if let createdAfter = value.createdAfter {
            let createdAfterQueryItem = Smithy.URIQueryItem(name: "createdAfter".urlPercentEncoding(), value: Swift.String(SmithyTimestamps.TimestampFormatter(format: .dateTime).string(from: createdAfter)).urlPercentEncoding())
            items.append(createdAfterQueryItem)
        }
        if let states = value.states {
            states.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "states".urlPercentEncoding(), value: Swift.String(queryItemValue.rawValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        return items
    }
}

extension StartJobRunInput {

    static func urlPathProvider(_ value: StartJobRunInput) -> Swift.String? {
        guard let virtualClusterId = value.virtualClusterId else {
            return nil
        }
        return "/virtualclusters/\(virtualClusterId.urlPercentEncoding())/jobruns"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension CreateJobTemplateInput {

    static func write(value: CreateJobTemplateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["clientToken"].write(value.clientToken)
        try writer["jobTemplateData"].write(value.jobTemplateData, with: EMRcontainersClientTypes.JobTemplateData.write(value:to:))
        try writer["kmsKeyArn"].write(value.kmsKeyArn)
        try writer["name"].write(value.name)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CreateManagedEndpointInput {

    static func write(value: CreateManagedEndpointInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["certificateArn"].write(value.certificateArn)
        try writer["clientToken"].write(value.clientToken)
        try writer["configurationOverrides"].write(value.configurationOverrides, with: EMRcontainersClientTypes.ConfigurationOverrides.write(value:to:))
        try writer["executionRoleArn"].write(value.executionRoleArn)
        try writer["name"].write(value.name)
        try writer["releaseLabel"].write(value.releaseLabel)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["type"].write(value.type)
    }
}

extension CreateSecurityConfigurationInput {

    static func write(value: CreateSecurityConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["clientToken"].write(value.clientToken)
        try writer["name"].write(value.name)
        try writer["securityConfigurationData"].write(value.securityConfigurationData, with: EMRcontainersClientTypes.SecurityConfigurationData.write(value:to:))
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CreateVirtualClusterInput {

    static func write(value: CreateVirtualClusterInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["clientToken"].write(value.clientToken)
        try writer["containerProvider"].write(value.containerProvider, with: EMRcontainersClientTypes.ContainerProvider.write(value:to:))
        try writer["name"].write(value.name)
        try writer["securityConfigurationId"].write(value.securityConfigurationId)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension GetManagedEndpointSessionCredentialsInput {

    static func write(value: GetManagedEndpointSessionCredentialsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["clientToken"].write(value.clientToken)
        try writer["credentialType"].write(value.credentialType)
        try writer["durationInSeconds"].write(value.durationInSeconds)
        try writer["executionRoleArn"].write(value.executionRoleArn)
        try writer["logContext"].write(value.logContext)
    }
}

extension StartJobRunInput {

    static func write(value: StartJobRunInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["clientToken"].write(value.clientToken)
        try writer["configurationOverrides"].write(value.configurationOverrides, with: EMRcontainersClientTypes.ConfigurationOverrides.write(value:to:))
        try writer["executionRoleArn"].write(value.executionRoleArn)
        try writer["jobDriver"].write(value.jobDriver, with: EMRcontainersClientTypes.JobDriver.write(value:to:))
        try writer["jobTemplateId"].write(value.jobTemplateId)
        try writer["jobTemplateParameters"].writeMap(value.jobTemplateParameters, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["name"].write(value.name)
        try writer["releaseLabel"].write(value.releaseLabel)
        try writer["retryPolicyConfiguration"].write(value.retryPolicyConfiguration, with: EMRcontainersClientTypes.RetryPolicyConfiguration.write(value:to:))
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CancelJobRunOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CancelJobRunOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CancelJobRunOutput()
        value.id = try reader["id"].readIfPresent()
        value.virtualClusterId = try reader["virtualClusterId"].readIfPresent()
        return value
    }
}

extension CreateJobTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateJobTemplateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateJobTemplateOutput()
        value.arn = try reader["arn"].readIfPresent()
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.id = try reader["id"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        return value
    }
}

extension CreateManagedEndpointOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateManagedEndpointOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateManagedEndpointOutput()
        value.arn = try reader["arn"].readIfPresent()
        value.id = try reader["id"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.virtualClusterId = try reader["virtualClusterId"].readIfPresent()
        return value
    }
}

extension CreateSecurityConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateSecurityConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateSecurityConfigurationOutput()
        value.arn = try reader["arn"].readIfPresent()
        value.id = try reader["id"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        return value
    }
}

extension CreateVirtualClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateVirtualClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateVirtualClusterOutput()
        value.arn = try reader["arn"].readIfPresent()
        value.id = try reader["id"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        return value
    }
}

extension DeleteJobTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteJobTemplateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteJobTemplateOutput()
        value.id = try reader["id"].readIfPresent()
        return value
    }
}

extension DeleteManagedEndpointOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteManagedEndpointOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteManagedEndpointOutput()
        value.id = try reader["id"].readIfPresent()
        value.virtualClusterId = try reader["virtualClusterId"].readIfPresent()
        return value
    }
}

extension DeleteVirtualClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteVirtualClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteVirtualClusterOutput()
        value.id = try reader["id"].readIfPresent()
        return value
    }
}

extension DescribeJobRunOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeJobRunOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeJobRunOutput()
        value.jobRun = try reader["jobRun"].readIfPresent(with: EMRcontainersClientTypes.JobRun.read(from:))
        return value
    }
}

extension DescribeJobTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeJobTemplateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeJobTemplateOutput()
        value.jobTemplate = try reader["jobTemplate"].readIfPresent(with: EMRcontainersClientTypes.JobTemplate.read(from:))
        return value
    }
}

extension DescribeManagedEndpointOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeManagedEndpointOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeManagedEndpointOutput()
        value.endpoint = try reader["endpoint"].readIfPresent(with: EMRcontainersClientTypes.Endpoint.read(from:))
        return value
    }
}

extension DescribeSecurityConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeSecurityConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeSecurityConfigurationOutput()
        value.securityConfiguration = try reader["securityConfiguration"].readIfPresent(with: EMRcontainersClientTypes.SecurityConfiguration.read(from:))
        return value
    }
}

extension DescribeVirtualClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeVirtualClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeVirtualClusterOutput()
        value.virtualCluster = try reader["virtualCluster"].readIfPresent(with: EMRcontainersClientTypes.VirtualCluster.read(from:))
        return value
    }
}

extension GetManagedEndpointSessionCredentialsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetManagedEndpointSessionCredentialsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetManagedEndpointSessionCredentialsOutput()
        value.credentials = try reader["credentials"].readIfPresent(with: EMRcontainersClientTypes.Credentials.read(from:))
        value.expiresAt = try reader["expiresAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.id = try reader["id"].readIfPresent()
        return value
    }
}

extension ListJobRunsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListJobRunsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListJobRunsOutput()
        value.jobRuns = try reader["jobRuns"].readListIfPresent(memberReadingClosure: EMRcontainersClientTypes.JobRun.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListJobTemplatesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListJobTemplatesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListJobTemplatesOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.templates = try reader["templates"].readListIfPresent(memberReadingClosure: EMRcontainersClientTypes.JobTemplate.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListManagedEndpointsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListManagedEndpointsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListManagedEndpointsOutput()
        value.endpoints = try reader["endpoints"].readListIfPresent(memberReadingClosure: EMRcontainersClientTypes.Endpoint.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListSecurityConfigurationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListSecurityConfigurationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListSecurityConfigurationsOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.securityConfigurations = try reader["securityConfigurations"].readListIfPresent(memberReadingClosure: EMRcontainersClientTypes.SecurityConfiguration.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension ListVirtualClustersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListVirtualClustersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListVirtualClustersOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.virtualClusters = try reader["virtualClusters"].readListIfPresent(memberReadingClosure: EMRcontainersClientTypes.VirtualCluster.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension StartJobRunOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartJobRunOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartJobRunOutput()
        value.arn = try reader["arn"].readIfPresent()
        value.id = try reader["id"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.virtualClusterId = try reader["virtualClusterId"].readIfPresent()
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

enum CancelJobRunOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateJobTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateManagedEndpointOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateSecurityConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateVirtualClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "EKSRequestThrottledException": return try EKSRequestThrottledException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteJobTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteManagedEndpointOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteVirtualClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeJobRunOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeJobTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeManagedEndpointOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeSecurityConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeVirtualClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetManagedEndpointSessionCredentialsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "RequestThrottledException": return try RequestThrottledException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListJobRunsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListJobTemplatesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListManagedEndpointsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListSecurityConfigurationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListVirtualClustersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartJobRunOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension EKSRequestThrottledException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> EKSRequestThrottledException {
        let reader = baseError.errorBodyReader
        var value = EKSRequestThrottledException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension RequestThrottledException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> RequestThrottledException {
        let reader = baseError.errorBodyReader
        var value = RequestThrottledException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension EMRcontainersClientTypes.JobRun {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.JobRun {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.JobRun()
        value.id = try reader["id"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.virtualClusterId = try reader["virtualClusterId"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.state = try reader["state"].readIfPresent()
        value.clientToken = try reader["clientToken"].readIfPresent()
        value.executionRoleArn = try reader["executionRoleArn"].readIfPresent()
        value.releaseLabel = try reader["releaseLabel"].readIfPresent()
        value.configurationOverrides = try reader["configurationOverrides"].readIfPresent(with: EMRcontainersClientTypes.ConfigurationOverrides.read(from:))
        value.jobDriver = try reader["jobDriver"].readIfPresent(with: EMRcontainersClientTypes.JobDriver.read(from:))
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.createdBy = try reader["createdBy"].readIfPresent()
        value.finishedAt = try reader["finishedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.stateDetails = try reader["stateDetails"].readIfPresent()
        value.failureReason = try reader["failureReason"].readIfPresent()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.retryPolicyConfiguration = try reader["retryPolicyConfiguration"].readIfPresent(with: EMRcontainersClientTypes.RetryPolicyConfiguration.read(from:))
        value.retryPolicyExecution = try reader["retryPolicyExecution"].readIfPresent(with: EMRcontainersClientTypes.RetryPolicyExecution.read(from:))
        return value
    }
}

extension EMRcontainersClientTypes.RetryPolicyExecution {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.RetryPolicyExecution {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.RetryPolicyExecution()
        value.currentAttemptCount = try reader["currentAttemptCount"].readIfPresent() ?? 0
        return value
    }
}

extension EMRcontainersClientTypes.RetryPolicyConfiguration {

    static func write(value: EMRcontainersClientTypes.RetryPolicyConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxAttempts"].write(value.maxAttempts)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.RetryPolicyConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.RetryPolicyConfiguration()
        value.maxAttempts = try reader["maxAttempts"].readIfPresent() ?? 0
        return value
    }
}

extension EMRcontainersClientTypes.JobDriver {

    static func write(value: EMRcontainersClientTypes.JobDriver?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sparkSqlJobDriver"].write(value.sparkSqlJobDriver, with: EMRcontainersClientTypes.SparkSqlJobDriver.write(value:to:))
        try writer["sparkSubmitJobDriver"].write(value.sparkSubmitJobDriver, with: EMRcontainersClientTypes.SparkSubmitJobDriver.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.JobDriver {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.JobDriver()
        value.sparkSubmitJobDriver = try reader["sparkSubmitJobDriver"].readIfPresent(with: EMRcontainersClientTypes.SparkSubmitJobDriver.read(from:))
        value.sparkSqlJobDriver = try reader["sparkSqlJobDriver"].readIfPresent(with: EMRcontainersClientTypes.SparkSqlJobDriver.read(from:))
        return value
    }
}

extension EMRcontainersClientTypes.SparkSqlJobDriver {

    static func write(value: EMRcontainersClientTypes.SparkSqlJobDriver?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["entryPoint"].write(value.entryPoint)
        try writer["sparkSqlParameters"].write(value.sparkSqlParameters)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.SparkSqlJobDriver {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.SparkSqlJobDriver()
        value.entryPoint = try reader["entryPoint"].readIfPresent()
        value.sparkSqlParameters = try reader["sparkSqlParameters"].readIfPresent()
        return value
    }
}

extension EMRcontainersClientTypes.SparkSubmitJobDriver {

    static func write(value: EMRcontainersClientTypes.SparkSubmitJobDriver?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["entryPoint"].write(value.entryPoint)
        try writer["entryPointArguments"].writeList(value.entryPointArguments, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["sparkSubmitParameters"].write(value.sparkSubmitParameters)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.SparkSubmitJobDriver {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.SparkSubmitJobDriver()
        value.entryPoint = try reader["entryPoint"].readIfPresent() ?? ""
        value.entryPointArguments = try reader["entryPointArguments"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.sparkSubmitParameters = try reader["sparkSubmitParameters"].readIfPresent()
        return value
    }
}

extension EMRcontainersClientTypes.ConfigurationOverrides {

    static func write(value: EMRcontainersClientTypes.ConfigurationOverrides?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["applicationConfiguration"].writeList(value.applicationConfiguration, memberWritingClosure: EMRcontainersClientTypes.Configuration.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["monitoringConfiguration"].write(value.monitoringConfiguration, with: EMRcontainersClientTypes.MonitoringConfiguration.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.ConfigurationOverrides {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.ConfigurationOverrides()
        value.applicationConfiguration = try reader["applicationConfiguration"].readListIfPresent(memberReadingClosure: EMRcontainersClientTypes.Configuration.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.monitoringConfiguration = try reader["monitoringConfiguration"].readIfPresent(with: EMRcontainersClientTypes.MonitoringConfiguration.read(from:))
        return value
    }
}

extension EMRcontainersClientTypes.MonitoringConfiguration {

    static func write(value: EMRcontainersClientTypes.MonitoringConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["cloudWatchMonitoringConfiguration"].write(value.cloudWatchMonitoringConfiguration, with: EMRcontainersClientTypes.CloudWatchMonitoringConfiguration.write(value:to:))
        try writer["containerLogRotationConfiguration"].write(value.containerLogRotationConfiguration, with: EMRcontainersClientTypes.ContainerLogRotationConfiguration.write(value:to:))
        try writer["persistentAppUI"].write(value.persistentAppUI)
        try writer["s3MonitoringConfiguration"].write(value.s3MonitoringConfiguration, with: EMRcontainersClientTypes.S3MonitoringConfiguration.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.MonitoringConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.MonitoringConfiguration()
        value.persistentAppUI = try reader["persistentAppUI"].readIfPresent()
        value.cloudWatchMonitoringConfiguration = try reader["cloudWatchMonitoringConfiguration"].readIfPresent(with: EMRcontainersClientTypes.CloudWatchMonitoringConfiguration.read(from:))
        value.s3MonitoringConfiguration = try reader["s3MonitoringConfiguration"].readIfPresent(with: EMRcontainersClientTypes.S3MonitoringConfiguration.read(from:))
        value.containerLogRotationConfiguration = try reader["containerLogRotationConfiguration"].readIfPresent(with: EMRcontainersClientTypes.ContainerLogRotationConfiguration.read(from:))
        return value
    }
}

extension EMRcontainersClientTypes.ContainerLogRotationConfiguration {

    static func write(value: EMRcontainersClientTypes.ContainerLogRotationConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxFilesToKeep"].write(value.maxFilesToKeep)
        try writer["rotationSize"].write(value.rotationSize)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.ContainerLogRotationConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.ContainerLogRotationConfiguration()
        value.rotationSize = try reader["rotationSize"].readIfPresent() ?? ""
        value.maxFilesToKeep = try reader["maxFilesToKeep"].readIfPresent() ?? 0
        return value
    }
}

extension EMRcontainersClientTypes.S3MonitoringConfiguration {

    static func write(value: EMRcontainersClientTypes.S3MonitoringConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["logUri"].write(value.logUri)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.S3MonitoringConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.S3MonitoringConfiguration()
        value.logUri = try reader["logUri"].readIfPresent() ?? ""
        return value
    }
}

extension EMRcontainersClientTypes.CloudWatchMonitoringConfiguration {

    static func write(value: EMRcontainersClientTypes.CloudWatchMonitoringConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["logGroupName"].write(value.logGroupName)
        try writer["logStreamNamePrefix"].write(value.logStreamNamePrefix)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.CloudWatchMonitoringConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.CloudWatchMonitoringConfiguration()
        value.logGroupName = try reader["logGroupName"].readIfPresent() ?? ""
        value.logStreamNamePrefix = try reader["logStreamNamePrefix"].readIfPresent()
        return value
    }
}

extension EMRcontainersClientTypes.Configuration {

    static func write(value: EMRcontainersClientTypes.Configuration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["classification"].write(value.classification)
        try writer["configurations"].writeList(value.configurations, memberWritingClosure: EMRcontainersClientTypes.Configuration.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["properties"].writeMap(value.properties, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.Configuration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.Configuration()
        value.classification = try reader["classification"].readIfPresent() ?? ""
        value.properties = try reader["properties"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.configurations = try reader["configurations"].readListIfPresent(memberReadingClosure: EMRcontainersClientTypes.Configuration.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension EMRcontainersClientTypes.JobTemplate {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.JobTemplate {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.JobTemplate()
        value.name = try reader["name"].readIfPresent()
        value.id = try reader["id"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.createdBy = try reader["createdBy"].readIfPresent()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.jobTemplateData = try reader["jobTemplateData"].readIfPresent(with: EMRcontainersClientTypes.JobTemplateData.read(from:))
        value.kmsKeyArn = try reader["kmsKeyArn"].readIfPresent()
        value.decryptionError = try reader["decryptionError"].readIfPresent()
        return value
    }
}

extension EMRcontainersClientTypes.JobTemplateData {

    static func write(value: EMRcontainersClientTypes.JobTemplateData?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["configurationOverrides"].write(value.configurationOverrides, with: EMRcontainersClientTypes.ParametricConfigurationOverrides.write(value:to:))
        try writer["executionRoleArn"].write(value.executionRoleArn)
        try writer["jobDriver"].write(value.jobDriver, with: EMRcontainersClientTypes.JobDriver.write(value:to:))
        try writer["jobTags"].writeMap(value.jobTags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["parameterConfiguration"].writeMap(value.parameterConfiguration, valueWritingClosure: EMRcontainersClientTypes.TemplateParameterConfiguration.write(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["releaseLabel"].write(value.releaseLabel)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.JobTemplateData {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.JobTemplateData()
        value.executionRoleArn = try reader["executionRoleArn"].readIfPresent() ?? ""
        value.releaseLabel = try reader["releaseLabel"].readIfPresent() ?? ""
        value.configurationOverrides = try reader["configurationOverrides"].readIfPresent(with: EMRcontainersClientTypes.ParametricConfigurationOverrides.read(from:))
        value.jobDriver = try reader["jobDriver"].readIfPresent(with: EMRcontainersClientTypes.JobDriver.read(from:))
        value.parameterConfiguration = try reader["parameterConfiguration"].readMapIfPresent(valueReadingClosure: EMRcontainersClientTypes.TemplateParameterConfiguration.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.jobTags = try reader["jobTags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension EMRcontainersClientTypes.TemplateParameterConfiguration {

    static func write(value: EMRcontainersClientTypes.TemplateParameterConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["defaultValue"].write(value.defaultValue)
        try writer["type"].write(value.type)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.TemplateParameterConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.TemplateParameterConfiguration()
        value.type = try reader["type"].readIfPresent()
        value.defaultValue = try reader["defaultValue"].readIfPresent()
        return value
    }
}

extension EMRcontainersClientTypes.ParametricConfigurationOverrides {

    static func write(value: EMRcontainersClientTypes.ParametricConfigurationOverrides?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["applicationConfiguration"].writeList(value.applicationConfiguration, memberWritingClosure: EMRcontainersClientTypes.Configuration.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["monitoringConfiguration"].write(value.monitoringConfiguration, with: EMRcontainersClientTypes.ParametricMonitoringConfiguration.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.ParametricConfigurationOverrides {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.ParametricConfigurationOverrides()
        value.applicationConfiguration = try reader["applicationConfiguration"].readListIfPresent(memberReadingClosure: EMRcontainersClientTypes.Configuration.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.monitoringConfiguration = try reader["monitoringConfiguration"].readIfPresent(with: EMRcontainersClientTypes.ParametricMonitoringConfiguration.read(from:))
        return value
    }
}

extension EMRcontainersClientTypes.ParametricMonitoringConfiguration {

    static func write(value: EMRcontainersClientTypes.ParametricMonitoringConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["cloudWatchMonitoringConfiguration"].write(value.cloudWatchMonitoringConfiguration, with: EMRcontainersClientTypes.ParametricCloudWatchMonitoringConfiguration.write(value:to:))
        try writer["persistentAppUI"].write(value.persistentAppUI)
        try writer["s3MonitoringConfiguration"].write(value.s3MonitoringConfiguration, with: EMRcontainersClientTypes.ParametricS3MonitoringConfiguration.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.ParametricMonitoringConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.ParametricMonitoringConfiguration()
        value.persistentAppUI = try reader["persistentAppUI"].readIfPresent()
        value.cloudWatchMonitoringConfiguration = try reader["cloudWatchMonitoringConfiguration"].readIfPresent(with: EMRcontainersClientTypes.ParametricCloudWatchMonitoringConfiguration.read(from:))
        value.s3MonitoringConfiguration = try reader["s3MonitoringConfiguration"].readIfPresent(with: EMRcontainersClientTypes.ParametricS3MonitoringConfiguration.read(from:))
        return value
    }
}

extension EMRcontainersClientTypes.ParametricS3MonitoringConfiguration {

    static func write(value: EMRcontainersClientTypes.ParametricS3MonitoringConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["logUri"].write(value.logUri)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.ParametricS3MonitoringConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.ParametricS3MonitoringConfiguration()
        value.logUri = try reader["logUri"].readIfPresent()
        return value
    }
}

extension EMRcontainersClientTypes.ParametricCloudWatchMonitoringConfiguration {

    static func write(value: EMRcontainersClientTypes.ParametricCloudWatchMonitoringConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["logGroupName"].write(value.logGroupName)
        try writer["logStreamNamePrefix"].write(value.logStreamNamePrefix)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.ParametricCloudWatchMonitoringConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.ParametricCloudWatchMonitoringConfiguration()
        value.logGroupName = try reader["logGroupName"].readIfPresent()
        value.logStreamNamePrefix = try reader["logStreamNamePrefix"].readIfPresent()
        return value
    }
}

extension EMRcontainersClientTypes.Endpoint {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.Endpoint {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.Endpoint()
        value.id = try reader["id"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.virtualClusterId = try reader["virtualClusterId"].readIfPresent()
        value.type = try reader["type"].readIfPresent()
        value.state = try reader["state"].readIfPresent()
        value.releaseLabel = try reader["releaseLabel"].readIfPresent()
        value.executionRoleArn = try reader["executionRoleArn"].readIfPresent()
        value.certificateArn = try reader["certificateArn"].readIfPresent()
        value.certificateAuthority = try reader["certificateAuthority"].readIfPresent(with: EMRcontainersClientTypes.Certificate.read(from:))
        value.configurationOverrides = try reader["configurationOverrides"].readIfPresent(with: EMRcontainersClientTypes.ConfigurationOverrides.read(from:))
        value.serverUrl = try reader["serverUrl"].readIfPresent()
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.securityGroup = try reader["securityGroup"].readIfPresent()
        value.subnetIds = try reader["subnetIds"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.stateDetails = try reader["stateDetails"].readIfPresent()
        value.failureReason = try reader["failureReason"].readIfPresent()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension EMRcontainersClientTypes.Certificate {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.Certificate {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.Certificate()
        value.certificateArn = try reader["certificateArn"].readIfPresent()
        value.certificateData = try reader["certificateData"].readIfPresent()
        return value
    }
}

extension EMRcontainersClientTypes.SecurityConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.SecurityConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.SecurityConfiguration()
        value.id = try reader["id"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.createdBy = try reader["createdBy"].readIfPresent()
        value.securityConfigurationData = try reader["securityConfigurationData"].readIfPresent(with: EMRcontainersClientTypes.SecurityConfigurationData.read(from:))
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension EMRcontainersClientTypes.SecurityConfigurationData {

    static func write(value: EMRcontainersClientTypes.SecurityConfigurationData?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["authorizationConfiguration"].write(value.authorizationConfiguration, with: EMRcontainersClientTypes.AuthorizationConfiguration.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.SecurityConfigurationData {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.SecurityConfigurationData()
        value.authorizationConfiguration = try reader["authorizationConfiguration"].readIfPresent(with: EMRcontainersClientTypes.AuthorizationConfiguration.read(from:))
        return value
    }
}

extension EMRcontainersClientTypes.AuthorizationConfiguration {

    static func write(value: EMRcontainersClientTypes.AuthorizationConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["encryptionConfiguration"].write(value.encryptionConfiguration, with: EMRcontainersClientTypes.EncryptionConfiguration.write(value:to:))
        try writer["lakeFormationConfiguration"].write(value.lakeFormationConfiguration, with: EMRcontainersClientTypes.LakeFormationConfiguration.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.AuthorizationConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.AuthorizationConfiguration()
        value.lakeFormationConfiguration = try reader["lakeFormationConfiguration"].readIfPresent(with: EMRcontainersClientTypes.LakeFormationConfiguration.read(from:))
        value.encryptionConfiguration = try reader["encryptionConfiguration"].readIfPresent(with: EMRcontainersClientTypes.EncryptionConfiguration.read(from:))
        return value
    }
}

extension EMRcontainersClientTypes.EncryptionConfiguration {

    static func write(value: EMRcontainersClientTypes.EncryptionConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["inTransitEncryptionConfiguration"].write(value.inTransitEncryptionConfiguration, with: EMRcontainersClientTypes.InTransitEncryptionConfiguration.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.EncryptionConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.EncryptionConfiguration()
        value.inTransitEncryptionConfiguration = try reader["inTransitEncryptionConfiguration"].readIfPresent(with: EMRcontainersClientTypes.InTransitEncryptionConfiguration.read(from:))
        return value
    }
}

extension EMRcontainersClientTypes.InTransitEncryptionConfiguration {

    static func write(value: EMRcontainersClientTypes.InTransitEncryptionConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tlsCertificateConfiguration"].write(value.tlsCertificateConfiguration, with: EMRcontainersClientTypes.TLSCertificateConfiguration.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.InTransitEncryptionConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.InTransitEncryptionConfiguration()
        value.tlsCertificateConfiguration = try reader["tlsCertificateConfiguration"].readIfPresent(with: EMRcontainersClientTypes.TLSCertificateConfiguration.read(from:))
        return value
    }
}

extension EMRcontainersClientTypes.TLSCertificateConfiguration {

    static func write(value: EMRcontainersClientTypes.TLSCertificateConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["certificateProviderType"].write(value.certificateProviderType)
        try writer["privateCertificateSecretArn"].write(value.privateCertificateSecretArn)
        try writer["publicCertificateSecretArn"].write(value.publicCertificateSecretArn)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.TLSCertificateConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.TLSCertificateConfiguration()
        value.certificateProviderType = try reader["certificateProviderType"].readIfPresent()
        value.publicCertificateSecretArn = try reader["publicCertificateSecretArn"].readIfPresent()
        value.privateCertificateSecretArn = try reader["privateCertificateSecretArn"].readIfPresent()
        return value
    }
}

extension EMRcontainersClientTypes.LakeFormationConfiguration {

    static func write(value: EMRcontainersClientTypes.LakeFormationConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["authorizedSessionTagValue"].write(value.authorizedSessionTagValue)
        try writer["queryEngineRoleArn"].write(value.queryEngineRoleArn)
        try writer["secureNamespaceInfo"].write(value.secureNamespaceInfo, with: EMRcontainersClientTypes.SecureNamespaceInfo.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.LakeFormationConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.LakeFormationConfiguration()
        value.authorizedSessionTagValue = try reader["authorizedSessionTagValue"].readIfPresent()
        value.secureNamespaceInfo = try reader["secureNamespaceInfo"].readIfPresent(with: EMRcontainersClientTypes.SecureNamespaceInfo.read(from:))
        value.queryEngineRoleArn = try reader["queryEngineRoleArn"].readIfPresent()
        return value
    }
}

extension EMRcontainersClientTypes.SecureNamespaceInfo {

    static func write(value: EMRcontainersClientTypes.SecureNamespaceInfo?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["clusterId"].write(value.clusterId)
        try writer["namespace"].write(value.namespace)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.SecureNamespaceInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.SecureNamespaceInfo()
        value.clusterId = try reader["clusterId"].readIfPresent()
        value.namespace = try reader["namespace"].readIfPresent()
        return value
    }
}

extension EMRcontainersClientTypes.VirtualCluster {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.VirtualCluster {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.VirtualCluster()
        value.id = try reader["id"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.state = try reader["state"].readIfPresent()
        value.containerProvider = try reader["containerProvider"].readIfPresent(with: EMRcontainersClientTypes.ContainerProvider.read(from:))
        value.createdAt = try reader["createdAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.securityConfigurationId = try reader["securityConfigurationId"].readIfPresent()
        return value
    }
}

extension EMRcontainersClientTypes.ContainerProvider {

    static func write(value: EMRcontainersClientTypes.ContainerProvider?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["id"].write(value.id)
        try writer["info"].write(value.info, with: EMRcontainersClientTypes.ContainerInfo.write(value:to:))
        try writer["type"].write(value.type)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.ContainerProvider {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.ContainerProvider()
        value.type = try reader["type"].readIfPresent() ?? .sdkUnknown("")
        value.id = try reader["id"].readIfPresent() ?? ""
        value.info = try reader["info"].readIfPresent(with: EMRcontainersClientTypes.ContainerInfo.read(from:))
        return value
    }
}

extension EMRcontainersClientTypes.ContainerInfo {

    static func write(value: EMRcontainersClientTypes.ContainerInfo?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        switch value {
            case let .eksinfo(eksinfo):
                try writer["eksInfo"].write(eksinfo, with: EMRcontainersClientTypes.EksInfo.write(value:to:))
            case let .sdkUnknown(sdkUnknown):
                try writer["sdkUnknown"].write(sdkUnknown)
        }
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.ContainerInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "eksInfo":
                return .eksinfo(try reader["eksInfo"].read(with: EMRcontainersClientTypes.EksInfo.read(from:)))
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension EMRcontainersClientTypes.EksInfo {

    static func write(value: EMRcontainersClientTypes.EksInfo?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["namespace"].write(value.namespace)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.EksInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = EMRcontainersClientTypes.EksInfo()
        value.namespace = try reader["namespace"].readIfPresent()
        return value
    }
}

extension EMRcontainersClientTypes.Credentials {

    static func read(from reader: SmithyJSON.Reader) throws -> EMRcontainersClientTypes.Credentials {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "token":
                return .token(try reader["token"].read())
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

public enum EMRcontainersClientTypes {}
