//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.<PERSON>y<PERSON>eader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
@_spi(SmithyReadWrite) import struct SmithyReadWrite.ReadingClosureBox
@_spi(SmithyReadWrite) import struct SmithyReadWrite.WritingClosureBox

/// You do not have sufficient access to perform this action.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CodeGuruReviewerClientTypes {

    public enum AnalysisType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case codeQuality
        case security
        case sdkUnknown(Swift.String)

        public static var allCases: [AnalysisType] {
            return [
                .codeQuality,
                .security
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .codeQuality: return "CodeQuality"
            case .security: return "Security"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The requested operation would cause a conflict with the current state of a service resource associated with the request. Resolve the conflict before retrying this request.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The server encountered an internal error and is unable to complete the request.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request was denied due to request throttling.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The input fails to satisfy the specified constraints.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CodeGuruReviewerClientTypes {

    public enum EncryptionOption: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case aocmk
        case cmcmk
        case sdkUnknown(Swift.String)

        public static var allCases: [EncryptionOption] {
            return [
                .aocmk,
                .cmcmk
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .aocmk: return "AWS_OWNED_CMK"
            case .cmcmk: return "CUSTOMER_MANAGED_CMK"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// An object that contains:
    ///
    /// * The encryption option for a repository association. It is either owned by Amazon Web Services Key Management Service (KMS) (AWS_OWNED_CMK) or customer managed (CUSTOMER_MANAGED_CMK).
    ///
    /// * The ID of the Amazon Web Services KMS key that is associated with a repository association.
    public struct KMSKeyDetails: Swift.Sendable {
        /// The encryption option for a repository association. It is either owned by Amazon Web Services Key Management Service (KMS) (AWS_OWNED_CMK) or customer managed (CUSTOMER_MANAGED_CMK).
        public var encryptionOption: CodeGuruReviewerClientTypes.EncryptionOption?
        /// The ID of the Amazon Web Services KMS key that is associated with a repository association.
        public var kmsKeyId: Swift.String?

        public init(
            encryptionOption: CodeGuruReviewerClientTypes.EncryptionOption? = nil,
            kmsKeyId: Swift.String? = nil
        )
        {
            self.encryptionOption = encryptionOption
            self.kmsKeyId = kmsKeyId
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Information about a third-party source repository connected to CodeGuru Reviewer.
    public struct ThirdPartySourceRepository: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of an Amazon Web Services CodeStar Connections connection. Its format is arn:aws:codestar-connections:region-id:aws-account_id:connection/connection-id. For more information, see [Connection](https://docs.aws.amazon.com/codestar-connections/latest/APIReference/API_Connection.html) in the Amazon Web Services CodeStar Connections API Reference.
        /// This member is required.
        public var connectionArn: Swift.String?
        /// The name of the third party source repository.
        /// This member is required.
        public var name: Swift.String?
        /// The owner of the repository. For a GitHub, GitHub Enterprise, or Bitbucket repository, this is the username for the account that owns the repository. For an S3 repository, this can be the username or Amazon Web Services account ID
        /// This member is required.
        public var owner: Swift.String?

        public init(
            connectionArn: Swift.String? = nil,
            name: Swift.String? = nil,
            owner: Swift.String? = nil
        )
        {
            self.connectionArn = connectionArn
            self.name = name
            self.owner = owner
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Information about an Amazon Web Services CodeCommit repository. The CodeCommit repository must be in the same Amazon Web Services Region and Amazon Web Services account where its CodeGuru Reviewer code reviews are configured.
    public struct CodeCommitRepository: Swift.Sendable {
        /// The name of the Amazon Web Services CodeCommit repository. For more information, see [repositoryName](https://docs.aws.amazon.com/codecommit/latest/APIReference/API_GetRepository.html#CodeCommit-GetRepository-request-repositoryName) in the Amazon Web Services CodeCommit API Reference.
        /// This member is required.
        public var name: Swift.String?

        public init(
            name: Swift.String? = nil
        )
        {
            self.name = name
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Information about a repository in an S3 bucket.
    public struct S3Repository: Swift.Sendable {
        /// The name of the S3 bucket used for associating a new S3 repository. It must begin with codeguru-reviewer-.
        /// This member is required.
        public var bucketName: Swift.String?
        /// The name of the repository in the S3 bucket.
        /// This member is required.
        public var name: Swift.String?

        public init(
            bucketName: Swift.String? = nil,
            name: Swift.String? = nil
        )
        {
            self.bucketName = bucketName
            self.name = name
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Information about an associated Amazon Web Services CodeCommit repository or an associated repository that is managed by Amazon Web Services CodeStar Connections (for example, Bitbucket). This Repository object is not used if your source code is in an associated GitHub repository.
    public struct Repository: Swift.Sendable {
        /// Information about a Bitbucket repository.
        public var bitbucket: CodeGuruReviewerClientTypes.ThirdPartySourceRepository?
        /// Information about an Amazon Web Services CodeCommit repository.
        public var codeCommit: CodeGuruReviewerClientTypes.CodeCommitRepository?
        /// Information about a GitHub Enterprise Server repository.
        public var gitHubEnterpriseServer: CodeGuruReviewerClientTypes.ThirdPartySourceRepository?
        /// Information about a repository in an S3 bucket.
        public var s3Bucket: CodeGuruReviewerClientTypes.S3Repository?

        public init(
            bitbucket: CodeGuruReviewerClientTypes.ThirdPartySourceRepository? = nil,
            codeCommit: CodeGuruReviewerClientTypes.CodeCommitRepository? = nil,
            gitHubEnterpriseServer: CodeGuruReviewerClientTypes.ThirdPartySourceRepository? = nil,
            s3Bucket: CodeGuruReviewerClientTypes.S3Repository? = nil
        )
        {
            self.bitbucket = bitbucket
            self.codeCommit = codeCommit
            self.gitHubEnterpriseServer = gitHubEnterpriseServer
            self.s3Bucket = s3Bucket
        }
    }
}

public struct AssociateRepositoryInput: Swift.Sendable {
    /// Amazon CodeGuru Reviewer uses this value to prevent the accidental creation of duplicate repository associations if there are failures and retries.
    public var clientRequestToken: Swift.String?
    /// A KMSKeyDetails object that contains:
    ///
    /// * The encryption option for this repository association. It is either owned by Amazon Web Services Key Management Service (KMS) (AWS_OWNED_CMK) or customer managed (CUSTOMER_MANAGED_CMK).
    ///
    /// * The ID of the Amazon Web Services KMS key that is associated with this repository association.
    public var kmsKeyDetails: CodeGuruReviewerClientTypes.KMSKeyDetails?
    /// The repository to associate.
    /// This member is required.
    public var repository: CodeGuruReviewerClientTypes.Repository?
    /// An array of key-value pairs used to tag an associated repository. A tag is a custom attribute label with two parts:
    ///
    /// * A tag key (for example, CostCenter, Environment, Project, or Secret). Tag keys are case sensitive.
    ///
    /// * An optional field known as a tag value (for example, 111122223333, Production, or a team name). Omitting the tag value is the same as using an empty string. Like tag keys, tag values are case sensitive.
    public var tags: [Swift.String: Swift.String]?

    public init(
        clientRequestToken: Swift.String? = nil,
        kmsKeyDetails: CodeGuruReviewerClientTypes.KMSKeyDetails? = nil,
        repository: CodeGuruReviewerClientTypes.Repository? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.clientRequestToken = clientRequestToken
        self.kmsKeyDetails = kmsKeyDetails
        self.repository = repository
        self.tags = tags
    }
}

extension CodeGuruReviewerClientTypes {

    public enum ProviderType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case bitbucket
        case codeCommit
        case gitHub
        case gitHubEnterpriseServer
        case s3Bucket
        case sdkUnknown(Swift.String)

        public static var allCases: [ProviderType] {
            return [
                .bitbucket,
                .codeCommit,
                .gitHub,
                .gitHubEnterpriseServer,
                .s3Bucket
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .bitbucket: return "Bitbucket"
            case .codeCommit: return "CodeCommit"
            case .gitHub: return "GitHub"
            case .gitHubEnterpriseServer: return "GitHubEnterpriseServer"
            case .s3Bucket: return "S3Bucket"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Code artifacts are source code artifacts and build artifacts used in a repository analysis or a pull request review.
    ///
    /// * Source code artifacts are source code files in a Git repository that are compressed into a .zip file.
    ///
    /// * Build artifacts are .jar or .class files that are compressed in a .zip file.
    public struct CodeArtifacts: Swift.Sendable {
        /// The S3 object key for a build artifacts .zip file that contains .jar or .class files. This is required for a code review with security analysis. For more information, see [Create code reviews with GitHub Actions](https://docs.aws.amazon.com/codeguru/latest/reviewer-ug/working-with-cicd.html) in the Amazon CodeGuru Reviewer User Guide.
        public var buildArtifactsObjectKey: Swift.String?
        /// The S3 object key for a source code .zip file. This is required for all code reviews.
        /// This member is required.
        public var sourceCodeArtifactsObjectKey: Swift.String?

        public init(
            buildArtifactsObjectKey: Swift.String? = nil,
            sourceCodeArtifactsObjectKey: Swift.String? = nil
        )
        {
            self.buildArtifactsObjectKey = buildArtifactsObjectKey
            self.sourceCodeArtifactsObjectKey = sourceCodeArtifactsObjectKey
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Specifies the name of an S3 bucket and a CodeArtifacts object that contains the S3 object keys for a source code .zip file and for a build artifacts .zip file that contains .jar or .class files.
    public struct S3RepositoryDetails: Swift.Sendable {
        /// The name of the S3 bucket used for associating a new S3 repository. It must begin with codeguru-reviewer-.
        public var bucketName: Swift.String?
        /// A CodeArtifacts object. The CodeArtifacts object includes the S3 object key for a source code .zip file and for a build artifacts .zip file that contains .jar or .class files.
        public var codeArtifacts: CodeGuruReviewerClientTypes.CodeArtifacts?

        public init(
            bucketName: Swift.String? = nil,
            codeArtifacts: CodeGuruReviewerClientTypes.CodeArtifacts? = nil
        )
        {
            self.bucketName = bucketName
            self.codeArtifacts = codeArtifacts
        }
    }
}

extension CodeGuruReviewerClientTypes {

    public enum RepositoryAssociationState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case associated
        case associating
        case disassociated
        case disassociating
        case failed
        case sdkUnknown(Swift.String)

        public static var allCases: [RepositoryAssociationState] {
            return [
                .associated,
                .associating,
                .disassociated,
                .disassociating,
                .failed
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .associated: return "Associated"
            case .associating: return "Associating"
            case .disassociated: return "Disassociated"
            case .disassociating: return "Disassociating"
            case .failed: return "Failed"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Information about a repository association. The [DescribeRepositoryAssociation](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_DescribeRepositoryAssociation.html) operation returns a RepositoryAssociation object.
    public struct RepositoryAssociation: Swift.Sendable {
        /// The Amazon Resource Name (ARN) identifying the repository association.
        public var associationArn: Swift.String?
        /// The ID of the repository association.
        public var associationId: Swift.String?
        /// The Amazon Resource Name (ARN) of an Amazon Web Services CodeStar Connections connection. Its format is arn:aws:codestar-connections:region-id:aws-account_id:connection/connection-id. For more information, see [Connection](https://docs.aws.amazon.com/codestar-connections/latest/APIReference/API_Connection.html) in the Amazon Web Services CodeStar Connections API Reference.
        public var connectionArn: Swift.String?
        /// The time, in milliseconds since the epoch, when the repository association was created.
        public var createdTimeStamp: Foundation.Date?
        /// A KMSKeyDetails object that contains:
        ///
        /// * The encryption option for this repository association. It is either owned by Amazon Web Services Key Management Service (KMS) (AWS_OWNED_CMK) or customer managed (CUSTOMER_MANAGED_CMK).
        ///
        /// * The ID of the Amazon Web Services KMS key that is associated with this repository association.
        public var kmsKeyDetails: CodeGuruReviewerClientTypes.KMSKeyDetails?
        /// The time, in milliseconds since the epoch, when the repository association was last updated.
        public var lastUpdatedTimeStamp: Foundation.Date?
        /// The name of the repository.
        public var name: Swift.String?
        /// The owner of the repository. For an Amazon Web Services CodeCommit repository, this is the Amazon Web Services account ID of the account that owns the repository. For a GitHub, GitHub Enterprise Server, or Bitbucket repository, this is the username for the account that owns the repository. For an S3 repository, it can be the username or Amazon Web Services account ID.
        public var owner: Swift.String?
        /// The provider type of the repository association.
        public var providerType: CodeGuruReviewerClientTypes.ProviderType?
        /// Specifies the name of an S3 bucket and a CodeArtifacts object that contains the S3 object keys for a source code .zip file and for a build artifacts .zip file that contains .jar or .class files.
        public var s3RepositoryDetails: CodeGuruReviewerClientTypes.S3RepositoryDetails?
        /// The state of the repository association. The valid repository association states are:
        ///
        /// * Associated: The repository association is complete.
        ///
        /// * Associating: CodeGuru Reviewer is:
        ///
        /// * Setting up pull request notifications. This is required for pull requests to trigger a CodeGuru Reviewer review. If your repository ProviderType is GitHub, GitHub Enterprise Server, or Bitbucket, CodeGuru Reviewer creates webhooks in your repository to trigger CodeGuru Reviewer reviews. If you delete these webhooks, reviews of code in your repository cannot be triggered.
        ///
        /// * Setting up source code access. This is required for CodeGuru Reviewer to securely clone code in your repository.
        ///
        ///
        ///
        ///
        /// * Failed: The repository failed to associate or disassociate.
        ///
        /// * Disassociating: CodeGuru Reviewer is removing the repository's pull request notifications and source code access.
        ///
        /// * Disassociated: CodeGuru Reviewer successfully disassociated the repository. You can create a new association with this repository if you want to review source code in it later. You can control access to code reviews created in anassociated repository with tags after it has been disassociated. For more information, see [Using tags to control access to associated repositories](https://docs.aws.amazon.com/codeguru/latest/reviewer-ug/auth-and-access-control-using-tags.html) in the Amazon CodeGuru Reviewer User Guide.
        public var state: CodeGuruReviewerClientTypes.RepositoryAssociationState?
        /// A description of why the repository association is in the current state.
        public var stateReason: Swift.String?

        public init(
            associationArn: Swift.String? = nil,
            associationId: Swift.String? = nil,
            connectionArn: Swift.String? = nil,
            createdTimeStamp: Foundation.Date? = nil,
            kmsKeyDetails: CodeGuruReviewerClientTypes.KMSKeyDetails? = nil,
            lastUpdatedTimeStamp: Foundation.Date? = nil,
            name: Swift.String? = nil,
            owner: Swift.String? = nil,
            providerType: CodeGuruReviewerClientTypes.ProviderType? = nil,
            s3RepositoryDetails: CodeGuruReviewerClientTypes.S3RepositoryDetails? = nil,
            state: CodeGuruReviewerClientTypes.RepositoryAssociationState? = nil,
            stateReason: Swift.String? = nil
        )
        {
            self.associationArn = associationArn
            self.associationId = associationId
            self.connectionArn = connectionArn
            self.createdTimeStamp = createdTimeStamp
            self.kmsKeyDetails = kmsKeyDetails
            self.lastUpdatedTimeStamp = lastUpdatedTimeStamp
            self.name = name
            self.owner = owner
            self.providerType = providerType
            self.s3RepositoryDetails = s3RepositoryDetails
            self.state = state
            self.stateReason = stateReason
        }
    }
}

public struct AssociateRepositoryOutput: Swift.Sendable {
    /// Information about the repository association.
    public var repositoryAssociation: CodeGuruReviewerClientTypes.RepositoryAssociation?
    /// An array of key-value pairs used to tag an associated repository. A tag is a custom attribute label with two parts:
    ///
    /// * A tag key (for example, CostCenter, Environment, Project, or Secret). Tag keys are case sensitive.
    ///
    /// * An optional field known as a tag value (for example, 111122223333, Production, or a team name). Omitting the tag value is the same as using an empty string. Like tag keys, tag values are case sensitive.
    public var tags: [Swift.String: Swift.String]?

    public init(
        repositoryAssociation: CodeGuruReviewerClientTypes.RepositoryAssociation? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.repositoryAssociation = repositoryAssociation
        self.tags = tags
    }
}

/// The resource specified in the request was not found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CodeGuruReviewerClientTypes {

    /// A [SourceCodeType](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_SourceCodeType) that specifies the tip of a branch in an associated repository.
    public struct RepositoryHeadSourceCodeType: Swift.Sendable {
        /// The name of the branch in an associated repository. The RepositoryHeadSourceCodeType specifies the tip of this branch.
        /// This member is required.
        public var branchName: Swift.String?

        public init(
            branchName: Swift.String? = nil
        )
        {
            self.branchName = branchName
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// A type of [SourceCodeType](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_SourceCodeType) that specifies a code diff between a source and destination branch in an associated repository.
    public struct BranchDiffSourceCodeType: Swift.Sendable {
        /// The destination branch for a diff in an associated repository.
        /// This member is required.
        public var destinationBranchName: Swift.String?
        /// The source branch for a diff in an associated repository.
        /// This member is required.
        public var sourceBranchName: Swift.String?

        public init(
            destinationBranchName: Swift.String? = nil,
            sourceBranchName: Swift.String? = nil
        )
        {
            self.destinationBranchName = destinationBranchName
            self.sourceBranchName = sourceBranchName
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// A type of [SourceCodeType](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_SourceCodeType) that specifies the commit diff for a pull request on an associated repository. The SourceCommit and DestinationCommit fields are required to do a pull request code review.
    public struct CommitDiffSourceCodeType: Swift.Sendable {
        /// The SHA of the destination commit used to generate a commit diff. This field is required for a pull request code review.
        public var destinationCommit: Swift.String?
        /// The SHA of the merge base of a commit.
        public var mergeBaseCommit: Swift.String?
        /// The SHA of the source commit used to generate a commit diff. This field is required for a pull request code review.
        public var sourceCommit: Swift.String?

        public init(
            destinationCommit: Swift.String? = nil,
            mergeBaseCommit: Swift.String? = nil,
            sourceCommit: Swift.String? = nil
        )
        {
            self.destinationCommit = destinationCommit
            self.mergeBaseCommit = mergeBaseCommit
            self.sourceCommit = sourceCommit
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Information about an event. The event might be a push, pull request, scheduled request, or another type of event.
    public struct EventInfo: Swift.Sendable {
        /// The name of the event. The possible names are pull_request, workflow_dispatch, schedule, and push
        public var name: Swift.String?
        /// The state of an event. The state might be open, closed, or another state.
        public var state: Swift.String?

        public init(
            name: Swift.String? = nil,
            state: Swift.String? = nil
        )
        {
            self.name = name
            self.state = state
        }
    }
}

extension CodeGuruReviewerClientTypes {

    public enum VendorName: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case github
        case gitlab
        case nativeS3
        case sdkUnknown(Swift.String)

        public static var allCases: [VendorName] {
            return [
                .github,
                .gitlab,
                .nativeS3
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .github: return "GitHub"
            case .gitlab: return "GitLab"
            case .nativeS3: return "NativeS3"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Metadata that is associated with a code review. This applies to both pull request and repository analysis code reviews.
    public struct RequestMetadata: Swift.Sendable {
        /// Information about the event associated with a code review.
        public var eventInfo: CodeGuruReviewerClientTypes.EventInfo?
        /// The ID of the request. This is required for a pull request code review.
        public var requestId: Swift.String?
        /// An identifier, such as a name or account ID, that is associated with the requester. The Requester is used to capture the author/actor name of the event request.
        public var requester: Swift.String?
        /// The name of the repository vendor used to upload code to an S3 bucket for a CI/CD code review. For example, if code and artifacts are uploaded to an S3 bucket for a CI/CD code review by GitHub scripts from a GitHub repository, then the repository association's ProviderType is S3Bucket and the CI/CD repository vendor name is GitHub. For more information, see the definition for ProviderType in [RepositoryAssociation](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_RepositoryAssociation.html).
        public var vendorName: CodeGuruReviewerClientTypes.VendorName?

        public init(
            eventInfo: CodeGuruReviewerClientTypes.EventInfo? = nil,
            requestId: Swift.String? = nil,
            requester: Swift.String? = nil,
            vendorName: CodeGuruReviewerClientTypes.VendorName? = nil
        )
        {
            self.eventInfo = eventInfo
            self.requestId = requestId
            self.requester = requester
            self.vendorName = vendorName
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Information about an associated repository in an S3 bucket. The associated repository contains a source code .zip file and a build artifacts .zip file that contains .jar or .class files.
    public struct S3BucketRepository: Swift.Sendable {
        /// An S3RepositoryDetails object that specifies the name of an S3 bucket and a CodeArtifacts object. The CodeArtifacts object includes the S3 object keys for a source code .zip file and for a build artifacts .zip file.
        public var details: CodeGuruReviewerClientTypes.S3RepositoryDetails?
        /// The name of the repository when the ProviderType is S3Bucket.
        /// This member is required.
        public var name: Swift.String?

        public init(
            details: CodeGuruReviewerClientTypes.S3RepositoryDetails? = nil,
            name: Swift.String? = nil
        )
        {
            self.details = details
            self.name = name
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Specifies the source code that is analyzed in a code review.
    public struct SourceCodeType: Swift.Sendable {
        /// A type of [SourceCodeType](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_SourceCodeType) that specifies a source branch name and a destination branch name in an associated repository.
        public var branchDiff: CodeGuruReviewerClientTypes.BranchDiffSourceCodeType?
        /// A [SourceCodeType](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_SourceCodeType) that specifies a commit diff created by a pull request on an associated repository.
        public var commitDiff: CodeGuruReviewerClientTypes.CommitDiffSourceCodeType?
        /// A [SourceCodeType](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_SourceCodeType) that specifies the tip of a branch in an associated repository.
        public var repositoryHead: CodeGuruReviewerClientTypes.RepositoryHeadSourceCodeType?
        /// Metadata that is associated with a code review. This applies to any type of code review supported by CodeGuru Reviewer. The RequestMetadaa field captures any event metadata. For example, it might capture metadata associated with an event trigger, such as a push or a pull request.
        public var requestMetadata: CodeGuruReviewerClientTypes.RequestMetadata?
        /// Information about an associated repository in an S3 bucket that includes its name and an S3RepositoryDetails object. The S3RepositoryDetails object includes the name of an S3 bucket, an S3 key for a source code .zip file, and an S3 key for a build artifacts .zip file. S3BucketRepository is required in [SourceCodeType](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_SourceCodeType) for S3BucketRepository based code reviews.
        public var s3BucketRepository: CodeGuruReviewerClientTypes.S3BucketRepository?

        public init(
            branchDiff: CodeGuruReviewerClientTypes.BranchDiffSourceCodeType? = nil,
            commitDiff: CodeGuruReviewerClientTypes.CommitDiffSourceCodeType? = nil,
            repositoryHead: CodeGuruReviewerClientTypes.RepositoryHeadSourceCodeType? = nil,
            requestMetadata: CodeGuruReviewerClientTypes.RequestMetadata? = nil,
            s3BucketRepository: CodeGuruReviewerClientTypes.S3BucketRepository? = nil
        )
        {
            self.branchDiff = branchDiff
            self.commitDiff = commitDiff
            self.repositoryHead = repositoryHead
            self.requestMetadata = requestMetadata
            self.s3BucketRepository = s3BucketRepository
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// A code review type that analyzes all code under a specified branch in an associated repository. The associated repository is specified using its ARN when you call [CreateCodeReview](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_CreateCodeReview).
    public struct RepositoryAnalysis: Swift.Sendable {
        /// A [SourceCodeType](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_SourceCodeType) that specifies the tip of a branch in an associated repository.
        public var repositoryHead: CodeGuruReviewerClientTypes.RepositoryHeadSourceCodeType?
        /// Specifies the source code that is analyzed in a code review.
        public var sourceCodeType: CodeGuruReviewerClientTypes.SourceCodeType?

        public init(
            repositoryHead: CodeGuruReviewerClientTypes.RepositoryHeadSourceCodeType? = nil,
            sourceCodeType: CodeGuruReviewerClientTypes.SourceCodeType? = nil
        )
        {
            self.repositoryHead = repositoryHead
            self.sourceCodeType = sourceCodeType
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// The type of a code review. There are two code review types:
    ///
    /// * PullRequest - A code review that is automatically triggered by a pull request on an associated repository.
    ///
    /// * RepositoryAnalysis - A code review that analyzes all code under a specified branch in an associated repository. The associated repository is specified using its ARN in [CreateCodeReview](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_CreateCodeReview).
    public struct CodeReviewType: Swift.Sendable {
        /// They types of analysis performed during a repository analysis or a pull request review. You can specify either Security, CodeQuality, or both.
        public var analysisTypes: [CodeGuruReviewerClientTypes.AnalysisType]?
        /// A code review that analyzes all code under a specified branch in an associated repository. The associated repository is specified using its ARN in [CreateCodeReview](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_CreateCodeReview).
        /// This member is required.
        public var repositoryAnalysis: CodeGuruReviewerClientTypes.RepositoryAnalysis?

        public init(
            analysisTypes: [CodeGuruReviewerClientTypes.AnalysisType]? = nil,
            repositoryAnalysis: CodeGuruReviewerClientTypes.RepositoryAnalysis? = nil
        )
        {
            self.analysisTypes = analysisTypes
            self.repositoryAnalysis = repositoryAnalysis
        }
    }
}

public struct CreateCodeReviewInput: Swift.Sendable {
    /// Amazon CodeGuru Reviewer uses this value to prevent the accidental creation of duplicate code reviews if there are failures and retries.
    public var clientRequestToken: Swift.String?
    /// The name of the code review. The name of each code review in your Amazon Web Services account must be unique.
    /// This member is required.
    public var name: Swift.String?
    /// The Amazon Resource Name (ARN) of the [RepositoryAssociation](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_RepositoryAssociation.html) object. You can retrieve this ARN by calling [ListRepositoryAssociations](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_ListRepositoryAssociations.html). A code review can only be created on an associated repository. This is the ARN of the associated repository.
    /// This member is required.
    public var repositoryAssociationArn: Swift.String?
    /// The type of code review to create. This is specified using a [CodeReviewType](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_CodeReviewType.html) object. You can create a code review only of type RepositoryAnalysis.
    /// This member is required.
    public var type: CodeGuruReviewerClientTypes.CodeReviewType?

    public init(
        clientRequestToken: Swift.String? = nil,
        name: Swift.String? = nil,
        repositoryAssociationArn: Swift.String? = nil,
        type: CodeGuruReviewerClientTypes.CodeReviewType? = nil
    )
    {
        self.clientRequestToken = clientRequestToken
        self.name = name
        self.repositoryAssociationArn = repositoryAssociationArn
        self.type = type
    }
}

extension CodeGuruReviewerClientTypes {

    public enum ConfigFileState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case absent
        case present
        case presentWithErrors
        case sdkUnknown(Swift.String)

        public static var allCases: [ConfigFileState] {
            return [
                .absent,
                .present,
                .presentWithErrors
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .absent: return "Absent"
            case .present: return "Present"
            case .presentWithErrors: return "PresentWithErrors"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Information about the statistics from the code review.
    public struct Metrics: Swift.Sendable {
        /// Total number of recommendations found in the code review.
        public var findingsCount: Swift.Int?
        /// MeteredLinesOfCodeCount is the number of lines of code in the repository where the code review happened. This does not include non-code lines such as comments and blank lines.
        public var meteredLinesOfCodeCount: Swift.Int?
        /// SuppressedLinesOfCodeCount is the number of lines of code in the repository where the code review happened that CodeGuru Reviewer did not analyze. The lines suppressed in the analysis is based on the excludeFiles variable in the aws-codeguru-reviewer.yml file. This number does not include non-code lines such as comments and blank lines.
        public var suppressedLinesOfCodeCount: Swift.Int?

        public init(
            findingsCount: Swift.Int? = nil,
            meteredLinesOfCodeCount: Swift.Int? = nil,
            suppressedLinesOfCodeCount: Swift.Int? = nil
        )
        {
            self.findingsCount = findingsCount
            self.meteredLinesOfCodeCount = meteredLinesOfCodeCount
            self.suppressedLinesOfCodeCount = suppressedLinesOfCodeCount
        }
    }
}

extension CodeGuruReviewerClientTypes {

    public enum JobState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case completed
        case deleting
        case failed
        case pending
        case sdkUnknown(Swift.String)

        public static var allCases: [JobState] {
            return [
                .completed,
                .deleting,
                .failed,
                .pending
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .completed: return "Completed"
            case .deleting: return "Deleting"
            case .failed: return "Failed"
            case .pending: return "Pending"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruReviewerClientTypes {

    public enum ModelType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case pullRequest
        case repositoryAnalysis
        case sdkUnknown(Swift.String)

        public static var allCases: [ModelType] {
            return [
                .pullRequest,
                .repositoryAnalysis
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .pullRequest: return "PullRequest"
            case .repositoryAnalysis: return "RepositoryAnalysis"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Information about a code review. A code review belongs to the associated repository that contains the reviewed code.
    public struct CodeReview: Swift.Sendable {
        /// The types of analysis performed during a repository analysis or a pull request review. You can specify either Security, CodeQuality, or both.
        public var analysisTypes: [CodeGuruReviewerClientTypes.AnalysisType]?
        /// The Amazon Resource Name (ARN) of the [RepositoryAssociation](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_RepositoryAssociation.html) that contains the reviewed source code. You can retrieve associated repository ARNs by calling [ListRepositoryAssociations](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_ListRepositoryAssociations.html).
        public var associationArn: Swift.String?
        /// The Amazon Resource Name (ARN) of the [CodeReview](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_CodeReview.html) object.
        public var codeReviewArn: Swift.String?
        /// The state of the aws-codeguru-reviewer.yml configuration file that allows the configuration of the CodeGuru Reviewer analysis. The file either exists, doesn't exist, or exists with errors at the root directory of your repository.
        public var configFileState: CodeGuruReviewerClientTypes.ConfigFileState?
        /// The time, in milliseconds since the epoch, when the code review was created.
        public var createdTimeStamp: Foundation.Date?
        /// The time, in milliseconds since the epoch, when the code review was last updated.
        public var lastUpdatedTimeStamp: Foundation.Date?
        /// The statistics from the code review.
        public var metrics: CodeGuruReviewerClientTypes.Metrics?
        /// The name of the code review.
        public var name: Swift.String?
        /// The owner of the repository. For an Amazon Web Services CodeCommit repository, this is the Amazon Web Services account ID of the account that owns the repository. For a GitHub, GitHub Enterprise Server, or Bitbucket repository, this is the username for the account that owns the repository. For an S3 repository, it can be the username or Amazon Web Services account ID.
        public var owner: Swift.String?
        /// The type of repository that contains the reviewed code (for example, GitHub or Bitbucket).
        public var providerType: CodeGuruReviewerClientTypes.ProviderType?
        /// The pull request ID for the code review.
        public var pullRequestId: Swift.String?
        /// The name of the repository.
        public var repositoryName: Swift.String?
        /// The type of the source code for the code review.
        public var sourceCodeType: CodeGuruReviewerClientTypes.SourceCodeType?
        /// The valid code review states are:
        ///
        /// * Completed: The code review is complete.
        ///
        /// * Pending: The code review started and has not completed or failed.
        ///
        /// * Failed: The code review failed.
        ///
        /// * Deleting: The code review is being deleted.
        public var state: CodeGuruReviewerClientTypes.JobState?
        /// The reason for the state of the code review.
        public var stateReason: Swift.String?
        /// The type of code review.
        public var type: CodeGuruReviewerClientTypes.ModelType?

        public init(
            analysisTypes: [CodeGuruReviewerClientTypes.AnalysisType]? = nil,
            associationArn: Swift.String? = nil,
            codeReviewArn: Swift.String? = nil,
            configFileState: CodeGuruReviewerClientTypes.ConfigFileState? = nil,
            createdTimeStamp: Foundation.Date? = nil,
            lastUpdatedTimeStamp: Foundation.Date? = nil,
            metrics: CodeGuruReviewerClientTypes.Metrics? = nil,
            name: Swift.String? = nil,
            owner: Swift.String? = nil,
            providerType: CodeGuruReviewerClientTypes.ProviderType? = nil,
            pullRequestId: Swift.String? = nil,
            repositoryName: Swift.String? = nil,
            sourceCodeType: CodeGuruReviewerClientTypes.SourceCodeType? = nil,
            state: CodeGuruReviewerClientTypes.JobState? = nil,
            stateReason: Swift.String? = nil,
            type: CodeGuruReviewerClientTypes.ModelType? = nil
        )
        {
            self.analysisTypes = analysisTypes
            self.associationArn = associationArn
            self.codeReviewArn = codeReviewArn
            self.configFileState = configFileState
            self.createdTimeStamp = createdTimeStamp
            self.lastUpdatedTimeStamp = lastUpdatedTimeStamp
            self.metrics = metrics
            self.name = name
            self.owner = owner
            self.providerType = providerType
            self.pullRequestId = pullRequestId
            self.repositoryName = repositoryName
            self.sourceCodeType = sourceCodeType
            self.state = state
            self.stateReason = stateReason
            self.type = type
        }
    }
}

public struct CreateCodeReviewOutput: Swift.Sendable {
    /// Information about a code review. A code review belongs to the associated repository that contains the reviewed code.
    public var codeReview: CodeGuruReviewerClientTypes.CodeReview?

    public init(
        codeReview: CodeGuruReviewerClientTypes.CodeReview? = nil
    )
    {
        self.codeReview = codeReview
    }
}

public struct DescribeCodeReviewInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the [CodeReview](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_CodeReview.html) object.
    /// This member is required.
    public var codeReviewArn: Swift.String?

    public init(
        codeReviewArn: Swift.String? = nil
    )
    {
        self.codeReviewArn = codeReviewArn
    }
}

public struct DescribeCodeReviewOutput: Swift.Sendable {
    /// Information about the code review.
    public var codeReview: CodeGuruReviewerClientTypes.CodeReview?

    public init(
        codeReview: CodeGuruReviewerClientTypes.CodeReview? = nil
    )
    {
        self.codeReview = codeReview
    }
}

public struct DescribeRecommendationFeedbackInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the [CodeReview](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_CodeReview.html) object.
    /// This member is required.
    public var codeReviewArn: Swift.String?
    /// The recommendation ID that can be used to track the provided recommendations and then to collect the feedback.
    /// This member is required.
    public var recommendationId: Swift.String?
    /// Optional parameter to describe the feedback for a given user. If this is not supplied, it defaults to the user making the request. The UserId is an IAM principal that can be specified as an Amazon Web Services account ID or an Amazon Resource Name (ARN). For more information, see [ Specifying a Principal](https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_elements_principal.html#Principal_specifying) in the Amazon Web Services Identity and Access Management User Guide.
    public var userId: Swift.String?

    public init(
        codeReviewArn: Swift.String? = nil,
        recommendationId: Swift.String? = nil,
        userId: Swift.String? = nil
    )
    {
        self.codeReviewArn = codeReviewArn
        self.recommendationId = recommendationId
        self.userId = userId
    }
}

extension CodeGuruReviewerClientTypes {

    public enum Reaction: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case thumbsDown
        case thumbsUp
        case sdkUnknown(Swift.String)

        public static var allCases: [Reaction] {
            return [
                .thumbsDown,
                .thumbsUp
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .thumbsDown: return "ThumbsDown"
            case .thumbsUp: return "ThumbsUp"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Information about the recommendation feedback.
    public struct RecommendationFeedback: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the [CodeReview](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_CodeReview.html) object.
        public var codeReviewArn: Swift.String?
        /// The time at which the feedback was created.
        public var createdTimeStamp: Foundation.Date?
        /// The time at which the feedback was last updated.
        public var lastUpdatedTimeStamp: Foundation.Date?
        /// List for storing reactions. Reactions are utf-8 text code for emojis. You can send an empty list to clear off all your feedback.
        public var reactions: [CodeGuruReviewerClientTypes.Reaction]?
        /// The recommendation ID that can be used to track the provided recommendations. Later on it can be used to collect the feedback.
        public var recommendationId: Swift.String?
        /// The ID of the user that made the API call. The UserId is an IAM principal that can be specified as an Amazon Web Services account ID or an Amazon Resource Name (ARN). For more information, see [ Specifying a Principal](https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_elements_principal.html#Principal_specifying) in the Amazon Web Services Identity and Access Management User Guide.
        public var userId: Swift.String?

        public init(
            codeReviewArn: Swift.String? = nil,
            createdTimeStamp: Foundation.Date? = nil,
            lastUpdatedTimeStamp: Foundation.Date? = nil,
            reactions: [CodeGuruReviewerClientTypes.Reaction]? = nil,
            recommendationId: Swift.String? = nil,
            userId: Swift.String? = nil
        )
        {
            self.codeReviewArn = codeReviewArn
            self.createdTimeStamp = createdTimeStamp
            self.lastUpdatedTimeStamp = lastUpdatedTimeStamp
            self.reactions = reactions
            self.recommendationId = recommendationId
            self.userId = userId
        }
    }
}

public struct DescribeRecommendationFeedbackOutput: Swift.Sendable {
    /// The recommendation feedback given by the user.
    public var recommendationFeedback: CodeGuruReviewerClientTypes.RecommendationFeedback?

    public init(
        recommendationFeedback: CodeGuruReviewerClientTypes.RecommendationFeedback? = nil
    )
    {
        self.recommendationFeedback = recommendationFeedback
    }
}

/// The resource specified in the request was not found.
public struct NotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "NotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DescribeRepositoryAssociationInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the [RepositoryAssociation](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_RepositoryAssociation.html) object. You can retrieve this ARN by calling [ListRepositoryAssociations](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_ListRepositoryAssociations.html).
    /// This member is required.
    public var associationArn: Swift.String?

    public init(
        associationArn: Swift.String? = nil
    )
    {
        self.associationArn = associationArn
    }
}

public struct DescribeRepositoryAssociationOutput: Swift.Sendable {
    /// Information about the repository association.
    public var repositoryAssociation: CodeGuruReviewerClientTypes.RepositoryAssociation?
    /// An array of key-value pairs used to tag an associated repository. A tag is a custom attribute label with two parts:
    ///
    /// * A tag key (for example, CostCenter, Environment, Project, or Secret). Tag keys are case sensitive.
    ///
    /// * An optional field known as a tag value (for example, 111122223333, Production, or a team name). Omitting the tag value is the same as using an empty string. Like tag keys, tag values are case sensitive.
    public var tags: [Swift.String: Swift.String]?

    public init(
        repositoryAssociation: CodeGuruReviewerClientTypes.RepositoryAssociation? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.repositoryAssociation = repositoryAssociation
        self.tags = tags
    }
}

public struct DisassociateRepositoryInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the [RepositoryAssociation](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_RepositoryAssociation.html) object. You can retrieve this ARN by calling [ListRepositoryAssociations](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_ListRepositoryAssociations.html).
    /// This member is required.
    public var associationArn: Swift.String?

    public init(
        associationArn: Swift.String? = nil
    )
    {
        self.associationArn = associationArn
    }
}

public struct DisassociateRepositoryOutput: Swift.Sendable {
    /// Information about the disassociated repository.
    public var repositoryAssociation: CodeGuruReviewerClientTypes.RepositoryAssociation?
    /// An array of key-value pairs used to tag an associated repository. A tag is a custom attribute label with two parts:
    ///
    /// * A tag key (for example, CostCenter, Environment, Project, or Secret). Tag keys are case sensitive.
    ///
    /// * An optional field known as a tag value (for example, 111122223333, Production, or a team name). Omitting the tag value is the same as using an empty string. Like tag keys, tag values are case sensitive.
    public var tags: [Swift.String: Swift.String]?

    public init(
        repositoryAssociation: CodeGuruReviewerClientTypes.RepositoryAssociation? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.repositoryAssociation = repositoryAssociation
        self.tags = tags
    }
}

public struct ListCodeReviewsInput: Swift.Sendable {
    /// The maximum number of results that are returned per call. The default is 100.
    public var maxResults: Swift.Int?
    /// If nextToken is returned, there are more results available. The value of nextToken is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged.
    public var nextToken: Swift.String?
    /// List of provider types for filtering that needs to be applied before displaying the result. For example, providerTypes=[GitHub] lists code reviews from GitHub.
    public var providerTypes: [CodeGuruReviewerClientTypes.ProviderType]?
    /// List of repository names for filtering that needs to be applied before displaying the result.
    public var repositoryNames: [Swift.String]?
    /// List of states for filtering that needs to be applied before displaying the result. For example, states=[Pending] lists code reviews in the Pending state. The valid code review states are:
    ///
    /// * Completed: The code review is complete.
    ///
    /// * Pending: The code review started and has not completed or failed.
    ///
    /// * Failed: The code review failed.
    ///
    /// * Deleting: The code review is being deleted.
    public var states: [CodeGuruReviewerClientTypes.JobState]?
    /// The type of code reviews to list in the response.
    /// This member is required.
    public var type: CodeGuruReviewerClientTypes.ModelType?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        providerTypes: [CodeGuruReviewerClientTypes.ProviderType]? = nil,
        repositoryNames: [Swift.String]? = nil,
        states: [CodeGuruReviewerClientTypes.JobState]? = nil,
        type: CodeGuruReviewerClientTypes.ModelType? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.providerTypes = providerTypes
        self.repositoryNames = repositoryNames
        self.states = states
        self.type = type
    }
}

extension CodeGuruReviewerClientTypes {

    /// Information about metrics summaries.
    public struct MetricsSummary: Swift.Sendable {
        /// Total number of recommendations found in the code review.
        public var findingsCount: Swift.Int?
        /// Lines of code metered in the code review. For the initial code review pull request and all subsequent revisions, this includes all lines of code in the files added to the pull request. In subsequent revisions, for files that already existed in the pull request, this includes only the changed lines of code. In both cases, this does not include non-code lines such as comments and import statements. For example, if you submit a pull request containing 5 files, each with 500 lines of code, and in a subsequent revision you added a new file with 200 lines of code, and also modified a total of 25 lines across the initial 5 files, MeteredLinesOfCodeCount includes the first 5 files (5 * 500 = 2,500 lines), the new file (200 lines) and the 25 changed lines of code for a total of 2,725 lines of code.
        public var meteredLinesOfCodeCount: Swift.Int?
        /// Lines of code suppressed in the code review based on the excludeFiles element in the aws-codeguru-reviewer.yml file. For full repository analyses, this number includes all lines of code in the files that are suppressed. For pull requests, this number only includes the changed lines of code that are suppressed. In both cases, this number does not include non-code lines such as comments and import statements. For example, if you initiate a full repository analysis on a repository containing 5 files, each file with 100 lines of code, and 2 files are listed as excluded in the aws-codeguru-reviewer.yml file, then SuppressedLinesOfCodeCount returns 200 (2 * 100) as the total number of lines of code suppressed. However, if you submit a pull request for the same repository, then SuppressedLinesOfCodeCount only includes the lines in the 2 files that changed. If only 1 of the 2 files changed in the pull request, then SuppressedLinesOfCodeCount returns 100 (1 * 100) as the total number of lines of code suppressed.
        public var suppressedLinesOfCodeCount: Swift.Int?

        public init(
            findingsCount: Swift.Int? = nil,
            meteredLinesOfCodeCount: Swift.Int? = nil,
            suppressedLinesOfCodeCount: Swift.Int? = nil
        )
        {
            self.findingsCount = findingsCount
            self.meteredLinesOfCodeCount = meteredLinesOfCodeCount
            self.suppressedLinesOfCodeCount = suppressedLinesOfCodeCount
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Information about the summary of the code review.
    public struct CodeReviewSummary: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the [CodeReview](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_CodeReview.html) object.
        public var codeReviewArn: Swift.String?
        /// The time, in milliseconds since the epoch, when the code review was created.
        public var createdTimeStamp: Foundation.Date?
        /// The time, in milliseconds since the epoch, when the code review was last updated.
        public var lastUpdatedTimeStamp: Foundation.Date?
        /// The statistics from the code review.
        public var metricsSummary: CodeGuruReviewerClientTypes.MetricsSummary?
        /// The name of the code review.
        public var name: Swift.String?
        /// The owner of the repository. For an Amazon Web Services CodeCommit repository, this is the Amazon Web Services account ID of the account that owns the repository. For a GitHub, GitHub Enterprise Server, or Bitbucket repository, this is the username for the account that owns the repository. For an S3 repository, it can be the username or Amazon Web Services account ID.
        public var owner: Swift.String?
        /// The provider type of the repository association.
        public var providerType: CodeGuruReviewerClientTypes.ProviderType?
        /// The pull request ID for the code review.
        public var pullRequestId: Swift.String?
        /// The name of the repository.
        public var repositoryName: Swift.String?
        /// Specifies the source code that is analyzed in a code review.
        public var sourceCodeType: CodeGuruReviewerClientTypes.SourceCodeType?
        /// The state of the code review. The valid code review states are:
        ///
        /// * Completed: The code review is complete.
        ///
        /// * Pending: The code review started and has not completed or failed.
        ///
        /// * Failed: The code review failed.
        ///
        /// * Deleting: The code review is being deleted.
        public var state: CodeGuruReviewerClientTypes.JobState?
        /// The type of the code review.
        public var type: CodeGuruReviewerClientTypes.ModelType?

        public init(
            codeReviewArn: Swift.String? = nil,
            createdTimeStamp: Foundation.Date? = nil,
            lastUpdatedTimeStamp: Foundation.Date? = nil,
            metricsSummary: CodeGuruReviewerClientTypes.MetricsSummary? = nil,
            name: Swift.String? = nil,
            owner: Swift.String? = nil,
            providerType: CodeGuruReviewerClientTypes.ProviderType? = nil,
            pullRequestId: Swift.String? = nil,
            repositoryName: Swift.String? = nil,
            sourceCodeType: CodeGuruReviewerClientTypes.SourceCodeType? = nil,
            state: CodeGuruReviewerClientTypes.JobState? = nil,
            type: CodeGuruReviewerClientTypes.ModelType? = nil
        )
        {
            self.codeReviewArn = codeReviewArn
            self.createdTimeStamp = createdTimeStamp
            self.lastUpdatedTimeStamp = lastUpdatedTimeStamp
            self.metricsSummary = metricsSummary
            self.name = name
            self.owner = owner
            self.providerType = providerType
            self.pullRequestId = pullRequestId
            self.repositoryName = repositoryName
            self.sourceCodeType = sourceCodeType
            self.state = state
            self.type = type
        }
    }
}

public struct ListCodeReviewsOutput: Swift.Sendable {
    /// A list of code reviews that meet the criteria of the request.
    public var codeReviewSummaries: [CodeGuruReviewerClientTypes.CodeReviewSummary]?
    /// Pagination token.
    public var nextToken: Swift.String?

    public init(
        codeReviewSummaries: [CodeGuruReviewerClientTypes.CodeReviewSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.codeReviewSummaries = codeReviewSummaries
        self.nextToken = nextToken
    }
}

public struct ListRecommendationFeedbackInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the [CodeReview](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_CodeReview.html) object.
    /// This member is required.
    public var codeReviewArn: Swift.String?
    /// The maximum number of results that are returned per call. The default is 100.
    public var maxResults: Swift.Int?
    /// If nextToken is returned, there are more results available. The value of nextToken is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged.
    public var nextToken: Swift.String?
    /// Used to query the recommendation feedback for a given recommendation.
    public var recommendationIds: [Swift.String]?
    /// An Amazon Web Services user's account ID or Amazon Resource Name (ARN). Use this ID to query the recommendation feedback for a code review from that user. The UserId is an IAM principal that can be specified as an Amazon Web Services account ID or an Amazon Resource Name (ARN). For more information, see [ Specifying a Principal](https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_elements_principal.html#Principal_specifying) in the Amazon Web Services Identity and Access Management User Guide.
    public var userIds: [Swift.String]?

    public init(
        codeReviewArn: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        recommendationIds: [Swift.String]? = nil,
        userIds: [Swift.String]? = nil
    )
    {
        self.codeReviewArn = codeReviewArn
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.recommendationIds = recommendationIds
        self.userIds = userIds
    }
}

extension CodeGuruReviewerClientTypes {

    /// Information about recommendation feedback summaries.
    public struct RecommendationFeedbackSummary: Swift.Sendable {
        /// List for storing reactions. Reactions are utf-8 text code for emojis.
        public var reactions: [CodeGuruReviewerClientTypes.Reaction]?
        /// The recommendation ID that can be used to track the provided recommendations. Later on it can be used to collect the feedback.
        public var recommendationId: Swift.String?
        /// The ID of the user that gave the feedback. The UserId is an IAM principal that can be specified as an Amazon Web Services account ID or an Amazon Resource Name (ARN). For more information, see [ Specifying a Principal](https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_elements_principal.html#Principal_specifying) in the Amazon Web Services Identity and Access Management User Guide.
        public var userId: Swift.String?

        public init(
            reactions: [CodeGuruReviewerClientTypes.Reaction]? = nil,
            recommendationId: Swift.String? = nil,
            userId: Swift.String? = nil
        )
        {
            self.reactions = reactions
            self.recommendationId = recommendationId
            self.userId = userId
        }
    }
}

public struct ListRecommendationFeedbackOutput: Swift.Sendable {
    /// If nextToken is returned, there are more results available. The value of nextToken is a unique pagination token for each page. Make the call again using the returned token to retrieve the next page. Keep all other arguments unchanged.
    public var nextToken: Swift.String?
    /// Recommendation feedback summaries corresponding to the code review ARN.
    public var recommendationFeedbackSummaries: [CodeGuruReviewerClientTypes.RecommendationFeedbackSummary]?

    public init(
        nextToken: Swift.String? = nil,
        recommendationFeedbackSummaries: [CodeGuruReviewerClientTypes.RecommendationFeedbackSummary]? = nil
    )
    {
        self.nextToken = nextToken
        self.recommendationFeedbackSummaries = recommendationFeedbackSummaries
    }
}

public struct ListRecommendationsInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the [CodeReview](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_CodeReview.html) object.
    /// This member is required.
    public var codeReviewArn: Swift.String?
    /// The maximum number of results that are returned per call. The default is 100.
    public var maxResults: Swift.Int?
    /// Pagination token.
    public var nextToken: Swift.String?

    public init(
        codeReviewArn: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.codeReviewArn = codeReviewArn
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension CodeGuruReviewerClientTypes {

    public enum RecommendationCategory: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case awsBestPractices
        case awsCloudformationIssues
        case codeInconsistencies
        case codeMaintenanceIssues
        case concurrencyIssues
        case duplicateCode
        case inputValidations
        case javaBestPractices
        case pythonBestPractices
        case resourceLeaks
        case securityIssues
        case sdkUnknown(Swift.String)

        public static var allCases: [RecommendationCategory] {
            return [
                .awsBestPractices,
                .awsCloudformationIssues,
                .codeInconsistencies,
                .codeMaintenanceIssues,
                .concurrencyIssues,
                .duplicateCode,
                .inputValidations,
                .javaBestPractices,
                .pythonBestPractices,
                .resourceLeaks,
                .securityIssues
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .awsBestPractices: return "AWSBestPractices"
            case .awsCloudformationIssues: return "AWSCloudFormationIssues"
            case .codeInconsistencies: return "CodeInconsistencies"
            case .codeMaintenanceIssues: return "CodeMaintenanceIssues"
            case .concurrencyIssues: return "ConcurrencyIssues"
            case .duplicateCode: return "DuplicateCode"
            case .inputValidations: return "InputValidations"
            case .javaBestPractices: return "JavaBestPractices"
            case .pythonBestPractices: return "PythonBestPractices"
            case .resourceLeaks: return "ResourceLeaks"
            case .securityIssues: return "SecurityIssues"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Metadata about a rule. Rule metadata includes an ID, a name, a list of tags, and a short and long description. CodeGuru Reviewer uses rules to analyze code. A rule's recommendation is included in analysis results if code is detected that violates the rule.
    public struct RuleMetadata: Swift.Sendable {
        /// A long description of the rule.
        public var longDescription: Swift.String?
        /// The ID of the rule.
        public var ruleId: Swift.String?
        /// The name of the rule.
        public var ruleName: Swift.String?
        /// Tags that are associated with the rule.
        public var ruleTags: [Swift.String]?
        /// A short description of the rule.
        public var shortDescription: Swift.String?

        public init(
            longDescription: Swift.String? = nil,
            ruleId: Swift.String? = nil,
            ruleName: Swift.String? = nil,
            ruleTags: [Swift.String]? = nil,
            shortDescription: Swift.String? = nil
        )
        {
            self.longDescription = longDescription
            self.ruleId = ruleId
            self.ruleName = ruleName
            self.ruleTags = ruleTags
            self.shortDescription = shortDescription
        }
    }
}

extension CodeGuruReviewerClientTypes {

    public enum Severity: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case critical
        case high
        case info
        case low
        case medium
        case sdkUnknown(Swift.String)

        public static var allCases: [Severity] {
            return [
                .critical,
                .high,
                .info,
                .low,
                .medium
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .critical: return "Critical"
            case .high: return "High"
            case .info: return "Info"
            case .low: return "Low"
            case .medium: return "Medium"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeGuruReviewerClientTypes {

    /// Information about recommendations.
    public struct RecommendationSummary: Swift.Sendable {
        /// A description of the recommendation generated by CodeGuru Reviewer for the lines of code between the start line and the end line.
        public var description: Swift.String?
        /// Last line where the recommendation is applicable in the source commit or source branch. For a single line comment the start line and end line values are the same.
        public var endLine: Swift.Int?
        /// Name of the file on which a recommendation is provided.
        public var filePath: Swift.String?
        /// The type of a recommendation.
        public var recommendationCategory: CodeGuruReviewerClientTypes.RecommendationCategory?
        /// The recommendation ID that can be used to track the provided recommendations. Later on it can be used to collect the feedback.
        public var recommendationId: Swift.String?
        /// Metadata about a rule. Rule metadata includes an ID, a name, a list of tags, and a short and long description. CodeGuru Reviewer uses rules to analyze code. A rule's recommendation is included in analysis results if code is detected that violates the rule.
        public var ruleMetadata: CodeGuruReviewerClientTypes.RuleMetadata?
        /// The severity of the issue in the code that generated this recommendation.
        public var severity: CodeGuruReviewerClientTypes.Severity?
        /// Start line from where the recommendation is applicable in the source commit or source branch.
        public var startLine: Swift.Int?

        public init(
            description: Swift.String? = nil,
            endLine: Swift.Int? = nil,
            filePath: Swift.String? = nil,
            recommendationCategory: CodeGuruReviewerClientTypes.RecommendationCategory? = nil,
            recommendationId: Swift.String? = nil,
            ruleMetadata: CodeGuruReviewerClientTypes.RuleMetadata? = nil,
            severity: CodeGuruReviewerClientTypes.Severity? = nil,
            startLine: Swift.Int? = nil
        )
        {
            self.description = description
            self.endLine = endLine
            self.filePath = filePath
            self.recommendationCategory = recommendationCategory
            self.recommendationId = recommendationId
            self.ruleMetadata = ruleMetadata
            self.severity = severity
            self.startLine = startLine
        }
    }
}

public struct ListRecommendationsOutput: Swift.Sendable {
    /// Pagination token.
    public var nextToken: Swift.String?
    /// List of recommendations for the requested code review.
    public var recommendationSummaries: [CodeGuruReviewerClientTypes.RecommendationSummary]?

    public init(
        nextToken: Swift.String? = nil,
        recommendationSummaries: [CodeGuruReviewerClientTypes.RecommendationSummary]? = nil
    )
    {
        self.nextToken = nextToken
        self.recommendationSummaries = recommendationSummaries
    }
}

public struct ListRepositoryAssociationsInput: Swift.Sendable {
    /// The maximum number of repository association results returned by ListRepositoryAssociations in paginated output. When this parameter is used, ListRepositoryAssociations only returns maxResults results in a single page with a nextToken response element. The remaining results of the initial request can be seen by sending another ListRepositoryAssociations request with the returned nextToken value. This value can be between 1 and 100. If this parameter is not used, ListRepositoryAssociations returns up to 100 results and a nextToken value if applicable.
    public var maxResults: Swift.Int?
    /// List of repository names to use as a filter.
    public var names: [Swift.String]?
    /// The nextToken value returned from a previous paginated ListRepositoryAssociations request where maxResults was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the nextToken value. Treat this token as an opaque identifier that is only used to retrieve the next items in a list and not for other programmatic purposes.
    public var nextToken: Swift.String?
    /// List of owners to use as a filter. For Amazon Web Services CodeCommit, it is the name of the CodeCommit account that was used to associate the repository. For other repository source providers, such as Bitbucket and GitHub Enterprise Server, this is name of the account that was used to associate the repository.
    public var owners: [Swift.String]?
    /// List of provider types to use as a filter.
    public var providerTypes: [CodeGuruReviewerClientTypes.ProviderType]?
    /// List of repository association states to use as a filter. The valid repository association states are:
    ///
    /// * Associated: The repository association is complete.
    ///
    /// * Associating: CodeGuru Reviewer is:
    ///
    /// * Setting up pull request notifications. This is required for pull requests to trigger a CodeGuru Reviewer review. If your repository ProviderType is GitHub, GitHub Enterprise Server, or Bitbucket, CodeGuru Reviewer creates webhooks in your repository to trigger CodeGuru Reviewer reviews. If you delete these webhooks, reviews of code in your repository cannot be triggered.
    ///
    /// * Setting up source code access. This is required for CodeGuru Reviewer to securely clone code in your repository.
    ///
    ///
    ///
    ///
    /// * Failed: The repository failed to associate or disassociate.
    ///
    /// * Disassociating: CodeGuru Reviewer is removing the repository's pull request notifications and source code access.
    ///
    /// * Disassociated: CodeGuru Reviewer successfully disassociated the repository. You can create a new association with this repository if you want to review source code in it later. You can control access to code reviews created in anassociated repository with tags after it has been disassociated. For more information, see [Using tags to control access to associated repositories](https://docs.aws.amazon.com/codeguru/latest/reviewer-ug/auth-and-access-control-using-tags.html) in the Amazon CodeGuru Reviewer User Guide.
    public var states: [CodeGuruReviewerClientTypes.RepositoryAssociationState]?

    public init(
        maxResults: Swift.Int? = nil,
        names: [Swift.String]? = nil,
        nextToken: Swift.String? = nil,
        owners: [Swift.String]? = nil,
        providerTypes: [CodeGuruReviewerClientTypes.ProviderType]? = nil,
        states: [CodeGuruReviewerClientTypes.RepositoryAssociationState]? = nil
    )
    {
        self.maxResults = maxResults
        self.names = names
        self.nextToken = nextToken
        self.owners = owners
        self.providerTypes = providerTypes
        self.states = states
    }
}

extension CodeGuruReviewerClientTypes {

    /// Summary information about a repository association. The [ListRepositoryAssociations](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_ListRepositoryAssociations.html) operation returns a list of RepositoryAssociationSummary objects.
    public struct RepositoryAssociationSummary: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the [RepositoryAssociation](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_RepositoryAssociation.html) object. You can retrieve this ARN by calling [ListRepositoryAssociations](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_ListRepositoryAssociations.html).
        public var associationArn: Swift.String?
        /// The repository association ID.
        public var associationId: Swift.String?
        /// The Amazon Resource Name (ARN) of an Amazon Web Services CodeStar Connections connection. Its format is arn:aws:codestar-connections:region-id:aws-account_id:connection/connection-id. For more information, see [Connection](https://docs.aws.amazon.com/codestar-connections/latest/APIReference/API_Connection.html) in the Amazon Web Services CodeStar Connections API Reference.
        public var connectionArn: Swift.String?
        /// The time, in milliseconds since the epoch, since the repository association was last updated.
        public var lastUpdatedTimeStamp: Foundation.Date?
        /// The name of the repository association.
        public var name: Swift.String?
        /// The owner of the repository. For an Amazon Web Services CodeCommit repository, this is the Amazon Web Services account ID of the account that owns the repository. For a GitHub, GitHub Enterprise Server, or Bitbucket repository, this is the username for the account that owns the repository. For an S3 repository, it can be the username or Amazon Web Services account ID.
        public var owner: Swift.String?
        /// The provider type of the repository association.
        public var providerType: CodeGuruReviewerClientTypes.ProviderType?
        /// The state of the repository association. The valid repository association states are:
        ///
        /// * Associated: The repository association is complete.
        ///
        /// * Associating: CodeGuru Reviewer is:
        ///
        /// * Setting up pull request notifications. This is required for pull requests to trigger a CodeGuru Reviewer review. If your repository ProviderType is GitHub, GitHub Enterprise Server, or Bitbucket, CodeGuru Reviewer creates webhooks in your repository to trigger CodeGuru Reviewer reviews. If you delete these webhooks, reviews of code in your repository cannot be triggered.
        ///
        /// * Setting up source code access. This is required for CodeGuru Reviewer to securely clone code in your repository.
        ///
        ///
        ///
        ///
        /// * Failed: The repository failed to associate or disassociate.
        ///
        /// * Disassociating: CodeGuru Reviewer is removing the repository's pull request notifications and source code access.
        ///
        /// * Disassociated: CodeGuru Reviewer successfully disassociated the repository. You can create a new association with this repository if you want to review source code in it later. You can control access to code reviews created in anassociated repository with tags after it has been disassociated. For more information, see [Using tags to control access to associated repositories](https://docs.aws.amazon.com/codeguru/latest/reviewer-ug/auth-and-access-control-using-tags.html) in the Amazon CodeGuru Reviewer User Guide.
        public var state: CodeGuruReviewerClientTypes.RepositoryAssociationState?

        public init(
            associationArn: Swift.String? = nil,
            associationId: Swift.String? = nil,
            connectionArn: Swift.String? = nil,
            lastUpdatedTimeStamp: Foundation.Date? = nil,
            name: Swift.String? = nil,
            owner: Swift.String? = nil,
            providerType: CodeGuruReviewerClientTypes.ProviderType? = nil,
            state: CodeGuruReviewerClientTypes.RepositoryAssociationState? = nil
        )
        {
            self.associationArn = associationArn
            self.associationId = associationId
            self.connectionArn = connectionArn
            self.lastUpdatedTimeStamp = lastUpdatedTimeStamp
            self.name = name
            self.owner = owner
            self.providerType = providerType
            self.state = state
        }
    }
}

public struct ListRepositoryAssociationsOutput: Swift.Sendable {
    /// The nextToken value to include in a future ListRecommendations request. When the results of a ListRecommendations request exceed maxResults, this value can be used to retrieve the next page of results. This value is null when there are no more results to return.
    public var nextToken: Swift.String?
    /// A list of repository associations that meet the criteria of the request.
    public var repositoryAssociationSummaries: [CodeGuruReviewerClientTypes.RepositoryAssociationSummary]?

    public init(
        nextToken: Swift.String? = nil,
        repositoryAssociationSummaries: [CodeGuruReviewerClientTypes.RepositoryAssociationSummary]? = nil
    )
    {
        self.nextToken = nextToken
        self.repositoryAssociationSummaries = repositoryAssociationSummaries
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the [RepositoryAssociation](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_RepositoryAssociation.html) object. You can retrieve this ARN by calling [ListRepositoryAssociations](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_ListRepositoryAssociations.html).
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// An array of key-value pairs used to tag an associated repository. A tag is a custom attribute label with two parts:
    ///
    /// * A tag key (for example, CostCenter, Environment, Project, or Secret). Tag keys are case sensitive.
    ///
    /// * An optional field known as a tag value (for example, 111122223333, Production, or a team name). Omitting the tag value is the same as using an empty string. Like tag keys, tag values are case sensitive.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

public struct PutRecommendationFeedbackInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the [CodeReview](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_CodeReview.html) object.
    /// This member is required.
    public var codeReviewArn: Swift.String?
    /// List for storing reactions. Reactions are utf-8 text code for emojis. If you send an empty list it clears all your feedback.
    /// This member is required.
    public var reactions: [CodeGuruReviewerClientTypes.Reaction]?
    /// The recommendation ID that can be used to track the provided recommendations and then to collect the feedback.
    /// This member is required.
    public var recommendationId: Swift.String?

    public init(
        codeReviewArn: Swift.String? = nil,
        reactions: [CodeGuruReviewerClientTypes.Reaction]? = nil,
        recommendationId: Swift.String? = nil
    )
    {
        self.codeReviewArn = codeReviewArn
        self.reactions = reactions
        self.recommendationId = recommendationId
    }
}

public struct PutRecommendationFeedbackOutput: Swift.Sendable {

    public init() { }
}

public struct TagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the [RepositoryAssociation](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_RepositoryAssociation.html) object. You can retrieve this ARN by calling [ListRepositoryAssociations](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_ListRepositoryAssociations.html).
    /// This member is required.
    public var resourceArn: Swift.String?
    /// An array of key-value pairs used to tag an associated repository. A tag is a custom attribute label with two parts:
    ///
    /// * A tag key (for example, CostCenter, Environment, Project, or Secret). Tag keys are case sensitive.
    ///
    /// * An optional field known as a tag value (for example, 111122223333, Production, or a team name). Omitting the tag value is the same as using an empty string. Like tag keys, tag values are case sensitive.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the [RepositoryAssociation](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_RepositoryAssociation.html) object. You can retrieve this ARN by calling [ListRepositoryAssociations](https://docs.aws.amazon.com/codeguru/latest/reviewer-api/API_ListRepositoryAssociations.html).
    /// This member is required.
    public var resourceArn: Swift.String?
    /// A list of the keys for each tag you want to remove from an associated repository.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

extension AssociateRepositoryInput {

    static func urlPathProvider(_ value: AssociateRepositoryInput) -> Swift.String? {
        return "/associations"
    }
}

extension CreateCodeReviewInput {

    static func urlPathProvider(_ value: CreateCodeReviewInput) -> Swift.String? {
        return "/codereviews"
    }
}

extension DescribeCodeReviewInput {

    static func urlPathProvider(_ value: DescribeCodeReviewInput) -> Swift.String? {
        guard let codeReviewArn = value.codeReviewArn else {
            return nil
        }
        return "/codereviews/\(codeReviewArn.urlPercentEncoding())"
    }
}

extension DescribeRecommendationFeedbackInput {

    static func urlPathProvider(_ value: DescribeRecommendationFeedbackInput) -> Swift.String? {
        guard let codeReviewArn = value.codeReviewArn else {
            return nil
        }
        return "/feedback/\(codeReviewArn.urlPercentEncoding())"
    }
}

extension DescribeRecommendationFeedbackInput {

    static func queryItemProvider(_ value: DescribeRecommendationFeedbackInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let userId = value.userId {
            let userIdQueryItem = Smithy.URIQueryItem(name: "UserId".urlPercentEncoding(), value: Swift.String(userId).urlPercentEncoding())
            items.append(userIdQueryItem)
        }
        guard let recommendationId = value.recommendationId else {
            let message = "Creating a URL Query Item failed. recommendationId is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let recommendationIdQueryItem = Smithy.URIQueryItem(name: "RecommendationId".urlPercentEncoding(), value: Swift.String(recommendationId).urlPercentEncoding())
        items.append(recommendationIdQueryItem)
        return items
    }
}

extension DescribeRepositoryAssociationInput {

    static func urlPathProvider(_ value: DescribeRepositoryAssociationInput) -> Swift.String? {
        guard let associationArn = value.associationArn else {
            return nil
        }
        return "/associations/\(associationArn.urlPercentEncoding())"
    }
}

extension DisassociateRepositoryInput {

    static func urlPathProvider(_ value: DisassociateRepositoryInput) -> Swift.String? {
        guard let associationArn = value.associationArn else {
            return nil
        }
        return "/associations/\(associationArn.urlPercentEncoding())"
    }
}

extension ListCodeReviewsInput {

    static func urlPathProvider(_ value: ListCodeReviewsInput) -> Swift.String? {
        return "/codereviews"
    }
}

extension ListCodeReviewsInput {

    static func queryItemProvider(_ value: ListCodeReviewsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let states = value.states {
            states.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "States".urlPercentEncoding(), value: Swift.String(queryItemValue.rawValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        if let repositoryNames = value.repositoryNames {
            repositoryNames.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "RepositoryNames".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        guard let type = value.type else {
            let message = "Creating a URL Query Item failed. type is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        let typeQueryItem = Smithy.URIQueryItem(name: "Type".urlPercentEncoding(), value: Swift.String(type.rawValue).urlPercentEncoding())
        items.append(typeQueryItem)
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "NextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let providerTypes = value.providerTypes {
            providerTypes.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "ProviderTypes".urlPercentEncoding(), value: Swift.String(queryItemValue.rawValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "MaxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListRecommendationFeedbackInput {

    static func urlPathProvider(_ value: ListRecommendationFeedbackInput) -> Swift.String? {
        guard let codeReviewArn = value.codeReviewArn else {
            return nil
        }
        return "/feedback/\(codeReviewArn.urlPercentEncoding())/RecommendationFeedback"
    }
}

extension ListRecommendationFeedbackInput {

    static func queryItemProvider(_ value: ListRecommendationFeedbackInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let recommendationIds = value.recommendationIds {
            recommendationIds.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "RecommendationIds".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "NextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "MaxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let userIds = value.userIds {
            userIds.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "UserIds".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        return items
    }
}

extension ListRecommendationsInput {

    static func urlPathProvider(_ value: ListRecommendationsInput) -> Swift.String? {
        guard let codeReviewArn = value.codeReviewArn else {
            return nil
        }
        return "/codereviews/\(codeReviewArn.urlPercentEncoding())/Recommendations"
    }
}

extension ListRecommendationsInput {

    static func queryItemProvider(_ value: ListRecommendationsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "NextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "MaxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListRepositoryAssociationsInput {

    static func urlPathProvider(_ value: ListRepositoryAssociationsInput) -> Swift.String? {
        return "/associations"
    }
}

extension ListRepositoryAssociationsInput {

    static func queryItemProvider(_ value: ListRepositoryAssociationsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let states = value.states {
            states.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "State".urlPercentEncoding(), value: Swift.String(queryItemValue.rawValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        if let names = value.names {
            names.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "Name".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "NextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let providerTypes = value.providerTypes {
            providerTypes.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "ProviderType".urlPercentEncoding(), value: Swift.String(queryItemValue.rawValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "MaxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let owners = value.owners {
            owners.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "Owner".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        return items
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension PutRecommendationFeedbackInput {

    static func urlPathProvider(_ value: PutRecommendationFeedbackInput) -> Swift.String? {
        return "/feedback"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension AssociateRepositoryInput {

    static func write(value: AssociateRepositoryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientRequestToken"].write(value.clientRequestToken)
        try writer["KMSKeyDetails"].write(value.kmsKeyDetails, with: CodeGuruReviewerClientTypes.KMSKeyDetails.write(value:to:))
        try writer["Repository"].write(value.repository, with: CodeGuruReviewerClientTypes.Repository.write(value:to:))
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CreateCodeReviewInput {

    static func write(value: CreateCodeReviewInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientRequestToken"].write(value.clientRequestToken)
        try writer["Name"].write(value.name)
        try writer["RepositoryAssociationArn"].write(value.repositoryAssociationArn)
        try writer["Type"].write(value.type, with: CodeGuruReviewerClientTypes.CodeReviewType.write(value:to:))
    }
}

extension PutRecommendationFeedbackInput {

    static func write(value: PutRecommendationFeedbackInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CodeReviewArn"].write(value.codeReviewArn)
        try writer["Reactions"].writeList(value.reactions, memberWritingClosure: SmithyReadWrite.WritingClosureBox<CodeGuruReviewerClientTypes.Reaction>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["RecommendationId"].write(value.recommendationId)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension AssociateRepositoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AssociateRepositoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = AssociateRepositoryOutput()
        value.repositoryAssociation = try reader["RepositoryAssociation"].readIfPresent(with: CodeGuruReviewerClientTypes.RepositoryAssociation.read(from:))
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension CreateCodeReviewOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateCodeReviewOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateCodeReviewOutput()
        value.codeReview = try reader["CodeReview"].readIfPresent(with: CodeGuruReviewerClientTypes.CodeReview.read(from:))
        return value
    }
}

extension DescribeCodeReviewOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeCodeReviewOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeCodeReviewOutput()
        value.codeReview = try reader["CodeReview"].readIfPresent(with: CodeGuruReviewerClientTypes.CodeReview.read(from:))
        return value
    }
}

extension DescribeRecommendationFeedbackOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeRecommendationFeedbackOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeRecommendationFeedbackOutput()
        value.recommendationFeedback = try reader["RecommendationFeedback"].readIfPresent(with: CodeGuruReviewerClientTypes.RecommendationFeedback.read(from:))
        return value
    }
}

extension DescribeRepositoryAssociationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeRepositoryAssociationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeRepositoryAssociationOutput()
        value.repositoryAssociation = try reader["RepositoryAssociation"].readIfPresent(with: CodeGuruReviewerClientTypes.RepositoryAssociation.read(from:))
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DisassociateRepositoryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisassociateRepositoryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DisassociateRepositoryOutput()
        value.repositoryAssociation = try reader["RepositoryAssociation"].readIfPresent(with: CodeGuruReviewerClientTypes.RepositoryAssociation.read(from:))
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension ListCodeReviewsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListCodeReviewsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListCodeReviewsOutput()
        value.codeReviewSummaries = try reader["CodeReviewSummaries"].readListIfPresent(memberReadingClosure: CodeGuruReviewerClientTypes.CodeReviewSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListRecommendationFeedbackOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRecommendationFeedbackOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRecommendationFeedbackOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.recommendationFeedbackSummaries = try reader["RecommendationFeedbackSummaries"].readListIfPresent(memberReadingClosure: CodeGuruReviewerClientTypes.RecommendationFeedbackSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListRecommendationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRecommendationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRecommendationsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.recommendationSummaries = try reader["RecommendationSummaries"].readListIfPresent(memberReadingClosure: CodeGuruReviewerClientTypes.RecommendationSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListRepositoryAssociationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRepositoryAssociationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRepositoryAssociationsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.repositoryAssociationSummaries = try reader["RepositoryAssociationSummaries"].readListIfPresent(memberReadingClosure: CodeGuruReviewerClientTypes.RepositoryAssociationSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension PutRecommendationFeedbackOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutRecommendationFeedbackOutput {
        return PutRecommendationFeedbackOutput()
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

enum AssociateRepositoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateCodeReviewOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeCodeReviewOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeRecommendationFeedbackOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeRepositoryAssociationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "NotFoundException": return try NotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisassociateRepositoryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "NotFoundException": return try NotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListCodeReviewsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRecommendationFeedbackOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRecommendationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRepositoryAssociationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutRecommendationFeedbackOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        var value = ThrottlingException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension NotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> NotFoundException {
        let reader = baseError.errorBodyReader
        var value = NotFoundException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CodeGuruReviewerClientTypes.RepositoryAssociation {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.RepositoryAssociation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.RepositoryAssociation()
        value.associationId = try reader["AssociationId"].readIfPresent()
        value.associationArn = try reader["AssociationArn"].readIfPresent()
        value.connectionArn = try reader["ConnectionArn"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.owner = try reader["Owner"].readIfPresent()
        value.providerType = try reader["ProviderType"].readIfPresent()
        value.state = try reader["State"].readIfPresent()
        value.stateReason = try reader["StateReason"].readIfPresent()
        value.lastUpdatedTimeStamp = try reader["LastUpdatedTimeStamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.createdTimeStamp = try reader["CreatedTimeStamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.kmsKeyDetails = try reader["KMSKeyDetails"].readIfPresent(with: CodeGuruReviewerClientTypes.KMSKeyDetails.read(from:))
        value.s3RepositoryDetails = try reader["S3RepositoryDetails"].readIfPresent(with: CodeGuruReviewerClientTypes.S3RepositoryDetails.read(from:))
        return value
    }
}

extension CodeGuruReviewerClientTypes.S3RepositoryDetails {

    static func write(value: CodeGuruReviewerClientTypes.S3RepositoryDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["BucketName"].write(value.bucketName)
        try writer["CodeArtifacts"].write(value.codeArtifacts, with: CodeGuruReviewerClientTypes.CodeArtifacts.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.S3RepositoryDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.S3RepositoryDetails()
        value.bucketName = try reader["BucketName"].readIfPresent()
        value.codeArtifacts = try reader["CodeArtifacts"].readIfPresent(with: CodeGuruReviewerClientTypes.CodeArtifacts.read(from:))
        return value
    }
}

extension CodeGuruReviewerClientTypes.CodeArtifacts {

    static func write(value: CodeGuruReviewerClientTypes.CodeArtifacts?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["BuildArtifactsObjectKey"].write(value.buildArtifactsObjectKey)
        try writer["SourceCodeArtifactsObjectKey"].write(value.sourceCodeArtifactsObjectKey)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.CodeArtifacts {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.CodeArtifacts()
        value.sourceCodeArtifactsObjectKey = try reader["SourceCodeArtifactsObjectKey"].readIfPresent() ?? ""
        value.buildArtifactsObjectKey = try reader["BuildArtifactsObjectKey"].readIfPresent()
        return value
    }
}

extension CodeGuruReviewerClientTypes.KMSKeyDetails {

    static func write(value: CodeGuruReviewerClientTypes.KMSKeyDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["EncryptionOption"].write(value.encryptionOption)
        try writer["KMSKeyId"].write(value.kmsKeyId)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.KMSKeyDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.KMSKeyDetails()
        value.kmsKeyId = try reader["KMSKeyId"].readIfPresent()
        value.encryptionOption = try reader["EncryptionOption"].readIfPresent()
        return value
    }
}

extension CodeGuruReviewerClientTypes.CodeReview {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.CodeReview {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.CodeReview()
        value.name = try reader["Name"].readIfPresent()
        value.codeReviewArn = try reader["CodeReviewArn"].readIfPresent()
        value.repositoryName = try reader["RepositoryName"].readIfPresent()
        value.owner = try reader["Owner"].readIfPresent()
        value.providerType = try reader["ProviderType"].readIfPresent()
        value.state = try reader["State"].readIfPresent()
        value.stateReason = try reader["StateReason"].readIfPresent()
        value.createdTimeStamp = try reader["CreatedTimeStamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastUpdatedTimeStamp = try reader["LastUpdatedTimeStamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.type = try reader["Type"].readIfPresent()
        value.pullRequestId = try reader["PullRequestId"].readIfPresent()
        value.sourceCodeType = try reader["SourceCodeType"].readIfPresent(with: CodeGuruReviewerClientTypes.SourceCodeType.read(from:))
        value.associationArn = try reader["AssociationArn"].readIfPresent()
        value.metrics = try reader["Metrics"].readIfPresent(with: CodeGuruReviewerClientTypes.Metrics.read(from:))
        value.analysisTypes = try reader["AnalysisTypes"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<CodeGuruReviewerClientTypes.AnalysisType>().read(from:), memberNodeInfo: "member", isFlattened: false)
        value.configFileState = try reader["ConfigFileState"].readIfPresent()
        return value
    }
}

extension CodeGuruReviewerClientTypes.Metrics {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.Metrics {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.Metrics()
        value.meteredLinesOfCodeCount = try reader["MeteredLinesOfCodeCount"].readIfPresent()
        value.suppressedLinesOfCodeCount = try reader["SuppressedLinesOfCodeCount"].readIfPresent()
        value.findingsCount = try reader["FindingsCount"].readIfPresent()
        return value
    }
}

extension CodeGuruReviewerClientTypes.SourceCodeType {

    static func write(value: CodeGuruReviewerClientTypes.SourceCodeType?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["BranchDiff"].write(value.branchDiff, with: CodeGuruReviewerClientTypes.BranchDiffSourceCodeType.write(value:to:))
        try writer["CommitDiff"].write(value.commitDiff, with: CodeGuruReviewerClientTypes.CommitDiffSourceCodeType.write(value:to:))
        try writer["RepositoryHead"].write(value.repositoryHead, with: CodeGuruReviewerClientTypes.RepositoryHeadSourceCodeType.write(value:to:))
        try writer["RequestMetadata"].write(value.requestMetadata, with: CodeGuruReviewerClientTypes.RequestMetadata.write(value:to:))
        try writer["S3BucketRepository"].write(value.s3BucketRepository, with: CodeGuruReviewerClientTypes.S3BucketRepository.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.SourceCodeType {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.SourceCodeType()
        value.commitDiff = try reader["CommitDiff"].readIfPresent(with: CodeGuruReviewerClientTypes.CommitDiffSourceCodeType.read(from:))
        value.repositoryHead = try reader["RepositoryHead"].readIfPresent(with: CodeGuruReviewerClientTypes.RepositoryHeadSourceCodeType.read(from:))
        value.branchDiff = try reader["BranchDiff"].readIfPresent(with: CodeGuruReviewerClientTypes.BranchDiffSourceCodeType.read(from:))
        value.s3BucketRepository = try reader["S3BucketRepository"].readIfPresent(with: CodeGuruReviewerClientTypes.S3BucketRepository.read(from:))
        value.requestMetadata = try reader["RequestMetadata"].readIfPresent(with: CodeGuruReviewerClientTypes.RequestMetadata.read(from:))
        return value
    }
}

extension CodeGuruReviewerClientTypes.RequestMetadata {

    static func write(value: CodeGuruReviewerClientTypes.RequestMetadata?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["EventInfo"].write(value.eventInfo, with: CodeGuruReviewerClientTypes.EventInfo.write(value:to:))
        try writer["RequestId"].write(value.requestId)
        try writer["Requester"].write(value.requester)
        try writer["VendorName"].write(value.vendorName)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.RequestMetadata {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.RequestMetadata()
        value.requestId = try reader["RequestId"].readIfPresent()
        value.requester = try reader["Requester"].readIfPresent()
        value.eventInfo = try reader["EventInfo"].readIfPresent(with: CodeGuruReviewerClientTypes.EventInfo.read(from:))
        value.vendorName = try reader["VendorName"].readIfPresent()
        return value
    }
}

extension CodeGuruReviewerClientTypes.EventInfo {

    static func write(value: CodeGuruReviewerClientTypes.EventInfo?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
        try writer["State"].write(value.state)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.EventInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.EventInfo()
        value.name = try reader["Name"].readIfPresent()
        value.state = try reader["State"].readIfPresent()
        return value
    }
}

extension CodeGuruReviewerClientTypes.S3BucketRepository {

    static func write(value: CodeGuruReviewerClientTypes.S3BucketRepository?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Details"].write(value.details, with: CodeGuruReviewerClientTypes.S3RepositoryDetails.write(value:to:))
        try writer["Name"].write(value.name)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.S3BucketRepository {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.S3BucketRepository()
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.details = try reader["Details"].readIfPresent(with: CodeGuruReviewerClientTypes.S3RepositoryDetails.read(from:))
        return value
    }
}

extension CodeGuruReviewerClientTypes.BranchDiffSourceCodeType {

    static func write(value: CodeGuruReviewerClientTypes.BranchDiffSourceCodeType?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DestinationBranchName"].write(value.destinationBranchName)
        try writer["SourceBranchName"].write(value.sourceBranchName)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.BranchDiffSourceCodeType {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.BranchDiffSourceCodeType()
        value.sourceBranchName = try reader["SourceBranchName"].readIfPresent() ?? ""
        value.destinationBranchName = try reader["DestinationBranchName"].readIfPresent() ?? ""
        return value
    }
}

extension CodeGuruReviewerClientTypes.RepositoryHeadSourceCodeType {

    static func write(value: CodeGuruReviewerClientTypes.RepositoryHeadSourceCodeType?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["BranchName"].write(value.branchName)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.RepositoryHeadSourceCodeType {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.RepositoryHeadSourceCodeType()
        value.branchName = try reader["BranchName"].readIfPresent() ?? ""
        return value
    }
}

extension CodeGuruReviewerClientTypes.CommitDiffSourceCodeType {

    static func write(value: CodeGuruReviewerClientTypes.CommitDiffSourceCodeType?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DestinationCommit"].write(value.destinationCommit)
        try writer["MergeBaseCommit"].write(value.mergeBaseCommit)
        try writer["SourceCommit"].write(value.sourceCommit)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.CommitDiffSourceCodeType {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.CommitDiffSourceCodeType()
        value.sourceCommit = try reader["SourceCommit"].readIfPresent()
        value.destinationCommit = try reader["DestinationCommit"].readIfPresent()
        value.mergeBaseCommit = try reader["MergeBaseCommit"].readIfPresent()
        return value
    }
}

extension CodeGuruReviewerClientTypes.RecommendationFeedback {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.RecommendationFeedback {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.RecommendationFeedback()
        value.codeReviewArn = try reader["CodeReviewArn"].readIfPresent()
        value.recommendationId = try reader["RecommendationId"].readIfPresent()
        value.reactions = try reader["Reactions"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<CodeGuruReviewerClientTypes.Reaction>().read(from:), memberNodeInfo: "member", isFlattened: false)
        value.userId = try reader["UserId"].readIfPresent()
        value.createdTimeStamp = try reader["CreatedTimeStamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastUpdatedTimeStamp = try reader["LastUpdatedTimeStamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension CodeGuruReviewerClientTypes.CodeReviewSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.CodeReviewSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.CodeReviewSummary()
        value.name = try reader["Name"].readIfPresent()
        value.codeReviewArn = try reader["CodeReviewArn"].readIfPresent()
        value.repositoryName = try reader["RepositoryName"].readIfPresent()
        value.owner = try reader["Owner"].readIfPresent()
        value.providerType = try reader["ProviderType"].readIfPresent()
        value.state = try reader["State"].readIfPresent()
        value.createdTimeStamp = try reader["CreatedTimeStamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastUpdatedTimeStamp = try reader["LastUpdatedTimeStamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.type = try reader["Type"].readIfPresent()
        value.pullRequestId = try reader["PullRequestId"].readIfPresent()
        value.metricsSummary = try reader["MetricsSummary"].readIfPresent(with: CodeGuruReviewerClientTypes.MetricsSummary.read(from:))
        value.sourceCodeType = try reader["SourceCodeType"].readIfPresent(with: CodeGuruReviewerClientTypes.SourceCodeType.read(from:))
        return value
    }
}

extension CodeGuruReviewerClientTypes.MetricsSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.MetricsSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.MetricsSummary()
        value.meteredLinesOfCodeCount = try reader["MeteredLinesOfCodeCount"].readIfPresent()
        value.suppressedLinesOfCodeCount = try reader["SuppressedLinesOfCodeCount"].readIfPresent()
        value.findingsCount = try reader["FindingsCount"].readIfPresent()
        return value
    }
}

extension CodeGuruReviewerClientTypes.RecommendationFeedbackSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.RecommendationFeedbackSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.RecommendationFeedbackSummary()
        value.recommendationId = try reader["RecommendationId"].readIfPresent()
        value.reactions = try reader["Reactions"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<CodeGuruReviewerClientTypes.Reaction>().read(from:), memberNodeInfo: "member", isFlattened: false)
        value.userId = try reader["UserId"].readIfPresent()
        return value
    }
}

extension CodeGuruReviewerClientTypes.RecommendationSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.RecommendationSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.RecommendationSummary()
        value.filePath = try reader["FilePath"].readIfPresent()
        value.recommendationId = try reader["RecommendationId"].readIfPresent()
        value.startLine = try reader["StartLine"].readIfPresent()
        value.endLine = try reader["EndLine"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.recommendationCategory = try reader["RecommendationCategory"].readIfPresent()
        value.ruleMetadata = try reader["RuleMetadata"].readIfPresent(with: CodeGuruReviewerClientTypes.RuleMetadata.read(from:))
        value.severity = try reader["Severity"].readIfPresent()
        return value
    }
}

extension CodeGuruReviewerClientTypes.RuleMetadata {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.RuleMetadata {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.RuleMetadata()
        value.ruleId = try reader["RuleId"].readIfPresent()
        value.ruleName = try reader["RuleName"].readIfPresent()
        value.shortDescription = try reader["ShortDescription"].readIfPresent()
        value.longDescription = try reader["LongDescription"].readIfPresent()
        value.ruleTags = try reader["RuleTags"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension CodeGuruReviewerClientTypes.RepositoryAssociationSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeGuruReviewerClientTypes.RepositoryAssociationSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeGuruReviewerClientTypes.RepositoryAssociationSummary()
        value.associationArn = try reader["AssociationArn"].readIfPresent()
        value.connectionArn = try reader["ConnectionArn"].readIfPresent()
        value.lastUpdatedTimeStamp = try reader["LastUpdatedTimeStamp"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.associationId = try reader["AssociationId"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.owner = try reader["Owner"].readIfPresent()
        value.providerType = try reader["ProviderType"].readIfPresent()
        value.state = try reader["State"].readIfPresent()
        return value
    }
}

extension CodeGuruReviewerClientTypes.Repository {

    static func write(value: CodeGuruReviewerClientTypes.Repository?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Bitbucket"].write(value.bitbucket, with: CodeGuruReviewerClientTypes.ThirdPartySourceRepository.write(value:to:))
        try writer["CodeCommit"].write(value.codeCommit, with: CodeGuruReviewerClientTypes.CodeCommitRepository.write(value:to:))
        try writer["GitHubEnterpriseServer"].write(value.gitHubEnterpriseServer, with: CodeGuruReviewerClientTypes.ThirdPartySourceRepository.write(value:to:))
        try writer["S3Bucket"].write(value.s3Bucket, with: CodeGuruReviewerClientTypes.S3Repository.write(value:to:))
    }
}

extension CodeGuruReviewerClientTypes.S3Repository {

    static func write(value: CodeGuruReviewerClientTypes.S3Repository?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["BucketName"].write(value.bucketName)
        try writer["Name"].write(value.name)
    }
}

extension CodeGuruReviewerClientTypes.ThirdPartySourceRepository {

    static func write(value: CodeGuruReviewerClientTypes.ThirdPartySourceRepository?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ConnectionArn"].write(value.connectionArn)
        try writer["Name"].write(value.name)
        try writer["Owner"].write(value.owner)
    }
}

extension CodeGuruReviewerClientTypes.CodeCommitRepository {

    static func write(value: CodeGuruReviewerClientTypes.CodeCommitRepository?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
    }
}

extension CodeGuruReviewerClientTypes.CodeReviewType {

    static func write(value: CodeGuruReviewerClientTypes.CodeReviewType?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AnalysisTypes"].writeList(value.analysisTypes, memberWritingClosure: SmithyReadWrite.WritingClosureBox<CodeGuruReviewerClientTypes.AnalysisType>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["RepositoryAnalysis"].write(value.repositoryAnalysis, with: CodeGuruReviewerClientTypes.RepositoryAnalysis.write(value:to:))
    }
}

extension CodeGuruReviewerClientTypes.RepositoryAnalysis {

    static func write(value: CodeGuruReviewerClientTypes.RepositoryAnalysis?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["RepositoryHead"].write(value.repositoryHead, with: CodeGuruReviewerClientTypes.RepositoryHeadSourceCodeType.write(value:to:))
        try writer["SourceCodeType"].write(value.sourceCodeType, with: CodeGuruReviewerClientTypes.SourceCodeType.write(value:to:))
    }
}

public enum CodeGuruReviewerClientTypes {}
