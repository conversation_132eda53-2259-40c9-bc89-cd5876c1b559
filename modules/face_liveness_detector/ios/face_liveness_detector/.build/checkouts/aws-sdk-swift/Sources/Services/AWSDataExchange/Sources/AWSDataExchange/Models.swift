//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.<PERSON>y<PERSON>eader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
import struct SmithyHTTPAPI.Header
import struct SmithyHTTPAPI.Headers
@_spi(SmithyReadWrite) import struct SmithyReadWrite.ReadingClosureBox
@_spi(SmithyReadWrite) import struct SmithyReadWrite.WritingClosureBox
@_spi(SmithyTimestamps) import struct SmithyTimestamps.TimestampFormatter


public struct CancelJobOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteAssetOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteDataGrantOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteDataSetOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteEventActionOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteRevisionOutput: Swift.Sendable {

    public init() { }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

extension DataExchangeClientTypes {

    public enum AcceptanceStateFilterValue: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case accepted
        case pendingReceiverAcceptance
        case sdkUnknown(Swift.String)

        public static var allCases: [AcceptanceStateFilterValue] {
            return [
                .accepted,
                .pendingReceiverAcceptance
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .accepted: return "ACCEPTED"
            case .pendingReceiverAcceptance: return "PENDING_RECEIVER_ACCEPTANCE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Access to the resource is denied.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Access to the resource is denied.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension DataExchangeClientTypes {

    public enum ResourceType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case asset
        case dataGrant
        case dataSet
        case eventAction
        case job
        case revision
        case sdkUnknown(Swift.String)

        public static var allCases: [ResourceType] {
            return [
                .asset,
                .dataGrant,
                .dataSet,
                .eventAction,
                .job,
                .revision
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .asset: return "ASSET"
            case .dataGrant: return "DATA_GRANT"
            case .dataSet: return "DATA_SET"
            case .eventAction: return "EVENT_ACTION"
            case .job: return "JOB"
            case .revision: return "REVISION"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The request couldn't be completed because it conflicted with the current state of the resource.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The request couldn't be completed because it conflicted with the current state of the resource.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The unique identifier for the resource with the conflict.
        public internal(set) var resourceId: Swift.String? = nil
        /// The type of the resource with the conflict.
        public internal(set) var resourceType: DataExchangeClientTypes.ResourceType? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: DataExchangeClientTypes.ResourceType? = nil
    )
    {
        self.properties.message = message
        self.properties.resourceId = resourceId
        self.properties.resourceType = resourceType
    }
}

/// An exception occurred with the service.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message identifying the service exception that occurred.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The resource couldn't be found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The resource couldn't be found.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The unique identifier for the resource that couldn't be found.
        public internal(set) var resourceId: Swift.String? = nil
        /// The type of resource that couldn't be found.
        public internal(set) var resourceType: DataExchangeClientTypes.ResourceType? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: DataExchangeClientTypes.ResourceType? = nil
    )
    {
        self.properties.message = message
        self.properties.resourceId = resourceId
        self.properties.resourceType = resourceType
    }
}

/// The limit on the number of requests per second was exceeded.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The limit on the number of requests per second was exceeded.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension DataExchangeClientTypes {

    public enum ExceptionCause: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case insufficients3bucketpolicy
        case s3accessdenied
        case sdkUnknown(Swift.String)

        public static var allCases: [ExceptionCause] {
            return [
                .insufficients3bucketpolicy,
                .s3accessdenied
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .insufficients3bucketpolicy: return "InsufficientS3BucketPolicy"
            case .s3accessdenied: return "S3AccessDenied"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The request was invalid.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The unique identifier for the resource that couldn't be found.
        public internal(set) var exceptionCause: DataExchangeClientTypes.ExceptionCause? = nil
        /// The message that informs you about what was invalid about the request.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        exceptionCause: DataExchangeClientTypes.ExceptionCause? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.exceptionCause = exceptionCause
        self.properties.message = message
    }
}

public struct AcceptDataGrantInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the data grant to accept.
    /// This member is required.
    public var dataGrantArn: Swift.String?

    public init(
        dataGrantArn: Swift.String? = nil
    )
    {
        self.dataGrantArn = dataGrantArn
    }
}

extension DataExchangeClientTypes {

    public enum DataGrantAcceptanceState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case accepted
        case pendingReceiverAcceptance
        case sdkUnknown(Swift.String)

        public static var allCases: [DataGrantAcceptanceState] {
            return [
                .accepted,
                .pendingReceiverAcceptance
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .accepted: return "ACCEPTED"
            case .pendingReceiverAcceptance: return "PENDING_RECEIVER_ACCEPTANCE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataExchangeClientTypes {

    public enum GrantDistributionScope: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case awsOrganization
        case `none`
        case sdkUnknown(Swift.String)

        public static var allCases: [GrantDistributionScope] {
            return [
                .awsOrganization,
                .none
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .awsOrganization: return "AWS_ORGANIZATION"
            case .none: return "NONE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct AcceptDataGrantOutput: Swift.Sendable {
    /// The acceptance state of the data grant.
    /// This member is required.
    public var acceptanceState: DataExchangeClientTypes.DataGrantAcceptanceState?
    /// The timestamp of when the data grant was accepted.
    public var acceptedAt: Foundation.Date?
    /// The Amazon Resource Name (ARN) of the accepted data grant.
    /// This member is required.
    public var arn: Swift.String?
    /// The timestamp of when the data grant was created.
    /// This member is required.
    public var createdAt: Foundation.Date?
    /// The ID of the data set associated to the data grant.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// The description of the accepted data grant.
    public var description: Swift.String?
    /// The timestamp of when access to the associated data set ends.
    public var endsAt: Foundation.Date?
    /// The distribution scope for the data grant.
    /// This member is required.
    public var grantDistributionScope: DataExchangeClientTypes.GrantDistributionScope?
    /// The ID of the data grant.
    /// This member is required.
    public var id: Swift.String?
    /// The name of the accepted data grant.
    /// This member is required.
    public var name: Swift.String?
    /// The Amazon Web Services account ID of the data grant receiver.
    /// This member is required.
    public var receiverPrincipal: Swift.String?
    /// The Amazon Web Services account ID of the data grant sender.
    public var senderPrincipal: Swift.String?
    /// The timestamp of when the data grant was last updated.
    /// This member is required.
    public var updatedAt: Foundation.Date?

    public init(
        acceptanceState: DataExchangeClientTypes.DataGrantAcceptanceState? = nil,
        acceptedAt: Foundation.Date? = nil,
        arn: Swift.String? = nil,
        createdAt: Foundation.Date? = nil,
        dataSetId: Swift.String? = nil,
        description: Swift.String? = nil,
        endsAt: Foundation.Date? = nil,
        grantDistributionScope: DataExchangeClientTypes.GrantDistributionScope? = nil,
        id: Swift.String? = nil,
        name: Swift.String? = nil,
        receiverPrincipal: Swift.String? = nil,
        senderPrincipal: Swift.String? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.acceptanceState = acceptanceState
        self.acceptedAt = acceptedAt
        self.arn = arn
        self.createdAt = createdAt
        self.dataSetId = dataSetId
        self.description = description
        self.endsAt = endsAt
        self.grantDistributionScope = grantDistributionScope
        self.id = id
        self.name = name
        self.receiverPrincipal = receiverPrincipal
        self.senderPrincipal = senderPrincipal
        self.updatedAt = updatedAt
    }
}

extension DataExchangeClientTypes {

    public enum ServerSideEncryptionTypes: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case aes256
        case awsKms
        case sdkUnknown(Swift.String)

        public static var allCases: [ServerSideEncryptionTypes] {
            return [
                .aes256,
                .awsKms
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .aes256: return "AES256"
            case .awsKms: return "aws:kms"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataExchangeClientTypes {

    /// Encryption configuration of the export job. Includes the encryption type in addition to the AWS KMS key. The KMS key is only necessary if you chose the KMS encryption type.
    public struct ExportServerSideEncryption: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the AWS KMS key you want to use to encrypt the Amazon S3 objects. This parameter is required if you choose aws:kms as an encryption type.
        public var kmsKeyArn: Swift.String?
        /// The type of server side encryption used for encrypting the objects in Amazon S3.
        /// This member is required.
        public var type: DataExchangeClientTypes.ServerSideEncryptionTypes?

        public init(
            kmsKeyArn: Swift.String? = nil,
            type: DataExchangeClientTypes.ServerSideEncryptionTypes? = nil
        )
        {
            self.kmsKeyArn = kmsKeyArn
            self.type = type
        }
    }
}

extension DataExchangeClientTypes {

    /// A revision destination is the Amazon S3 bucket folder destination to where the export will be sent.
    public struct AutoExportRevisionDestinationEntry: Swift.Sendable {
        /// The Amazon S3 bucket that is the destination for the event action.
        /// This member is required.
        public var bucket: Swift.String?
        /// A string representing the pattern for generated names of the individual assets in the revision. For more information about key patterns, see [Key patterns when exporting revisions](https://docs.aws.amazon.com/data-exchange/latest/userguide/jobs.html#revision-export-keypatterns).
        public var keyPattern: Swift.String?

        public init(
            bucket: Swift.String? = nil,
            keyPattern: Swift.String? = nil
        )
        {
            self.bucket = bucket
            self.keyPattern = keyPattern
        }
    }
}

extension DataExchangeClientTypes {

    /// Details of the operation to be performed by the job.
    public struct AutoExportRevisionToS3RequestDetails: Swift.Sendable {
        /// Encryption configuration for the auto export job.
        public var encryption: DataExchangeClientTypes.ExportServerSideEncryption?
        /// A revision destination is the Amazon S3 bucket folder destination to where the export will be sent.
        /// This member is required.
        public var revisionDestination: DataExchangeClientTypes.AutoExportRevisionDestinationEntry?

        public init(
            encryption: DataExchangeClientTypes.ExportServerSideEncryption? = nil,
            revisionDestination: DataExchangeClientTypes.AutoExportRevisionDestinationEntry? = nil
        )
        {
            self.encryption = encryption
            self.revisionDestination = revisionDestination
        }
    }
}

extension DataExchangeClientTypes {

    /// What occurs after a certain event.
    public struct Action: Swift.Sendable {
        /// Details for the export revision to Amazon S3 action.
        public var exportRevisionToS3: DataExchangeClientTypes.AutoExportRevisionToS3RequestDetails?

        public init(
            exportRevisionToS3: DataExchangeClientTypes.AutoExportRevisionToS3RequestDetails? = nil
        )
        {
            self.exportRevisionToS3 = exportRevisionToS3
        }
    }
}

extension DataExchangeClientTypes {

    public enum ProtocolType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case rest
        case sdkUnknown(Swift.String)

        public static var allCases: [ProtocolType] {
            return [
                .rest
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .rest: return "REST"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataExchangeClientTypes {

    /// The API Gateway API that is the asset.
    public struct ApiGatewayApiAsset: Swift.Sendable {
        /// The API description of the API asset.
        public var apiDescription: Swift.String?
        /// The API endpoint of the API asset.
        public var apiEndpoint: Swift.String?
        /// The unique identifier of the API asset.
        public var apiId: Swift.String?
        /// The API key of the API asset.
        public var apiKey: Swift.String?
        /// The API name of the API asset.
        public var apiName: Swift.String?
        /// The download URL of the API specification of the API asset.
        public var apiSpecificationDownloadUrl: Swift.String?
        /// The date and time that the upload URL expires, in ISO 8601 format.
        public var apiSpecificationDownloadUrlExpiresAt: Foundation.Date?
        /// The protocol type of the API asset.
        public var protocolType: DataExchangeClientTypes.ProtocolType?
        /// The stage of the API asset.
        public var stage: Swift.String?

        public init(
            apiDescription: Swift.String? = nil,
            apiEndpoint: Swift.String? = nil,
            apiId: Swift.String? = nil,
            apiKey: Swift.String? = nil,
            apiName: Swift.String? = nil,
            apiSpecificationDownloadUrl: Swift.String? = nil,
            apiSpecificationDownloadUrlExpiresAt: Foundation.Date? = nil,
            protocolType: DataExchangeClientTypes.ProtocolType? = nil,
            stage: Swift.String? = nil
        )
        {
            self.apiDescription = apiDescription
            self.apiEndpoint = apiEndpoint
            self.apiId = apiId
            self.apiKey = apiKey
            self.apiName = apiName
            self.apiSpecificationDownloadUrl = apiSpecificationDownloadUrl
            self.apiSpecificationDownloadUrlExpiresAt = apiSpecificationDownloadUrlExpiresAt
            self.protocolType = protocolType
            self.stage = stage
        }
    }
}

extension DataExchangeClientTypes {

    /// The destination for the asset.
    public struct AssetDestinationEntry: Swift.Sendable {
        /// The unique identifier for the asset.
        /// This member is required.
        public var assetId: Swift.String?
        /// The Amazon S3 bucket that is the destination for the asset.
        /// This member is required.
        public var bucket: Swift.String?
        /// The name of the object in Amazon S3 for the asset.
        public var key: Swift.String?

        public init(
            assetId: Swift.String? = nil,
            bucket: Swift.String? = nil,
            key: Swift.String? = nil
        )
        {
            self.assetId = assetId
            self.bucket = bucket
            self.key = key
        }
    }
}

extension DataExchangeClientTypes {

    /// A structure that allows an LF-admin to grant permissions on certain conditions.
    public struct LFTag: Swift.Sendable {
        /// The key name for the LF-tag.
        /// This member is required.
        public var tagKey: Swift.String?
        /// A list of LF-tag values.
        /// This member is required.
        public var tagValues: [Swift.String]?

        public init(
            tagKey: Swift.String? = nil,
            tagValues: [Swift.String]? = nil
        )
        {
            self.tagKey = tagKey
            self.tagValues = tagValues
        }
    }
}

extension DataExchangeClientTypes {

    /// The LF-tag policy for database resources.
    public struct DatabaseLFTagPolicy: Swift.Sendable {
        /// A list of LF-tag conditions that apply to database resources.
        /// This member is required.
        public var expression: [DataExchangeClientTypes.LFTag]?

        public init(
            expression: [DataExchangeClientTypes.LFTag]? = nil
        )
        {
            self.expression = expression
        }
    }
}

extension DataExchangeClientTypes {

    /// The LF-tag policy for a table resource.
    public struct TableLFTagPolicy: Swift.Sendable {
        /// A list of LF-tag conditions that apply to table resources.
        /// This member is required.
        public var expression: [DataExchangeClientTypes.LFTag]?

        public init(
            expression: [DataExchangeClientTypes.LFTag]? = nil
        )
        {
            self.expression = expression
        }
    }
}

extension DataExchangeClientTypes {

    /// Details about the AWS Lake Formation resource (Table or Database) included in the AWS Lake Formation data permission.
    public struct LFResourceDetails: Swift.Sendable {
        /// Details about the database resource included in the AWS Lake Formation data permission.
        public var database: DataExchangeClientTypes.DatabaseLFTagPolicy?
        /// Details about the table resource included in the AWS Lake Formation data permission.
        public var table: DataExchangeClientTypes.TableLFTagPolicy?

        public init(
            database: DataExchangeClientTypes.DatabaseLFTagPolicy? = nil,
            table: DataExchangeClientTypes.TableLFTagPolicy? = nil
        )
        {
            self.database = database
            self.table = table
        }
    }
}

extension DataExchangeClientTypes {

    public enum LFResourceType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case database
        case table
        case sdkUnknown(Swift.String)

        public static var allCases: [LFResourceType] {
            return [
                .database,
                .table
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .database: return "DATABASE"
            case .table: return "TABLE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataExchangeClientTypes {

    /// Details about the LF-tag policy.
    public struct LFTagPolicyDetails: Swift.Sendable {
        /// The identifier for the AWS Glue Data Catalog.
        /// This member is required.
        public var catalogId: Swift.String?
        /// Details for the Lake Formation Resources included in the LF-tag policy.
        /// This member is required.
        public var resourceDetails: DataExchangeClientTypes.LFResourceDetails?
        /// The resource type for which the LF-tag policy applies.
        /// This member is required.
        public var resourceType: DataExchangeClientTypes.LFResourceType?

        public init(
            catalogId: Swift.String? = nil,
            resourceDetails: DataExchangeClientTypes.LFResourceDetails? = nil,
            resourceType: DataExchangeClientTypes.LFResourceType? = nil
        )
        {
            self.catalogId = catalogId
            self.resourceDetails = resourceDetails
            self.resourceType = resourceType
        }
    }
}

extension DataExchangeClientTypes {

    /// Details about the AWS Lake Formation data permission.
    public struct LakeFormationDataPermissionDetails: Swift.Sendable {
        /// Details about the LF-tag policy.
        public var lfTagPolicy: DataExchangeClientTypes.LFTagPolicyDetails?

        public init(
            lfTagPolicy: DataExchangeClientTypes.LFTagPolicyDetails? = nil
        )
        {
            self.lfTagPolicy = lfTagPolicy
        }
    }
}

extension DataExchangeClientTypes {

    public enum LakeFormationDataPermissionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case lftagpolicy
        case sdkUnknown(Swift.String)

        public static var allCases: [LakeFormationDataPermissionType] {
            return [
                .lftagpolicy
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .lftagpolicy: return "LFTagPolicy"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataExchangeClientTypes {

    public enum LFPermission: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case describe
        case select
        case sdkUnknown(Swift.String)

        public static var allCases: [LFPermission] {
            return [
                .describe,
                .select
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .describe: return "DESCRIBE"
            case .select: return "SELECT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataExchangeClientTypes {

    /// The AWS Lake Formation data permission asset.
    public struct LakeFormationDataPermissionAsset: Swift.Sendable {
        /// Details about the AWS Lake Formation data permission.
        /// This member is required.
        public var lakeFormationDataPermissionDetails: DataExchangeClientTypes.LakeFormationDataPermissionDetails?
        /// The data permission type.
        /// This member is required.
        public var lakeFormationDataPermissionType: DataExchangeClientTypes.LakeFormationDataPermissionType?
        /// The permissions granted to the subscribers on the resource.
        /// This member is required.
        public var permissions: [DataExchangeClientTypes.LFPermission]?
        /// The IAM role's ARN that allows AWS Data Exchange to assume the role and grant and revoke permissions to AWS Lake Formation data permissions.
        public var roleArn: Swift.String?

        public init(
            lakeFormationDataPermissionDetails: DataExchangeClientTypes.LakeFormationDataPermissionDetails? = nil,
            lakeFormationDataPermissionType: DataExchangeClientTypes.LakeFormationDataPermissionType? = nil,
            permissions: [DataExchangeClientTypes.LFPermission]? = nil,
            roleArn: Swift.String? = nil
        )
        {
            self.lakeFormationDataPermissionDetails = lakeFormationDataPermissionDetails
            self.lakeFormationDataPermissionType = lakeFormationDataPermissionType
            self.permissions = permissions
            self.roleArn = roleArn
        }
    }
}

extension DataExchangeClientTypes {

    /// The Amazon Redshift datashare asset.
    public struct RedshiftDataShareAsset: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the datashare asset.
        /// This member is required.
        public var arn: Swift.String?

        public init(
            arn: Swift.String? = nil
        )
        {
            self.arn = arn
        }
    }
}

extension DataExchangeClientTypes {

    /// The Amazon Resource Name (ARN) of the AWS KMS key used to encrypt the shared S3 objects.
    public struct KmsKeyToGrant: Swift.Sendable {
        /// The AWS KMS CMK (Key Management System Customer Managed Key) used to encrypt S3 objects in the shared S3 Bucket. AWS Data exchange will create a KMS grant for each subscriber to allow them to access and decrypt their entitled data that is encrypted using this KMS key specified.
        /// This member is required.
        public var kmsKeyArn: Swift.String?

        public init(
            kmsKeyArn: Swift.String? = nil
        )
        {
            self.kmsKeyArn = kmsKeyArn
        }
    }
}

extension DataExchangeClientTypes {

    /// The Amazon S3 data access that is the asset.
    public struct S3DataAccessAsset: Swift.Sendable {
        /// The Amazon S3 bucket hosting data to be shared in the S3 data access.
        /// This member is required.
        public var bucket: Swift.String?
        /// The Amazon S3 bucket used for hosting shared data in the Amazon S3 data access.
        public var keyPrefixes: [Swift.String]?
        /// S3 keys made available using this asset.
        public var keys: [Swift.String]?
        /// List of AWS KMS CMKs (Key Management System Customer Managed Keys) and ARNs used to encrypt S3 objects being shared in this S3 Data Access asset. Providers must include all AWS KMS keys used to encrypt these shared S3 objects.
        public var kmsKeysToGrant: [DataExchangeClientTypes.KmsKeyToGrant]?
        /// The automatically-generated bucket-style alias for your Amazon S3 Access Point. Customers can access their entitled data using the S3 Access Point alias.
        public var s3AccessPointAlias: Swift.String?
        /// The ARN for your Amazon S3 Access Point. Customers can also access their entitled data using the S3 Access Point ARN.
        public var s3AccessPointArn: Swift.String?

        public init(
            bucket: Swift.String? = nil,
            keyPrefixes: [Swift.String]? = nil,
            keys: [Swift.String]? = nil,
            kmsKeysToGrant: [DataExchangeClientTypes.KmsKeyToGrant]? = nil,
            s3AccessPointAlias: Swift.String? = nil,
            s3AccessPointArn: Swift.String? = nil
        )
        {
            self.bucket = bucket
            self.keyPrefixes = keyPrefixes
            self.keys = keys
            self.kmsKeysToGrant = kmsKeysToGrant
            self.s3AccessPointAlias = s3AccessPointAlias
            self.s3AccessPointArn = s3AccessPointArn
        }
    }
}

extension DataExchangeClientTypes {

    /// The Amazon S3 object that is the asset.
    public struct S3SnapshotAsset: Swift.Sendable {
        /// The size of the Amazon S3 object that is the object.
        /// This member is required.
        public var size: Swift.Double?

        public init(
            size: Swift.Double? = 0.0
        )
        {
            self.size = size
        }
    }
}

extension DataExchangeClientTypes {

    /// Details about the asset.
    public struct AssetDetails: Swift.Sendable {
        /// Information about the API Gateway API asset.
        public var apiGatewayApiAsset: DataExchangeClientTypes.ApiGatewayApiAsset?
        /// The AWS Lake Formation data permission that is the asset.
        public var lakeFormationDataPermissionAsset: DataExchangeClientTypes.LakeFormationDataPermissionAsset?
        /// The Amazon Redshift datashare that is the asset.
        public var redshiftDataShareAsset: DataExchangeClientTypes.RedshiftDataShareAsset?
        /// The Amazon S3 data access that is the asset.
        public var s3DataAccessAsset: DataExchangeClientTypes.S3DataAccessAsset?
        /// The Amazon S3 object that is the asset.
        public var s3SnapshotAsset: DataExchangeClientTypes.S3SnapshotAsset?

        public init(
            apiGatewayApiAsset: DataExchangeClientTypes.ApiGatewayApiAsset? = nil,
            lakeFormationDataPermissionAsset: DataExchangeClientTypes.LakeFormationDataPermissionAsset? = nil,
            redshiftDataShareAsset: DataExchangeClientTypes.RedshiftDataShareAsset? = nil,
            s3DataAccessAsset: DataExchangeClientTypes.S3DataAccessAsset? = nil,
            s3SnapshotAsset: DataExchangeClientTypes.S3SnapshotAsset? = nil
        )
        {
            self.apiGatewayApiAsset = apiGatewayApiAsset
            self.lakeFormationDataPermissionAsset = lakeFormationDataPermissionAsset
            self.redshiftDataShareAsset = redshiftDataShareAsset
            self.s3DataAccessAsset = s3DataAccessAsset
            self.s3SnapshotAsset = s3SnapshotAsset
        }
    }
}

extension DataExchangeClientTypes {

    public enum AssetType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case apiGatewayApi
        case lakeFormationDataPermission
        case redshiftDataShare
        case s3DataAccess
        case s3Snapshot
        case sdkUnknown(Swift.String)

        public static var allCases: [AssetType] {
            return [
                .apiGatewayApi,
                .lakeFormationDataPermission,
                .redshiftDataShare,
                .s3DataAccess,
                .s3Snapshot
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .apiGatewayApi: return "API_GATEWAY_API"
            case .lakeFormationDataPermission: return "LAKE_FORMATION_DATA_PERMISSION"
            case .redshiftDataShare: return "REDSHIFT_DATA_SHARE"
            case .s3DataAccess: return "S3_DATA_ACCESS"
            case .s3Snapshot: return "S3_SNAPSHOT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataExchangeClientTypes {

    /// An asset in AWS Data Exchange is a piece of data (Amazon S3 object) or a means of fulfilling data (Amazon Redshift datashare or Amazon API Gateway API, AWS Lake Formation data permission, or Amazon S3 data access). The asset can be a structured data file, an image file, or some other data file that can be stored as an Amazon S3 object, an Amazon API Gateway API, or an Amazon Redshift datashare, an AWS Lake Formation data permission, or an Amazon S3 data access. When you create an import job for your files, API Gateway APIs, Amazon Redshift datashares, AWS Lake Formation data permission, or Amazon S3 data access, you create an asset in AWS Data Exchange.
    public struct AssetEntry: Swift.Sendable {
        /// The ARN for the asset.
        /// This member is required.
        public var arn: Swift.String?
        /// Details about the asset.
        /// This member is required.
        public var assetDetails: DataExchangeClientTypes.AssetDetails?
        /// The type of asset that is added to a data set.
        /// This member is required.
        public var assetType: DataExchangeClientTypes.AssetType?
        /// The date and time that the asset was created, in ISO 8601 format.
        /// This member is required.
        public var createdAt: Foundation.Date?
        /// The unique identifier for the data set associated with this asset.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The unique identifier for the asset.
        /// This member is required.
        public var id: Swift.String?
        /// The name of the asset. When importing from Amazon S3, the Amazon S3 object key is used as the asset name. When exporting to Amazon S3, the asset name is used as default target Amazon S3 object key. When importing from Amazon API Gateway API, the API name is used as the asset name. When importing from Amazon Redshift, the datashare name is used as the asset name. When importing from AWS Lake Formation, the static values of "Database(s) included in LF-tag policy" or "Table(s) included in LF-tag policy" are used as the asset name.
        /// This member is required.
        public var name: Swift.String?
        /// The unique identifier for the revision associated with this asset.
        /// This member is required.
        public var revisionId: Swift.String?
        /// The asset ID of the owned asset corresponding to the entitled asset being viewed. This parameter is returned when an asset owner is viewing the entitled copy of its owned asset.
        public var sourceId: Swift.String?
        /// The date and time that the asset was last updated, in ISO 8601 format.
        /// This member is required.
        public var updatedAt: Foundation.Date?

        public init(
            arn: Swift.String? = nil,
            assetDetails: DataExchangeClientTypes.AssetDetails? = nil,
            assetType: DataExchangeClientTypes.AssetType? = nil,
            createdAt: Foundation.Date? = nil,
            dataSetId: Swift.String? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil,
            revisionId: Swift.String? = nil,
            sourceId: Swift.String? = nil,
            updatedAt: Foundation.Date? = nil
        )
        {
            self.arn = arn
            self.assetDetails = assetDetails
            self.assetType = assetType
            self.createdAt = createdAt
            self.dataSetId = dataSetId
            self.id = id
            self.name = name
            self.revisionId = revisionId
            self.sourceId = sourceId
            self.updatedAt = updatedAt
        }
    }
}

extension DataExchangeClientTypes {

    /// The source of the assets.
    public struct AssetSourceEntry: Swift.Sendable {
        /// The Amazon S3 bucket that's part of the source of the asset.
        /// This member is required.
        public var bucket: Swift.String?
        /// The name of the object in Amazon S3 for the asset.
        /// This member is required.
        public var key: Swift.String?

        public init(
            bucket: Swift.String? = nil,
            key: Swift.String? = nil
        )
        {
            self.bucket = bucket
            self.key = key
        }
    }
}

public struct CancelJobInput: Swift.Sendable {
    /// The unique identifier for a job.
    /// This member is required.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

extension DataExchangeClientTypes {

    public enum Code: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case accessDeniedException
        case internalServerException
        case malwareDetected
        case malwareScanEncryptedFile
        case resourceNotFoundException
        case serviceQuotaExceededException
        case validationException
        case sdkUnknown(Swift.String)

        public static var allCases: [Code] {
            return [
                .accessDeniedException,
                .internalServerException,
                .malwareDetected,
                .malwareScanEncryptedFile,
                .resourceNotFoundException,
                .serviceQuotaExceededException,
                .validationException
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .accessDeniedException: return "ACCESS_DENIED_EXCEPTION"
            case .internalServerException: return "INTERNAL_SERVER_EXCEPTION"
            case .malwareDetected: return "MALWARE_DETECTED"
            case .malwareScanEncryptedFile: return "MALWARE_SCAN_ENCRYPTED_FILE"
            case .resourceNotFoundException: return "RESOURCE_NOT_FOUND_EXCEPTION"
            case .serviceQuotaExceededException: return "SERVICE_QUOTA_EXCEEDED_EXCEPTION"
            case .validationException: return "VALIDATION_EXCEPTION"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataExchangeClientTypes {

    public enum LimitName: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case awsLakeFormationDataPermissionAssetsPerRevision
        case activeAndPendingDataGrants
        case amazonApiGatewayApiAssetsPerRevision
        case amazonRedshiftDatashareAssetsPerImportJobFromRedshift
        case amazonRedshiftDatashareAssetsPerRevision
        case amazonS3DataAccessAssetsPerRevision
        case assetPerExportJobFromAmazonS3
        case assetSizeInGb
        case assetsPerImportJobFromAmazonS3
        case assetsPerRevision
        case autoExportEventActionsPerDataSet
        case concurrentInProgressJobsToCreateAmazonS3DataAccessAssetsFromS3Buckets
        case concurrentInProgressJobsToExportAssetsToAmazonS3
        case concurrentInProgressJobsToExportAssetsToASignedUrl
        case concurrentInProgressJobsToExportRevisionsToAmazonS3
        case concurrentInProgressJobsToImportAssetsFromAmazonRedshiftDatashares
        case concurrentInProgressJobsToImportAssetsFromAmazonS3
        case concurrentInProgressJobsToImportAssetsFromASignedUrl
        case concurrentInProgressJobsToImportAssetsFromAnApiGatewayApi
        case concurrentInProgressJobsToImportAssetsFromAnAwsLakeFormationTagPolicy
        case dataSetsPerAccount
        case dataSetsPerProduct
        case eventActionsPerAccount
        case pendingDataGrantsPerConsumer
        case productsPerAccount
        case revisionsPerAwsLakeFormationDataPermissionDataSet
        case revisionsPerAmazonApiGatewayApiDataSet
        case revisionsPerAmazonRedshiftDatashareDataSet
        case revisionsPerAmazonS3DataAccessDataSet
        case revisionsPerDataSet
        case sdkUnknown(Swift.String)

        public static var allCases: [LimitName] {
            return [
                .awsLakeFormationDataPermissionAssetsPerRevision,
                .activeAndPendingDataGrants,
                .amazonApiGatewayApiAssetsPerRevision,
                .amazonRedshiftDatashareAssetsPerImportJobFromRedshift,
                .amazonRedshiftDatashareAssetsPerRevision,
                .amazonS3DataAccessAssetsPerRevision,
                .assetPerExportJobFromAmazonS3,
                .assetSizeInGb,
                .assetsPerImportJobFromAmazonS3,
                .assetsPerRevision,
                .autoExportEventActionsPerDataSet,
                .concurrentInProgressJobsToCreateAmazonS3DataAccessAssetsFromS3Buckets,
                .concurrentInProgressJobsToExportAssetsToAmazonS3,
                .concurrentInProgressJobsToExportAssetsToASignedUrl,
                .concurrentInProgressJobsToExportRevisionsToAmazonS3,
                .concurrentInProgressJobsToImportAssetsFromAmazonRedshiftDatashares,
                .concurrentInProgressJobsToImportAssetsFromAmazonS3,
                .concurrentInProgressJobsToImportAssetsFromASignedUrl,
                .concurrentInProgressJobsToImportAssetsFromAnApiGatewayApi,
                .concurrentInProgressJobsToImportAssetsFromAnAwsLakeFormationTagPolicy,
                .dataSetsPerAccount,
                .dataSetsPerProduct,
                .eventActionsPerAccount,
                .pendingDataGrantsPerConsumer,
                .productsPerAccount,
                .revisionsPerAwsLakeFormationDataPermissionDataSet,
                .revisionsPerAmazonApiGatewayApiDataSet,
                .revisionsPerAmazonRedshiftDatashareDataSet,
                .revisionsPerAmazonS3DataAccessDataSet,
                .revisionsPerDataSet
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .awsLakeFormationDataPermissionAssetsPerRevision: return "AWS Lake Formation data permission assets per revision"
            case .activeAndPendingDataGrants: return "Active and pending data grants"
            case .amazonApiGatewayApiAssetsPerRevision: return "Amazon API Gateway API assets per revision"
            case .amazonRedshiftDatashareAssetsPerImportJobFromRedshift: return "Amazon Redshift datashare assets per import job from Redshift"
            case .amazonRedshiftDatashareAssetsPerRevision: return "Amazon Redshift datashare assets per revision"
            case .amazonS3DataAccessAssetsPerRevision: return "Amazon S3 data access assets per revision"
            case .assetPerExportJobFromAmazonS3: return "Asset per export job from Amazon S3"
            case .assetSizeInGb: return "Asset size in GB"
            case .assetsPerImportJobFromAmazonS3: return "Assets per import job from Amazon S3"
            case .assetsPerRevision: return "Assets per revision"
            case .autoExportEventActionsPerDataSet: return "Auto export event actions per data set"
            case .concurrentInProgressJobsToCreateAmazonS3DataAccessAssetsFromS3Buckets: return "Concurrent in progress jobs to create Amazon S3 data access assets from S3 buckets"
            case .concurrentInProgressJobsToExportAssetsToAmazonS3: return "Concurrent in progress jobs to export assets to Amazon S3"
            case .concurrentInProgressJobsToExportAssetsToASignedUrl: return "Concurrent in progress jobs to export assets to a signed URL"
            case .concurrentInProgressJobsToExportRevisionsToAmazonS3: return "Concurrent in progress jobs to export revisions to Amazon S3"
            case .concurrentInProgressJobsToImportAssetsFromAmazonRedshiftDatashares: return "Concurrent in progress jobs to import assets from Amazon Redshift datashares"
            case .concurrentInProgressJobsToImportAssetsFromAmazonS3: return "Concurrent in progress jobs to import assets from Amazon S3"
            case .concurrentInProgressJobsToImportAssetsFromASignedUrl: return "Concurrent in progress jobs to import assets from a signed URL"
            case .concurrentInProgressJobsToImportAssetsFromAnApiGatewayApi: return "Concurrent in progress jobs to import assets from an API Gateway API"
            case .concurrentInProgressJobsToImportAssetsFromAnAwsLakeFormationTagPolicy: return "Concurrent in progress jobs to import assets from an AWS Lake Formation tag policy"
            case .dataSetsPerAccount: return "Data sets per account"
            case .dataSetsPerProduct: return "Data sets per product"
            case .eventActionsPerAccount: return "Event actions per account"
            case .pendingDataGrantsPerConsumer: return "Pending data grants per consumer"
            case .productsPerAccount: return "Products per account"
            case .revisionsPerAwsLakeFormationDataPermissionDataSet: return "Revisions per AWS Lake Formation data permission data set"
            case .revisionsPerAmazonApiGatewayApiDataSet: return "Revisions per Amazon API Gateway API data set"
            case .revisionsPerAmazonRedshiftDatashareDataSet: return "Revisions per Amazon Redshift datashare data set"
            case .revisionsPerAmazonS3DataAccessDataSet: return "Revisions per Amazon S3 data access data set"
            case .revisionsPerDataSet: return "Revisions per data set"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The request has exceeded the quotas imposed by the service.
public struct ServiceLimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The name of the limit that was reached.
        public internal(set) var limitName: DataExchangeClientTypes.LimitName? = nil
        /// The value of the exceeded limit.
        public internal(set) var limitValue: Swift.Double? = 0.0
        /// The request has exceeded the quotas imposed by the service.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceLimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        limitName: DataExchangeClientTypes.LimitName? = nil,
        limitValue: Swift.Double? = 0.0,
        message: Swift.String? = nil
    )
    {
        self.properties.limitName = limitName
        self.properties.limitValue = limitValue
        self.properties.message = message
    }
}

public struct CreateDataGrantInput: Swift.Sendable {
    /// The description of the data grant.
    public var description: Swift.String?
    /// The timestamp of when access to the associated data set ends.
    public var endsAt: Foundation.Date?
    /// The distribution scope of the data grant.
    /// This member is required.
    public var grantDistributionScope: DataExchangeClientTypes.GrantDistributionScope?
    /// The name of the data grant.
    /// This member is required.
    public var name: Swift.String?
    /// The Amazon Web Services account ID of the data grant receiver.
    /// This member is required.
    public var receiverPrincipal: Swift.String?
    /// The ID of the data set used to create the data grant.
    /// This member is required.
    public var sourceDataSetId: Swift.String?
    /// The tags to add to the data grant. A tag is a key-value pair.
    public var tags: [Swift.String: Swift.String]?

    public init(
        description: Swift.String? = nil,
        endsAt: Foundation.Date? = nil,
        grantDistributionScope: DataExchangeClientTypes.GrantDistributionScope? = nil,
        name: Swift.String? = nil,
        receiverPrincipal: Swift.String? = nil,
        sourceDataSetId: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.description = description
        self.endsAt = endsAt
        self.grantDistributionScope = grantDistributionScope
        self.name = name
        self.receiverPrincipal = receiverPrincipal
        self.sourceDataSetId = sourceDataSetId
        self.tags = tags
    }
}

public struct CreateDataGrantOutput: Swift.Sendable {
    /// The acceptance state of the data grant.
    /// This member is required.
    public var acceptanceState: DataExchangeClientTypes.DataGrantAcceptanceState?
    /// The timestamp of when the data grant was accepted.
    public var acceptedAt: Foundation.Date?
    /// The Amazon Resource Name (ARN) of the data grant.
    /// This member is required.
    public var arn: Swift.String?
    /// The timestamp of when the data grant was created.
    /// This member is required.
    public var createdAt: Foundation.Date?
    /// The ID of the data set associated to the data grant.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// The description of the data grant.
    public var description: Swift.String?
    /// The timestamp of when access to the associated data set ends.
    public var endsAt: Foundation.Date?
    /// The distribution scope for the data grant.
    /// This member is required.
    public var grantDistributionScope: DataExchangeClientTypes.GrantDistributionScope?
    /// The ID of the data grant.
    /// This member is required.
    public var id: Swift.String?
    /// The name of the data grant.
    /// This member is required.
    public var name: Swift.String?
    /// The Amazon Web Services account ID of the data grant receiver.
    /// This member is required.
    public var receiverPrincipal: Swift.String?
    /// The Amazon Web Services account ID of the data grant sender.
    /// This member is required.
    public var senderPrincipal: Swift.String?
    /// The ID of the data set used to create the data grant.
    /// This member is required.
    public var sourceDataSetId: Swift.String?
    /// The tags associated to the data grant. A tag is a key-value pair.
    public var tags: [Swift.String: Swift.String]?
    /// The timestamp of when the data grant was last updated.
    /// This member is required.
    public var updatedAt: Foundation.Date?

    public init(
        acceptanceState: DataExchangeClientTypes.DataGrantAcceptanceState? = nil,
        acceptedAt: Foundation.Date? = nil,
        arn: Swift.String? = nil,
        createdAt: Foundation.Date? = nil,
        dataSetId: Swift.String? = nil,
        description: Swift.String? = nil,
        endsAt: Foundation.Date? = nil,
        grantDistributionScope: DataExchangeClientTypes.GrantDistributionScope? = nil,
        id: Swift.String? = nil,
        name: Swift.String? = nil,
        receiverPrincipal: Swift.String? = nil,
        senderPrincipal: Swift.String? = nil,
        sourceDataSetId: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.acceptanceState = acceptanceState
        self.acceptedAt = acceptedAt
        self.arn = arn
        self.createdAt = createdAt
        self.dataSetId = dataSetId
        self.description = description
        self.endsAt = endsAt
        self.grantDistributionScope = grantDistributionScope
        self.id = id
        self.name = name
        self.receiverPrincipal = receiverPrincipal
        self.senderPrincipal = senderPrincipal
        self.sourceDataSetId = sourceDataSetId
        self.tags = tags
        self.updatedAt = updatedAt
    }
}

public struct CreateDataSetInput: Swift.Sendable {
    /// The type of asset that is added to a data set.
    /// This member is required.
    public var assetType: DataExchangeClientTypes.AssetType?
    /// A description for the data set. This value can be up to 16,348 characters long.
    /// This member is required.
    public var description: Swift.String?
    /// The name of the data set.
    /// This member is required.
    public var name: Swift.String?
    /// A data set tag is an optional label that you can assign to a data set when you create it. Each tag consists of a key and an optional value, both of which you define. When you use tagging, you can also use tag-based access control in IAM policies to control access to these data sets and revisions.
    public var tags: [Swift.String: Swift.String]?

    public init(
        assetType: DataExchangeClientTypes.AssetType? = nil,
        description: Swift.String? = nil,
        name: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.assetType = assetType
        self.description = description
        self.name = name
        self.tags = tags
    }
}

extension DataExchangeClientTypes {

    public enum Origin: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case entitled
        case owned
        case sdkUnknown(Swift.String)

        public static var allCases: [Origin] {
            return [
                .entitled,
                .owned
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .entitled: return "ENTITLED"
            case .owned: return "OWNED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataExchangeClientTypes {

    /// Details about the origin of the data set.
    public struct OriginDetails: Swift.Sendable {
        /// The ID of the data grant.
        public var dataGrantId: Swift.String?
        /// The product ID of the origin of the data set.
        public var productId: Swift.String?

        public init(
            dataGrantId: Swift.String? = nil,
            productId: Swift.String? = nil
        )
        {
            self.dataGrantId = dataGrantId
            self.productId = productId
        }
    }
}

public struct CreateDataSetOutput: Swift.Sendable {
    /// The ARN for the data set.
    public var arn: Swift.String?
    /// The type of asset that is added to a data set.
    public var assetType: DataExchangeClientTypes.AssetType?
    /// The date and time that the data set was created, in ISO 8601 format.
    public var createdAt: Foundation.Date?
    /// The description for the data set.
    public var description: Swift.String?
    /// The unique identifier for the data set.
    public var id: Swift.String?
    /// The name of the data set.
    public var name: Swift.String?
    /// A property that defines the data set as OWNED by the account (for providers) or ENTITLED to the account (for subscribers).
    public var origin: DataExchangeClientTypes.Origin?
    /// If the origin of this data set is ENTITLED, includes the details for the product on AWS Marketplace.
    public var originDetails: DataExchangeClientTypes.OriginDetails?
    /// The data set ID of the owned data set corresponding to the entitled data set being viewed. This parameter is returned when a data set owner is viewing the entitled copy of its owned data set.
    public var sourceId: Swift.String?
    /// The tags for the data set.
    public var tags: [Swift.String: Swift.String]?
    /// The date and time that the data set was last updated, in ISO 8601 format.
    public var updatedAt: Foundation.Date?

    public init(
        arn: Swift.String? = nil,
        assetType: DataExchangeClientTypes.AssetType? = nil,
        createdAt: Foundation.Date? = nil,
        description: Swift.String? = nil,
        id: Swift.String? = nil,
        name: Swift.String? = nil,
        origin: DataExchangeClientTypes.Origin? = nil,
        originDetails: DataExchangeClientTypes.OriginDetails? = nil,
        sourceId: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.arn = arn
        self.assetType = assetType
        self.createdAt = createdAt
        self.description = description
        self.id = id
        self.name = name
        self.origin = origin
        self.originDetails = originDetails
        self.sourceId = sourceId
        self.tags = tags
        self.updatedAt = updatedAt
    }
}

extension DataExchangeClientTypes {

    /// Information about the published revision.
    public struct RevisionPublished: Swift.Sendable {
        /// The data set ID of the published revision.
        /// This member is required.
        public var dataSetId: Swift.String?

        public init(
            dataSetId: Swift.String? = nil
        )
        {
            self.dataSetId = dataSetId
        }
    }
}

extension DataExchangeClientTypes {

    /// What occurs to start an action.
    public struct Event: Swift.Sendable {
        /// What occurs to start the revision publish action.
        public var revisionPublished: DataExchangeClientTypes.RevisionPublished?

        public init(
            revisionPublished: DataExchangeClientTypes.RevisionPublished? = nil
        )
        {
            self.revisionPublished = revisionPublished
        }
    }
}

public struct CreateEventActionInput: Swift.Sendable {
    /// What occurs after a certain event.
    /// This member is required.
    public var action: DataExchangeClientTypes.Action?
    /// What occurs to start an action.
    /// This member is required.
    public var event: DataExchangeClientTypes.Event?

    public init(
        action: DataExchangeClientTypes.Action? = nil,
        event: DataExchangeClientTypes.Event? = nil
    )
    {
        self.action = action
        self.event = event
    }
}

public struct CreateEventActionOutput: Swift.Sendable {
    /// What occurs after a certain event.
    public var action: DataExchangeClientTypes.Action?
    /// The ARN for the event action.
    public var arn: Swift.String?
    /// The date and time that the event action was created, in ISO 8601 format.
    public var createdAt: Foundation.Date?
    /// What occurs to start an action.
    public var event: DataExchangeClientTypes.Event?
    /// The unique identifier for the event action.
    public var id: Swift.String?
    /// The date and time that the event action was last updated, in ISO 8601 format.
    public var updatedAt: Foundation.Date?

    public init(
        action: DataExchangeClientTypes.Action? = nil,
        arn: Swift.String? = nil,
        createdAt: Foundation.Date? = nil,
        event: DataExchangeClientTypes.Event? = nil,
        id: Swift.String? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.action = action
        self.arn = arn
        self.createdAt = createdAt
        self.event = event
        self.id = id
        self.updatedAt = updatedAt
    }
}

extension DataExchangeClientTypes {

    /// Source details for an Amazon S3 data access asset.
    public struct S3DataAccessAssetSourceEntry: Swift.Sendable {
        /// The Amazon S3 bucket used for hosting shared data in the Amazon S3 data access.
        /// This member is required.
        public var bucket: Swift.String?
        /// Organizes Amazon S3 asset key prefixes stored in an Amazon S3 bucket.
        public var keyPrefixes: [Swift.String]?
        /// The keys used to create the Amazon S3 data access.
        public var keys: [Swift.String]?
        /// List of AWS KMS CMKs (Key Management System Customer Managed Keys) and ARNs used to encrypt S3 objects being shared in this S3 Data Access asset.
        public var kmsKeysToGrant: [DataExchangeClientTypes.KmsKeyToGrant]?

        public init(
            bucket: Swift.String? = nil,
            keyPrefixes: [Swift.String]? = nil,
            keys: [Swift.String]? = nil,
            kmsKeysToGrant: [DataExchangeClientTypes.KmsKeyToGrant]? = nil
        )
        {
            self.bucket = bucket
            self.keyPrefixes = keyPrefixes
            self.keys = keys
            self.kmsKeysToGrant = kmsKeysToGrant
        }
    }
}

extension DataExchangeClientTypes {

    /// Details of the operation to create an Amazon S3 data access from an S3 bucket.
    public struct CreateS3DataAccessFromS3BucketRequestDetails: Swift.Sendable {
        /// Details about the S3 data access source asset.
        /// This member is required.
        public var assetSource: DataExchangeClientTypes.S3DataAccessAssetSourceEntry?
        /// The unique identifier for the data set associated with the creation of this Amazon S3 data access.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The unique identifier for a revision.
        /// This member is required.
        public var revisionId: Swift.String?

        public init(
            assetSource: DataExchangeClientTypes.S3DataAccessAssetSourceEntry? = nil,
            dataSetId: Swift.String? = nil,
            revisionId: Swift.String? = nil
        )
        {
            self.assetSource = assetSource
            self.dataSetId = dataSetId
            self.revisionId = revisionId
        }
    }
}

extension DataExchangeClientTypes {

    /// Details of the operation to be performed by the job.
    public struct ExportAssetsToS3RequestDetails: Swift.Sendable {
        /// The destination for the asset.
        /// This member is required.
        public var assetDestinations: [DataExchangeClientTypes.AssetDestinationEntry]?
        /// The unique identifier for the data set associated with this export job.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// Encryption configuration for the export job.
        public var encryption: DataExchangeClientTypes.ExportServerSideEncryption?
        /// The unique identifier for the revision associated with this export request.
        /// This member is required.
        public var revisionId: Swift.String?

        public init(
            assetDestinations: [DataExchangeClientTypes.AssetDestinationEntry]? = nil,
            dataSetId: Swift.String? = nil,
            encryption: DataExchangeClientTypes.ExportServerSideEncryption? = nil,
            revisionId: Swift.String? = nil
        )
        {
            self.assetDestinations = assetDestinations
            self.dataSetId = dataSetId
            self.encryption = encryption
            self.revisionId = revisionId
        }
    }
}

extension DataExchangeClientTypes {

    /// Details of the operation to be performed by the job.
    public struct ExportAssetToSignedUrlRequestDetails: Swift.Sendable {
        /// The unique identifier for the asset that is exported to a signed URL.
        /// This member is required.
        public var assetId: Swift.String?
        /// The unique identifier for the data set associated with this export job.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The unique identifier for the revision associated with this export request.
        /// This member is required.
        public var revisionId: Swift.String?

        public init(
            assetId: Swift.String? = nil,
            dataSetId: Swift.String? = nil,
            revisionId: Swift.String? = nil
        )
        {
            self.assetId = assetId
            self.dataSetId = dataSetId
            self.revisionId = revisionId
        }
    }
}

extension DataExchangeClientTypes {

    /// The destination where the assets in the revision will be exported.
    public struct RevisionDestinationEntry: Swift.Sendable {
        /// The Amazon S3 bucket that is the destination for the assets in the revision.
        /// This member is required.
        public var bucket: Swift.String?
        /// A string representing the pattern for generated names of the individual assets in the revision. For more information about key patterns, see [Key patterns when exporting revisions](https://docs.aws.amazon.com/data-exchange/latest/userguide/jobs.html#revision-export-keypatterns).
        public var keyPattern: Swift.String?
        /// The unique identifier for the revision.
        /// This member is required.
        public var revisionId: Swift.String?

        public init(
            bucket: Swift.String? = nil,
            keyPattern: Swift.String? = nil,
            revisionId: Swift.String? = nil
        )
        {
            self.bucket = bucket
            self.keyPattern = keyPattern
            self.revisionId = revisionId
        }
    }
}

extension DataExchangeClientTypes {

    /// Details of the operation to be performed by the job.
    public struct ExportRevisionsToS3RequestDetails: Swift.Sendable {
        /// The unique identifier for the data set associated with this export job.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// Encryption configuration for the export job.
        public var encryption: DataExchangeClientTypes.ExportServerSideEncryption?
        /// The destination for the revision.
        /// This member is required.
        public var revisionDestinations: [DataExchangeClientTypes.RevisionDestinationEntry]?

        public init(
            dataSetId: Swift.String? = nil,
            encryption: DataExchangeClientTypes.ExportServerSideEncryption? = nil,
            revisionDestinations: [DataExchangeClientTypes.RevisionDestinationEntry]? = nil
        )
        {
            self.dataSetId = dataSetId
            self.encryption = encryption
            self.revisionDestinations = revisionDestinations
        }
    }
}

extension DataExchangeClientTypes {

    /// The request details.
    public struct ImportAssetFromApiGatewayApiRequestDetails: Swift.Sendable {
        /// The API description. Markdown supported.
        public var apiDescription: Swift.String?
        /// The API Gateway API ID.
        /// This member is required.
        public var apiId: Swift.String?
        /// The API Gateway API key.
        public var apiKey: Swift.String?
        /// The API name.
        /// This member is required.
        public var apiName: Swift.String?
        /// The Base64-encoded MD5 hash of the OpenAPI 3.0 JSON API specification file. It is used to ensure the integrity of the file.
        /// This member is required.
        public var apiSpecificationMd5Hash: Swift.String?
        /// The data set ID.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The protocol type.
        /// This member is required.
        public var protocolType: DataExchangeClientTypes.ProtocolType?
        /// The revision ID.
        /// This member is required.
        public var revisionId: Swift.String?
        /// The API stage.
        /// This member is required.
        public var stage: Swift.String?

        public init(
            apiDescription: Swift.String? = nil,
            apiId: Swift.String? = nil,
            apiKey: Swift.String? = nil,
            apiName: Swift.String? = nil,
            apiSpecificationMd5Hash: Swift.String? = nil,
            dataSetId: Swift.String? = nil,
            protocolType: DataExchangeClientTypes.ProtocolType? = nil,
            revisionId: Swift.String? = nil,
            stage: Swift.String? = nil
        )
        {
            self.apiDescription = apiDescription
            self.apiId = apiId
            self.apiKey = apiKey
            self.apiName = apiName
            self.apiSpecificationMd5Hash = apiSpecificationMd5Hash
            self.dataSetId = dataSetId
            self.protocolType = protocolType
            self.revisionId = revisionId
            self.stage = stage
        }
    }
}

extension DataExchangeClientTypes {

    /// Details of the operation to be performed by the job.
    public struct ImportAssetFromSignedUrlRequestDetails: Swift.Sendable {
        /// The name of the asset. When importing from Amazon S3, the Amazon S3 object key is used as the asset name.
        /// This member is required.
        public var assetName: Swift.String?
        /// The unique identifier for the data set associated with this import job.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The Base64-encoded Md5 hash for the asset, used to ensure the integrity of the file at that location.
        /// This member is required.
        public var md5Hash: Swift.String?
        /// The unique identifier for the revision associated with this import request.
        /// This member is required.
        public var revisionId: Swift.String?

        public init(
            assetName: Swift.String? = nil,
            dataSetId: Swift.String? = nil,
            md5Hash: Swift.String? = nil,
            revisionId: Swift.String? = nil
        )
        {
            self.assetName = assetName
            self.dataSetId = dataSetId
            self.md5Hash = md5Hash
            self.revisionId = revisionId
        }
    }
}

extension DataExchangeClientTypes {

    public enum DatabaseLFTagPolicyPermission: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case describe
        case sdkUnknown(Swift.String)

        public static var allCases: [DatabaseLFTagPolicyPermission] {
            return [
                .describe
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .describe: return "DESCRIBE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataExchangeClientTypes {

    /// The LF-tag policy and permissions for database resources.
    public struct DatabaseLFTagPolicyAndPermissions: Swift.Sendable {
        /// A list of LF-tag conditions that apply to database resources.
        /// This member is required.
        public var expression: [DataExchangeClientTypes.LFTag]?
        /// The permissions granted to subscribers on database resources.
        /// This member is required.
        public var permissions: [DataExchangeClientTypes.DatabaseLFTagPolicyPermission]?

        public init(
            expression: [DataExchangeClientTypes.LFTag]? = nil,
            permissions: [DataExchangeClientTypes.DatabaseLFTagPolicyPermission]? = nil
        )
        {
            self.expression = expression
            self.permissions = permissions
        }
    }
}

extension DataExchangeClientTypes {

    public enum TableTagPolicyLFPermission: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case describe
        case select
        case sdkUnknown(Swift.String)

        public static var allCases: [TableTagPolicyLFPermission] {
            return [
                .describe,
                .select
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .describe: return "DESCRIBE"
            case .select: return "SELECT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataExchangeClientTypes {

    /// The LF-tag policy and permissions that apply to table resources.
    public struct TableLFTagPolicyAndPermissions: Swift.Sendable {
        /// A list of LF-tag conditions that apply to table resources.
        /// This member is required.
        public var expression: [DataExchangeClientTypes.LFTag]?
        /// The permissions granted to subscribers on table resources.
        /// This member is required.
        public var permissions: [DataExchangeClientTypes.TableTagPolicyLFPermission]?

        public init(
            expression: [DataExchangeClientTypes.LFTag]? = nil,
            permissions: [DataExchangeClientTypes.TableTagPolicyLFPermission]? = nil
        )
        {
            self.expression = expression
            self.permissions = permissions
        }
    }
}

extension DataExchangeClientTypes {

    /// Details about the assets imported from an AWS Lake Formation tag policy request.
    public struct ImportAssetsFromLakeFormationTagPolicyRequestDetails: Swift.Sendable {
        /// The identifier for the AWS Glue Data Catalog.
        /// This member is required.
        public var catalogId: Swift.String?
        /// The unique identifier for the data set associated with this import job.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// A structure for the database object.
        public var database: DataExchangeClientTypes.DatabaseLFTagPolicyAndPermissions?
        /// The unique identifier for the revision associated with this import job.
        /// This member is required.
        public var revisionId: Swift.String?
        /// The IAM role's ARN that allows AWS Data Exchange to assume the role and grant and revoke permissions of subscribers to AWS Lake Formation data permissions.
        /// This member is required.
        public var roleArn: Swift.String?
        /// A structure for the table object.
        public var table: DataExchangeClientTypes.TableLFTagPolicyAndPermissions?

        public init(
            catalogId: Swift.String? = nil,
            dataSetId: Swift.String? = nil,
            database: DataExchangeClientTypes.DatabaseLFTagPolicyAndPermissions? = nil,
            revisionId: Swift.String? = nil,
            roleArn: Swift.String? = nil,
            table: DataExchangeClientTypes.TableLFTagPolicyAndPermissions? = nil
        )
        {
            self.catalogId = catalogId
            self.dataSetId = dataSetId
            self.database = database
            self.revisionId = revisionId
            self.roleArn = roleArn
            self.table = table
        }
    }
}

extension DataExchangeClientTypes {

    /// The source of the Amazon Redshift datashare asset.
    public struct RedshiftDataShareAssetSourceEntry: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the datashare asset.
        /// This member is required.
        public var dataShareArn: Swift.String?

        public init(
            dataShareArn: Swift.String? = nil
        )
        {
            self.dataShareArn = dataShareArn
        }
    }
}

extension DataExchangeClientTypes {

    /// Details from an import from Amazon Redshift datashare request.
    public struct ImportAssetsFromRedshiftDataSharesRequestDetails: Swift.Sendable {
        /// A list of Amazon Redshift datashare assets.
        /// This member is required.
        public var assetSources: [DataExchangeClientTypes.RedshiftDataShareAssetSourceEntry]?
        /// The unique identifier for the data set associated with this import job.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The unique identifier for the revision associated with this import job.
        /// This member is required.
        public var revisionId: Swift.String?

        public init(
            assetSources: [DataExchangeClientTypes.RedshiftDataShareAssetSourceEntry]? = nil,
            dataSetId: Swift.String? = nil,
            revisionId: Swift.String? = nil
        )
        {
            self.assetSources = assetSources
            self.dataSetId = dataSetId
            self.revisionId = revisionId
        }
    }
}

extension DataExchangeClientTypes {

    /// Details of the operation to be performed by the job.
    public struct ImportAssetsFromS3RequestDetails: Swift.Sendable {
        /// Is a list of Amazon S3 bucket and object key pairs.
        /// This member is required.
        public var assetSources: [DataExchangeClientTypes.AssetSourceEntry]?
        /// The unique identifier for the data set associated with this import job.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The unique identifier for the revision associated with this import request.
        /// This member is required.
        public var revisionId: Swift.String?

        public init(
            assetSources: [DataExchangeClientTypes.AssetSourceEntry]? = nil,
            dataSetId: Swift.String? = nil,
            revisionId: Swift.String? = nil
        )
        {
            self.assetSources = assetSources
            self.dataSetId = dataSetId
            self.revisionId = revisionId
        }
    }
}

extension DataExchangeClientTypes {

    /// The details for the request.
    public struct RequestDetails: Swift.Sendable {
        /// Details of the request to create S3 data access from the Amazon S3 bucket.
        public var createS3DataAccessFromS3Bucket: DataExchangeClientTypes.CreateS3DataAccessFromS3BucketRequestDetails?
        /// Details about the export to signed URL request.
        public var exportAssetToSignedUrl: DataExchangeClientTypes.ExportAssetToSignedUrlRequestDetails?
        /// Details about the export to Amazon S3 request.
        public var exportAssetsToS3: DataExchangeClientTypes.ExportAssetsToS3RequestDetails?
        /// Details about the export to Amazon S3 request.
        public var exportRevisionsToS3: DataExchangeClientTypes.ExportRevisionsToS3RequestDetails?
        /// Details about the import from signed URL request.
        public var importAssetFromApiGatewayApi: DataExchangeClientTypes.ImportAssetFromApiGatewayApiRequestDetails?
        /// Details about the import from Amazon S3 request.
        public var importAssetFromSignedUrl: DataExchangeClientTypes.ImportAssetFromSignedUrlRequestDetails?
        /// Request details for the ImportAssetsFromLakeFormationTagPolicy job.
        public var importAssetsFromLakeFormationTagPolicy: DataExchangeClientTypes.ImportAssetsFromLakeFormationTagPolicyRequestDetails?
        /// Details from an import from Amazon Redshift datashare request.
        public var importAssetsFromRedshiftDataShares: DataExchangeClientTypes.ImportAssetsFromRedshiftDataSharesRequestDetails?
        /// Details about the import asset from API Gateway API request.
        public var importAssetsFromS3: DataExchangeClientTypes.ImportAssetsFromS3RequestDetails?

        public init(
            createS3DataAccessFromS3Bucket: DataExchangeClientTypes.CreateS3DataAccessFromS3BucketRequestDetails? = nil,
            exportAssetToSignedUrl: DataExchangeClientTypes.ExportAssetToSignedUrlRequestDetails? = nil,
            exportAssetsToS3: DataExchangeClientTypes.ExportAssetsToS3RequestDetails? = nil,
            exportRevisionsToS3: DataExchangeClientTypes.ExportRevisionsToS3RequestDetails? = nil,
            importAssetFromApiGatewayApi: DataExchangeClientTypes.ImportAssetFromApiGatewayApiRequestDetails? = nil,
            importAssetFromSignedUrl: DataExchangeClientTypes.ImportAssetFromSignedUrlRequestDetails? = nil,
            importAssetsFromLakeFormationTagPolicy: DataExchangeClientTypes.ImportAssetsFromLakeFormationTagPolicyRequestDetails? = nil,
            importAssetsFromRedshiftDataShares: DataExchangeClientTypes.ImportAssetsFromRedshiftDataSharesRequestDetails? = nil,
            importAssetsFromS3: DataExchangeClientTypes.ImportAssetsFromS3RequestDetails? = nil
        )
        {
            self.createS3DataAccessFromS3Bucket = createS3DataAccessFromS3Bucket
            self.exportAssetToSignedUrl = exportAssetToSignedUrl
            self.exportAssetsToS3 = exportAssetsToS3
            self.exportRevisionsToS3 = exportRevisionsToS3
            self.importAssetFromApiGatewayApi = importAssetFromApiGatewayApi
            self.importAssetFromSignedUrl = importAssetFromSignedUrl
            self.importAssetsFromLakeFormationTagPolicy = importAssetsFromLakeFormationTagPolicy
            self.importAssetsFromRedshiftDataShares = importAssetsFromRedshiftDataShares
            self.importAssetsFromS3 = importAssetsFromS3
        }
    }
}

extension DataExchangeClientTypes {

    public enum ModelType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case createS3DataAccessFromS3Bucket
        case exportAssetsToS3
        case exportAssetToSignedUrl
        case exportRevisionsToS3
        case importAssetsFromLakeFormationTagPolicy
        case importAssetsFromRedshiftDataShares
        case importAssetsFromS3
        case importAssetFromApiGatewayApi
        case importAssetFromSignedUrl
        case sdkUnknown(Swift.String)

        public static var allCases: [ModelType] {
            return [
                .createS3DataAccessFromS3Bucket,
                .exportAssetsToS3,
                .exportAssetToSignedUrl,
                .exportRevisionsToS3,
                .importAssetsFromLakeFormationTagPolicy,
                .importAssetsFromRedshiftDataShares,
                .importAssetsFromS3,
                .importAssetFromApiGatewayApi,
                .importAssetFromSignedUrl
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .createS3DataAccessFromS3Bucket: return "CREATE_S3_DATA_ACCESS_FROM_S3_BUCKET"
            case .exportAssetsToS3: return "EXPORT_ASSETS_TO_S3"
            case .exportAssetToSignedUrl: return "EXPORT_ASSET_TO_SIGNED_URL"
            case .exportRevisionsToS3: return "EXPORT_REVISIONS_TO_S3"
            case .importAssetsFromLakeFormationTagPolicy: return "IMPORT_ASSETS_FROM_LAKE_FORMATION_TAG_POLICY"
            case .importAssetsFromRedshiftDataShares: return "IMPORT_ASSETS_FROM_REDSHIFT_DATA_SHARES"
            case .importAssetsFromS3: return "IMPORT_ASSETS_FROM_S3"
            case .importAssetFromApiGatewayApi: return "IMPORT_ASSET_FROM_API_GATEWAY_API"
            case .importAssetFromSignedUrl: return "IMPORT_ASSET_FROM_SIGNED_URL"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct CreateJobInput: Swift.Sendable {
    /// The details for the CreateJob request.
    /// This member is required.
    public var details: DataExchangeClientTypes.RequestDetails?
    /// The type of job to be created.
    /// This member is required.
    public var type: DataExchangeClientTypes.ModelType?

    public init(
        details: DataExchangeClientTypes.RequestDetails? = nil,
        type: DataExchangeClientTypes.ModelType? = nil
    )
    {
        self.details = details
        self.type = type
    }
}

extension DataExchangeClientTypes {

    /// Details about the response of the operation to create an S3 data access from an S3 bucket.
    public struct CreateS3DataAccessFromS3BucketResponseDetails: Swift.Sendable {
        /// Details about the asset source from an Amazon S3 bucket.
        /// This member is required.
        public var assetSource: DataExchangeClientTypes.S3DataAccessAssetSourceEntry?
        /// The unique identifier for this data set.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The unique identifier for the revision.
        /// This member is required.
        public var revisionId: Swift.String?

        public init(
            assetSource: DataExchangeClientTypes.S3DataAccessAssetSourceEntry? = nil,
            dataSetId: Swift.String? = nil,
            revisionId: Swift.String? = nil
        )
        {
            self.assetSource = assetSource
            self.dataSetId = dataSetId
            self.revisionId = revisionId
        }
    }
}

extension DataExchangeClientTypes {

    /// Details about the export to Amazon S3 response.
    public struct ExportAssetsToS3ResponseDetails: Swift.Sendable {
        /// The destination in Amazon S3 where the asset is exported.
        /// This member is required.
        public var assetDestinations: [DataExchangeClientTypes.AssetDestinationEntry]?
        /// The unique identifier for the data set associated with this export job.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// Encryption configuration of the export job.
        public var encryption: DataExchangeClientTypes.ExportServerSideEncryption?
        /// The unique identifier for the revision associated with this export response.
        /// This member is required.
        public var revisionId: Swift.String?

        public init(
            assetDestinations: [DataExchangeClientTypes.AssetDestinationEntry]? = nil,
            dataSetId: Swift.String? = nil,
            encryption: DataExchangeClientTypes.ExportServerSideEncryption? = nil,
            revisionId: Swift.String? = nil
        )
        {
            self.assetDestinations = assetDestinations
            self.dataSetId = dataSetId
            self.encryption = encryption
            self.revisionId = revisionId
        }
    }
}

extension DataExchangeClientTypes {

    /// The details of the export to signed URL response.
    public struct ExportAssetToSignedUrlResponseDetails: Swift.Sendable {
        /// The unique identifier for the asset associated with this export job.
        /// This member is required.
        public var assetId: Swift.String?
        /// The unique identifier for the data set associated with this export job.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The unique identifier for the revision associated with this export response.
        /// This member is required.
        public var revisionId: Swift.String?
        /// The signed URL for the export request.
        public var signedUrl: Swift.String?
        /// The date and time that the signed URL expires, in ISO 8601 format.
        public var signedUrlExpiresAt: Foundation.Date?

        public init(
            assetId: Swift.String? = nil,
            dataSetId: Swift.String? = nil,
            revisionId: Swift.String? = nil,
            signedUrl: Swift.String? = nil,
            signedUrlExpiresAt: Foundation.Date? = nil
        )
        {
            self.assetId = assetId
            self.dataSetId = dataSetId
            self.revisionId = revisionId
            self.signedUrl = signedUrl
            self.signedUrlExpiresAt = signedUrlExpiresAt
        }
    }
}

extension DataExchangeClientTypes {

    /// Details about the export revisions to Amazon S3 response.
    public struct ExportRevisionsToS3ResponseDetails: Swift.Sendable {
        /// The unique identifier for the data set associated with this export job.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// Encryption configuration of the export job.
        public var encryption: DataExchangeClientTypes.ExportServerSideEncryption?
        /// The Amazon Resource Name (ARN) of the event action.
        public var eventActionArn: Swift.String?
        /// The destination in Amazon S3 where the revision is exported.
        /// This member is required.
        public var revisionDestinations: [DataExchangeClientTypes.RevisionDestinationEntry]?

        public init(
            dataSetId: Swift.String? = nil,
            encryption: DataExchangeClientTypes.ExportServerSideEncryption? = nil,
            eventActionArn: Swift.String? = nil,
            revisionDestinations: [DataExchangeClientTypes.RevisionDestinationEntry]? = nil
        )
        {
            self.dataSetId = dataSetId
            self.encryption = encryption
            self.eventActionArn = eventActionArn
            self.revisionDestinations = revisionDestinations
        }
    }
}

extension DataExchangeClientTypes {

    /// The response details.
    public struct ImportAssetFromApiGatewayApiResponseDetails: Swift.Sendable {
        /// The API description.
        public var apiDescription: Swift.String?
        /// The API ID.
        /// This member is required.
        public var apiId: Swift.String?
        /// The API key.
        public var apiKey: Swift.String?
        /// The API name.
        /// This member is required.
        public var apiName: Swift.String?
        /// The Base64-encoded Md5 hash for the API asset, used to ensure the integrity of the API at that location.
        /// This member is required.
        public var apiSpecificationMd5Hash: Swift.String?
        /// The upload URL of the API specification.
        /// This member is required.
        public var apiSpecificationUploadUrl: Swift.String?
        /// The date and time that the upload URL expires, in ISO 8601 format.
        /// This member is required.
        public var apiSpecificationUploadUrlExpiresAt: Foundation.Date?
        /// The data set ID.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The protocol type.
        /// This member is required.
        public var protocolType: DataExchangeClientTypes.ProtocolType?
        /// The revision ID.
        /// This member is required.
        public var revisionId: Swift.String?
        /// The API stage.
        /// This member is required.
        public var stage: Swift.String?

        public init(
            apiDescription: Swift.String? = nil,
            apiId: Swift.String? = nil,
            apiKey: Swift.String? = nil,
            apiName: Swift.String? = nil,
            apiSpecificationMd5Hash: Swift.String? = nil,
            apiSpecificationUploadUrl: Swift.String? = nil,
            apiSpecificationUploadUrlExpiresAt: Foundation.Date? = nil,
            dataSetId: Swift.String? = nil,
            protocolType: DataExchangeClientTypes.ProtocolType? = nil,
            revisionId: Swift.String? = nil,
            stage: Swift.String? = nil
        )
        {
            self.apiDescription = apiDescription
            self.apiId = apiId
            self.apiKey = apiKey
            self.apiName = apiName
            self.apiSpecificationMd5Hash = apiSpecificationMd5Hash
            self.apiSpecificationUploadUrl = apiSpecificationUploadUrl
            self.apiSpecificationUploadUrlExpiresAt = apiSpecificationUploadUrlExpiresAt
            self.dataSetId = dataSetId
            self.protocolType = protocolType
            self.revisionId = revisionId
            self.stage = stage
        }
    }
}

extension DataExchangeClientTypes {

    /// The details in the response for an import request, including the signed URL and other information.
    public struct ImportAssetFromSignedUrlResponseDetails: Swift.Sendable {
        /// The name for the asset associated with this import job.
        /// This member is required.
        public var assetName: Swift.String?
        /// The unique identifier for the data set associated with this import job.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The Base64-encoded Md5 hash for the asset, used to ensure the integrity of the file at that location.
        public var md5Hash: Swift.String?
        /// The unique identifier for the revision associated with this import response.
        /// This member is required.
        public var revisionId: Swift.String?
        /// The signed URL.
        public var signedUrl: Swift.String?
        /// The time and date at which the signed URL expires, in ISO 8601 format.
        public var signedUrlExpiresAt: Foundation.Date?

        public init(
            assetName: Swift.String? = nil,
            dataSetId: Swift.String? = nil,
            md5Hash: Swift.String? = nil,
            revisionId: Swift.String? = nil,
            signedUrl: Swift.String? = nil,
            signedUrlExpiresAt: Foundation.Date? = nil
        )
        {
            self.assetName = assetName
            self.dataSetId = dataSetId
            self.md5Hash = md5Hash
            self.revisionId = revisionId
            self.signedUrl = signedUrl
            self.signedUrlExpiresAt = signedUrlExpiresAt
        }
    }
}

extension DataExchangeClientTypes {

    /// Details from an import AWS Lake Formation tag policy job response.
    public struct ImportAssetsFromLakeFormationTagPolicyResponseDetails: Swift.Sendable {
        /// The identifier for the AWS Glue Data Catalog.
        /// This member is required.
        public var catalogId: Swift.String?
        /// The unique identifier for the data set associated with this import job.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// A structure for the database object.
        public var database: DataExchangeClientTypes.DatabaseLFTagPolicyAndPermissions?
        /// The unique identifier for the revision associated with this import job.
        /// This member is required.
        public var revisionId: Swift.String?
        /// The IAM role's ARN that allows AWS Data Exchange to assume the role and grant and revoke permissions to AWS Lake Formation data permissions.
        /// This member is required.
        public var roleArn: Swift.String?
        /// A structure for the table object.
        public var table: DataExchangeClientTypes.TableLFTagPolicyAndPermissions?

        public init(
            catalogId: Swift.String? = nil,
            dataSetId: Swift.String? = nil,
            database: DataExchangeClientTypes.DatabaseLFTagPolicyAndPermissions? = nil,
            revisionId: Swift.String? = nil,
            roleArn: Swift.String? = nil,
            table: DataExchangeClientTypes.TableLFTagPolicyAndPermissions? = nil
        )
        {
            self.catalogId = catalogId
            self.dataSetId = dataSetId
            self.database = database
            self.revisionId = revisionId
            self.roleArn = roleArn
            self.table = table
        }
    }
}

extension DataExchangeClientTypes {

    /// Details from an import from Amazon Redshift datashare response.
    public struct ImportAssetsFromRedshiftDataSharesResponseDetails: Swift.Sendable {
        /// A list of Amazon Redshift datashare asset sources.
        /// This member is required.
        public var assetSources: [DataExchangeClientTypes.RedshiftDataShareAssetSourceEntry]?
        /// The unique identifier for the data set associated with this import job.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The unique identifier for the revision associated with this import job.
        /// This member is required.
        public var revisionId: Swift.String?

        public init(
            assetSources: [DataExchangeClientTypes.RedshiftDataShareAssetSourceEntry]? = nil,
            dataSetId: Swift.String? = nil,
            revisionId: Swift.String? = nil
        )
        {
            self.assetSources = assetSources
            self.dataSetId = dataSetId
            self.revisionId = revisionId
        }
    }
}

extension DataExchangeClientTypes {

    /// Details from an import from Amazon S3 response.
    public struct ImportAssetsFromS3ResponseDetails: Swift.Sendable {
        /// Is a list of Amazon S3 bucket and object key pairs.
        /// This member is required.
        public var assetSources: [DataExchangeClientTypes.AssetSourceEntry]?
        /// The unique identifier for the data set associated with this import job.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The unique identifier for the revision associated with this import response.
        /// This member is required.
        public var revisionId: Swift.String?

        public init(
            assetSources: [DataExchangeClientTypes.AssetSourceEntry]? = nil,
            dataSetId: Swift.String? = nil,
            revisionId: Swift.String? = nil
        )
        {
            self.assetSources = assetSources
            self.dataSetId = dataSetId
            self.revisionId = revisionId
        }
    }
}

extension DataExchangeClientTypes {

    /// Details for the response.
    public struct ResponseDetails: Swift.Sendable {
        /// Response details from the CreateS3DataAccessFromS3Bucket job.
        public var createS3DataAccessFromS3Bucket: DataExchangeClientTypes.CreateS3DataAccessFromS3BucketResponseDetails?
        /// Details for the export to signed URL response.
        public var exportAssetToSignedUrl: DataExchangeClientTypes.ExportAssetToSignedUrlResponseDetails?
        /// Details for the export to Amazon S3 response.
        public var exportAssetsToS3: DataExchangeClientTypes.ExportAssetsToS3ResponseDetails?
        /// Details for the export revisions to Amazon S3 response.
        public var exportRevisionsToS3: DataExchangeClientTypes.ExportRevisionsToS3ResponseDetails?
        /// The response details.
        public var importAssetFromApiGatewayApi: DataExchangeClientTypes.ImportAssetFromApiGatewayApiResponseDetails?
        /// Details for the import from signed URL response.
        public var importAssetFromSignedUrl: DataExchangeClientTypes.ImportAssetFromSignedUrlResponseDetails?
        /// Response details from the ImportAssetsFromLakeFormationTagPolicy job.
        public var importAssetsFromLakeFormationTagPolicy: DataExchangeClientTypes.ImportAssetsFromLakeFormationTagPolicyResponseDetails?
        /// Details from an import from Amazon Redshift datashare response.
        public var importAssetsFromRedshiftDataShares: DataExchangeClientTypes.ImportAssetsFromRedshiftDataSharesResponseDetails?
        /// Details for the import from Amazon S3 response.
        public var importAssetsFromS3: DataExchangeClientTypes.ImportAssetsFromS3ResponseDetails?

        public init(
            createS3DataAccessFromS3Bucket: DataExchangeClientTypes.CreateS3DataAccessFromS3BucketResponseDetails? = nil,
            exportAssetToSignedUrl: DataExchangeClientTypes.ExportAssetToSignedUrlResponseDetails? = nil,
            exportAssetsToS3: DataExchangeClientTypes.ExportAssetsToS3ResponseDetails? = nil,
            exportRevisionsToS3: DataExchangeClientTypes.ExportRevisionsToS3ResponseDetails? = nil,
            importAssetFromApiGatewayApi: DataExchangeClientTypes.ImportAssetFromApiGatewayApiResponseDetails? = nil,
            importAssetFromSignedUrl: DataExchangeClientTypes.ImportAssetFromSignedUrlResponseDetails? = nil,
            importAssetsFromLakeFormationTagPolicy: DataExchangeClientTypes.ImportAssetsFromLakeFormationTagPolicyResponseDetails? = nil,
            importAssetsFromRedshiftDataShares: DataExchangeClientTypes.ImportAssetsFromRedshiftDataSharesResponseDetails? = nil,
            importAssetsFromS3: DataExchangeClientTypes.ImportAssetsFromS3ResponseDetails? = nil
        )
        {
            self.createS3DataAccessFromS3Bucket = createS3DataAccessFromS3Bucket
            self.exportAssetToSignedUrl = exportAssetToSignedUrl
            self.exportAssetsToS3 = exportAssetsToS3
            self.exportRevisionsToS3 = exportRevisionsToS3
            self.importAssetFromApiGatewayApi = importAssetFromApiGatewayApi
            self.importAssetFromSignedUrl = importAssetFromSignedUrl
            self.importAssetsFromLakeFormationTagPolicy = importAssetsFromLakeFormationTagPolicy
            self.importAssetsFromRedshiftDataShares = importAssetsFromRedshiftDataShares
            self.importAssetsFromS3 = importAssetsFromS3
        }
    }
}

extension DataExchangeClientTypes {

    /// Details about the job error.
    public struct ImportAssetFromSignedUrlJobErrorDetails: Swift.Sendable {
        /// Details about the job error.
        /// This member is required.
        public var assetName: Swift.String?

        public init(
            assetName: Swift.String? = nil
        )
        {
            self.assetName = assetName
        }
    }
}

extension DataExchangeClientTypes {

    /// Information about the job error.
    public struct Details: Swift.Sendable {
        /// Information about the job error.
        public var importAssetFromSignedUrlJobErrorDetails: DataExchangeClientTypes.ImportAssetFromSignedUrlJobErrorDetails?
        /// Details about the job error.
        public var importAssetsFromS3JobErrorDetails: [DataExchangeClientTypes.AssetSourceEntry]?

        public init(
            importAssetFromSignedUrlJobErrorDetails: DataExchangeClientTypes.ImportAssetFromSignedUrlJobErrorDetails? = nil,
            importAssetsFromS3JobErrorDetails: [DataExchangeClientTypes.AssetSourceEntry]? = nil
        )
        {
            self.importAssetFromSignedUrlJobErrorDetails = importAssetFromSignedUrlJobErrorDetails
            self.importAssetsFromS3JobErrorDetails = importAssetsFromS3JobErrorDetails
        }
    }
}

extension DataExchangeClientTypes {

    public enum JobErrorLimitName: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case awsLakeFormationDataPermissionAssetsPerRevision
        case amazonRedshiftDatashareAssetsPerRevision
        case amazonS3DataAccessAssetsPerRevision
        case assetSizeInGb
        case assetsPerRevision
        case sdkUnknown(Swift.String)

        public static var allCases: [JobErrorLimitName] {
            return [
                .awsLakeFormationDataPermissionAssetsPerRevision,
                .amazonRedshiftDatashareAssetsPerRevision,
                .amazonS3DataAccessAssetsPerRevision,
                .assetSizeInGb,
                .assetsPerRevision
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .awsLakeFormationDataPermissionAssetsPerRevision: return "AWS Lake Formation data permission assets per revision"
            case .amazonRedshiftDatashareAssetsPerRevision: return "Amazon Redshift datashare assets per revision"
            case .amazonS3DataAccessAssetsPerRevision: return "Amazon S3 data access assets per revision"
            case .assetSizeInGb: return "Asset size in GB"
            case .assetsPerRevision: return "Assets per revision"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataExchangeClientTypes {

    public enum JobErrorResourceTypes: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case asset
        case dataSet
        case revision
        case sdkUnknown(Swift.String)

        public static var allCases: [JobErrorResourceTypes] {
            return [
                .asset,
                .dataSet,
                .revision
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .asset: return "ASSET"
            case .dataSet: return "DATA_SET"
            case .revision: return "REVISION"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataExchangeClientTypes {

    /// An error that occurred with the job request.
    public struct JobError: Swift.Sendable {
        /// The code for the job error.
        /// This member is required.
        public var code: DataExchangeClientTypes.Code?
        /// The details about the job error.
        public var details: DataExchangeClientTypes.Details?
        /// The name of the limit that was reached.
        public var limitName: DataExchangeClientTypes.JobErrorLimitName?
        /// The value of the exceeded limit.
        public var limitValue: Swift.Double?
        /// The message related to the job error.
        /// This member is required.
        public var message: Swift.String?
        /// The unique identifier for the resource related to the error.
        public var resourceId: Swift.String?
        /// The type of resource related to the error.
        public var resourceType: DataExchangeClientTypes.JobErrorResourceTypes?

        public init(
            code: DataExchangeClientTypes.Code? = nil,
            details: DataExchangeClientTypes.Details? = nil,
            limitName: DataExchangeClientTypes.JobErrorLimitName? = nil,
            limitValue: Swift.Double? = 0.0,
            message: Swift.String? = nil,
            resourceId: Swift.String? = nil,
            resourceType: DataExchangeClientTypes.JobErrorResourceTypes? = nil
        )
        {
            self.code = code
            self.details = details
            self.limitName = limitName
            self.limitValue = limitValue
            self.message = message
            self.resourceId = resourceId
            self.resourceType = resourceType
        }
    }
}

extension DataExchangeClientTypes {

    public enum State: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cancelled
        case completed
        case error
        case inProgress
        case timedOut
        case waiting
        case sdkUnknown(Swift.String)

        public static var allCases: [State] {
            return [
                .cancelled,
                .completed,
                .error,
                .inProgress,
                .timedOut,
                .waiting
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cancelled: return "CANCELLED"
            case .completed: return "COMPLETED"
            case .error: return "ERROR"
            case .inProgress: return "IN_PROGRESS"
            case .timedOut: return "TIMED_OUT"
            case .waiting: return "WAITING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct CreateJobOutput: Swift.Sendable {
    /// The ARN for the job.
    public var arn: Swift.String?
    /// The date and time that the job was created, in ISO 8601 format.
    public var createdAt: Foundation.Date?
    /// Details about the job.
    public var details: DataExchangeClientTypes.ResponseDetails?
    /// The errors associated with jobs.
    public var errors: [DataExchangeClientTypes.JobError]?
    /// The unique identifier for the job.
    public var id: Swift.String?
    /// The state of the job.
    public var state: DataExchangeClientTypes.State?
    /// The job type.
    public var type: DataExchangeClientTypes.ModelType?
    /// The date and time that the job was last updated, in ISO 8601 format.
    public var updatedAt: Foundation.Date?

    public init(
        arn: Swift.String? = nil,
        createdAt: Foundation.Date? = nil,
        details: DataExchangeClientTypes.ResponseDetails? = nil,
        errors: [DataExchangeClientTypes.JobError]? = nil,
        id: Swift.String? = nil,
        state: DataExchangeClientTypes.State? = nil,
        type: DataExchangeClientTypes.ModelType? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.arn = arn
        self.createdAt = createdAt
        self.details = details
        self.errors = errors
        self.id = id
        self.state = state
        self.type = type
        self.updatedAt = updatedAt
    }
}

public struct CreateRevisionInput: Swift.Sendable {
    /// An optional comment about the revision.
    public var comment: Swift.String?
    /// The unique identifier for a data set.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// A revision tag is an optional label that you can assign to a revision when you create it. Each tag consists of a key and an optional value, both of which you define. When you use tagging, you can also use tag-based access control in IAM policies to control access to these data sets and revisions.
    public var tags: [Swift.String: Swift.String]?

    public init(
        comment: Swift.String? = nil,
        dataSetId: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.comment = comment
        self.dataSetId = dataSetId
        self.tags = tags
    }
}

public struct CreateRevisionOutput: Swift.Sendable {
    /// The ARN for the revision.
    public var arn: Swift.String?
    /// An optional comment about the revision.
    public var comment: Swift.String?
    /// The date and time that the revision was created, in ISO 8601 format.
    public var createdAt: Foundation.Date?
    /// The unique identifier for the data set associated with the data set revision.
    public var dataSetId: Swift.String?
    /// To publish a revision to a data set in a product, the revision must first be finalized. Finalizing a revision tells AWS Data Exchange that your changes to the assets in the revision are complete. After it's in this read-only state, you can publish the revision to your products. Finalized revisions can be published through the AWS Data Exchange console or the AWS Marketplace Catalog API, using the StartChangeSet AWS Marketplace Catalog API action. When using the API, revisions are uniquely identified by their ARN.
    public var finalized: Swift.Bool?
    /// The unique identifier for the revision.
    public var id: Swift.String?
    /// A required comment to inform subscribers of the reason their access to the revision was revoked.
    public var revocationComment: Swift.String?
    /// A status indicating that subscribers' access to the revision was revoked.
    public var revoked: Swift.Bool?
    /// The date and time that the revision was revoked, in ISO 8601 format.
    public var revokedAt: Foundation.Date?
    /// The revision ID of the owned revision corresponding to the entitled revision being viewed. This parameter is returned when a revision owner is viewing the entitled copy of its owned revision.
    public var sourceId: Swift.String?
    /// The tags for the revision.
    public var tags: [Swift.String: Swift.String]?
    /// The date and time that the revision was last updated, in ISO 8601 format.
    public var updatedAt: Foundation.Date?

    public init(
        arn: Swift.String? = nil,
        comment: Swift.String? = nil,
        createdAt: Foundation.Date? = nil,
        dataSetId: Swift.String? = nil,
        finalized: Swift.Bool? = false,
        id: Swift.String? = nil,
        revocationComment: Swift.String? = nil,
        revoked: Swift.Bool? = false,
        revokedAt: Foundation.Date? = nil,
        sourceId: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.arn = arn
        self.comment = comment
        self.createdAt = createdAt
        self.dataSetId = dataSetId
        self.finalized = finalized
        self.id = id
        self.revocationComment = revocationComment
        self.revoked = revoked
        self.revokedAt = revokedAt
        self.sourceId = sourceId
        self.tags = tags
        self.updatedAt = updatedAt
    }
}

public struct DeleteAssetInput: Swift.Sendable {
    /// The unique identifier for an asset.
    /// This member is required.
    public var assetId: Swift.String?
    /// The unique identifier for a data set.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// The unique identifier for a revision.
    /// This member is required.
    public var revisionId: Swift.String?

    public init(
        assetId: Swift.String? = nil,
        dataSetId: Swift.String? = nil,
        revisionId: Swift.String? = nil
    )
    {
        self.assetId = assetId
        self.dataSetId = dataSetId
        self.revisionId = revisionId
    }
}

public struct DeleteDataGrantInput: Swift.Sendable {
    /// The ID of the data grant to delete.
    /// This member is required.
    public var dataGrantId: Swift.String?

    public init(
        dataGrantId: Swift.String? = nil
    )
    {
        self.dataGrantId = dataGrantId
    }
}

public struct DeleteDataSetInput: Swift.Sendable {
    /// The unique identifier for a data set.
    /// This member is required.
    public var dataSetId: Swift.String?

    public init(
        dataSetId: Swift.String? = nil
    )
    {
        self.dataSetId = dataSetId
    }
}

public struct DeleteEventActionInput: Swift.Sendable {
    /// The unique identifier for the event action.
    /// This member is required.
    public var eventActionId: Swift.String?

    public init(
        eventActionId: Swift.String? = nil
    )
    {
        self.eventActionId = eventActionId
    }
}

public struct DeleteRevisionInput: Swift.Sendable {
    /// The unique identifier for a data set.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// The unique identifier for a revision.
    /// This member is required.
    public var revisionId: Swift.String?

    public init(
        dataSetId: Swift.String? = nil,
        revisionId: Swift.String? = nil
    )
    {
        self.dataSetId = dataSetId
        self.revisionId = revisionId
    }
}

public struct GetAssetInput: Swift.Sendable {
    /// The unique identifier for an asset.
    /// This member is required.
    public var assetId: Swift.String?
    /// The unique identifier for a data set.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// The unique identifier for a revision.
    /// This member is required.
    public var revisionId: Swift.String?

    public init(
        assetId: Swift.String? = nil,
        dataSetId: Swift.String? = nil,
        revisionId: Swift.String? = nil
    )
    {
        self.assetId = assetId
        self.dataSetId = dataSetId
        self.revisionId = revisionId
    }
}

public struct GetAssetOutput: Swift.Sendable {
    /// The ARN for the asset.
    public var arn: Swift.String?
    /// Details about the asset.
    public var assetDetails: DataExchangeClientTypes.AssetDetails?
    /// The type of asset that is added to a data set.
    public var assetType: DataExchangeClientTypes.AssetType?
    /// The date and time that the asset was created, in ISO 8601 format.
    public var createdAt: Foundation.Date?
    /// The unique identifier for the data set associated with this asset.
    public var dataSetId: Swift.String?
    /// The unique identifier for the asset.
    public var id: Swift.String?
    /// The name of the asset. When importing from Amazon S3, the Amazon S3 object key is used as the asset name. When exporting to Amazon S3, the asset name is used as default target Amazon S3 object key. When importing from Amazon API Gateway API, the API name is used as the asset name. When importing from Amazon Redshift, the datashare name is used as the asset name. When importing from AWS Lake Formation, the static values of "Database(s) included in the LF-tag policy" or "Table(s) included in the LF-tag policy" are used as the asset name.
    public var name: Swift.String?
    /// The unique identifier for the revision associated with this asset.
    public var revisionId: Swift.String?
    /// The asset ID of the owned asset corresponding to the entitled asset being viewed. This parameter is returned when an asset owner is viewing the entitled copy of its owned asset.
    public var sourceId: Swift.String?
    /// The date and time that the asset was last updated, in ISO 8601 format.
    public var updatedAt: Foundation.Date?

    public init(
        arn: Swift.String? = nil,
        assetDetails: DataExchangeClientTypes.AssetDetails? = nil,
        assetType: DataExchangeClientTypes.AssetType? = nil,
        createdAt: Foundation.Date? = nil,
        dataSetId: Swift.String? = nil,
        id: Swift.String? = nil,
        name: Swift.String? = nil,
        revisionId: Swift.String? = nil,
        sourceId: Swift.String? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.arn = arn
        self.assetDetails = assetDetails
        self.assetType = assetType
        self.createdAt = createdAt
        self.dataSetId = dataSetId
        self.id = id
        self.name = name
        self.revisionId = revisionId
        self.sourceId = sourceId
        self.updatedAt = updatedAt
    }
}

public struct GetDataGrantInput: Swift.Sendable {
    /// The ID of the data grant.
    /// This member is required.
    public var dataGrantId: Swift.String?

    public init(
        dataGrantId: Swift.String? = nil
    )
    {
        self.dataGrantId = dataGrantId
    }
}

public struct GetDataGrantOutput: Swift.Sendable {
    /// The acceptance state of the data grant.
    /// This member is required.
    public var acceptanceState: DataExchangeClientTypes.DataGrantAcceptanceState?
    /// The timestamp of when the data grant was accepted.
    public var acceptedAt: Foundation.Date?
    /// The Amazon Resource Name (ARN) of the data grant.
    /// This member is required.
    public var arn: Swift.String?
    /// The timestamp of when the data grant was created.
    /// This member is required.
    public var createdAt: Foundation.Date?
    /// The ID of the data set associated to the data grant.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// The description of the data grant.
    public var description: Swift.String?
    /// The timestamp of when access to the associated data set ends.
    public var endsAt: Foundation.Date?
    /// The distribution scope for the data grant.
    /// This member is required.
    public var grantDistributionScope: DataExchangeClientTypes.GrantDistributionScope?
    /// The ID of the data grant.
    /// This member is required.
    public var id: Swift.String?
    /// The name of the data grant.
    /// This member is required.
    public var name: Swift.String?
    /// The Amazon Web Services account ID of the data grant receiver.
    /// This member is required.
    public var receiverPrincipal: Swift.String?
    /// The Amazon Web Services account ID of the data grant sender.
    /// This member is required.
    public var senderPrincipal: Swift.String?
    /// The ID of the data set used to create the data grant.
    /// This member is required.
    public var sourceDataSetId: Swift.String?
    /// The tags associated to the data grant. A tag is a key-value pair.
    public var tags: [Swift.String: Swift.String]?
    /// The timestamp of when the data grant was last updated.
    /// This member is required.
    public var updatedAt: Foundation.Date?

    public init(
        acceptanceState: DataExchangeClientTypes.DataGrantAcceptanceState? = nil,
        acceptedAt: Foundation.Date? = nil,
        arn: Swift.String? = nil,
        createdAt: Foundation.Date? = nil,
        dataSetId: Swift.String? = nil,
        description: Swift.String? = nil,
        endsAt: Foundation.Date? = nil,
        grantDistributionScope: DataExchangeClientTypes.GrantDistributionScope? = nil,
        id: Swift.String? = nil,
        name: Swift.String? = nil,
        receiverPrincipal: Swift.String? = nil,
        senderPrincipal: Swift.String? = nil,
        sourceDataSetId: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.acceptanceState = acceptanceState
        self.acceptedAt = acceptedAt
        self.arn = arn
        self.createdAt = createdAt
        self.dataSetId = dataSetId
        self.description = description
        self.endsAt = endsAt
        self.grantDistributionScope = grantDistributionScope
        self.id = id
        self.name = name
        self.receiverPrincipal = receiverPrincipal
        self.senderPrincipal = senderPrincipal
        self.sourceDataSetId = sourceDataSetId
        self.tags = tags
        self.updatedAt = updatedAt
    }
}

public struct GetDataSetInput: Swift.Sendable {
    /// The unique identifier for a data set.
    /// This member is required.
    public var dataSetId: Swift.String?

    public init(
        dataSetId: Swift.String? = nil
    )
    {
        self.dataSetId = dataSetId
    }
}

public struct GetDataSetOutput: Swift.Sendable {
    /// The ARN for the data set.
    public var arn: Swift.String?
    /// The type of asset that is added to a data set.
    public var assetType: DataExchangeClientTypes.AssetType?
    /// The date and time that the data set was created, in ISO 8601 format.
    public var createdAt: Foundation.Date?
    /// The description for the data set.
    public var description: Swift.String?
    /// The unique identifier for the data set.
    public var id: Swift.String?
    /// The name of the data set.
    public var name: Swift.String?
    /// A property that defines the data set as OWNED by the account (for providers) or ENTITLED to the account (for subscribers).
    public var origin: DataExchangeClientTypes.Origin?
    /// If the origin of this data set is ENTITLED, includes the details for the product on AWS Marketplace.
    public var originDetails: DataExchangeClientTypes.OriginDetails?
    /// The data set ID of the owned data set corresponding to the entitled data set being viewed. This parameter is returned when a data set owner is viewing the entitled copy of its owned data set.
    public var sourceId: Swift.String?
    /// The tags for the data set.
    public var tags: [Swift.String: Swift.String]?
    /// The date and time that the data set was last updated, in ISO 8601 format.
    public var updatedAt: Foundation.Date?

    public init(
        arn: Swift.String? = nil,
        assetType: DataExchangeClientTypes.AssetType? = nil,
        createdAt: Foundation.Date? = nil,
        description: Swift.String? = nil,
        id: Swift.String? = nil,
        name: Swift.String? = nil,
        origin: DataExchangeClientTypes.Origin? = nil,
        originDetails: DataExchangeClientTypes.OriginDetails? = nil,
        sourceId: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.arn = arn
        self.assetType = assetType
        self.createdAt = createdAt
        self.description = description
        self.id = id
        self.name = name
        self.origin = origin
        self.originDetails = originDetails
        self.sourceId = sourceId
        self.tags = tags
        self.updatedAt = updatedAt
    }
}

public struct GetEventActionInput: Swift.Sendable {
    /// The unique identifier for the event action.
    /// This member is required.
    public var eventActionId: Swift.String?

    public init(
        eventActionId: Swift.String? = nil
    )
    {
        self.eventActionId = eventActionId
    }
}

public struct GetEventActionOutput: Swift.Sendable {
    /// What occurs after a certain event.
    public var action: DataExchangeClientTypes.Action?
    /// The ARN for the event action.
    public var arn: Swift.String?
    /// The date and time that the event action was created, in ISO 8601 format.
    public var createdAt: Foundation.Date?
    /// What occurs to start an action.
    public var event: DataExchangeClientTypes.Event?
    /// The unique identifier for the event action.
    public var id: Swift.String?
    /// The date and time that the event action was last updated, in ISO 8601 format.
    public var updatedAt: Foundation.Date?

    public init(
        action: DataExchangeClientTypes.Action? = nil,
        arn: Swift.String? = nil,
        createdAt: Foundation.Date? = nil,
        event: DataExchangeClientTypes.Event? = nil,
        id: Swift.String? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.action = action
        self.arn = arn
        self.createdAt = createdAt
        self.event = event
        self.id = id
        self.updatedAt = updatedAt
    }
}

public struct GetJobInput: Swift.Sendable {
    /// The unique identifier for a job.
    /// This member is required.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct GetJobOutput: Swift.Sendable {
    /// The ARN for the job.
    public var arn: Swift.String?
    /// The date and time that the job was created, in ISO 8601 format.
    public var createdAt: Foundation.Date?
    /// Details about the job.
    public var details: DataExchangeClientTypes.ResponseDetails?
    /// The errors associated with jobs.
    public var errors: [DataExchangeClientTypes.JobError]?
    /// The unique identifier for the job.
    public var id: Swift.String?
    /// The state of the job.
    public var state: DataExchangeClientTypes.State?
    /// The job type.
    public var type: DataExchangeClientTypes.ModelType?
    /// The date and time that the job was last updated, in ISO 8601 format.
    public var updatedAt: Foundation.Date?

    public init(
        arn: Swift.String? = nil,
        createdAt: Foundation.Date? = nil,
        details: DataExchangeClientTypes.ResponseDetails? = nil,
        errors: [DataExchangeClientTypes.JobError]? = nil,
        id: Swift.String? = nil,
        state: DataExchangeClientTypes.State? = nil,
        type: DataExchangeClientTypes.ModelType? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.arn = arn
        self.createdAt = createdAt
        self.details = details
        self.errors = errors
        self.id = id
        self.state = state
        self.type = type
        self.updatedAt = updatedAt
    }
}

public struct GetReceivedDataGrantInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the data grant.
    /// This member is required.
    public var dataGrantArn: Swift.String?

    public init(
        dataGrantArn: Swift.String? = nil
    )
    {
        self.dataGrantArn = dataGrantArn
    }
}

public struct GetReceivedDataGrantOutput: Swift.Sendable {
    /// The acceptance state of the data grant.
    /// This member is required.
    public var acceptanceState: DataExchangeClientTypes.DataGrantAcceptanceState?
    /// The timestamp of when the data grant was accepted.
    public var acceptedAt: Foundation.Date?
    /// The Amazon Resource Name (ARN) of the data grant.
    /// This member is required.
    public var arn: Swift.String?
    /// The timestamp of when the data grant was created.
    /// This member is required.
    public var createdAt: Foundation.Date?
    /// The ID of the data set associated to the data grant.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// The description of the data grant.
    public var description: Swift.String?
    /// The timestamp of when access to the associated data set ends.
    public var endsAt: Foundation.Date?
    /// The distribution scope for the data grant.
    /// This member is required.
    public var grantDistributionScope: DataExchangeClientTypes.GrantDistributionScope?
    /// The ID of the data grant.
    /// This member is required.
    public var id: Swift.String?
    /// The name of the data grant.
    /// This member is required.
    public var name: Swift.String?
    /// The Amazon Web Services account ID of the data grant receiver.
    /// This member is required.
    public var receiverPrincipal: Swift.String?
    /// The Amazon Web Services account ID of the data grant sender.
    public var senderPrincipal: Swift.String?
    /// The timestamp of when the data grant was last updated.
    /// This member is required.
    public var updatedAt: Foundation.Date?

    public init(
        acceptanceState: DataExchangeClientTypes.DataGrantAcceptanceState? = nil,
        acceptedAt: Foundation.Date? = nil,
        arn: Swift.String? = nil,
        createdAt: Foundation.Date? = nil,
        dataSetId: Swift.String? = nil,
        description: Swift.String? = nil,
        endsAt: Foundation.Date? = nil,
        grantDistributionScope: DataExchangeClientTypes.GrantDistributionScope? = nil,
        id: Swift.String? = nil,
        name: Swift.String? = nil,
        receiverPrincipal: Swift.String? = nil,
        senderPrincipal: Swift.String? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.acceptanceState = acceptanceState
        self.acceptedAt = acceptedAt
        self.arn = arn
        self.createdAt = createdAt
        self.dataSetId = dataSetId
        self.description = description
        self.endsAt = endsAt
        self.grantDistributionScope = grantDistributionScope
        self.id = id
        self.name = name
        self.receiverPrincipal = receiverPrincipal
        self.senderPrincipal = senderPrincipal
        self.updatedAt = updatedAt
    }
}

public struct GetRevisionInput: Swift.Sendable {
    /// The unique identifier for a data set.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// The unique identifier for a revision.
    /// This member is required.
    public var revisionId: Swift.String?

    public init(
        dataSetId: Swift.String? = nil,
        revisionId: Swift.String? = nil
    )
    {
        self.dataSetId = dataSetId
        self.revisionId = revisionId
    }
}

public struct GetRevisionOutput: Swift.Sendable {
    /// The ARN for the revision.
    public var arn: Swift.String?
    /// An optional comment about the revision.
    public var comment: Swift.String?
    /// The date and time that the revision was created, in ISO 8601 format.
    public var createdAt: Foundation.Date?
    /// The unique identifier for the data set associated with the data set revision.
    public var dataSetId: Swift.String?
    /// To publish a revision to a data set in a product, the revision must first be finalized. Finalizing a revision tells AWS Data Exchange that your changes to the assets in the revision are complete. After it's in this read-only state, you can publish the revision to your products. Finalized revisions can be published through the AWS Data Exchange console or the AWS Marketplace Catalog API, using the StartChangeSet AWS Marketplace Catalog API action. When using the API, revisions are uniquely identified by their ARN.
    public var finalized: Swift.Bool?
    /// The unique identifier for the revision.
    public var id: Swift.String?
    /// A required comment to inform subscribers of the reason their access to the revision was revoked.
    public var revocationComment: Swift.String?
    /// A status indicating that subscribers' access to the revision was revoked.
    public var revoked: Swift.Bool?
    /// The date and time that the revision was revoked, in ISO 8601 format.
    public var revokedAt: Foundation.Date?
    /// The revision ID of the owned revision corresponding to the entitled revision being viewed. This parameter is returned when a revision owner is viewing the entitled copy of its owned revision.
    public var sourceId: Swift.String?
    /// The tags for the revision.
    public var tags: [Swift.String: Swift.String]?
    /// The date and time that the revision was last updated, in ISO 8601 format.
    public var updatedAt: Foundation.Date?

    public init(
        arn: Swift.String? = nil,
        comment: Swift.String? = nil,
        createdAt: Foundation.Date? = nil,
        dataSetId: Swift.String? = nil,
        finalized: Swift.Bool? = false,
        id: Swift.String? = nil,
        revocationComment: Swift.String? = nil,
        revoked: Swift.Bool? = false,
        revokedAt: Foundation.Date? = nil,
        sourceId: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.arn = arn
        self.comment = comment
        self.createdAt = createdAt
        self.dataSetId = dataSetId
        self.finalized = finalized
        self.id = id
        self.revocationComment = revocationComment
        self.revoked = revoked
        self.revokedAt = revokedAt
        self.sourceId = sourceId
        self.tags = tags
        self.updatedAt = updatedAt
    }
}

public struct ListDataGrantsInput: Swift.Sendable {
    /// The maximum number of results to be included in the next page.
    public var maxResults: Swift.Int?
    /// The pagination token used to retrieve the next page of results for this operation.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = 0,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DataExchangeClientTypes {

    /// Information about a data grant.
    public struct DataGrantSummaryEntry: Swift.Sendable {
        /// The acceptance state of the data grant.
        /// This member is required.
        public var acceptanceState: DataExchangeClientTypes.DataGrantAcceptanceState?
        /// The timestamp of when the data grant was accepted.
        public var acceptedAt: Foundation.Date?
        /// The Amazon Resource Name (ARN) of the data grant.
        /// This member is required.
        public var arn: Swift.String?
        /// The timestamp of when the data grant was created.
        /// This member is required.
        public var createdAt: Foundation.Date?
        /// The ID of the data set associated to the data grant.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The timestamp of when access to the associated data set ends.
        public var endsAt: Foundation.Date?
        /// The ID of the data grant.
        /// This member is required.
        public var id: Swift.String?
        /// The name of the data grant.
        /// This member is required.
        public var name: Swift.String?
        /// The Amazon Web Services account ID of the data grant receiver.
        /// This member is required.
        public var receiverPrincipal: Swift.String?
        /// The Amazon Web Services account ID of the data grant sender.
        /// This member is required.
        public var senderPrincipal: Swift.String?
        /// The ID of the data set used to create the data grant.
        /// This member is required.
        public var sourceDataSetId: Swift.String?
        /// The timestamp of when the data grant was last updated.
        /// This member is required.
        public var updatedAt: Foundation.Date?

        public init(
            acceptanceState: DataExchangeClientTypes.DataGrantAcceptanceState? = nil,
            acceptedAt: Foundation.Date? = nil,
            arn: Swift.String? = nil,
            createdAt: Foundation.Date? = nil,
            dataSetId: Swift.String? = nil,
            endsAt: Foundation.Date? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil,
            receiverPrincipal: Swift.String? = nil,
            senderPrincipal: Swift.String? = nil,
            sourceDataSetId: Swift.String? = nil,
            updatedAt: Foundation.Date? = nil
        )
        {
            self.acceptanceState = acceptanceState
            self.acceptedAt = acceptedAt
            self.arn = arn
            self.createdAt = createdAt
            self.dataSetId = dataSetId
            self.endsAt = endsAt
            self.id = id
            self.name = name
            self.receiverPrincipal = receiverPrincipal
            self.senderPrincipal = senderPrincipal
            self.sourceDataSetId = sourceDataSetId
            self.updatedAt = updatedAt
        }
    }
}

public struct ListDataGrantsOutput: Swift.Sendable {
    /// An object that contains a list of data grant information.
    public var dataGrantSummaries: [DataExchangeClientTypes.DataGrantSummaryEntry]?
    /// The pagination token used to retrieve the next page of results for this operation.
    public var nextToken: Swift.String?

    public init(
        dataGrantSummaries: [DataExchangeClientTypes.DataGrantSummaryEntry]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.dataGrantSummaries = dataGrantSummaries
        self.nextToken = nextToken
    }
}

public struct ListDataSetRevisionsInput: Swift.Sendable {
    /// The unique identifier for a data set.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// The maximum number of results returned by a single call.
    public var maxResults: Swift.Int?
    /// The token value retrieved from a previous call to access the next page of results.
    public var nextToken: Swift.String?

    public init(
        dataSetId: Swift.String? = nil,
        maxResults: Swift.Int? = 0,
        nextToken: Swift.String? = nil
    )
    {
        self.dataSetId = dataSetId
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DataExchangeClientTypes {

    /// A revision is a container for one or more assets.
    public struct RevisionEntry: Swift.Sendable {
        /// The ARN for the revision.
        /// This member is required.
        public var arn: Swift.String?
        /// An optional comment about the revision.
        public var comment: Swift.String?
        /// The date and time that the revision was created, in ISO 8601 format.
        /// This member is required.
        public var createdAt: Foundation.Date?
        /// The unique identifier for the data set associated with the data set revision.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// To publish a revision to a data set in a product, the revision must first be finalized. Finalizing a revision tells AWS Data Exchange that your changes to the assets in the revision are complete. After it's in this read-only state, you can publish the revision to your products. Finalized revisions can be published through the AWS Data Exchange console or the AWS Marketplace Catalog API, using the StartChangeSet AWS Marketplace Catalog API action. When using the API, revisions are uniquely identified by their ARN.
        public var finalized: Swift.Bool?
        /// The unique identifier for the revision.
        /// This member is required.
        public var id: Swift.String?
        /// A required comment to inform subscribers of the reason their access to the revision was revoked.
        public var revocationComment: Swift.String?
        /// A status indicating that subscribers' access to the revision was revoked.
        public var revoked: Swift.Bool?
        /// The date and time that the revision was revoked, in ISO 8601 format.
        public var revokedAt: Foundation.Date?
        /// The revision ID of the owned revision corresponding to the entitled revision being viewed. This parameter is returned when a revision owner is viewing the entitled copy of its owned revision.
        public var sourceId: Swift.String?
        /// The date and time that the revision was last updated, in ISO 8601 format.
        /// This member is required.
        public var updatedAt: Foundation.Date?

        public init(
            arn: Swift.String? = nil,
            comment: Swift.String? = nil,
            createdAt: Foundation.Date? = nil,
            dataSetId: Swift.String? = nil,
            finalized: Swift.Bool? = false,
            id: Swift.String? = nil,
            revocationComment: Swift.String? = nil,
            revoked: Swift.Bool? = false,
            revokedAt: Foundation.Date? = nil,
            sourceId: Swift.String? = nil,
            updatedAt: Foundation.Date? = nil
        )
        {
            self.arn = arn
            self.comment = comment
            self.createdAt = createdAt
            self.dataSetId = dataSetId
            self.finalized = finalized
            self.id = id
            self.revocationComment = revocationComment
            self.revoked = revoked
            self.revokedAt = revokedAt
            self.sourceId = sourceId
            self.updatedAt = updatedAt
        }
    }
}

public struct ListDataSetRevisionsOutput: Swift.Sendable {
    /// The token value retrieved from a previous call to access the next page of results.
    public var nextToken: Swift.String?
    /// The asset objects listed by the request.
    public var revisions: [DataExchangeClientTypes.RevisionEntry]?

    public init(
        nextToken: Swift.String? = nil,
        revisions: [DataExchangeClientTypes.RevisionEntry]? = nil
    )
    {
        self.nextToken = nextToken
        self.revisions = revisions
    }
}

public struct ListDataSetsInput: Swift.Sendable {
    /// The maximum number of results returned by a single call.
    public var maxResults: Swift.Int?
    /// The token value retrieved from a previous call to access the next page of results.
    public var nextToken: Swift.String?
    /// A property that defines the data set as OWNED by the account (for providers) or ENTITLED to the account (for subscribers).
    public var origin: Swift.String?

    public init(
        maxResults: Swift.Int? = 0,
        nextToken: Swift.String? = nil,
        origin: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.origin = origin
    }
}

extension DataExchangeClientTypes {

    /// A data set is an AWS resource with one or more revisions.
    public struct DataSetEntry: Swift.Sendable {
        /// The ARN for the data set.
        /// This member is required.
        public var arn: Swift.String?
        /// The type of asset that is added to a data set.
        /// This member is required.
        public var assetType: DataExchangeClientTypes.AssetType?
        /// The date and time that the data set was created, in ISO 8601 format.
        /// This member is required.
        public var createdAt: Foundation.Date?
        /// The description for the data set.
        /// This member is required.
        public var description: Swift.String?
        /// The unique identifier for the data set.
        /// This member is required.
        public var id: Swift.String?
        /// The name of the data set.
        /// This member is required.
        public var name: Swift.String?
        /// A property that defines the data set as OWNED by the account (for providers) or ENTITLED to the account (for subscribers).
        /// This member is required.
        public var origin: DataExchangeClientTypes.Origin?
        /// If the origin of this data set is ENTITLED, includes the details for the product on AWS Marketplace.
        public var originDetails: DataExchangeClientTypes.OriginDetails?
        /// The data set ID of the owned data set corresponding to the entitled data set being viewed. This parameter is returned when a data set owner is viewing the entitled copy of its owned data set.
        public var sourceId: Swift.String?
        /// The date and time that the data set was last updated, in ISO 8601 format.
        /// This member is required.
        public var updatedAt: Foundation.Date?

        public init(
            arn: Swift.String? = nil,
            assetType: DataExchangeClientTypes.AssetType? = nil,
            createdAt: Foundation.Date? = nil,
            description: Swift.String? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil,
            origin: DataExchangeClientTypes.Origin? = nil,
            originDetails: DataExchangeClientTypes.OriginDetails? = nil,
            sourceId: Swift.String? = nil,
            updatedAt: Foundation.Date? = nil
        )
        {
            self.arn = arn
            self.assetType = assetType
            self.createdAt = createdAt
            self.description = description
            self.id = id
            self.name = name
            self.origin = origin
            self.originDetails = originDetails
            self.sourceId = sourceId
            self.updatedAt = updatedAt
        }
    }
}

public struct ListDataSetsOutput: Swift.Sendable {
    /// The data set objects listed by the request.
    public var dataSets: [DataExchangeClientTypes.DataSetEntry]?
    /// The token value retrieved from a previous call to access the next page of results.
    public var nextToken: Swift.String?

    public init(
        dataSets: [DataExchangeClientTypes.DataSetEntry]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.dataSets = dataSets
        self.nextToken = nextToken
    }
}

public struct ListEventActionsInput: Swift.Sendable {
    /// The unique identifier for the event source.
    public var eventSourceId: Swift.String?
    /// The maximum number of results returned by a single call.
    public var maxResults: Swift.Int?
    /// The token value retrieved from a previous call to access the next page of results.
    public var nextToken: Swift.String?

    public init(
        eventSourceId: Swift.String? = nil,
        maxResults: Swift.Int? = 0,
        nextToken: Swift.String? = nil
    )
    {
        self.eventSourceId = eventSourceId
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DataExchangeClientTypes {

    /// An event action is an object that defines the relationship between a specific event and an automated action that will be taken on behalf of the customer.
    public struct EventActionEntry: Swift.Sendable {
        /// What occurs after a certain event.
        /// This member is required.
        public var action: DataExchangeClientTypes.Action?
        /// The Amazon Resource Name (ARN) for the event action.
        /// This member is required.
        public var arn: Swift.String?
        /// The date and time that the event action was created, in ISO 8601 format.
        /// This member is required.
        public var createdAt: Foundation.Date?
        /// What occurs to start an action.
        /// This member is required.
        public var event: DataExchangeClientTypes.Event?
        /// The unique identifier for the event action.
        /// This member is required.
        public var id: Swift.String?
        /// The date and time that the event action was last updated, in ISO 8601 format.
        /// This member is required.
        public var updatedAt: Foundation.Date?

        public init(
            action: DataExchangeClientTypes.Action? = nil,
            arn: Swift.String? = nil,
            createdAt: Foundation.Date? = nil,
            event: DataExchangeClientTypes.Event? = nil,
            id: Swift.String? = nil,
            updatedAt: Foundation.Date? = nil
        )
        {
            self.action = action
            self.arn = arn
            self.createdAt = createdAt
            self.event = event
            self.id = id
            self.updatedAt = updatedAt
        }
    }
}

public struct ListEventActionsOutput: Swift.Sendable {
    /// The event action objects listed by the request.
    public var eventActions: [DataExchangeClientTypes.EventActionEntry]?
    /// The token value retrieved from a previous call to access the next page of results.
    public var nextToken: Swift.String?

    public init(
        eventActions: [DataExchangeClientTypes.EventActionEntry]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.eventActions = eventActions
        self.nextToken = nextToken
    }
}

public struct ListJobsInput: Swift.Sendable {
    /// The unique identifier for a data set.
    public var dataSetId: Swift.String?
    /// The maximum number of results returned by a single call.
    public var maxResults: Swift.Int?
    /// The token value retrieved from a previous call to access the next page of results.
    public var nextToken: Swift.String?
    /// The unique identifier for a revision.
    public var revisionId: Swift.String?

    public init(
        dataSetId: Swift.String? = nil,
        maxResults: Swift.Int? = 0,
        nextToken: Swift.String? = nil,
        revisionId: Swift.String? = nil
    )
    {
        self.dataSetId = dataSetId
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.revisionId = revisionId
    }
}

extension DataExchangeClientTypes {

    /// AWS Data Exchange Jobs are asynchronous import or export operations used to create or copy assets. A data set owner can both import and export as they see fit. Someone with an entitlement to a data set can only export. Jobs are deleted 90 days after they are created.
    public struct JobEntry: Swift.Sendable {
        /// The ARN for the job.
        /// This member is required.
        public var arn: Swift.String?
        /// The date and time that the job was created, in ISO 8601 format.
        /// This member is required.
        public var createdAt: Foundation.Date?
        /// Details of the operation to be performed by the job, such as export destination details or import source details.
        /// This member is required.
        public var details: DataExchangeClientTypes.ResponseDetails?
        /// Errors for jobs.
        public var errors: [DataExchangeClientTypes.JobError]?
        /// The unique identifier for the job.
        /// This member is required.
        public var id: Swift.String?
        /// The state of the job.
        /// This member is required.
        public var state: DataExchangeClientTypes.State?
        /// The job type.
        /// This member is required.
        public var type: DataExchangeClientTypes.ModelType?
        /// The date and time that the job was last updated, in ISO 8601 format.
        /// This member is required.
        public var updatedAt: Foundation.Date?

        public init(
            arn: Swift.String? = nil,
            createdAt: Foundation.Date? = nil,
            details: DataExchangeClientTypes.ResponseDetails? = nil,
            errors: [DataExchangeClientTypes.JobError]? = nil,
            id: Swift.String? = nil,
            state: DataExchangeClientTypes.State? = nil,
            type: DataExchangeClientTypes.ModelType? = nil,
            updatedAt: Foundation.Date? = nil
        )
        {
            self.arn = arn
            self.createdAt = createdAt
            self.details = details
            self.errors = errors
            self.id = id
            self.state = state
            self.type = type
            self.updatedAt = updatedAt
        }
    }
}

public struct ListJobsOutput: Swift.Sendable {
    /// The jobs listed by the request.
    public var jobs: [DataExchangeClientTypes.JobEntry]?
    /// The token value retrieved from a previous call to access the next page of results.
    public var nextToken: Swift.String?

    public init(
        jobs: [DataExchangeClientTypes.JobEntry]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.jobs = jobs
        self.nextToken = nextToken
    }
}

public struct ListReceivedDataGrantsInput: Swift.Sendable {
    /// The acceptance state of the data grants to list.
    public var acceptanceState: [DataExchangeClientTypes.AcceptanceStateFilterValue]?
    /// The maximum number of results to be included in the next page.
    public var maxResults: Swift.Int?
    /// The pagination token used to retrieve the next page of results for this operation.
    public var nextToken: Swift.String?

    public init(
        acceptanceState: [DataExchangeClientTypes.AcceptanceStateFilterValue]? = nil,
        maxResults: Swift.Int? = 0,
        nextToken: Swift.String? = nil
    )
    {
        self.acceptanceState = acceptanceState
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DataExchangeClientTypes {

    /// Information about a received data grant.
    public struct ReceivedDataGrantSummariesEntry: Swift.Sendable {
        /// The acceptance state of the data grant.
        /// This member is required.
        public var acceptanceState: DataExchangeClientTypes.DataGrantAcceptanceState?
        /// The timestamp of when the data grant was accepted.
        public var acceptedAt: Foundation.Date?
        /// The Amazon Resource Name (ARN) of the data grant.
        /// This member is required.
        public var arn: Swift.String?
        /// The timestamp of when the data grant was created.
        /// This member is required.
        public var createdAt: Foundation.Date?
        /// The ID of the data set associated to the data grant.
        /// This member is required.
        public var dataSetId: Swift.String?
        /// The timestamp of when access to the associated data set ends.
        public var endsAt: Foundation.Date?
        /// The ID of the data grant.
        /// This member is required.
        public var id: Swift.String?
        /// The name of the data grant.
        /// This member is required.
        public var name: Swift.String?
        /// The Amazon Web Services account ID of the data grant receiver.
        /// This member is required.
        public var receiverPrincipal: Swift.String?
        /// The Amazon Web Services account ID of the data grant sender.
        /// This member is required.
        public var senderPrincipal: Swift.String?
        /// The timestamp of when the data grant was last updated.
        /// This member is required.
        public var updatedAt: Foundation.Date?

        public init(
            acceptanceState: DataExchangeClientTypes.DataGrantAcceptanceState? = nil,
            acceptedAt: Foundation.Date? = nil,
            arn: Swift.String? = nil,
            createdAt: Foundation.Date? = nil,
            dataSetId: Swift.String? = nil,
            endsAt: Foundation.Date? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil,
            receiverPrincipal: Swift.String? = nil,
            senderPrincipal: Swift.String? = nil,
            updatedAt: Foundation.Date? = nil
        )
        {
            self.acceptanceState = acceptanceState
            self.acceptedAt = acceptedAt
            self.arn = arn
            self.createdAt = createdAt
            self.dataSetId = dataSetId
            self.endsAt = endsAt
            self.id = id
            self.name = name
            self.receiverPrincipal = receiverPrincipal
            self.senderPrincipal = senderPrincipal
            self.updatedAt = updatedAt
        }
    }
}

public struct ListReceivedDataGrantsOutput: Swift.Sendable {
    /// An object that contains a list of received data grant information.
    public var dataGrantSummaries: [DataExchangeClientTypes.ReceivedDataGrantSummariesEntry]?
    /// The pagination token used to retrieve the next page of results for this operation.
    public var nextToken: Swift.String?

    public init(
        dataGrantSummaries: [DataExchangeClientTypes.ReceivedDataGrantSummariesEntry]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.dataGrantSummaries = dataGrantSummaries
        self.nextToken = nextToken
    }
}

public struct ListRevisionAssetsInput: Swift.Sendable {
    /// The unique identifier for a data set.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// The maximum number of results returned by a single call.
    public var maxResults: Swift.Int?
    /// The token value retrieved from a previous call to access the next page of results.
    public var nextToken: Swift.String?
    /// The unique identifier for a revision.
    /// This member is required.
    public var revisionId: Swift.String?

    public init(
        dataSetId: Swift.String? = nil,
        maxResults: Swift.Int? = 0,
        nextToken: Swift.String? = nil,
        revisionId: Swift.String? = nil
    )
    {
        self.dataSetId = dataSetId
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.revisionId = revisionId
    }
}

public struct ListRevisionAssetsOutput: Swift.Sendable {
    /// The asset objects listed by the request.
    public var assets: [DataExchangeClientTypes.AssetEntry]?
    /// The token value retrieved from a previous call to access the next page of results.
    public var nextToken: Swift.String?

    public init(
        assets: [DataExchangeClientTypes.AssetEntry]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.assets = assets
        self.nextToken = nextToken
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// An Amazon Resource Name (ARN) that uniquely identifies an AWS resource.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// A label that consists of a customer-defined key and an optional value.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

public struct RevokeRevisionInput: Swift.Sendable {
    /// The unique identifier for a data set.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// The unique identifier for a revision.
    /// This member is required.
    public var revisionId: Swift.String?
    /// A required comment to inform subscribers of the reason their access to the revision was revoked.
    /// This member is required.
    public var revocationComment: Swift.String?

    public init(
        dataSetId: Swift.String? = nil,
        revisionId: Swift.String? = nil,
        revocationComment: Swift.String? = nil
    )
    {
        self.dataSetId = dataSetId
        self.revisionId = revisionId
        self.revocationComment = revocationComment
    }
}

public struct RevokeRevisionOutput: Swift.Sendable {
    /// The ARN for the revision.
    public var arn: Swift.String?
    /// An optional comment about the revision.
    public var comment: Swift.String?
    /// The date and time that the revision was created, in ISO 8601 format.
    public var createdAt: Foundation.Date?
    /// The unique identifier for the data set associated with the data set revision.
    public var dataSetId: Swift.String?
    /// To publish a revision to a data set in a product, the revision must first be finalized. Finalizing a revision tells AWS Data Exchange that changes to the assets in the revision are complete. After it's in this read-only state, you can publish the revision to your products. Finalized revisions can be published through the AWS Data Exchange console or the AWS Marketplace Catalog API, using the StartChangeSet AWS Marketplace Catalog API action. When using the API, revisions are uniquely identified by their ARN.
    public var finalized: Swift.Bool?
    /// The unique identifier for the revision.
    public var id: Swift.String?
    /// A required comment to inform subscribers of the reason their access to the revision was revoked.
    public var revocationComment: Swift.String?
    /// A status indicating that subscribers' access to the revision was revoked.
    public var revoked: Swift.Bool?
    /// The date and time that the revision was revoked, in ISO 8601 format.
    public var revokedAt: Foundation.Date?
    /// The revision ID of the owned revision corresponding to the entitled revision being viewed. This parameter is returned when a revision owner is viewing the entitled copy of its owned revision.
    public var sourceId: Swift.String?
    /// The date and time that the revision was last updated, in ISO 8601 format.
    public var updatedAt: Foundation.Date?

    public init(
        arn: Swift.String? = nil,
        comment: Swift.String? = nil,
        createdAt: Foundation.Date? = nil,
        dataSetId: Swift.String? = nil,
        finalized: Swift.Bool? = false,
        id: Swift.String? = nil,
        revocationComment: Swift.String? = nil,
        revoked: Swift.Bool? = false,
        revokedAt: Foundation.Date? = nil,
        sourceId: Swift.String? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.arn = arn
        self.comment = comment
        self.createdAt = createdAt
        self.dataSetId = dataSetId
        self.finalized = finalized
        self.id = id
        self.revocationComment = revocationComment
        self.revoked = revoked
        self.revokedAt = revokedAt
        self.sourceId = sourceId
        self.updatedAt = updatedAt
    }
}

public struct SendApiAssetInput: Swift.Sendable {
    /// Asset ID value for the API request.
    /// This member is required.
    public var assetId: Swift.String?
    /// The request body.
    public var body: Swift.String?
    /// Data set ID value for the API request.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// HTTP method value for the API request. Alternatively, you can use the appropriate verb in your request.
    public var method: Swift.String?
    /// URI path value for the API request. Alternatively, you can set the URI path directly by invoking /v1/{pathValue}.
    public var path: Swift.String?
    /// Attach query string parameters to the end of the URI (for example, /v1/examplePath?exampleParam=exampleValue).
    public var queryStringParameters: [Swift.String: Swift.String]?
    /// Any header value prefixed with x-amzn-dataexchange-header- will have that stripped before sending the Asset API request. Use this when you want to override a header that AWS Data Exchange uses. Alternatively, you can use the header without a prefix to the HTTP request.
    public var requestHeaders: [Swift.String: Swift.String]?
    /// Revision ID value for the API request.
    /// This member is required.
    public var revisionId: Swift.String?

    public init(
        assetId: Swift.String? = nil,
        body: Swift.String? = nil,
        dataSetId: Swift.String? = nil,
        method: Swift.String? = nil,
        path: Swift.String? = nil,
        queryStringParameters: [Swift.String: Swift.String]? = nil,
        requestHeaders: [Swift.String: Swift.String]? = nil,
        revisionId: Swift.String? = nil
    )
    {
        self.assetId = assetId
        self.body = body
        self.dataSetId = dataSetId
        self.method = method
        self.path = path
        self.queryStringParameters = queryStringParameters
        self.requestHeaders = requestHeaders
        self.revisionId = revisionId
    }
}

public struct SendApiAssetOutput: Swift.Sendable {
    /// The response body from the underlying API tracked by the API asset.
    public var body: Swift.String?
    /// The response headers from the underlying API tracked by the API asset.
    public var responseHeaders: [Swift.String: Swift.String]?

    public init(
        body: Swift.String? = nil,
        responseHeaders: [Swift.String: Swift.String]? = nil
    )
    {
        self.body = body
        self.responseHeaders = responseHeaders
    }
}

extension DataExchangeClientTypes {

    /// Extra details specific to a data update type notification.
    public struct DataUpdateRequestDetails: Swift.Sendable {
        /// A datetime in the past when the data was updated. This typically means that the underlying resource supporting the data set was updated.
        public var dataUpdatedAt: Foundation.Date?

        public init(
            dataUpdatedAt: Foundation.Date? = nil
        )
        {
            self.dataUpdatedAt = dataUpdatedAt
        }
    }
}

extension DataExchangeClientTypes {

    /// Extra details specific to a deprecation type notification.
    public struct DeprecationRequestDetails: Swift.Sendable {
        /// A datetime in the future when the data set will be deprecated.
        /// This member is required.
        public var deprecationAt: Foundation.Date?

        public init(
            deprecationAt: Foundation.Date? = nil
        )
        {
            self.deprecationAt = deprecationAt
        }
    }
}

extension DataExchangeClientTypes {

    public enum SchemaChangeType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case add
        case modify
        case remove
        case sdkUnknown(Swift.String)

        public static var allCases: [SchemaChangeType] {
            return [
                .add,
                .modify,
                .remove
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .add: return "ADD"
            case .modify: return "MODIFY"
            case .remove: return "REMOVE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataExchangeClientTypes {

    /// Object encompassing information about a schema change to a single, particular field, a notification can have up to 100 of these.
    public struct SchemaChangeDetails: Swift.Sendable {
        /// Description of what's changing about this field. This value can be up to 512 characters long.
        public var description: Swift.String?
        /// Name of the changing field. This value can be up to 255 characters long.
        /// This member is required.
        public var name: Swift.String?
        /// Is the field being added, removed, or modified?
        /// This member is required.
        public var type: DataExchangeClientTypes.SchemaChangeType?

        public init(
            description: Swift.String? = nil,
            name: Swift.String? = nil,
            type: DataExchangeClientTypes.SchemaChangeType? = nil
        )
        {
            self.description = description
            self.name = name
            self.type = type
        }
    }
}

extension DataExchangeClientTypes {

    /// Extra details specific to this schema change type notification.
    public struct SchemaChangeRequestDetails: Swift.Sendable {
        /// List of schema changes happening in the scope of this notification. This can have up to 100 entries.
        public var changes: [DataExchangeClientTypes.SchemaChangeDetails]?
        /// A date in the future when the schema change is taking effect.
        /// This member is required.
        public var schemaChangeAt: Foundation.Date?

        public init(
            changes: [DataExchangeClientTypes.SchemaChangeDetails]? = nil,
            schemaChangeAt: Foundation.Date? = nil
        )
        {
            self.changes = changes
            self.schemaChangeAt = schemaChangeAt
        }
    }
}

extension DataExchangeClientTypes {

    /// Extra details specific to this notification.
    public struct NotificationDetails: Swift.Sendable {
        /// Extra details specific to a data update type notification.
        public var dataUpdate: DataExchangeClientTypes.DataUpdateRequestDetails?
        /// Extra details specific to a deprecation type notification.
        public var deprecation: DataExchangeClientTypes.DeprecationRequestDetails?
        /// Extra details specific to a schema change type notification.
        public var schemaChange: DataExchangeClientTypes.SchemaChangeRequestDetails?

        public init(
            dataUpdate: DataExchangeClientTypes.DataUpdateRequestDetails? = nil,
            deprecation: DataExchangeClientTypes.DeprecationRequestDetails? = nil,
            schemaChange: DataExchangeClientTypes.SchemaChangeRequestDetails? = nil
        )
        {
            self.dataUpdate = dataUpdate
            self.deprecation = deprecation
            self.schemaChange = schemaChange
        }
    }
}

extension DataExchangeClientTypes {

    /// Extra details specific to the affected scope in this LF data set.
    public struct LakeFormationTagPolicyDetails: Swift.Sendable {
        /// The underlying Glue database that the notification is referring to.
        public var database: Swift.String?
        /// The underlying Glue table that the notification is referring to.
        public var table: Swift.String?

        public init(
            database: Swift.String? = nil,
            table: Swift.String? = nil
        )
        {
            self.database = database
            self.table = table
        }
    }
}

extension DataExchangeClientTypes {

    /// Extra details specific to the affected scope in this Redshift data set.
    public struct RedshiftDataShareDetails: Swift.Sendable {
        /// The ARN of the underlying Redshift data share that is being affected by this notification.
        /// This member is required.
        public var arn: Swift.String?
        /// The database name in the Redshift data share that is being affected by this notification.
        /// This member is required.
        public var database: Swift.String?
        /// A function name in the Redshift database that is being affected by this notification.
        public var function: Swift.String?
        /// A schema name in the Redshift database that is being affected by this notification.
        public var schema: Swift.String?
        /// A table name in the Redshift database that is being affected by this notification.
        public var table: Swift.String?
        /// A view name in the Redshift database that is being affected by this notification.
        public var view: Swift.String?

        public init(
            arn: Swift.String? = nil,
            database: Swift.String? = nil,
            function: Swift.String? = nil,
            schema: Swift.String? = nil,
            table: Swift.String? = nil,
            view: Swift.String? = nil
        )
        {
            self.arn = arn
            self.database = database
            self.function = function
            self.schema = schema
            self.table = table
            self.view = view
        }
    }
}

extension DataExchangeClientTypes {

    /// Extra details specific to the affected scope in this S3 Data Access data set.
    public struct S3DataAccessDetails: Swift.Sendable {
        /// A list of the key prefixes affected by this notification. This can have up to 50 entries.
        public var keyPrefixes: [Swift.String]?
        /// A list of the keys affected by this notification. This can have up to 50 entries.
        public var keys: [Swift.String]?

        public init(
            keyPrefixes: [Swift.String]? = nil,
            keys: [Swift.String]? = nil
        )
        {
            self.keyPrefixes = keyPrefixes
            self.keys = keys
        }
    }
}

extension DataExchangeClientTypes {

    /// Details about the scope of the notifications such as the affected resources.
    public struct ScopeDetails: Swift.Sendable {
        /// Underlying LF resources that will be affected by this notification.
        public var lakeFormationTagPolicies: [DataExchangeClientTypes.LakeFormationTagPolicyDetails]?
        /// Underlying Redshift resources that will be affected by this notification.
        public var redshiftDataShares: [DataExchangeClientTypes.RedshiftDataShareDetails]?
        /// Underlying S3 resources that will be affected by this notification.
        public var s3DataAccesses: [DataExchangeClientTypes.S3DataAccessDetails]?

        public init(
            lakeFormationTagPolicies: [DataExchangeClientTypes.LakeFormationTagPolicyDetails]? = nil,
            redshiftDataShares: [DataExchangeClientTypes.RedshiftDataShareDetails]? = nil,
            s3DataAccesses: [DataExchangeClientTypes.S3DataAccessDetails]? = nil
        )
        {
            self.lakeFormationTagPolicies = lakeFormationTagPolicies
            self.redshiftDataShares = redshiftDataShares
            self.s3DataAccesses = s3DataAccesses
        }
    }
}

extension DataExchangeClientTypes {

    public enum NotificationType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case dataDelay
        case dataUpdate
        case deprecation
        case schemaChange
        case sdkUnknown(Swift.String)

        public static var allCases: [NotificationType] {
            return [
                .dataDelay,
                .dataUpdate,
                .deprecation,
                .schemaChange
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .dataDelay: return "DATA_DELAY"
            case .dataUpdate: return "DATA_UPDATE"
            case .deprecation: return "DEPRECATION"
            case .schemaChange: return "SCHEMA_CHANGE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct SendDataSetNotificationInput: Swift.Sendable {
    /// Idempotency key for the notification, this key allows us to deduplicate notifications that are sent in quick succession erroneously.
    public var clientToken: Swift.String?
    /// Free-form text field for providers to add information about their notifications.
    public var comment: Swift.String?
    /// Affected data set of the notification.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// Extra details specific to this notification type.
    public var details: DataExchangeClientTypes.NotificationDetails?
    /// Affected scope of this notification such as the underlying resources affected by the notification event.
    public var scope: DataExchangeClientTypes.ScopeDetails?
    /// The type of the notification. Describing the kind of event the notification is alerting you to.
    /// This member is required.
    public var type: DataExchangeClientTypes.NotificationType?

    public init(
        clientToken: Swift.String? = nil,
        comment: Swift.String? = nil,
        dataSetId: Swift.String? = nil,
        details: DataExchangeClientTypes.NotificationDetails? = nil,
        scope: DataExchangeClientTypes.ScopeDetails? = nil,
        type: DataExchangeClientTypes.NotificationType? = nil
    )
    {
        self.clientToken = clientToken
        self.comment = comment
        self.dataSetId = dataSetId
        self.details = details
        self.scope = scope
        self.type = type
    }
}

public struct SendDataSetNotificationOutput: Swift.Sendable {

    public init() { }
}

public struct StartJobInput: Swift.Sendable {
    /// The unique identifier for a job.
    /// This member is required.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StartJobOutput: Swift.Sendable {

    public init() { }
}

public struct TagResourceInput: Swift.Sendable {
    /// An Amazon Resource Name (ARN) that uniquely identifies an AWS resource.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// A label that consists of a customer-defined key and an optional value.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct UntagResourceInput: Swift.Sendable {
    /// An Amazon Resource Name (ARN) that uniquely identifies an AWS resource.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The key tags.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UpdateAssetInput: Swift.Sendable {
    /// The unique identifier for an asset.
    /// This member is required.
    public var assetId: Swift.String?
    /// The unique identifier for a data set.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// The name of the asset. When importing from Amazon S3, the Amazon S3 object key is used as the asset name. When exporting to Amazon S3, the asset name is used as default target Amazon S3 object key. When importing from Amazon API Gateway API, the API name is used as the asset name. When importing from Amazon Redshift, the datashare name is used as the asset name. When importing from AWS Lake Formation, the static values of "Database(s) included in the LF-tag policy" or "Table(s) included in LF-tag policy" are used as the name.
    /// This member is required.
    public var name: Swift.String?
    /// The unique identifier for a revision.
    /// This member is required.
    public var revisionId: Swift.String?

    public init(
        assetId: Swift.String? = nil,
        dataSetId: Swift.String? = nil,
        name: Swift.String? = nil,
        revisionId: Swift.String? = nil
    )
    {
        self.assetId = assetId
        self.dataSetId = dataSetId
        self.name = name
        self.revisionId = revisionId
    }
}

public struct UpdateAssetOutput: Swift.Sendable {
    /// The ARN for the asset.
    public var arn: Swift.String?
    /// Details about the asset.
    public var assetDetails: DataExchangeClientTypes.AssetDetails?
    /// The type of asset that is added to a data set.
    public var assetType: DataExchangeClientTypes.AssetType?
    /// The date and time that the asset was created, in ISO 8601 format.
    public var createdAt: Foundation.Date?
    /// The unique identifier for the data set associated with this asset.
    public var dataSetId: Swift.String?
    /// The unique identifier for the asset.
    public var id: Swift.String?
    /// The name of the asset. When importing from Amazon S3, the Amazon S3 object key is used as the asset name. When exporting to Amazon S3, the asset name is used as default target Amazon S3 object key. When importing from Amazon API Gateway API, the API name is used as the asset name. When importing from Amazon Redshift, the datashare name is used as the asset name. When importing from AWS Lake Formation, the static values of "Database(s) included in the LF-tag policy"- or "Table(s) included in LF-tag policy" are used as the asset name.
    public var name: Swift.String?
    /// The unique identifier for the revision associated with this asset.
    public var revisionId: Swift.String?
    /// The asset ID of the owned asset corresponding to the entitled asset being viewed. This parameter is returned when an asset owner is viewing the entitled copy of its owned asset.
    public var sourceId: Swift.String?
    /// The date and time that the asset was last updated, in ISO 8601 format.
    public var updatedAt: Foundation.Date?

    public init(
        arn: Swift.String? = nil,
        assetDetails: DataExchangeClientTypes.AssetDetails? = nil,
        assetType: DataExchangeClientTypes.AssetType? = nil,
        createdAt: Foundation.Date? = nil,
        dataSetId: Swift.String? = nil,
        id: Swift.String? = nil,
        name: Swift.String? = nil,
        revisionId: Swift.String? = nil,
        sourceId: Swift.String? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.arn = arn
        self.assetDetails = assetDetails
        self.assetType = assetType
        self.createdAt = createdAt
        self.dataSetId = dataSetId
        self.id = id
        self.name = name
        self.revisionId = revisionId
        self.sourceId = sourceId
        self.updatedAt = updatedAt
    }
}

public struct UpdateDataSetInput: Swift.Sendable {
    /// The unique identifier for a data set.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// The description for the data set.
    public var description: Swift.String?
    /// The name of the data set.
    public var name: Swift.String?

    public init(
        dataSetId: Swift.String? = nil,
        description: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.dataSetId = dataSetId
        self.description = description
        self.name = name
    }
}

public struct UpdateDataSetOutput: Swift.Sendable {
    /// The ARN for the data set.
    public var arn: Swift.String?
    /// The type of asset that is added to a data set.
    public var assetType: DataExchangeClientTypes.AssetType?
    /// The date and time that the data set was created, in ISO 8601 format.
    public var createdAt: Foundation.Date?
    /// The description for the data set.
    public var description: Swift.String?
    /// The unique identifier for the data set.
    public var id: Swift.String?
    /// The name of the data set.
    public var name: Swift.String?
    /// A property that defines the data set as OWNED by the account (for providers) or ENTITLED to the account (for subscribers).
    public var origin: DataExchangeClientTypes.Origin?
    /// If the origin of this data set is ENTITLED, includes the details for the product on AWS Marketplace.
    public var originDetails: DataExchangeClientTypes.OriginDetails?
    /// The data set ID of the owned data set corresponding to the entitled data set being viewed. This parameter is returned when a data set owner is viewing the entitled copy of its owned data set.
    public var sourceId: Swift.String?
    /// The date and time that the data set was last updated, in ISO 8601 format.
    public var updatedAt: Foundation.Date?

    public init(
        arn: Swift.String? = nil,
        assetType: DataExchangeClientTypes.AssetType? = nil,
        createdAt: Foundation.Date? = nil,
        description: Swift.String? = nil,
        id: Swift.String? = nil,
        name: Swift.String? = nil,
        origin: DataExchangeClientTypes.Origin? = nil,
        originDetails: DataExchangeClientTypes.OriginDetails? = nil,
        sourceId: Swift.String? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.arn = arn
        self.assetType = assetType
        self.createdAt = createdAt
        self.description = description
        self.id = id
        self.name = name
        self.origin = origin
        self.originDetails = originDetails
        self.sourceId = sourceId
        self.updatedAt = updatedAt
    }
}

public struct UpdateEventActionInput: Swift.Sendable {
    /// What occurs after a certain event.
    public var action: DataExchangeClientTypes.Action?
    /// The unique identifier for the event action.
    /// This member is required.
    public var eventActionId: Swift.String?

    public init(
        action: DataExchangeClientTypes.Action? = nil,
        eventActionId: Swift.String? = nil
    )
    {
        self.action = action
        self.eventActionId = eventActionId
    }
}

public struct UpdateEventActionOutput: Swift.Sendable {
    /// What occurs after a certain event.
    public var action: DataExchangeClientTypes.Action?
    /// The ARN for the event action.
    public var arn: Swift.String?
    /// The date and time that the event action was created, in ISO 8601 format.
    public var createdAt: Foundation.Date?
    /// What occurs to start an action.
    public var event: DataExchangeClientTypes.Event?
    /// The unique identifier for the event action.
    public var id: Swift.String?
    /// The date and time that the event action was last updated, in ISO 8601 format.
    public var updatedAt: Foundation.Date?

    public init(
        action: DataExchangeClientTypes.Action? = nil,
        arn: Swift.String? = nil,
        createdAt: Foundation.Date? = nil,
        event: DataExchangeClientTypes.Event? = nil,
        id: Swift.String? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.action = action
        self.arn = arn
        self.createdAt = createdAt
        self.event = event
        self.id = id
        self.updatedAt = updatedAt
    }
}

public struct UpdateRevisionInput: Swift.Sendable {
    /// An optional comment about the revision.
    public var comment: Swift.String?
    /// The unique identifier for a data set.
    /// This member is required.
    public var dataSetId: Swift.String?
    /// Finalizing a revision tells AWS Data Exchange that your changes to the assets in the revision are complete. After it's in this read-only state, you can publish the revision to your products.
    public var finalized: Swift.Bool?
    /// The unique identifier for a revision.
    /// This member is required.
    public var revisionId: Swift.String?

    public init(
        comment: Swift.String? = nil,
        dataSetId: Swift.String? = nil,
        finalized: Swift.Bool? = false,
        revisionId: Swift.String? = nil
    )
    {
        self.comment = comment
        self.dataSetId = dataSetId
        self.finalized = finalized
        self.revisionId = revisionId
    }
}

public struct UpdateRevisionOutput: Swift.Sendable {
    /// The ARN for the revision.
    public var arn: Swift.String?
    /// An optional comment about the revision.
    public var comment: Swift.String?
    /// The date and time that the revision was created, in ISO 8601 format.
    public var createdAt: Foundation.Date?
    /// The unique identifier for the data set associated with the data set revision.
    public var dataSetId: Swift.String?
    /// To publish a revision to a data set in a product, the revision must first be finalized. Finalizing a revision tells AWS Data Exchange that changes to the assets in the revision are complete. After it's in this read-only state, you can publish the revision to your products. Finalized revisions can be published through the AWS Data Exchange console or the AWS Marketplace Catalog API, using the StartChangeSet AWS Marketplace Catalog API action. When using the API, revisions are uniquely identified by their ARN.
    public var finalized: Swift.Bool?
    /// The unique identifier for the revision.
    public var id: Swift.String?
    /// A required comment to inform subscribers of the reason their access to the revision was revoked.
    public var revocationComment: Swift.String?
    /// A status indicating that subscribers' access to the revision was revoked.
    public var revoked: Swift.Bool?
    /// The date and time that the revision was revoked, in ISO 8601 format.
    public var revokedAt: Foundation.Date?
    /// The revision ID of the owned revision corresponding to the entitled revision being viewed. This parameter is returned when a revision owner is viewing the entitled copy of its owned revision.
    public var sourceId: Swift.String?
    /// The date and time that the revision was last updated, in ISO 8601 format.
    public var updatedAt: Foundation.Date?

    public init(
        arn: Swift.String? = nil,
        comment: Swift.String? = nil,
        createdAt: Foundation.Date? = nil,
        dataSetId: Swift.String? = nil,
        finalized: Swift.Bool? = false,
        id: Swift.String? = nil,
        revocationComment: Swift.String? = nil,
        revoked: Swift.Bool? = false,
        revokedAt: Foundation.Date? = nil,
        sourceId: Swift.String? = nil,
        updatedAt: Foundation.Date? = nil
    )
    {
        self.arn = arn
        self.comment = comment
        self.createdAt = createdAt
        self.dataSetId = dataSetId
        self.finalized = finalized
        self.id = id
        self.revocationComment = revocationComment
        self.revoked = revoked
        self.revokedAt = revokedAt
        self.sourceId = sourceId
        self.updatedAt = updatedAt
    }
}

extension AcceptDataGrantInput {

    static func urlPathProvider(_ value: AcceptDataGrantInput) -> Swift.String? {
        guard let dataGrantArn = value.dataGrantArn else {
            return nil
        }
        return "/v1/data-grants/\(dataGrantArn.urlPercentEncoding())/accept"
    }
}

extension CancelJobInput {

    static func urlPathProvider(_ value: CancelJobInput) -> Swift.String? {
        guard let jobId = value.jobId else {
            return nil
        }
        return "/v1/jobs/\(jobId.urlPercentEncoding())"
    }
}

extension CreateDataGrantInput {

    static func urlPathProvider(_ value: CreateDataGrantInput) -> Swift.String? {
        return "/v1/data-grants"
    }
}

extension CreateDataSetInput {

    static func urlPathProvider(_ value: CreateDataSetInput) -> Swift.String? {
        return "/v1/data-sets"
    }
}

extension CreateEventActionInput {

    static func urlPathProvider(_ value: CreateEventActionInput) -> Swift.String? {
        return "/v1/event-actions"
    }
}

extension CreateJobInput {

    static func urlPathProvider(_ value: CreateJobInput) -> Swift.String? {
        return "/v1/jobs"
    }
}

extension CreateRevisionInput {

    static func urlPathProvider(_ value: CreateRevisionInput) -> Swift.String? {
        guard let dataSetId = value.dataSetId else {
            return nil
        }
        return "/v1/data-sets/\(dataSetId.urlPercentEncoding())/revisions"
    }
}

extension DeleteAssetInput {

    static func urlPathProvider(_ value: DeleteAssetInput) -> Swift.String? {
        guard let dataSetId = value.dataSetId else {
            return nil
        }
        guard let revisionId = value.revisionId else {
            return nil
        }
        guard let assetId = value.assetId else {
            return nil
        }
        return "/v1/data-sets/\(dataSetId.urlPercentEncoding())/revisions/\(revisionId.urlPercentEncoding())/assets/\(assetId.urlPercentEncoding())"
    }
}

extension DeleteDataGrantInput {

    static func urlPathProvider(_ value: DeleteDataGrantInput) -> Swift.String? {
        guard let dataGrantId = value.dataGrantId else {
            return nil
        }
        return "/v1/data-grants/\(dataGrantId.urlPercentEncoding())"
    }
}

extension DeleteDataSetInput {

    static func urlPathProvider(_ value: DeleteDataSetInput) -> Swift.String? {
        guard let dataSetId = value.dataSetId else {
            return nil
        }
        return "/v1/data-sets/\(dataSetId.urlPercentEncoding())"
    }
}

extension DeleteEventActionInput {

    static func urlPathProvider(_ value: DeleteEventActionInput) -> Swift.String? {
        guard let eventActionId = value.eventActionId else {
            return nil
        }
        return "/v1/event-actions/\(eventActionId.urlPercentEncoding())"
    }
}

extension DeleteRevisionInput {

    static func urlPathProvider(_ value: DeleteRevisionInput) -> Swift.String? {
        guard let dataSetId = value.dataSetId else {
            return nil
        }
        guard let revisionId = value.revisionId else {
            return nil
        }
        return "/v1/data-sets/\(dataSetId.urlPercentEncoding())/revisions/\(revisionId.urlPercentEncoding())"
    }
}

extension GetAssetInput {

    static func urlPathProvider(_ value: GetAssetInput) -> Swift.String? {
        guard let dataSetId = value.dataSetId else {
            return nil
        }
        guard let revisionId = value.revisionId else {
            return nil
        }
        guard let assetId = value.assetId else {
            return nil
        }
        return "/v1/data-sets/\(dataSetId.urlPercentEncoding())/revisions/\(revisionId.urlPercentEncoding())/assets/\(assetId.urlPercentEncoding())"
    }
}

extension GetDataGrantInput {

    static func urlPathProvider(_ value: GetDataGrantInput) -> Swift.String? {
        guard let dataGrantId = value.dataGrantId else {
            return nil
        }
        return "/v1/data-grants/\(dataGrantId.urlPercentEncoding())"
    }
}

extension GetDataSetInput {

    static func urlPathProvider(_ value: GetDataSetInput) -> Swift.String? {
        guard let dataSetId = value.dataSetId else {
            return nil
        }
        return "/v1/data-sets/\(dataSetId.urlPercentEncoding())"
    }
}

extension GetEventActionInput {

    static func urlPathProvider(_ value: GetEventActionInput) -> Swift.String? {
        guard let eventActionId = value.eventActionId else {
            return nil
        }
        return "/v1/event-actions/\(eventActionId.urlPercentEncoding())"
    }
}

extension GetJobInput {

    static func urlPathProvider(_ value: GetJobInput) -> Swift.String? {
        guard let jobId = value.jobId else {
            return nil
        }
        return "/v1/jobs/\(jobId.urlPercentEncoding())"
    }
}

extension GetReceivedDataGrantInput {

    static func urlPathProvider(_ value: GetReceivedDataGrantInput) -> Swift.String? {
        guard let dataGrantArn = value.dataGrantArn else {
            return nil
        }
        return "/v1/received-data-grants/\(dataGrantArn.urlPercentEncoding())"
    }
}

extension GetRevisionInput {

    static func urlPathProvider(_ value: GetRevisionInput) -> Swift.String? {
        guard let dataSetId = value.dataSetId else {
            return nil
        }
        guard let revisionId = value.revisionId else {
            return nil
        }
        return "/v1/data-sets/\(dataSetId.urlPercentEncoding())/revisions/\(revisionId.urlPercentEncoding())"
    }
}

extension ListDataGrantsInput {

    static func urlPathProvider(_ value: ListDataGrantsInput) -> Swift.String? {
        return "/v1/data-grants"
    }
}

extension ListDataGrantsInput {

    static func queryItemProvider(_ value: ListDataGrantsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListDataSetRevisionsInput {

    static func urlPathProvider(_ value: ListDataSetRevisionsInput) -> Swift.String? {
        guard let dataSetId = value.dataSetId else {
            return nil
        }
        return "/v1/data-sets/\(dataSetId.urlPercentEncoding())/revisions"
    }
}

extension ListDataSetRevisionsInput {

    static func queryItemProvider(_ value: ListDataSetRevisionsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListDataSetsInput {

    static func urlPathProvider(_ value: ListDataSetsInput) -> Swift.String? {
        return "/v1/data-sets"
    }
}

extension ListDataSetsInput {

    static func queryItemProvider(_ value: ListDataSetsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let origin = value.origin {
            let originQueryItem = Smithy.URIQueryItem(name: "origin".urlPercentEncoding(), value: Swift.String(origin).urlPercentEncoding())
            items.append(originQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListEventActionsInput {

    static func urlPathProvider(_ value: ListEventActionsInput) -> Swift.String? {
        return "/v1/event-actions"
    }
}

extension ListEventActionsInput {

    static func queryItemProvider(_ value: ListEventActionsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let eventSourceId = value.eventSourceId {
            let eventSourceIdQueryItem = Smithy.URIQueryItem(name: "eventSourceId".urlPercentEncoding(), value: Swift.String(eventSourceId).urlPercentEncoding())
            items.append(eventSourceIdQueryItem)
        }
        return items
    }
}

extension ListJobsInput {

    static func urlPathProvider(_ value: ListJobsInput) -> Swift.String? {
        return "/v1/jobs"
    }
}

extension ListJobsInput {

    static func queryItemProvider(_ value: ListJobsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let dataSetId = value.dataSetId {
            let dataSetIdQueryItem = Smithy.URIQueryItem(name: "dataSetId".urlPercentEncoding(), value: Swift.String(dataSetId).urlPercentEncoding())
            items.append(dataSetIdQueryItem)
        }
        if let revisionId = value.revisionId {
            let revisionIdQueryItem = Smithy.URIQueryItem(name: "revisionId".urlPercentEncoding(), value: Swift.String(revisionId).urlPercentEncoding())
            items.append(revisionIdQueryItem)
        }
        return items
    }
}

extension ListReceivedDataGrantsInput {

    static func urlPathProvider(_ value: ListReceivedDataGrantsInput) -> Swift.String? {
        return "/v1/received-data-grants"
    }
}

extension ListReceivedDataGrantsInput {

    static func queryItemProvider(_ value: ListReceivedDataGrantsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let acceptanceState = value.acceptanceState {
            acceptanceState.forEach { queryItemValue in
                let queryItem = Smithy.URIQueryItem(name: "acceptanceState".urlPercentEncoding(), value: Swift.String(queryItemValue.rawValue).urlPercentEncoding())
                items.append(queryItem)
            }
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListRevisionAssetsInput {

    static func urlPathProvider(_ value: ListRevisionAssetsInput) -> Swift.String? {
        guard let dataSetId = value.dataSetId else {
            return nil
        }
        guard let revisionId = value.revisionId else {
            return nil
        }
        return "/v1/data-sets/\(dataSetId.urlPercentEncoding())/revisions/\(revisionId.urlPercentEncoding())/assets"
    }
}

extension ListRevisionAssetsInput {

    static func queryItemProvider(_ value: ListRevisionAssetsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension RevokeRevisionInput {

    static func urlPathProvider(_ value: RevokeRevisionInput) -> Swift.String? {
        guard let dataSetId = value.dataSetId else {
            return nil
        }
        guard let revisionId = value.revisionId else {
            return nil
        }
        return "/v1/data-sets/\(dataSetId.urlPercentEncoding())/revisions/\(revisionId.urlPercentEncoding())/revoke"
    }
}

extension SendApiAssetInput {

    static func urlPathProvider(_ value: SendApiAssetInput) -> Swift.String? {
        return "/v1"
    }
}

extension SendApiAssetInput {

    static func headerProvider(_ value: SendApiAssetInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let assetId = value.assetId {
            items.add(SmithyHTTPAPI.Header(name: "x-amzn-dataexchange-asset-id", value: Swift.String(assetId)))
        }
        if let dataSetId = value.dataSetId {
            items.add(SmithyHTTPAPI.Header(name: "x-amzn-dataexchange-data-set-id", value: Swift.String(dataSetId)))
        }
        if let method = value.method {
            items.add(SmithyHTTPAPI.Header(name: "x-amzn-dataexchange-http-method", value: Swift.String(method)))
        }
        if let path = value.path {
            items.add(SmithyHTTPAPI.Header(name: "x-amzn-dataexchange-path", value: Swift.String(path)))
        }
        if let revisionId = value.revisionId {
            items.add(SmithyHTTPAPI.Header(name: "x-amzn-dataexchange-revision-id", value: Swift.String(revisionId)))
        }
        if let requestHeaders = value.requestHeaders {
            for (prefixHeaderMapKey, prefixHeaderMapValue) in requestHeaders {
                items.add(SmithyHTTPAPI.Header(name: "x-amzn-dataexchange-header-\(prefixHeaderMapKey)", value: Swift.String(prefixHeaderMapValue)))
            }
        }
        return items
    }
}

extension SendApiAssetInput {

    static func queryItemProvider(_ value: SendApiAssetInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let queryStringParameters = value.queryStringParameters {
            let currentQueryItemNames = items.map({$0.name})
            queryStringParameters.forEach { key0, value0 in
                if !currentQueryItemNames.contains(key0) {
                    let queryItem = Smithy.URIQueryItem(name: key0.urlPercentEncoding(), value: value0.urlPercentEncoding())
                    items.append(queryItem)
                }
            }
        }
        return items
    }
}

extension SendDataSetNotificationInput {

    static func urlPathProvider(_ value: SendDataSetNotificationInput) -> Swift.String? {
        guard let dataSetId = value.dataSetId else {
            return nil
        }
        return "/v1/data-sets/\(dataSetId.urlPercentEncoding())/notification"
    }
}

extension StartJobInput {

    static func urlPathProvider(_ value: StartJobInput) -> Swift.String? {
        guard let jobId = value.jobId else {
            return nil
        }
        return "/v1/jobs/\(jobId.urlPercentEncoding())"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension UpdateAssetInput {

    static func urlPathProvider(_ value: UpdateAssetInput) -> Swift.String? {
        guard let dataSetId = value.dataSetId else {
            return nil
        }
        guard let revisionId = value.revisionId else {
            return nil
        }
        guard let assetId = value.assetId else {
            return nil
        }
        return "/v1/data-sets/\(dataSetId.urlPercentEncoding())/revisions/\(revisionId.urlPercentEncoding())/assets/\(assetId.urlPercentEncoding())"
    }
}

extension UpdateDataSetInput {

    static func urlPathProvider(_ value: UpdateDataSetInput) -> Swift.String? {
        guard let dataSetId = value.dataSetId else {
            return nil
        }
        return "/v1/data-sets/\(dataSetId.urlPercentEncoding())"
    }
}

extension UpdateEventActionInput {

    static func urlPathProvider(_ value: UpdateEventActionInput) -> Swift.String? {
        guard let eventActionId = value.eventActionId else {
            return nil
        }
        return "/v1/event-actions/\(eventActionId.urlPercentEncoding())"
    }
}

extension UpdateRevisionInput {

    static func urlPathProvider(_ value: UpdateRevisionInput) -> Swift.String? {
        guard let dataSetId = value.dataSetId else {
            return nil
        }
        guard let revisionId = value.revisionId else {
            return nil
        }
        return "/v1/data-sets/\(dataSetId.urlPercentEncoding())/revisions/\(revisionId.urlPercentEncoding())"
    }
}

extension CreateDataGrantInput {

    static func write(value: CreateDataGrantInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Description"].write(value.description)
        try writer["EndsAt"].writeTimestamp(value.endsAt, format: SmithyTimestamps.TimestampFormat.dateTime)
        try writer["GrantDistributionScope"].write(value.grantDistributionScope)
        try writer["Name"].write(value.name)
        try writer["ReceiverPrincipal"].write(value.receiverPrincipal)
        try writer["SourceDataSetId"].write(value.sourceDataSetId)
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CreateDataSetInput {

    static func write(value: CreateDataSetInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AssetType"].write(value.assetType)
        try writer["Description"].write(value.description)
        try writer["Name"].write(value.name)
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CreateEventActionInput {

    static func write(value: CreateEventActionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Action"].write(value.action, with: DataExchangeClientTypes.Action.write(value:to:))
        try writer["Event"].write(value.event, with: DataExchangeClientTypes.Event.write(value:to:))
    }
}

extension CreateJobInput {

    static func write(value: CreateJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Details"].write(value.details, with: DataExchangeClientTypes.RequestDetails.write(value:to:))
        try writer["Type"].write(value.type)
    }
}

extension CreateRevisionInput {

    static func write(value: CreateRevisionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Comment"].write(value.comment)
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension RevokeRevisionInput {

    static func write(value: RevokeRevisionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["RevocationComment"].write(value.revocationComment)
    }
}

extension SendApiAssetInput {

    static func write(value: SendApiAssetInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Body"].write(value.body)
    }
}

extension SendDataSetNotificationInput {

    static func write(value: SendDataSetNotificationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["Comment"].write(value.comment)
        try writer["Details"].write(value.details, with: DataExchangeClientTypes.NotificationDetails.write(value:to:))
        try writer["Scope"].write(value.scope, with: DataExchangeClientTypes.ScopeDetails.write(value:to:))
        try writer["Type"].write(value.type)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension UpdateAssetInput {

    static func write(value: UpdateAssetInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
    }
}

extension UpdateDataSetInput {

    static func write(value: UpdateDataSetInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Description"].write(value.description)
        try writer["Name"].write(value.name)
    }
}

extension UpdateEventActionInput {

    static func write(value: UpdateEventActionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Action"].write(value.action, with: DataExchangeClientTypes.Action.write(value:to:))
    }
}

extension UpdateRevisionInput {

    static func write(value: UpdateRevisionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Comment"].write(value.comment)
        try writer["Finalized"].write(value.finalized)
    }
}

extension AcceptDataGrantOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AcceptDataGrantOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = AcceptDataGrantOutput()
        value.acceptanceState = try reader["AcceptanceState"].readIfPresent() ?? .sdkUnknown("")
        value.acceptedAt = try reader["AcceptedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.description = try reader["Description"].readIfPresent()
        value.endsAt = try reader["EndsAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.grantDistributionScope = try reader["GrantDistributionScope"].readIfPresent() ?? .sdkUnknown("")
        value.id = try reader["Id"].readIfPresent() ?? ""
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.receiverPrincipal = try reader["ReceiverPrincipal"].readIfPresent() ?? ""
        value.senderPrincipal = try reader["SenderPrincipal"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension CancelJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CancelJobOutput {
        return CancelJobOutput()
    }
}

extension CreateDataGrantOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateDataGrantOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateDataGrantOutput()
        value.acceptanceState = try reader["AcceptanceState"].readIfPresent() ?? .sdkUnknown("")
        value.acceptedAt = try reader["AcceptedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.description = try reader["Description"].readIfPresent()
        value.endsAt = try reader["EndsAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.grantDistributionScope = try reader["GrantDistributionScope"].readIfPresent() ?? .sdkUnknown("")
        value.id = try reader["Id"].readIfPresent() ?? ""
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.receiverPrincipal = try reader["ReceiverPrincipal"].readIfPresent() ?? ""
        value.senderPrincipal = try reader["SenderPrincipal"].readIfPresent() ?? ""
        value.sourceDataSetId = try reader["SourceDataSetId"].readIfPresent() ?? ""
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension CreateDataSetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateDataSetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateDataSetOutput()
        value.arn = try reader["Arn"].readIfPresent()
        value.assetType = try reader["AssetType"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.description = try reader["Description"].readIfPresent()
        value.id = try reader["Id"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.origin = try reader["Origin"].readIfPresent()
        value.originDetails = try reader["OriginDetails"].readIfPresent(with: DataExchangeClientTypes.OriginDetails.read(from:))
        value.sourceId = try reader["SourceId"].readIfPresent()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension CreateEventActionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateEventActionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateEventActionOutput()
        value.action = try reader["Action"].readIfPresent(with: DataExchangeClientTypes.Action.read(from:))
        value.arn = try reader["Arn"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.event = try reader["Event"].readIfPresent(with: DataExchangeClientTypes.Event.read(from:))
        value.id = try reader["Id"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension CreateJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateJobOutput()
        value.arn = try reader["Arn"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.details = try reader["Details"].readIfPresent(with: DataExchangeClientTypes.ResponseDetails.read(from:))
        value.errors = try reader["Errors"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.JobError.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.id = try reader["Id"].readIfPresent()
        value.state = try reader["State"].readIfPresent()
        value.type = try reader["Type"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension CreateRevisionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateRevisionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateRevisionOutput()
        value.arn = try reader["Arn"].readIfPresent()
        value.comment = try reader["Comment"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dataSetId = try reader["DataSetId"].readIfPresent()
        value.finalized = try reader["Finalized"].readIfPresent() ?? false
        value.id = try reader["Id"].readIfPresent()
        value.revocationComment = try reader["RevocationComment"].readIfPresent()
        value.revoked = try reader["Revoked"].readIfPresent() ?? false
        value.revokedAt = try reader["RevokedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.sourceId = try reader["SourceId"].readIfPresent()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension DeleteAssetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteAssetOutput {
        return DeleteAssetOutput()
    }
}

extension DeleteDataGrantOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteDataGrantOutput {
        return DeleteDataGrantOutput()
    }
}

extension DeleteDataSetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteDataSetOutput {
        return DeleteDataSetOutput()
    }
}

extension DeleteEventActionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteEventActionOutput {
        return DeleteEventActionOutput()
    }
}

extension DeleteRevisionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteRevisionOutput {
        return DeleteRevisionOutput()
    }
}

extension GetAssetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetAssetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetAssetOutput()
        value.arn = try reader["Arn"].readIfPresent()
        value.assetDetails = try reader["AssetDetails"].readIfPresent(with: DataExchangeClientTypes.AssetDetails.read(from:))
        value.assetType = try reader["AssetType"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dataSetId = try reader["DataSetId"].readIfPresent()
        value.id = try reader["Id"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.revisionId = try reader["RevisionId"].readIfPresent()
        value.sourceId = try reader["SourceId"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension GetDataGrantOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetDataGrantOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetDataGrantOutput()
        value.acceptanceState = try reader["AcceptanceState"].readIfPresent() ?? .sdkUnknown("")
        value.acceptedAt = try reader["AcceptedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.description = try reader["Description"].readIfPresent()
        value.endsAt = try reader["EndsAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.grantDistributionScope = try reader["GrantDistributionScope"].readIfPresent() ?? .sdkUnknown("")
        value.id = try reader["Id"].readIfPresent() ?? ""
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.receiverPrincipal = try reader["ReceiverPrincipal"].readIfPresent() ?? ""
        value.senderPrincipal = try reader["SenderPrincipal"].readIfPresent() ?? ""
        value.sourceDataSetId = try reader["SourceDataSetId"].readIfPresent() ?? ""
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension GetDataSetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetDataSetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetDataSetOutput()
        value.arn = try reader["Arn"].readIfPresent()
        value.assetType = try reader["AssetType"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.description = try reader["Description"].readIfPresent()
        value.id = try reader["Id"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.origin = try reader["Origin"].readIfPresent()
        value.originDetails = try reader["OriginDetails"].readIfPresent(with: DataExchangeClientTypes.OriginDetails.read(from:))
        value.sourceId = try reader["SourceId"].readIfPresent()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension GetEventActionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetEventActionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetEventActionOutput()
        value.action = try reader["Action"].readIfPresent(with: DataExchangeClientTypes.Action.read(from:))
        value.arn = try reader["Arn"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.event = try reader["Event"].readIfPresent(with: DataExchangeClientTypes.Event.read(from:))
        value.id = try reader["Id"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension GetJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetJobOutput()
        value.arn = try reader["Arn"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.details = try reader["Details"].readIfPresent(with: DataExchangeClientTypes.ResponseDetails.read(from:))
        value.errors = try reader["Errors"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.JobError.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.id = try reader["Id"].readIfPresent()
        value.state = try reader["State"].readIfPresent()
        value.type = try reader["Type"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension GetReceivedDataGrantOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetReceivedDataGrantOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetReceivedDataGrantOutput()
        value.acceptanceState = try reader["AcceptanceState"].readIfPresent() ?? .sdkUnknown("")
        value.acceptedAt = try reader["AcceptedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.description = try reader["Description"].readIfPresent()
        value.endsAt = try reader["EndsAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.grantDistributionScope = try reader["GrantDistributionScope"].readIfPresent() ?? .sdkUnknown("")
        value.id = try reader["Id"].readIfPresent() ?? ""
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.receiverPrincipal = try reader["ReceiverPrincipal"].readIfPresent() ?? ""
        value.senderPrincipal = try reader["SenderPrincipal"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension GetRevisionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetRevisionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetRevisionOutput()
        value.arn = try reader["Arn"].readIfPresent()
        value.comment = try reader["Comment"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dataSetId = try reader["DataSetId"].readIfPresent()
        value.finalized = try reader["Finalized"].readIfPresent() ?? false
        value.id = try reader["Id"].readIfPresent()
        value.revocationComment = try reader["RevocationComment"].readIfPresent()
        value.revoked = try reader["Revoked"].readIfPresent() ?? false
        value.revokedAt = try reader["RevokedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.sourceId = try reader["SourceId"].readIfPresent()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension ListDataGrantsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListDataGrantsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListDataGrantsOutput()
        value.dataGrantSummaries = try reader["DataGrantSummaries"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.DataGrantSummaryEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListDataSetRevisionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListDataSetRevisionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListDataSetRevisionsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.revisions = try reader["Revisions"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.RevisionEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListDataSetsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListDataSetsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListDataSetsOutput()
        value.dataSets = try reader["DataSets"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.DataSetEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListEventActionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListEventActionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListEventActionsOutput()
        value.eventActions = try reader["EventActions"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.EventActionEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListJobsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListJobsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListJobsOutput()
        value.jobs = try reader["Jobs"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.JobEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListReceivedDataGrantsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListReceivedDataGrantsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListReceivedDataGrantsOutput()
        value.dataGrantSummaries = try reader["DataGrantSummaries"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.ReceivedDataGrantSummariesEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListRevisionAssetsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRevisionAssetsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRevisionAssetsOutput()
        value.assets = try reader["Assets"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.AssetEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension RevokeRevisionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RevokeRevisionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = RevokeRevisionOutput()
        value.arn = try reader["Arn"].readIfPresent()
        value.comment = try reader["Comment"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dataSetId = try reader["DataSetId"].readIfPresent()
        value.finalized = try reader["Finalized"].readIfPresent() ?? false
        value.id = try reader["Id"].readIfPresent()
        value.revocationComment = try reader["RevocationComment"].readIfPresent()
        value.revoked = try reader["Revoked"].readIfPresent() ?? false
        value.revokedAt = try reader["RevokedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.sourceId = try reader["SourceId"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension SendApiAssetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SendApiAssetOutput {
        var value = SendApiAssetOutput()
        let keysForResponseHeaders = httpResponse.headers.dictionary.keys
        if (!keysForResponseHeaders.isEmpty) {
            var mapMember = [Swift.String: String]()
            for headerKey in keysForResponseHeaders {
                let mapMemberValue = httpResponse.headers.dictionary[headerKey]?[0]
                mapMember[headerKey] = mapMemberValue
            }
            value.responseHeaders = mapMember
        } else {
            value.responseHeaders = [:]
        }
        if let data = try await httpResponse.body.readData(), let output = Swift.String(data: data, encoding: .utf8) {
            value.body = output
        }
        return value
    }
}

extension SendDataSetNotificationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SendDataSetNotificationOutput {
        return SendDataSetNotificationOutput()
    }
}

extension StartJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartJobOutput {
        return StartJobOutput()
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateAssetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateAssetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateAssetOutput()
        value.arn = try reader["Arn"].readIfPresent()
        value.assetDetails = try reader["AssetDetails"].readIfPresent(with: DataExchangeClientTypes.AssetDetails.read(from:))
        value.assetType = try reader["AssetType"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dataSetId = try reader["DataSetId"].readIfPresent()
        value.id = try reader["Id"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.revisionId = try reader["RevisionId"].readIfPresent()
        value.sourceId = try reader["SourceId"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension UpdateDataSetOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateDataSetOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateDataSetOutput()
        value.arn = try reader["Arn"].readIfPresent()
        value.assetType = try reader["AssetType"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.description = try reader["Description"].readIfPresent()
        value.id = try reader["Id"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.origin = try reader["Origin"].readIfPresent()
        value.originDetails = try reader["OriginDetails"].readIfPresent(with: DataExchangeClientTypes.OriginDetails.read(from:))
        value.sourceId = try reader["SourceId"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension UpdateEventActionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateEventActionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateEventActionOutput()
        value.action = try reader["Action"].readIfPresent(with: DataExchangeClientTypes.Action.read(from:))
        value.arn = try reader["Arn"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.event = try reader["Event"].readIfPresent(with: DataExchangeClientTypes.Event.read(from:))
        value.id = try reader["Id"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension UpdateRevisionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateRevisionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateRevisionOutput()
        value.arn = try reader["Arn"].readIfPresent()
        value.comment = try reader["Comment"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dataSetId = try reader["DataSetId"].readIfPresent()
        value.finalized = try reader["Finalized"].readIfPresent() ?? false
        value.id = try reader["Id"].readIfPresent()
        value.revocationComment = try reader["RevocationComment"].readIfPresent()
        value.revoked = try reader["Revoked"].readIfPresent() ?? false
        value.revokedAt = try reader["RevokedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.sourceId = try reader["SourceId"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

enum AcceptDataGrantOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CancelJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateDataGrantOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceLimitExceededException": return try ServiceLimitExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateDataSetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ServiceLimitExceededException": return try ServiceLimitExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateEventActionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ServiceLimitExceededException": return try ServiceLimitExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateRevisionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteAssetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteDataGrantOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteDataSetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteEventActionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteRevisionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetAssetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetDataGrantOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetDataSetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetEventActionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetReceivedDataGrantOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetRevisionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListDataGrantsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListDataSetRevisionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListDataSetsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListEventActionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListJobsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListReceivedDataGrantsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRevisionAssetsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RevokeRevisionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SendApiAssetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SendDataSetNotificationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateAssetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateDataSetOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateEventActionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateRevisionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.properties.resourceId = try reader["ResourceId"].readIfPresent()
        value.properties.resourceType = try reader["ResourceType"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        var value = ThrottlingException()
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.exceptionCause = try reader["ExceptionCause"].readIfPresent()
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.properties.resourceId = try reader["ResourceId"].readIfPresent()
        value.properties.resourceType = try reader["ResourceType"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceLimitExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceLimitExceededException {
        let reader = baseError.errorBodyReader
        var value = ServiceLimitExceededException()
        value.properties.limitName = try reader["LimitName"].readIfPresent()
        value.properties.limitValue = try reader["LimitValue"].readIfPresent() ?? 0
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DataExchangeClientTypes.OriginDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.OriginDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.OriginDetails()
        value.productId = try reader["ProductId"].readIfPresent()
        value.dataGrantId = try reader["DataGrantId"].readIfPresent()
        return value
    }
}

extension DataExchangeClientTypes.Action {

    static func write(value: DataExchangeClientTypes.Action?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ExportRevisionToS3"].write(value.exportRevisionToS3, with: DataExchangeClientTypes.AutoExportRevisionToS3RequestDetails.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.Action {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.Action()
        value.exportRevisionToS3 = try reader["ExportRevisionToS3"].readIfPresent(with: DataExchangeClientTypes.AutoExportRevisionToS3RequestDetails.read(from:))
        return value
    }
}

extension DataExchangeClientTypes.AutoExportRevisionToS3RequestDetails {

    static func write(value: DataExchangeClientTypes.AutoExportRevisionToS3RequestDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Encryption"].write(value.encryption, with: DataExchangeClientTypes.ExportServerSideEncryption.write(value:to:))
        try writer["RevisionDestination"].write(value.revisionDestination, with: DataExchangeClientTypes.AutoExportRevisionDestinationEntry.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.AutoExportRevisionToS3RequestDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.AutoExportRevisionToS3RequestDetails()
        value.encryption = try reader["Encryption"].readIfPresent(with: DataExchangeClientTypes.ExportServerSideEncryption.read(from:))
        value.revisionDestination = try reader["RevisionDestination"].readIfPresent(with: DataExchangeClientTypes.AutoExportRevisionDestinationEntry.read(from:))
        return value
    }
}

extension DataExchangeClientTypes.AutoExportRevisionDestinationEntry {

    static func write(value: DataExchangeClientTypes.AutoExportRevisionDestinationEntry?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Bucket"].write(value.bucket)
        try writer["KeyPattern"].write(value.keyPattern)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.AutoExportRevisionDestinationEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.AutoExportRevisionDestinationEntry()
        value.bucket = try reader["Bucket"].readIfPresent() ?? ""
        value.keyPattern = try reader["KeyPattern"].readIfPresent()
        return value
    }
}

extension DataExchangeClientTypes.ExportServerSideEncryption {

    static func write(value: DataExchangeClientTypes.ExportServerSideEncryption?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["KmsKeyArn"].write(value.kmsKeyArn)
        try writer["Type"].write(value.type)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.ExportServerSideEncryption {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.ExportServerSideEncryption()
        value.kmsKeyArn = try reader["KmsKeyArn"].readIfPresent()
        value.type = try reader["Type"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension DataExchangeClientTypes.Event {

    static func write(value: DataExchangeClientTypes.Event?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["RevisionPublished"].write(value.revisionPublished, with: DataExchangeClientTypes.RevisionPublished.write(value:to:))
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.Event {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.Event()
        value.revisionPublished = try reader["RevisionPublished"].readIfPresent(with: DataExchangeClientTypes.RevisionPublished.read(from:))
        return value
    }
}

extension DataExchangeClientTypes.RevisionPublished {

    static func write(value: DataExchangeClientTypes.RevisionPublished?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DataSetId"].write(value.dataSetId)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.RevisionPublished {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.RevisionPublished()
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        return value
    }
}

extension DataExchangeClientTypes.ResponseDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.ResponseDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.ResponseDetails()
        value.exportAssetToSignedUrl = try reader["ExportAssetToSignedUrl"].readIfPresent(with: DataExchangeClientTypes.ExportAssetToSignedUrlResponseDetails.read(from:))
        value.exportAssetsToS3 = try reader["ExportAssetsToS3"].readIfPresent(with: DataExchangeClientTypes.ExportAssetsToS3ResponseDetails.read(from:))
        value.exportRevisionsToS3 = try reader["ExportRevisionsToS3"].readIfPresent(with: DataExchangeClientTypes.ExportRevisionsToS3ResponseDetails.read(from:))
        value.importAssetFromSignedUrl = try reader["ImportAssetFromSignedUrl"].readIfPresent(with: DataExchangeClientTypes.ImportAssetFromSignedUrlResponseDetails.read(from:))
        value.importAssetsFromS3 = try reader["ImportAssetsFromS3"].readIfPresent(with: DataExchangeClientTypes.ImportAssetsFromS3ResponseDetails.read(from:))
        value.importAssetsFromRedshiftDataShares = try reader["ImportAssetsFromRedshiftDataShares"].readIfPresent(with: DataExchangeClientTypes.ImportAssetsFromRedshiftDataSharesResponseDetails.read(from:))
        value.importAssetFromApiGatewayApi = try reader["ImportAssetFromApiGatewayApi"].readIfPresent(with: DataExchangeClientTypes.ImportAssetFromApiGatewayApiResponseDetails.read(from:))
        value.createS3DataAccessFromS3Bucket = try reader["CreateS3DataAccessFromS3Bucket"].readIfPresent(with: DataExchangeClientTypes.CreateS3DataAccessFromS3BucketResponseDetails.read(from:))
        value.importAssetsFromLakeFormationTagPolicy = try reader["ImportAssetsFromLakeFormationTagPolicy"].readIfPresent(with: DataExchangeClientTypes.ImportAssetsFromLakeFormationTagPolicyResponseDetails.read(from:))
        return value
    }
}

extension DataExchangeClientTypes.ImportAssetsFromLakeFormationTagPolicyResponseDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.ImportAssetsFromLakeFormationTagPolicyResponseDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.ImportAssetsFromLakeFormationTagPolicyResponseDetails()
        value.catalogId = try reader["CatalogId"].readIfPresent() ?? ""
        value.database = try reader["Database"].readIfPresent(with: DataExchangeClientTypes.DatabaseLFTagPolicyAndPermissions.read(from:))
        value.table = try reader["Table"].readIfPresent(with: DataExchangeClientTypes.TableLFTagPolicyAndPermissions.read(from:))
        value.roleArn = try reader["RoleArn"].readIfPresent() ?? ""
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.revisionId = try reader["RevisionId"].readIfPresent() ?? ""
        return value
    }
}

extension DataExchangeClientTypes.TableLFTagPolicyAndPermissions {

    static func write(value: DataExchangeClientTypes.TableLFTagPolicyAndPermissions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Expression"].writeList(value.expression, memberWritingClosure: DataExchangeClientTypes.LFTag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Permissions"].writeList(value.permissions, memberWritingClosure: SmithyReadWrite.WritingClosureBox<DataExchangeClientTypes.TableTagPolicyLFPermission>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.TableLFTagPolicyAndPermissions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.TableLFTagPolicyAndPermissions()
        value.expression = try reader["Expression"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.LFTag.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.permissions = try reader["Permissions"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<DataExchangeClientTypes.TableTagPolicyLFPermission>().read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DataExchangeClientTypes.LFTag {

    static func write(value: DataExchangeClientTypes.LFTag?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["TagKey"].write(value.tagKey)
        try writer["TagValues"].writeList(value.tagValues, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.LFTag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.LFTag()
        value.tagKey = try reader["TagKey"].readIfPresent() ?? ""
        value.tagValues = try reader["TagValues"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DataExchangeClientTypes.DatabaseLFTagPolicyAndPermissions {

    static func write(value: DataExchangeClientTypes.DatabaseLFTagPolicyAndPermissions?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Expression"].writeList(value.expression, memberWritingClosure: DataExchangeClientTypes.LFTag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Permissions"].writeList(value.permissions, memberWritingClosure: SmithyReadWrite.WritingClosureBox<DataExchangeClientTypes.DatabaseLFTagPolicyPermission>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.DatabaseLFTagPolicyAndPermissions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.DatabaseLFTagPolicyAndPermissions()
        value.expression = try reader["Expression"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.LFTag.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.permissions = try reader["Permissions"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<DataExchangeClientTypes.DatabaseLFTagPolicyPermission>().read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DataExchangeClientTypes.CreateS3DataAccessFromS3BucketResponseDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.CreateS3DataAccessFromS3BucketResponseDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.CreateS3DataAccessFromS3BucketResponseDetails()
        value.assetSource = try reader["AssetSource"].readIfPresent(with: DataExchangeClientTypes.S3DataAccessAssetSourceEntry.read(from:))
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.revisionId = try reader["RevisionId"].readIfPresent() ?? ""
        return value
    }
}

extension DataExchangeClientTypes.S3DataAccessAssetSourceEntry {

    static func write(value: DataExchangeClientTypes.S3DataAccessAssetSourceEntry?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Bucket"].write(value.bucket)
        try writer["KeyPrefixes"].writeList(value.keyPrefixes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Keys"].writeList(value.keys, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["KmsKeysToGrant"].writeList(value.kmsKeysToGrant, memberWritingClosure: DataExchangeClientTypes.KmsKeyToGrant.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.S3DataAccessAssetSourceEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.S3DataAccessAssetSourceEntry()
        value.bucket = try reader["Bucket"].readIfPresent() ?? ""
        value.keyPrefixes = try reader["KeyPrefixes"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.keys = try reader["Keys"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.kmsKeysToGrant = try reader["KmsKeysToGrant"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.KmsKeyToGrant.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DataExchangeClientTypes.KmsKeyToGrant {

    static func write(value: DataExchangeClientTypes.KmsKeyToGrant?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["KmsKeyArn"].write(value.kmsKeyArn)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.KmsKeyToGrant {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.KmsKeyToGrant()
        value.kmsKeyArn = try reader["KmsKeyArn"].readIfPresent() ?? ""
        return value
    }
}

extension DataExchangeClientTypes.ImportAssetFromApiGatewayApiResponseDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.ImportAssetFromApiGatewayApiResponseDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.ImportAssetFromApiGatewayApiResponseDetails()
        value.apiDescription = try reader["ApiDescription"].readIfPresent()
        value.apiId = try reader["ApiId"].readIfPresent() ?? ""
        value.apiKey = try reader["ApiKey"].readIfPresent()
        value.apiName = try reader["ApiName"].readIfPresent() ?? ""
        value.apiSpecificationMd5Hash = try reader["ApiSpecificationMd5Hash"].readIfPresent() ?? ""
        value.apiSpecificationUploadUrl = try reader["ApiSpecificationUploadUrl"].readIfPresent() ?? ""
        value.apiSpecificationUploadUrlExpiresAt = try reader["ApiSpecificationUploadUrlExpiresAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.protocolType = try reader["ProtocolType"].readIfPresent() ?? .sdkUnknown("")
        value.revisionId = try reader["RevisionId"].readIfPresent() ?? ""
        value.stage = try reader["Stage"].readIfPresent() ?? ""
        return value
    }
}

extension DataExchangeClientTypes.ImportAssetsFromRedshiftDataSharesResponseDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.ImportAssetsFromRedshiftDataSharesResponseDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.ImportAssetsFromRedshiftDataSharesResponseDetails()
        value.assetSources = try reader["AssetSources"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.RedshiftDataShareAssetSourceEntry.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.revisionId = try reader["RevisionId"].readIfPresent() ?? ""
        return value
    }
}

extension DataExchangeClientTypes.RedshiftDataShareAssetSourceEntry {

    static func write(value: DataExchangeClientTypes.RedshiftDataShareAssetSourceEntry?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DataShareArn"].write(value.dataShareArn)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.RedshiftDataShareAssetSourceEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.RedshiftDataShareAssetSourceEntry()
        value.dataShareArn = try reader["DataShareArn"].readIfPresent() ?? ""
        return value
    }
}

extension DataExchangeClientTypes.ImportAssetsFromS3ResponseDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.ImportAssetsFromS3ResponseDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.ImportAssetsFromS3ResponseDetails()
        value.assetSources = try reader["AssetSources"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.AssetSourceEntry.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.revisionId = try reader["RevisionId"].readIfPresent() ?? ""
        return value
    }
}

extension DataExchangeClientTypes.AssetSourceEntry {

    static func write(value: DataExchangeClientTypes.AssetSourceEntry?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Bucket"].write(value.bucket)
        try writer["Key"].write(value.key)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.AssetSourceEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.AssetSourceEntry()
        value.bucket = try reader["Bucket"].readIfPresent() ?? ""
        value.key = try reader["Key"].readIfPresent() ?? ""
        return value
    }
}

extension DataExchangeClientTypes.ImportAssetFromSignedUrlResponseDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.ImportAssetFromSignedUrlResponseDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.ImportAssetFromSignedUrlResponseDetails()
        value.assetName = try reader["AssetName"].readIfPresent() ?? ""
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.md5Hash = try reader["Md5Hash"].readIfPresent()
        value.revisionId = try reader["RevisionId"].readIfPresent() ?? ""
        value.signedUrl = try reader["SignedUrl"].readIfPresent()
        value.signedUrlExpiresAt = try reader["SignedUrlExpiresAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension DataExchangeClientTypes.ExportRevisionsToS3ResponseDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.ExportRevisionsToS3ResponseDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.ExportRevisionsToS3ResponseDetails()
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.encryption = try reader["Encryption"].readIfPresent(with: DataExchangeClientTypes.ExportServerSideEncryption.read(from:))
        value.revisionDestinations = try reader["RevisionDestinations"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.RevisionDestinationEntry.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.eventActionArn = try reader["EventActionArn"].readIfPresent()
        return value
    }
}

extension DataExchangeClientTypes.RevisionDestinationEntry {

    static func write(value: DataExchangeClientTypes.RevisionDestinationEntry?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Bucket"].write(value.bucket)
        try writer["KeyPattern"].write(value.keyPattern)
        try writer["RevisionId"].write(value.revisionId)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.RevisionDestinationEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.RevisionDestinationEntry()
        value.bucket = try reader["Bucket"].readIfPresent() ?? ""
        value.keyPattern = try reader["KeyPattern"].readIfPresent()
        value.revisionId = try reader["RevisionId"].readIfPresent() ?? ""
        return value
    }
}

extension DataExchangeClientTypes.ExportAssetsToS3ResponseDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.ExportAssetsToS3ResponseDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.ExportAssetsToS3ResponseDetails()
        value.assetDestinations = try reader["AssetDestinations"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.AssetDestinationEntry.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.encryption = try reader["Encryption"].readIfPresent(with: DataExchangeClientTypes.ExportServerSideEncryption.read(from:))
        value.revisionId = try reader["RevisionId"].readIfPresent() ?? ""
        return value
    }
}

extension DataExchangeClientTypes.AssetDestinationEntry {

    static func write(value: DataExchangeClientTypes.AssetDestinationEntry?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AssetId"].write(value.assetId)
        try writer["Bucket"].write(value.bucket)
        try writer["Key"].write(value.key)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.AssetDestinationEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.AssetDestinationEntry()
        value.assetId = try reader["AssetId"].readIfPresent() ?? ""
        value.bucket = try reader["Bucket"].readIfPresent() ?? ""
        value.key = try reader["Key"].readIfPresent()
        return value
    }
}

extension DataExchangeClientTypes.ExportAssetToSignedUrlResponseDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.ExportAssetToSignedUrlResponseDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.ExportAssetToSignedUrlResponseDetails()
        value.assetId = try reader["AssetId"].readIfPresent() ?? ""
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.revisionId = try reader["RevisionId"].readIfPresent() ?? ""
        value.signedUrl = try reader["SignedUrl"].readIfPresent()
        value.signedUrlExpiresAt = try reader["SignedUrlExpiresAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension DataExchangeClientTypes.JobError {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.JobError {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.JobError()
        value.code = try reader["Code"].readIfPresent() ?? .sdkUnknown("")
        value.details = try reader["Details"].readIfPresent(with: DataExchangeClientTypes.Details.read(from:))
        value.limitName = try reader["LimitName"].readIfPresent()
        value.limitValue = try reader["LimitValue"].readIfPresent() ?? 0
        value.message = try reader["Message"].readIfPresent() ?? ""
        value.resourceId = try reader["ResourceId"].readIfPresent()
        value.resourceType = try reader["ResourceType"].readIfPresent()
        return value
    }
}

extension DataExchangeClientTypes.Details {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.Details {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.Details()
        value.importAssetFromSignedUrlJobErrorDetails = try reader["ImportAssetFromSignedUrlJobErrorDetails"].readIfPresent(with: DataExchangeClientTypes.ImportAssetFromSignedUrlJobErrorDetails.read(from:))
        value.importAssetsFromS3JobErrorDetails = try reader["ImportAssetsFromS3JobErrorDetails"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.AssetSourceEntry.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DataExchangeClientTypes.ImportAssetFromSignedUrlJobErrorDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.ImportAssetFromSignedUrlJobErrorDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.ImportAssetFromSignedUrlJobErrorDetails()
        value.assetName = try reader["AssetName"].readIfPresent() ?? ""
        return value
    }
}

extension DataExchangeClientTypes.AssetDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.AssetDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.AssetDetails()
        value.s3SnapshotAsset = try reader["S3SnapshotAsset"].readIfPresent(with: DataExchangeClientTypes.S3SnapshotAsset.read(from:))
        value.redshiftDataShareAsset = try reader["RedshiftDataShareAsset"].readIfPresent(with: DataExchangeClientTypes.RedshiftDataShareAsset.read(from:))
        value.apiGatewayApiAsset = try reader["ApiGatewayApiAsset"].readIfPresent(with: DataExchangeClientTypes.ApiGatewayApiAsset.read(from:))
        value.s3DataAccessAsset = try reader["S3DataAccessAsset"].readIfPresent(with: DataExchangeClientTypes.S3DataAccessAsset.read(from:))
        value.lakeFormationDataPermissionAsset = try reader["LakeFormationDataPermissionAsset"].readIfPresent(with: DataExchangeClientTypes.LakeFormationDataPermissionAsset.read(from:))
        return value
    }
}

extension DataExchangeClientTypes.LakeFormationDataPermissionAsset {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.LakeFormationDataPermissionAsset {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.LakeFormationDataPermissionAsset()
        value.lakeFormationDataPermissionDetails = try reader["LakeFormationDataPermissionDetails"].readIfPresent(with: DataExchangeClientTypes.LakeFormationDataPermissionDetails.read(from:))
        value.lakeFormationDataPermissionType = try reader["LakeFormationDataPermissionType"].readIfPresent() ?? .sdkUnknown("")
        value.permissions = try reader["Permissions"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<DataExchangeClientTypes.LFPermission>().read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.roleArn = try reader["RoleArn"].readIfPresent()
        return value
    }
}

extension DataExchangeClientTypes.LakeFormationDataPermissionDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.LakeFormationDataPermissionDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.LakeFormationDataPermissionDetails()
        value.lfTagPolicy = try reader["LFTagPolicy"].readIfPresent(with: DataExchangeClientTypes.LFTagPolicyDetails.read(from:))
        return value
    }
}

extension DataExchangeClientTypes.LFTagPolicyDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.LFTagPolicyDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.LFTagPolicyDetails()
        value.catalogId = try reader["CatalogId"].readIfPresent() ?? ""
        value.resourceType = try reader["ResourceType"].readIfPresent() ?? .sdkUnknown("")
        value.resourceDetails = try reader["ResourceDetails"].readIfPresent(with: DataExchangeClientTypes.LFResourceDetails.read(from:))
        return value
    }
}

extension DataExchangeClientTypes.LFResourceDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.LFResourceDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.LFResourceDetails()
        value.database = try reader["Database"].readIfPresent(with: DataExchangeClientTypes.DatabaseLFTagPolicy.read(from:))
        value.table = try reader["Table"].readIfPresent(with: DataExchangeClientTypes.TableLFTagPolicy.read(from:))
        return value
    }
}

extension DataExchangeClientTypes.TableLFTagPolicy {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.TableLFTagPolicy {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.TableLFTagPolicy()
        value.expression = try reader["Expression"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.LFTag.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DataExchangeClientTypes.DatabaseLFTagPolicy {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.DatabaseLFTagPolicy {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.DatabaseLFTagPolicy()
        value.expression = try reader["Expression"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.LFTag.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DataExchangeClientTypes.S3DataAccessAsset {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.S3DataAccessAsset {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.S3DataAccessAsset()
        value.bucket = try reader["Bucket"].readIfPresent() ?? ""
        value.keyPrefixes = try reader["KeyPrefixes"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.keys = try reader["Keys"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.s3AccessPointAlias = try reader["S3AccessPointAlias"].readIfPresent()
        value.s3AccessPointArn = try reader["S3AccessPointArn"].readIfPresent()
        value.kmsKeysToGrant = try reader["KmsKeysToGrant"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.KmsKeyToGrant.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DataExchangeClientTypes.ApiGatewayApiAsset {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.ApiGatewayApiAsset {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.ApiGatewayApiAsset()
        value.apiDescription = try reader["ApiDescription"].readIfPresent()
        value.apiEndpoint = try reader["ApiEndpoint"].readIfPresent()
        value.apiId = try reader["ApiId"].readIfPresent()
        value.apiKey = try reader["ApiKey"].readIfPresent()
        value.apiName = try reader["ApiName"].readIfPresent()
        value.apiSpecificationDownloadUrl = try reader["ApiSpecificationDownloadUrl"].readIfPresent()
        value.apiSpecificationDownloadUrlExpiresAt = try reader["ApiSpecificationDownloadUrlExpiresAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.protocolType = try reader["ProtocolType"].readIfPresent()
        value.stage = try reader["Stage"].readIfPresent()
        return value
    }
}

extension DataExchangeClientTypes.RedshiftDataShareAsset {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.RedshiftDataShareAsset {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.RedshiftDataShareAsset()
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        return value
    }
}

extension DataExchangeClientTypes.S3SnapshotAsset {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.S3SnapshotAsset {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.S3SnapshotAsset()
        value.size = try reader["Size"].readIfPresent() ?? 0
        return value
    }
}

extension DataExchangeClientTypes.DataGrantSummaryEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.DataGrantSummaryEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.DataGrantSummaryEntry()
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.senderPrincipal = try reader["SenderPrincipal"].readIfPresent() ?? ""
        value.receiverPrincipal = try reader["ReceiverPrincipal"].readIfPresent() ?? ""
        value.acceptanceState = try reader["AcceptanceState"].readIfPresent() ?? .sdkUnknown("")
        value.acceptedAt = try reader["AcceptedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.endsAt = try reader["EndsAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.sourceDataSetId = try reader["SourceDataSetId"].readIfPresent() ?? ""
        value.id = try reader["Id"].readIfPresent() ?? ""
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension DataExchangeClientTypes.RevisionEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.RevisionEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.RevisionEntry()
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.comment = try reader["Comment"].readIfPresent()
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.finalized = try reader["Finalized"].readIfPresent() ?? false
        value.id = try reader["Id"].readIfPresent() ?? ""
        value.sourceId = try reader["SourceId"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.revocationComment = try reader["RevocationComment"].readIfPresent()
        value.revoked = try reader["Revoked"].readIfPresent() ?? false
        value.revokedAt = try reader["RevokedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension DataExchangeClientTypes.DataSetEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.DataSetEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.DataSetEntry()
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.assetType = try reader["AssetType"].readIfPresent() ?? .sdkUnknown("")
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.description = try reader["Description"].readIfPresent() ?? ""
        value.id = try reader["Id"].readIfPresent() ?? ""
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.origin = try reader["Origin"].readIfPresent() ?? .sdkUnknown("")
        value.originDetails = try reader["OriginDetails"].readIfPresent(with: DataExchangeClientTypes.OriginDetails.read(from:))
        value.sourceId = try reader["SourceId"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension DataExchangeClientTypes.EventActionEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.EventActionEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.EventActionEntry()
        value.action = try reader["Action"].readIfPresent(with: DataExchangeClientTypes.Action.read(from:))
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.event = try reader["Event"].readIfPresent(with: DataExchangeClientTypes.Event.read(from:))
        value.id = try reader["Id"].readIfPresent() ?? ""
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension DataExchangeClientTypes.JobEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.JobEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.JobEntry()
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.details = try reader["Details"].readIfPresent(with: DataExchangeClientTypes.ResponseDetails.read(from:))
        value.errors = try reader["Errors"].readListIfPresent(memberReadingClosure: DataExchangeClientTypes.JobError.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.id = try reader["Id"].readIfPresent() ?? ""
        value.state = try reader["State"].readIfPresent() ?? .sdkUnknown("")
        value.type = try reader["Type"].readIfPresent() ?? .sdkUnknown("")
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension DataExchangeClientTypes.ReceivedDataGrantSummariesEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.ReceivedDataGrantSummariesEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.ReceivedDataGrantSummariesEntry()
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.senderPrincipal = try reader["SenderPrincipal"].readIfPresent() ?? ""
        value.receiverPrincipal = try reader["ReceiverPrincipal"].readIfPresent() ?? ""
        value.acceptanceState = try reader["AcceptanceState"].readIfPresent() ?? .sdkUnknown("")
        value.acceptedAt = try reader["AcceptedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.endsAt = try reader["EndsAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.id = try reader["Id"].readIfPresent() ?? ""
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension DataExchangeClientTypes.AssetEntry {

    static func read(from reader: SmithyJSON.Reader) throws -> DataExchangeClientTypes.AssetEntry {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataExchangeClientTypes.AssetEntry()
        value.arn = try reader["Arn"].readIfPresent() ?? ""
        value.assetDetails = try reader["AssetDetails"].readIfPresent(with: DataExchangeClientTypes.AssetDetails.read(from:))
        value.assetType = try reader["AssetType"].readIfPresent() ?? .sdkUnknown("")
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.dataSetId = try reader["DataSetId"].readIfPresent() ?? ""
        value.id = try reader["Id"].readIfPresent() ?? ""
        value.name = try reader["Name"].readIfPresent() ?? ""
        value.revisionId = try reader["RevisionId"].readIfPresent() ?? ""
        value.sourceId = try reader["SourceId"].readIfPresent()
        value.updatedAt = try reader["UpdatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        return value
    }
}

extension DataExchangeClientTypes.RequestDetails {

    static func write(value: DataExchangeClientTypes.RequestDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CreateS3DataAccessFromS3Bucket"].write(value.createS3DataAccessFromS3Bucket, with: DataExchangeClientTypes.CreateS3DataAccessFromS3BucketRequestDetails.write(value:to:))
        try writer["ExportAssetToSignedUrl"].write(value.exportAssetToSignedUrl, with: DataExchangeClientTypes.ExportAssetToSignedUrlRequestDetails.write(value:to:))
        try writer["ExportAssetsToS3"].write(value.exportAssetsToS3, with: DataExchangeClientTypes.ExportAssetsToS3RequestDetails.write(value:to:))
        try writer["ExportRevisionsToS3"].write(value.exportRevisionsToS3, with: DataExchangeClientTypes.ExportRevisionsToS3RequestDetails.write(value:to:))
        try writer["ImportAssetFromApiGatewayApi"].write(value.importAssetFromApiGatewayApi, with: DataExchangeClientTypes.ImportAssetFromApiGatewayApiRequestDetails.write(value:to:))
        try writer["ImportAssetFromSignedUrl"].write(value.importAssetFromSignedUrl, with: DataExchangeClientTypes.ImportAssetFromSignedUrlRequestDetails.write(value:to:))
        try writer["ImportAssetsFromLakeFormationTagPolicy"].write(value.importAssetsFromLakeFormationTagPolicy, with: DataExchangeClientTypes.ImportAssetsFromLakeFormationTagPolicyRequestDetails.write(value:to:))
        try writer["ImportAssetsFromRedshiftDataShares"].write(value.importAssetsFromRedshiftDataShares, with: DataExchangeClientTypes.ImportAssetsFromRedshiftDataSharesRequestDetails.write(value:to:))
        try writer["ImportAssetsFromS3"].write(value.importAssetsFromS3, with: DataExchangeClientTypes.ImportAssetsFromS3RequestDetails.write(value:to:))
    }
}

extension DataExchangeClientTypes.ImportAssetsFromLakeFormationTagPolicyRequestDetails {

    static func write(value: DataExchangeClientTypes.ImportAssetsFromLakeFormationTagPolicyRequestDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CatalogId"].write(value.catalogId)
        try writer["DataSetId"].write(value.dataSetId)
        try writer["Database"].write(value.database, with: DataExchangeClientTypes.DatabaseLFTagPolicyAndPermissions.write(value:to:))
        try writer["RevisionId"].write(value.revisionId)
        try writer["RoleArn"].write(value.roleArn)
        try writer["Table"].write(value.table, with: DataExchangeClientTypes.TableLFTagPolicyAndPermissions.write(value:to:))
    }
}

extension DataExchangeClientTypes.CreateS3DataAccessFromS3BucketRequestDetails {

    static func write(value: DataExchangeClientTypes.CreateS3DataAccessFromS3BucketRequestDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AssetSource"].write(value.assetSource, with: DataExchangeClientTypes.S3DataAccessAssetSourceEntry.write(value:to:))
        try writer["DataSetId"].write(value.dataSetId)
        try writer["RevisionId"].write(value.revisionId)
    }
}

extension DataExchangeClientTypes.ImportAssetFromApiGatewayApiRequestDetails {

    static func write(value: DataExchangeClientTypes.ImportAssetFromApiGatewayApiRequestDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ApiDescription"].write(value.apiDescription)
        try writer["ApiId"].write(value.apiId)
        try writer["ApiKey"].write(value.apiKey)
        try writer["ApiName"].write(value.apiName)
        try writer["ApiSpecificationMd5Hash"].write(value.apiSpecificationMd5Hash)
        try writer["DataSetId"].write(value.dataSetId)
        try writer["ProtocolType"].write(value.protocolType)
        try writer["RevisionId"].write(value.revisionId)
        try writer["Stage"].write(value.stage)
    }
}

extension DataExchangeClientTypes.ImportAssetsFromRedshiftDataSharesRequestDetails {

    static func write(value: DataExchangeClientTypes.ImportAssetsFromRedshiftDataSharesRequestDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AssetSources"].writeList(value.assetSources, memberWritingClosure: DataExchangeClientTypes.RedshiftDataShareAssetSourceEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["DataSetId"].write(value.dataSetId)
        try writer["RevisionId"].write(value.revisionId)
    }
}

extension DataExchangeClientTypes.ImportAssetsFromS3RequestDetails {

    static func write(value: DataExchangeClientTypes.ImportAssetsFromS3RequestDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AssetSources"].writeList(value.assetSources, memberWritingClosure: DataExchangeClientTypes.AssetSourceEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["DataSetId"].write(value.dataSetId)
        try writer["RevisionId"].write(value.revisionId)
    }
}

extension DataExchangeClientTypes.ImportAssetFromSignedUrlRequestDetails {

    static func write(value: DataExchangeClientTypes.ImportAssetFromSignedUrlRequestDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AssetName"].write(value.assetName)
        try writer["DataSetId"].write(value.dataSetId)
        try writer["Md5Hash"].write(value.md5Hash)
        try writer["RevisionId"].write(value.revisionId)
    }
}

extension DataExchangeClientTypes.ExportRevisionsToS3RequestDetails {

    static func write(value: DataExchangeClientTypes.ExportRevisionsToS3RequestDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DataSetId"].write(value.dataSetId)
        try writer["Encryption"].write(value.encryption, with: DataExchangeClientTypes.ExportServerSideEncryption.write(value:to:))
        try writer["RevisionDestinations"].writeList(value.revisionDestinations, memberWritingClosure: DataExchangeClientTypes.RevisionDestinationEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DataExchangeClientTypes.ExportAssetsToS3RequestDetails {

    static func write(value: DataExchangeClientTypes.ExportAssetsToS3RequestDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AssetDestinations"].writeList(value.assetDestinations, memberWritingClosure: DataExchangeClientTypes.AssetDestinationEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["DataSetId"].write(value.dataSetId)
        try writer["Encryption"].write(value.encryption, with: DataExchangeClientTypes.ExportServerSideEncryption.write(value:to:))
        try writer["RevisionId"].write(value.revisionId)
    }
}

extension DataExchangeClientTypes.ExportAssetToSignedUrlRequestDetails {

    static func write(value: DataExchangeClientTypes.ExportAssetToSignedUrlRequestDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AssetId"].write(value.assetId)
        try writer["DataSetId"].write(value.dataSetId)
        try writer["RevisionId"].write(value.revisionId)
    }
}

extension DataExchangeClientTypes.ScopeDetails {

    static func write(value: DataExchangeClientTypes.ScopeDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["LakeFormationTagPolicies"].writeList(value.lakeFormationTagPolicies, memberWritingClosure: DataExchangeClientTypes.LakeFormationTagPolicyDetails.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["RedshiftDataShares"].writeList(value.redshiftDataShares, memberWritingClosure: DataExchangeClientTypes.RedshiftDataShareDetails.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["S3DataAccesses"].writeList(value.s3DataAccesses, memberWritingClosure: DataExchangeClientTypes.S3DataAccessDetails.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DataExchangeClientTypes.S3DataAccessDetails {

    static func write(value: DataExchangeClientTypes.S3DataAccessDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["KeyPrefixes"].writeList(value.keyPrefixes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Keys"].writeList(value.keys, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DataExchangeClientTypes.RedshiftDataShareDetails {

    static func write(value: DataExchangeClientTypes.RedshiftDataShareDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Arn"].write(value.arn)
        try writer["Database"].write(value.database)
        try writer["Function"].write(value.function)
        try writer["Schema"].write(value.schema)
        try writer["Table"].write(value.table)
        try writer["View"].write(value.view)
    }
}

extension DataExchangeClientTypes.LakeFormationTagPolicyDetails {

    static func write(value: DataExchangeClientTypes.LakeFormationTagPolicyDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Database"].write(value.database)
        try writer["Table"].write(value.table)
    }
}

extension DataExchangeClientTypes.NotificationDetails {

    static func write(value: DataExchangeClientTypes.NotificationDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DataUpdate"].write(value.dataUpdate, with: DataExchangeClientTypes.DataUpdateRequestDetails.write(value:to:))
        try writer["Deprecation"].write(value.deprecation, with: DataExchangeClientTypes.DeprecationRequestDetails.write(value:to:))
        try writer["SchemaChange"].write(value.schemaChange, with: DataExchangeClientTypes.SchemaChangeRequestDetails.write(value:to:))
    }
}

extension DataExchangeClientTypes.SchemaChangeRequestDetails {

    static func write(value: DataExchangeClientTypes.SchemaChangeRequestDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Changes"].writeList(value.changes, memberWritingClosure: DataExchangeClientTypes.SchemaChangeDetails.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["SchemaChangeAt"].writeTimestamp(value.schemaChangeAt, format: SmithyTimestamps.TimestampFormat.dateTime)
    }
}

extension DataExchangeClientTypes.SchemaChangeDetails {

    static func write(value: DataExchangeClientTypes.SchemaChangeDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Description"].write(value.description)
        try writer["Name"].write(value.name)
        try writer["Type"].write(value.type)
    }
}

extension DataExchangeClientTypes.DeprecationRequestDetails {

    static func write(value: DataExchangeClientTypes.DeprecationRequestDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DeprecationAt"].writeTimestamp(value.deprecationAt, format: SmithyTimestamps.TimestampFormat.dateTime)
    }
}

extension DataExchangeClientTypes.DataUpdateRequestDetails {

    static func write(value: DataExchangeClientTypes.DataUpdateRequestDetails?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DataUpdatedAt"].writeTimestamp(value.dataUpdatedAt, format: SmithyTimestamps.TimestampFormat.dateTime)
    }
}

public enum DataExchangeClientTypes {}
