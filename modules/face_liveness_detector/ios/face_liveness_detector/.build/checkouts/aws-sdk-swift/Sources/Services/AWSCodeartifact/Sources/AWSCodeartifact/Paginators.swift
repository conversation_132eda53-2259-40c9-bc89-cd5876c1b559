//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension CodeartifactClient {
    /// Paginate over `[ListAllowedRepositoriesForGroupOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListAllowedRepositoriesForGroupInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListAllowedRepositoriesForGroupOutput`
    public func listAllowedRepositoriesForGroupPaginated(input: ListAllowedRepositoriesForGroupInput) -> ClientRuntime.PaginatorSequence<ListAllowedRepositoriesForGroupInput, ListAllowedRepositoriesForGroupOutput> {
        return ClientRuntime.PaginatorSequence<ListAllowedRepositoriesForGroupInput, ListAllowedRepositoriesForGroupOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listAllowedRepositoriesForGroup(input:))
    }
}

extension ListAllowedRepositoriesForGroupInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListAllowedRepositoriesForGroupInput {
        return ListAllowedRepositoriesForGroupInput(
            domain: self.domain,
            domainOwner: self.domainOwner,
            maxResults: self.maxResults,
            nextToken: token,
            originRestrictionType: self.originRestrictionType,
            packageGroup: self.packageGroup
        )}
}

extension PaginatorSequence where OperationStackInput == ListAllowedRepositoriesForGroupInput, OperationStackOutput == ListAllowedRepositoriesForGroupOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listAllowedRepositoriesForGroupPaginated`
    /// to access the nested member `[Swift.String]`
    /// - Returns: `[Swift.String]`
    public func allowedRepositories() async throws -> [Swift.String] {
        return try await self.asyncCompactMap { item in item.allowedRepositories }
    }
}
extension CodeartifactClient {
    /// Paginate over `[ListAssociatedPackagesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListAssociatedPackagesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListAssociatedPackagesOutput`
    public func listAssociatedPackagesPaginated(input: ListAssociatedPackagesInput) -> ClientRuntime.PaginatorSequence<ListAssociatedPackagesInput, ListAssociatedPackagesOutput> {
        return ClientRuntime.PaginatorSequence<ListAssociatedPackagesInput, ListAssociatedPackagesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listAssociatedPackages(input:))
    }
}

extension ListAssociatedPackagesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListAssociatedPackagesInput {
        return ListAssociatedPackagesInput(
            domain: self.domain,
            domainOwner: self.domainOwner,
            maxResults: self.maxResults,
            nextToken: token,
            packageGroup: self.packageGroup,
            preview: self.preview
        )}
}

extension PaginatorSequence where OperationStackInput == ListAssociatedPackagesInput, OperationStackOutput == ListAssociatedPackagesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listAssociatedPackagesPaginated`
    /// to access the nested member `[CodeartifactClientTypes.AssociatedPackage]`
    /// - Returns: `[CodeartifactClientTypes.AssociatedPackage]`
    public func packages() async throws -> [CodeartifactClientTypes.AssociatedPackage] {
        return try await self.asyncCompactMap { item in item.packages }
    }
}
extension CodeartifactClient {
    /// Paginate over `[ListDomainsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListDomainsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListDomainsOutput`
    public func listDomainsPaginated(input: ListDomainsInput) -> ClientRuntime.PaginatorSequence<ListDomainsInput, ListDomainsOutput> {
        return ClientRuntime.PaginatorSequence<ListDomainsInput, ListDomainsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listDomains(input:))
    }
}

extension ListDomainsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListDomainsInput {
        return ListDomainsInput(
            maxResults: self.maxResults,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListDomainsInput, OperationStackOutput == ListDomainsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listDomainsPaginated`
    /// to access the nested member `[CodeartifactClientTypes.DomainSummary]`
    /// - Returns: `[CodeartifactClientTypes.DomainSummary]`
    public func domains() async throws -> [CodeartifactClientTypes.DomainSummary] {
        return try await self.asyncCompactMap { item in item.domains }
    }
}
extension CodeartifactClient {
    /// Paginate over `[ListPackageGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListPackageGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListPackageGroupsOutput`
    public func listPackageGroupsPaginated(input: ListPackageGroupsInput) -> ClientRuntime.PaginatorSequence<ListPackageGroupsInput, ListPackageGroupsOutput> {
        return ClientRuntime.PaginatorSequence<ListPackageGroupsInput, ListPackageGroupsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listPackageGroups(input:))
    }
}

extension ListPackageGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListPackageGroupsInput {
        return ListPackageGroupsInput(
            domain: self.domain,
            domainOwner: self.domainOwner,
            maxResults: self.maxResults,
            nextToken: token,
            prefix: self.prefix
        )}
}

extension PaginatorSequence where OperationStackInput == ListPackageGroupsInput, OperationStackOutput == ListPackageGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listPackageGroupsPaginated`
    /// to access the nested member `[CodeartifactClientTypes.PackageGroupSummary]`
    /// - Returns: `[CodeartifactClientTypes.PackageGroupSummary]`
    public func packageGroups() async throws -> [CodeartifactClientTypes.PackageGroupSummary] {
        return try await self.asyncCompactMap { item in item.packageGroups }
    }
}
extension CodeartifactClient {
    /// Paginate over `[ListPackagesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListPackagesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListPackagesOutput`
    public func listPackagesPaginated(input: ListPackagesInput) -> ClientRuntime.PaginatorSequence<ListPackagesInput, ListPackagesOutput> {
        return ClientRuntime.PaginatorSequence<ListPackagesInput, ListPackagesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listPackages(input:))
    }
}

extension ListPackagesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListPackagesInput {
        return ListPackagesInput(
            domain: self.domain,
            domainOwner: self.domainOwner,
            format: self.format,
            maxResults: self.maxResults,
            namespace: self.namespace,
            nextToken: token,
            packagePrefix: self.packagePrefix,
            publish: self.publish,
            repository: self.repository,
            upstream: self.upstream
        )}
}

extension PaginatorSequence where OperationStackInput == ListPackagesInput, OperationStackOutput == ListPackagesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listPackagesPaginated`
    /// to access the nested member `[CodeartifactClientTypes.PackageSummary]`
    /// - Returns: `[CodeartifactClientTypes.PackageSummary]`
    public func packages() async throws -> [CodeartifactClientTypes.PackageSummary] {
        return try await self.asyncCompactMap { item in item.packages }
    }
}
extension CodeartifactClient {
    /// Paginate over `[ListPackageVersionAssetsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListPackageVersionAssetsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListPackageVersionAssetsOutput`
    public func listPackageVersionAssetsPaginated(input: ListPackageVersionAssetsInput) -> ClientRuntime.PaginatorSequence<ListPackageVersionAssetsInput, ListPackageVersionAssetsOutput> {
        return ClientRuntime.PaginatorSequence<ListPackageVersionAssetsInput, ListPackageVersionAssetsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listPackageVersionAssets(input:))
    }
}

extension ListPackageVersionAssetsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListPackageVersionAssetsInput {
        return ListPackageVersionAssetsInput(
            domain: self.domain,
            domainOwner: self.domainOwner,
            format: self.format,
            maxResults: self.maxResults,
            namespace: self.namespace,
            nextToken: token,
            package: self.package,
            packageVersion: self.packageVersion,
            repository: self.repository
        )}
}

extension PaginatorSequence where OperationStackInput == ListPackageVersionAssetsInput, OperationStackOutput == ListPackageVersionAssetsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listPackageVersionAssetsPaginated`
    /// to access the nested member `[CodeartifactClientTypes.AssetSummary]`
    /// - Returns: `[CodeartifactClientTypes.AssetSummary]`
    public func assets() async throws -> [CodeartifactClientTypes.AssetSummary] {
        return try await self.asyncCompactMap { item in item.assets }
    }
}
extension CodeartifactClient {
    /// Paginate over `[ListPackageVersionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListPackageVersionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListPackageVersionsOutput`
    public func listPackageVersionsPaginated(input: ListPackageVersionsInput) -> ClientRuntime.PaginatorSequence<ListPackageVersionsInput, ListPackageVersionsOutput> {
        return ClientRuntime.PaginatorSequence<ListPackageVersionsInput, ListPackageVersionsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listPackageVersions(input:))
    }
}

extension ListPackageVersionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListPackageVersionsInput {
        return ListPackageVersionsInput(
            domain: self.domain,
            domainOwner: self.domainOwner,
            format: self.format,
            maxResults: self.maxResults,
            namespace: self.namespace,
            nextToken: token,
            originType: self.originType,
            package: self.package,
            repository: self.repository,
            sortBy: self.sortBy,
            status: self.status
        )}
}

extension PaginatorSequence where OperationStackInput == ListPackageVersionsInput, OperationStackOutput == ListPackageVersionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listPackageVersionsPaginated`
    /// to access the nested member `[CodeartifactClientTypes.PackageVersionSummary]`
    /// - Returns: `[CodeartifactClientTypes.PackageVersionSummary]`
    public func versions() async throws -> [CodeartifactClientTypes.PackageVersionSummary] {
        return try await self.asyncCompactMap { item in item.versions }
    }
}
extension CodeartifactClient {
    /// Paginate over `[ListRepositoriesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListRepositoriesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListRepositoriesOutput`
    public func listRepositoriesPaginated(input: ListRepositoriesInput) -> ClientRuntime.PaginatorSequence<ListRepositoriesInput, ListRepositoriesOutput> {
        return ClientRuntime.PaginatorSequence<ListRepositoriesInput, ListRepositoriesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listRepositories(input:))
    }
}

extension ListRepositoriesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListRepositoriesInput {
        return ListRepositoriesInput(
            maxResults: self.maxResults,
            nextToken: token,
            repositoryPrefix: self.repositoryPrefix
        )}
}

extension PaginatorSequence where OperationStackInput == ListRepositoriesInput, OperationStackOutput == ListRepositoriesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listRepositoriesPaginated`
    /// to access the nested member `[CodeartifactClientTypes.RepositorySummary]`
    /// - Returns: `[CodeartifactClientTypes.RepositorySummary]`
    public func repositories() async throws -> [CodeartifactClientTypes.RepositorySummary] {
        return try await self.asyncCompactMap { item in item.repositories }
    }
}
extension CodeartifactClient {
    /// Paginate over `[ListRepositoriesInDomainOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListRepositoriesInDomainInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListRepositoriesInDomainOutput`
    public func listRepositoriesInDomainPaginated(input: ListRepositoriesInDomainInput) -> ClientRuntime.PaginatorSequence<ListRepositoriesInDomainInput, ListRepositoriesInDomainOutput> {
        return ClientRuntime.PaginatorSequence<ListRepositoriesInDomainInput, ListRepositoriesInDomainOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listRepositoriesInDomain(input:))
    }
}

extension ListRepositoriesInDomainInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListRepositoriesInDomainInput {
        return ListRepositoriesInDomainInput(
            administratorAccount: self.administratorAccount,
            domain: self.domain,
            domainOwner: self.domainOwner,
            maxResults: self.maxResults,
            nextToken: token,
            repositoryPrefix: self.repositoryPrefix
        )}
}

extension PaginatorSequence where OperationStackInput == ListRepositoriesInDomainInput, OperationStackOutput == ListRepositoriesInDomainOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listRepositoriesInDomainPaginated`
    /// to access the nested member `[CodeartifactClientTypes.RepositorySummary]`
    /// - Returns: `[CodeartifactClientTypes.RepositorySummary]`
    public func repositories() async throws -> [CodeartifactClientTypes.RepositorySummary] {
        return try await self.asyncCompactMap { item in item.repositories }
    }
}
extension CodeartifactClient {
    /// Paginate over `[ListSubPackageGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListSubPackageGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListSubPackageGroupsOutput`
    public func listSubPackageGroupsPaginated(input: ListSubPackageGroupsInput) -> ClientRuntime.PaginatorSequence<ListSubPackageGroupsInput, ListSubPackageGroupsOutput> {
        return ClientRuntime.PaginatorSequence<ListSubPackageGroupsInput, ListSubPackageGroupsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listSubPackageGroups(input:))
    }
}

extension ListSubPackageGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListSubPackageGroupsInput {
        return ListSubPackageGroupsInput(
            domain: self.domain,
            domainOwner: self.domainOwner,
            maxResults: self.maxResults,
            nextToken: token,
            packageGroup: self.packageGroup
        )}
}

extension PaginatorSequence where OperationStackInput == ListSubPackageGroupsInput, OperationStackOutput == ListSubPackageGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listSubPackageGroupsPaginated`
    /// to access the nested member `[CodeartifactClientTypes.PackageGroupSummary]`
    /// - Returns: `[CodeartifactClientTypes.PackageGroupSummary]`
    public func packageGroups() async throws -> [CodeartifactClientTypes.PackageGroupSummary] {
        return try await self.asyncCompactMap { item in item.packageGroups }
    }
}
