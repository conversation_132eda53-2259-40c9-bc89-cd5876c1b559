//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
@_spi(SmithyReadWrite) import class SmithyFormURL.Writer
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyXML.Reader
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.AWSQueryError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError


public struct AddTagsToResourceOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteDBClusterParameterGroupOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteDBSubnetGroupOutput: Swift.Sendable {

    public init() { }
}

public struct RemoveTagsFromResourceOutput: Swift.Sendable {

    public init() { }
}

/// The requested source could not be found.
public struct SourceNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SourceNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The subscription name does not exist.
public struct SubscriptionNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SubscriptionNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [AddSourceIdentifierToSubscription].
public struct AddSourceIdentifierToSubscriptionInput: Swift.Sendable {
    /// The identifier of the event source to be added:
    ///
    /// * If the source type is an instance, a DBInstanceIdentifier must be provided.
    ///
    /// * If the source type is a security group, a DBSecurityGroupName must be provided.
    ///
    /// * If the source type is a parameter group, a DBParameterGroupName must be provided.
    ///
    /// * If the source type is a snapshot, a DBSnapshotIdentifier must be provided.
    /// This member is required.
    public var sourceIdentifier: Swift.String?
    /// The name of the Amazon DocumentDB event notification subscription that you want to add a source identifier to.
    /// This member is required.
    public var subscriptionName: Swift.String?

    public init(
        sourceIdentifier: Swift.String? = nil,
        subscriptionName: Swift.String? = nil
    )
    {
        self.sourceIdentifier = sourceIdentifier
        self.subscriptionName = subscriptionName
    }
}

extension DocDBClientTypes {

    /// Detailed information about an event to which you have subscribed.
    public struct EventSubscription: Swift.Sendable {
        /// The Amazon DocumentDB event notification subscription ID.
        public var custSubscriptionId: Swift.String?
        /// The Amazon Web Services customer account that is associated with the Amazon DocumentDB event notification subscription.
        public var customerAwsId: Swift.String?
        /// A Boolean value indicating whether the subscription is enabled. A value of true indicates that the subscription is enabled.
        public var enabled: Swift.Bool?
        /// A list of event categories for the Amazon DocumentDB event notification subscription.
        public var eventCategoriesList: [Swift.String]?
        /// The Amazon Resource Name (ARN) for the event subscription.
        public var eventSubscriptionArn: Swift.String?
        /// The topic ARN of the Amazon DocumentDB event notification subscription.
        public var snsTopicArn: Swift.String?
        /// A list of source IDs for the Amazon DocumentDB event notification subscription.
        public var sourceIdsList: [Swift.String]?
        /// The source type for the Amazon DocumentDB event notification subscription.
        public var sourceType: Swift.String?
        /// The status of the Amazon DocumentDB event notification subscription. Constraints: Can be one of the following: creating, modifying, deleting, active, no-permission, topic-not-exist The no-permission status indicates that Amazon DocumentDB no longer has permission to post to the SNS topic. The topic-not-exist status indicates that the topic was deleted after the subscription was created.
        public var status: Swift.String?
        /// The time at which the Amazon DocumentDB event notification subscription was created.
        public var subscriptionCreationTime: Swift.String?

        public init(
            custSubscriptionId: Swift.String? = nil,
            customerAwsId: Swift.String? = nil,
            enabled: Swift.Bool? = nil,
            eventCategoriesList: [Swift.String]? = nil,
            eventSubscriptionArn: Swift.String? = nil,
            snsTopicArn: Swift.String? = nil,
            sourceIdsList: [Swift.String]? = nil,
            sourceType: Swift.String? = nil,
            status: Swift.String? = nil,
            subscriptionCreationTime: Swift.String? = nil
        )
        {
            self.custSubscriptionId = custSubscriptionId
            self.customerAwsId = customerAwsId
            self.enabled = enabled
            self.eventCategoriesList = eventCategoriesList
            self.eventSubscriptionArn = eventSubscriptionArn
            self.snsTopicArn = snsTopicArn
            self.sourceIdsList = sourceIdsList
            self.sourceType = sourceType
            self.status = status
            self.subscriptionCreationTime = subscriptionCreationTime
        }
    }
}

public struct AddSourceIdentifierToSubscriptionOutput: Swift.Sendable {
    /// Detailed information about an event to which you have subscribed.
    public var eventSubscription: DocDBClientTypes.EventSubscription?

    public init(
        eventSubscription: DocDBClientTypes.EventSubscription? = nil
    )
    {
        self.eventSubscription = eventSubscription
    }
}

/// DBClusterIdentifier doesn't refer to an existing cluster.
public struct DBClusterNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBClusterNotFoundFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// DBInstanceIdentifier doesn't refer to an existing instance.
public struct DBInstanceNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBInstanceNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// DBSnapshotIdentifier doesn't refer to an existing snapshot.
public struct DBSnapshotNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBSnapshotNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension DocDBClientTypes {

    /// Metadata assigned to an Amazon DocumentDB resource consisting of a key-value pair.
    public struct Tag: Swift.Sendable {
        /// The required name of the tag. The string value can be from 1 to 128 Unicode characters in length and can't be prefixed with "aws:" or "rds:". The string can contain only the set of Unicode letters, digits, white space, '_', '.', '/', '=', '+', '-' (Java regex: "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-]*)$").
        public var key: Swift.String?
        /// The optional value of the tag. The string value can be from 1 to 256 Unicode characters in length and can't be prefixed with "aws:" or "rds:". The string can contain only the set of Unicode letters, digits, white space, '_', '.', '/', '=', '+', '-' (Java regex: "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-]*)$").
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

/// Represents the input to [AddTagsToResource].
public struct AddTagsToResourceInput: Swift.Sendable {
    /// The Amazon DocumentDB resource that the tags are added to. This value is an Amazon Resource Name .
    /// This member is required.
    public var resourceName: Swift.String?
    /// The tags to be assigned to the Amazon DocumentDB resource.
    /// This member is required.
    public var tags: [DocDBClientTypes.Tag]?

    public init(
        resourceName: Swift.String? = nil,
        tags: [DocDBClientTypes.Tag]? = nil
    )
    {
        self.resourceName = resourceName
        self.tags = tags
    }
}

/// The cluster isn't in a valid state.
public struct InvalidDBClusterStateFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidDBClusterStateFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified instance isn't in the available state.
public struct InvalidDBInstanceStateFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidDBInstanceState" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified resource ID was not found.
public struct ResourceNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [ApplyPendingMaintenanceAction].
public struct ApplyPendingMaintenanceActionInput: Swift.Sendable {
    /// The pending maintenance action to apply to this resource. Valid values: system-update, db-upgrade
    /// This member is required.
    public var applyAction: Swift.String?
    /// A value that specifies the type of opt-in request or undoes an opt-in request. An opt-in request of type immediate can't be undone. Valid values:
    ///
    /// * immediate - Apply the maintenance action immediately.
    ///
    /// * next-maintenance - Apply the maintenance action during the next maintenance window for the resource.
    ///
    /// * undo-opt-in - Cancel any existing next-maintenance opt-in requests.
    /// This member is required.
    public var optInType: Swift.String?
    /// The Amazon Resource Name (ARN) of the resource that the pending maintenance action applies to.
    /// This member is required.
    public var resourceIdentifier: Swift.String?

    public init(
        applyAction: Swift.String? = nil,
        optInType: Swift.String? = nil,
        resourceIdentifier: Swift.String? = nil
    )
    {
        self.applyAction = applyAction
        self.optInType = optInType
        self.resourceIdentifier = resourceIdentifier
    }
}

extension DocDBClientTypes {

    /// Provides information about a pending maintenance action for a resource.
    public struct PendingMaintenanceAction: Swift.Sendable {
        /// The type of pending maintenance action that is available for the resource.
        public var action: Swift.String?
        /// The date of the maintenance window when the action is applied. The maintenance action is applied to the resource during its first maintenance window after this date. If this date is specified, any next-maintenance opt-in requests are ignored.
        public var autoAppliedAfterDate: Foundation.Date?
        /// The effective date when the pending maintenance action is applied to the resource.
        public var currentApplyDate: Foundation.Date?
        /// A description providing more detail about the maintenance action.
        public var description: Swift.String?
        /// The date when the maintenance action is automatically applied. The maintenance action is applied to the resource on this date regardless of the maintenance window for the resource. If this date is specified, any immediate opt-in requests are ignored.
        public var forcedApplyDate: Foundation.Date?
        /// Indicates the type of opt-in request that has been received for the resource.
        public var optInStatus: Swift.String?

        public init(
            action: Swift.String? = nil,
            autoAppliedAfterDate: Foundation.Date? = nil,
            currentApplyDate: Foundation.Date? = nil,
            description: Swift.String? = nil,
            forcedApplyDate: Foundation.Date? = nil,
            optInStatus: Swift.String? = nil
        )
        {
            self.action = action
            self.autoAppliedAfterDate = autoAppliedAfterDate
            self.currentApplyDate = currentApplyDate
            self.description = description
            self.forcedApplyDate = forcedApplyDate
            self.optInStatus = optInStatus
        }
    }
}

extension DocDBClientTypes {

    /// Represents the output of [ApplyPendingMaintenanceAction].
    public struct ResourcePendingMaintenanceActions: Swift.Sendable {
        /// A list that provides details about the pending maintenance actions for the resource.
        public var pendingMaintenanceActionDetails: [DocDBClientTypes.PendingMaintenanceAction]?
        /// The Amazon Resource Name (ARN) of the resource that has pending maintenance actions.
        public var resourceIdentifier: Swift.String?

        public init(
            pendingMaintenanceActionDetails: [DocDBClientTypes.PendingMaintenanceAction]? = nil,
            resourceIdentifier: Swift.String? = nil
        )
        {
            self.pendingMaintenanceActionDetails = pendingMaintenanceActionDetails
            self.resourceIdentifier = resourceIdentifier
        }
    }
}

public struct ApplyPendingMaintenanceActionOutput: Swift.Sendable {
    /// Represents the output of [ApplyPendingMaintenanceAction].
    public var resourcePendingMaintenanceActions: DocDBClientTypes.ResourcePendingMaintenanceActions?

    public init(
        resourcePendingMaintenanceActions: DocDBClientTypes.ResourcePendingMaintenanceActions? = nil
    )
    {
        self.resourcePendingMaintenanceActions = resourcePendingMaintenanceActions
    }
}

/// A parameter group with the same name already exists.
public struct DBParameterGroupAlreadyExistsFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBParameterGroupAlreadyExists" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// DBParameterGroupName doesn't refer to an existing parameter group.
public struct DBParameterGroupNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBParameterGroupNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// This request would cause you to exceed the allowed number of parameter groups.
public struct DBParameterGroupQuotaExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBParameterGroupQuotaExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [CopyDBClusterParameterGroup].
public struct CopyDBClusterParameterGroupInput: Swift.Sendable {
    /// The identifier or Amazon Resource Name (ARN) for the source cluster parameter group. Constraints:
    ///
    /// * Must specify a valid cluster parameter group.
    ///
    /// * If the source cluster parameter group is in the same Amazon Web Services Region as the copy, specify a valid parameter group identifier; for example, my-db-cluster-param-group, or a valid ARN.
    ///
    /// * If the source parameter group is in a different Amazon Web Services Region than the copy, specify a valid cluster parameter group ARN; for example, arn:aws:rds:us-east-1:123456789012:sample-cluster:sample-parameter-group.
    /// This member is required.
    public var sourceDBClusterParameterGroupIdentifier: Swift.String?
    /// The tags that are to be assigned to the parameter group.
    public var tags: [DocDBClientTypes.Tag]?
    /// A description for the copied cluster parameter group.
    /// This member is required.
    public var targetDBClusterParameterGroupDescription: Swift.String?
    /// The identifier for the copied cluster parameter group. Constraints:
    ///
    /// * Cannot be null, empty, or blank.
    ///
    /// * Must contain from 1 to 255 letters, numbers, or hyphens.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    ///
    ///
    /// Example: my-cluster-param-group1
    /// This member is required.
    public var targetDBClusterParameterGroupIdentifier: Swift.String?

    public init(
        sourceDBClusterParameterGroupIdentifier: Swift.String? = nil,
        tags: [DocDBClientTypes.Tag]? = nil,
        targetDBClusterParameterGroupDescription: Swift.String? = nil,
        targetDBClusterParameterGroupIdentifier: Swift.String? = nil
    )
    {
        self.sourceDBClusterParameterGroupIdentifier = sourceDBClusterParameterGroupIdentifier
        self.tags = tags
        self.targetDBClusterParameterGroupDescription = targetDBClusterParameterGroupDescription
        self.targetDBClusterParameterGroupIdentifier = targetDBClusterParameterGroupIdentifier
    }
}

extension DocDBClientTypes {

    /// Detailed information about a cluster parameter group.
    public struct DBClusterParameterGroup: Swift.Sendable {
        /// The Amazon Resource Name (ARN) for the cluster parameter group.
        public var dbClusterParameterGroupArn: Swift.String?
        /// Provides the name of the cluster parameter group.
        public var dbClusterParameterGroupName: Swift.String?
        /// Provides the name of the parameter group family that this cluster parameter group is compatible with.
        public var dbParameterGroupFamily: Swift.String?
        /// Provides the customer-specified description for this cluster parameter group.
        public var description: Swift.String?

        public init(
            dbClusterParameterGroupArn: Swift.String? = nil,
            dbClusterParameterGroupName: Swift.String? = nil,
            dbParameterGroupFamily: Swift.String? = nil,
            description: Swift.String? = nil
        )
        {
            self.dbClusterParameterGroupArn = dbClusterParameterGroupArn
            self.dbClusterParameterGroupName = dbClusterParameterGroupName
            self.dbParameterGroupFamily = dbParameterGroupFamily
            self.description = description
        }
    }
}

public struct CopyDBClusterParameterGroupOutput: Swift.Sendable {
    /// Detailed information about a cluster parameter group.
    public var dbClusterParameterGroup: DocDBClientTypes.DBClusterParameterGroup?

    public init(
        dbClusterParameterGroup: DocDBClientTypes.DBClusterParameterGroup? = nil
    )
    {
        self.dbClusterParameterGroup = dbClusterParameterGroup
    }
}

/// You already have a cluster snapshot with the given identifier.
public struct DBClusterSnapshotAlreadyExistsFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBClusterSnapshotAlreadyExistsFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// DBClusterSnapshotIdentifier doesn't refer to an existing cluster snapshot.
public struct DBClusterSnapshotNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBClusterSnapshotNotFoundFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The provided value isn't a valid cluster snapshot state.
public struct InvalidDBClusterSnapshotStateFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidDBClusterSnapshotStateFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// An error occurred when accessing an KMS key.
public struct KMSKeyNotAccessibleFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "KMSKeyNotAccessibleFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request would cause you to exceed the allowed number of snapshots.
public struct SnapshotQuotaExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SnapshotQuotaExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [CopyDBClusterSnapshot].
public struct CopyDBClusterSnapshotInput: Swift.Sendable {
    /// Set to true to copy all tags from the source cluster snapshot to the target cluster snapshot, and otherwise false. The default is false.
    public var copyTags: Swift.Bool?
    /// The KMS key ID for an encrypted cluster snapshot. The KMS key ID is the Amazon Resource Name (ARN), KMS key identifier, or the KMS key alias for the KMS encryption key. If you copy an encrypted cluster snapshot from your Amazon Web Services account, you can specify a value for KmsKeyId to encrypt the copy with a new KMS encryption key. If you don't specify a value for KmsKeyId, then the copy of the cluster snapshot is encrypted with the same KMS key as the source cluster snapshot. If you copy an encrypted cluster snapshot that is shared from another Amazon Web Services account, then you must specify a value for KmsKeyId. To copy an encrypted cluster snapshot to another Amazon Web Services Region, set KmsKeyId to the KMS key ID that you want to use to encrypt the copy of the cluster snapshot in the destination Region. KMS encryption keys are specific to the Amazon Web Services Region that they are created in, and you can't use encryption keys from one Amazon Web Services Region in another Amazon Web Services Region. If you copy an unencrypted cluster snapshot and specify a value for the KmsKeyId parameter, an error is returned.
    public var kmsKeyId: Swift.String?
    /// The URL that contains a Signature Version 4 signed request for theCopyDBClusterSnapshot API action in the Amazon Web Services Region that contains the source cluster snapshot to copy. You must use the PreSignedUrl parameter when copying a cluster snapshot from another Amazon Web Services Region. If you are using an Amazon Web Services SDK tool or the CLI, you can specify SourceRegion (or --source-region for the CLI) instead of specifying PreSignedUrl manually. Specifying SourceRegion autogenerates a pre-signed URL that is a valid request for the operation that can be executed in the source Amazon Web Services Region. The presigned URL must be a valid request for the CopyDBClusterSnapshot API action that can be executed in the source Amazon Web Services Region that contains the cluster snapshot to be copied. The presigned URL request must contain the following parameter values:
    ///
    /// * SourceRegion - The ID of the region that contains the snapshot to be copied.
    ///
    /// * SourceDBClusterSnapshotIdentifier - The identifier for the the encrypted cluster snapshot to be copied. This identifier must be in the Amazon Resource Name (ARN) format for the source Amazon Web Services Region. For example, if you are copying an encrypted cluster snapshot from the us-east-1 Amazon Web Services Region, then your SourceDBClusterSnapshotIdentifier looks something like the following: arn:aws:rds:us-east-1:12345678012:sample-cluster:sample-cluster-snapshot.
    ///
    /// * TargetDBClusterSnapshotIdentifier - The identifier for the new cluster snapshot to be created. This parameter isn't case sensitive.
    public var preSignedUrl: Swift.String?
    /// The identifier of the cluster snapshot to copy. This parameter is not case sensitive. Constraints:
    ///
    /// * Must specify a valid system snapshot in the available state.
    ///
    /// * If the source snapshot is in the same Amazon Web Services Region as the copy, specify a valid snapshot identifier.
    ///
    /// * If the source snapshot is in a different Amazon Web Services Region than the copy, specify a valid cluster snapshot ARN.
    ///
    ///
    /// Example: my-cluster-snapshot1
    /// This member is required.
    public var sourceDBClusterSnapshotIdentifier: Swift.String?
    /// The tags to be assigned to the cluster snapshot.
    public var tags: [DocDBClientTypes.Tag]?
    /// The identifier of the new cluster snapshot to create from the source cluster snapshot. This parameter is not case sensitive. Constraints:
    ///
    /// * Must contain from 1 to 63 letters, numbers, or hyphens.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    ///
    ///
    /// Example: my-cluster-snapshot2
    /// This member is required.
    public var targetDBClusterSnapshotIdentifier: Swift.String?

    public init(
        copyTags: Swift.Bool? = nil,
        kmsKeyId: Swift.String? = nil,
        preSignedUrl: Swift.String? = nil,
        sourceDBClusterSnapshotIdentifier: Swift.String? = nil,
        tags: [DocDBClientTypes.Tag]? = nil,
        targetDBClusterSnapshotIdentifier: Swift.String? = nil
    )
    {
        self.copyTags = copyTags
        self.kmsKeyId = kmsKeyId
        self.preSignedUrl = preSignedUrl
        self.sourceDBClusterSnapshotIdentifier = sourceDBClusterSnapshotIdentifier
        self.tags = tags
        self.targetDBClusterSnapshotIdentifier = targetDBClusterSnapshotIdentifier
    }
}

extension DocDBClientTypes {

    /// Detailed information about a cluster snapshot.
    public struct DBClusterSnapshot: Swift.Sendable {
        /// Provides the list of Amazon EC2 Availability Zones that instances in the cluster snapshot can be restored in.
        public var availabilityZones: [Swift.String]?
        /// Specifies the time when the cluster was created, in Universal Coordinated Time (UTC).
        public var clusterCreateTime: Foundation.Date?
        /// Specifies the cluster identifier of the cluster that this cluster snapshot was created from.
        public var dbClusterIdentifier: Swift.String?
        /// The Amazon Resource Name (ARN) for the cluster snapshot.
        public var dbClusterSnapshotArn: Swift.String?
        /// Specifies the identifier for the cluster snapshot.
        public var dbClusterSnapshotIdentifier: Swift.String?
        /// Specifies the name of the database engine.
        public var engine: Swift.String?
        /// Provides the version of the database engine for this cluster snapshot.
        public var engineVersion: Swift.String?
        /// If StorageEncrypted is true, the KMS key identifier for the encrypted cluster snapshot.
        public var kmsKeyId: Swift.String?
        /// Provides the master user name for the cluster snapshot.
        public var masterUsername: Swift.String?
        /// Specifies the percentage of the estimated data that has been transferred.
        public var percentProgress: Swift.Int?
        /// Specifies the port that the cluster was listening on at the time of the snapshot.
        public var port: Swift.Int?
        /// Provides the time when the snapshot was taken, in UTC.
        public var snapshotCreateTime: Foundation.Date?
        /// Provides the type of the cluster snapshot.
        public var snapshotType: Swift.String?
        /// If the cluster snapshot was copied from a source cluster snapshot, the ARN for the source cluster snapshot; otherwise, a null value.
        public var sourceDBClusterSnapshotArn: Swift.String?
        /// Specifies the status of this cluster snapshot.
        public var status: Swift.String?
        /// Specifies whether the cluster snapshot is encrypted.
        public var storageEncrypted: Swift.Bool?
        /// Storage type associated with your cluster snapshot For information on storage types for Amazon DocumentDB clusters, see Cluster storage configurations in the Amazon DocumentDB Developer Guide. Valid values for storage type - standard | iopt1 Default value is standard
        public var storageType: Swift.String?
        /// Provides the virtual private cloud (VPC) ID that is associated with the cluster snapshot.
        public var vpcId: Swift.String?

        public init(
            availabilityZones: [Swift.String]? = nil,
            clusterCreateTime: Foundation.Date? = nil,
            dbClusterIdentifier: Swift.String? = nil,
            dbClusterSnapshotArn: Swift.String? = nil,
            dbClusterSnapshotIdentifier: Swift.String? = nil,
            engine: Swift.String? = nil,
            engineVersion: Swift.String? = nil,
            kmsKeyId: Swift.String? = nil,
            masterUsername: Swift.String? = nil,
            percentProgress: Swift.Int? = nil,
            port: Swift.Int? = nil,
            snapshotCreateTime: Foundation.Date? = nil,
            snapshotType: Swift.String? = nil,
            sourceDBClusterSnapshotArn: Swift.String? = nil,
            status: Swift.String? = nil,
            storageEncrypted: Swift.Bool? = nil,
            storageType: Swift.String? = nil,
            vpcId: Swift.String? = nil
        )
        {
            self.availabilityZones = availabilityZones
            self.clusterCreateTime = clusterCreateTime
            self.dbClusterIdentifier = dbClusterIdentifier
            self.dbClusterSnapshotArn = dbClusterSnapshotArn
            self.dbClusterSnapshotIdentifier = dbClusterSnapshotIdentifier
            self.engine = engine
            self.engineVersion = engineVersion
            self.kmsKeyId = kmsKeyId
            self.masterUsername = masterUsername
            self.percentProgress = percentProgress
            self.port = port
            self.snapshotCreateTime = snapshotCreateTime
            self.snapshotType = snapshotType
            self.sourceDBClusterSnapshotArn = sourceDBClusterSnapshotArn
            self.status = status
            self.storageEncrypted = storageEncrypted
            self.storageType = storageType
            self.vpcId = vpcId
        }
    }
}

public struct CopyDBClusterSnapshotOutput: Swift.Sendable {
    /// Detailed information about a cluster snapshot.
    public var dbClusterSnapshot: DocDBClientTypes.DBClusterSnapshot?

    public init(
        dbClusterSnapshot: DocDBClientTypes.DBClusterSnapshot? = nil
    )
    {
        self.dbClusterSnapshot = dbClusterSnapshot
    }
}

/// You already have a cluster with the given identifier.
public struct DBClusterAlreadyExistsFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBClusterAlreadyExistsFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// DBClusterParameterGroupName doesn't refer to an existing cluster parameter group.
public struct DBClusterParameterGroupNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBClusterParameterGroupNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The cluster can't be created because you have reached the maximum allowed quota of clusters.
public struct DBClusterQuotaExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBClusterQuotaExceededFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Subnets in the subnet group should cover at least two Availability Zones unless there is only one Availability Zone.
public struct DBSubnetGroupDoesNotCoverEnoughAZs: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBSubnetGroupDoesNotCoverEnoughAZs" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// DBSubnetGroupName doesn't refer to an existing subnet group.
public struct DBSubnetGroupNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBSubnetGroupNotFoundFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The GlobalClusterIdentifier doesn't refer to an existing global cluster.
public struct GlobalClusterNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "GlobalClusterNotFoundFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// There is not enough storage available for the current action. You might be able to resolve this error by updating your subnet group to use different Availability Zones that have more storage available.
public struct InsufficientStorageClusterCapacityFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InsufficientStorageClusterCapacity" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The subnet group can't be deleted because it's in use.
public struct InvalidDBSubnetGroupStateFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidDBSubnetGroupStateFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The requested operation can't be performed while the cluster is in this state.
public struct InvalidGlobalClusterStateFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidGlobalClusterStateFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The requested subnet is not valid, or multiple subnets were requested that are not all in a common virtual private cloud (VPC).
public struct InvalidSubnet: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidSubnet" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The subnet group doesn't cover all Availability Zones after it is created because of changes that were made.
public struct InvalidVPCNetworkStateFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidVPCNetworkStateFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request would cause you to exceed the allowed amount of storage available across all instances.
public struct StorageQuotaExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "StorageQuotaExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [CreateDBCluster].
public struct CreateDBClusterInput: Swift.Sendable {
    /// A list of Amazon EC2 Availability Zones that instances in the cluster can be created in.
    public var availabilityZones: [Swift.String]?
    /// The number of days for which automated backups are retained. You must specify a minimum value of 1. Default: 1 Constraints:
    ///
    /// * Must be a value from 1 to 35.
    public var backupRetentionPeriod: Swift.Int?
    /// The cluster identifier. This parameter is stored as a lowercase string. Constraints:
    ///
    /// * Must contain from 1 to 63 letters, numbers, or hyphens.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    ///
    ///
    /// Example: my-cluster
    /// This member is required.
    public var dbClusterIdentifier: Swift.String?
    /// The name of the cluster parameter group to associate with this cluster.
    public var dbClusterParameterGroupName: Swift.String?
    /// A subnet group to associate with this cluster. Constraints: Must match the name of an existing DBSubnetGroup. Must not be default. Example: mySubnetgroup
    public var dbSubnetGroupName: Swift.String?
    /// Specifies whether this cluster can be deleted. If DeletionProtection is enabled, the cluster cannot be deleted unless it is modified and DeletionProtection is disabled. DeletionProtection protects clusters from being accidentally deleted.
    public var deletionProtection: Swift.Bool?
    /// A list of log types that need to be enabled for exporting to Amazon CloudWatch Logs. You can enable audit logs or profiler logs. For more information, see [ Auditing Amazon DocumentDB Events](https://docs.aws.amazon.com/documentdb/latest/developerguide/event-auditing.html) and [ Profiling Amazon DocumentDB Operations](https://docs.aws.amazon.com/documentdb/latest/developerguide/profiling.html).
    public var enableCloudwatchLogsExports: [Swift.String]?
    /// The name of the database engine to be used for this cluster. Valid values: docdb
    /// This member is required.
    public var engine: Swift.String?
    /// The version number of the database engine to use. The --engine-version will default to the latest major engine version. For production workloads, we recommend explicitly declaring this parameter with the intended major engine version.
    public var engineVersion: Swift.String?
    /// The cluster identifier of the new global cluster.
    public var globalClusterIdentifier: Swift.String?
    /// The KMS key identifier for an encrypted cluster. The KMS key identifier is the Amazon Resource Name (ARN) for the KMS encryption key. If you are creating a cluster using the same Amazon Web Services account that owns the KMS encryption key that is used to encrypt the new cluster, you can use the KMS key alias instead of the ARN for the KMS encryption key. If an encryption key is not specified in KmsKeyId:
    ///
    /// * If the StorageEncrypted parameter is true, Amazon DocumentDB uses your default encryption key.
    ///
    ///
    /// KMS creates the default encryption key for your Amazon Web Services account. Your Amazon Web Services account has a different default encryption key for each Amazon Web Services Regions.
    public var kmsKeyId: Swift.String?
    /// Specifies whether to manage the master user password with Amazon Web Services Secrets Manager. Constraint: You can't manage the master user password with Amazon Web Services Secrets Manager if MasterUserPassword is specified.
    public var manageMasterUserPassword: Swift.Bool?
    /// The password for the master database user. This password can contain any printable ASCII character except forward slash (/), double quote ("), or the "at" symbol (@). Constraints: Must contain from 8 to 100 characters.
    public var masterUserPassword: Swift.String?
    /// The Amazon Web Services KMS key identifier to encrypt a secret that is automatically generated and managed in Amazon Web Services Secrets Manager. This setting is valid only if the master user password is managed by Amazon DocumentDB in Amazon Web Services Secrets Manager for the DB cluster. The Amazon Web Services KMS key identifier is the key ARN, key ID, alias ARN, or alias name for the KMS key. To use a KMS key in a different Amazon Web Services account, specify the key ARN or alias ARN. If you don't specify MasterUserSecretKmsKeyId, then the aws/secretsmanager KMS key is used to encrypt the secret. If the secret is in a different Amazon Web Services account, then you can't use the aws/secretsmanager KMS key to encrypt the secret, and you must use a customer managed KMS key. There is a default KMS key for your Amazon Web Services account. Your Amazon Web Services account has a different default KMS key for each Amazon Web Services Region.
    public var masterUserSecretKmsKeyId: Swift.String?
    /// The name of the master user for the cluster. Constraints:
    ///
    /// * Must be from 1 to 63 letters or numbers.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot be a reserved word for the chosen database engine.
    public var masterUsername: Swift.String?
    /// The port number on which the instances in the cluster accept connections.
    public var port: Swift.Int?
    /// Not currently supported.
    public var preSignedUrl: Swift.String?
    /// The daily time range during which automated backups are created if automated backups are enabled using the BackupRetentionPeriod parameter. The default is a 30-minute window selected at random from an 8-hour block of time for each Amazon Web Services Region. Constraints:
    ///
    /// * Must be in the format hh24:mi-hh24:mi.
    ///
    /// * Must be in Universal Coordinated Time (UTC).
    ///
    /// * Must not conflict with the preferred maintenance window.
    ///
    /// * Must be at least 30 minutes.
    public var preferredBackupWindow: Swift.String?
    /// The weekly time range during which system maintenance can occur, in Universal Coordinated Time (UTC). Format: ddd:hh24:mi-ddd:hh24:mi The default is a 30-minute window selected at random from an 8-hour block of time for each Amazon Web Services Region, occurring on a random day of the week. Valid days: Mon, Tue, Wed, Thu, Fri, Sat, Sun Constraints: Minimum 30-minute window.
    public var preferredMaintenanceWindow: Swift.String?
    /// Specifies whether the cluster is encrypted.
    public var storageEncrypted: Swift.Bool?
    /// The storage type to associate with the DB cluster. For information on storage types for Amazon DocumentDB clusters, see Cluster storage configurations in the Amazon DocumentDB Developer Guide. Valid values for storage type - standard | iopt1 Default value is standard  When you create a DocumentDB DB cluster with the storage type set to iopt1, the storage type is returned in the response. The storage type isn't returned when you set it to standard.
    public var storageType: Swift.String?
    /// The tags to be assigned to the cluster.
    public var tags: [DocDBClientTypes.Tag]?
    /// A list of EC2 VPC security groups to associate with this cluster.
    public var vpcSecurityGroupIds: [Swift.String]?

    public init(
        availabilityZones: [Swift.String]? = nil,
        backupRetentionPeriod: Swift.Int? = nil,
        dbClusterIdentifier: Swift.String? = nil,
        dbClusterParameterGroupName: Swift.String? = nil,
        dbSubnetGroupName: Swift.String? = nil,
        deletionProtection: Swift.Bool? = nil,
        enableCloudwatchLogsExports: [Swift.String]? = nil,
        engine: Swift.String? = nil,
        engineVersion: Swift.String? = nil,
        globalClusterIdentifier: Swift.String? = nil,
        kmsKeyId: Swift.String? = nil,
        manageMasterUserPassword: Swift.Bool? = nil,
        masterUserPassword: Swift.String? = nil,
        masterUserSecretKmsKeyId: Swift.String? = nil,
        masterUsername: Swift.String? = nil,
        port: Swift.Int? = nil,
        preSignedUrl: Swift.String? = nil,
        preferredBackupWindow: Swift.String? = nil,
        preferredMaintenanceWindow: Swift.String? = nil,
        storageEncrypted: Swift.Bool? = nil,
        storageType: Swift.String? = nil,
        tags: [DocDBClientTypes.Tag]? = nil,
        vpcSecurityGroupIds: [Swift.String]? = nil
    )
    {
        self.availabilityZones = availabilityZones
        self.backupRetentionPeriod = backupRetentionPeriod
        self.dbClusterIdentifier = dbClusterIdentifier
        self.dbClusterParameterGroupName = dbClusterParameterGroupName
        self.dbSubnetGroupName = dbSubnetGroupName
        self.deletionProtection = deletionProtection
        self.enableCloudwatchLogsExports = enableCloudwatchLogsExports
        self.engine = engine
        self.engineVersion = engineVersion
        self.globalClusterIdentifier = globalClusterIdentifier
        self.kmsKeyId = kmsKeyId
        self.manageMasterUserPassword = manageMasterUserPassword
        self.masterUserPassword = masterUserPassword
        self.masterUserSecretKmsKeyId = masterUserSecretKmsKeyId
        self.masterUsername = masterUsername
        self.port = port
        self.preSignedUrl = preSignedUrl
        self.preferredBackupWindow = preferredBackupWindow
        self.preferredMaintenanceWindow = preferredMaintenanceWindow
        self.storageEncrypted = storageEncrypted
        self.storageType = storageType
        self.tags = tags
        self.vpcSecurityGroupIds = vpcSecurityGroupIds
    }
}

extension DocDBClientTypes {

    /// Describes an Identity and Access Management (IAM) role that is associated with a cluster.
    public struct DBClusterRole: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the IAMrole that is associated with the DB cluster.
        public var roleArn: Swift.String?
        /// Describes the state of association between the IAMrole and the cluster. The Status property returns one of the following values:
        ///
        /// * ACTIVE - The IAMrole ARN is associated with the cluster and can be used to access other Amazon Web Services services on your behalf.
        ///
        /// * PENDING - The IAMrole ARN is being associated with the cluster.
        ///
        /// * INVALID - The IAMrole ARN is associated with the cluster, but the cluster cannot assume the IAMrole to access other Amazon Web Services services on your behalf.
        public var status: Swift.String?

        public init(
            roleArn: Swift.String? = nil,
            status: Swift.String? = nil
        )
        {
            self.roleArn = roleArn
            self.status = status
        }
    }
}

extension DocDBClientTypes {

    /// Contains information about an instance that is part of a cluster.
    public struct DBClusterMember: Swift.Sendable {
        /// Specifies the status of the cluster parameter group for this member of the DB cluster.
        public var dbClusterParameterGroupStatus: Swift.String?
        /// Specifies the instance identifier for this member of the cluster.
        public var dbInstanceIdentifier: Swift.String?
        /// A value that is true if the cluster member is the primary instance for the cluster and false otherwise.
        public var isClusterWriter: Swift.Bool?
        /// A value that specifies the order in which an Amazon DocumentDB replica is promoted to the primary instance after a failure of the existing primary instance.
        public var promotionTier: Swift.Int?

        public init(
            dbClusterParameterGroupStatus: Swift.String? = nil,
            dbInstanceIdentifier: Swift.String? = nil,
            isClusterWriter: Swift.Bool? = nil,
            promotionTier: Swift.Int? = nil
        )
        {
            self.dbClusterParameterGroupStatus = dbClusterParameterGroupStatus
            self.dbInstanceIdentifier = dbInstanceIdentifier
            self.isClusterWriter = isClusterWriter
            self.promotionTier = promotionTier
        }
    }
}

extension DocDBClientTypes {

    /// Contains the secret managed by Amazon DocumentDB in Amazon Web Services Secrets Manager for the master user password.
    public struct ClusterMasterUserSecret: Swift.Sendable {
        /// The Amazon Web Services KMS key identifier that is used to encrypt the secret.
        public var kmsKeyId: Swift.String?
        /// The Amazon Resource Name (ARN) of the secret.
        public var secretArn: Swift.String?
        /// The status of the secret. The possible status values include the following:
        ///
        /// * creating - The secret is being created.
        ///
        /// * active - The secret is available for normal use and rotation.
        ///
        /// * rotating - The secret is being rotated.
        ///
        /// * impaired - The secret can be used to access database credentials, but it can't be rotated. A secret might have this status if, for example, permissions are changed so that Amazon DocumentDB can no longer access either the secret or the KMS key for the secret. When a secret has this status, you can correct the condition that caused the status. Alternatively, modify the instance to turn off automatic management of database credentials, and then modify the instance again to turn on automatic management of database credentials.
        public var secretStatus: Swift.String?

        public init(
            kmsKeyId: Swift.String? = nil,
            secretArn: Swift.String? = nil,
            secretStatus: Swift.String? = nil
        )
        {
            self.kmsKeyId = kmsKeyId
            self.secretArn = secretArn
            self.secretStatus = secretStatus
        }
    }
}

extension DocDBClientTypes {

    /// Used as a response element for queries on virtual private cloud (VPC) security group membership.
    public struct VpcSecurityGroupMembership: Swift.Sendable {
        /// The status of the VPC security group.
        public var status: Swift.String?
        /// The name of the VPC security group.
        public var vpcSecurityGroupId: Swift.String?

        public init(
            status: Swift.String? = nil,
            vpcSecurityGroupId: Swift.String? = nil
        )
        {
            self.status = status
            self.vpcSecurityGroupId = vpcSecurityGroupId
        }
    }
}

extension DocDBClientTypes {

    /// Detailed information about a cluster.
    public struct DBCluster: Swift.Sendable {
        /// Provides a list of the Identity and Access Management (IAM) roles that are associated with the cluster. (IAM) roles that are associated with a cluster grant permission for the cluster to access other Amazon Web Services services on your behalf.
        public var associatedRoles: [DocDBClientTypes.DBClusterRole]?
        /// Provides the list of Amazon EC2 Availability Zones that instances in the cluster can be created in.
        public var availabilityZones: [Swift.String]?
        /// Specifies the number of days for which automatic snapshots are retained.
        public var backupRetentionPeriod: Swift.Int?
        /// Identifies the clone group to which the DB cluster is associated.
        public var cloneGroupId: Swift.String?
        /// Specifies the time when the cluster was created, in Universal Coordinated Time (UTC).
        public var clusterCreateTime: Foundation.Date?
        /// The Amazon Resource Name (ARN) for the cluster.
        public var dbClusterArn: Swift.String?
        /// Contains a user-supplied cluster identifier. This identifier is the unique key that identifies a cluster.
        public var dbClusterIdentifier: Swift.String?
        /// Provides the list of instances that make up the cluster.
        public var dbClusterMembers: [DocDBClientTypes.DBClusterMember]?
        /// Specifies the name of the cluster parameter group for the cluster.
        public var dbClusterParameterGroup: Swift.String?
        /// The Amazon Web Services Region-unique, immutable identifier for the cluster. This identifier is found in CloudTrail log entries whenever the KMS key for the cluster is accessed.
        public var dbClusterResourceId: Swift.String?
        /// Specifies information on the subnet group that is associated with the cluster, including the name, description, and subnets in the subnet group.
        public var dbSubnetGroup: Swift.String?
        /// Specifies whether this cluster can be deleted. If DeletionProtection is enabled, the cluster cannot be deleted unless it is modified and DeletionProtection is disabled. DeletionProtection protects clusters from being accidentally deleted.
        public var deletionProtection: Swift.Bool?
        /// The earliest time to which a database can be restored with point-in-time restore.
        public var earliestRestorableTime: Foundation.Date?
        /// A list of log types that this cluster is configured to export to Amazon CloudWatch Logs.
        public var enabledCloudwatchLogsExports: [Swift.String]?
        /// Specifies the connection endpoint for the primary instance of the cluster.
        public var endpoint: Swift.String?
        /// Provides the name of the database engine to be used for this cluster.
        public var engine: Swift.String?
        /// Indicates the database engine version.
        public var engineVersion: Swift.String?
        /// Specifies the ID that Amazon Route 53 assigns when you create a hosted zone.
        public var hostedZoneId: Swift.String?
        /// If StorageEncrypted is true, the KMS key identifier for the encrypted cluster.
        public var kmsKeyId: Swift.String?
        /// Specifies the latest time to which a database can be restored with point-in-time restore.
        public var latestRestorableTime: Foundation.Date?
        /// The secret managed by Amazon DocumentDB in Amazon Web Services Secrets Manager for the master user password.
        public var masterUserSecret: DocDBClientTypes.ClusterMasterUserSecret?
        /// Contains the master user name for the cluster.
        public var masterUsername: Swift.String?
        /// Specifies whether the cluster has instances in multiple Availability Zones.
        public var multiAZ: Swift.Bool?
        /// Specifies the progress of the operation as a percentage.
        public var percentProgress: Swift.String?
        /// Specifies the port that the database engine is listening on.
        public var port: Swift.Int?
        /// Specifies the daily time range during which automated backups are created if automated backups are enabled, as determined by the BackupRetentionPeriod.
        public var preferredBackupWindow: Swift.String?
        /// Specifies the weekly time range during which system maintenance can occur, in Universal Coordinated Time (UTC).
        public var preferredMaintenanceWindow: Swift.String?
        /// Contains one or more identifiers of the secondary clusters that are associated with this cluster.
        public var readReplicaIdentifiers: [Swift.String]?
        /// The reader endpoint for the cluster. The reader endpoint for a cluster load balances connections across the Amazon DocumentDB replicas that are available in a cluster. As clients request new connections to the reader endpoint, Amazon DocumentDB distributes the connection requests among the Amazon DocumentDB replicas in the cluster. This functionality can help balance your read workload across multiple Amazon DocumentDB replicas in your cluster. If a failover occurs, and the Amazon DocumentDB replica that you are connected to is promoted to be the primary instance, your connection is dropped. To continue sending your read workload to other Amazon DocumentDB replicas in the cluster, you can then reconnect to the reader endpoint.
        public var readerEndpoint: Swift.String?
        /// Contains the identifier of the source cluster if this cluster is a secondary cluster.
        public var replicationSourceIdentifier: Swift.String?
        /// Specifies the current state of this cluster.
        public var status: Swift.String?
        /// Specifies whether the cluster is encrypted.
        public var storageEncrypted: Swift.Bool?
        /// Storage type associated with your cluster Storage type associated with your cluster For information on storage types for Amazon DocumentDB clusters, see Cluster storage configurations in the Amazon DocumentDB Developer Guide. Valid values for storage type - standard | iopt1 Default value is standard
        public var storageType: Swift.String?
        /// Provides a list of virtual private cloud (VPC) security groups that the cluster belongs to.
        public var vpcSecurityGroups: [DocDBClientTypes.VpcSecurityGroupMembership]?

        public init(
            associatedRoles: [DocDBClientTypes.DBClusterRole]? = nil,
            availabilityZones: [Swift.String]? = nil,
            backupRetentionPeriod: Swift.Int? = nil,
            cloneGroupId: Swift.String? = nil,
            clusterCreateTime: Foundation.Date? = nil,
            dbClusterArn: Swift.String? = nil,
            dbClusterIdentifier: Swift.String? = nil,
            dbClusterMembers: [DocDBClientTypes.DBClusterMember]? = nil,
            dbClusterParameterGroup: Swift.String? = nil,
            dbClusterResourceId: Swift.String? = nil,
            dbSubnetGroup: Swift.String? = nil,
            deletionProtection: Swift.Bool? = nil,
            earliestRestorableTime: Foundation.Date? = nil,
            enabledCloudwatchLogsExports: [Swift.String]? = nil,
            endpoint: Swift.String? = nil,
            engine: Swift.String? = nil,
            engineVersion: Swift.String? = nil,
            hostedZoneId: Swift.String? = nil,
            kmsKeyId: Swift.String? = nil,
            latestRestorableTime: Foundation.Date? = nil,
            masterUserSecret: DocDBClientTypes.ClusterMasterUserSecret? = nil,
            masterUsername: Swift.String? = nil,
            multiAZ: Swift.Bool? = nil,
            percentProgress: Swift.String? = nil,
            port: Swift.Int? = nil,
            preferredBackupWindow: Swift.String? = nil,
            preferredMaintenanceWindow: Swift.String? = nil,
            readReplicaIdentifiers: [Swift.String]? = nil,
            readerEndpoint: Swift.String? = nil,
            replicationSourceIdentifier: Swift.String? = nil,
            status: Swift.String? = nil,
            storageEncrypted: Swift.Bool? = nil,
            storageType: Swift.String? = nil,
            vpcSecurityGroups: [DocDBClientTypes.VpcSecurityGroupMembership]? = nil
        )
        {
            self.associatedRoles = associatedRoles
            self.availabilityZones = availabilityZones
            self.backupRetentionPeriod = backupRetentionPeriod
            self.cloneGroupId = cloneGroupId
            self.clusterCreateTime = clusterCreateTime
            self.dbClusterArn = dbClusterArn
            self.dbClusterIdentifier = dbClusterIdentifier
            self.dbClusterMembers = dbClusterMembers
            self.dbClusterParameterGroup = dbClusterParameterGroup
            self.dbClusterResourceId = dbClusterResourceId
            self.dbSubnetGroup = dbSubnetGroup
            self.deletionProtection = deletionProtection
            self.earliestRestorableTime = earliestRestorableTime
            self.enabledCloudwatchLogsExports = enabledCloudwatchLogsExports
            self.endpoint = endpoint
            self.engine = engine
            self.engineVersion = engineVersion
            self.hostedZoneId = hostedZoneId
            self.kmsKeyId = kmsKeyId
            self.latestRestorableTime = latestRestorableTime
            self.masterUserSecret = masterUserSecret
            self.masterUsername = masterUsername
            self.multiAZ = multiAZ
            self.percentProgress = percentProgress
            self.port = port
            self.preferredBackupWindow = preferredBackupWindow
            self.preferredMaintenanceWindow = preferredMaintenanceWindow
            self.readReplicaIdentifiers = readReplicaIdentifiers
            self.readerEndpoint = readerEndpoint
            self.replicationSourceIdentifier = replicationSourceIdentifier
            self.status = status
            self.storageEncrypted = storageEncrypted
            self.storageType = storageType
            self.vpcSecurityGroups = vpcSecurityGroups
        }
    }
}

public struct CreateDBClusterOutput: Swift.Sendable {
    /// Detailed information about a cluster.
    public var dbCluster: DocDBClientTypes.DBCluster?

    public init(
        dbCluster: DocDBClientTypes.DBCluster? = nil
    )
    {
        self.dbCluster = dbCluster
    }
}

/// Represents the input of [CreateDBClusterParameterGroup].
public struct CreateDBClusterParameterGroupInput: Swift.Sendable {
    /// The name of the cluster parameter group. Constraints:
    ///
    /// * Must not match the name of an existing DBClusterParameterGroup.
    ///
    ///
    /// This value is stored as a lowercase string.
    /// This member is required.
    public var dbClusterParameterGroupName: Swift.String?
    /// The cluster parameter group family name.
    /// This member is required.
    public var dbParameterGroupFamily: Swift.String?
    /// The description for the cluster parameter group.
    /// This member is required.
    public var description: Swift.String?
    /// The tags to be assigned to the cluster parameter group.
    public var tags: [DocDBClientTypes.Tag]?

    public init(
        dbClusterParameterGroupName: Swift.String? = nil,
        dbParameterGroupFamily: Swift.String? = nil,
        description: Swift.String? = nil,
        tags: [DocDBClientTypes.Tag]? = nil
    )
    {
        self.dbClusterParameterGroupName = dbClusterParameterGroupName
        self.dbParameterGroupFamily = dbParameterGroupFamily
        self.description = description
        self.tags = tags
    }
}

public struct CreateDBClusterParameterGroupOutput: Swift.Sendable {
    /// Detailed information about a cluster parameter group.
    public var dbClusterParameterGroup: DocDBClientTypes.DBClusterParameterGroup?

    public init(
        dbClusterParameterGroup: DocDBClientTypes.DBClusterParameterGroup? = nil
    )
    {
        self.dbClusterParameterGroup = dbClusterParameterGroup
    }
}

/// Represents the input of [CreateDBClusterSnapshot].
public struct CreateDBClusterSnapshotInput: Swift.Sendable {
    /// The identifier of the cluster to create a snapshot for. This parameter is not case sensitive. Constraints:
    ///
    /// * Must match the identifier of an existing DBCluster.
    ///
    ///
    /// Example: my-cluster
    /// This member is required.
    public var dbClusterIdentifier: Swift.String?
    /// The identifier of the cluster snapshot. This parameter is stored as a lowercase string. Constraints:
    ///
    /// * Must contain from 1 to 63 letters, numbers, or hyphens.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    ///
    ///
    /// Example: my-cluster-snapshot1
    /// This member is required.
    public var dbClusterSnapshotIdentifier: Swift.String?
    /// The tags to be assigned to the cluster snapshot.
    public var tags: [DocDBClientTypes.Tag]?

    public init(
        dbClusterIdentifier: Swift.String? = nil,
        dbClusterSnapshotIdentifier: Swift.String? = nil,
        tags: [DocDBClientTypes.Tag]? = nil
    )
    {
        self.dbClusterIdentifier = dbClusterIdentifier
        self.dbClusterSnapshotIdentifier = dbClusterSnapshotIdentifier
        self.tags = tags
    }
}

public struct CreateDBClusterSnapshotOutput: Swift.Sendable {
    /// Detailed information about a cluster snapshot.
    public var dbClusterSnapshot: DocDBClientTypes.DBClusterSnapshot?

    public init(
        dbClusterSnapshot: DocDBClientTypes.DBClusterSnapshot? = nil
    )
    {
        self.dbClusterSnapshot = dbClusterSnapshot
    }
}

/// The specified CIDR IP or Amazon EC2 security group isn't authorized for the specified security group. Amazon DocumentDB also might not be authorized to perform necessary actions on your behalf using IAM.
public struct AuthorizationNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AuthorizationNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// You already have a instance with the given identifier.
public struct DBInstanceAlreadyExistsFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBInstanceAlreadyExists" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// DBSecurityGroupName doesn't refer to an existing security group.
public struct DBSecurityGroupNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBSecurityGroupNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request would cause you to exceed the allowed number of instances.
public struct InstanceQuotaExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InstanceQuotaExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified instance class isn't available in the specified Availability Zone.
public struct InsufficientDBInstanceCapacityFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InsufficientDBInstanceCapacity" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Storage of the specified StorageType can't be associated with the DB instance.
public struct StorageTypeNotSupportedFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "StorageTypeNotSupported" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [CreateDBInstance].
public struct CreateDBInstanceInput: Swift.Sendable {
    /// This parameter does not apply to Amazon DocumentDB. Amazon DocumentDB does not perform minor version upgrades regardless of the value set. Default: false
    public var autoMinorVersionUpgrade: Swift.Bool?
    /// The Amazon EC2 Availability Zone that the instance is created in. Default: A random, system-chosen Availability Zone in the endpoint's Amazon Web Services Region. Example: us-east-1d
    public var availabilityZone: Swift.String?
    /// The CA certificate identifier to use for the DB instance's server certificate. For more information, see [Updating Your Amazon DocumentDB TLS Certificates](https://docs.aws.amazon.com/documentdb/latest/developerguide/ca_cert_rotation.html) and [ Encrypting Data in Transit](https://docs.aws.amazon.com/documentdb/latest/developerguide/security.encryption.ssl.html) in the Amazon DocumentDB Developer Guide.
    public var caCertificateIdentifier: Swift.String?
    /// A value that indicates whether to copy tags from the DB instance to snapshots of the DB instance. By default, tags are not copied.
    public var copyTagsToSnapshot: Swift.Bool?
    /// The identifier of the cluster that the instance will belong to.
    /// This member is required.
    public var dbClusterIdentifier: Swift.String?
    /// The compute and memory capacity of the instance; for example, db.r5.large.
    /// This member is required.
    public var dbInstanceClass: Swift.String?
    /// The instance identifier. This parameter is stored as a lowercase string. Constraints:
    ///
    /// * Must contain from 1 to 63 letters, numbers, or hyphens.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    ///
    ///
    /// Example: mydbinstance
    /// This member is required.
    public var dbInstanceIdentifier: Swift.String?
    /// A value that indicates whether to enable Performance Insights for the DB Instance. For more information, see [Using Amazon Performance Insights](https://docs.aws.amazon.com/documentdb/latest/developerguide/performance-insights.html).
    public var enablePerformanceInsights: Swift.Bool?
    /// The name of the database engine to be used for this instance. Valid value: docdb
    /// This member is required.
    public var engine: Swift.String?
    /// The KMS key identifier for encryption of Performance Insights data. The KMS key identifier is the key ARN, key ID, alias ARN, or alias name for the KMS key. If you do not specify a value for PerformanceInsightsKMSKeyId, then Amazon DocumentDB uses your default KMS key. There is a default KMS key for your Amazon Web Services account. Your Amazon Web Services account has a different default KMS key for each Amazon Web Services region.
    public var performanceInsightsKMSKeyId: Swift.String?
    /// The time range each week during which system maintenance can occur, in Universal Coordinated Time (UTC). Format: ddd:hh24:mi-ddd:hh24:mi The default is a 30-minute window selected at random from an 8-hour block of time for each Amazon Web Services Region, occurring on a random day of the week. Valid days: Mon, Tue, Wed, Thu, Fri, Sat, Sun Constraints: Minimum 30-minute window.
    public var preferredMaintenanceWindow: Swift.String?
    /// A value that specifies the order in which an Amazon DocumentDB replica is promoted to the primary instance after a failure of the existing primary instance. Default: 1 Valid values: 0-15
    public var promotionTier: Swift.Int?
    /// The tags to be assigned to the instance. You can assign up to 10 tags to an instance.
    public var tags: [DocDBClientTypes.Tag]?

    public init(
        autoMinorVersionUpgrade: Swift.Bool? = nil,
        availabilityZone: Swift.String? = nil,
        caCertificateIdentifier: Swift.String? = nil,
        copyTagsToSnapshot: Swift.Bool? = nil,
        dbClusterIdentifier: Swift.String? = nil,
        dbInstanceClass: Swift.String? = nil,
        dbInstanceIdentifier: Swift.String? = nil,
        enablePerformanceInsights: Swift.Bool? = nil,
        engine: Swift.String? = nil,
        performanceInsightsKMSKeyId: Swift.String? = nil,
        preferredMaintenanceWindow: Swift.String? = nil,
        promotionTier: Swift.Int? = nil,
        tags: [DocDBClientTypes.Tag]? = nil
    )
    {
        self.autoMinorVersionUpgrade = autoMinorVersionUpgrade
        self.availabilityZone = availabilityZone
        self.caCertificateIdentifier = caCertificateIdentifier
        self.copyTagsToSnapshot = copyTagsToSnapshot
        self.dbClusterIdentifier = dbClusterIdentifier
        self.dbInstanceClass = dbInstanceClass
        self.dbInstanceIdentifier = dbInstanceIdentifier
        self.enablePerformanceInsights = enablePerformanceInsights
        self.engine = engine
        self.performanceInsightsKMSKeyId = performanceInsightsKMSKeyId
        self.preferredMaintenanceWindow = preferredMaintenanceWindow
        self.promotionTier = promotionTier
        self.tags = tags
    }
}

extension DocDBClientTypes {

    /// Returns the details of the DB instance’s server certificate. For more information, see [Updating Your Amazon DocumentDB TLS Certificates](https://docs.aws.amazon.com/documentdb/latest/developerguide/ca_cert_rotation.html) and [ Encrypting Data in Transit](https://docs.aws.amazon.com/documentdb/latest/developerguide/security.encryption.ssl.html) in the Amazon DocumentDB Developer Guide.
    public struct CertificateDetails: Swift.Sendable {
        /// The CA identifier of the CA certificate used for the DB instance's server certificate.
        public var caIdentifier: Swift.String?
        /// The expiration date of the DB instance’s server certificate.
        public var validTill: Foundation.Date?

        public init(
            caIdentifier: Swift.String? = nil,
            validTill: Foundation.Date? = nil
        )
        {
            self.caIdentifier = caIdentifier
            self.validTill = validTill
        }
    }
}

extension DocDBClientTypes {

    /// Information about an Availability Zone.
    public struct AvailabilityZone: Swift.Sendable {
        /// The name of the Availability Zone.
        public var name: Swift.String?

        public init(
            name: Swift.String? = nil
        )
        {
            self.name = name
        }
    }
}

extension DocDBClientTypes {

    /// Detailed information about a subnet.
    public struct Subnet: Swift.Sendable {
        /// Specifies the Availability Zone for the subnet.
        public var subnetAvailabilityZone: DocDBClientTypes.AvailabilityZone?
        /// Specifies the identifier of the subnet.
        public var subnetIdentifier: Swift.String?
        /// Specifies the status of the subnet.
        public var subnetStatus: Swift.String?

        public init(
            subnetAvailabilityZone: DocDBClientTypes.AvailabilityZone? = nil,
            subnetIdentifier: Swift.String? = nil,
            subnetStatus: Swift.String? = nil
        )
        {
            self.subnetAvailabilityZone = subnetAvailabilityZone
            self.subnetIdentifier = subnetIdentifier
            self.subnetStatus = subnetStatus
        }
    }
}

extension DocDBClientTypes {

    /// Detailed information about a subnet group.
    public struct DBSubnetGroup: Swift.Sendable {
        /// The Amazon Resource Name (ARN) for the DB subnet group.
        public var dbSubnetGroupArn: Swift.String?
        /// Provides the description of the subnet group.
        public var dbSubnetGroupDescription: Swift.String?
        /// The name of the subnet group.
        public var dbSubnetGroupName: Swift.String?
        /// Provides the status of the subnet group.
        public var subnetGroupStatus: Swift.String?
        /// Detailed information about one or more subnets within a subnet group.
        public var subnets: [DocDBClientTypes.Subnet]?
        /// Provides the virtual private cloud (VPC) ID of the subnet group.
        public var vpcId: Swift.String?

        public init(
            dbSubnetGroupArn: Swift.String? = nil,
            dbSubnetGroupDescription: Swift.String? = nil,
            dbSubnetGroupName: Swift.String? = nil,
            subnetGroupStatus: Swift.String? = nil,
            subnets: [DocDBClientTypes.Subnet]? = nil,
            vpcId: Swift.String? = nil
        )
        {
            self.dbSubnetGroupArn = dbSubnetGroupArn
            self.dbSubnetGroupDescription = dbSubnetGroupDescription
            self.dbSubnetGroupName = dbSubnetGroupName
            self.subnetGroupStatus = subnetGroupStatus
            self.subnets = subnets
            self.vpcId = vpcId
        }
    }
}

extension DocDBClientTypes {

    /// Network information for accessing a cluster or instance. Client programs must specify a valid endpoint to access these Amazon DocumentDB resources.
    public struct Endpoint: Swift.Sendable {
        /// Specifies the DNS address of the instance.
        public var address: Swift.String?
        /// Specifies the ID that Amazon Route 53 assigns when you create a hosted zone.
        public var hostedZoneId: Swift.String?
        /// Specifies the port that the database engine is listening on.
        public var port: Swift.Int?

        public init(
            address: Swift.String? = nil,
            hostedZoneId: Swift.String? = nil,
            port: Swift.Int? = nil
        )
        {
            self.address = address
            self.hostedZoneId = hostedZoneId
            self.port = port
        }
    }
}

extension DocDBClientTypes {

    /// A list of the log types whose configuration is still pending. These log types are in the process of being activated or deactivated.
    public struct PendingCloudwatchLogsExports: Swift.Sendable {
        /// Log types that are in the process of being enabled. After they are enabled, these log types are exported to Amazon CloudWatch Logs.
        public var logTypesToDisable: [Swift.String]?
        /// Log types that are in the process of being deactivated. After they are deactivated, these log types aren't exported to CloudWatch Logs.
        public var logTypesToEnable: [Swift.String]?

        public init(
            logTypesToDisable: [Swift.String]? = nil,
            logTypesToEnable: [Swift.String]? = nil
        )
        {
            self.logTypesToDisable = logTypesToDisable
            self.logTypesToEnable = logTypesToEnable
        }
    }
}

extension DocDBClientTypes {

    /// One or more modified settings for an instance. These modified settings have been requested, but haven't been applied yet.
    public struct PendingModifiedValues: Swift.Sendable {
        /// Contains the new AllocatedStorage size for then instance that will be applied or is currently being applied.
        public var allocatedStorage: Swift.Int?
        /// Specifies the pending number of days for which automated backups are retained.
        public var backupRetentionPeriod: Swift.Int?
        /// Specifies the identifier of the certificate authority (CA) certificate for the DB instance.
        public var caCertificateIdentifier: Swift.String?
        /// Contains the new DBInstanceClass for the instance that will be applied or is currently being applied.
        public var dbInstanceClass: Swift.String?
        /// Contains the new DBInstanceIdentifier for the instance that will be applied or is currently being applied.
        public var dbInstanceIdentifier: Swift.String?
        /// The new subnet group for the instance.
        public var dbSubnetGroupName: Swift.String?
        /// Indicates the database engine version.
        public var engineVersion: Swift.String?
        /// Specifies the new Provisioned IOPS value for the instance that will be applied or is currently being applied.
        public var iops: Swift.Int?
        /// The license model for the instance. Valid values: license-included, bring-your-own-license, general-public-license
        public var licenseModel: Swift.String?
        /// Contains the pending or currently in-progress change of the master credentials for the instance.
        public var masterUserPassword: Swift.String?
        /// Indicates that the Single-AZ instance is to change to a Multi-AZ deployment.
        public var multiAZ: Swift.Bool?
        /// A list of the log types whose configuration is still pending. These log types are in the process of being activated or deactivated.
        public var pendingCloudwatchLogsExports: DocDBClientTypes.PendingCloudwatchLogsExports?
        /// Specifies the pending port for the instance.
        public var port: Swift.Int?
        /// Specifies the storage type to be associated with the instance.
        public var storageType: Swift.String?

        public init(
            allocatedStorage: Swift.Int? = nil,
            backupRetentionPeriod: Swift.Int? = nil,
            caCertificateIdentifier: Swift.String? = nil,
            dbInstanceClass: Swift.String? = nil,
            dbInstanceIdentifier: Swift.String? = nil,
            dbSubnetGroupName: Swift.String? = nil,
            engineVersion: Swift.String? = nil,
            iops: Swift.Int? = nil,
            licenseModel: Swift.String? = nil,
            masterUserPassword: Swift.String? = nil,
            multiAZ: Swift.Bool? = nil,
            pendingCloudwatchLogsExports: DocDBClientTypes.PendingCloudwatchLogsExports? = nil,
            port: Swift.Int? = nil,
            storageType: Swift.String? = nil
        )
        {
            self.allocatedStorage = allocatedStorage
            self.backupRetentionPeriod = backupRetentionPeriod
            self.caCertificateIdentifier = caCertificateIdentifier
            self.dbInstanceClass = dbInstanceClass
            self.dbInstanceIdentifier = dbInstanceIdentifier
            self.dbSubnetGroupName = dbSubnetGroupName
            self.engineVersion = engineVersion
            self.iops = iops
            self.licenseModel = licenseModel
            self.masterUserPassword = masterUserPassword
            self.multiAZ = multiAZ
            self.pendingCloudwatchLogsExports = pendingCloudwatchLogsExports
            self.port = port
            self.storageType = storageType
        }
    }
}

extension DocDBClientTypes {

    /// Provides a list of status information for an instance.
    public struct DBInstanceStatusInfo: Swift.Sendable {
        /// Details of the error if there is an error for the instance. If the instance is not in an error state, this value is blank.
        public var message: Swift.String?
        /// A Boolean value that is true if the instance is operating normally, or false if the instance is in an error state.
        public var normal: Swift.Bool?
        /// Status of the instance. For a StatusType of read replica, the values can be replicating, error, stopped, or terminated.
        public var status: Swift.String?
        /// This value is currently "read replication."
        public var statusType: Swift.String?

        public init(
            message: Swift.String? = nil,
            normal: Swift.Bool? = nil,
            status: Swift.String? = nil,
            statusType: Swift.String? = nil
        )
        {
            self.message = message
            self.normal = normal
            self.status = status
            self.statusType = statusType
        }
    }
}

extension DocDBClientTypes {

    /// Detailed information about an instance.
    public struct DBInstance: Swift.Sendable {
        /// Does not apply. This parameter does not apply to Amazon DocumentDB. Amazon DocumentDB does not perform minor version upgrades regardless of the value set.
        public var autoMinorVersionUpgrade: Swift.Bool?
        /// Specifies the name of the Availability Zone that the instance is located in.
        public var availabilityZone: Swift.String?
        /// Specifies the number of days for which automatic snapshots are retained.
        public var backupRetentionPeriod: Swift.Int?
        /// The identifier of the CA certificate for this DB instance.
        public var caCertificateIdentifier: Swift.String?
        /// The details of the DB instance's server certificate.
        public var certificateDetails: DocDBClientTypes.CertificateDetails?
        /// A value that indicates whether to copy tags from the DB instance to snapshots of the DB instance. By default, tags are not copied.
        public var copyTagsToSnapshot: Swift.Bool?
        /// Contains the name of the cluster that the instance is a member of if the instance is a member of a cluster.
        public var dbClusterIdentifier: Swift.String?
        /// The Amazon Resource Name (ARN) for the instance.
        public var dbInstanceArn: Swift.String?
        /// Contains the name of the compute and memory capacity class of the instance.
        public var dbInstanceClass: Swift.String?
        /// Contains a user-provided database identifier. This identifier is the unique key that identifies an instance.
        public var dbInstanceIdentifier: Swift.String?
        /// Specifies the current state of this database.
        public var dbInstanceStatus: Swift.String?
        /// Specifies information on the subnet group that is associated with the instance, including the name, description, and subnets in the subnet group.
        public var dbSubnetGroup: DocDBClientTypes.DBSubnetGroup?
        /// The Amazon Web Services Region-unique, immutable identifier for the instance. This identifier is found in CloudTrail log entries whenever the KMS key for the instance is accessed.
        public var dbiResourceId: Swift.String?
        /// A list of log types that this instance is configured to export to CloudWatch Logs.
        public var enabledCloudwatchLogsExports: [Swift.String]?
        /// Specifies the connection endpoint.
        public var endpoint: DocDBClientTypes.Endpoint?
        /// Provides the name of the database engine to be used for this instance.
        public var engine: Swift.String?
        /// Indicates the database engine version.
        public var engineVersion: Swift.String?
        /// Provides the date and time that the instance was created.
        public var instanceCreateTime: Foundation.Date?
        /// If StorageEncrypted is true, the KMS key identifier for the encrypted instance.
        public var kmsKeyId: Swift.String?
        /// Specifies the latest time to which a database can be restored with point-in-time restore.
        public var latestRestorableTime: Foundation.Date?
        /// Specifies that changes to the instance are pending. This element is included only when changes are pending. Specific changes are identified by subelements.
        public var pendingModifiedValues: DocDBClientTypes.PendingModifiedValues?
        /// Set to true if Amazon RDS Performance Insights is enabled for the DB instance, and otherwise false.
        public var performanceInsightsEnabled: Swift.Bool?
        /// The KMS key identifier for encryption of Performance Insights data. The KMS key ID is the Amazon Resource Name (ARN), KMS key identifier, or the KMS key alias for the KMS encryption key.
        public var performanceInsightsKMSKeyId: Swift.String?
        /// Specifies the daily time range during which automated backups are created if automated backups are enabled, as determined by the BackupRetentionPeriod.
        public var preferredBackupWindow: Swift.String?
        /// Specifies the weekly time range during which system maintenance can occur, in Universal Coordinated Time (UTC).
        public var preferredMaintenanceWindow: Swift.String?
        /// A value that specifies the order in which an Amazon DocumentDB replica is promoted to the primary instance after a failure of the existing primary instance.
        public var promotionTier: Swift.Int?
        /// Not supported. Amazon DocumentDB does not currently support public endpoints. The value of PubliclyAccessible is always false.
        public var publiclyAccessible: Swift.Bool?
        /// The status of a read replica. If the instance is not a read replica, this is blank.
        public var statusInfos: [DocDBClientTypes.DBInstanceStatusInfo]?
        /// Specifies whether or not the instance is encrypted.
        public var storageEncrypted: Swift.Bool?
        /// Provides a list of VPC security group elements that the instance belongs to.
        public var vpcSecurityGroups: [DocDBClientTypes.VpcSecurityGroupMembership]?

        public init(
            autoMinorVersionUpgrade: Swift.Bool? = nil,
            availabilityZone: Swift.String? = nil,
            backupRetentionPeriod: Swift.Int? = nil,
            caCertificateIdentifier: Swift.String? = nil,
            certificateDetails: DocDBClientTypes.CertificateDetails? = nil,
            copyTagsToSnapshot: Swift.Bool? = nil,
            dbClusterIdentifier: Swift.String? = nil,
            dbInstanceArn: Swift.String? = nil,
            dbInstanceClass: Swift.String? = nil,
            dbInstanceIdentifier: Swift.String? = nil,
            dbInstanceStatus: Swift.String? = nil,
            dbSubnetGroup: DocDBClientTypes.DBSubnetGroup? = nil,
            dbiResourceId: Swift.String? = nil,
            enabledCloudwatchLogsExports: [Swift.String]? = nil,
            endpoint: DocDBClientTypes.Endpoint? = nil,
            engine: Swift.String? = nil,
            engineVersion: Swift.String? = nil,
            instanceCreateTime: Foundation.Date? = nil,
            kmsKeyId: Swift.String? = nil,
            latestRestorableTime: Foundation.Date? = nil,
            pendingModifiedValues: DocDBClientTypes.PendingModifiedValues? = nil,
            performanceInsightsEnabled: Swift.Bool? = nil,
            performanceInsightsKMSKeyId: Swift.String? = nil,
            preferredBackupWindow: Swift.String? = nil,
            preferredMaintenanceWindow: Swift.String? = nil,
            promotionTier: Swift.Int? = nil,
            publiclyAccessible: Swift.Bool? = nil,
            statusInfos: [DocDBClientTypes.DBInstanceStatusInfo]? = nil,
            storageEncrypted: Swift.Bool? = nil,
            vpcSecurityGroups: [DocDBClientTypes.VpcSecurityGroupMembership]? = nil
        )
        {
            self.autoMinorVersionUpgrade = autoMinorVersionUpgrade
            self.availabilityZone = availabilityZone
            self.backupRetentionPeriod = backupRetentionPeriod
            self.caCertificateIdentifier = caCertificateIdentifier
            self.certificateDetails = certificateDetails
            self.copyTagsToSnapshot = copyTagsToSnapshot
            self.dbClusterIdentifier = dbClusterIdentifier
            self.dbInstanceArn = dbInstanceArn
            self.dbInstanceClass = dbInstanceClass
            self.dbInstanceIdentifier = dbInstanceIdentifier
            self.dbInstanceStatus = dbInstanceStatus
            self.dbSubnetGroup = dbSubnetGroup
            self.dbiResourceId = dbiResourceId
            self.enabledCloudwatchLogsExports = enabledCloudwatchLogsExports
            self.endpoint = endpoint
            self.engine = engine
            self.engineVersion = engineVersion
            self.instanceCreateTime = instanceCreateTime
            self.kmsKeyId = kmsKeyId
            self.latestRestorableTime = latestRestorableTime
            self.pendingModifiedValues = pendingModifiedValues
            self.performanceInsightsEnabled = performanceInsightsEnabled
            self.performanceInsightsKMSKeyId = performanceInsightsKMSKeyId
            self.preferredBackupWindow = preferredBackupWindow
            self.preferredMaintenanceWindow = preferredMaintenanceWindow
            self.promotionTier = promotionTier
            self.publiclyAccessible = publiclyAccessible
            self.statusInfos = statusInfos
            self.storageEncrypted = storageEncrypted
            self.vpcSecurityGroups = vpcSecurityGroups
        }
    }
}

public struct CreateDBInstanceOutput: Swift.Sendable {
    /// Detailed information about an instance.
    public var dbInstance: DocDBClientTypes.DBInstance?

    public init(
        dbInstance: DocDBClientTypes.DBInstance? = nil
    )
    {
        self.dbInstance = dbInstance
    }
}

/// DBSubnetGroupName is already being used by an existing subnet group.
public struct DBSubnetGroupAlreadyExistsFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBSubnetGroupAlreadyExists" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request would cause you to exceed the allowed number of subnet groups.
public struct DBSubnetGroupQuotaExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBSubnetGroupQuotaExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request would cause you to exceed the allowed number of subnets in a subnet group.
public struct DBSubnetQuotaExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBSubnetQuotaExceededFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [CreateDBSubnetGroup].
public struct CreateDBSubnetGroupInput: Swift.Sendable {
    /// The description for the subnet group.
    /// This member is required.
    public var dbSubnetGroupDescription: Swift.String?
    /// The name for the subnet group. This value is stored as a lowercase string. Constraints: Must contain no more than 255 letters, numbers, periods, underscores, spaces, or hyphens. Must not be default. Example: mySubnetgroup
    /// This member is required.
    public var dbSubnetGroupName: Swift.String?
    /// The Amazon EC2 subnet IDs for the subnet group.
    /// This member is required.
    public var subnetIds: [Swift.String]?
    /// The tags to be assigned to the subnet group.
    public var tags: [DocDBClientTypes.Tag]?

    public init(
        dbSubnetGroupDescription: Swift.String? = nil,
        dbSubnetGroupName: Swift.String? = nil,
        subnetIds: [Swift.String]? = nil,
        tags: [DocDBClientTypes.Tag]? = nil
    )
    {
        self.dbSubnetGroupDescription = dbSubnetGroupDescription
        self.dbSubnetGroupName = dbSubnetGroupName
        self.subnetIds = subnetIds
        self.tags = tags
    }
}

public struct CreateDBSubnetGroupOutput: Swift.Sendable {
    /// Detailed information about a subnet group.
    public var dbSubnetGroup: DocDBClientTypes.DBSubnetGroup?

    public init(
        dbSubnetGroup: DocDBClientTypes.DBSubnetGroup? = nil
    )
    {
        self.dbSubnetGroup = dbSubnetGroup
    }
}

/// You have reached the maximum number of event subscriptions.
public struct EventSubscriptionQuotaExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "EventSubscriptionQuotaExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Amazon SNS has responded that there is a problem with the specified topic.
public struct SNSInvalidTopicFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SNSInvalidTopic" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// You do not have permission to publish to the SNS topic Amazon Resource Name (ARN).
public struct SNSNoAuthorizationFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SNSNoAuthorization" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The SNS topic Amazon Resource Name (ARN) does not exist.
public struct SNSTopicArnNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SNSTopicArnNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The provided subscription name already exists.
public struct SubscriptionAlreadyExistFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SubscriptionAlreadyExist" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The provided category does not exist.
public struct SubscriptionCategoryNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SubscriptionCategoryNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [CreateEventSubscription].
public struct CreateEventSubscriptionInput: Swift.Sendable {
    /// A Boolean value; set to true to activate the subscription, set to false to create the subscription but not active it.
    public var enabled: Swift.Bool?
    /// A list of event categories for a SourceType that you want to subscribe to.
    public var eventCategories: [Swift.String]?
    /// The Amazon Resource Name (ARN) of the SNS topic created for event notification. Amazon SNS creates the ARN when you create a topic and subscribe to it.
    /// This member is required.
    public var snsTopicArn: Swift.String?
    /// The list of identifiers of the event sources for which events are returned. If not specified, then all sources are included in the response. An identifier must begin with a letter and must contain only ASCII letters, digits, and hyphens; it can't end with a hyphen or contain two consecutive hyphens. Constraints:
    ///
    /// * If SourceIds are provided, SourceType must also be provided.
    ///
    /// * If the source type is an instance, a DBInstanceIdentifier must be provided.
    ///
    /// * If the source type is a security group, a DBSecurityGroupName must be provided.
    ///
    /// * If the source type is a parameter group, a DBParameterGroupName must be provided.
    ///
    /// * If the source type is a snapshot, a DBSnapshotIdentifier must be provided.
    public var sourceIds: [Swift.String]?
    /// The type of source that is generating the events. For example, if you want to be notified of events generated by an instance, you would set this parameter to db-instance. If this value is not specified, all events are returned. Valid values: db-instance, db-cluster, db-parameter-group, db-security-group, db-cluster-snapshot
    public var sourceType: Swift.String?
    /// The name of the subscription. Constraints: The name must be fewer than 255 characters.
    /// This member is required.
    public var subscriptionName: Swift.String?
    /// The tags to be assigned to the event subscription.
    public var tags: [DocDBClientTypes.Tag]?

    public init(
        enabled: Swift.Bool? = nil,
        eventCategories: [Swift.String]? = nil,
        snsTopicArn: Swift.String? = nil,
        sourceIds: [Swift.String]? = nil,
        sourceType: Swift.String? = nil,
        subscriptionName: Swift.String? = nil,
        tags: [DocDBClientTypes.Tag]? = nil
    )
    {
        self.enabled = enabled
        self.eventCategories = eventCategories
        self.snsTopicArn = snsTopicArn
        self.sourceIds = sourceIds
        self.sourceType = sourceType
        self.subscriptionName = subscriptionName
        self.tags = tags
    }
}

public struct CreateEventSubscriptionOutput: Swift.Sendable {
    /// Detailed information about an event to which you have subscribed.
    public var eventSubscription: DocDBClientTypes.EventSubscription?

    public init(
        eventSubscription: DocDBClientTypes.EventSubscription? = nil
    )
    {
        self.eventSubscription = eventSubscription
    }
}

/// The GlobalClusterIdentifier already exists. Choose a new global cluster identifier (unique name) to create a new global cluster.
public struct GlobalClusterAlreadyExistsFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "GlobalClusterAlreadyExistsFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The number of global clusters for this account is already at the maximum allowed.
public struct GlobalClusterQuotaExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "GlobalClusterQuotaExceededFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [CreateGlobalCluster].
public struct CreateGlobalClusterInput: Swift.Sendable {
    /// The name for your database of up to 64 alpha-numeric characters. If you do not provide a name, Amazon DocumentDB will not create a database in the global cluster you are creating.
    public var databaseName: Swift.String?
    /// The deletion protection setting for the new global cluster. The global cluster can't be deleted when deletion protection is enabled.
    public var deletionProtection: Swift.Bool?
    /// The name of the database engine to be used for this cluster.
    public var engine: Swift.String?
    /// The engine version of the global cluster.
    public var engineVersion: Swift.String?
    /// The cluster identifier of the new global cluster.
    /// This member is required.
    public var globalClusterIdentifier: Swift.String?
    /// The Amazon Resource Name (ARN) to use as the primary cluster of the global cluster. This parameter is optional.
    public var sourceDBClusterIdentifier: Swift.String?
    /// The storage encryption setting for the new global cluster.
    public var storageEncrypted: Swift.Bool?

    public init(
        databaseName: Swift.String? = nil,
        deletionProtection: Swift.Bool? = nil,
        engine: Swift.String? = nil,
        engineVersion: Swift.String? = nil,
        globalClusterIdentifier: Swift.String? = nil,
        sourceDBClusterIdentifier: Swift.String? = nil,
        storageEncrypted: Swift.Bool? = nil
    )
    {
        self.databaseName = databaseName
        self.deletionProtection = deletionProtection
        self.engine = engine
        self.engineVersion = engineVersion
        self.globalClusterIdentifier = globalClusterIdentifier
        self.sourceDBClusterIdentifier = sourceDBClusterIdentifier
        self.storageEncrypted = storageEncrypted
    }
}

extension DocDBClientTypes {

    /// A data structure with information about any primary and secondary clusters associated with an Amazon DocumentDB global clusters.
    public struct GlobalClusterMember: Swift.Sendable {
        /// The Amazon Resource Name (ARN) for each Amazon DocumentDB cluster.
        public var dbClusterArn: Swift.String?
        /// Specifies whether the Amazon DocumentDB cluster is the primary cluster (that is, has read-write capability) for the Amazon DocumentDB global cluster with which it is associated.
        public var isWriter: Swift.Bool?
        /// The Amazon Resource Name (ARN) for each read-only secondary cluster associated with the Aurora global cluster.
        public var readers: [Swift.String]?

        public init(
            dbClusterArn: Swift.String? = nil,
            isWriter: Swift.Bool? = nil,
            readers: [Swift.String]? = nil
        )
        {
            self.dbClusterArn = dbClusterArn
            self.isWriter = isWriter
            self.readers = readers
        }
    }
}

extension DocDBClientTypes {

    /// A data type representing an Amazon DocumentDB global cluster.
    public struct GlobalCluster: Swift.Sendable {
        /// The default database name within the new global cluster.
        public var databaseName: Swift.String?
        /// The deletion protection setting for the new global cluster.
        public var deletionProtection: Swift.Bool?
        /// The Amazon DocumentDB database engine used by the global cluster.
        public var engine: Swift.String?
        /// Indicates the database engine version.
        public var engineVersion: Swift.String?
        /// The Amazon Resource Name (ARN) for the global cluster.
        public var globalClusterArn: Swift.String?
        /// Contains a user-supplied global cluster identifier. This identifier is the unique key that identifies a global cluster.
        public var globalClusterIdentifier: Swift.String?
        /// The list of cluster IDs for secondary clusters within the global cluster. Currently limited to one item.
        public var globalClusterMembers: [DocDBClientTypes.GlobalClusterMember]?
        /// The Amazon Web Services Region-unique, immutable identifier for the global database cluster. This identifier is found in CloudTrail log entries whenever the KMS customer master key (CMK) for the cluster is accessed.
        public var globalClusterResourceId: Swift.String?
        /// Specifies the current state of this global cluster.
        public var status: Swift.String?
        /// The storage encryption setting for the global cluster.
        public var storageEncrypted: Swift.Bool?

        public init(
            databaseName: Swift.String? = nil,
            deletionProtection: Swift.Bool? = nil,
            engine: Swift.String? = nil,
            engineVersion: Swift.String? = nil,
            globalClusterArn: Swift.String? = nil,
            globalClusterIdentifier: Swift.String? = nil,
            globalClusterMembers: [DocDBClientTypes.GlobalClusterMember]? = nil,
            globalClusterResourceId: Swift.String? = nil,
            status: Swift.String? = nil,
            storageEncrypted: Swift.Bool? = nil
        )
        {
            self.databaseName = databaseName
            self.deletionProtection = deletionProtection
            self.engine = engine
            self.engineVersion = engineVersion
            self.globalClusterArn = globalClusterArn
            self.globalClusterIdentifier = globalClusterIdentifier
            self.globalClusterMembers = globalClusterMembers
            self.globalClusterResourceId = globalClusterResourceId
            self.status = status
            self.storageEncrypted = storageEncrypted
        }
    }
}

public struct CreateGlobalClusterOutput: Swift.Sendable {
    /// A data type representing an Amazon DocumentDB global cluster.
    public var globalCluster: DocDBClientTypes.GlobalCluster?

    public init(
        globalCluster: DocDBClientTypes.GlobalCluster? = nil
    )
    {
        self.globalCluster = globalCluster
    }
}

/// Represents the input to [DeleteDBCluster].
public struct DeleteDBClusterInput: Swift.Sendable {
    /// The cluster identifier for the cluster to be deleted. This parameter isn't case sensitive. Constraints:
    ///
    /// * Must match an existing DBClusterIdentifier.
    /// This member is required.
    public var dbClusterIdentifier: Swift.String?
    /// The cluster snapshot identifier of the new cluster snapshot created when SkipFinalSnapshot is set to false. Specifying this parameter and also setting the SkipFinalShapshot parameter to true results in an error. Constraints:
    ///
    /// * Must be from 1 to 255 letters, numbers, or hyphens.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    public var finalDBSnapshotIdentifier: Swift.String?
    /// Determines whether a final cluster snapshot is created before the cluster is deleted. If true is specified, no cluster snapshot is created. If false is specified, a cluster snapshot is created before the DB cluster is deleted. If SkipFinalSnapshot is false, you must specify a FinalDBSnapshotIdentifier parameter. Default: false
    public var skipFinalSnapshot: Swift.Bool?

    public init(
        dbClusterIdentifier: Swift.String? = nil,
        finalDBSnapshotIdentifier: Swift.String? = nil,
        skipFinalSnapshot: Swift.Bool? = nil
    )
    {
        self.dbClusterIdentifier = dbClusterIdentifier
        self.finalDBSnapshotIdentifier = finalDBSnapshotIdentifier
        self.skipFinalSnapshot = skipFinalSnapshot
    }
}

public struct DeleteDBClusterOutput: Swift.Sendable {
    /// Detailed information about a cluster.
    public var dbCluster: DocDBClientTypes.DBCluster?

    public init(
        dbCluster: DocDBClientTypes.DBCluster? = nil
    )
    {
        self.dbCluster = dbCluster
    }
}

/// The parameter group is in use, or it is in a state that is not valid. If you are trying to delete the parameter group, you can't delete it when the parameter group is in this state.
public struct InvalidDBParameterGroupStateFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidDBParameterGroupState" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [DeleteDBClusterParameterGroup].
public struct DeleteDBClusterParameterGroupInput: Swift.Sendable {
    /// The name of the cluster parameter group. Constraints:
    ///
    /// * Must be the name of an existing cluster parameter group.
    ///
    /// * You can't delete a default cluster parameter group.
    ///
    /// * Cannot be associated with any clusters.
    /// This member is required.
    public var dbClusterParameterGroupName: Swift.String?

    public init(
        dbClusterParameterGroupName: Swift.String? = nil
    )
    {
        self.dbClusterParameterGroupName = dbClusterParameterGroupName
    }
}

/// Represents the input to [DeleteDBClusterSnapshot].
public struct DeleteDBClusterSnapshotInput: Swift.Sendable {
    /// The identifier of the cluster snapshot to delete. Constraints: Must be the name of an existing cluster snapshot in the available state.
    /// This member is required.
    public var dbClusterSnapshotIdentifier: Swift.String?

    public init(
        dbClusterSnapshotIdentifier: Swift.String? = nil
    )
    {
        self.dbClusterSnapshotIdentifier = dbClusterSnapshotIdentifier
    }
}

public struct DeleteDBClusterSnapshotOutput: Swift.Sendable {
    /// Detailed information about a cluster snapshot.
    public var dbClusterSnapshot: DocDBClientTypes.DBClusterSnapshot?

    public init(
        dbClusterSnapshot: DocDBClientTypes.DBClusterSnapshot? = nil
    )
    {
        self.dbClusterSnapshot = dbClusterSnapshot
    }
}

/// DBSnapshotIdentifier is already being used by an existing snapshot.
public struct DBSnapshotAlreadyExistsFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBSnapshotAlreadyExists" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [DeleteDBInstance].
public struct DeleteDBInstanceInput: Swift.Sendable {
    /// The instance identifier for the instance to be deleted. This parameter isn't case sensitive. Constraints:
    ///
    /// * Must match the name of an existing instance.
    /// This member is required.
    public var dbInstanceIdentifier: Swift.String?

    public init(
        dbInstanceIdentifier: Swift.String? = nil
    )
    {
        self.dbInstanceIdentifier = dbInstanceIdentifier
    }
}

public struct DeleteDBInstanceOutput: Swift.Sendable {
    /// Detailed information about an instance.
    public var dbInstance: DocDBClientTypes.DBInstance?

    public init(
        dbInstance: DocDBClientTypes.DBInstance? = nil
    )
    {
        self.dbInstance = dbInstance
    }
}

/// The subnet isn't in the available state.
public struct InvalidDBSubnetStateFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidDBSubnetStateFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [DeleteDBSubnetGroup].
public struct DeleteDBSubnetGroupInput: Swift.Sendable {
    /// The name of the database subnet group to delete. You can't delete the default subnet group. Constraints: Must match the name of an existing DBSubnetGroup. Must not be default. Example: mySubnetgroup
    /// This member is required.
    public var dbSubnetGroupName: Swift.String?

    public init(
        dbSubnetGroupName: Swift.String? = nil
    )
    {
        self.dbSubnetGroupName = dbSubnetGroupName
    }
}

/// Someone else might be modifying a subscription. Wait a few seconds, and try again.
public struct InvalidEventSubscriptionStateFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidEventSubscriptionState" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [DeleteEventSubscription].
public struct DeleteEventSubscriptionInput: Swift.Sendable {
    /// The name of the Amazon DocumentDB event notification subscription that you want to delete.
    /// This member is required.
    public var subscriptionName: Swift.String?

    public init(
        subscriptionName: Swift.String? = nil
    )
    {
        self.subscriptionName = subscriptionName
    }
}

public struct DeleteEventSubscriptionOutput: Swift.Sendable {
    /// Detailed information about an event to which you have subscribed.
    public var eventSubscription: DocDBClientTypes.EventSubscription?

    public init(
        eventSubscription: DocDBClientTypes.EventSubscription? = nil
    )
    {
        self.eventSubscription = eventSubscription
    }
}

/// Represents the input to [DeleteGlobalCluster].
public struct DeleteGlobalClusterInput: Swift.Sendable {
    /// The cluster identifier of the global cluster being deleted.
    /// This member is required.
    public var globalClusterIdentifier: Swift.String?

    public init(
        globalClusterIdentifier: Swift.String? = nil
    )
    {
        self.globalClusterIdentifier = globalClusterIdentifier
    }
}

public struct DeleteGlobalClusterOutput: Swift.Sendable {
    /// A data type representing an Amazon DocumentDB global cluster.
    public var globalCluster: DocDBClientTypes.GlobalCluster?

    public init(
        globalCluster: DocDBClientTypes.GlobalCluster? = nil
    )
    {
        self.globalCluster = globalCluster
    }
}

/// CertificateIdentifier doesn't refer to an existing certificate.
public struct CertificateNotFoundFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "CertificateNotFound" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension DocDBClientTypes {

    /// A named set of filter values, used to return a more specific list of results. You can use a filter to match a set of resources by specific criteria, such as IDs. Wildcards are not supported in filters.
    public struct Filter: Swift.Sendable {
        /// The name of the filter. Filter names are case sensitive.
        /// This member is required.
        public var name: Swift.String?
        /// One or more filter values. Filter values are case sensitive.
        /// This member is required.
        public var values: [Swift.String]?

        public init(
            name: Swift.String? = nil,
            values: [Swift.String]? = nil
        )
        {
            self.name = name
            self.values = values
        }
    }
}

public struct DescribeCertificatesInput: Swift.Sendable {
    /// The user-supplied certificate identifier. If this parameter is specified, information for only the specified certificate is returned. If this parameter is omitted, a list of up to MaxRecords certificates is returned. This parameter is not case sensitive. Constraints
    ///
    /// * Must match an existing CertificateIdentifier.
    public var certificateIdentifier: Swift.String?
    /// This parameter is not currently supported.
    public var filters: [DocDBClientTypes.Filter]?
    /// An optional pagination token provided by a previous DescribeCertificates request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token called a marker is included in the response so that the remaining results can be retrieved. Default: 100 Constraints:
    ///
    /// * Minimum: 20
    ///
    /// * Maximum: 100
    public var maxRecords: Swift.Int?

    public init(
        certificateIdentifier: Swift.String? = nil,
        filters: [DocDBClientTypes.Filter]? = nil,
        marker: Swift.String? = nil,
        maxRecords: Swift.Int? = nil
    )
    {
        self.certificateIdentifier = certificateIdentifier
        self.filters = filters
        self.marker = marker
        self.maxRecords = maxRecords
    }
}

extension DocDBClientTypes {

    /// A certificate authority (CA) certificate for an Amazon Web Services account.
    public struct Certificate: Swift.Sendable {
        /// The Amazon Resource Name (ARN) for the certificate. Example: arn:aws:rds:us-east-1::cert:rds-ca-2019
        public var certificateArn: Swift.String?
        /// The unique key that identifies a certificate. Example: rds-ca-2019
        public var certificateIdentifier: Swift.String?
        /// The type of the certificate. Example: CA
        public var certificateType: Swift.String?
        /// The thumbprint of the certificate.
        public var thumbprint: Swift.String?
        /// The starting date-time from which the certificate is valid. Example: 2019-07-31T17:57:09Z
        public var validFrom: Foundation.Date?
        /// The date-time after which the certificate is no longer valid. Example: 2024-07-31T17:57:09Z
        public var validTill: Foundation.Date?

        public init(
            certificateArn: Swift.String? = nil,
            certificateIdentifier: Swift.String? = nil,
            certificateType: Swift.String? = nil,
            thumbprint: Swift.String? = nil,
            validFrom: Foundation.Date? = nil,
            validTill: Foundation.Date? = nil
        )
        {
            self.certificateArn = certificateArn
            self.certificateIdentifier = certificateIdentifier
            self.certificateType = certificateType
            self.thumbprint = thumbprint
            self.validFrom = validFrom
            self.validTill = validTill
        }
    }
}

public struct DescribeCertificatesOutput: Swift.Sendable {
    /// A list of certificates for this Amazon Web Services account.
    public var certificates: [DocDBClientTypes.Certificate]?
    /// An optional pagination token provided if the number of records retrieved is greater than MaxRecords. If this parameter is specified, the marker specifies the next record in the list. Including the value of Marker in the next call to DescribeCertificates results in the next page of certificates.
    public var marker: Swift.String?

    public init(
        certificates: [DocDBClientTypes.Certificate]? = nil,
        marker: Swift.String? = nil
    )
    {
        self.certificates = certificates
        self.marker = marker
    }
}

/// Represents the input to [DescribeDBClusterParameterGroups].
public struct DescribeDBClusterParameterGroupsInput: Swift.Sendable {
    /// The name of a specific cluster parameter group to return details for. Constraints:
    ///
    /// * If provided, must match the name of an existing DBClusterParameterGroup.
    public var dbClusterParameterGroupName: Swift.String?
    /// This parameter is not currently supported.
    public var filters: [DocDBClientTypes.Filter]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token (marker) is included in the response so that the remaining results can be retrieved. Default: 100 Constraints: Minimum 20, maximum 100.
    public var maxRecords: Swift.Int?

    public init(
        dbClusterParameterGroupName: Swift.String? = nil,
        filters: [DocDBClientTypes.Filter]? = nil,
        marker: Swift.String? = nil,
        maxRecords: Swift.Int? = nil
    )
    {
        self.dbClusterParameterGroupName = dbClusterParameterGroupName
        self.filters = filters
        self.marker = marker
        self.maxRecords = maxRecords
    }
}

/// Represents the output of [DBClusterParameterGroups].
public struct DescribeDBClusterParameterGroupsOutput: Swift.Sendable {
    /// A list of cluster parameter groups.
    public var dbClusterParameterGroups: [DocDBClientTypes.DBClusterParameterGroup]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?

    public init(
        dbClusterParameterGroups: [DocDBClientTypes.DBClusterParameterGroup]? = nil,
        marker: Swift.String? = nil
    )
    {
        self.dbClusterParameterGroups = dbClusterParameterGroups
        self.marker = marker
    }
}

/// Represents the input to [DescribeDBClusterParameters].
public struct DescribeDBClusterParametersInput: Swift.Sendable {
    /// The name of a specific cluster parameter group to return parameter details for. Constraints:
    ///
    /// * If provided, must match the name of an existing DBClusterParameterGroup.
    /// This member is required.
    public var dbClusterParameterGroupName: Swift.String?
    /// This parameter is not currently supported.
    public var filters: [DocDBClientTypes.Filter]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token (marker) is included in the response so that the remaining results can be retrieved. Default: 100 Constraints: Minimum 20, maximum 100.
    public var maxRecords: Swift.Int?
    /// A value that indicates to return only parameters for a specific source. Parameter sources can be engine, service, or customer.
    public var source: Swift.String?

    public init(
        dbClusterParameterGroupName: Swift.String? = nil,
        filters: [DocDBClientTypes.Filter]? = nil,
        marker: Swift.String? = nil,
        maxRecords: Swift.Int? = nil,
        source: Swift.String? = nil
    )
    {
        self.dbClusterParameterGroupName = dbClusterParameterGroupName
        self.filters = filters
        self.marker = marker
        self.maxRecords = maxRecords
        self.source = source
    }
}

extension DocDBClientTypes {

    public enum ApplyMethod: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case immediate
        case pendingReboot
        case sdkUnknown(Swift.String)

        public static var allCases: [ApplyMethod] {
            return [
                .immediate,
                .pendingReboot
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .immediate: return "immediate"
            case .pendingReboot: return "pending-reboot"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DocDBClientTypes {

    /// Detailed information about an individual parameter.
    public struct Parameter: Swift.Sendable {
        /// Specifies the valid range of values for the parameter.
        public var allowedValues: Swift.String?
        /// Indicates when to apply parameter updates.
        public var applyMethod: DocDBClientTypes.ApplyMethod?
        /// Specifies the engine-specific parameters type.
        public var applyType: Swift.String?
        /// Specifies the valid data type for the parameter.
        public var dataType: Swift.String?
        /// Provides a description of the parameter.
        public var description: Swift.String?
        /// Indicates whether (true) or not (false) the parameter can be modified. Some parameters have security or operational implications that prevent them from being changed.
        public var isModifiable: Swift.Bool?
        /// The earliest engine version to which the parameter can apply.
        public var minimumEngineVersion: Swift.String?
        /// Specifies the name of the parameter.
        public var parameterName: Swift.String?
        /// Specifies the value of the parameter.
        public var parameterValue: Swift.String?
        /// Indicates the source of the parameter value.
        public var source: Swift.String?

        public init(
            allowedValues: Swift.String? = nil,
            applyMethod: DocDBClientTypes.ApplyMethod? = nil,
            applyType: Swift.String? = nil,
            dataType: Swift.String? = nil,
            description: Swift.String? = nil,
            isModifiable: Swift.Bool? = nil,
            minimumEngineVersion: Swift.String? = nil,
            parameterName: Swift.String? = nil,
            parameterValue: Swift.String? = nil,
            source: Swift.String? = nil
        )
        {
            self.allowedValues = allowedValues
            self.applyMethod = applyMethod
            self.applyType = applyType
            self.dataType = dataType
            self.description = description
            self.isModifiable = isModifiable
            self.minimumEngineVersion = minimumEngineVersion
            self.parameterName = parameterName
            self.parameterValue = parameterValue
            self.source = source
        }
    }
}

/// Represents the output of [DBClusterParameterGroup].
public struct DescribeDBClusterParametersOutput: Swift.Sendable {
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// Provides a list of parameters for the cluster parameter group.
    public var parameters: [DocDBClientTypes.Parameter]?

    public init(
        marker: Swift.String? = nil,
        parameters: [DocDBClientTypes.Parameter]? = nil
    )
    {
        self.marker = marker
        self.parameters = parameters
    }
}

/// Represents the input to [DescribeDBClusters].
public struct DescribeDBClustersInput: Swift.Sendable {
    /// The user-provided cluster identifier. If this parameter is specified, information from only the specific cluster is returned. This parameter isn't case sensitive. Constraints:
    ///
    /// * If provided, must match an existing DBClusterIdentifier.
    public var dbClusterIdentifier: Swift.String?
    /// A filter that specifies one or more clusters to describe. Supported filters:
    ///
    /// * db-cluster-id - Accepts cluster identifiers and cluster Amazon Resource Names (ARNs). The results list only includes information about the clusters identified by these ARNs.
    public var filters: [DocDBClientTypes.Filter]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token (marker) is included in the response so that the remaining results can be retrieved. Default: 100 Constraints: Minimum 20, maximum 100.
    public var maxRecords: Swift.Int?

    public init(
        dbClusterIdentifier: Swift.String? = nil,
        filters: [DocDBClientTypes.Filter]? = nil,
        marker: Swift.String? = nil,
        maxRecords: Swift.Int? = nil
    )
    {
        self.dbClusterIdentifier = dbClusterIdentifier
        self.filters = filters
        self.marker = marker
        self.maxRecords = maxRecords
    }
}

/// Represents the output of [DescribeDBClusters].
public struct DescribeDBClustersOutput: Swift.Sendable {
    /// A list of clusters.
    public var dbClusters: [DocDBClientTypes.DBCluster]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?

    public init(
        dbClusters: [DocDBClientTypes.DBCluster]? = nil,
        marker: Swift.String? = nil
    )
    {
        self.dbClusters = dbClusters
        self.marker = marker
    }
}

/// Represents the input to [DescribeDBClusterSnapshotAttributes].
public struct DescribeDBClusterSnapshotAttributesInput: Swift.Sendable {
    /// The identifier for the cluster snapshot to describe the attributes for.
    /// This member is required.
    public var dbClusterSnapshotIdentifier: Swift.String?

    public init(
        dbClusterSnapshotIdentifier: Swift.String? = nil
    )
    {
        self.dbClusterSnapshotIdentifier = dbClusterSnapshotIdentifier
    }
}

extension DocDBClientTypes {

    /// Contains the name and values of a manual cluster snapshot attribute. Manual cluster snapshot attributes are used to authorize other Amazon Web Services accounts to restore a manual cluster snapshot.
    public struct DBClusterSnapshotAttribute: Swift.Sendable {
        /// The name of the manual cluster snapshot attribute. The attribute named restore refers to the list of Amazon Web Services accounts that have permission to copy or restore the manual cluster snapshot.
        public var attributeName: Swift.String?
        /// The values for the manual cluster snapshot attribute. If the AttributeName field is set to restore, then this element returns a list of IDs of the Amazon Web Services accounts that are authorized to copy or restore the manual cluster snapshot. If a value of all is in the list, then the manual cluster snapshot is public and available for any Amazon Web Services account to copy or restore.
        public var attributeValues: [Swift.String]?

        public init(
            attributeName: Swift.String? = nil,
            attributeValues: [Swift.String]? = nil
        )
        {
            self.attributeName = attributeName
            self.attributeValues = attributeValues
        }
    }
}

extension DocDBClientTypes {

    /// Detailed information about the attributes that are associated with a cluster snapshot.
    public struct DBClusterSnapshotAttributesResult: Swift.Sendable {
        /// The list of attributes and values for the cluster snapshot.
        public var dbClusterSnapshotAttributes: [DocDBClientTypes.DBClusterSnapshotAttribute]?
        /// The identifier of the cluster snapshot that the attributes apply to.
        public var dbClusterSnapshotIdentifier: Swift.String?

        public init(
            dbClusterSnapshotAttributes: [DocDBClientTypes.DBClusterSnapshotAttribute]? = nil,
            dbClusterSnapshotIdentifier: Swift.String? = nil
        )
        {
            self.dbClusterSnapshotAttributes = dbClusterSnapshotAttributes
            self.dbClusterSnapshotIdentifier = dbClusterSnapshotIdentifier
        }
    }
}

public struct DescribeDBClusterSnapshotAttributesOutput: Swift.Sendable {
    /// Detailed information about the attributes that are associated with a cluster snapshot.
    public var dbClusterSnapshotAttributesResult: DocDBClientTypes.DBClusterSnapshotAttributesResult?

    public init(
        dbClusterSnapshotAttributesResult: DocDBClientTypes.DBClusterSnapshotAttributesResult? = nil
    )
    {
        self.dbClusterSnapshotAttributesResult = dbClusterSnapshotAttributesResult
    }
}

/// Represents the input to [DescribeDBClusterSnapshots].
public struct DescribeDBClusterSnapshotsInput: Swift.Sendable {
    /// The ID of the cluster to retrieve the list of cluster snapshots for. This parameter can't be used with the DBClusterSnapshotIdentifier parameter. This parameter is not case sensitive. Constraints:
    ///
    /// * If provided, must match the identifier of an existing DBCluster.
    public var dbClusterIdentifier: Swift.String?
    /// A specific cluster snapshot identifier to describe. This parameter can't be used with the DBClusterIdentifier parameter. This value is stored as a lowercase string. Constraints:
    ///
    /// * If provided, must match the identifier of an existing DBClusterSnapshot.
    ///
    /// * If this identifier is for an automated snapshot, the SnapshotType parameter must also be specified.
    public var dbClusterSnapshotIdentifier: Swift.String?
    /// This parameter is not currently supported.
    public var filters: [DocDBClientTypes.Filter]?
    /// Set to true to include manual cluster snapshots that are public and can be copied or restored by any Amazon Web Services account, and otherwise false. The default is false.
    public var includePublic: Swift.Bool?
    /// Set to true to include shared manual cluster snapshots from other Amazon Web Services accounts that this Amazon Web Services account has been given permission to copy or restore, and otherwise false. The default is false.
    public var includeShared: Swift.Bool?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token (marker) is included in the response so that the remaining results can be retrieved. Default: 100 Constraints: Minimum 20, maximum 100.
    public var maxRecords: Swift.Int?
    /// The type of cluster snapshots to be returned. You can specify one of the following values:
    ///
    /// * automated - Return all cluster snapshots that Amazon DocumentDB has automatically created for your Amazon Web Services account.
    ///
    /// * manual - Return all cluster snapshots that you have manually created for your Amazon Web Services account.
    ///
    /// * shared - Return all manual cluster snapshots that have been shared to your Amazon Web Services account.
    ///
    /// * public - Return all cluster snapshots that have been marked as public.
    ///
    ///
    /// If you don't specify a SnapshotType value, then both automated and manual cluster snapshots are returned. You can include shared cluster snapshots with these results by setting the IncludeShared parameter to true. You can include public cluster snapshots with these results by setting theIncludePublic parameter to true. The IncludeShared and IncludePublic parameters don't apply for SnapshotType values of manual or automated. The IncludePublic parameter doesn't apply when SnapshotType is set to shared. The IncludeShared parameter doesn't apply when SnapshotType is set to public.
    public var snapshotType: Swift.String?

    public init(
        dbClusterIdentifier: Swift.String? = nil,
        dbClusterSnapshotIdentifier: Swift.String? = nil,
        filters: [DocDBClientTypes.Filter]? = nil,
        includePublic: Swift.Bool? = nil,
        includeShared: Swift.Bool? = nil,
        marker: Swift.String? = nil,
        maxRecords: Swift.Int? = nil,
        snapshotType: Swift.String? = nil
    )
    {
        self.dbClusterIdentifier = dbClusterIdentifier
        self.dbClusterSnapshotIdentifier = dbClusterSnapshotIdentifier
        self.filters = filters
        self.includePublic = includePublic
        self.includeShared = includeShared
        self.marker = marker
        self.maxRecords = maxRecords
        self.snapshotType = snapshotType
    }
}

/// Represents the output of [DescribeDBClusterSnapshots].
public struct DescribeDBClusterSnapshotsOutput: Swift.Sendable {
    /// Provides a list of cluster snapshots.
    public var dbClusterSnapshots: [DocDBClientTypes.DBClusterSnapshot]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?

    public init(
        dbClusterSnapshots: [DocDBClientTypes.DBClusterSnapshot]? = nil,
        marker: Swift.String? = nil
    )
    {
        self.dbClusterSnapshots = dbClusterSnapshots
        self.marker = marker
    }
}

/// Represents the input to [DescribeDBEngineVersions].
public struct DescribeDBEngineVersionsInput: Swift.Sendable {
    /// The name of a specific parameter group family to return details for. Constraints:
    ///
    /// * If provided, must match an existing DBParameterGroupFamily.
    public var dbParameterGroupFamily: Swift.String?
    /// Indicates that only the default version of the specified engine or engine and major version combination is returned.
    public var defaultOnly: Swift.Bool?
    /// The database engine to return.
    public var engine: Swift.String?
    /// The database engine version to return. Example: 3.6.0
    public var engineVersion: Swift.String?
    /// This parameter is not currently supported.
    public var filters: [DocDBClientTypes.Filter]?
    /// If this parameter is specified and the requested engine supports the CharacterSetName parameter for CreateDBInstance, the response includes a list of supported character sets for each engine version.
    public var listSupportedCharacterSets: Swift.Bool?
    /// If this parameter is specified and the requested engine supports the TimeZone parameter for CreateDBInstance, the response includes a list of supported time zones for each engine version.
    public var listSupportedTimezones: Swift.Bool?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token (marker) is included in the response so that the remaining results can be retrieved. Default: 100 Constraints: Minimum 20, maximum 100.
    public var maxRecords: Swift.Int?

    public init(
        dbParameterGroupFamily: Swift.String? = nil,
        defaultOnly: Swift.Bool? = nil,
        engine: Swift.String? = nil,
        engineVersion: Swift.String? = nil,
        filters: [DocDBClientTypes.Filter]? = nil,
        listSupportedCharacterSets: Swift.Bool? = nil,
        listSupportedTimezones: Swift.Bool? = nil,
        marker: Swift.String? = nil,
        maxRecords: Swift.Int? = nil
    )
    {
        self.dbParameterGroupFamily = dbParameterGroupFamily
        self.defaultOnly = defaultOnly
        self.engine = engine
        self.engineVersion = engineVersion
        self.filters = filters
        self.listSupportedCharacterSets = listSupportedCharacterSets
        self.listSupportedTimezones = listSupportedTimezones
        self.marker = marker
        self.maxRecords = maxRecords
    }
}

extension DocDBClientTypes {

    /// The version of the database engine that an instance can be upgraded to.
    public struct UpgradeTarget: Swift.Sendable {
        /// A value that indicates whether the target version is applied to any source DB instances that have AutoMinorVersionUpgrade set to true.
        public var autoUpgrade: Swift.Bool?
        /// The version of the database engine that an instance can be upgraded to.
        public var description: Swift.String?
        /// The name of the upgrade target database engine.
        public var engine: Swift.String?
        /// The version number of the upgrade target database engine.
        public var engineVersion: Swift.String?
        /// A value that indicates whether a database engine is upgraded to a major version.
        public var isMajorVersionUpgrade: Swift.Bool?

        public init(
            autoUpgrade: Swift.Bool? = nil,
            description: Swift.String? = nil,
            engine: Swift.String? = nil,
            engineVersion: Swift.String? = nil,
            isMajorVersionUpgrade: Swift.Bool? = nil
        )
        {
            self.autoUpgrade = autoUpgrade
            self.description = description
            self.engine = engine
            self.engineVersion = engineVersion
            self.isMajorVersionUpgrade = isMajorVersionUpgrade
        }
    }
}

extension DocDBClientTypes {

    /// Detailed information about an engine version.
    public struct DBEngineVersion: Swift.Sendable {
        /// The description of the database engine.
        public var dbEngineDescription: Swift.String?
        /// The description of the database engine version.
        public var dbEngineVersionDescription: Swift.String?
        /// The name of the parameter group family for the database engine.
        public var dbParameterGroupFamily: Swift.String?
        /// The name of the database engine.
        public var engine: Swift.String?
        /// The version number of the database engine.
        public var engineVersion: Swift.String?
        /// The types of logs that the database engine has available for export to Amazon CloudWatch Logs.
        public var exportableLogTypes: [Swift.String]?
        /// A list of the supported CA certificate identifiers. For more information, see [Updating Your Amazon DocumentDB TLS Certificates](https://docs.aws.amazon.com/documentdb/latest/developerguide/ca_cert_rotation.html) and [ Encrypting Data in Transit](https://docs.aws.amazon.com/documentdb/latest/developerguide/security.encryption.ssl.html) in the Amazon DocumentDB Developer Guide.
        public var supportedCACertificateIdentifiers: [Swift.String]?
        /// Indicates whether the engine version supports rotating the server certificate without rebooting the DB instance.
        public var supportsCertificateRotationWithoutRestart: Swift.Bool?
        /// A value that indicates whether the engine version supports exporting the log types specified by ExportableLogTypes to CloudWatch Logs.
        public var supportsLogExportsToCloudwatchLogs: Swift.Bool?
        /// A list of engine versions that this database engine version can be upgraded to.
        public var validUpgradeTarget: [DocDBClientTypes.UpgradeTarget]?

        public init(
            dbEngineDescription: Swift.String? = nil,
            dbEngineVersionDescription: Swift.String? = nil,
            dbParameterGroupFamily: Swift.String? = nil,
            engine: Swift.String? = nil,
            engineVersion: Swift.String? = nil,
            exportableLogTypes: [Swift.String]? = nil,
            supportedCACertificateIdentifiers: [Swift.String]? = nil,
            supportsCertificateRotationWithoutRestart: Swift.Bool? = nil,
            supportsLogExportsToCloudwatchLogs: Swift.Bool? = nil,
            validUpgradeTarget: [DocDBClientTypes.UpgradeTarget]? = nil
        )
        {
            self.dbEngineDescription = dbEngineDescription
            self.dbEngineVersionDescription = dbEngineVersionDescription
            self.dbParameterGroupFamily = dbParameterGroupFamily
            self.engine = engine
            self.engineVersion = engineVersion
            self.exportableLogTypes = exportableLogTypes
            self.supportedCACertificateIdentifiers = supportedCACertificateIdentifiers
            self.supportsCertificateRotationWithoutRestart = supportsCertificateRotationWithoutRestart
            self.supportsLogExportsToCloudwatchLogs = supportsLogExportsToCloudwatchLogs
            self.validUpgradeTarget = validUpgradeTarget
        }
    }
}

/// Represents the output of [DescribeDBEngineVersions].
public struct DescribeDBEngineVersionsOutput: Swift.Sendable {
    /// Detailed information about one or more engine versions.
    public var dbEngineVersions: [DocDBClientTypes.DBEngineVersion]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?

    public init(
        dbEngineVersions: [DocDBClientTypes.DBEngineVersion]? = nil,
        marker: Swift.String? = nil
    )
    {
        self.dbEngineVersions = dbEngineVersions
        self.marker = marker
    }
}

/// Represents the input to [DescribeDBInstances].
public struct DescribeDBInstancesInput: Swift.Sendable {
    /// The user-provided instance identifier. If this parameter is specified, information from only the specific instance is returned. This parameter isn't case sensitive. Constraints:
    ///
    /// * If provided, must match the identifier of an existing DBInstance.
    public var dbInstanceIdentifier: Swift.String?
    /// A filter that specifies one or more instances to describe. Supported filters:
    ///
    /// * db-cluster-id - Accepts cluster identifiers and cluster Amazon Resource Names (ARNs). The results list includes only the information about the instances that are associated with the clusters that are identified by these ARNs.
    ///
    /// * db-instance-id - Accepts instance identifiers and instance ARNs. The results list includes only the information about the instances that are identified by these ARNs.
    public var filters: [DocDBClientTypes.Filter]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token (marker) is included in the response so that the remaining results can be retrieved. Default: 100 Constraints: Minimum 20, maximum 100.
    public var maxRecords: Swift.Int?

    public init(
        dbInstanceIdentifier: Swift.String? = nil,
        filters: [DocDBClientTypes.Filter]? = nil,
        marker: Swift.String? = nil,
        maxRecords: Swift.Int? = nil
    )
    {
        self.dbInstanceIdentifier = dbInstanceIdentifier
        self.filters = filters
        self.marker = marker
        self.maxRecords = maxRecords
    }
}

/// Represents the output of [DescribeDBInstances].
public struct DescribeDBInstancesOutput: Swift.Sendable {
    /// Detailed information about one or more instances.
    public var dbInstances: [DocDBClientTypes.DBInstance]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?

    public init(
        dbInstances: [DocDBClientTypes.DBInstance]? = nil,
        marker: Swift.String? = nil
    )
    {
        self.dbInstances = dbInstances
        self.marker = marker
    }
}

/// Represents the input to [DescribeDBSubnetGroups].
public struct DescribeDBSubnetGroupsInput: Swift.Sendable {
    /// The name of the subnet group to return details for.
    public var dbSubnetGroupName: Swift.String?
    /// This parameter is not currently supported.
    public var filters: [DocDBClientTypes.Filter]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token (marker) is included in the response so that the remaining results can be retrieved. Default: 100 Constraints: Minimum 20, maximum 100.
    public var maxRecords: Swift.Int?

    public init(
        dbSubnetGroupName: Swift.String? = nil,
        filters: [DocDBClientTypes.Filter]? = nil,
        marker: Swift.String? = nil,
        maxRecords: Swift.Int? = nil
    )
    {
        self.dbSubnetGroupName = dbSubnetGroupName
        self.filters = filters
        self.marker = marker
        self.maxRecords = maxRecords
    }
}

/// Represents the output of [DescribeDBSubnetGroups].
public struct DescribeDBSubnetGroupsOutput: Swift.Sendable {
    /// Detailed information about one or more subnet groups.
    public var dbSubnetGroups: [DocDBClientTypes.DBSubnetGroup]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?

    public init(
        dbSubnetGroups: [DocDBClientTypes.DBSubnetGroup]? = nil,
        marker: Swift.String? = nil
    )
    {
        self.dbSubnetGroups = dbSubnetGroups
        self.marker = marker
    }
}

/// Represents the input to [DescribeEngineDefaultClusterParameters].
public struct DescribeEngineDefaultClusterParametersInput: Swift.Sendable {
    /// The name of the cluster parameter group family to return the engine parameter information for.
    /// This member is required.
    public var dbParameterGroupFamily: Swift.String?
    /// This parameter is not currently supported.
    public var filters: [DocDBClientTypes.Filter]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token (marker) is included in the response so that the remaining results can be retrieved. Default: 100 Constraints: Minimum 20, maximum 100.
    public var maxRecords: Swift.Int?

    public init(
        dbParameterGroupFamily: Swift.String? = nil,
        filters: [DocDBClientTypes.Filter]? = nil,
        marker: Swift.String? = nil,
        maxRecords: Swift.Int? = nil
    )
    {
        self.dbParameterGroupFamily = dbParameterGroupFamily
        self.filters = filters
        self.marker = marker
        self.maxRecords = maxRecords
    }
}

extension DocDBClientTypes {

    /// Contains the result of a successful invocation of the DescribeEngineDefaultClusterParameters operation.
    public struct EngineDefaults: Swift.Sendable {
        /// The name of the cluster parameter group family to return the engine parameter information for.
        public var dbParameterGroupFamily: Swift.String?
        /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
        public var marker: Swift.String?
        /// The parameters of a particular cluster parameter group family.
        public var parameters: [DocDBClientTypes.Parameter]?

        public init(
            dbParameterGroupFamily: Swift.String? = nil,
            marker: Swift.String? = nil,
            parameters: [DocDBClientTypes.Parameter]? = nil
        )
        {
            self.dbParameterGroupFamily = dbParameterGroupFamily
            self.marker = marker
            self.parameters = parameters
        }
    }
}

public struct DescribeEngineDefaultClusterParametersOutput: Swift.Sendable {
    /// Contains the result of a successful invocation of the DescribeEngineDefaultClusterParameters operation.
    public var engineDefaults: DocDBClientTypes.EngineDefaults?

    public init(
        engineDefaults: DocDBClientTypes.EngineDefaults? = nil
    )
    {
        self.engineDefaults = engineDefaults
    }
}

/// Represents the input to [DescribeEventCategories].
public struct DescribeEventCategoriesInput: Swift.Sendable {
    /// This parameter is not currently supported.
    public var filters: [DocDBClientTypes.Filter]?
    /// The type of source that is generating the events. Valid values: db-instance, db-parameter-group, db-security-group
    public var sourceType: Swift.String?

    public init(
        filters: [DocDBClientTypes.Filter]? = nil,
        sourceType: Swift.String? = nil
    )
    {
        self.filters = filters
        self.sourceType = sourceType
    }
}

extension DocDBClientTypes {

    /// An event source type, accompanied by one or more event category names.
    public struct EventCategoriesMap: Swift.Sendable {
        /// The event categories for the specified source type.
        public var eventCategories: [Swift.String]?
        /// The source type that the returned categories belong to.
        public var sourceType: Swift.String?

        public init(
            eventCategories: [Swift.String]? = nil,
            sourceType: Swift.String? = nil
        )
        {
            self.eventCategories = eventCategories
            self.sourceType = sourceType
        }
    }
}

/// Represents the output of [DescribeEventCategories].
public struct DescribeEventCategoriesOutput: Swift.Sendable {
    /// A list of event category maps.
    public var eventCategoriesMapList: [DocDBClientTypes.EventCategoriesMap]?

    public init(
        eventCategoriesMapList: [DocDBClientTypes.EventCategoriesMap]? = nil
    )
    {
        self.eventCategoriesMapList = eventCategoriesMapList
    }
}

extension DocDBClientTypes {

    public enum SourceType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case dbCluster
        case dbClusterSnapshot
        case dbInstance
        case dbParameterGroup
        case dbSecurityGroup
        case dbSnapshot
        case sdkUnknown(Swift.String)

        public static var allCases: [SourceType] {
            return [
                .dbCluster,
                .dbClusterSnapshot,
                .dbInstance,
                .dbParameterGroup,
                .dbSecurityGroup,
                .dbSnapshot
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .dbCluster: return "db-cluster"
            case .dbClusterSnapshot: return "db-cluster-snapshot"
            case .dbInstance: return "db-instance"
            case .dbParameterGroup: return "db-parameter-group"
            case .dbSecurityGroup: return "db-security-group"
            case .dbSnapshot: return "db-snapshot"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Represents the input to [DescribeEvents].
public struct DescribeEventsInput: Swift.Sendable {
    /// The number of minutes to retrieve events for. Default: 60
    public var duration: Swift.Int?
    /// The end of the time interval for which to retrieve events, specified in ISO 8601 format. Example: 2009-07-08T18:00Z
    public var endTime: Foundation.Date?
    /// A list of event categories that trigger notifications for an event notification subscription.
    public var eventCategories: [Swift.String]?
    /// This parameter is not currently supported.
    public var filters: [DocDBClientTypes.Filter]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token (marker) is included in the response so that the remaining results can be retrieved. Default: 100 Constraints: Minimum 20, maximum 100.
    public var maxRecords: Swift.Int?
    /// The identifier of the event source for which events are returned. If not specified, then all sources are included in the response. Constraints:
    ///
    /// * If SourceIdentifier is provided, SourceType must also be provided.
    ///
    /// * If the source type is DBInstance, a DBInstanceIdentifier must be provided.
    ///
    /// * If the source type is DBSecurityGroup, a DBSecurityGroupName must be provided.
    ///
    /// * If the source type is DBParameterGroup, a DBParameterGroupName must be provided.
    ///
    /// * If the source type is DBSnapshot, a DBSnapshotIdentifier must be provided.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    public var sourceIdentifier: Swift.String?
    /// The event source to retrieve events for. If no value is specified, all events are returned.
    public var sourceType: DocDBClientTypes.SourceType?
    /// The beginning of the time interval to retrieve events for, specified in ISO 8601 format. Example: 2009-07-08T18:00Z
    public var startTime: Foundation.Date?

    public init(
        duration: Swift.Int? = nil,
        endTime: Foundation.Date? = nil,
        eventCategories: [Swift.String]? = nil,
        filters: [DocDBClientTypes.Filter]? = nil,
        marker: Swift.String? = nil,
        maxRecords: Swift.Int? = nil,
        sourceIdentifier: Swift.String? = nil,
        sourceType: DocDBClientTypes.SourceType? = nil,
        startTime: Foundation.Date? = nil
    )
    {
        self.duration = duration
        self.endTime = endTime
        self.eventCategories = eventCategories
        self.filters = filters
        self.marker = marker
        self.maxRecords = maxRecords
        self.sourceIdentifier = sourceIdentifier
        self.sourceType = sourceType
        self.startTime = startTime
    }
}

extension DocDBClientTypes {

    /// Detailed information about an event.
    public struct Event: Swift.Sendable {
        /// Specifies the date and time of the event.
        public var date: Foundation.Date?
        /// Specifies the category for the event.
        public var eventCategories: [Swift.String]?
        /// Provides the text of this event.
        public var message: Swift.String?
        /// The Amazon Resource Name (ARN) for the event.
        public var sourceArn: Swift.String?
        /// Provides the identifier for the source of the event.
        public var sourceIdentifier: Swift.String?
        /// Specifies the source type for this event.
        public var sourceType: DocDBClientTypes.SourceType?

        public init(
            date: Foundation.Date? = nil,
            eventCategories: [Swift.String]? = nil,
            message: Swift.String? = nil,
            sourceArn: Swift.String? = nil,
            sourceIdentifier: Swift.String? = nil,
            sourceType: DocDBClientTypes.SourceType? = nil
        )
        {
            self.date = date
            self.eventCategories = eventCategories
            self.message = message
            self.sourceArn = sourceArn
            self.sourceIdentifier = sourceIdentifier
            self.sourceType = sourceType
        }
    }
}

/// Represents the output of [DescribeEvents].
public struct DescribeEventsOutput: Swift.Sendable {
    /// Detailed information about one or more events.
    public var events: [DocDBClientTypes.Event]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?

    public init(
        events: [DocDBClientTypes.Event]? = nil,
        marker: Swift.String? = nil
    )
    {
        self.events = events
        self.marker = marker
    }
}

/// Represents the input to [DescribeEventSubscriptions].
public struct DescribeEventSubscriptionsInput: Swift.Sendable {
    /// This parameter is not currently supported.
    public var filters: [DocDBClientTypes.Filter]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token (marker) is included in the response so that the remaining results can be retrieved. Default: 100 Constraints: Minimum 20, maximum 100.
    public var maxRecords: Swift.Int?
    /// The name of the Amazon DocumentDB event notification subscription that you want to describe.
    public var subscriptionName: Swift.String?

    public init(
        filters: [DocDBClientTypes.Filter]? = nil,
        marker: Swift.String? = nil,
        maxRecords: Swift.Int? = nil,
        subscriptionName: Swift.String? = nil
    )
    {
        self.filters = filters
        self.marker = marker
        self.maxRecords = maxRecords
        self.subscriptionName = subscriptionName
    }
}

/// Represents the output of [DescribeEventSubscriptions].
public struct DescribeEventSubscriptionsOutput: Swift.Sendable {
    /// A list of event subscriptions.
    public var eventSubscriptionsList: [DocDBClientTypes.EventSubscription]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?

    public init(
        eventSubscriptionsList: [DocDBClientTypes.EventSubscription]? = nil,
        marker: Swift.String? = nil
    )
    {
        self.eventSubscriptionsList = eventSubscriptionsList
        self.marker = marker
    }
}

public struct DescribeGlobalClustersInput: Swift.Sendable {
    /// A filter that specifies one or more global DB clusters to describe. Supported filters: db-cluster-id accepts cluster identifiers and cluster Amazon Resource Names (ARNs). The results list will only include information about the clusters identified by these ARNs.
    public var filters: [DocDBClientTypes.Filter]?
    /// The user-supplied cluster identifier. If this parameter is specified, information from only the specific cluster is returned. This parameter isn't case-sensitive.
    public var globalClusterIdentifier: Swift.String?
    /// An optional pagination token provided by a previous DescribeGlobalClusters request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token called a marker is included in the response so that you can retrieve the remaining results.
    public var maxRecords: Swift.Int?

    public init(
        filters: [DocDBClientTypes.Filter]? = nil,
        globalClusterIdentifier: Swift.String? = nil,
        marker: Swift.String? = nil,
        maxRecords: Swift.Int? = nil
    )
    {
        self.filters = filters
        self.globalClusterIdentifier = globalClusterIdentifier
        self.marker = marker
        self.maxRecords = maxRecords
    }
}

public struct DescribeGlobalClustersOutput: Swift.Sendable {
    ///
    public var globalClusters: [DocDBClientTypes.GlobalCluster]?
    ///
    public var marker: Swift.String?

    public init(
        globalClusters: [DocDBClientTypes.GlobalCluster]? = nil,
        marker: Swift.String? = nil
    )
    {
        self.globalClusters = globalClusters
        self.marker = marker
    }
}

/// Represents the input to [DescribeOrderableDBInstanceOptions].
public struct DescribeOrderableDBInstanceOptionsInput: Swift.Sendable {
    /// The instance class filter value. Specify this parameter to show only the available offerings that match the specified instance class.
    public var dbInstanceClass: Swift.String?
    /// The name of the engine to retrieve instance options for.
    /// This member is required.
    public var engine: Swift.String?
    /// The engine version filter value. Specify this parameter to show only the available offerings that match the specified engine version.
    public var engineVersion: Swift.String?
    /// This parameter is not currently supported.
    public var filters: [DocDBClientTypes.Filter]?
    /// The license model filter value. Specify this parameter to show only the available offerings that match the specified license model.
    public var licenseModel: Swift.String?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token (marker) is included in the response so that the remaining results can be retrieved. Default: 100 Constraints: Minimum 20, maximum 100.
    public var maxRecords: Swift.Int?
    /// The virtual private cloud (VPC) filter value. Specify this parameter to show only the available VPC or non-VPC offerings.
    public var vpc: Swift.Bool?

    public init(
        dbInstanceClass: Swift.String? = nil,
        engine: Swift.String? = nil,
        engineVersion: Swift.String? = nil,
        filters: [DocDBClientTypes.Filter]? = nil,
        licenseModel: Swift.String? = nil,
        marker: Swift.String? = nil,
        maxRecords: Swift.Int? = nil,
        vpc: Swift.Bool? = nil
    )
    {
        self.dbInstanceClass = dbInstanceClass
        self.engine = engine
        self.engineVersion = engineVersion
        self.filters = filters
        self.licenseModel = licenseModel
        self.marker = marker
        self.maxRecords = maxRecords
        self.vpc = vpc
    }
}

extension DocDBClientTypes {

    /// The options that are available for an instance.
    public struct OrderableDBInstanceOption: Swift.Sendable {
        /// A list of Availability Zones for an instance.
        public var availabilityZones: [DocDBClientTypes.AvailabilityZone]?
        /// The instance class for an instance.
        public var dbInstanceClass: Swift.String?
        /// The engine type of an instance.
        public var engine: Swift.String?
        /// The engine version of an instance.
        public var engineVersion: Swift.String?
        /// The license model for an instance.
        public var licenseModel: Swift.String?
        /// The storage type to associate with the DB cluster
        public var storageType: Swift.String?
        /// Indicates whether an instance is in a virtual private cloud (VPC).
        public var vpc: Swift.Bool?

        public init(
            availabilityZones: [DocDBClientTypes.AvailabilityZone]? = nil,
            dbInstanceClass: Swift.String? = nil,
            engine: Swift.String? = nil,
            engineVersion: Swift.String? = nil,
            licenseModel: Swift.String? = nil,
            storageType: Swift.String? = nil,
            vpc: Swift.Bool? = nil
        )
        {
            self.availabilityZones = availabilityZones
            self.dbInstanceClass = dbInstanceClass
            self.engine = engine
            self.engineVersion = engineVersion
            self.licenseModel = licenseModel
            self.storageType = storageType
            self.vpc = vpc
        }
    }
}

/// Represents the output of [DescribeOrderableDBInstanceOptions].
public struct DescribeOrderableDBInstanceOptionsOutput: Swift.Sendable {
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The options that are available for a particular orderable instance.
    public var orderableDBInstanceOptions: [DocDBClientTypes.OrderableDBInstanceOption]?

    public init(
        marker: Swift.String? = nil,
        orderableDBInstanceOptions: [DocDBClientTypes.OrderableDBInstanceOption]? = nil
    )
    {
        self.marker = marker
        self.orderableDBInstanceOptions = orderableDBInstanceOptions
    }
}

/// Represents the input to [DescribePendingMaintenanceActions].
public struct DescribePendingMaintenanceActionsInput: Swift.Sendable {
    /// A filter that specifies one or more resources to return pending maintenance actions for. Supported filters:
    ///
    /// * db-cluster-id - Accepts cluster identifiers and cluster Amazon Resource Names (ARNs). The results list includes only pending maintenance actions for the clusters identified by these ARNs.
    ///
    /// * db-instance-id - Accepts instance identifiers and instance ARNs. The results list includes only pending maintenance actions for the DB instances identified by these ARNs.
    public var filters: [DocDBClientTypes.Filter]?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maximum number of records to include in the response. If more records exist than the specified MaxRecords value, a pagination token (marker) is included in the response so that the remaining results can be retrieved. Default: 100 Constraints: Minimum 20, maximum 100.
    public var maxRecords: Swift.Int?
    /// The ARN of a resource to return pending maintenance actions for.
    public var resourceIdentifier: Swift.String?

    public init(
        filters: [DocDBClientTypes.Filter]? = nil,
        marker: Swift.String? = nil,
        maxRecords: Swift.Int? = nil,
        resourceIdentifier: Swift.String? = nil
    )
    {
        self.filters = filters
        self.marker = marker
        self.maxRecords = maxRecords
        self.resourceIdentifier = resourceIdentifier
    }
}

/// Represents the output of [DescribePendingMaintenanceActions].
public struct DescribePendingMaintenanceActionsOutput: Swift.Sendable {
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by MaxRecords.
    public var marker: Swift.String?
    /// The maintenance actions to be applied.
    public var pendingMaintenanceActions: [DocDBClientTypes.ResourcePendingMaintenanceActions]?

    public init(
        marker: Swift.String? = nil,
        pendingMaintenanceActions: [DocDBClientTypes.ResourcePendingMaintenanceActions]? = nil
    )
    {
        self.marker = marker
        self.pendingMaintenanceActions = pendingMaintenanceActions
    }
}

/// Represents the input to [FailoverDBCluster].
public struct FailoverDBClusterInput: Swift.Sendable {
    /// A cluster identifier to force a failover for. This parameter is not case sensitive. Constraints:
    ///
    /// * Must match the identifier of an existing DBCluster.
    public var dbClusterIdentifier: Swift.String?
    /// The name of the instance to promote to the primary instance. You must specify the instance identifier for an Amazon DocumentDB replica in the cluster. For example, mydbcluster-replica1.
    public var targetDBInstanceIdentifier: Swift.String?

    public init(
        dbClusterIdentifier: Swift.String? = nil,
        targetDBInstanceIdentifier: Swift.String? = nil
    )
    {
        self.dbClusterIdentifier = dbClusterIdentifier
        self.targetDBInstanceIdentifier = targetDBInstanceIdentifier
    }
}

public struct FailoverDBClusterOutput: Swift.Sendable {
    /// Detailed information about a cluster.
    public var dbCluster: DocDBClientTypes.DBCluster?

    public init(
        dbCluster: DocDBClientTypes.DBCluster? = nil
    )
    {
        self.dbCluster = dbCluster
    }
}

public struct FailoverGlobalClusterInput: Swift.Sendable {
    /// Specifies whether to allow data loss for this global cluster operation. Allowing data loss triggers a global failover operation. If you don't specify AllowDataLoss, the global cluster operation defaults to a switchover. Constraints:
    ///
    /// * Can't be specified together with the Switchover parameter.
    public var allowDataLoss: Swift.Bool?
    /// The identifier of the Amazon DocumentDB global cluster to apply this operation. The identifier is the unique key assigned by the user when the cluster is created. In other words, it's the name of the global cluster. Constraints:
    ///
    /// * Must match the identifier of an existing global cluster.
    ///
    /// * Minimum length of 1. Maximum length of 255.
    ///
    ///
    /// Pattern: [A-Za-z][0-9A-Za-z-:._]*
    /// This member is required.
    public var globalClusterIdentifier: Swift.String?
    /// Specifies whether to switch over this global database cluster. Constraints:
    ///
    /// * Can't be specified together with the AllowDataLoss parameter.
    public var switchover: Swift.Bool?
    /// The identifier of the secondary Amazon DocumentDB cluster that you want to promote to the primary for the global cluster. Use the Amazon Resource Name (ARN) for the identifier so that Amazon DocumentDB can locate the cluster in its Amazon Web Services region. Constraints:
    ///
    /// * Must match the identifier of an existing secondary cluster.
    ///
    /// * Minimum length of 1. Maximum length of 255.
    ///
    ///
    /// Pattern: [A-Za-z][0-9A-Za-z-:._]*
    /// This member is required.
    public var targetDbClusterIdentifier: Swift.String?

    public init(
        allowDataLoss: Swift.Bool? = nil,
        globalClusterIdentifier: Swift.String? = nil,
        switchover: Swift.Bool? = nil,
        targetDbClusterIdentifier: Swift.String? = nil
    )
    {
        self.allowDataLoss = allowDataLoss
        self.globalClusterIdentifier = globalClusterIdentifier
        self.switchover = switchover
        self.targetDbClusterIdentifier = targetDbClusterIdentifier
    }
}

public struct FailoverGlobalClusterOutput: Swift.Sendable {
    /// A data type representing an Amazon DocumentDB global cluster.
    public var globalCluster: DocDBClientTypes.GlobalCluster?

    public init(
        globalCluster: DocDBClientTypes.GlobalCluster? = nil
    )
    {
        self.globalCluster = globalCluster
    }
}

/// Represents the input to [ListTagsForResource].
public struct ListTagsForResourceInput: Swift.Sendable {
    /// This parameter is not currently supported.
    public var filters: [DocDBClientTypes.Filter]?
    /// The Amazon DocumentDB resource with tags to be listed. This value is an Amazon Resource Name (ARN).
    /// This member is required.
    public var resourceName: Swift.String?

    public init(
        filters: [DocDBClientTypes.Filter]? = nil,
        resourceName: Swift.String? = nil
    )
    {
        self.filters = filters
        self.resourceName = resourceName
    }
}

/// Represents the output of [ListTagsForResource].
public struct ListTagsForResourceOutput: Swift.Sendable {
    /// A list of one or more tags.
    public var tagList: [DocDBClientTypes.Tag]?

    public init(
        tagList: [DocDBClientTypes.Tag]? = nil
    )
    {
        self.tagList = tagList
    }
}

/// The state of the security group doesn't allow deletion.
public struct InvalidDBSecurityGroupStateFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidDBSecurityGroupState" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension DocDBClientTypes {

    /// The configuration setting for the log types to be enabled for export to Amazon CloudWatch Logs for a specific instance or cluster. The EnableLogTypes and DisableLogTypes arrays determine which logs are exported (or not exported) to CloudWatch Logs. The values within these arrays depend on the engine that is being used.
    public struct CloudwatchLogsExportConfiguration: Swift.Sendable {
        /// The list of log types to disable.
        public var disableLogTypes: [Swift.String]?
        /// The list of log types to enable.
        public var enableLogTypes: [Swift.String]?

        public init(
            disableLogTypes: [Swift.String]? = nil,
            enableLogTypes: [Swift.String]? = nil
        )
        {
            self.disableLogTypes = disableLogTypes
            self.enableLogTypes = enableLogTypes
        }
    }
}

/// Represents the input to [ModifyDBCluster].
public struct ModifyDBClusterInput: Swift.Sendable {
    /// A value that indicates whether major version upgrades are allowed. Constraints: You must allow major version upgrades when specifying a value for the EngineVersion parameter that is a different major version than the DB cluster's current version.
    public var allowMajorVersionUpgrade: Swift.Bool?
    /// A value that specifies whether the changes in this request and any pending changes are asynchronously applied as soon as possible, regardless of the PreferredMaintenanceWindow setting for the cluster. If this parameter is set to false, changes to the cluster are applied during the next maintenance window. The ApplyImmediately parameter affects only the NewDBClusterIdentifier and MasterUserPassword values. If you set this parameter value to false, the changes to the NewDBClusterIdentifier and MasterUserPassword values are applied during the next maintenance window. All other changes are applied immediately, regardless of the value of the ApplyImmediately parameter. Default: false
    public var applyImmediately: Swift.Bool?
    /// The number of days for which automated backups are retained. You must specify a minimum value of 1. Default: 1 Constraints:
    ///
    /// * Must be a value from 1 to 35.
    public var backupRetentionPeriod: Swift.Int?
    /// The configuration setting for the log types to be enabled for export to Amazon CloudWatch Logs for a specific instance or cluster. The EnableLogTypes and DisableLogTypes arrays determine which logs are exported (or not exported) to CloudWatch Logs.
    public var cloudwatchLogsExportConfiguration: DocDBClientTypes.CloudwatchLogsExportConfiguration?
    /// The cluster identifier for the cluster that is being modified. This parameter is not case sensitive. Constraints:
    ///
    /// * Must match the identifier of an existing DBCluster.
    /// This member is required.
    public var dbClusterIdentifier: Swift.String?
    /// The name of the cluster parameter group to use for the cluster.
    public var dbClusterParameterGroupName: Swift.String?
    /// Specifies whether this cluster can be deleted. If DeletionProtection is enabled, the cluster cannot be deleted unless it is modified and DeletionProtection is disabled. DeletionProtection protects clusters from being accidentally deleted.
    public var deletionProtection: Swift.Bool?
    /// The version number of the database engine to which you want to upgrade. Changing this parameter results in an outage. The change is applied during the next maintenance window unless ApplyImmediately is enabled. To list all of the available engine versions for Amazon DocumentDB use the following command: aws docdb describe-db-engine-versions --engine docdb --query "DBEngineVersions[].EngineVersion"
    public var engineVersion: Swift.String?
    /// Specifies whether to manage the master user password with Amazon Web Services Secrets Manager. If the cluster doesn't manage the master user password with Amazon Web Services Secrets Manager, you can turn on this management. In this case, you can't specify MasterUserPassword. If the cluster already manages the master user password with Amazon Web Services Secrets Manager, and you specify that the master user password is not managed with Amazon Web Services Secrets Manager, then you must specify MasterUserPassword. In this case, Amazon DocumentDB deletes the secret and uses the new password for the master user specified by MasterUserPassword.
    public var manageMasterUserPassword: Swift.Bool?
    /// The password for the master database user. This password can contain any printable ASCII character except forward slash (/), double quote ("), or the "at" symbol (@). Constraints: Must contain from 8 to 100 characters.
    public var masterUserPassword: Swift.String?
    /// The Amazon Web Services KMS key identifier to encrypt a secret that is automatically generated and managed in Amazon Web Services Secrets Manager. This setting is valid only if both of the following conditions are met:
    ///
    /// * The cluster doesn't manage the master user password in Amazon Web Services Secrets Manager. If the cluster already manages the master user password in Amazon Web Services Secrets Manager, you can't change the KMS key that is used to encrypt the secret.
    ///
    /// * You are enabling ManageMasterUserPassword to manage the master user password in Amazon Web Services Secrets Manager. If you are turning on ManageMasterUserPassword and don't specify MasterUserSecretKmsKeyId, then the aws/secretsmanager KMS key is used to encrypt the secret. If the secret is in a different Amazon Web Services account, then you can't use the aws/secretsmanager KMS key to encrypt the secret, and you must use a customer managed KMS key.
    ///
    ///
    /// The Amazon Web Services KMS key identifier is the key ARN, key ID, alias ARN, or alias name for the KMS key. To use a KMS key in a different Amazon Web Services account, specify the key ARN or alias ARN. There is a default KMS key for your Amazon Web Services account. Your Amazon Web Services account has a different default KMS key for each Amazon Web Services Region.
    public var masterUserSecretKmsKeyId: Swift.String?
    /// The new cluster identifier for the cluster when renaming a cluster. This value is stored as a lowercase string. Constraints:
    ///
    /// * Must contain from 1 to 63 letters, numbers, or hyphens.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    ///
    ///
    /// Example: my-cluster2
    public var newDBClusterIdentifier: Swift.String?
    /// The port number on which the cluster accepts connections. Constraints: Must be a value from 1150 to 65535. Default: The same port as the original cluster.
    public var port: Swift.Int?
    /// The daily time range during which automated backups are created if automated backups are enabled, using the BackupRetentionPeriod parameter. The default is a 30-minute window selected at random from an 8-hour block of time for each Amazon Web Services Region. Constraints:
    ///
    /// * Must be in the format hh24:mi-hh24:mi.
    ///
    /// * Must be in Universal Coordinated Time (UTC).
    ///
    /// * Must not conflict with the preferred maintenance window.
    ///
    /// * Must be at least 30 minutes.
    public var preferredBackupWindow: Swift.String?
    /// The weekly time range during which system maintenance can occur, in Universal Coordinated Time (UTC). Format: ddd:hh24:mi-ddd:hh24:mi The default is a 30-minute window selected at random from an 8-hour block of time for each Amazon Web Services Region, occurring on a random day of the week. Valid days: Mon, Tue, Wed, Thu, Fri, Sat, Sun Constraints: Minimum 30-minute window.
    public var preferredMaintenanceWindow: Swift.String?
    /// Specifies whether to rotate the secret managed by Amazon Web Services Secrets Manager for the master user password. This setting is valid only if the master user password is managed by Amazon DocumentDB in Amazon Web Services Secrets Manager for the cluster. The secret value contains the updated password. Constraint: You must apply the change immediately when rotating the master user password.
    public var rotateMasterUserPassword: Swift.Bool?
    /// The storage type to associate with the DB cluster. For information on storage types for Amazon DocumentDB clusters, see Cluster storage configurations in the Amazon DocumentDB Developer Guide. Valid values for storage type - standard | iopt1 Default value is standard
    public var storageType: Swift.String?
    /// A list of virtual private cloud (VPC) security groups that the cluster will belong to.
    public var vpcSecurityGroupIds: [Swift.String]?

    public init(
        allowMajorVersionUpgrade: Swift.Bool? = nil,
        applyImmediately: Swift.Bool? = nil,
        backupRetentionPeriod: Swift.Int? = nil,
        cloudwatchLogsExportConfiguration: DocDBClientTypes.CloudwatchLogsExportConfiguration? = nil,
        dbClusterIdentifier: Swift.String? = nil,
        dbClusterParameterGroupName: Swift.String? = nil,
        deletionProtection: Swift.Bool? = nil,
        engineVersion: Swift.String? = nil,
        manageMasterUserPassword: Swift.Bool? = nil,
        masterUserPassword: Swift.String? = nil,
        masterUserSecretKmsKeyId: Swift.String? = nil,
        newDBClusterIdentifier: Swift.String? = nil,
        port: Swift.Int? = nil,
        preferredBackupWindow: Swift.String? = nil,
        preferredMaintenanceWindow: Swift.String? = nil,
        rotateMasterUserPassword: Swift.Bool? = nil,
        storageType: Swift.String? = nil,
        vpcSecurityGroupIds: [Swift.String]? = nil
    )
    {
        self.allowMajorVersionUpgrade = allowMajorVersionUpgrade
        self.applyImmediately = applyImmediately
        self.backupRetentionPeriod = backupRetentionPeriod
        self.cloudwatchLogsExportConfiguration = cloudwatchLogsExportConfiguration
        self.dbClusterIdentifier = dbClusterIdentifier
        self.dbClusterParameterGroupName = dbClusterParameterGroupName
        self.deletionProtection = deletionProtection
        self.engineVersion = engineVersion
        self.manageMasterUserPassword = manageMasterUserPassword
        self.masterUserPassword = masterUserPassword
        self.masterUserSecretKmsKeyId = masterUserSecretKmsKeyId
        self.newDBClusterIdentifier = newDBClusterIdentifier
        self.port = port
        self.preferredBackupWindow = preferredBackupWindow
        self.preferredMaintenanceWindow = preferredMaintenanceWindow
        self.rotateMasterUserPassword = rotateMasterUserPassword
        self.storageType = storageType
        self.vpcSecurityGroupIds = vpcSecurityGroupIds
    }
}

public struct ModifyDBClusterOutput: Swift.Sendable {
    /// Detailed information about a cluster.
    public var dbCluster: DocDBClientTypes.DBCluster?

    public init(
        dbCluster: DocDBClientTypes.DBCluster? = nil
    )
    {
        self.dbCluster = dbCluster
    }
}

/// Represents the input to [ModifyDBClusterParameterGroup].
public struct ModifyDBClusterParameterGroupInput: Swift.Sendable {
    /// The name of the cluster parameter group to modify.
    /// This member is required.
    public var dbClusterParameterGroupName: Swift.String?
    /// A list of parameters in the cluster parameter group to modify.
    /// This member is required.
    public var parameters: [DocDBClientTypes.Parameter]?

    public init(
        dbClusterParameterGroupName: Swift.String? = nil,
        parameters: [DocDBClientTypes.Parameter]? = nil
    )
    {
        self.dbClusterParameterGroupName = dbClusterParameterGroupName
        self.parameters = parameters
    }
}

/// Contains the name of a cluster parameter group.
public struct ModifyDBClusterParameterGroupOutput: Swift.Sendable {
    /// The name of a cluster parameter group. Constraints:
    ///
    /// * Must be from 1 to 255 letters or numbers.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    ///
    ///
    /// This value is stored as a lowercase string.
    public var dbClusterParameterGroupName: Swift.String?

    public init(
        dbClusterParameterGroupName: Swift.String? = nil
    )
    {
        self.dbClusterParameterGroupName = dbClusterParameterGroupName
    }
}

/// You have exceeded the maximum number of accounts that you can share a manual DB snapshot with.
public struct SharedSnapshotQuotaExceededFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SharedSnapshotQuotaExceeded" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [ModifyDBClusterSnapshotAttribute].
public struct ModifyDBClusterSnapshotAttributeInput: Swift.Sendable {
    /// The name of the cluster snapshot attribute to modify. To manage authorization for other Amazon Web Services accounts to copy or restore a manual cluster snapshot, set this value to restore.
    /// This member is required.
    public var attributeName: Swift.String?
    /// The identifier for the cluster snapshot to modify the attributes for.
    /// This member is required.
    public var dbClusterSnapshotIdentifier: Swift.String?
    /// A list of cluster snapshot attributes to add to the attribute specified by AttributeName. To authorize other Amazon Web Services accounts to copy or restore a manual cluster snapshot, set this list to include one or more Amazon Web Services account IDs. To make the manual cluster snapshot restorable by any Amazon Web Services account, set it to all. Do not add the all value for any manual cluster snapshots that contain private information that you don't want to be available to all Amazon Web Services accounts.
    public var valuesToAdd: [Swift.String]?
    /// A list of cluster snapshot attributes to remove from the attribute specified by AttributeName. To remove authorization for other Amazon Web Services accounts to copy or restore a manual cluster snapshot, set this list to include one or more Amazon Web Services account identifiers. To remove authorization for any Amazon Web Services account to copy or restore the cluster snapshot, set it to all . If you specify all, an Amazon Web Services account whose account ID is explicitly added to the restore attribute can still copy or restore a manual cluster snapshot.
    public var valuesToRemove: [Swift.String]?

    public init(
        attributeName: Swift.String? = nil,
        dbClusterSnapshotIdentifier: Swift.String? = nil,
        valuesToAdd: [Swift.String]? = nil,
        valuesToRemove: [Swift.String]? = nil
    )
    {
        self.attributeName = attributeName
        self.dbClusterSnapshotIdentifier = dbClusterSnapshotIdentifier
        self.valuesToAdd = valuesToAdd
        self.valuesToRemove = valuesToRemove
    }
}

public struct ModifyDBClusterSnapshotAttributeOutput: Swift.Sendable {
    /// Detailed information about the attributes that are associated with a cluster snapshot.
    public var dbClusterSnapshotAttributesResult: DocDBClientTypes.DBClusterSnapshotAttributesResult?

    public init(
        dbClusterSnapshotAttributesResult: DocDBClientTypes.DBClusterSnapshotAttributesResult? = nil
    )
    {
        self.dbClusterSnapshotAttributesResult = dbClusterSnapshotAttributesResult
    }
}

/// The upgrade failed because a resource that the depends on can't be modified.
public struct DBUpgradeDependencyFailureFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DBUpgradeDependencyFailure" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [ModifyDBInstance].
public struct ModifyDBInstanceInput: Swift.Sendable {
    /// Specifies whether the modifications in this request and any pending modifications are asynchronously applied as soon as possible, regardless of the PreferredMaintenanceWindow setting for the instance. If this parameter is set to false, changes to the instance are applied during the next maintenance window. Some parameter changes can cause an outage and are applied on the next reboot. Default: false
    public var applyImmediately: Swift.Bool?
    /// This parameter does not apply to Amazon DocumentDB. Amazon DocumentDB does not perform minor version upgrades regardless of the value set.
    public var autoMinorVersionUpgrade: Swift.Bool?
    /// Indicates the certificate that needs to be associated with the instance.
    public var caCertificateIdentifier: Swift.String?
    /// Specifies whether the DB instance is restarted when you rotate your SSL/TLS certificate. By default, the DB instance is restarted when you rotate your SSL/TLS certificate. The certificate is not updated until the DB instance is restarted. Set this parameter only if you are not using SSL/TLS to connect to the DB instance. If you are using SSL/TLS to connect to the DB instance, see [Updating Your Amazon DocumentDB TLS Certificates](https://docs.aws.amazon.com/documentdb/latest/developerguide/ca_cert_rotation.html) and [ Encrypting Data in Transit](https://docs.aws.amazon.com/documentdb/latest/developerguide/security.encryption.ssl.html) in the Amazon DocumentDB Developer Guide.
    public var certificateRotationRestart: Swift.Bool?
    /// A value that indicates whether to copy all tags from the DB instance to snapshots of the DB instance. By default, tags are not copied.
    public var copyTagsToSnapshot: Swift.Bool?
    /// The new compute and memory capacity of the instance; for example, db.r5.large. Not all instance classes are available in all Amazon Web Services Regions. If you modify the instance class, an outage occurs during the change. The change is applied during the next maintenance window, unless ApplyImmediately is specified as true for this request. Default: Uses existing setting.
    public var dbInstanceClass: Swift.String?
    /// The instance identifier. This value is stored as a lowercase string. Constraints:
    ///
    /// * Must match the identifier of an existing DBInstance.
    /// This member is required.
    public var dbInstanceIdentifier: Swift.String?
    /// A value that indicates whether to enable Performance Insights for the DB Instance. For more information, see [Using Amazon Performance Insights](https://docs.aws.amazon.com/documentdb/latest/developerguide/performance-insights.html).
    public var enablePerformanceInsights: Swift.Bool?
    /// The new instance identifier for the instance when renaming an instance. When you change the instance identifier, an instance reboot occurs immediately if you set Apply Immediately to true. It occurs during the next maintenance window if you set Apply Immediately to false. This value is stored as a lowercase string. Constraints:
    ///
    /// * Must contain from 1 to 63 letters, numbers, or hyphens.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    ///
    ///
    /// Example: mydbinstance
    public var newDBInstanceIdentifier: Swift.String?
    /// The KMS key identifier for encryption of Performance Insights data. The KMS key identifier is the key ARN, key ID, alias ARN, or alias name for the KMS key. If you do not specify a value for PerformanceInsightsKMSKeyId, then Amazon DocumentDB uses your default KMS key. There is a default KMS key for your Amazon Web Services account. Your Amazon Web Services account has a different default KMS key for each Amazon Web Services region.
    public var performanceInsightsKMSKeyId: Swift.String?
    /// The weekly time range (in UTC) during which system maintenance can occur, which might result in an outage. Changing this parameter doesn't result in an outage except in the following situation, and the change is asynchronously applied as soon as possible. If there are pending actions that cause a reboot, and the maintenance window is changed to include the current time, changing this parameter causes a reboot of the instance. If you are moving this window to the current time, there must be at least 30 minutes between the current time and end of the window to ensure that pending changes are applied. Default: Uses existing setting. Format: ddd:hh24:mi-ddd:hh24:mi Valid days: Mon, Tue, Wed, Thu, Fri, Sat, Sun Constraints: Must be at least 30 minutes.
    public var preferredMaintenanceWindow: Swift.String?
    /// A value that specifies the order in which an Amazon DocumentDB replica is promoted to the primary instance after a failure of the existing primary instance. Default: 1 Valid values: 0-15
    public var promotionTier: Swift.Int?

    public init(
        applyImmediately: Swift.Bool? = nil,
        autoMinorVersionUpgrade: Swift.Bool? = nil,
        caCertificateIdentifier: Swift.String? = nil,
        certificateRotationRestart: Swift.Bool? = nil,
        copyTagsToSnapshot: Swift.Bool? = nil,
        dbInstanceClass: Swift.String? = nil,
        dbInstanceIdentifier: Swift.String? = nil,
        enablePerformanceInsights: Swift.Bool? = nil,
        newDBInstanceIdentifier: Swift.String? = nil,
        performanceInsightsKMSKeyId: Swift.String? = nil,
        preferredMaintenanceWindow: Swift.String? = nil,
        promotionTier: Swift.Int? = nil
    )
    {
        self.applyImmediately = applyImmediately
        self.autoMinorVersionUpgrade = autoMinorVersionUpgrade
        self.caCertificateIdentifier = caCertificateIdentifier
        self.certificateRotationRestart = certificateRotationRestart
        self.copyTagsToSnapshot = copyTagsToSnapshot
        self.dbInstanceClass = dbInstanceClass
        self.dbInstanceIdentifier = dbInstanceIdentifier
        self.enablePerformanceInsights = enablePerformanceInsights
        self.newDBInstanceIdentifier = newDBInstanceIdentifier
        self.performanceInsightsKMSKeyId = performanceInsightsKMSKeyId
        self.preferredMaintenanceWindow = preferredMaintenanceWindow
        self.promotionTier = promotionTier
    }
}

public struct ModifyDBInstanceOutput: Swift.Sendable {
    /// Detailed information about an instance.
    public var dbInstance: DocDBClientTypes.DBInstance?

    public init(
        dbInstance: DocDBClientTypes.DBInstance? = nil
    )
    {
        self.dbInstance = dbInstance
    }
}

/// The subnet is already in use in the Availability Zone.
public struct SubnetAlreadyInUse: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SubnetAlreadyInUse" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [ModifyDBSubnetGroup].
public struct ModifyDBSubnetGroupInput: Swift.Sendable {
    /// The description for the subnet group.
    public var dbSubnetGroupDescription: Swift.String?
    /// The name for the subnet group. This value is stored as a lowercase string. You can't modify the default subnet group. Constraints: Must match the name of an existing DBSubnetGroup. Must not be default. Example: mySubnetgroup
    /// This member is required.
    public var dbSubnetGroupName: Swift.String?
    /// The Amazon EC2 subnet IDs for the subnet group.
    /// This member is required.
    public var subnetIds: [Swift.String]?

    public init(
        dbSubnetGroupDescription: Swift.String? = nil,
        dbSubnetGroupName: Swift.String? = nil,
        subnetIds: [Swift.String]? = nil
    )
    {
        self.dbSubnetGroupDescription = dbSubnetGroupDescription
        self.dbSubnetGroupName = dbSubnetGroupName
        self.subnetIds = subnetIds
    }
}

public struct ModifyDBSubnetGroupOutput: Swift.Sendable {
    /// Detailed information about a subnet group.
    public var dbSubnetGroup: DocDBClientTypes.DBSubnetGroup?

    public init(
        dbSubnetGroup: DocDBClientTypes.DBSubnetGroup? = nil
    )
    {
        self.dbSubnetGroup = dbSubnetGroup
    }
}

/// Represents the input to [ModifyEventSubscription].
public struct ModifyEventSubscriptionInput: Swift.Sendable {
    /// A Boolean value; set to true to activate the subscription.
    public var enabled: Swift.Bool?
    /// A list of event categories for a SourceType that you want to subscribe to.
    public var eventCategories: [Swift.String]?
    /// The Amazon Resource Name (ARN) of the SNS topic created for event notification. The ARN is created by Amazon SNS when you create a topic and subscribe to it.
    public var snsTopicArn: Swift.String?
    /// The type of source that is generating the events. For example, if you want to be notified of events generated by an instance, set this parameter to db-instance. If this value is not specified, all events are returned. Valid values: db-instance, db-parameter-group, db-security-group
    public var sourceType: Swift.String?
    /// The name of the Amazon DocumentDB event notification subscription.
    /// This member is required.
    public var subscriptionName: Swift.String?

    public init(
        enabled: Swift.Bool? = nil,
        eventCategories: [Swift.String]? = nil,
        snsTopicArn: Swift.String? = nil,
        sourceType: Swift.String? = nil,
        subscriptionName: Swift.String? = nil
    )
    {
        self.enabled = enabled
        self.eventCategories = eventCategories
        self.snsTopicArn = snsTopicArn
        self.sourceType = sourceType
        self.subscriptionName = subscriptionName
    }
}

public struct ModifyEventSubscriptionOutput: Swift.Sendable {
    /// Detailed information about an event to which you have subscribed.
    public var eventSubscription: DocDBClientTypes.EventSubscription?

    public init(
        eventSubscription: DocDBClientTypes.EventSubscription? = nil
    )
    {
        self.eventSubscription = eventSubscription
    }
}

/// Represents the input to [ModifyGlobalCluster].
public struct ModifyGlobalClusterInput: Swift.Sendable {
    /// Indicates if the global cluster has deletion protection enabled. The global cluster can't be deleted when deletion protection is enabled.
    public var deletionProtection: Swift.Bool?
    /// The identifier for the global cluster being modified. This parameter isn't case-sensitive. Constraints:
    ///
    /// * Must match the identifier of an existing global cluster.
    /// This member is required.
    public var globalClusterIdentifier: Swift.String?
    /// The new identifier for a global cluster when you modify a global cluster. This value is stored as a lowercase string.
    ///
    /// * Must contain from 1 to 63 letters, numbers, or hyphens The first character must be a letter Can't end with a hyphen or contain two consecutive hyphens
    ///
    ///
    /// Example: my-cluster2
    public var newGlobalClusterIdentifier: Swift.String?

    public init(
        deletionProtection: Swift.Bool? = nil,
        globalClusterIdentifier: Swift.String? = nil,
        newGlobalClusterIdentifier: Swift.String? = nil
    )
    {
        self.deletionProtection = deletionProtection
        self.globalClusterIdentifier = globalClusterIdentifier
        self.newGlobalClusterIdentifier = newGlobalClusterIdentifier
    }
}

public struct ModifyGlobalClusterOutput: Swift.Sendable {
    /// A data type representing an Amazon DocumentDB global cluster.
    public var globalCluster: DocDBClientTypes.GlobalCluster?

    public init(
        globalCluster: DocDBClientTypes.GlobalCluster? = nil
    )
    {
        self.globalCluster = globalCluster
    }
}

/// Represents the input to [RebootDBInstance].
public struct RebootDBInstanceInput: Swift.Sendable {
    /// The instance identifier. This parameter is stored as a lowercase string. Constraints:
    ///
    /// * Must match the identifier of an existing DBInstance.
    /// This member is required.
    public var dbInstanceIdentifier: Swift.String?
    /// When true, the reboot is conducted through a Multi-AZ failover. Constraint: You can't specify true if the instance is not configured for Multi-AZ.
    public var forceFailover: Swift.Bool?

    public init(
        dbInstanceIdentifier: Swift.String? = nil,
        forceFailover: Swift.Bool? = nil
    )
    {
        self.dbInstanceIdentifier = dbInstanceIdentifier
        self.forceFailover = forceFailover
    }
}

public struct RebootDBInstanceOutput: Swift.Sendable {
    /// Detailed information about an instance.
    public var dbInstance: DocDBClientTypes.DBInstance?

    public init(
        dbInstance: DocDBClientTypes.DBInstance? = nil
    )
    {
        self.dbInstance = dbInstance
    }
}

/// Represents the input to [RemoveFromGlobalCluster].
public struct RemoveFromGlobalClusterInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) identifying the cluster that was detached from the Amazon DocumentDB global cluster.
    /// This member is required.
    public var dbClusterIdentifier: Swift.String?
    /// The cluster identifier to detach from the Amazon DocumentDB global cluster.
    /// This member is required.
    public var globalClusterIdentifier: Swift.String?

    public init(
        dbClusterIdentifier: Swift.String? = nil,
        globalClusterIdentifier: Swift.String? = nil
    )
    {
        self.dbClusterIdentifier = dbClusterIdentifier
        self.globalClusterIdentifier = globalClusterIdentifier
    }
}

public struct RemoveFromGlobalClusterOutput: Swift.Sendable {
    /// A data type representing an Amazon DocumentDB global cluster.
    public var globalCluster: DocDBClientTypes.GlobalCluster?

    public init(
        globalCluster: DocDBClientTypes.GlobalCluster? = nil
    )
    {
        self.globalCluster = globalCluster
    }
}

/// Represents the input to [RemoveSourceIdentifierFromSubscription].
public struct RemoveSourceIdentifierFromSubscriptionInput: Swift.Sendable {
    /// The source identifier to be removed from the subscription, such as the instance identifier for an instance, or the name of a security group.
    /// This member is required.
    public var sourceIdentifier: Swift.String?
    /// The name of the Amazon DocumentDB event notification subscription that you want to remove a source identifier from.
    /// This member is required.
    public var subscriptionName: Swift.String?

    public init(
        sourceIdentifier: Swift.String? = nil,
        subscriptionName: Swift.String? = nil
    )
    {
        self.sourceIdentifier = sourceIdentifier
        self.subscriptionName = subscriptionName
    }
}

public struct RemoveSourceIdentifierFromSubscriptionOutput: Swift.Sendable {
    /// Detailed information about an event to which you have subscribed.
    public var eventSubscription: DocDBClientTypes.EventSubscription?

    public init(
        eventSubscription: DocDBClientTypes.EventSubscription? = nil
    )
    {
        self.eventSubscription = eventSubscription
    }
}

/// Represents the input to [RemoveTagsFromResource].
public struct RemoveTagsFromResourceInput: Swift.Sendable {
    /// The Amazon DocumentDB resource that the tags are removed from. This value is an Amazon Resource Name (ARN).
    /// This member is required.
    public var resourceName: Swift.String?
    /// The tag key (name) of the tag to be removed.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceName: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceName = resourceName
        self.tagKeys = tagKeys
    }
}

/// Represents the input to [ResetDBClusterParameterGroup].
public struct ResetDBClusterParameterGroupInput: Swift.Sendable {
    /// The name of the cluster parameter group to reset.
    /// This member is required.
    public var dbClusterParameterGroupName: Swift.String?
    /// A list of parameter names in the cluster parameter group to reset to the default values. You can't use this parameter if the ResetAllParameters parameter is set to true.
    public var parameters: [DocDBClientTypes.Parameter]?
    /// A value that is set to true to reset all parameters in the cluster parameter group to their default values, and false otherwise. You can't use this parameter if there is a list of parameter names specified for the Parameters parameter.
    public var resetAllParameters: Swift.Bool?

    public init(
        dbClusterParameterGroupName: Swift.String? = nil,
        parameters: [DocDBClientTypes.Parameter]? = nil,
        resetAllParameters: Swift.Bool? = nil
    )
    {
        self.dbClusterParameterGroupName = dbClusterParameterGroupName
        self.parameters = parameters
        self.resetAllParameters = resetAllParameters
    }
}

/// Contains the name of a cluster parameter group.
public struct ResetDBClusterParameterGroupOutput: Swift.Sendable {
    /// The name of a cluster parameter group. Constraints:
    ///
    /// * Must be from 1 to 255 letters or numbers.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    ///
    ///
    /// This value is stored as a lowercase string.
    public var dbClusterParameterGroupName: Swift.String?

    public init(
        dbClusterParameterGroupName: Swift.String? = nil
    )
    {
        self.dbClusterParameterGroupName = dbClusterParameterGroupName
    }
}

/// The cluster doesn't have enough capacity for the current operation.
public struct InsufficientDBClusterCapacityFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InsufficientDBClusterCapacityFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The state of the snapshot doesn't allow deletion.
public struct InvalidDBSnapshotStateFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidDBSnapshotState" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// You cannot restore from a virtual private cloud (VPC) backup to a non-VPC DB instance.
public struct InvalidRestoreFault: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidRestoreFault" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Represents the input to [RestoreDBClusterFromSnapshot].
public struct RestoreDBClusterFromSnapshotInput: Swift.Sendable {
    /// Provides the list of Amazon EC2 Availability Zones that instances in the restored DB cluster can be created in.
    public var availabilityZones: [Swift.String]?
    /// The name of the cluster to create from the snapshot or cluster snapshot. This parameter isn't case sensitive. Constraints:
    ///
    /// * Must contain from 1 to 63 letters, numbers, or hyphens.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    ///
    ///
    /// Example: my-snapshot-id
    /// This member is required.
    public var dbClusterIdentifier: Swift.String?
    /// The name of the DB cluster parameter group to associate with this DB cluster. Type: String. Required: No. If this argument is omitted, the default DB cluster parameter group is used. If supplied, must match the name of an existing default DB cluster parameter group. The string must consist of from 1 to 255 letters, numbers or hyphens. Its first character must be a letter, and it cannot end with a hyphen or contain two consecutive hyphens.
    public var dbClusterParameterGroupName: Swift.String?
    /// The name of the subnet group to use for the new cluster. Constraints: If provided, must match the name of an existing DBSubnetGroup. Example: mySubnetgroup
    public var dbSubnetGroupName: Swift.String?
    /// Specifies whether this cluster can be deleted. If DeletionProtection is enabled, the cluster cannot be deleted unless it is modified and DeletionProtection is disabled. DeletionProtection protects clusters from being accidentally deleted.
    public var deletionProtection: Swift.Bool?
    /// A list of log types that must be enabled for exporting to Amazon CloudWatch Logs.
    public var enableCloudwatchLogsExports: [Swift.String]?
    /// The database engine to use for the new cluster. Default: The same as source. Constraint: Must be compatible with the engine of the source.
    /// This member is required.
    public var engine: Swift.String?
    /// The version of the database engine to use for the new cluster.
    public var engineVersion: Swift.String?
    /// The KMS key identifier to use when restoring an encrypted cluster from a DB snapshot or cluster snapshot. The KMS key identifier is the Amazon Resource Name (ARN) for the KMS encryption key. If you are restoring a cluster with the same Amazon Web Services account that owns the KMS encryption key used to encrypt the new cluster, then you can use the KMS key alias instead of the ARN for the KMS encryption key. If you do not specify a value for the KmsKeyId parameter, then the following occurs:
    ///
    /// * If the snapshot or cluster snapshot in SnapshotIdentifier is encrypted, then the restored cluster is encrypted using the KMS key that was used to encrypt the snapshot or the cluster snapshot.
    ///
    /// * If the snapshot or the cluster snapshot in SnapshotIdentifier is not encrypted, then the restored DB cluster is not encrypted.
    public var kmsKeyId: Swift.String?
    /// The port number on which the new cluster accepts connections. Constraints: Must be a value from 1150 to 65535. Default: The same port as the original cluster.
    public var port: Swift.Int?
    /// The identifier for the snapshot or cluster snapshot to restore from. You can use either the name or the Amazon Resource Name (ARN) to specify a cluster snapshot. However, you can use only the ARN to specify a snapshot. Constraints:
    ///
    /// * Must match the identifier of an existing snapshot.
    /// This member is required.
    public var snapshotIdentifier: Swift.String?
    /// The storage type to associate with the DB cluster. For information on storage types for Amazon DocumentDB clusters, see Cluster storage configurations in the Amazon DocumentDB Developer Guide. Valid values for storage type - standard | iopt1 Default value is standard
    public var storageType: Swift.String?
    /// The tags to be assigned to the restored cluster.
    public var tags: [DocDBClientTypes.Tag]?
    /// A list of virtual private cloud (VPC) security groups that the new cluster will belong to.
    public var vpcSecurityGroupIds: [Swift.String]?

    public init(
        availabilityZones: [Swift.String]? = nil,
        dbClusterIdentifier: Swift.String? = nil,
        dbClusterParameterGroupName: Swift.String? = nil,
        dbSubnetGroupName: Swift.String? = nil,
        deletionProtection: Swift.Bool? = nil,
        enableCloudwatchLogsExports: [Swift.String]? = nil,
        engine: Swift.String? = nil,
        engineVersion: Swift.String? = nil,
        kmsKeyId: Swift.String? = nil,
        port: Swift.Int? = nil,
        snapshotIdentifier: Swift.String? = nil,
        storageType: Swift.String? = nil,
        tags: [DocDBClientTypes.Tag]? = nil,
        vpcSecurityGroupIds: [Swift.String]? = nil
    )
    {
        self.availabilityZones = availabilityZones
        self.dbClusterIdentifier = dbClusterIdentifier
        self.dbClusterParameterGroupName = dbClusterParameterGroupName
        self.dbSubnetGroupName = dbSubnetGroupName
        self.deletionProtection = deletionProtection
        self.enableCloudwatchLogsExports = enableCloudwatchLogsExports
        self.engine = engine
        self.engineVersion = engineVersion
        self.kmsKeyId = kmsKeyId
        self.port = port
        self.snapshotIdentifier = snapshotIdentifier
        self.storageType = storageType
        self.tags = tags
        self.vpcSecurityGroupIds = vpcSecurityGroupIds
    }
}

public struct RestoreDBClusterFromSnapshotOutput: Swift.Sendable {
    /// Detailed information about a cluster.
    public var dbCluster: DocDBClientTypes.DBCluster?

    public init(
        dbCluster: DocDBClientTypes.DBCluster? = nil
    )
    {
        self.dbCluster = dbCluster
    }
}

/// Represents the input to [RestoreDBClusterToPointInTime].
public struct RestoreDBClusterToPointInTimeInput: Swift.Sendable {
    /// The name of the new cluster to be created. Constraints:
    ///
    /// * Must contain from 1 to 63 letters, numbers, or hyphens.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    /// This member is required.
    public var dbClusterIdentifier: Swift.String?
    /// The subnet group name to use for the new cluster. Constraints: If provided, must match the name of an existing DBSubnetGroup. Example: mySubnetgroup
    public var dbSubnetGroupName: Swift.String?
    /// Specifies whether this cluster can be deleted. If DeletionProtection is enabled, the cluster cannot be deleted unless it is modified and DeletionProtection is disabled. DeletionProtection protects clusters from being accidentally deleted.
    public var deletionProtection: Swift.Bool?
    /// A list of log types that must be enabled for exporting to Amazon CloudWatch Logs.
    public var enableCloudwatchLogsExports: [Swift.String]?
    /// The KMS key identifier to use when restoring an encrypted cluster from an encrypted cluster. The KMS key identifier is the Amazon Resource Name (ARN) for the KMS encryption key. If you are restoring a cluster with the same Amazon Web Services account that owns the KMS encryption key used to encrypt the new cluster, then you can use the KMS key alias instead of the ARN for the KMS encryption key. You can restore to a new cluster and encrypt the new cluster with an KMS key that is different from the KMS key used to encrypt the source cluster. The new DB cluster is encrypted with the KMS key identified by the KmsKeyId parameter. If you do not specify a value for the KmsKeyId parameter, then the following occurs:
    ///
    /// * If the cluster is encrypted, then the restored cluster is encrypted using the KMS key that was used to encrypt the source cluster.
    ///
    /// * If the cluster is not encrypted, then the restored cluster is not encrypted.
    ///
    ///
    /// If DBClusterIdentifier refers to a cluster that is not encrypted, then the restore request is rejected.
    public var kmsKeyId: Swift.String?
    /// The port number on which the new cluster accepts connections. Constraints: Must be a value from 1150 to 65535. Default: The default port for the engine.
    public var port: Swift.Int?
    /// The date and time to restore the cluster to. Valid values: A time in Universal Coordinated Time (UTC) format. Constraints:
    ///
    /// * Must be before the latest restorable time for the instance.
    ///
    /// * Must be specified if the UseLatestRestorableTime parameter is not provided.
    ///
    /// * Cannot be specified if the UseLatestRestorableTime parameter is true.
    ///
    /// * Cannot be specified if the RestoreType parameter is copy-on-write.
    ///
    ///
    /// Example: 2015-03-07T23:45:00Z
    public var restoreToTime: Foundation.Date?
    /// The type of restore to be performed. You can specify one of the following values:
    ///
    /// * full-copy - The new DB cluster is restored as a full copy of the source DB cluster.
    ///
    /// * copy-on-write - The new DB cluster is restored as a clone of the source DB cluster.
    ///
    ///
    /// Constraints: You can't specify copy-on-write if the engine version of the source DB cluster is earlier than 1.11. If you don't specify a RestoreType value, then the new DB cluster is restored as a full copy of the source DB cluster.
    public var restoreType: Swift.String?
    /// The identifier of the source cluster from which to restore. Constraints:
    ///
    /// * Must match the identifier of an existing DBCluster.
    /// This member is required.
    public var sourceDBClusterIdentifier: Swift.String?
    /// The storage type to associate with the DB cluster. For information on storage types for Amazon DocumentDB clusters, see Cluster storage configurations in the Amazon DocumentDB Developer Guide. Valid values for storage type - standard | iopt1 Default value is standard
    public var storageType: Swift.String?
    /// The tags to be assigned to the restored cluster.
    public var tags: [DocDBClientTypes.Tag]?
    /// A value that is set to true to restore the cluster to the latest restorable backup time, and false otherwise. Default: false Constraints: Cannot be specified if the RestoreToTime parameter is provided.
    public var useLatestRestorableTime: Swift.Bool?
    /// A list of VPC security groups that the new cluster belongs to.
    public var vpcSecurityGroupIds: [Swift.String]?

    public init(
        dbClusterIdentifier: Swift.String? = nil,
        dbSubnetGroupName: Swift.String? = nil,
        deletionProtection: Swift.Bool? = nil,
        enableCloudwatchLogsExports: [Swift.String]? = nil,
        kmsKeyId: Swift.String? = nil,
        port: Swift.Int? = nil,
        restoreToTime: Foundation.Date? = nil,
        restoreType: Swift.String? = nil,
        sourceDBClusterIdentifier: Swift.String? = nil,
        storageType: Swift.String? = nil,
        tags: [DocDBClientTypes.Tag]? = nil,
        useLatestRestorableTime: Swift.Bool? = nil,
        vpcSecurityGroupIds: [Swift.String]? = nil
    )
    {
        self.dbClusterIdentifier = dbClusterIdentifier
        self.dbSubnetGroupName = dbSubnetGroupName
        self.deletionProtection = deletionProtection
        self.enableCloudwatchLogsExports = enableCloudwatchLogsExports
        self.kmsKeyId = kmsKeyId
        self.port = port
        self.restoreToTime = restoreToTime
        self.restoreType = restoreType
        self.sourceDBClusterIdentifier = sourceDBClusterIdentifier
        self.storageType = storageType
        self.tags = tags
        self.useLatestRestorableTime = useLatestRestorableTime
        self.vpcSecurityGroupIds = vpcSecurityGroupIds
    }
}

public struct RestoreDBClusterToPointInTimeOutput: Swift.Sendable {
    /// Detailed information about a cluster.
    public var dbCluster: DocDBClientTypes.DBCluster?

    public init(
        dbCluster: DocDBClientTypes.DBCluster? = nil
    )
    {
        self.dbCluster = dbCluster
    }
}

public struct StartDBClusterInput: Swift.Sendable {
    /// The identifier of the cluster to restart. Example: docdb-2019-05-28-15-24-52
    /// This member is required.
    public var dbClusterIdentifier: Swift.String?

    public init(
        dbClusterIdentifier: Swift.String? = nil
    )
    {
        self.dbClusterIdentifier = dbClusterIdentifier
    }
}

public struct StartDBClusterOutput: Swift.Sendable {
    /// Detailed information about a cluster.
    public var dbCluster: DocDBClientTypes.DBCluster?

    public init(
        dbCluster: DocDBClientTypes.DBCluster? = nil
    )
    {
        self.dbCluster = dbCluster
    }
}

public struct StopDBClusterInput: Swift.Sendable {
    /// The identifier of the cluster to stop. Example: docdb-2019-05-28-15-24-52
    /// This member is required.
    public var dbClusterIdentifier: Swift.String?

    public init(
        dbClusterIdentifier: Swift.String? = nil
    )
    {
        self.dbClusterIdentifier = dbClusterIdentifier
    }
}

public struct StopDBClusterOutput: Swift.Sendable {
    /// Detailed information about a cluster.
    public var dbCluster: DocDBClientTypes.DBCluster?

    public init(
        dbCluster: DocDBClientTypes.DBCluster? = nil
    )
    {
        self.dbCluster = dbCluster
    }
}

public struct SwitchoverGlobalClusterInput: Swift.Sendable {
    /// The identifier of the Amazon DocumentDB global database cluster to switch over. The identifier is the unique key assigned by the user when the cluster is created. In other words, it's the name of the global cluster. This parameter isn’t case-sensitive. Constraints:
    ///
    /// * Must match the identifier of an existing global cluster (Amazon DocumentDB global database).
    ///
    /// * Minimum length of 1. Maximum length of 255.
    ///
    ///
    /// Pattern: [A-Za-z][0-9A-Za-z-:._]*
    /// This member is required.
    public var globalClusterIdentifier: Swift.String?
    /// The identifier of the secondary Amazon DocumentDB cluster to promote to the new primary for the global database cluster. Use the Amazon Resource Name (ARN) for the identifier so that Amazon DocumentDB can locate the cluster in its Amazon Web Services region. Constraints:
    ///
    /// * Must match the identifier of an existing secondary cluster.
    ///
    /// * Minimum length of 1. Maximum length of 255.
    ///
    ///
    /// Pattern: [A-Za-z][0-9A-Za-z-:._]*
    /// This member is required.
    public var targetDbClusterIdentifier: Swift.String?

    public init(
        globalClusterIdentifier: Swift.String? = nil,
        targetDbClusterIdentifier: Swift.String? = nil
    )
    {
        self.globalClusterIdentifier = globalClusterIdentifier
        self.targetDbClusterIdentifier = targetDbClusterIdentifier
    }
}

public struct SwitchoverGlobalClusterOutput: Swift.Sendable {
    /// A data type representing an Amazon DocumentDB global cluster.
    public var globalCluster: DocDBClientTypes.GlobalCluster?

    public init(
        globalCluster: DocDBClientTypes.GlobalCluster? = nil
    )
    {
        self.globalCluster = globalCluster
    }
}

extension AddSourceIdentifierToSubscriptionInput {

    static func urlPathProvider(_ value: AddSourceIdentifierToSubscriptionInput) -> Swift.String? {
        return "/"
    }
}

extension AddTagsToResourceInput {

    static func urlPathProvider(_ value: AddTagsToResourceInput) -> Swift.String? {
        return "/"
    }
}

extension ApplyPendingMaintenanceActionInput {

    static func urlPathProvider(_ value: ApplyPendingMaintenanceActionInput) -> Swift.String? {
        return "/"
    }
}

extension CopyDBClusterParameterGroupInput {

    static func urlPathProvider(_ value: CopyDBClusterParameterGroupInput) -> Swift.String? {
        return "/"
    }
}

extension CopyDBClusterSnapshotInput {

    static func urlPathProvider(_ value: CopyDBClusterSnapshotInput) -> Swift.String? {
        return "/"
    }
}

extension CreateDBClusterInput {

    static func urlPathProvider(_ value: CreateDBClusterInput) -> Swift.String? {
        return "/"
    }
}

extension CreateDBClusterParameterGroupInput {

    static func urlPathProvider(_ value: CreateDBClusterParameterGroupInput) -> Swift.String? {
        return "/"
    }
}

extension CreateDBClusterSnapshotInput {

    static func urlPathProvider(_ value: CreateDBClusterSnapshotInput) -> Swift.String? {
        return "/"
    }
}

extension CreateDBInstanceInput {

    static func urlPathProvider(_ value: CreateDBInstanceInput) -> Swift.String? {
        return "/"
    }
}

extension CreateDBSubnetGroupInput {

    static func urlPathProvider(_ value: CreateDBSubnetGroupInput) -> Swift.String? {
        return "/"
    }
}

extension CreateEventSubscriptionInput {

    static func urlPathProvider(_ value: CreateEventSubscriptionInput) -> Swift.String? {
        return "/"
    }
}

extension CreateGlobalClusterInput {

    static func urlPathProvider(_ value: CreateGlobalClusterInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteDBClusterInput {

    static func urlPathProvider(_ value: DeleteDBClusterInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteDBClusterParameterGroupInput {

    static func urlPathProvider(_ value: DeleteDBClusterParameterGroupInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteDBClusterSnapshotInput {

    static func urlPathProvider(_ value: DeleteDBClusterSnapshotInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteDBInstanceInput {

    static func urlPathProvider(_ value: DeleteDBInstanceInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteDBSubnetGroupInput {

    static func urlPathProvider(_ value: DeleteDBSubnetGroupInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteEventSubscriptionInput {

    static func urlPathProvider(_ value: DeleteEventSubscriptionInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteGlobalClusterInput {

    static func urlPathProvider(_ value: DeleteGlobalClusterInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeCertificatesInput {

    static func urlPathProvider(_ value: DescribeCertificatesInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeDBClusterParameterGroupsInput {

    static func urlPathProvider(_ value: DescribeDBClusterParameterGroupsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeDBClusterParametersInput {

    static func urlPathProvider(_ value: DescribeDBClusterParametersInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeDBClustersInput {

    static func urlPathProvider(_ value: DescribeDBClustersInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeDBClusterSnapshotAttributesInput {

    static func urlPathProvider(_ value: DescribeDBClusterSnapshotAttributesInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeDBClusterSnapshotsInput {

    static func urlPathProvider(_ value: DescribeDBClusterSnapshotsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeDBEngineVersionsInput {

    static func urlPathProvider(_ value: DescribeDBEngineVersionsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeDBInstancesInput {

    static func urlPathProvider(_ value: DescribeDBInstancesInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeDBSubnetGroupsInput {

    static func urlPathProvider(_ value: DescribeDBSubnetGroupsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeEngineDefaultClusterParametersInput {

    static func urlPathProvider(_ value: DescribeEngineDefaultClusterParametersInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeEventCategoriesInput {

    static func urlPathProvider(_ value: DescribeEventCategoriesInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeEventsInput {

    static func urlPathProvider(_ value: DescribeEventsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeEventSubscriptionsInput {

    static func urlPathProvider(_ value: DescribeEventSubscriptionsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeGlobalClustersInput {

    static func urlPathProvider(_ value: DescribeGlobalClustersInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeOrderableDBInstanceOptionsInput {

    static func urlPathProvider(_ value: DescribeOrderableDBInstanceOptionsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribePendingMaintenanceActionsInput {

    static func urlPathProvider(_ value: DescribePendingMaintenanceActionsInput) -> Swift.String? {
        return "/"
    }
}

extension FailoverDBClusterInput {

    static func urlPathProvider(_ value: FailoverDBClusterInput) -> Swift.String? {
        return "/"
    }
}

extension FailoverGlobalClusterInput {

    static func urlPathProvider(_ value: FailoverGlobalClusterInput) -> Swift.String? {
        return "/"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        return "/"
    }
}

extension ModifyDBClusterInput {

    static func urlPathProvider(_ value: ModifyDBClusterInput) -> Swift.String? {
        return "/"
    }
}

extension ModifyDBClusterParameterGroupInput {

    static func urlPathProvider(_ value: ModifyDBClusterParameterGroupInput) -> Swift.String? {
        return "/"
    }
}

extension ModifyDBClusterSnapshotAttributeInput {

    static func urlPathProvider(_ value: ModifyDBClusterSnapshotAttributeInput) -> Swift.String? {
        return "/"
    }
}

extension ModifyDBInstanceInput {

    static func urlPathProvider(_ value: ModifyDBInstanceInput) -> Swift.String? {
        return "/"
    }
}

extension ModifyDBSubnetGroupInput {

    static func urlPathProvider(_ value: ModifyDBSubnetGroupInput) -> Swift.String? {
        return "/"
    }
}

extension ModifyEventSubscriptionInput {

    static func urlPathProvider(_ value: ModifyEventSubscriptionInput) -> Swift.String? {
        return "/"
    }
}

extension ModifyGlobalClusterInput {

    static func urlPathProvider(_ value: ModifyGlobalClusterInput) -> Swift.String? {
        return "/"
    }
}

extension RebootDBInstanceInput {

    static func urlPathProvider(_ value: RebootDBInstanceInput) -> Swift.String? {
        return "/"
    }
}

extension RemoveFromGlobalClusterInput {

    static func urlPathProvider(_ value: RemoveFromGlobalClusterInput) -> Swift.String? {
        return "/"
    }
}

extension RemoveSourceIdentifierFromSubscriptionInput {

    static func urlPathProvider(_ value: RemoveSourceIdentifierFromSubscriptionInput) -> Swift.String? {
        return "/"
    }
}

extension RemoveTagsFromResourceInput {

    static func urlPathProvider(_ value: RemoveTagsFromResourceInput) -> Swift.String? {
        return "/"
    }
}

extension ResetDBClusterParameterGroupInput {

    static func urlPathProvider(_ value: ResetDBClusterParameterGroupInput) -> Swift.String? {
        return "/"
    }
}

extension RestoreDBClusterFromSnapshotInput {

    static func urlPathProvider(_ value: RestoreDBClusterFromSnapshotInput) -> Swift.String? {
        return "/"
    }
}

extension RestoreDBClusterToPointInTimeInput {

    static func urlPathProvider(_ value: RestoreDBClusterToPointInTimeInput) -> Swift.String? {
        return "/"
    }
}

extension StartDBClusterInput {

    static func urlPathProvider(_ value: StartDBClusterInput) -> Swift.String? {
        return "/"
    }
}

extension StopDBClusterInput {

    static func urlPathProvider(_ value: StopDBClusterInput) -> Swift.String? {
        return "/"
    }
}

extension SwitchoverGlobalClusterInput {

    static func urlPathProvider(_ value: SwitchoverGlobalClusterInput) -> Swift.String? {
        return "/"
    }
}

extension AddSourceIdentifierToSubscriptionInput {

    static func write(value: AddSourceIdentifierToSubscriptionInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["SourceIdentifier"].write(value.sourceIdentifier)
        try writer["SubscriptionName"].write(value.subscriptionName)
        try writer["Action"].write("AddSourceIdentifierToSubscription")
        try writer["Version"].write("2014-10-31")
    }
}

extension AddTagsToResourceInput {

    static func write(value: AddTagsToResourceInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ResourceName"].write(value.resourceName)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DocDBClientTypes.Tag.write(value:to:), memberNodeInfo: "Tag", isFlattened: false)
        try writer["Action"].write("AddTagsToResource")
        try writer["Version"].write("2014-10-31")
    }
}

extension ApplyPendingMaintenanceActionInput {

    static func write(value: ApplyPendingMaintenanceActionInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplyAction"].write(value.applyAction)
        try writer["OptInType"].write(value.optInType)
        try writer["ResourceIdentifier"].write(value.resourceIdentifier)
        try writer["Action"].write("ApplyPendingMaintenanceAction")
        try writer["Version"].write("2014-10-31")
    }
}

extension CopyDBClusterParameterGroupInput {

    static func write(value: CopyDBClusterParameterGroupInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["SourceDBClusterParameterGroupIdentifier"].write(value.sourceDBClusterParameterGroupIdentifier)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DocDBClientTypes.Tag.write(value:to:), memberNodeInfo: "Tag", isFlattened: false)
        try writer["TargetDBClusterParameterGroupDescription"].write(value.targetDBClusterParameterGroupDescription)
        try writer["TargetDBClusterParameterGroupIdentifier"].write(value.targetDBClusterParameterGroupIdentifier)
        try writer["Action"].write("CopyDBClusterParameterGroup")
        try writer["Version"].write("2014-10-31")
    }
}

extension CopyDBClusterSnapshotInput {

    static func write(value: CopyDBClusterSnapshotInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["CopyTags"].write(value.copyTags)
        try writer["KmsKeyId"].write(value.kmsKeyId)
        try writer["PreSignedUrl"].write(value.preSignedUrl)
        try writer["SourceDBClusterSnapshotIdentifier"].write(value.sourceDBClusterSnapshotIdentifier)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DocDBClientTypes.Tag.write(value:to:), memberNodeInfo: "Tag", isFlattened: false)
        try writer["TargetDBClusterSnapshotIdentifier"].write(value.targetDBClusterSnapshotIdentifier)
        try writer["Action"].write("CopyDBClusterSnapshot")
        try writer["Version"].write("2014-10-31")
    }
}

extension CreateDBClusterInput {

    static func write(value: CreateDBClusterInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["AvailabilityZones"].writeList(value.availabilityZones, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "AvailabilityZone", isFlattened: false)
        try writer["BackupRetentionPeriod"].write(value.backupRetentionPeriod)
        try writer["DBClusterIdentifier"].write(value.dbClusterIdentifier)
        try writer["DBClusterParameterGroupName"].write(value.dbClusterParameterGroupName)
        try writer["DBSubnetGroupName"].write(value.dbSubnetGroupName)
        try writer["DeletionProtection"].write(value.deletionProtection)
        try writer["EnableCloudwatchLogsExports"].writeList(value.enableCloudwatchLogsExports, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Engine"].write(value.engine)
        try writer["EngineVersion"].write(value.engineVersion)
        try writer["GlobalClusterIdentifier"].write(value.globalClusterIdentifier)
        try writer["KmsKeyId"].write(value.kmsKeyId)
        try writer["ManageMasterUserPassword"].write(value.manageMasterUserPassword)
        try writer["MasterUserPassword"].write(value.masterUserPassword)
        try writer["MasterUserSecretKmsKeyId"].write(value.masterUserSecretKmsKeyId)
        try writer["MasterUsername"].write(value.masterUsername)
        try writer["Port"].write(value.port)
        try writer["PreSignedUrl"].write(value.preSignedUrl)
        try writer["PreferredBackupWindow"].write(value.preferredBackupWindow)
        try writer["PreferredMaintenanceWindow"].write(value.preferredMaintenanceWindow)
        try writer["StorageEncrypted"].write(value.storageEncrypted)
        try writer["StorageType"].write(value.storageType)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DocDBClientTypes.Tag.write(value:to:), memberNodeInfo: "Tag", isFlattened: false)
        try writer["VpcSecurityGroupIds"].writeList(value.vpcSecurityGroupIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "VpcSecurityGroupId", isFlattened: false)
        try writer["Action"].write("CreateDBCluster")
        try writer["Version"].write("2014-10-31")
    }
}

extension CreateDBClusterParameterGroupInput {

    static func write(value: CreateDBClusterParameterGroupInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterParameterGroupName"].write(value.dbClusterParameterGroupName)
        try writer["DBParameterGroupFamily"].write(value.dbParameterGroupFamily)
        try writer["Description"].write(value.description)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DocDBClientTypes.Tag.write(value:to:), memberNodeInfo: "Tag", isFlattened: false)
        try writer["Action"].write("CreateDBClusterParameterGroup")
        try writer["Version"].write("2014-10-31")
    }
}

extension CreateDBClusterSnapshotInput {

    static func write(value: CreateDBClusterSnapshotInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterIdentifier"].write(value.dbClusterIdentifier)
        try writer["DBClusterSnapshotIdentifier"].write(value.dbClusterSnapshotIdentifier)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DocDBClientTypes.Tag.write(value:to:), memberNodeInfo: "Tag", isFlattened: false)
        try writer["Action"].write("CreateDBClusterSnapshot")
        try writer["Version"].write("2014-10-31")
    }
}

extension CreateDBInstanceInput {

    static func write(value: CreateDBInstanceInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["AutoMinorVersionUpgrade"].write(value.autoMinorVersionUpgrade)
        try writer["AvailabilityZone"].write(value.availabilityZone)
        try writer["CACertificateIdentifier"].write(value.caCertificateIdentifier)
        try writer["CopyTagsToSnapshot"].write(value.copyTagsToSnapshot)
        try writer["DBClusterIdentifier"].write(value.dbClusterIdentifier)
        try writer["DBInstanceClass"].write(value.dbInstanceClass)
        try writer["DBInstanceIdentifier"].write(value.dbInstanceIdentifier)
        try writer["EnablePerformanceInsights"].write(value.enablePerformanceInsights)
        try writer["Engine"].write(value.engine)
        try writer["PerformanceInsightsKMSKeyId"].write(value.performanceInsightsKMSKeyId)
        try writer["PreferredMaintenanceWindow"].write(value.preferredMaintenanceWindow)
        try writer["PromotionTier"].write(value.promotionTier)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DocDBClientTypes.Tag.write(value:to:), memberNodeInfo: "Tag", isFlattened: false)
        try writer["Action"].write("CreateDBInstance")
        try writer["Version"].write("2014-10-31")
    }
}

extension CreateDBSubnetGroupInput {

    static func write(value: CreateDBSubnetGroupInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBSubnetGroupDescription"].write(value.dbSubnetGroupDescription)
        try writer["DBSubnetGroupName"].write(value.dbSubnetGroupName)
        try writer["SubnetIds"].writeList(value.subnetIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "SubnetIdentifier", isFlattened: false)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DocDBClientTypes.Tag.write(value:to:), memberNodeInfo: "Tag", isFlattened: false)
        try writer["Action"].write("CreateDBSubnetGroup")
        try writer["Version"].write("2014-10-31")
    }
}

extension CreateEventSubscriptionInput {

    static func write(value: CreateEventSubscriptionInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Enabled"].write(value.enabled)
        try writer["EventCategories"].writeList(value.eventCategories, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "EventCategory", isFlattened: false)
        try writer["SnsTopicArn"].write(value.snsTopicArn)
        try writer["SourceIds"].writeList(value.sourceIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "SourceId", isFlattened: false)
        try writer["SourceType"].write(value.sourceType)
        try writer["SubscriptionName"].write(value.subscriptionName)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DocDBClientTypes.Tag.write(value:to:), memberNodeInfo: "Tag", isFlattened: false)
        try writer["Action"].write("CreateEventSubscription")
        try writer["Version"].write("2014-10-31")
    }
}

extension CreateGlobalClusterInput {

    static func write(value: CreateGlobalClusterInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DatabaseName"].write(value.databaseName)
        try writer["DeletionProtection"].write(value.deletionProtection)
        try writer["Engine"].write(value.engine)
        try writer["EngineVersion"].write(value.engineVersion)
        try writer["GlobalClusterIdentifier"].write(value.globalClusterIdentifier)
        try writer["SourceDBClusterIdentifier"].write(value.sourceDBClusterIdentifier)
        try writer["StorageEncrypted"].write(value.storageEncrypted)
        try writer["Action"].write("CreateGlobalCluster")
        try writer["Version"].write("2014-10-31")
    }
}

extension DeleteDBClusterInput {

    static func write(value: DeleteDBClusterInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterIdentifier"].write(value.dbClusterIdentifier)
        try writer["FinalDBSnapshotIdentifier"].write(value.finalDBSnapshotIdentifier)
        try writer["SkipFinalSnapshot"].write(value.skipFinalSnapshot)
        try writer["Action"].write("DeleteDBCluster")
        try writer["Version"].write("2014-10-31")
    }
}

extension DeleteDBClusterParameterGroupInput {

    static func write(value: DeleteDBClusterParameterGroupInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterParameterGroupName"].write(value.dbClusterParameterGroupName)
        try writer["Action"].write("DeleteDBClusterParameterGroup")
        try writer["Version"].write("2014-10-31")
    }
}

extension DeleteDBClusterSnapshotInput {

    static func write(value: DeleteDBClusterSnapshotInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterSnapshotIdentifier"].write(value.dbClusterSnapshotIdentifier)
        try writer["Action"].write("DeleteDBClusterSnapshot")
        try writer["Version"].write("2014-10-31")
    }
}

extension DeleteDBInstanceInput {

    static func write(value: DeleteDBInstanceInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBInstanceIdentifier"].write(value.dbInstanceIdentifier)
        try writer["Action"].write("DeleteDBInstance")
        try writer["Version"].write("2014-10-31")
    }
}

extension DeleteDBSubnetGroupInput {

    static func write(value: DeleteDBSubnetGroupInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBSubnetGroupName"].write(value.dbSubnetGroupName)
        try writer["Action"].write("DeleteDBSubnetGroup")
        try writer["Version"].write("2014-10-31")
    }
}

extension DeleteEventSubscriptionInput {

    static func write(value: DeleteEventSubscriptionInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["SubscriptionName"].write(value.subscriptionName)
        try writer["Action"].write("DeleteEventSubscription")
        try writer["Version"].write("2014-10-31")
    }
}

extension DeleteGlobalClusterInput {

    static func write(value: DeleteGlobalClusterInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["GlobalClusterIdentifier"].write(value.globalClusterIdentifier)
        try writer["Action"].write("DeleteGlobalCluster")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeCertificatesInput {

    static func write(value: DescribeCertificatesInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["CertificateIdentifier"].write(value.certificateIdentifier)
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["Marker"].write(value.marker)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["Action"].write("DescribeCertificates")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeDBClusterParameterGroupsInput {

    static func write(value: DescribeDBClusterParameterGroupsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterParameterGroupName"].write(value.dbClusterParameterGroupName)
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["Marker"].write(value.marker)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["Action"].write("DescribeDBClusterParameterGroups")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeDBClusterParametersInput {

    static func write(value: DescribeDBClusterParametersInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterParameterGroupName"].write(value.dbClusterParameterGroupName)
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["Marker"].write(value.marker)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["Source"].write(value.source)
        try writer["Action"].write("DescribeDBClusterParameters")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeDBClustersInput {

    static func write(value: DescribeDBClustersInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterIdentifier"].write(value.dbClusterIdentifier)
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["Marker"].write(value.marker)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["Action"].write("DescribeDBClusters")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeDBClusterSnapshotAttributesInput {

    static func write(value: DescribeDBClusterSnapshotAttributesInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterSnapshotIdentifier"].write(value.dbClusterSnapshotIdentifier)
        try writer["Action"].write("DescribeDBClusterSnapshotAttributes")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeDBClusterSnapshotsInput {

    static func write(value: DescribeDBClusterSnapshotsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterIdentifier"].write(value.dbClusterIdentifier)
        try writer["DBClusterSnapshotIdentifier"].write(value.dbClusterSnapshotIdentifier)
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["IncludePublic"].write(value.includePublic)
        try writer["IncludeShared"].write(value.includeShared)
        try writer["Marker"].write(value.marker)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["SnapshotType"].write(value.snapshotType)
        try writer["Action"].write("DescribeDBClusterSnapshots")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeDBEngineVersionsInput {

    static func write(value: DescribeDBEngineVersionsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBParameterGroupFamily"].write(value.dbParameterGroupFamily)
        try writer["DefaultOnly"].write(value.defaultOnly)
        try writer["Engine"].write(value.engine)
        try writer["EngineVersion"].write(value.engineVersion)
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["ListSupportedCharacterSets"].write(value.listSupportedCharacterSets)
        try writer["ListSupportedTimezones"].write(value.listSupportedTimezones)
        try writer["Marker"].write(value.marker)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["Action"].write("DescribeDBEngineVersions")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeDBInstancesInput {

    static func write(value: DescribeDBInstancesInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBInstanceIdentifier"].write(value.dbInstanceIdentifier)
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["Marker"].write(value.marker)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["Action"].write("DescribeDBInstances")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeDBSubnetGroupsInput {

    static func write(value: DescribeDBSubnetGroupsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBSubnetGroupName"].write(value.dbSubnetGroupName)
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["Marker"].write(value.marker)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["Action"].write("DescribeDBSubnetGroups")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeEngineDefaultClusterParametersInput {

    static func write(value: DescribeEngineDefaultClusterParametersInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBParameterGroupFamily"].write(value.dbParameterGroupFamily)
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["Marker"].write(value.marker)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["Action"].write("DescribeEngineDefaultClusterParameters")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeEventCategoriesInput {

    static func write(value: DescribeEventCategoriesInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["SourceType"].write(value.sourceType)
        try writer["Action"].write("DescribeEventCategories")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeEventsInput {

    static func write(value: DescribeEventsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Duration"].write(value.duration)
        try writer["EndTime"].writeTimestamp(value.endTime, format: SmithyTimestamps.TimestampFormat.dateTime)
        try writer["EventCategories"].writeList(value.eventCategories, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "EventCategory", isFlattened: false)
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["Marker"].write(value.marker)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["SourceIdentifier"].write(value.sourceIdentifier)
        try writer["SourceType"].write(value.sourceType)
        try writer["StartTime"].writeTimestamp(value.startTime, format: SmithyTimestamps.TimestampFormat.dateTime)
        try writer["Action"].write("DescribeEvents")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeEventSubscriptionsInput {

    static func write(value: DescribeEventSubscriptionsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["Marker"].write(value.marker)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["SubscriptionName"].write(value.subscriptionName)
        try writer["Action"].write("DescribeEventSubscriptions")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeGlobalClustersInput {

    static func write(value: DescribeGlobalClustersInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["GlobalClusterIdentifier"].write(value.globalClusterIdentifier)
        try writer["Marker"].write(value.marker)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["Action"].write("DescribeGlobalClusters")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribeOrderableDBInstanceOptionsInput {

    static func write(value: DescribeOrderableDBInstanceOptionsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBInstanceClass"].write(value.dbInstanceClass)
        try writer["Engine"].write(value.engine)
        try writer["EngineVersion"].write(value.engineVersion)
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["LicenseModel"].write(value.licenseModel)
        try writer["Marker"].write(value.marker)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["Vpc"].write(value.vpc)
        try writer["Action"].write("DescribeOrderableDBInstanceOptions")
        try writer["Version"].write("2014-10-31")
    }
}

extension DescribePendingMaintenanceActionsInput {

    static func write(value: DescribePendingMaintenanceActionsInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["Marker"].write(value.marker)
        try writer["MaxRecords"].write(value.maxRecords)
        try writer["ResourceIdentifier"].write(value.resourceIdentifier)
        try writer["Action"].write("DescribePendingMaintenanceActions")
        try writer["Version"].write("2014-10-31")
    }
}

extension FailoverDBClusterInput {

    static func write(value: FailoverDBClusterInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterIdentifier"].write(value.dbClusterIdentifier)
        try writer["TargetDBInstanceIdentifier"].write(value.targetDBInstanceIdentifier)
        try writer["Action"].write("FailoverDBCluster")
        try writer["Version"].write("2014-10-31")
    }
}

extension FailoverGlobalClusterInput {

    static func write(value: FailoverGlobalClusterInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["AllowDataLoss"].write(value.allowDataLoss)
        try writer["GlobalClusterIdentifier"].write(value.globalClusterIdentifier)
        try writer["Switchover"].write(value.switchover)
        try writer["TargetDbClusterIdentifier"].write(value.targetDbClusterIdentifier)
        try writer["Action"].write("FailoverGlobalCluster")
        try writer["Version"].write("2014-10-31")
    }
}

extension ListTagsForResourceInput {

    static func write(value: ListTagsForResourceInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Filters"].writeList(value.filters, memberWritingClosure: DocDBClientTypes.Filter.write(value:to:), memberNodeInfo: "Filter", isFlattened: false)
        try writer["ResourceName"].write(value.resourceName)
        try writer["Action"].write("ListTagsForResource")
        try writer["Version"].write("2014-10-31")
    }
}

extension ModifyDBClusterInput {

    static func write(value: ModifyDBClusterInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["AllowMajorVersionUpgrade"].write(value.allowMajorVersionUpgrade)
        try writer["ApplyImmediately"].write(value.applyImmediately)
        try writer["BackupRetentionPeriod"].write(value.backupRetentionPeriod)
        try writer["CloudwatchLogsExportConfiguration"].write(value.cloudwatchLogsExportConfiguration, with: DocDBClientTypes.CloudwatchLogsExportConfiguration.write(value:to:))
        try writer["DBClusterIdentifier"].write(value.dbClusterIdentifier)
        try writer["DBClusterParameterGroupName"].write(value.dbClusterParameterGroupName)
        try writer["DeletionProtection"].write(value.deletionProtection)
        try writer["EngineVersion"].write(value.engineVersion)
        try writer["ManageMasterUserPassword"].write(value.manageMasterUserPassword)
        try writer["MasterUserPassword"].write(value.masterUserPassword)
        try writer["MasterUserSecretKmsKeyId"].write(value.masterUserSecretKmsKeyId)
        try writer["NewDBClusterIdentifier"].write(value.newDBClusterIdentifier)
        try writer["Port"].write(value.port)
        try writer["PreferredBackupWindow"].write(value.preferredBackupWindow)
        try writer["PreferredMaintenanceWindow"].write(value.preferredMaintenanceWindow)
        try writer["RotateMasterUserPassword"].write(value.rotateMasterUserPassword)
        try writer["StorageType"].write(value.storageType)
        try writer["VpcSecurityGroupIds"].writeList(value.vpcSecurityGroupIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "VpcSecurityGroupId", isFlattened: false)
        try writer["Action"].write("ModifyDBCluster")
        try writer["Version"].write("2014-10-31")
    }
}

extension ModifyDBClusterParameterGroupInput {

    static func write(value: ModifyDBClusterParameterGroupInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterParameterGroupName"].write(value.dbClusterParameterGroupName)
        try writer["Parameters"].writeList(value.parameters, memberWritingClosure: DocDBClientTypes.Parameter.write(value:to:), memberNodeInfo: "Parameter", isFlattened: false)
        try writer["Action"].write("ModifyDBClusterParameterGroup")
        try writer["Version"].write("2014-10-31")
    }
}

extension ModifyDBClusterSnapshotAttributeInput {

    static func write(value: ModifyDBClusterSnapshotAttributeInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["AttributeName"].write(value.attributeName)
        try writer["DBClusterSnapshotIdentifier"].write(value.dbClusterSnapshotIdentifier)
        try writer["ValuesToAdd"].writeList(value.valuesToAdd, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "AttributeValue", isFlattened: false)
        try writer["ValuesToRemove"].writeList(value.valuesToRemove, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "AttributeValue", isFlattened: false)
        try writer["Action"].write("ModifyDBClusterSnapshotAttribute")
        try writer["Version"].write("2014-10-31")
    }
}

extension ModifyDBInstanceInput {

    static func write(value: ModifyDBInstanceInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ApplyImmediately"].write(value.applyImmediately)
        try writer["AutoMinorVersionUpgrade"].write(value.autoMinorVersionUpgrade)
        try writer["CACertificateIdentifier"].write(value.caCertificateIdentifier)
        try writer["CertificateRotationRestart"].write(value.certificateRotationRestart)
        try writer["CopyTagsToSnapshot"].write(value.copyTagsToSnapshot)
        try writer["DBInstanceClass"].write(value.dbInstanceClass)
        try writer["DBInstanceIdentifier"].write(value.dbInstanceIdentifier)
        try writer["EnablePerformanceInsights"].write(value.enablePerformanceInsights)
        try writer["NewDBInstanceIdentifier"].write(value.newDBInstanceIdentifier)
        try writer["PerformanceInsightsKMSKeyId"].write(value.performanceInsightsKMSKeyId)
        try writer["PreferredMaintenanceWindow"].write(value.preferredMaintenanceWindow)
        try writer["PromotionTier"].write(value.promotionTier)
        try writer["Action"].write("ModifyDBInstance")
        try writer["Version"].write("2014-10-31")
    }
}

extension ModifyDBSubnetGroupInput {

    static func write(value: ModifyDBSubnetGroupInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBSubnetGroupDescription"].write(value.dbSubnetGroupDescription)
        try writer["DBSubnetGroupName"].write(value.dbSubnetGroupName)
        try writer["SubnetIds"].writeList(value.subnetIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "SubnetIdentifier", isFlattened: false)
        try writer["Action"].write("ModifyDBSubnetGroup")
        try writer["Version"].write("2014-10-31")
    }
}

extension ModifyEventSubscriptionInput {

    static func write(value: ModifyEventSubscriptionInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Enabled"].write(value.enabled)
        try writer["EventCategories"].writeList(value.eventCategories, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "EventCategory", isFlattened: false)
        try writer["SnsTopicArn"].write(value.snsTopicArn)
        try writer["SourceType"].write(value.sourceType)
        try writer["SubscriptionName"].write(value.subscriptionName)
        try writer["Action"].write("ModifyEventSubscription")
        try writer["Version"].write("2014-10-31")
    }
}

extension ModifyGlobalClusterInput {

    static func write(value: ModifyGlobalClusterInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DeletionProtection"].write(value.deletionProtection)
        try writer["GlobalClusterIdentifier"].write(value.globalClusterIdentifier)
        try writer["NewGlobalClusterIdentifier"].write(value.newGlobalClusterIdentifier)
        try writer["Action"].write("ModifyGlobalCluster")
        try writer["Version"].write("2014-10-31")
    }
}

extension RebootDBInstanceInput {

    static func write(value: RebootDBInstanceInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBInstanceIdentifier"].write(value.dbInstanceIdentifier)
        try writer["ForceFailover"].write(value.forceFailover)
        try writer["Action"].write("RebootDBInstance")
        try writer["Version"].write("2014-10-31")
    }
}

extension RemoveFromGlobalClusterInput {

    static func write(value: RemoveFromGlobalClusterInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DbClusterIdentifier"].write(value.dbClusterIdentifier)
        try writer["GlobalClusterIdentifier"].write(value.globalClusterIdentifier)
        try writer["Action"].write("RemoveFromGlobalCluster")
        try writer["Version"].write("2014-10-31")
    }
}

extension RemoveSourceIdentifierFromSubscriptionInput {

    static func write(value: RemoveSourceIdentifierFromSubscriptionInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["SourceIdentifier"].write(value.sourceIdentifier)
        try writer["SubscriptionName"].write(value.subscriptionName)
        try writer["Action"].write("RemoveSourceIdentifierFromSubscription")
        try writer["Version"].write("2014-10-31")
    }
}

extension RemoveTagsFromResourceInput {

    static func write(value: RemoveTagsFromResourceInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["ResourceName"].write(value.resourceName)
        try writer["TagKeys"].writeList(value.tagKeys, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Action"].write("RemoveTagsFromResource")
        try writer["Version"].write("2014-10-31")
    }
}

extension ResetDBClusterParameterGroupInput {

    static func write(value: ResetDBClusterParameterGroupInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterParameterGroupName"].write(value.dbClusterParameterGroupName)
        try writer["Parameters"].writeList(value.parameters, memberWritingClosure: DocDBClientTypes.Parameter.write(value:to:), memberNodeInfo: "Parameter", isFlattened: false)
        try writer["ResetAllParameters"].write(value.resetAllParameters)
        try writer["Action"].write("ResetDBClusterParameterGroup")
        try writer["Version"].write("2014-10-31")
    }
}

extension RestoreDBClusterFromSnapshotInput {

    static func write(value: RestoreDBClusterFromSnapshotInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["AvailabilityZones"].writeList(value.availabilityZones, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "AvailabilityZone", isFlattened: false)
        try writer["DBClusterIdentifier"].write(value.dbClusterIdentifier)
        try writer["DBClusterParameterGroupName"].write(value.dbClusterParameterGroupName)
        try writer["DBSubnetGroupName"].write(value.dbSubnetGroupName)
        try writer["DeletionProtection"].write(value.deletionProtection)
        try writer["EnableCloudwatchLogsExports"].writeList(value.enableCloudwatchLogsExports, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["Engine"].write(value.engine)
        try writer["EngineVersion"].write(value.engineVersion)
        try writer["KmsKeyId"].write(value.kmsKeyId)
        try writer["Port"].write(value.port)
        try writer["SnapshotIdentifier"].write(value.snapshotIdentifier)
        try writer["StorageType"].write(value.storageType)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DocDBClientTypes.Tag.write(value:to:), memberNodeInfo: "Tag", isFlattened: false)
        try writer["VpcSecurityGroupIds"].writeList(value.vpcSecurityGroupIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "VpcSecurityGroupId", isFlattened: false)
        try writer["Action"].write("RestoreDBClusterFromSnapshot")
        try writer["Version"].write("2014-10-31")
    }
}

extension RestoreDBClusterToPointInTimeInput {

    static func write(value: RestoreDBClusterToPointInTimeInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterIdentifier"].write(value.dbClusterIdentifier)
        try writer["DBSubnetGroupName"].write(value.dbSubnetGroupName)
        try writer["DeletionProtection"].write(value.deletionProtection)
        try writer["EnableCloudwatchLogsExports"].writeList(value.enableCloudwatchLogsExports, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["KmsKeyId"].write(value.kmsKeyId)
        try writer["Port"].write(value.port)
        try writer["RestoreToTime"].writeTimestamp(value.restoreToTime, format: SmithyTimestamps.TimestampFormat.dateTime)
        try writer["RestoreType"].write(value.restoreType)
        try writer["SourceDBClusterIdentifier"].write(value.sourceDBClusterIdentifier)
        try writer["StorageType"].write(value.storageType)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: DocDBClientTypes.Tag.write(value:to:), memberNodeInfo: "Tag", isFlattened: false)
        try writer["UseLatestRestorableTime"].write(value.useLatestRestorableTime)
        try writer["VpcSecurityGroupIds"].writeList(value.vpcSecurityGroupIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "VpcSecurityGroupId", isFlattened: false)
        try writer["Action"].write("RestoreDBClusterToPointInTime")
        try writer["Version"].write("2014-10-31")
    }
}

extension StartDBClusterInput {

    static func write(value: StartDBClusterInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterIdentifier"].write(value.dbClusterIdentifier)
        try writer["Action"].write("StartDBCluster")
        try writer["Version"].write("2014-10-31")
    }
}

extension StopDBClusterInput {

    static func write(value: StopDBClusterInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DBClusterIdentifier"].write(value.dbClusterIdentifier)
        try writer["Action"].write("StopDBCluster")
        try writer["Version"].write("2014-10-31")
    }
}

extension SwitchoverGlobalClusterInput {

    static func write(value: SwitchoverGlobalClusterInput?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["GlobalClusterIdentifier"].write(value.globalClusterIdentifier)
        try writer["TargetDbClusterIdentifier"].write(value.targetDbClusterIdentifier)
        try writer["Action"].write("SwitchoverGlobalCluster")
        try writer["Version"].write("2014-10-31")
    }
}

extension AddSourceIdentifierToSubscriptionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AddSourceIdentifierToSubscriptionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["AddSourceIdentifierToSubscriptionResult"]
        var value = AddSourceIdentifierToSubscriptionOutput()
        value.eventSubscription = try reader["EventSubscription"].readIfPresent(with: DocDBClientTypes.EventSubscription.read(from:))
        return value
    }
}

extension AddTagsToResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AddTagsToResourceOutput {
        return AddTagsToResourceOutput()
    }
}

extension ApplyPendingMaintenanceActionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ApplyPendingMaintenanceActionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ApplyPendingMaintenanceActionResult"]
        var value = ApplyPendingMaintenanceActionOutput()
        value.resourcePendingMaintenanceActions = try reader["ResourcePendingMaintenanceActions"].readIfPresent(with: DocDBClientTypes.ResourcePendingMaintenanceActions.read(from:))
        return value
    }
}

extension CopyDBClusterParameterGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CopyDBClusterParameterGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CopyDBClusterParameterGroupResult"]
        var value = CopyDBClusterParameterGroupOutput()
        value.dbClusterParameterGroup = try reader["DBClusterParameterGroup"].readIfPresent(with: DocDBClientTypes.DBClusterParameterGroup.read(from:))
        return value
    }
}

extension CopyDBClusterSnapshotOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CopyDBClusterSnapshotOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CopyDBClusterSnapshotResult"]
        var value = CopyDBClusterSnapshotOutput()
        value.dbClusterSnapshot = try reader["DBClusterSnapshot"].readIfPresent(with: DocDBClientTypes.DBClusterSnapshot.read(from:))
        return value
    }
}

extension CreateDBClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateDBClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CreateDBClusterResult"]
        var value = CreateDBClusterOutput()
        value.dbCluster = try reader["DBCluster"].readIfPresent(with: DocDBClientTypes.DBCluster.read(from:))
        return value
    }
}

extension CreateDBClusterParameterGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateDBClusterParameterGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CreateDBClusterParameterGroupResult"]
        var value = CreateDBClusterParameterGroupOutput()
        value.dbClusterParameterGroup = try reader["DBClusterParameterGroup"].readIfPresent(with: DocDBClientTypes.DBClusterParameterGroup.read(from:))
        return value
    }
}

extension CreateDBClusterSnapshotOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateDBClusterSnapshotOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CreateDBClusterSnapshotResult"]
        var value = CreateDBClusterSnapshotOutput()
        value.dbClusterSnapshot = try reader["DBClusterSnapshot"].readIfPresent(with: DocDBClientTypes.DBClusterSnapshot.read(from:))
        return value
    }
}

extension CreateDBInstanceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateDBInstanceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CreateDBInstanceResult"]
        var value = CreateDBInstanceOutput()
        value.dbInstance = try reader["DBInstance"].readIfPresent(with: DocDBClientTypes.DBInstance.read(from:))
        return value
    }
}

extension CreateDBSubnetGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateDBSubnetGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CreateDBSubnetGroupResult"]
        var value = CreateDBSubnetGroupOutput()
        value.dbSubnetGroup = try reader["DBSubnetGroup"].readIfPresent(with: DocDBClientTypes.DBSubnetGroup.read(from:))
        return value
    }
}

extension CreateEventSubscriptionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateEventSubscriptionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CreateEventSubscriptionResult"]
        var value = CreateEventSubscriptionOutput()
        value.eventSubscription = try reader["EventSubscription"].readIfPresent(with: DocDBClientTypes.EventSubscription.read(from:))
        return value
    }
}

extension CreateGlobalClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateGlobalClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["CreateGlobalClusterResult"]
        var value = CreateGlobalClusterOutput()
        value.globalCluster = try reader["GlobalCluster"].readIfPresent(with: DocDBClientTypes.GlobalCluster.read(from:))
        return value
    }
}

extension DeleteDBClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteDBClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DeleteDBClusterResult"]
        var value = DeleteDBClusterOutput()
        value.dbCluster = try reader["DBCluster"].readIfPresent(with: DocDBClientTypes.DBCluster.read(from:))
        return value
    }
}

extension DeleteDBClusterParameterGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteDBClusterParameterGroupOutput {
        return DeleteDBClusterParameterGroupOutput()
    }
}

extension DeleteDBClusterSnapshotOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteDBClusterSnapshotOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DeleteDBClusterSnapshotResult"]
        var value = DeleteDBClusterSnapshotOutput()
        value.dbClusterSnapshot = try reader["DBClusterSnapshot"].readIfPresent(with: DocDBClientTypes.DBClusterSnapshot.read(from:))
        return value
    }
}

extension DeleteDBInstanceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteDBInstanceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DeleteDBInstanceResult"]
        var value = DeleteDBInstanceOutput()
        value.dbInstance = try reader["DBInstance"].readIfPresent(with: DocDBClientTypes.DBInstance.read(from:))
        return value
    }
}

extension DeleteDBSubnetGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteDBSubnetGroupOutput {
        return DeleteDBSubnetGroupOutput()
    }
}

extension DeleteEventSubscriptionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteEventSubscriptionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DeleteEventSubscriptionResult"]
        var value = DeleteEventSubscriptionOutput()
        value.eventSubscription = try reader["EventSubscription"].readIfPresent(with: DocDBClientTypes.EventSubscription.read(from:))
        return value
    }
}

extension DeleteGlobalClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteGlobalClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DeleteGlobalClusterResult"]
        var value = DeleteGlobalClusterOutput()
        value.globalCluster = try reader["GlobalCluster"].readIfPresent(with: DocDBClientTypes.GlobalCluster.read(from:))
        return value
    }
}

extension DescribeCertificatesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeCertificatesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeCertificatesResult"]
        var value = DescribeCertificatesOutput()
        value.certificates = try reader["Certificates"].readListIfPresent(memberReadingClosure: DocDBClientTypes.Certificate.read(from:), memberNodeInfo: "Certificate", isFlattened: false)
        value.marker = try reader["Marker"].readIfPresent()
        return value
    }
}

extension DescribeDBClusterParameterGroupsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDBClusterParameterGroupsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeDBClusterParameterGroupsResult"]
        var value = DescribeDBClusterParameterGroupsOutput()
        value.dbClusterParameterGroups = try reader["DBClusterParameterGroups"].readListIfPresent(memberReadingClosure: DocDBClientTypes.DBClusterParameterGroup.read(from:), memberNodeInfo: "DBClusterParameterGroup", isFlattened: false)
        value.marker = try reader["Marker"].readIfPresent()
        return value
    }
}

extension DescribeDBClusterParametersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDBClusterParametersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeDBClusterParametersResult"]
        var value = DescribeDBClusterParametersOutput()
        value.marker = try reader["Marker"].readIfPresent()
        value.parameters = try reader["Parameters"].readListIfPresent(memberReadingClosure: DocDBClientTypes.Parameter.read(from:), memberNodeInfo: "Parameter", isFlattened: false)
        return value
    }
}

extension DescribeDBClustersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDBClustersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeDBClustersResult"]
        var value = DescribeDBClustersOutput()
        value.dbClusters = try reader["DBClusters"].readListIfPresent(memberReadingClosure: DocDBClientTypes.DBCluster.read(from:), memberNodeInfo: "DBCluster", isFlattened: false)
        value.marker = try reader["Marker"].readIfPresent()
        return value
    }
}

extension DescribeDBClusterSnapshotAttributesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDBClusterSnapshotAttributesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeDBClusterSnapshotAttributesResult"]
        var value = DescribeDBClusterSnapshotAttributesOutput()
        value.dbClusterSnapshotAttributesResult = try reader["DBClusterSnapshotAttributesResult"].readIfPresent(with: DocDBClientTypes.DBClusterSnapshotAttributesResult.read(from:))
        return value
    }
}

extension DescribeDBClusterSnapshotsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDBClusterSnapshotsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeDBClusterSnapshotsResult"]
        var value = DescribeDBClusterSnapshotsOutput()
        value.dbClusterSnapshots = try reader["DBClusterSnapshots"].readListIfPresent(memberReadingClosure: DocDBClientTypes.DBClusterSnapshot.read(from:), memberNodeInfo: "DBClusterSnapshot", isFlattened: false)
        value.marker = try reader["Marker"].readIfPresent()
        return value
    }
}

extension DescribeDBEngineVersionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDBEngineVersionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeDBEngineVersionsResult"]
        var value = DescribeDBEngineVersionsOutput()
        value.dbEngineVersions = try reader["DBEngineVersions"].readListIfPresent(memberReadingClosure: DocDBClientTypes.DBEngineVersion.read(from:), memberNodeInfo: "DBEngineVersion", isFlattened: false)
        value.marker = try reader["Marker"].readIfPresent()
        return value
    }
}

extension DescribeDBInstancesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDBInstancesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeDBInstancesResult"]
        var value = DescribeDBInstancesOutput()
        value.dbInstances = try reader["DBInstances"].readListIfPresent(memberReadingClosure: DocDBClientTypes.DBInstance.read(from:), memberNodeInfo: "DBInstance", isFlattened: false)
        value.marker = try reader["Marker"].readIfPresent()
        return value
    }
}

extension DescribeDBSubnetGroupsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeDBSubnetGroupsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeDBSubnetGroupsResult"]
        var value = DescribeDBSubnetGroupsOutput()
        value.dbSubnetGroups = try reader["DBSubnetGroups"].readListIfPresent(memberReadingClosure: DocDBClientTypes.DBSubnetGroup.read(from:), memberNodeInfo: "DBSubnetGroup", isFlattened: false)
        value.marker = try reader["Marker"].readIfPresent()
        return value
    }
}

extension DescribeEngineDefaultClusterParametersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeEngineDefaultClusterParametersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeEngineDefaultClusterParametersResult"]
        var value = DescribeEngineDefaultClusterParametersOutput()
        value.engineDefaults = try reader["EngineDefaults"].readIfPresent(with: DocDBClientTypes.EngineDefaults.read(from:))
        return value
    }
}

extension DescribeEventCategoriesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeEventCategoriesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeEventCategoriesResult"]
        var value = DescribeEventCategoriesOutput()
        value.eventCategoriesMapList = try reader["EventCategoriesMapList"].readListIfPresent(memberReadingClosure: DocDBClientTypes.EventCategoriesMap.read(from:), memberNodeInfo: "EventCategoriesMap", isFlattened: false)
        return value
    }
}

extension DescribeEventsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeEventsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeEventsResult"]
        var value = DescribeEventsOutput()
        value.events = try reader["Events"].readListIfPresent(memberReadingClosure: DocDBClientTypes.Event.read(from:), memberNodeInfo: "Event", isFlattened: false)
        value.marker = try reader["Marker"].readIfPresent()
        return value
    }
}

extension DescribeEventSubscriptionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeEventSubscriptionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeEventSubscriptionsResult"]
        var value = DescribeEventSubscriptionsOutput()
        value.eventSubscriptionsList = try reader["EventSubscriptionsList"].readListIfPresent(memberReadingClosure: DocDBClientTypes.EventSubscription.read(from:), memberNodeInfo: "EventSubscription", isFlattened: false)
        value.marker = try reader["Marker"].readIfPresent()
        return value
    }
}

extension DescribeGlobalClustersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeGlobalClustersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeGlobalClustersResult"]
        var value = DescribeGlobalClustersOutput()
        value.globalClusters = try reader["GlobalClusters"].readListIfPresent(memberReadingClosure: DocDBClientTypes.GlobalCluster.read(from:), memberNodeInfo: "GlobalClusterMember", isFlattened: false)
        value.marker = try reader["Marker"].readIfPresent()
        return value
    }
}

extension DescribeOrderableDBInstanceOptionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeOrderableDBInstanceOptionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribeOrderableDBInstanceOptionsResult"]
        var value = DescribeOrderableDBInstanceOptionsOutput()
        value.marker = try reader["Marker"].readIfPresent()
        value.orderableDBInstanceOptions = try reader["OrderableDBInstanceOptions"].readListIfPresent(memberReadingClosure: DocDBClientTypes.OrderableDBInstanceOption.read(from:), memberNodeInfo: "OrderableDBInstanceOption", isFlattened: false)
        return value
    }
}

extension DescribePendingMaintenanceActionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribePendingMaintenanceActionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["DescribePendingMaintenanceActionsResult"]
        var value = DescribePendingMaintenanceActionsOutput()
        value.marker = try reader["Marker"].readIfPresent()
        value.pendingMaintenanceActions = try reader["PendingMaintenanceActions"].readListIfPresent(memberReadingClosure: DocDBClientTypes.ResourcePendingMaintenanceActions.read(from:), memberNodeInfo: "ResourcePendingMaintenanceActions", isFlattened: false)
        return value
    }
}

extension FailoverDBClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> FailoverDBClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["FailoverDBClusterResult"]
        var value = FailoverDBClusterOutput()
        value.dbCluster = try reader["DBCluster"].readIfPresent(with: DocDBClientTypes.DBCluster.read(from:))
        return value
    }
}

extension FailoverGlobalClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> FailoverGlobalClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["FailoverGlobalClusterResult"]
        var value = FailoverGlobalClusterOutput()
        value.globalCluster = try reader["GlobalCluster"].readIfPresent(with: DocDBClientTypes.GlobalCluster.read(from:))
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ListTagsForResourceResult"]
        var value = ListTagsForResourceOutput()
        value.tagList = try reader["TagList"].readListIfPresent(memberReadingClosure: DocDBClientTypes.Tag.read(from:), memberNodeInfo: "Tag", isFlattened: false)
        return value
    }
}

extension ModifyDBClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ModifyDBClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ModifyDBClusterResult"]
        var value = ModifyDBClusterOutput()
        value.dbCluster = try reader["DBCluster"].readIfPresent(with: DocDBClientTypes.DBCluster.read(from:))
        return value
    }
}

extension ModifyDBClusterParameterGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ModifyDBClusterParameterGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ModifyDBClusterParameterGroupResult"]
        var value = ModifyDBClusterParameterGroupOutput()
        value.dbClusterParameterGroupName = try reader["DBClusterParameterGroupName"].readIfPresent()
        return value
    }
}

extension ModifyDBClusterSnapshotAttributeOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ModifyDBClusterSnapshotAttributeOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ModifyDBClusterSnapshotAttributeResult"]
        var value = ModifyDBClusterSnapshotAttributeOutput()
        value.dbClusterSnapshotAttributesResult = try reader["DBClusterSnapshotAttributesResult"].readIfPresent(with: DocDBClientTypes.DBClusterSnapshotAttributesResult.read(from:))
        return value
    }
}

extension ModifyDBInstanceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ModifyDBInstanceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ModifyDBInstanceResult"]
        var value = ModifyDBInstanceOutput()
        value.dbInstance = try reader["DBInstance"].readIfPresent(with: DocDBClientTypes.DBInstance.read(from:))
        return value
    }
}

extension ModifyDBSubnetGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ModifyDBSubnetGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ModifyDBSubnetGroupResult"]
        var value = ModifyDBSubnetGroupOutput()
        value.dbSubnetGroup = try reader["DBSubnetGroup"].readIfPresent(with: DocDBClientTypes.DBSubnetGroup.read(from:))
        return value
    }
}

extension ModifyEventSubscriptionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ModifyEventSubscriptionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ModifyEventSubscriptionResult"]
        var value = ModifyEventSubscriptionOutput()
        value.eventSubscription = try reader["EventSubscription"].readIfPresent(with: DocDBClientTypes.EventSubscription.read(from:))
        return value
    }
}

extension ModifyGlobalClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ModifyGlobalClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ModifyGlobalClusterResult"]
        var value = ModifyGlobalClusterOutput()
        value.globalCluster = try reader["GlobalCluster"].readIfPresent(with: DocDBClientTypes.GlobalCluster.read(from:))
        return value
    }
}

extension RebootDBInstanceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RebootDBInstanceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["RebootDBInstanceResult"]
        var value = RebootDBInstanceOutput()
        value.dbInstance = try reader["DBInstance"].readIfPresent(with: DocDBClientTypes.DBInstance.read(from:))
        return value
    }
}

extension RemoveFromGlobalClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RemoveFromGlobalClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["RemoveFromGlobalClusterResult"]
        var value = RemoveFromGlobalClusterOutput()
        value.globalCluster = try reader["GlobalCluster"].readIfPresent(with: DocDBClientTypes.GlobalCluster.read(from:))
        return value
    }
}

extension RemoveSourceIdentifierFromSubscriptionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RemoveSourceIdentifierFromSubscriptionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["RemoveSourceIdentifierFromSubscriptionResult"]
        var value = RemoveSourceIdentifierFromSubscriptionOutput()
        value.eventSubscription = try reader["EventSubscription"].readIfPresent(with: DocDBClientTypes.EventSubscription.read(from:))
        return value
    }
}

extension RemoveTagsFromResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RemoveTagsFromResourceOutput {
        return RemoveTagsFromResourceOutput()
    }
}

extension ResetDBClusterParameterGroupOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ResetDBClusterParameterGroupOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["ResetDBClusterParameterGroupResult"]
        var value = ResetDBClusterParameterGroupOutput()
        value.dbClusterParameterGroupName = try reader["DBClusterParameterGroupName"].readIfPresent()
        return value
    }
}

extension RestoreDBClusterFromSnapshotOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RestoreDBClusterFromSnapshotOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["RestoreDBClusterFromSnapshotResult"]
        var value = RestoreDBClusterFromSnapshotOutput()
        value.dbCluster = try reader["DBCluster"].readIfPresent(with: DocDBClientTypes.DBCluster.read(from:))
        return value
    }
}

extension RestoreDBClusterToPointInTimeOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RestoreDBClusterToPointInTimeOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["RestoreDBClusterToPointInTimeResult"]
        var value = RestoreDBClusterToPointInTimeOutput()
        value.dbCluster = try reader["DBCluster"].readIfPresent(with: DocDBClientTypes.DBCluster.read(from:))
        return value
    }
}

extension StartDBClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartDBClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["StartDBClusterResult"]
        var value = StartDBClusterOutput()
        value.dbCluster = try reader["DBCluster"].readIfPresent(with: DocDBClientTypes.DBCluster.read(from:))
        return value
    }
}

extension StopDBClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopDBClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["StopDBClusterResult"]
        var value = StopDBClusterOutput()
        value.dbCluster = try reader["DBCluster"].readIfPresent(with: DocDBClientTypes.DBCluster.read(from:))
        return value
    }
}

extension SwitchoverGlobalClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SwitchoverGlobalClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let reader = responseReader["SwitchoverGlobalClusterResult"]
        var value = SwitchoverGlobalClusterOutput()
        value.globalCluster = try reader["GlobalCluster"].readIfPresent(with: DocDBClientTypes.GlobalCluster.read(from:))
        return value
    }
}

enum AddSourceIdentifierToSubscriptionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "SourceNotFound": return try SourceNotFoundFault.makeError(baseError: baseError)
            case "SubscriptionNotFound": return try SubscriptionNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum AddTagsToResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "DBInstanceNotFound": return try DBInstanceNotFoundFault.makeError(baseError: baseError)
            case "DBSnapshotNotFound": return try DBSnapshotNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ApplyPendingMaintenanceActionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            case "InvalidDBInstanceState": return try InvalidDBInstanceStateFault.makeError(baseError: baseError)
            case "ResourceNotFoundFault": return try ResourceNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CopyDBClusterParameterGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBParameterGroupAlreadyExists": return try DBParameterGroupAlreadyExistsFault.makeError(baseError: baseError)
            case "DBParameterGroupNotFound": return try DBParameterGroupNotFoundFault.makeError(baseError: baseError)
            case "DBParameterGroupQuotaExceeded": return try DBParameterGroupQuotaExceededFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CopyDBClusterSnapshotOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterSnapshotAlreadyExistsFault": return try DBClusterSnapshotAlreadyExistsFault.makeError(baseError: baseError)
            case "DBClusterSnapshotNotFoundFault": return try DBClusterSnapshotNotFoundFault.makeError(baseError: baseError)
            case "InvalidDBClusterSnapshotStateFault": return try InvalidDBClusterSnapshotStateFault.makeError(baseError: baseError)
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            case "KMSKeyNotAccessibleFault": return try KMSKeyNotAccessibleFault.makeError(baseError: baseError)
            case "SnapshotQuotaExceeded": return try SnapshotQuotaExceededFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateDBClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterAlreadyExistsFault": return try DBClusterAlreadyExistsFault.makeError(baseError: baseError)
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "DBClusterParameterGroupNotFound": return try DBClusterParameterGroupNotFoundFault.makeError(baseError: baseError)
            case "DBClusterQuotaExceededFault": return try DBClusterQuotaExceededFault.makeError(baseError: baseError)
            case "DBInstanceNotFound": return try DBInstanceNotFoundFault.makeError(baseError: baseError)
            case "DBSubnetGroupDoesNotCoverEnoughAZs": return try DBSubnetGroupDoesNotCoverEnoughAZs.makeError(baseError: baseError)
            case "DBSubnetGroupNotFoundFault": return try DBSubnetGroupNotFoundFault.makeError(baseError: baseError)
            case "GlobalClusterNotFoundFault": return try GlobalClusterNotFoundFault.makeError(baseError: baseError)
            case "InsufficientStorageClusterCapacity": return try InsufficientStorageClusterCapacityFault.makeError(baseError: baseError)
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            case "InvalidDBInstanceState": return try InvalidDBInstanceStateFault.makeError(baseError: baseError)
            case "InvalidDBSubnetGroupStateFault": return try InvalidDBSubnetGroupStateFault.makeError(baseError: baseError)
            case "InvalidGlobalClusterStateFault": return try InvalidGlobalClusterStateFault.makeError(baseError: baseError)
            case "InvalidSubnet": return try InvalidSubnet.makeError(baseError: baseError)
            case "InvalidVPCNetworkStateFault": return try InvalidVPCNetworkStateFault.makeError(baseError: baseError)
            case "KMSKeyNotAccessibleFault": return try KMSKeyNotAccessibleFault.makeError(baseError: baseError)
            case "StorageQuotaExceeded": return try StorageQuotaExceededFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateDBClusterParameterGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBParameterGroupAlreadyExists": return try DBParameterGroupAlreadyExistsFault.makeError(baseError: baseError)
            case "DBParameterGroupQuotaExceeded": return try DBParameterGroupQuotaExceededFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateDBClusterSnapshotOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "DBClusterSnapshotAlreadyExistsFault": return try DBClusterSnapshotAlreadyExistsFault.makeError(baseError: baseError)
            case "InvalidDBClusterSnapshotStateFault": return try InvalidDBClusterSnapshotStateFault.makeError(baseError: baseError)
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            case "SnapshotQuotaExceeded": return try SnapshotQuotaExceededFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateDBInstanceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AuthorizationNotFound": return try AuthorizationNotFoundFault.makeError(baseError: baseError)
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "DBInstanceAlreadyExists": return try DBInstanceAlreadyExistsFault.makeError(baseError: baseError)
            case "DBParameterGroupNotFound": return try DBParameterGroupNotFoundFault.makeError(baseError: baseError)
            case "DBSecurityGroupNotFound": return try DBSecurityGroupNotFoundFault.makeError(baseError: baseError)
            case "DBSubnetGroupDoesNotCoverEnoughAZs": return try DBSubnetGroupDoesNotCoverEnoughAZs.makeError(baseError: baseError)
            case "DBSubnetGroupNotFoundFault": return try DBSubnetGroupNotFoundFault.makeError(baseError: baseError)
            case "InstanceQuotaExceeded": return try InstanceQuotaExceededFault.makeError(baseError: baseError)
            case "InsufficientDBInstanceCapacity": return try InsufficientDBInstanceCapacityFault.makeError(baseError: baseError)
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            case "InvalidSubnet": return try InvalidSubnet.makeError(baseError: baseError)
            case "InvalidVPCNetworkStateFault": return try InvalidVPCNetworkStateFault.makeError(baseError: baseError)
            case "KMSKeyNotAccessibleFault": return try KMSKeyNotAccessibleFault.makeError(baseError: baseError)
            case "StorageQuotaExceeded": return try StorageQuotaExceededFault.makeError(baseError: baseError)
            case "StorageTypeNotSupported": return try StorageTypeNotSupportedFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateDBSubnetGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBSubnetGroupAlreadyExists": return try DBSubnetGroupAlreadyExistsFault.makeError(baseError: baseError)
            case "DBSubnetGroupDoesNotCoverEnoughAZs": return try DBSubnetGroupDoesNotCoverEnoughAZs.makeError(baseError: baseError)
            case "DBSubnetGroupQuotaExceeded": return try DBSubnetGroupQuotaExceededFault.makeError(baseError: baseError)
            case "DBSubnetQuotaExceededFault": return try DBSubnetQuotaExceededFault.makeError(baseError: baseError)
            case "InvalidSubnet": return try InvalidSubnet.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateEventSubscriptionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "EventSubscriptionQuotaExceeded": return try EventSubscriptionQuotaExceededFault.makeError(baseError: baseError)
            case "SNSInvalidTopic": return try SNSInvalidTopicFault.makeError(baseError: baseError)
            case "SNSNoAuthorization": return try SNSNoAuthorizationFault.makeError(baseError: baseError)
            case "SNSTopicArnNotFound": return try SNSTopicArnNotFoundFault.makeError(baseError: baseError)
            case "SourceNotFound": return try SourceNotFoundFault.makeError(baseError: baseError)
            case "SubscriptionAlreadyExist": return try SubscriptionAlreadyExistFault.makeError(baseError: baseError)
            case "SubscriptionCategoryNotFound": return try SubscriptionCategoryNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateGlobalClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "GlobalClusterAlreadyExistsFault": return try GlobalClusterAlreadyExistsFault.makeError(baseError: baseError)
            case "GlobalClusterQuotaExceededFault": return try GlobalClusterQuotaExceededFault.makeError(baseError: baseError)
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteDBClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "DBClusterSnapshotAlreadyExistsFault": return try DBClusterSnapshotAlreadyExistsFault.makeError(baseError: baseError)
            case "InvalidDBClusterSnapshotStateFault": return try InvalidDBClusterSnapshotStateFault.makeError(baseError: baseError)
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            case "SnapshotQuotaExceeded": return try SnapshotQuotaExceededFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteDBClusterParameterGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBParameterGroupNotFound": return try DBParameterGroupNotFoundFault.makeError(baseError: baseError)
            case "InvalidDBParameterGroupState": return try InvalidDBParameterGroupStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteDBClusterSnapshotOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterSnapshotNotFoundFault": return try DBClusterSnapshotNotFoundFault.makeError(baseError: baseError)
            case "InvalidDBClusterSnapshotStateFault": return try InvalidDBClusterSnapshotStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteDBInstanceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBInstanceNotFound": return try DBInstanceNotFoundFault.makeError(baseError: baseError)
            case "DBSnapshotAlreadyExists": return try DBSnapshotAlreadyExistsFault.makeError(baseError: baseError)
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            case "InvalidDBInstanceState": return try InvalidDBInstanceStateFault.makeError(baseError: baseError)
            case "SnapshotQuotaExceeded": return try SnapshotQuotaExceededFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteDBSubnetGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBSubnetGroupNotFoundFault": return try DBSubnetGroupNotFoundFault.makeError(baseError: baseError)
            case "InvalidDBSubnetGroupStateFault": return try InvalidDBSubnetGroupStateFault.makeError(baseError: baseError)
            case "InvalidDBSubnetStateFault": return try InvalidDBSubnetStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteEventSubscriptionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InvalidEventSubscriptionState": return try InvalidEventSubscriptionStateFault.makeError(baseError: baseError)
            case "SubscriptionNotFound": return try SubscriptionNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteGlobalClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "GlobalClusterNotFoundFault": return try GlobalClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidGlobalClusterStateFault": return try InvalidGlobalClusterStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeCertificatesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "CertificateNotFound": return try CertificateNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDBClusterParameterGroupsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBParameterGroupNotFound": return try DBParameterGroupNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDBClusterParametersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBParameterGroupNotFound": return try DBParameterGroupNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDBClustersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDBClusterSnapshotAttributesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterSnapshotNotFoundFault": return try DBClusterSnapshotNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDBClusterSnapshotsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterSnapshotNotFoundFault": return try DBClusterSnapshotNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDBEngineVersionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDBInstancesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBInstanceNotFound": return try DBInstanceNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeDBSubnetGroupsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBSubnetGroupNotFoundFault": return try DBSubnetGroupNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeEngineDefaultClusterParametersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeEventCategoriesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeEventsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeEventSubscriptionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "SubscriptionNotFound": return try SubscriptionNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeGlobalClustersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "GlobalClusterNotFoundFault": return try GlobalClusterNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeOrderableDBInstanceOptionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribePendingMaintenanceActionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundFault": return try ResourceNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum FailoverDBClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            case "InvalidDBInstanceState": return try InvalidDBInstanceStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum FailoverGlobalClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "GlobalClusterNotFoundFault": return try GlobalClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            case "InvalidGlobalClusterStateFault": return try InvalidGlobalClusterStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "DBInstanceNotFound": return try DBInstanceNotFoundFault.makeError(baseError: baseError)
            case "DBSnapshotNotFound": return try DBSnapshotNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ModifyDBClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterAlreadyExistsFault": return try DBClusterAlreadyExistsFault.makeError(baseError: baseError)
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "DBClusterParameterGroupNotFound": return try DBClusterParameterGroupNotFoundFault.makeError(baseError: baseError)
            case "DBSubnetGroupNotFoundFault": return try DBSubnetGroupNotFoundFault.makeError(baseError: baseError)
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            case "InvalidDBInstanceState": return try InvalidDBInstanceStateFault.makeError(baseError: baseError)
            case "InvalidDBSecurityGroupState": return try InvalidDBSecurityGroupStateFault.makeError(baseError: baseError)
            case "InvalidDBSubnetGroupStateFault": return try InvalidDBSubnetGroupStateFault.makeError(baseError: baseError)
            case "InvalidSubnet": return try InvalidSubnet.makeError(baseError: baseError)
            case "InvalidVPCNetworkStateFault": return try InvalidVPCNetworkStateFault.makeError(baseError: baseError)
            case "StorageQuotaExceeded": return try StorageQuotaExceededFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ModifyDBClusterParameterGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBParameterGroupNotFound": return try DBParameterGroupNotFoundFault.makeError(baseError: baseError)
            case "InvalidDBParameterGroupState": return try InvalidDBParameterGroupStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ModifyDBClusterSnapshotAttributeOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterSnapshotNotFoundFault": return try DBClusterSnapshotNotFoundFault.makeError(baseError: baseError)
            case "InvalidDBClusterSnapshotStateFault": return try InvalidDBClusterSnapshotStateFault.makeError(baseError: baseError)
            case "SharedSnapshotQuotaExceeded": return try SharedSnapshotQuotaExceededFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ModifyDBInstanceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AuthorizationNotFound": return try AuthorizationNotFoundFault.makeError(baseError: baseError)
            case "CertificateNotFound": return try CertificateNotFoundFault.makeError(baseError: baseError)
            case "DBInstanceAlreadyExists": return try DBInstanceAlreadyExistsFault.makeError(baseError: baseError)
            case "DBInstanceNotFound": return try DBInstanceNotFoundFault.makeError(baseError: baseError)
            case "DBParameterGroupNotFound": return try DBParameterGroupNotFoundFault.makeError(baseError: baseError)
            case "DBSecurityGroupNotFound": return try DBSecurityGroupNotFoundFault.makeError(baseError: baseError)
            case "DBUpgradeDependencyFailure": return try DBUpgradeDependencyFailureFault.makeError(baseError: baseError)
            case "InsufficientDBInstanceCapacity": return try InsufficientDBInstanceCapacityFault.makeError(baseError: baseError)
            case "InvalidDBInstanceState": return try InvalidDBInstanceStateFault.makeError(baseError: baseError)
            case "InvalidDBSecurityGroupState": return try InvalidDBSecurityGroupStateFault.makeError(baseError: baseError)
            case "InvalidVPCNetworkStateFault": return try InvalidVPCNetworkStateFault.makeError(baseError: baseError)
            case "StorageQuotaExceeded": return try StorageQuotaExceededFault.makeError(baseError: baseError)
            case "StorageTypeNotSupported": return try StorageTypeNotSupportedFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ModifyDBSubnetGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBSubnetGroupDoesNotCoverEnoughAZs": return try DBSubnetGroupDoesNotCoverEnoughAZs.makeError(baseError: baseError)
            case "DBSubnetGroupNotFoundFault": return try DBSubnetGroupNotFoundFault.makeError(baseError: baseError)
            case "DBSubnetQuotaExceededFault": return try DBSubnetQuotaExceededFault.makeError(baseError: baseError)
            case "InvalidSubnet": return try InvalidSubnet.makeError(baseError: baseError)
            case "SubnetAlreadyInUse": return try SubnetAlreadyInUse.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ModifyEventSubscriptionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "EventSubscriptionQuotaExceeded": return try EventSubscriptionQuotaExceededFault.makeError(baseError: baseError)
            case "SNSInvalidTopic": return try SNSInvalidTopicFault.makeError(baseError: baseError)
            case "SNSNoAuthorization": return try SNSNoAuthorizationFault.makeError(baseError: baseError)
            case "SNSTopicArnNotFound": return try SNSTopicArnNotFoundFault.makeError(baseError: baseError)
            case "SubscriptionCategoryNotFound": return try SubscriptionCategoryNotFoundFault.makeError(baseError: baseError)
            case "SubscriptionNotFound": return try SubscriptionNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ModifyGlobalClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "GlobalClusterNotFoundFault": return try GlobalClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidGlobalClusterStateFault": return try InvalidGlobalClusterStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RebootDBInstanceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBInstanceNotFound": return try DBInstanceNotFoundFault.makeError(baseError: baseError)
            case "InvalidDBInstanceState": return try InvalidDBInstanceStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RemoveFromGlobalClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "GlobalClusterNotFoundFault": return try GlobalClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidGlobalClusterStateFault": return try InvalidGlobalClusterStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RemoveSourceIdentifierFromSubscriptionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "SourceNotFound": return try SourceNotFoundFault.makeError(baseError: baseError)
            case "SubscriptionNotFound": return try SubscriptionNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RemoveTagsFromResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "DBInstanceNotFound": return try DBInstanceNotFoundFault.makeError(baseError: baseError)
            case "DBSnapshotNotFound": return try DBSnapshotNotFoundFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ResetDBClusterParameterGroupOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBParameterGroupNotFound": return try DBParameterGroupNotFoundFault.makeError(baseError: baseError)
            case "InvalidDBParameterGroupState": return try InvalidDBParameterGroupStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RestoreDBClusterFromSnapshotOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterAlreadyExistsFault": return try DBClusterAlreadyExistsFault.makeError(baseError: baseError)
            case "DBClusterQuotaExceededFault": return try DBClusterQuotaExceededFault.makeError(baseError: baseError)
            case "DBClusterSnapshotNotFoundFault": return try DBClusterSnapshotNotFoundFault.makeError(baseError: baseError)
            case "DBSnapshotNotFound": return try DBSnapshotNotFoundFault.makeError(baseError: baseError)
            case "DBSubnetGroupNotFoundFault": return try DBSubnetGroupNotFoundFault.makeError(baseError: baseError)
            case "InsufficientDBClusterCapacityFault": return try InsufficientDBClusterCapacityFault.makeError(baseError: baseError)
            case "InsufficientStorageClusterCapacity": return try InsufficientStorageClusterCapacityFault.makeError(baseError: baseError)
            case "InvalidDBClusterSnapshotStateFault": return try InvalidDBClusterSnapshotStateFault.makeError(baseError: baseError)
            case "InvalidDBSnapshotState": return try InvalidDBSnapshotStateFault.makeError(baseError: baseError)
            case "InvalidRestoreFault": return try InvalidRestoreFault.makeError(baseError: baseError)
            case "InvalidSubnet": return try InvalidSubnet.makeError(baseError: baseError)
            case "InvalidVPCNetworkStateFault": return try InvalidVPCNetworkStateFault.makeError(baseError: baseError)
            case "KMSKeyNotAccessibleFault": return try KMSKeyNotAccessibleFault.makeError(baseError: baseError)
            case "StorageQuotaExceeded": return try StorageQuotaExceededFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RestoreDBClusterToPointInTimeOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterAlreadyExistsFault": return try DBClusterAlreadyExistsFault.makeError(baseError: baseError)
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "DBClusterQuotaExceededFault": return try DBClusterQuotaExceededFault.makeError(baseError: baseError)
            case "DBClusterSnapshotNotFoundFault": return try DBClusterSnapshotNotFoundFault.makeError(baseError: baseError)
            case "DBSubnetGroupNotFoundFault": return try DBSubnetGroupNotFoundFault.makeError(baseError: baseError)
            case "InsufficientDBClusterCapacityFault": return try InsufficientDBClusterCapacityFault.makeError(baseError: baseError)
            case "InsufficientStorageClusterCapacity": return try InsufficientStorageClusterCapacityFault.makeError(baseError: baseError)
            case "InvalidDBClusterSnapshotStateFault": return try InvalidDBClusterSnapshotStateFault.makeError(baseError: baseError)
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            case "InvalidDBSnapshotState": return try InvalidDBSnapshotStateFault.makeError(baseError: baseError)
            case "InvalidRestoreFault": return try InvalidRestoreFault.makeError(baseError: baseError)
            case "InvalidSubnet": return try InvalidSubnet.makeError(baseError: baseError)
            case "InvalidVPCNetworkStateFault": return try InvalidVPCNetworkStateFault.makeError(baseError: baseError)
            case "KMSKeyNotAccessibleFault": return try KMSKeyNotAccessibleFault.makeError(baseError: baseError)
            case "StorageQuotaExceeded": return try StorageQuotaExceededFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartDBClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            case "InvalidDBInstanceState": return try InvalidDBInstanceStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopDBClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            case "InvalidDBInstanceState": return try InvalidDBInstanceStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SwitchoverGlobalClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyXML.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSQueryError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DBClusterNotFoundFault": return try DBClusterNotFoundFault.makeError(baseError: baseError)
            case "GlobalClusterNotFoundFault": return try GlobalClusterNotFoundFault.makeError(baseError: baseError)
            case "InvalidDBClusterStateFault": return try InvalidDBClusterStateFault.makeError(baseError: baseError)
            case "InvalidGlobalClusterStateFault": return try InvalidGlobalClusterStateFault.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension SourceNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> SourceNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = SourceNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SubscriptionNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> SubscriptionNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = SubscriptionNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBInstanceNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBInstanceNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = DBInstanceNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBSnapshotNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBSnapshotNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = DBSnapshotNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBClusterNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBClusterNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = DBClusterNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> ResourceNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidDBClusterStateFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InvalidDBClusterStateFault {
        let reader = baseError.errorBodyReader
        var value = InvalidDBClusterStateFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidDBInstanceStateFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InvalidDBInstanceStateFault {
        let reader = baseError.errorBodyReader
        var value = InvalidDBInstanceStateFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBParameterGroupQuotaExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBParameterGroupQuotaExceededFault {
        let reader = baseError.errorBodyReader
        var value = DBParameterGroupQuotaExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBParameterGroupAlreadyExistsFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBParameterGroupAlreadyExistsFault {
        let reader = baseError.errorBodyReader
        var value = DBParameterGroupAlreadyExistsFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBParameterGroupNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBParameterGroupNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = DBParameterGroupNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBClusterSnapshotNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBClusterSnapshotNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = DBClusterSnapshotNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidDBClusterSnapshotStateFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InvalidDBClusterSnapshotStateFault {
        let reader = baseError.errorBodyReader
        var value = InvalidDBClusterSnapshotStateFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBClusterSnapshotAlreadyExistsFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBClusterSnapshotAlreadyExistsFault {
        let reader = baseError.errorBodyReader
        var value = DBClusterSnapshotAlreadyExistsFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SnapshotQuotaExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> SnapshotQuotaExceededFault {
        let reader = baseError.errorBodyReader
        var value = SnapshotQuotaExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension KMSKeyNotAccessibleFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> KMSKeyNotAccessibleFault {
        let reader = baseError.errorBodyReader
        var value = KMSKeyNotAccessibleFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidGlobalClusterStateFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InvalidGlobalClusterStateFault {
        let reader = baseError.errorBodyReader
        var value = InvalidGlobalClusterStateFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBClusterAlreadyExistsFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBClusterAlreadyExistsFault {
        let reader = baseError.errorBodyReader
        var value = DBClusterAlreadyExistsFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBClusterQuotaExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBClusterQuotaExceededFault {
        let reader = baseError.errorBodyReader
        var value = DBClusterQuotaExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidVPCNetworkStateFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InvalidVPCNetworkStateFault {
        let reader = baseError.errorBodyReader
        var value = InvalidVPCNetworkStateFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBSubnetGroupNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBSubnetGroupNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = DBSubnetGroupNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension GlobalClusterNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> GlobalClusterNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = GlobalClusterNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidDBSubnetGroupStateFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InvalidDBSubnetGroupStateFault {
        let reader = baseError.errorBodyReader
        var value = InvalidDBSubnetGroupStateFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidSubnet {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InvalidSubnet {
        let reader = baseError.errorBodyReader
        var value = InvalidSubnet()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBSubnetGroupDoesNotCoverEnoughAZs {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBSubnetGroupDoesNotCoverEnoughAZs {
        let reader = baseError.errorBodyReader
        var value = DBSubnetGroupDoesNotCoverEnoughAZs()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBClusterParameterGroupNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBClusterParameterGroupNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = DBClusterParameterGroupNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InsufficientStorageClusterCapacityFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InsufficientStorageClusterCapacityFault {
        let reader = baseError.errorBodyReader
        var value = InsufficientStorageClusterCapacityFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension StorageQuotaExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> StorageQuotaExceededFault {
        let reader = baseError.errorBodyReader
        var value = StorageQuotaExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBSecurityGroupNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBSecurityGroupNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = DBSecurityGroupNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InstanceQuotaExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InstanceQuotaExceededFault {
        let reader = baseError.errorBodyReader
        var value = InstanceQuotaExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension StorageTypeNotSupportedFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> StorageTypeNotSupportedFault {
        let reader = baseError.errorBodyReader
        var value = StorageTypeNotSupportedFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AuthorizationNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> AuthorizationNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = AuthorizationNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InsufficientDBInstanceCapacityFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InsufficientDBInstanceCapacityFault {
        let reader = baseError.errorBodyReader
        var value = InsufficientDBInstanceCapacityFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBInstanceAlreadyExistsFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBInstanceAlreadyExistsFault {
        let reader = baseError.errorBodyReader
        var value = DBInstanceAlreadyExistsFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBSubnetQuotaExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBSubnetQuotaExceededFault {
        let reader = baseError.errorBodyReader
        var value = DBSubnetQuotaExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBSubnetGroupQuotaExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBSubnetGroupQuotaExceededFault {
        let reader = baseError.errorBodyReader
        var value = DBSubnetGroupQuotaExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBSubnetGroupAlreadyExistsFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBSubnetGroupAlreadyExistsFault {
        let reader = baseError.errorBodyReader
        var value = DBSubnetGroupAlreadyExistsFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SNSInvalidTopicFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> SNSInvalidTopicFault {
        let reader = baseError.errorBodyReader
        var value = SNSInvalidTopicFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SNSTopicArnNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> SNSTopicArnNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = SNSTopicArnNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SubscriptionAlreadyExistFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> SubscriptionAlreadyExistFault {
        let reader = baseError.errorBodyReader
        var value = SubscriptionAlreadyExistFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SubscriptionCategoryNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> SubscriptionCategoryNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = SubscriptionCategoryNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension EventSubscriptionQuotaExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> EventSubscriptionQuotaExceededFault {
        let reader = baseError.errorBodyReader
        var value = EventSubscriptionQuotaExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SNSNoAuthorizationFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> SNSNoAuthorizationFault {
        let reader = baseError.errorBodyReader
        var value = SNSNoAuthorizationFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension GlobalClusterAlreadyExistsFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> GlobalClusterAlreadyExistsFault {
        let reader = baseError.errorBodyReader
        var value = GlobalClusterAlreadyExistsFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension GlobalClusterQuotaExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> GlobalClusterQuotaExceededFault {
        let reader = baseError.errorBodyReader
        var value = GlobalClusterQuotaExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidDBParameterGroupStateFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InvalidDBParameterGroupStateFault {
        let reader = baseError.errorBodyReader
        var value = InvalidDBParameterGroupStateFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBSnapshotAlreadyExistsFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBSnapshotAlreadyExistsFault {
        let reader = baseError.errorBodyReader
        var value = DBSnapshotAlreadyExistsFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidDBSubnetStateFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InvalidDBSubnetStateFault {
        let reader = baseError.errorBodyReader
        var value = InvalidDBSubnetStateFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidEventSubscriptionStateFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InvalidEventSubscriptionStateFault {
        let reader = baseError.errorBodyReader
        var value = InvalidEventSubscriptionStateFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CertificateNotFoundFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> CertificateNotFoundFault {
        let reader = baseError.errorBodyReader
        var value = CertificateNotFoundFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidDBSecurityGroupStateFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InvalidDBSecurityGroupStateFault {
        let reader = baseError.errorBodyReader
        var value = InvalidDBSecurityGroupStateFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SharedSnapshotQuotaExceededFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> SharedSnapshotQuotaExceededFault {
        let reader = baseError.errorBodyReader
        var value = SharedSnapshotQuotaExceededFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DBUpgradeDependencyFailureFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> DBUpgradeDependencyFailureFault {
        let reader = baseError.errorBodyReader
        var value = DBUpgradeDependencyFailureFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SubnetAlreadyInUse {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> SubnetAlreadyInUse {
        let reader = baseError.errorBodyReader
        var value = SubnetAlreadyInUse()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InsufficientDBClusterCapacityFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InsufficientDBClusterCapacityFault {
        let reader = baseError.errorBodyReader
        var value = InsufficientDBClusterCapacityFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidDBSnapshotStateFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InvalidDBSnapshotStateFault {
        let reader = baseError.errorBodyReader
        var value = InvalidDBSnapshotStateFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidRestoreFault {

    static func makeError(baseError: AWSClientRuntime.AWSQueryError) throws -> InvalidRestoreFault {
        let reader = baseError.errorBodyReader
        var value = InvalidRestoreFault()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DocDBClientTypes.EventSubscription {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.EventSubscription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.EventSubscription()
        value.customerAwsId = try reader["CustomerAwsId"].readIfPresent()
        value.custSubscriptionId = try reader["CustSubscriptionId"].readIfPresent()
        value.snsTopicArn = try reader["SnsTopicArn"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.subscriptionCreationTime = try reader["SubscriptionCreationTime"].readIfPresent()
        value.sourceType = try reader["SourceType"].readIfPresent()
        value.sourceIdsList = try reader["SourceIdsList"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "SourceId", isFlattened: false)
        value.eventCategoriesList = try reader["EventCategoriesList"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "EventCategory", isFlattened: false)
        value.enabled = try reader["Enabled"].readIfPresent()
        value.eventSubscriptionArn = try reader["EventSubscriptionArn"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.ResourcePendingMaintenanceActions {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.ResourcePendingMaintenanceActions {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.ResourcePendingMaintenanceActions()
        value.resourceIdentifier = try reader["ResourceIdentifier"].readIfPresent()
        value.pendingMaintenanceActionDetails = try reader["PendingMaintenanceActionDetails"].readListIfPresent(memberReadingClosure: DocDBClientTypes.PendingMaintenanceAction.read(from:), memberNodeInfo: "PendingMaintenanceAction", isFlattened: false)
        return value
    }
}

extension DocDBClientTypes.PendingMaintenanceAction {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.PendingMaintenanceAction {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.PendingMaintenanceAction()
        value.action = try reader["Action"].readIfPresent()
        value.autoAppliedAfterDate = try reader["AutoAppliedAfterDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.forcedApplyDate = try reader["ForcedApplyDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.optInStatus = try reader["OptInStatus"].readIfPresent()
        value.currentApplyDate = try reader["CurrentApplyDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.description = try reader["Description"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.DBClusterParameterGroup {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.DBClusterParameterGroup {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.DBClusterParameterGroup()
        value.dbClusterParameterGroupName = try reader["DBClusterParameterGroupName"].readIfPresent()
        value.dbParameterGroupFamily = try reader["DBParameterGroupFamily"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.dbClusterParameterGroupArn = try reader["DBClusterParameterGroupArn"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.DBClusterSnapshot {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.DBClusterSnapshot {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.DBClusterSnapshot()
        value.availabilityZones = try reader["AvailabilityZones"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "AvailabilityZone", isFlattened: false)
        value.dbClusterSnapshotIdentifier = try reader["DBClusterSnapshotIdentifier"].readIfPresent()
        value.dbClusterIdentifier = try reader["DBClusterIdentifier"].readIfPresent()
        value.snapshotCreateTime = try reader["SnapshotCreateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.engine = try reader["Engine"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.port = try reader["Port"].readIfPresent()
        value.vpcId = try reader["VpcId"].readIfPresent()
        value.clusterCreateTime = try reader["ClusterCreateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.masterUsername = try reader["MasterUsername"].readIfPresent()
        value.engineVersion = try reader["EngineVersion"].readIfPresent()
        value.snapshotType = try reader["SnapshotType"].readIfPresent()
        value.percentProgress = try reader["PercentProgress"].readIfPresent()
        value.storageEncrypted = try reader["StorageEncrypted"].readIfPresent()
        value.kmsKeyId = try reader["KmsKeyId"].readIfPresent()
        value.dbClusterSnapshotArn = try reader["DBClusterSnapshotArn"].readIfPresent()
        value.sourceDBClusterSnapshotArn = try reader["SourceDBClusterSnapshotArn"].readIfPresent()
        value.storageType = try reader["StorageType"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.DBCluster {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.DBCluster {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.DBCluster()
        value.availabilityZones = try reader["AvailabilityZones"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "AvailabilityZone", isFlattened: false)
        value.backupRetentionPeriod = try reader["BackupRetentionPeriod"].readIfPresent()
        value.dbClusterIdentifier = try reader["DBClusterIdentifier"].readIfPresent()
        value.dbClusterParameterGroup = try reader["DBClusterParameterGroup"].readIfPresent()
        value.dbSubnetGroup = try reader["DBSubnetGroup"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.percentProgress = try reader["PercentProgress"].readIfPresent()
        value.earliestRestorableTime = try reader["EarliestRestorableTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.endpoint = try reader["Endpoint"].readIfPresent()
        value.readerEndpoint = try reader["ReaderEndpoint"].readIfPresent()
        value.multiAZ = try reader["MultiAZ"].readIfPresent()
        value.engine = try reader["Engine"].readIfPresent()
        value.engineVersion = try reader["EngineVersion"].readIfPresent()
        value.latestRestorableTime = try reader["LatestRestorableTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.port = try reader["Port"].readIfPresent()
        value.masterUsername = try reader["MasterUsername"].readIfPresent()
        value.preferredBackupWindow = try reader["PreferredBackupWindow"].readIfPresent()
        value.preferredMaintenanceWindow = try reader["PreferredMaintenanceWindow"].readIfPresent()
        value.replicationSourceIdentifier = try reader["ReplicationSourceIdentifier"].readIfPresent()
        value.readReplicaIdentifiers = try reader["ReadReplicaIdentifiers"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "ReadReplicaIdentifier", isFlattened: false)
        value.dbClusterMembers = try reader["DBClusterMembers"].readListIfPresent(memberReadingClosure: DocDBClientTypes.DBClusterMember.read(from:), memberNodeInfo: "DBClusterMember", isFlattened: false)
        value.vpcSecurityGroups = try reader["VpcSecurityGroups"].readListIfPresent(memberReadingClosure: DocDBClientTypes.VpcSecurityGroupMembership.read(from:), memberNodeInfo: "VpcSecurityGroupMembership", isFlattened: false)
        value.hostedZoneId = try reader["HostedZoneId"].readIfPresent()
        value.storageEncrypted = try reader["StorageEncrypted"].readIfPresent()
        value.kmsKeyId = try reader["KmsKeyId"].readIfPresent()
        value.dbClusterResourceId = try reader["DbClusterResourceId"].readIfPresent()
        value.dbClusterArn = try reader["DBClusterArn"].readIfPresent()
        value.associatedRoles = try reader["AssociatedRoles"].readListIfPresent(memberReadingClosure: DocDBClientTypes.DBClusterRole.read(from:), memberNodeInfo: "DBClusterRole", isFlattened: false)
        value.cloneGroupId = try reader["CloneGroupId"].readIfPresent()
        value.clusterCreateTime = try reader["ClusterCreateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.enabledCloudwatchLogsExports = try reader["EnabledCloudwatchLogsExports"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.deletionProtection = try reader["DeletionProtection"].readIfPresent()
        value.storageType = try reader["StorageType"].readIfPresent()
        value.masterUserSecret = try reader["MasterUserSecret"].readIfPresent(with: DocDBClientTypes.ClusterMasterUserSecret.read(from:))
        return value
    }
}

extension DocDBClientTypes.ClusterMasterUserSecret {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.ClusterMasterUserSecret {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.ClusterMasterUserSecret()
        value.secretArn = try reader["SecretArn"].readIfPresent()
        value.secretStatus = try reader["SecretStatus"].readIfPresent()
        value.kmsKeyId = try reader["KmsKeyId"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.DBClusterRole {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.DBClusterRole {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.DBClusterRole()
        value.roleArn = try reader["RoleArn"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.VpcSecurityGroupMembership {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.VpcSecurityGroupMembership {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.VpcSecurityGroupMembership()
        value.vpcSecurityGroupId = try reader["VpcSecurityGroupId"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.DBClusterMember {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.DBClusterMember {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.DBClusterMember()
        value.dbInstanceIdentifier = try reader["DBInstanceIdentifier"].readIfPresent()
        value.isClusterWriter = try reader["IsClusterWriter"].readIfPresent()
        value.dbClusterParameterGroupStatus = try reader["DBClusterParameterGroupStatus"].readIfPresent()
        value.promotionTier = try reader["PromotionTier"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.DBInstance {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.DBInstance {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.DBInstance()
        value.dbInstanceIdentifier = try reader["DBInstanceIdentifier"].readIfPresent()
        value.dbInstanceClass = try reader["DBInstanceClass"].readIfPresent()
        value.engine = try reader["Engine"].readIfPresent()
        value.dbInstanceStatus = try reader["DBInstanceStatus"].readIfPresent()
        value.endpoint = try reader["Endpoint"].readIfPresent(with: DocDBClientTypes.Endpoint.read(from:))
        value.instanceCreateTime = try reader["InstanceCreateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.preferredBackupWindow = try reader["PreferredBackupWindow"].readIfPresent()
        value.backupRetentionPeriod = try reader["BackupRetentionPeriod"].readIfPresent()
        value.vpcSecurityGroups = try reader["VpcSecurityGroups"].readListIfPresent(memberReadingClosure: DocDBClientTypes.VpcSecurityGroupMembership.read(from:), memberNodeInfo: "VpcSecurityGroupMembership", isFlattened: false)
        value.availabilityZone = try reader["AvailabilityZone"].readIfPresent()
        value.dbSubnetGroup = try reader["DBSubnetGroup"].readIfPresent(with: DocDBClientTypes.DBSubnetGroup.read(from:))
        value.preferredMaintenanceWindow = try reader["PreferredMaintenanceWindow"].readIfPresent()
        value.pendingModifiedValues = try reader["PendingModifiedValues"].readIfPresent(with: DocDBClientTypes.PendingModifiedValues.read(from:))
        value.latestRestorableTime = try reader["LatestRestorableTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.engineVersion = try reader["EngineVersion"].readIfPresent()
        value.autoMinorVersionUpgrade = try reader["AutoMinorVersionUpgrade"].readIfPresent()
        value.publiclyAccessible = try reader["PubliclyAccessible"].readIfPresent()
        value.statusInfos = try reader["StatusInfos"].readListIfPresent(memberReadingClosure: DocDBClientTypes.DBInstanceStatusInfo.read(from:), memberNodeInfo: "DBInstanceStatusInfo", isFlattened: false)
        value.dbClusterIdentifier = try reader["DBClusterIdentifier"].readIfPresent()
        value.storageEncrypted = try reader["StorageEncrypted"].readIfPresent()
        value.kmsKeyId = try reader["KmsKeyId"].readIfPresent()
        value.dbiResourceId = try reader["DbiResourceId"].readIfPresent()
        value.caCertificateIdentifier = try reader["CACertificateIdentifier"].readIfPresent()
        value.copyTagsToSnapshot = try reader["CopyTagsToSnapshot"].readIfPresent()
        value.promotionTier = try reader["PromotionTier"].readIfPresent()
        value.dbInstanceArn = try reader["DBInstanceArn"].readIfPresent()
        value.enabledCloudwatchLogsExports = try reader["EnabledCloudwatchLogsExports"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.certificateDetails = try reader["CertificateDetails"].readIfPresent(with: DocDBClientTypes.CertificateDetails.read(from:))
        value.performanceInsightsEnabled = try reader["PerformanceInsightsEnabled"].readIfPresent()
        value.performanceInsightsKMSKeyId = try reader["PerformanceInsightsKMSKeyId"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.CertificateDetails {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.CertificateDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.CertificateDetails()
        value.caIdentifier = try reader["CAIdentifier"].readIfPresent()
        value.validTill = try reader["ValidTill"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        return value
    }
}

extension DocDBClientTypes.DBInstanceStatusInfo {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.DBInstanceStatusInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.DBInstanceStatusInfo()
        value.statusType = try reader["StatusType"].readIfPresent()
        value.normal = try reader["Normal"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.message = try reader["Message"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.PendingModifiedValues {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.PendingModifiedValues {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.PendingModifiedValues()
        value.dbInstanceClass = try reader["DBInstanceClass"].readIfPresent()
        value.allocatedStorage = try reader["AllocatedStorage"].readIfPresent()
        value.masterUserPassword = try reader["MasterUserPassword"].readIfPresent()
        value.port = try reader["Port"].readIfPresent()
        value.backupRetentionPeriod = try reader["BackupRetentionPeriod"].readIfPresent()
        value.multiAZ = try reader["MultiAZ"].readIfPresent()
        value.engineVersion = try reader["EngineVersion"].readIfPresent()
        value.licenseModel = try reader["LicenseModel"].readIfPresent()
        value.iops = try reader["Iops"].readIfPresent()
        value.dbInstanceIdentifier = try reader["DBInstanceIdentifier"].readIfPresent()
        value.storageType = try reader["StorageType"].readIfPresent()
        value.caCertificateIdentifier = try reader["CACertificateIdentifier"].readIfPresent()
        value.dbSubnetGroupName = try reader["DBSubnetGroupName"].readIfPresent()
        value.pendingCloudwatchLogsExports = try reader["PendingCloudwatchLogsExports"].readIfPresent(with: DocDBClientTypes.PendingCloudwatchLogsExports.read(from:))
        return value
    }
}

extension DocDBClientTypes.PendingCloudwatchLogsExports {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.PendingCloudwatchLogsExports {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.PendingCloudwatchLogsExports()
        value.logTypesToEnable = try reader["LogTypesToEnable"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.logTypesToDisable = try reader["LogTypesToDisable"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DocDBClientTypes.DBSubnetGroup {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.DBSubnetGroup {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.DBSubnetGroup()
        value.dbSubnetGroupName = try reader["DBSubnetGroupName"].readIfPresent()
        value.dbSubnetGroupDescription = try reader["DBSubnetGroupDescription"].readIfPresent()
        value.vpcId = try reader["VpcId"].readIfPresent()
        value.subnetGroupStatus = try reader["SubnetGroupStatus"].readIfPresent()
        value.subnets = try reader["Subnets"].readListIfPresent(memberReadingClosure: DocDBClientTypes.Subnet.read(from:), memberNodeInfo: "Subnet", isFlattened: false)
        value.dbSubnetGroupArn = try reader["DBSubnetGroupArn"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.Subnet {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.Subnet {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.Subnet()
        value.subnetIdentifier = try reader["SubnetIdentifier"].readIfPresent()
        value.subnetAvailabilityZone = try reader["SubnetAvailabilityZone"].readIfPresent(with: DocDBClientTypes.AvailabilityZone.read(from:))
        value.subnetStatus = try reader["SubnetStatus"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.AvailabilityZone {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.AvailabilityZone {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.AvailabilityZone()
        value.name = try reader["Name"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.Endpoint {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.Endpoint {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.Endpoint()
        value.address = try reader["Address"].readIfPresent()
        value.port = try reader["Port"].readIfPresent()
        value.hostedZoneId = try reader["HostedZoneId"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.GlobalCluster {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.GlobalCluster {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.GlobalCluster()
        value.globalClusterIdentifier = try reader["GlobalClusterIdentifier"].readIfPresent()
        value.globalClusterResourceId = try reader["GlobalClusterResourceId"].readIfPresent()
        value.globalClusterArn = try reader["GlobalClusterArn"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.engine = try reader["Engine"].readIfPresent()
        value.engineVersion = try reader["EngineVersion"].readIfPresent()
        value.databaseName = try reader["DatabaseName"].readIfPresent()
        value.storageEncrypted = try reader["StorageEncrypted"].readIfPresent()
        value.deletionProtection = try reader["DeletionProtection"].readIfPresent()
        value.globalClusterMembers = try reader["GlobalClusterMembers"].readListIfPresent(memberReadingClosure: DocDBClientTypes.GlobalClusterMember.read(from:), memberNodeInfo: "GlobalClusterMember", isFlattened: false)
        return value
    }
}

extension DocDBClientTypes.GlobalClusterMember {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.GlobalClusterMember {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.GlobalClusterMember()
        value.dbClusterArn = try reader["DBClusterArn"].readIfPresent()
        value.readers = try reader["Readers"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.isWriter = try reader["IsWriter"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.Certificate {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.Certificate {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.Certificate()
        value.certificateIdentifier = try reader["CertificateIdentifier"].readIfPresent()
        value.certificateType = try reader["CertificateType"].readIfPresent()
        value.thumbprint = try reader["Thumbprint"].readIfPresent()
        value.validFrom = try reader["ValidFrom"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.validTill = try reader["ValidTill"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.certificateArn = try reader["CertificateArn"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.Parameter {

    static func write(value: DocDBClientTypes.Parameter?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["AllowedValues"].write(value.allowedValues)
        try writer["ApplyMethod"].write(value.applyMethod)
        try writer["ApplyType"].write(value.applyType)
        try writer["DataType"].write(value.dataType)
        try writer["Description"].write(value.description)
        try writer["IsModifiable"].write(value.isModifiable)
        try writer["MinimumEngineVersion"].write(value.minimumEngineVersion)
        try writer["ParameterName"].write(value.parameterName)
        try writer["ParameterValue"].write(value.parameterValue)
        try writer["Source"].write(value.source)
    }

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.Parameter {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.Parameter()
        value.parameterName = try reader["ParameterName"].readIfPresent()
        value.parameterValue = try reader["ParameterValue"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.source = try reader["Source"].readIfPresent()
        value.applyType = try reader["ApplyType"].readIfPresent()
        value.dataType = try reader["DataType"].readIfPresent()
        value.allowedValues = try reader["AllowedValues"].readIfPresent()
        value.isModifiable = try reader["IsModifiable"].readIfPresent()
        value.minimumEngineVersion = try reader["MinimumEngineVersion"].readIfPresent()
        value.applyMethod = try reader["ApplyMethod"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.DBClusterSnapshotAttributesResult {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.DBClusterSnapshotAttributesResult {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.DBClusterSnapshotAttributesResult()
        value.dbClusterSnapshotIdentifier = try reader["DBClusterSnapshotIdentifier"].readIfPresent()
        value.dbClusterSnapshotAttributes = try reader["DBClusterSnapshotAttributes"].readListIfPresent(memberReadingClosure: DocDBClientTypes.DBClusterSnapshotAttribute.read(from:), memberNodeInfo: "DBClusterSnapshotAttribute", isFlattened: false)
        return value
    }
}

extension DocDBClientTypes.DBClusterSnapshotAttribute {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.DBClusterSnapshotAttribute {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.DBClusterSnapshotAttribute()
        value.attributeName = try reader["AttributeName"].readIfPresent()
        value.attributeValues = try reader["AttributeValues"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "AttributeValue", isFlattened: false)
        return value
    }
}

extension DocDBClientTypes.DBEngineVersion {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.DBEngineVersion {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.DBEngineVersion()
        value.engine = try reader["Engine"].readIfPresent()
        value.engineVersion = try reader["EngineVersion"].readIfPresent()
        value.dbParameterGroupFamily = try reader["DBParameterGroupFamily"].readIfPresent()
        value.dbEngineDescription = try reader["DBEngineDescription"].readIfPresent()
        value.dbEngineVersionDescription = try reader["DBEngineVersionDescription"].readIfPresent()
        value.validUpgradeTarget = try reader["ValidUpgradeTarget"].readListIfPresent(memberReadingClosure: DocDBClientTypes.UpgradeTarget.read(from:), memberNodeInfo: "UpgradeTarget", isFlattened: false)
        value.exportableLogTypes = try reader["ExportableLogTypes"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.supportsLogExportsToCloudwatchLogs = try reader["SupportsLogExportsToCloudwatchLogs"].readIfPresent()
        value.supportedCACertificateIdentifiers = try reader["SupportedCACertificateIdentifiers"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.supportsCertificateRotationWithoutRestart = try reader["SupportsCertificateRotationWithoutRestart"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.UpgradeTarget {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.UpgradeTarget {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.UpgradeTarget()
        value.engine = try reader["Engine"].readIfPresent()
        value.engineVersion = try reader["EngineVersion"].readIfPresent()
        value.description = try reader["Description"].readIfPresent()
        value.autoUpgrade = try reader["AutoUpgrade"].readIfPresent()
        value.isMajorVersionUpgrade = try reader["IsMajorVersionUpgrade"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.EngineDefaults {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.EngineDefaults {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.EngineDefaults()
        value.dbParameterGroupFamily = try reader["DBParameterGroupFamily"].readIfPresent()
        value.marker = try reader["Marker"].readIfPresent()
        value.parameters = try reader["Parameters"].readListIfPresent(memberReadingClosure: DocDBClientTypes.Parameter.read(from:), memberNodeInfo: "Parameter", isFlattened: false)
        return value
    }
}

extension DocDBClientTypes.EventCategoriesMap {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.EventCategoriesMap {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.EventCategoriesMap()
        value.sourceType = try reader["SourceType"].readIfPresent()
        value.eventCategories = try reader["EventCategories"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "EventCategory", isFlattened: false)
        return value
    }
}

extension DocDBClientTypes.Event {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.Event {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.Event()
        value.sourceIdentifier = try reader["SourceIdentifier"].readIfPresent()
        value.sourceType = try reader["SourceType"].readIfPresent()
        value.message = try reader["Message"].readIfPresent()
        value.eventCategories = try reader["EventCategories"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "EventCategory", isFlattened: false)
        value.date = try reader["Date"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.sourceArn = try reader["SourceArn"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.OrderableDBInstanceOption {

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.OrderableDBInstanceOption {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.OrderableDBInstanceOption()
        value.engine = try reader["Engine"].readIfPresent()
        value.engineVersion = try reader["EngineVersion"].readIfPresent()
        value.dbInstanceClass = try reader["DBInstanceClass"].readIfPresent()
        value.licenseModel = try reader["LicenseModel"].readIfPresent()
        value.availabilityZones = try reader["AvailabilityZones"].readListIfPresent(memberReadingClosure: DocDBClientTypes.AvailabilityZone.read(from:), memberNodeInfo: "AvailabilityZone", isFlattened: false)
        value.vpc = try reader["Vpc"].readIfPresent()
        value.storageType = try reader["StorageType"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.Tag {

    static func write(value: DocDBClientTypes.Tag?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Key"].write(value.key)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyXML.Reader) throws -> DocDBClientTypes.Tag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBClientTypes.Tag()
        value.key = try reader["Key"].readIfPresent()
        value.value = try reader["Value"].readIfPresent()
        return value
    }
}

extension DocDBClientTypes.Filter {

    static func write(value: DocDBClientTypes.Filter?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
        try writer["Values"].writeList(value.values, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "Value", isFlattened: false)
    }
}

extension DocDBClientTypes.CloudwatchLogsExportConfiguration {

    static func write(value: DocDBClientTypes.CloudwatchLogsExportConfiguration?, to writer: SmithyFormURL.Writer) throws {
        guard let value else { return }
        try writer["DisableLogTypes"].writeList(value.disableLogTypes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["EnableLogTypes"].writeList(value.enableLogTypes, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

public enum DocDBClientTypes {}
