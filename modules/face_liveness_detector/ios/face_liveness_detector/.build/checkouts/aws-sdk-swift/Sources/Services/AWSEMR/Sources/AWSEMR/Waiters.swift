//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import class SmithyWaitersAPI.Waiter
import enum SmithyWaitersAPI.JMESUtils
import struct SmithyWaitersAPI.WaiterConfiguration
import struct SmithyWaitersAPI.WaiterOptions
import struct Smithy<PERSON>aitersAPI.WaiterOutcome

extension EMRClient {

    static func clusterRunningWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeClusterInput, DescribeClusterOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeClusterInput, DescribeClusterOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeClusterInput, result: Swift.Result<DescribeClusterOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Cluster.Status.State"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "RUNNING"
                guard case .success(let output) = result else { return false }
                let cluster = output.cluster
                let status = cluster?.status
                let state = status?.state
                return SmithyWaitersAPI.JMESUtils.compare(state, ==, "RUNNING")
            }),
            .init(state: .success, matcher: { (input: DescribeClusterInput, result: Swift.Result<DescribeClusterOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Cluster.Status.State"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "WAITING"
                guard case .success(let output) = result else { return false }
                let cluster = output.cluster
                let status = cluster?.status
                let state = status?.state
                return SmithyWaitersAPI.JMESUtils.compare(state, ==, "WAITING")
            }),
            .init(state: .failure, matcher: { (input: DescribeClusterInput, result: Swift.Result<DescribeClusterOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Cluster.Status.State"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "TERMINATING"
                guard case .success(let output) = result else { return false }
                let cluster = output.cluster
                let status = cluster?.status
                let state = status?.state
                return SmithyWaitersAPI.JMESUtils.compare(state, ==, "TERMINATING")
            }),
            .init(state: .failure, matcher: { (input: DescribeClusterInput, result: Swift.Result<DescribeClusterOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Cluster.Status.State"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "TERMINATED"
                guard case .success(let output) = result else { return false }
                let cluster = output.cluster
                let status = cluster?.status
                let state = status?.state
                return SmithyWaitersAPI.JMESUtils.compare(state, ==, "TERMINATED")
            }),
            .init(state: .failure, matcher: { (input: DescribeClusterInput, result: Swift.Result<DescribeClusterOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Cluster.Status.State"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "TERMINATED_WITH_ERRORS"
                guard case .success(let output) = result else { return false }
                let cluster = output.cluster
                let status = cluster?.status
                let state = status?.state
                return SmithyWaitersAPI.JMESUtils.compare(state, ==, "TERMINATED_WITH_ERRORS")
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeClusterInput, DescribeClusterOutput>(acceptors: acceptors, minDelay: 30.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ClusterRunning event on the describeCluster operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeClusterInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilClusterRunning(options: SmithyWaitersAPI.WaiterOptions, input: DescribeClusterInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeClusterOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.clusterRunningWaiterConfig(), operation: self.describeCluster(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func clusterTerminatedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeClusterInput, DescribeClusterOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeClusterInput, DescribeClusterOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeClusterInput, result: Swift.Result<DescribeClusterOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Cluster.Status.State"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "TERMINATED"
                guard case .success(let output) = result else { return false }
                let cluster = output.cluster
                let status = cluster?.status
                let state = status?.state
                return SmithyWaitersAPI.JMESUtils.compare(state, ==, "TERMINATED")
            }),
            .init(state: .failure, matcher: { (input: DescribeClusterInput, result: Swift.Result<DescribeClusterOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Cluster.Status.State"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "TERMINATED_WITH_ERRORS"
                guard case .success(let output) = result else { return false }
                let cluster = output.cluster
                let status = cluster?.status
                let state = status?.state
                return SmithyWaitersAPI.JMESUtils.compare(state, ==, "TERMINATED_WITH_ERRORS")
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeClusterInput, DescribeClusterOutput>(acceptors: acceptors, minDelay: 30.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ClusterTerminated event on the describeCluster operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeClusterInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilClusterTerminated(options: SmithyWaitersAPI.WaiterOptions, input: DescribeClusterInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeClusterOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.clusterTerminatedWaiterConfig(), operation: self.describeCluster(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func stepCompleteWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeStepInput, DescribeStepOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeStepInput, DescribeStepOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeStepInput, result: Swift.Result<DescribeStepOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Step.Status.State"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "COMPLETED"
                guard case .success(let output) = result else { return false }
                let step = output.step
                let status = step?.status
                let state = status?.state
                return SmithyWaitersAPI.JMESUtils.compare(state, ==, "COMPLETED")
            }),
            .init(state: .failure, matcher: { (input: DescribeStepInput, result: Swift.Result<DescribeStepOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Step.Status.State"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "FAILED"
                guard case .success(let output) = result else { return false }
                let step = output.step
                let status = step?.status
                let state = status?.state
                return SmithyWaitersAPI.JMESUtils.compare(state, ==, "FAILED")
            }),
            .init(state: .failure, matcher: { (input: DescribeStepInput, result: Swift.Result<DescribeStepOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Step.Status.State"
                // JMESPath comparator: "stringEquals"
                // JMESPath expected value: "CANCELLED"
                guard case .success(let output) = result else { return false }
                let step = output.step
                let status = step?.status
                let state = status?.state
                return SmithyWaitersAPI.JMESUtils.compare(state, ==, "CANCELLED")
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeStepInput, DescribeStepOutput>(acceptors: acceptors, minDelay: 30.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the StepComplete event on the describeStep operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeStepInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilStepComplete(options: SmithyWaitersAPI.WaiterOptions, input: DescribeStepInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeStepOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.stepCompleteWaiterConfig(), operation: self.describeStep(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }
}
