//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import class Smithy<PERSON><PERSON><PERSON>I.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem

/// An exception that occurs when there are not sufficient permissions to perform an action.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// An error message explaining why access was denied.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// There was an access conflict.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The ID of the resource where there was an access conflict.
        /// This member is required.
        public internal(set) var resourceId: Swift.String? = nil
        /// The type of the resource where there was an access conflict.
        /// This member is required.
        public internal(set) var resourceType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.resourceId = resourceId
        self.properties.resourceType = resourceType
    }
}

/// There was an internal server error.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified resource could not be located.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// An error message describing the failure.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The ID of the resource that could not be located.
        /// This member is required.
        public internal(set) var resourceId: Swift.String? = nil
        /// The type of the resource that could not be found.
        /// This member is required.
        public internal(set) var resourceType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.resourceId = resourceId
        self.properties.resourceType = resourceType
    }
}

/// ThrottlingException will be thrown when request was denied due to request throttling.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The number of seconds to wait before retrying the operation.
        public internal(set) var retryAfterSeconds: Swift.Int? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        retryAfterSeconds: Swift.Int? = nil
    )
    {
        self.properties.message = message
        self.properties.retryAfterSeconds = retryAfterSeconds
    }
}

extension DocDBElasticClientTypes {

    /// A specific field in which a given validation exception occurred.
    public struct ValidationExceptionField: Swift.Sendable {
        /// An error message describing the validation exception in this field.
        /// This member is required.
        public var message: Swift.String?
        /// The name of the field where the validation exception occurred.
        /// This member is required.
        public var name: Swift.String?

        public init(
            message: Swift.String? = nil,
            name: Swift.String? = nil
        )
        {
            self.message = message
            self.name = name
        }
    }
}

extension DocDBElasticClientTypes {

    public enum ValidationExceptionReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cannotParse
        case fieldValidationFailed
        case other
        case unknownOperation
        case sdkUnknown(Swift.String)

        public static var allCases: [ValidationExceptionReason] {
            return [
                .cannotParse,
                .fieldValidationFailed,
                .other,
                .unknownOperation
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cannotParse: return "cannotParse"
            case .fieldValidationFailed: return "fieldValidationFailed"
            case .other: return "other"
            case .unknownOperation: return "unknownOperation"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// A structure defining a validation exception.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// A list of the fields in which the validation exception occurred.
        public internal(set) var fieldList: [DocDBElasticClientTypes.ValidationExceptionField]? = nil
        /// An error message describing the validation exception.
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The reason why the validation exception occurred (one of unknownOperation, cannotParse, fieldValidationFailed, or other).
        /// This member is required.
        public internal(set) var reason: DocDBElasticClientTypes.ValidationExceptionReason? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        fieldList: [DocDBElasticClientTypes.ValidationExceptionField]? = nil,
        message: Swift.String? = nil,
        reason: DocDBElasticClientTypes.ValidationExceptionReason? = nil
    )
    {
        self.properties.fieldList = fieldList
        self.properties.message = message
        self.properties.reason = reason
    }
}

extension DocDBElasticClientTypes {

    public enum OptInType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case applyOn
        case immediate
        case nextMaintenance
        case undoOptIn
        case sdkUnknown(Swift.String)

        public static var allCases: [OptInType] {
            return [
                .applyOn,
                .immediate,
                .nextMaintenance,
                .undoOptIn
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .applyOn: return "APPLY_ON"
            case .immediate: return "IMMEDIATE"
            case .nextMaintenance: return "NEXT_MAINTENANCE"
            case .undoOptIn: return "UNDO_OPT_IN"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct ApplyPendingMaintenanceActionInput: Swift.Sendable {
    /// The pending maintenance action to apply to the resource. Valid actions are:
    ///
    /// * ENGINE_UPDATE
    ///
    /// * ENGINE_UPGRADE
    ///
    /// * SECURITY_UPDATE
    ///
    /// * OS_UPDATE
    ///
    /// * MASTER_USER_PASSWORD_UPDATE
    /// This member is required.
    public var applyAction: Swift.String?
    /// A specific date to apply the pending maintenance action. Required if opt-in-type is APPLY_ON. Format: yyyy/MM/dd HH:mm-yyyy/MM/dd HH:mm
    public var applyOn: Swift.String?
    /// A value that specifies the type of opt-in request, or undoes an opt-in request. An opt-in request of type IMMEDIATE can't be undone.
    /// This member is required.
    public var optInType: DocDBElasticClientTypes.OptInType?
    /// The Amazon DocumentDB Amazon Resource Name (ARN) of the resource to which the pending maintenance action applies.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        applyAction: Swift.String? = nil,
        applyOn: Swift.String? = nil,
        optInType: DocDBElasticClientTypes.OptInType? = nil,
        resourceArn: Swift.String? = nil
    )
    {
        self.applyAction = applyAction
        self.applyOn = applyOn
        self.optInType = optInType
        self.resourceArn = resourceArn
    }
}

extension DocDBElasticClientTypes {

    /// Retrieves the details of maintenance actions that are pending.
    public struct PendingMaintenanceActionDetails: Swift.Sendable {
        /// Displays the specific action of a pending maintenance action.
        /// This member is required.
        public var action: Swift.String?
        /// Displays the date of the maintenance window when the action is applied. The maintenance action is applied to the resource during its first maintenance window after this date. If this date is specified, any NEXT_MAINTENANCEoptInType requests are ignored.
        public var autoAppliedAfterDate: Swift.String?
        /// Displays the effective date when the pending maintenance action is applied to the resource.
        public var currentApplyDate: Swift.String?
        /// Displays a description providing more detail about the maintenance action.
        public var description: Swift.String?
        /// Displays the date when the maintenance action is automatically applied. The maintenance action is applied to the resource on this date regardless of the maintenance window for the resource. If this date is specified, any IMMEDIATEoptInType requests are ignored.
        public var forcedApplyDate: Swift.String?
        /// Displays the type of optInType request that has been received for the resource.
        public var optInStatus: Swift.String?

        public init(
            action: Swift.String? = nil,
            autoAppliedAfterDate: Swift.String? = nil,
            currentApplyDate: Swift.String? = nil,
            description: Swift.String? = nil,
            forcedApplyDate: Swift.String? = nil,
            optInStatus: Swift.String? = nil
        )
        {
            self.action = action
            self.autoAppliedAfterDate = autoAppliedAfterDate
            self.currentApplyDate = currentApplyDate
            self.description = description
            self.forcedApplyDate = forcedApplyDate
            self.optInStatus = optInStatus
        }
    }
}

extension DocDBElasticClientTypes {

    /// Provides information about a pending maintenance action for a resource.
    public struct ResourcePendingMaintenanceAction: Swift.Sendable {
        /// Provides information about a pending maintenance action for a resource.
        public var pendingMaintenanceActionDetails: [DocDBElasticClientTypes.PendingMaintenanceActionDetails]?
        /// The Amazon DocumentDB Amazon Resource Name (ARN) of the resource to which the pending maintenance action applies.
        public var resourceArn: Swift.String?

        public init(
            pendingMaintenanceActionDetails: [DocDBElasticClientTypes.PendingMaintenanceActionDetails]? = nil,
            resourceArn: Swift.String? = nil
        )
        {
            self.pendingMaintenanceActionDetails = pendingMaintenanceActionDetails
            self.resourceArn = resourceArn
        }
    }
}

public struct ApplyPendingMaintenanceActionOutput: Swift.Sendable {
    /// The output of the pending maintenance action being applied.
    /// This member is required.
    public var resourcePendingMaintenanceAction: DocDBElasticClientTypes.ResourcePendingMaintenanceAction?

    public init(
        resourcePendingMaintenanceAction: DocDBElasticClientTypes.ResourcePendingMaintenanceAction? = nil
    )
    {
        self.resourcePendingMaintenanceAction = resourcePendingMaintenanceAction
    }
}

extension DocDBElasticClientTypes {

    public enum Auth: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case plainText
        case secretArn
        case sdkUnknown(Swift.String)

        public static var allCases: [Auth] {
            return [
                .plainText,
                .secretArn
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .plainText: return "PLAIN_TEXT"
            case .secretArn: return "SECRET_ARN"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The service quota for the action was exceeded.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceQuotaExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct CopyClusterSnapshotInput: Swift.Sendable {
    /// Set to true to copy all tags from the source cluster snapshot to the target elastic cluster snapshot. The default is false.
    public var copyTags: Swift.Bool?
    /// The Amazon Web Services KMS key ID for an encrypted elastic cluster snapshot. The Amazon Web Services KMS key ID is the Amazon Resource Name (ARN), Amazon Web Services KMS key identifier, or the Amazon Web Services KMS key alias for the Amazon Web Services KMS encryption key. If you copy an encrypted elastic cluster snapshot from your Amazon Web Services account, you can specify a value for KmsKeyId to encrypt the copy with a new Amazon Web ServicesS KMS encryption key. If you don't specify a value for KmsKeyId, then the copy of the elastic cluster snapshot is encrypted with the same AWS KMS key as the source elastic cluster snapshot. To copy an encrypted elastic cluster snapshot to another Amazon Web Services region, set KmsKeyId to the Amazon Web Services KMS key ID that you want to use to encrypt the copy of the elastic cluster snapshot in the destination region. Amazon Web Services KMS encryption keys are specific to the Amazon Web Services region that they are created in, and you can't use encryption keys from one Amazon Web Services region in another Amazon Web Services region. If you copy an unencrypted elastic cluster snapshot and specify a value for the KmsKeyId parameter, an error is returned.
    public var kmsKeyId: Swift.String?
    /// The Amazon Resource Name (ARN) identifier of the elastic cluster snapshot.
    /// This member is required.
    public var snapshotArn: Swift.String?
    /// The tags to be assigned to the elastic cluster snapshot.
    public var tags: [Swift.String: Swift.String]?
    /// The identifier of the new elastic cluster snapshot to create from the source cluster snapshot. This parameter is not case sensitive. Constraints:
    ///
    /// * Must contain from 1 to 63 letters, numbers, or hyphens.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    ///
    ///
    /// Example: elastic-cluster-snapshot-5
    /// This member is required.
    public var targetSnapshotName: Swift.String?

    public init(
        copyTags: Swift.Bool? = nil,
        kmsKeyId: Swift.String? = nil,
        snapshotArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        targetSnapshotName: Swift.String? = nil
    )
    {
        self.copyTags = copyTags
        self.kmsKeyId = kmsKeyId
        self.snapshotArn = snapshotArn
        self.tags = tags
        self.targetSnapshotName = targetSnapshotName
    }
}

extension DocDBElasticClientTypes {

    public enum SnapshotType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case automated
        case manual
        case sdkUnknown(Swift.String)

        public static var allCases: [SnapshotType] {
            return [
                .automated,
                .manual
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .automated: return "AUTOMATED"
            case .manual: return "MANUAL"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DocDBElasticClientTypes {

    public enum Status: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case active
        case copying
        case creating
        case deleting
        case inaccessibleEncryptionCredentialsRecoverable
        case inaccessibleEncryptionCreds
        case inaccessibleSecretArn
        case inaccessibleVpcEndpoint
        case incompatibleNetwork
        case invalidSecurityGroupId
        case invalidSubnetId
        case ipAddressLimitExceeded
        case maintenance
        case merging
        case modifying
        case splitting
        case starting
        case stopped
        case stopping
        case updating
        case vpcEndpointLimitExceeded
        case sdkUnknown(Swift.String)

        public static var allCases: [Status] {
            return [
                .active,
                .copying,
                .creating,
                .deleting,
                .inaccessibleEncryptionCredentialsRecoverable,
                .inaccessibleEncryptionCreds,
                .inaccessibleSecretArn,
                .inaccessibleVpcEndpoint,
                .incompatibleNetwork,
                .invalidSecurityGroupId,
                .invalidSubnetId,
                .ipAddressLimitExceeded,
                .maintenance,
                .merging,
                .modifying,
                .splitting,
                .starting,
                .stopped,
                .stopping,
                .updating,
                .vpcEndpointLimitExceeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .active: return "ACTIVE"
            case .copying: return "COPYING"
            case .creating: return "CREATING"
            case .deleting: return "DELETING"
            case .inaccessibleEncryptionCredentialsRecoverable: return "INACCESSIBLE_ENCRYPTION_CREDENTIALS_RECOVERABLE"
            case .inaccessibleEncryptionCreds: return "INACCESSIBLE_ENCRYPTION_CREDS"
            case .inaccessibleSecretArn: return "INACCESSIBLE_SECRET_ARN"
            case .inaccessibleVpcEndpoint: return "INACCESSIBLE_VPC_ENDPOINT"
            case .incompatibleNetwork: return "INCOMPATIBLE_NETWORK"
            case .invalidSecurityGroupId: return "INVALID_SECURITY_GROUP_ID"
            case .invalidSubnetId: return "INVALID_SUBNET_ID"
            case .ipAddressLimitExceeded: return "IP_ADDRESS_LIMIT_EXCEEDED"
            case .maintenance: return "MAINTENANCE"
            case .merging: return "MERGING"
            case .modifying: return "MODIFYING"
            case .splitting: return "SPLITTING"
            case .starting: return "STARTING"
            case .stopped: return "STOPPED"
            case .stopping: return "STOPPING"
            case .updating: return "UPDATING"
            case .vpcEndpointLimitExceeded: return "VPC_ENDPOINT_LIMIT_EXCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DocDBElasticClientTypes {

    /// Returns information about a specific elastic cluster snapshot.
    public struct ClusterSnapshot: Swift.Sendable {
        /// The name of the elastic cluster administrator.
        /// This member is required.
        public var adminUserName: Swift.String?
        /// The ARN identifier of the elastic cluster.
        /// This member is required.
        public var clusterArn: Swift.String?
        /// The time when the elastic cluster was created in Universal Coordinated Time (UTC).
        /// This member is required.
        public var clusterCreationTime: Swift.String?
        /// The KMS key identifier is the Amazon Resource Name (ARN) for the KMS encryption key. If you are creating a cluster using the same Amazon account that owns this KMS encryption key, you can use the KMS key alias instead of the ARN as the KMS encryption key. If an encryption key is not specified here, Amazon DocumentDB uses the default encryption key that KMS creates for your account. Your account has a different default encryption key for each Amazon Region.
        /// This member is required.
        public var kmsKeyId: Swift.String?
        /// The ARN identifier of the elastic cluster snapshot.
        /// This member is required.
        public var snapshotArn: Swift.String?
        /// The time when the elastic cluster snapshot was created in Universal Coordinated Time (UTC).
        /// This member is required.
        public var snapshotCreationTime: Swift.String?
        /// The name of the elastic cluster snapshot.
        /// This member is required.
        public var snapshotName: Swift.String?
        /// The type of cluster snapshots to be returned. You can specify one of the following values:
        ///
        /// * automated - Return all cluster snapshots that Amazon DocumentDB has automatically created for your Amazon Web Services account.
        ///
        /// * manual - Return all cluster snapshots that you have manually created for your Amazon Web Services account.
        public var snapshotType: DocDBElasticClientTypes.SnapshotType?
        /// The status of the elastic cluster snapshot.
        /// This member is required.
        public var status: DocDBElasticClientTypes.Status?
        /// The Amazon EC2 subnet IDs for the elastic cluster.
        /// This member is required.
        public var subnetIds: [Swift.String]?
        /// A list of EC2 VPC security groups to associate with the elastic cluster.
        /// This member is required.
        public var vpcSecurityGroupIds: [Swift.String]?

        public init(
            adminUserName: Swift.String? = nil,
            clusterArn: Swift.String? = nil,
            clusterCreationTime: Swift.String? = nil,
            kmsKeyId: Swift.String? = nil,
            snapshotArn: Swift.String? = nil,
            snapshotCreationTime: Swift.String? = nil,
            snapshotName: Swift.String? = nil,
            snapshotType: DocDBElasticClientTypes.SnapshotType? = nil,
            status: DocDBElasticClientTypes.Status? = nil,
            subnetIds: [Swift.String]? = nil,
            vpcSecurityGroupIds: [Swift.String]? = nil
        )
        {
            self.adminUserName = adminUserName
            self.clusterArn = clusterArn
            self.clusterCreationTime = clusterCreationTime
            self.kmsKeyId = kmsKeyId
            self.snapshotArn = snapshotArn
            self.snapshotCreationTime = snapshotCreationTime
            self.snapshotName = snapshotName
            self.snapshotType = snapshotType
            self.status = status
            self.subnetIds = subnetIds
            self.vpcSecurityGroupIds = vpcSecurityGroupIds
        }
    }
}

public struct CopyClusterSnapshotOutput: Swift.Sendable {
    /// Returns information about a specific elastic cluster snapshot.
    /// This member is required.
    public var snapshot: DocDBElasticClientTypes.ClusterSnapshot?

    public init(
        snapshot: DocDBElasticClientTypes.ClusterSnapshot? = nil
    )
    {
        self.snapshot = snapshot
    }
}

public struct CreateClusterInput: Swift.Sendable {
    /// The name of the Amazon DocumentDB elastic clusters administrator. Constraints:
    ///
    /// * Must be from 1 to 63 letters or numbers.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot be a reserved word.
    /// This member is required.
    public var adminUserName: Swift.String?
    /// The password for the Amazon DocumentDB elastic clusters administrator. The password can contain any printable ASCII characters. Constraints:
    ///
    /// * Must contain from 8 to 100 characters.
    ///
    /// * Cannot contain a forward slash (/), double quote ("), or the "at" symbol (@).
    /// This member is required.
    public var adminUserPassword: Swift.String?
    /// The authentication type used to determine where to fetch the password used for accessing the elastic cluster. Valid types are PLAIN_TEXT or SECRET_ARN.
    /// This member is required.
    public var authType: DocDBElasticClientTypes.Auth?
    /// The number of days for which automatic snapshots are retained.
    public var backupRetentionPeriod: Swift.Int?
    /// The client token for the elastic cluster.
    public var clientToken: Swift.String?
    /// The name of the new elastic cluster. This parameter is stored as a lowercase string. Constraints:
    ///
    /// * Must contain from 1 to 63 letters, numbers, or hyphens.
    ///
    /// * The first character must be a letter.
    ///
    /// * Cannot end with a hyphen or contain two consecutive hyphens.
    ///
    ///
    /// Example: my-cluster
    /// This member is required.
    public var clusterName: Swift.String?
    /// The KMS key identifier to use to encrypt the new elastic cluster. The KMS key identifier is the Amazon Resource Name (ARN) for the KMS encryption key. If you are creating a cluster using the same Amazon account that owns this KMS encryption key, you can use the KMS key alias instead of the ARN as the KMS encryption key. If an encryption key is not specified, Amazon DocumentDB uses the default encryption key that KMS creates for your account. Your account has a different default encryption key for each Amazon Region.
    public var kmsKeyId: Swift.String?
    /// The daily time range during which automated backups are created if automated backups are enabled, as determined by the backupRetentionPeriod.
    public var preferredBackupWindow: Swift.String?
    /// The weekly time range during which system maintenance can occur, in Universal Coordinated Time (UTC). Format: ddd:hh24:mi-ddd:hh24:mi Default: a 30-minute window selected at random from an 8-hour block of time for each Amazon Web Services Region, occurring on a random day of the week. Valid days: Mon, Tue, Wed, Thu, Fri, Sat, Sun Constraints: Minimum 30-minute window.
    public var preferredMaintenanceWindow: Swift.String?
    /// The number of vCPUs assigned to each elastic cluster shard. Maximum is 64. Allowed values are 2, 4, 8, 16, 32, 64.
    /// This member is required.
    public var shardCapacity: Swift.Int?
    /// The number of shards assigned to the elastic cluster. Maximum is 32.
    /// This member is required.
    public var shardCount: Swift.Int?
    /// The number of replica instances applying to all shards in the elastic cluster. A shardInstanceCount value of 1 means there is one writer instance, and any additional instances are replicas that can be used for reads and to improve availability.
    public var shardInstanceCount: Swift.Int?
    /// The Amazon EC2 subnet IDs for the new elastic cluster.
    public var subnetIds: [Swift.String]?
    /// The tags to be assigned to the new elastic cluster.
    public var tags: [Swift.String: Swift.String]?
    /// A list of EC2 VPC security groups to associate with the new elastic cluster.
    public var vpcSecurityGroupIds: [Swift.String]?

    public init(
        adminUserName: Swift.String? = nil,
        adminUserPassword: Swift.String? = nil,
        authType: DocDBElasticClientTypes.Auth? = nil,
        backupRetentionPeriod: Swift.Int? = nil,
        clientToken: Swift.String? = nil,
        clusterName: Swift.String? = nil,
        kmsKeyId: Swift.String? = nil,
        preferredBackupWindow: Swift.String? = nil,
        preferredMaintenanceWindow: Swift.String? = nil,
        shardCapacity: Swift.Int? = nil,
        shardCount: Swift.Int? = nil,
        shardInstanceCount: Swift.Int? = nil,
        subnetIds: [Swift.String]? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        vpcSecurityGroupIds: [Swift.String]? = nil
    )
    {
        self.adminUserName = adminUserName
        self.adminUserPassword = adminUserPassword
        self.authType = authType
        self.backupRetentionPeriod = backupRetentionPeriod
        self.clientToken = clientToken
        self.clusterName = clusterName
        self.kmsKeyId = kmsKeyId
        self.preferredBackupWindow = preferredBackupWindow
        self.preferredMaintenanceWindow = preferredMaintenanceWindow
        self.shardCapacity = shardCapacity
        self.shardCount = shardCount
        self.shardInstanceCount = shardInstanceCount
        self.subnetIds = subnetIds
        self.tags = tags
        self.vpcSecurityGroupIds = vpcSecurityGroupIds
    }
}

extension CreateClusterInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateClusterInput(adminUserName: \(Swift.String(describing: adminUserName)), authType: \(Swift.String(describing: authType)), backupRetentionPeriod: \(Swift.String(describing: backupRetentionPeriod)), clientToken: \(Swift.String(describing: clientToken)), clusterName: \(Swift.String(describing: clusterName)), kmsKeyId: \(Swift.String(describing: kmsKeyId)), preferredBackupWindow: \(Swift.String(describing: preferredBackupWindow)), preferredMaintenanceWindow: \(Swift.String(describing: preferredMaintenanceWindow)), shardCapacity: \(Swift.String(describing: shardCapacity)), shardCount: \(Swift.String(describing: shardCount)), shardInstanceCount: \(Swift.String(describing: shardInstanceCount)), subnetIds: \(Swift.String(describing: subnetIds)), tags: \(Swift.String(describing: tags)), vpcSecurityGroupIds: \(Swift.String(describing: vpcSecurityGroupIds)), adminUserPassword: \"CONTENT_REDACTED\")"}
}

extension DocDBElasticClientTypes {

    /// The name of the shard.
    public struct Shard: Swift.Sendable {
        /// The time when the shard was created in Universal Coordinated Time (UTC).
        /// This member is required.
        public var createTime: Swift.String?
        /// The ID of the shard.
        /// This member is required.
        public var shardId: Swift.String?
        /// The current status of the shard.
        /// This member is required.
        public var status: DocDBElasticClientTypes.Status?

        public init(
            createTime: Swift.String? = nil,
            shardId: Swift.String? = nil,
            status: DocDBElasticClientTypes.Status? = nil
        )
        {
            self.createTime = createTime
            self.shardId = shardId
            self.status = status
        }
    }
}

extension DocDBElasticClientTypes {

    /// Returns information about a specific elastic cluster.
    public struct Cluster: Swift.Sendable {
        /// The name of the elastic cluster administrator.
        /// This member is required.
        public var adminUserName: Swift.String?
        /// The authentication type for the elastic cluster.
        /// This member is required.
        public var authType: DocDBElasticClientTypes.Auth?
        /// The number of days for which automatic snapshots are retained.
        public var backupRetentionPeriod: Swift.Int?
        /// The ARN identifier of the elastic cluster.
        /// This member is required.
        public var clusterArn: Swift.String?
        /// The URL used to connect to the elastic cluster.
        /// This member is required.
        public var clusterEndpoint: Swift.String?
        /// The name of the elastic cluster.
        /// This member is required.
        public var clusterName: Swift.String?
        /// The time when the elastic cluster was created in Universal Coordinated Time (UTC).
        /// This member is required.
        public var createTime: Swift.String?
        /// The KMS key identifier to use to encrypt the elastic cluster.
        /// This member is required.
        public var kmsKeyId: Swift.String?
        /// The daily time range during which automated backups are created if automated backups are enabled, as determined by backupRetentionPeriod.
        public var preferredBackupWindow: Swift.String?
        /// The weekly time range during which system maintenance can occur, in Universal Coordinated Time (UTC). Format: ddd:hh24:mi-ddd:hh24:mi
        /// This member is required.
        public var preferredMaintenanceWindow: Swift.String?
        /// The number of vCPUs assigned to each elastic cluster shard. Maximum is 64. Allowed values are 2, 4, 8, 16, 32, 64.
        /// This member is required.
        public var shardCapacity: Swift.Int?
        /// The number of shards assigned to the elastic cluster. Maximum is 32.
        /// This member is required.
        public var shardCount: Swift.Int?
        /// The number of replica instances applying to all shards in the cluster. A shardInstanceCount value of 1 means there is one writer instance, and any additional instances are replicas that can be used for reads and to improve availability.
        public var shardInstanceCount: Swift.Int?
        /// The total number of shards in the cluster.
        public var shards: [DocDBElasticClientTypes.Shard]?
        /// The status of the elastic cluster.
        /// This member is required.
        public var status: DocDBElasticClientTypes.Status?
        /// The Amazon EC2 subnet IDs for the elastic cluster.
        /// This member is required.
        public var subnetIds: [Swift.String]?
        /// A list of EC2 VPC security groups associated with thie elastic cluster.
        /// This member is required.
        public var vpcSecurityGroupIds: [Swift.String]?

        public init(
            adminUserName: Swift.String? = nil,
            authType: DocDBElasticClientTypes.Auth? = nil,
            backupRetentionPeriod: Swift.Int? = nil,
            clusterArn: Swift.String? = nil,
            clusterEndpoint: Swift.String? = nil,
            clusterName: Swift.String? = nil,
            createTime: Swift.String? = nil,
            kmsKeyId: Swift.String? = nil,
            preferredBackupWindow: Swift.String? = nil,
            preferredMaintenanceWindow: Swift.String? = nil,
            shardCapacity: Swift.Int? = nil,
            shardCount: Swift.Int? = nil,
            shardInstanceCount: Swift.Int? = nil,
            shards: [DocDBElasticClientTypes.Shard]? = nil,
            status: DocDBElasticClientTypes.Status? = nil,
            subnetIds: [Swift.String]? = nil,
            vpcSecurityGroupIds: [Swift.String]? = nil
        )
        {
            self.adminUserName = adminUserName
            self.authType = authType
            self.backupRetentionPeriod = backupRetentionPeriod
            self.clusterArn = clusterArn
            self.clusterEndpoint = clusterEndpoint
            self.clusterName = clusterName
            self.createTime = createTime
            self.kmsKeyId = kmsKeyId
            self.preferredBackupWindow = preferredBackupWindow
            self.preferredMaintenanceWindow = preferredMaintenanceWindow
            self.shardCapacity = shardCapacity
            self.shardCount = shardCount
            self.shardInstanceCount = shardInstanceCount
            self.shards = shards
            self.status = status
            self.subnetIds = subnetIds
            self.vpcSecurityGroupIds = vpcSecurityGroupIds
        }
    }
}

public struct CreateClusterOutput: Swift.Sendable {
    /// The new elastic cluster that has been created.
    /// This member is required.
    public var cluster: DocDBElasticClientTypes.Cluster?

    public init(
        cluster: DocDBElasticClientTypes.Cluster? = nil
    )
    {
        self.cluster = cluster
    }
}

public struct CreateClusterSnapshotInput: Swift.Sendable {
    /// The ARN identifier of the elastic cluster of which you want to create a snapshot.
    /// This member is required.
    public var clusterArn: Swift.String?
    /// The name of the new elastic cluster snapshot.
    /// This member is required.
    public var snapshotName: Swift.String?
    /// The tags to be assigned to the new elastic cluster snapshot.
    public var tags: [Swift.String: Swift.String]?

    public init(
        clusterArn: Swift.String? = nil,
        snapshotName: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.clusterArn = clusterArn
        self.snapshotName = snapshotName
        self.tags = tags
    }
}

public struct CreateClusterSnapshotOutput: Swift.Sendable {
    /// Returns information about the new elastic cluster snapshot.
    /// This member is required.
    public var snapshot: DocDBElasticClientTypes.ClusterSnapshot?

    public init(
        snapshot: DocDBElasticClientTypes.ClusterSnapshot? = nil
    )
    {
        self.snapshot = snapshot
    }
}

public struct DeleteClusterInput: Swift.Sendable {
    /// The ARN identifier of the elastic cluster that is to be deleted.
    /// This member is required.
    public var clusterArn: Swift.String?

    public init(
        clusterArn: Swift.String? = nil
    )
    {
        self.clusterArn = clusterArn
    }
}

public struct DeleteClusterOutput: Swift.Sendable {
    /// Returns information about the newly deleted elastic cluster.
    /// This member is required.
    public var cluster: DocDBElasticClientTypes.Cluster?

    public init(
        cluster: DocDBElasticClientTypes.Cluster? = nil
    )
    {
        self.cluster = cluster
    }
}

public struct DeleteClusterSnapshotInput: Swift.Sendable {
    /// The ARN identifier of the elastic cluster snapshot that is to be deleted.
    /// This member is required.
    public var snapshotArn: Swift.String?

    public init(
        snapshotArn: Swift.String? = nil
    )
    {
        self.snapshotArn = snapshotArn
    }
}

public struct DeleteClusterSnapshotOutput: Swift.Sendable {
    /// Returns information about the newly deleted elastic cluster snapshot.
    /// This member is required.
    public var snapshot: DocDBElasticClientTypes.ClusterSnapshot?

    public init(
        snapshot: DocDBElasticClientTypes.ClusterSnapshot? = nil
    )
    {
        self.snapshot = snapshot
    }
}

public struct GetClusterInput: Swift.Sendable {
    /// The ARN identifier of the elastic cluster.
    /// This member is required.
    public var clusterArn: Swift.String?

    public init(
        clusterArn: Swift.String? = nil
    )
    {
        self.clusterArn = clusterArn
    }
}

public struct GetClusterOutput: Swift.Sendable {
    /// Returns information about a specific elastic cluster.
    /// This member is required.
    public var cluster: DocDBElasticClientTypes.Cluster?

    public init(
        cluster: DocDBElasticClientTypes.Cluster? = nil
    )
    {
        self.cluster = cluster
    }
}

public struct GetClusterSnapshotInput: Swift.Sendable {
    /// The ARN identifier of the elastic cluster snapshot.
    /// This member is required.
    public var snapshotArn: Swift.String?

    public init(
        snapshotArn: Swift.String? = nil
    )
    {
        self.snapshotArn = snapshotArn
    }
}

public struct GetClusterSnapshotOutput: Swift.Sendable {
    /// Returns information about a specific elastic cluster snapshot.
    /// This member is required.
    public var snapshot: DocDBElasticClientTypes.ClusterSnapshot?

    public init(
        snapshot: DocDBElasticClientTypes.ClusterSnapshot? = nil
    )
    {
        self.snapshot = snapshot
    }
}

public struct GetPendingMaintenanceActionInput: Swift.Sendable {
    /// Retrieves pending maintenance actions for a specific Amazon Resource Name (ARN).
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct GetPendingMaintenanceActionOutput: Swift.Sendable {
    /// Provides information about a pending maintenance action for a resource.
    /// This member is required.
    public var resourcePendingMaintenanceAction: DocDBElasticClientTypes.ResourcePendingMaintenanceAction?

    public init(
        resourcePendingMaintenanceAction: DocDBElasticClientTypes.ResourcePendingMaintenanceAction? = nil
    )
    {
        self.resourcePendingMaintenanceAction = resourcePendingMaintenanceAction
    }
}

public struct ListClustersInput: Swift.Sendable {
    /// The maximum number of elastic cluster snapshot results to receive in the response.
    public var maxResults: Swift.Int?
    /// A pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond this token, up to the value specified by max-results. If there is no more data in the responce, the nextToken will not be returned.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DocDBElasticClientTypes {

    /// A list of Amazon DocumentDB elastic clusters.
    public struct ClusterInList: Swift.Sendable {
        /// The ARN identifier of the elastic cluster.
        /// This member is required.
        public var clusterArn: Swift.String?
        /// The name of the elastic cluster.
        /// This member is required.
        public var clusterName: Swift.String?
        /// The status of the elastic cluster.
        /// This member is required.
        public var status: DocDBElasticClientTypes.Status?

        public init(
            clusterArn: Swift.String? = nil,
            clusterName: Swift.String? = nil,
            status: DocDBElasticClientTypes.Status? = nil
        )
        {
            self.clusterArn = clusterArn
            self.clusterName = clusterName
            self.status = status
        }
    }
}

public struct ListClustersOutput: Swift.Sendable {
    /// A list of Amazon DocumentDB elastic clusters.
    public var clusters: [DocDBElasticClientTypes.ClusterInList]?
    /// A pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond this token, up to the value specified by max-results. If there is no more data in the responce, the nextToken will not be returned.
    public var nextToken: Swift.String?

    public init(
        clusters: [DocDBElasticClientTypes.ClusterInList]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.clusters = clusters
        self.nextToken = nextToken
    }
}

public struct ListClusterSnapshotsInput: Swift.Sendable {
    /// The ARN identifier of the elastic cluster.
    public var clusterArn: Swift.String?
    /// The maximum number of elastic cluster snapshot results to receive in the response.
    public var maxResults: Swift.Int?
    /// A pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond this token, up to the value specified by max-results. If there is no more data in the responce, the nextToken will not be returned.
    public var nextToken: Swift.String?
    /// The type of cluster snapshots to be returned. You can specify one of the following values:
    ///
    /// * automated - Return all cluster snapshots that Amazon DocumentDB has automatically created for your Amazon Web Services account.
    ///
    /// * manual - Return all cluster snapshots that you have manually created for your Amazon Web Services account.
    public var snapshotType: Swift.String?

    public init(
        clusterArn: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        snapshotType: Swift.String? = nil
    )
    {
        self.clusterArn = clusterArn
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.snapshotType = snapshotType
    }
}

extension DocDBElasticClientTypes {

    /// A list of elastic cluster snapshots.
    public struct ClusterSnapshotInList: Swift.Sendable {
        /// The ARN identifier of the elastic cluster.
        /// This member is required.
        public var clusterArn: Swift.String?
        /// The ARN identifier of the elastic cluster snapshot.
        /// This member is required.
        public var snapshotArn: Swift.String?
        /// The time when the elastic cluster snapshot was created in Universal Coordinated Time (UTC).
        /// This member is required.
        public var snapshotCreationTime: Swift.String?
        /// The name of the elastic cluster snapshot.
        /// This member is required.
        public var snapshotName: Swift.String?
        /// The status of the elastic cluster snapshot.
        /// This member is required.
        public var status: DocDBElasticClientTypes.Status?

        public init(
            clusterArn: Swift.String? = nil,
            snapshotArn: Swift.String? = nil,
            snapshotCreationTime: Swift.String? = nil,
            snapshotName: Swift.String? = nil,
            status: DocDBElasticClientTypes.Status? = nil
        )
        {
            self.clusterArn = clusterArn
            self.snapshotArn = snapshotArn
            self.snapshotCreationTime = snapshotCreationTime
            self.snapshotName = snapshotName
            self.status = status
        }
    }
}

public struct ListClusterSnapshotsOutput: Swift.Sendable {
    /// A pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond this token, up to the value specified by max-results. If there is no more data in the responce, the nextToken will not be returned.
    public var nextToken: Swift.String?
    /// A list of snapshots for a specified elastic cluster.
    public var snapshots: [DocDBElasticClientTypes.ClusterSnapshotInList]?

    public init(
        nextToken: Swift.String? = nil,
        snapshots: [DocDBElasticClientTypes.ClusterSnapshotInList]? = nil
    )
    {
        self.nextToken = nextToken
        self.snapshots = snapshots
    }
}

public struct ListPendingMaintenanceActionsInput: Swift.Sendable {
    /// The maximum number of results to include in the response. If more records exist than the specified maxResults value, a pagination token (marker) is included in the response so that the remaining results can be retrieved.
    public var maxResults: Swift.Int?
    /// An optional pagination token provided by a previous request. If this parameter is specified, the response includes only records beyond the marker, up to the value specified by maxResults.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListPendingMaintenanceActionsOutput: Swift.Sendable {
    /// An optional pagination token provided by a previous request. If this parameter is displayed, the responses will include only records beyond the marker, up to the value specified by maxResults.
    public var nextToken: Swift.String?
    /// Provides information about a pending maintenance action for a resource.
    /// This member is required.
    public var resourcePendingMaintenanceActions: [DocDBElasticClientTypes.ResourcePendingMaintenanceAction]?

    public init(
        nextToken: Swift.String? = nil,
        resourcePendingMaintenanceActions: [DocDBElasticClientTypes.ResourcePendingMaintenanceAction]? = nil
    )
    {
        self.nextToken = nextToken
        self.resourcePendingMaintenanceActions = resourcePendingMaintenanceActions
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The ARN identifier of the elastic cluster resource.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// The list of tags for the specified elastic cluster resource.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

public struct RestoreClusterFromSnapshotInput: Swift.Sendable {
    /// The name of the elastic cluster.
    /// This member is required.
    public var clusterName: Swift.String?
    /// The KMS key identifier to use to encrypt the new Amazon DocumentDB elastic clusters cluster. The KMS key identifier is the Amazon Resource Name (ARN) for the KMS encryption key. If you are creating a cluster using the same Amazon account that owns this KMS encryption key, you can use the KMS key alias instead of the ARN as the KMS encryption key. If an encryption key is not specified here, Amazon DocumentDB uses the default encryption key that KMS creates for your account. Your account has a different default encryption key for each Amazon Region.
    public var kmsKeyId: Swift.String?
    /// The capacity of each shard in the new restored elastic cluster.
    public var shardCapacity: Swift.Int?
    /// The number of replica instances applying to all shards in the elastic cluster. A shardInstanceCount value of 1 means there is one writer instance, and any additional instances are replicas that can be used for reads and to improve availability.
    public var shardInstanceCount: Swift.Int?
    /// The ARN identifier of the elastic cluster snapshot.
    /// This member is required.
    public var snapshotArn: Swift.String?
    /// The Amazon EC2 subnet IDs for the elastic cluster.
    public var subnetIds: [Swift.String]?
    /// A list of the tag names to be assigned to the restored elastic cluster, in the form of an array of key-value pairs in which the key is the tag name and the value is the key value.
    public var tags: [Swift.String: Swift.String]?
    /// A list of EC2 VPC security groups to associate with the elastic cluster.
    public var vpcSecurityGroupIds: [Swift.String]?

    public init(
        clusterName: Swift.String? = nil,
        kmsKeyId: Swift.String? = nil,
        shardCapacity: Swift.Int? = nil,
        shardInstanceCount: Swift.Int? = nil,
        snapshotArn: Swift.String? = nil,
        subnetIds: [Swift.String]? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        vpcSecurityGroupIds: [Swift.String]? = nil
    )
    {
        self.clusterName = clusterName
        self.kmsKeyId = kmsKeyId
        self.shardCapacity = shardCapacity
        self.shardInstanceCount = shardInstanceCount
        self.snapshotArn = snapshotArn
        self.subnetIds = subnetIds
        self.tags = tags
        self.vpcSecurityGroupIds = vpcSecurityGroupIds
    }
}

public struct RestoreClusterFromSnapshotOutput: Swift.Sendable {
    /// Returns information about a the restored elastic cluster.
    /// This member is required.
    public var cluster: DocDBElasticClientTypes.Cluster?

    public init(
        cluster: DocDBElasticClientTypes.Cluster? = nil
    )
    {
        self.cluster = cluster
    }
}

public struct StartClusterInput: Swift.Sendable {
    /// The ARN identifier of the elastic cluster.
    /// This member is required.
    public var clusterArn: Swift.String?

    public init(
        clusterArn: Swift.String? = nil
    )
    {
        self.clusterArn = clusterArn
    }
}

public struct StartClusterOutput: Swift.Sendable {
    /// Returns information about a specific elastic cluster.
    /// This member is required.
    public var cluster: DocDBElasticClientTypes.Cluster?

    public init(
        cluster: DocDBElasticClientTypes.Cluster? = nil
    )
    {
        self.cluster = cluster
    }
}

public struct StopClusterInput: Swift.Sendable {
    /// The ARN identifier of the elastic cluster.
    /// This member is required.
    public var clusterArn: Swift.String?

    public init(
        clusterArn: Swift.String? = nil
    )
    {
        self.clusterArn = clusterArn
    }
}

public struct StopClusterOutput: Swift.Sendable {
    /// Returns information about a specific elastic cluster.
    /// This member is required.
    public var cluster: DocDBElasticClientTypes.Cluster?

    public init(
        cluster: DocDBElasticClientTypes.Cluster? = nil
    )
    {
        self.cluster = cluster
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The ARN identifier of the elastic cluster resource.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tags that are assigned to the elastic cluster resource.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The ARN identifier of the elastic cluster resource.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tag keys to be removed from the elastic cluster resource.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateClusterInput: Swift.Sendable {
    /// The password associated with the elastic cluster administrator. This password can contain any printable ASCII character except forward slash (/), double quote ("), or the "at" symbol (@). Constraints: Must contain from 8 to 100 characters.
    public var adminUserPassword: Swift.String?
    /// The authentication type used to determine where to fetch the password used for accessing the elastic cluster. Valid types are PLAIN_TEXT or SECRET_ARN.
    public var authType: DocDBElasticClientTypes.Auth?
    /// The number of days for which automatic snapshots are retained.
    public var backupRetentionPeriod: Swift.Int?
    /// The client token for the elastic cluster.
    public var clientToken: Swift.String?
    /// The ARN identifier of the elastic cluster.
    /// This member is required.
    public var clusterArn: Swift.String?
    /// The daily time range during which automated backups are created if automated backups are enabled, as determined by the backupRetentionPeriod.
    public var preferredBackupWindow: Swift.String?
    /// The weekly time range during which system maintenance can occur, in Universal Coordinated Time (UTC). Format: ddd:hh24:mi-ddd:hh24:mi Default: a 30-minute window selected at random from an 8-hour block of time for each Amazon Web Services Region, occurring on a random day of the week. Valid days: Mon, Tue, Wed, Thu, Fri, Sat, Sun Constraints: Minimum 30-minute window.
    public var preferredMaintenanceWindow: Swift.String?
    /// The number of vCPUs assigned to each elastic cluster shard. Maximum is 64. Allowed values are 2, 4, 8, 16, 32, 64.
    public var shardCapacity: Swift.Int?
    /// The number of shards assigned to the elastic cluster. Maximum is 32.
    public var shardCount: Swift.Int?
    /// The number of replica instances applying to all shards in the elastic cluster. A shardInstanceCount value of 1 means there is one writer instance, and any additional instances are replicas that can be used for reads and to improve availability.
    public var shardInstanceCount: Swift.Int?
    /// The Amazon EC2 subnet IDs for the elastic cluster.
    public var subnetIds: [Swift.String]?
    /// A list of EC2 VPC security groups to associate with the elastic cluster.
    public var vpcSecurityGroupIds: [Swift.String]?

    public init(
        adminUserPassword: Swift.String? = nil,
        authType: DocDBElasticClientTypes.Auth? = nil,
        backupRetentionPeriod: Swift.Int? = nil,
        clientToken: Swift.String? = nil,
        clusterArn: Swift.String? = nil,
        preferredBackupWindow: Swift.String? = nil,
        preferredMaintenanceWindow: Swift.String? = nil,
        shardCapacity: Swift.Int? = nil,
        shardCount: Swift.Int? = nil,
        shardInstanceCount: Swift.Int? = nil,
        subnetIds: [Swift.String]? = nil,
        vpcSecurityGroupIds: [Swift.String]? = nil
    )
    {
        self.adminUserPassword = adminUserPassword
        self.authType = authType
        self.backupRetentionPeriod = backupRetentionPeriod
        self.clientToken = clientToken
        self.clusterArn = clusterArn
        self.preferredBackupWindow = preferredBackupWindow
        self.preferredMaintenanceWindow = preferredMaintenanceWindow
        self.shardCapacity = shardCapacity
        self.shardCount = shardCount
        self.shardInstanceCount = shardInstanceCount
        self.subnetIds = subnetIds
        self.vpcSecurityGroupIds = vpcSecurityGroupIds
    }
}

extension UpdateClusterInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "UpdateClusterInput(authType: \(Swift.String(describing: authType)), backupRetentionPeriod: \(Swift.String(describing: backupRetentionPeriod)), clientToken: \(Swift.String(describing: clientToken)), clusterArn: \(Swift.String(describing: clusterArn)), preferredBackupWindow: \(Swift.String(describing: preferredBackupWindow)), preferredMaintenanceWindow: \(Swift.String(describing: preferredMaintenanceWindow)), shardCapacity: \(Swift.String(describing: shardCapacity)), shardCount: \(Swift.String(describing: shardCount)), shardInstanceCount: \(Swift.String(describing: shardInstanceCount)), subnetIds: \(Swift.String(describing: subnetIds)), vpcSecurityGroupIds: \(Swift.String(describing: vpcSecurityGroupIds)), adminUserPassword: \"CONTENT_REDACTED\")"}
}

public struct UpdateClusterOutput: Swift.Sendable {
    /// Returns information about the updated elastic cluster.
    /// This member is required.
    public var cluster: DocDBElasticClientTypes.Cluster?

    public init(
        cluster: DocDBElasticClientTypes.Cluster? = nil
    )
    {
        self.cluster = cluster
    }
}

extension ApplyPendingMaintenanceActionInput {

    static func urlPathProvider(_ value: ApplyPendingMaintenanceActionInput) -> Swift.String? {
        return "/pending-action"
    }
}

extension CopyClusterSnapshotInput {

    static func urlPathProvider(_ value: CopyClusterSnapshotInput) -> Swift.String? {
        guard let snapshotArn = value.snapshotArn else {
            return nil
        }
        return "/cluster-snapshot/\(snapshotArn.urlPercentEncoding())/copy"
    }
}

extension CreateClusterInput {

    static func urlPathProvider(_ value: CreateClusterInput) -> Swift.String? {
        return "/cluster"
    }
}

extension CreateClusterSnapshotInput {

    static func urlPathProvider(_ value: CreateClusterSnapshotInput) -> Swift.String? {
        return "/cluster-snapshot"
    }
}

extension DeleteClusterInput {

    static func urlPathProvider(_ value: DeleteClusterInput) -> Swift.String? {
        guard let clusterArn = value.clusterArn else {
            return nil
        }
        return "/cluster/\(clusterArn.urlPercentEncoding())"
    }
}

extension DeleteClusterSnapshotInput {

    static func urlPathProvider(_ value: DeleteClusterSnapshotInput) -> Swift.String? {
        guard let snapshotArn = value.snapshotArn else {
            return nil
        }
        return "/cluster-snapshot/\(snapshotArn.urlPercentEncoding())"
    }
}

extension GetClusterInput {

    static func urlPathProvider(_ value: GetClusterInput) -> Swift.String? {
        guard let clusterArn = value.clusterArn else {
            return nil
        }
        return "/cluster/\(clusterArn.urlPercentEncoding())"
    }
}

extension GetClusterSnapshotInput {

    static func urlPathProvider(_ value: GetClusterSnapshotInput) -> Swift.String? {
        guard let snapshotArn = value.snapshotArn else {
            return nil
        }
        return "/cluster-snapshot/\(snapshotArn.urlPercentEncoding())"
    }
}

extension GetPendingMaintenanceActionInput {

    static func urlPathProvider(_ value: GetPendingMaintenanceActionInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/pending-action/\(resourceArn.urlPercentEncoding())"
    }
}

extension ListClustersInput {

    static func urlPathProvider(_ value: ListClustersInput) -> Swift.String? {
        return "/clusters"
    }
}

extension ListClustersInput {

    static func queryItemProvider(_ value: ListClustersInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListClusterSnapshotsInput {

    static func urlPathProvider(_ value: ListClusterSnapshotsInput) -> Swift.String? {
        return "/cluster-snapshots"
    }
}

extension ListClusterSnapshotsInput {

    static func queryItemProvider(_ value: ListClusterSnapshotsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let clusterArn = value.clusterArn {
            let clusterArnQueryItem = Smithy.URIQueryItem(name: "clusterArn".urlPercentEncoding(), value: Swift.String(clusterArn).urlPercentEncoding())
            items.append(clusterArnQueryItem)
        }
        if let snapshotType = value.snapshotType {
            let snapshotTypeQueryItem = Smithy.URIQueryItem(name: "snapshotType".urlPercentEncoding(), value: Swift.String(snapshotType).urlPercentEncoding())
            items.append(snapshotTypeQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListPendingMaintenanceActionsInput {

    static func urlPathProvider(_ value: ListPendingMaintenanceActionsInput) -> Swift.String? {
        return "/pending-actions"
    }
}

extension ListPendingMaintenanceActionsInput {

    static func queryItemProvider(_ value: ListPendingMaintenanceActionsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        return items
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension RestoreClusterFromSnapshotInput {

    static func urlPathProvider(_ value: RestoreClusterFromSnapshotInput) -> Swift.String? {
        guard let snapshotArn = value.snapshotArn else {
            return nil
        }
        return "/cluster-snapshot/\(snapshotArn.urlPercentEncoding())/restore"
    }
}

extension StartClusterInput {

    static func urlPathProvider(_ value: StartClusterInput) -> Swift.String? {
        guard let clusterArn = value.clusterArn else {
            return nil
        }
        return "/cluster/\(clusterArn.urlPercentEncoding())/start"
    }
}

extension StopClusterInput {

    static func urlPathProvider(_ value: StopClusterInput) -> Swift.String? {
        guard let clusterArn = value.clusterArn else {
            return nil
        }
        return "/cluster/\(clusterArn.urlPercentEncoding())/stop"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension UpdateClusterInput {

    static func urlPathProvider(_ value: UpdateClusterInput) -> Swift.String? {
        guard let clusterArn = value.clusterArn else {
            return nil
        }
        return "/cluster/\(clusterArn.urlPercentEncoding())"
    }
}

extension ApplyPendingMaintenanceActionInput {

    static func write(value: ApplyPendingMaintenanceActionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["applyAction"].write(value.applyAction)
        try writer["applyOn"].write(value.applyOn)
        try writer["optInType"].write(value.optInType)
        try writer["resourceArn"].write(value.resourceArn)
    }
}

extension CopyClusterSnapshotInput {

    static func write(value: CopyClusterSnapshotInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["copyTags"].write(value.copyTags)
        try writer["kmsKeyId"].write(value.kmsKeyId)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["targetSnapshotName"].write(value.targetSnapshotName)
    }
}

extension CreateClusterInput {

    static func write(value: CreateClusterInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["adminUserName"].write(value.adminUserName)
        try writer["adminUserPassword"].write(value.adminUserPassword)
        try writer["authType"].write(value.authType)
        try writer["backupRetentionPeriod"].write(value.backupRetentionPeriod)
        try writer["clientToken"].write(value.clientToken)
        try writer["clusterName"].write(value.clusterName)
        try writer["kmsKeyId"].write(value.kmsKeyId)
        try writer["preferredBackupWindow"].write(value.preferredBackupWindow)
        try writer["preferredMaintenanceWindow"].write(value.preferredMaintenanceWindow)
        try writer["shardCapacity"].write(value.shardCapacity)
        try writer["shardCount"].write(value.shardCount)
        try writer["shardInstanceCount"].write(value.shardInstanceCount)
        try writer["subnetIds"].writeList(value.subnetIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["vpcSecurityGroupIds"].writeList(value.vpcSecurityGroupIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateClusterSnapshotInput {

    static func write(value: CreateClusterSnapshotInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["clusterArn"].write(value.clusterArn)
        try writer["snapshotName"].write(value.snapshotName)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension RestoreClusterFromSnapshotInput {

    static func write(value: RestoreClusterFromSnapshotInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["clusterName"].write(value.clusterName)
        try writer["kmsKeyId"].write(value.kmsKeyId)
        try writer["shardCapacity"].write(value.shardCapacity)
        try writer["shardInstanceCount"].write(value.shardInstanceCount)
        try writer["subnetIds"].writeList(value.subnetIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["vpcSecurityGroupIds"].writeList(value.vpcSecurityGroupIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension UpdateClusterInput {

    static func write(value: UpdateClusterInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["adminUserPassword"].write(value.adminUserPassword)
        try writer["authType"].write(value.authType)
        try writer["backupRetentionPeriod"].write(value.backupRetentionPeriod)
        try writer["clientToken"].write(value.clientToken)
        try writer["preferredBackupWindow"].write(value.preferredBackupWindow)
        try writer["preferredMaintenanceWindow"].write(value.preferredMaintenanceWindow)
        try writer["shardCapacity"].write(value.shardCapacity)
        try writer["shardCount"].write(value.shardCount)
        try writer["shardInstanceCount"].write(value.shardInstanceCount)
        try writer["subnetIds"].writeList(value.subnetIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["vpcSecurityGroupIds"].writeList(value.vpcSecurityGroupIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ApplyPendingMaintenanceActionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ApplyPendingMaintenanceActionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ApplyPendingMaintenanceActionOutput()
        value.resourcePendingMaintenanceAction = try reader["resourcePendingMaintenanceAction"].readIfPresent(with: DocDBElasticClientTypes.ResourcePendingMaintenanceAction.read(from:))
        return value
    }
}

extension CopyClusterSnapshotOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CopyClusterSnapshotOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CopyClusterSnapshotOutput()
        value.snapshot = try reader["snapshot"].readIfPresent(with: DocDBElasticClientTypes.ClusterSnapshot.read(from:))
        return value
    }
}

extension CreateClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateClusterOutput()
        value.cluster = try reader["cluster"].readIfPresent(with: DocDBElasticClientTypes.Cluster.read(from:))
        return value
    }
}

extension CreateClusterSnapshotOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateClusterSnapshotOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateClusterSnapshotOutput()
        value.snapshot = try reader["snapshot"].readIfPresent(with: DocDBElasticClientTypes.ClusterSnapshot.read(from:))
        return value
    }
}

extension DeleteClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteClusterOutput()
        value.cluster = try reader["cluster"].readIfPresent(with: DocDBElasticClientTypes.Cluster.read(from:))
        return value
    }
}

extension DeleteClusterSnapshotOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteClusterSnapshotOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteClusterSnapshotOutput()
        value.snapshot = try reader["snapshot"].readIfPresent(with: DocDBElasticClientTypes.ClusterSnapshot.read(from:))
        return value
    }
}

extension GetClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetClusterOutput()
        value.cluster = try reader["cluster"].readIfPresent(with: DocDBElasticClientTypes.Cluster.read(from:))
        return value
    }
}

extension GetClusterSnapshotOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetClusterSnapshotOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetClusterSnapshotOutput()
        value.snapshot = try reader["snapshot"].readIfPresent(with: DocDBElasticClientTypes.ClusterSnapshot.read(from:))
        return value
    }
}

extension GetPendingMaintenanceActionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetPendingMaintenanceActionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetPendingMaintenanceActionOutput()
        value.resourcePendingMaintenanceAction = try reader["resourcePendingMaintenanceAction"].readIfPresent(with: DocDBElasticClientTypes.ResourcePendingMaintenanceAction.read(from:))
        return value
    }
}

extension ListClustersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListClustersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListClustersOutput()
        value.clusters = try reader["clusters"].readListIfPresent(memberReadingClosure: DocDBElasticClientTypes.ClusterInList.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListClusterSnapshotsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListClusterSnapshotsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListClusterSnapshotsOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.snapshots = try reader["snapshots"].readListIfPresent(memberReadingClosure: DocDBElasticClientTypes.ClusterSnapshotInList.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListPendingMaintenanceActionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListPendingMaintenanceActionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListPendingMaintenanceActionsOutput()
        value.nextToken = try reader["nextToken"].readIfPresent()
        value.resourcePendingMaintenanceActions = try reader["resourcePendingMaintenanceActions"].readListIfPresent(memberReadingClosure: DocDBElasticClientTypes.ResourcePendingMaintenanceAction.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension RestoreClusterFromSnapshotOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RestoreClusterFromSnapshotOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = RestoreClusterFromSnapshotOutput()
        value.cluster = try reader["cluster"].readIfPresent(with: DocDBElasticClientTypes.Cluster.read(from:))
        return value
    }
}

extension StartClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartClusterOutput()
        value.cluster = try reader["cluster"].readIfPresent(with: DocDBElasticClientTypes.Cluster.read(from:))
        return value
    }
}

extension StopClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StopClusterOutput()
        value.cluster = try reader["cluster"].readIfPresent(with: DocDBElasticClientTypes.Cluster.read(from:))
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateClusterOutput()
        value.cluster = try reader["cluster"].readIfPresent(with: DocDBElasticClientTypes.Cluster.read(from:))
        return value
    }
}

enum ApplyPendingMaintenanceActionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CopyClusterSnapshotOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateClusterSnapshotOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteClusterSnapshotOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetClusterSnapshotOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetPendingMaintenanceActionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListClustersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListClusterSnapshotsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListPendingMaintenanceActionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RestoreClusterFromSnapshotOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.resourceId = try reader["resourceId"].readIfPresent() ?? ""
        value.properties.resourceType = try reader["resourceType"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.resourceId = try reader["resourceId"].readIfPresent() ?? ""
        value.properties.resourceType = try reader["resourceType"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.fieldList = try reader["fieldList"].readListIfPresent(memberReadingClosure: DocDBElasticClientTypes.ValidationExceptionField.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.reason = try reader["reason"].readIfPresent() ?? .sdkUnknown("")
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ThrottlingException()
        if let retryAfterSecondsHeaderValue = httpResponse.headers.value(for: "Retry-After") {
            value.properties.retryAfterSeconds = Swift.Int(retryAfterSecondsHeaderValue) ?? 0
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceQuotaExceededException {
        let reader = baseError.errorBodyReader
        var value = ServiceQuotaExceededException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DocDBElasticClientTypes.ResourcePendingMaintenanceAction {

    static func read(from reader: SmithyJSON.Reader) throws -> DocDBElasticClientTypes.ResourcePendingMaintenanceAction {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBElasticClientTypes.ResourcePendingMaintenanceAction()
        value.resourceArn = try reader["resourceArn"].readIfPresent()
        value.pendingMaintenanceActionDetails = try reader["pendingMaintenanceActionDetails"].readListIfPresent(memberReadingClosure: DocDBElasticClientTypes.PendingMaintenanceActionDetails.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DocDBElasticClientTypes.PendingMaintenanceActionDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> DocDBElasticClientTypes.PendingMaintenanceActionDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBElasticClientTypes.PendingMaintenanceActionDetails()
        value.action = try reader["action"].readIfPresent() ?? ""
        value.autoAppliedAfterDate = try reader["autoAppliedAfterDate"].readIfPresent()
        value.forcedApplyDate = try reader["forcedApplyDate"].readIfPresent()
        value.optInStatus = try reader["optInStatus"].readIfPresent()
        value.currentApplyDate = try reader["currentApplyDate"].readIfPresent()
        value.description = try reader["description"].readIfPresent()
        return value
    }
}

extension DocDBElasticClientTypes.ClusterSnapshot {

    static func read(from reader: SmithyJSON.Reader) throws -> DocDBElasticClientTypes.ClusterSnapshot {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBElasticClientTypes.ClusterSnapshot()
        value.subnetIds = try reader["subnetIds"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.snapshotName = try reader["snapshotName"].readIfPresent() ?? ""
        value.snapshotArn = try reader["snapshotArn"].readIfPresent() ?? ""
        value.snapshotCreationTime = try reader["snapshotCreationTime"].readIfPresent() ?? ""
        value.clusterArn = try reader["clusterArn"].readIfPresent() ?? ""
        value.clusterCreationTime = try reader["clusterCreationTime"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        value.vpcSecurityGroupIds = try reader["vpcSecurityGroupIds"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.adminUserName = try reader["adminUserName"].readIfPresent() ?? ""
        value.kmsKeyId = try reader["kmsKeyId"].readIfPresent() ?? ""
        value.snapshotType = try reader["snapshotType"].readIfPresent()
        return value
    }
}

extension DocDBElasticClientTypes.Cluster {

    static func read(from reader: SmithyJSON.Reader) throws -> DocDBElasticClientTypes.Cluster {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBElasticClientTypes.Cluster()
        value.clusterName = try reader["clusterName"].readIfPresent() ?? ""
        value.clusterArn = try reader["clusterArn"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        value.clusterEndpoint = try reader["clusterEndpoint"].readIfPresent() ?? ""
        value.createTime = try reader["createTime"].readIfPresent() ?? ""
        value.adminUserName = try reader["adminUserName"].readIfPresent() ?? ""
        value.authType = try reader["authType"].readIfPresent() ?? .sdkUnknown("")
        value.shardCapacity = try reader["shardCapacity"].readIfPresent() ?? 0
        value.shardCount = try reader["shardCount"].readIfPresent() ?? 0
        value.vpcSecurityGroupIds = try reader["vpcSecurityGroupIds"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.subnetIds = try reader["subnetIds"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.preferredMaintenanceWindow = try reader["preferredMaintenanceWindow"].readIfPresent() ?? ""
        value.kmsKeyId = try reader["kmsKeyId"].readIfPresent() ?? ""
        value.shards = try reader["shards"].readListIfPresent(memberReadingClosure: DocDBElasticClientTypes.Shard.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.backupRetentionPeriod = try reader["backupRetentionPeriod"].readIfPresent()
        value.preferredBackupWindow = try reader["preferredBackupWindow"].readIfPresent()
        value.shardInstanceCount = try reader["shardInstanceCount"].readIfPresent()
        return value
    }
}

extension DocDBElasticClientTypes.Shard {

    static func read(from reader: SmithyJSON.Reader) throws -> DocDBElasticClientTypes.Shard {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBElasticClientTypes.Shard()
        value.shardId = try reader["shardId"].readIfPresent() ?? ""
        value.createTime = try reader["createTime"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension DocDBElasticClientTypes.ClusterInList {

    static func read(from reader: SmithyJSON.Reader) throws -> DocDBElasticClientTypes.ClusterInList {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBElasticClientTypes.ClusterInList()
        value.clusterName = try reader["clusterName"].readIfPresent() ?? ""
        value.clusterArn = try reader["clusterArn"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension DocDBElasticClientTypes.ClusterSnapshotInList {

    static func read(from reader: SmithyJSON.Reader) throws -> DocDBElasticClientTypes.ClusterSnapshotInList {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBElasticClientTypes.ClusterSnapshotInList()
        value.snapshotName = try reader["snapshotName"].readIfPresent() ?? ""
        value.snapshotArn = try reader["snapshotArn"].readIfPresent() ?? ""
        value.clusterArn = try reader["clusterArn"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        value.snapshotCreationTime = try reader["snapshotCreationTime"].readIfPresent() ?? ""
        return value
    }
}

extension DocDBElasticClientTypes.ValidationExceptionField {

    static func read(from reader: SmithyJSON.Reader) throws -> DocDBElasticClientTypes.ValidationExceptionField {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DocDBElasticClientTypes.ValidationExceptionField()
        value.name = try reader["name"].readIfPresent() ?? ""
        value.message = try reader["message"].readIfPresent() ?? ""
        return value
    }
}

public enum DocDBElasticClientTypes {}
