//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHT<PERSON>API.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.AWSJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError


public struct DeletePipelineOutput: Swift.Sendable {

    public init() { }
}

public struct SetStatusOutput: Swift.Sendable {

    public init() { }
}

/// An internal service error occurred.
public struct InternalServiceError: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Description of the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServiceError" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request was not valid. Verify that your request was properly formatted, that the signature was generated with the correct credentials, and that you haven't exceeded any of the service limits for your account.
public struct InvalidRequestException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Description of the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidRequestException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified pipeline has been deleted.
public struct PipelineDeletedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Description of the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "PipelineDeletedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified pipeline was not found. Verify that you used the correct user and account identifiers.
public struct PipelineNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Description of the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "PipelineNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension DataPipelineClientTypes {

    /// A value or list of parameter values.
    public struct ParameterValue: Swift.Sendable {
        /// The ID of the parameter value.
        /// This member is required.
        public var id: Swift.String?
        /// The field value, expressed as a String.
        /// This member is required.
        public var stringValue: Swift.String?

        public init(
            id: Swift.String? = nil,
            stringValue: Swift.String? = nil
        )
        {
            self.id = id
            self.stringValue = stringValue
        }
    }
}

/// Contains the parameters for ActivatePipeline.
public struct ActivatePipelineInput: Swift.Sendable {
    /// A list of parameter values to pass to the pipeline at activation.
    public var parameterValues: [DataPipelineClientTypes.ParameterValue]?
    /// The ID of the pipeline.
    /// This member is required.
    public var pipelineId: Swift.String?
    /// The date and time to resume the pipeline. By default, the pipeline resumes from the last completed execution.
    public var startTimestamp: Foundation.Date?

    public init(
        parameterValues: [DataPipelineClientTypes.ParameterValue]? = nil,
        pipelineId: Swift.String? = nil,
        startTimestamp: Foundation.Date? = nil
    )
    {
        self.parameterValues = parameterValues
        self.pipelineId = pipelineId
        self.startTimestamp = startTimestamp
    }
}

/// Contains the output of ActivatePipeline.
public struct ActivatePipelineOutput: Swift.Sendable {

    public init() { }
}

extension DataPipelineClientTypes {

    /// Tags are key/value pairs defined by a user and associated with a pipeline to control access. AWS Data Pipeline allows you to associate ten tags per pipeline. For more information, see [Controlling User Access to Pipelines](http://docs.aws.amazon.com/datapipeline/latest/DeveloperGuide/dp-control-access.html) in the AWS Data Pipeline Developer Guide.
    public struct Tag: Swift.Sendable {
        /// The key name of a tag defined by a user. For more information, see [Controlling User Access to Pipelines](http://docs.aws.amazon.com/datapipeline/latest/DeveloperGuide/dp-control-access.html) in the AWS Data Pipeline Developer Guide.
        /// This member is required.
        public var key: Swift.String?
        /// The optional value portion of a tag defined by a user. For more information, see [Controlling User Access to Pipelines](http://docs.aws.amazon.com/datapipeline/latest/DeveloperGuide/dp-control-access.html) in the AWS Data Pipeline Developer Guide.
        /// This member is required.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

/// Contains the parameters for AddTags.
public struct AddTagsInput: Swift.Sendable {
    /// The ID of the pipeline.
    /// This member is required.
    public var pipelineId: Swift.String?
    /// The tags to add, as key/value pairs.
    /// This member is required.
    public var tags: [DataPipelineClientTypes.Tag]?

    public init(
        pipelineId: Swift.String? = nil,
        tags: [DataPipelineClientTypes.Tag]? = nil
    )
    {
        self.pipelineId = pipelineId
        self.tags = tags
    }
}

/// Contains the output of AddTags.
public struct AddTagsOutput: Swift.Sendable {

    public init() { }
}

/// Contains the parameters for CreatePipeline.
public struct CreatePipelineInput: Swift.Sendable {
    /// The description for the pipeline.
    public var description: Swift.String?
    /// The name for the pipeline. You can use the same name for multiple pipelines associated with your AWS account, because AWS Data Pipeline assigns each pipeline a unique pipeline identifier.
    /// This member is required.
    public var name: Swift.String?
    /// A list of tags to associate with the pipeline at creation. Tags let you control access to pipelines. For more information, see [Controlling User Access to Pipelines](http://docs.aws.amazon.com/datapipeline/latest/DeveloperGuide/dp-control-access.html) in the AWS Data Pipeline Developer Guide.
    public var tags: [DataPipelineClientTypes.Tag]?
    /// A unique identifier. This identifier is not the same as the pipeline identifier assigned by AWS Data Pipeline. You are responsible for defining the format and ensuring the uniqueness of this identifier. You use this parameter to ensure idempotency during repeated calls to CreatePipeline. For example, if the first call to CreatePipeline does not succeed, you can pass in the same unique identifier and pipeline name combination on a subsequent call to CreatePipeline. CreatePipeline ensures that if a pipeline already exists with the same name and unique identifier, a new pipeline is not created. Instead, you'll receive the pipeline identifier from the previous attempt. The uniqueness of the name and unique identifier combination is scoped to the AWS account or IAM user credentials.
    /// This member is required.
    public var uniqueId: Swift.String?

    public init(
        description: Swift.String? = nil,
        name: Swift.String? = nil,
        tags: [DataPipelineClientTypes.Tag]? = nil,
        uniqueId: Swift.String? = nil
    )
    {
        self.description = description
        self.name = name
        self.tags = tags
        self.uniqueId = uniqueId
    }
}

/// Contains the output of CreatePipeline.
public struct CreatePipelineOutput: Swift.Sendable {
    /// The ID that AWS Data Pipeline assigns the newly created pipeline. For example, df-06372391ZG65EXAMPLE.
    /// This member is required.
    public var pipelineId: Swift.String?

    public init(
        pipelineId: Swift.String? = nil
    )
    {
        self.pipelineId = pipelineId
    }
}

/// Contains the parameters for DeactivatePipeline.
public struct DeactivatePipelineInput: Swift.Sendable {
    /// Indicates whether to cancel any running objects. The default is true, which sets the state of any running objects to CANCELED. If this value is false, the pipeline is deactivated after all running objects finish.
    public var cancelActive: Swift.Bool?
    /// The ID of the pipeline.
    /// This member is required.
    public var pipelineId: Swift.String?

    public init(
        cancelActive: Swift.Bool? = nil,
        pipelineId: Swift.String? = nil
    )
    {
        self.cancelActive = cancelActive
        self.pipelineId = pipelineId
    }
}

/// Contains the output of DeactivatePipeline.
public struct DeactivatePipelineOutput: Swift.Sendable {

    public init() { }
}

/// Contains the parameters for DeletePipeline.
public struct DeletePipelineInput: Swift.Sendable {
    /// The ID of the pipeline.
    /// This member is required.
    public var pipelineId: Swift.String?

    public init(
        pipelineId: Swift.String? = nil
    )
    {
        self.pipelineId = pipelineId
    }
}

/// Contains the parameters for DescribeObjects.
public struct DescribeObjectsInput: Swift.Sendable {
    /// Indicates whether any expressions in the object should be evaluated when the object descriptions are returned.
    public var evaluateExpressions: Swift.Bool?
    /// The starting point for the results to be returned. For the first call, this value should be empty. As long as there are more results, continue to call DescribeObjects with the marker value from the previous call to retrieve the next set of results.
    public var marker: Swift.String?
    /// The IDs of the pipeline objects that contain the definitions to be described. You can pass as many as 25 identifiers in a single call to DescribeObjects.
    /// This member is required.
    public var objectIds: [Swift.String]?
    /// The ID of the pipeline that contains the object definitions.
    /// This member is required.
    public var pipelineId: Swift.String?

    public init(
        evaluateExpressions: Swift.Bool? = false,
        marker: Swift.String? = nil,
        objectIds: [Swift.String]? = nil,
        pipelineId: Swift.String? = nil
    )
    {
        self.evaluateExpressions = evaluateExpressions
        self.marker = marker
        self.objectIds = objectIds
        self.pipelineId = pipelineId
    }
}

extension DataPipelineClientTypes {

    /// A key-value pair that describes a property of a pipeline object. The value is specified as either a string value (StringValue) or a reference to another object (RefValue) but not as both.
    public struct Field: Swift.Sendable {
        /// The field identifier.
        /// This member is required.
        public var key: Swift.String?
        /// The field value, expressed as the identifier of another object.
        public var refValue: Swift.String?
        /// The field value, expressed as a String.
        public var stringValue: Swift.String?

        public init(
            key: Swift.String? = nil,
            refValue: Swift.String? = nil,
            stringValue: Swift.String? = nil
        )
        {
            self.key = key
            self.refValue = refValue
            self.stringValue = stringValue
        }
    }
}

extension DataPipelineClientTypes {

    /// Contains information about a pipeline object. This can be a logical, physical, or physical attempt pipeline object. The complete set of components of a pipeline defines the pipeline.
    public struct PipelineObject: Swift.Sendable {
        /// Key-value pairs that define the properties of the object.
        /// This member is required.
        public var fields: [DataPipelineClientTypes.Field]?
        /// The ID of the object.
        /// This member is required.
        public var id: Swift.String?
        /// The name of the object.
        /// This member is required.
        public var name: Swift.String?

        public init(
            fields: [DataPipelineClientTypes.Field]? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil
        )
        {
            self.fields = fields
            self.id = id
            self.name = name
        }
    }
}

/// Contains the output of DescribeObjects.
public struct DescribeObjectsOutput: Swift.Sendable {
    /// Indicates whether there are more results to return.
    public var hasMoreResults: Swift.Bool
    /// The starting point for the next page of results. To view the next page of results, call DescribeObjects again with this marker value. If the value is null, there are no more results.
    public var marker: Swift.String?
    /// An array of object definitions.
    /// This member is required.
    public var pipelineObjects: [DataPipelineClientTypes.PipelineObject]?

    public init(
        hasMoreResults: Swift.Bool = false,
        marker: Swift.String? = nil,
        pipelineObjects: [DataPipelineClientTypes.PipelineObject]? = nil
    )
    {
        self.hasMoreResults = hasMoreResults
        self.marker = marker
        self.pipelineObjects = pipelineObjects
    }
}

/// Contains the parameters for DescribePipelines.
public struct DescribePipelinesInput: Swift.Sendable {
    /// The IDs of the pipelines to describe. You can pass as many as 25 identifiers in a single call. To obtain pipeline IDs, call [ListPipelines].
    /// This member is required.
    public var pipelineIds: [Swift.String]?

    public init(
        pipelineIds: [Swift.String]? = nil
    )
    {
        self.pipelineIds = pipelineIds
    }
}

extension DataPipelineClientTypes {

    /// Contains pipeline metadata.
    public struct PipelineDescription: Swift.Sendable {
        /// Description of the pipeline.
        public var description: Swift.String?
        /// A list of read-only fields that contain metadata about the pipeline: @userId, @accountId, and @pipelineState.
        /// This member is required.
        public var fields: [DataPipelineClientTypes.Field]?
        /// The name of the pipeline.
        /// This member is required.
        public var name: Swift.String?
        /// The pipeline identifier that was assigned by AWS Data Pipeline. This is a string of the form df-297EG78HU43EEXAMPLE.
        /// This member is required.
        public var pipelineId: Swift.String?
        /// A list of tags to associated with a pipeline. Tags let you control access to pipelines. For more information, see [Controlling User Access to Pipelines](http://docs.aws.amazon.com/datapipeline/latest/DeveloperGuide/dp-control-access.html) in the AWS Data Pipeline Developer Guide.
        public var tags: [DataPipelineClientTypes.Tag]?

        public init(
            description: Swift.String? = nil,
            fields: [DataPipelineClientTypes.Field]? = nil,
            name: Swift.String? = nil,
            pipelineId: Swift.String? = nil,
            tags: [DataPipelineClientTypes.Tag]? = nil
        )
        {
            self.description = description
            self.fields = fields
            self.name = name
            self.pipelineId = pipelineId
            self.tags = tags
        }
    }
}

/// Contains the output of DescribePipelines.
public struct DescribePipelinesOutput: Swift.Sendable {
    /// An array of descriptions for the specified pipelines.
    /// This member is required.
    public var pipelineDescriptionList: [DataPipelineClientTypes.PipelineDescription]?

    public init(
        pipelineDescriptionList: [DataPipelineClientTypes.PipelineDescription]? = nil
    )
    {
        self.pipelineDescriptionList = pipelineDescriptionList
    }
}

/// The specified task was not found.
public struct TaskNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Description of the error message.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TaskNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Contains the parameters for EvaluateExpression.
public struct EvaluateExpressionInput: Swift.Sendable {
    /// The expression to evaluate.
    /// This member is required.
    public var expression: Swift.String?
    /// The ID of the object.
    /// This member is required.
    public var objectId: Swift.String?
    /// The ID of the pipeline.
    /// This member is required.
    public var pipelineId: Swift.String?

    public init(
        expression: Swift.String? = nil,
        objectId: Swift.String? = nil,
        pipelineId: Swift.String? = nil
    )
    {
        self.expression = expression
        self.objectId = objectId
        self.pipelineId = pipelineId
    }
}

/// Contains the output of EvaluateExpression.
public struct EvaluateExpressionOutput: Swift.Sendable {
    /// The evaluated expression.
    /// This member is required.
    public var evaluatedExpression: Swift.String?

    public init(
        evaluatedExpression: Swift.String? = nil
    )
    {
        self.evaluatedExpression = evaluatedExpression
    }
}

/// Contains the parameters for GetPipelineDefinition.
public struct GetPipelineDefinitionInput: Swift.Sendable {
    /// The ID of the pipeline.
    /// This member is required.
    public var pipelineId: Swift.String?
    /// The version of the pipeline definition to retrieve. Set this parameter to latest (default) to use the last definition saved to the pipeline or active to use the last definition that was activated.
    public var version: Swift.String?

    public init(
        pipelineId: Swift.String? = nil,
        version: Swift.String? = nil
    )
    {
        self.pipelineId = pipelineId
        self.version = version
    }
}

extension DataPipelineClientTypes {

    /// The attributes allowed or specified with a parameter object.
    public struct ParameterAttribute: Swift.Sendable {
        /// The field identifier.
        /// This member is required.
        public var key: Swift.String?
        /// The field value, expressed as a String.
        /// This member is required.
        public var stringValue: Swift.String?

        public init(
            key: Swift.String? = nil,
            stringValue: Swift.String? = nil
        )
        {
            self.key = key
            self.stringValue = stringValue
        }
    }
}

extension DataPipelineClientTypes {

    /// Contains information about a parameter object.
    public struct ParameterObject: Swift.Sendable {
        /// The attributes of the parameter object.
        /// This member is required.
        public var attributes: [DataPipelineClientTypes.ParameterAttribute]?
        /// The ID of the parameter object.
        /// This member is required.
        public var id: Swift.String?

        public init(
            attributes: [DataPipelineClientTypes.ParameterAttribute]? = nil,
            id: Swift.String? = nil
        )
        {
            self.attributes = attributes
            self.id = id
        }
    }
}

/// Contains the output of GetPipelineDefinition.
public struct GetPipelineDefinitionOutput: Swift.Sendable {
    /// The parameter objects used in the pipeline definition.
    public var parameterObjects: [DataPipelineClientTypes.ParameterObject]?
    /// The parameter values used in the pipeline definition.
    public var parameterValues: [DataPipelineClientTypes.ParameterValue]?
    /// The objects defined in the pipeline.
    public var pipelineObjects: [DataPipelineClientTypes.PipelineObject]?

    public init(
        parameterObjects: [DataPipelineClientTypes.ParameterObject]? = nil,
        parameterValues: [DataPipelineClientTypes.ParameterValue]? = nil,
        pipelineObjects: [DataPipelineClientTypes.PipelineObject]? = nil
    )
    {
        self.parameterObjects = parameterObjects
        self.parameterValues = parameterValues
        self.pipelineObjects = pipelineObjects
    }
}

/// Contains the parameters for ListPipelines.
public struct ListPipelinesInput: Swift.Sendable {
    /// The starting point for the results to be returned. For the first call, this value should be empty. As long as there are more results, continue to call ListPipelines with the marker value from the previous call to retrieve the next set of results.
    public var marker: Swift.String?

    public init(
        marker: Swift.String? = nil
    )
    {
        self.marker = marker
    }
}

extension DataPipelineClientTypes {

    /// Contains the name and identifier of a pipeline.
    public struct PipelineIdName: Swift.Sendable {
        /// The ID of the pipeline that was assigned by AWS Data Pipeline. This is a string of the form df-297EG78HU43EEXAMPLE.
        public var id: Swift.String?
        /// The name of the pipeline.
        public var name: Swift.String?

        public init(
            id: Swift.String? = nil,
            name: Swift.String? = nil
        )
        {
            self.id = id
            self.name = name
        }
    }
}

/// Contains the output of ListPipelines.
public struct ListPipelinesOutput: Swift.Sendable {
    /// Indicates whether there are more results that can be obtained by a subsequent call.
    public var hasMoreResults: Swift.Bool
    /// The starting point for the next page of results. To view the next page of results, call ListPipelinesOutput again with this marker value. If the value is null, there are no more results.
    public var marker: Swift.String?
    /// The pipeline identifiers. If you require additional information about the pipelines, you can use these identifiers to call [DescribePipelines] and [GetPipelineDefinition].
    /// This member is required.
    public var pipelineIdList: [DataPipelineClientTypes.PipelineIdName]?

    public init(
        hasMoreResults: Swift.Bool = false,
        marker: Swift.String? = nil,
        pipelineIdList: [DataPipelineClientTypes.PipelineIdName]? = nil
    )
    {
        self.hasMoreResults = hasMoreResults
        self.marker = marker
        self.pipelineIdList = pipelineIdList
    }
}

extension DataPipelineClientTypes {

    /// Identity information for the EC2 instance that is hosting the task runner. You can get this value by calling a metadata URI from the EC2 instance. For more information, see [Instance Metadata](http://docs.aws.amazon.com/AWSEC2/latest/UserGuide/AESDG-chapter-instancedata.html) in the Amazon Elastic Compute Cloud User Guide. Passing in this value proves that your task runner is running on an EC2 instance, and ensures the proper AWS Data Pipeline service charges are applied to your pipeline.
    public struct InstanceIdentity: Swift.Sendable {
        /// A description of an EC2 instance that is generated when the instance is launched and exposed to the instance via the instance metadata service in the form of a JSON representation of an object.
        public var document: Swift.String?
        /// A signature which can be used to verify the accuracy and authenticity of the information provided in the instance identity document.
        public var signature: Swift.String?

        public init(
            document: Swift.String? = nil,
            signature: Swift.String? = nil
        )
        {
            self.document = document
            self.signature = signature
        }
    }
}

/// Contains the parameters for PollForTask.
public struct PollForTaskInput: Swift.Sendable {
    /// The public DNS name of the calling task runner.
    public var hostname: Swift.String?
    /// Identity information for the EC2 instance that is hosting the task runner. You can get this value from the instance using http://***************/latest/meta-data/instance-id. For more information, see [Instance Metadata](http://docs.aws.amazon.com/AWSEC2/latest/UserGuide/AESDG-chapter-instancedata.html) in the Amazon Elastic Compute Cloud User Guide. Passing in this value proves that your task runner is running on an EC2 instance, and ensures the proper AWS Data Pipeline service charges are applied to your pipeline.
    public var instanceIdentity: DataPipelineClientTypes.InstanceIdentity?
    /// The type of task the task runner is configured to accept and process. The worker group is set as a field on objects in the pipeline when they are created. You can only specify a single value for workerGroup in the call to PollForTask. There are no wildcard values permitted in workerGroup; the string must be an exact, case-sensitive, match.
    /// This member is required.
    public var workerGroup: Swift.String?

    public init(
        hostname: Swift.String? = nil,
        instanceIdentity: DataPipelineClientTypes.InstanceIdentity? = nil,
        workerGroup: Swift.String? = nil
    )
    {
        self.hostname = hostname
        self.instanceIdentity = instanceIdentity
        self.workerGroup = workerGroup
    }
}

extension DataPipelineClientTypes {

    /// Contains information about a pipeline task that is assigned to a task runner.
    public struct TaskObject: Swift.Sendable {
        /// The ID of the pipeline task attempt object. AWS Data Pipeline uses this value to track how many times a task is attempted.
        public var attemptId: Swift.String?
        /// Connection information for the location where the task runner will publish the output of the task.
        public var objects: [Swift.String: DataPipelineClientTypes.PipelineObject]?
        /// The ID of the pipeline that provided the task.
        public var pipelineId: Swift.String?
        /// An internal identifier for the task. This ID is passed to the [SetTaskStatus] and [ReportTaskProgress] actions.
        public var taskId: Swift.String?

        public init(
            attemptId: Swift.String? = nil,
            objects: [Swift.String: DataPipelineClientTypes.PipelineObject]? = nil,
            pipelineId: Swift.String? = nil,
            taskId: Swift.String? = nil
        )
        {
            self.attemptId = attemptId
            self.objects = objects
            self.pipelineId = pipelineId
            self.taskId = taskId
        }
    }
}

/// Contains the output of PollForTask.
public struct PollForTaskOutput: Swift.Sendable {
    /// The information needed to complete the task that is being assigned to the task runner. One of the fields returned in this object is taskId, which contains an identifier for the task being assigned. The calling task runner uses taskId in subsequent calls to [ReportTaskProgress] and [SetTaskStatus].
    public var taskObject: DataPipelineClientTypes.TaskObject?

    public init(
        taskObject: DataPipelineClientTypes.TaskObject? = nil
    )
    {
        self.taskObject = taskObject
    }
}

/// Contains the parameters for PutPipelineDefinition.
public struct PutPipelineDefinitionInput: Swift.Sendable {
    /// The parameter objects used with the pipeline.
    public var parameterObjects: [DataPipelineClientTypes.ParameterObject]?
    /// The parameter values used with the pipeline.
    public var parameterValues: [DataPipelineClientTypes.ParameterValue]?
    /// The ID of the pipeline.
    /// This member is required.
    public var pipelineId: Swift.String?
    /// The objects that define the pipeline. These objects overwrite the existing pipeline definition.
    /// This member is required.
    public var pipelineObjects: [DataPipelineClientTypes.PipelineObject]?

    public init(
        parameterObjects: [DataPipelineClientTypes.ParameterObject]? = nil,
        parameterValues: [DataPipelineClientTypes.ParameterValue]? = nil,
        pipelineId: Swift.String? = nil,
        pipelineObjects: [DataPipelineClientTypes.PipelineObject]? = nil
    )
    {
        self.parameterObjects = parameterObjects
        self.parameterValues = parameterValues
        self.pipelineId = pipelineId
        self.pipelineObjects = pipelineObjects
    }
}

extension DataPipelineClientTypes {

    /// Defines a validation error. Validation errors prevent pipeline activation. The set of validation errors that can be returned are defined by AWS Data Pipeline.
    public struct ValidationError: Swift.Sendable {
        /// A description of the validation error.
        public var errors: [Swift.String]?
        /// The identifier of the object that contains the validation error.
        public var id: Swift.String?

        public init(
            errors: [Swift.String]? = nil,
            id: Swift.String? = nil
        )
        {
            self.errors = errors
            self.id = id
        }
    }
}

extension DataPipelineClientTypes {

    /// Defines a validation warning. Validation warnings do not prevent pipeline activation. The set of validation warnings that can be returned are defined by AWS Data Pipeline.
    public struct ValidationWarning: Swift.Sendable {
        /// The identifier of the object that contains the validation warning.
        public var id: Swift.String?
        /// A description of the validation warning.
        public var warnings: [Swift.String]?

        public init(
            id: Swift.String? = nil,
            warnings: [Swift.String]? = nil
        )
        {
            self.id = id
            self.warnings = warnings
        }
    }
}

/// Contains the output of PutPipelineDefinition.
public struct PutPipelineDefinitionOutput: Swift.Sendable {
    /// Indicates whether there were validation errors, and the pipeline definition is stored but cannot be activated until you correct the pipeline and call PutPipelineDefinition to commit the corrected pipeline.
    /// This member is required.
    public var errored: Swift.Bool
    /// The validation errors that are associated with the objects defined in pipelineObjects.
    public var validationErrors: [DataPipelineClientTypes.ValidationError]?
    /// The validation warnings that are associated with the objects defined in pipelineObjects.
    public var validationWarnings: [DataPipelineClientTypes.ValidationWarning]?

    public init(
        errored: Swift.Bool = false,
        validationErrors: [DataPipelineClientTypes.ValidationError]? = nil,
        validationWarnings: [DataPipelineClientTypes.ValidationWarning]? = nil
    )
    {
        self.errored = errored
        self.validationErrors = validationErrors
        self.validationWarnings = validationWarnings
    }
}

extension DataPipelineClientTypes {

    public enum OperatorType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case between
        case equal
        case greaterthanorequal
        case lessthanorequal
        case referenceequal
        case sdkUnknown(Swift.String)

        public static var allCases: [OperatorType] {
            return [
                .between,
                .equal,
                .greaterthanorequal,
                .lessthanorequal,
                .referenceequal
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .between: return "BETWEEN"
            case .equal: return "EQ"
            case .greaterthanorequal: return "GE"
            case .lessthanorequal: return "LE"
            case .referenceequal: return "REF_EQ"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DataPipelineClientTypes {

    /// Contains a logical operation for comparing the value of a field with a specified value.
    public struct Operator: Swift.Sendable {
        /// The logical operation to be performed: equal (EQ), equal reference (REF_EQ), less than or equal (LE), greater than or equal (GE), or between (BETWEEN). Equal reference (REF_EQ) can be used only with reference fields. The other comparison types can be used only with String fields. The comparison types you can use apply only to certain object fields, as detailed below. The comparison operators EQ and REF_EQ act on the following fields:
        ///
        /// * name
        ///
        /// * @sphere
        ///
        /// * parent
        ///
        /// * @componentParent
        ///
        /// * @instanceParent
        ///
        /// * @status
        ///
        /// * @scheduledStartTime
        ///
        /// * @scheduledEndTime
        ///
        /// * @actualStartTime
        ///
        /// * @actualEndTime
        ///
        ///
        /// The comparison operators GE, LE, and BETWEEN act on the following fields:
        ///
        /// * @scheduledStartTime
        ///
        /// * @scheduledEndTime
        ///
        /// * @actualStartTime
        ///
        /// * @actualEndTime
        ///
        ///
        /// Note that fields beginning with the at sign (@) are read-only and set by the web service. When you name fields, you should choose names containing only alpha-numeric values, as symbols may be reserved by AWS Data Pipeline. User-defined fields that you add to a pipeline should prefix their name with the string "my".
        public var type: DataPipelineClientTypes.OperatorType?
        /// The value that the actual field value will be compared with.
        public var values: [Swift.String]?

        public init(
            type: DataPipelineClientTypes.OperatorType? = nil,
            values: [Swift.String]? = nil
        )
        {
            self.type = type
            self.values = values
        }
    }
}

extension DataPipelineClientTypes {

    /// A comparision that is used to determine whether a query should return this object.
    public struct Selector: Swift.Sendable {
        /// The name of the field that the operator will be applied to. The field name is the "key" portion of the field definition in the pipeline definition syntax that is used by the AWS Data Pipeline API. If the field is not set on the object, the condition fails.
        public var fieldName: Swift.String?
        /// Contains a logical operation for comparing the value of a field with a specified value.
        public var `operator`: DataPipelineClientTypes.Operator?

        public init(
            fieldName: Swift.String? = nil,
            `operator`: DataPipelineClientTypes.Operator? = nil
        )
        {
            self.fieldName = fieldName
            self.`operator` = `operator`
        }
    }
}

extension DataPipelineClientTypes {

    /// Defines the query to run against an object.
    public struct Query: Swift.Sendable {
        /// List of selectors that define the query. An object must satisfy all of the selectors to match the query.
        public var selectors: [DataPipelineClientTypes.Selector]?

        public init(
            selectors: [DataPipelineClientTypes.Selector]? = nil
        )
        {
            self.selectors = selectors
        }
    }
}

/// Contains the parameters for QueryObjects.
public struct QueryObjectsInput: Swift.Sendable {
    /// The maximum number of object names that QueryObjects will return in a single call. The default value is 100.
    public var limit: Swift.Int?
    /// The starting point for the results to be returned. For the first call, this value should be empty. As long as there are more results, continue to call QueryObjects with the marker value from the previous call to retrieve the next set of results.
    public var marker: Swift.String?
    /// The ID of the pipeline.
    /// This member is required.
    public var pipelineId: Swift.String?
    /// The query that defines the objects to be returned. The Query object can contain a maximum of ten selectors. The conditions in the query are limited to top-level String fields in the object. These filters can be applied to components, instances, and attempts.
    public var query: DataPipelineClientTypes.Query?
    /// Indicates whether the query applies to components or instances. The possible values are: COMPONENT, INSTANCE, and ATTEMPT.
    /// This member is required.
    public var sphere: Swift.String?

    public init(
        limit: Swift.Int? = nil,
        marker: Swift.String? = nil,
        pipelineId: Swift.String? = nil,
        query: DataPipelineClientTypes.Query? = nil,
        sphere: Swift.String? = nil
    )
    {
        self.limit = limit
        self.marker = marker
        self.pipelineId = pipelineId
        self.query = query
        self.sphere = sphere
    }
}

/// Contains the output of QueryObjects.
public struct QueryObjectsOutput: Swift.Sendable {
    /// Indicates whether there are more results that can be obtained by a subsequent call.
    public var hasMoreResults: Swift.Bool
    /// The identifiers that match the query selectors.
    public var ids: [Swift.String]?
    /// The starting point for the next page of results. To view the next page of results, call QueryObjects again with this marker value. If the value is null, there are no more results.
    public var marker: Swift.String?

    public init(
        hasMoreResults: Swift.Bool = false,
        ids: [Swift.String]? = nil,
        marker: Swift.String? = nil
    )
    {
        self.hasMoreResults = hasMoreResults
        self.ids = ids
        self.marker = marker
    }
}

/// Contains the parameters for RemoveTags.
public struct RemoveTagsInput: Swift.Sendable {
    /// The ID of the pipeline.
    /// This member is required.
    public var pipelineId: Swift.String?
    /// The keys of the tags to remove.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        pipelineId: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.pipelineId = pipelineId
        self.tagKeys = tagKeys
    }
}

/// Contains the output of RemoveTags.
public struct RemoveTagsOutput: Swift.Sendable {

    public init() { }
}

/// Contains the parameters for ReportTaskProgress.
public struct ReportTaskProgressInput: Swift.Sendable {
    /// Key-value pairs that define the properties of the ReportTaskProgressInput object.
    public var fields: [DataPipelineClientTypes.Field]?
    /// The ID of the task assigned to the task runner. This value is provided in the response for [PollForTask].
    /// This member is required.
    public var taskId: Swift.String?

    public init(
        fields: [DataPipelineClientTypes.Field]? = nil,
        taskId: Swift.String? = nil
    )
    {
        self.fields = fields
        self.taskId = taskId
    }
}

/// Contains the output of ReportTaskProgress.
public struct ReportTaskProgressOutput: Swift.Sendable {
    /// If true, the calling task runner should cancel processing of the task. The task runner does not need to call [SetTaskStatus] for canceled tasks.
    /// This member is required.
    public var canceled: Swift.Bool

    public init(
        canceled: Swift.Bool = false
    )
    {
        self.canceled = canceled
    }
}

/// Contains the parameters for ReportTaskRunnerHeartbeat.
public struct ReportTaskRunnerHeartbeatInput: Swift.Sendable {
    /// The public DNS name of the task runner.
    public var hostname: Swift.String?
    /// The ID of the task runner. This value should be unique across your AWS account. In the case of AWS Data Pipeline Task Runner launched on a resource managed by AWS Data Pipeline, the web service provides a unique identifier when it launches the application. If you have written a custom task runner, you should assign a unique identifier for the task runner.
    /// This member is required.
    public var taskrunnerId: Swift.String?
    /// The type of task the task runner is configured to accept and process. The worker group is set as a field on objects in the pipeline when they are created. You can only specify a single value for workerGroup. There are no wildcard values permitted in workerGroup; the string must be an exact, case-sensitive, match.
    public var workerGroup: Swift.String?

    public init(
        hostname: Swift.String? = nil,
        taskrunnerId: Swift.String? = nil,
        workerGroup: Swift.String? = nil
    )
    {
        self.hostname = hostname
        self.taskrunnerId = taskrunnerId
        self.workerGroup = workerGroup
    }
}

/// Contains the output of ReportTaskRunnerHeartbeat.
public struct ReportTaskRunnerHeartbeatOutput: Swift.Sendable {
    /// Indicates whether the calling task runner should terminate.
    /// This member is required.
    public var terminate: Swift.Bool

    public init(
        terminate: Swift.Bool = false
    )
    {
        self.terminate = terminate
    }
}

/// Contains the parameters for SetStatus.
public struct SetStatusInput: Swift.Sendable {
    /// The IDs of the objects. The corresponding objects can be either physical or components, but not a mix of both types.
    /// This member is required.
    public var objectIds: [Swift.String]?
    /// The ID of the pipeline that contains the objects.
    /// This member is required.
    public var pipelineId: Swift.String?
    /// The status to be set on all the objects specified in objectIds. For components, use PAUSE or RESUME. For instances, use TRY_CANCEL, RERUN, or MARK_FINISHED.
    /// This member is required.
    public var status: Swift.String?

    public init(
        objectIds: [Swift.String]? = nil,
        pipelineId: Swift.String? = nil,
        status: Swift.String? = nil
    )
    {
        self.objectIds = objectIds
        self.pipelineId = pipelineId
        self.status = status
    }
}

extension DataPipelineClientTypes {

    public enum TaskStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case `false`
        case finished
        case sdkUnknown(Swift.String)

        public static var allCases: [TaskStatus] {
            return [
                .failed,
                .false,
                .finished
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .false: return "FALSE"
            case .finished: return "FINISHED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Contains the parameters for SetTaskStatus.
public struct SetTaskStatusInput: Swift.Sendable {
    /// If an error occurred during the task, this value specifies the error code. This value is set on the physical attempt object. It is used to display error information to the user. It should not start with string "Service_" which is reserved by the system.
    public var errorId: Swift.String?
    /// If an error occurred during the task, this value specifies a text description of the error. This value is set on the physical attempt object. It is used to display error information to the user. The web service does not parse this value.
    public var errorMessage: Swift.String?
    /// If an error occurred during the task, this value specifies the stack trace associated with the error. This value is set on the physical attempt object. It is used to display error information to the user. The web service does not parse this value.
    public var errorStackTrace: Swift.String?
    /// The ID of the task assigned to the task runner. This value is provided in the response for [PollForTask].
    /// This member is required.
    public var taskId: Swift.String?
    /// If FINISHED, the task successfully completed. If FAILED, the task ended unsuccessfully. Preconditions use false.
    /// This member is required.
    public var taskStatus: DataPipelineClientTypes.TaskStatus?

    public init(
        errorId: Swift.String? = nil,
        errorMessage: Swift.String? = nil,
        errorStackTrace: Swift.String? = nil,
        taskId: Swift.String? = nil,
        taskStatus: DataPipelineClientTypes.TaskStatus? = nil
    )
    {
        self.errorId = errorId
        self.errorMessage = errorMessage
        self.errorStackTrace = errorStackTrace
        self.taskId = taskId
        self.taskStatus = taskStatus
    }
}

/// Contains the output of SetTaskStatus.
public struct SetTaskStatusOutput: Swift.Sendable {

    public init() { }
}

/// Contains the parameters for ValidatePipelineDefinition.
public struct ValidatePipelineDefinitionInput: Swift.Sendable {
    /// The parameter objects used with the pipeline.
    public var parameterObjects: [DataPipelineClientTypes.ParameterObject]?
    /// The parameter values used with the pipeline.
    public var parameterValues: [DataPipelineClientTypes.ParameterValue]?
    /// The ID of the pipeline.
    /// This member is required.
    public var pipelineId: Swift.String?
    /// The objects that define the pipeline changes to validate against the pipeline.
    /// This member is required.
    public var pipelineObjects: [DataPipelineClientTypes.PipelineObject]?

    public init(
        parameterObjects: [DataPipelineClientTypes.ParameterObject]? = nil,
        parameterValues: [DataPipelineClientTypes.ParameterValue]? = nil,
        pipelineId: Swift.String? = nil,
        pipelineObjects: [DataPipelineClientTypes.PipelineObject]? = nil
    )
    {
        self.parameterObjects = parameterObjects
        self.parameterValues = parameterValues
        self.pipelineId = pipelineId
        self.pipelineObjects = pipelineObjects
    }
}

/// Contains the output of ValidatePipelineDefinition.
public struct ValidatePipelineDefinitionOutput: Swift.Sendable {
    /// Indicates whether there were validation errors.
    /// This member is required.
    public var errored: Swift.Bool
    /// Any validation errors that were found.
    public var validationErrors: [DataPipelineClientTypes.ValidationError]?
    /// Any validation warnings that were found.
    public var validationWarnings: [DataPipelineClientTypes.ValidationWarning]?

    public init(
        errored: Swift.Bool = false,
        validationErrors: [DataPipelineClientTypes.ValidationError]? = nil,
        validationWarnings: [DataPipelineClientTypes.ValidationWarning]? = nil
    )
    {
        self.errored = errored
        self.validationErrors = validationErrors
        self.validationWarnings = validationWarnings
    }
}

extension ActivatePipelineInput {

    static func urlPathProvider(_ value: ActivatePipelineInput) -> Swift.String? {
        return "/"
    }
}

extension AddTagsInput {

    static func urlPathProvider(_ value: AddTagsInput) -> Swift.String? {
        return "/"
    }
}

extension CreatePipelineInput {

    static func urlPathProvider(_ value: CreatePipelineInput) -> Swift.String? {
        return "/"
    }
}

extension DeactivatePipelineInput {

    static func urlPathProvider(_ value: DeactivatePipelineInput) -> Swift.String? {
        return "/"
    }
}

extension DeletePipelineInput {

    static func urlPathProvider(_ value: DeletePipelineInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeObjectsInput {

    static func urlPathProvider(_ value: DescribeObjectsInput) -> Swift.String? {
        return "/"
    }
}

extension DescribePipelinesInput {

    static func urlPathProvider(_ value: DescribePipelinesInput) -> Swift.String? {
        return "/"
    }
}

extension EvaluateExpressionInput {

    static func urlPathProvider(_ value: EvaluateExpressionInput) -> Swift.String? {
        return "/"
    }
}

extension GetPipelineDefinitionInput {

    static func urlPathProvider(_ value: GetPipelineDefinitionInput) -> Swift.String? {
        return "/"
    }
}

extension ListPipelinesInput {

    static func urlPathProvider(_ value: ListPipelinesInput) -> Swift.String? {
        return "/"
    }
}

extension PollForTaskInput {

    static func urlPathProvider(_ value: PollForTaskInput) -> Swift.String? {
        return "/"
    }
}

extension PutPipelineDefinitionInput {

    static func urlPathProvider(_ value: PutPipelineDefinitionInput) -> Swift.String? {
        return "/"
    }
}

extension QueryObjectsInput {

    static func urlPathProvider(_ value: QueryObjectsInput) -> Swift.String? {
        return "/"
    }
}

extension RemoveTagsInput {

    static func urlPathProvider(_ value: RemoveTagsInput) -> Swift.String? {
        return "/"
    }
}

extension ReportTaskProgressInput {

    static func urlPathProvider(_ value: ReportTaskProgressInput) -> Swift.String? {
        return "/"
    }
}

extension ReportTaskRunnerHeartbeatInput {

    static func urlPathProvider(_ value: ReportTaskRunnerHeartbeatInput) -> Swift.String? {
        return "/"
    }
}

extension SetStatusInput {

    static func urlPathProvider(_ value: SetStatusInput) -> Swift.String? {
        return "/"
    }
}

extension SetTaskStatusInput {

    static func urlPathProvider(_ value: SetTaskStatusInput) -> Swift.String? {
        return "/"
    }
}

extension ValidatePipelineDefinitionInput {

    static func urlPathProvider(_ value: ValidatePipelineDefinitionInput) -> Swift.String? {
        return "/"
    }
}

extension ActivatePipelineInput {

    static func write(value: ActivatePipelineInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["parameterValues"].writeList(value.parameterValues, memberWritingClosure: DataPipelineClientTypes.ParameterValue.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["pipelineId"].write(value.pipelineId)
        try writer["startTimestamp"].writeTimestamp(value.startTimestamp, format: SmithyTimestamps.TimestampFormat.epochSeconds)
    }
}

extension AddTagsInput {

    static func write(value: AddTagsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["pipelineId"].write(value.pipelineId)
        try writer["tags"].writeList(value.tags, memberWritingClosure: DataPipelineClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreatePipelineInput {

    static func write(value: CreatePipelineInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["description"].write(value.description)
        try writer["name"].write(value.name)
        try writer["tags"].writeList(value.tags, memberWritingClosure: DataPipelineClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["uniqueId"].write(value.uniqueId)
    }
}

extension DeactivatePipelineInput {

    static func write(value: DeactivatePipelineInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["cancelActive"].write(value.cancelActive)
        try writer["pipelineId"].write(value.pipelineId)
    }
}

extension DeletePipelineInput {

    static func write(value: DeletePipelineInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["pipelineId"].write(value.pipelineId)
    }
}

extension DescribeObjectsInput {

    static func write(value: DescribeObjectsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["evaluateExpressions"].write(value.evaluateExpressions)
        try writer["marker"].write(value.marker)
        try writer["objectIds"].writeList(value.objectIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["pipelineId"].write(value.pipelineId)
    }
}

extension DescribePipelinesInput {

    static func write(value: DescribePipelinesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["pipelineIds"].writeList(value.pipelineIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension EvaluateExpressionInput {

    static func write(value: EvaluateExpressionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["expression"].write(value.expression)
        try writer["objectId"].write(value.objectId)
        try writer["pipelineId"].write(value.pipelineId)
    }
}

extension GetPipelineDefinitionInput {

    static func write(value: GetPipelineDefinitionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["pipelineId"].write(value.pipelineId)
        try writer["version"].write(value.version)
    }
}

extension ListPipelinesInput {

    static func write(value: ListPipelinesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["marker"].write(value.marker)
    }
}

extension PollForTaskInput {

    static func write(value: PollForTaskInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["hostname"].write(value.hostname)
        try writer["instanceIdentity"].write(value.instanceIdentity, with: DataPipelineClientTypes.InstanceIdentity.write(value:to:))
        try writer["workerGroup"].write(value.workerGroup)
    }
}

extension PutPipelineDefinitionInput {

    static func write(value: PutPipelineDefinitionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["parameterObjects"].writeList(value.parameterObjects, memberWritingClosure: DataPipelineClientTypes.ParameterObject.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["parameterValues"].writeList(value.parameterValues, memberWritingClosure: DataPipelineClientTypes.ParameterValue.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["pipelineId"].write(value.pipelineId)
        try writer["pipelineObjects"].writeList(value.pipelineObjects, memberWritingClosure: DataPipelineClientTypes.PipelineObject.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension QueryObjectsInput {

    static func write(value: QueryObjectsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["limit"].write(value.limit)
        try writer["marker"].write(value.marker)
        try writer["pipelineId"].write(value.pipelineId)
        try writer["query"].write(value.query, with: DataPipelineClientTypes.Query.write(value:to:))
        try writer["sphere"].write(value.sphere)
    }
}

extension RemoveTagsInput {

    static func write(value: RemoveTagsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["pipelineId"].write(value.pipelineId)
        try writer["tagKeys"].writeList(value.tagKeys, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ReportTaskProgressInput {

    static func write(value: ReportTaskProgressInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["fields"].writeList(value.fields, memberWritingClosure: DataPipelineClientTypes.Field.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["taskId"].write(value.taskId)
    }
}

extension ReportTaskRunnerHeartbeatInput {

    static func write(value: ReportTaskRunnerHeartbeatInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["hostname"].write(value.hostname)
        try writer["taskrunnerId"].write(value.taskrunnerId)
        try writer["workerGroup"].write(value.workerGroup)
    }
}

extension SetStatusInput {

    static func write(value: SetStatusInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["objectIds"].writeList(value.objectIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["pipelineId"].write(value.pipelineId)
        try writer["status"].write(value.status)
    }
}

extension SetTaskStatusInput {

    static func write(value: SetTaskStatusInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["errorId"].write(value.errorId)
        try writer["errorMessage"].write(value.errorMessage)
        try writer["errorStackTrace"].write(value.errorStackTrace)
        try writer["taskId"].write(value.taskId)
        try writer["taskStatus"].write(value.taskStatus)
    }
}

extension ValidatePipelineDefinitionInput {

    static func write(value: ValidatePipelineDefinitionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["parameterObjects"].writeList(value.parameterObjects, memberWritingClosure: DataPipelineClientTypes.ParameterObject.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["parameterValues"].writeList(value.parameterValues, memberWritingClosure: DataPipelineClientTypes.ParameterValue.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["pipelineId"].write(value.pipelineId)
        try writer["pipelineObjects"].writeList(value.pipelineObjects, memberWritingClosure: DataPipelineClientTypes.PipelineObject.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ActivatePipelineOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ActivatePipelineOutput {
        return ActivatePipelineOutput()
    }
}

extension AddTagsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AddTagsOutput {
        return AddTagsOutput()
    }
}

extension CreatePipelineOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreatePipelineOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreatePipelineOutput()
        value.pipelineId = try reader["pipelineId"].readIfPresent() ?? ""
        return value
    }
}

extension DeactivatePipelineOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeactivatePipelineOutput {
        return DeactivatePipelineOutput()
    }
}

extension DeletePipelineOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeletePipelineOutput {
        return DeletePipelineOutput()
    }
}

extension DescribeObjectsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeObjectsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeObjectsOutput()
        value.hasMoreResults = try reader["hasMoreResults"].readIfPresent() ?? false
        value.marker = try reader["marker"].readIfPresent()
        value.pipelineObjects = try reader["pipelineObjects"].readListIfPresent(memberReadingClosure: DataPipelineClientTypes.PipelineObject.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DescribePipelinesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribePipelinesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribePipelinesOutput()
        value.pipelineDescriptionList = try reader["pipelineDescriptionList"].readListIfPresent(memberReadingClosure: DataPipelineClientTypes.PipelineDescription.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension EvaluateExpressionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> EvaluateExpressionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = EvaluateExpressionOutput()
        value.evaluatedExpression = try reader["evaluatedExpression"].readIfPresent() ?? ""
        return value
    }
}

extension GetPipelineDefinitionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetPipelineDefinitionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetPipelineDefinitionOutput()
        value.parameterObjects = try reader["parameterObjects"].readListIfPresent(memberReadingClosure: DataPipelineClientTypes.ParameterObject.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.parameterValues = try reader["parameterValues"].readListIfPresent(memberReadingClosure: DataPipelineClientTypes.ParameterValue.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.pipelineObjects = try reader["pipelineObjects"].readListIfPresent(memberReadingClosure: DataPipelineClientTypes.PipelineObject.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListPipelinesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListPipelinesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListPipelinesOutput()
        value.hasMoreResults = try reader["hasMoreResults"].readIfPresent() ?? false
        value.marker = try reader["marker"].readIfPresent()
        value.pipelineIdList = try reader["pipelineIdList"].readListIfPresent(memberReadingClosure: DataPipelineClientTypes.PipelineIdName.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension PollForTaskOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PollForTaskOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PollForTaskOutput()
        value.taskObject = try reader["taskObject"].readIfPresent(with: DataPipelineClientTypes.TaskObject.read(from:))
        return value
    }
}

extension PutPipelineDefinitionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutPipelineDefinitionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutPipelineDefinitionOutput()
        value.errored = try reader["errored"].readIfPresent() ?? false
        value.validationErrors = try reader["validationErrors"].readListIfPresent(memberReadingClosure: DataPipelineClientTypes.ValidationError.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.validationWarnings = try reader["validationWarnings"].readListIfPresent(memberReadingClosure: DataPipelineClientTypes.ValidationWarning.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension QueryObjectsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> QueryObjectsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = QueryObjectsOutput()
        value.hasMoreResults = try reader["hasMoreResults"].readIfPresent() ?? false
        value.ids = try reader["ids"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.marker = try reader["marker"].readIfPresent()
        return value
    }
}

extension RemoveTagsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RemoveTagsOutput {
        return RemoveTagsOutput()
    }
}

extension ReportTaskProgressOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ReportTaskProgressOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ReportTaskProgressOutput()
        value.canceled = try reader["canceled"].readIfPresent() ?? false
        return value
    }
}

extension ReportTaskRunnerHeartbeatOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ReportTaskRunnerHeartbeatOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ReportTaskRunnerHeartbeatOutput()
        value.terminate = try reader["terminate"].readIfPresent() ?? false
        return value
    }
}

extension SetStatusOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SetStatusOutput {
        return SetStatusOutput()
    }
}

extension SetTaskStatusOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SetTaskStatusOutput {
        return SetTaskStatusOutput()
    }
}

extension ValidatePipelineDefinitionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ValidatePipelineDefinitionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ValidatePipelineDefinitionOutput()
        value.errored = try reader["errored"].readIfPresent() ?? false
        value.validationErrors = try reader["validationErrors"].readListIfPresent(memberReadingClosure: DataPipelineClientTypes.ValidationError.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.validationWarnings = try reader["validationWarnings"].readListIfPresent(memberReadingClosure: DataPipelineClientTypes.ValidationWarning.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

enum ActivatePipelineOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineDeletedException": return try PipelineDeletedException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum AddTagsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineDeletedException": return try PipelineDeletedException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreatePipelineOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeactivatePipelineOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineDeletedException": return try PipelineDeletedException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeletePipelineOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeObjectsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineDeletedException": return try PipelineDeletedException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribePipelinesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineDeletedException": return try PipelineDeletedException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum EvaluateExpressionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineDeletedException": return try PipelineDeletedException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            case "TaskNotFoundException": return try TaskNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetPipelineDefinitionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineDeletedException": return try PipelineDeletedException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListPipelinesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PollForTaskOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "TaskNotFoundException": return try TaskNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutPipelineDefinitionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineDeletedException": return try PipelineDeletedException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum QueryObjectsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineDeletedException": return try PipelineDeletedException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RemoveTagsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineDeletedException": return try PipelineDeletedException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ReportTaskProgressOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineDeletedException": return try PipelineDeletedException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            case "TaskNotFoundException": return try TaskNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ReportTaskRunnerHeartbeatOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SetStatusOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineDeletedException": return try PipelineDeletedException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SetTaskStatusOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineDeletedException": return try PipelineDeletedException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            case "TaskNotFoundException": return try TaskNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ValidatePipelineDefinitionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServiceError": return try InternalServiceError.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "PipelineDeletedException": return try PipelineDeletedException.makeError(baseError: baseError)
            case "PipelineNotFoundException": return try PipelineNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension PipelineNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> PipelineNotFoundException {
        let reader = baseError.errorBodyReader
        var value = PipelineNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidRequestException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidRequestException {
        let reader = baseError.errorBodyReader
        var value = InvalidRequestException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServiceError {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InternalServiceError {
        let reader = baseError.errorBodyReader
        var value = InternalServiceError()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension PipelineDeletedException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> PipelineDeletedException {
        let reader = baseError.errorBodyReader
        var value = PipelineDeletedException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TaskNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> TaskNotFoundException {
        let reader = baseError.errorBodyReader
        var value = TaskNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DataPipelineClientTypes.PipelineObject {

    static func write(value: DataPipelineClientTypes.PipelineObject?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["fields"].writeList(value.fields, memberWritingClosure: DataPipelineClientTypes.Field.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["id"].write(value.id)
        try writer["name"].write(value.name)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataPipelineClientTypes.PipelineObject {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataPipelineClientTypes.PipelineObject()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent() ?? ""
        value.fields = try reader["fields"].readListIfPresent(memberReadingClosure: DataPipelineClientTypes.Field.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DataPipelineClientTypes.Field {

    static func write(value: DataPipelineClientTypes.Field?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["key"].write(value.key)
        try writer["refValue"].write(value.refValue)
        try writer["stringValue"].write(value.stringValue)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataPipelineClientTypes.Field {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataPipelineClientTypes.Field()
        value.key = try reader["key"].readIfPresent() ?? ""
        value.stringValue = try reader["stringValue"].readIfPresent()
        value.refValue = try reader["refValue"].readIfPresent()
        return value
    }
}

extension DataPipelineClientTypes.PipelineDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> DataPipelineClientTypes.PipelineDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataPipelineClientTypes.PipelineDescription()
        value.pipelineId = try reader["pipelineId"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent() ?? ""
        value.fields = try reader["fields"].readListIfPresent(memberReadingClosure: DataPipelineClientTypes.Field.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.description = try reader["description"].readIfPresent()
        value.tags = try reader["tags"].readListIfPresent(memberReadingClosure: DataPipelineClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DataPipelineClientTypes.Tag {

    static func write(value: DataPipelineClientTypes.Tag?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["key"].write(value.key)
        try writer["value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataPipelineClientTypes.Tag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataPipelineClientTypes.Tag()
        value.key = try reader["key"].readIfPresent() ?? ""
        value.value = try reader["value"].readIfPresent() ?? ""
        return value
    }
}

extension DataPipelineClientTypes.ParameterObject {

    static func write(value: DataPipelineClientTypes.ParameterObject?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["attributes"].writeList(value.attributes, memberWritingClosure: DataPipelineClientTypes.ParameterAttribute.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["id"].write(value.id)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataPipelineClientTypes.ParameterObject {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataPipelineClientTypes.ParameterObject()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.attributes = try reader["attributes"].readListIfPresent(memberReadingClosure: DataPipelineClientTypes.ParameterAttribute.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DataPipelineClientTypes.ParameterAttribute {

    static func write(value: DataPipelineClientTypes.ParameterAttribute?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["key"].write(value.key)
        try writer["stringValue"].write(value.stringValue)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataPipelineClientTypes.ParameterAttribute {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataPipelineClientTypes.ParameterAttribute()
        value.key = try reader["key"].readIfPresent() ?? ""
        value.stringValue = try reader["stringValue"].readIfPresent() ?? ""
        return value
    }
}

extension DataPipelineClientTypes.ParameterValue {

    static func write(value: DataPipelineClientTypes.ParameterValue?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["id"].write(value.id)
        try writer["stringValue"].write(value.stringValue)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DataPipelineClientTypes.ParameterValue {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataPipelineClientTypes.ParameterValue()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.stringValue = try reader["stringValue"].readIfPresent() ?? ""
        return value
    }
}

extension DataPipelineClientTypes.PipelineIdName {

    static func read(from reader: SmithyJSON.Reader) throws -> DataPipelineClientTypes.PipelineIdName {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataPipelineClientTypes.PipelineIdName()
        value.id = try reader["id"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        return value
    }
}

extension DataPipelineClientTypes.TaskObject {

    static func read(from reader: SmithyJSON.Reader) throws -> DataPipelineClientTypes.TaskObject {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataPipelineClientTypes.TaskObject()
        value.taskId = try reader["taskId"].readIfPresent()
        value.pipelineId = try reader["pipelineId"].readIfPresent()
        value.attemptId = try reader["attemptId"].readIfPresent()
        value.objects = try reader["objects"].readMapIfPresent(valueReadingClosure: DataPipelineClientTypes.PipelineObject.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DataPipelineClientTypes.ValidationError {

    static func read(from reader: SmithyJSON.Reader) throws -> DataPipelineClientTypes.ValidationError {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataPipelineClientTypes.ValidationError()
        value.id = try reader["id"].readIfPresent()
        value.errors = try reader["errors"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DataPipelineClientTypes.ValidationWarning {

    static func read(from reader: SmithyJSON.Reader) throws -> DataPipelineClientTypes.ValidationWarning {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DataPipelineClientTypes.ValidationWarning()
        value.id = try reader["id"].readIfPresent()
        value.warnings = try reader["warnings"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DataPipelineClientTypes.InstanceIdentity {

    static func write(value: DataPipelineClientTypes.InstanceIdentity?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["document"].write(value.document)
        try writer["signature"].write(value.signature)
    }
}

extension DataPipelineClientTypes.Query {

    static func write(value: DataPipelineClientTypes.Query?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["selectors"].writeList(value.selectors, memberWritingClosure: DataPipelineClientTypes.Selector.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DataPipelineClientTypes.Selector {

    static func write(value: DataPipelineClientTypes.Selector?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["fieldName"].write(value.fieldName)
        try writer["operator"].write(value.`operator`, with: DataPipelineClientTypes.Operator.write(value:to:))
    }
}

extension DataPipelineClientTypes.Operator {

    static func write(value: DataPipelineClientTypes.Operator?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["type"].write(value.type)
        try writer["values"].writeList(value.values, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

public enum DataPipelineClientTypes {}
