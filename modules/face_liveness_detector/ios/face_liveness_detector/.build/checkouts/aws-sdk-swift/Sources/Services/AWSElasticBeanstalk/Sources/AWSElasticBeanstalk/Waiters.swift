//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import class SmithyWaitersAPI.Waiter
import enum SmithyWaitersAPI.JMESUtils
import struct SmithyWaitersAPI.WaiterConfiguration
import struct SmithyWaitersAPI.WaiterOptions
import struct Smithy<PERSON>aitersAPI.WaiterOutcome

extension ElasticBeanstalkClient {

    static func environmentExistsWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeEnvironmentsInput, DescribeEnvironmentsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeEnvironmentsInput, DescribeEnvironmentsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeEnvironmentsInput, result: Swift.Result<DescribeEnvironmentsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Environments[].Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "Ready"
                guard case .success(let output) = result else { return false }
                let environments = output.environments
                let projection: [ElasticBeanstalkClientTypes.EnvironmentStatus]? = environments?.compactMap { original in
                    let status = original.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "Ready") } ?? false)
            }),
            .init(state: .retry, matcher: { (input: DescribeEnvironmentsInput, result: Swift.Result<DescribeEnvironmentsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Environments[].Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "Launching"
                guard case .success(let output) = result else { return false }
                let environments = output.environments
                let projection: [ElasticBeanstalkClientTypes.EnvironmentStatus]? = environments?.compactMap { original in
                    let status = original.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "Launching") } ?? false)
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeEnvironmentsInput, DescribeEnvironmentsOutput>(acceptors: acceptors, minDelay: 20.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the EnvironmentExists event on the describeEnvironments operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeEnvironmentsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilEnvironmentExists(options: SmithyWaitersAPI.WaiterOptions, input: DescribeEnvironmentsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeEnvironmentsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.environmentExistsWaiterConfig(), operation: self.describeEnvironments(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func environmentTerminatedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeEnvironmentsInput, DescribeEnvironmentsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeEnvironmentsInput, DescribeEnvironmentsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeEnvironmentsInput, result: Swift.Result<DescribeEnvironmentsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Environments[].Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "Terminated"
                guard case .success(let output) = result else { return false }
                let environments = output.environments
                let projection: [ElasticBeanstalkClientTypes.EnvironmentStatus]? = environments?.compactMap { original in
                    let status = original.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "Terminated") } ?? false)
            }),
            .init(state: .retry, matcher: { (input: DescribeEnvironmentsInput, result: Swift.Result<DescribeEnvironmentsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Environments[].Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "Terminating"
                guard case .success(let output) = result else { return false }
                let environments = output.environments
                let projection: [ElasticBeanstalkClientTypes.EnvironmentStatus]? = environments?.compactMap { original in
                    let status = original.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "Terminating") } ?? false)
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeEnvironmentsInput, DescribeEnvironmentsOutput>(acceptors: acceptors, minDelay: 20.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the EnvironmentTerminated event on the describeEnvironments operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeEnvironmentsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilEnvironmentTerminated(options: SmithyWaitersAPI.WaiterOptions, input: DescribeEnvironmentsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeEnvironmentsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.environmentTerminatedWaiterConfig(), operation: self.describeEnvironments(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func environmentUpdatedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeEnvironmentsInput, DescribeEnvironmentsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeEnvironmentsInput, DescribeEnvironmentsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeEnvironmentsInput, result: Swift.Result<DescribeEnvironmentsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Environments[].Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "Ready"
                guard case .success(let output) = result else { return false }
                let environments = output.environments
                let projection: [ElasticBeanstalkClientTypes.EnvironmentStatus]? = environments?.compactMap { original in
                    let status = original.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "Ready") } ?? false)
            }),
            .init(state: .retry, matcher: { (input: DescribeEnvironmentsInput, result: Swift.Result<DescribeEnvironmentsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Environments[].Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "Updating"
                guard case .success(let output) = result else { return false }
                let environments = output.environments
                let projection: [ElasticBeanstalkClientTypes.EnvironmentStatus]? = environments?.compactMap { original in
                    let status = original.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "Updating") } ?? false)
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeEnvironmentsInput, DescribeEnvironmentsOutput>(acceptors: acceptors, minDelay: 20.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the EnvironmentUpdated event on the describeEnvironments operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeEnvironmentsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilEnvironmentUpdated(options: SmithyWaitersAPI.WaiterOptions, input: DescribeEnvironmentsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeEnvironmentsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.environmentUpdatedWaiterConfig(), operation: self.describeEnvironments(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }
}
