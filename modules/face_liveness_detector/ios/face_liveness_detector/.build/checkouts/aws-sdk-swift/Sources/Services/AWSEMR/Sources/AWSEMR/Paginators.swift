//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import Foundation
import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension EMRClient {
    /// Paginate over `[ListBootstrapActionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListBootstrapActionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListBootstrapActionsOutput`
    public func listBootstrapActionsPaginated(input: ListBootstrapActionsInput) -> ClientRuntime.PaginatorSequence<ListBootstrapActionsInput, ListBootstrapActionsOutput> {
        return ClientRuntime.PaginatorSequence<ListBootstrapActionsInput, ListBootstrapActionsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.listBootstrapActions(input:))
    }
}

extension ListBootstrapActionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListBootstrapActionsInput {
        return ListBootstrapActionsInput(
            clusterId: self.clusterId,
            marker: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListBootstrapActionsInput, OperationStackOutput == ListBootstrapActionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listBootstrapActionsPaginated`
    /// to access the nested member `[EMRClientTypes.Command]`
    /// - Returns: `[EMRClientTypes.Command]`
    public func bootstrapActions() async throws -> [EMRClientTypes.Command] {
        return try await self.asyncCompactMap { item in item.bootstrapActions }
    }
}
extension EMRClient {
    /// Paginate over `[ListClustersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListClustersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListClustersOutput`
    public func listClustersPaginated(input: ListClustersInput) -> ClientRuntime.PaginatorSequence<ListClustersInput, ListClustersOutput> {
        return ClientRuntime.PaginatorSequence<ListClustersInput, ListClustersOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.listClusters(input:))
    }
}

extension ListClustersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListClustersInput {
        return ListClustersInput(
            clusterStates: self.clusterStates,
            createdAfter: self.createdAfter,
            createdBefore: self.createdBefore,
            marker: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListClustersInput, OperationStackOutput == ListClustersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listClustersPaginated`
    /// to access the nested member `[EMRClientTypes.ClusterSummary]`
    /// - Returns: `[EMRClientTypes.ClusterSummary]`
    public func clusters() async throws -> [EMRClientTypes.ClusterSummary] {
        return try await self.asyncCompactMap { item in item.clusters }
    }
}
extension EMRClient {
    /// Paginate over `[ListInstanceFleetsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListInstanceFleetsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListInstanceFleetsOutput`
    public func listInstanceFleetsPaginated(input: ListInstanceFleetsInput) -> ClientRuntime.PaginatorSequence<ListInstanceFleetsInput, ListInstanceFleetsOutput> {
        return ClientRuntime.PaginatorSequence<ListInstanceFleetsInput, ListInstanceFleetsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.listInstanceFleets(input:))
    }
}

extension ListInstanceFleetsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListInstanceFleetsInput {
        return ListInstanceFleetsInput(
            clusterId: self.clusterId,
            marker: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListInstanceFleetsInput, OperationStackOutput == ListInstanceFleetsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listInstanceFleetsPaginated`
    /// to access the nested member `[EMRClientTypes.InstanceFleet]`
    /// - Returns: `[EMRClientTypes.InstanceFleet]`
    public func instanceFleets() async throws -> [EMRClientTypes.InstanceFleet] {
        return try await self.asyncCompactMap { item in item.instanceFleets }
    }
}
extension EMRClient {
    /// Paginate over `[ListInstanceGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListInstanceGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListInstanceGroupsOutput`
    public func listInstanceGroupsPaginated(input: ListInstanceGroupsInput) -> ClientRuntime.PaginatorSequence<ListInstanceGroupsInput, ListInstanceGroupsOutput> {
        return ClientRuntime.PaginatorSequence<ListInstanceGroupsInput, ListInstanceGroupsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.listInstanceGroups(input:))
    }
}

extension ListInstanceGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListInstanceGroupsInput {
        return ListInstanceGroupsInput(
            clusterId: self.clusterId,
            marker: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListInstanceGroupsInput, OperationStackOutput == ListInstanceGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listInstanceGroupsPaginated`
    /// to access the nested member `[EMRClientTypes.InstanceGroup]`
    /// - Returns: `[EMRClientTypes.InstanceGroup]`
    public func instanceGroups() async throws -> [EMRClientTypes.InstanceGroup] {
        return try await self.asyncCompactMap { item in item.instanceGroups }
    }
}
extension EMRClient {
    /// Paginate over `[ListInstancesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListInstancesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListInstancesOutput`
    public func listInstancesPaginated(input: ListInstancesInput) -> ClientRuntime.PaginatorSequence<ListInstancesInput, ListInstancesOutput> {
        return ClientRuntime.PaginatorSequence<ListInstancesInput, ListInstancesOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.listInstances(input:))
    }
}

extension ListInstancesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListInstancesInput {
        return ListInstancesInput(
            clusterId: self.clusterId,
            instanceFleetId: self.instanceFleetId,
            instanceFleetType: self.instanceFleetType,
            instanceGroupId: self.instanceGroupId,
            instanceGroupTypes: self.instanceGroupTypes,
            instanceStates: self.instanceStates,
            marker: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListInstancesInput, OperationStackOutput == ListInstancesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listInstancesPaginated`
    /// to access the nested member `[EMRClientTypes.Instance]`
    /// - Returns: `[EMRClientTypes.Instance]`
    public func instances() async throws -> [EMRClientTypes.Instance] {
        return try await self.asyncCompactMap { item in item.instances }
    }
}
extension EMRClient {
    /// Paginate over `[ListNotebookExecutionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListNotebookExecutionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListNotebookExecutionsOutput`
    public func listNotebookExecutionsPaginated(input: ListNotebookExecutionsInput) -> ClientRuntime.PaginatorSequence<ListNotebookExecutionsInput, ListNotebookExecutionsOutput> {
        return ClientRuntime.PaginatorSequence<ListNotebookExecutionsInput, ListNotebookExecutionsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.listNotebookExecutions(input:))
    }
}

extension ListNotebookExecutionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListNotebookExecutionsInput {
        return ListNotebookExecutionsInput(
            editorId: self.editorId,
            executionEngineId: self.executionEngineId,
            from: self.from,
            marker: token,
            status: self.status,
            to: self.to
        )}
}

extension PaginatorSequence where OperationStackInput == ListNotebookExecutionsInput, OperationStackOutput == ListNotebookExecutionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listNotebookExecutionsPaginated`
    /// to access the nested member `[EMRClientTypes.NotebookExecutionSummary]`
    /// - Returns: `[EMRClientTypes.NotebookExecutionSummary]`
    public func notebookExecutions() async throws -> [EMRClientTypes.NotebookExecutionSummary] {
        return try await self.asyncCompactMap { item in item.notebookExecutions }
    }
}
extension EMRClient {
    /// Paginate over `[ListReleaseLabelsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListReleaseLabelsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListReleaseLabelsOutput`
    public func listReleaseLabelsPaginated(input: ListReleaseLabelsInput) -> ClientRuntime.PaginatorSequence<ListReleaseLabelsInput, ListReleaseLabelsOutput> {
        return ClientRuntime.PaginatorSequence<ListReleaseLabelsInput, ListReleaseLabelsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listReleaseLabels(input:))
    }
}

extension ListReleaseLabelsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListReleaseLabelsInput {
        return ListReleaseLabelsInput(
            filters: self.filters,
            maxResults: self.maxResults,
            nextToken: token
        )}
}
extension EMRClient {
    /// Paginate over `[ListSecurityConfigurationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListSecurityConfigurationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListSecurityConfigurationsOutput`
    public func listSecurityConfigurationsPaginated(input: ListSecurityConfigurationsInput) -> ClientRuntime.PaginatorSequence<ListSecurityConfigurationsInput, ListSecurityConfigurationsOutput> {
        return ClientRuntime.PaginatorSequence<ListSecurityConfigurationsInput, ListSecurityConfigurationsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.listSecurityConfigurations(input:))
    }
}

extension ListSecurityConfigurationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListSecurityConfigurationsInput {
        return ListSecurityConfigurationsInput(
            marker: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListSecurityConfigurationsInput, OperationStackOutput == ListSecurityConfigurationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listSecurityConfigurationsPaginated`
    /// to access the nested member `[EMRClientTypes.SecurityConfigurationSummary]`
    /// - Returns: `[EMRClientTypes.SecurityConfigurationSummary]`
    public func securityConfigurations() async throws -> [EMRClientTypes.SecurityConfigurationSummary] {
        return try await self.asyncCompactMap { item in item.securityConfigurations }
    }
}
extension EMRClient {
    /// Paginate over `[ListStepsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListStepsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListStepsOutput`
    public func listStepsPaginated(input: ListStepsInput) -> ClientRuntime.PaginatorSequence<ListStepsInput, ListStepsOutput> {
        return ClientRuntime.PaginatorSequence<ListStepsInput, ListStepsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.listSteps(input:))
    }
}

extension ListStepsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListStepsInput {
        return ListStepsInput(
            clusterId: self.clusterId,
            marker: token,
            stepIds: self.stepIds,
            stepStates: self.stepStates
        )}
}

extension PaginatorSequence where OperationStackInput == ListStepsInput, OperationStackOutput == ListStepsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listStepsPaginated`
    /// to access the nested member `[EMRClientTypes.StepSummary]`
    /// - Returns: `[EMRClientTypes.StepSummary]`
    public func steps() async throws -> [EMRClientTypes.StepSummary] {
        return try await self.asyncCompactMap { item in item.steps }
    }
}
extension EMRClient {
    /// Paginate over `[ListStudiosOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListStudiosInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListStudiosOutput`
    public func listStudiosPaginated(input: ListStudiosInput) -> ClientRuntime.PaginatorSequence<ListStudiosInput, ListStudiosOutput> {
        return ClientRuntime.PaginatorSequence<ListStudiosInput, ListStudiosOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.listStudios(input:))
    }
}

extension ListStudiosInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListStudiosInput {
        return ListStudiosInput(
            marker: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListStudiosInput, OperationStackOutput == ListStudiosOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listStudiosPaginated`
    /// to access the nested member `[EMRClientTypes.StudioSummary]`
    /// - Returns: `[EMRClientTypes.StudioSummary]`
    public func studios() async throws -> [EMRClientTypes.StudioSummary] {
        return try await self.asyncCompactMap { item in item.studios }
    }
}
extension EMRClient {
    /// Paginate over `[ListStudioSessionMappingsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListStudioSessionMappingsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListStudioSessionMappingsOutput`
    public func listStudioSessionMappingsPaginated(input: ListStudioSessionMappingsInput) -> ClientRuntime.PaginatorSequence<ListStudioSessionMappingsInput, ListStudioSessionMappingsOutput> {
        return ClientRuntime.PaginatorSequence<ListStudioSessionMappingsInput, ListStudioSessionMappingsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.listStudioSessionMappings(input:))
    }
}

extension ListStudioSessionMappingsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListStudioSessionMappingsInput {
        return ListStudioSessionMappingsInput(
            identityType: self.identityType,
            marker: token,
            studioId: self.studioId
        )}
}

extension PaginatorSequence where OperationStackInput == ListStudioSessionMappingsInput, OperationStackOutput == ListStudioSessionMappingsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listStudioSessionMappingsPaginated`
    /// to access the nested member `[EMRClientTypes.SessionMappingSummary]`
    /// - Returns: `[EMRClientTypes.SessionMappingSummary]`
    public func sessionMappings() async throws -> [EMRClientTypes.SessionMappingSummary] {
        return try await self.asyncCompactMap { item in item.sessionMappings }
    }
}
extension EMRClient {
    /// Paginate over `[ListSupportedInstanceTypesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListSupportedInstanceTypesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListSupportedInstanceTypesOutput`
    public func listSupportedInstanceTypesPaginated(input: ListSupportedInstanceTypesInput) -> ClientRuntime.PaginatorSequence<ListSupportedInstanceTypesInput, ListSupportedInstanceTypesOutput> {
        return ClientRuntime.PaginatorSequence<ListSupportedInstanceTypesInput, ListSupportedInstanceTypesOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.listSupportedInstanceTypes(input:))
    }
}

extension ListSupportedInstanceTypesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListSupportedInstanceTypesInput {
        return ListSupportedInstanceTypesInput(
            marker: token,
            releaseLabel: self.releaseLabel
        )}
}
