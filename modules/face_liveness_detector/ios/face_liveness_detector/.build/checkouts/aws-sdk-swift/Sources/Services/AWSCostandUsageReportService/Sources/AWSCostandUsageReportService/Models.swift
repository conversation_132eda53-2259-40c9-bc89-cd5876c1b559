//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import class Smithy<PERSON><PERSON><PERSON>I.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.AWSJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
@_spi(SmithyReadWrite) import struct SmithyReadWrite.ReadingClosureBox
@_spi(SmithyReadWrite) import struct SmithyReadWrite.WritingClosureBox

extension CostandUsageReportClientTypes {

    /// The types of manifest that you want Amazon Web Services to create for this report.
    public enum AdditionalArtifact: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case athena
        case quicksight
        case redshift
        case sdkUnknown(Swift.String)

        public static var allCases: [AdditionalArtifact] {
            return [
                .athena,
                .quicksight,
                .redshift
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .athena: return "ATHENA"
            case .quicksight: return "QUICKSIGHT"
            case .redshift: return "REDSHIFT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// An error on the server occurred during the processing of your request. Try again later.
public struct InternalErrorException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// A message to show the detail of the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalErrorException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The input fails to satisfy the constraints specified by an Amazon Web Services service.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// A message to show the detail of the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Deletes the specified report.
public struct DeleteReportDefinitionInput: Swift.Sendable {
    /// The name of the report that you want to delete. The name must be unique, is case sensitive, and can't include spaces.
    /// This member is required.
    public var reportName: Swift.String?

    public init(
        reportName: Swift.String? = nil
    )
    {
        self.reportName = reportName
    }
}

/// If the action is successful, the service sends back an HTTP 200 response.
public struct DeleteReportDefinitionOutput: Swift.Sendable {
    /// Whether the deletion was successful or not.
    public var responseMessage: Swift.String?

    public init(
        responseMessage: Swift.String? = nil
    )
    {
        self.responseMessage = responseMessage
    }
}

/// Requests a Amazon Web Services Cost and Usage Report list owned by the account.
public struct DescribeReportDefinitionsInput: Swift.Sendable {
    /// The maximum number of results that Amazon Web Services returns for the operation.
    public var maxResults: Swift.Int?
    /// A generic string.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension CostandUsageReportClientTypes {

    /// Whether or not Amazon Web Services includes resource IDs in the report.
    public enum SchemaElement: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case manualDiscountCompatibility
        case resources
        case splitCostAllocationData
        case sdkUnknown(Swift.String)

        public static var allCases: [SchemaElement] {
            return [
                .manualDiscountCompatibility,
                .resources,
                .splitCostAllocationData
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .manualDiscountCompatibility: return "MANUAL_DISCOUNT_COMPATIBILITY"
            case .resources: return "RESOURCES"
            case .splitCostAllocationData: return "SPLIT_COST_ALLOCATION_DATA"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CostandUsageReportClientTypes {

    /// The compression format that Amazon Web Services uses for the report.
    public enum CompressionFormat: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case gzip
        case parquet
        case zip
        case sdkUnknown(Swift.String)

        public static var allCases: [CompressionFormat] {
            return [
                .gzip,
                .parquet,
                .zip
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .gzip: return "GZIP"
            case .parquet: return "Parquet"
            case .zip: return "ZIP"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CostandUsageReportClientTypes {

    /// The format that Amazon Web Services saves the report in.
    public enum ReportFormat: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case csv
        case parquet
        case sdkUnknown(Swift.String)

        public static var allCases: [ReportFormat] {
            return [
                .csv,
                .parquet
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .csv: return "textORcsv"
            case .parquet: return "Parquet"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CostandUsageReportClientTypes {

    public enum LastStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case errorNoBucket
        case errorPermissions
        case success
        case sdkUnknown(Swift.String)

        public static var allCases: [LastStatus] {
            return [
                .errorNoBucket,
                .errorPermissions,
                .success
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .errorNoBucket: return "ERROR_NO_BUCKET"
            case .errorPermissions: return "ERROR_PERMISSIONS"
            case .success: return "SUCCESS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CostandUsageReportClientTypes {

    /// A two element dictionary with a lastDelivery and lastStatus key whose values describe the date and status of the last delivered report for a particular report definition.
    public struct ReportStatus: Swift.Sendable {
        /// A timestamp that gives the date of a report delivery.
        public var lastDelivery: Swift.String?
        /// An enum that gives the status of a report delivery.
        public var lastStatus: CostandUsageReportClientTypes.LastStatus?

        public init(
            lastDelivery: Swift.String? = nil,
            lastStatus: CostandUsageReportClientTypes.LastStatus? = nil
        )
        {
            self.lastDelivery = lastDelivery
            self.lastStatus = lastStatus
        }
    }
}

extension CostandUsageReportClientTypes {

    public enum ReportVersioning: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case createNewReport
        case overwriteReport
        case sdkUnknown(Swift.String)

        public static var allCases: [ReportVersioning] {
            return [
                .createNewReport,
                .overwriteReport
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .createNewReport: return "CREATE_NEW_REPORT"
            case .overwriteReport: return "OVERWRITE_REPORT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CostandUsageReportClientTypes {

    /// The region of the S3 bucket that Amazon Web Services delivers the report into.
    public enum AWSRegion: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case bahrain
        case beijing
        case canadaCentral
        case capeTown
        case frankfurt
        case hongKong
        case hyderabad
        case ireland
        case jakarta
        case london
        case milano
        case mumbai
        case ningxia
        case northernCalifornia
        case ohio
        case oregon
        case osaka
        case paris
        case saoPaulo
        case seoul
        case singapore
        case spain
        case stockholm
        case sydney
        case tokyo
        case uae
        case usStandard
        case zurich
        case sdkUnknown(Swift.String)

        public static var allCases: [AWSRegion] {
            return [
                .bahrain,
                .beijing,
                .canadaCentral,
                .capeTown,
                .frankfurt,
                .hongKong,
                .hyderabad,
                .ireland,
                .jakarta,
                .london,
                .milano,
                .mumbai,
                .ningxia,
                .northernCalifornia,
                .ohio,
                .oregon,
                .osaka,
                .paris,
                .saoPaulo,
                .seoul,
                .singapore,
                .spain,
                .stockholm,
                .sydney,
                .tokyo,
                .uae,
                .usStandard,
                .zurich
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .bahrain: return "me-south-1"
            case .beijing: return "cn-north-1"
            case .canadaCentral: return "ca-central-1"
            case .capeTown: return "af-south-1"
            case .frankfurt: return "eu-central-1"
            case .hongKong: return "ap-east-1"
            case .hyderabad: return "ap-south-2"
            case .ireland: return "eu-west-1"
            case .jakarta: return "ap-southeast-3"
            case .london: return "eu-west-2"
            case .milano: return "eu-south-1"
            case .mumbai: return "ap-south-1"
            case .ningxia: return "cn-northwest-1"
            case .northernCalifornia: return "us-west-1"
            case .ohio: return "us-east-2"
            case .oregon: return "us-west-2"
            case .osaka: return "ap-northeast-3"
            case .paris: return "eu-west-3"
            case .saoPaulo: return "sa-east-1"
            case .seoul: return "ap-northeast-2"
            case .singapore: return "ap-southeast-1"
            case .spain: return "eu-south-2"
            case .stockholm: return "eu-north-1"
            case .sydney: return "ap-southeast-2"
            case .tokyo: return "ap-northeast-1"
            case .uae: return "me-central-1"
            case .usStandard: return "us-east-1"
            case .zurich: return "eu-central-2"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CostandUsageReportClientTypes {

    /// The length of time covered by the report.
    public enum TimeUnit: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case daily
        case hourly
        case monthly
        case sdkUnknown(Swift.String)

        public static var allCases: [TimeUnit] {
            return [
                .daily,
                .hourly,
                .monthly
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .daily: return "DAILY"
            case .hourly: return "HOURLY"
            case .monthly: return "MONTHLY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CostandUsageReportClientTypes {

    /// The definition of Amazon Web Services Cost and Usage Report. You can specify the report name, time unit, report format, compression format, S3 bucket, additional artifacts, and schema elements in the definition.
    public struct ReportDefinition: Swift.Sendable {
        /// A list of manifests that you want Amazon Web Services to create for this report.
        public var additionalArtifacts: [CostandUsageReportClientTypes.AdditionalArtifact]?
        /// A list of strings that indicate additional content that Amazon Web Services includes in the report, such as individual resource IDs.
        /// This member is required.
        public var additionalSchemaElements: [CostandUsageReportClientTypes.SchemaElement]?
        /// The Amazon resource name of the billing view. The BillingViewArn is needed to create Amazon Web Services Cost and Usage Report for each billing group maintained in the Amazon Web Services Billing Conductor service. The BillingViewArn for a billing group can be constructed as: arn:aws:billing::payer-account-id:billingview/billing-group-primary-account-id
        public var billingViewArn: Swift.String?
        /// The compression format that Amazon Web Services uses for the report.
        /// This member is required.
        public var compression: CostandUsageReportClientTypes.CompressionFormat?
        /// The format that Amazon Web Services saves the report in.
        /// This member is required.
        public var format: CostandUsageReportClientTypes.ReportFormat?
        /// Whether you want Amazon Web Services to update your reports after they have been finalized if Amazon Web Services detects charges related to previous months. These charges can include refunds, credits, or support fees.
        public var refreshClosedReports: Swift.Bool?
        /// The name of the report that you want to create. The name must be unique, is case sensitive, and can't include spaces.
        /// This member is required.
        public var reportName: Swift.String?
        /// The status of the report.
        public var reportStatus: CostandUsageReportClientTypes.ReportStatus?
        /// Whether you want Amazon Web Services to overwrite the previous version of each report or to deliver the report in addition to the previous versions.
        public var reportVersioning: CostandUsageReportClientTypes.ReportVersioning?
        /// The S3 bucket where Amazon Web Services delivers the report.
        /// This member is required.
        public var s3Bucket: Swift.String?
        /// The prefix that Amazon Web Services adds to the report name when Amazon Web Services delivers the report. Your prefix can't include spaces.
        /// This member is required.
        public var s3Prefix: Swift.String?
        /// The region of the S3 bucket that Amazon Web Services delivers the report into.
        /// This member is required.
        public var s3Region: CostandUsageReportClientTypes.AWSRegion?
        /// The length of time covered by the report.
        /// This member is required.
        public var timeUnit: CostandUsageReportClientTypes.TimeUnit?

        public init(
            additionalArtifacts: [CostandUsageReportClientTypes.AdditionalArtifact]? = nil,
            additionalSchemaElements: [CostandUsageReportClientTypes.SchemaElement]? = nil,
            billingViewArn: Swift.String? = nil,
            compression: CostandUsageReportClientTypes.CompressionFormat? = nil,
            format: CostandUsageReportClientTypes.ReportFormat? = nil,
            refreshClosedReports: Swift.Bool? = nil,
            reportName: Swift.String? = nil,
            reportStatus: CostandUsageReportClientTypes.ReportStatus? = nil,
            reportVersioning: CostandUsageReportClientTypes.ReportVersioning? = nil,
            s3Bucket: Swift.String? = nil,
            s3Prefix: Swift.String? = nil,
            s3Region: CostandUsageReportClientTypes.AWSRegion? = nil,
            timeUnit: CostandUsageReportClientTypes.TimeUnit? = nil
        )
        {
            self.additionalArtifacts = additionalArtifacts
            self.additionalSchemaElements = additionalSchemaElements
            self.billingViewArn = billingViewArn
            self.compression = compression
            self.format = format
            self.refreshClosedReports = refreshClosedReports
            self.reportName = reportName
            self.reportStatus = reportStatus
            self.reportVersioning = reportVersioning
            self.s3Bucket = s3Bucket
            self.s3Prefix = s3Prefix
            self.s3Region = s3Region
            self.timeUnit = timeUnit
        }
    }
}

/// If the action is successful, the service sends back an HTTP 200 response.
public struct DescribeReportDefinitionsOutput: Swift.Sendable {
    /// A generic string.
    public var nextToken: Swift.String?
    /// An Amazon Web Services Cost and Usage Report list owned by the account.
    public var reportDefinitions: [CostandUsageReportClientTypes.ReportDefinition]?

    public init(
        nextToken: Swift.String? = nil,
        reportDefinitions: [CostandUsageReportClientTypes.ReportDefinition]? = nil
    )
    {
        self.nextToken = nextToken
        self.reportDefinitions = reportDefinitions
    }
}

/// The specified report (ReportName) in the request doesn't exist.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// A message to show the detail of the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The report name of the report definition that tags are to be returned for.
    /// This member is required.
    public var reportName: Swift.String?

    public init(
        reportName: Swift.String? = nil
    )
    {
        self.reportName = reportName
    }
}

extension CostandUsageReportClientTypes {

    /// Describes a tag. A tag is a key-value pair. You can add up to 50 tags to a report definition.
    public struct Tag: Swift.Sendable {
        /// The key of the tag. Tag keys are case sensitive. Each report definition can only have up to one tag with the same key. If you try to add an existing tag with the same key, the existing tag value will be updated to the new value.
        /// This member is required.
        public var key: Swift.String?
        /// The value of the tag. Tag values are case-sensitive. This can be an empty string.
        /// This member is required.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// The tags assigned to the report definition resource.
    public var tags: [CostandUsageReportClientTypes.Tag]?

    public init(
        tags: [CostandUsageReportClientTypes.Tag]? = nil
    )
    {
        self.tags = tags
    }
}

public struct ModifyReportDefinitionInput: Swift.Sendable {
    /// The definition of Amazon Web Services Cost and Usage Report. You can specify the report name, time unit, report format, compression format, S3 bucket, additional artifacts, and schema elements in the definition.
    /// This member is required.
    public var reportDefinition: CostandUsageReportClientTypes.ReportDefinition?
    /// The name of the report that you want to create. The name must be unique, is case sensitive, and can't include spaces.
    /// This member is required.
    public var reportName: Swift.String?

    public init(
        reportDefinition: CostandUsageReportClientTypes.ReportDefinition? = nil,
        reportName: Swift.String? = nil
    )
    {
        self.reportDefinition = reportDefinition
        self.reportName = reportName
    }
}

public struct ModifyReportDefinitionOutput: Swift.Sendable {

    public init() { }
}

/// A report with the specified name already exists in the account. Specify a different report name.
public struct DuplicateReportNameException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// A message to show the detail of the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DuplicateReportNameException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// This account already has five reports defined. To define a new report, you must delete an existing report.
public struct ReportLimitReachedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// A message to show the detail of the exception.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ReportLimitReachedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Creates a Cost and Usage Report.
public struct PutReportDefinitionInput: Swift.Sendable {
    /// Represents the output of the PutReportDefinition operation. The content consists of the detailed metadata and data file information.
    /// This member is required.
    public var reportDefinition: CostandUsageReportClientTypes.ReportDefinition?
    /// The tags to be assigned to the report definition resource.
    public var tags: [CostandUsageReportClientTypes.Tag]?

    public init(
        reportDefinition: CostandUsageReportClientTypes.ReportDefinition? = nil,
        tags: [CostandUsageReportClientTypes.Tag]? = nil
    )
    {
        self.reportDefinition = reportDefinition
        self.tags = tags
    }
}

/// If the action is successful, the service sends back an HTTP 200 response with an empty HTTP body.
public struct PutReportDefinitionOutput: Swift.Sendable {

    public init() { }
}

public struct TagResourceInput: Swift.Sendable {
    /// The report name of the report definition that tags are to be associated with.
    /// This member is required.
    public var reportName: Swift.String?
    /// The tags to be assigned to the report definition resource.
    /// This member is required.
    public var tags: [CostandUsageReportClientTypes.Tag]?

    public init(
        reportName: Swift.String? = nil,
        tags: [CostandUsageReportClientTypes.Tag]? = nil
    )
    {
        self.reportName = reportName
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The report name of the report definition that tags are to be disassociated from.
    /// This member is required.
    public var reportName: Swift.String?
    /// The tags to be disassociated from the report definition resource.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        reportName: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.reportName = reportName
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

extension DeleteReportDefinitionInput {

    static func urlPathProvider(_ value: DeleteReportDefinitionInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeReportDefinitionsInput {

    static func urlPathProvider(_ value: DescribeReportDefinitionsInput) -> Swift.String? {
        return "/"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        return "/"
    }
}

extension ModifyReportDefinitionInput {

    static func urlPathProvider(_ value: ModifyReportDefinitionInput) -> Swift.String? {
        return "/"
    }
}

extension PutReportDefinitionInput {

    static func urlPathProvider(_ value: PutReportDefinitionInput) -> Swift.String? {
        return "/"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        return "/"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteReportDefinitionInput {

    static func write(value: DeleteReportDefinitionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ReportName"].write(value.reportName)
    }
}

extension DescribeReportDefinitionsInput {

    static func write(value: DescribeReportDefinitionsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListTagsForResourceInput {

    static func write(value: ListTagsForResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ReportName"].write(value.reportName)
    }
}

extension ModifyReportDefinitionInput {

    static func write(value: ModifyReportDefinitionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ReportDefinition"].write(value.reportDefinition, with: CostandUsageReportClientTypes.ReportDefinition.write(value:to:))
        try writer["ReportName"].write(value.reportName)
    }
}

extension PutReportDefinitionInput {

    static func write(value: PutReportDefinitionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ReportDefinition"].write(value.reportDefinition, with: CostandUsageReportClientTypes.ReportDefinition.write(value:to:))
        try writer["Tags"].writeList(value.tags, memberWritingClosure: CostandUsageReportClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ReportName"].write(value.reportName)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: CostandUsageReportClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UntagResourceInput {

    static func write(value: UntagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ReportName"].write(value.reportName)
        try writer["TagKeys"].writeList(value.tagKeys, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DeleteReportDefinitionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteReportDefinitionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteReportDefinitionOutput()
        value.responseMessage = try reader["ResponseMessage"].readIfPresent()
        return value
    }
}

extension DescribeReportDefinitionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeReportDefinitionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeReportDefinitionsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.reportDefinitions = try reader["ReportDefinitions"].readListIfPresent(memberReadingClosure: CostandUsageReportClientTypes.ReportDefinition.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: CostandUsageReportClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ModifyReportDefinitionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ModifyReportDefinitionOutput {
        return ModifyReportDefinitionOutput()
    }
}

extension PutReportDefinitionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutReportDefinitionOutput {
        return PutReportDefinitionOutput()
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

enum DeleteReportDefinitionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeReportDefinitionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ModifyReportDefinitionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutReportDefinitionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DuplicateReportNameException": return try DuplicateReportNameException.makeError(baseError: baseError)
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "ReportLimitReachedException": return try ReportLimitReachedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalErrorException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InternalErrorException {
        let reader = baseError.errorBodyReader
        var value = InternalErrorException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ReportLimitReachedException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ReportLimitReachedException {
        let reader = baseError.errorBodyReader
        var value = ReportLimitReachedException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DuplicateReportNameException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> DuplicateReportNameException {
        let reader = baseError.errorBodyReader
        var value = DuplicateReportNameException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CostandUsageReportClientTypes.ReportDefinition {

    static func write(value: CostandUsageReportClientTypes.ReportDefinition?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AdditionalArtifacts"].writeList(value.additionalArtifacts, memberWritingClosure: SmithyReadWrite.WritingClosureBox<CostandUsageReportClientTypes.AdditionalArtifact>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["AdditionalSchemaElements"].writeList(value.additionalSchemaElements, memberWritingClosure: SmithyReadWrite.WritingClosureBox<CostandUsageReportClientTypes.SchemaElement>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["BillingViewArn"].write(value.billingViewArn)
        try writer["Compression"].write(value.compression)
        try writer["Format"].write(value.format)
        try writer["RefreshClosedReports"].write(value.refreshClosedReports)
        try writer["ReportName"].write(value.reportName)
        try writer["ReportStatus"].write(value.reportStatus, with: CostandUsageReportClientTypes.ReportStatus.write(value:to:))
        try writer["ReportVersioning"].write(value.reportVersioning)
        try writer["S3Bucket"].write(value.s3Bucket)
        try writer["S3Prefix"].write(value.s3Prefix)
        try writer["S3Region"].write(value.s3Region)
        try writer["TimeUnit"].write(value.timeUnit)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CostandUsageReportClientTypes.ReportDefinition {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostandUsageReportClientTypes.ReportDefinition()
        value.reportName = try reader["ReportName"].readIfPresent() ?? ""
        value.timeUnit = try reader["TimeUnit"].readIfPresent() ?? .sdkUnknown("")
        value.format = try reader["Format"].readIfPresent() ?? .sdkUnknown("")
        value.compression = try reader["Compression"].readIfPresent() ?? .sdkUnknown("")
        value.additionalSchemaElements = try reader["AdditionalSchemaElements"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<CostandUsageReportClientTypes.SchemaElement>().read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.s3Bucket = try reader["S3Bucket"].readIfPresent() ?? ""
        value.s3Prefix = try reader["S3Prefix"].readIfPresent() ?? ""
        value.s3Region = try reader["S3Region"].readIfPresent() ?? .sdkUnknown("")
        value.additionalArtifacts = try reader["AdditionalArtifacts"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosureBox<CostandUsageReportClientTypes.AdditionalArtifact>().read(from:), memberNodeInfo: "member", isFlattened: false)
        value.refreshClosedReports = try reader["RefreshClosedReports"].readIfPresent()
        value.reportVersioning = try reader["ReportVersioning"].readIfPresent()
        value.billingViewArn = try reader["BillingViewArn"].readIfPresent()
        value.reportStatus = try reader["ReportStatus"].readIfPresent(with: CostandUsageReportClientTypes.ReportStatus.read(from:))
        return value
    }
}

extension CostandUsageReportClientTypes.ReportStatus {

    static func write(value: CostandUsageReportClientTypes.ReportStatus?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["lastDelivery"].write(value.lastDelivery)
        try writer["lastStatus"].write(value.lastStatus)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CostandUsageReportClientTypes.ReportStatus {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostandUsageReportClientTypes.ReportStatus()
        value.lastDelivery = try reader["lastDelivery"].readIfPresent()
        value.lastStatus = try reader["lastStatus"].readIfPresent()
        return value
    }
}

extension CostandUsageReportClientTypes.Tag {

    static func write(value: CostandUsageReportClientTypes.Tag?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Key"].write(value.key)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CostandUsageReportClientTypes.Tag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CostandUsageReportClientTypes.Tag()
        value.key = try reader["Key"].readIfPresent() ?? ""
        value.value = try reader["Value"].readIfPresent() ?? ""
        return value
    }
}

public enum CostandUsageReportClientTypes {}
