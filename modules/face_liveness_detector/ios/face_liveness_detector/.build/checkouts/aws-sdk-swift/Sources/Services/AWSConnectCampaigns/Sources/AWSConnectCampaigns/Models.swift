//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHT<PERSON>API.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.<PERSON>y<PERSON>eader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem


public struct DeleteCampaignOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteConnectInstanceConfigOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteInstanceOnboardingJobOutput: Swift.Sendable {

    public init() { }
}

public struct PauseCampaignOutput: Swift.Sendable {

    public init() { }
}

public struct ResumeCampaignOutput: Swift.Sendable {

    public init() { }
}

public struct StartCampaignOutput: Swift.Sendable {

    public init() { }
}

public struct StopCampaignOutput: Swift.Sendable {

    public init() { }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateCampaignDialerConfigOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateCampaignNameOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateCampaignOutboundCallConfigOutput: Swift.Sendable {

    public init() { }
}

/// You do not have sufficient access to perform this action.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

extension ConnectCampaignsClientTypes {

    /// Agentless Dialer config
    public struct AgentlessDialerConfig: Swift.Sendable {
        /// Allocates dialing capacity for this campaign between multiple active campaigns
        public var dialingCapacity: Swift.Double?

        public init(
            dialingCapacity: Swift.Double? = nil
        )
        {
            self.dialingCapacity = dialingCapacity
        }
    }
}

/// The request could not be processed because of conflict in the current state of the resource.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

/// Request processing failed because of an error or failure with the service.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

/// The specified resource was not found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

/// Request would cause a service quota to be exceeded.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceQuotaExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

/// The request was denied due to request throttling.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

/// The input fails to satisfy the constraints specified by an AWS service.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

extension ConnectCampaignsClientTypes {

    /// Predictive Dialer config
    public struct PredictiveDialerConfig: Swift.Sendable {
        /// The bandwidth allocation of a queue resource.
        /// This member is required.
        public var bandwidthAllocation: Swift.Double?
        /// Allocates dialing capacity for this campaign between multiple active campaigns
        public var dialingCapacity: Swift.Double?

        public init(
            bandwidthAllocation: Swift.Double? = nil,
            dialingCapacity: Swift.Double? = nil
        )
        {
            self.bandwidthAllocation = bandwidthAllocation
            self.dialingCapacity = dialingCapacity
        }
    }
}

extension ConnectCampaignsClientTypes {

    /// Progressive Dialer config
    public struct ProgressiveDialerConfig: Swift.Sendable {
        /// The bandwidth allocation of a queue resource.
        /// This member is required.
        public var bandwidthAllocation: Swift.Double?
        /// Allocates dialing capacity for this campaign between multiple active campaigns
        public var dialingCapacity: Swift.Double?

        public init(
            bandwidthAllocation: Swift.Double? = nil,
            dialingCapacity: Swift.Double? = nil
        )
        {
            self.bandwidthAllocation = bandwidthAllocation
            self.dialingCapacity = dialingCapacity
        }
    }
}

extension ConnectCampaignsClientTypes {

    /// The possible types of dialer config parameters
    public enum DialerConfig: Swift.Sendable {
        /// Progressive Dialer config
        case progressivedialerconfig(ConnectCampaignsClientTypes.ProgressiveDialerConfig)
        /// Predictive Dialer config
        case predictivedialerconfig(ConnectCampaignsClientTypes.PredictiveDialerConfig)
        /// Agentless Dialer config
        case agentlessdialerconfig(ConnectCampaignsClientTypes.AgentlessDialerConfig)
        case sdkUnknown(Swift.String)
    }
}

extension ConnectCampaignsClientTypes {

    /// Answering Machine Detection config
    public struct AnswerMachineDetectionConfig: Swift.Sendable {
        /// Enable or disable await answer machine prompt
        public var awaitAnswerMachinePrompt: Swift.Bool?
        /// Enable or disable answering machine detection
        /// This member is required.
        public var enableAnswerMachineDetection: Swift.Bool?

        public init(
            awaitAnswerMachinePrompt: Swift.Bool? = nil,
            enableAnswerMachineDetection: Swift.Bool? = nil
        )
        {
            self.awaitAnswerMachinePrompt = awaitAnswerMachinePrompt
            self.enableAnswerMachineDetection = enableAnswerMachineDetection
        }
    }
}

extension ConnectCampaignsClientTypes {

    /// The configuration used for outbound calls.
    public struct OutboundCallConfig: Swift.Sendable {
        /// Answering Machine Detection config
        public var answerMachineDetectionConfig: ConnectCampaignsClientTypes.AnswerMachineDetectionConfig?
        /// The identifier of the contact flow for the outbound call.
        /// This member is required.
        public var connectContactFlowId: Swift.String?
        /// The queue for the call. If you specify a queue, the phone displayed for caller ID is the phone number specified in the queue. If you do not specify a queue, the queue defined in the contact flow is used. If you do not specify a queue, you must specify a source phone number.
        public var connectQueueId: Swift.String?
        /// The phone number associated with the Amazon Connect instance, in E.164 format. If you do not specify a source phone number, you must specify a queue.
        public var connectSourcePhoneNumber: Swift.String?

        public init(
            answerMachineDetectionConfig: ConnectCampaignsClientTypes.AnswerMachineDetectionConfig? = nil,
            connectContactFlowId: Swift.String? = nil,
            connectQueueId: Swift.String? = nil,
            connectSourcePhoneNumber: Swift.String? = nil
        )
        {
            self.answerMachineDetectionConfig = answerMachineDetectionConfig
            self.connectContactFlowId = connectContactFlowId
            self.connectQueueId = connectQueueId
            self.connectSourcePhoneNumber = connectSourcePhoneNumber
        }
    }
}

/// The request for Create Campaign API.
public struct CreateCampaignInput: Swift.Sendable {
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?
    /// The possible types of dialer config parameters
    /// This member is required.
    public var dialerConfig: ConnectCampaignsClientTypes.DialerConfig?
    /// The name of an Amazon Connect Campaign name.
    /// This member is required.
    public var name: Swift.String?
    /// The configuration used for outbound calls.
    /// This member is required.
    public var outboundCallConfig: ConnectCampaignsClientTypes.OutboundCallConfig?
    /// Tag map with key and value.
    public var tags: [Swift.String: Swift.String]?

    public init(
        connectInstanceId: Swift.String? = nil,
        dialerConfig: ConnectCampaignsClientTypes.DialerConfig? = nil,
        name: Swift.String? = nil,
        outboundCallConfig: ConnectCampaignsClientTypes.OutboundCallConfig? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.connectInstanceId = connectInstanceId
        self.dialerConfig = dialerConfig
        self.name = name
        self.outboundCallConfig = outboundCallConfig
        self.tags = tags
    }
}

/// The response for Create Campaign API
public struct CreateCampaignOutput: Swift.Sendable {
    /// The resource name of an Amazon Connect campaign.
    public var arn: Swift.String?
    /// Identifier representing a Campaign
    public var id: Swift.String?
    /// Tag map with key and value.
    public var tags: [Swift.String: Swift.String]?

    public init(
        arn: Swift.String? = nil,
        id: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.arn = arn
        self.id = id
        self.tags = tags
    }
}

/// DeleteCampaignRequest
public struct DeleteCampaignInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

/// The request could not be processed because of conflict in the current state.
public struct InvalidStateException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidStateException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

/// DeleteCampaignRequest
public struct DeleteConnectInstanceConfigInput: Swift.Sendable {
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?

    public init(
        connectInstanceId: Swift.String? = nil
    )
    {
        self.connectInstanceId = connectInstanceId
    }
}

/// The request for DeleteInstanceOnboardingJob API.
public struct DeleteInstanceOnboardingJobInput: Swift.Sendable {
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?

    public init(
        connectInstanceId: Swift.String? = nil
    )
    {
        self.connectInstanceId = connectInstanceId
    }
}

/// DescribeCampaignRequests
public struct DescribeCampaignInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

extension ConnectCampaignsClientTypes {

    /// An Amazon Connect campaign.
    public struct Campaign: Swift.Sendable {
        /// The resource name of an Amazon Connect campaign.
        /// This member is required.
        public var arn: Swift.String?
        /// Amazon Connect Instance Id
        /// This member is required.
        public var connectInstanceId: Swift.String?
        /// The possible types of dialer config parameters
        /// This member is required.
        public var dialerConfig: ConnectCampaignsClientTypes.DialerConfig?
        /// Identifier representing a Campaign
        /// This member is required.
        public var id: Swift.String?
        /// The name of an Amazon Connect Campaign name.
        /// This member is required.
        public var name: Swift.String?
        /// The configuration used for outbound calls.
        /// This member is required.
        public var outboundCallConfig: ConnectCampaignsClientTypes.OutboundCallConfig?
        /// Tag map with key and value.
        public var tags: [Swift.String: Swift.String]?

        public init(
            arn: Swift.String? = nil,
            connectInstanceId: Swift.String? = nil,
            dialerConfig: ConnectCampaignsClientTypes.DialerConfig? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil,
            outboundCallConfig: ConnectCampaignsClientTypes.OutboundCallConfig? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.arn = arn
            self.connectInstanceId = connectInstanceId
            self.dialerConfig = dialerConfig
            self.id = id
            self.name = name
            self.outboundCallConfig = outboundCallConfig
            self.tags = tags
        }
    }
}

/// DescribeCampaignResponse
public struct DescribeCampaignOutput: Swift.Sendable {
    /// An Amazon Connect campaign.
    public var campaign: ConnectCampaignsClientTypes.Campaign?

    public init(
        campaign: ConnectCampaignsClientTypes.Campaign? = nil
    )
    {
        self.campaign = campaign
    }
}

/// GetCampaignStateRequest
public struct GetCampaignStateInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

extension ConnectCampaignsClientTypes {

    /// State of a campaign
    public enum CampaignState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// Campaign is in failed state
        case failed
        /// Campaign is in initialized state
        case initialized
        /// Campaign is in paused state
        case paused
        /// Campaign is in running state
        case running
        /// Campaign is in stopped state
        case stopped
        case sdkUnknown(Swift.String)

        public static var allCases: [CampaignState] {
            return [
                .failed,
                .initialized,
                .paused,
                .running,
                .stopped
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "Failed"
            case .initialized: return "Initialized"
            case .paused: return "Paused"
            case .running: return "Running"
            case .stopped: return "Stopped"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// GetCampaignStateResponse
public struct GetCampaignStateOutput: Swift.Sendable {
    /// State of a campaign
    public var state: ConnectCampaignsClientTypes.CampaignState?

    public init(
        state: ConnectCampaignsClientTypes.CampaignState? = nil
    )
    {
        self.state = state
    }
}

/// GetCampaignStateBatchRequest
public struct GetCampaignStateBatchInput: Swift.Sendable {
    /// List of CampaignId
    /// This member is required.
    public var campaignIds: [Swift.String]?

    public init(
        campaignIds: [Swift.String]? = nil
    )
    {
        self.campaignIds = campaignIds
    }
}

extension ConnectCampaignsClientTypes {

    /// A predefined code indicating the error that caused the failure in getting state of campaigns
    public enum GetCampaignStateBatchFailureCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// The specified resource was not found
        case resourceNotFound
        /// Unexpected error during processing of request
        case unknownError
        case sdkUnknown(Swift.String)

        public static var allCases: [GetCampaignStateBatchFailureCode] {
            return [
                .resourceNotFound,
                .unknownError
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .resourceNotFound: return "ResourceNotFound"
            case .unknownError: return "UnknownError"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsClientTypes {

    /// Failed response of campaign state
    public struct FailedCampaignStateResponse: Swift.Sendable {
        /// Identifier representing a Campaign
        public var campaignId: Swift.String?
        /// A predefined code indicating the error that caused the failure in getting state of campaigns
        public var failureCode: ConnectCampaignsClientTypes.GetCampaignStateBatchFailureCode?

        public init(
            campaignId: Swift.String? = nil,
            failureCode: ConnectCampaignsClientTypes.GetCampaignStateBatchFailureCode? = nil
        )
        {
            self.campaignId = campaignId
            self.failureCode = failureCode
        }
    }
}

extension ConnectCampaignsClientTypes {

    /// Successful response of campaign state
    public struct SuccessfulCampaignStateResponse: Swift.Sendable {
        /// Identifier representing a Campaign
        public var campaignId: Swift.String?
        /// State of a campaign
        public var state: ConnectCampaignsClientTypes.CampaignState?

        public init(
            campaignId: Swift.String? = nil,
            state: ConnectCampaignsClientTypes.CampaignState? = nil
        )
        {
            self.campaignId = campaignId
            self.state = state
        }
    }
}

/// GetCampaignStateBatchResponse
public struct GetCampaignStateBatchOutput: Swift.Sendable {
    /// List of failed requests of campaign state
    public var failedRequests: [ConnectCampaignsClientTypes.FailedCampaignStateResponse]?
    /// List of successful response of campaign state
    public var successfulRequests: [ConnectCampaignsClientTypes.SuccessfulCampaignStateResponse]?

    public init(
        failedRequests: [ConnectCampaignsClientTypes.FailedCampaignStateResponse]? = nil,
        successfulRequests: [ConnectCampaignsClientTypes.SuccessfulCampaignStateResponse]? = nil
    )
    {
        self.failedRequests = failedRequests
        self.successfulRequests = successfulRequests
    }
}

/// GetConnectInstanceConfigRequest
public struct GetConnectInstanceConfigInput: Swift.Sendable {
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?

    public init(
        connectInstanceId: Swift.String? = nil
    )
    {
        self.connectInstanceId = connectInstanceId
    }
}

extension ConnectCampaignsClientTypes {

    /// Server-side encryption type.
    public enum EncryptionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case kms
        case sdkUnknown(Swift.String)

        public static var allCases: [EncryptionType] {
            return [
                .kms
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .kms: return "KMS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsClientTypes {

    /// Encryption config for Connect Instance. Note that sensitive data will always be encrypted. If disabled, service will perform encryption with its own key. If enabled, a KMS key id needs to be provided and KMS charges will apply. KMS is only type supported
    public struct EncryptionConfig: Swift.Sendable {
        /// Boolean to indicate if custom encryption has been enabled.
        /// This member is required.
        public var enabled: Swift.Bool
        /// Server-side encryption type.
        public var encryptionType: ConnectCampaignsClientTypes.EncryptionType?
        /// KMS key id/arn for encryption config.
        public var keyArn: Swift.String?

        public init(
            enabled: Swift.Bool = false,
            encryptionType: ConnectCampaignsClientTypes.EncryptionType? = nil,
            keyArn: Swift.String? = nil
        )
        {
            self.enabled = enabled
            self.encryptionType = encryptionType
            self.keyArn = keyArn
        }
    }
}

extension ConnectCampaignsClientTypes {

    /// Instance config object
    public struct InstanceConfig: Swift.Sendable {
        /// Amazon Connect Instance Id
        /// This member is required.
        public var connectInstanceId: Swift.String?
        /// Encryption config for Connect Instance. Note that sensitive data will always be encrypted. If disabled, service will perform encryption with its own key. If enabled, a KMS key id needs to be provided and KMS charges will apply. KMS is only type supported
        /// This member is required.
        public var encryptionConfig: ConnectCampaignsClientTypes.EncryptionConfig?
        /// Service linked role arn
        /// This member is required.
        public var serviceLinkedRoleArn: Swift.String?

        public init(
            connectInstanceId: Swift.String? = nil,
            encryptionConfig: ConnectCampaignsClientTypes.EncryptionConfig? = nil,
            serviceLinkedRoleArn: Swift.String? = nil
        )
        {
            self.connectInstanceId = connectInstanceId
            self.encryptionConfig = encryptionConfig
            self.serviceLinkedRoleArn = serviceLinkedRoleArn
        }
    }
}

/// GetConnectInstanceConfigResponse
public struct GetConnectInstanceConfigOutput: Swift.Sendable {
    /// Instance config object
    public var connectInstanceConfig: ConnectCampaignsClientTypes.InstanceConfig?

    public init(
        connectInstanceConfig: ConnectCampaignsClientTypes.InstanceConfig? = nil
    )
    {
        self.connectInstanceConfig = connectInstanceConfig
    }
}

/// GetInstanceOnboardingJobStatusRequest
public struct GetInstanceOnboardingJobStatusInput: Swift.Sendable {
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?

    public init(
        connectInstanceId: Swift.String? = nil
    )
    {
        self.connectInstanceId = connectInstanceId
    }
}

extension ConnectCampaignsClientTypes {

    /// Enumeration of the possible failure codes for instance onboarding job
    public enum InstanceOnboardingJobFailureCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case eventBridgeAccessDenied
        case eventBridgeManagedRuleLimitExceeded
        case iamAccessDenied
        case internalFailure
        case kmsAccessDenied
        case kmsKeyNotFound
        case sdkUnknown(Swift.String)

        public static var allCases: [InstanceOnboardingJobFailureCode] {
            return [
                .eventBridgeAccessDenied,
                .eventBridgeManagedRuleLimitExceeded,
                .iamAccessDenied,
                .internalFailure,
                .kmsAccessDenied,
                .kmsKeyNotFound
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .eventBridgeAccessDenied: return "EVENT_BRIDGE_ACCESS_DENIED"
            case .eventBridgeManagedRuleLimitExceeded: return "EVENT_BRIDGE_MANAGED_RULE_LIMIT_EXCEEDED"
            case .iamAccessDenied: return "IAM_ACCESS_DENIED"
            case .internalFailure: return "INTERNAL_FAILURE"
            case .kmsAccessDenied: return "KMS_ACCESS_DENIED"
            case .kmsKeyNotFound: return "KMS_KEY_NOT_FOUND"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsClientTypes {

    /// Enumeration of the possible states for instance onboarding job
    public enum InstanceOnboardingJobStatusCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case inProgress
        case succeeded
        case sdkUnknown(Swift.String)

        public static var allCases: [InstanceOnboardingJobStatusCode] {
            return [
                .failed,
                .inProgress,
                .succeeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case .succeeded: return "SUCCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsClientTypes {

    /// Instance onboarding job status object
    public struct InstanceOnboardingJobStatus: Swift.Sendable {
        /// Amazon Connect Instance Id
        /// This member is required.
        public var connectInstanceId: Swift.String?
        /// Enumeration of the possible failure codes for instance onboarding job
        public var failureCode: ConnectCampaignsClientTypes.InstanceOnboardingJobFailureCode?
        /// Enumeration of the possible states for instance onboarding job
        /// This member is required.
        public var status: ConnectCampaignsClientTypes.InstanceOnboardingJobStatusCode?

        public init(
            connectInstanceId: Swift.String? = nil,
            failureCode: ConnectCampaignsClientTypes.InstanceOnboardingJobFailureCode? = nil,
            status: ConnectCampaignsClientTypes.InstanceOnboardingJobStatusCode? = nil
        )
        {
            self.connectInstanceId = connectInstanceId
            self.failureCode = failureCode
            self.status = status
        }
    }
}

/// GetInstanceOnboardingJobStatusResponse
public struct GetInstanceOnboardingJobStatusOutput: Swift.Sendable {
    /// Instance onboarding job status object
    public var connectInstanceOnboardingJobStatus: ConnectCampaignsClientTypes.InstanceOnboardingJobStatus?

    public init(
        connectInstanceOnboardingJobStatus: ConnectCampaignsClientTypes.InstanceOnboardingJobStatus? = nil
    )
    {
        self.connectInstanceOnboardingJobStatus = connectInstanceOnboardingJobStatus
    }
}

extension ConnectCampaignsClientTypes {

    /// Operators for Connect instance identifier filter
    public enum InstanceIdFilterOperator: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// Equals operator
        case eq
        case sdkUnknown(Swift.String)

        public static var allCases: [InstanceIdFilterOperator] {
            return [
                .eq
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .eq: return "Eq"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsClientTypes {

    /// Connect instance identifier filter
    public struct InstanceIdFilter: Swift.Sendable {
        /// Operators for Connect instance identifier filter
        /// This member is required.
        public var `operator`: ConnectCampaignsClientTypes.InstanceIdFilterOperator?
        /// Amazon Connect Instance Id
        /// This member is required.
        public var value: Swift.String?

        public init(
            `operator`: ConnectCampaignsClientTypes.InstanceIdFilterOperator? = nil,
            value: Swift.String? = nil
        )
        {
            self.`operator` = `operator`
            self.value = value
        }
    }
}

extension ConnectCampaignsClientTypes {

    /// Filter model by type
    public struct CampaignFilters: Swift.Sendable {
        /// Connect instance identifier filter
        public var instanceIdFilter: ConnectCampaignsClientTypes.InstanceIdFilter?

        public init(
            instanceIdFilter: ConnectCampaignsClientTypes.InstanceIdFilter? = nil
        )
        {
            self.instanceIdFilter = instanceIdFilter
        }
    }
}

/// ListCampaignsRequest
public struct ListCampaignsInput: Swift.Sendable {
    /// Filter model by type
    public var filters: ConnectCampaignsClientTypes.CampaignFilters?
    /// The maximum number of results to return per page.
    public var maxResults: Swift.Int?
    /// The token for the next set of results.
    public var nextToken: Swift.String?

    public init(
        filters: ConnectCampaignsClientTypes.CampaignFilters? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension ConnectCampaignsClientTypes {

    /// An Amazon Connect campaign summary.
    public struct CampaignSummary: Swift.Sendable {
        /// The resource name of an Amazon Connect campaign.
        /// This member is required.
        public var arn: Swift.String?
        /// Amazon Connect Instance Id
        /// This member is required.
        public var connectInstanceId: Swift.String?
        /// Identifier representing a Campaign
        /// This member is required.
        public var id: Swift.String?
        /// The name of an Amazon Connect Campaign name.
        /// This member is required.
        public var name: Swift.String?

        public init(
            arn: Swift.String? = nil,
            connectInstanceId: Swift.String? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil
        )
        {
            self.arn = arn
            self.connectInstanceId = connectInstanceId
            self.id = id
            self.name = name
        }
    }
}

/// ListCampaignsResponse
public struct ListCampaignsOutput: Swift.Sendable {
    /// A list of Amazon Connect campaigns.
    public var campaignSummaryList: [ConnectCampaignsClientTypes.CampaignSummary]?
    /// The token for the next set of results.
    public var nextToken: Swift.String?

    public init(
        campaignSummaryList: [ConnectCampaignsClientTypes.CampaignSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.campaignSummaryList = campaignSummaryList
        self.nextToken = nextToken
    }
}

/// ListTagsForResource
public struct ListTagsForResourceInput: Swift.Sendable {
    /// Arn
    /// This member is required.
    public var arn: Swift.String?

    public init(
        arn: Swift.String? = nil
    )
    {
        self.arn = arn
    }
}

/// ListTagsForResponse
public struct ListTagsForResourceOutput: Swift.Sendable {
    /// Tag map with key and value.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

/// The request could not be processed because of conflict in the current state of the campaign.
public struct InvalidCampaignStateException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// State of a campaign
        /// This member is required.
        public internal(set) var state: ConnectCampaignsClientTypes.CampaignState? = nil
        /// A header that defines the error encountered while processing the request.
        public internal(set) var xAmzErrorType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidCampaignStateException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        state: ConnectCampaignsClientTypes.CampaignState? = nil,
        xAmzErrorType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.state = state
        self.properties.xAmzErrorType = xAmzErrorType
    }
}

/// PauseCampaignRequest
public struct PauseCampaignInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

extension ConnectCampaignsClientTypes {

    /// A dial request for a campaign.
    public struct DialRequest: Swift.Sendable {
        /// A custom key-value pair using an attribute map. The attributes are standard Amazon Connect attributes, and can be accessed in contact flows just like any other contact attributes.
        /// This member is required.
        public var attributes: [Swift.String: Swift.String]?
        /// Client provided parameter used for idempotency. Its value must be unique for each request.
        /// This member is required.
        public var clientToken: Swift.String?
        /// Timestamp with no UTC offset or timezone
        /// This member is required.
        public var expirationTime: Foundation.Date?
        /// The phone number of the customer, in E.164 format.
        /// This member is required.
        public var phoneNumber: Swift.String?

        public init(
            attributes: [Swift.String: Swift.String]? = nil,
            clientToken: Swift.String? = nil,
            expirationTime: Foundation.Date? = nil,
            phoneNumber: Swift.String? = nil
        )
        {
            self.attributes = attributes
            self.clientToken = clientToken
            self.expirationTime = expirationTime
            self.phoneNumber = phoneNumber
        }
    }
}

extension ConnectCampaignsClientTypes.DialRequest: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "DialRequest(clientToken: \(Swift.String(describing: clientToken)), expirationTime: \(Swift.String(describing: expirationTime)), attributes: \"CONTENT_REDACTED\", phoneNumber: \"CONTENT_REDACTED\")"}
}

/// PutDialRequestBatchRequest
public struct PutDialRequestBatchInput: Swift.Sendable {
    /// A list of dial requests.
    /// This member is required.
    public var dialRequests: [ConnectCampaignsClientTypes.DialRequest]?
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        dialRequests: [ConnectCampaignsClientTypes.DialRequest]? = nil,
        id: Swift.String? = nil
    )
    {
        self.dialRequests = dialRequests
        self.id = id
    }
}

extension ConnectCampaignsClientTypes {

    /// A predefined code indicating the error that caused the failure.
    public enum FailureCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        /// The request failed to satisfy the constraints specified by the service
        case invalidInput
        /// Request throttled due to large number of pending dial requests
        case requestThrottled
        /// Unexpected error during processing of request
        case unknownError
        case sdkUnknown(Swift.String)

        public static var allCases: [FailureCode] {
            return [
                .invalidInput,
                .requestThrottled,
                .unknownError
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .invalidInput: return "InvalidInput"
            case .requestThrottled: return "RequestThrottled"
            case .unknownError: return "UnknownError"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectCampaignsClientTypes {

    /// A failed request identified by the unique client token.
    public struct FailedRequest: Swift.Sendable {
        /// Client provided parameter used for idempotency. Its value must be unique for each request.
        public var clientToken: Swift.String?
        /// A predefined code indicating the error that caused the failure.
        public var failureCode: ConnectCampaignsClientTypes.FailureCode?
        /// Identifier representing a Dial request
        public var id: Swift.String?

        public init(
            clientToken: Swift.String? = nil,
            failureCode: ConnectCampaignsClientTypes.FailureCode? = nil,
            id: Swift.String? = nil
        )
        {
            self.clientToken = clientToken
            self.failureCode = failureCode
            self.id = id
        }
    }
}

extension ConnectCampaignsClientTypes {

    /// A successful request identified by the unique client token.
    public struct SuccessfulRequest: Swift.Sendable {
        /// Client provided parameter used for idempotency. Its value must be unique for each request.
        public var clientToken: Swift.String?
        /// Identifier representing a Dial request
        public var id: Swift.String?

        public init(
            clientToken: Swift.String? = nil,
            id: Swift.String? = nil
        )
        {
            self.clientToken = clientToken
            self.id = id
        }
    }
}

/// PutDialRequestBatchResponse
public struct PutDialRequestBatchOutput: Swift.Sendable {
    /// A list of failed requests.
    public var failedRequests: [ConnectCampaignsClientTypes.FailedRequest]?
    /// A list of successful requests identified by the unique client token.
    public var successfulRequests: [ConnectCampaignsClientTypes.SuccessfulRequest]?

    public init(
        failedRequests: [ConnectCampaignsClientTypes.FailedRequest]? = nil,
        successfulRequests: [ConnectCampaignsClientTypes.SuccessfulRequest]? = nil
    )
    {
        self.failedRequests = failedRequests
        self.successfulRequests = successfulRequests
    }
}

/// ResumeCampaignRequest
public struct ResumeCampaignInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

/// StartCampaignRequest
public struct StartCampaignInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

/// The request for StartInstanceOnboardingJob API.
public struct StartInstanceOnboardingJobInput: Swift.Sendable {
    /// Amazon Connect Instance Id
    /// This member is required.
    public var connectInstanceId: Swift.String?
    /// Encryption config for Connect Instance. Note that sensitive data will always be encrypted. If disabled, service will perform encryption with its own key. If enabled, a KMS key id needs to be provided and KMS charges will apply. KMS is only type supported
    /// This member is required.
    public var encryptionConfig: ConnectCampaignsClientTypes.EncryptionConfig?

    public init(
        connectInstanceId: Swift.String? = nil,
        encryptionConfig: ConnectCampaignsClientTypes.EncryptionConfig? = nil
    )
    {
        self.connectInstanceId = connectInstanceId
        self.encryptionConfig = encryptionConfig
    }
}

/// The response for StartInstanceOnboardingJob API.
public struct StartInstanceOnboardingJobOutput: Swift.Sendable {
    /// Instance onboarding job status object
    public var connectInstanceOnboardingJobStatus: ConnectCampaignsClientTypes.InstanceOnboardingJobStatus?

    public init(
        connectInstanceOnboardingJobStatus: ConnectCampaignsClientTypes.InstanceOnboardingJobStatus? = nil
    )
    {
        self.connectInstanceOnboardingJobStatus = connectInstanceOnboardingJobStatus
    }
}

/// StopCampaignRequest
public struct StopCampaignInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        id: Swift.String? = nil
    )
    {
        self.id = id
    }
}

/// TagResourceRequest
public struct TagResourceInput: Swift.Sendable {
    /// Arn
    /// This member is required.
    public var arn: Swift.String?
    /// Tag map with key and value.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        arn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.arn = arn
        self.tags = tags
    }
}

/// UntagResourceRequest
public struct UntagResourceInput: Swift.Sendable {
    /// Arn
    /// This member is required.
    public var arn: Swift.String?
    /// List of tag keys.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        arn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.arn = arn
        self.tagKeys = tagKeys
    }
}

/// UpdateCampaignDialerConfigRequest
public struct UpdateCampaignDialerConfigInput: Swift.Sendable {
    /// The possible types of dialer config parameters
    /// This member is required.
    public var dialerConfig: ConnectCampaignsClientTypes.DialerConfig?
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        dialerConfig: ConnectCampaignsClientTypes.DialerConfig? = nil,
        id: Swift.String? = nil
    )
    {
        self.dialerConfig = dialerConfig
        self.id = id
    }
}

/// UpdateCampaignNameRequest
public struct UpdateCampaignNameInput: Swift.Sendable {
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?
    /// The name of an Amazon Connect Campaign name.
    /// This member is required.
    public var name: Swift.String?

    public init(
        id: Swift.String? = nil,
        name: Swift.String? = nil
    )
    {
        self.id = id
        self.name = name
    }
}

/// UpdateCampaignOutboundCallConfigRequest
public struct UpdateCampaignOutboundCallConfigInput: Swift.Sendable {
    /// Answering Machine Detection config
    public var answerMachineDetectionConfig: ConnectCampaignsClientTypes.AnswerMachineDetectionConfig?
    /// The identifier of the contact flow for the outbound call.
    public var connectContactFlowId: Swift.String?
    /// The phone number associated with the Amazon Connect instance, in E.164 format. If you do not specify a source phone number, you must specify a queue.
    public var connectSourcePhoneNumber: Swift.String?
    /// Identifier representing a Campaign
    /// This member is required.
    public var id: Swift.String?

    public init(
        answerMachineDetectionConfig: ConnectCampaignsClientTypes.AnswerMachineDetectionConfig? = nil,
        connectContactFlowId: Swift.String? = nil,
        connectSourcePhoneNumber: Swift.String? = nil,
        id: Swift.String? = nil
    )
    {
        self.answerMachineDetectionConfig = answerMachineDetectionConfig
        self.connectContactFlowId = connectContactFlowId
        self.connectSourcePhoneNumber = connectSourcePhoneNumber
        self.id = id
    }
}

extension CreateCampaignInput {

    static func urlPathProvider(_ value: CreateCampaignInput) -> Swift.String? {
        return "/campaigns"
    }
}

extension DeleteCampaignInput {

    static func urlPathProvider(_ value: DeleteCampaignInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/campaigns/\(id.urlPercentEncoding())"
    }
}

extension DeleteConnectInstanceConfigInput {

    static func urlPathProvider(_ value: DeleteConnectInstanceConfigInput) -> Swift.String? {
        guard let connectInstanceId = value.connectInstanceId else {
            return nil
        }
        return "/connect-instance/\(connectInstanceId.urlPercentEncoding())/config"
    }
}

extension DeleteInstanceOnboardingJobInput {

    static func urlPathProvider(_ value: DeleteInstanceOnboardingJobInput) -> Swift.String? {
        guard let connectInstanceId = value.connectInstanceId else {
            return nil
        }
        return "/connect-instance/\(connectInstanceId.urlPercentEncoding())/onboarding"
    }
}

extension DescribeCampaignInput {

    static func urlPathProvider(_ value: DescribeCampaignInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/campaigns/\(id.urlPercentEncoding())"
    }
}

extension GetCampaignStateInput {

    static func urlPathProvider(_ value: GetCampaignStateInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/campaigns/\(id.urlPercentEncoding())/state"
    }
}

extension GetCampaignStateBatchInput {

    static func urlPathProvider(_ value: GetCampaignStateBatchInput) -> Swift.String? {
        return "/campaigns-state"
    }
}

extension GetConnectInstanceConfigInput {

    static func urlPathProvider(_ value: GetConnectInstanceConfigInput) -> Swift.String? {
        guard let connectInstanceId = value.connectInstanceId else {
            return nil
        }
        return "/connect-instance/\(connectInstanceId.urlPercentEncoding())/config"
    }
}

extension GetInstanceOnboardingJobStatusInput {

    static func urlPathProvider(_ value: GetInstanceOnboardingJobStatusInput) -> Swift.String? {
        guard let connectInstanceId = value.connectInstanceId else {
            return nil
        }
        return "/connect-instance/\(connectInstanceId.urlPercentEncoding())/onboarding"
    }
}

extension ListCampaignsInput {

    static func urlPathProvider(_ value: ListCampaignsInput) -> Swift.String? {
        return "/campaigns-summary"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let arn = value.arn else {
            return nil
        }
        return "/tags/\(arn.urlPercentEncoding())"
    }
}

extension PauseCampaignInput {

    static func urlPathProvider(_ value: PauseCampaignInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/campaigns/\(id.urlPercentEncoding())/pause"
    }
}

extension PutDialRequestBatchInput {

    static func urlPathProvider(_ value: PutDialRequestBatchInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/campaigns/\(id.urlPercentEncoding())/dial-requests"
    }
}

extension ResumeCampaignInput {

    static func urlPathProvider(_ value: ResumeCampaignInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/campaigns/\(id.urlPercentEncoding())/resume"
    }
}

extension StartCampaignInput {

    static func urlPathProvider(_ value: StartCampaignInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/campaigns/\(id.urlPercentEncoding())/start"
    }
}

extension StartInstanceOnboardingJobInput {

    static func urlPathProvider(_ value: StartInstanceOnboardingJobInput) -> Swift.String? {
        guard let connectInstanceId = value.connectInstanceId else {
            return nil
        }
        return "/connect-instance/\(connectInstanceId.urlPercentEncoding())/onboarding"
    }
}

extension StopCampaignInput {

    static func urlPathProvider(_ value: StopCampaignInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/campaigns/\(id.urlPercentEncoding())/stop"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let arn = value.arn else {
            return nil
        }
        return "/tags/\(arn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let arn = value.arn else {
            return nil
        }
        return "/tags/\(arn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension UpdateCampaignDialerConfigInput {

    static func urlPathProvider(_ value: UpdateCampaignDialerConfigInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/campaigns/\(id.urlPercentEncoding())/dialer-config"
    }
}

extension UpdateCampaignNameInput {

    static func urlPathProvider(_ value: UpdateCampaignNameInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/campaigns/\(id.urlPercentEncoding())/name"
    }
}

extension UpdateCampaignOutboundCallConfigInput {

    static func urlPathProvider(_ value: UpdateCampaignOutboundCallConfigInput) -> Swift.String? {
        guard let id = value.id else {
            return nil
        }
        return "/campaigns/\(id.urlPercentEncoding())/outbound-call-config"
    }
}

extension CreateCampaignInput {

    static func write(value: CreateCampaignInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["connectInstanceId"].write(value.connectInstanceId)
        try writer["dialerConfig"].write(value.dialerConfig, with: ConnectCampaignsClientTypes.DialerConfig.write(value:to:))
        try writer["name"].write(value.name)
        try writer["outboundCallConfig"].write(value.outboundCallConfig, with: ConnectCampaignsClientTypes.OutboundCallConfig.write(value:to:))
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension GetCampaignStateBatchInput {

    static func write(value: GetCampaignStateBatchInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["campaignIds"].writeList(value.campaignIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension ListCampaignsInput {

    static func write(value: ListCampaignsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filters"].write(value.filters, with: ConnectCampaignsClientTypes.CampaignFilters.write(value:to:))
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension PutDialRequestBatchInput {

    static func write(value: PutDialRequestBatchInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["dialRequests"].writeList(value.dialRequests, memberWritingClosure: ConnectCampaignsClientTypes.DialRequest.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension StartInstanceOnboardingJobInput {

    static func write(value: StartInstanceOnboardingJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["encryptionConfig"].write(value.encryptionConfig, with: ConnectCampaignsClientTypes.EncryptionConfig.write(value:to:))
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension UpdateCampaignDialerConfigInput {

    static func write(value: UpdateCampaignDialerConfigInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["dialerConfig"].write(value.dialerConfig, with: ConnectCampaignsClientTypes.DialerConfig.write(value:to:))
    }
}

extension UpdateCampaignNameInput {

    static func write(value: UpdateCampaignNameInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["name"].write(value.name)
    }
}

extension UpdateCampaignOutboundCallConfigInput {

    static func write(value: UpdateCampaignOutboundCallConfigInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["answerMachineDetectionConfig"].write(value.answerMachineDetectionConfig, with: ConnectCampaignsClientTypes.AnswerMachineDetectionConfig.write(value:to:))
        try writer["connectContactFlowId"].write(value.connectContactFlowId)
        try writer["connectSourcePhoneNumber"].write(value.connectSourcePhoneNumber)
    }
}

extension CreateCampaignOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateCampaignOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateCampaignOutput()
        value.arn = try reader["arn"].readIfPresent()
        value.id = try reader["id"].readIfPresent()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DeleteCampaignOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteCampaignOutput {
        return DeleteCampaignOutput()
    }
}

extension DeleteConnectInstanceConfigOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteConnectInstanceConfigOutput {
        return DeleteConnectInstanceConfigOutput()
    }
}

extension DeleteInstanceOnboardingJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteInstanceOnboardingJobOutput {
        return DeleteInstanceOnboardingJobOutput()
    }
}

extension DescribeCampaignOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeCampaignOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeCampaignOutput()
        value.campaign = try reader["campaign"].readIfPresent(with: ConnectCampaignsClientTypes.Campaign.read(from:))
        return value
    }
}

extension GetCampaignStateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetCampaignStateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetCampaignStateOutput()
        value.state = try reader["state"].readIfPresent()
        return value
    }
}

extension GetCampaignStateBatchOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetCampaignStateBatchOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetCampaignStateBatchOutput()
        value.failedRequests = try reader["failedRequests"].readListIfPresent(memberReadingClosure: ConnectCampaignsClientTypes.FailedCampaignStateResponse.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.successfulRequests = try reader["successfulRequests"].readListIfPresent(memberReadingClosure: ConnectCampaignsClientTypes.SuccessfulCampaignStateResponse.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension GetConnectInstanceConfigOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetConnectInstanceConfigOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetConnectInstanceConfigOutput()
        value.connectInstanceConfig = try reader["connectInstanceConfig"].readIfPresent(with: ConnectCampaignsClientTypes.InstanceConfig.read(from:))
        return value
    }
}

extension GetInstanceOnboardingJobStatusOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetInstanceOnboardingJobStatusOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetInstanceOnboardingJobStatusOutput()
        value.connectInstanceOnboardingJobStatus = try reader["connectInstanceOnboardingJobStatus"].readIfPresent(with: ConnectCampaignsClientTypes.InstanceOnboardingJobStatus.read(from:))
        return value
    }
}

extension ListCampaignsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListCampaignsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListCampaignsOutput()
        value.campaignSummaryList = try reader["campaignSummaryList"].readListIfPresent(memberReadingClosure: ConnectCampaignsClientTypes.CampaignSummary.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension PauseCampaignOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PauseCampaignOutput {
        return PauseCampaignOutput()
    }
}

extension PutDialRequestBatchOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutDialRequestBatchOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutDialRequestBatchOutput()
        value.failedRequests = try reader["failedRequests"].readListIfPresent(memberReadingClosure: ConnectCampaignsClientTypes.FailedRequest.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.successfulRequests = try reader["successfulRequests"].readListIfPresent(memberReadingClosure: ConnectCampaignsClientTypes.SuccessfulRequest.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ResumeCampaignOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ResumeCampaignOutput {
        return ResumeCampaignOutput()
    }
}

extension StartCampaignOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartCampaignOutput {
        return StartCampaignOutput()
    }
}

extension StartInstanceOnboardingJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartInstanceOnboardingJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartInstanceOnboardingJobOutput()
        value.connectInstanceOnboardingJobStatus = try reader["connectInstanceOnboardingJobStatus"].readIfPresent(with: ConnectCampaignsClientTypes.InstanceOnboardingJobStatus.read(from:))
        return value
    }
}

extension StopCampaignOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopCampaignOutput {
        return StopCampaignOutput()
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateCampaignDialerConfigOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateCampaignDialerConfigOutput {
        return UpdateCampaignDialerConfigOutput()
    }
}

extension UpdateCampaignNameOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateCampaignNameOutput {
        return UpdateCampaignNameOutput()
    }
}

extension UpdateCampaignOutboundCallConfigOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateCampaignOutboundCallConfigOutput {
        return UpdateCampaignOutboundCallConfigOutput()
    }
}

enum CreateCampaignOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteCampaignOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteConnectInstanceConfigOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidStateException": return try InvalidStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteInstanceOnboardingJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidStateException": return try InvalidStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeCampaignOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetCampaignStateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetCampaignStateBatchOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetConnectInstanceConfigOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetInstanceOnboardingJobStatusOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListCampaignsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PauseCampaignOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutDialRequestBatchOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ResumeCampaignOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartCampaignOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartInstanceOnboardingJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopCampaignOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidCampaignStateException": return try InvalidCampaignStateException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateCampaignDialerConfigOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateCampaignNameOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateCampaignOutboundCallConfigOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceQuotaExceededException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ServiceQuotaExceededException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = AccessDeniedException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ConflictException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ValidationException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ResourceNotFoundException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ThrottlingException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = InternalServerException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidStateException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InvalidStateException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = InvalidStateException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidCampaignStateException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InvalidCampaignStateException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = InvalidCampaignStateException()
        if let xAmzErrorTypeHeaderValue = httpResponse.headers.value(for: "x-amzn-ErrorType") {
            value.properties.xAmzErrorType = xAmzErrorTypeHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.state = try reader["state"].readIfPresent() ?? .sdkUnknown("")
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConnectCampaignsClientTypes.Campaign {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.Campaign {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsClientTypes.Campaign()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent() ?? ""
        value.connectInstanceId = try reader["connectInstanceId"].readIfPresent() ?? ""
        value.dialerConfig = try reader["dialerConfig"].readIfPresent(with: ConnectCampaignsClientTypes.DialerConfig.read(from:))
        value.outboundCallConfig = try reader["outboundCallConfig"].readIfPresent(with: ConnectCampaignsClientTypes.OutboundCallConfig.read(from:))
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension ConnectCampaignsClientTypes.OutboundCallConfig {

    static func write(value: ConnectCampaignsClientTypes.OutboundCallConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["answerMachineDetectionConfig"].write(value.answerMachineDetectionConfig, with: ConnectCampaignsClientTypes.AnswerMachineDetectionConfig.write(value:to:))
        try writer["connectContactFlowId"].write(value.connectContactFlowId)
        try writer["connectQueueId"].write(value.connectQueueId)
        try writer["connectSourcePhoneNumber"].write(value.connectSourcePhoneNumber)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.OutboundCallConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsClientTypes.OutboundCallConfig()
        value.connectContactFlowId = try reader["connectContactFlowId"].readIfPresent() ?? ""
        value.connectSourcePhoneNumber = try reader["connectSourcePhoneNumber"].readIfPresent()
        value.connectQueueId = try reader["connectQueueId"].readIfPresent()
        value.answerMachineDetectionConfig = try reader["answerMachineDetectionConfig"].readIfPresent(with: ConnectCampaignsClientTypes.AnswerMachineDetectionConfig.read(from:))
        return value
    }
}

extension ConnectCampaignsClientTypes.AnswerMachineDetectionConfig {

    static func write(value: ConnectCampaignsClientTypes.AnswerMachineDetectionConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["awaitAnswerMachinePrompt"].write(value.awaitAnswerMachinePrompt)
        try writer["enableAnswerMachineDetection"].write(value.enableAnswerMachineDetection)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.AnswerMachineDetectionConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsClientTypes.AnswerMachineDetectionConfig()
        value.enableAnswerMachineDetection = try reader["enableAnswerMachineDetection"].readIfPresent() ?? false
        value.awaitAnswerMachinePrompt = try reader["awaitAnswerMachinePrompt"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsClientTypes.DialerConfig {

    static func write(value: ConnectCampaignsClientTypes.DialerConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        switch value {
            case let .agentlessdialerconfig(agentlessdialerconfig):
                try writer["agentlessDialerConfig"].write(agentlessdialerconfig, with: ConnectCampaignsClientTypes.AgentlessDialerConfig.write(value:to:))
            case let .predictivedialerconfig(predictivedialerconfig):
                try writer["predictiveDialerConfig"].write(predictivedialerconfig, with: ConnectCampaignsClientTypes.PredictiveDialerConfig.write(value:to:))
            case let .progressivedialerconfig(progressivedialerconfig):
                try writer["progressiveDialerConfig"].write(progressivedialerconfig, with: ConnectCampaignsClientTypes.ProgressiveDialerConfig.write(value:to:))
            case let .sdkUnknown(sdkUnknown):
                try writer["sdkUnknown"].write(sdkUnknown)
        }
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.DialerConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "progressiveDialerConfig":
                return .progressivedialerconfig(try reader["progressiveDialerConfig"].read(with: ConnectCampaignsClientTypes.ProgressiveDialerConfig.read(from:)))
            case "predictiveDialerConfig":
                return .predictivedialerconfig(try reader["predictiveDialerConfig"].read(with: ConnectCampaignsClientTypes.PredictiveDialerConfig.read(from:)))
            case "agentlessDialerConfig":
                return .agentlessdialerconfig(try reader["agentlessDialerConfig"].read(with: ConnectCampaignsClientTypes.AgentlessDialerConfig.read(from:)))
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension ConnectCampaignsClientTypes.AgentlessDialerConfig {

    static func write(value: ConnectCampaignsClientTypes.AgentlessDialerConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["dialingCapacity"].write(value.dialingCapacity)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.AgentlessDialerConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsClientTypes.AgentlessDialerConfig()
        value.dialingCapacity = try reader["dialingCapacity"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsClientTypes.PredictiveDialerConfig {

    static func write(value: ConnectCampaignsClientTypes.PredictiveDialerConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["bandwidthAllocation"].write(value.bandwidthAllocation)
        try writer["dialingCapacity"].write(value.dialingCapacity)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.PredictiveDialerConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsClientTypes.PredictiveDialerConfig()
        value.bandwidthAllocation = try reader["bandwidthAllocation"].readIfPresent() ?? 0.0
        value.dialingCapacity = try reader["dialingCapacity"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsClientTypes.ProgressiveDialerConfig {

    static func write(value: ConnectCampaignsClientTypes.ProgressiveDialerConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["bandwidthAllocation"].write(value.bandwidthAllocation)
        try writer["dialingCapacity"].write(value.dialingCapacity)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.ProgressiveDialerConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsClientTypes.ProgressiveDialerConfig()
        value.bandwidthAllocation = try reader["bandwidthAllocation"].readIfPresent() ?? 0.0
        value.dialingCapacity = try reader["dialingCapacity"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsClientTypes.SuccessfulCampaignStateResponse {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.SuccessfulCampaignStateResponse {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsClientTypes.SuccessfulCampaignStateResponse()
        value.campaignId = try reader["campaignId"].readIfPresent()
        value.state = try reader["state"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsClientTypes.FailedCampaignStateResponse {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.FailedCampaignStateResponse {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsClientTypes.FailedCampaignStateResponse()
        value.campaignId = try reader["campaignId"].readIfPresent()
        value.failureCode = try reader["failureCode"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsClientTypes.InstanceConfig {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.InstanceConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsClientTypes.InstanceConfig()
        value.connectInstanceId = try reader["connectInstanceId"].readIfPresent() ?? ""
        value.serviceLinkedRoleArn = try reader["serviceLinkedRoleArn"].readIfPresent() ?? ""
        value.encryptionConfig = try reader["encryptionConfig"].readIfPresent(with: ConnectCampaignsClientTypes.EncryptionConfig.read(from:))
        return value
    }
}

extension ConnectCampaignsClientTypes.EncryptionConfig {

    static func write(value: ConnectCampaignsClientTypes.EncryptionConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["enabled"].write(value.enabled)
        try writer["encryptionType"].write(value.encryptionType)
        try writer["keyArn"].write(value.keyArn)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.EncryptionConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsClientTypes.EncryptionConfig()
        value.enabled = try reader["enabled"].readIfPresent() ?? false
        value.encryptionType = try reader["encryptionType"].readIfPresent()
        value.keyArn = try reader["keyArn"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsClientTypes.InstanceOnboardingJobStatus {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.InstanceOnboardingJobStatus {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsClientTypes.InstanceOnboardingJobStatus()
        value.connectInstanceId = try reader["connectInstanceId"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        value.failureCode = try reader["failureCode"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsClientTypes.CampaignSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.CampaignSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsClientTypes.CampaignSummary()
        value.id = try reader["id"].readIfPresent() ?? ""
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.name = try reader["name"].readIfPresent() ?? ""
        value.connectInstanceId = try reader["connectInstanceId"].readIfPresent() ?? ""
        return value
    }
}

extension ConnectCampaignsClientTypes.SuccessfulRequest {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.SuccessfulRequest {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsClientTypes.SuccessfulRequest()
        value.clientToken = try reader["clientToken"].readIfPresent()
        value.id = try reader["id"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsClientTypes.FailedRequest {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectCampaignsClientTypes.FailedRequest {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectCampaignsClientTypes.FailedRequest()
        value.clientToken = try reader["clientToken"].readIfPresent()
        value.id = try reader["id"].readIfPresent()
        value.failureCode = try reader["failureCode"].readIfPresent()
        return value
    }
}

extension ConnectCampaignsClientTypes.CampaignFilters {

    static func write(value: ConnectCampaignsClientTypes.CampaignFilters?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["instanceIdFilter"].write(value.instanceIdFilter, with: ConnectCampaignsClientTypes.InstanceIdFilter.write(value:to:))
    }
}

extension ConnectCampaignsClientTypes.InstanceIdFilter {

    static func write(value: ConnectCampaignsClientTypes.InstanceIdFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["operator"].write(value.`operator`)
        try writer["value"].write(value.value)
    }
}

extension ConnectCampaignsClientTypes.DialRequest {

    static func write(value: ConnectCampaignsClientTypes.DialRequest?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["attributes"].writeMap(value.attributes, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["clientToken"].write(value.clientToken)
        try writer["expirationTime"].writeTimestamp(value.expirationTime, format: SmithyTimestamps.TimestampFormat.dateTime)
        try writer["phoneNumber"].write(value.phoneNumber)
    }
}

public enum ConnectCampaignsClientTypes {}
