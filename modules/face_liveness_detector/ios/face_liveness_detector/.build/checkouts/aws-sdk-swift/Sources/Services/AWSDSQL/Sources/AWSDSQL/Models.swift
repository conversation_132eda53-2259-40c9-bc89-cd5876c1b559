//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.<PERSON>y<PERSON>eader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem
@_spi(SmithyTimestamps) import struct SmithyTimestamps.TimestampFormatter


public struct DeleteMultiRegionClustersOutput: Swift.Sendable {

    public init() { }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

/// You do not have sufficient access to perform this action.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension DSQLClientTypes {

    /// Cluster Status
    public enum ClusterStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case active
        case creating
        case deleted
        case deleting
        case failed
        case updating
        case sdkUnknown(Swift.String)

        public static var allCases: [ClusterStatus] {
            return [
                .active,
                .creating,
                .deleted,
                .deleting,
                .failed,
                .updating
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .active: return "ACTIVE"
            case .creating: return "CREATING"
            case .deleted: return "DELETED"
            case .deleting: return "DELETING"
            case .failed: return "FAILED"
            case .updating: return "UPDATING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The submitted action has conflicts.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// Resource Id
        public internal(set) var resourceId: Swift.String? = nil
        /// Resource Type
        public internal(set) var resourceType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.resourceId = resourceId
        self.properties.resourceType = resourceType
    }
}

/// The service limit was exceeded.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Description of the error
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// Service Quotas requirement to identify originating quota
        /// This member is required.
        public internal(set) var quotaCode: Swift.String? = nil
        /// Identifier of the resource affected
        /// This member is required.
        public internal(set) var resourceId: Swift.String? = nil
        /// Type of the resource affected
        /// This member is required.
        public internal(set) var resourceType: Swift.String? = nil
        /// Service Quotas requirement to identify originating service
        /// This member is required.
        public internal(set) var serviceCode: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceQuotaExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        quotaCode: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: Swift.String? = nil,
        serviceCode: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.quotaCode = quotaCode
        self.properties.resourceId = resourceId
        self.properties.resourceType = resourceType
        self.properties.serviceCode = serviceCode
    }
}

public struct CreateClusterInput: Swift.Sendable {
    /// A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, the subsequent retries with the same client token return the result from the original successful request and they have no additional effect. If you don't specify a client token, the Amazon Web Services SDK automatically generates one.
    public var clientToken: Swift.String?
    /// If enabled, you can't delete your cluster. You must first disable this property before you can delete your cluster.
    public var deletionProtectionEnabled: Swift.Bool?
    /// A map of key and value pairs to use to tag your cluster.
    public var tags: [Swift.String: Swift.String]?

    public init(
        clientToken: Swift.String? = nil,
        deletionProtectionEnabled: Swift.Bool? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.clientToken = clientToken
        self.deletionProtectionEnabled = deletionProtectionEnabled
        self.tags = tags
    }
}

/// Output Mixin
public struct CreateClusterOutput: Swift.Sendable {
    /// The ARN of the created cluster.
    /// This member is required.
    public var arn: Swift.String?
    /// The time of when created the cluster.
    /// This member is required.
    public var creationTime: Foundation.Date?
    /// Whether deletion protection is enabled on this cluster.
    /// This member is required.
    public var deletionProtectionEnabled: Swift.Bool?
    /// The ID of the created cluster.
    /// This member is required.
    public var identifier: Swift.String?
    /// The status of the created cluster.
    /// This member is required.
    public var status: DSQLClientTypes.ClusterStatus?

    public init(
        arn: Swift.String? = nil,
        creationTime: Foundation.Date? = nil,
        deletionProtectionEnabled: Swift.Bool? = nil,
        identifier: Swift.String? = nil,
        status: DSQLClientTypes.ClusterStatus? = nil
    )
    {
        self.arn = arn
        self.creationTime = creationTime
        self.deletionProtectionEnabled = deletionProtectionEnabled
        self.identifier = identifier
        self.status = status
    }
}

extension DSQLClientTypes {

    /// Properties of linked clusters.
    public struct LinkedClusterProperties: Swift.Sendable {
        /// Whether deletion protection is enabled.
        public var deletionProtectionEnabled: Swift.Bool?
        /// A map of key and value pairs the linked cluster is tagged with.
        public var tags: [Swift.String: Swift.String]?

        public init(
            deletionProtectionEnabled: Swift.Bool? = true,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.deletionProtectionEnabled = deletionProtectionEnabled
            self.tags = tags
        }
    }
}

public struct CreateMultiRegionClustersInput: Swift.Sendable {
    /// A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully. The subsequent retries with the same client token return the result from the original successful request and they have no additional effect. If you don't specify a client token, the Amazon Web Services SDK automatically generates one.
    public var clientToken: Swift.String?
    /// A mapping of properties to use when creating linked clusters.
    public var clusterProperties: [Swift.String: DSQLClientTypes.LinkedClusterProperties]?
    /// An array of the Regions in which you want to create additional clusters.
    /// This member is required.
    public var linkedRegionList: [Swift.String]?
    /// The witness Region of multi-Region clusters.
    /// This member is required.
    public var witnessRegion: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        clusterProperties: [Swift.String: DSQLClientTypes.LinkedClusterProperties]? = nil,
        linkedRegionList: [Swift.String]? = nil,
        witnessRegion: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.clusterProperties = clusterProperties
        self.linkedRegionList = linkedRegionList
        self.witnessRegion = witnessRegion
    }
}

public struct CreateMultiRegionClustersOutput: Swift.Sendable {
    /// An array that contains the ARNs of all linked clusters.
    /// This member is required.
    public var linkedClusterArns: [Swift.String]?

    public init(
        linkedClusterArns: [Swift.String]? = nil
    )
    {
        self.linkedClusterArns = linkedClusterArns
    }
}

/// The resource could not be found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// Hypothetical identifier of the resource which does not exist
        /// This member is required.
        public internal(set) var resourceId: Swift.String? = nil
        /// Hypothetical type of the resource which does not exist
        /// This member is required.
        public internal(set) var resourceType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.resourceId = resourceId
        self.properties.resourceType = resourceType
    }
}

public struct DeleteClusterInput: Swift.Sendable {
    /// A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully. The subsequent retries with the same client token return the result from the original successful request and they have no additional effect. If you don't specify a client token, the Amazon Web Services SDK automatically generates one.
    public var clientToken: Swift.String?
    /// The ID of the cluster to delete.
    /// This member is required.
    public var identifier: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        identifier: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.identifier = identifier
    }
}

/// Output Mixin
public struct DeleteClusterOutput: Swift.Sendable {
    /// The ARN of the deleted cluster.
    /// This member is required.
    public var arn: Swift.String?
    /// The time of when the cluster was created.
    /// This member is required.
    public var creationTime: Foundation.Date?
    /// Specifies whether deletion protection was enabled on the cluster.
    /// This member is required.
    public var deletionProtectionEnabled: Swift.Bool?
    /// The ID of the deleted cluster.
    /// This member is required.
    public var identifier: Swift.String?
    /// The status of the cluster.
    /// This member is required.
    public var status: DSQLClientTypes.ClusterStatus?

    public init(
        arn: Swift.String? = nil,
        creationTime: Foundation.Date? = nil,
        deletionProtectionEnabled: Swift.Bool? = nil,
        identifier: Swift.String? = nil,
        status: DSQLClientTypes.ClusterStatus? = nil
    )
    {
        self.arn = arn
        self.creationTime = creationTime
        self.deletionProtectionEnabled = deletionProtectionEnabled
        self.identifier = identifier
        self.status = status
    }
}

public struct DeleteMultiRegionClustersInput: Swift.Sendable {
    /// A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully. The subsequent retries with the same client token return the result from the original successful request and they have no additional effect. If you don't specify a client token, the Amazon Web Services SDK automatically generates one.
    public var clientToken: Swift.String?
    /// The ARNs of the clusters linked to the cluster you want to delete. also deletes these clusters as part of the operation.
    /// This member is required.
    public var linkedClusterArns: [Swift.String]?

    public init(
        clientToken: Swift.String? = nil,
        linkedClusterArns: [Swift.String]? = nil
    )
    {
        self.clientToken = clientToken
        self.linkedClusterArns = linkedClusterArns
    }
}

public struct GetClusterInput: Swift.Sendable {
    /// The ID of the cluster to retrieve.
    /// This member is required.
    public var identifier: Swift.String?

    public init(
        identifier: Swift.String? = nil
    )
    {
        self.identifier = identifier
    }
}

/// Output Mixin
public struct GetClusterOutput: Swift.Sendable {
    /// The ARN of the retrieved cluster.
    /// This member is required.
    public var arn: Swift.String?
    /// The time of when the cluster was created.
    /// This member is required.
    public var creationTime: Foundation.Date?
    /// Whether deletion protection is enabled in this cluster.
    /// This member is required.
    public var deletionProtectionEnabled: Swift.Bool?
    /// The ID of the retrieved cluster.
    /// This member is required.
    public var identifier: Swift.String?
    /// The ARNs of the clusters linked to the retrieved cluster.
    public var linkedClusterArns: [Swift.String]?
    /// The status of the retrieved cluster.
    /// This member is required.
    public var status: DSQLClientTypes.ClusterStatus?
    /// The witness Region of the cluster. Applicable only for multi-Region clusters.
    public var witnessRegion: Swift.String?

    public init(
        arn: Swift.String? = nil,
        creationTime: Foundation.Date? = nil,
        deletionProtectionEnabled: Swift.Bool? = nil,
        identifier: Swift.String? = nil,
        linkedClusterArns: [Swift.String]? = nil,
        status: DSQLClientTypes.ClusterStatus? = nil,
        witnessRegion: Swift.String? = nil
    )
    {
        self.arn = arn
        self.creationTime = creationTime
        self.deletionProtectionEnabled = deletionProtectionEnabled
        self.identifier = identifier
        self.linkedClusterArns = linkedClusterArns
        self.status = status
        self.witnessRegion = witnessRegion
    }
}

public struct ListClustersInput: Swift.Sendable {
    /// An optional parameter that specifies the maximum number of results to return. You can use nextToken to display the next page of results.
    public var maxResults: Swift.Int?
    /// If your initial ListClusters operation returns a nextToken, you can include the returned nextToken in following ListClusters operations, which returns results in the next page.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DSQLClientTypes {

    /// A summary of the properties of a cluster.
    public struct ClusterSummary: Swift.Sendable {
        /// The ARN of the cluster.
        /// This member is required.
        public var arn: Swift.String?
        /// The ID of the cluster.
        /// This member is required.
        public var identifier: Swift.String?

        public init(
            arn: Swift.String? = nil,
            identifier: Swift.String? = nil
        )
        {
            self.arn = arn
            self.identifier = identifier
        }
    }
}

public struct ListClustersOutput: Swift.Sendable {
    /// An array of the returned clusters.
    /// This member is required.
    public var clusters: [DSQLClientTypes.ClusterSummary]?
    /// If nextToken is returned, there are more results available. The value of nextToken is a unique pagination token for each page. To retrieve the next page, make the call again using the returned token.
    public var nextToken: Swift.String?

    public init(
        clusters: [DSQLClientTypes.ClusterSummary]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.clusters = clusters
        self.nextToken = nextToken
    }
}

public struct UpdateClusterInput: Swift.Sendable {
    /// A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully. The subsequent retries with the same client token return the result from the original successful request and they have no additional effect. If you don't specify a client token, the Amazon Web Services SDK automatically generates one.
    public var clientToken: Swift.String?
    /// Specifies whether to enable deletion protection in your cluster.
    public var deletionProtectionEnabled: Swift.Bool?
    /// The ID of the cluster you want to update.
    /// This member is required.
    public var identifier: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        deletionProtectionEnabled: Swift.Bool? = nil,
        identifier: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.deletionProtectionEnabled = deletionProtectionEnabled
        self.identifier = identifier
    }
}

/// Output Mixin
public struct UpdateClusterOutput: Swift.Sendable {
    /// The ARN of the updated cluster.
    /// This member is required.
    public var arn: Swift.String?
    /// The time of when the cluster was created.
    /// This member is required.
    public var creationTime: Foundation.Date?
    /// Whether deletion protection is enabled for the updated cluster.
    /// This member is required.
    public var deletionProtectionEnabled: Swift.Bool?
    /// The ID of the cluster to update.
    /// This member is required.
    public var identifier: Swift.String?
    /// The ARNs of the clusters linked to the updated cluster. Applicable only for multi-Region clusters.
    public var linkedClusterArns: [Swift.String]?
    /// The status of the updated cluster.
    /// This member is required.
    public var status: DSQLClientTypes.ClusterStatus?
    /// The Region that receives all data you write to linked clusters.
    public var witnessRegion: Swift.String?

    public init(
        arn: Swift.String? = nil,
        creationTime: Foundation.Date? = nil,
        deletionProtectionEnabled: Swift.Bool? = nil,
        identifier: Swift.String? = nil,
        linkedClusterArns: [Swift.String]? = nil,
        status: DSQLClientTypes.ClusterStatus? = nil,
        witnessRegion: Swift.String? = nil
    )
    {
        self.arn = arn
        self.creationTime = creationTime
        self.deletionProtectionEnabled = deletionProtectionEnabled
        self.identifier = identifier
        self.linkedClusterArns = linkedClusterArns
        self.status = status
        self.witnessRegion = witnessRegion
    }
}

/// The request processing has failed because of an unknown error, exception or failure.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// Retry after seconds.
        public internal(set) var retryAfterSeconds: Swift.Int? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        retryAfterSeconds: Swift.Int? = nil
    )
    {
        self.properties.message = message
        self.properties.retryAfterSeconds = retryAfterSeconds
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The ARN of the resource for which you want to list the tags.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// A map of key and value pairs that you used to tag your resource.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The ARN of the resource that you want to tag.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// A map of key and value pairs to use to tag your resource.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

/// The request was denied due to request throttling.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Description of the error
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// Service Quotas requirement to identify originating quota
        public internal(set) var quotaCode: Swift.String? = nil
        /// Advice to clients on when the call can be safely retried
        public internal(set) var retryAfterSeconds: Swift.Int? = nil
        /// Service Quotas requirement to identify originating service
        public internal(set) var serviceCode: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { true }
    public static var isThrottling: Swift.Bool { true }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        quotaCode: Swift.String? = nil,
        retryAfterSeconds: Swift.Int? = nil,
        serviceCode: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.quotaCode = quotaCode
        self.properties.retryAfterSeconds = retryAfterSeconds
        self.properties.serviceCode = serviceCode
    }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The ARN of the resource from which to remove tags.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The array of keys of the tags that you want to remove.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

extension DSQLClientTypes {

    /// Stores information about a field passed inside a request that resulted in an validation error.
    public struct ValidationExceptionField: Swift.Sendable {
        /// A message describing why this field failed validation.
        /// This member is required.
        public var message: Swift.String?
        /// The name of the field.
        /// This member is required.
        public var name: Swift.String?

        public init(
            message: Swift.String? = nil,
            name: Swift.String? = nil
        )
        {
            self.message = message
            self.name = name
        }
    }
}

extension DSQLClientTypes {

    /// Reason the request failed validation
    public enum ValidationExceptionReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cannotParse
        case deletionProtectionEnabled
        case fieldValidationFailed
        case other
        case unknownOperation
        case sdkUnknown(Swift.String)

        public static var allCases: [ValidationExceptionReason] {
            return [
                .cannotParse,
                .deletionProtectionEnabled,
                .fieldValidationFailed,
                .other,
                .unknownOperation
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cannotParse: return "cannotParse"
            case .deletionProtectionEnabled: return "deletionProtectionEnabled"
            case .fieldValidationFailed: return "fieldValidationFailed"
            case .other: return "other"
            case .unknownOperation: return "unknownOperation"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The input failed to satisfy the constraints specified by an Amazon Web Services service.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// List of fields that caused the error
        public internal(set) var fieldList: [DSQLClientTypes.ValidationExceptionField]? = nil
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// Reason the request failed validation
        /// This member is required.
        public internal(set) var reason: DSQLClientTypes.ValidationExceptionReason? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        fieldList: [DSQLClientTypes.ValidationExceptionField]? = nil,
        message: Swift.String? = nil,
        reason: DSQLClientTypes.ValidationExceptionReason? = nil
    )
    {
        self.properties.fieldList = fieldList
        self.properties.message = message
        self.properties.reason = reason
    }
}

extension CreateClusterInput {

    static func urlPathProvider(_ value: CreateClusterInput) -> Swift.String? {
        return "/cluster"
    }
}

extension CreateMultiRegionClustersInput {

    static func urlPathProvider(_ value: CreateMultiRegionClustersInput) -> Swift.String? {
        return "/multi-region-clusters"
    }
}

extension DeleteClusterInput {

    static func urlPathProvider(_ value: DeleteClusterInput) -> Swift.String? {
        guard let identifier = value.identifier else {
            return nil
        }
        return "/cluster/\(identifier.urlPercentEncoding())"
    }
}

extension DeleteClusterInput {

    static func queryItemProvider(_ value: DeleteClusterInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let clientToken = value.clientToken {
            let clientTokenQueryItem = Smithy.URIQueryItem(name: "client-token".urlPercentEncoding(), value: Swift.String(clientToken).urlPercentEncoding())
            items.append(clientTokenQueryItem)
        }
        return items
    }
}

extension DeleteMultiRegionClustersInput {

    static func urlPathProvider(_ value: DeleteMultiRegionClustersInput) -> Swift.String? {
        return "/multi-region-clusters"
    }
}

extension DeleteMultiRegionClustersInput {

    static func queryItemProvider(_ value: DeleteMultiRegionClustersInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let linkedClusterArns = value.linkedClusterArns else {
            let message = "Creating a URL Query Item failed. linkedClusterArns is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        linkedClusterArns.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "linked-cluster-arns".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        if let clientToken = value.clientToken {
            let clientTokenQueryItem = Smithy.URIQueryItem(name: "client-token".urlPercentEncoding(), value: Swift.String(clientToken).urlPercentEncoding())
            items.append(clientTokenQueryItem)
        }
        return items
    }
}

extension GetClusterInput {

    static func urlPathProvider(_ value: GetClusterInput) -> Swift.String? {
        guard let identifier = value.identifier else {
            return nil
        }
        return "/cluster/\(identifier.urlPercentEncoding())"
    }
}

extension ListClustersInput {

    static func urlPathProvider(_ value: ListClustersInput) -> Swift.String? {
        return "/cluster"
    }
}

extension ListClustersInput {

    static func queryItemProvider(_ value: ListClustersInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "max-results".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "next-token".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        return items
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension UpdateClusterInput {

    static func urlPathProvider(_ value: UpdateClusterInput) -> Swift.String? {
        guard let identifier = value.identifier else {
            return nil
        }
        return "/cluster/\(identifier.urlPercentEncoding())"
    }
}

extension CreateClusterInput {

    static func write(value: CreateClusterInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["clientToken"].write(value.clientToken)
        try writer["deletionProtectionEnabled"].write(value.deletionProtectionEnabled)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CreateMultiRegionClustersInput {

    static func write(value: CreateMultiRegionClustersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["clientToken"].write(value.clientToken)
        try writer["clusterProperties"].writeMap(value.clusterProperties, valueWritingClosure: DSQLClientTypes.LinkedClusterProperties.write(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["linkedRegionList"].writeList(value.linkedRegionList, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["witnessRegion"].write(value.witnessRegion)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension UpdateClusterInput {

    static func write(value: UpdateClusterInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["clientToken"].write(value.clientToken)
        try writer["deletionProtectionEnabled"].write(value.deletionProtectionEnabled)
    }
}

extension CreateClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateClusterOutput()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.creationTime = try reader["creationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.deletionProtectionEnabled = try reader["deletionProtectionEnabled"].readIfPresent() ?? false
        value.identifier = try reader["identifier"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension CreateMultiRegionClustersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateMultiRegionClustersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateMultiRegionClustersOutput()
        value.linkedClusterArns = try reader["linkedClusterArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension DeleteClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteClusterOutput()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.creationTime = try reader["creationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.deletionProtectionEnabled = try reader["deletionProtectionEnabled"].readIfPresent() ?? false
        value.identifier = try reader["identifier"].readIfPresent() ?? ""
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        return value
    }
}

extension DeleteMultiRegionClustersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteMultiRegionClustersOutput {
        return DeleteMultiRegionClustersOutput()
    }
}

extension GetClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetClusterOutput()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.creationTime = try reader["creationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.deletionProtectionEnabled = try reader["deletionProtectionEnabled"].readIfPresent() ?? false
        value.identifier = try reader["identifier"].readIfPresent() ?? ""
        value.linkedClusterArns = try reader["linkedClusterArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        value.witnessRegion = try reader["witnessRegion"].readIfPresent()
        return value
    }
}

extension ListClustersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListClustersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListClustersOutput()
        value.clusters = try reader["clusters"].readListIfPresent(memberReadingClosure: DSQLClientTypes.ClusterSummary.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateClusterOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateClusterOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateClusterOutput()
        value.arn = try reader["arn"].readIfPresent() ?? ""
        value.creationTime = try reader["creationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.deletionProtectionEnabled = try reader["deletionProtectionEnabled"].readIfPresent() ?? false
        value.identifier = try reader["identifier"].readIfPresent() ?? ""
        value.linkedClusterArns = try reader["linkedClusterArns"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.status = try reader["status"].readIfPresent() ?? .sdkUnknown("")
        value.witnessRegion = try reader["witnessRegion"].readIfPresent()
        return value
    }
}

func httpServiceError(baseError: AWSClientRuntime.RestJSONError) throws -> Swift.Error? {
    switch baseError.code {
        case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
        case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
        case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
        case "ValidationException": return try ValidationException.makeError(baseError: baseError)
        default: return nil
    }
}

enum CreateClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateMultiRegionClustersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteMultiRegionClustersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListClustersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateClusterOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        if let error = try httpServiceError(baseError: baseError) { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceQuotaExceededException {
        let reader = baseError.errorBodyReader
        var value = ServiceQuotaExceededException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.quotaCode = try reader["quotaCode"].readIfPresent() ?? ""
        value.properties.resourceId = try reader["resourceId"].readIfPresent() ?? ""
        value.properties.resourceType = try reader["resourceType"].readIfPresent() ?? ""
        value.properties.serviceCode = try reader["serviceCode"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.resourceId = try reader["resourceId"].readIfPresent()
        value.properties.resourceType = try reader["resourceType"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.resourceId = try reader["resourceId"].readIfPresent() ?? ""
        value.properties.resourceType = try reader["resourceType"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = InternalServerException()
        if let retryAfterSecondsHeaderValue = httpResponse.headers.value(for: "Retry-After") {
            value.properties.retryAfterSeconds = Swift.Int(retryAfterSecondsHeaderValue) ?? 0
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ThrottlingException()
        if let retryAfterSecondsHeaderValue = httpResponse.headers.value(for: "Retry-After") {
            value.properties.retryAfterSeconds = Swift.Int(retryAfterSecondsHeaderValue) ?? 0
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.quotaCode = try reader["quotaCode"].readIfPresent()
        value.properties.serviceCode = try reader["serviceCode"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.fieldList = try reader["fieldList"].readListIfPresent(memberReadingClosure: DSQLClientTypes.ValidationExceptionField.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.reason = try reader["reason"].readIfPresent() ?? .sdkUnknown("")
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DSQLClientTypes.ClusterSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> DSQLClientTypes.ClusterSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DSQLClientTypes.ClusterSummary()
        value.identifier = try reader["identifier"].readIfPresent() ?? ""
        value.arn = try reader["arn"].readIfPresent() ?? ""
        return value
    }
}

extension DSQLClientTypes.ValidationExceptionField {

    static func read(from reader: SmithyJSON.Reader) throws -> DSQLClientTypes.ValidationExceptionField {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DSQLClientTypes.ValidationExceptionField()
        value.name = try reader["name"].readIfPresent() ?? ""
        value.message = try reader["message"].readIfPresent() ?? ""
        return value
    }
}

extension DSQLClientTypes.LinkedClusterProperties {

    static func write(value: DSQLClientTypes.LinkedClusterProperties?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["deletionProtectionEnabled"].write(value.deletionProtectionEnabled)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

public enum DSQLClientTypes {}
