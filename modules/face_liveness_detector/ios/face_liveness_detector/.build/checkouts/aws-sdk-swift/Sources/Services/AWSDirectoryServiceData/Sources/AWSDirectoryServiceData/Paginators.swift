//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension DirectoryServiceDataClient {
    /// Paginate over `[ListGroupMembersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListGroupMembersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListGroupMembersOutput`
    public func listGroupMembersPaginated(input: ListGroupMembersInput) -> ClientRuntime.PaginatorSequence<ListGroupMembersInput, ListGroupMembersOutput> {
        return ClientRuntime.PaginatorSequence<ListGroupMembersInput, ListGroupMembersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listGroupMembers(input:))
    }
}

extension ListGroupMembersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListGroupMembersInput {
        return ListGroupMembersInput(
            directoryId: self.directoryId,
            maxResults: self.maxResults,
            memberRealm: self.memberRealm,
            nextToken: token,
            realm: self.realm,
            samAccountName: self.samAccountName
        )}
}

extension PaginatorSequence where OperationStackInput == ListGroupMembersInput, OperationStackOutput == ListGroupMembersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listGroupMembersPaginated`
    /// to access the nested member `[DirectoryServiceDataClientTypes.Member]`
    /// - Returns: `[DirectoryServiceDataClientTypes.Member]`
    public func members() async throws -> [DirectoryServiceDataClientTypes.Member] {
        return try await self.asyncCompactMap { item in item.members }
    }
}
extension DirectoryServiceDataClient {
    /// Paginate over `[ListGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListGroupsOutput`
    public func listGroupsPaginated(input: ListGroupsInput) -> ClientRuntime.PaginatorSequence<ListGroupsInput, ListGroupsOutput> {
        return ClientRuntime.PaginatorSequence<ListGroupsInput, ListGroupsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listGroups(input:))
    }
}

extension ListGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListGroupsInput {
        return ListGroupsInput(
            directoryId: self.directoryId,
            maxResults: self.maxResults,
            nextToken: token,
            realm: self.realm
        )}
}

extension PaginatorSequence where OperationStackInput == ListGroupsInput, OperationStackOutput == ListGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listGroupsPaginated`
    /// to access the nested member `[DirectoryServiceDataClientTypes.GroupSummary]`
    /// - Returns: `[DirectoryServiceDataClientTypes.GroupSummary]`
    public func groups() async throws -> [DirectoryServiceDataClientTypes.GroupSummary] {
        return try await self.asyncCompactMap { item in item.groups }
    }
}
extension DirectoryServiceDataClient {
    /// Paginate over `[ListGroupsForMemberOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListGroupsForMemberInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListGroupsForMemberOutput`
    public func listGroupsForMemberPaginated(input: ListGroupsForMemberInput) -> ClientRuntime.PaginatorSequence<ListGroupsForMemberInput, ListGroupsForMemberOutput> {
        return ClientRuntime.PaginatorSequence<ListGroupsForMemberInput, ListGroupsForMemberOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listGroupsForMember(input:))
    }
}

extension ListGroupsForMemberInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListGroupsForMemberInput {
        return ListGroupsForMemberInput(
            directoryId: self.directoryId,
            maxResults: self.maxResults,
            memberRealm: self.memberRealm,
            nextToken: token,
            realm: self.realm,
            samAccountName: self.samAccountName
        )}
}

extension PaginatorSequence where OperationStackInput == ListGroupsForMemberInput, OperationStackOutput == ListGroupsForMemberOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listGroupsForMemberPaginated`
    /// to access the nested member `[DirectoryServiceDataClientTypes.GroupSummary]`
    /// - Returns: `[DirectoryServiceDataClientTypes.GroupSummary]`
    public func groups() async throws -> [DirectoryServiceDataClientTypes.GroupSummary] {
        return try await self.asyncCompactMap { item in item.groups }
    }
}
extension DirectoryServiceDataClient {
    /// Paginate over `[ListUsersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListUsersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListUsersOutput`
    public func listUsersPaginated(input: ListUsersInput) -> ClientRuntime.PaginatorSequence<ListUsersInput, ListUsersOutput> {
        return ClientRuntime.PaginatorSequence<ListUsersInput, ListUsersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listUsers(input:))
    }
}

extension ListUsersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListUsersInput {
        return ListUsersInput(
            directoryId: self.directoryId,
            maxResults: self.maxResults,
            nextToken: token,
            realm: self.realm
        )}
}

extension PaginatorSequence where OperationStackInput == ListUsersInput, OperationStackOutput == ListUsersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listUsersPaginated`
    /// to access the nested member `[DirectoryServiceDataClientTypes.UserSummary]`
    /// - Returns: `[DirectoryServiceDataClientTypes.UserSummary]`
    public func users() async throws -> [DirectoryServiceDataClientTypes.UserSummary] {
        return try await self.asyncCompactMap { item in item.users }
    }
}
extension DirectoryServiceDataClient {
    /// Paginate over `[SearchGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchGroupsOutput`
    public func searchGroupsPaginated(input: SearchGroupsInput) -> ClientRuntime.PaginatorSequence<SearchGroupsInput, SearchGroupsOutput> {
        return ClientRuntime.PaginatorSequence<SearchGroupsInput, SearchGroupsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchGroups(input:))
    }
}

extension SearchGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchGroupsInput {
        return SearchGroupsInput(
            directoryId: self.directoryId,
            maxResults: self.maxResults,
            nextToken: token,
            realm: self.realm,
            searchAttributes: self.searchAttributes,
            searchString: self.searchString
        )}
}

extension PaginatorSequence where OperationStackInput == SearchGroupsInput, OperationStackOutput == SearchGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchGroupsPaginated`
    /// to access the nested member `[DirectoryServiceDataClientTypes.Group]`
    /// - Returns: `[DirectoryServiceDataClientTypes.Group]`
    public func groups() async throws -> [DirectoryServiceDataClientTypes.Group] {
        return try await self.asyncCompactMap { item in item.groups }
    }
}
extension DirectoryServiceDataClient {
    /// Paginate over `[SearchUsersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[SearchUsersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `SearchUsersOutput`
    public func searchUsersPaginated(input: SearchUsersInput) -> ClientRuntime.PaginatorSequence<SearchUsersInput, SearchUsersOutput> {
        return ClientRuntime.PaginatorSequence<SearchUsersInput, SearchUsersOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.searchUsers(input:))
    }
}

extension SearchUsersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> SearchUsersInput {
        return SearchUsersInput(
            directoryId: self.directoryId,
            maxResults: self.maxResults,
            nextToken: token,
            realm: self.realm,
            searchAttributes: self.searchAttributes,
            searchString: self.searchString
        )}
}

extension PaginatorSequence where OperationStackInput == SearchUsersInput, OperationStackOutput == SearchUsersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `searchUsersPaginated`
    /// to access the nested member `[DirectoryServiceDataClientTypes.User]`
    /// - Returns: `[DirectoryServiceDataClientTypes.User]`
    public func users() async throws -> [DirectoryServiceDataClientTypes.User] {
        return try await self.asyncCompactMap { item in item.users }
    }
}
