//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHT<PERSON>API.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.AWSJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
@_spi(SmithyTimestamps) import struct SmithyTimestamps.TimestampFormatter

/// You do not have sufficient access to perform this action.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CodeStarconnectionsClientTypes {

    public enum BlockerStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case active
        case resolved
        case sdkUnknown(Swift.String)

        public static var allCases: [BlockerStatus] {
            return [
                .active,
                .resolved
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .active: return "ACTIVE"
            case .resolved: return "RESOLVED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeStarconnectionsClientTypes {

    public enum BlockerType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case automated
        case sdkUnknown(Swift.String)

        public static var allCases: [BlockerType] {
            return [
                .automated
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .automated: return "AUTOMATED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Exceeded the maximum limit for connections.
public struct LimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Resource not found. Verify the connection resource ARN and try again.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Resource not found. Verify the ARN for the host resource and try again.
public struct ResourceUnavailableException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceUnavailableException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CodeStarconnectionsClientTypes {

    public enum ProviderType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case bitbucket
        case github
        case githubEnterpriseServer
        case gitlab
        case gitlabSelfManaged
        case sdkUnknown(Swift.String)

        public static var allCases: [ProviderType] {
            return [
                .bitbucket,
                .github,
                .githubEnterpriseServer,
                .gitlab,
                .gitlabSelfManaged
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .bitbucket: return "Bitbucket"
            case .github: return "GitHub"
            case .githubEnterpriseServer: return "GitHubEnterpriseServer"
            case .gitlab: return "GitLab"
            case .gitlabSelfManaged: return "GitLabSelfManaged"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeStarconnectionsClientTypes {

    /// A tag is a key-value pair that is used to manage the resource. This tag is available for use by Amazon Web Services services that support tags.
    public struct Tag: Swift.Sendable {
        /// The tag's key.
        /// This member is required.
        public var key: Swift.String?
        /// The tag's value.
        /// This member is required.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

public struct CreateConnectionInput: Swift.Sendable {
    /// The name of the connection to be created.
    /// This member is required.
    public var connectionName: Swift.String?
    /// The Amazon Resource Name (ARN) of the host associated with the connection to be created.
    public var hostArn: Swift.String?
    /// The name of the external provider where your third-party code repository is configured.
    public var providerType: CodeStarconnectionsClientTypes.ProviderType?
    /// The key-value pair to use when tagging the resource.
    public var tags: [CodeStarconnectionsClientTypes.Tag]?

    public init(
        connectionName: Swift.String? = nil,
        hostArn: Swift.String? = nil,
        providerType: CodeStarconnectionsClientTypes.ProviderType? = nil,
        tags: [CodeStarconnectionsClientTypes.Tag]? = nil
    )
    {
        self.connectionName = connectionName
        self.hostArn = hostArn
        self.providerType = providerType
        self.tags = tags
    }
}

public struct CreateConnectionOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the connection to be created. The ARN is used as the connection reference when the connection is shared between Amazon Web Services services. The ARN is never reused if the connection is deleted.
    /// This member is required.
    public var connectionArn: Swift.String?
    /// Specifies the tags applied to the resource.
    public var tags: [CodeStarconnectionsClientTypes.Tag]?

    public init(
        connectionArn: Swift.String? = nil,
        tags: [CodeStarconnectionsClientTypes.Tag]? = nil
    )
    {
        self.connectionArn = connectionArn
        self.tags = tags
    }
}

extension CodeStarconnectionsClientTypes {

    /// The VPC configuration provisioned for the host.
    public struct VpcConfiguration: Swift.Sendable {
        /// The ID of the security group or security groups associated with the Amazon VPC connected to the infrastructure where your provider type is installed.
        /// This member is required.
        public var securityGroupIds: [Swift.String]?
        /// The ID of the subnet or subnets associated with the Amazon VPC connected to the infrastructure where your provider type is installed.
        /// This member is required.
        public var subnetIds: [Swift.String]?
        /// The value of the Transport Layer Security (TLS) certificate associated with the infrastructure where your provider type is installed.
        public var tlsCertificate: Swift.String?
        /// The ID of the Amazon VPC connected to the infrastructure where your provider type is installed.
        /// This member is required.
        public var vpcId: Swift.String?

        public init(
            securityGroupIds: [Swift.String]? = nil,
            subnetIds: [Swift.String]? = nil,
            tlsCertificate: Swift.String? = nil,
            vpcId: Swift.String? = nil
        )
        {
            self.securityGroupIds = securityGroupIds
            self.subnetIds = subnetIds
            self.tlsCertificate = tlsCertificate
            self.vpcId = vpcId
        }
    }
}

public struct CreateHostInput: Swift.Sendable {
    /// The name of the host to be created.
    /// This member is required.
    public var name: Swift.String?
    /// The endpoint of the infrastructure to be represented by the host after it is created.
    /// This member is required.
    public var providerEndpoint: Swift.String?
    /// The name of the installed provider to be associated with your connection. The host resource represents the infrastructure where your provider type is installed. The valid provider type is GitHub Enterprise Server.
    /// This member is required.
    public var providerType: CodeStarconnectionsClientTypes.ProviderType?
    /// Tags for the host to be created.
    public var tags: [CodeStarconnectionsClientTypes.Tag]?
    /// The VPC configuration to be provisioned for the host. A VPC must be configured and the infrastructure to be represented by the host must already be connected to the VPC.
    public var vpcConfiguration: CodeStarconnectionsClientTypes.VpcConfiguration?

    public init(
        name: Swift.String? = nil,
        providerEndpoint: Swift.String? = nil,
        providerType: CodeStarconnectionsClientTypes.ProviderType? = nil,
        tags: [CodeStarconnectionsClientTypes.Tag]? = nil,
        vpcConfiguration: CodeStarconnectionsClientTypes.VpcConfiguration? = nil
    )
    {
        self.name = name
        self.providerEndpoint = providerEndpoint
        self.providerType = providerType
        self.tags = tags
        self.vpcConfiguration = vpcConfiguration
    }
}

public struct CreateHostOutput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the host to be created.
    public var hostArn: Swift.String?
    /// Tags for the created host.
    public var tags: [CodeStarconnectionsClientTypes.Tag]?

    public init(
        hostArn: Swift.String? = nil,
        tags: [CodeStarconnectionsClientTypes.Tag]? = nil
    )
    {
        self.hostArn = hostArn
        self.tags = tags
    }
}

/// Exception thrown as a result of concurrent modification to an application. For example, two individuals attempting to edit the same application at the same time.
public struct ConcurrentModificationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConcurrentModificationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Received an internal server exception. Try again later.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The input is not valid. Verify that the action is typed correctly.
public struct InvalidInputException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidInputException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Unable to create resource. Resource already exists.
public struct ResourceAlreadyExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceAlreadyExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request was denied due to request throttling.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct CreateRepositoryLinkInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the connection to be associated with the repository link.
    /// This member is required.
    public var connectionArn: Swift.String?
    /// The Amazon Resource Name (ARN) encryption key for the repository to be associated with the repository link.
    public var encryptionKeyArn: Swift.String?
    /// The owner ID for the repository associated with a specific sync configuration, such as the owner ID in GitHub.
    /// This member is required.
    public var ownerId: Swift.String?
    /// The name of the repository to be associated with the repository link.
    /// This member is required.
    public var repositoryName: Swift.String?
    /// The tags for the repository to be associated with the repository link.
    public var tags: [CodeStarconnectionsClientTypes.Tag]?

    public init(
        connectionArn: Swift.String? = nil,
        encryptionKeyArn: Swift.String? = nil,
        ownerId: Swift.String? = nil,
        repositoryName: Swift.String? = nil,
        tags: [CodeStarconnectionsClientTypes.Tag]? = nil
    )
    {
        self.connectionArn = connectionArn
        self.encryptionKeyArn = encryptionKeyArn
        self.ownerId = ownerId
        self.repositoryName = repositoryName
        self.tags = tags
    }
}

extension CodeStarconnectionsClientTypes {

    /// Information about the repository link resource, such as the repository link ARN, the associated connection ARN, encryption key ARN, and owner ID.
    public struct RepositoryLinkInfo: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the connection associated with the repository link.
        /// This member is required.
        public var connectionArn: Swift.String?
        /// The Amazon Resource Name (ARN) of the encryption key for the repository associated with the repository link.
        public var encryptionKeyArn: Swift.String?
        /// The owner ID for the repository associated with the repository link, such as the owner ID in GitHub.
        /// This member is required.
        public var ownerId: Swift.String?
        /// The provider type for the connection, such as GitHub, associated with the repository link.
        /// This member is required.
        public var providerType: CodeStarconnectionsClientTypes.ProviderType?
        /// The Amazon Resource Name (ARN) of the repository link.
        /// This member is required.
        public var repositoryLinkArn: Swift.String?
        /// The ID of the repository link.
        /// This member is required.
        public var repositoryLinkId: Swift.String?
        /// The name of the repository associated with the repository link.
        /// This member is required.
        public var repositoryName: Swift.String?

        public init(
            connectionArn: Swift.String? = nil,
            encryptionKeyArn: Swift.String? = nil,
            ownerId: Swift.String? = nil,
            providerType: CodeStarconnectionsClientTypes.ProviderType? = nil,
            repositoryLinkArn: Swift.String? = nil,
            repositoryLinkId: Swift.String? = nil,
            repositoryName: Swift.String? = nil
        )
        {
            self.connectionArn = connectionArn
            self.encryptionKeyArn = encryptionKeyArn
            self.ownerId = ownerId
            self.providerType = providerType
            self.repositoryLinkArn = repositoryLinkArn
            self.repositoryLinkId = repositoryLinkId
            self.repositoryName = repositoryName
        }
    }
}

public struct CreateRepositoryLinkOutput: Swift.Sendable {
    /// The returned information about the created repository link.
    /// This member is required.
    public var repositoryLinkInfo: CodeStarconnectionsClientTypes.RepositoryLinkInfo?

    public init(
        repositoryLinkInfo: CodeStarconnectionsClientTypes.RepositoryLinkInfo? = nil
    )
    {
        self.repositoryLinkInfo = repositoryLinkInfo
    }
}

extension CodeStarconnectionsClientTypes {

    public enum PublishDeploymentStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case enabled
        case sdkUnknown(Swift.String)

        public static var allCases: [PublishDeploymentStatus] {
            return [
                .disabled,
                .enabled
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "DISABLED"
            case .enabled: return "ENABLED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeStarconnectionsClientTypes {

    public enum SyncConfigurationType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cfnStackSync
        case sdkUnknown(Swift.String)

        public static var allCases: [SyncConfigurationType] {
            return [
                .cfnStackSync
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cfnStackSync: return "CFN_STACK_SYNC"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeStarconnectionsClientTypes {

    public enum TriggerResourceUpdateOn: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case anyChange
        case fileChange
        case sdkUnknown(Swift.String)

        public static var allCases: [TriggerResourceUpdateOn] {
            return [
                .anyChange,
                .fileChange
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .anyChange: return "ANY_CHANGE"
            case .fileChange: return "FILE_CHANGE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct CreateSyncConfigurationInput: Swift.Sendable {
    /// The branch in the repository from which changes will be synced.
    /// This member is required.
    public var branch: Swift.String?
    /// The file name of the configuration file that manages syncing between the connection and the repository. This configuration file is stored in the repository.
    /// This member is required.
    public var configFile: Swift.String?
    /// Whether to enable or disable publishing of deployment status to source providers.
    public var publishDeploymentStatus: CodeStarconnectionsClientTypes.PublishDeploymentStatus?
    /// The ID of the repository link created for the connection. A repository link allows Git sync to monitor and sync changes to files in a specified Git repository.
    /// This member is required.
    public var repositoryLinkId: Swift.String?
    /// The name of the Amazon Web Services resource (for example, a CloudFormation stack in the case of CFN_STACK_SYNC) that will be synchronized from the linked repository.
    /// This member is required.
    public var resourceName: Swift.String?
    /// The ARN of the IAM role that grants permission for Amazon Web Services to use Git sync to update a given Amazon Web Services resource on your behalf.
    /// This member is required.
    public var roleArn: Swift.String?
    /// The type of sync configuration.
    /// This member is required.
    public var syncType: CodeStarconnectionsClientTypes.SyncConfigurationType?
    /// When to trigger Git sync to begin the stack update.
    public var triggerResourceUpdateOn: CodeStarconnectionsClientTypes.TriggerResourceUpdateOn?

    public init(
        branch: Swift.String? = nil,
        configFile: Swift.String? = nil,
        publishDeploymentStatus: CodeStarconnectionsClientTypes.PublishDeploymentStatus? = nil,
        repositoryLinkId: Swift.String? = nil,
        resourceName: Swift.String? = nil,
        roleArn: Swift.String? = nil,
        syncType: CodeStarconnectionsClientTypes.SyncConfigurationType? = nil,
        triggerResourceUpdateOn: CodeStarconnectionsClientTypes.TriggerResourceUpdateOn? = nil
    )
    {
        self.branch = branch
        self.configFile = configFile
        self.publishDeploymentStatus = publishDeploymentStatus
        self.repositoryLinkId = repositoryLinkId
        self.resourceName = resourceName
        self.roleArn = roleArn
        self.syncType = syncType
        self.triggerResourceUpdateOn = triggerResourceUpdateOn
    }
}

extension CodeStarconnectionsClientTypes {

    /// Information, such as repository, branch, provider, and resource names for a specific sync configuration.
    public struct SyncConfiguration: Swift.Sendable {
        /// The branch associated with a specific sync configuration.
        /// This member is required.
        public var branch: Swift.String?
        /// The file path to the configuration file associated with a specific sync configuration. The path should point to an actual file in the sync configurations linked repository.
        public var configFile: Swift.String?
        /// The owner ID for the repository associated with a specific sync configuration, such as the owner ID in GitHub.
        /// This member is required.
        public var ownerId: Swift.String?
        /// The connection provider type associated with a specific sync configuration, such as GitHub.
        /// This member is required.
        public var providerType: CodeStarconnectionsClientTypes.ProviderType?
        /// Whether to enable or disable publishing of deployment status to source providers.
        public var publishDeploymentStatus: CodeStarconnectionsClientTypes.PublishDeploymentStatus?
        /// The ID of the repository link associated with a specific sync configuration.
        /// This member is required.
        public var repositoryLinkId: Swift.String?
        /// The name of the repository associated with a specific sync configuration.
        /// This member is required.
        public var repositoryName: Swift.String?
        /// The name of the connection resource associated with a specific sync configuration.
        /// This member is required.
        public var resourceName: Swift.String?
        /// The Amazon Resource Name (ARN) of the IAM role associated with a specific sync configuration.
        /// This member is required.
        public var roleArn: Swift.String?
        /// The type of sync for a specific sync configuration.
        /// This member is required.
        public var syncType: CodeStarconnectionsClientTypes.SyncConfigurationType?
        /// When to trigger Git sync to begin the stack update.
        public var triggerResourceUpdateOn: CodeStarconnectionsClientTypes.TriggerResourceUpdateOn?

        public init(
            branch: Swift.String? = nil,
            configFile: Swift.String? = nil,
            ownerId: Swift.String? = nil,
            providerType: CodeStarconnectionsClientTypes.ProviderType? = nil,
            publishDeploymentStatus: CodeStarconnectionsClientTypes.PublishDeploymentStatus? = nil,
            repositoryLinkId: Swift.String? = nil,
            repositoryName: Swift.String? = nil,
            resourceName: Swift.String? = nil,
            roleArn: Swift.String? = nil,
            syncType: CodeStarconnectionsClientTypes.SyncConfigurationType? = nil,
            triggerResourceUpdateOn: CodeStarconnectionsClientTypes.TriggerResourceUpdateOn? = nil
        )
        {
            self.branch = branch
            self.configFile = configFile
            self.ownerId = ownerId
            self.providerType = providerType
            self.publishDeploymentStatus = publishDeploymentStatus
            self.repositoryLinkId = repositoryLinkId
            self.repositoryName = repositoryName
            self.resourceName = resourceName
            self.roleArn = roleArn
            self.syncType = syncType
            self.triggerResourceUpdateOn = triggerResourceUpdateOn
        }
    }
}

public struct CreateSyncConfigurationOutput: Swift.Sendable {
    /// The created sync configuration for the connection. A sync configuration allows Amazon Web Services to sync content from a Git repository to update a specified Amazon Web Services resource.
    /// This member is required.
    public var syncConfiguration: CodeStarconnectionsClientTypes.SyncConfiguration?

    public init(
        syncConfiguration: CodeStarconnectionsClientTypes.SyncConfiguration? = nil
    )
    {
        self.syncConfiguration = syncConfiguration
    }
}

public struct DeleteConnectionInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the connection to be deleted. The ARN is never reused if the connection is deleted.
    /// This member is required.
    public var connectionArn: Swift.String?

    public init(
        connectionArn: Swift.String? = nil
    )
    {
        self.connectionArn = connectionArn
    }
}

public struct DeleteConnectionOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteHostInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the host to be deleted.
    /// This member is required.
    public var hostArn: Swift.String?

    public init(
        hostArn: Swift.String? = nil
    )
    {
        self.hostArn = hostArn
    }
}

public struct DeleteHostOutput: Swift.Sendable {

    public init() { }
}

/// Unable to continue. The sync blocker still exists.
public struct SyncConfigurationStillExistsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SyncConfigurationStillExistsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The specified provider type is not supported for connections.
public struct UnsupportedProviderTypeException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UnsupportedProviderTypeException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DeleteRepositoryLinkInput: Swift.Sendable {
    /// The ID of the repository link to be deleted.
    /// This member is required.
    public var repositoryLinkId: Swift.String?

    public init(
        repositoryLinkId: Swift.String? = nil
    )
    {
        self.repositoryLinkId = repositoryLinkId
    }
}

public struct DeleteRepositoryLinkOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteSyncConfigurationInput: Swift.Sendable {
    /// The name of the Amazon Web Services resource associated with the sync configuration to be deleted.
    /// This member is required.
    public var resourceName: Swift.String?
    /// The type of sync configuration to be deleted.
    /// This member is required.
    public var syncType: CodeStarconnectionsClientTypes.SyncConfigurationType?

    public init(
        resourceName: Swift.String? = nil,
        syncType: CodeStarconnectionsClientTypes.SyncConfigurationType? = nil
    )
    {
        self.resourceName = resourceName
        self.syncType = syncType
    }
}

public struct DeleteSyncConfigurationOutput: Swift.Sendable {

    public init() { }
}

public struct GetConnectionInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of a connection.
    /// This member is required.
    public var connectionArn: Swift.String?

    public init(
        connectionArn: Swift.String? = nil
    )
    {
        self.connectionArn = connectionArn
    }
}

extension CodeStarconnectionsClientTypes {

    public enum ConnectionStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case available
        case error
        case pending
        case sdkUnknown(Swift.String)

        public static var allCases: [ConnectionStatus] {
            return [
                .available,
                .error,
                .pending
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .available: return "AVAILABLE"
            case .error: return "ERROR"
            case .pending: return "PENDING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeStarconnectionsClientTypes {

    /// A resource that is used to connect third-party source providers with services like CodePipeline. Note: A connection created through CloudFormation, the CLI, or the SDK is in `PENDING` status by default. You can make its status `AVAILABLE` by updating the connection in the console.
    public struct Connection: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the connection. The ARN is used as the connection reference when the connection is shared between Amazon Web Services. The ARN is never reused if the connection is deleted.
        public var connectionArn: Swift.String?
        /// The name of the connection. Connection names must be unique in an Amazon Web Services account.
        public var connectionName: Swift.String?
        /// The current status of the connection.
        public var connectionStatus: CodeStarconnectionsClientTypes.ConnectionStatus?
        /// The Amazon Resource Name (ARN) of the host associated with the connection.
        public var hostArn: Swift.String?
        /// The identifier of the external provider where your third-party code repository is configured. For Bitbucket, this is the account ID of the owner of the Bitbucket repository.
        public var ownerAccountId: Swift.String?
        /// The name of the external provider where your third-party code repository is configured.
        public var providerType: CodeStarconnectionsClientTypes.ProviderType?

        public init(
            connectionArn: Swift.String? = nil,
            connectionName: Swift.String? = nil,
            connectionStatus: CodeStarconnectionsClientTypes.ConnectionStatus? = nil,
            hostArn: Swift.String? = nil,
            ownerAccountId: Swift.String? = nil,
            providerType: CodeStarconnectionsClientTypes.ProviderType? = nil
        )
        {
            self.connectionArn = connectionArn
            self.connectionName = connectionName
            self.connectionStatus = connectionStatus
            self.hostArn = hostArn
            self.ownerAccountId = ownerAccountId
            self.providerType = providerType
        }
    }
}

public struct GetConnectionOutput: Swift.Sendable {
    /// The connection details, such as status, owner, and provider type.
    public var connection: CodeStarconnectionsClientTypes.Connection?

    public init(
        connection: CodeStarconnectionsClientTypes.Connection? = nil
    )
    {
        self.connection = connection
    }
}

public struct GetHostInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the requested host.
    /// This member is required.
    public var hostArn: Swift.String?

    public init(
        hostArn: Swift.String? = nil
    )
    {
        self.hostArn = hostArn
    }
}

public struct GetHostOutput: Swift.Sendable {
    /// The name of the requested host.
    public var name: Swift.String?
    /// The endpoint of the infrastructure represented by the requested host.
    public var providerEndpoint: Swift.String?
    /// The provider type of the requested host, such as GitHub Enterprise Server.
    public var providerType: CodeStarconnectionsClientTypes.ProviderType?
    /// The status of the requested host.
    public var status: Swift.String?
    /// The VPC configuration of the requested host.
    public var vpcConfiguration: CodeStarconnectionsClientTypes.VpcConfiguration?

    public init(
        name: Swift.String? = nil,
        providerEndpoint: Swift.String? = nil,
        providerType: CodeStarconnectionsClientTypes.ProviderType? = nil,
        status: Swift.String? = nil,
        vpcConfiguration: CodeStarconnectionsClientTypes.VpcConfiguration? = nil
    )
    {
        self.name = name
        self.providerEndpoint = providerEndpoint
        self.providerType = providerType
        self.status = status
        self.vpcConfiguration = vpcConfiguration
    }
}

public struct GetRepositoryLinkInput: Swift.Sendable {
    /// The ID of the repository link to get.
    /// This member is required.
    public var repositoryLinkId: Swift.String?

    public init(
        repositoryLinkId: Swift.String? = nil
    )
    {
        self.repositoryLinkId = repositoryLinkId
    }
}

public struct GetRepositoryLinkOutput: Swift.Sendable {
    /// The information returned for a specified repository link.
    /// This member is required.
    public var repositoryLinkInfo: CodeStarconnectionsClientTypes.RepositoryLinkInfo?

    public init(
        repositoryLinkInfo: CodeStarconnectionsClientTypes.RepositoryLinkInfo? = nil
    )
    {
        self.repositoryLinkInfo = repositoryLinkInfo
    }
}

public struct GetRepositorySyncStatusInput: Swift.Sendable {
    /// The branch of the repository link for the requested repository sync status.
    /// This member is required.
    public var branch: Swift.String?
    /// The repository link ID for the requested repository sync status.
    /// This member is required.
    public var repositoryLinkId: Swift.String?
    /// The sync type of the requested sync status.
    /// This member is required.
    public var syncType: CodeStarconnectionsClientTypes.SyncConfigurationType?

    public init(
        branch: Swift.String? = nil,
        repositoryLinkId: Swift.String? = nil,
        syncType: CodeStarconnectionsClientTypes.SyncConfigurationType? = nil
    )
    {
        self.branch = branch
        self.repositoryLinkId = repositoryLinkId
        self.syncType = syncType
    }
}

extension CodeStarconnectionsClientTypes {

    /// Information about a repository sync event.
    public struct RepositorySyncEvent: Swift.Sendable {
        /// A description of a repository sync event.
        /// This member is required.
        public var event: Swift.String?
        /// The ID for a repository sync event.
        public var externalId: Swift.String?
        /// The time that a repository sync event occurred.
        /// This member is required.
        public var time: Foundation.Date?
        /// The event type for a repository sync event.
        /// This member is required.
        public var type: Swift.String?

        public init(
            event: Swift.String? = nil,
            externalId: Swift.String? = nil,
            time: Foundation.Date? = nil,
            type: Swift.String? = nil
        )
        {
            self.event = event
            self.externalId = externalId
            self.time = time
            self.type = type
        }
    }
}

extension CodeStarconnectionsClientTypes {

    public enum RepositorySyncStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case initiated
        case inProgress
        case queued
        case succeeded
        case sdkUnknown(Swift.String)

        public static var allCases: [RepositorySyncStatus] {
            return [
                .failed,
                .initiated,
                .inProgress,
                .queued,
                .succeeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .initiated: return "INITIATED"
            case .inProgress: return "IN_PROGRESS"
            case .queued: return "QUEUED"
            case .succeeded: return "SUCCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeStarconnectionsClientTypes {

    /// Information about a repository sync attempt for a repository with a sync configuration.
    public struct RepositorySyncAttempt: Swift.Sendable {
        /// The events associated with a specific sync attempt.
        /// This member is required.
        public var events: [CodeStarconnectionsClientTypes.RepositorySyncEvent]?
        /// The start time of a specific sync attempt.
        /// This member is required.
        public var startedAt: Foundation.Date?
        /// The status of a specific sync attempt. The following are valid statuses:
        ///
        /// * INITIATED - A repository sync attempt has been created and will begin soon.
        ///
        /// * IN_PROGRESS - A repository sync attempt has started and work is being done to reconcile the branch.
        ///
        /// * SUCCEEDED - The repository sync attempt has completed successfully.
        ///
        /// * FAILED - The repository sync attempt has failed.
        ///
        /// * QUEUED - The repository sync attempt didn't execute and was queued.
        /// This member is required.
        public var status: CodeStarconnectionsClientTypes.RepositorySyncStatus?

        public init(
            events: [CodeStarconnectionsClientTypes.RepositorySyncEvent]? = nil,
            startedAt: Foundation.Date? = nil,
            status: CodeStarconnectionsClientTypes.RepositorySyncStatus? = nil
        )
        {
            self.events = events
            self.startedAt = startedAt
            self.status = status
        }
    }
}

public struct GetRepositorySyncStatusOutput: Swift.Sendable {
    /// The status of the latest sync returned for a specified repository and branch.
    /// This member is required.
    public var latestSync: CodeStarconnectionsClientTypes.RepositorySyncAttempt?

    public init(
        latestSync: CodeStarconnectionsClientTypes.RepositorySyncAttempt? = nil
    )
    {
        self.latestSync = latestSync
    }
}

public struct GetResourceSyncStatusInput: Swift.Sendable {
    /// The name of the Amazon Web Services resource for the sync status with the Git repository.
    /// This member is required.
    public var resourceName: Swift.String?
    /// The sync type for the sync status with the Git repository.
    /// This member is required.
    public var syncType: CodeStarconnectionsClientTypes.SyncConfigurationType?

    public init(
        resourceName: Swift.String? = nil,
        syncType: CodeStarconnectionsClientTypes.SyncConfigurationType? = nil
    )
    {
        self.resourceName = resourceName
        self.syncType = syncType
    }
}

extension CodeStarconnectionsClientTypes {

    /// Information about the revision for a specific sync event, such as the branch, owner ID, and name of the repository.
    public struct Revision: Swift.Sendable {
        /// The branch name for a specific revision.
        /// This member is required.
        public var branch: Swift.String?
        /// The directory, if any, for a specific revision.
        /// This member is required.
        public var directory: Swift.String?
        /// The owner ID for a specific revision, such as the GitHub owner ID for a GitHub repository.
        /// This member is required.
        public var ownerId: Swift.String?
        /// The provider type for a revision, such as GitHub.
        /// This member is required.
        public var providerType: CodeStarconnectionsClientTypes.ProviderType?
        /// The repository name for a specific revision.
        /// This member is required.
        public var repositoryName: Swift.String?
        /// The SHA, such as the commit ID, for a specific revision.
        /// This member is required.
        public var sha: Swift.String?

        public init(
            branch: Swift.String? = nil,
            directory: Swift.String? = nil,
            ownerId: Swift.String? = nil,
            providerType: CodeStarconnectionsClientTypes.ProviderType? = nil,
            repositoryName: Swift.String? = nil,
            sha: Swift.String? = nil
        )
        {
            self.branch = branch
            self.directory = directory
            self.ownerId = ownerId
            self.providerType = providerType
            self.repositoryName = repositoryName
            self.sha = sha
        }
    }
}

extension CodeStarconnectionsClientTypes {

    /// Information about a resource sync event for the resource associated with a sync configuration.
    public struct ResourceSyncEvent: Swift.Sendable {
        /// The event for a resource sync event.
        /// This member is required.
        public var event: Swift.String?
        /// The ID for a resource sync event.
        public var externalId: Swift.String?
        /// The time that a resource sync event occurred.
        /// This member is required.
        public var time: Foundation.Date?
        /// The type of resource sync event.
        /// This member is required.
        public var type: Swift.String?

        public init(
            event: Swift.String? = nil,
            externalId: Swift.String? = nil,
            time: Foundation.Date? = nil,
            type: Swift.String? = nil
        )
        {
            self.event = event
            self.externalId = externalId
            self.time = time
            self.type = type
        }
    }
}

extension CodeStarconnectionsClientTypes {

    public enum ResourceSyncStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case initiated
        case inProgress
        case succeeded
        case sdkUnknown(Swift.String)

        public static var allCases: [ResourceSyncStatus] {
            return [
                .failed,
                .initiated,
                .inProgress,
                .succeeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .initiated: return "INITIATED"
            case .inProgress: return "IN_PROGRESS"
            case .succeeded: return "SUCCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CodeStarconnectionsClientTypes {

    /// Information about a resource sync attempt.
    public struct ResourceSyncAttempt: Swift.Sendable {
        /// The events related to a resource sync attempt.
        /// This member is required.
        public var events: [CodeStarconnectionsClientTypes.ResourceSyncEvent]?
        /// The current state of the resource as defined in the resource's config-file in the linked repository.
        /// This member is required.
        public var initialRevision: CodeStarconnectionsClientTypes.Revision?
        /// The start time for a resource sync attempt.
        /// This member is required.
        public var startedAt: Foundation.Date?
        /// The status for a resource sync attempt. The follow are valid statuses:
        ///
        /// * SYNC-INITIATED - A resource sync attempt has been created and will begin soon.
        ///
        /// * SYNCING - Syncing has started and work is being done to reconcile state.
        ///
        /// * SYNCED - Syncing has completed successfully.
        ///
        /// * SYNC_FAILED - A resource sync attempt has failed.
        /// This member is required.
        public var status: CodeStarconnectionsClientTypes.ResourceSyncStatus?
        /// The name of the Amazon Web Services resource that is attempted to be synchronized.
        /// This member is required.
        public var target: Swift.String?
        /// The desired state of the resource as defined in the resource's config-file in the linked repository. Git sync attempts to update the resource to this state.
        /// This member is required.
        public var targetRevision: CodeStarconnectionsClientTypes.Revision?

        public init(
            events: [CodeStarconnectionsClientTypes.ResourceSyncEvent]? = nil,
            initialRevision: CodeStarconnectionsClientTypes.Revision? = nil,
            startedAt: Foundation.Date? = nil,
            status: CodeStarconnectionsClientTypes.ResourceSyncStatus? = nil,
            target: Swift.String? = nil,
            targetRevision: CodeStarconnectionsClientTypes.Revision? = nil
        )
        {
            self.events = events
            self.initialRevision = initialRevision
            self.startedAt = startedAt
            self.status = status
            self.target = target
            self.targetRevision = targetRevision
        }
    }
}

public struct GetResourceSyncStatusOutput: Swift.Sendable {
    /// The desired state of the Amazon Web Services resource for the sync status with the Git repository.
    public var desiredState: CodeStarconnectionsClientTypes.Revision?
    /// The latest successful sync for the sync status with the Git repository.
    public var latestSuccessfulSync: CodeStarconnectionsClientTypes.ResourceSyncAttempt?
    /// The latest sync for the sync status with the Git repository, whether successful or not.
    /// This member is required.
    public var latestSync: CodeStarconnectionsClientTypes.ResourceSyncAttempt?

    public init(
        desiredState: CodeStarconnectionsClientTypes.Revision? = nil,
        latestSuccessfulSync: CodeStarconnectionsClientTypes.ResourceSyncAttempt? = nil,
        latestSync: CodeStarconnectionsClientTypes.ResourceSyncAttempt? = nil
    )
    {
        self.desiredState = desiredState
        self.latestSuccessfulSync = latestSuccessfulSync
        self.latestSync = latestSync
    }
}

public struct GetSyncBlockerSummaryInput: Swift.Sendable {
    /// The name of the Amazon Web Services resource currently blocked from automatically being synced from a Git repository.
    /// This member is required.
    public var resourceName: Swift.String?
    /// The sync type for the sync blocker summary.
    /// This member is required.
    public var syncType: CodeStarconnectionsClientTypes.SyncConfigurationType?

    public init(
        resourceName: Swift.String? = nil,
        syncType: CodeStarconnectionsClientTypes.SyncConfigurationType? = nil
    )
    {
        self.resourceName = resourceName
        self.syncType = syncType
    }
}

extension CodeStarconnectionsClientTypes {

    /// The context for a specific sync blocker.
    public struct SyncBlockerContext: Swift.Sendable {
        /// The key provided for a context key-value pair for a specific sync blocker.
        /// This member is required.
        public var key: Swift.String?
        /// The value provided for a context key-value pair for a specific sync blocker.
        /// This member is required.
        public var value: Swift.String?

        public init(
            key: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.key = key
            self.value = value
        }
    }
}

extension CodeStarconnectionsClientTypes {

    /// Information about a blocker for a sync event.
    public struct SyncBlocker: Swift.Sendable {
        /// The contexts for a specific sync blocker.
        public var contexts: [CodeStarconnectionsClientTypes.SyncBlockerContext]?
        /// The creation time for a specific sync blocker.
        /// This member is required.
        public var createdAt: Foundation.Date?
        /// The provided reason for a specific sync blocker.
        /// This member is required.
        public var createdReason: Swift.String?
        /// The ID for a specific sync blocker.
        /// This member is required.
        public var id: Swift.String?
        /// The time that a specific sync blocker was resolved.
        public var resolvedAt: Foundation.Date?
        /// The resolved reason for a specific sync blocker.
        public var resolvedReason: Swift.String?
        /// The status for a specific sync blocker.
        /// This member is required.
        public var status: CodeStarconnectionsClientTypes.BlockerStatus?
        /// The sync blocker type.
        /// This member is required.
        public var type: CodeStarconnectionsClientTypes.BlockerType?

        public init(
            contexts: [CodeStarconnectionsClientTypes.SyncBlockerContext]? = nil,
            createdAt: Foundation.Date? = nil,
            createdReason: Swift.String? = nil,
            id: Swift.String? = nil,
            resolvedAt: Foundation.Date? = nil,
            resolvedReason: Swift.String? = nil,
            status: CodeStarconnectionsClientTypes.BlockerStatus? = nil,
            type: CodeStarconnectionsClientTypes.BlockerType? = nil
        )
        {
            self.contexts = contexts
            self.createdAt = createdAt
            self.createdReason = createdReason
            self.id = id
            self.resolvedAt = resolvedAt
            self.resolvedReason = resolvedReason
            self.status = status
            self.type = type
        }
    }
}

extension CodeStarconnectionsClientTypes {

    /// A summary for sync blockers.
    public struct SyncBlockerSummary: Swift.Sendable {
        /// The latest events for a sync blocker summary.
        public var latestBlockers: [CodeStarconnectionsClientTypes.SyncBlocker]?
        /// The parent resource name for a sync blocker summary.
        public var parentResourceName: Swift.String?
        /// The resource name for sync blocker summary.
        /// This member is required.
        public var resourceName: Swift.String?

        public init(
            latestBlockers: [CodeStarconnectionsClientTypes.SyncBlocker]? = nil,
            parentResourceName: Swift.String? = nil,
            resourceName: Swift.String? = nil
        )
        {
            self.latestBlockers = latestBlockers
            self.parentResourceName = parentResourceName
            self.resourceName = resourceName
        }
    }
}

public struct GetSyncBlockerSummaryOutput: Swift.Sendable {
    /// The list of sync blockers for a specified resource.
    /// This member is required.
    public var syncBlockerSummary: CodeStarconnectionsClientTypes.SyncBlockerSummary?

    public init(
        syncBlockerSummary: CodeStarconnectionsClientTypes.SyncBlockerSummary? = nil
    )
    {
        self.syncBlockerSummary = syncBlockerSummary
    }
}

public struct GetSyncConfigurationInput: Swift.Sendable {
    /// The name of the Amazon Web Services resource for the sync configuration for which you want to retrieve information.
    /// This member is required.
    public var resourceName: Swift.String?
    /// The sync type for the sync configuration for which you want to retrieve information.
    /// This member is required.
    public var syncType: CodeStarconnectionsClientTypes.SyncConfigurationType?

    public init(
        resourceName: Swift.String? = nil,
        syncType: CodeStarconnectionsClientTypes.SyncConfigurationType? = nil
    )
    {
        self.resourceName = resourceName
        self.syncType = syncType
    }
}

public struct GetSyncConfigurationOutput: Swift.Sendable {
    /// The details about the sync configuration for which you want to retrieve information.
    /// This member is required.
    public var syncConfiguration: CodeStarconnectionsClientTypes.SyncConfiguration?

    public init(
        syncConfiguration: CodeStarconnectionsClientTypes.SyncConfiguration? = nil
    )
    {
        self.syncConfiguration = syncConfiguration
    }
}

public struct ListConnectionsInput: Swift.Sendable {
    /// Filters the list of connections to those associated with a specified host.
    public var hostArnFilter: Swift.String?
    /// The maximum number of results to return in a single call. To retrieve the remaining results, make another call with the returned nextToken value.
    public var maxResults: Swift.Int?
    /// The token that was returned from the previous ListConnections call, which can be used to return the next set of connections in the list.
    public var nextToken: Swift.String?
    /// Filters the list of connections to those associated with a specified provider, such as Bitbucket.
    public var providerTypeFilter: CodeStarconnectionsClientTypes.ProviderType?

    public init(
        hostArnFilter: Swift.String? = nil,
        maxResults: Swift.Int? = 0,
        nextToken: Swift.String? = nil,
        providerTypeFilter: CodeStarconnectionsClientTypes.ProviderType? = nil
    )
    {
        self.hostArnFilter = hostArnFilter
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.providerTypeFilter = providerTypeFilter
    }
}

public struct ListConnectionsOutput: Swift.Sendable {
    /// A list of connections and the details for each connection, such as status, owner, and provider type.
    public var connections: [CodeStarconnectionsClientTypes.Connection]?
    /// A token that can be used in the next ListConnections call. To view all items in the list, continue to call this operation with each subsequent token until no more nextToken values are returned.
    public var nextToken: Swift.String?

    public init(
        connections: [CodeStarconnectionsClientTypes.Connection]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.connections = connections
        self.nextToken = nextToken
    }
}

public struct ListHostsInput: Swift.Sendable {
    /// The maximum number of results to return in a single call. To retrieve the remaining results, make another call with the returned nextToken value.
    public var maxResults: Swift.Int?
    /// The token that was returned from the previous ListHosts call, which can be used to return the next set of hosts in the list.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = 0,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension CodeStarconnectionsClientTypes {

    /// A resource that represents the infrastructure where a third-party provider is installed. The host is used when you create connections to an installed third-party provider type, such as GitHub Enterprise Server. You create one host for all connections to that provider. A host created through the CLI or the SDK is in `PENDING` status by default. You can make its status `AVAILABLE` by setting up the host in the console.
    public struct Host: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the host.
        public var hostArn: Swift.String?
        /// The name of the host.
        public var name: Swift.String?
        /// The endpoint of the infrastructure where your provider type is installed.
        public var providerEndpoint: Swift.String?
        /// The name of the installed provider to be associated with your connection. The host resource represents the infrastructure where your provider type is installed. The valid provider type is GitHub Enterprise Server.
        public var providerType: CodeStarconnectionsClientTypes.ProviderType?
        /// The status of the host, such as PENDING, AVAILABLE, VPC_CONFIG_DELETING, VPC_CONFIG_INITIALIZING, and VPC_CONFIG_FAILED_INITIALIZATION.
        public var status: Swift.String?
        /// The status description for the host.
        public var statusMessage: Swift.String?
        /// The VPC configuration provisioned for the host.
        public var vpcConfiguration: CodeStarconnectionsClientTypes.VpcConfiguration?

        public init(
            hostArn: Swift.String? = nil,
            name: Swift.String? = nil,
            providerEndpoint: Swift.String? = nil,
            providerType: CodeStarconnectionsClientTypes.ProviderType? = nil,
            status: Swift.String? = nil,
            statusMessage: Swift.String? = nil,
            vpcConfiguration: CodeStarconnectionsClientTypes.VpcConfiguration? = nil
        )
        {
            self.hostArn = hostArn
            self.name = name
            self.providerEndpoint = providerEndpoint
            self.providerType = providerType
            self.status = status
            self.statusMessage = statusMessage
            self.vpcConfiguration = vpcConfiguration
        }
    }
}

public struct ListHostsOutput: Swift.Sendable {
    /// A list of hosts and the details for each host, such as status, endpoint, and provider type.
    public var hosts: [CodeStarconnectionsClientTypes.Host]?
    /// A token that can be used in the next ListHosts call. To view all items in the list, continue to call this operation with each subsequent token until no more nextToken values are returned.
    public var nextToken: Swift.String?

    public init(
        hosts: [CodeStarconnectionsClientTypes.Host]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.hosts = hosts
        self.nextToken = nextToken
    }
}

public struct ListRepositoryLinksInput: Swift.Sendable {
    /// A non-zero, non-negative integer used to limit the number of returned results.
    public var maxResults: Swift.Int?
    /// An enumeration token that, when provided in a request, returns the next batch of the results.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = 0,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListRepositoryLinksOutput: Swift.Sendable {
    /// An enumeration token that allows the operation to batch the results of the operation.
    public var nextToken: Swift.String?
    /// Lists the repository links called by the list repository links operation.
    /// This member is required.
    public var repositoryLinks: [CodeStarconnectionsClientTypes.RepositoryLinkInfo]?

    public init(
        nextToken: Swift.String? = nil,
        repositoryLinks: [CodeStarconnectionsClientTypes.RepositoryLinkInfo]? = nil
    )
    {
        self.nextToken = nextToken
        self.repositoryLinks = repositoryLinks
    }
}

public struct ListRepositorySyncDefinitionsInput: Swift.Sendable {
    /// The ID of the repository link for the sync definition for which you want to retrieve information.
    /// This member is required.
    public var repositoryLinkId: Swift.String?
    /// The sync type of the repository link for the the sync definition for which you want to retrieve information.
    /// This member is required.
    public var syncType: CodeStarconnectionsClientTypes.SyncConfigurationType?

    public init(
        repositoryLinkId: Swift.String? = nil,
        syncType: CodeStarconnectionsClientTypes.SyncConfigurationType? = nil
    )
    {
        self.repositoryLinkId = repositoryLinkId
        self.syncType = syncType
    }
}

extension CodeStarconnectionsClientTypes {

    /// The definition for a repository with a sync configuration.
    public struct RepositorySyncDefinition: Swift.Sendable {
        /// The branch specified for a repository sync definition.
        /// This member is required.
        public var branch: Swift.String?
        /// The configuration file for a repository sync definition. This value comes from creating or updating the config-file field of a sync-configuration.
        /// This member is required.
        public var directory: Swift.String?
        /// The parent resource specified for a repository sync definition.
        /// This member is required.
        public var parent: Swift.String?
        /// The target resource specified for a repository sync definition. In some cases, such as CFN_STACK_SYNC, the parent and target resource are the same.
        /// This member is required.
        public var target: Swift.String?

        public init(
            branch: Swift.String? = nil,
            directory: Swift.String? = nil,
            parent: Swift.String? = nil,
            target: Swift.String? = nil
        )
        {
            self.branch = branch
            self.directory = directory
            self.parent = parent
            self.target = target
        }
    }
}

public struct ListRepositorySyncDefinitionsOutput: Swift.Sendable {
    /// An enumeration token that, when provided in a request, returns the next batch of the results.
    public var nextToken: Swift.String?
    /// The list of repository sync definitions returned by the request. A RepositorySyncDefinition is a mapping from a repository branch to all the Amazon Web Services resources that are being synced from that branch.
    /// This member is required.
    public var repositorySyncDefinitions: [CodeStarconnectionsClientTypes.RepositorySyncDefinition]?

    public init(
        nextToken: Swift.String? = nil,
        repositorySyncDefinitions: [CodeStarconnectionsClientTypes.RepositorySyncDefinition]? = nil
    )
    {
        self.nextToken = nextToken
        self.repositorySyncDefinitions = repositorySyncDefinitions
    }
}

public struct ListSyncConfigurationsInput: Swift.Sendable {
    /// A non-zero, non-negative integer used to limit the number of returned results.
    public var maxResults: Swift.Int?
    /// An enumeration token that allows the operation to batch the results of the operation.
    public var nextToken: Swift.String?
    /// The ID of the repository link for the requested list of sync configurations.
    /// This member is required.
    public var repositoryLinkId: Swift.String?
    /// The sync type for the requested list of sync configurations.
    /// This member is required.
    public var syncType: CodeStarconnectionsClientTypes.SyncConfigurationType?

    public init(
        maxResults: Swift.Int? = 0,
        nextToken: Swift.String? = nil,
        repositoryLinkId: Swift.String? = nil,
        syncType: CodeStarconnectionsClientTypes.SyncConfigurationType? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.repositoryLinkId = repositoryLinkId
        self.syncType = syncType
    }
}

public struct ListSyncConfigurationsOutput: Swift.Sendable {
    /// An enumeration token that allows the operation to batch the next results of the operation.
    public var nextToken: Swift.String?
    /// The list of repository sync definitions returned by the request.
    /// This member is required.
    public var syncConfigurations: [CodeStarconnectionsClientTypes.SyncConfiguration]?

    public init(
        nextToken: Swift.String? = nil,
        syncConfigurations: [CodeStarconnectionsClientTypes.SyncConfiguration]? = nil
    )
    {
        self.nextToken = nextToken
        self.syncConfigurations = syncConfigurations
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource for which you want to get information about tags, if any.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// A list of tag key and value pairs associated with the specified resource.
    public var tags: [CodeStarconnectionsClientTypes.Tag]?

    public init(
        tags: [CodeStarconnectionsClientTypes.Tag]? = nil
    )
    {
        self.tags = tags
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource to which you want to add or update tags.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tags you want to modify or add to the resource.
    /// This member is required.
    public var tags: [CodeStarconnectionsClientTypes.Tag]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [CodeStarconnectionsClientTypes.Tag]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the resource to remove tags from.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The list of keys for the tags to be removed from the resource.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

/// Two conflicting operations have been made on the same resource.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The operation is not supported. Check the connection status and try again.
public struct UnsupportedOperationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UnsupportedOperationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct UpdateHostInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the host to be updated.
    /// This member is required.
    public var hostArn: Swift.String?
    /// The URL or endpoint of the host to be updated.
    public var providerEndpoint: Swift.String?
    /// The VPC configuration of the host to be updated. A VPC must be configured and the infrastructure to be represented by the host must already be connected to the VPC.
    public var vpcConfiguration: CodeStarconnectionsClientTypes.VpcConfiguration?

    public init(
        hostArn: Swift.String? = nil,
        providerEndpoint: Swift.String? = nil,
        vpcConfiguration: CodeStarconnectionsClientTypes.VpcConfiguration? = nil
    )
    {
        self.hostArn = hostArn
        self.providerEndpoint = providerEndpoint
        self.vpcConfiguration = vpcConfiguration
    }
}

public struct UpdateHostOutput: Swift.Sendable {

    public init() { }
}

/// The conditional check failed. Try again later.
public struct ConditionalCheckFailedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConditionalCheckFailedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The update is out of sync. Try syncing again.
public struct UpdateOutOfSyncException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UpdateOutOfSyncException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct UpdateRepositoryLinkInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the connection for the repository link to be updated. The updated connection ARN must have the same providerType (such as GitHub) as the original connection ARN for the repo link.
    public var connectionArn: Swift.String?
    /// The Amazon Resource Name (ARN) of the encryption key for the repository link to be updated.
    public var encryptionKeyArn: Swift.String?
    /// The ID of the repository link to be updated.
    /// This member is required.
    public var repositoryLinkId: Swift.String?

    public init(
        connectionArn: Swift.String? = nil,
        encryptionKeyArn: Swift.String? = nil,
        repositoryLinkId: Swift.String? = nil
    )
    {
        self.connectionArn = connectionArn
        self.encryptionKeyArn = encryptionKeyArn
        self.repositoryLinkId = repositoryLinkId
    }
}

public struct UpdateRepositoryLinkOutput: Swift.Sendable {
    /// Information about the repository link to be updated.
    /// This member is required.
    public var repositoryLinkInfo: CodeStarconnectionsClientTypes.RepositoryLinkInfo?

    public init(
        repositoryLinkInfo: CodeStarconnectionsClientTypes.RepositoryLinkInfo? = nil
    )
    {
        self.repositoryLinkInfo = repositoryLinkInfo
    }
}

/// Retrying the latest commit failed. Try again later.
public struct RetryLatestCommitFailedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "RetryLatestCommitFailedException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Unable to continue. The sync blocker does not exist.
public struct SyncBlockerDoesNotExistException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "SyncBlockerDoesNotExistException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct UpdateSyncBlockerInput: Swift.Sendable {
    /// The ID of the sync blocker to be updated.
    /// This member is required.
    public var id: Swift.String?
    /// The reason for resolving the sync blocker.
    /// This member is required.
    public var resolvedReason: Swift.String?
    /// The name of the resource for the sync blocker to be updated.
    /// This member is required.
    public var resourceName: Swift.String?
    /// The sync type of the sync blocker to be updated.
    /// This member is required.
    public var syncType: CodeStarconnectionsClientTypes.SyncConfigurationType?

    public init(
        id: Swift.String? = nil,
        resolvedReason: Swift.String? = nil,
        resourceName: Swift.String? = nil,
        syncType: CodeStarconnectionsClientTypes.SyncConfigurationType? = nil
    )
    {
        self.id = id
        self.resolvedReason = resolvedReason
        self.resourceName = resourceName
        self.syncType = syncType
    }
}

public struct UpdateSyncBlockerOutput: Swift.Sendable {
    /// The parent resource name for the sync blocker.
    public var parentResourceName: Swift.String?
    /// The resource name for the sync blocker.
    /// This member is required.
    public var resourceName: Swift.String?
    /// Information about the sync blocker to be updated.
    /// This member is required.
    public var syncBlocker: CodeStarconnectionsClientTypes.SyncBlocker?

    public init(
        parentResourceName: Swift.String? = nil,
        resourceName: Swift.String? = nil,
        syncBlocker: CodeStarconnectionsClientTypes.SyncBlocker? = nil
    )
    {
        self.parentResourceName = parentResourceName
        self.resourceName = resourceName
        self.syncBlocker = syncBlocker
    }
}

public struct UpdateSyncConfigurationInput: Swift.Sendable {
    /// The branch for the sync configuration to be updated.
    public var branch: Swift.String?
    /// The configuration file for the sync configuration to be updated.
    public var configFile: Swift.String?
    /// Whether to enable or disable publishing of deployment status to source providers.
    public var publishDeploymentStatus: CodeStarconnectionsClientTypes.PublishDeploymentStatus?
    /// The ID of the repository link for the sync configuration to be updated.
    public var repositoryLinkId: Swift.String?
    /// The name of the Amazon Web Services resource for the sync configuration to be updated.
    /// This member is required.
    public var resourceName: Swift.String?
    /// The ARN of the IAM role for the sync configuration to be updated.
    public var roleArn: Swift.String?
    /// The sync type for the sync configuration to be updated.
    /// This member is required.
    public var syncType: CodeStarconnectionsClientTypes.SyncConfigurationType?
    /// When to trigger Git sync to begin the stack update.
    public var triggerResourceUpdateOn: CodeStarconnectionsClientTypes.TriggerResourceUpdateOn?

    public init(
        branch: Swift.String? = nil,
        configFile: Swift.String? = nil,
        publishDeploymentStatus: CodeStarconnectionsClientTypes.PublishDeploymentStatus? = nil,
        repositoryLinkId: Swift.String? = nil,
        resourceName: Swift.String? = nil,
        roleArn: Swift.String? = nil,
        syncType: CodeStarconnectionsClientTypes.SyncConfigurationType? = nil,
        triggerResourceUpdateOn: CodeStarconnectionsClientTypes.TriggerResourceUpdateOn? = nil
    )
    {
        self.branch = branch
        self.configFile = configFile
        self.publishDeploymentStatus = publishDeploymentStatus
        self.repositoryLinkId = repositoryLinkId
        self.resourceName = resourceName
        self.roleArn = roleArn
        self.syncType = syncType
        self.triggerResourceUpdateOn = triggerResourceUpdateOn
    }
}

public struct UpdateSyncConfigurationOutput: Swift.Sendable {
    /// The information returned for the sync configuration to be updated.
    /// This member is required.
    public var syncConfiguration: CodeStarconnectionsClientTypes.SyncConfiguration?

    public init(
        syncConfiguration: CodeStarconnectionsClientTypes.SyncConfiguration? = nil
    )
    {
        self.syncConfiguration = syncConfiguration
    }
}

extension CreateConnectionInput {

    static func urlPathProvider(_ value: CreateConnectionInput) -> Swift.String? {
        return "/"
    }
}

extension CreateHostInput {

    static func urlPathProvider(_ value: CreateHostInput) -> Swift.String? {
        return "/"
    }
}

extension CreateRepositoryLinkInput {

    static func urlPathProvider(_ value: CreateRepositoryLinkInput) -> Swift.String? {
        return "/"
    }
}

extension CreateSyncConfigurationInput {

    static func urlPathProvider(_ value: CreateSyncConfigurationInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteConnectionInput {

    static func urlPathProvider(_ value: DeleteConnectionInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteHostInput {

    static func urlPathProvider(_ value: DeleteHostInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteRepositoryLinkInput {

    static func urlPathProvider(_ value: DeleteRepositoryLinkInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteSyncConfigurationInput {

    static func urlPathProvider(_ value: DeleteSyncConfigurationInput) -> Swift.String? {
        return "/"
    }
}

extension GetConnectionInput {

    static func urlPathProvider(_ value: GetConnectionInput) -> Swift.String? {
        return "/"
    }
}

extension GetHostInput {

    static func urlPathProvider(_ value: GetHostInput) -> Swift.String? {
        return "/"
    }
}

extension GetRepositoryLinkInput {

    static func urlPathProvider(_ value: GetRepositoryLinkInput) -> Swift.String? {
        return "/"
    }
}

extension GetRepositorySyncStatusInput {

    static func urlPathProvider(_ value: GetRepositorySyncStatusInput) -> Swift.String? {
        return "/"
    }
}

extension GetResourceSyncStatusInput {

    static func urlPathProvider(_ value: GetResourceSyncStatusInput) -> Swift.String? {
        return "/"
    }
}

extension GetSyncBlockerSummaryInput {

    static func urlPathProvider(_ value: GetSyncBlockerSummaryInput) -> Swift.String? {
        return "/"
    }
}

extension GetSyncConfigurationInput {

    static func urlPathProvider(_ value: GetSyncConfigurationInput) -> Swift.String? {
        return "/"
    }
}

extension ListConnectionsInput {

    static func urlPathProvider(_ value: ListConnectionsInput) -> Swift.String? {
        return "/"
    }
}

extension ListHostsInput {

    static func urlPathProvider(_ value: ListHostsInput) -> Swift.String? {
        return "/"
    }
}

extension ListRepositoryLinksInput {

    static func urlPathProvider(_ value: ListRepositoryLinksInput) -> Swift.String? {
        return "/"
    }
}

extension ListRepositorySyncDefinitionsInput {

    static func urlPathProvider(_ value: ListRepositorySyncDefinitionsInput) -> Swift.String? {
        return "/"
    }
}

extension ListSyncConfigurationsInput {

    static func urlPathProvider(_ value: ListSyncConfigurationsInput) -> Swift.String? {
        return "/"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        return "/"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        return "/"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateHostInput {

    static func urlPathProvider(_ value: UpdateHostInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateRepositoryLinkInput {

    static func urlPathProvider(_ value: UpdateRepositoryLinkInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateSyncBlockerInput {

    static func urlPathProvider(_ value: UpdateSyncBlockerInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateSyncConfigurationInput {

    static func urlPathProvider(_ value: UpdateSyncConfigurationInput) -> Swift.String? {
        return "/"
    }
}

extension CreateConnectionInput {

    static func write(value: CreateConnectionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ConnectionName"].write(value.connectionName)
        try writer["HostArn"].write(value.hostArn)
        try writer["ProviderType"].write(value.providerType)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: CodeStarconnectionsClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateHostInput {

    static func write(value: CreateHostInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Name"].write(value.name)
        try writer["ProviderEndpoint"].write(value.providerEndpoint)
        try writer["ProviderType"].write(value.providerType)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: CodeStarconnectionsClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["VpcConfiguration"].write(value.vpcConfiguration, with: CodeStarconnectionsClientTypes.VpcConfiguration.write(value:to:))
    }
}

extension CreateRepositoryLinkInput {

    static func write(value: CreateRepositoryLinkInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ConnectionArn"].write(value.connectionArn)
        try writer["EncryptionKeyArn"].write(value.encryptionKeyArn)
        try writer["OwnerId"].write(value.ownerId)
        try writer["RepositoryName"].write(value.repositoryName)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: CodeStarconnectionsClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension CreateSyncConfigurationInput {

    static func write(value: CreateSyncConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Branch"].write(value.branch)
        try writer["ConfigFile"].write(value.configFile)
        try writer["PublishDeploymentStatus"].write(value.publishDeploymentStatus)
        try writer["RepositoryLinkId"].write(value.repositoryLinkId)
        try writer["ResourceName"].write(value.resourceName)
        try writer["RoleArn"].write(value.roleArn)
        try writer["SyncType"].write(value.syncType)
        try writer["TriggerResourceUpdateOn"].write(value.triggerResourceUpdateOn)
    }
}

extension DeleteConnectionInput {

    static func write(value: DeleteConnectionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ConnectionArn"].write(value.connectionArn)
    }
}

extension DeleteHostInput {

    static func write(value: DeleteHostInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["HostArn"].write(value.hostArn)
    }
}

extension DeleteRepositoryLinkInput {

    static func write(value: DeleteRepositoryLinkInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["RepositoryLinkId"].write(value.repositoryLinkId)
    }
}

extension DeleteSyncConfigurationInput {

    static func write(value: DeleteSyncConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceName"].write(value.resourceName)
        try writer["SyncType"].write(value.syncType)
    }
}

extension GetConnectionInput {

    static func write(value: GetConnectionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ConnectionArn"].write(value.connectionArn)
    }
}

extension GetHostInput {

    static func write(value: GetHostInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["HostArn"].write(value.hostArn)
    }
}

extension GetRepositoryLinkInput {

    static func write(value: GetRepositoryLinkInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["RepositoryLinkId"].write(value.repositoryLinkId)
    }
}

extension GetRepositorySyncStatusInput {

    static func write(value: GetRepositorySyncStatusInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Branch"].write(value.branch)
        try writer["RepositoryLinkId"].write(value.repositoryLinkId)
        try writer["SyncType"].write(value.syncType)
    }
}

extension GetResourceSyncStatusInput {

    static func write(value: GetResourceSyncStatusInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceName"].write(value.resourceName)
        try writer["SyncType"].write(value.syncType)
    }
}

extension GetSyncBlockerSummaryInput {

    static func write(value: GetSyncBlockerSummaryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceName"].write(value.resourceName)
        try writer["SyncType"].write(value.syncType)
    }
}

extension GetSyncConfigurationInput {

    static func write(value: GetSyncConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceName"].write(value.resourceName)
        try writer["SyncType"].write(value.syncType)
    }
}

extension ListConnectionsInput {

    static func write(value: ListConnectionsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["HostArnFilter"].write(value.hostArnFilter)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["ProviderTypeFilter"].write(value.providerTypeFilter)
    }
}

extension ListHostsInput {

    static func write(value: ListHostsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListRepositoryLinksInput {

    static func write(value: ListRepositoryLinksInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListRepositorySyncDefinitionsInput {

    static func write(value: ListRepositorySyncDefinitionsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["RepositoryLinkId"].write(value.repositoryLinkId)
        try writer["SyncType"].write(value.syncType)
    }
}

extension ListSyncConfigurationsInput {

    static func write(value: ListSyncConfigurationsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["RepositoryLinkId"].write(value.repositoryLinkId)
        try writer["SyncType"].write(value.syncType)
    }
}

extension ListTagsForResourceInput {

    static func write(value: ListTagsForResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceArn"].write(value.resourceArn)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceArn"].write(value.resourceArn)
        try writer["Tags"].writeList(value.tags, memberWritingClosure: CodeStarconnectionsClientTypes.Tag.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UntagResourceInput {

    static func write(value: UntagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceArn"].write(value.resourceArn)
        try writer["TagKeys"].writeList(value.tagKeys, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdateHostInput {

    static func write(value: UpdateHostInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["HostArn"].write(value.hostArn)
        try writer["ProviderEndpoint"].write(value.providerEndpoint)
        try writer["VpcConfiguration"].write(value.vpcConfiguration, with: CodeStarconnectionsClientTypes.VpcConfiguration.write(value:to:))
    }
}

extension UpdateRepositoryLinkInput {

    static func write(value: UpdateRepositoryLinkInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ConnectionArn"].write(value.connectionArn)
        try writer["EncryptionKeyArn"].write(value.encryptionKeyArn)
        try writer["RepositoryLinkId"].write(value.repositoryLinkId)
    }
}

extension UpdateSyncBlockerInput {

    static func write(value: UpdateSyncBlockerInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Id"].write(value.id)
        try writer["ResolvedReason"].write(value.resolvedReason)
        try writer["ResourceName"].write(value.resourceName)
        try writer["SyncType"].write(value.syncType)
    }
}

extension UpdateSyncConfigurationInput {

    static func write(value: UpdateSyncConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Branch"].write(value.branch)
        try writer["ConfigFile"].write(value.configFile)
        try writer["PublishDeploymentStatus"].write(value.publishDeploymentStatus)
        try writer["RepositoryLinkId"].write(value.repositoryLinkId)
        try writer["ResourceName"].write(value.resourceName)
        try writer["RoleArn"].write(value.roleArn)
        try writer["SyncType"].write(value.syncType)
        try writer["TriggerResourceUpdateOn"].write(value.triggerResourceUpdateOn)
    }
}

extension CreateConnectionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateConnectionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateConnectionOutput()
        value.connectionArn = try reader["ConnectionArn"].readIfPresent() ?? ""
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: CodeStarconnectionsClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension CreateHostOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateHostOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateHostOutput()
        value.hostArn = try reader["HostArn"].readIfPresent()
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: CodeStarconnectionsClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension CreateRepositoryLinkOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateRepositoryLinkOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateRepositoryLinkOutput()
        value.repositoryLinkInfo = try reader["RepositoryLinkInfo"].readIfPresent(with: CodeStarconnectionsClientTypes.RepositoryLinkInfo.read(from:))
        return value
    }
}

extension CreateSyncConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateSyncConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateSyncConfigurationOutput()
        value.syncConfiguration = try reader["SyncConfiguration"].readIfPresent(with: CodeStarconnectionsClientTypes.SyncConfiguration.read(from:))
        return value
    }
}

extension DeleteConnectionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteConnectionOutput {
        return DeleteConnectionOutput()
    }
}

extension DeleteHostOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteHostOutput {
        return DeleteHostOutput()
    }
}

extension DeleteRepositoryLinkOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteRepositoryLinkOutput {
        return DeleteRepositoryLinkOutput()
    }
}

extension DeleteSyncConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteSyncConfigurationOutput {
        return DeleteSyncConfigurationOutput()
    }
}

extension GetConnectionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetConnectionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetConnectionOutput()
        value.connection = try reader["Connection"].readIfPresent(with: CodeStarconnectionsClientTypes.Connection.read(from:))
        return value
    }
}

extension GetHostOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetHostOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetHostOutput()
        value.name = try reader["Name"].readIfPresent()
        value.providerEndpoint = try reader["ProviderEndpoint"].readIfPresent()
        value.providerType = try reader["ProviderType"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        value.vpcConfiguration = try reader["VpcConfiguration"].readIfPresent(with: CodeStarconnectionsClientTypes.VpcConfiguration.read(from:))
        return value
    }
}

extension GetRepositoryLinkOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetRepositoryLinkOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetRepositoryLinkOutput()
        value.repositoryLinkInfo = try reader["RepositoryLinkInfo"].readIfPresent(with: CodeStarconnectionsClientTypes.RepositoryLinkInfo.read(from:))
        return value
    }
}

extension GetRepositorySyncStatusOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetRepositorySyncStatusOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetRepositorySyncStatusOutput()
        value.latestSync = try reader["LatestSync"].readIfPresent(with: CodeStarconnectionsClientTypes.RepositorySyncAttempt.read(from:))
        return value
    }
}

extension GetResourceSyncStatusOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetResourceSyncStatusOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetResourceSyncStatusOutput()
        value.desiredState = try reader["DesiredState"].readIfPresent(with: CodeStarconnectionsClientTypes.Revision.read(from:))
        value.latestSuccessfulSync = try reader["LatestSuccessfulSync"].readIfPresent(with: CodeStarconnectionsClientTypes.ResourceSyncAttempt.read(from:))
        value.latestSync = try reader["LatestSync"].readIfPresent(with: CodeStarconnectionsClientTypes.ResourceSyncAttempt.read(from:))
        return value
    }
}

extension GetSyncBlockerSummaryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetSyncBlockerSummaryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetSyncBlockerSummaryOutput()
        value.syncBlockerSummary = try reader["SyncBlockerSummary"].readIfPresent(with: CodeStarconnectionsClientTypes.SyncBlockerSummary.read(from:))
        return value
    }
}

extension GetSyncConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetSyncConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetSyncConfigurationOutput()
        value.syncConfiguration = try reader["SyncConfiguration"].readIfPresent(with: CodeStarconnectionsClientTypes.SyncConfiguration.read(from:))
        return value
    }
}

extension ListConnectionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListConnectionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListConnectionsOutput()
        value.connections = try reader["Connections"].readListIfPresent(memberReadingClosure: CodeStarconnectionsClientTypes.Connection.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListHostsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListHostsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListHostsOutput()
        value.hosts = try reader["Hosts"].readListIfPresent(memberReadingClosure: CodeStarconnectionsClientTypes.Host.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListRepositoryLinksOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRepositoryLinksOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRepositoryLinksOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.repositoryLinks = try reader["RepositoryLinks"].readListIfPresent(memberReadingClosure: CodeStarconnectionsClientTypes.RepositoryLinkInfo.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ListRepositorySyncDefinitionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRepositorySyncDefinitionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRepositorySyncDefinitionsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.repositorySyncDefinitions = try reader["RepositorySyncDefinitions"].readListIfPresent(memberReadingClosure: CodeStarconnectionsClientTypes.RepositorySyncDefinition.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ListSyncConfigurationsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListSyncConfigurationsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListSyncConfigurationsOutput()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.syncConfigurations = try reader["SyncConfigurations"].readListIfPresent(memberReadingClosure: CodeStarconnectionsClientTypes.SyncConfiguration.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["Tags"].readListIfPresent(memberReadingClosure: CodeStarconnectionsClientTypes.Tag.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateHostOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateHostOutput {
        return UpdateHostOutput()
    }
}

extension UpdateRepositoryLinkOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateRepositoryLinkOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateRepositoryLinkOutput()
        value.repositoryLinkInfo = try reader["RepositoryLinkInfo"].readIfPresent(with: CodeStarconnectionsClientTypes.RepositoryLinkInfo.read(from:))
        return value
    }
}

extension UpdateSyncBlockerOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateSyncBlockerOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateSyncBlockerOutput()
        value.parentResourceName = try reader["ParentResourceName"].readIfPresent()
        value.resourceName = try reader["ResourceName"].readIfPresent() ?? ""
        value.syncBlocker = try reader["SyncBlocker"].readIfPresent(with: CodeStarconnectionsClientTypes.SyncBlocker.read(from:))
        return value
    }
}

extension UpdateSyncConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateSyncConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateSyncConfigurationOutput()
        value.syncConfiguration = try reader["SyncConfiguration"].readIfPresent(with: CodeStarconnectionsClientTypes.SyncConfiguration.read(from:))
        return value
    }
}

enum CreateConnectionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ResourceUnavailableException": return try ResourceUnavailableException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateHostOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateRepositoryLinkOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConcurrentModificationException": return try ConcurrentModificationException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ResourceAlreadyExistsException": return try ResourceAlreadyExistsException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateSyncConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConcurrentModificationException": return try ConcurrentModificationException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ResourceAlreadyExistsException": return try ResourceAlreadyExistsException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteConnectionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteHostOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ResourceUnavailableException": return try ResourceUnavailableException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteRepositoryLinkOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConcurrentModificationException": return try ConcurrentModificationException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "SyncConfigurationStillExistsException": return try SyncConfigurationStillExistsException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UnsupportedProviderTypeException": return try UnsupportedProviderTypeException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteSyncConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConcurrentModificationException": return try ConcurrentModificationException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetConnectionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ResourceUnavailableException": return try ResourceUnavailableException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetHostOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ResourceUnavailableException": return try ResourceUnavailableException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetRepositoryLinkOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConcurrentModificationException": return try ConcurrentModificationException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetRepositorySyncStatusOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetResourceSyncStatusOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetSyncBlockerSummaryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetSyncConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListConnectionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListHostsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRepositoryLinksOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConcurrentModificationException": return try ConcurrentModificationException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRepositorySyncDefinitionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListSyncConfigurationsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateHostOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ResourceUnavailableException": return try ResourceUnavailableException.makeError(baseError: baseError)
            case "UnsupportedOperationException": return try UnsupportedOperationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateRepositoryLinkOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConditionalCheckFailedException": return try ConditionalCheckFailedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UpdateOutOfSyncException": return try UpdateOutOfSyncException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateSyncBlockerOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "RetryLatestCommitFailedException": return try RetryLatestCommitFailedException.makeError(baseError: baseError)
            case "SyncBlockerDoesNotExistException": return try SyncBlockerDoesNotExistException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateSyncConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConcurrentModificationException": return try ConcurrentModificationException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidInputException": return try InvalidInputException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UpdateOutOfSyncException": return try UpdateOutOfSyncException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension ResourceUnavailableException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ResourceUnavailableException {
        let reader = baseError.errorBodyReader
        var value = ResourceUnavailableException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LimitExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LimitExceededException {
        let reader = baseError.errorBodyReader
        var value = LimitExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceAlreadyExistsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ResourceAlreadyExistsException {
        let reader = baseError.errorBodyReader
        var value = ResourceAlreadyExistsException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidInputException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidInputException {
        let reader = baseError.errorBodyReader
        var value = InvalidInputException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        var value = ThrottlingException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConcurrentModificationException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ConcurrentModificationException {
        let reader = baseError.errorBodyReader
        var value = ConcurrentModificationException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UnsupportedProviderTypeException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UnsupportedProviderTypeException {
        let reader = baseError.errorBodyReader
        var value = UnsupportedProviderTypeException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SyncConfigurationStillExistsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> SyncConfigurationStillExistsException {
        let reader = baseError.errorBodyReader
        var value = SyncConfigurationStillExistsException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UnsupportedOperationException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UnsupportedOperationException {
        let reader = baseError.errorBodyReader
        var value = UnsupportedOperationException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConditionalCheckFailedException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ConditionalCheckFailedException {
        let reader = baseError.errorBodyReader
        var value = ConditionalCheckFailedException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension UpdateOutOfSyncException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> UpdateOutOfSyncException {
        let reader = baseError.errorBodyReader
        var value = UpdateOutOfSyncException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension RetryLatestCommitFailedException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> RetryLatestCommitFailedException {
        let reader = baseError.errorBodyReader
        var value = RetryLatestCommitFailedException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension SyncBlockerDoesNotExistException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> SyncBlockerDoesNotExistException {
        let reader = baseError.errorBodyReader
        var value = SyncBlockerDoesNotExistException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CodeStarconnectionsClientTypes.Tag {

    static func write(value: CodeStarconnectionsClientTypes.Tag?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Key"].write(value.key)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.Tag {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.Tag()
        value.key = try reader["Key"].readIfPresent() ?? ""
        value.value = try reader["Value"].readIfPresent() ?? ""
        return value
    }
}

extension CodeStarconnectionsClientTypes.RepositoryLinkInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.RepositoryLinkInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.RepositoryLinkInfo()
        value.connectionArn = try reader["ConnectionArn"].readIfPresent() ?? ""
        value.encryptionKeyArn = try reader["EncryptionKeyArn"].readIfPresent()
        value.ownerId = try reader["OwnerId"].readIfPresent() ?? ""
        value.providerType = try reader["ProviderType"].readIfPresent() ?? .sdkUnknown("")
        value.repositoryLinkArn = try reader["RepositoryLinkArn"].readIfPresent() ?? ""
        value.repositoryLinkId = try reader["RepositoryLinkId"].readIfPresent() ?? ""
        value.repositoryName = try reader["RepositoryName"].readIfPresent() ?? ""
        return value
    }
}

extension CodeStarconnectionsClientTypes.SyncConfiguration {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.SyncConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.SyncConfiguration()
        value.branch = try reader["Branch"].readIfPresent() ?? ""
        value.configFile = try reader["ConfigFile"].readIfPresent()
        value.ownerId = try reader["OwnerId"].readIfPresent() ?? ""
        value.providerType = try reader["ProviderType"].readIfPresent() ?? .sdkUnknown("")
        value.repositoryLinkId = try reader["RepositoryLinkId"].readIfPresent() ?? ""
        value.repositoryName = try reader["RepositoryName"].readIfPresent() ?? ""
        value.resourceName = try reader["ResourceName"].readIfPresent() ?? ""
        value.roleArn = try reader["RoleArn"].readIfPresent() ?? ""
        value.syncType = try reader["SyncType"].readIfPresent() ?? .sdkUnknown("")
        value.publishDeploymentStatus = try reader["PublishDeploymentStatus"].readIfPresent()
        value.triggerResourceUpdateOn = try reader["TriggerResourceUpdateOn"].readIfPresent()
        return value
    }
}

extension CodeStarconnectionsClientTypes.Connection {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.Connection {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.Connection()
        value.connectionName = try reader["ConnectionName"].readIfPresent()
        value.connectionArn = try reader["ConnectionArn"].readIfPresent()
        value.providerType = try reader["ProviderType"].readIfPresent()
        value.ownerAccountId = try reader["OwnerAccountId"].readIfPresent()
        value.connectionStatus = try reader["ConnectionStatus"].readIfPresent()
        value.hostArn = try reader["HostArn"].readIfPresent()
        return value
    }
}

extension CodeStarconnectionsClientTypes.VpcConfiguration {

    static func write(value: CodeStarconnectionsClientTypes.VpcConfiguration?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["SecurityGroupIds"].writeList(value.securityGroupIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["SubnetIds"].writeList(value.subnetIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["TlsCertificate"].write(value.tlsCertificate)
        try writer["VpcId"].write(value.vpcId)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.VpcConfiguration {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.VpcConfiguration()
        value.vpcId = try reader["VpcId"].readIfPresent() ?? ""
        value.subnetIds = try reader["SubnetIds"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.securityGroupIds = try reader["SecurityGroupIds"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.tlsCertificate = try reader["TlsCertificate"].readIfPresent()
        return value
    }
}

extension CodeStarconnectionsClientTypes.RepositorySyncAttempt {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.RepositorySyncAttempt {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.RepositorySyncAttempt()
        value.startedAt = try reader["StartedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.status = try reader["Status"].readIfPresent() ?? .sdkUnknown("")
        value.events = try reader["Events"].readListIfPresent(memberReadingClosure: CodeStarconnectionsClientTypes.RepositorySyncEvent.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension CodeStarconnectionsClientTypes.RepositorySyncEvent {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.RepositorySyncEvent {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.RepositorySyncEvent()
        value.event = try reader["Event"].readIfPresent() ?? ""
        value.externalId = try reader["ExternalId"].readIfPresent()
        value.time = try reader["Time"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.type = try reader["Type"].readIfPresent() ?? ""
        return value
    }
}

extension CodeStarconnectionsClientTypes.Revision {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.Revision {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.Revision()
        value.branch = try reader["Branch"].readIfPresent() ?? ""
        value.directory = try reader["Directory"].readIfPresent() ?? ""
        value.ownerId = try reader["OwnerId"].readIfPresent() ?? ""
        value.repositoryName = try reader["RepositoryName"].readIfPresent() ?? ""
        value.providerType = try reader["ProviderType"].readIfPresent() ?? .sdkUnknown("")
        value.sha = try reader["Sha"].readIfPresent() ?? ""
        return value
    }
}

extension CodeStarconnectionsClientTypes.ResourceSyncAttempt {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.ResourceSyncAttempt {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.ResourceSyncAttempt()
        value.events = try reader["Events"].readListIfPresent(memberReadingClosure: CodeStarconnectionsClientTypes.ResourceSyncEvent.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.initialRevision = try reader["InitialRevision"].readIfPresent(with: CodeStarconnectionsClientTypes.Revision.read(from:))
        value.startedAt = try reader["StartedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.status = try reader["Status"].readIfPresent() ?? .sdkUnknown("")
        value.targetRevision = try reader["TargetRevision"].readIfPresent(with: CodeStarconnectionsClientTypes.Revision.read(from:))
        value.target = try reader["Target"].readIfPresent() ?? ""
        return value
    }
}

extension CodeStarconnectionsClientTypes.ResourceSyncEvent {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.ResourceSyncEvent {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.ResourceSyncEvent()
        value.event = try reader["Event"].readIfPresent() ?? ""
        value.externalId = try reader["ExternalId"].readIfPresent()
        value.time = try reader["Time"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.type = try reader["Type"].readIfPresent() ?? ""
        return value
    }
}

extension CodeStarconnectionsClientTypes.SyncBlockerSummary {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.SyncBlockerSummary {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.SyncBlockerSummary()
        value.resourceName = try reader["ResourceName"].readIfPresent() ?? ""
        value.parentResourceName = try reader["ParentResourceName"].readIfPresent()
        value.latestBlockers = try reader["LatestBlockers"].readListIfPresent(memberReadingClosure: CodeStarconnectionsClientTypes.SyncBlocker.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension CodeStarconnectionsClientTypes.SyncBlocker {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.SyncBlocker {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.SyncBlocker()
        value.id = try reader["Id"].readIfPresent() ?? ""
        value.type = try reader["Type"].readIfPresent() ?? .sdkUnknown("")
        value.status = try reader["Status"].readIfPresent() ?? .sdkUnknown("")
        value.createdReason = try reader["CreatedReason"].readIfPresent() ?? ""
        value.createdAt = try reader["CreatedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds) ?? SmithyTimestamps.TimestampFormatter(format: .dateTime).date(from: "1970-01-01T00:00:00Z")
        value.contexts = try reader["Contexts"].readListIfPresent(memberReadingClosure: CodeStarconnectionsClientTypes.SyncBlockerContext.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.resolvedReason = try reader["ResolvedReason"].readIfPresent()
        value.resolvedAt = try reader["ResolvedAt"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension CodeStarconnectionsClientTypes.SyncBlockerContext {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.SyncBlockerContext {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.SyncBlockerContext()
        value.key = try reader["Key"].readIfPresent() ?? ""
        value.value = try reader["Value"].readIfPresent() ?? ""
        return value
    }
}

extension CodeStarconnectionsClientTypes.Host {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.Host {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.Host()
        value.name = try reader["Name"].readIfPresent()
        value.hostArn = try reader["HostArn"].readIfPresent()
        value.providerType = try reader["ProviderType"].readIfPresent()
        value.providerEndpoint = try reader["ProviderEndpoint"].readIfPresent()
        value.vpcConfiguration = try reader["VpcConfiguration"].readIfPresent(with: CodeStarconnectionsClientTypes.VpcConfiguration.read(from:))
        value.status = try reader["Status"].readIfPresent()
        value.statusMessage = try reader["StatusMessage"].readIfPresent()
        return value
    }
}

extension CodeStarconnectionsClientTypes.RepositorySyncDefinition {

    static func read(from reader: SmithyJSON.Reader) throws -> CodeStarconnectionsClientTypes.RepositorySyncDefinition {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CodeStarconnectionsClientTypes.RepositorySyncDefinition()
        value.branch = try reader["Branch"].readIfPresent() ?? ""
        value.directory = try reader["Directory"].readIfPresent() ?? ""
        value.parent = try reader["Parent"].readIfPresent() ?? ""
        value.target = try reader["Target"].readIfPresent() ?? ""
        return value
    }
}

public enum CodeStarconnectionsClientTypes {}
