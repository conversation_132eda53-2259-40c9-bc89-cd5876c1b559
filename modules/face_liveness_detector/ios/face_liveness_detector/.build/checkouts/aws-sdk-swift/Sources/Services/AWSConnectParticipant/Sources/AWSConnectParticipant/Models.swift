//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import class SmithyHT<PERSON>API.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct SmithyHTTPAPI.Header
import struct SmithyHTTPAPI.Headers
@_spi(SmithyReadWrite) import struct SmithyReadWrite.WritingClosureBox

/// You do not have sufficient access to perform this action.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// This exception occurs when there is an internal failure in the Amazon Connect service.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request was denied due to request throttling.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The input fails to satisfy the constraints specified by Amazon Connect.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct CancelParticipantAuthenticationInput: Swift.Sendable {
    /// The authentication token associated with the participant's connection.
    /// This member is required.
    public var connectionToken: Swift.String?
    /// The sessionId provided in the authenticationInitiated event.
    /// This member is required.
    public var sessionId: Swift.String?

    public init(
        connectionToken: Swift.String? = nil,
        sessionId: Swift.String? = nil
    )
    {
        self.connectionToken = connectionToken
        self.sessionId = sessionId
    }
}

public struct CancelParticipantAuthenticationOutput: Swift.Sendable {

    public init() { }
}

/// The requested operation conflicts with the current state of a service resource associated with the request.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The number of attachments per contact exceeds the quota.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceQuotaExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct CompleteAttachmentUploadInput: Swift.Sendable {
    /// A list of unique identifiers for the attachments.
    /// This member is required.
    public var attachmentIds: [Swift.String]?
    /// A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If not provided, the Amazon Web Services SDK populates this field. For more information about idempotency, see [Making retries safe with idempotent APIs](https://aws.amazon.com/builders-library/making-retries-safe-with-idempotent-APIs/).
    /// This member is required.
    public var clientToken: Swift.String?
    /// The authentication token associated with the participant's connection.
    /// This member is required.
    public var connectionToken: Swift.String?

    public init(
        attachmentIds: [Swift.String]? = nil,
        clientToken: Swift.String? = nil,
        connectionToken: Swift.String? = nil
    )
    {
        self.attachmentIds = attachmentIds
        self.clientToken = clientToken
        self.connectionToken = connectionToken
    }
}

public struct CompleteAttachmentUploadOutput: Swift.Sendable {

    public init() { }
}

extension ConnectParticipantClientTypes {

    public enum ConnectionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case connectionCredentials
        case websocket
        case sdkUnknown(Swift.String)

        public static var allCases: [ConnectionType] {
            return [
                .connectionCredentials,
                .websocket
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .connectionCredentials: return "CONNECTION_CREDENTIALS"
            case .websocket: return "WEBSOCKET"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct CreateParticipantConnectionInput: Swift.Sendable {
    /// Amazon Connect Participant is used to mark the participant as connected for customer participant in message streaming, as well as for agent or manager participant in non-streaming chats.
    public var connectParticipant: Swift.Bool?
    /// This is a header parameter. The ParticipantToken as obtained from [StartChatContact](https://docs.aws.amazon.com/connect/latest/APIReference/API_StartChatContact.html) API response.
    /// This member is required.
    public var participantToken: Swift.String?
    /// Type of connection information required. If you need CONNECTION_CREDENTIALS along with marking participant as connected, pass CONNECTION_CREDENTIALS in Type.
    public var type: [ConnectParticipantClientTypes.ConnectionType]?

    public init(
        connectParticipant: Swift.Bool? = nil,
        participantToken: Swift.String? = nil,
        type: [ConnectParticipantClientTypes.ConnectionType]? = nil
    )
    {
        self.connectParticipant = connectParticipant
        self.participantToken = participantToken
        self.type = type
    }
}

extension ConnectParticipantClientTypes {

    /// Connection credentials.
    public struct ConnectionCredentials: Swift.Sendable {
        /// The connection token.
        public var connectionToken: Swift.String?
        /// The expiration of the token. It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.
        public var expiry: Swift.String?

        public init(
            connectionToken: Swift.String? = nil,
            expiry: Swift.String? = nil
        )
        {
            self.connectionToken = connectionToken
            self.expiry = expiry
        }
    }
}

extension ConnectParticipantClientTypes {

    /// The websocket for the participant's connection.
    public struct Websocket: Swift.Sendable {
        /// The URL expiration timestamp in ISO date format. It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.
        public var connectionExpiry: Swift.String?
        /// The URL of the websocket.
        public var url: Swift.String?

        public init(
            connectionExpiry: Swift.String? = nil,
            url: Swift.String? = nil
        )
        {
            self.connectionExpiry = connectionExpiry
            self.url = url
        }
    }
}

public struct CreateParticipantConnectionOutput: Swift.Sendable {
    /// Creates the participant's connection credentials. The authentication token associated with the participant's connection.
    public var connectionCredentials: ConnectParticipantClientTypes.ConnectionCredentials?
    /// Creates the participant's websocket connection.
    public var websocket: ConnectParticipantClientTypes.Websocket?

    public init(
        connectionCredentials: ConnectParticipantClientTypes.ConnectionCredentials? = nil,
        websocket: ConnectParticipantClientTypes.Websocket? = nil
    )
    {
        self.connectionCredentials = connectionCredentials
        self.websocket = websocket
    }
}

extension ConnectParticipantClientTypes {

    public enum ResourceType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case contact
        case contactFlow
        case hierarchyGroup
        case hierarchyLevel
        case instance
        case participant
        case phoneNumber
        case user
        case sdkUnknown(Swift.String)

        public static var allCases: [ResourceType] {
            return [
                .contact,
                .contactFlow,
                .hierarchyGroup,
                .hierarchyLevel,
                .instance,
                .participant,
                .phoneNumber,
                .user
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .contact: return "CONTACT"
            case .contactFlow: return "CONTACT_FLOW"
            case .hierarchyGroup: return "HIERARCHY_GROUP"
            case .hierarchyLevel: return "HIERARCHY_LEVEL"
            case .instance: return "INSTANCE"
            case .participant: return "PARTICIPANT"
            case .phoneNumber: return "PHONE_NUMBER"
            case .user: return "USER"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The resource was not found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
        /// The identifier of the resource.
        public internal(set) var resourceId: Swift.String? = nil
        /// The type of Amazon Connect resource.
        public internal(set) var resourceType: ConnectParticipantClientTypes.ResourceType? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: ConnectParticipantClientTypes.ResourceType? = nil
    )
    {
        self.properties.message = message
        self.properties.resourceId = resourceId
        self.properties.resourceType = resourceType
    }
}

public struct DescribeViewInput: Swift.Sendable {
    /// The connection token.
    /// This member is required.
    public var connectionToken: Swift.String?
    /// An encrypted token originating from the interactive message of a ShowView block operation. Represents the desired view.
    /// This member is required.
    public var viewToken: Swift.String?

    public init(
        connectionToken: Swift.String? = nil,
        viewToken: Swift.String? = nil
    )
    {
        self.connectionToken = connectionToken
        self.viewToken = viewToken
    }
}

extension ConnectParticipantClientTypes {

    /// View content containing all content necessary to render a view except for runtime input data.
    public struct ViewContent: Swift.Sendable {
        /// A list of actions possible from the view
        public var actions: [Swift.String]?
        /// The schema representing the input data that the view template must be supplied to render.
        public var inputSchema: Swift.String?
        /// The view template representing the structure of the view.
        public var template: Swift.String?

        public init(
            actions: [Swift.String]? = nil,
            inputSchema: Swift.String? = nil,
            template: Swift.String? = nil
        )
        {
            self.actions = actions
            self.inputSchema = inputSchema
            self.template = template
        }
    }
}

extension ConnectParticipantClientTypes.ViewContent: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ViewContent(actions: \"CONTENT_REDACTED\", inputSchema: \"CONTENT_REDACTED\", template: \"CONTENT_REDACTED\")"}
}

extension ConnectParticipantClientTypes {

    /// A view resource object. Contains metadata and content necessary to render the view.
    public struct View: Swift.Sendable {
        /// The Amazon Resource Name (ARN) of the view.
        public var arn: Swift.String?
        /// View content containing all content necessary to render a view except for runtime input data.
        public var content: ConnectParticipantClientTypes.ViewContent?
        /// The identifier of the view.
        public var id: Swift.String?
        /// The name of the view.
        public var name: Swift.String?
        /// The current version of the view.
        public var version: Swift.Int?

        public init(
            arn: Swift.String? = nil,
            content: ConnectParticipantClientTypes.ViewContent? = nil,
            id: Swift.String? = nil,
            name: Swift.String? = nil,
            version: Swift.Int? = nil
        )
        {
            self.arn = arn
            self.content = content
            self.id = id
            self.name = name
            self.version = version
        }
    }
}

extension ConnectParticipantClientTypes.View: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "View(arn: \(Swift.String(describing: arn)), content: \(Swift.String(describing: content)), id: \(Swift.String(describing: id)), version: \(Swift.String(describing: version)), name: \"CONTENT_REDACTED\")"}
}

public struct DescribeViewOutput: Swift.Sendable {
    /// A view resource object. Contains metadata and content necessary to render the view.
    public var view: ConnectParticipantClientTypes.View?

    public init(
        view: ConnectParticipantClientTypes.View? = nil
    )
    {
        self.view = view
    }
}

public struct DisconnectParticipantInput: Swift.Sendable {
    /// A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If not provided, the Amazon Web Services SDK populates this field. For more information about idempotency, see [Making retries safe with idempotent APIs](https://aws.amazon.com/builders-library/making-retries-safe-with-idempotent-APIs/).
    public var clientToken: Swift.String?
    /// The authentication token associated with the participant's connection.
    /// This member is required.
    public var connectionToken: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        connectionToken: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.connectionToken = connectionToken
    }
}

public struct DisconnectParticipantOutput: Swift.Sendable {

    public init() { }
}

public struct GetAttachmentInput: Swift.Sendable {
    /// A unique identifier for the attachment.
    /// This member is required.
    public var attachmentId: Swift.String?
    /// The authentication token associated with the participant's connection.
    /// This member is required.
    public var connectionToken: Swift.String?
    /// The expiration time of the URL in ISO timestamp. It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.
    public var urlExpiryInSeconds: Swift.Int?

    public init(
        attachmentId: Swift.String? = nil,
        connectionToken: Swift.String? = nil,
        urlExpiryInSeconds: Swift.Int? = nil
    )
    {
        self.attachmentId = attachmentId
        self.connectionToken = connectionToken
        self.urlExpiryInSeconds = urlExpiryInSeconds
    }
}

public struct GetAttachmentOutput: Swift.Sendable {
    /// The size of the attachment in bytes.
    /// This member is required.
    public var attachmentSizeInBytes: Swift.Int?
    /// This is the pre-signed URL that can be used for uploading the file to Amazon S3 when used in response to [StartAttachmentUpload](https://docs.aws.amazon.com/connect-participant/latest/APIReference/API_StartAttachmentUpload.html).
    public var url: Swift.String?
    /// The expiration time of the URL in ISO timestamp. It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.
    public var urlExpiry: Swift.String?

    public init(
        attachmentSizeInBytes: Swift.Int? = 0,
        url: Swift.String? = nil,
        urlExpiry: Swift.String? = nil
    )
    {
        self.attachmentSizeInBytes = attachmentSizeInBytes
        self.url = url
        self.urlExpiry = urlExpiry
    }
}

public struct GetAuthenticationUrlInput: Swift.Sendable {
    /// The authentication token associated with the participant's connection.
    /// This member is required.
    public var connectionToken: Swift.String?
    /// The URL where the customer will be redirected after Amazon Cognito authorizes the user.
    /// This member is required.
    public var redirectUri: Swift.String?
    /// The sessionId provided in the authenticationInitiated event.
    /// This member is required.
    public var sessionId: Swift.String?

    public init(
        connectionToken: Swift.String? = nil,
        redirectUri: Swift.String? = nil,
        sessionId: Swift.String? = nil
    )
    {
        self.connectionToken = connectionToken
        self.redirectUri = redirectUri
        self.sessionId = sessionId
    }
}

public struct GetAuthenticationUrlOutput: Swift.Sendable {
    /// The URL where the customer will sign in to the identity provider. This URL contains the authorize endpoint for the Cognito UserPool used in the authentication.
    public var authenticationUrl: Swift.String?

    public init(
        authenticationUrl: Swift.String? = nil
    )
    {
        self.authenticationUrl = authenticationUrl
    }
}

extension ConnectParticipantClientTypes {

    public enum ScanDirection: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case backward
        case forward
        case sdkUnknown(Swift.String)

        public static var allCases: [ScanDirection] {
            return [
                .backward,
                .forward
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .backward: return "BACKWARD"
            case .forward: return "FORWARD"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectParticipantClientTypes {

    public enum SortKey: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case ascending
        case descending
        case sdkUnknown(Swift.String)

        public static var allCases: [SortKey] {
            return [
                .ascending,
                .descending
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .ascending: return "ASCENDING"
            case .descending: return "DESCENDING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectParticipantClientTypes {

    /// A filtering option for where to start. For example, if you sent 100 messages, start with message 50.
    public struct StartPosition: Swift.Sendable {
        /// The time in ISO format where to start. It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.
        public var absoluteTime: Swift.String?
        /// The ID of the message or event where to start.
        public var id: Swift.String?
        /// The start position of the most recent message where you want to start.
        public var mostRecent: Swift.Int

        public init(
            absoluteTime: Swift.String? = nil,
            id: Swift.String? = nil,
            mostRecent: Swift.Int = 0
        )
        {
            self.absoluteTime = absoluteTime
            self.id = id
            self.mostRecent = mostRecent
        }
    }
}

public struct GetTranscriptInput: Swift.Sendable {
    /// The authentication token associated with the participant's connection.
    /// This member is required.
    public var connectionToken: Swift.String?
    /// The contactId from the current contact chain for which transcript is needed.
    public var contactId: Swift.String?
    /// The maximum number of results to return in the page. Default: 10.
    public var maxResults: Swift.Int?
    /// The pagination token. Use the value returned previously in the next subsequent request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// The direction from StartPosition from which to retrieve message. Default: BACKWARD when no StartPosition is provided, FORWARD with StartPosition.
    public var scanDirection: ConnectParticipantClientTypes.ScanDirection?
    /// The sort order for the records. Default: DESCENDING.
    public var sortOrder: ConnectParticipantClientTypes.SortKey?
    /// A filtering option for where to start.
    public var startPosition: ConnectParticipantClientTypes.StartPosition?

    public init(
        connectionToken: Swift.String? = nil,
        contactId: Swift.String? = nil,
        maxResults: Swift.Int? = 0,
        nextToken: Swift.String? = nil,
        scanDirection: ConnectParticipantClientTypes.ScanDirection? = nil,
        sortOrder: ConnectParticipantClientTypes.SortKey? = nil,
        startPosition: ConnectParticipantClientTypes.StartPosition? = nil
    )
    {
        self.connectionToken = connectionToken
        self.contactId = contactId
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.scanDirection = scanDirection
        self.sortOrder = sortOrder
        self.startPosition = startPosition
    }
}

extension ConnectParticipantClientTypes {

    public enum ArtifactStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case approved
        case inProgress
        case rejected
        case sdkUnknown(Swift.String)

        public static var allCases: [ArtifactStatus] {
            return [
                .approved,
                .inProgress,
                .rejected
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .approved: return "APPROVED"
            case .inProgress: return "IN_PROGRESS"
            case .rejected: return "REJECTED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectParticipantClientTypes {

    /// The case-insensitive input to indicate standard MIME type that describes the format of the file that will be uploaded.
    public struct AttachmentItem: Swift.Sendable {
        /// A unique identifier for the attachment.
        public var attachmentId: Swift.String?
        /// A case-sensitive name of the attachment being uploaded.
        public var attachmentName: Swift.String?
        /// Describes the MIME file type of the attachment. For a list of supported file types, see [Feature specifications](https://docs.aws.amazon.com/connect/latest/adminguide/feature-limits.html) in the Amazon Connect Administrator Guide.
        public var contentType: Swift.String?
        /// Status of the attachment.
        public var status: ConnectParticipantClientTypes.ArtifactStatus?

        public init(
            attachmentId: Swift.String? = nil,
            attachmentName: Swift.String? = nil,
            contentType: Swift.String? = nil,
            status: ConnectParticipantClientTypes.ArtifactStatus? = nil
        )
        {
            self.attachmentId = attachmentId
            self.attachmentName = attachmentName
            self.contentType = contentType
            self.status = status
        }
    }
}

extension ConnectParticipantClientTypes {

    /// The receipt for the message delivered to the recipient.
    public struct Receipt: Swift.Sendable {
        /// The time when the message was delivered to the recipient.
        public var deliveredTimestamp: Swift.String?
        /// The time when the message was read by the recipient.
        public var readTimestamp: Swift.String?
        /// The identifier of the recipient of the message.
        public var recipientParticipantId: Swift.String?

        public init(
            deliveredTimestamp: Swift.String? = nil,
            readTimestamp: Swift.String? = nil,
            recipientParticipantId: Swift.String? = nil
        )
        {
            self.deliveredTimestamp = deliveredTimestamp
            self.readTimestamp = readTimestamp
            self.recipientParticipantId = recipientParticipantId
        }
    }
}

extension ConnectParticipantClientTypes {

    /// Contains metadata related to a message.
    public struct MessageMetadata: Swift.Sendable {
        /// The identifier of the message that contains the metadata information.
        public var messageId: Swift.String?
        /// The list of receipt information for a message for different recipients.
        public var receipts: [ConnectParticipantClientTypes.Receipt]?

        public init(
            messageId: Swift.String? = nil,
            receipts: [ConnectParticipantClientTypes.Receipt]? = nil
        )
        {
            self.messageId = messageId
            self.receipts = receipts
        }
    }
}

extension ConnectParticipantClientTypes {

    public enum ParticipantRole: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case agent
        case customer
        case customBot
        case supervisor
        case system
        case sdkUnknown(Swift.String)

        public static var allCases: [ParticipantRole] {
            return [
                .agent,
                .customer,
                .customBot,
                .supervisor,
                .system
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .agent: return "AGENT"
            case .customer: return "CUSTOMER"
            case .customBot: return "CUSTOM_BOT"
            case .supervisor: return "SUPERVISOR"
            case .system: return "SYSTEM"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectParticipantClientTypes {

    public enum ChatItemType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case attachment
        case chatEnded
        case connectionAck
        case event
        case message
        case messageDelivered
        case messageRead
        case participantJoined
        case participantLeft
        case transferFailed
        case transferSucceeded
        case typing
        case sdkUnknown(Swift.String)

        public static var allCases: [ChatItemType] {
            return [
                .attachment,
                .chatEnded,
                .connectionAck,
                .event,
                .message,
                .messageDelivered,
                .messageRead,
                .participantJoined,
                .participantLeft,
                .transferFailed,
                .transferSucceeded,
                .typing
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .attachment: return "ATTACHMENT"
            case .chatEnded: return "CHAT_ENDED"
            case .connectionAck: return "CONNECTION_ACK"
            case .event: return "EVENT"
            case .message: return "MESSAGE"
            case .messageDelivered: return "MESSAGE_DELIVERED"
            case .messageRead: return "MESSAGE_READ"
            case .participantJoined: return "PARTICIPANT_JOINED"
            case .participantLeft: return "PARTICIPANT_LEFT"
            case .transferFailed: return "TRANSFER_FAILED"
            case .transferSucceeded: return "TRANSFER_SUCCEEDED"
            case .typing: return "TYPING"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ConnectParticipantClientTypes {

    /// An item - message or event - that has been sent.
    public struct Item: Swift.Sendable {
        /// The time when the message or event was sent. It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.
        public var absoluteTime: Swift.String?
        /// Provides information about the attachments.
        public var attachments: [ConnectParticipantClientTypes.AttachmentItem]?
        /// The contactId on which the transcript item was originally sent. This field is populated only when the transcript item is from the current chat session.
        public var contactId: Swift.String?
        /// The content of the message or event.
        public var content: Swift.String?
        /// The type of content of the item.
        public var contentType: Swift.String?
        /// The chat display name of the sender.
        public var displayName: Swift.String?
        /// The ID of the item.
        public var id: Swift.String?
        /// The metadata related to the message. Currently this supports only information related to message receipts.
        public var messageMetadata: ConnectParticipantClientTypes.MessageMetadata?
        /// The ID of the sender in the session.
        public var participantId: Swift.String?
        /// The role of the sender. For example, is it a customer, agent, or system.
        public var participantRole: ConnectParticipantClientTypes.ParticipantRole?
        /// The contactId on which the transcript item was originally sent. This field is only populated for persistent chats when the transcript item is from the past chat session. For more information, see [Enable persistent chat](https://docs.aws.amazon.com/connect/latest/adminguide/chat-persistence.html).
        public var relatedContactId: Swift.String?
        /// Type of the item: message or event.
        public var type: ConnectParticipantClientTypes.ChatItemType?

        public init(
            absoluteTime: Swift.String? = nil,
            attachments: [ConnectParticipantClientTypes.AttachmentItem]? = nil,
            contactId: Swift.String? = nil,
            content: Swift.String? = nil,
            contentType: Swift.String? = nil,
            displayName: Swift.String? = nil,
            id: Swift.String? = nil,
            messageMetadata: ConnectParticipantClientTypes.MessageMetadata? = nil,
            participantId: Swift.String? = nil,
            participantRole: ConnectParticipantClientTypes.ParticipantRole? = nil,
            relatedContactId: Swift.String? = nil,
            type: ConnectParticipantClientTypes.ChatItemType? = nil
        )
        {
            self.absoluteTime = absoluteTime
            self.attachments = attachments
            self.contactId = contactId
            self.content = content
            self.contentType = contentType
            self.displayName = displayName
            self.id = id
            self.messageMetadata = messageMetadata
            self.participantId = participantId
            self.participantRole = participantRole
            self.relatedContactId = relatedContactId
            self.type = type
        }
    }
}

public struct GetTranscriptOutput: Swift.Sendable {
    /// The initial contact ID for the contact.
    public var initialContactId: Swift.String?
    /// The pagination token. Use the value returned previously in the next subsequent request to retrieve the next set of results.
    public var nextToken: Swift.String?
    /// The list of messages in the session.
    public var transcript: [ConnectParticipantClientTypes.Item]?

    public init(
        initialContactId: Swift.String? = nil,
        nextToken: Swift.String? = nil,
        transcript: [ConnectParticipantClientTypes.Item]? = nil
    )
    {
        self.initialContactId = initialContactId
        self.nextToken = nextToken
        self.transcript = transcript
    }
}

public struct SendEventInput: Swift.Sendable {
    /// A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If not provided, the Amazon Web Services SDK populates this field. For more information about idempotency, see [Making retries safe with idempotent APIs](https://aws.amazon.com/builders-library/making-retries-safe-with-idempotent-APIs/).
    public var clientToken: Swift.String?
    /// The authentication token associated with the participant's connection.
    /// This member is required.
    public var connectionToken: Swift.String?
    /// The content of the event to be sent (for example, message text). For content related to message receipts, this is supported in the form of a JSON string. Sample Content: "{\"messageId\":\"11111111-aaaa-bbbb-cccc-EXAMPLE01234\"}"
    public var content: Swift.String?
    /// The content type of the request. Supported types are:
    ///
    /// * application/vnd.amazonaws.connect.event.typing
    ///
    /// * application/vnd.amazonaws.connect.event.connection.acknowledged (will be deprecated on December 31, 2024)
    ///
    /// * application/vnd.amazonaws.connect.event.message.delivered
    ///
    /// * application/vnd.amazonaws.connect.event.message.read
    /// This member is required.
    public var contentType: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        connectionToken: Swift.String? = nil,
        content: Swift.String? = nil,
        contentType: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.connectionToken = connectionToken
        self.content = content
        self.contentType = contentType
    }
}

public struct SendEventOutput: Swift.Sendable {
    /// The time when the event was sent. It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.
    public var absoluteTime: Swift.String?
    /// The ID of the response.
    public var id: Swift.String?

    public init(
        absoluteTime: Swift.String? = nil,
        id: Swift.String? = nil
    )
    {
        self.absoluteTime = absoluteTime
        self.id = id
    }
}

public struct SendMessageInput: Swift.Sendable {
    /// A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If not provided, the Amazon Web Services SDK populates this field. For more information about idempotency, see [Making retries safe with idempotent APIs](https://aws.amazon.com/builders-library/making-retries-safe-with-idempotent-APIs/).
    public var clientToken: Swift.String?
    /// The authentication token associated with the connection.
    /// This member is required.
    public var connectionToken: Swift.String?
    /// The content of the message.
    ///
    /// * For text/plain and text/markdown, the Length Constraints are Minimum of 1, Maximum of 1024.
    ///
    /// * For application/json, the Length Constraints are Minimum of 1, Maximum of 12000.
    ///
    /// * For application/vnd.amazonaws.connect.message.interactive.response, the Length Constraints are Minimum of 1, Maximum of 12288.
    /// This member is required.
    public var content: Swift.String?
    /// The type of the content. Supported types are text/plain, text/markdown, application/json, and application/vnd.amazonaws.connect.message.interactive.response.
    /// This member is required.
    public var contentType: Swift.String?

    public init(
        clientToken: Swift.String? = nil,
        connectionToken: Swift.String? = nil,
        content: Swift.String? = nil,
        contentType: Swift.String? = nil
    )
    {
        self.clientToken = clientToken
        self.connectionToken = connectionToken
        self.content = content
        self.contentType = contentType
    }
}

public struct SendMessageOutput: Swift.Sendable {
    /// The time when the message was sent. It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.
    public var absoluteTime: Swift.String?
    /// The ID of the message.
    public var id: Swift.String?

    public init(
        absoluteTime: Swift.String? = nil,
        id: Swift.String? = nil
    )
    {
        self.absoluteTime = absoluteTime
        self.id = id
    }
}

public struct StartAttachmentUploadInput: Swift.Sendable {
    /// A case-sensitive name of the attachment being uploaded.
    /// This member is required.
    public var attachmentName: Swift.String?
    /// The size of the attachment in bytes.
    /// This member is required.
    public var attachmentSizeInBytes: Swift.Int?
    /// A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If not provided, the Amazon Web Services SDK populates this field. For more information about idempotency, see [Making retries safe with idempotent APIs](https://aws.amazon.com/builders-library/making-retries-safe-with-idempotent-APIs/).
    /// This member is required.
    public var clientToken: Swift.String?
    /// The authentication token associated with the participant's connection.
    /// This member is required.
    public var connectionToken: Swift.String?
    /// Describes the MIME file type of the attachment. For a list of supported file types, see [Feature specifications](https://docs.aws.amazon.com/connect/latest/adminguide/feature-limits.html) in the Amazon Connect Administrator Guide.
    /// This member is required.
    public var contentType: Swift.String?

    public init(
        attachmentName: Swift.String? = nil,
        attachmentSizeInBytes: Swift.Int? = 0,
        clientToken: Swift.String? = nil,
        connectionToken: Swift.String? = nil,
        contentType: Swift.String? = nil
    )
    {
        self.attachmentName = attachmentName
        self.attachmentSizeInBytes = attachmentSizeInBytes
        self.clientToken = clientToken
        self.connectionToken = connectionToken
        self.contentType = contentType
    }
}

extension ConnectParticipantClientTypes {

    /// Fields to be used while uploading the attachment.
    public struct UploadMetadata: Swift.Sendable {
        /// The headers to be provided while uploading the file to the URL.
        public var headersToInclude: [Swift.String: Swift.String]?
        /// This is the pre-signed URL that can be used for uploading the file to Amazon S3 when used in response to [StartAttachmentUpload](https://docs.aws.amazon.com/connect-participant/latest/APIReference/API_StartAttachmentUpload.html).
        public var url: Swift.String?
        /// The expiration time of the URL in ISO timestamp. It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.
        public var urlExpiry: Swift.String?

        public init(
            headersToInclude: [Swift.String: Swift.String]? = nil,
            url: Swift.String? = nil,
            urlExpiry: Swift.String? = nil
        )
        {
            self.headersToInclude = headersToInclude
            self.url = url
            self.urlExpiry = urlExpiry
        }
    }
}

public struct StartAttachmentUploadOutput: Swift.Sendable {
    /// A unique identifier for the attachment.
    public var attachmentId: Swift.String?
    /// The headers to be provided while uploading the file to the URL.
    public var uploadMetadata: ConnectParticipantClientTypes.UploadMetadata?

    public init(
        attachmentId: Swift.String? = nil,
        uploadMetadata: ConnectParticipantClientTypes.UploadMetadata? = nil
    )
    {
        self.attachmentId = attachmentId
        self.uploadMetadata = uploadMetadata
    }
}

extension CancelParticipantAuthenticationInput {

    static func urlPathProvider(_ value: CancelParticipantAuthenticationInput) -> Swift.String? {
        return "/participant/cancel-authentication"
    }
}

extension CancelParticipantAuthenticationInput {

    static func headerProvider(_ value: CancelParticipantAuthenticationInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let connectionToken = value.connectionToken {
            items.add(SmithyHTTPAPI.Header(name: "X-Amz-Bearer", value: Swift.String(connectionToken)))
        }
        return items
    }
}

extension CompleteAttachmentUploadInput {

    static func urlPathProvider(_ value: CompleteAttachmentUploadInput) -> Swift.String? {
        return "/participant/complete-attachment-upload"
    }
}

extension CompleteAttachmentUploadInput {

    static func headerProvider(_ value: CompleteAttachmentUploadInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let connectionToken = value.connectionToken {
            items.add(SmithyHTTPAPI.Header(name: "X-Amz-Bearer", value: Swift.String(connectionToken)))
        }
        return items
    }
}

extension CreateParticipantConnectionInput {

    static func urlPathProvider(_ value: CreateParticipantConnectionInput) -> Swift.String? {
        return "/participant/connection"
    }
}

extension CreateParticipantConnectionInput {

    static func headerProvider(_ value: CreateParticipantConnectionInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let participantToken = value.participantToken {
            items.add(SmithyHTTPAPI.Header(name: "X-Amz-Bearer", value: Swift.String(participantToken)))
        }
        return items
    }
}

extension DescribeViewInput {

    static func urlPathProvider(_ value: DescribeViewInput) -> Swift.String? {
        guard let viewToken = value.viewToken else {
            return nil
        }
        return "/participant/views/\(viewToken.urlPercentEncoding())"
    }
}

extension DescribeViewInput {

    static func headerProvider(_ value: DescribeViewInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let connectionToken = value.connectionToken {
            items.add(SmithyHTTPAPI.Header(name: "X-Amz-Bearer", value: Swift.String(connectionToken)))
        }
        return items
    }
}

extension DisconnectParticipantInput {

    static func urlPathProvider(_ value: DisconnectParticipantInput) -> Swift.String? {
        return "/participant/disconnect"
    }
}

extension DisconnectParticipantInput {

    static func headerProvider(_ value: DisconnectParticipantInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let connectionToken = value.connectionToken {
            items.add(SmithyHTTPAPI.Header(name: "X-Amz-Bearer", value: Swift.String(connectionToken)))
        }
        return items
    }
}

extension GetAttachmentInput {

    static func urlPathProvider(_ value: GetAttachmentInput) -> Swift.String? {
        return "/participant/attachment"
    }
}

extension GetAttachmentInput {

    static func headerProvider(_ value: GetAttachmentInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let connectionToken = value.connectionToken {
            items.add(SmithyHTTPAPI.Header(name: "X-Amz-Bearer", value: Swift.String(connectionToken)))
        }
        return items
    }
}

extension GetAuthenticationUrlInput {

    static func urlPathProvider(_ value: GetAuthenticationUrlInput) -> Swift.String? {
        return "/participant/authentication-url"
    }
}

extension GetAuthenticationUrlInput {

    static func headerProvider(_ value: GetAuthenticationUrlInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let connectionToken = value.connectionToken {
            items.add(SmithyHTTPAPI.Header(name: "X-Amz-Bearer", value: Swift.String(connectionToken)))
        }
        return items
    }
}

extension GetTranscriptInput {

    static func urlPathProvider(_ value: GetTranscriptInput) -> Swift.String? {
        return "/participant/transcript"
    }
}

extension GetTranscriptInput {

    static func headerProvider(_ value: GetTranscriptInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let connectionToken = value.connectionToken {
            items.add(SmithyHTTPAPI.Header(name: "X-Amz-Bearer", value: Swift.String(connectionToken)))
        }
        return items
    }
}

extension SendEventInput {

    static func urlPathProvider(_ value: SendEventInput) -> Swift.String? {
        return "/participant/event"
    }
}

extension SendEventInput {

    static func headerProvider(_ value: SendEventInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let connectionToken = value.connectionToken {
            items.add(SmithyHTTPAPI.Header(name: "X-Amz-Bearer", value: Swift.String(connectionToken)))
        }
        return items
    }
}

extension SendMessageInput {

    static func urlPathProvider(_ value: SendMessageInput) -> Swift.String? {
        return "/participant/message"
    }
}

extension SendMessageInput {

    static func headerProvider(_ value: SendMessageInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let connectionToken = value.connectionToken {
            items.add(SmithyHTTPAPI.Header(name: "X-Amz-Bearer", value: Swift.String(connectionToken)))
        }
        return items
    }
}

extension StartAttachmentUploadInput {

    static func urlPathProvider(_ value: StartAttachmentUploadInput) -> Swift.String? {
        return "/participant/start-attachment-upload"
    }
}

extension StartAttachmentUploadInput {

    static func headerProvider(_ value: StartAttachmentUploadInput) -> SmithyHTTPAPI.Headers {
        var items = SmithyHTTPAPI.Headers()
        if let connectionToken = value.connectionToken {
            items.add(SmithyHTTPAPI.Header(name: "X-Amz-Bearer", value: Swift.String(connectionToken)))
        }
        return items
    }
}

extension CancelParticipantAuthenticationInput {

    static func write(value: CancelParticipantAuthenticationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["SessionId"].write(value.sessionId)
    }
}

extension CompleteAttachmentUploadInput {

    static func write(value: CompleteAttachmentUploadInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AttachmentIds"].writeList(value.attachmentIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["ClientToken"].write(value.clientToken)
    }
}

extension CreateParticipantConnectionInput {

    static func write(value: CreateParticipantConnectionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ConnectParticipant"].write(value.connectParticipant)
        try writer["Type"].writeList(value.type, memberWritingClosure: SmithyReadWrite.WritingClosureBox<ConnectParticipantClientTypes.ConnectionType>().write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DisconnectParticipantInput {

    static func write(value: DisconnectParticipantInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
    }
}

extension GetAttachmentInput {

    static func write(value: GetAttachmentInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AttachmentId"].write(value.attachmentId)
        try writer["UrlExpiryInSeconds"].write(value.urlExpiryInSeconds)
    }
}

extension GetAuthenticationUrlInput {

    static func write(value: GetAuthenticationUrlInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["RedirectUri"].write(value.redirectUri)
        try writer["SessionId"].write(value.sessionId)
    }
}

extension GetTranscriptInput {

    static func write(value: GetTranscriptInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ContactId"].write(value.contactId)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
        try writer["ScanDirection"].write(value.scanDirection)
        try writer["SortOrder"].write(value.sortOrder)
        try writer["StartPosition"].write(value.startPosition, with: ConnectParticipantClientTypes.StartPosition.write(value:to:))
    }
}

extension SendEventInput {

    static func write(value: SendEventInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["Content"].write(value.content)
        try writer["ContentType"].write(value.contentType)
    }
}

extension SendMessageInput {

    static func write(value: SendMessageInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientToken"].write(value.clientToken)
        try writer["Content"].write(value.content)
        try writer["ContentType"].write(value.contentType)
    }
}

extension StartAttachmentUploadInput {

    static func write(value: StartAttachmentUploadInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AttachmentName"].write(value.attachmentName)
        try writer["AttachmentSizeInBytes"].write(value.attachmentSizeInBytes)
        try writer["ClientToken"].write(value.clientToken)
        try writer["ContentType"].write(value.contentType)
    }
}

extension CancelParticipantAuthenticationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CancelParticipantAuthenticationOutput {
        return CancelParticipantAuthenticationOutput()
    }
}

extension CompleteAttachmentUploadOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CompleteAttachmentUploadOutput {
        return CompleteAttachmentUploadOutput()
    }
}

extension CreateParticipantConnectionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateParticipantConnectionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateParticipantConnectionOutput()
        value.connectionCredentials = try reader["ConnectionCredentials"].readIfPresent(with: ConnectParticipantClientTypes.ConnectionCredentials.read(from:))
        value.websocket = try reader["Websocket"].readIfPresent(with: ConnectParticipantClientTypes.Websocket.read(from:))
        return value
    }
}

extension DescribeViewOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeViewOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeViewOutput()
        value.view = try reader["View"].readIfPresent(with: ConnectParticipantClientTypes.View.read(from:))
        return value
    }
}

extension DisconnectParticipantOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisconnectParticipantOutput {
        return DisconnectParticipantOutput()
    }
}

extension GetAttachmentOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetAttachmentOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetAttachmentOutput()
        value.attachmentSizeInBytes = try reader["AttachmentSizeInBytes"].readIfPresent()
        value.url = try reader["Url"].readIfPresent()
        value.urlExpiry = try reader["UrlExpiry"].readIfPresent()
        return value
    }
}

extension GetAuthenticationUrlOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetAuthenticationUrlOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetAuthenticationUrlOutput()
        value.authenticationUrl = try reader["AuthenticationUrl"].readIfPresent()
        return value
    }
}

extension GetTranscriptOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetTranscriptOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetTranscriptOutput()
        value.initialContactId = try reader["InitialContactId"].readIfPresent()
        value.nextToken = try reader["NextToken"].readIfPresent()
        value.transcript = try reader["Transcript"].readListIfPresent(memberReadingClosure: ConnectParticipantClientTypes.Item.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension SendEventOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SendEventOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = SendEventOutput()
        value.absoluteTime = try reader["AbsoluteTime"].readIfPresent()
        value.id = try reader["Id"].readIfPresent()
        return value
    }
}

extension SendMessageOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SendMessageOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = SendMessageOutput()
        value.absoluteTime = try reader["AbsoluteTime"].readIfPresent()
        value.id = try reader["Id"].readIfPresent()
        return value
    }
}

extension StartAttachmentUploadOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartAttachmentUploadOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartAttachmentUploadOutput()
        value.attachmentId = try reader["AttachmentId"].readIfPresent()
        value.uploadMetadata = try reader["UploadMetadata"].readIfPresent(with: ConnectParticipantClientTypes.UploadMetadata.read(from:))
        return value
    }
}

enum CancelParticipantAuthenticationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CompleteAttachmentUploadOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateParticipantConnectionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeViewOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisconnectParticipantOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetAttachmentOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetAuthenticationUrlOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetTranscriptOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SendEventOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SendMessageOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartAttachmentUploadOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        var value = ThrottlingException()
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceQuotaExceededException {
        let reader = baseError.errorBodyReader
        var value = ServiceQuotaExceededException()
        value.properties.message = try reader["Message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.properties.resourceId = try reader["ResourceId"].readIfPresent()
        value.properties.resourceType = try reader["ResourceType"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConnectParticipantClientTypes.Websocket {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectParticipantClientTypes.Websocket {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectParticipantClientTypes.Websocket()
        value.url = try reader["Url"].readIfPresent()
        value.connectionExpiry = try reader["ConnectionExpiry"].readIfPresent()
        return value
    }
}

extension ConnectParticipantClientTypes.ConnectionCredentials {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectParticipantClientTypes.ConnectionCredentials {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectParticipantClientTypes.ConnectionCredentials()
        value.connectionToken = try reader["ConnectionToken"].readIfPresent()
        value.expiry = try reader["Expiry"].readIfPresent()
        return value
    }
}

extension ConnectParticipantClientTypes.View {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectParticipantClientTypes.View {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectParticipantClientTypes.View()
        value.id = try reader["Id"].readIfPresent()
        value.arn = try reader["Arn"].readIfPresent()
        value.name = try reader["Name"].readIfPresent()
        value.version = try reader["Version"].readIfPresent()
        value.content = try reader["Content"].readIfPresent(with: ConnectParticipantClientTypes.ViewContent.read(from:))
        return value
    }
}

extension ConnectParticipantClientTypes.ViewContent {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectParticipantClientTypes.ViewContent {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectParticipantClientTypes.ViewContent()
        value.inputSchema = try reader["InputSchema"].readIfPresent()
        value.template = try reader["Template"].readIfPresent()
        value.actions = try reader["Actions"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ConnectParticipantClientTypes.Item {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectParticipantClientTypes.Item {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectParticipantClientTypes.Item()
        value.absoluteTime = try reader["AbsoluteTime"].readIfPresent()
        value.content = try reader["Content"].readIfPresent()
        value.contentType = try reader["ContentType"].readIfPresent()
        value.id = try reader["Id"].readIfPresent()
        value.type = try reader["Type"].readIfPresent()
        value.participantId = try reader["ParticipantId"].readIfPresent()
        value.displayName = try reader["DisplayName"].readIfPresent()
        value.participantRole = try reader["ParticipantRole"].readIfPresent()
        value.attachments = try reader["Attachments"].readListIfPresent(memberReadingClosure: ConnectParticipantClientTypes.AttachmentItem.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.messageMetadata = try reader["MessageMetadata"].readIfPresent(with: ConnectParticipantClientTypes.MessageMetadata.read(from:))
        value.relatedContactId = try reader["RelatedContactId"].readIfPresent()
        value.contactId = try reader["ContactId"].readIfPresent()
        return value
    }
}

extension ConnectParticipantClientTypes.MessageMetadata {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectParticipantClientTypes.MessageMetadata {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectParticipantClientTypes.MessageMetadata()
        value.messageId = try reader["MessageId"].readIfPresent()
        value.receipts = try reader["Receipts"].readListIfPresent(memberReadingClosure: ConnectParticipantClientTypes.Receipt.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ConnectParticipantClientTypes.Receipt {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectParticipantClientTypes.Receipt {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectParticipantClientTypes.Receipt()
        value.deliveredTimestamp = try reader["DeliveredTimestamp"].readIfPresent()
        value.readTimestamp = try reader["ReadTimestamp"].readIfPresent()
        value.recipientParticipantId = try reader["RecipientParticipantId"].readIfPresent()
        return value
    }
}

extension ConnectParticipantClientTypes.AttachmentItem {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectParticipantClientTypes.AttachmentItem {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectParticipantClientTypes.AttachmentItem()
        value.contentType = try reader["ContentType"].readIfPresent()
        value.attachmentId = try reader["AttachmentId"].readIfPresent()
        value.attachmentName = try reader["AttachmentName"].readIfPresent()
        value.status = try reader["Status"].readIfPresent()
        return value
    }
}

extension ConnectParticipantClientTypes.UploadMetadata {

    static func read(from reader: SmithyJSON.Reader) throws -> ConnectParticipantClientTypes.UploadMetadata {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ConnectParticipantClientTypes.UploadMetadata()
        value.url = try reader["Url"].readIfPresent()
        value.urlExpiry = try reader["UrlExpiry"].readIfPresent()
        value.headersToInclude = try reader["HeadersToInclude"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension ConnectParticipantClientTypes.StartPosition {

    static func write(value: ConnectParticipantClientTypes.StartPosition?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AbsoluteTime"].write(value.absoluteTime)
        try writer["Id"].write(value.id)
        try writer["MostRecent"].write(value.mostRecent)
    }
}

public enum ConnectParticipantClientTypes {}
