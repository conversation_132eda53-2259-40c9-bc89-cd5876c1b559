//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class Smithy<PERSON><PERSON><PERSON>I.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.AWSJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError

extension ComprehendMedicalClientTypes {

    public enum EntityType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case anatomy
        case behavioralEnvironmentalSocial
        case medicalCondition
        case medication
        case protectedHealthInformation
        case testTreatmentProcedure
        case timeExpression
        case sdkUnknown(Swift.String)

        public static var allCases: [EntityType] {
            return [
                .anatomy,
                .behavioralEnvironmentalSocial,
                .medicalCondition,
                .medication,
                .protectedHealthInformation,
                .testTreatmentProcedure,
                .timeExpression
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .anatomy: return "ANATOMY"
            case .behavioralEnvironmentalSocial: return "BEHAVIORAL_ENVIRONMENTAL_SOCIAL"
            case .medicalCondition: return "MEDICAL_CONDITION"
            case .medication: return "MEDICATION"
            case .protectedHealthInformation: return "PROTECTED_HEALTH_INFORMATION"
            case .testTreatmentProcedure: return "TEST_TREATMENT_PROCEDURE"
            case .timeExpression: return "TIME_EXPRESSION"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum RelationshipType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case acuity
        case administeredVia
        case amount
        case direction
        case dosage
        case duration
        case every
        case `for`
        case form
        case frequency
        case negative
        case overlap
        case quality
        case rate
        case routeOrMode
        case strength
        case systemOrganSite
        case testUnit
        case testUnits
        case testValue
        case usage
        case withDosage
        case sdkUnknown(Swift.String)

        public static var allCases: [RelationshipType] {
            return [
                .acuity,
                .administeredVia,
                .amount,
                .direction,
                .dosage,
                .duration,
                .every,
                .for,
                .form,
                .frequency,
                .negative,
                .overlap,
                .quality,
                .rate,
                .routeOrMode,
                .strength,
                .systemOrganSite,
                .testUnit,
                .testUnits,
                .testValue,
                .usage,
                .withDosage
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .acuity: return "ACUITY"
            case .administeredVia: return "ADMINISTERED_VIA"
            case .amount: return "AMOUNT"
            case .direction: return "DIRECTION"
            case .dosage: return "DOSAGE"
            case .duration: return "DURATION"
            case .every: return "EVERY"
            case .for: return "FOR"
            case .form: return "FORM"
            case .frequency: return "FREQUENCY"
            case .negative: return "NEGATIVE"
            case .overlap: return "OVERLAP"
            case .quality: return "QUALITY"
            case .rate: return "RATE"
            case .routeOrMode: return "ROUTE_OR_MODE"
            case .strength: return "STRENGTH"
            case .systemOrganSite: return "SYSTEM_ORGAN_SITE"
            case .testUnit: return "TEST_UNIT"
            case .testUnits: return "TEST_UNITS"
            case .testValue: return "TEST_VALUE"
            case .usage: return "USAGE"
            case .withDosage: return "WITH_DOSAGE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum AttributeName: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case diagnosis
        case future
        case hypothetical
        case lowConfidence
        case negation
        case pastHistory
        case pertainsToFamily
        case sign
        case symptom
        case sdkUnknown(Swift.String)

        public static var allCases: [AttributeName] {
            return [
                .diagnosis,
                .future,
                .hypothetical,
                .lowConfidence,
                .negation,
                .pastHistory,
                .pertainsToFamily,
                .sign,
                .symptom
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .diagnosis: return "DIAGNOSIS"
            case .future: return "FUTURE"
            case .hypothetical: return "HYPOTHETICAL"
            case .lowConfidence: return "LOW_CONFIDENCE"
            case .negation: return "NEGATION"
            case .pastHistory: return "PAST_HISTORY"
            case .pertainsToFamily: return "PERTAINS_TO_FAMILY"
            case .sign: return "SIGN"
            case .symptom: return "SYMPTOM"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// Provides contextual information about the extracted entity.
    public struct Trait: Swift.Sendable {
        /// Provides a name or contextual description about the trait.
        public var name: ComprehendMedicalClientTypes.AttributeName?
        /// The level of confidence that Amazon Comprehend Medical has in the accuracy of this trait.
        public var score: Swift.Float?

        public init(
            name: ComprehendMedicalClientTypes.AttributeName? = nil,
            score: Swift.Float? = nil
        )
        {
            self.name = name
            self.score = score
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum EntitySubType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case acuity
        case address
        case age
        case alcoholConsumption
        case allergies
        case amount
        case brandName
        case contactPoint
        case date
        case direction
        case dosage
        case duration
        case dxName
        case email
        case form
        case frequency
        case gender
        case genericName
        case id
        case identifier
        case name
        case phoneOrFax
        case procedureName
        case profession
        case quality
        case quantity
        case raceEthnicity
        case rate
        case recDrugUse
        case routeOrMode
        case strength
        case systemOrganSite
        case testName
        case testUnit
        case testUnits
        case testValue
        case timeExpression
        case timeToDxName
        case timeToMedicationName
        case timeToProcedureName
        case timeToTestName
        case timeToTreatmentName
        case tobaccoUse
        case treatmentName
        case url
        case sdkUnknown(Swift.String)

        public static var allCases: [EntitySubType] {
            return [
                .acuity,
                .address,
                .age,
                .alcoholConsumption,
                .allergies,
                .amount,
                .brandName,
                .contactPoint,
                .date,
                .direction,
                .dosage,
                .duration,
                .dxName,
                .email,
                .form,
                .frequency,
                .gender,
                .genericName,
                .id,
                .identifier,
                .name,
                .phoneOrFax,
                .procedureName,
                .profession,
                .quality,
                .quantity,
                .raceEthnicity,
                .rate,
                .recDrugUse,
                .routeOrMode,
                .strength,
                .systemOrganSite,
                .testName,
                .testUnit,
                .testUnits,
                .testValue,
                .timeExpression,
                .timeToDxName,
                .timeToMedicationName,
                .timeToProcedureName,
                .timeToTestName,
                .timeToTreatmentName,
                .tobaccoUse,
                .treatmentName,
                .url
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .acuity: return "ACUITY"
            case .address: return "ADDRESS"
            case .age: return "AGE"
            case .alcoholConsumption: return "ALCOHOL_CONSUMPTION"
            case .allergies: return "ALLERGIES"
            case .amount: return "AMOUNT"
            case .brandName: return "BRAND_NAME"
            case .contactPoint: return "CONTACT_POINT"
            case .date: return "DATE"
            case .direction: return "DIRECTION"
            case .dosage: return "DOSAGE"
            case .duration: return "DURATION"
            case .dxName: return "DX_NAME"
            case .email: return "EMAIL"
            case .form: return "FORM"
            case .frequency: return "FREQUENCY"
            case .gender: return "GENDER"
            case .genericName: return "GENERIC_NAME"
            case .id: return "ID"
            case .identifier: return "IDENTIFIER"
            case .name: return "NAME"
            case .phoneOrFax: return "PHONE_OR_FAX"
            case .procedureName: return "PROCEDURE_NAME"
            case .profession: return "PROFESSION"
            case .quality: return "QUALITY"
            case .quantity: return "QUANTITY"
            case .raceEthnicity: return "RACE_ETHNICITY"
            case .rate: return "RATE"
            case .recDrugUse: return "REC_DRUG_USE"
            case .routeOrMode: return "ROUTE_OR_MODE"
            case .strength: return "STRENGTH"
            case .systemOrganSite: return "SYSTEM_ORGAN_SITE"
            case .testName: return "TEST_NAME"
            case .testUnit: return "TEST_UNIT"
            case .testUnits: return "TEST_UNITS"
            case .testValue: return "TEST_VALUE"
            case .timeExpression: return "TIME_EXPRESSION"
            case .timeToDxName: return "TIME_TO_DX_NAME"
            case .timeToMedicationName: return "TIME_TO_MEDICATION_NAME"
            case .timeToProcedureName: return "TIME_TO_PROCEDURE_NAME"
            case .timeToTestName: return "TIME_TO_TEST_NAME"
            case .timeToTreatmentName: return "TIME_TO_TREATMENT_NAME"
            case .tobaccoUse: return "TOBACCO_USE"
            case .treatmentName: return "TREATMENT_NAME"
            case .url: return "URL"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// An extracted segment of the text that is an attribute of an entity, or otherwise related to an entity, such as the dosage of a medication taken. It contains information about the attribute such as id, begin and end offset within the input text, and the segment of the input text.
    public struct Attribute: Swift.Sendable {
        /// The 0-based character offset in the input text that shows where the attribute begins. The offset returns the UTF-8 code point in the string.
        public var beginOffset: Swift.Int?
        /// The category of attribute.
        public var category: ComprehendMedicalClientTypes.EntityType?
        /// The 0-based character offset in the input text that shows where the attribute ends. The offset returns the UTF-8 code point in the string.
        public var endOffset: Swift.Int?
        /// The numeric identifier for this attribute. This is a monotonically increasing id unique within this response rather than a global unique identifier.
        public var id: Swift.Int?
        /// The level of confidence that Amazon Comprehend Medical has that this attribute is correctly related to this entity.
        public var relationshipScore: Swift.Float?
        /// The type of relationship between the entity and attribute. Type for the relationship is OVERLAP, indicating that the entity occurred at the same time as the Date_Expression.
        public var relationshipType: ComprehendMedicalClientTypes.RelationshipType?
        /// The level of confidence that Amazon Comprehend Medical has that the segment of text is correctly recognized as an attribute.
        public var score: Swift.Float?
        /// The segment of input text extracted as this attribute.
        public var text: Swift.String?
        /// Contextual information for this attribute.
        public var traits: [ComprehendMedicalClientTypes.Trait]?
        /// The type of attribute.
        public var type: ComprehendMedicalClientTypes.EntitySubType?

        public init(
            beginOffset: Swift.Int? = nil,
            category: ComprehendMedicalClientTypes.EntityType? = nil,
            endOffset: Swift.Int? = nil,
            id: Swift.Int? = nil,
            relationshipScore: Swift.Float? = nil,
            relationshipType: ComprehendMedicalClientTypes.RelationshipType? = nil,
            score: Swift.Float? = nil,
            text: Swift.String? = nil,
            traits: [ComprehendMedicalClientTypes.Trait]? = nil,
            type: ComprehendMedicalClientTypes.EntitySubType? = nil
        )
        {
            self.beginOffset = beginOffset
            self.category = category
            self.endOffset = endOffset
            self.id = id
            self.relationshipScore = relationshipScore
            self.relationshipType = relationshipType
            self.score = score
            self.text = text
            self.traits = traits
            self.type = type
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// The number of characters in the input text to be analyzed.
    public struct Characters: Swift.Sendable {
        /// The number of characters present in the input text document as processed by Amazon Comprehend Medical.
        public var originalTextCharacters: Swift.Int?

        public init(
            originalTextCharacters: Swift.Int? = nil
        )
        {
            self.originalTextCharacters = originalTextCharacters
        }
    }
}

/// An internal server error occurred. Retry your request.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The request that you made is invalid. Check your request to determine why it's invalid and then retry the request.
public struct InvalidRequestException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidRequestException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The resource identified by the specified Amazon Resource Name (ARN) was not found. Check the ARN and try your request again.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// You have made too many requests within a short period of time. Wait for a short time and then try your request again. Contact customer support for more information about a service limit increase.
public struct TooManyRequestsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TooManyRequestsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DescribeEntitiesDetectionV2JobInput: Swift.Sendable {
    /// The identifier that Amazon Comprehend Medical generated for the job. The StartEntitiesDetectionV2Job operation returns this identifier in its response.
    /// This member is required.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

extension ComprehendMedicalClientTypes {

    /// The input properties for an entities detection job. This includes the name of the S3 bucket and the path to the files to be analyzed.
    public struct InputDataConfig: Swift.Sendable {
        /// The URI of the S3 bucket that contains the input data. The bucket must be in the same region as the API endpoint that you are calling.
        /// This member is required.
        public var s3Bucket: Swift.String?
        /// The path to the input data files in the S3 bucket.
        public var s3Key: Swift.String?

        public init(
            s3Bucket: Swift.String? = nil,
            s3Key: Swift.String? = nil
        )
        {
            self.s3Bucket = s3Bucket
            self.s3Key = s3Key
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum JobStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case completed
        case failed
        case inProgress
        case partialSuccess
        case stopped
        case stopRequested
        case submitted
        case sdkUnknown(Swift.String)

        public static var allCases: [JobStatus] {
            return [
                .completed,
                .failed,
                .inProgress,
                .partialSuccess,
                .stopped,
                .stopRequested,
                .submitted
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .completed: return "COMPLETED"
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case .partialSuccess: return "PARTIAL_SUCCESS"
            case .stopped: return "STOPPED"
            case .stopRequested: return "STOP_REQUESTED"
            case .submitted: return "SUBMITTED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum LanguageCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case en
        case sdkUnknown(Swift.String)

        public static var allCases: [LanguageCode] {
            return [
                .en
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .en: return "en"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// The output properties for a detection job.
    public struct OutputDataConfig: Swift.Sendable {
        /// When you use the OutputDataConfig object with asynchronous operations, you specify the Amazon S3 location where you want to write the output data. The URI must be in the same region as the API endpoint that you are calling. The location is used as the prefix for the actual location of the output.
        /// This member is required.
        public var s3Bucket: Swift.String?
        /// The path to the output data files in the S3 bucket. Amazon Comprehend Medical creates an output directory using the job ID so that the output from one job does not overwrite the output of another.
        public var s3Key: Swift.String?

        public init(
            s3Bucket: Swift.String? = nil,
            s3Key: Swift.String? = nil
        )
        {
            self.s3Bucket = s3Bucket
            self.s3Key = s3Key
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// Provides information about a detection job.
    public struct ComprehendMedicalAsyncJobProperties: Swift.Sendable {
        /// The Amazon Resource Name (ARN) that gives Amazon Comprehend Medical read access to your input data.
        public var dataAccessRoleArn: Swift.String?
        /// The time that the detection job completed.
        public var endTime: Foundation.Date?
        /// The date and time that job metadata is deleted from the server. Output files in your S3 bucket will not be deleted. After the metadata is deleted, the job will no longer appear in the results of the ListEntitiesDetectionV2Job or the ListPHIDetectionJobs operation.
        public var expirationTime: Foundation.Date?
        /// The input data configuration that you supplied when you created the detection job.
        public var inputDataConfig: ComprehendMedicalClientTypes.InputDataConfig?
        /// The identifier assigned to the detection job.
        public var jobId: Swift.String?
        /// The name that you assigned to the detection job.
        public var jobName: Swift.String?
        /// The current status of the detection job. If the status is FAILED, the Message field shows the reason for the failure.
        public var jobStatus: ComprehendMedicalClientTypes.JobStatus?
        /// The AWS Key Management Service key, if any, used to encrypt the output files.
        public var kmsKey: Swift.String?
        /// The language code of the input documents.
        public var languageCode: ComprehendMedicalClientTypes.LanguageCode?
        /// The path to the file that describes the results of a batch job.
        public var manifestFilePath: Swift.String?
        /// A description of the status of a job.
        public var message: Swift.String?
        /// The version of the model used to analyze the documents. The version number looks like X.X.X. You can use this information to track the model used for a particular batch of documents.
        public var modelVersion: Swift.String?
        /// The output data configuration that you supplied when you created the detection job.
        public var outputDataConfig: ComprehendMedicalClientTypes.OutputDataConfig?
        /// The time that the detection job was submitted for processing.
        public var submitTime: Foundation.Date?

        public init(
            dataAccessRoleArn: Swift.String? = nil,
            endTime: Foundation.Date? = nil,
            expirationTime: Foundation.Date? = nil,
            inputDataConfig: ComprehendMedicalClientTypes.InputDataConfig? = nil,
            jobId: Swift.String? = nil,
            jobName: Swift.String? = nil,
            jobStatus: ComprehendMedicalClientTypes.JobStatus? = nil,
            kmsKey: Swift.String? = nil,
            languageCode: ComprehendMedicalClientTypes.LanguageCode? = nil,
            manifestFilePath: Swift.String? = nil,
            message: Swift.String? = nil,
            modelVersion: Swift.String? = nil,
            outputDataConfig: ComprehendMedicalClientTypes.OutputDataConfig? = nil,
            submitTime: Foundation.Date? = nil
        )
        {
            self.dataAccessRoleArn = dataAccessRoleArn
            self.endTime = endTime
            self.expirationTime = expirationTime
            self.inputDataConfig = inputDataConfig
            self.jobId = jobId
            self.jobName = jobName
            self.jobStatus = jobStatus
            self.kmsKey = kmsKey
            self.languageCode = languageCode
            self.manifestFilePath = manifestFilePath
            self.message = message
            self.modelVersion = modelVersion
            self.outputDataConfig = outputDataConfig
            self.submitTime = submitTime
        }
    }
}

public struct DescribeEntitiesDetectionV2JobOutput: Swift.Sendable {
    /// An object that contains the properties associated with a detection job.
    public var comprehendMedicalAsyncJobProperties: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties?

    public init(
        comprehendMedicalAsyncJobProperties: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties? = nil
    )
    {
        self.comprehendMedicalAsyncJobProperties = comprehendMedicalAsyncJobProperties
    }
}

public struct DescribeICD10CMInferenceJobInput: Swift.Sendable {
    /// The identifier that Amazon Comprehend Medical generated for the job. The StartICD10CMInferenceJob operation returns this identifier in its response.
    /// This member is required.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct DescribeICD10CMInferenceJobOutput: Swift.Sendable {
    /// An object that contains the properties associated with a detection job.
    public var comprehendMedicalAsyncJobProperties: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties?

    public init(
        comprehendMedicalAsyncJobProperties: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties? = nil
    )
    {
        self.comprehendMedicalAsyncJobProperties = comprehendMedicalAsyncJobProperties
    }
}

public struct DescribePHIDetectionJobInput: Swift.Sendable {
    /// The identifier that Amazon Comprehend Medical generated for the job. The StartPHIDetectionJob operation returns this identifier in its response.
    /// This member is required.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct DescribePHIDetectionJobOutput: Swift.Sendable {
    /// An object that contains the properties associated with a detection job.
    public var comprehendMedicalAsyncJobProperties: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties?

    public init(
        comprehendMedicalAsyncJobProperties: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties? = nil
    )
    {
        self.comprehendMedicalAsyncJobProperties = comprehendMedicalAsyncJobProperties
    }
}

public struct DescribeRxNormInferenceJobInput: Swift.Sendable {
    /// The identifier that Amazon Comprehend Medical generated for the job. The StartRxNormInferenceJob operation returns this identifier in its response.
    /// This member is required.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct DescribeRxNormInferenceJobOutput: Swift.Sendable {
    /// An object that contains the properties associated with a detection job.
    public var comprehendMedicalAsyncJobProperties: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties?

    public init(
        comprehendMedicalAsyncJobProperties: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties? = nil
    )
    {
        self.comprehendMedicalAsyncJobProperties = comprehendMedicalAsyncJobProperties
    }
}

public struct DescribeSNOMEDCTInferenceJobInput: Swift.Sendable {
    /// The identifier that Amazon Comprehend Medical generated for the job. The StartSNOMEDCTInferenceJob operation returns this identifier in its response.
    /// This member is required.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct DescribeSNOMEDCTInferenceJobOutput: Swift.Sendable {
    /// Provides information about a detection job.
    public var comprehendMedicalAsyncJobProperties: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties?

    public init(
        comprehendMedicalAsyncJobProperties: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties? = nil
    )
    {
        self.comprehendMedicalAsyncJobProperties = comprehendMedicalAsyncJobProperties
    }
}

/// The input text was not in valid UTF-8 character encoding. Check your text then retry your request.
public struct InvalidEncodingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidEncodingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The Amazon Comprehend Medical service is temporarily unavailable. Please wait and then retry your request.
public struct ServiceUnavailableException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceUnavailableException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// The size of the text you submitted exceeds the size limit. Reduce the size of the text or use a smaller document and then retry your request.
public struct TextSizeLimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TextSizeLimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

public struct DetectEntitiesInput: Swift.Sendable {
    /// A UTF-8 text string containing the clinical content being examined for entities.
    /// This member is required.
    public var text: Swift.String?

    public init(
        text: Swift.String? = nil
    )
    {
        self.text = text
    }
}

extension ComprehendMedicalClientTypes {

    /// Provides information about an extracted medical entity.
    public struct Entity: Swift.Sendable {
        /// The extracted attributes that relate to this entity.
        public var attributes: [ComprehendMedicalClientTypes.Attribute]?
        /// The 0-based character offset in the input text that shows where the entity begins. The offset returns the UTF-8 code point in the string.
        public var beginOffset: Swift.Int?
        /// The category of the entity.
        public var category: ComprehendMedicalClientTypes.EntityType?
        /// The 0-based character offset in the input text that shows where the entity ends. The offset returns the UTF-8 code point in the string.
        public var endOffset: Swift.Int?
        /// The numeric identifier for the entity. This is a monotonically increasing id unique within this response rather than a global unique identifier.
        public var id: Swift.Int?
        /// The level of confidence that Amazon Comprehend Medical has in the accuracy of the detection.
        public var score: Swift.Float?
        /// The segment of input text extracted as this entity.
        public var text: Swift.String?
        /// Contextual information for the entity.
        public var traits: [ComprehendMedicalClientTypes.Trait]?
        /// Describes the specific type of entity with category of entities.
        public var type: ComprehendMedicalClientTypes.EntitySubType?

        public init(
            attributes: [ComprehendMedicalClientTypes.Attribute]? = nil,
            beginOffset: Swift.Int? = nil,
            category: ComprehendMedicalClientTypes.EntityType? = nil,
            endOffset: Swift.Int? = nil,
            id: Swift.Int? = nil,
            score: Swift.Float? = nil,
            text: Swift.String? = nil,
            traits: [ComprehendMedicalClientTypes.Trait]? = nil,
            type: ComprehendMedicalClientTypes.EntitySubType? = nil
        )
        {
            self.attributes = attributes
            self.beginOffset = beginOffset
            self.category = category
            self.endOffset = endOffset
            self.id = id
            self.score = score
            self.text = text
            self.traits = traits
            self.type = type
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// An attribute that was extracted, but Amazon Comprehend Medical was unable to relate to an entity.
    public struct UnmappedAttribute: Swift.Sendable {
        /// The specific attribute that has been extracted but not mapped to an entity.
        public var attribute: ComprehendMedicalClientTypes.Attribute?
        /// The type of the unmapped attribute, could be one of the following values: "MEDICATION", "MEDICAL_CONDITION", "ANATOMY", "TEST_AND_TREATMENT_PROCEDURE" or "PROTECTED_HEALTH_INFORMATION".
        public var type: ComprehendMedicalClientTypes.EntityType?

        public init(
            attribute: ComprehendMedicalClientTypes.Attribute? = nil,
            type: ComprehendMedicalClientTypes.EntityType? = nil
        )
        {
            self.attribute = attribute
            self.type = type
        }
    }
}

public struct DetectEntitiesOutput: Swift.Sendable {
    /// The collection of medical entities extracted from the input text and their associated information. For each entity, the response provides the entity text, the entity category, where the entity text begins and ends, and the level of confidence that Amazon Comprehend Medical has in the detection and analysis. Attributes and traits of the entity are also returned.
    /// This member is required.
    public var entities: [ComprehendMedicalClientTypes.Entity]?
    /// The version of the model used to analyze the documents. The version number looks like X.X.X. You can use this information to track the model used for a particular batch of documents.
    /// This member is required.
    public var modelVersion: Swift.String?
    /// If the result of the previous request to DetectEntities was truncated, include the PaginationToken to fetch the next page of entities.
    public var paginationToken: Swift.String?
    /// Attributes extracted from the input text that we were unable to relate to an entity.
    public var unmappedAttributes: [ComprehendMedicalClientTypes.UnmappedAttribute]?

    public init(
        entities: [ComprehendMedicalClientTypes.Entity]? = nil,
        modelVersion: Swift.String? = nil,
        paginationToken: Swift.String? = nil,
        unmappedAttributes: [ComprehendMedicalClientTypes.UnmappedAttribute]? = nil
    )
    {
        self.entities = entities
        self.modelVersion = modelVersion
        self.paginationToken = paginationToken
        self.unmappedAttributes = unmappedAttributes
    }
}

public struct DetectEntitiesV2Input: Swift.Sendable {
    /// A UTF-8 string containing the clinical content being examined for entities.
    /// This member is required.
    public var text: Swift.String?

    public init(
        text: Swift.String? = nil
    )
    {
        self.text = text
    }
}

public struct DetectEntitiesV2Output: Swift.Sendable {
    /// The collection of medical entities extracted from the input text and their associated information. For each entity, the response provides the entity text, the entity category, where the entity text begins and ends, and the level of confidence in the detection and analysis. Attributes and traits of the entity are also returned.
    /// This member is required.
    public var entities: [ComprehendMedicalClientTypes.Entity]?
    /// The version of the model used to analyze the documents. The version number looks like X.X.X. You can use this information to track the model used for a particular batch of documents.
    /// This member is required.
    public var modelVersion: Swift.String?
    /// If the result to the DetectEntitiesV2 operation was truncated, include the PaginationToken to fetch the next page of entities.
    public var paginationToken: Swift.String?
    /// Attributes extracted from the input text that couldn't be related to an entity.
    public var unmappedAttributes: [ComprehendMedicalClientTypes.UnmappedAttribute]?

    public init(
        entities: [ComprehendMedicalClientTypes.Entity]? = nil,
        modelVersion: Swift.String? = nil,
        paginationToken: Swift.String? = nil,
        unmappedAttributes: [ComprehendMedicalClientTypes.UnmappedAttribute]? = nil
    )
    {
        self.entities = entities
        self.modelVersion = modelVersion
        self.paginationToken = paginationToken
        self.unmappedAttributes = unmappedAttributes
    }
}

public struct DetectPHIInput: Swift.Sendable {
    /// A UTF-8 text string containing the clinical content being examined for PHI entities.
    /// This member is required.
    public var text: Swift.String?

    public init(
        text: Swift.String? = nil
    )
    {
        self.text = text
    }
}

public struct DetectPHIOutput: Swift.Sendable {
    /// The collection of PHI entities extracted from the input text and their associated information. For each entity, the response provides the entity text, the entity category, where the entity text begins and ends, and the level of confidence that Amazon Comprehend Medical has in its detection.
    /// This member is required.
    public var entities: [ComprehendMedicalClientTypes.Entity]?
    /// The version of the model used to analyze the documents. The version number looks like X.X.X. You can use this information to track the model used for a particular batch of documents.
    /// This member is required.
    public var modelVersion: Swift.String?
    /// If the result of the previous request to DetectPHI was truncated, include the PaginationToken to fetch the next page of PHI entities.
    public var paginationToken: Swift.String?

    public init(
        entities: [ComprehendMedicalClientTypes.Entity]? = nil,
        modelVersion: Swift.String? = nil,
        paginationToken: Swift.String? = nil
    )
    {
        self.entities = entities
        self.modelVersion = modelVersion
        self.paginationToken = paginationToken
    }
}

public struct InferICD10CMInput: Swift.Sendable {
    /// The input text used for analysis.
    /// This member is required.
    public var text: Swift.String?

    public init(
        text: Swift.String? = nil
    )
    {
        self.text = text
    }
}

extension ComprehendMedicalClientTypes {

    public enum ICD10CMEntityType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case dxName
        case timeExpression
        case sdkUnknown(Swift.String)

        public static var allCases: [ICD10CMEntityType] {
            return [
                .dxName,
                .timeExpression
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .dxName: return "DX_NAME"
            case .timeExpression: return "TIME_EXPRESSION"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum ICD10CMRelationshipType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case overlap
        case quality
        case systemOrganSite
        case sdkUnknown(Swift.String)

        public static var allCases: [ICD10CMRelationshipType] {
            return [
                .overlap,
                .quality,
                .systemOrganSite
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .overlap: return "OVERLAP"
            case .quality: return "QUALITY"
            case .systemOrganSite: return "SYSTEM_ORGAN_SITE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum ICD10CMTraitName: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case diagnosis
        case hypothetical
        case lowConfidence
        case negation
        case pertainsToFamily
        case sign
        case symptom
        case sdkUnknown(Swift.String)

        public static var allCases: [ICD10CMTraitName] {
            return [
                .diagnosis,
                .hypothetical,
                .lowConfidence,
                .negation,
                .pertainsToFamily,
                .sign,
                .symptom
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .diagnosis: return "DIAGNOSIS"
            case .hypothetical: return "HYPOTHETICAL"
            case .lowConfidence: return "LOW_CONFIDENCE"
            case .negation: return "NEGATION"
            case .pertainsToFamily: return "PERTAINS_TO_FAMILY"
            case .sign: return "SIGN"
            case .symptom: return "SYMPTOM"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// Contextual information for the entity. The traits recognized by InferICD10CM are DIAGNOSIS, SIGN, SYMPTOM, and NEGATION.
    public struct ICD10CMTrait: Swift.Sendable {
        /// Provides a name or contextual description about the trait.
        public var name: ComprehendMedicalClientTypes.ICD10CMTraitName?
        /// The level of confidence that Amazon Comprehend Medical has that the segment of text is correctly recognized as a trait.
        public var score: Swift.Float?

        public init(
            name: ComprehendMedicalClientTypes.ICD10CMTraitName? = nil,
            score: Swift.Float? = nil
        )
        {
            self.name = name
            self.score = score
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum ICD10CMAttributeType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case acuity
        case direction
        case quality
        case quantity
        case systemOrganSite
        case timeExpression
        case timeToDxName
        case sdkUnknown(Swift.String)

        public static var allCases: [ICD10CMAttributeType] {
            return [
                .acuity,
                .direction,
                .quality,
                .quantity,
                .systemOrganSite,
                .timeExpression,
                .timeToDxName
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .acuity: return "ACUITY"
            case .direction: return "DIRECTION"
            case .quality: return "QUALITY"
            case .quantity: return "QUANTITY"
            case .systemOrganSite: return "SYSTEM_ORGAN_SITE"
            case .timeExpression: return "TIME_EXPRESSION"
            case .timeToDxName: return "TIME_TO_DX_NAME"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// The detected attributes that relate to an entity. This includes an extracted segment of the text that is an attribute of an entity, or otherwise related to an entity. InferICD10CM detects the following attributes: Direction, System, Organ or Site, and Acuity.
    public struct ICD10CMAttribute: Swift.Sendable {
        /// The 0-based character offset in the input text that shows where the attribute begins. The offset returns the UTF-8 code point in the string.
        public var beginOffset: Swift.Int?
        /// The category of attribute. Can be either of DX_NAME or TIME_EXPRESSION.
        public var category: ComprehendMedicalClientTypes.ICD10CMEntityType?
        /// The 0-based character offset in the input text that shows where the attribute ends. The offset returns the UTF-8 code point in the string.
        public var endOffset: Swift.Int?
        /// The numeric identifier for this attribute. This is a monotonically increasing id unique within this response rather than a global unique identifier.
        public var id: Swift.Int?
        /// The level of confidence that Amazon Comprehend Medical has that this attribute is correctly related to this entity.
        public var relationshipScore: Swift.Float?
        /// The type of relationship between the entity and attribute. Type for the relationship can be either of OVERLAP or SYSTEM_ORGAN_SITE.
        public var relationshipType: ComprehendMedicalClientTypes.ICD10CMRelationshipType?
        /// The level of confidence that Amazon Comprehend Medical has that the segment of text is correctly recognized as an attribute.
        public var score: Swift.Float?
        /// The segment of input text which contains the detected attribute.
        public var text: Swift.String?
        /// The contextual information for the attribute. The traits recognized by InferICD10CM are DIAGNOSIS, SIGN, SYMPTOM, and NEGATION.
        public var traits: [ComprehendMedicalClientTypes.ICD10CMTrait]?
        /// The type of attribute. InferICD10CM detects entities of the type DX_NAME.
        public var type: ComprehendMedicalClientTypes.ICD10CMAttributeType?

        public init(
            beginOffset: Swift.Int? = nil,
            category: ComprehendMedicalClientTypes.ICD10CMEntityType? = nil,
            endOffset: Swift.Int? = nil,
            id: Swift.Int? = nil,
            relationshipScore: Swift.Float? = nil,
            relationshipType: ComprehendMedicalClientTypes.ICD10CMRelationshipType? = nil,
            score: Swift.Float? = nil,
            text: Swift.String? = nil,
            traits: [ComprehendMedicalClientTypes.ICD10CMTrait]? = nil,
            type: ComprehendMedicalClientTypes.ICD10CMAttributeType? = nil
        )
        {
            self.beginOffset = beginOffset
            self.category = category
            self.endOffset = endOffset
            self.id = id
            self.relationshipScore = relationshipScore
            self.relationshipType = relationshipType
            self.score = score
            self.text = text
            self.traits = traits
            self.type = type
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum ICD10CMEntityCategory: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case medicalCondition
        case sdkUnknown(Swift.String)

        public static var allCases: [ICD10CMEntityCategory] {
            return [
                .medicalCondition
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .medicalCondition: return "MEDICAL_CONDITION"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// The ICD-10-CM concepts that the entity could refer to, along with a score indicating the likelihood of the match.
    public struct ICD10CMConcept: Swift.Sendable {
        /// The ICD-10-CM code that identifies the concept found in the knowledge base from the Centers for Disease Control.
        public var code: Swift.String?
        /// The long description of the ICD-10-CM code in the ontology.
        public var description: Swift.String?
        /// The level of confidence that Amazon Comprehend Medical has that the entity is accurately linked to an ICD-10-CM concept.
        public var score: Swift.Float?

        public init(
            code: Swift.String? = nil,
            description: Swift.String? = nil,
            score: Swift.Float? = nil
        )
        {
            self.code = code
            self.description = description
            self.score = score
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// The collection of medical entities extracted from the input text and their associated information. For each entity, the response provides the entity text, the entity category, where the entity text begins and ends, and the level of confidence that Amazon Comprehend Medical has in the detection and analysis. Attributes and traits of the entity are also returned.
    public struct ICD10CMEntity: Swift.Sendable {
        /// The detected attributes that relate to the entity. An extracted segment of the text that is an attribute of an entity, or otherwise related to an entity, such as the nature of a medical condition.
        public var attributes: [ComprehendMedicalClientTypes.ICD10CMAttribute]?
        /// The 0-based character offset in the input text that shows where the entity begins. The offset returns the UTF-8 code point in the string.
        public var beginOffset: Swift.Int?
        /// The category of the entity. InferICD10CM detects entities in the MEDICAL_CONDITION category.
        public var category: ComprehendMedicalClientTypes.ICD10CMEntityCategory?
        /// The 0-based character offset in the input text that shows where the entity ends. The offset returns the UTF-8 code point in the string.
        public var endOffset: Swift.Int?
        /// The ICD-10-CM concepts that the entity could refer to, along with a score indicating the likelihood of the match.
        public var icd10CMConcepts: [ComprehendMedicalClientTypes.ICD10CMConcept]?
        /// The numeric identifier for the entity. This is a monotonically increasing id unique within this response rather than a global unique identifier.
        public var id: Swift.Int?
        /// The level of confidence that Amazon Comprehend Medical has in the accuracy of the detection.
        public var score: Swift.Float?
        /// The segment of input text that is matched to the detected entity.
        public var text: Swift.String?
        /// Provides Contextual information for the entity. The traits recognized by InferICD10CM are DIAGNOSIS, SIGN, SYMPTOM, and NEGATION.
        public var traits: [ComprehendMedicalClientTypes.ICD10CMTrait]?
        /// Describes the specific type of entity with category of entities. InferICD10CM detects entities of the type DX_NAME and TIME_EXPRESSION.
        public var type: ComprehendMedicalClientTypes.ICD10CMEntityType?

        public init(
            attributes: [ComprehendMedicalClientTypes.ICD10CMAttribute]? = nil,
            beginOffset: Swift.Int? = nil,
            category: ComprehendMedicalClientTypes.ICD10CMEntityCategory? = nil,
            endOffset: Swift.Int? = nil,
            icd10CMConcepts: [ComprehendMedicalClientTypes.ICD10CMConcept]? = nil,
            id: Swift.Int? = nil,
            score: Swift.Float? = nil,
            text: Swift.String? = nil,
            traits: [ComprehendMedicalClientTypes.ICD10CMTrait]? = nil,
            type: ComprehendMedicalClientTypes.ICD10CMEntityType? = nil
        )
        {
            self.attributes = attributes
            self.beginOffset = beginOffset
            self.category = category
            self.endOffset = endOffset
            self.icd10CMConcepts = icd10CMConcepts
            self.id = id
            self.score = score
            self.text = text
            self.traits = traits
            self.type = type
        }
    }
}

public struct InferICD10CMOutput: Swift.Sendable {
    /// The medical conditions detected in the text linked to ICD-10-CM concepts. If the action is successful, the service sends back an HTTP 200 response, as well as the entities detected.
    /// This member is required.
    public var entities: [ComprehendMedicalClientTypes.ICD10CMEntity]?
    /// The version of the model used to analyze the documents, in the format n.n.n You can use this information to track the model used for a particular batch of documents.
    public var modelVersion: Swift.String?
    /// If the result of the previous request to InferICD10CM was truncated, include the PaginationToken to fetch the next page of medical condition entities.
    public var paginationToken: Swift.String?

    public init(
        entities: [ComprehendMedicalClientTypes.ICD10CMEntity]? = nil,
        modelVersion: Swift.String? = nil,
        paginationToken: Swift.String? = nil
    )
    {
        self.entities = entities
        self.modelVersion = modelVersion
        self.paginationToken = paginationToken
    }
}

public struct InferRxNormInput: Swift.Sendable {
    /// The input text used for analysis.
    /// This member is required.
    public var text: Swift.String?

    public init(
        text: Swift.String? = nil
    )
    {
        self.text = text
    }
}

extension ComprehendMedicalClientTypes {

    public enum RxNormTraitName: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case negation
        case pastHistory
        case sdkUnknown(Swift.String)

        public static var allCases: [RxNormTraitName] {
            return [
                .negation,
                .pastHistory
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .negation: return "NEGATION"
            case .pastHistory: return "PAST_HISTORY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// The contextual information for the entity. InferRxNorm recognizes the trait NEGATION, which is any indication that the patient is not taking a medication.
    public struct RxNormTrait: Swift.Sendable {
        /// Provides a name or contextual description about the trait.
        public var name: ComprehendMedicalClientTypes.RxNormTraitName?
        /// The level of confidence that Amazon Comprehend Medical has in the accuracy of the detected trait.
        public var score: Swift.Float?

        public init(
            name: ComprehendMedicalClientTypes.RxNormTraitName? = nil,
            score: Swift.Float? = nil
        )
        {
            self.name = name
            self.score = score
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum RxNormAttributeType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case dosage
        case duration
        case form
        case frequency
        case rate
        case routeOrMode
        case strength
        case sdkUnknown(Swift.String)

        public static var allCases: [RxNormAttributeType] {
            return [
                .dosage,
                .duration,
                .form,
                .frequency,
                .rate,
                .routeOrMode,
                .strength
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .dosage: return "DOSAGE"
            case .duration: return "DURATION"
            case .form: return "FORM"
            case .frequency: return "FREQUENCY"
            case .rate: return "RATE"
            case .routeOrMode: return "ROUTE_OR_MODE"
            case .strength: return "STRENGTH"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// The extracted attributes that relate to this entity. The attributes recognized by InferRxNorm are DOSAGE, DURATION, FORM, FREQUENCY, RATE, ROUTE_OR_MODE.
    public struct RxNormAttribute: Swift.Sendable {
        /// The 0-based character offset in the input text that shows where the attribute begins. The offset returns the UTF-8 code point in the string.
        public var beginOffset: Swift.Int?
        /// The 0-based character offset in the input text that shows where the attribute ends. The offset returns the UTF-8 code point in the string.
        public var endOffset: Swift.Int?
        /// The numeric identifier for this attribute. This is a monotonically increasing id unique within this response rather than a global unique identifier.
        public var id: Swift.Int?
        /// The level of confidence that Amazon Comprehend Medical has that the attribute is accurately linked to an entity.
        public var relationshipScore: Swift.Float?
        /// The level of confidence that Amazon Comprehend Medical has that the segment of text is correctly recognized as an attribute.
        public var score: Swift.Float?
        /// The segment of input text which corresponds to the detected attribute.
        public var text: Swift.String?
        /// Contextual information for the attribute. InferRxNorm recognizes the trait NEGATION for attributes, i.e. that the patient is not taking a specific dose or form of a medication.
        public var traits: [ComprehendMedicalClientTypes.RxNormTrait]?
        /// The type of attribute. The types of attributes recognized by InferRxNorm are BRAND_NAME and GENERIC_NAME.
        public var type: ComprehendMedicalClientTypes.RxNormAttributeType?

        public init(
            beginOffset: Swift.Int? = nil,
            endOffset: Swift.Int? = nil,
            id: Swift.Int? = nil,
            relationshipScore: Swift.Float? = nil,
            score: Swift.Float? = nil,
            text: Swift.String? = nil,
            traits: [ComprehendMedicalClientTypes.RxNormTrait]? = nil,
            type: ComprehendMedicalClientTypes.RxNormAttributeType? = nil
        )
        {
            self.beginOffset = beginOffset
            self.endOffset = endOffset
            self.id = id
            self.relationshipScore = relationshipScore
            self.score = score
            self.text = text
            self.traits = traits
            self.type = type
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum RxNormEntityCategory: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case medication
        case sdkUnknown(Swift.String)

        public static var allCases: [RxNormEntityCategory] {
            return [
                .medication
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .medication: return "MEDICATION"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// The RxNorm concept that the entity could refer to, along with a score indicating the likelihood of the match.
    public struct RxNormConcept: Swift.Sendable {
        /// RxNorm concept ID, also known as the RxCUI.
        public var code: Swift.String?
        /// The description of the RxNorm concept.
        public var description: Swift.String?
        /// The level of confidence that Amazon Comprehend Medical has that the entity is accurately linked to the reported RxNorm concept.
        public var score: Swift.Float?

        public init(
            code: Swift.String? = nil,
            description: Swift.String? = nil,
            score: Swift.Float? = nil
        )
        {
            self.code = code
            self.description = description
            self.score = score
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum RxNormEntityType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case brandName
        case genericName
        case sdkUnknown(Swift.String)

        public static var allCases: [RxNormEntityType] {
            return [
                .brandName,
                .genericName
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .brandName: return "BRAND_NAME"
            case .genericName: return "GENERIC_NAME"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// The collection of medical entities extracted from the input text and their associated information. For each entity, the response provides the entity text, the entity category, where the entity text begins and ends, and the level of confidence that Amazon Comprehend Medical has in the detection and analysis. Attributes and traits of the entity are also returned.
    public struct RxNormEntity: Swift.Sendable {
        /// The extracted attributes that relate to the entity. The attributes recognized by InferRxNorm are DOSAGE, DURATION, FORM, FREQUENCY, RATE, ROUTE_OR_MODE, and STRENGTH.
        public var attributes: [ComprehendMedicalClientTypes.RxNormAttribute]?
        /// The 0-based character offset in the input text that shows where the entity begins. The offset returns the UTF-8 code point in the string.
        public var beginOffset: Swift.Int?
        /// The category of the entity. The recognized categories are GENERIC or BRAND_NAME.
        public var category: ComprehendMedicalClientTypes.RxNormEntityCategory?
        /// The 0-based character offset in the input text that shows where the entity ends. The offset returns the UTF-8 code point in the string.
        public var endOffset: Swift.Int?
        /// The numeric identifier for the entity. This is a monotonically increasing id unique within this response rather than a global unique identifier.
        public var id: Swift.Int?
        /// The RxNorm concepts that the entity could refer to, along with a score indicating the likelihood of the match.
        public var rxNormConcepts: [ComprehendMedicalClientTypes.RxNormConcept]?
        /// The level of confidence that Amazon Comprehend Medical has in the accuracy of the detected entity.
        public var score: Swift.Float?
        /// The segment of input text extracted from which the entity was detected.
        public var text: Swift.String?
        /// Contextual information for the entity.
        public var traits: [ComprehendMedicalClientTypes.RxNormTrait]?
        /// Describes the specific type of entity. For InferRxNorm, the recognized entity type is MEDICATION.
        public var type: ComprehendMedicalClientTypes.RxNormEntityType?

        public init(
            attributes: [ComprehendMedicalClientTypes.RxNormAttribute]? = nil,
            beginOffset: Swift.Int? = nil,
            category: ComprehendMedicalClientTypes.RxNormEntityCategory? = nil,
            endOffset: Swift.Int? = nil,
            id: Swift.Int? = nil,
            rxNormConcepts: [ComprehendMedicalClientTypes.RxNormConcept]? = nil,
            score: Swift.Float? = nil,
            text: Swift.String? = nil,
            traits: [ComprehendMedicalClientTypes.RxNormTrait]? = nil,
            type: ComprehendMedicalClientTypes.RxNormEntityType? = nil
        )
        {
            self.attributes = attributes
            self.beginOffset = beginOffset
            self.category = category
            self.endOffset = endOffset
            self.id = id
            self.rxNormConcepts = rxNormConcepts
            self.score = score
            self.text = text
            self.traits = traits
            self.type = type
        }
    }
}

public struct InferRxNormOutput: Swift.Sendable {
    /// The medication entities detected in the text linked to RxNorm concepts. If the action is successful, the service sends back an HTTP 200 response, as well as the entities detected.
    /// This member is required.
    public var entities: [ComprehendMedicalClientTypes.RxNormEntity]?
    /// The version of the model used to analyze the documents, in the format n.n.n You can use this information to track the model used for a particular batch of documents.
    public var modelVersion: Swift.String?
    /// If the result of the previous request to InferRxNorm was truncated, include the PaginationToken to fetch the next page of medication entities.
    public var paginationToken: Swift.String?

    public init(
        entities: [ComprehendMedicalClientTypes.RxNormEntity]? = nil,
        modelVersion: Swift.String? = nil,
        paginationToken: Swift.String? = nil
    )
    {
        self.entities = entities
        self.modelVersion = modelVersion
        self.paginationToken = paginationToken
    }
}

public struct InferSNOMEDCTInput: Swift.Sendable {
    /// The input text to be analyzed using InferSNOMEDCT.
    /// This member is required.
    public var text: Swift.String?

    public init(
        text: Swift.String? = nil
    )
    {
        self.text = text
    }
}

extension ComprehendMedicalClientTypes {

    public enum SNOMEDCTEntityCategory: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case anatomy
        case medicalCondition
        case testTreatmentProcedure
        case sdkUnknown(Swift.String)

        public static var allCases: [SNOMEDCTEntityCategory] {
            return [
                .anatomy,
                .medicalCondition,
                .testTreatmentProcedure
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .anatomy: return "ANATOMY"
            case .medicalCondition: return "MEDICAL_CONDITION"
            case .testTreatmentProcedure: return "TEST_TREATMENT_PROCEDURE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum SNOMEDCTRelationshipType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case acuity
        case direction
        case quality
        case systemOrganSite
        case testUnit
        case testUnits
        case testValue
        case sdkUnknown(Swift.String)

        public static var allCases: [SNOMEDCTRelationshipType] {
            return [
                .acuity,
                .direction,
                .quality,
                .systemOrganSite,
                .testUnit,
                .testUnits,
                .testValue
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .acuity: return "ACUITY"
            case .direction: return "DIRECTION"
            case .quality: return "QUALITY"
            case .systemOrganSite: return "SYSTEM_ORGAN_SITE"
            case .testUnit: return "TEST_UNIT"
            case .testUnits: return "TEST_UNITS"
            case .testValue: return "TEST_VALUE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// The SNOMED-CT concepts that the entity could refer to, along with a score indicating the likelihood of the match.
    public struct SNOMEDCTConcept: Swift.Sendable {
        /// The numeric ID for the SNOMED-CT concept.
        public var code: Swift.String?
        /// The description of the SNOMED-CT concept.
        public var description: Swift.String?
        /// The level of confidence Amazon Comprehend Medical has that the entity should be linked to the identified SNOMED-CT concept.
        public var score: Swift.Float?

        public init(
            code: Swift.String? = nil,
            description: Swift.String? = nil,
            score: Swift.Float? = nil
        )
        {
            self.code = code
            self.description = description
            self.score = score
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum SNOMEDCTTraitName: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case diagnosis
        case future
        case hypothetical
        case lowConfidence
        case negation
        case pastHistory
        case pertainsToFamily
        case sign
        case symptom
        case sdkUnknown(Swift.String)

        public static var allCases: [SNOMEDCTTraitName] {
            return [
                .diagnosis,
                .future,
                .hypothetical,
                .lowConfidence,
                .negation,
                .pastHistory,
                .pertainsToFamily,
                .sign,
                .symptom
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .diagnosis: return "DIAGNOSIS"
            case .future: return "FUTURE"
            case .hypothetical: return "HYPOTHETICAL"
            case .lowConfidence: return "LOW_CONFIDENCE"
            case .negation: return "NEGATION"
            case .pastHistory: return "PAST_HISTORY"
            case .pertainsToFamily: return "PERTAINS_TO_FAMILY"
            case .sign: return "SIGN"
            case .symptom: return "SYMPTOM"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// Contextual information for an entity.
    public struct SNOMEDCTTrait: Swift.Sendable {
        /// The name or contextual description of a detected trait.
        public var name: ComprehendMedicalClientTypes.SNOMEDCTTraitName?
        /// The level of confidence that Amazon Comprehend Medical has in the accuracy of a detected trait.
        public var score: Swift.Float?

        public init(
            name: ComprehendMedicalClientTypes.SNOMEDCTTraitName? = nil,
            score: Swift.Float? = nil
        )
        {
            self.name = name
            self.score = score
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum SNOMEDCTAttributeType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case acuity
        case direction
        case quality
        case systemOrganSite
        case testUnit
        case testValue
        case sdkUnknown(Swift.String)

        public static var allCases: [SNOMEDCTAttributeType] {
            return [
                .acuity,
                .direction,
                .quality,
                .systemOrganSite,
                .testUnit,
                .testValue
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .acuity: return "ACUITY"
            case .direction: return "DIRECTION"
            case .quality: return "QUALITY"
            case .systemOrganSite: return "SYSTEM_ORGAN_SITE"
            case .testUnit: return "TEST_UNIT"
            case .testValue: return "TEST_VALUE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// The extracted attributes that relate to an entity. An extracted segment of the text that is an attribute of an entity, or otherwise related to an entity, such as the dosage of a medication taken.
    public struct SNOMEDCTAttribute: Swift.Sendable {
        /// The 0-based character offset in the input text that shows where the attribute begins. The offset returns the UTF-8 code point in the string.
        public var beginOffset: Swift.Int?
        /// The category of the detected attribute. Possible categories include MEDICAL_CONDITION, ANATOMY, and TEST_TREATMENT_PROCEDURE.
        public var category: ComprehendMedicalClientTypes.SNOMEDCTEntityCategory?
        /// The 0-based character offset in the input text that shows where the attribute ends. The offset returns the UTF-8 code point in the string.
        public var endOffset: Swift.Int?
        /// The numeric identifier for this attribute. This is a monotonically increasing id unique within this response rather than a global unique identifier.
        public var id: Swift.Int?
        /// The level of confidence that Amazon Comprehend Medical has that this attribute is correctly related to this entity.
        public var relationshipScore: Swift.Float?
        /// The type of relationship that exists between the entity and the related attribute.
        public var relationshipType: ComprehendMedicalClientTypes.SNOMEDCTRelationshipType?
        /// The level of confidence that Amazon Comprehend Medical has that the segment of text is correctly recognized as an attribute.
        public var score: Swift.Float?
        /// The SNOMED-CT concepts specific to an attribute, along with a score indicating the likelihood of the match.
        public var snomedctConcepts: [ComprehendMedicalClientTypes.SNOMEDCTConcept]?
        /// The segment of input text extracted as this attribute.
        public var text: Swift.String?
        /// Contextual information for an attribute. Examples include signs, symptoms, diagnosis, and negation.
        public var traits: [ComprehendMedicalClientTypes.SNOMEDCTTrait]?
        /// The type of attribute. Possible types include DX_NAME, ACUITY, DIRECTION, SYSTEM_ORGAN_SITE,TEST_NAME, TEST_VALUE, TEST_UNIT, PROCEDURE_NAME, and TREATMENT_NAME.
        public var type: ComprehendMedicalClientTypes.SNOMEDCTAttributeType?

        public init(
            beginOffset: Swift.Int? = nil,
            category: ComprehendMedicalClientTypes.SNOMEDCTEntityCategory? = nil,
            endOffset: Swift.Int? = nil,
            id: Swift.Int? = nil,
            relationshipScore: Swift.Float? = nil,
            relationshipType: ComprehendMedicalClientTypes.SNOMEDCTRelationshipType? = nil,
            score: Swift.Float? = nil,
            snomedctConcepts: [ComprehendMedicalClientTypes.SNOMEDCTConcept]? = nil,
            text: Swift.String? = nil,
            traits: [ComprehendMedicalClientTypes.SNOMEDCTTrait]? = nil,
            type: ComprehendMedicalClientTypes.SNOMEDCTAttributeType? = nil
        )
        {
            self.beginOffset = beginOffset
            self.category = category
            self.endOffset = endOffset
            self.id = id
            self.relationshipScore = relationshipScore
            self.relationshipType = relationshipType
            self.score = score
            self.snomedctConcepts = snomedctConcepts
            self.text = text
            self.traits = traits
            self.type = type
        }
    }
}

extension ComprehendMedicalClientTypes {

    public enum SNOMEDCTEntityType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case dxName
        case procedureName
        case testName
        case treatmentName
        case sdkUnknown(Swift.String)

        public static var allCases: [SNOMEDCTEntityType] {
            return [
                .dxName,
                .procedureName,
                .testName,
                .treatmentName
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .dxName: return "DX_NAME"
            case .procedureName: return "PROCEDURE_NAME"
            case .testName: return "TEST_NAME"
            case .treatmentName: return "TREATMENT_NAME"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// The collection of medical entities extracted from the input text and their associated information. For each entity, the response provides the entity text, the entity category, where the entity text begins and ends, and the level of confidence that Amazon Comprehend Medical has in the detection and analysis. Attributes and traits of the entity are also returned.
    public struct SNOMEDCTEntity: Swift.Sendable {
        /// An extracted segment of the text that is an attribute of an entity, or otherwise related to an entity, such as the dosage of a medication taken.
        public var attributes: [ComprehendMedicalClientTypes.SNOMEDCTAttribute]?
        /// The 0-based character offset in the input text that shows where the entity begins. The offset returns the UTF-8 code point in the string.
        public var beginOffset: Swift.Int?
        /// The category of the detected entity. Possible categories are MEDICAL_CONDITION, ANATOMY, or TEST_TREATMENT_PROCEDURE.
        public var category: ComprehendMedicalClientTypes.SNOMEDCTEntityCategory?
        /// The 0-based character offset in the input text that shows where the entity ends. The offset returns the UTF-8 code point in the string.
        public var endOffset: Swift.Int?
        /// The numeric identifier for the entity. This is a monotonically increasing id unique within this response rather than a global unique identifier.
        public var id: Swift.Int?
        /// The level of confidence that Amazon Comprehend Medical has in the accuracy of the detected entity.
        public var score: Swift.Float?
        /// The SNOMED concepts that the entity could refer to, along with a score indicating the likelihood of the match.
        public var snomedctConcepts: [ComprehendMedicalClientTypes.SNOMEDCTConcept]?
        /// The segment of input text extracted as this entity.
        public var text: Swift.String?
        /// Contextual information for the entity.
        public var traits: [ComprehendMedicalClientTypes.SNOMEDCTTrait]?
        /// Describes the specific type of entity with category of entities. Possible types include DX_NAME, ACUITY, DIRECTION, SYSTEM_ORGAN_SITE, TEST_NAME, TEST_VALUE, TEST_UNIT, PROCEDURE_NAME, or TREATMENT_NAME.
        public var type: ComprehendMedicalClientTypes.SNOMEDCTEntityType?

        public init(
            attributes: [ComprehendMedicalClientTypes.SNOMEDCTAttribute]? = nil,
            beginOffset: Swift.Int? = nil,
            category: ComprehendMedicalClientTypes.SNOMEDCTEntityCategory? = nil,
            endOffset: Swift.Int? = nil,
            id: Swift.Int? = nil,
            score: Swift.Float? = nil,
            snomedctConcepts: [ComprehendMedicalClientTypes.SNOMEDCTConcept]? = nil,
            text: Swift.String? = nil,
            traits: [ComprehendMedicalClientTypes.SNOMEDCTTrait]? = nil,
            type: ComprehendMedicalClientTypes.SNOMEDCTEntityType? = nil
        )
        {
            self.attributes = attributes
            self.beginOffset = beginOffset
            self.category = category
            self.endOffset = endOffset
            self.id = id
            self.score = score
            self.snomedctConcepts = snomedctConcepts
            self.text = text
            self.traits = traits
            self.type = type
        }
    }
}

extension ComprehendMedicalClientTypes {

    /// The information about the revision of the SNOMED-CT ontology in the response. Specifically, the details include the SNOMED-CT edition, language, and version date.
    public struct SNOMEDCTDetails: Swift.Sendable {
        /// The edition of SNOMED-CT used. The edition used for the InferSNOMEDCT editions is the US edition.
        public var edition: Swift.String?
        /// The language used in the SNOMED-CT ontology. All Amazon Comprehend Medical operations are US English (en).
        public var language: Swift.String?
        /// The version date of the SNOMED-CT ontology used.
        public var versionDate: Swift.String?

        public init(
            edition: Swift.String? = nil,
            language: Swift.String? = nil,
            versionDate: Swift.String? = nil
        )
        {
            self.edition = edition
            self.language = language
            self.versionDate = versionDate
        }
    }
}

public struct InferSNOMEDCTOutput: Swift.Sendable {
    /// The number of characters in the input request documentation.
    public var characters: ComprehendMedicalClientTypes.Characters?
    /// The collection of medical concept entities extracted from the input text and their associated information. For each entity, the response provides the entity text, the entity category, where the entity text begins and ends, and the level of confidence that Amazon Comprehend Medical has in the detection and analysis. Attributes and traits of the entity are also returned.
    /// This member is required.
    public var entities: [ComprehendMedicalClientTypes.SNOMEDCTEntity]?
    /// The version of the model used to analyze the documents, in the format n.n.n You can use this information to track the model used for a particular batch of documents.
    public var modelVersion: Swift.String?
    /// If the result of the request is truncated, the pagination token can be used to fetch the next page of entities.
    public var paginationToken: Swift.String?
    /// The details of the SNOMED-CT revision, including the edition, language, and version date.
    public var snomedctDetails: ComprehendMedicalClientTypes.SNOMEDCTDetails?

    public init(
        characters: ComprehendMedicalClientTypes.Characters? = nil,
        entities: [ComprehendMedicalClientTypes.SNOMEDCTEntity]? = nil,
        modelVersion: Swift.String? = nil,
        paginationToken: Swift.String? = nil,
        snomedctDetails: ComprehendMedicalClientTypes.SNOMEDCTDetails? = nil
    )
    {
        self.characters = characters
        self.entities = entities
        self.modelVersion = modelVersion
        self.paginationToken = paginationToken
        self.snomedctDetails = snomedctDetails
    }
}

/// The filter that you specified for the operation is invalid. Check the filter values that you entered and try your request again.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension ComprehendMedicalClientTypes {

    /// Provides information for filtering a list of detection jobs.
    public struct ComprehendMedicalAsyncJobFilter: Swift.Sendable {
        /// Filters on the name of the job.
        public var jobName: Swift.String?
        /// Filters the list of jobs based on job status. Returns only jobs with the specified status.
        public var jobStatus: ComprehendMedicalClientTypes.JobStatus?
        /// Filters the list of jobs based on the time that the job was submitted for processing. Returns only jobs submitted after the specified time. Jobs are returned in descending order, newest to oldest.
        public var submitTimeAfter: Foundation.Date?
        /// Filters the list of jobs based on the time that the job was submitted for processing. Returns only jobs submitted before the specified time. Jobs are returned in ascending order, oldest to newest.
        public var submitTimeBefore: Foundation.Date?

        public init(
            jobName: Swift.String? = nil,
            jobStatus: ComprehendMedicalClientTypes.JobStatus? = nil,
            submitTimeAfter: Foundation.Date? = nil,
            submitTimeBefore: Foundation.Date? = nil
        )
        {
            self.jobName = jobName
            self.jobStatus = jobStatus
            self.submitTimeAfter = submitTimeAfter
            self.submitTimeBefore = submitTimeBefore
        }
    }
}

public struct ListEntitiesDetectionV2JobsInput: Swift.Sendable {
    /// Filters the jobs that are returned. You can filter jobs based on their names, status, or the date and time that they were submitted. You can only set one filter at a time.
    public var filter: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter?
    /// The maximum number of results to return in each page. The default is 100.
    public var maxResults: Swift.Int?
    /// Identifies the next page of results to return.
    public var nextToken: Swift.String?

    public init(
        filter: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filter = filter
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListEntitiesDetectionV2JobsOutput: Swift.Sendable {
    /// A list containing the properties of each job returned.
    public var comprehendMedicalAsyncJobPropertiesList: [ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties]?
    /// Identifies the next page of results to return.
    public var nextToken: Swift.String?

    public init(
        comprehendMedicalAsyncJobPropertiesList: [ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.comprehendMedicalAsyncJobPropertiesList = comprehendMedicalAsyncJobPropertiesList
        self.nextToken = nextToken
    }
}

public struct ListICD10CMInferenceJobsInput: Swift.Sendable {
    /// Filters the jobs that are returned. You can filter jobs based on their names, status, or the date and time that they were submitted. You can only set one filter at a time.
    public var filter: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter?
    /// The maximum number of results to return in each page. The default is 100.
    public var maxResults: Swift.Int?
    /// Identifies the next page of results to return.
    public var nextToken: Swift.String?

    public init(
        filter: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filter = filter
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListICD10CMInferenceJobsOutput: Swift.Sendable {
    /// A list containing the properties of each job that is returned.
    public var comprehendMedicalAsyncJobPropertiesList: [ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties]?
    /// Identifies the next page of results to return.
    public var nextToken: Swift.String?

    public init(
        comprehendMedicalAsyncJobPropertiesList: [ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.comprehendMedicalAsyncJobPropertiesList = comprehendMedicalAsyncJobPropertiesList
        self.nextToken = nextToken
    }
}

public struct ListPHIDetectionJobsInput: Swift.Sendable {
    /// Filters the jobs that are returned. You can filter jobs based on their names, status, or the date and time that they were submitted. You can only set one filter at a time.
    public var filter: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter?
    /// The maximum number of results to return in each page. The default is 100.
    public var maxResults: Swift.Int?
    /// Identifies the next page of results to return.
    public var nextToken: Swift.String?

    public init(
        filter: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filter = filter
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListPHIDetectionJobsOutput: Swift.Sendable {
    /// A list containing the properties of each job returned.
    public var comprehendMedicalAsyncJobPropertiesList: [ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties]?
    /// Identifies the next page of results to return.
    public var nextToken: Swift.String?

    public init(
        comprehendMedicalAsyncJobPropertiesList: [ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.comprehendMedicalAsyncJobPropertiesList = comprehendMedicalAsyncJobPropertiesList
        self.nextToken = nextToken
    }
}

public struct ListRxNormInferenceJobsInput: Swift.Sendable {
    /// Filters the jobs that are returned. You can filter jobs based on their names, status, or the date and time that they were submitted. You can only set one filter at a time.
    public var filter: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter?
    /// Identifies the next page of results to return.
    public var maxResults: Swift.Int?
    /// Identifies the next page of results to return.
    public var nextToken: Swift.String?

    public init(
        filter: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filter = filter
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListRxNormInferenceJobsOutput: Swift.Sendable {
    /// The maximum number of results to return in each page. The default is 100.
    public var comprehendMedicalAsyncJobPropertiesList: [ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties]?
    /// Identifies the next page of results to return.
    public var nextToken: Swift.String?

    public init(
        comprehendMedicalAsyncJobPropertiesList: [ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.comprehendMedicalAsyncJobPropertiesList = comprehendMedicalAsyncJobPropertiesList
        self.nextToken = nextToken
    }
}

public struct ListSNOMEDCTInferenceJobsInput: Swift.Sendable {
    /// Provides information for filtering a list of detection jobs.
    public var filter: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter?
    /// The maximum number of results to return in each page. The default is 100.
    public var maxResults: Swift.Int?
    /// Identifies the next page of InferSNOMEDCT results to return.
    public var nextToken: Swift.String?

    public init(
        filter: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filter = filter
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListSNOMEDCTInferenceJobsOutput: Swift.Sendable {
    /// A list containing the properties of each job that is returned.
    public var comprehendMedicalAsyncJobPropertiesList: [ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties]?
    /// Identifies the next page of results to return.
    public var nextToken: Swift.String?

    public init(
        comprehendMedicalAsyncJobPropertiesList: [ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.comprehendMedicalAsyncJobPropertiesList = comprehendMedicalAsyncJobPropertiesList
        self.nextToken = nextToken
    }
}

public struct StartEntitiesDetectionV2JobInput: Swift.Sendable {
    /// A unique identifier for the request. If you don't set the client request token, Amazon Comprehend Medical generates one for you.
    public var clientRequestToken: Swift.String?
    /// The Amazon Resource Name (ARN) of the AWS Identity and Access Management (IAM) role that grants Amazon Comprehend Medical read access to your input data. For more information, see [Role-Based Permissions Required for Asynchronous Operations](https://docs.aws.amazon.com/comprehend/latest/dg/access-control-managing-permissions-med.html#auth-role-permissions-med).
    /// This member is required.
    public var dataAccessRoleArn: Swift.String?
    /// The input configuration that specifies the format and location of the input data for the job.
    /// This member is required.
    public var inputDataConfig: ComprehendMedicalClientTypes.InputDataConfig?
    /// The identifier of the job.
    public var jobName: Swift.String?
    /// An AWS Key Management Service key to encrypt your output files. If you do not specify a key, the files are written in plain text.
    public var kmsKey: Swift.String?
    /// The language of the input documents. All documents must be in the same language. Amazon Comprehend Medical processes files in US English (en).
    /// This member is required.
    public var languageCode: ComprehendMedicalClientTypes.LanguageCode?
    /// The output configuration that specifies where to send the output files.
    /// This member is required.
    public var outputDataConfig: ComprehendMedicalClientTypes.OutputDataConfig?

    public init(
        clientRequestToken: Swift.String? = nil,
        dataAccessRoleArn: Swift.String? = nil,
        inputDataConfig: ComprehendMedicalClientTypes.InputDataConfig? = nil,
        jobName: Swift.String? = nil,
        kmsKey: Swift.String? = nil,
        languageCode: ComprehendMedicalClientTypes.LanguageCode? = nil,
        outputDataConfig: ComprehendMedicalClientTypes.OutputDataConfig? = nil
    )
    {
        self.clientRequestToken = clientRequestToken
        self.dataAccessRoleArn = dataAccessRoleArn
        self.inputDataConfig = inputDataConfig
        self.jobName = jobName
        self.kmsKey = kmsKey
        self.languageCode = languageCode
        self.outputDataConfig = outputDataConfig
    }
}

public struct StartEntitiesDetectionV2JobOutput: Swift.Sendable {
    /// The identifier generated for the job. To get the status of a job, use this identifier with the DescribeEntitiesDetectionV2Job operation.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StartICD10CMInferenceJobInput: Swift.Sendable {
    /// A unique identifier for the request. If you don't set the client request token, Amazon Comprehend Medical generates one.
    public var clientRequestToken: Swift.String?
    /// The Amazon Resource Name (ARN) of the AWS Identity and Access Management (IAM) role that grants Amazon Comprehend Medical read access to your input data. For more information, see [ Role-Based Permissions Required for Asynchronous Operations](https://docs.aws.amazon.com/comprehend/latest/dg/access-control-managing-permissions-med.html#auth-role-permissions-med).
    /// This member is required.
    public var dataAccessRoleArn: Swift.String?
    /// Specifies the format and location of the input data for the job.
    /// This member is required.
    public var inputDataConfig: ComprehendMedicalClientTypes.InputDataConfig?
    /// The identifier of the job.
    public var jobName: Swift.String?
    /// An AWS Key Management Service key to encrypt your output files. If you do not specify a key, the files are written in plain text.
    public var kmsKey: Swift.String?
    /// The language of the input documents. All documents must be in the same language.
    /// This member is required.
    public var languageCode: ComprehendMedicalClientTypes.LanguageCode?
    /// Specifies where to send the output files.
    /// This member is required.
    public var outputDataConfig: ComprehendMedicalClientTypes.OutputDataConfig?

    public init(
        clientRequestToken: Swift.String? = nil,
        dataAccessRoleArn: Swift.String? = nil,
        inputDataConfig: ComprehendMedicalClientTypes.InputDataConfig? = nil,
        jobName: Swift.String? = nil,
        kmsKey: Swift.String? = nil,
        languageCode: ComprehendMedicalClientTypes.LanguageCode? = nil,
        outputDataConfig: ComprehendMedicalClientTypes.OutputDataConfig? = nil
    )
    {
        self.clientRequestToken = clientRequestToken
        self.dataAccessRoleArn = dataAccessRoleArn
        self.inputDataConfig = inputDataConfig
        self.jobName = jobName
        self.kmsKey = kmsKey
        self.languageCode = languageCode
        self.outputDataConfig = outputDataConfig
    }
}

public struct StartICD10CMInferenceJobOutput: Swift.Sendable {
    /// The identifier generated for the job. To get the status of a job, use this identifier with the StartICD10CMInferenceJob operation.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StartPHIDetectionJobInput: Swift.Sendable {
    /// A unique identifier for the request. If you don't set the client request token, Amazon Comprehend Medical generates one.
    public var clientRequestToken: Swift.String?
    /// The Amazon Resource Name (ARN) of the AWS Identity and Access Management (IAM) role that grants Amazon Comprehend Medical read access to your input data. For more information, see [ Role-Based Permissions Required for Asynchronous Operations](https://docs.aws.amazon.com/comprehend/latest/dg/access-control-managing-permissions-med.html#auth-role-permissions-med).
    /// This member is required.
    public var dataAccessRoleArn: Swift.String?
    /// Specifies the format and location of the input data for the job.
    /// This member is required.
    public var inputDataConfig: ComprehendMedicalClientTypes.InputDataConfig?
    /// The identifier of the job.
    public var jobName: Swift.String?
    /// An AWS Key Management Service key to encrypt your output files. If you do not specify a key, the files are written in plain text.
    public var kmsKey: Swift.String?
    /// The language of the input documents. All documents must be in the same language.
    /// This member is required.
    public var languageCode: ComprehendMedicalClientTypes.LanguageCode?
    /// Specifies where to send the output files.
    /// This member is required.
    public var outputDataConfig: ComprehendMedicalClientTypes.OutputDataConfig?

    public init(
        clientRequestToken: Swift.String? = nil,
        dataAccessRoleArn: Swift.String? = nil,
        inputDataConfig: ComprehendMedicalClientTypes.InputDataConfig? = nil,
        jobName: Swift.String? = nil,
        kmsKey: Swift.String? = nil,
        languageCode: ComprehendMedicalClientTypes.LanguageCode? = nil,
        outputDataConfig: ComprehendMedicalClientTypes.OutputDataConfig? = nil
    )
    {
        self.clientRequestToken = clientRequestToken
        self.dataAccessRoleArn = dataAccessRoleArn
        self.inputDataConfig = inputDataConfig
        self.jobName = jobName
        self.kmsKey = kmsKey
        self.languageCode = languageCode
        self.outputDataConfig = outputDataConfig
    }
}

public struct StartPHIDetectionJobOutput: Swift.Sendable {
    /// The identifier generated for the job. To get the status of a job, use this identifier with the DescribePHIDetectionJob operation.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StartRxNormInferenceJobInput: Swift.Sendable {
    /// A unique identifier for the request. If you don't set the client request token, Amazon Comprehend Medical generates one.
    public var clientRequestToken: Swift.String?
    /// The Amazon Resource Name (ARN) of the AWS Identity and Access Management (IAM) role that grants Amazon Comprehend Medical read access to your input data. For more information, see [ Role-Based Permissions Required for Asynchronous Operations](https://docs.aws.amazon.com/comprehend/latest/dg/access-control-managing-permissions-med.html#auth-role-permissions-med).
    /// This member is required.
    public var dataAccessRoleArn: Swift.String?
    /// Specifies the format and location of the input data for the job.
    /// This member is required.
    public var inputDataConfig: ComprehendMedicalClientTypes.InputDataConfig?
    /// The identifier of the job.
    public var jobName: Swift.String?
    /// An AWS Key Management Service key to encrypt your output files. If you do not specify a key, the files are written in plain text.
    public var kmsKey: Swift.String?
    /// The language of the input documents. All documents must be in the same language.
    /// This member is required.
    public var languageCode: ComprehendMedicalClientTypes.LanguageCode?
    /// Specifies where to send the output files.
    /// This member is required.
    public var outputDataConfig: ComprehendMedicalClientTypes.OutputDataConfig?

    public init(
        clientRequestToken: Swift.String? = nil,
        dataAccessRoleArn: Swift.String? = nil,
        inputDataConfig: ComprehendMedicalClientTypes.InputDataConfig? = nil,
        jobName: Swift.String? = nil,
        kmsKey: Swift.String? = nil,
        languageCode: ComprehendMedicalClientTypes.LanguageCode? = nil,
        outputDataConfig: ComprehendMedicalClientTypes.OutputDataConfig? = nil
    )
    {
        self.clientRequestToken = clientRequestToken
        self.dataAccessRoleArn = dataAccessRoleArn
        self.inputDataConfig = inputDataConfig
        self.jobName = jobName
        self.kmsKey = kmsKey
        self.languageCode = languageCode
        self.outputDataConfig = outputDataConfig
    }
}

public struct StartRxNormInferenceJobOutput: Swift.Sendable {
    /// The identifier of the job.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StartSNOMEDCTInferenceJobInput: Swift.Sendable {
    /// A unique identifier for the request. If you don't set the client request token, Amazon Comprehend Medical generates one.
    public var clientRequestToken: Swift.String?
    /// The Amazon Resource Name (ARN) of the AWS Identity and Access Management (IAM) role that grants Amazon Comprehend Medical read access to your input data.
    /// This member is required.
    public var dataAccessRoleArn: Swift.String?
    /// The input properties for an entities detection job. This includes the name of the S3 bucket and the path to the files to be analyzed.
    /// This member is required.
    public var inputDataConfig: ComprehendMedicalClientTypes.InputDataConfig?
    /// The user generated name the asynchronous InferSNOMEDCT job.
    public var jobName: Swift.String?
    /// An AWS Key Management Service key used to encrypt your output files. If you do not specify a key, the files are written in plain text.
    public var kmsKey: Swift.String?
    /// The language of the input documents. All documents must be in the same language.
    /// This member is required.
    public var languageCode: ComprehendMedicalClientTypes.LanguageCode?
    /// The output properties for a detection job.
    /// This member is required.
    public var outputDataConfig: ComprehendMedicalClientTypes.OutputDataConfig?

    public init(
        clientRequestToken: Swift.String? = nil,
        dataAccessRoleArn: Swift.String? = nil,
        inputDataConfig: ComprehendMedicalClientTypes.InputDataConfig? = nil,
        jobName: Swift.String? = nil,
        kmsKey: Swift.String? = nil,
        languageCode: ComprehendMedicalClientTypes.LanguageCode? = nil,
        outputDataConfig: ComprehendMedicalClientTypes.OutputDataConfig? = nil
    )
    {
        self.clientRequestToken = clientRequestToken
        self.dataAccessRoleArn = dataAccessRoleArn
        self.inputDataConfig = inputDataConfig
        self.jobName = jobName
        self.kmsKey = kmsKey
        self.languageCode = languageCode
        self.outputDataConfig = outputDataConfig
    }
}

public struct StartSNOMEDCTInferenceJobOutput: Swift.Sendable {
    /// The identifier generated for the job. To get the status of a job, use this identifier with the StartSNOMEDCTInferenceJob operation.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StopEntitiesDetectionV2JobInput: Swift.Sendable {
    /// The identifier of the medical entities job to stop.
    /// This member is required.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StopEntitiesDetectionV2JobOutput: Swift.Sendable {
    /// The identifier of the medical entities detection job that was stopped.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StopICD10CMInferenceJobInput: Swift.Sendable {
    /// The identifier of the job.
    /// This member is required.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StopICD10CMInferenceJobOutput: Swift.Sendable {
    /// The identifier generated for the job. To get the status of job, use this identifier with the DescribeICD10CMInferenceJob operation.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StopPHIDetectionJobInput: Swift.Sendable {
    /// The identifier of the PHI detection job to stop.
    /// This member is required.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StopPHIDetectionJobOutput: Swift.Sendable {
    /// The identifier of the PHI detection job that was stopped.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StopRxNormInferenceJobInput: Swift.Sendable {
    /// The identifier of the job.
    /// This member is required.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StopRxNormInferenceJobOutput: Swift.Sendable {
    /// The identifier generated for the job. To get the status of job, use this identifier with the DescribeRxNormInferenceJob operation.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StopSNOMEDCTInferenceJobInput: Swift.Sendable {
    /// The job id of the asynchronous InferSNOMEDCT job to be stopped.
    /// This member is required.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

public struct StopSNOMEDCTInferenceJobOutput: Swift.Sendable {
    /// The identifier generated for the job. To get the status of job, use this identifier with the DescribeSNOMEDCTInferenceJob operation.
    public var jobId: Swift.String?

    public init(
        jobId: Swift.String? = nil
    )
    {
        self.jobId = jobId
    }
}

extension DescribeEntitiesDetectionV2JobInput {

    static func urlPathProvider(_ value: DescribeEntitiesDetectionV2JobInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeICD10CMInferenceJobInput {

    static func urlPathProvider(_ value: DescribeICD10CMInferenceJobInput) -> Swift.String? {
        return "/"
    }
}

extension DescribePHIDetectionJobInput {

    static func urlPathProvider(_ value: DescribePHIDetectionJobInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeRxNormInferenceJobInput {

    static func urlPathProvider(_ value: DescribeRxNormInferenceJobInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeSNOMEDCTInferenceJobInput {

    static func urlPathProvider(_ value: DescribeSNOMEDCTInferenceJobInput) -> Swift.String? {
        return "/"
    }
}

extension DetectEntitiesInput {

    static func urlPathProvider(_ value: DetectEntitiesInput) -> Swift.String? {
        return "/"
    }
}

extension DetectEntitiesV2Input {

    static func urlPathProvider(_ value: DetectEntitiesV2Input) -> Swift.String? {
        return "/"
    }
}

extension DetectPHIInput {

    static func urlPathProvider(_ value: DetectPHIInput) -> Swift.String? {
        return "/"
    }
}

extension InferICD10CMInput {

    static func urlPathProvider(_ value: InferICD10CMInput) -> Swift.String? {
        return "/"
    }
}

extension InferRxNormInput {

    static func urlPathProvider(_ value: InferRxNormInput) -> Swift.String? {
        return "/"
    }
}

extension InferSNOMEDCTInput {

    static func urlPathProvider(_ value: InferSNOMEDCTInput) -> Swift.String? {
        return "/"
    }
}

extension ListEntitiesDetectionV2JobsInput {

    static func urlPathProvider(_ value: ListEntitiesDetectionV2JobsInput) -> Swift.String? {
        return "/"
    }
}

extension ListICD10CMInferenceJobsInput {

    static func urlPathProvider(_ value: ListICD10CMInferenceJobsInput) -> Swift.String? {
        return "/"
    }
}

extension ListPHIDetectionJobsInput {

    static func urlPathProvider(_ value: ListPHIDetectionJobsInput) -> Swift.String? {
        return "/"
    }
}

extension ListRxNormInferenceJobsInput {

    static func urlPathProvider(_ value: ListRxNormInferenceJobsInput) -> Swift.String? {
        return "/"
    }
}

extension ListSNOMEDCTInferenceJobsInput {

    static func urlPathProvider(_ value: ListSNOMEDCTInferenceJobsInput) -> Swift.String? {
        return "/"
    }
}

extension StartEntitiesDetectionV2JobInput {

    static func urlPathProvider(_ value: StartEntitiesDetectionV2JobInput) -> Swift.String? {
        return "/"
    }
}

extension StartICD10CMInferenceJobInput {

    static func urlPathProvider(_ value: StartICD10CMInferenceJobInput) -> Swift.String? {
        return "/"
    }
}

extension StartPHIDetectionJobInput {

    static func urlPathProvider(_ value: StartPHIDetectionJobInput) -> Swift.String? {
        return "/"
    }
}

extension StartRxNormInferenceJobInput {

    static func urlPathProvider(_ value: StartRxNormInferenceJobInput) -> Swift.String? {
        return "/"
    }
}

extension StartSNOMEDCTInferenceJobInput {

    static func urlPathProvider(_ value: StartSNOMEDCTInferenceJobInput) -> Swift.String? {
        return "/"
    }
}

extension StopEntitiesDetectionV2JobInput {

    static func urlPathProvider(_ value: StopEntitiesDetectionV2JobInput) -> Swift.String? {
        return "/"
    }
}

extension StopICD10CMInferenceJobInput {

    static func urlPathProvider(_ value: StopICD10CMInferenceJobInput) -> Swift.String? {
        return "/"
    }
}

extension StopPHIDetectionJobInput {

    static func urlPathProvider(_ value: StopPHIDetectionJobInput) -> Swift.String? {
        return "/"
    }
}

extension StopRxNormInferenceJobInput {

    static func urlPathProvider(_ value: StopRxNormInferenceJobInput) -> Swift.String? {
        return "/"
    }
}

extension StopSNOMEDCTInferenceJobInput {

    static func urlPathProvider(_ value: StopSNOMEDCTInferenceJobInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeEntitiesDetectionV2JobInput {

    static func write(value: DescribeEntitiesDetectionV2JobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["JobId"].write(value.jobId)
    }
}

extension DescribeICD10CMInferenceJobInput {

    static func write(value: DescribeICD10CMInferenceJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["JobId"].write(value.jobId)
    }
}

extension DescribePHIDetectionJobInput {

    static func write(value: DescribePHIDetectionJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["JobId"].write(value.jobId)
    }
}

extension DescribeRxNormInferenceJobInput {

    static func write(value: DescribeRxNormInferenceJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["JobId"].write(value.jobId)
    }
}

extension DescribeSNOMEDCTInferenceJobInput {

    static func write(value: DescribeSNOMEDCTInferenceJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["JobId"].write(value.jobId)
    }
}

extension DetectEntitiesInput {

    static func write(value: DetectEntitiesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Text"].write(value.text)
    }
}

extension DetectEntitiesV2Input {

    static func write(value: DetectEntitiesV2Input?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Text"].write(value.text)
    }
}

extension DetectPHIInput {

    static func write(value: DetectPHIInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Text"].write(value.text)
    }
}

extension InferICD10CMInput {

    static func write(value: InferICD10CMInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Text"].write(value.text)
    }
}

extension InferRxNormInput {

    static func write(value: InferRxNormInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Text"].write(value.text)
    }
}

extension InferSNOMEDCTInput {

    static func write(value: InferSNOMEDCTInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Text"].write(value.text)
    }
}

extension ListEntitiesDetectionV2JobsInput {

    static func write(value: ListEntitiesDetectionV2JobsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Filter"].write(value.filter, with: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter.write(value:to:))
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListICD10CMInferenceJobsInput {

    static func write(value: ListICD10CMInferenceJobsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Filter"].write(value.filter, with: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter.write(value:to:))
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListPHIDetectionJobsInput {

    static func write(value: ListPHIDetectionJobsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Filter"].write(value.filter, with: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter.write(value:to:))
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListRxNormInferenceJobsInput {

    static func write(value: ListRxNormInferenceJobsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Filter"].write(value.filter, with: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter.write(value:to:))
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListSNOMEDCTInferenceJobsInput {

    static func write(value: ListSNOMEDCTInferenceJobsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Filter"].write(value.filter, with: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter.write(value:to:))
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension StartEntitiesDetectionV2JobInput {

    static func write(value: StartEntitiesDetectionV2JobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientRequestToken"].write(value.clientRequestToken)
        try writer["DataAccessRoleArn"].write(value.dataAccessRoleArn)
        try writer["InputDataConfig"].write(value.inputDataConfig, with: ComprehendMedicalClientTypes.InputDataConfig.write(value:to:))
        try writer["JobName"].write(value.jobName)
        try writer["KMSKey"].write(value.kmsKey)
        try writer["LanguageCode"].write(value.languageCode)
        try writer["OutputDataConfig"].write(value.outputDataConfig, with: ComprehendMedicalClientTypes.OutputDataConfig.write(value:to:))
    }
}

extension StartICD10CMInferenceJobInput {

    static func write(value: StartICD10CMInferenceJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientRequestToken"].write(value.clientRequestToken)
        try writer["DataAccessRoleArn"].write(value.dataAccessRoleArn)
        try writer["InputDataConfig"].write(value.inputDataConfig, with: ComprehendMedicalClientTypes.InputDataConfig.write(value:to:))
        try writer["JobName"].write(value.jobName)
        try writer["KMSKey"].write(value.kmsKey)
        try writer["LanguageCode"].write(value.languageCode)
        try writer["OutputDataConfig"].write(value.outputDataConfig, with: ComprehendMedicalClientTypes.OutputDataConfig.write(value:to:))
    }
}

extension StartPHIDetectionJobInput {

    static func write(value: StartPHIDetectionJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientRequestToken"].write(value.clientRequestToken)
        try writer["DataAccessRoleArn"].write(value.dataAccessRoleArn)
        try writer["InputDataConfig"].write(value.inputDataConfig, with: ComprehendMedicalClientTypes.InputDataConfig.write(value:to:))
        try writer["JobName"].write(value.jobName)
        try writer["KMSKey"].write(value.kmsKey)
        try writer["LanguageCode"].write(value.languageCode)
        try writer["OutputDataConfig"].write(value.outputDataConfig, with: ComprehendMedicalClientTypes.OutputDataConfig.write(value:to:))
    }
}

extension StartRxNormInferenceJobInput {

    static func write(value: StartRxNormInferenceJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientRequestToken"].write(value.clientRequestToken)
        try writer["DataAccessRoleArn"].write(value.dataAccessRoleArn)
        try writer["InputDataConfig"].write(value.inputDataConfig, with: ComprehendMedicalClientTypes.InputDataConfig.write(value:to:))
        try writer["JobName"].write(value.jobName)
        try writer["KMSKey"].write(value.kmsKey)
        try writer["LanguageCode"].write(value.languageCode)
        try writer["OutputDataConfig"].write(value.outputDataConfig, with: ComprehendMedicalClientTypes.OutputDataConfig.write(value:to:))
    }
}

extension StartSNOMEDCTInferenceJobInput {

    static func write(value: StartSNOMEDCTInferenceJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientRequestToken"].write(value.clientRequestToken)
        try writer["DataAccessRoleArn"].write(value.dataAccessRoleArn)
        try writer["InputDataConfig"].write(value.inputDataConfig, with: ComprehendMedicalClientTypes.InputDataConfig.write(value:to:))
        try writer["JobName"].write(value.jobName)
        try writer["KMSKey"].write(value.kmsKey)
        try writer["LanguageCode"].write(value.languageCode)
        try writer["OutputDataConfig"].write(value.outputDataConfig, with: ComprehendMedicalClientTypes.OutputDataConfig.write(value:to:))
    }
}

extension StopEntitiesDetectionV2JobInput {

    static func write(value: StopEntitiesDetectionV2JobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["JobId"].write(value.jobId)
    }
}

extension StopICD10CMInferenceJobInput {

    static func write(value: StopICD10CMInferenceJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["JobId"].write(value.jobId)
    }
}

extension StopPHIDetectionJobInput {

    static func write(value: StopPHIDetectionJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["JobId"].write(value.jobId)
    }
}

extension StopRxNormInferenceJobInput {

    static func write(value: StopRxNormInferenceJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["JobId"].write(value.jobId)
    }
}

extension StopSNOMEDCTInferenceJobInput {

    static func write(value: StopSNOMEDCTInferenceJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["JobId"].write(value.jobId)
    }
}

extension DescribeEntitiesDetectionV2JobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeEntitiesDetectionV2JobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeEntitiesDetectionV2JobOutput()
        value.comprehendMedicalAsyncJobProperties = try reader["ComprehendMedicalAsyncJobProperties"].readIfPresent(with: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties.read(from:))
        return value
    }
}

extension DescribeICD10CMInferenceJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeICD10CMInferenceJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeICD10CMInferenceJobOutput()
        value.comprehendMedicalAsyncJobProperties = try reader["ComprehendMedicalAsyncJobProperties"].readIfPresent(with: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties.read(from:))
        return value
    }
}

extension DescribePHIDetectionJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribePHIDetectionJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribePHIDetectionJobOutput()
        value.comprehendMedicalAsyncJobProperties = try reader["ComprehendMedicalAsyncJobProperties"].readIfPresent(with: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties.read(from:))
        return value
    }
}

extension DescribeRxNormInferenceJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeRxNormInferenceJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeRxNormInferenceJobOutput()
        value.comprehendMedicalAsyncJobProperties = try reader["ComprehendMedicalAsyncJobProperties"].readIfPresent(with: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties.read(from:))
        return value
    }
}

extension DescribeSNOMEDCTInferenceJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeSNOMEDCTInferenceJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeSNOMEDCTInferenceJobOutput()
        value.comprehendMedicalAsyncJobProperties = try reader["ComprehendMedicalAsyncJobProperties"].readIfPresent(with: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties.read(from:))
        return value
    }
}

extension DetectEntitiesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DetectEntitiesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DetectEntitiesOutput()
        value.entities = try reader["Entities"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.Entity.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.modelVersion = try reader["ModelVersion"].readIfPresent() ?? ""
        value.paginationToken = try reader["PaginationToken"].readIfPresent()
        value.unmappedAttributes = try reader["UnmappedAttributes"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.UnmappedAttribute.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DetectEntitiesV2Output {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DetectEntitiesV2Output {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DetectEntitiesV2Output()
        value.entities = try reader["Entities"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.Entity.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.modelVersion = try reader["ModelVersion"].readIfPresent() ?? ""
        value.paginationToken = try reader["PaginationToken"].readIfPresent()
        value.unmappedAttributes = try reader["UnmappedAttributes"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.UnmappedAttribute.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DetectPHIOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DetectPHIOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DetectPHIOutput()
        value.entities = try reader["Entities"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.Entity.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.modelVersion = try reader["ModelVersion"].readIfPresent() ?? ""
        value.paginationToken = try reader["PaginationToken"].readIfPresent()
        return value
    }
}

extension InferICD10CMOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> InferICD10CMOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = InferICD10CMOutput()
        value.entities = try reader["Entities"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.ICD10CMEntity.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.modelVersion = try reader["ModelVersion"].readIfPresent()
        value.paginationToken = try reader["PaginationToken"].readIfPresent()
        return value
    }
}

extension InferRxNormOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> InferRxNormOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = InferRxNormOutput()
        value.entities = try reader["Entities"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.RxNormEntity.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.modelVersion = try reader["ModelVersion"].readIfPresent()
        value.paginationToken = try reader["PaginationToken"].readIfPresent()
        return value
    }
}

extension InferSNOMEDCTOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> InferSNOMEDCTOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = InferSNOMEDCTOutput()
        value.characters = try reader["Characters"].readIfPresent(with: ComprehendMedicalClientTypes.Characters.read(from:))
        value.entities = try reader["Entities"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.SNOMEDCTEntity.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        value.modelVersion = try reader["ModelVersion"].readIfPresent()
        value.paginationToken = try reader["PaginationToken"].readIfPresent()
        value.snomedctDetails = try reader["SNOMEDCTDetails"].readIfPresent(with: ComprehendMedicalClientTypes.SNOMEDCTDetails.read(from:))
        return value
    }
}

extension ListEntitiesDetectionV2JobsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListEntitiesDetectionV2JobsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListEntitiesDetectionV2JobsOutput()
        value.comprehendMedicalAsyncJobPropertiesList = try reader["ComprehendMedicalAsyncJobPropertiesList"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListICD10CMInferenceJobsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListICD10CMInferenceJobsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListICD10CMInferenceJobsOutput()
        value.comprehendMedicalAsyncJobPropertiesList = try reader["ComprehendMedicalAsyncJobPropertiesList"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListPHIDetectionJobsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListPHIDetectionJobsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListPHIDetectionJobsOutput()
        value.comprehendMedicalAsyncJobPropertiesList = try reader["ComprehendMedicalAsyncJobPropertiesList"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListRxNormInferenceJobsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListRxNormInferenceJobsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListRxNormInferenceJobsOutput()
        value.comprehendMedicalAsyncJobPropertiesList = try reader["ComprehendMedicalAsyncJobPropertiesList"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListSNOMEDCTInferenceJobsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListSNOMEDCTInferenceJobsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListSNOMEDCTInferenceJobsOutput()
        value.comprehendMedicalAsyncJobPropertiesList = try reader["ComprehendMedicalAsyncJobPropertiesList"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension StartEntitiesDetectionV2JobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartEntitiesDetectionV2JobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartEntitiesDetectionV2JobOutput()
        value.jobId = try reader["JobId"].readIfPresent()
        return value
    }
}

extension StartICD10CMInferenceJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartICD10CMInferenceJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartICD10CMInferenceJobOutput()
        value.jobId = try reader["JobId"].readIfPresent()
        return value
    }
}

extension StartPHIDetectionJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartPHIDetectionJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartPHIDetectionJobOutput()
        value.jobId = try reader["JobId"].readIfPresent()
        return value
    }
}

extension StartRxNormInferenceJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartRxNormInferenceJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartRxNormInferenceJobOutput()
        value.jobId = try reader["JobId"].readIfPresent()
        return value
    }
}

extension StartSNOMEDCTInferenceJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartSNOMEDCTInferenceJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartSNOMEDCTInferenceJobOutput()
        value.jobId = try reader["JobId"].readIfPresent()
        return value
    }
}

extension StopEntitiesDetectionV2JobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopEntitiesDetectionV2JobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StopEntitiesDetectionV2JobOutput()
        value.jobId = try reader["JobId"].readIfPresent()
        return value
    }
}

extension StopICD10CMInferenceJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopICD10CMInferenceJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StopICD10CMInferenceJobOutput()
        value.jobId = try reader["JobId"].readIfPresent()
        return value
    }
}

extension StopPHIDetectionJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopPHIDetectionJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StopPHIDetectionJobOutput()
        value.jobId = try reader["JobId"].readIfPresent()
        return value
    }
}

extension StopRxNormInferenceJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopRxNormInferenceJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StopRxNormInferenceJobOutput()
        value.jobId = try reader["JobId"].readIfPresent()
        return value
    }
}

extension StopSNOMEDCTInferenceJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopSNOMEDCTInferenceJobOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StopSNOMEDCTInferenceJobOutput()
        value.jobId = try reader["JobId"].readIfPresent()
        return value
    }
}

enum DescribeEntitiesDetectionV2JobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeICD10CMInferenceJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribePHIDetectionJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeRxNormInferenceJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeSNOMEDCTInferenceJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DetectEntitiesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidEncodingException": return try InvalidEncodingException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ServiceUnavailableException": return try ServiceUnavailableException.makeError(baseError: baseError)
            case "TextSizeLimitExceededException": return try TextSizeLimitExceededException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DetectEntitiesV2OutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidEncodingException": return try InvalidEncodingException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ServiceUnavailableException": return try ServiceUnavailableException.makeError(baseError: baseError)
            case "TextSizeLimitExceededException": return try TextSizeLimitExceededException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DetectPHIOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidEncodingException": return try InvalidEncodingException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ServiceUnavailableException": return try ServiceUnavailableException.makeError(baseError: baseError)
            case "TextSizeLimitExceededException": return try TextSizeLimitExceededException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum InferICD10CMOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidEncodingException": return try InvalidEncodingException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ServiceUnavailableException": return try ServiceUnavailableException.makeError(baseError: baseError)
            case "TextSizeLimitExceededException": return try TextSizeLimitExceededException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum InferRxNormOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidEncodingException": return try InvalidEncodingException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ServiceUnavailableException": return try ServiceUnavailableException.makeError(baseError: baseError)
            case "TextSizeLimitExceededException": return try TextSizeLimitExceededException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum InferSNOMEDCTOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidEncodingException": return try InvalidEncodingException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ServiceUnavailableException": return try ServiceUnavailableException.makeError(baseError: baseError)
            case "TextSizeLimitExceededException": return try TextSizeLimitExceededException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListEntitiesDetectionV2JobsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListICD10CMInferenceJobsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListPHIDetectionJobsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListRxNormInferenceJobsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListSNOMEDCTInferenceJobsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartEntitiesDetectionV2JobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartICD10CMInferenceJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartPHIDetectionJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartRxNormInferenceJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartSNOMEDCTInferenceJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopEntitiesDetectionV2JobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopICD10CMInferenceJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopPHIDetectionJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopRxNormInferenceJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopSNOMEDCTInferenceJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "InvalidRequestException": return try InvalidRequestException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        var value = InternalServerException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TooManyRequestsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> TooManyRequestsException {
        let reader = baseError.errorBodyReader
        var value = TooManyRequestsException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidRequestException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidRequestException {
        let reader = baseError.errorBodyReader
        var value = InvalidRequestException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidEncodingException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidEncodingException {
        let reader = baseError.errorBodyReader
        var value = InvalidEncodingException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension TextSizeLimitExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> TextSizeLimitExceededException {
        let reader = baseError.errorBodyReader
        var value = TextSizeLimitExceededException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceUnavailableException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ServiceUnavailableException {
        let reader = baseError.errorBodyReader
        var value = ServiceUnavailableException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.message = try reader["Message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobProperties()
        value.jobId = try reader["JobId"].readIfPresent()
        value.jobName = try reader["JobName"].readIfPresent()
        value.jobStatus = try reader["JobStatus"].readIfPresent()
        value.message = try reader["Message"].readIfPresent()
        value.submitTime = try reader["SubmitTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.endTime = try reader["EndTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.expirationTime = try reader["ExpirationTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.inputDataConfig = try reader["InputDataConfig"].readIfPresent(with: ComprehendMedicalClientTypes.InputDataConfig.read(from:))
        value.outputDataConfig = try reader["OutputDataConfig"].readIfPresent(with: ComprehendMedicalClientTypes.OutputDataConfig.read(from:))
        value.languageCode = try reader["LanguageCode"].readIfPresent()
        value.dataAccessRoleArn = try reader["DataAccessRoleArn"].readIfPresent()
        value.manifestFilePath = try reader["ManifestFilePath"].readIfPresent()
        value.kmsKey = try reader["KMSKey"].readIfPresent()
        value.modelVersion = try reader["ModelVersion"].readIfPresent()
        return value
    }
}

extension ComprehendMedicalClientTypes.OutputDataConfig {

    static func write(value: ComprehendMedicalClientTypes.OutputDataConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["S3Bucket"].write(value.s3Bucket)
        try writer["S3Key"].write(value.s3Key)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.OutputDataConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.OutputDataConfig()
        value.s3Bucket = try reader["S3Bucket"].readIfPresent() ?? ""
        value.s3Key = try reader["S3Key"].readIfPresent()
        return value
    }
}

extension ComprehendMedicalClientTypes.InputDataConfig {

    static func write(value: ComprehendMedicalClientTypes.InputDataConfig?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["S3Bucket"].write(value.s3Bucket)
        try writer["S3Key"].write(value.s3Key)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.InputDataConfig {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.InputDataConfig()
        value.s3Bucket = try reader["S3Bucket"].readIfPresent() ?? ""
        value.s3Key = try reader["S3Key"].readIfPresent()
        return value
    }
}

extension ComprehendMedicalClientTypes.Entity {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.Entity {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.Entity()
        value.id = try reader["Id"].readIfPresent()
        value.beginOffset = try reader["BeginOffset"].readIfPresent()
        value.endOffset = try reader["EndOffset"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        value.text = try reader["Text"].readIfPresent()
        value.category = try reader["Category"].readIfPresent()
        value.type = try reader["Type"].readIfPresent()
        value.traits = try reader["Traits"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.Trait.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.attributes = try reader["Attributes"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.Attribute.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ComprehendMedicalClientTypes.Attribute {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.Attribute {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.Attribute()
        value.type = try reader["Type"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        value.relationshipScore = try reader["RelationshipScore"].readIfPresent()
        value.relationshipType = try reader["RelationshipType"].readIfPresent()
        value.id = try reader["Id"].readIfPresent()
        value.beginOffset = try reader["BeginOffset"].readIfPresent()
        value.endOffset = try reader["EndOffset"].readIfPresent()
        value.text = try reader["Text"].readIfPresent()
        value.category = try reader["Category"].readIfPresent()
        value.traits = try reader["Traits"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.Trait.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ComprehendMedicalClientTypes.Trait {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.Trait {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.Trait()
        value.name = try reader["Name"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        return value
    }
}

extension ComprehendMedicalClientTypes.UnmappedAttribute {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.UnmappedAttribute {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.UnmappedAttribute()
        value.type = try reader["Type"].readIfPresent()
        value.attribute = try reader["Attribute"].readIfPresent(with: ComprehendMedicalClientTypes.Attribute.read(from:))
        return value
    }
}

extension ComprehendMedicalClientTypes.ICD10CMEntity {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.ICD10CMEntity {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.ICD10CMEntity()
        value.id = try reader["Id"].readIfPresent()
        value.text = try reader["Text"].readIfPresent()
        value.category = try reader["Category"].readIfPresent()
        value.type = try reader["Type"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        value.beginOffset = try reader["BeginOffset"].readIfPresent()
        value.endOffset = try reader["EndOffset"].readIfPresent()
        value.attributes = try reader["Attributes"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.ICD10CMAttribute.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.traits = try reader["Traits"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.ICD10CMTrait.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.icd10CMConcepts = try reader["ICD10CMConcepts"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.ICD10CMConcept.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ComprehendMedicalClientTypes.ICD10CMConcept {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.ICD10CMConcept {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.ICD10CMConcept()
        value.description = try reader["Description"].readIfPresent()
        value.code = try reader["Code"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        return value
    }
}

extension ComprehendMedicalClientTypes.ICD10CMTrait {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.ICD10CMTrait {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.ICD10CMTrait()
        value.name = try reader["Name"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        return value
    }
}

extension ComprehendMedicalClientTypes.ICD10CMAttribute {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.ICD10CMAttribute {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.ICD10CMAttribute()
        value.type = try reader["Type"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        value.relationshipScore = try reader["RelationshipScore"].readIfPresent()
        value.id = try reader["Id"].readIfPresent()
        value.beginOffset = try reader["BeginOffset"].readIfPresent()
        value.endOffset = try reader["EndOffset"].readIfPresent()
        value.text = try reader["Text"].readIfPresent()
        value.traits = try reader["Traits"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.ICD10CMTrait.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.category = try reader["Category"].readIfPresent()
        value.relationshipType = try reader["RelationshipType"].readIfPresent()
        return value
    }
}

extension ComprehendMedicalClientTypes.RxNormEntity {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.RxNormEntity {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.RxNormEntity()
        value.id = try reader["Id"].readIfPresent()
        value.text = try reader["Text"].readIfPresent()
        value.category = try reader["Category"].readIfPresent()
        value.type = try reader["Type"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        value.beginOffset = try reader["BeginOffset"].readIfPresent()
        value.endOffset = try reader["EndOffset"].readIfPresent()
        value.attributes = try reader["Attributes"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.RxNormAttribute.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.traits = try reader["Traits"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.RxNormTrait.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.rxNormConcepts = try reader["RxNormConcepts"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.RxNormConcept.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ComprehendMedicalClientTypes.RxNormConcept {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.RxNormConcept {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.RxNormConcept()
        value.description = try reader["Description"].readIfPresent()
        value.code = try reader["Code"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        return value
    }
}

extension ComprehendMedicalClientTypes.RxNormTrait {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.RxNormTrait {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.RxNormTrait()
        value.name = try reader["Name"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        return value
    }
}

extension ComprehendMedicalClientTypes.RxNormAttribute {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.RxNormAttribute {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.RxNormAttribute()
        value.type = try reader["Type"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        value.relationshipScore = try reader["RelationshipScore"].readIfPresent()
        value.id = try reader["Id"].readIfPresent()
        value.beginOffset = try reader["BeginOffset"].readIfPresent()
        value.endOffset = try reader["EndOffset"].readIfPresent()
        value.text = try reader["Text"].readIfPresent()
        value.traits = try reader["Traits"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.RxNormTrait.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ComprehendMedicalClientTypes.SNOMEDCTEntity {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.SNOMEDCTEntity {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.SNOMEDCTEntity()
        value.id = try reader["Id"].readIfPresent()
        value.text = try reader["Text"].readIfPresent()
        value.category = try reader["Category"].readIfPresent()
        value.type = try reader["Type"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        value.beginOffset = try reader["BeginOffset"].readIfPresent()
        value.endOffset = try reader["EndOffset"].readIfPresent()
        value.attributes = try reader["Attributes"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.SNOMEDCTAttribute.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.traits = try reader["Traits"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.SNOMEDCTTrait.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.snomedctConcepts = try reader["SNOMEDCTConcepts"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.SNOMEDCTConcept.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ComprehendMedicalClientTypes.SNOMEDCTConcept {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.SNOMEDCTConcept {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.SNOMEDCTConcept()
        value.description = try reader["Description"].readIfPresent()
        value.code = try reader["Code"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        return value
    }
}

extension ComprehendMedicalClientTypes.SNOMEDCTTrait {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.SNOMEDCTTrait {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.SNOMEDCTTrait()
        value.name = try reader["Name"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        return value
    }
}

extension ComprehendMedicalClientTypes.SNOMEDCTAttribute {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.SNOMEDCTAttribute {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.SNOMEDCTAttribute()
        value.category = try reader["Category"].readIfPresent()
        value.type = try reader["Type"].readIfPresent()
        value.score = try reader["Score"].readIfPresent()
        value.relationshipScore = try reader["RelationshipScore"].readIfPresent()
        value.relationshipType = try reader["RelationshipType"].readIfPresent()
        value.id = try reader["Id"].readIfPresent()
        value.beginOffset = try reader["BeginOffset"].readIfPresent()
        value.endOffset = try reader["EndOffset"].readIfPresent()
        value.text = try reader["Text"].readIfPresent()
        value.traits = try reader["Traits"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.SNOMEDCTTrait.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.snomedctConcepts = try reader["SNOMEDCTConcepts"].readListIfPresent(memberReadingClosure: ComprehendMedicalClientTypes.SNOMEDCTConcept.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension ComprehendMedicalClientTypes.SNOMEDCTDetails {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.SNOMEDCTDetails {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.SNOMEDCTDetails()
        value.edition = try reader["Edition"].readIfPresent()
        value.language = try reader["Language"].readIfPresent()
        value.versionDate = try reader["VersionDate"].readIfPresent()
        return value
    }
}

extension ComprehendMedicalClientTypes.Characters {

    static func read(from reader: SmithyJSON.Reader) throws -> ComprehendMedicalClientTypes.Characters {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = ComprehendMedicalClientTypes.Characters()
        value.originalTextCharacters = try reader["OriginalTextCharacters"].readIfPresent()
        return value
    }
}

extension ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter {

    static func write(value: ComprehendMedicalClientTypes.ComprehendMedicalAsyncJobFilter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["JobName"].write(value.jobName)
        try writer["JobStatus"].write(value.jobStatus)
        try writer["SubmitTimeAfter"].writeTimestamp(value.submitTimeAfter, format: SmithyTimestamps.TimestampFormat.epochSeconds)
        try writer["SubmitTimeBefore"].writeTimestamp(value.submitTimeBefore, format: SmithyTimestamps.TimestampFormat.epochSeconds)
    }
}

public enum ComprehendMedicalClientTypes {}
