//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import Foundation
import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension CloudWatchClient {
    /// Paginate over `[DescribeAlarmHistoryOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeAlarmHistoryInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeAlarmHistoryOutput`
    public func describeAlarmHistoryPaginated(input: DescribeAlarmHistoryInput) -> ClientRuntime.PaginatorSequence<DescribeAlarmHistoryInput, DescribeAlarmHistoryOutput> {
        return ClientRuntime.PaginatorSequence<DescribeAlarmHistoryInput, DescribeAlarmHistoryOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeAlarmHistory(input:))
    }
}

extension DescribeAlarmHistoryInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeAlarmHistoryInput {
        return DescribeAlarmHistoryInput(
            alarmName: self.alarmName,
            alarmTypes: self.alarmTypes,
            endDate: self.endDate,
            historyItemType: self.historyItemType,
            maxRecords: self.maxRecords,
            nextToken: token,
            scanBy: self.scanBy,
            startDate: self.startDate
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeAlarmHistoryInput, OperationStackOutput == DescribeAlarmHistoryOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeAlarmHistoryPaginated`
    /// to access the nested member `[CloudWatchClientTypes.AlarmHistoryItem]`
    /// - Returns: `[CloudWatchClientTypes.AlarmHistoryItem]`
    public func alarmHistoryItems() async throws -> [CloudWatchClientTypes.AlarmHistoryItem] {
        return try await self.asyncCompactMap { item in item.alarmHistoryItems }
    }
}
extension CloudWatchClient {
    /// Paginate over `[DescribeAlarmsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeAlarmsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeAlarmsOutput`
    public func describeAlarmsPaginated(input: DescribeAlarmsInput) -> ClientRuntime.PaginatorSequence<DescribeAlarmsInput, DescribeAlarmsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeAlarmsInput, DescribeAlarmsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeAlarms(input:))
    }
}

extension DescribeAlarmsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeAlarmsInput {
        return DescribeAlarmsInput(
            actionPrefix: self.actionPrefix,
            alarmNamePrefix: self.alarmNamePrefix,
            alarmNames: self.alarmNames,
            alarmTypes: self.alarmTypes,
            childrenOfAlarmName: self.childrenOfAlarmName,
            maxRecords: self.maxRecords,
            nextToken: token,
            parentsOfAlarmName: self.parentsOfAlarmName,
            stateValue: self.stateValue
        )}
}
extension CloudWatchClient {
    /// Paginate over `[DescribeAnomalyDetectorsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeAnomalyDetectorsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeAnomalyDetectorsOutput`
    public func describeAnomalyDetectorsPaginated(input: DescribeAnomalyDetectorsInput) -> ClientRuntime.PaginatorSequence<DescribeAnomalyDetectorsInput, DescribeAnomalyDetectorsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeAnomalyDetectorsInput, DescribeAnomalyDetectorsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeAnomalyDetectors(input:))
    }
}

extension DescribeAnomalyDetectorsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeAnomalyDetectorsInput {
        return DescribeAnomalyDetectorsInput(
            anomalyDetectorTypes: self.anomalyDetectorTypes,
            dimensions: self.dimensions,
            maxResults: self.maxResults,
            metricName: self.metricName,
            namespace: self.namespace,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeAnomalyDetectorsInput, OperationStackOutput == DescribeAnomalyDetectorsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeAnomalyDetectorsPaginated`
    /// to access the nested member `[CloudWatchClientTypes.AnomalyDetector]`
    /// - Returns: `[CloudWatchClientTypes.AnomalyDetector]`
    public func anomalyDetectors() async throws -> [CloudWatchClientTypes.AnomalyDetector] {
        return try await self.asyncCompactMap { item in item.anomalyDetectors }
    }
}
extension CloudWatchClient {
    /// Paginate over `[DescribeInsightRulesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeInsightRulesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeInsightRulesOutput`
    public func describeInsightRulesPaginated(input: DescribeInsightRulesInput) -> ClientRuntime.PaginatorSequence<DescribeInsightRulesInput, DescribeInsightRulesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeInsightRulesInput, DescribeInsightRulesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeInsightRules(input:))
    }
}

extension DescribeInsightRulesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeInsightRulesInput {
        return DescribeInsightRulesInput(
            maxResults: self.maxResults,
            nextToken: token
        )}
}
extension CloudWatchClient {
    /// Paginate over `[GetMetricDataOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[GetMetricDataInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `GetMetricDataOutput`
    public func getMetricDataPaginated(input: GetMetricDataInput) -> ClientRuntime.PaginatorSequence<GetMetricDataInput, GetMetricDataOutput> {
        return ClientRuntime.PaginatorSequence<GetMetricDataInput, GetMetricDataOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.getMetricData(input:))
    }
}

extension GetMetricDataInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> GetMetricDataInput {
        return GetMetricDataInput(
            endTime: self.endTime,
            labelOptions: self.labelOptions,
            maxDatapoints: self.maxDatapoints,
            metricDataQueries: self.metricDataQueries,
            nextToken: token,
            scanBy: self.scanBy,
            startTime: self.startTime
        )}
}
extension CloudWatchClient {
    /// Paginate over `[ListDashboardsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListDashboardsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListDashboardsOutput`
    public func listDashboardsPaginated(input: ListDashboardsInput) -> ClientRuntime.PaginatorSequence<ListDashboardsInput, ListDashboardsOutput> {
        return ClientRuntime.PaginatorSequence<ListDashboardsInput, ListDashboardsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listDashboards(input:))
    }
}

extension ListDashboardsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListDashboardsInput {
        return ListDashboardsInput(
            dashboardNamePrefix: self.dashboardNamePrefix,
            nextToken: token
        )}
}

extension PaginatorSequence where OperationStackInput == ListDashboardsInput, OperationStackOutput == ListDashboardsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `listDashboardsPaginated`
    /// to access the nested member `[CloudWatchClientTypes.DashboardEntry]`
    /// - Returns: `[CloudWatchClientTypes.DashboardEntry]`
    public func dashboardEntries() async throws -> [CloudWatchClientTypes.DashboardEntry] {
        return try await self.asyncCompactMap { item in item.dashboardEntries }
    }
}
extension CloudWatchClient {
    /// Paginate over `[ListManagedInsightRulesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListManagedInsightRulesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListManagedInsightRulesOutput`
    public func listManagedInsightRulesPaginated(input: ListManagedInsightRulesInput) -> ClientRuntime.PaginatorSequence<ListManagedInsightRulesInput, ListManagedInsightRulesOutput> {
        return ClientRuntime.PaginatorSequence<ListManagedInsightRulesInput, ListManagedInsightRulesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listManagedInsightRules(input:))
    }
}

extension ListManagedInsightRulesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListManagedInsightRulesInput {
        return ListManagedInsightRulesInput(
            maxResults: self.maxResults,
            nextToken: token,
            resourceARN: self.resourceARN
        )}
}
extension CloudWatchClient {
    /// Paginate over `[ListMetricsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListMetricsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListMetricsOutput`
    public func listMetricsPaginated(input: ListMetricsInput) -> ClientRuntime.PaginatorSequence<ListMetricsInput, ListMetricsOutput> {
        return ClientRuntime.PaginatorSequence<ListMetricsInput, ListMetricsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listMetrics(input:))
    }
}

extension ListMetricsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListMetricsInput {
        return ListMetricsInput(
            dimensions: self.dimensions,
            includeLinkedAccounts: self.includeLinkedAccounts,
            metricName: self.metricName,
            namespace: self.namespace,
            nextToken: token,
            owningAccount: self.owningAccount,
            recentlyActive: self.recentlyActive
        )}
}
extension CloudWatchClient {
    /// Paginate over `[ListMetricStreamsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListMetricStreamsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListMetricStreamsOutput`
    public func listMetricStreamsPaginated(input: ListMetricStreamsInput) -> ClientRuntime.PaginatorSequence<ListMetricStreamsInput, ListMetricStreamsOutput> {
        return ClientRuntime.PaginatorSequence<ListMetricStreamsInput, ListMetricStreamsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listMetricStreams(input:))
    }
}

extension ListMetricStreamsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListMetricStreamsInput {
        return ListMetricStreamsInput(
            maxResults: self.maxResults,
            nextToken: token
        )}
}
