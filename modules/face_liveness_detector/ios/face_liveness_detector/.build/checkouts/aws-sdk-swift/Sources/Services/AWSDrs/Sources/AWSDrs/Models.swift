//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHTTPAPI.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum Smithy.ClientError
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
@_spi(SmithyReadWrite) import func SmithyReadWrite.listReadingClosure
@_spi(SmithyReadWrite) import func SmithyReadWrite.mapReadingClosure
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.RestJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError
import struct Smithy.URIQueryItem


public struct DeleteRecoveryInstanceOutput: Swift.Sendable {

    public init() { }
}

public struct DisconnectRecoveryInstanceOutput: Swift.Sendable {

    public init() { }
}

public struct StopFailbackOutput: Swift.Sendable {

    public init() { }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateFailbackReplicationConfigurationOutput: Swift.Sendable {

    public init() { }
}

/// You do not have sufficient access to perform this action.
public struct AccessDeniedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var code: Swift.String? = nil
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "AccessDeniedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        code: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.code = code
        self.properties.message = message
    }
}

extension DrsClientTypes {

    /// AWS account.
    public struct Account: Swift.Sendable {
        /// Account ID of AWS account.
        public var accountID: Swift.String?

        public init(
            accountID: Swift.String? = nil
        )
        {
            self.accountID = accountID
        }
    }
}

/// The request could not be completed due to a conflict with the current state of the target resource.
public struct ConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var code: Swift.String? = nil
        public internal(set) var message: Swift.String? = nil
        /// The ID of the resource.
        public internal(set) var resourceId: Swift.String? = nil
        /// The type of the resource.
        public internal(set) var resourceType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        code: Swift.String? = nil,
        message: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: Swift.String? = nil
    )
    {
        self.properties.code = code
        self.properties.message = message
        self.properties.resourceId = resourceId
        self.properties.resourceType = resourceType
    }
}

/// The request processing has failed because of an unknown error, exception or failure.
public struct InternalServerException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// The number of seconds after which the request should be safe to retry.
        public internal(set) var retryAfterSeconds: Swift.Int = 0
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalServerException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        retryAfterSeconds: Swift.Int = 0
    )
    {
        self.properties.message = message
        self.properties.retryAfterSeconds = retryAfterSeconds
    }
}

/// The resource for this operation was not found.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var code: Swift.String? = nil
        public internal(set) var message: Swift.String? = nil
        /// The ID of the resource.
        public internal(set) var resourceId: Swift.String? = nil
        /// The type of the resource.
        public internal(set) var resourceType: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        code: Swift.String? = nil,
        message: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: Swift.String? = nil
    )
    {
        self.properties.code = code
        self.properties.message = message
        self.properties.resourceId = resourceId
        self.properties.resourceType = resourceType
    }
}

/// The request could not be completed because its exceeded the service quota.
public struct ServiceQuotaExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var code: Swift.String? = nil
        public internal(set) var message: Swift.String? = nil
        /// Quota code.
        public internal(set) var quotaCode: Swift.String? = nil
        /// The ID of the resource.
        public internal(set) var resourceId: Swift.String? = nil
        /// The type of the resource.
        public internal(set) var resourceType: Swift.String? = nil
        /// Service code.
        public internal(set) var serviceCode: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ServiceQuotaExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        code: Swift.String? = nil,
        message: Swift.String? = nil,
        quotaCode: Swift.String? = nil,
        resourceId: Swift.String? = nil,
        resourceType: Swift.String? = nil,
        serviceCode: Swift.String? = nil
    )
    {
        self.properties.code = code
        self.properties.message = message
        self.properties.quotaCode = quotaCode
        self.properties.resourceId = resourceId
        self.properties.resourceType = resourceType
        self.properties.serviceCode = serviceCode
    }
}

/// The request was denied due to request throttling.
public struct ThrottlingException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This member is required.
        public internal(set) var message: Swift.String? = nil
        /// Quota code.
        public internal(set) var quotaCode: Swift.String? = nil
        /// The number of seconds after which the request should be safe to retry.
        public internal(set) var retryAfterSeconds: Swift.String? = nil
        /// Service code.
        public internal(set) var serviceCode: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ThrottlingException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil,
        quotaCode: Swift.String? = nil,
        retryAfterSeconds: Swift.String? = nil,
        serviceCode: Swift.String? = nil
    )
    {
        self.properties.message = message
        self.properties.quotaCode = quotaCode
        self.properties.retryAfterSeconds = retryAfterSeconds
        self.properties.serviceCode = serviceCode
    }
}

/// The account performing the request has not been initialized.
public struct UninitializedAccountException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var code: Swift.String? = nil
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "UninitializedAccountException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        code: Swift.String? = nil,
        message: Swift.String? = nil
    )
    {
        self.properties.code = code
        self.properties.message = message
    }
}

extension DrsClientTypes {

    /// Validate exception field.
    public struct ValidationExceptionField: Swift.Sendable {
        /// Validate exception field message.
        public var message: Swift.String?
        /// Validate exception field name.
        public var name: Swift.String?

        public init(
            message: Swift.String? = nil,
            name: Swift.String? = nil
        )
        {
            self.message = message
            self.name = name
        }
    }
}

extension DrsClientTypes {

    public enum ValidationExceptionReason: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cannotParse
        case fieldValidationFailed
        case other
        case unknownOperation
        case sdkUnknown(Swift.String)

        public static var allCases: [ValidationExceptionReason] {
            return [
                .cannotParse,
                .fieldValidationFailed,
                .other,
                .unknownOperation
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cannotParse: return "cannotParse"
            case .fieldValidationFailed: return "fieldValidationFailed"
            case .other: return "other"
            case .unknownOperation: return "unknownOperation"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// The input fails to satisfy the constraints specified by the AWS service.
public struct ValidationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        public internal(set) var code: Swift.String? = nil
        /// A list of fields that failed validation.
        public internal(set) var fieldList: [DrsClientTypes.ValidationExceptionField]? = nil
        public internal(set) var message: Swift.String? = nil
        /// Validation exception reason.
        public internal(set) var reason: DrsClientTypes.ValidationExceptionReason? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ValidationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        code: Swift.String? = nil,
        fieldList: [DrsClientTypes.ValidationExceptionField]? = nil,
        message: Swift.String? = nil,
        reason: DrsClientTypes.ValidationExceptionReason? = nil
    )
    {
        self.properties.code = code
        self.properties.fieldList = fieldList
        self.properties.message = message
        self.properties.reason = reason
    }
}

public struct AssociateSourceNetworkStackInput: Swift.Sendable {
    /// CloudFormation template to associate with a Source Network.
    /// This member is required.
    public var cfnStackName: Swift.String?
    /// The Source Network ID to associate with CloudFormation template.
    /// This member is required.
    public var sourceNetworkID: Swift.String?

    public init(
        cfnStackName: Swift.String? = nil,
        sourceNetworkID: Swift.String? = nil
    )
    {
        self.cfnStackName = cfnStackName
        self.sourceNetworkID = sourceNetworkID
    }
}

extension AssociateSourceNetworkStackInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "AssociateSourceNetworkStackInput(sourceNetworkID: \(Swift.String(describing: sourceNetworkID)), cfnStackName: \"CONTENT_REDACTED\")"}
}

extension DrsClientTypes {

    public enum InitiatedBy: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case associateNetworkRecovery
        case createNetworkRecovery
        case diagnostic
        case failback
        case startDrill
        case startRecovery
        case targetAccount
        case terminateRecoveryInstances
        case updateNetworkRecovery
        case sdkUnknown(Swift.String)

        public static var allCases: [InitiatedBy] {
            return [
                .associateNetworkRecovery,
                .createNetworkRecovery,
                .diagnostic,
                .failback,
                .startDrill,
                .startRecovery,
                .targetAccount,
                .terminateRecoveryInstances,
                .updateNetworkRecovery
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .associateNetworkRecovery: return "ASSOCIATE_NETWORK_RECOVERY"
            case .createNetworkRecovery: return "CREATE_NETWORK_RECOVERY"
            case .diagnostic: return "DIAGNOSTIC"
            case .failback: return "FAILBACK"
            case .startDrill: return "START_DRILL"
            case .startRecovery: return "START_RECOVERY"
            case .targetAccount: return "TARGET_ACCOUNT"
            case .terminateRecoveryInstances: return "TERMINATE_RECOVERY_INSTANCES"
            case .updateNetworkRecovery: return "UPDATE_NETWORK_RECOVERY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    public enum LaunchStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case inProgress
        case launched
        case pending
        case terminated
        case sdkUnknown(Swift.String)

        public static var allCases: [LaunchStatus] {
            return [
                .failed,
                .inProgress,
                .launched,
                .pending,
                .terminated
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case .launched: return "LAUNCHED"
            case .pending: return "PENDING"
            case .terminated: return "TERMINATED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// ID of a resource participating in an asynchronous Job.
    public enum ParticipatingResourceID: Swift.Sendable {
        /// Source Network ID.
        case sourcenetworkid(Swift.String)
        case sdkUnknown(Swift.String)
    }
}

extension DrsClientTypes {

    /// Represents a resource participating in an asynchronous Job.
    public struct ParticipatingResource: Swift.Sendable {
        /// The launch status of a participating resource.
        public var launchStatus: DrsClientTypes.LaunchStatus?
        /// The ID of a participating resource.
        public var participatingResourceID: DrsClientTypes.ParticipatingResourceID?

        public init(
            launchStatus: DrsClientTypes.LaunchStatus? = nil,
            participatingResourceID: DrsClientTypes.ParticipatingResourceID? = nil
        )
        {
            self.launchStatus = launchStatus
            self.participatingResourceID = participatingResourceID
        }
    }
}

extension DrsClientTypes {

    /// Launch action category.
    public enum LaunchActionCategory: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case configuration
        case monitoring
        case other
        case security
        case validation
        case sdkUnknown(Swift.String)

        public static var allCases: [LaunchActionCategory] {
            return [
                .configuration,
                .monitoring,
                .other,
                .security,
                .validation
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .configuration: return "CONFIGURATION"
            case .monitoring: return "MONITORING"
            case .other: return "OTHER"
            case .security: return "SECURITY"
            case .validation: return "VALIDATION"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    public enum LaunchActionParameterType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case `dynamic`
        case ssmStore
        case sdkUnknown(Swift.String)

        public static var allCases: [LaunchActionParameterType] {
            return [
                .dynamic,
                .ssmStore
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .dynamic: return "DYNAMIC"
            case .ssmStore: return "SSM_STORE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// Launch action parameter.
    public struct LaunchActionParameter: Swift.Sendable {
        /// Type.
        public var type: DrsClientTypes.LaunchActionParameterType?
        /// Value.
        public var value: Swift.String?

        public init(
            type: DrsClientTypes.LaunchActionParameterType? = nil,
            value: Swift.String? = nil
        )
        {
            self.type = type
            self.value = value
        }
    }
}

extension DrsClientTypes {

    public enum LaunchActionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case ssmAutomation
        case ssmCommand
        case sdkUnknown(Swift.String)

        public static var allCases: [LaunchActionType] {
            return [
                .ssmAutomation,
                .ssmCommand
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .ssmAutomation: return "SSM_AUTOMATION"
            case .ssmCommand: return "SSM_COMMAND"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// Launch action.
    public struct LaunchAction: Swift.Sendable {
        /// Launch action code.
        public var actionCode: Swift.String?
        /// Launch action Id.
        public var actionId: Swift.String?
        /// Launch action version.
        public var actionVersion: Swift.String?
        /// Whether the launch action is active.
        public var active: Swift.Bool?
        /// Launch action category.
        public var category: DrsClientTypes.LaunchActionCategory?
        /// Launch action description.
        public var description: Swift.String?
        /// Launch action name.
        public var name: Swift.String?
        /// Whether the launch will not be marked as failed if this action fails.
        public var `optional`: Swift.Bool?
        /// Launch action order.
        public var order: Swift.Int?
        /// Launch action parameters.
        public var parameters: [Swift.String: DrsClientTypes.LaunchActionParameter]?
        /// Launch action type.
        public var type: DrsClientTypes.LaunchActionType?

        public init(
            actionCode: Swift.String? = nil,
            actionId: Swift.String? = nil,
            actionVersion: Swift.String? = nil,
            active: Swift.Bool? = nil,
            category: DrsClientTypes.LaunchActionCategory? = nil,
            description: Swift.String? = nil,
            name: Swift.String? = nil,
            `optional`: Swift.Bool? = nil,
            order: Swift.Int? = nil,
            parameters: [Swift.String: DrsClientTypes.LaunchActionParameter]? = nil,
            type: DrsClientTypes.LaunchActionType? = nil
        )
        {
            self.actionCode = actionCode
            self.actionId = actionId
            self.actionVersion = actionVersion
            self.active = active
            self.category = category
            self.description = description
            self.name = name
            self.`optional` = `optional`
            self.order = order
            self.parameters = parameters
            self.type = type
        }
    }
}

extension DrsClientTypes {

    public enum LaunchActionRunStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case inProgress
        case succeeded
        case sdkUnknown(Swift.String)

        public static var allCases: [LaunchActionRunStatus] {
            return [
                .failed,
                .inProgress,
                .succeeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case .succeeded: return "SUCCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// Launch action run.
    public struct LaunchActionRun: Swift.Sendable {
        /// Action.
        public var action: DrsClientTypes.LaunchAction?
        /// Failure reason.
        public var failureReason: Swift.String?
        /// Run Id.
        public var runId: Swift.String?
        /// Run status.
        public var status: DrsClientTypes.LaunchActionRunStatus?

        public init(
            action: DrsClientTypes.LaunchAction? = nil,
            failureReason: Swift.String? = nil,
            runId: Swift.String? = nil,
            status: DrsClientTypes.LaunchActionRunStatus? = nil
        )
        {
            self.action = action
            self.failureReason = failureReason
            self.runId = runId
            self.status = status
        }
    }
}

extension DrsClientTypes {

    /// Launch actions status.
    public struct LaunchActionsStatus: Swift.Sendable {
        /// List of post launch action status.
        public var runs: [DrsClientTypes.LaunchActionRun]?
        /// Time where the AWS Systems Manager was detected as running on the launched instance.
        public var ssmAgentDiscoveryDatetime: Swift.String?

        public init(
            runs: [DrsClientTypes.LaunchActionRun]? = nil,
            ssmAgentDiscoveryDatetime: Swift.String? = nil
        )
        {
            self.runs = runs
            self.ssmAgentDiscoveryDatetime = ssmAgentDiscoveryDatetime
        }
    }
}

extension DrsClientTypes {

    /// Represents a server participating in an asynchronous Job.
    public struct ParticipatingServer: Swift.Sendable {
        /// The post-launch action runs of a participating server.
        public var launchActionsStatus: DrsClientTypes.LaunchActionsStatus?
        /// The launch status of a participating server.
        public var launchStatus: DrsClientTypes.LaunchStatus?
        /// The Recovery Instance ID of a participating server.
        public var recoveryInstanceID: Swift.String?
        /// The Source Server ID of a participating server.
        public var sourceServerID: Swift.String?

        public init(
            launchActionsStatus: DrsClientTypes.LaunchActionsStatus? = nil,
            launchStatus: DrsClientTypes.LaunchStatus? = nil,
            recoveryInstanceID: Swift.String? = nil,
            sourceServerID: Swift.String? = nil
        )
        {
            self.launchActionsStatus = launchActionsStatus
            self.launchStatus = launchStatus
            self.recoveryInstanceID = recoveryInstanceID
            self.sourceServerID = sourceServerID
        }
    }
}

extension DrsClientTypes {

    public enum JobStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case completed
        case pending
        case started
        case sdkUnknown(Swift.String)

        public static var allCases: [JobStatus] {
            return [
                .completed,
                .pending,
                .started
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .completed: return "COMPLETED"
            case .pending: return "PENDING"
            case .started: return "STARTED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    public enum JobType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case createConvertedSnapshot
        case launch
        case terminate
        case sdkUnknown(Swift.String)

        public static var allCases: [JobType] {
            return [
                .createConvertedSnapshot,
                .launch,
                .terminate
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .createConvertedSnapshot: return "CREATE_CONVERTED_SNAPSHOT"
            case .launch: return "LAUNCH"
            case .terminate: return "TERMINATE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// A job is an asynchronous workflow.
    public struct Job: Swift.Sendable {
        /// The ARN of a Job.
        public var arn: Swift.String?
        /// The date and time of when the Job was created.
        public var creationDateTime: Swift.String?
        /// The date and time of when the Job ended.
        public var endDateTime: Swift.String?
        /// A string representing who initiated the Job.
        public var initiatedBy: DrsClientTypes.InitiatedBy?
        /// The ID of the Job.
        /// This member is required.
        public var jobID: Swift.String?
        /// A list of resources that the Job is acting upon.
        public var participatingResources: [DrsClientTypes.ParticipatingResource]?
        /// A list of servers that the Job is acting upon.
        public var participatingServers: [DrsClientTypes.ParticipatingServer]?
        /// The status of the Job.
        public var status: DrsClientTypes.JobStatus?
        /// A list of tags associated with the Job.
        public var tags: [Swift.String: Swift.String]?
        /// The type of the Job.
        public var type: DrsClientTypes.JobType?

        public init(
            arn: Swift.String? = nil,
            creationDateTime: Swift.String? = nil,
            endDateTime: Swift.String? = nil,
            initiatedBy: DrsClientTypes.InitiatedBy? = nil,
            jobID: Swift.String? = nil,
            participatingResources: [DrsClientTypes.ParticipatingResource]? = nil,
            participatingServers: [DrsClientTypes.ParticipatingServer]? = nil,
            status: DrsClientTypes.JobStatus? = nil,
            tags: [Swift.String: Swift.String]? = nil,
            type: DrsClientTypes.JobType? = nil
        )
        {
            self.arn = arn
            self.creationDateTime = creationDateTime
            self.endDateTime = endDateTime
            self.initiatedBy = initiatedBy
            self.jobID = jobID
            self.participatingResources = participatingResources
            self.participatingServers = participatingServers
            self.status = status
            self.tags = tags
            self.type = type
        }
    }
}

extension DrsClientTypes.Job: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "Job(arn: \(Swift.String(describing: arn)), creationDateTime: \(Swift.String(describing: creationDateTime)), endDateTime: \(Swift.String(describing: endDateTime)), initiatedBy: \(Swift.String(describing: initiatedBy)), jobID: \(Swift.String(describing: jobID)), participatingResources: \(Swift.String(describing: participatingResources)), participatingServers: \(Swift.String(describing: participatingServers)), status: \(Swift.String(describing: status)), type: \(Swift.String(describing: type)), tags: \"CONTENT_REDACTED\")"}
}

public struct AssociateSourceNetworkStackOutput: Swift.Sendable {
    /// The Source Network association Job.
    public var job: DrsClientTypes.Job?

    public init(
        job: DrsClientTypes.Job? = nil
    )
    {
        self.job = job
    }
}

extension DrsClientTypes {

    public enum ProductCodeMode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case disabled
        case enabled
        case sdkUnknown(Swift.String)

        public static var allCases: [ProductCodeMode] {
            return [
                .disabled,
                .enabled
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .disabled: return "DISABLED"
            case .enabled: return "ENABLED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// Properties of a product code associated with a volume.
    public struct ProductCode: Swift.Sendable {
        /// Id of a product code associated with a volume.
        public var productCodeId: Swift.String?
        /// Mode of a product code associated with a volume.
        public var productCodeMode: DrsClientTypes.ProductCodeMode?

        public init(
            productCodeId: Swift.String? = nil,
            productCodeMode: DrsClientTypes.ProductCodeMode? = nil
        )
        {
            self.productCodeId = productCodeId
            self.productCodeMode = productCodeMode
        }
    }
}

extension DrsClientTypes {

    /// Properties of a conversion job
    public struct ConversionProperties: Swift.Sendable {
        /// The timestamp of when the snapshot being converted was taken
        public var dataTimestamp: Swift.String?
        /// Whether the volume being converted uses UEFI or not
        public var forceUefi: Swift.Bool?
        /// The root volume name of a conversion job
        public var rootVolumeName: Swift.String?
        /// A mapping between the volumes being converted and the converted snapshot ids
        public var volumeToConversionMap: [Swift.String: [Swift.String: Swift.String]]?
        /// A mapping between the volumes being converted and the product codes associated with them
        public var volumeToProductCodes: [Swift.String: [DrsClientTypes.ProductCode]]?
        /// A mapping between the volumes and their sizes
        public var volumeToVolumeSize: [Swift.String: Swift.Int]?

        public init(
            dataTimestamp: Swift.String? = nil,
            forceUefi: Swift.Bool? = nil,
            rootVolumeName: Swift.String? = nil,
            volumeToConversionMap: [Swift.String: [Swift.String: Swift.String]]? = nil,
            volumeToProductCodes: [Swift.String: [DrsClientTypes.ProductCode]]? = nil,
            volumeToVolumeSize: [Swift.String: Swift.Int]? = nil
        )
        {
            self.dataTimestamp = dataTimestamp
            self.forceUefi = forceUefi
            self.rootVolumeName = rootVolumeName
            self.volumeToConversionMap = volumeToConversionMap
            self.volumeToProductCodes = volumeToProductCodes
            self.volumeToVolumeSize = volumeToVolumeSize
        }
    }
}

extension DrsClientTypes {

    /// Information about a server's CPU.
    public struct CPU: Swift.Sendable {
        /// The number of CPU cores.
        public var cores: Swift.Int
        /// The model name of the CPU.
        public var modelName: Swift.String?

        public init(
            cores: Swift.Int = 0,
            modelName: Swift.String? = nil
        )
        {
            self.cores = cores
            self.modelName = modelName
        }
    }
}

public struct CreateExtendedSourceServerInput: Swift.Sendable {
    /// This defines the ARN of the source server in staging Account based on which you want to create an extended source server.
    /// This member is required.
    public var sourceServerArn: Swift.String?
    /// A list of tags associated with the extended source server.
    public var tags: [Swift.String: Swift.String]?

    public init(
        sourceServerArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.sourceServerArn = sourceServerArn
        self.tags = tags
    }
}

extension CreateExtendedSourceServerInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateExtendedSourceServerInput(sourceServerArn: \(Swift.String(describing: sourceServerArn)), tags: \"CONTENT_REDACTED\")"}
}

extension DrsClientTypes {

    public enum DataReplicationErrorString: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case agentNotSeen
        case failedToAttachStagingDisks
        case failedToAuthenticateWithService
        case failedToBootReplicationServer
        case failedToConnectAgentToReplicationServer
        case failedToCreateSecurityGroup
        case failedToCreateStagingDisks
        case failedToDownloadReplicationSoftware
        case failedToLaunchReplicationServer
        case failedToPairReplicationServerWithAgent
        case failedToStartDataTransfer
        case notConverging
        case snapshotsFailure
        case unstableNetwork
        case sdkUnknown(Swift.String)

        public static var allCases: [DataReplicationErrorString] {
            return [
                .agentNotSeen,
                .failedToAttachStagingDisks,
                .failedToAuthenticateWithService,
                .failedToBootReplicationServer,
                .failedToConnectAgentToReplicationServer,
                .failedToCreateSecurityGroup,
                .failedToCreateStagingDisks,
                .failedToDownloadReplicationSoftware,
                .failedToLaunchReplicationServer,
                .failedToPairReplicationServerWithAgent,
                .failedToStartDataTransfer,
                .notConverging,
                .snapshotsFailure,
                .unstableNetwork
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .agentNotSeen: return "AGENT_NOT_SEEN"
            case .failedToAttachStagingDisks: return "FAILED_TO_ATTACH_STAGING_DISKS"
            case .failedToAuthenticateWithService: return "FAILED_TO_AUTHENTICATE_WITH_SERVICE"
            case .failedToBootReplicationServer: return "FAILED_TO_BOOT_REPLICATION_SERVER"
            case .failedToConnectAgentToReplicationServer: return "FAILED_TO_CONNECT_AGENT_TO_REPLICATION_SERVER"
            case .failedToCreateSecurityGroup: return "FAILED_TO_CREATE_SECURITY_GROUP"
            case .failedToCreateStagingDisks: return "FAILED_TO_CREATE_STAGING_DISKS"
            case .failedToDownloadReplicationSoftware: return "FAILED_TO_DOWNLOAD_REPLICATION_SOFTWARE"
            case .failedToLaunchReplicationServer: return "FAILED_TO_LAUNCH_REPLICATION_SERVER"
            case .failedToPairReplicationServerWithAgent: return "FAILED_TO_PAIR_REPLICATION_SERVER_WITH_AGENT"
            case .failedToStartDataTransfer: return "FAILED_TO_START_DATA_TRANSFER"
            case .notConverging: return "NOT_CONVERGING"
            case .snapshotsFailure: return "SNAPSHOTS_FAILURE"
            case .unstableNetwork: return "UNSTABLE_NETWORK"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// Error in data replication.
    public struct DataReplicationError: Swift.Sendable {
        /// Error in data replication.
        public var error: DrsClientTypes.DataReplicationErrorString?
        /// Error in data replication.
        public var rawError: Swift.String?

        public init(
            error: DrsClientTypes.DataReplicationErrorString? = nil,
            rawError: Swift.String? = nil
        )
        {
            self.error = error
            self.rawError = rawError
        }
    }
}

extension DrsClientTypes {

    public enum DataReplicationInitiationStepName: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case attachStagingDisks
        case authenticateWithService
        case bootReplicationServer
        case connectAgentToReplicationServer
        case createSecurityGroup
        case createStagingDisks
        case downloadReplicationSoftware
        case launchReplicationServer
        case pairReplicationServerWithAgent
        case startDataTransfer
        case wait
        case sdkUnknown(Swift.String)

        public static var allCases: [DataReplicationInitiationStepName] {
            return [
                .attachStagingDisks,
                .authenticateWithService,
                .bootReplicationServer,
                .connectAgentToReplicationServer,
                .createSecurityGroup,
                .createStagingDisks,
                .downloadReplicationSoftware,
                .launchReplicationServer,
                .pairReplicationServerWithAgent,
                .startDataTransfer,
                .wait
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .attachStagingDisks: return "ATTACH_STAGING_DISKS"
            case .authenticateWithService: return "AUTHENTICATE_WITH_SERVICE"
            case .bootReplicationServer: return "BOOT_REPLICATION_SERVER"
            case .connectAgentToReplicationServer: return "CONNECT_AGENT_TO_REPLICATION_SERVER"
            case .createSecurityGroup: return "CREATE_SECURITY_GROUP"
            case .createStagingDisks: return "CREATE_STAGING_DISKS"
            case .downloadReplicationSoftware: return "DOWNLOAD_REPLICATION_SOFTWARE"
            case .launchReplicationServer: return "LAUNCH_REPLICATION_SERVER"
            case .pairReplicationServerWithAgent: return "PAIR_REPLICATION_SERVER_WITH_AGENT"
            case .startDataTransfer: return "START_DATA_TRANSFER"
            case .wait: return "WAIT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    public enum DataReplicationInitiationStepStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case inProgress
        case notStarted
        case skipped
        case succeeded
        case sdkUnknown(Swift.String)

        public static var allCases: [DataReplicationInitiationStepStatus] {
            return [
                .failed,
                .inProgress,
                .notStarted,
                .skipped,
                .succeeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case .notStarted: return "NOT_STARTED"
            case .skipped: return "SKIPPED"
            case .succeeded: return "SUCCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// Data replication initiation step.
    public struct DataReplicationInitiationStep: Swift.Sendable {
        /// The name of the step.
        public var name: DrsClientTypes.DataReplicationInitiationStepName?
        /// The status of the step.
        public var status: DrsClientTypes.DataReplicationInitiationStepStatus?

        public init(
            name: DrsClientTypes.DataReplicationInitiationStepName? = nil,
            status: DrsClientTypes.DataReplicationInitiationStepStatus? = nil
        )
        {
            self.name = name
            self.status = status
        }
    }
}

extension DrsClientTypes {

    /// Data replication initiation.
    public struct DataReplicationInitiation: Swift.Sendable {
        /// The date and time of the next attempt to initiate data replication.
        public var nextAttemptDateTime: Swift.String?
        /// The date and time of the current attempt to initiate data replication.
        public var startDateTime: Swift.String?
        /// The steps of the current attempt to initiate data replication.
        public var steps: [DrsClientTypes.DataReplicationInitiationStep]?

        public init(
            nextAttemptDateTime: Swift.String? = nil,
            startDateTime: Swift.String? = nil,
            steps: [DrsClientTypes.DataReplicationInitiationStep]? = nil
        )
        {
            self.nextAttemptDateTime = nextAttemptDateTime
            self.startDateTime = startDateTime
            self.steps = steps
        }
    }
}

extension DrsClientTypes {

    public enum DataReplicationState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case backlog
        case continuous
        case creatingSnapshot
        case disconnected
        case initialSync
        case initiating
        case paused
        case rescan
        case stalled
        case stopped
        case sdkUnknown(Swift.String)

        public static var allCases: [DataReplicationState] {
            return [
                .backlog,
                .continuous,
                .creatingSnapshot,
                .disconnected,
                .initialSync,
                .initiating,
                .paused,
                .rescan,
                .stalled,
                .stopped
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .backlog: return "BACKLOG"
            case .continuous: return "CONTINUOUS"
            case .creatingSnapshot: return "CREATING_SNAPSHOT"
            case .disconnected: return "DISCONNECTED"
            case .initialSync: return "INITIAL_SYNC"
            case .initiating: return "INITIATING"
            case .paused: return "PAUSED"
            case .rescan: return "RESCAN"
            case .stalled: return "STALLED"
            case .stopped: return "STOPPED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    public enum VolumeStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case containsMarketplaceProductCodes
        case missingVolumeAttributes
        case missingVolumeAttributesAndPrecheckUnavailable
        case pending
        case regular
        case sdkUnknown(Swift.String)

        public static var allCases: [VolumeStatus] {
            return [
                .containsMarketplaceProductCodes,
                .missingVolumeAttributes,
                .missingVolumeAttributesAndPrecheckUnavailable,
                .pending,
                .regular
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .containsMarketplaceProductCodes: return "CONTAINS_MARKETPLACE_PRODUCT_CODES"
            case .missingVolumeAttributes: return "MISSING_VOLUME_ATTRIBUTES"
            case .missingVolumeAttributesAndPrecheckUnavailable: return "MISSING_VOLUME_ATTRIBUTES_AND_PRECHECK_UNAVAILABLE"
            case .pending: return "PENDING"
            case .regular: return "REGULAR"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// A disk that should be replicated.
    public struct DataReplicationInfoReplicatedDisk: Swift.Sendable {
        /// The size of the replication backlog in bytes.
        public var backloggedStorageBytes: Swift.Int
        /// The name of the device.
        public var deviceName: Swift.String?
        /// The amount of data replicated so far in bytes.
        public var replicatedStorageBytes: Swift.Int
        /// The amount of data to be rescanned in bytes.
        public var rescannedStorageBytes: Swift.Int
        /// The total amount of data to be replicated in bytes.
        public var totalStorageBytes: Swift.Int
        /// The status of the volume.
        public var volumeStatus: DrsClientTypes.VolumeStatus?

        public init(
            backloggedStorageBytes: Swift.Int = 0,
            deviceName: Swift.String? = nil,
            replicatedStorageBytes: Swift.Int = 0,
            rescannedStorageBytes: Swift.Int = 0,
            totalStorageBytes: Swift.Int = 0,
            volumeStatus: DrsClientTypes.VolumeStatus? = nil
        )
        {
            self.backloggedStorageBytes = backloggedStorageBytes
            self.deviceName = deviceName
            self.replicatedStorageBytes = replicatedStorageBytes
            self.rescannedStorageBytes = rescannedStorageBytes
            self.totalStorageBytes = totalStorageBytes
            self.volumeStatus = volumeStatus
        }
    }
}

extension DrsClientTypes {

    /// Information about Data Replication
    public struct DataReplicationInfo: Swift.Sendable {
        /// Error in data replication.
        public var dataReplicationError: DrsClientTypes.DataReplicationError?
        /// Information about whether the data replication has been initiated.
        public var dataReplicationInitiation: DrsClientTypes.DataReplicationInitiation?
        /// The state of the data replication.
        public var dataReplicationState: DrsClientTypes.DataReplicationState?
        /// An estimate of when the data replication will be completed.
        public var etaDateTime: Swift.String?
        /// Data replication lag duration.
        public var lagDuration: Swift.String?
        /// The disks that should be replicated.
        public var replicatedDisks: [DrsClientTypes.DataReplicationInfoReplicatedDisk]?
        /// AWS Availability zone into which data is being replicated.
        public var stagingAvailabilityZone: Swift.String?
        /// The ARN of the staging Outpost
        public var stagingOutpostArn: Swift.String?

        public init(
            dataReplicationError: DrsClientTypes.DataReplicationError? = nil,
            dataReplicationInitiation: DrsClientTypes.DataReplicationInitiation? = nil,
            dataReplicationState: DrsClientTypes.DataReplicationState? = nil,
            etaDateTime: Swift.String? = nil,
            lagDuration: Swift.String? = nil,
            replicatedDisks: [DrsClientTypes.DataReplicationInfoReplicatedDisk]? = nil,
            stagingAvailabilityZone: Swift.String? = nil,
            stagingOutpostArn: Swift.String? = nil
        )
        {
            self.dataReplicationError = dataReplicationError
            self.dataReplicationInitiation = dataReplicationInitiation
            self.dataReplicationState = dataReplicationState
            self.etaDateTime = etaDateTime
            self.lagDuration = lagDuration
            self.replicatedDisks = replicatedDisks
            self.stagingAvailabilityZone = stagingAvailabilityZone
            self.stagingOutpostArn = stagingOutpostArn
        }
    }
}

extension DrsClientTypes {

    public enum LastLaunchResult: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case notStarted
        case pending
        case succeeded
        case sdkUnknown(Swift.String)

        public static var allCases: [LastLaunchResult] {
            return [
                .failed,
                .notStarted,
                .pending,
                .succeeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .notStarted: return "NOT_STARTED"
            case .pending: return "PENDING"
            case .succeeded: return "SUCCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    public enum LastLaunchType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case drill
        case recovery
        case sdkUnknown(Swift.String)

        public static var allCases: [LastLaunchType] {
            return [
                .drill,
                .recovery
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .drill: return "DRILL"
            case .recovery: return "RECOVERY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// An object containing information regarding the initiation of the last launch of a Source Server.
    public struct LifeCycleLastLaunchInitiated: Swift.Sendable {
        /// The date and time the last Source Server launch was initiated.
        public var apiCallDateTime: Swift.String?
        /// The ID of the Job that was used to last launch the Source Server.
        public var jobID: Swift.String?
        /// The Job type that was used to last launch the Source Server.
        public var type: DrsClientTypes.LastLaunchType?

        public init(
            apiCallDateTime: Swift.String? = nil,
            jobID: Swift.String? = nil,
            type: DrsClientTypes.LastLaunchType? = nil
        )
        {
            self.apiCallDateTime = apiCallDateTime
            self.jobID = jobID
            self.type = type
        }
    }
}

extension DrsClientTypes {

    /// An object containing information regarding the last launch of a Source Server.
    public struct LifeCycleLastLaunch: Swift.Sendable {
        /// An object containing information regarding the initiation of the last launch of a Source Server.
        public var initiated: DrsClientTypes.LifeCycleLastLaunchInitiated?
        /// Status of Source Server's last launch.
        public var status: DrsClientTypes.LaunchStatus?

        public init(
            initiated: DrsClientTypes.LifeCycleLastLaunchInitiated? = nil,
            status: DrsClientTypes.LaunchStatus? = nil
        )
        {
            self.initiated = initiated
            self.status = status
        }
    }
}

extension DrsClientTypes {

    /// An object representing the Source Server Lifecycle.
    public struct LifeCycle: Swift.Sendable {
        /// The date and time of when the Source Server was added to the service.
        public var addedToServiceDateTime: Swift.String?
        /// The amount of time that the Source Server has been replicating for.
        public var elapsedReplicationDuration: Swift.String?
        /// The date and time of the first byte that was replicated from the Source Server.
        public var firstByteDateTime: Swift.String?
        /// An object containing information regarding the last launch of the Source Server.
        public var lastLaunch: DrsClientTypes.LifeCycleLastLaunch?
        /// The date and time this Source Server was last seen by the service.
        public var lastSeenByServiceDateTime: Swift.String?

        public init(
            addedToServiceDateTime: Swift.String? = nil,
            elapsedReplicationDuration: Swift.String? = nil,
            firstByteDateTime: Swift.String? = nil,
            lastLaunch: DrsClientTypes.LifeCycleLastLaunch? = nil,
            lastSeenByServiceDateTime: Swift.String? = nil
        )
        {
            self.addedToServiceDateTime = addedToServiceDateTime
            self.elapsedReplicationDuration = elapsedReplicationDuration
            self.firstByteDateTime = firstByteDateTime
            self.lastLaunch = lastLaunch
            self.lastSeenByServiceDateTime = lastSeenByServiceDateTime
        }
    }
}

extension DrsClientTypes {

    /// Replication direction designates if this is a failover replication, or a failback replication. When a DRS agent is installed on an instance, the replication direction is failover. In cases where a recovery launch was made in the recovery location and a new recovery instance was created, and then a failback replication was initiated from that recovery instance back to the origin location, then the replication direction will be failback.
    public enum ReplicationDirection: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failback
        case failover
        case sdkUnknown(Swift.String)

        public static var allCases: [ReplicationDirection] {
            return [
                .failback,
                .failover
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failback: return "FAILBACK"
            case .failover: return "FAILOVER"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// Properties of the cloud environment where this Source Server originated from.
    public struct SourceCloudProperties: Swift.Sendable {
        /// AWS Account ID for an EC2-originated Source Server.
        public var originAccountID: Swift.String?
        /// AWS Availability Zone for an EC2-originated Source Server.
        public var originAvailabilityZone: Swift.String?
        /// AWS Region for an EC2-originated Source Server.
        public var originRegion: Swift.String?
        /// The ARN of the source Outpost
        public var sourceOutpostArn: Swift.String?

        public init(
            originAccountID: Swift.String? = nil,
            originAvailabilityZone: Swift.String? = nil,
            originRegion: Swift.String? = nil,
            sourceOutpostArn: Swift.String? = nil
        )
        {
            self.originAccountID = originAccountID
            self.originAvailabilityZone = originAvailabilityZone
            self.originRegion = originRegion
            self.sourceOutpostArn = sourceOutpostArn
        }
    }
}

extension DrsClientTypes {

    /// An object representing a data storage device on a server.
    public struct Disk: Swift.Sendable {
        /// The amount of storage on the disk in bytes.
        public var bytes: Swift.Int
        /// The disk or device name.
        public var deviceName: Swift.String?

        public init(
            bytes: Swift.Int = 0,
            deviceName: Swift.String? = nil
        )
        {
            self.bytes = bytes
            self.deviceName = deviceName
        }
    }
}

extension DrsClientTypes {

    /// Hints used to uniquely identify a machine.
    public struct IdentificationHints: Swift.Sendable {
        /// AWS Instance ID identification hint.
        public var awsInstanceID: Swift.String?
        /// Fully Qualified Domain Name identification hint.
        public var fqdn: Swift.String?
        /// Hostname identification hint.
        public var hostname: Swift.String?
        /// vCenter VM path identification hint.
        public var vmWareUuid: Swift.String?

        public init(
            awsInstanceID: Swift.String? = nil,
            fqdn: Swift.String? = nil,
            hostname: Swift.String? = nil,
            vmWareUuid: Swift.String? = nil
        )
        {
            self.awsInstanceID = awsInstanceID
            self.fqdn = fqdn
            self.hostname = hostname
            self.vmWareUuid = vmWareUuid
        }
    }
}

extension DrsClientTypes {

    /// Network interface.
    public struct NetworkInterface: Swift.Sendable {
        /// Network interface IPs.
        public var ips: [Swift.String]?
        /// Whether this is the primary network interface.
        public var isPrimary: Swift.Bool?
        /// The MAC address of the network interface.
        public var macAddress: Swift.String?

        public init(
            ips: [Swift.String]? = nil,
            isPrimary: Swift.Bool? = nil,
            macAddress: Swift.String? = nil
        )
        {
            self.ips = ips
            self.isPrimary = isPrimary
            self.macAddress = macAddress
        }
    }
}

extension DrsClientTypes {

    /// Operating System.
    public struct OS: Swift.Sendable {
        /// The long name of the Operating System.
        public var fullString: Swift.String?

        public init(
            fullString: Swift.String? = nil
        )
        {
            self.fullString = fullString
        }
    }
}

extension DrsClientTypes {

    /// Properties of the Source Server machine.
    public struct SourceProperties: Swift.Sendable {
        /// An array of CPUs.
        public var cpus: [DrsClientTypes.CPU]?
        /// An array of disks.
        public var disks: [DrsClientTypes.Disk]?
        /// Hints used to uniquely identify a machine.
        public var identificationHints: DrsClientTypes.IdentificationHints?
        /// The date and time the Source Properties were last updated on.
        public var lastUpdatedDateTime: Swift.String?
        /// An array of network interfaces.
        public var networkInterfaces: [DrsClientTypes.NetworkInterface]?
        /// Operating system.
        public var os: DrsClientTypes.OS?
        /// The amount of RAM in bytes.
        public var ramBytes: Swift.Int
        /// The recommended EC2 instance type that will be used when recovering the Source Server.
        public var recommendedInstanceType: Swift.String?
        /// Are EC2 nitro instance types supported when recovering the Source Server.
        public var supportsNitroInstances: Swift.Bool?

        public init(
            cpus: [DrsClientTypes.CPU]? = nil,
            disks: [DrsClientTypes.Disk]? = nil,
            identificationHints: DrsClientTypes.IdentificationHints? = nil,
            lastUpdatedDateTime: Swift.String? = nil,
            networkInterfaces: [DrsClientTypes.NetworkInterface]? = nil,
            os: DrsClientTypes.OS? = nil,
            ramBytes: Swift.Int = 0,
            recommendedInstanceType: Swift.String? = nil,
            supportsNitroInstances: Swift.Bool? = nil
        )
        {
            self.cpus = cpus
            self.disks = disks
            self.identificationHints = identificationHints
            self.lastUpdatedDateTime = lastUpdatedDateTime
            self.networkInterfaces = networkInterfaces
            self.os = os
            self.ramBytes = ramBytes
            self.recommendedInstanceType = recommendedInstanceType
            self.supportsNitroInstances = supportsNitroInstances
        }
    }
}

extension DrsClientTypes {

    public enum ExtensionStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case extended
        case extensionError
        case notExtended
        case sdkUnknown(Swift.String)

        public static var allCases: [ExtensionStatus] {
            return [
                .extended,
                .extensionError,
                .notExtended
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .extended: return "EXTENDED"
            case .extensionError: return "EXTENSION_ERROR"
            case .notExtended: return "NOT_EXTENDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// Staging information related to source server.
    public struct StagingArea: Swift.Sendable {
        /// Shows an error message that occurred when DRS tried to access the staging source server. In this case StagingArea$status will have value EXTENSION_ERROR
        public var errorMessage: Swift.String?
        /// Account ID of the account to which source server belongs. If this source server is extended - shows Account ID of staging source server.
        public var stagingAccountID: Swift.String?
        /// Arn of the staging source server if this source server is extended
        public var stagingSourceServerArn: Swift.String?
        /// Status of Source server extension. Possible values: (a) NOT_EXTENDED - This is a source server that is replicating in the current account. (b) EXTENDED - Source server is extended from a staging source server. In this case, the value of stagingSourceServerArn is pointing to the Arn of the source server in the staging account. (c) EXTENSION_ERROR - Some issue occurred when accessing staging source server. In this case, errorMessage field will contain an error message that explains what happened.
        public var status: DrsClientTypes.ExtensionStatus?

        public init(
            errorMessage: Swift.String? = nil,
            stagingAccountID: Swift.String? = nil,
            stagingSourceServerArn: Swift.String? = nil,
            status: DrsClientTypes.ExtensionStatus? = nil
        )
        {
            self.errorMessage = errorMessage
            self.stagingAccountID = stagingAccountID
            self.stagingSourceServerArn = stagingSourceServerArn
            self.status = status
        }
    }
}

extension DrsClientTypes {

    public struct SourceServer: Swift.Sendable {
        /// The version of the DRS agent installed on the source server
        public var agentVersion: Swift.String?
        /// The ARN of the Source Server.
        public var arn: Swift.String?
        /// The Data Replication Info of the Source Server.
        public var dataReplicationInfo: DrsClientTypes.DataReplicationInfo?
        /// The status of the last recovery launch of this Source Server.
        public var lastLaunchResult: DrsClientTypes.LastLaunchResult?
        /// The lifecycle information of this Source Server.
        public var lifeCycle: DrsClientTypes.LifeCycle?
        /// The ID of the Recovery Instance associated with this Source Server.
        public var recoveryInstanceId: Swift.String?
        /// Replication direction of the Source Server.
        public var replicationDirection: DrsClientTypes.ReplicationDirection?
        /// For EC2-originated Source Servers which have been failed over and then failed back, this value will mean the ARN of the Source Server on the opposite replication direction.
        public var reversedDirectionSourceServerArn: Swift.String?
        /// Source cloud properties of the Source Server.
        public var sourceCloudProperties: DrsClientTypes.SourceCloudProperties?
        /// ID of the Source Network which is protecting this Source Server's network.
        public var sourceNetworkID: Swift.String?
        /// The source properties of the Source Server.
        public var sourceProperties: DrsClientTypes.SourceProperties?
        /// The ID of the Source Server.
        public var sourceServerID: Swift.String?
        /// The staging area of the source server.
        public var stagingArea: DrsClientTypes.StagingArea?
        /// The tags associated with the Source Server.
        public var tags: [Swift.String: Swift.String]?

        public init(
            agentVersion: Swift.String? = nil,
            arn: Swift.String? = nil,
            dataReplicationInfo: DrsClientTypes.DataReplicationInfo? = nil,
            lastLaunchResult: DrsClientTypes.LastLaunchResult? = nil,
            lifeCycle: DrsClientTypes.LifeCycle? = nil,
            recoveryInstanceId: Swift.String? = nil,
            replicationDirection: DrsClientTypes.ReplicationDirection? = nil,
            reversedDirectionSourceServerArn: Swift.String? = nil,
            sourceCloudProperties: DrsClientTypes.SourceCloudProperties? = nil,
            sourceNetworkID: Swift.String? = nil,
            sourceProperties: DrsClientTypes.SourceProperties? = nil,
            sourceServerID: Swift.String? = nil,
            stagingArea: DrsClientTypes.StagingArea? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.agentVersion = agentVersion
            self.arn = arn
            self.dataReplicationInfo = dataReplicationInfo
            self.lastLaunchResult = lastLaunchResult
            self.lifeCycle = lifeCycle
            self.recoveryInstanceId = recoveryInstanceId
            self.replicationDirection = replicationDirection
            self.reversedDirectionSourceServerArn = reversedDirectionSourceServerArn
            self.sourceCloudProperties = sourceCloudProperties
            self.sourceNetworkID = sourceNetworkID
            self.sourceProperties = sourceProperties
            self.sourceServerID = sourceServerID
            self.stagingArea = stagingArea
            self.tags = tags
        }
    }
}

extension DrsClientTypes.SourceServer: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "SourceServer(agentVersion: \(Swift.String(describing: agentVersion)), arn: \(Swift.String(describing: arn)), dataReplicationInfo: \(Swift.String(describing: dataReplicationInfo)), lastLaunchResult: \(Swift.String(describing: lastLaunchResult)), lifeCycle: \(Swift.String(describing: lifeCycle)), recoveryInstanceId: \(Swift.String(describing: recoveryInstanceId)), replicationDirection: \(Swift.String(describing: replicationDirection)), reversedDirectionSourceServerArn: \(Swift.String(describing: reversedDirectionSourceServerArn)), sourceCloudProperties: \(Swift.String(describing: sourceCloudProperties)), sourceNetworkID: \(Swift.String(describing: sourceNetworkID)), sourceProperties: \(Swift.String(describing: sourceProperties)), sourceServerID: \(Swift.String(describing: sourceServerID)), stagingArea: \(Swift.String(describing: stagingArea)), tags: \"CONTENT_REDACTED\")"}
}

public struct CreateExtendedSourceServerOutput: Swift.Sendable {
    /// Created extended source server.
    public var sourceServer: DrsClientTypes.SourceServer?

    public init(
        sourceServer: DrsClientTypes.SourceServer? = nil
    )
    {
        self.sourceServer = sourceServer
    }
}

extension DrsClientTypes {

    public enum LaunchDisposition: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case started
        case stopped
        case sdkUnknown(Swift.String)

        public static var allCases: [LaunchDisposition] {
            return [
                .started,
                .stopped
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .started: return "STARTED"
            case .stopped: return "STOPPED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// Configuration of a machine's license.
    public struct Licensing: Swift.Sendable {
        /// Whether to enable "Bring your own license" or not.
        public var osByol: Swift.Bool?

        public init(
            osByol: Swift.Bool? = nil
        )
        {
            self.osByol = osByol
        }
    }
}

extension DrsClientTypes {

    public enum TargetInstanceTypeRightSizingMethod: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case basic
        case inAws
        case `none`
        case sdkUnknown(Swift.String)

        public static var allCases: [TargetInstanceTypeRightSizingMethod] {
            return [
                .basic,
                .inAws,
                .none
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .basic: return "BASIC"
            case .inAws: return "IN_AWS"
            case .none: return "NONE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct CreateLaunchConfigurationTemplateInput: Swift.Sendable {
    /// Copy private IP.
    public var copyPrivateIp: Swift.Bool?
    /// Copy tags.
    public var copyTags: Swift.Bool?
    /// S3 bucket ARN to export Source Network templates.
    public var exportBucketArn: Swift.String?
    /// Launch disposition.
    public var launchDisposition: DrsClientTypes.LaunchDisposition?
    /// DRS will set the 'launch into instance ID' of any source server when performing a drill, recovery or failback to the previous region or availability zone, using the instance ID of the source instance.
    public var launchIntoSourceInstance: Swift.Bool?
    /// Licensing.
    public var licensing: DrsClientTypes.Licensing?
    /// Whether we want to activate post-launch actions.
    public var postLaunchEnabled: Swift.Bool?
    /// Request to associate tags during creation of a Launch Configuration Template.
    public var tags: [Swift.String: Swift.String]?
    /// Target instance type right-sizing method.
    public var targetInstanceTypeRightSizingMethod: DrsClientTypes.TargetInstanceTypeRightSizingMethod?

    public init(
        copyPrivateIp: Swift.Bool? = nil,
        copyTags: Swift.Bool? = nil,
        exportBucketArn: Swift.String? = nil,
        launchDisposition: DrsClientTypes.LaunchDisposition? = nil,
        launchIntoSourceInstance: Swift.Bool? = nil,
        licensing: DrsClientTypes.Licensing? = nil,
        postLaunchEnabled: Swift.Bool? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        targetInstanceTypeRightSizingMethod: DrsClientTypes.TargetInstanceTypeRightSizingMethod? = nil
    )
    {
        self.copyPrivateIp = copyPrivateIp
        self.copyTags = copyTags
        self.exportBucketArn = exportBucketArn
        self.launchDisposition = launchDisposition
        self.launchIntoSourceInstance = launchIntoSourceInstance
        self.licensing = licensing
        self.postLaunchEnabled = postLaunchEnabled
        self.tags = tags
        self.targetInstanceTypeRightSizingMethod = targetInstanceTypeRightSizingMethod
    }
}

extension CreateLaunchConfigurationTemplateInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateLaunchConfigurationTemplateInput(copyPrivateIp: \(Swift.String(describing: copyPrivateIp)), copyTags: \(Swift.String(describing: copyTags)), exportBucketArn: \(Swift.String(describing: exportBucketArn)), launchDisposition: \(Swift.String(describing: launchDisposition)), launchIntoSourceInstance: \(Swift.String(describing: launchIntoSourceInstance)), licensing: \(Swift.String(describing: licensing)), postLaunchEnabled: \(Swift.String(describing: postLaunchEnabled)), targetInstanceTypeRightSizingMethod: \(Swift.String(describing: targetInstanceTypeRightSizingMethod)), tags: \"CONTENT_REDACTED\")"}
}

extension DrsClientTypes {

    /// Account level Launch Configuration Template.
    public struct LaunchConfigurationTemplate: Swift.Sendable {
        /// ARN of the Launch Configuration Template.
        public var arn: Swift.String?
        /// Copy private IP.
        public var copyPrivateIp: Swift.Bool?
        /// Copy tags.
        public var copyTags: Swift.Bool?
        /// S3 bucket ARN to export Source Network templates.
        public var exportBucketArn: Swift.String?
        /// ID of the Launch Configuration Template.
        public var launchConfigurationTemplateID: Swift.String?
        /// Launch disposition.
        public var launchDisposition: DrsClientTypes.LaunchDisposition?
        /// DRS will set the 'launch into instance ID' of any source server when performing a drill, recovery or failback to the previous region or availability zone, using the instance ID of the source instance.
        public var launchIntoSourceInstance: Swift.Bool?
        /// Licensing.
        public var licensing: DrsClientTypes.Licensing?
        /// Post-launch actions activated.
        public var postLaunchEnabled: Swift.Bool?
        /// Tags of the Launch Configuration Template.
        public var tags: [Swift.String: Swift.String]?
        /// Target instance type right-sizing method.
        public var targetInstanceTypeRightSizingMethod: DrsClientTypes.TargetInstanceTypeRightSizingMethod?

        public init(
            arn: Swift.String? = nil,
            copyPrivateIp: Swift.Bool? = nil,
            copyTags: Swift.Bool? = nil,
            exportBucketArn: Swift.String? = nil,
            launchConfigurationTemplateID: Swift.String? = nil,
            launchDisposition: DrsClientTypes.LaunchDisposition? = nil,
            launchIntoSourceInstance: Swift.Bool? = nil,
            licensing: DrsClientTypes.Licensing? = nil,
            postLaunchEnabled: Swift.Bool? = nil,
            tags: [Swift.String: Swift.String]? = nil,
            targetInstanceTypeRightSizingMethod: DrsClientTypes.TargetInstanceTypeRightSizingMethod? = nil
        )
        {
            self.arn = arn
            self.copyPrivateIp = copyPrivateIp
            self.copyTags = copyTags
            self.exportBucketArn = exportBucketArn
            self.launchConfigurationTemplateID = launchConfigurationTemplateID
            self.launchDisposition = launchDisposition
            self.launchIntoSourceInstance = launchIntoSourceInstance
            self.licensing = licensing
            self.postLaunchEnabled = postLaunchEnabled
            self.tags = tags
            self.targetInstanceTypeRightSizingMethod = targetInstanceTypeRightSizingMethod
        }
    }
}

extension DrsClientTypes.LaunchConfigurationTemplate: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "LaunchConfigurationTemplate(arn: \(Swift.String(describing: arn)), copyPrivateIp: \(Swift.String(describing: copyPrivateIp)), copyTags: \(Swift.String(describing: copyTags)), exportBucketArn: \(Swift.String(describing: exportBucketArn)), launchConfigurationTemplateID: \(Swift.String(describing: launchConfigurationTemplateID)), launchDisposition: \(Swift.String(describing: launchDisposition)), launchIntoSourceInstance: \(Swift.String(describing: launchIntoSourceInstance)), licensing: \(Swift.String(describing: licensing)), postLaunchEnabled: \(Swift.String(describing: postLaunchEnabled)), targetInstanceTypeRightSizingMethod: \(Swift.String(describing: targetInstanceTypeRightSizingMethod)), tags: \"CONTENT_REDACTED\")"}
}

public struct CreateLaunchConfigurationTemplateOutput: Swift.Sendable {
    /// Created Launch Configuration Template.
    public var launchConfigurationTemplate: DrsClientTypes.LaunchConfigurationTemplate?

    public init(
        launchConfigurationTemplate: DrsClientTypes.LaunchConfigurationTemplate? = nil
    )
    {
        self.launchConfigurationTemplate = launchConfigurationTemplate
    }
}

extension DrsClientTypes {

    public enum ReplicationConfigurationDataPlaneRouting: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case privateIp
        case publicIp
        case sdkUnknown(Swift.String)

        public static var allCases: [ReplicationConfigurationDataPlaneRouting] {
            return [
                .privateIp,
                .publicIp
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .privateIp: return "PRIVATE_IP"
            case .publicIp: return "PUBLIC_IP"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    public enum ReplicationConfigurationDefaultLargeStagingDiskType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case auto
        case gp2
        case gp3
        case st1
        case sdkUnknown(Swift.String)

        public static var allCases: [ReplicationConfigurationDefaultLargeStagingDiskType] {
            return [
                .auto,
                .gp2,
                .gp3,
                .st1
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .auto: return "AUTO"
            case .gp2: return "GP2"
            case .gp3: return "GP3"
            case .st1: return "ST1"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    public enum ReplicationConfigurationEbsEncryption: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case custom
        case `default`
        case `none`
        case sdkUnknown(Swift.String)

        public static var allCases: [ReplicationConfigurationEbsEncryption] {
            return [
                .custom,
                .default,
                .none
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .custom: return "CUSTOM"
            case .default: return "DEFAULT"
            case .none: return "NONE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    public enum PITPolicyRuleUnits: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case day
        case hour
        case minute
        case sdkUnknown(Swift.String)

        public static var allCases: [PITPolicyRuleUnits] {
            return [
                .day,
                .hour,
                .minute
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .day: return "DAY"
            case .hour: return "HOUR"
            case .minute: return "MINUTE"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// A rule in the Point in Time (PIT) policy representing when to take snapshots and how long to retain them for.
    public struct PITPolicyRule: Swift.Sendable {
        /// Whether this rule is enabled or not.
        public var enabled: Swift.Bool?
        /// How often, in the chosen units, a snapshot should be taken.
        /// This member is required.
        public var interval: Swift.Int?
        /// The duration to retain a snapshot for, in the chosen units.
        /// This member is required.
        public var retentionDuration: Swift.Int?
        /// The ID of the rule.
        public var ruleID: Swift.Int
        /// The units used to measure the interval and retentionDuration.
        /// This member is required.
        public var units: DrsClientTypes.PITPolicyRuleUnits?

        public init(
            enabled: Swift.Bool? = nil,
            interval: Swift.Int? = nil,
            retentionDuration: Swift.Int? = nil,
            ruleID: Swift.Int = 0,
            units: DrsClientTypes.PITPolicyRuleUnits? = nil
        )
        {
            self.enabled = enabled
            self.interval = interval
            self.retentionDuration = retentionDuration
            self.ruleID = ruleID
            self.units = units
        }
    }
}

public struct CreateReplicationConfigurationTemplateInput: Swift.Sendable {
    /// Whether to associate the default Elastic Disaster Recovery Security group with the Replication Configuration Template.
    /// This member is required.
    public var associateDefaultSecurityGroup: Swift.Bool?
    /// Whether to allow the AWS replication agent to automatically replicate newly added disks.
    public var autoReplicateNewDisks: Swift.Bool?
    /// Configure bandwidth throttling for the outbound data transfer rate of the Source Server in Mbps.
    /// This member is required.
    public var bandwidthThrottling: Swift.Int
    /// Whether to create a Public IP for the Recovery Instance by default.
    /// This member is required.
    public var createPublicIP: Swift.Bool?
    /// The data plane routing mechanism that will be used for replication.
    /// This member is required.
    public var dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting?
    /// The Staging Disk EBS volume type to be used during replication.
    /// This member is required.
    public var defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType?
    /// The type of EBS encryption to be used during replication.
    /// This member is required.
    public var ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption?
    /// The ARN of the EBS encryption key to be used during replication.
    public var ebsEncryptionKeyArn: Swift.String?
    /// The Point in time (PIT) policy to manage snapshots taken during replication.
    /// This member is required.
    public var pitPolicy: [DrsClientTypes.PITPolicyRule]?
    /// The instance type to be used for the replication server.
    /// This member is required.
    public var replicationServerInstanceType: Swift.String?
    /// The security group IDs that will be used by the replication server.
    /// This member is required.
    public var replicationServersSecurityGroupsIDs: [Swift.String]?
    /// The subnet to be used by the replication staging area.
    /// This member is required.
    public var stagingAreaSubnetId: Swift.String?
    /// A set of tags to be associated with all resources created in the replication staging area: EC2 replication server, EBS volumes, EBS snapshots, etc.
    /// This member is required.
    public var stagingAreaTags: [Swift.String: Swift.String]?
    /// A set of tags to be associated with the Replication Configuration Template resource.
    public var tags: [Swift.String: Swift.String]?
    /// Whether to use a dedicated Replication Server in the replication staging area.
    /// This member is required.
    public var useDedicatedReplicationServer: Swift.Bool?

    public init(
        associateDefaultSecurityGroup: Swift.Bool? = nil,
        autoReplicateNewDisks: Swift.Bool? = nil,
        bandwidthThrottling: Swift.Int = 0,
        createPublicIP: Swift.Bool? = nil,
        dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting? = nil,
        defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType? = nil,
        ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption? = nil,
        ebsEncryptionKeyArn: Swift.String? = nil,
        pitPolicy: [DrsClientTypes.PITPolicyRule]? = nil,
        replicationServerInstanceType: Swift.String? = nil,
        replicationServersSecurityGroupsIDs: [Swift.String]? = nil,
        stagingAreaSubnetId: Swift.String? = nil,
        stagingAreaTags: [Swift.String: Swift.String]? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        useDedicatedReplicationServer: Swift.Bool? = nil
    )
    {
        self.associateDefaultSecurityGroup = associateDefaultSecurityGroup
        self.autoReplicateNewDisks = autoReplicateNewDisks
        self.bandwidthThrottling = bandwidthThrottling
        self.createPublicIP = createPublicIP
        self.dataPlaneRouting = dataPlaneRouting
        self.defaultLargeStagingDiskType = defaultLargeStagingDiskType
        self.ebsEncryption = ebsEncryption
        self.ebsEncryptionKeyArn = ebsEncryptionKeyArn
        self.pitPolicy = pitPolicy
        self.replicationServerInstanceType = replicationServerInstanceType
        self.replicationServersSecurityGroupsIDs = replicationServersSecurityGroupsIDs
        self.stagingAreaSubnetId = stagingAreaSubnetId
        self.stagingAreaTags = stagingAreaTags
        self.tags = tags
        self.useDedicatedReplicationServer = useDedicatedReplicationServer
    }
}

extension CreateReplicationConfigurationTemplateInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateReplicationConfigurationTemplateInput(associateDefaultSecurityGroup: \(Swift.String(describing: associateDefaultSecurityGroup)), autoReplicateNewDisks: \(Swift.String(describing: autoReplicateNewDisks)), bandwidthThrottling: \(Swift.String(describing: bandwidthThrottling)), createPublicIP: \(Swift.String(describing: createPublicIP)), dataPlaneRouting: \(Swift.String(describing: dataPlaneRouting)), defaultLargeStagingDiskType: \(Swift.String(describing: defaultLargeStagingDiskType)), ebsEncryption: \(Swift.String(describing: ebsEncryption)), ebsEncryptionKeyArn: \(Swift.String(describing: ebsEncryptionKeyArn)), pitPolicy: \(Swift.String(describing: pitPolicy)), replicationServerInstanceType: \(Swift.String(describing: replicationServerInstanceType)), replicationServersSecurityGroupsIDs: \(Swift.String(describing: replicationServersSecurityGroupsIDs)), stagingAreaSubnetId: \(Swift.String(describing: stagingAreaSubnetId)), useDedicatedReplicationServer: \(Swift.String(describing: useDedicatedReplicationServer)), stagingAreaTags: \"CONTENT_REDACTED\", tags: \"CONTENT_REDACTED\")"}
}

public struct CreateReplicationConfigurationTemplateOutput: Swift.Sendable {
    /// The Replication Configuration Template ARN.
    public var arn: Swift.String?
    /// Whether to associate the default Elastic Disaster Recovery Security group with the Replication Configuration Template.
    public var associateDefaultSecurityGroup: Swift.Bool?
    /// Whether to allow the AWS replication agent to automatically replicate newly added disks.
    public var autoReplicateNewDisks: Swift.Bool?
    /// Configure bandwidth throttling for the outbound data transfer rate of the Source Server in Mbps.
    public var bandwidthThrottling: Swift.Int
    /// Whether to create a Public IP for the Recovery Instance by default.
    public var createPublicIP: Swift.Bool?
    /// The data plane routing mechanism that will be used for replication.
    public var dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting?
    /// The Staging Disk EBS volume type to be used during replication.
    public var defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType?
    /// The type of EBS encryption to be used during replication.
    public var ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption?
    /// The ARN of the EBS encryption key to be used during replication.
    public var ebsEncryptionKeyArn: Swift.String?
    /// The Point in time (PIT) policy to manage snapshots taken during replication.
    public var pitPolicy: [DrsClientTypes.PITPolicyRule]?
    /// The Replication Configuration Template ID.
    /// This member is required.
    public var replicationConfigurationTemplateID: Swift.String?
    /// The instance type to be used for the replication server.
    public var replicationServerInstanceType: Swift.String?
    /// The security group IDs that will be used by the replication server.
    public var replicationServersSecurityGroupsIDs: [Swift.String]?
    /// The subnet to be used by the replication staging area.
    public var stagingAreaSubnetId: Swift.String?
    /// A set of tags to be associated with all resources created in the replication staging area: EC2 replication server, EBS volumes, EBS snapshots, etc.
    public var stagingAreaTags: [Swift.String: Swift.String]?
    /// A set of tags to be associated with the Replication Configuration Template resource.
    public var tags: [Swift.String: Swift.String]?
    /// Whether to use a dedicated Replication Server in the replication staging area.
    public var useDedicatedReplicationServer: Swift.Bool?

    public init(
        arn: Swift.String? = nil,
        associateDefaultSecurityGroup: Swift.Bool? = nil,
        autoReplicateNewDisks: Swift.Bool? = nil,
        bandwidthThrottling: Swift.Int = 0,
        createPublicIP: Swift.Bool? = nil,
        dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting? = nil,
        defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType? = nil,
        ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption? = nil,
        ebsEncryptionKeyArn: Swift.String? = nil,
        pitPolicy: [DrsClientTypes.PITPolicyRule]? = nil,
        replicationConfigurationTemplateID: Swift.String? = nil,
        replicationServerInstanceType: Swift.String? = nil,
        replicationServersSecurityGroupsIDs: [Swift.String]? = nil,
        stagingAreaSubnetId: Swift.String? = nil,
        stagingAreaTags: [Swift.String: Swift.String]? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        useDedicatedReplicationServer: Swift.Bool? = nil
    )
    {
        self.arn = arn
        self.associateDefaultSecurityGroup = associateDefaultSecurityGroup
        self.autoReplicateNewDisks = autoReplicateNewDisks
        self.bandwidthThrottling = bandwidthThrottling
        self.createPublicIP = createPublicIP
        self.dataPlaneRouting = dataPlaneRouting
        self.defaultLargeStagingDiskType = defaultLargeStagingDiskType
        self.ebsEncryption = ebsEncryption
        self.ebsEncryptionKeyArn = ebsEncryptionKeyArn
        self.pitPolicy = pitPolicy
        self.replicationConfigurationTemplateID = replicationConfigurationTemplateID
        self.replicationServerInstanceType = replicationServerInstanceType
        self.replicationServersSecurityGroupsIDs = replicationServersSecurityGroupsIDs
        self.stagingAreaSubnetId = stagingAreaSubnetId
        self.stagingAreaTags = stagingAreaTags
        self.tags = tags
        self.useDedicatedReplicationServer = useDedicatedReplicationServer
    }
}

extension CreateReplicationConfigurationTemplateOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateReplicationConfigurationTemplateOutput(arn: \(Swift.String(describing: arn)), associateDefaultSecurityGroup: \(Swift.String(describing: associateDefaultSecurityGroup)), autoReplicateNewDisks: \(Swift.String(describing: autoReplicateNewDisks)), bandwidthThrottling: \(Swift.String(describing: bandwidthThrottling)), createPublicIP: \(Swift.String(describing: createPublicIP)), dataPlaneRouting: \(Swift.String(describing: dataPlaneRouting)), defaultLargeStagingDiskType: \(Swift.String(describing: defaultLargeStagingDiskType)), ebsEncryption: \(Swift.String(describing: ebsEncryption)), ebsEncryptionKeyArn: \(Swift.String(describing: ebsEncryptionKeyArn)), pitPolicy: \(Swift.String(describing: pitPolicy)), replicationConfigurationTemplateID: \(Swift.String(describing: replicationConfigurationTemplateID)), replicationServerInstanceType: \(Swift.String(describing: replicationServerInstanceType)), replicationServersSecurityGroupsIDs: \(Swift.String(describing: replicationServersSecurityGroupsIDs)), stagingAreaSubnetId: \(Swift.String(describing: stagingAreaSubnetId)), useDedicatedReplicationServer: \(Swift.String(describing: useDedicatedReplicationServer)), stagingAreaTags: \"CONTENT_REDACTED\", tags: \"CONTENT_REDACTED\")"}
}

public struct CreateSourceNetworkInput: Swift.Sendable {
    /// Account containing the VPC to protect.
    /// This member is required.
    public var originAccountID: Swift.String?
    /// Region containing the VPC to protect.
    /// This member is required.
    public var originRegion: Swift.String?
    /// A set of tags to be associated with the Source Network resource.
    public var tags: [Swift.String: Swift.String]?
    /// Which VPC ID to protect.
    /// This member is required.
    public var vpcID: Swift.String?

    public init(
        originAccountID: Swift.String? = nil,
        originRegion: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        vpcID: Swift.String? = nil
    )
    {
        self.originAccountID = originAccountID
        self.originRegion = originRegion
        self.tags = tags
        self.vpcID = vpcID
    }
}

extension CreateSourceNetworkInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "CreateSourceNetworkInput(originAccountID: \(Swift.String(describing: originAccountID)), originRegion: \(Swift.String(describing: originRegion)), vpcID: \(Swift.String(describing: vpcID)), tags: \"CONTENT_REDACTED\")"}
}

public struct CreateSourceNetworkOutput: Swift.Sendable {
    /// ID of the created Source Network.
    public var sourceNetworkID: Swift.String?

    public init(
        sourceNetworkID: Swift.String? = nil
    )
    {
        self.sourceNetworkID = sourceNetworkID
    }
}

public struct DeleteJobInput: Swift.Sendable {
    /// The ID of the Job to be deleted.
    /// This member is required.
    public var jobID: Swift.String?

    public init(
        jobID: Swift.String? = nil
    )
    {
        self.jobID = jobID
    }
}

public struct DeleteJobOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteLaunchActionInput: Swift.Sendable {
    /// Launch action Id.
    /// This member is required.
    public var actionId: Swift.String?
    /// Launch configuration template Id or Source Server Id
    /// This member is required.
    public var resourceId: Swift.String?

    public init(
        actionId: Swift.String? = nil,
        resourceId: Swift.String? = nil
    )
    {
        self.actionId = actionId
        self.resourceId = resourceId
    }
}

public struct DeleteLaunchActionOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteLaunchConfigurationTemplateInput: Swift.Sendable {
    /// The ID of the Launch Configuration Template to be deleted.
    /// This member is required.
    public var launchConfigurationTemplateID: Swift.String?

    public init(
        launchConfigurationTemplateID: Swift.String? = nil
    )
    {
        self.launchConfigurationTemplateID = launchConfigurationTemplateID
    }
}

public struct DeleteLaunchConfigurationTemplateOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteRecoveryInstanceInput: Swift.Sendable {
    /// The ID of the Recovery Instance to be deleted.
    /// This member is required.
    public var recoveryInstanceID: Swift.String?

    public init(
        recoveryInstanceID: Swift.String? = nil
    )
    {
        self.recoveryInstanceID = recoveryInstanceID
    }
}

public struct DeleteReplicationConfigurationTemplateInput: Swift.Sendable {
    /// The ID of the Replication Configuration Template to be deleted.
    /// This member is required.
    public var replicationConfigurationTemplateID: Swift.String?

    public init(
        replicationConfigurationTemplateID: Swift.String? = nil
    )
    {
        self.replicationConfigurationTemplateID = replicationConfigurationTemplateID
    }
}

public struct DeleteReplicationConfigurationTemplateOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteSourceNetworkInput: Swift.Sendable {
    /// ID of the Source Network to delete.
    /// This member is required.
    public var sourceNetworkID: Swift.String?

    public init(
        sourceNetworkID: Swift.String? = nil
    )
    {
        self.sourceNetworkID = sourceNetworkID
    }
}

public struct DeleteSourceNetworkOutput: Swift.Sendable {

    public init() { }
}

public struct DeleteSourceServerInput: Swift.Sendable {
    /// The ID of the Source Server to be deleted.
    /// This member is required.
    public var sourceServerID: Swift.String?

    public init(
        sourceServerID: Swift.String? = nil
    )
    {
        self.sourceServerID = sourceServerID
    }
}

public struct DeleteSourceServerOutput: Swift.Sendable {

    public init() { }
}

public struct DescribeJobLogItemsInput: Swift.Sendable {
    /// The ID of the Job for which Job log items will be retrieved.
    /// This member is required.
    public var jobID: Swift.String?
    /// Maximum number of Job log items to retrieve.
    public var maxResults: Swift.Int?
    /// The token of the next Job log items to retrieve.
    public var nextToken: Swift.String?

    public init(
        jobID: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.jobID = jobID
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DrsClientTypes {

    public enum JobLogEvent: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case cleanupEnd
        case cleanupFail
        case cleanupStart
        case conversionEnd
        case conversionFail
        case conversionStart
        case deployNetworkConfigurationEnd
        case deployNetworkConfigurationFailed
        case deployNetworkConfigurationStart
        case jobCancel
        case jobEnd
        case jobStart
        case launchFailed
        case launchStart
        case networkRecoveryFail
        case serverSkipped
        case snapshotEnd
        case snapshotFail
        case snapshotStart
        case updateLaunchTemplateEnd
        case updateLaunchTemplateFailed
        case updateLaunchTemplateStart
        case updateNetworkConfigurationEnd
        case updateNetworkConfigurationFailed
        case updateNetworkConfigurationStart
        case usingPreviousSnapshot
        case usingPreviousSnapshotFailed
        case sdkUnknown(Swift.String)

        public static var allCases: [JobLogEvent] {
            return [
                .cleanupEnd,
                .cleanupFail,
                .cleanupStart,
                .conversionEnd,
                .conversionFail,
                .conversionStart,
                .deployNetworkConfigurationEnd,
                .deployNetworkConfigurationFailed,
                .deployNetworkConfigurationStart,
                .jobCancel,
                .jobEnd,
                .jobStart,
                .launchFailed,
                .launchStart,
                .networkRecoveryFail,
                .serverSkipped,
                .snapshotEnd,
                .snapshotFail,
                .snapshotStart,
                .updateLaunchTemplateEnd,
                .updateLaunchTemplateFailed,
                .updateLaunchTemplateStart,
                .updateNetworkConfigurationEnd,
                .updateNetworkConfigurationFailed,
                .updateNetworkConfigurationStart,
                .usingPreviousSnapshot,
                .usingPreviousSnapshotFailed
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .cleanupEnd: return "CLEANUP_END"
            case .cleanupFail: return "CLEANUP_FAIL"
            case .cleanupStart: return "CLEANUP_START"
            case .conversionEnd: return "CONVERSION_END"
            case .conversionFail: return "CONVERSION_FAIL"
            case .conversionStart: return "CONVERSION_START"
            case .deployNetworkConfigurationEnd: return "DEPLOY_NETWORK_CONFIGURATION_END"
            case .deployNetworkConfigurationFailed: return "DEPLOY_NETWORK_CONFIGURATION_FAILED"
            case .deployNetworkConfigurationStart: return "DEPLOY_NETWORK_CONFIGURATION_START"
            case .jobCancel: return "JOB_CANCEL"
            case .jobEnd: return "JOB_END"
            case .jobStart: return "JOB_START"
            case .launchFailed: return "LAUNCH_FAILED"
            case .launchStart: return "LAUNCH_START"
            case .networkRecoveryFail: return "NETWORK_RECOVERY_FAIL"
            case .serverSkipped: return "SERVER_SKIPPED"
            case .snapshotEnd: return "SNAPSHOT_END"
            case .snapshotFail: return "SNAPSHOT_FAIL"
            case .snapshotStart: return "SNAPSHOT_START"
            case .updateLaunchTemplateEnd: return "UPDATE_LAUNCH_TEMPLATE_END"
            case .updateLaunchTemplateFailed: return "UPDATE_LAUNCH_TEMPLATE_FAILED"
            case .updateLaunchTemplateStart: return "UPDATE_LAUNCH_TEMPLATE_START"
            case .updateNetworkConfigurationEnd: return "UPDATE_NETWORK_CONFIGURATION_END"
            case .updateNetworkConfigurationFailed: return "UPDATE_NETWORK_CONFIGURATION_FAILED"
            case .updateNetworkConfigurationStart: return "UPDATE_NETWORK_CONFIGURATION_START"
            case .usingPreviousSnapshot: return "USING_PREVIOUS_SNAPSHOT"
            case .usingPreviousSnapshotFailed: return "USING_PREVIOUS_SNAPSHOT_FAILED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// Properties of Source Network related to a job event.
    public struct SourceNetworkData: Swift.Sendable {
        /// Source Network ID.
        public var sourceNetworkID: Swift.String?
        /// VPC ID protected by the Source Network.
        public var sourceVpc: Swift.String?
        /// CloudFormation stack name that was deployed for recovering the Source Network.
        public var stackName: Swift.String?
        /// ID of the recovered VPC following Source Network recovery.
        public var targetVpc: Swift.String?

        public init(
            sourceNetworkID: Swift.String? = nil,
            sourceVpc: Swift.String? = nil,
            stackName: Swift.String? = nil,
            targetVpc: Swift.String? = nil
        )
        {
            self.sourceNetworkID = sourceNetworkID
            self.sourceVpc = sourceVpc
            self.stackName = stackName
            self.targetVpc = targetVpc
        }
    }
}

extension DrsClientTypes {

    /// Properties of resource related to a job event.
    public enum EventResourceData: Swift.Sendable {
        /// Source Network properties.
        case sourcenetworkdata(DrsClientTypes.SourceNetworkData)
        case sdkUnknown(Swift.String)
    }
}

extension DrsClientTypes {

    /// Metadata associated with a Job log.
    public struct JobLogEventData: Swift.Sendable {
        /// Properties of a conversion job
        public var conversionProperties: DrsClientTypes.ConversionProperties?
        /// The ID of a conversion server.
        public var conversionServerID: Swift.String?
        /// Properties of resource related to a job event.
        public var eventResourceData: DrsClientTypes.EventResourceData?
        /// A string representing a job error.
        public var rawError: Swift.String?
        /// The ID of a Source Server.
        public var sourceServerID: Swift.String?
        /// The ID of a Recovery Instance.
        public var targetInstanceID: Swift.String?

        public init(
            conversionProperties: DrsClientTypes.ConversionProperties? = nil,
            conversionServerID: Swift.String? = nil,
            eventResourceData: DrsClientTypes.EventResourceData? = nil,
            rawError: Swift.String? = nil,
            sourceServerID: Swift.String? = nil,
            targetInstanceID: Swift.String? = nil
        )
        {
            self.conversionProperties = conversionProperties
            self.conversionServerID = conversionServerID
            self.eventResourceData = eventResourceData
            self.rawError = rawError
            self.sourceServerID = sourceServerID
            self.targetInstanceID = targetInstanceID
        }
    }
}

extension DrsClientTypes {

    /// A log outputted by a Job.
    public struct JobLog: Swift.Sendable {
        /// The event represents the type of a log.
        public var event: DrsClientTypes.JobLogEvent?
        /// Metadata associated with a Job log.
        public var eventData: DrsClientTypes.JobLogEventData?
        /// The date and time the log was taken.
        public var logDateTime: Swift.String?

        public init(
            event: DrsClientTypes.JobLogEvent? = nil,
            eventData: DrsClientTypes.JobLogEventData? = nil,
            logDateTime: Swift.String? = nil
        )
        {
            self.event = event
            self.eventData = eventData
            self.logDateTime = logDateTime
        }
    }
}

public struct DescribeJobLogItemsOutput: Swift.Sendable {
    /// An array of Job log items.
    public var items: [DrsClientTypes.JobLog]?
    /// The token of the next Job log items to retrieve.
    public var nextToken: Swift.String?

    public init(
        items: [DrsClientTypes.JobLog]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

extension DrsClientTypes {

    /// A set of filters by which to return Jobs.
    public struct DescribeJobsRequestFilters: Swift.Sendable {
        /// The start date in a date range query.
        public var fromDate: Swift.String?
        /// An array of Job IDs that should be returned. An empty array means all jobs.
        public var jobIDs: [Swift.String]?
        /// The end date in a date range query.
        public var toDate: Swift.String?

        public init(
            fromDate: Swift.String? = nil,
            jobIDs: [Swift.String]? = nil,
            toDate: Swift.String? = nil
        )
        {
            self.fromDate = fromDate
            self.jobIDs = jobIDs
            self.toDate = toDate
        }
    }
}

public struct DescribeJobsInput: Swift.Sendable {
    /// A set of filters by which to return Jobs.
    public var filters: DrsClientTypes.DescribeJobsRequestFilters?
    /// Maximum number of Jobs to retrieve.
    public var maxResults: Swift.Int?
    /// The token of the next Job to retrieve.
    public var nextToken: Swift.String?

    public init(
        filters: DrsClientTypes.DescribeJobsRequestFilters? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct DescribeJobsOutput: Swift.Sendable {
    /// An array of Jobs.
    public var items: [DrsClientTypes.Job]?
    /// The token of the next Job to retrieve.
    public var nextToken: Swift.String?

    public init(
        items: [DrsClientTypes.Job]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

public struct DescribeLaunchConfigurationTemplatesInput: Swift.Sendable {
    /// Request to filter Launch Configuration Templates list by Launch Configuration Template ID.
    public var launchConfigurationTemplateIDs: [Swift.String]?
    /// Maximum results to be returned in DescribeLaunchConfigurationTemplates.
    public var maxResults: Swift.Int?
    /// The token of the next Launch Configuration Template to retrieve.
    public var nextToken: Swift.String?

    public init(
        launchConfigurationTemplateIDs: [Swift.String]? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.launchConfigurationTemplateIDs = launchConfigurationTemplateIDs
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct DescribeLaunchConfigurationTemplatesOutput: Swift.Sendable {
    /// List of items returned by DescribeLaunchConfigurationTemplates.
    public var items: [DrsClientTypes.LaunchConfigurationTemplate]?
    /// The token of the next Launch Configuration Template to retrieve.
    public var nextToken: Swift.String?

    public init(
        items: [DrsClientTypes.LaunchConfigurationTemplate]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

extension DrsClientTypes {

    /// A set of filters by which to return Recovery Instances.
    public struct DescribeRecoveryInstancesRequestFilters: Swift.Sendable {
        /// An array of Recovery Instance IDs that should be returned. An empty array means all Recovery Instances.
        public var recoveryInstanceIDs: [Swift.String]?
        /// An array of Source Server IDs for which associated Recovery Instances should be returned.
        public var sourceServerIDs: [Swift.String]?

        public init(
            recoveryInstanceIDs: [Swift.String]? = nil,
            sourceServerIDs: [Swift.String]? = nil
        )
        {
            self.recoveryInstanceIDs = recoveryInstanceIDs
            self.sourceServerIDs = sourceServerIDs
        }
    }
}

public struct DescribeRecoveryInstancesInput: Swift.Sendable {
    /// A set of filters by which to return Recovery Instances.
    public var filters: DrsClientTypes.DescribeRecoveryInstancesRequestFilters?
    /// Maximum number of Recovery Instances to retrieve.
    public var maxResults: Swift.Int?
    /// The token of the next Recovery Instance to retrieve.
    public var nextToken: Swift.String?

    public init(
        filters: DrsClientTypes.DescribeRecoveryInstancesRequestFilters? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DrsClientTypes {

    public enum FailbackReplicationError: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case agentNotSeen
        case failbackClientNotSeen
        case failedGettingReplicationState
        case failedToAttachStagingDisks
        case failedToAuthenticateWithService
        case failedToBootReplicationServer
        case failedToConfigureReplicationSoftware
        case failedToConnectAgentToReplicationServer
        case failedToCreateSecurityGroup
        case failedToCreateStagingDisks
        case failedToDownloadReplicationSoftware
        case failedToDownloadReplicationSoftwareToFailbackClient
        case failedToEstablishAgentReplicatorSoftwareCommunication
        case failedToEstablishRecoveryInstanceCommunication
        case failedToLaunchReplicationServer
        case failedToPairAgentWithReplicationSoftware
        case failedToPairReplicationServerWithAgent
        case failedToStartDataTransfer
        case notConverging
        case snapshotsFailure
        case unstableNetwork
        case sdkUnknown(Swift.String)

        public static var allCases: [FailbackReplicationError] {
            return [
                .agentNotSeen,
                .failbackClientNotSeen,
                .failedGettingReplicationState,
                .failedToAttachStagingDisks,
                .failedToAuthenticateWithService,
                .failedToBootReplicationServer,
                .failedToConfigureReplicationSoftware,
                .failedToConnectAgentToReplicationServer,
                .failedToCreateSecurityGroup,
                .failedToCreateStagingDisks,
                .failedToDownloadReplicationSoftware,
                .failedToDownloadReplicationSoftwareToFailbackClient,
                .failedToEstablishAgentReplicatorSoftwareCommunication,
                .failedToEstablishRecoveryInstanceCommunication,
                .failedToLaunchReplicationServer,
                .failedToPairAgentWithReplicationSoftware,
                .failedToPairReplicationServerWithAgent,
                .failedToStartDataTransfer,
                .notConverging,
                .snapshotsFailure,
                .unstableNetwork
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .agentNotSeen: return "AGENT_NOT_SEEN"
            case .failbackClientNotSeen: return "FAILBACK_CLIENT_NOT_SEEN"
            case .failedGettingReplicationState: return "FAILED_GETTING_REPLICATION_STATE"
            case .failedToAttachStagingDisks: return "FAILED_TO_ATTACH_STAGING_DISKS"
            case .failedToAuthenticateWithService: return "FAILED_TO_AUTHENTICATE_WITH_SERVICE"
            case .failedToBootReplicationServer: return "FAILED_TO_BOOT_REPLICATION_SERVER"
            case .failedToConfigureReplicationSoftware: return "FAILED_TO_CONFIGURE_REPLICATION_SOFTWARE"
            case .failedToConnectAgentToReplicationServer: return "FAILED_TO_CONNECT_AGENT_TO_REPLICATION_SERVER"
            case .failedToCreateSecurityGroup: return "FAILED_TO_CREATE_SECURITY_GROUP"
            case .failedToCreateStagingDisks: return "FAILED_TO_CREATE_STAGING_DISKS"
            case .failedToDownloadReplicationSoftware: return "FAILED_TO_DOWNLOAD_REPLICATION_SOFTWARE"
            case .failedToDownloadReplicationSoftwareToFailbackClient: return "FAILED_TO_DOWNLOAD_REPLICATION_SOFTWARE_TO_FAILBACK_CLIENT"
            case .failedToEstablishAgentReplicatorSoftwareCommunication: return "FAILED_TO_ESTABLISH_AGENT_REPLICATOR_SOFTWARE_COMMUNICATION"
            case .failedToEstablishRecoveryInstanceCommunication: return "FAILED_TO_ESTABLISH_RECOVERY_INSTANCE_COMMUNICATION"
            case .failedToLaunchReplicationServer: return "FAILED_TO_LAUNCH_REPLICATION_SERVER"
            case .failedToPairAgentWithReplicationSoftware: return "FAILED_TO_PAIR_AGENT_WITH_REPLICATION_SOFTWARE"
            case .failedToPairReplicationServerWithAgent: return "FAILED_TO_PAIR_REPLICATION_SERVER_WITH_AGENT"
            case .failedToStartDataTransfer: return "FAILED_TO_START_DATA_TRANSFER"
            case .notConverging: return "NOT_CONVERGING"
            case .snapshotsFailure: return "SNAPSHOTS_FAILURE"
            case .unstableNetwork: return "UNSTABLE_NETWORK"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// Error in data replication.
    public struct RecoveryInstanceDataReplicationError: Swift.Sendable {
        /// Error in data replication.
        public var error: DrsClientTypes.FailbackReplicationError?
        /// Error in data replication.
        public var rawError: Swift.String?

        public init(
            error: DrsClientTypes.FailbackReplicationError? = nil,
            rawError: Swift.String? = nil
        )
        {
            self.error = error
            self.rawError = rawError
        }
    }
}

extension DrsClientTypes {

    public enum RecoveryInstanceDataReplicationInitiationStepName: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case attachStagingDisks
        case authenticateWithService
        case bootReplicationServer
        case completeVolumeMapping
        case configureReplicationSoftware
        case connectAgentToReplicationServer
        case createSecurityGroup
        case createStagingDisks
        case downloadReplicationSoftware
        case downloadReplicationSoftwareToFailbackClient
        case establishAgentReplicatorSoftwareCommunication
        case establishRecoveryInstanceCommunication
        case launchReplicationServer
        case linkFailbackClientWithRecoveryInstance
        case pairAgentWithReplicationSoftware
        case pairReplicationServerWithAgent
        case startDataTransfer
        case wait
        case sdkUnknown(Swift.String)

        public static var allCases: [RecoveryInstanceDataReplicationInitiationStepName] {
            return [
                .attachStagingDisks,
                .authenticateWithService,
                .bootReplicationServer,
                .completeVolumeMapping,
                .configureReplicationSoftware,
                .connectAgentToReplicationServer,
                .createSecurityGroup,
                .createStagingDisks,
                .downloadReplicationSoftware,
                .downloadReplicationSoftwareToFailbackClient,
                .establishAgentReplicatorSoftwareCommunication,
                .establishRecoveryInstanceCommunication,
                .launchReplicationServer,
                .linkFailbackClientWithRecoveryInstance,
                .pairAgentWithReplicationSoftware,
                .pairReplicationServerWithAgent,
                .startDataTransfer,
                .wait
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .attachStagingDisks: return "ATTACH_STAGING_DISKS"
            case .authenticateWithService: return "AUTHENTICATE_WITH_SERVICE"
            case .bootReplicationServer: return "BOOT_REPLICATION_SERVER"
            case .completeVolumeMapping: return "COMPLETE_VOLUME_MAPPING"
            case .configureReplicationSoftware: return "CONFIGURE_REPLICATION_SOFTWARE"
            case .connectAgentToReplicationServer: return "CONNECT_AGENT_TO_REPLICATION_SERVER"
            case .createSecurityGroup: return "CREATE_SECURITY_GROUP"
            case .createStagingDisks: return "CREATE_STAGING_DISKS"
            case .downloadReplicationSoftware: return "DOWNLOAD_REPLICATION_SOFTWARE"
            case .downloadReplicationSoftwareToFailbackClient: return "DOWNLOAD_REPLICATION_SOFTWARE_TO_FAILBACK_CLIENT"
            case .establishAgentReplicatorSoftwareCommunication: return "ESTABLISH_AGENT_REPLICATOR_SOFTWARE_COMMUNICATION"
            case .establishRecoveryInstanceCommunication: return "ESTABLISH_RECOVERY_INSTANCE_COMMUNICATION"
            case .launchReplicationServer: return "LAUNCH_REPLICATION_SERVER"
            case .linkFailbackClientWithRecoveryInstance: return "LINK_FAILBACK_CLIENT_WITH_RECOVERY_INSTANCE"
            case .pairAgentWithReplicationSoftware: return "PAIR_AGENT_WITH_REPLICATION_SOFTWARE"
            case .pairReplicationServerWithAgent: return "PAIR_REPLICATION_SERVER_WITH_AGENT"
            case .startDataTransfer: return "START_DATA_TRANSFER"
            case .wait: return "WAIT"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    public enum RecoveryInstanceDataReplicationInitiationStepStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failed
        case inProgress
        case notStarted
        case skipped
        case succeeded
        case sdkUnknown(Swift.String)

        public static var allCases: [RecoveryInstanceDataReplicationInitiationStepStatus] {
            return [
                .failed,
                .inProgress,
                .notStarted,
                .skipped,
                .succeeded
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failed: return "FAILED"
            case .inProgress: return "IN_PROGRESS"
            case .notStarted: return "NOT_STARTED"
            case .skipped: return "SKIPPED"
            case .succeeded: return "SUCCEEDED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// Data replication initiation step.
    public struct RecoveryInstanceDataReplicationInitiationStep: Swift.Sendable {
        /// The name of the step.
        public var name: DrsClientTypes.RecoveryInstanceDataReplicationInitiationStepName?
        /// The status of the step.
        public var status: DrsClientTypes.RecoveryInstanceDataReplicationInitiationStepStatus?

        public init(
            name: DrsClientTypes.RecoveryInstanceDataReplicationInitiationStepName? = nil,
            status: DrsClientTypes.RecoveryInstanceDataReplicationInitiationStepStatus? = nil
        )
        {
            self.name = name
            self.status = status
        }
    }
}

extension DrsClientTypes {

    /// Data replication initiation.
    public struct RecoveryInstanceDataReplicationInitiation: Swift.Sendable {
        /// The date and time of the current attempt to initiate data replication.
        public var startDateTime: Swift.String?
        /// The steps of the current attempt to initiate data replication.
        public var steps: [DrsClientTypes.RecoveryInstanceDataReplicationInitiationStep]?

        public init(
            startDateTime: Swift.String? = nil,
            steps: [DrsClientTypes.RecoveryInstanceDataReplicationInitiationStep]? = nil
        )
        {
            self.startDateTime = startDateTime
            self.steps = steps
        }
    }
}

extension DrsClientTypes {

    public enum RecoveryInstanceDataReplicationState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case backlog
        case continuous
        case creatingSnapshot
        case disconnected
        case initialSync
        case initiating
        case notStarted
        case paused
        case replicationStateNotAvailable
        case rescan
        case stalled
        case stopped
        case sdkUnknown(Swift.String)

        public static var allCases: [RecoveryInstanceDataReplicationState] {
            return [
                .backlog,
                .continuous,
                .creatingSnapshot,
                .disconnected,
                .initialSync,
                .initiating,
                .notStarted,
                .paused,
                .replicationStateNotAvailable,
                .rescan,
                .stalled,
                .stopped
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .backlog: return "BACKLOG"
            case .continuous: return "CONTINUOUS"
            case .creatingSnapshot: return "CREATING_SNAPSHOT"
            case .disconnected: return "DISCONNECTED"
            case .initialSync: return "INITIAL_SYNC"
            case .initiating: return "INITIATING"
            case .notStarted: return "NOT_STARTED"
            case .paused: return "PAUSED"
            case .replicationStateNotAvailable: return "REPLICATION_STATE_NOT_AVAILABLE"
            case .rescan: return "RESCAN"
            case .stalled: return "STALLED"
            case .stopped: return "STOPPED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// A disk that should be replicated.
    public struct RecoveryInstanceDataReplicationInfoReplicatedDisk: Swift.Sendable {
        /// The size of the replication backlog in bytes.
        public var backloggedStorageBytes: Swift.Int
        /// The name of the device.
        public var deviceName: Swift.String?
        /// The amount of data replicated so far in bytes.
        public var replicatedStorageBytes: Swift.Int
        /// The amount of data to be rescanned in bytes.
        public var rescannedStorageBytes: Swift.Int
        /// The total amount of data to be replicated in bytes.
        public var totalStorageBytes: Swift.Int

        public init(
            backloggedStorageBytes: Swift.Int = 0,
            deviceName: Swift.String? = nil,
            replicatedStorageBytes: Swift.Int = 0,
            rescannedStorageBytes: Swift.Int = 0,
            totalStorageBytes: Swift.Int = 0
        )
        {
            self.backloggedStorageBytes = backloggedStorageBytes
            self.deviceName = deviceName
            self.replicatedStorageBytes = replicatedStorageBytes
            self.rescannedStorageBytes = rescannedStorageBytes
            self.totalStorageBytes = totalStorageBytes
        }
    }
}

extension DrsClientTypes {

    /// Information about Data Replication
    public struct RecoveryInstanceDataReplicationInfo: Swift.Sendable {
        /// Information about Data Replication
        public var dataReplicationError: DrsClientTypes.RecoveryInstanceDataReplicationError?
        /// Information about whether the data replication has been initiated.
        public var dataReplicationInitiation: DrsClientTypes.RecoveryInstanceDataReplicationInitiation?
        /// The state of the data replication.
        public var dataReplicationState: DrsClientTypes.RecoveryInstanceDataReplicationState?
        /// An estimate of when the data replication will be completed.
        public var etaDateTime: Swift.String?
        /// Data replication lag duration.
        public var lagDuration: Swift.String?
        /// The disks that should be replicated.
        public var replicatedDisks: [DrsClientTypes.RecoveryInstanceDataReplicationInfoReplicatedDisk]?
        /// AWS Availability zone into which data is being replicated.
        public var stagingAvailabilityZone: Swift.String?
        /// The ARN of the staging Outpost
        public var stagingOutpostArn: Swift.String?

        public init(
            dataReplicationError: DrsClientTypes.RecoveryInstanceDataReplicationError? = nil,
            dataReplicationInitiation: DrsClientTypes.RecoveryInstanceDataReplicationInitiation? = nil,
            dataReplicationState: DrsClientTypes.RecoveryInstanceDataReplicationState? = nil,
            etaDateTime: Swift.String? = nil,
            lagDuration: Swift.String? = nil,
            replicatedDisks: [DrsClientTypes.RecoveryInstanceDataReplicationInfoReplicatedDisk]? = nil,
            stagingAvailabilityZone: Swift.String? = nil,
            stagingOutpostArn: Swift.String? = nil
        )
        {
            self.dataReplicationError = dataReplicationError
            self.dataReplicationInitiation = dataReplicationInitiation
            self.dataReplicationState = dataReplicationState
            self.etaDateTime = etaDateTime
            self.lagDuration = lagDuration
            self.replicatedDisks = replicatedDisks
            self.stagingAvailabilityZone = stagingAvailabilityZone
            self.stagingOutpostArn = stagingOutpostArn
        }
    }
}

extension DrsClientTypes {

    public enum EC2InstanceState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case notFound
        case pending
        case running
        case shuttingDown
        case stopped
        case stopping
        case terminated
        case sdkUnknown(Swift.String)

        public static var allCases: [EC2InstanceState] {
            return [
                .notFound,
                .pending,
                .running,
                .shuttingDown,
                .stopped,
                .stopping,
                .terminated
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .notFound: return "NOT_FOUND"
            case .pending: return "PENDING"
            case .running: return "RUNNING"
            case .shuttingDown: return "SHUTTING-DOWN"
            case .stopped: return "STOPPED"
            case .stopping: return "STOPPING"
            case .terminated: return "TERMINATED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    public enum FailbackLaunchType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case drill
        case recovery
        case sdkUnknown(Swift.String)

        public static var allCases: [FailbackLaunchType] {
            return [
                .drill,
                .recovery
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .drill: return "DRILL"
            case .recovery: return "RECOVERY"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    public enum FailbackState: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case failbackCompleted
        case failbackError
        case failbackInProgress
        case failbackLaunchStateNotAvailable
        case failbackNotReadyForLaunch
        case failbackNotStarted
        case failbackReadyForLaunch
        case sdkUnknown(Swift.String)

        public static var allCases: [FailbackState] {
            return [
                .failbackCompleted,
                .failbackError,
                .failbackInProgress,
                .failbackLaunchStateNotAvailable,
                .failbackNotReadyForLaunch,
                .failbackNotStarted,
                .failbackReadyForLaunch
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .failbackCompleted: return "FAILBACK_COMPLETED"
            case .failbackError: return "FAILBACK_ERROR"
            case .failbackInProgress: return "FAILBACK_IN_PROGRESS"
            case .failbackLaunchStateNotAvailable: return "FAILBACK_LAUNCH_STATE_NOT_AVAILABLE"
            case .failbackNotReadyForLaunch: return "FAILBACK_NOT_READY_FOR_LAUNCH"
            case .failbackNotStarted: return "FAILBACK_NOT_STARTED"
            case .failbackReadyForLaunch: return "FAILBACK_READY_FOR_LAUNCH"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// An object representing failback related information of the Recovery Instance.
    public struct RecoveryInstanceFailback: Swift.Sendable {
        /// The date and time the agent on the Recovery Instance was last seen by the service.
        public var agentLastSeenByServiceDateTime: Swift.String?
        /// The amount of time that the Recovery Instance has been replicating for.
        public var elapsedReplicationDuration: Swift.String?
        /// The ID of the failback client that this Recovery Instance is associated with.
        public var failbackClientID: Swift.String?
        /// The date and time that the failback client was last seen by the service.
        public var failbackClientLastSeenByServiceDateTime: Swift.String?
        /// The date and time that the failback initiation started.
        public var failbackInitiationTime: Swift.String?
        /// The Job ID of the last failback log for this Recovery Instance.
        public var failbackJobID: Swift.String?
        /// The launch type (Recovery / Drill) of the last launch for the failback replication of this recovery instance.
        public var failbackLaunchType: DrsClientTypes.FailbackLaunchType?
        /// Whether we are failing back to the original Source Server for this Recovery Instance.
        public var failbackToOriginalServer: Swift.Bool?
        /// The date and time of the first byte that was replicated from the Recovery Instance.
        public var firstByteDateTime: Swift.String?
        /// The state of the failback process that this Recovery Instance is in.
        public var state: DrsClientTypes.FailbackState?

        public init(
            agentLastSeenByServiceDateTime: Swift.String? = nil,
            elapsedReplicationDuration: Swift.String? = nil,
            failbackClientID: Swift.String? = nil,
            failbackClientLastSeenByServiceDateTime: Swift.String? = nil,
            failbackInitiationTime: Swift.String? = nil,
            failbackJobID: Swift.String? = nil,
            failbackLaunchType: DrsClientTypes.FailbackLaunchType? = nil,
            failbackToOriginalServer: Swift.Bool? = nil,
            firstByteDateTime: Swift.String? = nil,
            state: DrsClientTypes.FailbackState? = nil
        )
        {
            self.agentLastSeenByServiceDateTime = agentLastSeenByServiceDateTime
            self.elapsedReplicationDuration = elapsedReplicationDuration
            self.failbackClientID = failbackClientID
            self.failbackClientLastSeenByServiceDateTime = failbackClientLastSeenByServiceDateTime
            self.failbackInitiationTime = failbackInitiationTime
            self.failbackJobID = failbackJobID
            self.failbackLaunchType = failbackLaunchType
            self.failbackToOriginalServer = failbackToOriginalServer
            self.firstByteDateTime = firstByteDateTime
            self.state = state
        }
    }
}

extension DrsClientTypes {

    public enum OriginEnvironment: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case aws
        case onPremises
        case sdkUnknown(Swift.String)

        public static var allCases: [OriginEnvironment] {
            return [
                .aws,
                .onPremises
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .aws: return "AWS"
            case .onPremises: return "ON_PREMISES"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// An object representing a block storage device on the Recovery Instance.
    public struct RecoveryInstanceDisk: Swift.Sendable {
        /// The amount of storage on the disk in bytes.
        public var bytes: Swift.Int
        /// The EBS Volume ID of this disk.
        public var ebsVolumeID: Swift.String?
        /// The internal device name of this disk. This is the name that is visible on the machine itself and not from the EC2 console.
        public var internalDeviceName: Swift.String?

        public init(
            bytes: Swift.Int = 0,
            ebsVolumeID: Swift.String? = nil,
            internalDeviceName: Swift.String? = nil
        )
        {
            self.bytes = bytes
            self.ebsVolumeID = ebsVolumeID
            self.internalDeviceName = internalDeviceName
        }
    }
}

extension DrsClientTypes {

    /// Properties of the Recovery Instance machine.
    public struct RecoveryInstanceProperties: Swift.Sendable {
        /// An array of CPUs.
        public var cpus: [DrsClientTypes.CPU]?
        /// An array of disks.
        public var disks: [DrsClientTypes.RecoveryInstanceDisk]?
        /// Hints used to uniquely identify a machine.
        public var identificationHints: DrsClientTypes.IdentificationHints?
        /// The date and time the Recovery Instance properties were last updated on.
        public var lastUpdatedDateTime: Swift.String?
        /// An array of network interfaces.
        public var networkInterfaces: [DrsClientTypes.NetworkInterface]?
        /// Operating system.
        public var os: DrsClientTypes.OS?
        /// The amount of RAM in bytes.
        public var ramBytes: Swift.Int

        public init(
            cpus: [DrsClientTypes.CPU]? = nil,
            disks: [DrsClientTypes.RecoveryInstanceDisk]? = nil,
            identificationHints: DrsClientTypes.IdentificationHints? = nil,
            lastUpdatedDateTime: Swift.String? = nil,
            networkInterfaces: [DrsClientTypes.NetworkInterface]? = nil,
            os: DrsClientTypes.OS? = nil,
            ramBytes: Swift.Int = 0
        )
        {
            self.cpus = cpus
            self.disks = disks
            self.identificationHints = identificationHints
            self.lastUpdatedDateTime = lastUpdatedDateTime
            self.networkInterfaces = networkInterfaces
            self.os = os
            self.ramBytes = ramBytes
        }
    }
}

extension DrsClientTypes {

    /// A Recovery Instance is a replica of a Source Server running on EC2.
    public struct RecoveryInstance: Swift.Sendable {
        /// The version of the DRS agent installed on the recovery instance
        public var agentVersion: Swift.String?
        /// The ARN of the Recovery Instance.
        public var arn: Swift.String?
        /// The Data Replication Info of the Recovery Instance.
        public var dataReplicationInfo: DrsClientTypes.RecoveryInstanceDataReplicationInfo?
        /// The EC2 instance ID of the Recovery Instance.
        public var ec2InstanceID: Swift.String?
        /// The state of the EC2 instance for this Recovery Instance.
        public var ec2InstanceState: DrsClientTypes.EC2InstanceState?
        /// An object representing failback related information of the Recovery Instance.
        public var failback: DrsClientTypes.RecoveryInstanceFailback?
        /// Whether this Recovery Instance was created for a drill or for an actual Recovery event.
        public var isDrill: Swift.Bool?
        /// The ID of the Job that created the Recovery Instance.
        public var jobID: Swift.String?
        /// AWS availability zone associated with the recovery instance.
        public var originAvailabilityZone: Swift.String?
        /// Environment (On Premises / AWS) of the instance that the recovery instance originated from.
        public var originEnvironment: DrsClientTypes.OriginEnvironment?
        /// The date and time of the Point in Time (PIT) snapshot that this Recovery Instance was launched from.
        public var pointInTimeSnapshotDateTime: Swift.String?
        /// The ID of the Recovery Instance.
        public var recoveryInstanceID: Swift.String?
        /// Properties of the Recovery Instance machine.
        public var recoveryInstanceProperties: DrsClientTypes.RecoveryInstanceProperties?
        /// The ARN of the source Outpost
        public var sourceOutpostArn: Swift.String?
        /// The Source Server ID that this Recovery Instance is associated with.
        public var sourceServerID: Swift.String?
        /// An array of tags that are associated with the Recovery Instance.
        public var tags: [Swift.String: Swift.String]?

        public init(
            agentVersion: Swift.String? = nil,
            arn: Swift.String? = nil,
            dataReplicationInfo: DrsClientTypes.RecoveryInstanceDataReplicationInfo? = nil,
            ec2InstanceID: Swift.String? = nil,
            ec2InstanceState: DrsClientTypes.EC2InstanceState? = nil,
            failback: DrsClientTypes.RecoveryInstanceFailback? = nil,
            isDrill: Swift.Bool? = nil,
            jobID: Swift.String? = nil,
            originAvailabilityZone: Swift.String? = nil,
            originEnvironment: DrsClientTypes.OriginEnvironment? = nil,
            pointInTimeSnapshotDateTime: Swift.String? = nil,
            recoveryInstanceID: Swift.String? = nil,
            recoveryInstanceProperties: DrsClientTypes.RecoveryInstanceProperties? = nil,
            sourceOutpostArn: Swift.String? = nil,
            sourceServerID: Swift.String? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.agentVersion = agentVersion
            self.arn = arn
            self.dataReplicationInfo = dataReplicationInfo
            self.ec2InstanceID = ec2InstanceID
            self.ec2InstanceState = ec2InstanceState
            self.failback = failback
            self.isDrill = isDrill
            self.jobID = jobID
            self.originAvailabilityZone = originAvailabilityZone
            self.originEnvironment = originEnvironment
            self.pointInTimeSnapshotDateTime = pointInTimeSnapshotDateTime
            self.recoveryInstanceID = recoveryInstanceID
            self.recoveryInstanceProperties = recoveryInstanceProperties
            self.sourceOutpostArn = sourceOutpostArn
            self.sourceServerID = sourceServerID
            self.tags = tags
        }
    }
}

extension DrsClientTypes.RecoveryInstance: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "RecoveryInstance(agentVersion: \(Swift.String(describing: agentVersion)), arn: \(Swift.String(describing: arn)), dataReplicationInfo: \(Swift.String(describing: dataReplicationInfo)), ec2InstanceID: \(Swift.String(describing: ec2InstanceID)), ec2InstanceState: \(Swift.String(describing: ec2InstanceState)), failback: \(Swift.String(describing: failback)), isDrill: \(Swift.String(describing: isDrill)), jobID: \(Swift.String(describing: jobID)), originAvailabilityZone: \(Swift.String(describing: originAvailabilityZone)), originEnvironment: \(Swift.String(describing: originEnvironment)), pointInTimeSnapshotDateTime: \(Swift.String(describing: pointInTimeSnapshotDateTime)), recoveryInstanceID: \(Swift.String(describing: recoveryInstanceID)), recoveryInstanceProperties: \(Swift.String(describing: recoveryInstanceProperties)), sourceOutpostArn: \(Swift.String(describing: sourceOutpostArn)), sourceServerID: \(Swift.String(describing: sourceServerID)), tags: \"CONTENT_REDACTED\")"}
}

public struct DescribeRecoveryInstancesOutput: Swift.Sendable {
    /// An array of Recovery Instances.
    public var items: [DrsClientTypes.RecoveryInstance]?
    /// The token of the next Recovery Instance to retrieve.
    public var nextToken: Swift.String?

    public init(
        items: [DrsClientTypes.RecoveryInstance]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

extension DrsClientTypes {

    /// A set of filters by which to return Recovery Snapshots.
    public struct DescribeRecoverySnapshotsRequestFilters: Swift.Sendable {
        /// The start date in a date range query.
        public var fromDateTime: Swift.String?
        /// The end date in a date range query.
        public var toDateTime: Swift.String?

        public init(
            fromDateTime: Swift.String? = nil,
            toDateTime: Swift.String? = nil
        )
        {
            self.fromDateTime = fromDateTime
            self.toDateTime = toDateTime
        }
    }
}

extension DrsClientTypes {

    public enum RecoverySnapshotsOrder: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case asc
        case desc
        case sdkUnknown(Swift.String)

        public static var allCases: [RecoverySnapshotsOrder] {
            return [
                .asc,
                .desc
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .asc: return "ASC"
            case .desc: return "DESC"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

public struct DescribeRecoverySnapshotsInput: Swift.Sendable {
    /// A set of filters by which to return Recovery Snapshots.
    public var filters: DrsClientTypes.DescribeRecoverySnapshotsRequestFilters?
    /// Maximum number of Recovery Snapshots to retrieve.
    public var maxResults: Swift.Int?
    /// The token of the next Recovery Snapshot to retrieve.
    public var nextToken: Swift.String?
    /// The sorted ordering by which to return Recovery Snapshots.
    public var order: DrsClientTypes.RecoverySnapshotsOrder?
    /// Filter Recovery Snapshots by Source Server ID.
    /// This member is required.
    public var sourceServerID: Swift.String?

    public init(
        filters: DrsClientTypes.DescribeRecoverySnapshotsRequestFilters? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        order: DrsClientTypes.RecoverySnapshotsOrder? = nil,
        sourceServerID: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.order = order
        self.sourceServerID = sourceServerID
    }
}

extension DrsClientTypes {

    /// A snapshot of a Source Server used during recovery.
    public struct RecoverySnapshot: Swift.Sendable {
        /// A list of EBS snapshots.
        public var ebsSnapshots: [Swift.String]?
        /// The timestamp of when we expect the snapshot to be taken.
        /// This member is required.
        public var expectedTimestamp: Swift.String?
        /// The ID of the Recovery Snapshot.
        /// This member is required.
        public var snapshotID: Swift.String?
        /// The ID of the Source Server that the snapshot was taken for.
        /// This member is required.
        public var sourceServerID: Swift.String?
        /// The actual timestamp that the snapshot was taken.
        public var timestamp: Swift.String?

        public init(
            ebsSnapshots: [Swift.String]? = nil,
            expectedTimestamp: Swift.String? = nil,
            snapshotID: Swift.String? = nil,
            sourceServerID: Swift.String? = nil,
            timestamp: Swift.String? = nil
        )
        {
            self.ebsSnapshots = ebsSnapshots
            self.expectedTimestamp = expectedTimestamp
            self.snapshotID = snapshotID
            self.sourceServerID = sourceServerID
            self.timestamp = timestamp
        }
    }
}

public struct DescribeRecoverySnapshotsOutput: Swift.Sendable {
    /// An array of Recovery Snapshots.
    public var items: [DrsClientTypes.RecoverySnapshot]?
    /// The token of the next Recovery Snapshot to retrieve.
    public var nextToken: Swift.String?

    public init(
        items: [DrsClientTypes.RecoverySnapshot]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

public struct DescribeReplicationConfigurationTemplatesInput: Swift.Sendable {
    /// Maximum number of Replication Configuration Templates to retrieve.
    public var maxResults: Swift.Int?
    /// The token of the next Replication Configuration Template to retrieve.
    public var nextToken: Swift.String?
    /// The IDs of the Replication Configuration Templates to retrieve. An empty list means all Replication Configuration Templates.
    public var replicationConfigurationTemplateIDs: [Swift.String]?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        replicationConfigurationTemplateIDs: [Swift.String]? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.replicationConfigurationTemplateIDs = replicationConfigurationTemplateIDs
    }
}

extension DrsClientTypes {

    public struct ReplicationConfigurationTemplate: Swift.Sendable {
        /// The Replication Configuration Template ARN.
        public var arn: Swift.String?
        /// Whether to associate the default Elastic Disaster Recovery Security group with the Replication Configuration Template.
        public var associateDefaultSecurityGroup: Swift.Bool?
        /// Whether to allow the AWS replication agent to automatically replicate newly added disks.
        public var autoReplicateNewDisks: Swift.Bool?
        /// Configure bandwidth throttling for the outbound data transfer rate of the Source Server in Mbps.
        public var bandwidthThrottling: Swift.Int
        /// Whether to create a Public IP for the Recovery Instance by default.
        public var createPublicIP: Swift.Bool?
        /// The data plane routing mechanism that will be used for replication.
        public var dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting?
        /// The Staging Disk EBS volume type to be used during replication.
        public var defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType?
        /// The type of EBS encryption to be used during replication.
        public var ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption?
        /// The ARN of the EBS encryption key to be used during replication.
        public var ebsEncryptionKeyArn: Swift.String?
        /// The Point in time (PIT) policy to manage snapshots taken during replication.
        public var pitPolicy: [DrsClientTypes.PITPolicyRule]?
        /// The Replication Configuration Template ID.
        /// This member is required.
        public var replicationConfigurationTemplateID: Swift.String?
        /// The instance type to be used for the replication server.
        public var replicationServerInstanceType: Swift.String?
        /// The security group IDs that will be used by the replication server.
        public var replicationServersSecurityGroupsIDs: [Swift.String]?
        /// The subnet to be used by the replication staging area.
        public var stagingAreaSubnetId: Swift.String?
        /// A set of tags to be associated with all resources created in the replication staging area: EC2 replication server, EBS volumes, EBS snapshots, etc.
        public var stagingAreaTags: [Swift.String: Swift.String]?
        /// A set of tags to be associated with the Replication Configuration Template resource.
        public var tags: [Swift.String: Swift.String]?
        /// Whether to use a dedicated Replication Server in the replication staging area.
        public var useDedicatedReplicationServer: Swift.Bool?

        public init(
            arn: Swift.String? = nil,
            associateDefaultSecurityGroup: Swift.Bool? = nil,
            autoReplicateNewDisks: Swift.Bool? = nil,
            bandwidthThrottling: Swift.Int = 0,
            createPublicIP: Swift.Bool? = nil,
            dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting? = nil,
            defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType? = nil,
            ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption? = nil,
            ebsEncryptionKeyArn: Swift.String? = nil,
            pitPolicy: [DrsClientTypes.PITPolicyRule]? = nil,
            replicationConfigurationTemplateID: Swift.String? = nil,
            replicationServerInstanceType: Swift.String? = nil,
            replicationServersSecurityGroupsIDs: [Swift.String]? = nil,
            stagingAreaSubnetId: Swift.String? = nil,
            stagingAreaTags: [Swift.String: Swift.String]? = nil,
            tags: [Swift.String: Swift.String]? = nil,
            useDedicatedReplicationServer: Swift.Bool? = nil
        )
        {
            self.arn = arn
            self.associateDefaultSecurityGroup = associateDefaultSecurityGroup
            self.autoReplicateNewDisks = autoReplicateNewDisks
            self.bandwidthThrottling = bandwidthThrottling
            self.createPublicIP = createPublicIP
            self.dataPlaneRouting = dataPlaneRouting
            self.defaultLargeStagingDiskType = defaultLargeStagingDiskType
            self.ebsEncryption = ebsEncryption
            self.ebsEncryptionKeyArn = ebsEncryptionKeyArn
            self.pitPolicy = pitPolicy
            self.replicationConfigurationTemplateID = replicationConfigurationTemplateID
            self.replicationServerInstanceType = replicationServerInstanceType
            self.replicationServersSecurityGroupsIDs = replicationServersSecurityGroupsIDs
            self.stagingAreaSubnetId = stagingAreaSubnetId
            self.stagingAreaTags = stagingAreaTags
            self.tags = tags
            self.useDedicatedReplicationServer = useDedicatedReplicationServer
        }
    }
}

extension DrsClientTypes.ReplicationConfigurationTemplate: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ReplicationConfigurationTemplate(arn: \(Swift.String(describing: arn)), associateDefaultSecurityGroup: \(Swift.String(describing: associateDefaultSecurityGroup)), autoReplicateNewDisks: \(Swift.String(describing: autoReplicateNewDisks)), bandwidthThrottling: \(Swift.String(describing: bandwidthThrottling)), createPublicIP: \(Swift.String(describing: createPublicIP)), dataPlaneRouting: \(Swift.String(describing: dataPlaneRouting)), defaultLargeStagingDiskType: \(Swift.String(describing: defaultLargeStagingDiskType)), ebsEncryption: \(Swift.String(describing: ebsEncryption)), ebsEncryptionKeyArn: \(Swift.String(describing: ebsEncryptionKeyArn)), pitPolicy: \(Swift.String(describing: pitPolicy)), replicationConfigurationTemplateID: \(Swift.String(describing: replicationConfigurationTemplateID)), replicationServerInstanceType: \(Swift.String(describing: replicationServerInstanceType)), replicationServersSecurityGroupsIDs: \(Swift.String(describing: replicationServersSecurityGroupsIDs)), stagingAreaSubnetId: \(Swift.String(describing: stagingAreaSubnetId)), useDedicatedReplicationServer: \(Swift.String(describing: useDedicatedReplicationServer)), stagingAreaTags: \"CONTENT_REDACTED\", tags: \"CONTENT_REDACTED\")"}
}

public struct DescribeReplicationConfigurationTemplatesOutput: Swift.Sendable {
    /// An array of Replication Configuration Templates.
    public var items: [DrsClientTypes.ReplicationConfigurationTemplate]?
    /// The token of the next Replication Configuration Template to retrieve.
    public var nextToken: Swift.String?

    public init(
        items: [DrsClientTypes.ReplicationConfigurationTemplate]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

extension DrsClientTypes {

    /// A set of filters by which to return Source Networks.
    public struct DescribeSourceNetworksRequestFilters: Swift.Sendable {
        /// Filter Source Networks by account ID containing the protected VPCs.
        public var originAccountID: Swift.String?
        /// Filter Source Networks by the region containing the protected VPCs.
        public var originRegion: Swift.String?
        /// An array of Source Network IDs that should be returned. An empty array means all Source Networks.
        public var sourceNetworkIDs: [Swift.String]?

        public init(
            originAccountID: Swift.String? = nil,
            originRegion: Swift.String? = nil,
            sourceNetworkIDs: [Swift.String]? = nil
        )
        {
            self.originAccountID = originAccountID
            self.originRegion = originRegion
            self.sourceNetworkIDs = sourceNetworkIDs
        }
    }
}

public struct DescribeSourceNetworksInput: Swift.Sendable {
    /// A set of filters by which to return Source Networks.
    public var filters: DrsClientTypes.DescribeSourceNetworksRequestFilters?
    /// Maximum number of Source Networks to retrieve.
    public var maxResults: Swift.Int?
    /// The token of the next Source Networks to retrieve.
    public var nextToken: Swift.String?

    public init(
        filters: DrsClientTypes.DescribeSourceNetworksRequestFilters? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension DrsClientTypes {

    public enum RecoveryResult: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case associateFail
        case associateSuccess
        case fail
        case inProgress
        case notStarted
        case partialSuccess
        case success
        case sdkUnknown(Swift.String)

        public static var allCases: [RecoveryResult] {
            return [
                .associateFail,
                .associateSuccess,
                .fail,
                .inProgress,
                .notStarted,
                .partialSuccess,
                .success
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .associateFail: return "ASSOCIATE_FAIL"
            case .associateSuccess: return "ASSOCIATE_SUCCESS"
            case .fail: return "FAIL"
            case .inProgress: return "IN_PROGRESS"
            case .notStarted: return "NOT_STARTED"
            case .partialSuccess: return "PARTIAL_SUCCESS"
            case .success: return "SUCCESS"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// An object representing the Source Network recovery Lifecycle.
    public struct RecoveryLifeCycle: Swift.Sendable {
        /// The date and time the last Source Network recovery was initiated.
        public var apiCallDateTime: Foundation.Date?
        /// The ID of the Job that was used to last recover the Source Network.
        public var jobID: Swift.String?
        /// The status of the last recovery status of this Source Network.
        public var lastRecoveryResult: DrsClientTypes.RecoveryResult?

        public init(
            apiCallDateTime: Foundation.Date? = nil,
            jobID: Swift.String? = nil,
            lastRecoveryResult: DrsClientTypes.RecoveryResult? = nil
        )
        {
            self.apiCallDateTime = apiCallDateTime
            self.jobID = jobID
            self.lastRecoveryResult = lastRecoveryResult
        }
    }
}

extension DrsClientTypes {

    public enum ReplicationStatus: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case error
        case inProgress
        case protected
        case stopped
        case sdkUnknown(Swift.String)

        public static var allCases: [ReplicationStatus] {
            return [
                .error,
                .inProgress,
                .protected,
                .stopped
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .error: return "ERROR"
            case .inProgress: return "IN_PROGRESS"
            case .protected: return "PROTECTED"
            case .stopped: return "STOPPED"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// The ARN of the Source Network.
    public struct SourceNetwork: Swift.Sendable {
        /// The ARN of the Source Network.
        public var arn: Swift.String?
        /// CloudFormation stack name that was deployed for recovering the Source Network.
        public var cfnStackName: Swift.String?
        /// An object containing information regarding the last recovery of the Source Network.
        public var lastRecovery: DrsClientTypes.RecoveryLifeCycle?
        /// ID of the recovered VPC following Source Network recovery.
        public var launchedVpcID: Swift.String?
        /// Status of Source Network Replication. Possible values: (a) STOPPED - Source Network is not replicating. (b) IN_PROGRESS - Source Network is being replicated. (c) PROTECTED - Source Network was replicated successfully and is being synchronized for changes. (d) ERROR - Source Network replication has failed
        public var replicationStatus: DrsClientTypes.ReplicationStatus?
        /// Error details in case Source Network replication status is ERROR.
        public var replicationStatusDetails: Swift.String?
        /// Account ID containing the VPC protected by the Source Network.
        public var sourceAccountID: Swift.String?
        /// Source Network ID.
        public var sourceNetworkID: Swift.String?
        /// Region containing the VPC protected by the Source Network.
        public var sourceRegion: Swift.String?
        /// VPC ID protected by the Source Network.
        public var sourceVpcID: Swift.String?
        /// A list of tags associated with the Source Network.
        public var tags: [Swift.String: Swift.String]?

        public init(
            arn: Swift.String? = nil,
            cfnStackName: Swift.String? = nil,
            lastRecovery: DrsClientTypes.RecoveryLifeCycle? = nil,
            launchedVpcID: Swift.String? = nil,
            replicationStatus: DrsClientTypes.ReplicationStatus? = nil,
            replicationStatusDetails: Swift.String? = nil,
            sourceAccountID: Swift.String? = nil,
            sourceNetworkID: Swift.String? = nil,
            sourceRegion: Swift.String? = nil,
            sourceVpcID: Swift.String? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.arn = arn
            self.cfnStackName = cfnStackName
            self.lastRecovery = lastRecovery
            self.launchedVpcID = launchedVpcID
            self.replicationStatus = replicationStatus
            self.replicationStatusDetails = replicationStatusDetails
            self.sourceAccountID = sourceAccountID
            self.sourceNetworkID = sourceNetworkID
            self.sourceRegion = sourceRegion
            self.sourceVpcID = sourceVpcID
            self.tags = tags
        }
    }
}

extension DrsClientTypes.SourceNetwork: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "SourceNetwork(arn: \(Swift.String(describing: arn)), lastRecovery: \(Swift.String(describing: lastRecovery)), launchedVpcID: \(Swift.String(describing: launchedVpcID)), replicationStatus: \(Swift.String(describing: replicationStatus)), sourceAccountID: \(Swift.String(describing: sourceAccountID)), sourceNetworkID: \(Swift.String(describing: sourceNetworkID)), sourceRegion: \(Swift.String(describing: sourceRegion)), sourceVpcID: \(Swift.String(describing: sourceVpcID)), cfnStackName: \"CONTENT_REDACTED\", replicationStatusDetails: \"CONTENT_REDACTED\", tags: \"CONTENT_REDACTED\")"}
}

public struct DescribeSourceNetworksOutput: Swift.Sendable {
    /// An array of Source Networks.
    public var items: [DrsClientTypes.SourceNetwork]?
    /// The token of the next Source Networks to retrieve.
    public var nextToken: Swift.String?

    public init(
        items: [DrsClientTypes.SourceNetwork]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

extension DrsClientTypes {

    /// A set of filters by which to return Source Servers.
    public struct DescribeSourceServersRequestFilters: Swift.Sendable {
        /// An ID that describes the hardware of the Source Server. This is either an EC2 instance id, a VMware uuid or a mac address.
        public var hardwareId: Swift.String?
        /// An array of Source Servers IDs that should be returned. An empty array means all Source Servers.
        public var sourceServerIDs: [Swift.String]?
        /// An array of staging account IDs that extended source servers belong to. An empty array means all source servers will be shown.
        public var stagingAccountIDs: [Swift.String]?

        public init(
            hardwareId: Swift.String? = nil,
            sourceServerIDs: [Swift.String]? = nil,
            stagingAccountIDs: [Swift.String]? = nil
        )
        {
            self.hardwareId = hardwareId
            self.sourceServerIDs = sourceServerIDs
            self.stagingAccountIDs = stagingAccountIDs
        }
    }
}

public struct DescribeSourceServersInput: Swift.Sendable {
    /// A set of filters by which to return Source Servers.
    public var filters: DrsClientTypes.DescribeSourceServersRequestFilters?
    /// Maximum number of Source Servers to retrieve.
    public var maxResults: Swift.Int?
    /// The token of the next Source Server to retrieve.
    public var nextToken: Swift.String?

    public init(
        filters: DrsClientTypes.DescribeSourceServersRequestFilters? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct DescribeSourceServersOutput: Swift.Sendable {
    /// An array of Source Servers.
    public var items: [DrsClientTypes.SourceServer]?
    /// The token of the next Source Server to retrieve.
    public var nextToken: Swift.String?

    public init(
        items: [DrsClientTypes.SourceServer]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

public struct DisconnectRecoveryInstanceInput: Swift.Sendable {
    /// The ID of the Recovery Instance to disconnect.
    /// This member is required.
    public var recoveryInstanceID: Swift.String?

    public init(
        recoveryInstanceID: Swift.String? = nil
    )
    {
        self.recoveryInstanceID = recoveryInstanceID
    }
}

public struct DisconnectSourceServerInput: Swift.Sendable {
    /// The ID of the Source Server to disconnect.
    /// This member is required.
    public var sourceServerID: Swift.String?

    public init(
        sourceServerID: Swift.String? = nil
    )
    {
        self.sourceServerID = sourceServerID
    }
}

public struct DisconnectSourceServerOutput: Swift.Sendable {
    /// The version of the DRS agent installed on the source server
    public var agentVersion: Swift.String?
    /// The ARN of the Source Server.
    public var arn: Swift.String?
    /// The Data Replication Info of the Source Server.
    public var dataReplicationInfo: DrsClientTypes.DataReplicationInfo?
    /// The status of the last recovery launch of this Source Server.
    public var lastLaunchResult: DrsClientTypes.LastLaunchResult?
    /// The lifecycle information of this Source Server.
    public var lifeCycle: DrsClientTypes.LifeCycle?
    /// The ID of the Recovery Instance associated with this Source Server.
    public var recoveryInstanceId: Swift.String?
    /// Replication direction of the Source Server.
    public var replicationDirection: DrsClientTypes.ReplicationDirection?
    /// For EC2-originated Source Servers which have been failed over and then failed back, this value will mean the ARN of the Source Server on the opposite replication direction.
    public var reversedDirectionSourceServerArn: Swift.String?
    /// Source cloud properties of the Source Server.
    public var sourceCloudProperties: DrsClientTypes.SourceCloudProperties?
    /// ID of the Source Network which is protecting this Source Server's network.
    public var sourceNetworkID: Swift.String?
    /// The source properties of the Source Server.
    public var sourceProperties: DrsClientTypes.SourceProperties?
    /// The ID of the Source Server.
    public var sourceServerID: Swift.String?
    /// The staging area of the source server.
    public var stagingArea: DrsClientTypes.StagingArea?
    /// The tags associated with the Source Server.
    public var tags: [Swift.String: Swift.String]?

    public init(
        agentVersion: Swift.String? = nil,
        arn: Swift.String? = nil,
        dataReplicationInfo: DrsClientTypes.DataReplicationInfo? = nil,
        lastLaunchResult: DrsClientTypes.LastLaunchResult? = nil,
        lifeCycle: DrsClientTypes.LifeCycle? = nil,
        recoveryInstanceId: Swift.String? = nil,
        replicationDirection: DrsClientTypes.ReplicationDirection? = nil,
        reversedDirectionSourceServerArn: Swift.String? = nil,
        sourceCloudProperties: DrsClientTypes.SourceCloudProperties? = nil,
        sourceNetworkID: Swift.String? = nil,
        sourceProperties: DrsClientTypes.SourceProperties? = nil,
        sourceServerID: Swift.String? = nil,
        stagingArea: DrsClientTypes.StagingArea? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.agentVersion = agentVersion
        self.arn = arn
        self.dataReplicationInfo = dataReplicationInfo
        self.lastLaunchResult = lastLaunchResult
        self.lifeCycle = lifeCycle
        self.recoveryInstanceId = recoveryInstanceId
        self.replicationDirection = replicationDirection
        self.reversedDirectionSourceServerArn = reversedDirectionSourceServerArn
        self.sourceCloudProperties = sourceCloudProperties
        self.sourceNetworkID = sourceNetworkID
        self.sourceProperties = sourceProperties
        self.sourceServerID = sourceServerID
        self.stagingArea = stagingArea
        self.tags = tags
    }
}

extension DisconnectSourceServerOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "DisconnectSourceServerOutput(agentVersion: \(Swift.String(describing: agentVersion)), arn: \(Swift.String(describing: arn)), dataReplicationInfo: \(Swift.String(describing: dataReplicationInfo)), lastLaunchResult: \(Swift.String(describing: lastLaunchResult)), lifeCycle: \(Swift.String(describing: lifeCycle)), recoveryInstanceId: \(Swift.String(describing: recoveryInstanceId)), replicationDirection: \(Swift.String(describing: replicationDirection)), reversedDirectionSourceServerArn: \(Swift.String(describing: reversedDirectionSourceServerArn)), sourceCloudProperties: \(Swift.String(describing: sourceCloudProperties)), sourceNetworkID: \(Swift.String(describing: sourceNetworkID)), sourceProperties: \(Swift.String(describing: sourceProperties)), sourceServerID: \(Swift.String(describing: sourceServerID)), stagingArea: \(Swift.String(describing: stagingArea)), tags: \"CONTENT_REDACTED\")"}
}

public struct InitializeServiceInput: Swift.Sendable {

    public init() { }
}

public struct InitializeServiceOutput: Swift.Sendable {

    public init() { }
}

public struct UpdateLaunchConfigurationTemplateInput: Swift.Sendable {
    /// Copy private IP.
    public var copyPrivateIp: Swift.Bool?
    /// Copy tags.
    public var copyTags: Swift.Bool?
    /// S3 bucket ARN to export Source Network templates.
    public var exportBucketArn: Swift.String?
    /// Launch Configuration Template ID.
    /// This member is required.
    public var launchConfigurationTemplateID: Swift.String?
    /// Launch disposition.
    public var launchDisposition: DrsClientTypes.LaunchDisposition?
    /// DRS will set the 'launch into instance ID' of any source server when performing a drill, recovery or failback to the previous region or availability zone, using the instance ID of the source instance.
    public var launchIntoSourceInstance: Swift.Bool?
    /// Licensing.
    public var licensing: DrsClientTypes.Licensing?
    /// Whether we want to activate post-launch actions.
    public var postLaunchEnabled: Swift.Bool?
    /// Target instance type right-sizing method.
    public var targetInstanceTypeRightSizingMethod: DrsClientTypes.TargetInstanceTypeRightSizingMethod?

    public init(
        copyPrivateIp: Swift.Bool? = nil,
        copyTags: Swift.Bool? = nil,
        exportBucketArn: Swift.String? = nil,
        launchConfigurationTemplateID: Swift.String? = nil,
        launchDisposition: DrsClientTypes.LaunchDisposition? = nil,
        launchIntoSourceInstance: Swift.Bool? = nil,
        licensing: DrsClientTypes.Licensing? = nil,
        postLaunchEnabled: Swift.Bool? = nil,
        targetInstanceTypeRightSizingMethod: DrsClientTypes.TargetInstanceTypeRightSizingMethod? = nil
    )
    {
        self.copyPrivateIp = copyPrivateIp
        self.copyTags = copyTags
        self.exportBucketArn = exportBucketArn
        self.launchConfigurationTemplateID = launchConfigurationTemplateID
        self.launchDisposition = launchDisposition
        self.launchIntoSourceInstance = launchIntoSourceInstance
        self.licensing = licensing
        self.postLaunchEnabled = postLaunchEnabled
        self.targetInstanceTypeRightSizingMethod = targetInstanceTypeRightSizingMethod
    }
}

public struct UpdateLaunchConfigurationTemplateOutput: Swift.Sendable {
    /// Updated Launch Configuration Template.
    public var launchConfigurationTemplate: DrsClientTypes.LaunchConfigurationTemplate?

    public init(
        launchConfigurationTemplate: DrsClientTypes.LaunchConfigurationTemplate? = nil
    )
    {
        self.launchConfigurationTemplate = launchConfigurationTemplate
    }
}

public struct ListExtensibleSourceServersInput: Swift.Sendable {
    /// The maximum number of extensible source servers to retrieve.
    public var maxResults: Swift.Int?
    /// The token of the next extensible source server to retrieve.
    public var nextToken: Swift.String?
    /// The Id of the staging Account to retrieve extensible source servers from.
    /// This member is required.
    public var stagingAccountID: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        stagingAccountID: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.stagingAccountID = stagingAccountID
    }
}

extension DrsClientTypes {

    /// Source server in staging account that extended source server connected to.
    public struct StagingSourceServer: Swift.Sendable {
        /// The ARN of the source server.
        public var arn: Swift.String?
        /// Hostname of staging source server.
        public var hostname: Swift.String?
        /// A list of tags associated with the staging source server.
        public var tags: [Swift.String: Swift.String]?

        public init(
            arn: Swift.String? = nil,
            hostname: Swift.String? = nil,
            tags: [Swift.String: Swift.String]? = nil
        )
        {
            self.arn = arn
            self.hostname = hostname
            self.tags = tags
        }
    }
}

extension DrsClientTypes.StagingSourceServer: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "StagingSourceServer(arn: \(Swift.String(describing: arn)), hostname: \(Swift.String(describing: hostname)), tags: \"CONTENT_REDACTED\")"}
}

public struct ListExtensibleSourceServersOutput: Swift.Sendable {
    /// A list of source servers on a staging Account that are extensible.
    public var items: [DrsClientTypes.StagingSourceServer]?
    /// The token of the next extensible source server to retrieve.
    public var nextToken: Swift.String?

    public init(
        items: [DrsClientTypes.StagingSourceServer]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

extension DrsClientTypes {

    /// Resource launch actions filter.
    public struct LaunchActionsRequestFilters: Swift.Sendable {
        /// Launch actions Ids.
        public var actionIds: [Swift.String]?

        public init(
            actionIds: [Swift.String]? = nil
        )
        {
            self.actionIds = actionIds
        }
    }
}

public struct ListLaunchActionsInput: Swift.Sendable {
    /// Filters to apply when listing resource launch actions.
    public var filters: DrsClientTypes.LaunchActionsRequestFilters?
    /// Maximum amount of items to return when listing resource launch actions.
    public var maxResults: Swift.Int?
    /// Next token to use when listing resource launch actions.
    public var nextToken: Swift.String?
    /// Launch configuration template Id or Source Server Id
    /// This member is required.
    public var resourceId: Swift.String?

    public init(
        filters: DrsClientTypes.LaunchActionsRequestFilters? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil,
        resourceId: Swift.String? = nil
    )
    {
        self.filters = filters
        self.maxResults = maxResults
        self.nextToken = nextToken
        self.resourceId = resourceId
    }
}

public struct ListLaunchActionsOutput: Swift.Sendable {
    /// List of resource launch actions.
    public var items: [DrsClientTypes.LaunchAction]?
    /// Next token returned when listing resource launch actions.
    public var nextToken: Swift.String?

    public init(
        items: [DrsClientTypes.LaunchAction]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.items = items
        self.nextToken = nextToken
    }
}

public struct ListStagingAccountsInput: Swift.Sendable {
    /// The maximum number of staging Accounts to retrieve.
    public var maxResults: Swift.Int?
    /// The token of the next staging Account to retrieve.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

public struct ListStagingAccountsOutput: Swift.Sendable {
    /// An array of staging AWS Accounts.
    public var accounts: [DrsClientTypes.Account]?
    /// The token of the next staging Account to retrieve.
    public var nextToken: Swift.String?

    public init(
        accounts: [DrsClientTypes.Account]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.accounts = accounts
        self.nextToken = nextToken
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The ARN of the resource whose tags should be returned.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// The tags of the requested resource.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

extension ListTagsForResourceOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "ListTagsForResourceOutput(tags: \"CONTENT_REDACTED\")"}
}

public struct PutLaunchActionInput: Swift.Sendable {
    /// Launch action code.
    /// This member is required.
    public var actionCode: Swift.String?
    /// Launch action Id.
    /// This member is required.
    public var actionId: Swift.String?
    /// Launch action version.
    /// This member is required.
    public var actionVersion: Swift.String?
    /// Whether the launch action is active.
    /// This member is required.
    public var active: Swift.Bool?
    /// Launch action category.
    /// This member is required.
    public var category: DrsClientTypes.LaunchActionCategory?
    /// Launch action description.
    /// This member is required.
    public var description: Swift.String?
    /// Launch action name.
    /// This member is required.
    public var name: Swift.String?
    /// Whether the launch will not be marked as failed if this action fails.
    /// This member is required.
    public var `optional`: Swift.Bool?
    /// Launch action order.
    /// This member is required.
    public var order: Swift.Int?
    /// Launch action parameters.
    public var parameters: [Swift.String: DrsClientTypes.LaunchActionParameter]?
    /// Launch configuration template Id or Source Server Id
    /// This member is required.
    public var resourceId: Swift.String?

    public init(
        actionCode: Swift.String? = nil,
        actionId: Swift.String? = nil,
        actionVersion: Swift.String? = nil,
        active: Swift.Bool? = nil,
        category: DrsClientTypes.LaunchActionCategory? = nil,
        description: Swift.String? = nil,
        name: Swift.String? = nil,
        `optional`: Swift.Bool? = nil,
        order: Swift.Int? = nil,
        parameters: [Swift.String: DrsClientTypes.LaunchActionParameter]? = nil,
        resourceId: Swift.String? = nil
    )
    {
        self.actionCode = actionCode
        self.actionId = actionId
        self.actionVersion = actionVersion
        self.active = active
        self.category = category
        self.description = description
        self.name = name
        self.`optional` = `optional`
        self.order = order
        self.parameters = parameters
        self.resourceId = resourceId
    }
}

public struct PutLaunchActionOutput: Swift.Sendable {
    /// Launch action code.
    public var actionCode: Swift.String?
    /// Launch action Id.
    public var actionId: Swift.String?
    /// Launch action version.
    public var actionVersion: Swift.String?
    /// Whether the launch action is active.
    public var active: Swift.Bool?
    /// Launch action category.
    public var category: DrsClientTypes.LaunchActionCategory?
    /// Launch action description.
    public var description: Swift.String?
    /// Launch action name.
    public var name: Swift.String?
    /// Whether the launch will not be marked as failed if this action fails.
    public var `optional`: Swift.Bool?
    /// Launch action order.
    public var order: Swift.Int?
    /// Launch action parameters.
    public var parameters: [Swift.String: DrsClientTypes.LaunchActionParameter]?
    /// Launch configuration template Id or Source Server Id
    public var resourceId: Swift.String?
    /// Launch action type.
    public var type: DrsClientTypes.LaunchActionType?

    public init(
        actionCode: Swift.String? = nil,
        actionId: Swift.String? = nil,
        actionVersion: Swift.String? = nil,
        active: Swift.Bool? = nil,
        category: DrsClientTypes.LaunchActionCategory? = nil,
        description: Swift.String? = nil,
        name: Swift.String? = nil,
        `optional`: Swift.Bool? = nil,
        order: Swift.Int? = nil,
        parameters: [Swift.String: DrsClientTypes.LaunchActionParameter]? = nil,
        resourceId: Swift.String? = nil,
        type: DrsClientTypes.LaunchActionType? = nil
    )
    {
        self.actionCode = actionCode
        self.actionId = actionId
        self.actionVersion = actionVersion
        self.active = active
        self.category = category
        self.description = description
        self.name = name
        self.`optional` = `optional`
        self.order = order
        self.parameters = parameters
        self.resourceId = resourceId
        self.type = type
    }
}

public struct GetFailbackReplicationConfigurationInput: Swift.Sendable {
    /// The ID of the Recovery Instance whose failback replication configuration should be returned.
    /// This member is required.
    public var recoveryInstanceID: Swift.String?

    public init(
        recoveryInstanceID: Swift.String? = nil
    )
    {
        self.recoveryInstanceID = recoveryInstanceID
    }
}

public struct GetFailbackReplicationConfigurationOutput: Swift.Sendable {
    /// Configure bandwidth throttling for the outbound data transfer rate of the Recovery Instance in Mbps.
    public var bandwidthThrottling: Swift.Int
    /// The name of the Failback Replication Configuration.
    public var name: Swift.String?
    /// The ID of the Recovery Instance.
    /// This member is required.
    public var recoveryInstanceID: Swift.String?
    /// Whether to use Private IP for the failback replication of the Recovery Instance.
    public var usePrivateIP: Swift.Bool?

    public init(
        bandwidthThrottling: Swift.Int = 0,
        name: Swift.String? = nil,
        recoveryInstanceID: Swift.String? = nil,
        usePrivateIP: Swift.Bool? = nil
    )
    {
        self.bandwidthThrottling = bandwidthThrottling
        self.name = name
        self.recoveryInstanceID = recoveryInstanceID
        self.usePrivateIP = usePrivateIP
    }
}

public struct ReverseReplicationInput: Swift.Sendable {
    /// The ID of the Recovery Instance that we want to reverse the replication for.
    /// This member is required.
    public var recoveryInstanceID: Swift.String?

    public init(
        recoveryInstanceID: Swift.String? = nil
    )
    {
        self.recoveryInstanceID = recoveryInstanceID
    }
}

public struct ReverseReplicationOutput: Swift.Sendable {
    /// ARN of created SourceServer.
    public var reversedDirectionSourceServerArn: Swift.String?

    public init(
        reversedDirectionSourceServerArn: Swift.String? = nil
    )
    {
        self.reversedDirectionSourceServerArn = reversedDirectionSourceServerArn
    }
}

public struct StartFailbackLaunchInput: Swift.Sendable {
    /// The IDs of the Recovery Instance whose failback launch we want to request.
    /// This member is required.
    public var recoveryInstanceIDs: [Swift.String]?
    /// The tags to be associated with the failback launch Job.
    public var tags: [Swift.String: Swift.String]?

    public init(
        recoveryInstanceIDs: [Swift.String]? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.recoveryInstanceIDs = recoveryInstanceIDs
        self.tags = tags
    }
}

extension StartFailbackLaunchInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "StartFailbackLaunchInput(recoveryInstanceIDs: \(Swift.String(describing: recoveryInstanceIDs)), tags: \"CONTENT_REDACTED\")"}
}

public struct StartFailbackLaunchOutput: Swift.Sendable {
    /// The failback launch Job.
    public var job: DrsClientTypes.Job?

    public init(
        job: DrsClientTypes.Job? = nil
    )
    {
        self.job = job
    }
}

public struct StopFailbackInput: Swift.Sendable {
    /// The ID of the Recovery Instance we want to stop failback for.
    /// This member is required.
    public var recoveryInstanceID: Swift.String?

    public init(
        recoveryInstanceID: Swift.String? = nil
    )
    {
        self.recoveryInstanceID = recoveryInstanceID
    }
}

public struct TerminateRecoveryInstancesInput: Swift.Sendable {
    /// The IDs of the Recovery Instances that should be terminated.
    /// This member is required.
    public var recoveryInstanceIDs: [Swift.String]?

    public init(
        recoveryInstanceIDs: [Swift.String]? = nil
    )
    {
        self.recoveryInstanceIDs = recoveryInstanceIDs
    }
}

public struct TerminateRecoveryInstancesOutput: Swift.Sendable {
    /// The Job for terminating the Recovery Instances.
    public var job: DrsClientTypes.Job?

    public init(
        job: DrsClientTypes.Job? = nil
    )
    {
        self.job = job
    }
}

public struct UpdateFailbackReplicationConfigurationInput: Swift.Sendable {
    /// Configure bandwidth throttling for the outbound data transfer rate of the Recovery Instance in Mbps.
    public var bandwidthThrottling: Swift.Int
    /// The name of the Failback Replication Configuration.
    public var name: Swift.String?
    /// The ID of the Recovery Instance.
    /// This member is required.
    public var recoveryInstanceID: Swift.String?
    /// Whether to use Private IP for the failback replication of the Recovery Instance.
    public var usePrivateIP: Swift.Bool?

    public init(
        bandwidthThrottling: Swift.Int = 0,
        name: Swift.String? = nil,
        recoveryInstanceID: Swift.String? = nil,
        usePrivateIP: Swift.Bool? = nil
    )
    {
        self.bandwidthThrottling = bandwidthThrottling
        self.name = name
        self.recoveryInstanceID = recoveryInstanceID
        self.usePrivateIP = usePrivateIP
    }
}

public struct UpdateReplicationConfigurationTemplateInput: Swift.Sendable {
    /// The Replication Configuration Template ARN.
    public var arn: Swift.String?
    /// Whether to associate the default Elastic Disaster Recovery Security group with the Replication Configuration Template.
    public var associateDefaultSecurityGroup: Swift.Bool?
    /// Whether to allow the AWS replication agent to automatically replicate newly added disks.
    public var autoReplicateNewDisks: Swift.Bool?
    /// Configure bandwidth throttling for the outbound data transfer rate of the Source Server in Mbps.
    public var bandwidthThrottling: Swift.Int
    /// Whether to create a Public IP for the Recovery Instance by default.
    public var createPublicIP: Swift.Bool?
    /// The data plane routing mechanism that will be used for replication.
    public var dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting?
    /// The Staging Disk EBS volume type to be used during replication.
    public var defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType?
    /// The type of EBS encryption to be used during replication.
    public var ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption?
    /// The ARN of the EBS encryption key to be used during replication.
    public var ebsEncryptionKeyArn: Swift.String?
    /// The Point in time (PIT) policy to manage snapshots taken during replication.
    public var pitPolicy: [DrsClientTypes.PITPolicyRule]?
    /// The Replication Configuration Template ID.
    /// This member is required.
    public var replicationConfigurationTemplateID: Swift.String?
    /// The instance type to be used for the replication server.
    public var replicationServerInstanceType: Swift.String?
    /// The security group IDs that will be used by the replication server.
    public var replicationServersSecurityGroupsIDs: [Swift.String]?
    /// The subnet to be used by the replication staging area.
    public var stagingAreaSubnetId: Swift.String?
    /// A set of tags to be associated with all resources created in the replication staging area: EC2 replication server, EBS volumes, EBS snapshots, etc.
    public var stagingAreaTags: [Swift.String: Swift.String]?
    /// Whether to use a dedicated Replication Server in the replication staging area.
    public var useDedicatedReplicationServer: Swift.Bool?

    public init(
        arn: Swift.String? = nil,
        associateDefaultSecurityGroup: Swift.Bool? = nil,
        autoReplicateNewDisks: Swift.Bool? = nil,
        bandwidthThrottling: Swift.Int = 0,
        createPublicIP: Swift.Bool? = nil,
        dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting? = nil,
        defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType? = nil,
        ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption? = nil,
        ebsEncryptionKeyArn: Swift.String? = nil,
        pitPolicy: [DrsClientTypes.PITPolicyRule]? = nil,
        replicationConfigurationTemplateID: Swift.String? = nil,
        replicationServerInstanceType: Swift.String? = nil,
        replicationServersSecurityGroupsIDs: [Swift.String]? = nil,
        stagingAreaSubnetId: Swift.String? = nil,
        stagingAreaTags: [Swift.String: Swift.String]? = nil,
        useDedicatedReplicationServer: Swift.Bool? = nil
    )
    {
        self.arn = arn
        self.associateDefaultSecurityGroup = associateDefaultSecurityGroup
        self.autoReplicateNewDisks = autoReplicateNewDisks
        self.bandwidthThrottling = bandwidthThrottling
        self.createPublicIP = createPublicIP
        self.dataPlaneRouting = dataPlaneRouting
        self.defaultLargeStagingDiskType = defaultLargeStagingDiskType
        self.ebsEncryption = ebsEncryption
        self.ebsEncryptionKeyArn = ebsEncryptionKeyArn
        self.pitPolicy = pitPolicy
        self.replicationConfigurationTemplateID = replicationConfigurationTemplateID
        self.replicationServerInstanceType = replicationServerInstanceType
        self.replicationServersSecurityGroupsIDs = replicationServersSecurityGroupsIDs
        self.stagingAreaSubnetId = stagingAreaSubnetId
        self.stagingAreaTags = stagingAreaTags
        self.useDedicatedReplicationServer = useDedicatedReplicationServer
    }
}

extension UpdateReplicationConfigurationTemplateInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "UpdateReplicationConfigurationTemplateInput(arn: \(Swift.String(describing: arn)), associateDefaultSecurityGroup: \(Swift.String(describing: associateDefaultSecurityGroup)), autoReplicateNewDisks: \(Swift.String(describing: autoReplicateNewDisks)), bandwidthThrottling: \(Swift.String(describing: bandwidthThrottling)), createPublicIP: \(Swift.String(describing: createPublicIP)), dataPlaneRouting: \(Swift.String(describing: dataPlaneRouting)), defaultLargeStagingDiskType: \(Swift.String(describing: defaultLargeStagingDiskType)), ebsEncryption: \(Swift.String(describing: ebsEncryption)), ebsEncryptionKeyArn: \(Swift.String(describing: ebsEncryptionKeyArn)), pitPolicy: \(Swift.String(describing: pitPolicy)), replicationConfigurationTemplateID: \(Swift.String(describing: replicationConfigurationTemplateID)), replicationServerInstanceType: \(Swift.String(describing: replicationServerInstanceType)), replicationServersSecurityGroupsIDs: \(Swift.String(describing: replicationServersSecurityGroupsIDs)), stagingAreaSubnetId: \(Swift.String(describing: stagingAreaSubnetId)), useDedicatedReplicationServer: \(Swift.String(describing: useDedicatedReplicationServer)), stagingAreaTags: \"CONTENT_REDACTED\")"}
}

public struct UpdateReplicationConfigurationTemplateOutput: Swift.Sendable {
    /// The Replication Configuration Template ARN.
    public var arn: Swift.String?
    /// Whether to associate the default Elastic Disaster Recovery Security group with the Replication Configuration Template.
    public var associateDefaultSecurityGroup: Swift.Bool?
    /// Whether to allow the AWS replication agent to automatically replicate newly added disks.
    public var autoReplicateNewDisks: Swift.Bool?
    /// Configure bandwidth throttling for the outbound data transfer rate of the Source Server in Mbps.
    public var bandwidthThrottling: Swift.Int
    /// Whether to create a Public IP for the Recovery Instance by default.
    public var createPublicIP: Swift.Bool?
    /// The data plane routing mechanism that will be used for replication.
    public var dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting?
    /// The Staging Disk EBS volume type to be used during replication.
    public var defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType?
    /// The type of EBS encryption to be used during replication.
    public var ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption?
    /// The ARN of the EBS encryption key to be used during replication.
    public var ebsEncryptionKeyArn: Swift.String?
    /// The Point in time (PIT) policy to manage snapshots taken during replication.
    public var pitPolicy: [DrsClientTypes.PITPolicyRule]?
    /// The Replication Configuration Template ID.
    /// This member is required.
    public var replicationConfigurationTemplateID: Swift.String?
    /// The instance type to be used for the replication server.
    public var replicationServerInstanceType: Swift.String?
    /// The security group IDs that will be used by the replication server.
    public var replicationServersSecurityGroupsIDs: [Swift.String]?
    /// The subnet to be used by the replication staging area.
    public var stagingAreaSubnetId: Swift.String?
    /// A set of tags to be associated with all resources created in the replication staging area: EC2 replication server, EBS volumes, EBS snapshots, etc.
    public var stagingAreaTags: [Swift.String: Swift.String]?
    /// A set of tags to be associated with the Replication Configuration Template resource.
    public var tags: [Swift.String: Swift.String]?
    /// Whether to use a dedicated Replication Server in the replication staging area.
    public var useDedicatedReplicationServer: Swift.Bool?

    public init(
        arn: Swift.String? = nil,
        associateDefaultSecurityGroup: Swift.Bool? = nil,
        autoReplicateNewDisks: Swift.Bool? = nil,
        bandwidthThrottling: Swift.Int = 0,
        createPublicIP: Swift.Bool? = nil,
        dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting? = nil,
        defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType? = nil,
        ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption? = nil,
        ebsEncryptionKeyArn: Swift.String? = nil,
        pitPolicy: [DrsClientTypes.PITPolicyRule]? = nil,
        replicationConfigurationTemplateID: Swift.String? = nil,
        replicationServerInstanceType: Swift.String? = nil,
        replicationServersSecurityGroupsIDs: [Swift.String]? = nil,
        stagingAreaSubnetId: Swift.String? = nil,
        stagingAreaTags: [Swift.String: Swift.String]? = nil,
        tags: [Swift.String: Swift.String]? = nil,
        useDedicatedReplicationServer: Swift.Bool? = nil
    )
    {
        self.arn = arn
        self.associateDefaultSecurityGroup = associateDefaultSecurityGroup
        self.autoReplicateNewDisks = autoReplicateNewDisks
        self.bandwidthThrottling = bandwidthThrottling
        self.createPublicIP = createPublicIP
        self.dataPlaneRouting = dataPlaneRouting
        self.defaultLargeStagingDiskType = defaultLargeStagingDiskType
        self.ebsEncryption = ebsEncryption
        self.ebsEncryptionKeyArn = ebsEncryptionKeyArn
        self.pitPolicy = pitPolicy
        self.replicationConfigurationTemplateID = replicationConfigurationTemplateID
        self.replicationServerInstanceType = replicationServerInstanceType
        self.replicationServersSecurityGroupsIDs = replicationServersSecurityGroupsIDs
        self.stagingAreaSubnetId = stagingAreaSubnetId
        self.stagingAreaTags = stagingAreaTags
        self.tags = tags
        self.useDedicatedReplicationServer = useDedicatedReplicationServer
    }
}

extension UpdateReplicationConfigurationTemplateOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "UpdateReplicationConfigurationTemplateOutput(arn: \(Swift.String(describing: arn)), associateDefaultSecurityGroup: \(Swift.String(describing: associateDefaultSecurityGroup)), autoReplicateNewDisks: \(Swift.String(describing: autoReplicateNewDisks)), bandwidthThrottling: \(Swift.String(describing: bandwidthThrottling)), createPublicIP: \(Swift.String(describing: createPublicIP)), dataPlaneRouting: \(Swift.String(describing: dataPlaneRouting)), defaultLargeStagingDiskType: \(Swift.String(describing: defaultLargeStagingDiskType)), ebsEncryption: \(Swift.String(describing: ebsEncryption)), ebsEncryptionKeyArn: \(Swift.String(describing: ebsEncryptionKeyArn)), pitPolicy: \(Swift.String(describing: pitPolicy)), replicationConfigurationTemplateID: \(Swift.String(describing: replicationConfigurationTemplateID)), replicationServerInstanceType: \(Swift.String(describing: replicationServerInstanceType)), replicationServersSecurityGroupsIDs: \(Swift.String(describing: replicationServersSecurityGroupsIDs)), stagingAreaSubnetId: \(Swift.String(describing: stagingAreaSubnetId)), useDedicatedReplicationServer: \(Swift.String(describing: useDedicatedReplicationServer)), stagingAreaTags: \"CONTENT_REDACTED\", tags: \"CONTENT_REDACTED\")"}
}

public struct ExportSourceNetworkCfnTemplateInput: Swift.Sendable {
    /// The Source Network ID to export its CloudFormation template to an S3 bucket.
    /// This member is required.
    public var sourceNetworkID: Swift.String?

    public init(
        sourceNetworkID: Swift.String? = nil
    )
    {
        self.sourceNetworkID = sourceNetworkID
    }
}

public struct ExportSourceNetworkCfnTemplateOutput: Swift.Sendable {
    /// S3 bucket URL where the Source Network CloudFormation template was exported to.
    public var s3DestinationUrl: Swift.String?

    public init(
        s3DestinationUrl: Swift.String? = nil
    )
    {
        self.s3DestinationUrl = s3DestinationUrl
    }
}

extension DrsClientTypes {

    /// An object representing the Source Network to recover.
    public struct StartSourceNetworkRecoveryRequestNetworkEntry: Swift.Sendable {
        /// CloudFormation stack name to be used for recovering the network.
        public var cfnStackName: Swift.String?
        /// The ID of the Source Network you want to recover.
        /// This member is required.
        public var sourceNetworkID: Swift.String?

        public init(
            cfnStackName: Swift.String? = nil,
            sourceNetworkID: Swift.String? = nil
        )
        {
            self.cfnStackName = cfnStackName
            self.sourceNetworkID = sourceNetworkID
        }
    }
}

extension DrsClientTypes.StartSourceNetworkRecoveryRequestNetworkEntry: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "StartSourceNetworkRecoveryRequestNetworkEntry(sourceNetworkID: \(Swift.String(describing: sourceNetworkID)), cfnStackName: \"CONTENT_REDACTED\")"}
}

public struct StartSourceNetworkRecoveryInput: Swift.Sendable {
    /// Don't update existing CloudFormation Stack, recover the network using a new stack.
    public var deployAsNew: Swift.Bool?
    /// The Source Networks that we want to start a Recovery Job for.
    /// This member is required.
    public var sourceNetworks: [DrsClientTypes.StartSourceNetworkRecoveryRequestNetworkEntry]?
    /// The tags to be associated with the Source Network recovery Job.
    public var tags: [Swift.String: Swift.String]?

    public init(
        deployAsNew: Swift.Bool? = nil,
        sourceNetworks: [DrsClientTypes.StartSourceNetworkRecoveryRequestNetworkEntry]? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.deployAsNew = deployAsNew
        self.sourceNetworks = sourceNetworks
        self.tags = tags
    }
}

extension StartSourceNetworkRecoveryInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "StartSourceNetworkRecoveryInput(deployAsNew: \(Swift.String(describing: deployAsNew)), sourceNetworks: \(Swift.String(describing: sourceNetworks)), tags: \"CONTENT_REDACTED\")"}
}

public struct StartSourceNetworkRecoveryOutput: Swift.Sendable {
    /// The Source Network recovery Job.
    public var job: DrsClientTypes.Job?

    public init(
        job: DrsClientTypes.Job? = nil
    )
    {
        self.job = job
    }
}

public struct StartSourceNetworkReplicationInput: Swift.Sendable {
    /// ID of the Source Network to replicate.
    /// This member is required.
    public var sourceNetworkID: Swift.String?

    public init(
        sourceNetworkID: Swift.String? = nil
    )
    {
        self.sourceNetworkID = sourceNetworkID
    }
}

public struct StartSourceNetworkReplicationOutput: Swift.Sendable {
    /// Source Network which was requested for replication.
    public var sourceNetwork: DrsClientTypes.SourceNetwork?

    public init(
        sourceNetwork: DrsClientTypes.SourceNetwork? = nil
    )
    {
        self.sourceNetwork = sourceNetwork
    }
}

public struct StopSourceNetworkReplicationInput: Swift.Sendable {
    /// ID of the Source Network to stop replication.
    /// This member is required.
    public var sourceNetworkID: Swift.String?

    public init(
        sourceNetworkID: Swift.String? = nil
    )
    {
        self.sourceNetworkID = sourceNetworkID
    }
}

public struct StopSourceNetworkReplicationOutput: Swift.Sendable {
    /// Source Network which was requested to stop replication.
    public var sourceNetwork: DrsClientTypes.SourceNetwork?

    public init(
        sourceNetwork: DrsClientTypes.SourceNetwork? = nil
    )
    {
        self.sourceNetwork = sourceNetwork
    }
}

public struct GetLaunchConfigurationInput: Swift.Sendable {
    /// The ID of the Source Server that we want to retrieve a Launch Configuration for.
    /// This member is required.
    public var sourceServerID: Swift.String?

    public init(
        sourceServerID: Swift.String? = nil
    )
    {
        self.sourceServerID = sourceServerID
    }
}

extension DrsClientTypes {

    /// Launch into existing instance.
    public struct LaunchIntoInstanceProperties: Swift.Sendable {
        /// Optionally holds EC2 instance ID of an instance to launch into, instead of launching a new instance during drill, recovery or failback.
        public var launchIntoEC2InstanceID: Swift.String?

        public init(
            launchIntoEC2InstanceID: Swift.String? = nil
        )
        {
            self.launchIntoEC2InstanceID = launchIntoEC2InstanceID
        }
    }
}

public struct GetLaunchConfigurationOutput: Swift.Sendable {
    /// Whether we should copy the Private IP of the Source Server to the Recovery Instance.
    public var copyPrivateIp: Swift.Bool?
    /// Whether we want to copy the tags of the Source Server to the EC2 machine of the Recovery Instance.
    public var copyTags: Swift.Bool?
    /// The EC2 launch template ID of this launch configuration.
    public var ec2LaunchTemplateID: Swift.String?
    /// The state of the Recovery Instance in EC2 after the recovery operation.
    public var launchDisposition: DrsClientTypes.LaunchDisposition?
    /// Launch into existing instance properties.
    public var launchIntoInstanceProperties: DrsClientTypes.LaunchIntoInstanceProperties?
    /// The licensing configuration to be used for this launch configuration.
    public var licensing: DrsClientTypes.Licensing?
    /// The name of the launch configuration.
    public var name: Swift.String?
    /// Whether we want to activate post-launch actions for the Source Server.
    public var postLaunchEnabled: Swift.Bool?
    /// The ID of the Source Server for this launch configuration.
    public var sourceServerID: Swift.String?
    /// Whether Elastic Disaster Recovery should try to automatically choose the instance type that best matches the OS, CPU, and RAM of your Source Server.
    public var targetInstanceTypeRightSizingMethod: DrsClientTypes.TargetInstanceTypeRightSizingMethod?

    public init(
        copyPrivateIp: Swift.Bool? = nil,
        copyTags: Swift.Bool? = nil,
        ec2LaunchTemplateID: Swift.String? = nil,
        launchDisposition: DrsClientTypes.LaunchDisposition? = nil,
        launchIntoInstanceProperties: DrsClientTypes.LaunchIntoInstanceProperties? = nil,
        licensing: DrsClientTypes.Licensing? = nil,
        name: Swift.String? = nil,
        postLaunchEnabled: Swift.Bool? = nil,
        sourceServerID: Swift.String? = nil,
        targetInstanceTypeRightSizingMethod: DrsClientTypes.TargetInstanceTypeRightSizingMethod? = nil
    )
    {
        self.copyPrivateIp = copyPrivateIp
        self.copyTags = copyTags
        self.ec2LaunchTemplateID = ec2LaunchTemplateID
        self.launchDisposition = launchDisposition
        self.launchIntoInstanceProperties = launchIntoInstanceProperties
        self.licensing = licensing
        self.name = name
        self.postLaunchEnabled = postLaunchEnabled
        self.sourceServerID = sourceServerID
        self.targetInstanceTypeRightSizingMethod = targetInstanceTypeRightSizingMethod
    }
}

public struct GetReplicationConfigurationInput: Swift.Sendable {
    /// The ID of the Source Serve for this Replication Configuration.r
    /// This member is required.
    public var sourceServerID: Swift.String?

    public init(
        sourceServerID: Swift.String? = nil
    )
    {
        self.sourceServerID = sourceServerID
    }
}

extension DrsClientTypes {

    public enum ReplicationConfigurationReplicatedDiskStagingDiskType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case auto
        case gp2
        case gp3
        case io1
        case sc1
        case st1
        case standard
        case sdkUnknown(Swift.String)

        public static var allCases: [ReplicationConfigurationReplicatedDiskStagingDiskType] {
            return [
                .auto,
                .gp2,
                .gp3,
                .io1,
                .sc1,
                .st1,
                .standard
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .auto: return "AUTO"
            case .gp2: return "GP2"
            case .gp3: return "GP3"
            case .io1: return "IO1"
            case .sc1: return "SC1"
            case .st1: return "ST1"
            case .standard: return "STANDARD"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension DrsClientTypes {

    /// The configuration of a disk of the Source Server to be replicated.
    public struct ReplicationConfigurationReplicatedDisk: Swift.Sendable {
        /// The name of the device.
        public var deviceName: Swift.String?
        /// The requested number of I/O operations per second (IOPS).
        public var iops: Swift.Int
        /// Whether to boot from this disk or not.
        public var isBootDisk: Swift.Bool?
        /// The Staging Disk EBS volume type to be used during replication when stagingDiskType is set to Auto. This is a read-only field.
        public var optimizedStagingDiskType: DrsClientTypes.ReplicationConfigurationReplicatedDiskStagingDiskType?
        /// The Staging Disk EBS volume type to be used during replication.
        public var stagingDiskType: DrsClientTypes.ReplicationConfigurationReplicatedDiskStagingDiskType?
        /// The throughput to use for the EBS volume in MiB/s. This parameter is valid only for gp3 volumes.
        public var throughput: Swift.Int

        public init(
            deviceName: Swift.String? = nil,
            iops: Swift.Int = 0,
            isBootDisk: Swift.Bool? = nil,
            optimizedStagingDiskType: DrsClientTypes.ReplicationConfigurationReplicatedDiskStagingDiskType? = nil,
            stagingDiskType: DrsClientTypes.ReplicationConfigurationReplicatedDiskStagingDiskType? = nil,
            throughput: Swift.Int = 0
        )
        {
            self.deviceName = deviceName
            self.iops = iops
            self.isBootDisk = isBootDisk
            self.optimizedStagingDiskType = optimizedStagingDiskType
            self.stagingDiskType = stagingDiskType
            self.throughput = throughput
        }
    }
}

public struct GetReplicationConfigurationOutput: Swift.Sendable {
    /// Whether to associate the default Elastic Disaster Recovery Security group with the Replication Configuration.
    public var associateDefaultSecurityGroup: Swift.Bool?
    /// Whether to allow the AWS replication agent to automatically replicate newly added disks.
    public var autoReplicateNewDisks: Swift.Bool?
    /// Configure bandwidth throttling for the outbound data transfer rate of the Source Server in Mbps.
    public var bandwidthThrottling: Swift.Int
    /// Whether to create a Public IP for the Recovery Instance by default.
    public var createPublicIP: Swift.Bool?
    /// The data plane routing mechanism that will be used for replication.
    public var dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting?
    /// The Staging Disk EBS volume type to be used during replication.
    public var defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType?
    /// The type of EBS encryption to be used during replication.
    public var ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption?
    /// The ARN of the EBS encryption key to be used during replication.
    public var ebsEncryptionKeyArn: Swift.String?
    /// The name of the Replication Configuration.
    public var name: Swift.String?
    /// The Point in time (PIT) policy to manage snapshots taken during replication.
    public var pitPolicy: [DrsClientTypes.PITPolicyRule]?
    /// The configuration of the disks of the Source Server to be replicated.
    public var replicatedDisks: [DrsClientTypes.ReplicationConfigurationReplicatedDisk]?
    /// The instance type to be used for the replication server.
    public var replicationServerInstanceType: Swift.String?
    /// The security group IDs that will be used by the replication server.
    public var replicationServersSecurityGroupsIDs: [Swift.String]?
    /// The ID of the Source Server for this Replication Configuration.
    public var sourceServerID: Swift.String?
    /// The subnet to be used by the replication staging area.
    public var stagingAreaSubnetId: Swift.String?
    /// A set of tags to be associated with all resources created in the replication staging area: EC2 replication server, EBS volumes, EBS snapshots, etc.
    public var stagingAreaTags: [Swift.String: Swift.String]?
    /// Whether to use a dedicated Replication Server in the replication staging area.
    public var useDedicatedReplicationServer: Swift.Bool?

    public init(
        associateDefaultSecurityGroup: Swift.Bool? = nil,
        autoReplicateNewDisks: Swift.Bool? = nil,
        bandwidthThrottling: Swift.Int = 0,
        createPublicIP: Swift.Bool? = nil,
        dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting? = nil,
        defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType? = nil,
        ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption? = nil,
        ebsEncryptionKeyArn: Swift.String? = nil,
        name: Swift.String? = nil,
        pitPolicy: [DrsClientTypes.PITPolicyRule]? = nil,
        replicatedDisks: [DrsClientTypes.ReplicationConfigurationReplicatedDisk]? = nil,
        replicationServerInstanceType: Swift.String? = nil,
        replicationServersSecurityGroupsIDs: [Swift.String]? = nil,
        sourceServerID: Swift.String? = nil,
        stagingAreaSubnetId: Swift.String? = nil,
        stagingAreaTags: [Swift.String: Swift.String]? = nil,
        useDedicatedReplicationServer: Swift.Bool? = nil
    )
    {
        self.associateDefaultSecurityGroup = associateDefaultSecurityGroup
        self.autoReplicateNewDisks = autoReplicateNewDisks
        self.bandwidthThrottling = bandwidthThrottling
        self.createPublicIP = createPublicIP
        self.dataPlaneRouting = dataPlaneRouting
        self.defaultLargeStagingDiskType = defaultLargeStagingDiskType
        self.ebsEncryption = ebsEncryption
        self.ebsEncryptionKeyArn = ebsEncryptionKeyArn
        self.name = name
        self.pitPolicy = pitPolicy
        self.replicatedDisks = replicatedDisks
        self.replicationServerInstanceType = replicationServerInstanceType
        self.replicationServersSecurityGroupsIDs = replicationServersSecurityGroupsIDs
        self.sourceServerID = sourceServerID
        self.stagingAreaSubnetId = stagingAreaSubnetId
        self.stagingAreaTags = stagingAreaTags
        self.useDedicatedReplicationServer = useDedicatedReplicationServer
    }
}

extension GetReplicationConfigurationOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "GetReplicationConfigurationOutput(associateDefaultSecurityGroup: \(Swift.String(describing: associateDefaultSecurityGroup)), autoReplicateNewDisks: \(Swift.String(describing: autoReplicateNewDisks)), bandwidthThrottling: \(Swift.String(describing: bandwidthThrottling)), createPublicIP: \(Swift.String(describing: createPublicIP)), dataPlaneRouting: \(Swift.String(describing: dataPlaneRouting)), defaultLargeStagingDiskType: \(Swift.String(describing: defaultLargeStagingDiskType)), ebsEncryption: \(Swift.String(describing: ebsEncryption)), ebsEncryptionKeyArn: \(Swift.String(describing: ebsEncryptionKeyArn)), name: \(Swift.String(describing: name)), pitPolicy: \(Swift.String(describing: pitPolicy)), replicatedDisks: \(Swift.String(describing: replicatedDisks)), replicationServerInstanceType: \(Swift.String(describing: replicationServerInstanceType)), replicationServersSecurityGroupsIDs: \(Swift.String(describing: replicationServersSecurityGroupsIDs)), sourceServerID: \(Swift.String(describing: sourceServerID)), stagingAreaSubnetId: \(Swift.String(describing: stagingAreaSubnetId)), useDedicatedReplicationServer: \(Swift.String(describing: useDedicatedReplicationServer)), stagingAreaTags: \"CONTENT_REDACTED\")"}
}

@available(*, deprecated, message: "WARNING: RetryDataReplication is deprecated")
public struct RetryDataReplicationInput: Swift.Sendable {
    /// The ID of the Source Server whose data replication should be retried.
    /// This member is required.
    public var sourceServerID: Swift.String?

    public init(
        sourceServerID: Swift.String? = nil
    )
    {
        self.sourceServerID = sourceServerID
    }
}

public struct RetryDataReplicationOutput: Swift.Sendable {
    /// The version of the DRS agent installed on the source server
    public var agentVersion: Swift.String?
    /// The ARN of the Source Server.
    public var arn: Swift.String?
    /// The Data Replication Info of the Source Server.
    public var dataReplicationInfo: DrsClientTypes.DataReplicationInfo?
    /// The status of the last recovery launch of this Source Server.
    public var lastLaunchResult: DrsClientTypes.LastLaunchResult?
    /// The lifecycle information of this Source Server.
    public var lifeCycle: DrsClientTypes.LifeCycle?
    /// The ID of the Recovery Instance associated with this Source Server.
    public var recoveryInstanceId: Swift.String?
    /// Replication direction of the Source Server.
    public var replicationDirection: DrsClientTypes.ReplicationDirection?
    /// For EC2-originated Source Servers which have been failed over and then failed back, this value will mean the ARN of the Source Server on the opposite replication direction.
    public var reversedDirectionSourceServerArn: Swift.String?
    /// Source cloud properties of the Source Server.
    public var sourceCloudProperties: DrsClientTypes.SourceCloudProperties?
    /// ID of the Source Network which is protecting this Source Server's network.
    public var sourceNetworkID: Swift.String?
    /// The source properties of the Source Server.
    public var sourceProperties: DrsClientTypes.SourceProperties?
    /// The ID of the Source Server.
    public var sourceServerID: Swift.String?
    /// The staging area of the source server.
    public var stagingArea: DrsClientTypes.StagingArea?
    /// The tags associated with the Source Server.
    public var tags: [Swift.String: Swift.String]?

    public init(
        agentVersion: Swift.String? = nil,
        arn: Swift.String? = nil,
        dataReplicationInfo: DrsClientTypes.DataReplicationInfo? = nil,
        lastLaunchResult: DrsClientTypes.LastLaunchResult? = nil,
        lifeCycle: DrsClientTypes.LifeCycle? = nil,
        recoveryInstanceId: Swift.String? = nil,
        replicationDirection: DrsClientTypes.ReplicationDirection? = nil,
        reversedDirectionSourceServerArn: Swift.String? = nil,
        sourceCloudProperties: DrsClientTypes.SourceCloudProperties? = nil,
        sourceNetworkID: Swift.String? = nil,
        sourceProperties: DrsClientTypes.SourceProperties? = nil,
        sourceServerID: Swift.String? = nil,
        stagingArea: DrsClientTypes.StagingArea? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.agentVersion = agentVersion
        self.arn = arn
        self.dataReplicationInfo = dataReplicationInfo
        self.lastLaunchResult = lastLaunchResult
        self.lifeCycle = lifeCycle
        self.recoveryInstanceId = recoveryInstanceId
        self.replicationDirection = replicationDirection
        self.reversedDirectionSourceServerArn = reversedDirectionSourceServerArn
        self.sourceCloudProperties = sourceCloudProperties
        self.sourceNetworkID = sourceNetworkID
        self.sourceProperties = sourceProperties
        self.sourceServerID = sourceServerID
        self.stagingArea = stagingArea
        self.tags = tags
    }
}

extension RetryDataReplicationOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "RetryDataReplicationOutput(agentVersion: \(Swift.String(describing: agentVersion)), arn: \(Swift.String(describing: arn)), dataReplicationInfo: \(Swift.String(describing: dataReplicationInfo)), lastLaunchResult: \(Swift.String(describing: lastLaunchResult)), lifeCycle: \(Swift.String(describing: lifeCycle)), recoveryInstanceId: \(Swift.String(describing: recoveryInstanceId)), replicationDirection: \(Swift.String(describing: replicationDirection)), reversedDirectionSourceServerArn: \(Swift.String(describing: reversedDirectionSourceServerArn)), sourceCloudProperties: \(Swift.String(describing: sourceCloudProperties)), sourceNetworkID: \(Swift.String(describing: sourceNetworkID)), sourceProperties: \(Swift.String(describing: sourceProperties)), sourceServerID: \(Swift.String(describing: sourceServerID)), stagingArea: \(Swift.String(describing: stagingArea)), tags: \"CONTENT_REDACTED\")"}
}

extension DrsClientTypes {

    /// An object representing the Source Server to recover.
    public struct StartRecoveryRequestSourceServer: Swift.Sendable {
        /// The ID of a Recovery Snapshot we want to recover from. Omit this field to launch from the latest data by taking an on-demand snapshot.
        public var recoverySnapshotID: Swift.String?
        /// The ID of the Source Server you want to recover.
        /// This member is required.
        public var sourceServerID: Swift.String?

        public init(
            recoverySnapshotID: Swift.String? = nil,
            sourceServerID: Swift.String? = nil
        )
        {
            self.recoverySnapshotID = recoverySnapshotID
            self.sourceServerID = sourceServerID
        }
    }
}

public struct StartRecoveryInput: Swift.Sendable {
    /// Whether this Source Server Recovery operation is a drill or not.
    public var isDrill: Swift.Bool?
    /// The Source Servers that we want to start a Recovery Job for.
    /// This member is required.
    public var sourceServers: [DrsClientTypes.StartRecoveryRequestSourceServer]?
    /// The tags to be associated with the Recovery Job.
    public var tags: [Swift.String: Swift.String]?

    public init(
        isDrill: Swift.Bool? = nil,
        sourceServers: [DrsClientTypes.StartRecoveryRequestSourceServer]? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.isDrill = isDrill
        self.sourceServers = sourceServers
        self.tags = tags
    }
}

extension StartRecoveryInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "StartRecoveryInput(isDrill: \(Swift.String(describing: isDrill)), sourceServers: \(Swift.String(describing: sourceServers)), tags: \"CONTENT_REDACTED\")"}
}

public struct StartRecoveryOutput: Swift.Sendable {
    /// The Recovery Job.
    public var job: DrsClientTypes.Job?

    public init(
        job: DrsClientTypes.Job? = nil
    )
    {
        self.job = job
    }
}

public struct StartReplicationInput: Swift.Sendable {
    /// The ID of the Source Server to start replication for.
    /// This member is required.
    public var sourceServerID: Swift.String?

    public init(
        sourceServerID: Swift.String? = nil
    )
    {
        self.sourceServerID = sourceServerID
    }
}

public struct StartReplicationOutput: Swift.Sendable {
    /// The Source Server that this action was targeted on.
    public var sourceServer: DrsClientTypes.SourceServer?

    public init(
        sourceServer: DrsClientTypes.SourceServer? = nil
    )
    {
        self.sourceServer = sourceServer
    }
}

public struct StopReplicationInput: Swift.Sendable {
    /// The ID of the Source Server to stop replication for.
    /// This member is required.
    public var sourceServerID: Swift.String?

    public init(
        sourceServerID: Swift.String? = nil
    )
    {
        self.sourceServerID = sourceServerID
    }
}

public struct StopReplicationOutput: Swift.Sendable {
    /// The Source Server that this action was targeted on.
    public var sourceServer: DrsClientTypes.SourceServer?

    public init(
        sourceServer: DrsClientTypes.SourceServer? = nil
    )
    {
        self.sourceServer = sourceServer
    }
}

public struct UpdateLaunchConfigurationInput: Swift.Sendable {
    /// Whether we should copy the Private IP of the Source Server to the Recovery Instance.
    public var copyPrivateIp: Swift.Bool?
    /// Whether we want to copy the tags of the Source Server to the EC2 machine of the Recovery Instance.
    public var copyTags: Swift.Bool?
    /// The state of the Recovery Instance in EC2 after the recovery operation.
    public var launchDisposition: DrsClientTypes.LaunchDisposition?
    /// Launch into existing instance properties.
    public var launchIntoInstanceProperties: DrsClientTypes.LaunchIntoInstanceProperties?
    /// The licensing configuration to be used for this launch configuration.
    public var licensing: DrsClientTypes.Licensing?
    /// The name of the launch configuration.
    public var name: Swift.String?
    /// Whether we want to enable post-launch actions for the Source Server.
    public var postLaunchEnabled: Swift.Bool?
    /// The ID of the Source Server that we want to retrieve a Launch Configuration for.
    /// This member is required.
    public var sourceServerID: Swift.String?
    /// Whether Elastic Disaster Recovery should try to automatically choose the instance type that best matches the OS, CPU, and RAM of your Source Server.
    public var targetInstanceTypeRightSizingMethod: DrsClientTypes.TargetInstanceTypeRightSizingMethod?

    public init(
        copyPrivateIp: Swift.Bool? = nil,
        copyTags: Swift.Bool? = nil,
        launchDisposition: DrsClientTypes.LaunchDisposition? = nil,
        launchIntoInstanceProperties: DrsClientTypes.LaunchIntoInstanceProperties? = nil,
        licensing: DrsClientTypes.Licensing? = nil,
        name: Swift.String? = nil,
        postLaunchEnabled: Swift.Bool? = nil,
        sourceServerID: Swift.String? = nil,
        targetInstanceTypeRightSizingMethod: DrsClientTypes.TargetInstanceTypeRightSizingMethod? = nil
    )
    {
        self.copyPrivateIp = copyPrivateIp
        self.copyTags = copyTags
        self.launchDisposition = launchDisposition
        self.launchIntoInstanceProperties = launchIntoInstanceProperties
        self.licensing = licensing
        self.name = name
        self.postLaunchEnabled = postLaunchEnabled
        self.sourceServerID = sourceServerID
        self.targetInstanceTypeRightSizingMethod = targetInstanceTypeRightSizingMethod
    }
}

public struct UpdateLaunchConfigurationOutput: Swift.Sendable {
    /// Whether we should copy the Private IP of the Source Server to the Recovery Instance.
    public var copyPrivateIp: Swift.Bool?
    /// Whether we want to copy the tags of the Source Server to the EC2 machine of the Recovery Instance.
    public var copyTags: Swift.Bool?
    /// The EC2 launch template ID of this launch configuration.
    public var ec2LaunchTemplateID: Swift.String?
    /// The state of the Recovery Instance in EC2 after the recovery operation.
    public var launchDisposition: DrsClientTypes.LaunchDisposition?
    /// Launch into existing instance properties.
    public var launchIntoInstanceProperties: DrsClientTypes.LaunchIntoInstanceProperties?
    /// The licensing configuration to be used for this launch configuration.
    public var licensing: DrsClientTypes.Licensing?
    /// The name of the launch configuration.
    public var name: Swift.String?
    /// Whether we want to activate post-launch actions for the Source Server.
    public var postLaunchEnabled: Swift.Bool?
    /// The ID of the Source Server for this launch configuration.
    public var sourceServerID: Swift.String?
    /// Whether Elastic Disaster Recovery should try to automatically choose the instance type that best matches the OS, CPU, and RAM of your Source Server.
    public var targetInstanceTypeRightSizingMethod: DrsClientTypes.TargetInstanceTypeRightSizingMethod?

    public init(
        copyPrivateIp: Swift.Bool? = nil,
        copyTags: Swift.Bool? = nil,
        ec2LaunchTemplateID: Swift.String? = nil,
        launchDisposition: DrsClientTypes.LaunchDisposition? = nil,
        launchIntoInstanceProperties: DrsClientTypes.LaunchIntoInstanceProperties? = nil,
        licensing: DrsClientTypes.Licensing? = nil,
        name: Swift.String? = nil,
        postLaunchEnabled: Swift.Bool? = nil,
        sourceServerID: Swift.String? = nil,
        targetInstanceTypeRightSizingMethod: DrsClientTypes.TargetInstanceTypeRightSizingMethod? = nil
    )
    {
        self.copyPrivateIp = copyPrivateIp
        self.copyTags = copyTags
        self.ec2LaunchTemplateID = ec2LaunchTemplateID
        self.launchDisposition = launchDisposition
        self.launchIntoInstanceProperties = launchIntoInstanceProperties
        self.licensing = licensing
        self.name = name
        self.postLaunchEnabled = postLaunchEnabled
        self.sourceServerID = sourceServerID
        self.targetInstanceTypeRightSizingMethod = targetInstanceTypeRightSizingMethod
    }
}

public struct UpdateReplicationConfigurationInput: Swift.Sendable {
    /// Whether to associate the default Elastic Disaster Recovery Security group with the Replication Configuration.
    public var associateDefaultSecurityGroup: Swift.Bool?
    /// Whether to allow the AWS replication agent to automatically replicate newly added disks.
    public var autoReplicateNewDisks: Swift.Bool?
    /// Configure bandwidth throttling for the outbound data transfer rate of the Source Server in Mbps.
    public var bandwidthThrottling: Swift.Int
    /// Whether to create a Public IP for the Recovery Instance by default.
    public var createPublicIP: Swift.Bool?
    /// The data plane routing mechanism that will be used for replication.
    public var dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting?
    /// The Staging Disk EBS volume type to be used during replication.
    public var defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType?
    /// The type of EBS encryption to be used during replication.
    public var ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption?
    /// The ARN of the EBS encryption key to be used during replication.
    public var ebsEncryptionKeyArn: Swift.String?
    /// The name of the Replication Configuration.
    public var name: Swift.String?
    /// The Point in time (PIT) policy to manage snapshots taken during replication.
    public var pitPolicy: [DrsClientTypes.PITPolicyRule]?
    /// The configuration of the disks of the Source Server to be replicated.
    public var replicatedDisks: [DrsClientTypes.ReplicationConfigurationReplicatedDisk]?
    /// The instance type to be used for the replication server.
    public var replicationServerInstanceType: Swift.String?
    /// The security group IDs that will be used by the replication server.
    public var replicationServersSecurityGroupsIDs: [Swift.String]?
    /// The ID of the Source Server for this Replication Configuration.
    /// This member is required.
    public var sourceServerID: Swift.String?
    /// The subnet to be used by the replication staging area.
    public var stagingAreaSubnetId: Swift.String?
    /// A set of tags to be associated with all resources created in the replication staging area: EC2 replication server, EBS volumes, EBS snapshots, etc.
    public var stagingAreaTags: [Swift.String: Swift.String]?
    /// Whether to use a dedicated Replication Server in the replication staging area.
    public var useDedicatedReplicationServer: Swift.Bool?

    public init(
        associateDefaultSecurityGroup: Swift.Bool? = nil,
        autoReplicateNewDisks: Swift.Bool? = nil,
        bandwidthThrottling: Swift.Int = 0,
        createPublicIP: Swift.Bool? = nil,
        dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting? = nil,
        defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType? = nil,
        ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption? = nil,
        ebsEncryptionKeyArn: Swift.String? = nil,
        name: Swift.String? = nil,
        pitPolicy: [DrsClientTypes.PITPolicyRule]? = nil,
        replicatedDisks: [DrsClientTypes.ReplicationConfigurationReplicatedDisk]? = nil,
        replicationServerInstanceType: Swift.String? = nil,
        replicationServersSecurityGroupsIDs: [Swift.String]? = nil,
        sourceServerID: Swift.String? = nil,
        stagingAreaSubnetId: Swift.String? = nil,
        stagingAreaTags: [Swift.String: Swift.String]? = nil,
        useDedicatedReplicationServer: Swift.Bool? = nil
    )
    {
        self.associateDefaultSecurityGroup = associateDefaultSecurityGroup
        self.autoReplicateNewDisks = autoReplicateNewDisks
        self.bandwidthThrottling = bandwidthThrottling
        self.createPublicIP = createPublicIP
        self.dataPlaneRouting = dataPlaneRouting
        self.defaultLargeStagingDiskType = defaultLargeStagingDiskType
        self.ebsEncryption = ebsEncryption
        self.ebsEncryptionKeyArn = ebsEncryptionKeyArn
        self.name = name
        self.pitPolicy = pitPolicy
        self.replicatedDisks = replicatedDisks
        self.replicationServerInstanceType = replicationServerInstanceType
        self.replicationServersSecurityGroupsIDs = replicationServersSecurityGroupsIDs
        self.sourceServerID = sourceServerID
        self.stagingAreaSubnetId = stagingAreaSubnetId
        self.stagingAreaTags = stagingAreaTags
        self.useDedicatedReplicationServer = useDedicatedReplicationServer
    }
}

extension UpdateReplicationConfigurationInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "UpdateReplicationConfigurationInput(associateDefaultSecurityGroup: \(Swift.String(describing: associateDefaultSecurityGroup)), autoReplicateNewDisks: \(Swift.String(describing: autoReplicateNewDisks)), bandwidthThrottling: \(Swift.String(describing: bandwidthThrottling)), createPublicIP: \(Swift.String(describing: createPublicIP)), dataPlaneRouting: \(Swift.String(describing: dataPlaneRouting)), defaultLargeStagingDiskType: \(Swift.String(describing: defaultLargeStagingDiskType)), ebsEncryption: \(Swift.String(describing: ebsEncryption)), ebsEncryptionKeyArn: \(Swift.String(describing: ebsEncryptionKeyArn)), name: \(Swift.String(describing: name)), pitPolicy: \(Swift.String(describing: pitPolicy)), replicatedDisks: \(Swift.String(describing: replicatedDisks)), replicationServerInstanceType: \(Swift.String(describing: replicationServerInstanceType)), replicationServersSecurityGroupsIDs: \(Swift.String(describing: replicationServersSecurityGroupsIDs)), sourceServerID: \(Swift.String(describing: sourceServerID)), stagingAreaSubnetId: \(Swift.String(describing: stagingAreaSubnetId)), useDedicatedReplicationServer: \(Swift.String(describing: useDedicatedReplicationServer)), stagingAreaTags: \"CONTENT_REDACTED\")"}
}

public struct UpdateReplicationConfigurationOutput: Swift.Sendable {
    /// Whether to associate the default Elastic Disaster Recovery Security group with the Replication Configuration.
    public var associateDefaultSecurityGroup: Swift.Bool?
    /// Whether to allow the AWS replication agent to automatically replicate newly added disks.
    public var autoReplicateNewDisks: Swift.Bool?
    /// Configure bandwidth throttling for the outbound data transfer rate of the Source Server in Mbps.
    public var bandwidthThrottling: Swift.Int
    /// Whether to create a Public IP for the Recovery Instance by default.
    public var createPublicIP: Swift.Bool?
    /// The data plane routing mechanism that will be used for replication.
    public var dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting?
    /// The Staging Disk EBS volume type to be used during replication.
    public var defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType?
    /// The type of EBS encryption to be used during replication.
    public var ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption?
    /// The ARN of the EBS encryption key to be used during replication.
    public var ebsEncryptionKeyArn: Swift.String?
    /// The name of the Replication Configuration.
    public var name: Swift.String?
    /// The Point in time (PIT) policy to manage snapshots taken during replication.
    public var pitPolicy: [DrsClientTypes.PITPolicyRule]?
    /// The configuration of the disks of the Source Server to be replicated.
    public var replicatedDisks: [DrsClientTypes.ReplicationConfigurationReplicatedDisk]?
    /// The instance type to be used for the replication server.
    public var replicationServerInstanceType: Swift.String?
    /// The security group IDs that will be used by the replication server.
    public var replicationServersSecurityGroupsIDs: [Swift.String]?
    /// The ID of the Source Server for this Replication Configuration.
    public var sourceServerID: Swift.String?
    /// The subnet to be used by the replication staging area.
    public var stagingAreaSubnetId: Swift.String?
    /// A set of tags to be associated with all resources created in the replication staging area: EC2 replication server, EBS volumes, EBS snapshots, etc.
    public var stagingAreaTags: [Swift.String: Swift.String]?
    /// Whether to use a dedicated Replication Server in the replication staging area.
    public var useDedicatedReplicationServer: Swift.Bool?

    public init(
        associateDefaultSecurityGroup: Swift.Bool? = nil,
        autoReplicateNewDisks: Swift.Bool? = nil,
        bandwidthThrottling: Swift.Int = 0,
        createPublicIP: Swift.Bool? = nil,
        dataPlaneRouting: DrsClientTypes.ReplicationConfigurationDataPlaneRouting? = nil,
        defaultLargeStagingDiskType: DrsClientTypes.ReplicationConfigurationDefaultLargeStagingDiskType? = nil,
        ebsEncryption: DrsClientTypes.ReplicationConfigurationEbsEncryption? = nil,
        ebsEncryptionKeyArn: Swift.String? = nil,
        name: Swift.String? = nil,
        pitPolicy: [DrsClientTypes.PITPolicyRule]? = nil,
        replicatedDisks: [DrsClientTypes.ReplicationConfigurationReplicatedDisk]? = nil,
        replicationServerInstanceType: Swift.String? = nil,
        replicationServersSecurityGroupsIDs: [Swift.String]? = nil,
        sourceServerID: Swift.String? = nil,
        stagingAreaSubnetId: Swift.String? = nil,
        stagingAreaTags: [Swift.String: Swift.String]? = nil,
        useDedicatedReplicationServer: Swift.Bool? = nil
    )
    {
        self.associateDefaultSecurityGroup = associateDefaultSecurityGroup
        self.autoReplicateNewDisks = autoReplicateNewDisks
        self.bandwidthThrottling = bandwidthThrottling
        self.createPublicIP = createPublicIP
        self.dataPlaneRouting = dataPlaneRouting
        self.defaultLargeStagingDiskType = defaultLargeStagingDiskType
        self.ebsEncryption = ebsEncryption
        self.ebsEncryptionKeyArn = ebsEncryptionKeyArn
        self.name = name
        self.pitPolicy = pitPolicy
        self.replicatedDisks = replicatedDisks
        self.replicationServerInstanceType = replicationServerInstanceType
        self.replicationServersSecurityGroupsIDs = replicationServersSecurityGroupsIDs
        self.sourceServerID = sourceServerID
        self.stagingAreaSubnetId = stagingAreaSubnetId
        self.stagingAreaTags = stagingAreaTags
        self.useDedicatedReplicationServer = useDedicatedReplicationServer
    }
}

extension UpdateReplicationConfigurationOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "UpdateReplicationConfigurationOutput(associateDefaultSecurityGroup: \(Swift.String(describing: associateDefaultSecurityGroup)), autoReplicateNewDisks: \(Swift.String(describing: autoReplicateNewDisks)), bandwidthThrottling: \(Swift.String(describing: bandwidthThrottling)), createPublicIP: \(Swift.String(describing: createPublicIP)), dataPlaneRouting: \(Swift.String(describing: dataPlaneRouting)), defaultLargeStagingDiskType: \(Swift.String(describing: defaultLargeStagingDiskType)), ebsEncryption: \(Swift.String(describing: ebsEncryption)), ebsEncryptionKeyArn: \(Swift.String(describing: ebsEncryptionKeyArn)), name: \(Swift.String(describing: name)), pitPolicy: \(Swift.String(describing: pitPolicy)), replicatedDisks: \(Swift.String(describing: replicatedDisks)), replicationServerInstanceType: \(Swift.String(describing: replicationServerInstanceType)), replicationServersSecurityGroupsIDs: \(Swift.String(describing: replicationServersSecurityGroupsIDs)), sourceServerID: \(Swift.String(describing: sourceServerID)), stagingAreaSubnetId: \(Swift.String(describing: stagingAreaSubnetId)), useDedicatedReplicationServer: \(Swift.String(describing: useDedicatedReplicationServer)), stagingAreaTags: \"CONTENT_REDACTED\")"}
}

public struct TagResourceInput: Swift.Sendable {
    /// ARN of the resource for which tags are to be added or updated.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// Array of tags to be added or updated.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

extension TagResourceInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "TagResourceInput(resourceArn: \(Swift.String(describing: resourceArn)), tags: \"CONTENT_REDACTED\")"}
}

public struct UntagResourceInput: Swift.Sendable {
    /// ARN of the resource for which tags are to be removed.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// Array of tags to be removed.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

extension UntagResourceInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "UntagResourceInput(resourceArn: \(Swift.String(describing: resourceArn)), tagKeys: \"CONTENT_REDACTED\")"}
}

extension AssociateSourceNetworkStackInput {

    static func urlPathProvider(_ value: AssociateSourceNetworkStackInput) -> Swift.String? {
        return "/AssociateSourceNetworkStack"
    }
}

extension CreateExtendedSourceServerInput {

    static func urlPathProvider(_ value: CreateExtendedSourceServerInput) -> Swift.String? {
        return "/CreateExtendedSourceServer"
    }
}

extension CreateLaunchConfigurationTemplateInput {

    static func urlPathProvider(_ value: CreateLaunchConfigurationTemplateInput) -> Swift.String? {
        return "/CreateLaunchConfigurationTemplate"
    }
}

extension CreateReplicationConfigurationTemplateInput {

    static func urlPathProvider(_ value: CreateReplicationConfigurationTemplateInput) -> Swift.String? {
        return "/CreateReplicationConfigurationTemplate"
    }
}

extension CreateSourceNetworkInput {

    static func urlPathProvider(_ value: CreateSourceNetworkInput) -> Swift.String? {
        return "/CreateSourceNetwork"
    }
}

extension DeleteJobInput {

    static func urlPathProvider(_ value: DeleteJobInput) -> Swift.String? {
        return "/DeleteJob"
    }
}

extension DeleteLaunchActionInput {

    static func urlPathProvider(_ value: DeleteLaunchActionInput) -> Swift.String? {
        return "/DeleteLaunchAction"
    }
}

extension DeleteLaunchConfigurationTemplateInput {

    static func urlPathProvider(_ value: DeleteLaunchConfigurationTemplateInput) -> Swift.String? {
        return "/DeleteLaunchConfigurationTemplate"
    }
}

extension DeleteRecoveryInstanceInput {

    static func urlPathProvider(_ value: DeleteRecoveryInstanceInput) -> Swift.String? {
        return "/DeleteRecoveryInstance"
    }
}

extension DeleteReplicationConfigurationTemplateInput {

    static func urlPathProvider(_ value: DeleteReplicationConfigurationTemplateInput) -> Swift.String? {
        return "/DeleteReplicationConfigurationTemplate"
    }
}

extension DeleteSourceNetworkInput {

    static func urlPathProvider(_ value: DeleteSourceNetworkInput) -> Swift.String? {
        return "/DeleteSourceNetwork"
    }
}

extension DeleteSourceServerInput {

    static func urlPathProvider(_ value: DeleteSourceServerInput) -> Swift.String? {
        return "/DeleteSourceServer"
    }
}

extension DescribeJobLogItemsInput {

    static func urlPathProvider(_ value: DescribeJobLogItemsInput) -> Swift.String? {
        return "/DescribeJobLogItems"
    }
}

extension DescribeJobsInput {

    static func urlPathProvider(_ value: DescribeJobsInput) -> Swift.String? {
        return "/DescribeJobs"
    }
}

extension DescribeLaunchConfigurationTemplatesInput {

    static func urlPathProvider(_ value: DescribeLaunchConfigurationTemplatesInput) -> Swift.String? {
        return "/DescribeLaunchConfigurationTemplates"
    }
}

extension DescribeRecoveryInstancesInput {

    static func urlPathProvider(_ value: DescribeRecoveryInstancesInput) -> Swift.String? {
        return "/DescribeRecoveryInstances"
    }
}

extension DescribeRecoverySnapshotsInput {

    static func urlPathProvider(_ value: DescribeRecoverySnapshotsInput) -> Swift.String? {
        return "/DescribeRecoverySnapshots"
    }
}

extension DescribeReplicationConfigurationTemplatesInput {

    static func urlPathProvider(_ value: DescribeReplicationConfigurationTemplatesInput) -> Swift.String? {
        return "/DescribeReplicationConfigurationTemplates"
    }
}

extension DescribeSourceNetworksInput {

    static func urlPathProvider(_ value: DescribeSourceNetworksInput) -> Swift.String? {
        return "/DescribeSourceNetworks"
    }
}

extension DescribeSourceServersInput {

    static func urlPathProvider(_ value: DescribeSourceServersInput) -> Swift.String? {
        return "/DescribeSourceServers"
    }
}

extension DisconnectRecoveryInstanceInput {

    static func urlPathProvider(_ value: DisconnectRecoveryInstanceInput) -> Swift.String? {
        return "/DisconnectRecoveryInstance"
    }
}

extension DisconnectSourceServerInput {

    static func urlPathProvider(_ value: DisconnectSourceServerInput) -> Swift.String? {
        return "/DisconnectSourceServer"
    }
}

extension ExportSourceNetworkCfnTemplateInput {

    static func urlPathProvider(_ value: ExportSourceNetworkCfnTemplateInput) -> Swift.String? {
        return "/ExportSourceNetworkCfnTemplate"
    }
}

extension GetFailbackReplicationConfigurationInput {

    static func urlPathProvider(_ value: GetFailbackReplicationConfigurationInput) -> Swift.String? {
        return "/GetFailbackReplicationConfiguration"
    }
}

extension GetLaunchConfigurationInput {

    static func urlPathProvider(_ value: GetLaunchConfigurationInput) -> Swift.String? {
        return "/GetLaunchConfiguration"
    }
}

extension GetReplicationConfigurationInput {

    static func urlPathProvider(_ value: GetReplicationConfigurationInput) -> Swift.String? {
        return "/GetReplicationConfiguration"
    }
}

extension InitializeServiceInput {

    static func urlPathProvider(_ value: InitializeServiceInput) -> Swift.String? {
        return "/InitializeService"
    }
}

extension ListExtensibleSourceServersInput {

    static func urlPathProvider(_ value: ListExtensibleSourceServersInput) -> Swift.String? {
        return "/ListExtensibleSourceServers"
    }
}

extension ListLaunchActionsInput {

    static func urlPathProvider(_ value: ListLaunchActionsInput) -> Swift.String? {
        return "/ListLaunchActions"
    }
}

extension ListStagingAccountsInput {

    static func urlPathProvider(_ value: ListStagingAccountsInput) -> Swift.String? {
        return "/ListStagingAccounts"
    }
}

extension ListStagingAccountsInput {

    static func queryItemProvider(_ value: ListStagingAccountsInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        if let maxResults = value.maxResults {
            let maxResultsQueryItem = Smithy.URIQueryItem(name: "maxResults".urlPercentEncoding(), value: Swift.String(maxResults).urlPercentEncoding())
            items.append(maxResultsQueryItem)
        }
        if let nextToken = value.nextToken {
            let nextTokenQueryItem = Smithy.URIQueryItem(name: "nextToken".urlPercentEncoding(), value: Swift.String(nextToken).urlPercentEncoding())
            items.append(nextTokenQueryItem)
        }
        return items
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension PutLaunchActionInput {

    static func urlPathProvider(_ value: PutLaunchActionInput) -> Swift.String? {
        return "/PutLaunchAction"
    }
}

extension RetryDataReplicationInput {

    static func urlPathProvider(_ value: RetryDataReplicationInput) -> Swift.String? {
        return "/RetryDataReplication"
    }
}

extension ReverseReplicationInput {

    static func urlPathProvider(_ value: ReverseReplicationInput) -> Swift.String? {
        return "/ReverseReplication"
    }
}

extension StartFailbackLaunchInput {

    static func urlPathProvider(_ value: StartFailbackLaunchInput) -> Swift.String? {
        return "/StartFailbackLaunch"
    }
}

extension StartRecoveryInput {

    static func urlPathProvider(_ value: StartRecoveryInput) -> Swift.String? {
        return "/StartRecovery"
    }
}

extension StartReplicationInput {

    static func urlPathProvider(_ value: StartReplicationInput) -> Swift.String? {
        return "/StartReplication"
    }
}

extension StartSourceNetworkRecoveryInput {

    static func urlPathProvider(_ value: StartSourceNetworkRecoveryInput) -> Swift.String? {
        return "/StartSourceNetworkRecovery"
    }
}

extension StartSourceNetworkReplicationInput {

    static func urlPathProvider(_ value: StartSourceNetworkReplicationInput) -> Swift.String? {
        return "/StartSourceNetworkReplication"
    }
}

extension StopFailbackInput {

    static func urlPathProvider(_ value: StopFailbackInput) -> Swift.String? {
        return "/StopFailback"
    }
}

extension StopReplicationInput {

    static func urlPathProvider(_ value: StopReplicationInput) -> Swift.String? {
        return "/StopReplication"
    }
}

extension StopSourceNetworkReplicationInput {

    static func urlPathProvider(_ value: StopSourceNetworkReplicationInput) -> Swift.String? {
        return "/StopSourceNetworkReplication"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension TerminateRecoveryInstancesInput {

    static func urlPathProvider(_ value: TerminateRecoveryInstancesInput) -> Swift.String? {
        return "/TerminateRecoveryInstances"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        guard let resourceArn = value.resourceArn else {
            return nil
        }
        return "/tags/\(resourceArn.urlPercentEncoding())"
    }
}

extension UntagResourceInput {

    static func queryItemProvider(_ value: UntagResourceInput) throws -> [Smithy.URIQueryItem] {
        var items = [Smithy.URIQueryItem]()
        guard let tagKeys = value.tagKeys else {
            let message = "Creating a URL Query Item failed. tagKeys is required and must not be nil."
            throw Smithy.ClientError.unknownError(message)
        }
        tagKeys.forEach { queryItemValue in
            let queryItem = Smithy.URIQueryItem(name: "tagKeys".urlPercentEncoding(), value: Swift.String(queryItemValue).urlPercentEncoding())
            items.append(queryItem)
        }
        return items
    }
}

extension UpdateFailbackReplicationConfigurationInput {

    static func urlPathProvider(_ value: UpdateFailbackReplicationConfigurationInput) -> Swift.String? {
        return "/UpdateFailbackReplicationConfiguration"
    }
}

extension UpdateLaunchConfigurationInput {

    static func urlPathProvider(_ value: UpdateLaunchConfigurationInput) -> Swift.String? {
        return "/UpdateLaunchConfiguration"
    }
}

extension UpdateLaunchConfigurationTemplateInput {

    static func urlPathProvider(_ value: UpdateLaunchConfigurationTemplateInput) -> Swift.String? {
        return "/UpdateLaunchConfigurationTemplate"
    }
}

extension UpdateReplicationConfigurationInput {

    static func urlPathProvider(_ value: UpdateReplicationConfigurationInput) -> Swift.String? {
        return "/UpdateReplicationConfiguration"
    }
}

extension UpdateReplicationConfigurationTemplateInput {

    static func urlPathProvider(_ value: UpdateReplicationConfigurationTemplateInput) -> Swift.String? {
        return "/UpdateReplicationConfigurationTemplate"
    }
}

extension AssociateSourceNetworkStackInput {

    static func write(value: AssociateSourceNetworkStackInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["cfnStackName"].write(value.cfnStackName)
        try writer["sourceNetworkID"].write(value.sourceNetworkID)
    }
}

extension CreateExtendedSourceServerInput {

    static func write(value: CreateExtendedSourceServerInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sourceServerArn"].write(value.sourceServerArn)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CreateLaunchConfigurationTemplateInput {

    static func write(value: CreateLaunchConfigurationTemplateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["copyPrivateIp"].write(value.copyPrivateIp)
        try writer["copyTags"].write(value.copyTags)
        try writer["exportBucketArn"].write(value.exportBucketArn)
        try writer["launchDisposition"].write(value.launchDisposition)
        try writer["launchIntoSourceInstance"].write(value.launchIntoSourceInstance)
        try writer["licensing"].write(value.licensing, with: DrsClientTypes.Licensing.write(value:to:))
        try writer["postLaunchEnabled"].write(value.postLaunchEnabled)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["targetInstanceTypeRightSizingMethod"].write(value.targetInstanceTypeRightSizingMethod)
    }
}

extension CreateReplicationConfigurationTemplateInput {

    static func write(value: CreateReplicationConfigurationTemplateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["associateDefaultSecurityGroup"].write(value.associateDefaultSecurityGroup)
        try writer["autoReplicateNewDisks"].write(value.autoReplicateNewDisks)
        try writer["bandwidthThrottling"].write(value.bandwidthThrottling)
        try writer["createPublicIP"].write(value.createPublicIP)
        try writer["dataPlaneRouting"].write(value.dataPlaneRouting)
        try writer["defaultLargeStagingDiskType"].write(value.defaultLargeStagingDiskType)
        try writer["ebsEncryption"].write(value.ebsEncryption)
        try writer["ebsEncryptionKeyArn"].write(value.ebsEncryptionKeyArn)
        try writer["pitPolicy"].writeList(value.pitPolicy, memberWritingClosure: DrsClientTypes.PITPolicyRule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["replicationServerInstanceType"].write(value.replicationServerInstanceType)
        try writer["replicationServersSecurityGroupsIDs"].writeList(value.replicationServersSecurityGroupsIDs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["stagingAreaSubnetId"].write(value.stagingAreaSubnetId)
        try writer["stagingAreaTags"].writeMap(value.stagingAreaTags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["useDedicatedReplicationServer"].write(value.useDedicatedReplicationServer)
    }
}

extension CreateSourceNetworkInput {

    static func write(value: CreateSourceNetworkInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["originAccountID"].write(value.originAccountID)
        try writer["originRegion"].write(value.originRegion)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["vpcID"].write(value.vpcID)
    }
}

extension DeleteJobInput {

    static func write(value: DeleteJobInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["jobID"].write(value.jobID)
    }
}

extension DeleteLaunchActionInput {

    static func write(value: DeleteLaunchActionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["actionId"].write(value.actionId)
        try writer["resourceId"].write(value.resourceId)
    }
}

extension DeleteLaunchConfigurationTemplateInput {

    static func write(value: DeleteLaunchConfigurationTemplateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["launchConfigurationTemplateID"].write(value.launchConfigurationTemplateID)
    }
}

extension DeleteRecoveryInstanceInput {

    static func write(value: DeleteRecoveryInstanceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["recoveryInstanceID"].write(value.recoveryInstanceID)
    }
}

extension DeleteReplicationConfigurationTemplateInput {

    static func write(value: DeleteReplicationConfigurationTemplateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["replicationConfigurationTemplateID"].write(value.replicationConfigurationTemplateID)
    }
}

extension DeleteSourceNetworkInput {

    static func write(value: DeleteSourceNetworkInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sourceNetworkID"].write(value.sourceNetworkID)
    }
}

extension DeleteSourceServerInput {

    static func write(value: DeleteSourceServerInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sourceServerID"].write(value.sourceServerID)
    }
}

extension DescribeJobLogItemsInput {

    static func write(value: DescribeJobLogItemsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["jobID"].write(value.jobID)
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension DescribeJobsInput {

    static func write(value: DescribeJobsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filters"].write(value.filters, with: DrsClientTypes.DescribeJobsRequestFilters.write(value:to:))
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension DescribeLaunchConfigurationTemplatesInput {

    static func write(value: DescribeLaunchConfigurationTemplatesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["launchConfigurationTemplateIDs"].writeList(value.launchConfigurationTemplateIDs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension DescribeRecoveryInstancesInput {

    static func write(value: DescribeRecoveryInstancesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filters"].write(value.filters, with: DrsClientTypes.DescribeRecoveryInstancesRequestFilters.write(value:to:))
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension DescribeRecoverySnapshotsInput {

    static func write(value: DescribeRecoverySnapshotsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filters"].write(value.filters, with: DrsClientTypes.DescribeRecoverySnapshotsRequestFilters.write(value:to:))
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["order"].write(value.order)
        try writer["sourceServerID"].write(value.sourceServerID)
    }
}

extension DescribeReplicationConfigurationTemplatesInput {

    static func write(value: DescribeReplicationConfigurationTemplatesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["replicationConfigurationTemplateIDs"].writeList(value.replicationConfigurationTemplateIDs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DescribeSourceNetworksInput {

    static func write(value: DescribeSourceNetworksInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filters"].write(value.filters, with: DrsClientTypes.DescribeSourceNetworksRequestFilters.write(value:to:))
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension DescribeSourceServersInput {

    static func write(value: DescribeSourceServersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filters"].write(value.filters, with: DrsClientTypes.DescribeSourceServersRequestFilters.write(value:to:))
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
    }
}

extension DisconnectRecoveryInstanceInput {

    static func write(value: DisconnectRecoveryInstanceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["recoveryInstanceID"].write(value.recoveryInstanceID)
    }
}

extension DisconnectSourceServerInput {

    static func write(value: DisconnectSourceServerInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sourceServerID"].write(value.sourceServerID)
    }
}

extension ExportSourceNetworkCfnTemplateInput {

    static func write(value: ExportSourceNetworkCfnTemplateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sourceNetworkID"].write(value.sourceNetworkID)
    }
}

extension GetFailbackReplicationConfigurationInput {

    static func write(value: GetFailbackReplicationConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["recoveryInstanceID"].write(value.recoveryInstanceID)
    }
}

extension GetLaunchConfigurationInput {

    static func write(value: GetLaunchConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sourceServerID"].write(value.sourceServerID)
    }
}

extension GetReplicationConfigurationInput {

    static func write(value: GetReplicationConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sourceServerID"].write(value.sourceServerID)
    }
}

extension ListExtensibleSourceServersInput {

    static func write(value: ListExtensibleSourceServersInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["stagingAccountID"].write(value.stagingAccountID)
    }
}

extension ListLaunchActionsInput {

    static func write(value: ListLaunchActionsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["filters"].write(value.filters, with: DrsClientTypes.LaunchActionsRequestFilters.write(value:to:))
        try writer["maxResults"].write(value.maxResults)
        try writer["nextToken"].write(value.nextToken)
        try writer["resourceId"].write(value.resourceId)
    }
}

extension PutLaunchActionInput {

    static func write(value: PutLaunchActionInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["actionCode"].write(value.actionCode)
        try writer["actionId"].write(value.actionId)
        try writer["actionVersion"].write(value.actionVersion)
        try writer["active"].write(value.active)
        try writer["category"].write(value.category)
        try writer["description"].write(value.description)
        try writer["name"].write(value.name)
        try writer["optional"].write(value.`optional`)
        try writer["order"].write(value.order)
        try writer["parameters"].writeMap(value.parameters, valueWritingClosure: DrsClientTypes.LaunchActionParameter.write(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["resourceId"].write(value.resourceId)
    }
}

extension RetryDataReplicationInput {

    static func write(value: RetryDataReplicationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sourceServerID"].write(value.sourceServerID)
    }
}

extension ReverseReplicationInput {

    static func write(value: ReverseReplicationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["recoveryInstanceID"].write(value.recoveryInstanceID)
    }
}

extension StartFailbackLaunchInput {

    static func write(value: StartFailbackLaunchInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["recoveryInstanceIDs"].writeList(value.recoveryInstanceIDs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension StartRecoveryInput {

    static func write(value: StartRecoveryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["isDrill"].write(value.isDrill)
        try writer["sourceServers"].writeList(value.sourceServers, memberWritingClosure: DrsClientTypes.StartRecoveryRequestSourceServer.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension StartReplicationInput {

    static func write(value: StartReplicationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sourceServerID"].write(value.sourceServerID)
    }
}

extension StartSourceNetworkRecoveryInput {

    static func write(value: StartSourceNetworkRecoveryInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["deployAsNew"].write(value.deployAsNew)
        try writer["sourceNetworks"].writeList(value.sourceNetworks, memberWritingClosure: DrsClientTypes.StartSourceNetworkRecoveryRequestNetworkEntry.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension StartSourceNetworkReplicationInput {

    static func write(value: StartSourceNetworkReplicationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sourceNetworkID"].write(value.sourceNetworkID)
    }
}

extension StopFailbackInput {

    static func write(value: StopFailbackInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["recoveryInstanceID"].write(value.recoveryInstanceID)
    }
}

extension StopReplicationInput {

    static func write(value: StopReplicationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sourceServerID"].write(value.sourceServerID)
    }
}

extension StopSourceNetworkReplicationInput {

    static func write(value: StopSourceNetworkReplicationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["sourceNetworkID"].write(value.sourceNetworkID)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension TerminateRecoveryInstancesInput {

    static func write(value: TerminateRecoveryInstancesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["recoveryInstanceIDs"].writeList(value.recoveryInstanceIDs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdateFailbackReplicationConfigurationInput {

    static func write(value: UpdateFailbackReplicationConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["bandwidthThrottling"].write(value.bandwidthThrottling)
        try writer["name"].write(value.name)
        try writer["recoveryInstanceID"].write(value.recoveryInstanceID)
        try writer["usePrivateIP"].write(value.usePrivateIP)
    }
}

extension UpdateLaunchConfigurationInput {

    static func write(value: UpdateLaunchConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["copyPrivateIp"].write(value.copyPrivateIp)
        try writer["copyTags"].write(value.copyTags)
        try writer["launchDisposition"].write(value.launchDisposition)
        try writer["launchIntoInstanceProperties"].write(value.launchIntoInstanceProperties, with: DrsClientTypes.LaunchIntoInstanceProperties.write(value:to:))
        try writer["licensing"].write(value.licensing, with: DrsClientTypes.Licensing.write(value:to:))
        try writer["name"].write(value.name)
        try writer["postLaunchEnabled"].write(value.postLaunchEnabled)
        try writer["sourceServerID"].write(value.sourceServerID)
        try writer["targetInstanceTypeRightSizingMethod"].write(value.targetInstanceTypeRightSizingMethod)
    }
}

extension UpdateLaunchConfigurationTemplateInput {

    static func write(value: UpdateLaunchConfigurationTemplateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["copyPrivateIp"].write(value.copyPrivateIp)
        try writer["copyTags"].write(value.copyTags)
        try writer["exportBucketArn"].write(value.exportBucketArn)
        try writer["launchConfigurationTemplateID"].write(value.launchConfigurationTemplateID)
        try writer["launchDisposition"].write(value.launchDisposition)
        try writer["launchIntoSourceInstance"].write(value.launchIntoSourceInstance)
        try writer["licensing"].write(value.licensing, with: DrsClientTypes.Licensing.write(value:to:))
        try writer["postLaunchEnabled"].write(value.postLaunchEnabled)
        try writer["targetInstanceTypeRightSizingMethod"].write(value.targetInstanceTypeRightSizingMethod)
    }
}

extension UpdateReplicationConfigurationInput {

    static func write(value: UpdateReplicationConfigurationInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["associateDefaultSecurityGroup"].write(value.associateDefaultSecurityGroup)
        try writer["autoReplicateNewDisks"].write(value.autoReplicateNewDisks)
        try writer["bandwidthThrottling"].write(value.bandwidthThrottling)
        try writer["createPublicIP"].write(value.createPublicIP)
        try writer["dataPlaneRouting"].write(value.dataPlaneRouting)
        try writer["defaultLargeStagingDiskType"].write(value.defaultLargeStagingDiskType)
        try writer["ebsEncryption"].write(value.ebsEncryption)
        try writer["ebsEncryptionKeyArn"].write(value.ebsEncryptionKeyArn)
        try writer["name"].write(value.name)
        try writer["pitPolicy"].writeList(value.pitPolicy, memberWritingClosure: DrsClientTypes.PITPolicyRule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["replicatedDisks"].writeList(value.replicatedDisks, memberWritingClosure: DrsClientTypes.ReplicationConfigurationReplicatedDisk.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["replicationServerInstanceType"].write(value.replicationServerInstanceType)
        try writer["replicationServersSecurityGroupsIDs"].writeList(value.replicationServersSecurityGroupsIDs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["sourceServerID"].write(value.sourceServerID)
        try writer["stagingAreaSubnetId"].write(value.stagingAreaSubnetId)
        try writer["stagingAreaTags"].writeMap(value.stagingAreaTags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["useDedicatedReplicationServer"].write(value.useDedicatedReplicationServer)
    }
}

extension UpdateReplicationConfigurationTemplateInput {

    static func write(value: UpdateReplicationConfigurationTemplateInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["arn"].write(value.arn)
        try writer["associateDefaultSecurityGroup"].write(value.associateDefaultSecurityGroup)
        try writer["autoReplicateNewDisks"].write(value.autoReplicateNewDisks)
        try writer["bandwidthThrottling"].write(value.bandwidthThrottling)
        try writer["createPublicIP"].write(value.createPublicIP)
        try writer["dataPlaneRouting"].write(value.dataPlaneRouting)
        try writer["defaultLargeStagingDiskType"].write(value.defaultLargeStagingDiskType)
        try writer["ebsEncryption"].write(value.ebsEncryption)
        try writer["ebsEncryptionKeyArn"].write(value.ebsEncryptionKeyArn)
        try writer["pitPolicy"].writeList(value.pitPolicy, memberWritingClosure: DrsClientTypes.PITPolicyRule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["replicationConfigurationTemplateID"].write(value.replicationConfigurationTemplateID)
        try writer["replicationServerInstanceType"].write(value.replicationServerInstanceType)
        try writer["replicationServersSecurityGroupsIDs"].writeList(value.replicationServersSecurityGroupsIDs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["stagingAreaSubnetId"].write(value.stagingAreaSubnetId)
        try writer["stagingAreaTags"].writeMap(value.stagingAreaTags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["useDedicatedReplicationServer"].write(value.useDedicatedReplicationServer)
    }
}

extension AssociateSourceNetworkStackOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> AssociateSourceNetworkStackOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = AssociateSourceNetworkStackOutput()
        value.job = try reader["job"].readIfPresent(with: DrsClientTypes.Job.read(from:))
        return value
    }
}

extension CreateExtendedSourceServerOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateExtendedSourceServerOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateExtendedSourceServerOutput()
        value.sourceServer = try reader["sourceServer"].readIfPresent(with: DrsClientTypes.SourceServer.read(from:))
        return value
    }
}

extension CreateLaunchConfigurationTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateLaunchConfigurationTemplateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateLaunchConfigurationTemplateOutput()
        value.launchConfigurationTemplate = try reader["launchConfigurationTemplate"].readIfPresent(with: DrsClientTypes.LaunchConfigurationTemplate.read(from:))
        return value
    }
}

extension CreateReplicationConfigurationTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateReplicationConfigurationTemplateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateReplicationConfigurationTemplateOutput()
        value.arn = try reader["arn"].readIfPresent()
        value.associateDefaultSecurityGroup = try reader["associateDefaultSecurityGroup"].readIfPresent()
        value.autoReplicateNewDisks = try reader["autoReplicateNewDisks"].readIfPresent()
        value.bandwidthThrottling = try reader["bandwidthThrottling"].readIfPresent() ?? 0
        value.createPublicIP = try reader["createPublicIP"].readIfPresent()
        value.dataPlaneRouting = try reader["dataPlaneRouting"].readIfPresent()
        value.defaultLargeStagingDiskType = try reader["defaultLargeStagingDiskType"].readIfPresent()
        value.ebsEncryption = try reader["ebsEncryption"].readIfPresent()
        value.ebsEncryptionKeyArn = try reader["ebsEncryptionKeyArn"].readIfPresent()
        value.pitPolicy = try reader["pitPolicy"].readListIfPresent(memberReadingClosure: DrsClientTypes.PITPolicyRule.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.replicationConfigurationTemplateID = try reader["replicationConfigurationTemplateID"].readIfPresent() ?? ""
        value.replicationServerInstanceType = try reader["replicationServerInstanceType"].readIfPresent()
        value.replicationServersSecurityGroupsIDs = try reader["replicationServersSecurityGroupsIDs"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.stagingAreaSubnetId = try reader["stagingAreaSubnetId"].readIfPresent()
        value.stagingAreaTags = try reader["stagingAreaTags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.useDedicatedReplicationServer = try reader["useDedicatedReplicationServer"].readIfPresent()
        return value
    }
}

extension CreateSourceNetworkOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateSourceNetworkOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateSourceNetworkOutput()
        value.sourceNetworkID = try reader["sourceNetworkID"].readIfPresent()
        return value
    }
}

extension DeleteJobOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteJobOutput {
        return DeleteJobOutput()
    }
}

extension DeleteLaunchActionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteLaunchActionOutput {
        return DeleteLaunchActionOutput()
    }
}

extension DeleteLaunchConfigurationTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteLaunchConfigurationTemplateOutput {
        return DeleteLaunchConfigurationTemplateOutput()
    }
}

extension DeleteRecoveryInstanceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteRecoveryInstanceOutput {
        return DeleteRecoveryInstanceOutput()
    }
}

extension DeleteReplicationConfigurationTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteReplicationConfigurationTemplateOutput {
        return DeleteReplicationConfigurationTemplateOutput()
    }
}

extension DeleteSourceNetworkOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteSourceNetworkOutput {
        return DeleteSourceNetworkOutput()
    }
}

extension DeleteSourceServerOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteSourceServerOutput {
        return DeleteSourceServerOutput()
    }
}

extension DescribeJobLogItemsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeJobLogItemsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeJobLogItemsOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: DrsClientTypes.JobLog.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension DescribeJobsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeJobsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeJobsOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: DrsClientTypes.Job.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension DescribeLaunchConfigurationTemplatesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeLaunchConfigurationTemplatesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeLaunchConfigurationTemplatesOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: DrsClientTypes.LaunchConfigurationTemplate.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension DescribeRecoveryInstancesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeRecoveryInstancesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeRecoveryInstancesOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: DrsClientTypes.RecoveryInstance.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension DescribeRecoverySnapshotsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeRecoverySnapshotsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeRecoverySnapshotsOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: DrsClientTypes.RecoverySnapshot.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension DescribeReplicationConfigurationTemplatesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeReplicationConfigurationTemplatesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeReplicationConfigurationTemplatesOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: DrsClientTypes.ReplicationConfigurationTemplate.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension DescribeSourceNetworksOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeSourceNetworksOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeSourceNetworksOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: DrsClientTypes.SourceNetwork.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension DescribeSourceServersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeSourceServersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeSourceServersOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: DrsClientTypes.SourceServer.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension DisconnectRecoveryInstanceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisconnectRecoveryInstanceOutput {
        return DisconnectRecoveryInstanceOutput()
    }
}

extension DisconnectSourceServerOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DisconnectSourceServerOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DisconnectSourceServerOutput()
        value.agentVersion = try reader["agentVersion"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.dataReplicationInfo = try reader["dataReplicationInfo"].readIfPresent(with: DrsClientTypes.DataReplicationInfo.read(from:))
        value.lastLaunchResult = try reader["lastLaunchResult"].readIfPresent()
        value.lifeCycle = try reader["lifeCycle"].readIfPresent(with: DrsClientTypes.LifeCycle.read(from:))
        value.recoveryInstanceId = try reader["recoveryInstanceId"].readIfPresent()
        value.replicationDirection = try reader["replicationDirection"].readIfPresent()
        value.reversedDirectionSourceServerArn = try reader["reversedDirectionSourceServerArn"].readIfPresent()
        value.sourceCloudProperties = try reader["sourceCloudProperties"].readIfPresent(with: DrsClientTypes.SourceCloudProperties.read(from:))
        value.sourceNetworkID = try reader["sourceNetworkID"].readIfPresent()
        value.sourceProperties = try reader["sourceProperties"].readIfPresent(with: DrsClientTypes.SourceProperties.read(from:))
        value.sourceServerID = try reader["sourceServerID"].readIfPresent()
        value.stagingArea = try reader["stagingArea"].readIfPresent(with: DrsClientTypes.StagingArea.read(from:))
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension ExportSourceNetworkCfnTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ExportSourceNetworkCfnTemplateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ExportSourceNetworkCfnTemplateOutput()
        value.s3DestinationUrl = try reader["s3DestinationUrl"].readIfPresent()
        return value
    }
}

extension GetFailbackReplicationConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetFailbackReplicationConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetFailbackReplicationConfigurationOutput()
        value.bandwidthThrottling = try reader["bandwidthThrottling"].readIfPresent() ?? 0
        value.name = try reader["name"].readIfPresent()
        value.recoveryInstanceID = try reader["recoveryInstanceID"].readIfPresent() ?? ""
        value.usePrivateIP = try reader["usePrivateIP"].readIfPresent()
        return value
    }
}

extension GetLaunchConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetLaunchConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetLaunchConfigurationOutput()
        value.copyPrivateIp = try reader["copyPrivateIp"].readIfPresent()
        value.copyTags = try reader["copyTags"].readIfPresent()
        value.ec2LaunchTemplateID = try reader["ec2LaunchTemplateID"].readIfPresent()
        value.launchDisposition = try reader["launchDisposition"].readIfPresent()
        value.launchIntoInstanceProperties = try reader["launchIntoInstanceProperties"].readIfPresent(with: DrsClientTypes.LaunchIntoInstanceProperties.read(from:))
        value.licensing = try reader["licensing"].readIfPresent(with: DrsClientTypes.Licensing.read(from:))
        value.name = try reader["name"].readIfPresent()
        value.postLaunchEnabled = try reader["postLaunchEnabled"].readIfPresent()
        value.sourceServerID = try reader["sourceServerID"].readIfPresent()
        value.targetInstanceTypeRightSizingMethod = try reader["targetInstanceTypeRightSizingMethod"].readIfPresent()
        return value
    }
}

extension GetReplicationConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetReplicationConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetReplicationConfigurationOutput()
        value.associateDefaultSecurityGroup = try reader["associateDefaultSecurityGroup"].readIfPresent()
        value.autoReplicateNewDisks = try reader["autoReplicateNewDisks"].readIfPresent()
        value.bandwidthThrottling = try reader["bandwidthThrottling"].readIfPresent() ?? 0
        value.createPublicIP = try reader["createPublicIP"].readIfPresent()
        value.dataPlaneRouting = try reader["dataPlaneRouting"].readIfPresent()
        value.defaultLargeStagingDiskType = try reader["defaultLargeStagingDiskType"].readIfPresent()
        value.ebsEncryption = try reader["ebsEncryption"].readIfPresent()
        value.ebsEncryptionKeyArn = try reader["ebsEncryptionKeyArn"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.pitPolicy = try reader["pitPolicy"].readListIfPresent(memberReadingClosure: DrsClientTypes.PITPolicyRule.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.replicatedDisks = try reader["replicatedDisks"].readListIfPresent(memberReadingClosure: DrsClientTypes.ReplicationConfigurationReplicatedDisk.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.replicationServerInstanceType = try reader["replicationServerInstanceType"].readIfPresent()
        value.replicationServersSecurityGroupsIDs = try reader["replicationServersSecurityGroupsIDs"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.sourceServerID = try reader["sourceServerID"].readIfPresent()
        value.stagingAreaSubnetId = try reader["stagingAreaSubnetId"].readIfPresent()
        value.stagingAreaTags = try reader["stagingAreaTags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.useDedicatedReplicationServer = try reader["useDedicatedReplicationServer"].readIfPresent()
        return value
    }
}

extension InitializeServiceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> InitializeServiceOutput {
        return InitializeServiceOutput()
    }
}

extension ListExtensibleSourceServersOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListExtensibleSourceServersOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListExtensibleSourceServersOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: DrsClientTypes.StagingSourceServer.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListLaunchActionsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListLaunchActionsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListLaunchActionsOutput()
        value.items = try reader["items"].readListIfPresent(memberReadingClosure: DrsClientTypes.LaunchAction.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListStagingAccountsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListStagingAccountsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListStagingAccountsOutput()
        value.accounts = try reader["accounts"].readListIfPresent(memberReadingClosure: DrsClientTypes.Account.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["nextToken"].readIfPresent()
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension PutLaunchActionOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> PutLaunchActionOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = PutLaunchActionOutput()
        value.actionCode = try reader["actionCode"].readIfPresent()
        value.actionId = try reader["actionId"].readIfPresent()
        value.actionVersion = try reader["actionVersion"].readIfPresent()
        value.active = try reader["active"].readIfPresent()
        value.category = try reader["category"].readIfPresent()
        value.description = try reader["description"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.`optional` = try reader["optional"].readIfPresent()
        value.order = try reader["order"].readIfPresent()
        value.parameters = try reader["parameters"].readMapIfPresent(valueReadingClosure: DrsClientTypes.LaunchActionParameter.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.resourceId = try reader["resourceId"].readIfPresent()
        value.type = try reader["type"].readIfPresent()
        return value
    }
}

extension RetryDataReplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> RetryDataReplicationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = RetryDataReplicationOutput()
        value.agentVersion = try reader["agentVersion"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.dataReplicationInfo = try reader["dataReplicationInfo"].readIfPresent(with: DrsClientTypes.DataReplicationInfo.read(from:))
        value.lastLaunchResult = try reader["lastLaunchResult"].readIfPresent()
        value.lifeCycle = try reader["lifeCycle"].readIfPresent(with: DrsClientTypes.LifeCycle.read(from:))
        value.recoveryInstanceId = try reader["recoveryInstanceId"].readIfPresent()
        value.replicationDirection = try reader["replicationDirection"].readIfPresent()
        value.reversedDirectionSourceServerArn = try reader["reversedDirectionSourceServerArn"].readIfPresent()
        value.sourceCloudProperties = try reader["sourceCloudProperties"].readIfPresent(with: DrsClientTypes.SourceCloudProperties.read(from:))
        value.sourceNetworkID = try reader["sourceNetworkID"].readIfPresent()
        value.sourceProperties = try reader["sourceProperties"].readIfPresent(with: DrsClientTypes.SourceProperties.read(from:))
        value.sourceServerID = try reader["sourceServerID"].readIfPresent()
        value.stagingArea = try reader["stagingArea"].readIfPresent(with: DrsClientTypes.StagingArea.read(from:))
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension ReverseReplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ReverseReplicationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ReverseReplicationOutput()
        value.reversedDirectionSourceServerArn = try reader["reversedDirectionSourceServerArn"].readIfPresent()
        return value
    }
}

extension StartFailbackLaunchOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartFailbackLaunchOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartFailbackLaunchOutput()
        value.job = try reader["job"].readIfPresent(with: DrsClientTypes.Job.read(from:))
        return value
    }
}

extension StartRecoveryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartRecoveryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartRecoveryOutput()
        value.job = try reader["job"].readIfPresent(with: DrsClientTypes.Job.read(from:))
        return value
    }
}

extension StartReplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartReplicationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartReplicationOutput()
        value.sourceServer = try reader["sourceServer"].readIfPresent(with: DrsClientTypes.SourceServer.read(from:))
        return value
    }
}

extension StartSourceNetworkRecoveryOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartSourceNetworkRecoveryOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartSourceNetworkRecoveryOutput()
        value.job = try reader["job"].readIfPresent(with: DrsClientTypes.Job.read(from:))
        return value
    }
}

extension StartSourceNetworkReplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StartSourceNetworkReplicationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StartSourceNetworkReplicationOutput()
        value.sourceNetwork = try reader["sourceNetwork"].readIfPresent(with: DrsClientTypes.SourceNetwork.read(from:))
        return value
    }
}

extension StopFailbackOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopFailbackOutput {
        return StopFailbackOutput()
    }
}

extension StopReplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopReplicationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StopReplicationOutput()
        value.sourceServer = try reader["sourceServer"].readIfPresent(with: DrsClientTypes.SourceServer.read(from:))
        return value
    }
}

extension StopSourceNetworkReplicationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> StopSourceNetworkReplicationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = StopSourceNetworkReplicationOutput()
        value.sourceNetwork = try reader["sourceNetwork"].readIfPresent(with: DrsClientTypes.SourceNetwork.read(from:))
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension TerminateRecoveryInstancesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TerminateRecoveryInstancesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = TerminateRecoveryInstancesOutput()
        value.job = try reader["job"].readIfPresent(with: DrsClientTypes.Job.read(from:))
        return value
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateFailbackReplicationConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateFailbackReplicationConfigurationOutput {
        return UpdateFailbackReplicationConfigurationOutput()
    }
}

extension UpdateLaunchConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLaunchConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateLaunchConfigurationOutput()
        value.copyPrivateIp = try reader["copyPrivateIp"].readIfPresent()
        value.copyTags = try reader["copyTags"].readIfPresent()
        value.ec2LaunchTemplateID = try reader["ec2LaunchTemplateID"].readIfPresent()
        value.launchDisposition = try reader["launchDisposition"].readIfPresent()
        value.launchIntoInstanceProperties = try reader["launchIntoInstanceProperties"].readIfPresent(with: DrsClientTypes.LaunchIntoInstanceProperties.read(from:))
        value.licensing = try reader["licensing"].readIfPresent(with: DrsClientTypes.Licensing.read(from:))
        value.name = try reader["name"].readIfPresent()
        value.postLaunchEnabled = try reader["postLaunchEnabled"].readIfPresent()
        value.sourceServerID = try reader["sourceServerID"].readIfPresent()
        value.targetInstanceTypeRightSizingMethod = try reader["targetInstanceTypeRightSizingMethod"].readIfPresent()
        return value
    }
}

extension UpdateLaunchConfigurationTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateLaunchConfigurationTemplateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateLaunchConfigurationTemplateOutput()
        value.launchConfigurationTemplate = try reader["launchConfigurationTemplate"].readIfPresent(with: DrsClientTypes.LaunchConfigurationTemplate.read(from:))
        return value
    }
}

extension UpdateReplicationConfigurationOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateReplicationConfigurationOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateReplicationConfigurationOutput()
        value.associateDefaultSecurityGroup = try reader["associateDefaultSecurityGroup"].readIfPresent()
        value.autoReplicateNewDisks = try reader["autoReplicateNewDisks"].readIfPresent()
        value.bandwidthThrottling = try reader["bandwidthThrottling"].readIfPresent() ?? 0
        value.createPublicIP = try reader["createPublicIP"].readIfPresent()
        value.dataPlaneRouting = try reader["dataPlaneRouting"].readIfPresent()
        value.defaultLargeStagingDiskType = try reader["defaultLargeStagingDiskType"].readIfPresent()
        value.ebsEncryption = try reader["ebsEncryption"].readIfPresent()
        value.ebsEncryptionKeyArn = try reader["ebsEncryptionKeyArn"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.pitPolicy = try reader["pitPolicy"].readListIfPresent(memberReadingClosure: DrsClientTypes.PITPolicyRule.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.replicatedDisks = try reader["replicatedDisks"].readListIfPresent(memberReadingClosure: DrsClientTypes.ReplicationConfigurationReplicatedDisk.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.replicationServerInstanceType = try reader["replicationServerInstanceType"].readIfPresent()
        value.replicationServersSecurityGroupsIDs = try reader["replicationServersSecurityGroupsIDs"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.sourceServerID = try reader["sourceServerID"].readIfPresent()
        value.stagingAreaSubnetId = try reader["stagingAreaSubnetId"].readIfPresent()
        value.stagingAreaTags = try reader["stagingAreaTags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.useDedicatedReplicationServer = try reader["useDedicatedReplicationServer"].readIfPresent()
        return value
    }
}

extension UpdateReplicationConfigurationTemplateOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateReplicationConfigurationTemplateOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateReplicationConfigurationTemplateOutput()
        value.arn = try reader["arn"].readIfPresent()
        value.associateDefaultSecurityGroup = try reader["associateDefaultSecurityGroup"].readIfPresent()
        value.autoReplicateNewDisks = try reader["autoReplicateNewDisks"].readIfPresent()
        value.bandwidthThrottling = try reader["bandwidthThrottling"].readIfPresent() ?? 0
        value.createPublicIP = try reader["createPublicIP"].readIfPresent()
        value.dataPlaneRouting = try reader["dataPlaneRouting"].readIfPresent()
        value.defaultLargeStagingDiskType = try reader["defaultLargeStagingDiskType"].readIfPresent()
        value.ebsEncryption = try reader["ebsEncryption"].readIfPresent()
        value.ebsEncryptionKeyArn = try reader["ebsEncryptionKeyArn"].readIfPresent()
        value.pitPolicy = try reader["pitPolicy"].readListIfPresent(memberReadingClosure: DrsClientTypes.PITPolicyRule.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.replicationConfigurationTemplateID = try reader["replicationConfigurationTemplateID"].readIfPresent() ?? ""
        value.replicationServerInstanceType = try reader["replicationServerInstanceType"].readIfPresent()
        value.replicationServersSecurityGroupsIDs = try reader["replicationServersSecurityGroupsIDs"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.stagingAreaSubnetId = try reader["stagingAreaSubnetId"].readIfPresent()
        value.stagingAreaTags = try reader["stagingAreaTags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.useDedicatedReplicationServer = try reader["useDedicatedReplicationServer"].readIfPresent()
        return value
    }
}

enum AssociateSourceNetworkStackOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateExtendedSourceServerOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateLaunchConfigurationTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateReplicationConfigurationTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum CreateSourceNetworkOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteJobOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteLaunchActionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteLaunchConfigurationTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteRecoveryInstanceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteReplicationConfigurationTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteSourceNetworkOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteSourceServerOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeJobLogItemsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeJobsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeLaunchConfigurationTemplatesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeRecoveryInstancesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeRecoverySnapshotsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeReplicationConfigurationTemplatesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeSourceNetworksOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeSourceServersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisconnectRecoveryInstanceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DisconnectSourceServerOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ExportSourceNetworkCfnTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetFailbackReplicationConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetLaunchConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetReplicationConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum InitializeServiceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListExtensibleSourceServersOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListLaunchActionsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListStagingAccountsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum PutLaunchActionOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum RetryDataReplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ReverseReplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartFailbackLaunchOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartRecoveryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartReplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartSourceNetworkRecoveryOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StartSourceNetworkReplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopFailbackOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopReplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum StopSourceNetworkReplicationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TerminateRecoveryInstancesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ServiceQuotaExceededException": return try ServiceQuotaExceededException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateFailbackReplicationConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLaunchConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateLaunchConfigurationTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateReplicationConfigurationOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "ConflictException": return try ConflictException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateReplicationConfigurationTemplateOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.RestJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "AccessDeniedException": return try AccessDeniedException.makeError(baseError: baseError)
            case "InternalServerException": return try InternalServerException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "ThrottlingException": return try ThrottlingException.makeError(baseError: baseError)
            case "UninitializedAccountException": return try UninitializedAccountException.makeError(baseError: baseError)
            case "ValidationException": return try ValidationException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension UninitializedAccountException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> UninitializedAccountException {
        let reader = baseError.errorBodyReader
        var value = UninitializedAccountException()
        value.properties.code = try reader["code"].readIfPresent()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.code = try reader["code"].readIfPresent()
        value.properties.message = try reader["message"].readIfPresent()
        value.properties.resourceId = try reader["resourceId"].readIfPresent()
        value.properties.resourceType = try reader["resourceType"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ThrottlingException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ThrottlingException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = ThrottlingException()
        if let retryAfterSecondsHeaderValue = httpResponse.headers.value(for: "Retry-After") {
            value.properties.retryAfterSeconds = retryAfterSecondsHeaderValue
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.properties.quotaCode = try reader["quotaCode"].readIfPresent()
        value.properties.serviceCode = try reader["serviceCode"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ServiceQuotaExceededException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ServiceQuotaExceededException {
        let reader = baseError.errorBodyReader
        var value = ServiceQuotaExceededException()
        value.properties.code = try reader["code"].readIfPresent()
        value.properties.message = try reader["message"].readIfPresent()
        value.properties.quotaCode = try reader["quotaCode"].readIfPresent()
        value.properties.resourceId = try reader["resourceId"].readIfPresent()
        value.properties.resourceType = try reader["resourceType"].readIfPresent()
        value.properties.serviceCode = try reader["serviceCode"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ValidationException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ValidationException {
        let reader = baseError.errorBodyReader
        var value = ValidationException()
        value.properties.code = try reader["code"].readIfPresent()
        value.properties.fieldList = try reader["fieldList"].readListIfPresent(memberReadingClosure: DrsClientTypes.ValidationExceptionField.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.properties.message = try reader["message"].readIfPresent()
        value.properties.reason = try reader["reason"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalServerException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> InternalServerException {
        let reader = baseError.errorBodyReader
        let httpResponse = baseError.httpResponse
        var value = InternalServerException()
        if let retryAfterSecondsHeaderValue = httpResponse.headers.value(for: "Retry-After") {
            value.properties.retryAfterSeconds = Swift.Int(retryAfterSecondsHeaderValue) ?? 0
        }
        value.properties.message = try reader["message"].readIfPresent() ?? ""
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConflictException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> ConflictException {
        let reader = baseError.errorBodyReader
        var value = ConflictException()
        value.properties.code = try reader["code"].readIfPresent()
        value.properties.message = try reader["message"].readIfPresent()
        value.properties.resourceId = try reader["resourceId"].readIfPresent()
        value.properties.resourceType = try reader["resourceType"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension AccessDeniedException {

    static func makeError(baseError: AWSClientRuntime.RestJSONError) throws -> AccessDeniedException {
        let reader = baseError.errorBodyReader
        var value = AccessDeniedException()
        value.properties.code = try reader["code"].readIfPresent()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DrsClientTypes.Job {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.Job {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.Job()
        value.jobID = try reader["jobID"].readIfPresent() ?? ""
        value.arn = try reader["arn"].readIfPresent()
        value.type = try reader["type"].readIfPresent()
        value.initiatedBy = try reader["initiatedBy"].readIfPresent()
        value.creationDateTime = try reader["creationDateTime"].readIfPresent()
        value.endDateTime = try reader["endDateTime"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        value.participatingServers = try reader["participatingServers"].readListIfPresent(memberReadingClosure: DrsClientTypes.ParticipatingServer.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.participatingResources = try reader["participatingResources"].readListIfPresent(memberReadingClosure: DrsClientTypes.ParticipatingResource.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DrsClientTypes.ParticipatingResource {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.ParticipatingResource {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.ParticipatingResource()
        value.participatingResourceID = try reader["participatingResourceID"].readIfPresent(with: DrsClientTypes.ParticipatingResourceID.read(from:))
        value.launchStatus = try reader["launchStatus"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.ParticipatingResourceID {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.ParticipatingResourceID {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "sourceNetworkID":
                return .sourcenetworkid(try reader["sourceNetworkID"].read())
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension DrsClientTypes.ParticipatingServer {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.ParticipatingServer {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.ParticipatingServer()
        value.sourceServerID = try reader["sourceServerID"].readIfPresent()
        value.recoveryInstanceID = try reader["recoveryInstanceID"].readIfPresent()
        value.launchStatus = try reader["launchStatus"].readIfPresent()
        value.launchActionsStatus = try reader["launchActionsStatus"].readIfPresent(with: DrsClientTypes.LaunchActionsStatus.read(from:))
        return value
    }
}

extension DrsClientTypes.LaunchActionsStatus {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.LaunchActionsStatus {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.LaunchActionsStatus()
        value.ssmAgentDiscoveryDatetime = try reader["ssmAgentDiscoveryDatetime"].readIfPresent()
        value.runs = try reader["runs"].readListIfPresent(memberReadingClosure: DrsClientTypes.LaunchActionRun.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DrsClientTypes.LaunchActionRun {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.LaunchActionRun {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.LaunchActionRun()
        value.action = try reader["action"].readIfPresent(with: DrsClientTypes.LaunchAction.read(from:))
        value.runId = try reader["runId"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        value.failureReason = try reader["failureReason"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.LaunchAction {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.LaunchAction {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.LaunchAction()
        value.actionId = try reader["actionId"].readIfPresent()
        value.actionCode = try reader["actionCode"].readIfPresent()
        value.type = try reader["type"].readIfPresent()
        value.name = try reader["name"].readIfPresent()
        value.active = try reader["active"].readIfPresent()
        value.order = try reader["order"].readIfPresent()
        value.actionVersion = try reader["actionVersion"].readIfPresent()
        value.`optional` = try reader["optional"].readIfPresent()
        value.parameters = try reader["parameters"].readMapIfPresent(valueReadingClosure: DrsClientTypes.LaunchActionParameter.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.description = try reader["description"].readIfPresent()
        value.category = try reader["category"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.LaunchActionParameter {

    static func write(value: DrsClientTypes.LaunchActionParameter?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["type"].write(value.type)
        try writer["value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.LaunchActionParameter {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.LaunchActionParameter()
        value.value = try reader["value"].readIfPresent()
        value.type = try reader["type"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.SourceServer {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.SourceServer {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.SourceServer()
        value.sourceServerID = try reader["sourceServerID"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.recoveryInstanceId = try reader["recoveryInstanceId"].readIfPresent()
        value.lastLaunchResult = try reader["lastLaunchResult"].readIfPresent()
        value.dataReplicationInfo = try reader["dataReplicationInfo"].readIfPresent(with: DrsClientTypes.DataReplicationInfo.read(from:))
        value.lifeCycle = try reader["lifeCycle"].readIfPresent(with: DrsClientTypes.LifeCycle.read(from:))
        value.sourceProperties = try reader["sourceProperties"].readIfPresent(with: DrsClientTypes.SourceProperties.read(from:))
        value.stagingArea = try reader["stagingArea"].readIfPresent(with: DrsClientTypes.StagingArea.read(from:))
        value.sourceCloudProperties = try reader["sourceCloudProperties"].readIfPresent(with: DrsClientTypes.SourceCloudProperties.read(from:))
        value.replicationDirection = try reader["replicationDirection"].readIfPresent()
        value.reversedDirectionSourceServerArn = try reader["reversedDirectionSourceServerArn"].readIfPresent()
        value.sourceNetworkID = try reader["sourceNetworkID"].readIfPresent()
        value.agentVersion = try reader["agentVersion"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.SourceCloudProperties {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.SourceCloudProperties {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.SourceCloudProperties()
        value.originAccountID = try reader["originAccountID"].readIfPresent()
        value.originRegion = try reader["originRegion"].readIfPresent()
        value.originAvailabilityZone = try reader["originAvailabilityZone"].readIfPresent()
        value.sourceOutpostArn = try reader["sourceOutpostArn"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.StagingArea {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.StagingArea {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.StagingArea()
        value.status = try reader["status"].readIfPresent()
        value.stagingAccountID = try reader["stagingAccountID"].readIfPresent()
        value.stagingSourceServerArn = try reader["stagingSourceServerArn"].readIfPresent()
        value.errorMessage = try reader["errorMessage"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.SourceProperties {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.SourceProperties {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.SourceProperties()
        value.lastUpdatedDateTime = try reader["lastUpdatedDateTime"].readIfPresent()
        value.recommendedInstanceType = try reader["recommendedInstanceType"].readIfPresent()
        value.identificationHints = try reader["identificationHints"].readIfPresent(with: DrsClientTypes.IdentificationHints.read(from:))
        value.networkInterfaces = try reader["networkInterfaces"].readListIfPresent(memberReadingClosure: DrsClientTypes.NetworkInterface.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.disks = try reader["disks"].readListIfPresent(memberReadingClosure: DrsClientTypes.Disk.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.cpus = try reader["cpus"].readListIfPresent(memberReadingClosure: DrsClientTypes.CPU.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.ramBytes = try reader["ramBytes"].readIfPresent() ?? 0
        value.os = try reader["os"].readIfPresent(with: DrsClientTypes.OS.read(from:))
        value.supportsNitroInstances = try reader["supportsNitroInstances"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.OS {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.OS {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.OS()
        value.fullString = try reader["fullString"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.CPU {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.CPU {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.CPU()
        value.cores = try reader["cores"].readIfPresent() ?? 0
        value.modelName = try reader["modelName"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.Disk {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.Disk {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.Disk()
        value.deviceName = try reader["deviceName"].readIfPresent()
        value.bytes = try reader["bytes"].readIfPresent() ?? 0
        return value
    }
}

extension DrsClientTypes.NetworkInterface {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.NetworkInterface {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.NetworkInterface()
        value.macAddress = try reader["macAddress"].readIfPresent()
        value.ips = try reader["ips"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.isPrimary = try reader["isPrimary"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.IdentificationHints {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.IdentificationHints {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.IdentificationHints()
        value.fqdn = try reader["fqdn"].readIfPresent()
        value.hostname = try reader["hostname"].readIfPresent()
        value.vmWareUuid = try reader["vmWareUuid"].readIfPresent()
        value.awsInstanceID = try reader["awsInstanceID"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.LifeCycle {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.LifeCycle {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.LifeCycle()
        value.addedToServiceDateTime = try reader["addedToServiceDateTime"].readIfPresent()
        value.firstByteDateTime = try reader["firstByteDateTime"].readIfPresent()
        value.elapsedReplicationDuration = try reader["elapsedReplicationDuration"].readIfPresent()
        value.lastSeenByServiceDateTime = try reader["lastSeenByServiceDateTime"].readIfPresent()
        value.lastLaunch = try reader["lastLaunch"].readIfPresent(with: DrsClientTypes.LifeCycleLastLaunch.read(from:))
        return value
    }
}

extension DrsClientTypes.LifeCycleLastLaunch {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.LifeCycleLastLaunch {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.LifeCycleLastLaunch()
        value.initiated = try reader["initiated"].readIfPresent(with: DrsClientTypes.LifeCycleLastLaunchInitiated.read(from:))
        value.status = try reader["status"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.LifeCycleLastLaunchInitiated {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.LifeCycleLastLaunchInitiated {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.LifeCycleLastLaunchInitiated()
        value.apiCallDateTime = try reader["apiCallDateTime"].readIfPresent()
        value.jobID = try reader["jobID"].readIfPresent()
        value.type = try reader["type"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.DataReplicationInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.DataReplicationInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.DataReplicationInfo()
        value.lagDuration = try reader["lagDuration"].readIfPresent()
        value.etaDateTime = try reader["etaDateTime"].readIfPresent()
        value.replicatedDisks = try reader["replicatedDisks"].readListIfPresent(memberReadingClosure: DrsClientTypes.DataReplicationInfoReplicatedDisk.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.dataReplicationState = try reader["dataReplicationState"].readIfPresent()
        value.dataReplicationInitiation = try reader["dataReplicationInitiation"].readIfPresent(with: DrsClientTypes.DataReplicationInitiation.read(from:))
        value.dataReplicationError = try reader["dataReplicationError"].readIfPresent(with: DrsClientTypes.DataReplicationError.read(from:))
        value.stagingAvailabilityZone = try reader["stagingAvailabilityZone"].readIfPresent()
        value.stagingOutpostArn = try reader["stagingOutpostArn"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.DataReplicationError {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.DataReplicationError {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.DataReplicationError()
        value.error = try reader["error"].readIfPresent()
        value.rawError = try reader["rawError"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.DataReplicationInitiation {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.DataReplicationInitiation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.DataReplicationInitiation()
        value.startDateTime = try reader["startDateTime"].readIfPresent()
        value.nextAttemptDateTime = try reader["nextAttemptDateTime"].readIfPresent()
        value.steps = try reader["steps"].readListIfPresent(memberReadingClosure: DrsClientTypes.DataReplicationInitiationStep.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DrsClientTypes.DataReplicationInitiationStep {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.DataReplicationInitiationStep {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.DataReplicationInitiationStep()
        value.name = try reader["name"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.DataReplicationInfoReplicatedDisk {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.DataReplicationInfoReplicatedDisk {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.DataReplicationInfoReplicatedDisk()
        value.deviceName = try reader["deviceName"].readIfPresent()
        value.totalStorageBytes = try reader["totalStorageBytes"].readIfPresent() ?? 0
        value.replicatedStorageBytes = try reader["replicatedStorageBytes"].readIfPresent() ?? 0
        value.rescannedStorageBytes = try reader["rescannedStorageBytes"].readIfPresent() ?? 0
        value.backloggedStorageBytes = try reader["backloggedStorageBytes"].readIfPresent() ?? 0
        value.volumeStatus = try reader["volumeStatus"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.LaunchConfigurationTemplate {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.LaunchConfigurationTemplate {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.LaunchConfigurationTemplate()
        value.launchConfigurationTemplateID = try reader["launchConfigurationTemplateID"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.launchDisposition = try reader["launchDisposition"].readIfPresent()
        value.targetInstanceTypeRightSizingMethod = try reader["targetInstanceTypeRightSizingMethod"].readIfPresent()
        value.copyPrivateIp = try reader["copyPrivateIp"].readIfPresent()
        value.copyTags = try reader["copyTags"].readIfPresent()
        value.licensing = try reader["licensing"].readIfPresent(with: DrsClientTypes.Licensing.read(from:))
        value.exportBucketArn = try reader["exportBucketArn"].readIfPresent()
        value.postLaunchEnabled = try reader["postLaunchEnabled"].readIfPresent()
        value.launchIntoSourceInstance = try reader["launchIntoSourceInstance"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.Licensing {

    static func write(value: DrsClientTypes.Licensing?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["osByol"].write(value.osByol)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.Licensing {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.Licensing()
        value.osByol = try reader["osByol"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.PITPolicyRule {

    static func write(value: DrsClientTypes.PITPolicyRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["enabled"].write(value.enabled)
        try writer["interval"].write(value.interval)
        try writer["retentionDuration"].write(value.retentionDuration)
        try writer["ruleID"].write(value.ruleID)
        try writer["units"].write(value.units)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.PITPolicyRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.PITPolicyRule()
        value.ruleID = try reader["ruleID"].readIfPresent() ?? 0
        value.units = try reader["units"].readIfPresent() ?? .sdkUnknown("")
        value.interval = try reader["interval"].readIfPresent() ?? 0
        value.retentionDuration = try reader["retentionDuration"].readIfPresent() ?? 0
        value.enabled = try reader["enabled"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.JobLog {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.JobLog {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.JobLog()
        value.logDateTime = try reader["logDateTime"].readIfPresent()
        value.event = try reader["event"].readIfPresent()
        value.eventData = try reader["eventData"].readIfPresent(with: DrsClientTypes.JobLogEventData.read(from:))
        return value
    }
}

extension DrsClientTypes.JobLogEventData {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.JobLogEventData {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.JobLogEventData()
        value.sourceServerID = try reader["sourceServerID"].readIfPresent()
        value.conversionServerID = try reader["conversionServerID"].readIfPresent()
        value.targetInstanceID = try reader["targetInstanceID"].readIfPresent()
        value.rawError = try reader["rawError"].readIfPresent()
        value.conversionProperties = try reader["conversionProperties"].readIfPresent(with: DrsClientTypes.ConversionProperties.read(from:))
        value.eventResourceData = try reader["eventResourceData"].readIfPresent(with: DrsClientTypes.EventResourceData.read(from:))
        return value
    }
}

extension DrsClientTypes.EventResourceData {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.EventResourceData {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        let name = reader.children.filter { $0.hasContent && $0.nodeInfo.name != "__type" }.first?.nodeInfo.name
        switch name {
            case "sourceNetworkData":
                return .sourcenetworkdata(try reader["sourceNetworkData"].read(with: DrsClientTypes.SourceNetworkData.read(from:)))
            default:
                return .sdkUnknown(name ?? "")
        }
    }
}

extension DrsClientTypes.SourceNetworkData {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.SourceNetworkData {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.SourceNetworkData()
        value.sourceNetworkID = try reader["sourceNetworkID"].readIfPresent()
        value.sourceVpc = try reader["sourceVpc"].readIfPresent()
        value.targetVpc = try reader["targetVpc"].readIfPresent()
        value.stackName = try reader["stackName"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.ConversionProperties {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.ConversionProperties {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.ConversionProperties()
        value.volumeToConversionMap = try reader["volumeToConversionMap"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.mapReadingClosure(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.rootVolumeName = try reader["rootVolumeName"].readIfPresent()
        value.forceUefi = try reader["forceUefi"].readIfPresent()
        value.dataTimestamp = try reader["dataTimestamp"].readIfPresent()
        value.volumeToVolumeSize = try reader["volumeToVolumeSize"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readInt(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.volumeToProductCodes = try reader["volumeToProductCodes"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.listReadingClosure(memberReadingClosure: DrsClientTypes.ProductCode.read(from:), memberNodeInfo: "member", isFlattened: false), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DrsClientTypes.ProductCode {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.ProductCode {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.ProductCode()
        value.productCodeId = try reader["productCodeId"].readIfPresent()
        value.productCodeMode = try reader["productCodeMode"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.RecoveryInstance {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.RecoveryInstance {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.RecoveryInstance()
        value.ec2InstanceID = try reader["ec2InstanceID"].readIfPresent()
        value.ec2InstanceState = try reader["ec2InstanceState"].readIfPresent()
        value.jobID = try reader["jobID"].readIfPresent()
        value.recoveryInstanceID = try reader["recoveryInstanceID"].readIfPresent()
        value.sourceServerID = try reader["sourceServerID"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.failback = try reader["failback"].readIfPresent(with: DrsClientTypes.RecoveryInstanceFailback.read(from:))
        value.dataReplicationInfo = try reader["dataReplicationInfo"].readIfPresent(with: DrsClientTypes.RecoveryInstanceDataReplicationInfo.read(from:))
        value.recoveryInstanceProperties = try reader["recoveryInstanceProperties"].readIfPresent(with: DrsClientTypes.RecoveryInstanceProperties.read(from:))
        value.pointInTimeSnapshotDateTime = try reader["pointInTimeSnapshotDateTime"].readIfPresent()
        value.isDrill = try reader["isDrill"].readIfPresent()
        value.originEnvironment = try reader["originEnvironment"].readIfPresent()
        value.originAvailabilityZone = try reader["originAvailabilityZone"].readIfPresent()
        value.agentVersion = try reader["agentVersion"].readIfPresent()
        value.sourceOutpostArn = try reader["sourceOutpostArn"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.RecoveryInstanceProperties {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.RecoveryInstanceProperties {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.RecoveryInstanceProperties()
        value.lastUpdatedDateTime = try reader["lastUpdatedDateTime"].readIfPresent()
        value.identificationHints = try reader["identificationHints"].readIfPresent(with: DrsClientTypes.IdentificationHints.read(from:))
        value.networkInterfaces = try reader["networkInterfaces"].readListIfPresent(memberReadingClosure: DrsClientTypes.NetworkInterface.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.disks = try reader["disks"].readListIfPresent(memberReadingClosure: DrsClientTypes.RecoveryInstanceDisk.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.cpus = try reader["cpus"].readListIfPresent(memberReadingClosure: DrsClientTypes.CPU.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.ramBytes = try reader["ramBytes"].readIfPresent() ?? 0
        value.os = try reader["os"].readIfPresent(with: DrsClientTypes.OS.read(from:))
        return value
    }
}

extension DrsClientTypes.RecoveryInstanceDisk {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.RecoveryInstanceDisk {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.RecoveryInstanceDisk()
        value.internalDeviceName = try reader["internalDeviceName"].readIfPresent()
        value.bytes = try reader["bytes"].readIfPresent() ?? 0
        value.ebsVolumeID = try reader["ebsVolumeID"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.RecoveryInstanceDataReplicationInfo {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.RecoveryInstanceDataReplicationInfo {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.RecoveryInstanceDataReplicationInfo()
        value.lagDuration = try reader["lagDuration"].readIfPresent()
        value.etaDateTime = try reader["etaDateTime"].readIfPresent()
        value.replicatedDisks = try reader["replicatedDisks"].readListIfPresent(memberReadingClosure: DrsClientTypes.RecoveryInstanceDataReplicationInfoReplicatedDisk.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.dataReplicationState = try reader["dataReplicationState"].readIfPresent()
        value.dataReplicationInitiation = try reader["dataReplicationInitiation"].readIfPresent(with: DrsClientTypes.RecoveryInstanceDataReplicationInitiation.read(from:))
        value.dataReplicationError = try reader["dataReplicationError"].readIfPresent(with: DrsClientTypes.RecoveryInstanceDataReplicationError.read(from:))
        value.stagingAvailabilityZone = try reader["stagingAvailabilityZone"].readIfPresent()
        value.stagingOutpostArn = try reader["stagingOutpostArn"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.RecoveryInstanceDataReplicationError {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.RecoveryInstanceDataReplicationError {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.RecoveryInstanceDataReplicationError()
        value.error = try reader["error"].readIfPresent()
        value.rawError = try reader["rawError"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.RecoveryInstanceDataReplicationInitiation {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.RecoveryInstanceDataReplicationInitiation {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.RecoveryInstanceDataReplicationInitiation()
        value.startDateTime = try reader["startDateTime"].readIfPresent()
        value.steps = try reader["steps"].readListIfPresent(memberReadingClosure: DrsClientTypes.RecoveryInstanceDataReplicationInitiationStep.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DrsClientTypes.RecoveryInstanceDataReplicationInitiationStep {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.RecoveryInstanceDataReplicationInitiationStep {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.RecoveryInstanceDataReplicationInitiationStep()
        value.name = try reader["name"].readIfPresent()
        value.status = try reader["status"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.RecoveryInstanceDataReplicationInfoReplicatedDisk {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.RecoveryInstanceDataReplicationInfoReplicatedDisk {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.RecoveryInstanceDataReplicationInfoReplicatedDisk()
        value.deviceName = try reader["deviceName"].readIfPresent()
        value.totalStorageBytes = try reader["totalStorageBytes"].readIfPresent() ?? 0
        value.replicatedStorageBytes = try reader["replicatedStorageBytes"].readIfPresent() ?? 0
        value.rescannedStorageBytes = try reader["rescannedStorageBytes"].readIfPresent() ?? 0
        value.backloggedStorageBytes = try reader["backloggedStorageBytes"].readIfPresent() ?? 0
        return value
    }
}

extension DrsClientTypes.RecoveryInstanceFailback {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.RecoveryInstanceFailback {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.RecoveryInstanceFailback()
        value.failbackClientID = try reader["failbackClientID"].readIfPresent()
        value.failbackJobID = try reader["failbackJobID"].readIfPresent()
        value.failbackInitiationTime = try reader["failbackInitiationTime"].readIfPresent()
        value.state = try reader["state"].readIfPresent()
        value.agentLastSeenByServiceDateTime = try reader["agentLastSeenByServiceDateTime"].readIfPresent()
        value.failbackClientLastSeenByServiceDateTime = try reader["failbackClientLastSeenByServiceDateTime"].readIfPresent()
        value.failbackToOriginalServer = try reader["failbackToOriginalServer"].readIfPresent()
        value.firstByteDateTime = try reader["firstByteDateTime"].readIfPresent()
        value.elapsedReplicationDuration = try reader["elapsedReplicationDuration"].readIfPresent()
        value.failbackLaunchType = try reader["failbackLaunchType"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.RecoverySnapshot {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.RecoverySnapshot {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.RecoverySnapshot()
        value.snapshotID = try reader["snapshotID"].readIfPresent() ?? ""
        value.sourceServerID = try reader["sourceServerID"].readIfPresent() ?? ""
        value.expectedTimestamp = try reader["expectedTimestamp"].readIfPresent() ?? ""
        value.timestamp = try reader["timestamp"].readIfPresent()
        value.ebsSnapshots = try reader["ebsSnapshots"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DrsClientTypes.ReplicationConfigurationTemplate {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.ReplicationConfigurationTemplate {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.ReplicationConfigurationTemplate()
        value.replicationConfigurationTemplateID = try reader["replicationConfigurationTemplateID"].readIfPresent() ?? ""
        value.arn = try reader["arn"].readIfPresent()
        value.stagingAreaSubnetId = try reader["stagingAreaSubnetId"].readIfPresent()
        value.associateDefaultSecurityGroup = try reader["associateDefaultSecurityGroup"].readIfPresent()
        value.replicationServersSecurityGroupsIDs = try reader["replicationServersSecurityGroupsIDs"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.replicationServerInstanceType = try reader["replicationServerInstanceType"].readIfPresent()
        value.useDedicatedReplicationServer = try reader["useDedicatedReplicationServer"].readIfPresent()
        value.defaultLargeStagingDiskType = try reader["defaultLargeStagingDiskType"].readIfPresent()
        value.ebsEncryption = try reader["ebsEncryption"].readIfPresent()
        value.ebsEncryptionKeyArn = try reader["ebsEncryptionKeyArn"].readIfPresent()
        value.bandwidthThrottling = try reader["bandwidthThrottling"].readIfPresent() ?? 0
        value.dataPlaneRouting = try reader["dataPlaneRouting"].readIfPresent()
        value.createPublicIP = try reader["createPublicIP"].readIfPresent()
        value.stagingAreaTags = try reader["stagingAreaTags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.pitPolicy = try reader["pitPolicy"].readListIfPresent(memberReadingClosure: DrsClientTypes.PITPolicyRule.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.autoReplicateNewDisks = try reader["autoReplicateNewDisks"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.SourceNetwork {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.SourceNetwork {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.SourceNetwork()
        value.sourceNetworkID = try reader["sourceNetworkID"].readIfPresent()
        value.sourceVpcID = try reader["sourceVpcID"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.replicationStatus = try reader["replicationStatus"].readIfPresent()
        value.replicationStatusDetails = try reader["replicationStatusDetails"].readIfPresent()
        value.cfnStackName = try reader["cfnStackName"].readIfPresent()
        value.sourceRegion = try reader["sourceRegion"].readIfPresent()
        value.sourceAccountID = try reader["sourceAccountID"].readIfPresent()
        value.lastRecovery = try reader["lastRecovery"].readIfPresent(with: DrsClientTypes.RecoveryLifeCycle.read(from:))
        value.launchedVpcID = try reader["launchedVpcID"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.RecoveryLifeCycle {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.RecoveryLifeCycle {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.RecoveryLifeCycle()
        value.apiCallDateTime = try reader["apiCallDateTime"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.dateTime)
        value.jobID = try reader["jobID"].readIfPresent()
        value.lastRecoveryResult = try reader["lastRecoveryResult"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.LaunchIntoInstanceProperties {

    static func write(value: DrsClientTypes.LaunchIntoInstanceProperties?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["launchIntoEC2InstanceID"].write(value.launchIntoEC2InstanceID)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.LaunchIntoInstanceProperties {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.LaunchIntoInstanceProperties()
        value.launchIntoEC2InstanceID = try reader["launchIntoEC2InstanceID"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.ReplicationConfigurationReplicatedDisk {

    static func write(value: DrsClientTypes.ReplicationConfigurationReplicatedDisk?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["deviceName"].write(value.deviceName)
        try writer["iops"].write(value.iops)
        try writer["isBootDisk"].write(value.isBootDisk)
        try writer["optimizedStagingDiskType"].write(value.optimizedStagingDiskType)
        try writer["stagingDiskType"].write(value.stagingDiskType)
        try writer["throughput"].write(value.throughput)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.ReplicationConfigurationReplicatedDisk {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.ReplicationConfigurationReplicatedDisk()
        value.deviceName = try reader["deviceName"].readIfPresent()
        value.isBootDisk = try reader["isBootDisk"].readIfPresent()
        value.stagingDiskType = try reader["stagingDiskType"].readIfPresent()
        value.iops = try reader["iops"].readIfPresent() ?? 0
        value.throughput = try reader["throughput"].readIfPresent() ?? 0
        value.optimizedStagingDiskType = try reader["optimizedStagingDiskType"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.StagingSourceServer {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.StagingSourceServer {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.StagingSourceServer()
        value.hostname = try reader["hostname"].readIfPresent()
        value.arn = try reader["arn"].readIfPresent()
        value.tags = try reader["tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DrsClientTypes.Account {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.Account {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.Account()
        value.accountID = try reader["accountID"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.ValidationExceptionField {

    static func read(from reader: SmithyJSON.Reader) throws -> DrsClientTypes.ValidationExceptionField {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = DrsClientTypes.ValidationExceptionField()
        value.name = try reader["name"].readIfPresent()
        value.message = try reader["message"].readIfPresent()
        return value
    }
}

extension DrsClientTypes.DescribeJobsRequestFilters {

    static func write(value: DrsClientTypes.DescribeJobsRequestFilters?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["fromDate"].write(value.fromDate)
        try writer["jobIDs"].writeList(value.jobIDs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["toDate"].write(value.toDate)
    }
}

extension DrsClientTypes.DescribeRecoveryInstancesRequestFilters {

    static func write(value: DrsClientTypes.DescribeRecoveryInstancesRequestFilters?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["recoveryInstanceIDs"].writeList(value.recoveryInstanceIDs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["sourceServerIDs"].writeList(value.sourceServerIDs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DrsClientTypes.DescribeRecoverySnapshotsRequestFilters {

    static func write(value: DrsClientTypes.DescribeRecoverySnapshotsRequestFilters?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["fromDateTime"].write(value.fromDateTime)
        try writer["toDateTime"].write(value.toDateTime)
    }
}

extension DrsClientTypes.DescribeSourceNetworksRequestFilters {

    static func write(value: DrsClientTypes.DescribeSourceNetworksRequestFilters?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["originAccountID"].write(value.originAccountID)
        try writer["originRegion"].write(value.originRegion)
        try writer["sourceNetworkIDs"].writeList(value.sourceNetworkIDs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DrsClientTypes.DescribeSourceServersRequestFilters {

    static func write(value: DrsClientTypes.DescribeSourceServersRequestFilters?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["hardwareId"].write(value.hardwareId)
        try writer["sourceServerIDs"].writeList(value.sourceServerIDs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["stagingAccountIDs"].writeList(value.stagingAccountIDs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DrsClientTypes.LaunchActionsRequestFilters {

    static func write(value: DrsClientTypes.LaunchActionsRequestFilters?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["actionIds"].writeList(value.actionIds, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DrsClientTypes.StartRecoveryRequestSourceServer {

    static func write(value: DrsClientTypes.StartRecoveryRequestSourceServer?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["recoverySnapshotID"].write(value.recoverySnapshotID)
        try writer["sourceServerID"].write(value.sourceServerID)
    }
}

extension DrsClientTypes.StartSourceNetworkRecoveryRequestNetworkEntry {

    static func write(value: DrsClientTypes.StartSourceNetworkRecoveryRequestNetworkEntry?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["cfnStackName"].write(value.cfnStackName)
        try writer["sourceNetworkID"].write(value.sourceNetworkID)
    }
}

public enum DrsClientTypes {}
