//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

@_spi(SmithyReadWrite) import ClientRuntime
import Foundation
import class SmithyHT<PERSON>API.HTTPResponse
@_spi(SmithyReadWrite) import class SmithyJSON.Reader
@_spi(SmithyReadWrite) import class SmithyJSON.Writer
import enum ClientRuntime.ErrorFault
import enum SmithyReadWrite.ReaderError
@_spi(SmithyReadWrite) import enum SmithyReadWrite.ReadingClosures
@_spi(SmithyReadWrite) import enum SmithyReadWrite.WritingClosures
@_spi(SmithyTimestamps) import enum SmithyTimestamps.TimestampFormat
import protocol AWSClientRuntime.AWSServiceError
import protocol ClientRuntime.HTTPError
import protocol ClientRuntime.ModeledError
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyReader
@_spi(SmithyReadWrite) import protocol SmithyReadWrite.SmithyWriter
@_spi(SmithyReadWrite) import struct AWSClientRuntime.AWSJSONError
@_spi(UnknownAWSHTTPServiceError) import struct AWSClientRuntime.UnknownAWSHTTPServiceError


public struct DeleteIdentityPoolOutput: Swift.Sendable {

    public init() { }
}

public struct SetIdentityPoolRolesOutput: Swift.Sendable {

    public init() { }
}

public struct UnlinkDeveloperIdentityOutput: Swift.Sendable {

    public init() { }
}

public struct UnlinkIdentityOutput: Swift.Sendable {

    public init() { }
}

extension CognitoIdentityClientTypes {

    public enum AmbiguousRoleResolutionType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case authenticatedRole
        case deny
        case sdkUnknown(Swift.String)

        public static var allCases: [AmbiguousRoleResolutionType] {
            return [
                .authenticatedRole,
                .deny
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .authenticatedRole: return "AuthenticatedRole"
            case .deny: return "Deny"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

/// Thrown when the service encounters an error during processing the request.
public struct InternalErrorException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message returned by an InternalErrorException.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InternalErrorException" }
    public static var fault: ClientRuntime.ErrorFault { .server }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Thrown for missing or bad input parameter(s).
public struct InvalidParameterException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message returned by an InvalidParameterException.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidParameterException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Thrown when the total number of user pools has exceeded a preset limit.
public struct LimitExceededException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message returned by a LimitExceededException.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "LimitExceededException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Thrown when a user is not authorized to access the requested resource.
public struct NotAuthorizedException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message returned by a NotAuthorizedException
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "NotAuthorizedException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Thrown when a user tries to use a login which is already linked to another account.
public struct ResourceConflictException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message returned by a ResourceConflictException.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceConflictException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Thrown when a request is throttled.
public struct TooManyRequestsException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// Message returned by a TooManyRequestsException
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "TooManyRequestsException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

extension CognitoIdentityClientTypes {

    /// A provider representing an Amazon Cognito user pool and its client ID.
    public struct CognitoIdentityProvider: Swift.Sendable {
        /// The client ID for the Amazon Cognito user pool.
        public var clientId: Swift.String?
        /// The provider name for an Amazon Cognito user pool. For example, cognito-idp.us-east-1.amazonaws.com/us-east-1_123456789.
        public var providerName: Swift.String?
        /// TRUE if server-side token validation is enabled for the identity provider’s token. Once you set ServerSideTokenCheck to TRUE for an identity pool, that identity pool will check with the integrated user pools to make sure that the user has not been globally signed out or deleted before the identity pool provides an OIDC token or AWS credentials for the user. If the user is signed out or deleted, the identity pool will return a 400 Not Authorized error.
        public var serverSideTokenCheck: Swift.Bool?

        public init(
            clientId: Swift.String? = nil,
            providerName: Swift.String? = nil,
            serverSideTokenCheck: Swift.Bool? = false
        )
        {
            self.clientId = clientId
            self.providerName = providerName
            self.serverSideTokenCheck = serverSideTokenCheck
        }
    }
}

/// Input to the CreateIdentityPool action.
public struct CreateIdentityPoolInput: Swift.Sendable {
    /// Enables or disables the Basic (Classic) authentication flow. For more information, see [Identity Pools (Federated Identities) Authentication Flow](https://docs.aws.amazon.com/cognito/latest/developerguide/authentication-flow.html) in the Amazon Cognito Developer Guide.
    public var allowClassicFlow: Swift.Bool?
    /// TRUE if the identity pool supports unauthenticated logins.
    /// This member is required.
    public var allowUnauthenticatedIdentities: Swift.Bool?
    /// An array of Amazon Cognito user pools and their client IDs.
    public var cognitoIdentityProviders: [CognitoIdentityClientTypes.CognitoIdentityProvider]?
    /// The "domain" by which Cognito will refer to your users. This name acts as a placeholder that allows your backend and the Cognito service to communicate about the developer provider. For the DeveloperProviderName, you can use letters as well as period (.), underscore (_), and dash (-). Once you have set a developer provider name, you cannot change it. Please take care in setting this parameter.
    public var developerProviderName: Swift.String?
    /// A string that you provide.
    /// This member is required.
    public var identityPoolName: Swift.String?
    /// Tags to assign to the identity pool. A tag is a label that you can apply to identity pools to categorize and manage them in different ways, such as by purpose, owner, environment, or other criteria.
    public var identityPoolTags: [Swift.String: Swift.String]?
    /// The Amazon Resource Names (ARN) of the OpenID Connect providers.
    public var openIdConnectProviderARNs: [Swift.String]?
    /// An array of Amazon Resource Names (ARNs) of the SAML provider for your identity pool.
    public var samlProviderARNs: [Swift.String]?
    /// Optional key:value pairs mapping provider names to provider app IDs.
    public var supportedLoginProviders: [Swift.String: Swift.String]?

    public init(
        allowClassicFlow: Swift.Bool? = nil,
        allowUnauthenticatedIdentities: Swift.Bool? = false,
        cognitoIdentityProviders: [CognitoIdentityClientTypes.CognitoIdentityProvider]? = nil,
        developerProviderName: Swift.String? = nil,
        identityPoolName: Swift.String? = nil,
        identityPoolTags: [Swift.String: Swift.String]? = nil,
        openIdConnectProviderARNs: [Swift.String]? = nil,
        samlProviderARNs: [Swift.String]? = nil,
        supportedLoginProviders: [Swift.String: Swift.String]? = nil
    )
    {
        self.allowClassicFlow = allowClassicFlow
        self.allowUnauthenticatedIdentities = allowUnauthenticatedIdentities
        self.cognitoIdentityProviders = cognitoIdentityProviders
        self.developerProviderName = developerProviderName
        self.identityPoolName = identityPoolName
        self.identityPoolTags = identityPoolTags
        self.openIdConnectProviderARNs = openIdConnectProviderARNs
        self.samlProviderARNs = samlProviderARNs
        self.supportedLoginProviders = supportedLoginProviders
    }
}

/// An object representing an Amazon Cognito identity pool.
public struct CreateIdentityPoolOutput: Swift.Sendable {
    /// Enables or disables the Basic (Classic) authentication flow. For more information, see [Identity Pools (Federated Identities) Authentication Flow](https://docs.aws.amazon.com/cognito/latest/developerguide/authentication-flow.html) in the Amazon Cognito Developer Guide.
    public var allowClassicFlow: Swift.Bool?
    /// TRUE if the identity pool supports unauthenticated logins.
    /// This member is required.
    public var allowUnauthenticatedIdentities: Swift.Bool
    /// A list representing an Amazon Cognito user pool and its client ID.
    public var cognitoIdentityProviders: [CognitoIdentityClientTypes.CognitoIdentityProvider]?
    /// The "domain" by which Cognito will refer to your users.
    public var developerProviderName: Swift.String?
    /// An identity pool ID in the format REGION:GUID.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// A string that you provide.
    /// This member is required.
    public var identityPoolName: Swift.String?
    /// The tags that are assigned to the identity pool. A tag is a label that you can apply to identity pools to categorize and manage them in different ways, such as by purpose, owner, environment, or other criteria.
    public var identityPoolTags: [Swift.String: Swift.String]?
    /// The ARNs of the OpenID Connect providers.
    public var openIdConnectProviderARNs: [Swift.String]?
    /// An array of Amazon Resource Names (ARNs) of the SAML provider for your identity pool.
    public var samlProviderARNs: [Swift.String]?
    /// Optional key:value pairs mapping provider names to provider app IDs.
    public var supportedLoginProviders: [Swift.String: Swift.String]?

    public init(
        allowClassicFlow: Swift.Bool? = nil,
        allowUnauthenticatedIdentities: Swift.Bool = false,
        cognitoIdentityProviders: [CognitoIdentityClientTypes.CognitoIdentityProvider]? = nil,
        developerProviderName: Swift.String? = nil,
        identityPoolId: Swift.String? = nil,
        identityPoolName: Swift.String? = nil,
        identityPoolTags: [Swift.String: Swift.String]? = nil,
        openIdConnectProviderARNs: [Swift.String]? = nil,
        samlProviderARNs: [Swift.String]? = nil,
        supportedLoginProviders: [Swift.String: Swift.String]? = nil
    )
    {
        self.allowClassicFlow = allowClassicFlow
        self.allowUnauthenticatedIdentities = allowUnauthenticatedIdentities
        self.cognitoIdentityProviders = cognitoIdentityProviders
        self.developerProviderName = developerProviderName
        self.identityPoolId = identityPoolId
        self.identityPoolName = identityPoolName
        self.identityPoolTags = identityPoolTags
        self.openIdConnectProviderARNs = openIdConnectProviderARNs
        self.samlProviderARNs = samlProviderARNs
        self.supportedLoginProviders = supportedLoginProviders
    }
}

/// Input to the DeleteIdentities action.
public struct DeleteIdentitiesInput: Swift.Sendable {
    /// A list of 1-60 identities that you want to delete.
    /// This member is required.
    public var identityIdsToDelete: [Swift.String]?

    public init(
        identityIdsToDelete: [Swift.String]? = nil
    )
    {
        self.identityIdsToDelete = identityIdsToDelete
    }
}

extension CognitoIdentityClientTypes {

    public enum ErrorCode: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case accessDenied
        case internalServerError
        case sdkUnknown(Swift.String)

        public static var allCases: [ErrorCode] {
            return [
                .accessDenied,
                .internalServerError
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .accessDenied: return "AccessDenied"
            case .internalServerError: return "InternalServerError"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CognitoIdentityClientTypes {

    /// An array of UnprocessedIdentityId objects, each of which contains an ErrorCode and IdentityId.
    public struct UnprocessedIdentityId: Swift.Sendable {
        /// The error code indicating the type of error that occurred.
        public var errorCode: CognitoIdentityClientTypes.ErrorCode?
        /// A unique identifier in the format REGION:GUID.
        public var identityId: Swift.String?

        public init(
            errorCode: CognitoIdentityClientTypes.ErrorCode? = nil,
            identityId: Swift.String? = nil
        )
        {
            self.errorCode = errorCode
            self.identityId = identityId
        }
    }
}

/// Returned in response to a successful DeleteIdentities operation.
public struct DeleteIdentitiesOutput: Swift.Sendable {
    /// An array of UnprocessedIdentityId objects, each of which contains an ErrorCode and IdentityId.
    public var unprocessedIdentityIds: [CognitoIdentityClientTypes.UnprocessedIdentityId]?

    public init(
        unprocessedIdentityIds: [CognitoIdentityClientTypes.UnprocessedIdentityId]? = nil
    )
    {
        self.unprocessedIdentityIds = unprocessedIdentityIds
    }
}

/// Thrown when the requested resource (for example, a dataset or record) does not exist.
public struct ResourceNotFoundException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message returned by a ResourceNotFoundException.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ResourceNotFoundException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Input to the DeleteIdentityPool action.
public struct DeleteIdentityPoolInput: Swift.Sendable {
    /// An identity pool ID in the format REGION:GUID.
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        identityPoolId: Swift.String? = nil
    )
    {
        self.identityPoolId = identityPoolId
    }
}

/// Input to the DescribeIdentity action.
public struct DescribeIdentityInput: Swift.Sendable {
    /// A unique identifier in the format REGION:GUID.
    /// This member is required.
    public var identityId: Swift.String?

    public init(
        identityId: Swift.String? = nil
    )
    {
        self.identityId = identityId
    }
}

/// A description of the identity.
public struct DescribeIdentityOutput: Swift.Sendable {
    /// Date on which the identity was created.
    public var creationDate: Foundation.Date?
    /// A unique identifier in the format REGION:GUID.
    public var identityId: Swift.String?
    /// Date on which the identity was last modified.
    public var lastModifiedDate: Foundation.Date?
    /// The provider names.
    public var logins: [Swift.String]?

    public init(
        creationDate: Foundation.Date? = nil,
        identityId: Swift.String? = nil,
        lastModifiedDate: Foundation.Date? = nil,
        logins: [Swift.String]? = nil
    )
    {
        self.creationDate = creationDate
        self.identityId = identityId
        self.lastModifiedDate = lastModifiedDate
        self.logins = logins
    }
}

/// Input to the DescribeIdentityPool action.
public struct DescribeIdentityPoolInput: Swift.Sendable {
    /// An identity pool ID in the format REGION:GUID.
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        identityPoolId: Swift.String? = nil
    )
    {
        self.identityPoolId = identityPoolId
    }
}

/// An object representing an Amazon Cognito identity pool.
public struct DescribeIdentityPoolOutput: Swift.Sendable {
    /// Enables or disables the Basic (Classic) authentication flow. For more information, see [Identity Pools (Federated Identities) Authentication Flow](https://docs.aws.amazon.com/cognito/latest/developerguide/authentication-flow.html) in the Amazon Cognito Developer Guide.
    public var allowClassicFlow: Swift.Bool?
    /// TRUE if the identity pool supports unauthenticated logins.
    /// This member is required.
    public var allowUnauthenticatedIdentities: Swift.Bool
    /// A list representing an Amazon Cognito user pool and its client ID.
    public var cognitoIdentityProviders: [CognitoIdentityClientTypes.CognitoIdentityProvider]?
    /// The "domain" by which Cognito will refer to your users.
    public var developerProviderName: Swift.String?
    /// An identity pool ID in the format REGION:GUID.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// A string that you provide.
    /// This member is required.
    public var identityPoolName: Swift.String?
    /// The tags that are assigned to the identity pool. A tag is a label that you can apply to identity pools to categorize and manage them in different ways, such as by purpose, owner, environment, or other criteria.
    public var identityPoolTags: [Swift.String: Swift.String]?
    /// The ARNs of the OpenID Connect providers.
    public var openIdConnectProviderARNs: [Swift.String]?
    /// An array of Amazon Resource Names (ARNs) of the SAML provider for your identity pool.
    public var samlProviderARNs: [Swift.String]?
    /// Optional key:value pairs mapping provider names to provider app IDs.
    public var supportedLoginProviders: [Swift.String: Swift.String]?

    public init(
        allowClassicFlow: Swift.Bool? = nil,
        allowUnauthenticatedIdentities: Swift.Bool = false,
        cognitoIdentityProviders: [CognitoIdentityClientTypes.CognitoIdentityProvider]? = nil,
        developerProviderName: Swift.String? = nil,
        identityPoolId: Swift.String? = nil,
        identityPoolName: Swift.String? = nil,
        identityPoolTags: [Swift.String: Swift.String]? = nil,
        openIdConnectProviderARNs: [Swift.String]? = nil,
        samlProviderARNs: [Swift.String]? = nil,
        supportedLoginProviders: [Swift.String: Swift.String]? = nil
    )
    {
        self.allowClassicFlow = allowClassicFlow
        self.allowUnauthenticatedIdentities = allowUnauthenticatedIdentities
        self.cognitoIdentityProviders = cognitoIdentityProviders
        self.developerProviderName = developerProviderName
        self.identityPoolId = identityPoolId
        self.identityPoolName = identityPoolName
        self.identityPoolTags = identityPoolTags
        self.openIdConnectProviderARNs = openIdConnectProviderARNs
        self.samlProviderARNs = samlProviderARNs
        self.supportedLoginProviders = supportedLoginProviders
    }
}

/// An exception thrown when a dependent service such as Facebook or Twitter is not responding
public struct ExternalServiceException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message returned by an ExternalServiceException
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ExternalServiceException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Thrown if the identity pool has no role associated for the given auth type (auth/unauth) or if the AssumeRole fails.
public struct InvalidIdentityPoolConfigurationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message returned for an InvalidIdentityPoolConfigurationException
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "InvalidIdentityPoolConfigurationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Input to the GetCredentialsForIdentity action.
public struct GetCredentialsForIdentityInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the role to be assumed when multiple roles were received in the token from the identity provider. For example, a SAML-based identity provider. This parameter is optional for identity providers that do not support role customization.
    public var customRoleArn: Swift.String?
    /// A unique identifier in the format REGION:GUID.
    /// This member is required.
    public var identityId: Swift.String?
    /// A set of optional name-value pairs that map provider names to provider tokens. The name-value pair will follow the syntax "provider_name": "provider_user_identifier". Logins should not be specified when trying to get credentials for an unauthenticated identity. The Logins parameter is required when using identities associated with external identity providers such as Facebook. For examples of Logins maps, see the code examples in the [External Identity Providers](https://docs.aws.amazon.com/cognito/latest/developerguide/external-identity-providers.html) section of the Amazon Cognito Developer Guide.
    public var logins: [Swift.String: Swift.String]?

    public init(
        customRoleArn: Swift.String? = nil,
        identityId: Swift.String? = nil,
        logins: [Swift.String: Swift.String]? = nil
    )
    {
        self.customRoleArn = customRoleArn
        self.identityId = identityId
        self.logins = logins
    }
}

extension GetCredentialsForIdentityInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "GetCredentialsForIdentityInput(customRoleArn: \(Swift.String(describing: customRoleArn)), identityId: \(Swift.String(describing: identityId)), logins: [keys: \(Swift.String(describing: logins?.keys)), values: \"CONTENT_REDACTED\"])"}
}

extension CognitoIdentityClientTypes {

    /// Credentials for the provided identity ID.
    public struct Credentials: Swift.Sendable {
        /// The Access Key portion of the credentials.
        public var accessKeyId: Swift.String?
        /// The date at which these credentials will expire.
        public var expiration: Foundation.Date?
        /// The Secret Access Key portion of the credentials
        public var secretKey: Swift.String?
        /// The Session Token portion of the credentials
        public var sessionToken: Swift.String?

        public init(
            accessKeyId: Swift.String? = nil,
            expiration: Foundation.Date? = nil,
            secretKey: Swift.String? = nil,
            sessionToken: Swift.String? = nil
        )
        {
            self.accessKeyId = accessKeyId
            self.expiration = expiration
            self.secretKey = secretKey
            self.sessionToken = sessionToken
        }
    }
}

extension CognitoIdentityClientTypes.Credentials: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "Credentials(accessKeyId: \(Swift.String(describing: accessKeyId)), expiration: \(Swift.String(describing: expiration)), sessionToken: \(Swift.String(describing: sessionToken)), secretKey: \"CONTENT_REDACTED\")"}
}

/// Returned in response to a successful GetCredentialsForIdentity operation.
public struct GetCredentialsForIdentityOutput: Swift.Sendable {
    /// Credentials for the provided identity ID.
    public var credentials: CognitoIdentityClientTypes.Credentials?
    /// A unique identifier in the format REGION:GUID.
    public var identityId: Swift.String?

    public init(
        credentials: CognitoIdentityClientTypes.Credentials? = nil,
        identityId: Swift.String? = nil
    )
    {
        self.credentials = credentials
        self.identityId = identityId
    }
}

/// Input to the GetId action.
public struct GetIdInput: Swift.Sendable {
    /// A standard AWS account ID (9+ digits).
    public var accountId: Swift.String?
    /// An identity pool ID in the format REGION:GUID.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// A set of optional name-value pairs that map provider names to provider tokens. The available provider names for Logins are as follows:
    ///
    /// * Facebook: graph.facebook.com
    ///
    /// * Amazon Cognito user pool: cognito-idp..amazonaws.com/, for example, cognito-idp.us-east-1.amazonaws.com/us-east-1_123456789.
    ///
    /// * Google: accounts.google.com
    ///
    /// * Amazon: www.amazon.com
    ///
    /// * Twitter: api.twitter.com
    ///
    /// * Digits: www.digits.com
    public var logins: [Swift.String: Swift.String]?

    public init(
        accountId: Swift.String? = nil,
        identityPoolId: Swift.String? = nil,
        logins: [Swift.String: Swift.String]? = nil
    )
    {
        self.accountId = accountId
        self.identityPoolId = identityPoolId
        self.logins = logins
    }
}

extension GetIdInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "GetIdInput(accountId: \(Swift.String(describing: accountId)), identityPoolId: \(Swift.String(describing: identityPoolId)), logins: [keys: \(Swift.String(describing: logins?.keys)), values: \"CONTENT_REDACTED\"])"}
}

/// Returned in response to a GetId request.
public struct GetIdOutput: Swift.Sendable {
    /// A unique identifier in the format REGION:GUID.
    public var identityId: Swift.String?

    public init(
        identityId: Swift.String? = nil
    )
    {
        self.identityId = identityId
    }
}

/// Input to the GetIdentityPoolRoles action.
public struct GetIdentityPoolRolesInput: Swift.Sendable {
    /// An identity pool ID in the format REGION:GUID.
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        identityPoolId: Swift.String? = nil
    )
    {
        self.identityPoolId = identityPoolId
    }
}

extension CognitoIdentityClientTypes {

    public enum MappingRuleMatchType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case contains
        case equals
        case notEqual
        case startsWith
        case sdkUnknown(Swift.String)

        public static var allCases: [MappingRuleMatchType] {
            return [
                .contains,
                .equals,
                .notEqual,
                .startsWith
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .contains: return "Contains"
            case .equals: return "Equals"
            case .notEqual: return "NotEqual"
            case .startsWith: return "StartsWith"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CognitoIdentityClientTypes {

    /// A rule that maps a claim name, a claim value, and a match type to a role ARN.
    public struct MappingRule: Swift.Sendable {
        /// The claim name that must be present in the token, for example, "isAdmin" or "paid".
        /// This member is required.
        public var claim: Swift.String?
        /// The match condition that specifies how closely the claim value in the IdP token must match Value.
        /// This member is required.
        public var matchType: CognitoIdentityClientTypes.MappingRuleMatchType?
        /// The role ARN.
        /// This member is required.
        public var roleARN: Swift.String?
        /// A brief string that the claim must match, for example, "paid" or "yes".
        /// This member is required.
        public var value: Swift.String?

        public init(
            claim: Swift.String? = nil,
            matchType: CognitoIdentityClientTypes.MappingRuleMatchType? = nil,
            roleARN: Swift.String? = nil,
            value: Swift.String? = nil
        )
        {
            self.claim = claim
            self.matchType = matchType
            self.roleARN = roleARN
            self.value = value
        }
    }
}

extension CognitoIdentityClientTypes {

    /// A container for rules.
    public struct RulesConfigurationType: Swift.Sendable {
        /// An array of rules. You can specify up to 25 rules per identity provider. Rules are evaluated in order. The first one to match specifies the role.
        /// This member is required.
        public var rules: [CognitoIdentityClientTypes.MappingRule]?

        public init(
            rules: [CognitoIdentityClientTypes.MappingRule]? = nil
        )
        {
            self.rules = rules
        }
    }
}

extension CognitoIdentityClientTypes {

    public enum RoleMappingType: Swift.Sendable, Swift.Equatable, Swift.RawRepresentable, Swift.CaseIterable, Swift.Hashable {
        case rules
        case token
        case sdkUnknown(Swift.String)

        public static var allCases: [RoleMappingType] {
            return [
                .rules,
                .token
            ]
        }

        public init?(rawValue: Swift.String) {
            let value = Self.allCases.first(where: { $0.rawValue == rawValue })
            self = value ?? Self.sdkUnknown(rawValue)
        }

        public var rawValue: Swift.String {
            switch self {
            case .rules: return "Rules"
            case .token: return "Token"
            case let .sdkUnknown(s): return s
            }
        }
    }
}

extension CognitoIdentityClientTypes {

    /// A role mapping.
    public struct RoleMapping: Swift.Sendable {
        /// If you specify Token or Rules as the Type, AmbiguousRoleResolution is required. Specifies the action to be taken if either no rules match the claim value for the Rules type, or there is no cognito:preferred_role claim and there are multiple cognito:roles matches for the Token type.
        public var ambiguousRoleResolution: CognitoIdentityClientTypes.AmbiguousRoleResolutionType?
        /// The rules to be used for mapping users to roles. If you specify Rules as the role mapping type, RulesConfiguration is required.
        public var rulesConfiguration: CognitoIdentityClientTypes.RulesConfigurationType?
        /// The role mapping type. Token will use cognito:roles and cognito:preferred_role claims from the Cognito identity provider token to map groups to roles. Rules will attempt to match claims from the token to map to a role.
        /// This member is required.
        public var type: CognitoIdentityClientTypes.RoleMappingType?

        public init(
            ambiguousRoleResolution: CognitoIdentityClientTypes.AmbiguousRoleResolutionType? = nil,
            rulesConfiguration: CognitoIdentityClientTypes.RulesConfigurationType? = nil,
            type: CognitoIdentityClientTypes.RoleMappingType? = nil
        )
        {
            self.ambiguousRoleResolution = ambiguousRoleResolution
            self.rulesConfiguration = rulesConfiguration
            self.type = type
        }
    }
}

/// Returned in response to a successful GetIdentityPoolRoles operation.
public struct GetIdentityPoolRolesOutput: Swift.Sendable {
    /// An identity pool ID in the format REGION:GUID.
    public var identityPoolId: Swift.String?
    /// How users for a specific identity provider are to mapped to roles. This is a String-to-[RoleMapping] object map. The string identifies the identity provider, for example, "graph.facebook.com" or "cognito-idp.us-east-1.amazonaws.com/us-east-1_abcdefghi:app_client_id".
    public var roleMappings: [Swift.String: CognitoIdentityClientTypes.RoleMapping]?
    /// The map of roles associated with this pool. Currently only authenticated and unauthenticated roles are supported.
    public var roles: [Swift.String: Swift.String]?

    public init(
        identityPoolId: Swift.String? = nil,
        roleMappings: [Swift.String: CognitoIdentityClientTypes.RoleMapping]? = nil,
        roles: [Swift.String: Swift.String]? = nil
    )
    {
        self.identityPoolId = identityPoolId
        self.roleMappings = roleMappings
        self.roles = roles
    }
}

/// Input to the GetOpenIdToken action.
public struct GetOpenIdTokenInput: Swift.Sendable {
    /// A unique identifier in the format REGION:GUID.
    /// This member is required.
    public var identityId: Swift.String?
    /// A set of optional name-value pairs that map provider names to provider tokens. When using graph.facebook.com and www.amazon.com, supply the access_token returned from the provider's authflow. For accounts.google.com, an Amazon Cognito user pool provider, or any other OpenID Connect provider, always include the id_token.
    public var logins: [Swift.String: Swift.String]?

    public init(
        identityId: Swift.String? = nil,
        logins: [Swift.String: Swift.String]? = nil
    )
    {
        self.identityId = identityId
        self.logins = logins
    }
}

extension GetOpenIdTokenInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "GetOpenIdTokenInput(identityId: \(Swift.String(describing: identityId)), logins: [keys: \(Swift.String(describing: logins?.keys)), values: \"CONTENT_REDACTED\"])"}
}

/// Returned in response to a successful GetOpenIdToken request.
public struct GetOpenIdTokenOutput: Swift.Sendable {
    /// A unique identifier in the format REGION:GUID. Note that the IdentityId returned may not match the one passed on input.
    public var identityId: Swift.String?
    /// An OpenID token, valid for 10 minutes.
    public var token: Swift.String?

    public init(
        identityId: Swift.String? = nil,
        token: Swift.String? = nil
    )
    {
        self.identityId = identityId
        self.token = token
    }
}

extension GetOpenIdTokenOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "GetOpenIdTokenOutput(identityId: \(Swift.String(describing: identityId)), token: \"CONTENT_REDACTED\")"}
}

/// The provided developer user identifier is already registered with Cognito under a different identity ID.
public struct DeveloperUserAlreadyRegisteredException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// This developer user identifier is already registered with Cognito.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "DeveloperUserAlreadyRegisteredException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Input to the GetOpenIdTokenForDeveloperIdentity action.
public struct GetOpenIdTokenForDeveloperIdentityInput: Swift.Sendable {
    /// A unique identifier in the format REGION:GUID.
    public var identityId: Swift.String?
    /// An identity pool ID in the format REGION:GUID.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// A set of optional name-value pairs that map provider names to provider tokens. Each name-value pair represents a user from a public provider or developer provider. If the user is from a developer provider, the name-value pair will follow the syntax "developer_provider_name": "developer_user_identifier". The developer provider is the "domain" by which Cognito will refer to your users; you provided this domain while creating/updating the identity pool. The developer user identifier is an identifier from your backend that uniquely identifies a user. When you create an identity pool, you can specify the supported logins.
    /// This member is required.
    public var logins: [Swift.String: Swift.String]?
    /// Use this operation to configure attribute mappings for custom providers.
    public var principalTags: [Swift.String: Swift.String]?
    /// The expiration time of the token, in seconds. You can specify a custom expiration time for the token so that you can cache it. If you don't provide an expiration time, the token is valid for 15 minutes. You can exchange the token with Amazon STS for temporary AWS credentials, which are valid for a maximum of one hour. The maximum token duration you can set is 24 hours. You should take care in setting the expiration time for a token, as there are significant security implications: an attacker could use a leaked token to access your AWS resources for the token's duration. Please provide for a small grace period, usually no more than 5 minutes, to account for clock skew.
    public var tokenDuration: Swift.Int?

    public init(
        identityId: Swift.String? = nil,
        identityPoolId: Swift.String? = nil,
        logins: [Swift.String: Swift.String]? = nil,
        principalTags: [Swift.String: Swift.String]? = nil,
        tokenDuration: Swift.Int? = nil
    )
    {
        self.identityId = identityId
        self.identityPoolId = identityPoolId
        self.logins = logins
        self.principalTags = principalTags
        self.tokenDuration = tokenDuration
    }
}

extension GetOpenIdTokenForDeveloperIdentityInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "GetOpenIdTokenForDeveloperIdentityInput(identityId: \(Swift.String(describing: identityId)), identityPoolId: \(Swift.String(describing: identityPoolId)), principalTags: \(Swift.String(describing: principalTags)), tokenDuration: \(Swift.String(describing: tokenDuration)), logins: [keys: \(Swift.String(describing: logins?.keys)), values: \"CONTENT_REDACTED\"])"}
}

/// Returned in response to a successful GetOpenIdTokenForDeveloperIdentity request.
public struct GetOpenIdTokenForDeveloperIdentityOutput: Swift.Sendable {
    /// A unique identifier in the format REGION:GUID.
    public var identityId: Swift.String?
    /// An OpenID token.
    public var token: Swift.String?

    public init(
        identityId: Swift.String? = nil,
        token: Swift.String? = nil
    )
    {
        self.identityId = identityId
        self.token = token
    }
}

extension GetOpenIdTokenForDeveloperIdentityOutput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "GetOpenIdTokenForDeveloperIdentityOutput(identityId: \(Swift.String(describing: identityId)), token: \"CONTENT_REDACTED\")"}
}

public struct GetPrincipalTagAttributeMapInput: Swift.Sendable {
    /// You can use this operation to get the ID of the Identity Pool you setup attribute mappings for.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// You can use this operation to get the provider name.
    /// This member is required.
    public var identityProviderName: Swift.String?

    public init(
        identityPoolId: Swift.String? = nil,
        identityProviderName: Swift.String? = nil
    )
    {
        self.identityPoolId = identityPoolId
        self.identityProviderName = identityProviderName
    }
}

public struct GetPrincipalTagAttributeMapOutput: Swift.Sendable {
    /// You can use this operation to get the ID of the Identity Pool you setup attribute mappings for.
    public var identityPoolId: Swift.String?
    /// You can use this operation to get the provider name.
    public var identityProviderName: Swift.String?
    /// You can use this operation to add principal tags. The PrincipalTagsoperation enables you to reference user attributes in your IAM permissions policy.
    public var principalTags: [Swift.String: Swift.String]?
    /// You can use this operation to list
    public var useDefaults: Swift.Bool?

    public init(
        identityPoolId: Swift.String? = nil,
        identityProviderName: Swift.String? = nil,
        principalTags: [Swift.String: Swift.String]? = nil,
        useDefaults: Swift.Bool? = nil
    )
    {
        self.identityPoolId = identityPoolId
        self.identityProviderName = identityProviderName
        self.principalTags = principalTags
        self.useDefaults = useDefaults
    }
}

/// Input to the ListIdentities action.
public struct ListIdentitiesInput: Swift.Sendable {
    /// An optional boolean parameter that allows you to hide disabled identities. If omitted, the ListIdentities API will include disabled identities in the response.
    public var hideDisabled: Swift.Bool?
    /// An identity pool ID in the format REGION:GUID.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// The maximum number of identities to return.
    /// This member is required.
    public var maxResults: Swift.Int?
    /// A pagination token.
    public var nextToken: Swift.String?

    public init(
        hideDisabled: Swift.Bool? = false,
        identityPoolId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.hideDisabled = hideDisabled
        self.identityPoolId = identityPoolId
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension CognitoIdentityClientTypes {

    /// A description of the identity.
    public struct IdentityDescription: Swift.Sendable {
        /// Date on which the identity was created.
        public var creationDate: Foundation.Date?
        /// A unique identifier in the format REGION:GUID.
        public var identityId: Swift.String?
        /// Date on which the identity was last modified.
        public var lastModifiedDate: Foundation.Date?
        /// The provider names.
        public var logins: [Swift.String]?

        public init(
            creationDate: Foundation.Date? = nil,
            identityId: Swift.String? = nil,
            lastModifiedDate: Foundation.Date? = nil,
            logins: [Swift.String]? = nil
        )
        {
            self.creationDate = creationDate
            self.identityId = identityId
            self.lastModifiedDate = lastModifiedDate
            self.logins = logins
        }
    }
}

/// The response to a ListIdentities request.
public struct ListIdentitiesOutput: Swift.Sendable {
    /// An object containing a set of identities and associated mappings.
    public var identities: [CognitoIdentityClientTypes.IdentityDescription]?
    /// An identity pool ID in the format REGION:GUID.
    public var identityPoolId: Swift.String?
    /// A pagination token.
    public var nextToken: Swift.String?

    public init(
        identities: [CognitoIdentityClientTypes.IdentityDescription]? = nil,
        identityPoolId: Swift.String? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.identities = identities
        self.identityPoolId = identityPoolId
        self.nextToken = nextToken
    }
}

/// Input to the ListIdentityPools action.
public struct ListIdentityPoolsInput: Swift.Sendable {
    /// The maximum number of identities to return.
    /// This member is required.
    public var maxResults: Swift.Int?
    /// A pagination token.
    public var nextToken: Swift.String?

    public init(
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

extension CognitoIdentityClientTypes {

    /// A description of the identity pool.
    public struct IdentityPoolShortDescription: Swift.Sendable {
        /// An identity pool ID in the format REGION:GUID.
        public var identityPoolId: Swift.String?
        /// A string that you provide.
        public var identityPoolName: Swift.String?

        public init(
            identityPoolId: Swift.String? = nil,
            identityPoolName: Swift.String? = nil
        )
        {
            self.identityPoolId = identityPoolId
            self.identityPoolName = identityPoolName
        }
    }
}

/// The result of a successful ListIdentityPools action.
public struct ListIdentityPoolsOutput: Swift.Sendable {
    /// The identity pools returned by the ListIdentityPools action.
    public var identityPools: [CognitoIdentityClientTypes.IdentityPoolShortDescription]?
    /// A pagination token.
    public var nextToken: Swift.String?

    public init(
        identityPools: [CognitoIdentityClientTypes.IdentityPoolShortDescription]? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.identityPools = identityPools
        self.nextToken = nextToken
    }
}

public struct ListTagsForResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the identity pool that the tags are assigned to.
    /// This member is required.
    public var resourceArn: Swift.String?

    public init(
        resourceArn: Swift.String? = nil
    )
    {
        self.resourceArn = resourceArn
    }
}

public struct ListTagsForResourceOutput: Swift.Sendable {
    /// The tags that are assigned to the identity pool.
    public var tags: [Swift.String: Swift.String]?

    public init(
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.tags = tags
    }
}

/// Input to the LookupDeveloperIdentityInput action.
public struct LookupDeveloperIdentityInput: Swift.Sendable {
    /// A unique ID used by your backend authentication process to identify a user. Typically, a developer identity provider would issue many developer user identifiers, in keeping with the number of users.
    public var developerUserIdentifier: Swift.String?
    /// A unique identifier in the format REGION:GUID.
    public var identityId: Swift.String?
    /// An identity pool ID in the format REGION:GUID.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// The maximum number of identities to return.
    public var maxResults: Swift.Int?
    /// A pagination token. The first call you make will have NextToken set to null. After that the service will return NextToken values as needed. For example, let's say you make a request with MaxResults set to 10, and there are 20 matches in the database. The service will return a pagination token as a part of the response. This token can be used to call the API again and get results starting from the 11th match.
    public var nextToken: Swift.String?

    public init(
        developerUserIdentifier: Swift.String? = nil,
        identityId: Swift.String? = nil,
        identityPoolId: Swift.String? = nil,
        maxResults: Swift.Int? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.developerUserIdentifier = developerUserIdentifier
        self.identityId = identityId
        self.identityPoolId = identityPoolId
        self.maxResults = maxResults
        self.nextToken = nextToken
    }
}

/// Returned in response to a successful LookupDeveloperIdentity action.
public struct LookupDeveloperIdentityOutput: Swift.Sendable {
    /// This is the list of developer user identifiers associated with an identity ID. Cognito supports the association of multiple developer user identifiers with an identity ID.
    public var developerUserIdentifierList: [Swift.String]?
    /// A unique identifier in the format REGION:GUID.
    public var identityId: Swift.String?
    /// A pagination token. The first call you make will have NextToken set to null. After that the service will return NextToken values as needed. For example, let's say you make a request with MaxResults set to 10, and there are 20 matches in the database. The service will return a pagination token as a part of the response. This token can be used to call the API again and get results starting from the 11th match.
    public var nextToken: Swift.String?

    public init(
        developerUserIdentifierList: [Swift.String]? = nil,
        identityId: Swift.String? = nil,
        nextToken: Swift.String? = nil
    )
    {
        self.developerUserIdentifierList = developerUserIdentifierList
        self.identityId = identityId
        self.nextToken = nextToken
    }
}

/// Input to the MergeDeveloperIdentities action.
public struct MergeDeveloperIdentitiesInput: Swift.Sendable {
    /// User identifier for the destination user. The value should be a DeveloperUserIdentifier.
    /// This member is required.
    public var destinationUserIdentifier: Swift.String?
    /// The "domain" by which Cognito will refer to your users. This is a (pseudo) domain name that you provide while creating an identity pool. This name acts as a placeholder that allows your backend and the Cognito service to communicate about the developer provider. For the DeveloperProviderName, you can use letters as well as period (.), underscore (_), and dash (-).
    /// This member is required.
    public var developerProviderName: Swift.String?
    /// An identity pool ID in the format REGION:GUID.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// User identifier for the source user. The value should be a DeveloperUserIdentifier.
    /// This member is required.
    public var sourceUserIdentifier: Swift.String?

    public init(
        destinationUserIdentifier: Swift.String? = nil,
        developerProviderName: Swift.String? = nil,
        identityPoolId: Swift.String? = nil,
        sourceUserIdentifier: Swift.String? = nil
    )
    {
        self.destinationUserIdentifier = destinationUserIdentifier
        self.developerProviderName = developerProviderName
        self.identityPoolId = identityPoolId
        self.sourceUserIdentifier = sourceUserIdentifier
    }
}

/// Returned in response to a successful MergeDeveloperIdentities action.
public struct MergeDeveloperIdentitiesOutput: Swift.Sendable {
    /// A unique identifier in the format REGION:GUID.
    public var identityId: Swift.String?

    public init(
        identityId: Swift.String? = nil
    )
    {
        self.identityId = identityId
    }
}

/// Thrown if there are parallel requests to modify a resource.
public struct ConcurrentModificationException: ClientRuntime.ModeledError, AWSClientRuntime.AWSServiceError, ClientRuntime.HTTPError, Swift.Error, Swift.Sendable {

    public struct Properties: Swift.Sendable {
        /// The message returned by a ConcurrentModificationException.
        public internal(set) var message: Swift.String? = nil
    }

    public internal(set) var properties = Properties()
    public static var typeName: Swift.String { "ConcurrentModificationException" }
    public static var fault: ClientRuntime.ErrorFault { .client }
    public static var isRetryable: Swift.Bool { false }
    public static var isThrottling: Swift.Bool { false }
    public internal(set) var httpResponse = SmithyHTTPAPI.HTTPResponse()
    public internal(set) var message: Swift.String?
    public internal(set) var requestID: Swift.String?

    public init(
        message: Swift.String? = nil
    )
    {
        self.properties.message = message
    }
}

/// Input to the SetIdentityPoolRoles action.
public struct SetIdentityPoolRolesInput: Swift.Sendable {
    /// An identity pool ID in the format REGION:GUID.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// How users for a specific identity provider are to mapped to roles. This is a string to [RoleMapping] object map. The string identifies the identity provider, for example, "graph.facebook.com" or "cognito-idp.us-east-1.amazonaws.com/us-east-1_abcdefghi:app_client_id". Up to 25 rules can be specified per identity provider.
    public var roleMappings: [Swift.String: CognitoIdentityClientTypes.RoleMapping]?
    /// The map of roles associated with this pool. For a given role, the key will be either "authenticated" or "unauthenticated" and the value will be the Role ARN.
    /// This member is required.
    public var roles: [Swift.String: Swift.String]?

    public init(
        identityPoolId: Swift.String? = nil,
        roleMappings: [Swift.String: CognitoIdentityClientTypes.RoleMapping]? = nil,
        roles: [Swift.String: Swift.String]? = nil
    )
    {
        self.identityPoolId = identityPoolId
        self.roleMappings = roleMappings
        self.roles = roles
    }
}

public struct SetPrincipalTagAttributeMapInput: Swift.Sendable {
    /// The ID of the Identity Pool you want to set attribute mappings for.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// The provider name you want to use for attribute mappings.
    /// This member is required.
    public var identityProviderName: Swift.String?
    /// You can use this operation to add principal tags.
    public var principalTags: [Swift.String: Swift.String]?
    /// You can use this operation to use default (username and clientID) attribute mappings.
    public var useDefaults: Swift.Bool?

    public init(
        identityPoolId: Swift.String? = nil,
        identityProviderName: Swift.String? = nil,
        principalTags: [Swift.String: Swift.String]? = nil,
        useDefaults: Swift.Bool? = nil
    )
    {
        self.identityPoolId = identityPoolId
        self.identityProviderName = identityProviderName
        self.principalTags = principalTags
        self.useDefaults = useDefaults
    }
}

public struct SetPrincipalTagAttributeMapOutput: Swift.Sendable {
    /// The ID of the Identity Pool you want to set attribute mappings for.
    public var identityPoolId: Swift.String?
    /// The provider name you want to use for attribute mappings.
    public var identityProviderName: Swift.String?
    /// You can use this operation to add principal tags. The PrincipalTagsoperation enables you to reference user attributes in your IAM permissions policy.
    public var principalTags: [Swift.String: Swift.String]?
    /// You can use this operation to select default (username and clientID) attribute mappings.
    public var useDefaults: Swift.Bool?

    public init(
        identityPoolId: Swift.String? = nil,
        identityProviderName: Swift.String? = nil,
        principalTags: [Swift.String: Swift.String]? = nil,
        useDefaults: Swift.Bool? = nil
    )
    {
        self.identityPoolId = identityPoolId
        self.identityProviderName = identityProviderName
        self.principalTags = principalTags
        self.useDefaults = useDefaults
    }
}

public struct TagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the identity pool.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The tags to assign to the identity pool.
    /// This member is required.
    public var tags: [Swift.String: Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tags: [Swift.String: Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tags = tags
    }
}

public struct TagResourceOutput: Swift.Sendable {

    public init() { }
}

/// Input to the UnlinkDeveloperIdentity action.
public struct UnlinkDeveloperIdentityInput: Swift.Sendable {
    /// The "domain" by which Cognito will refer to your users.
    /// This member is required.
    public var developerProviderName: Swift.String?
    /// A unique ID used by your backend authentication process to identify a user.
    /// This member is required.
    public var developerUserIdentifier: Swift.String?
    /// A unique identifier in the format REGION:GUID.
    /// This member is required.
    public var identityId: Swift.String?
    /// An identity pool ID in the format REGION:GUID.
    /// This member is required.
    public var identityPoolId: Swift.String?

    public init(
        developerProviderName: Swift.String? = nil,
        developerUserIdentifier: Swift.String? = nil,
        identityId: Swift.String? = nil,
        identityPoolId: Swift.String? = nil
    )
    {
        self.developerProviderName = developerProviderName
        self.developerUserIdentifier = developerUserIdentifier
        self.identityId = identityId
        self.identityPoolId = identityPoolId
    }
}

/// Input to the UnlinkIdentity action.
public struct UnlinkIdentityInput: Swift.Sendable {
    /// A unique identifier in the format REGION:GUID.
    /// This member is required.
    public var identityId: Swift.String?
    /// A set of optional name-value pairs that map provider names to provider tokens.
    /// This member is required.
    public var logins: [Swift.String: Swift.String]?
    /// Provider names to unlink from this identity.
    /// This member is required.
    public var loginsToRemove: [Swift.String]?

    public init(
        identityId: Swift.String? = nil,
        logins: [Swift.String: Swift.String]? = nil,
        loginsToRemove: [Swift.String]? = nil
    )
    {
        self.identityId = identityId
        self.logins = logins
        self.loginsToRemove = loginsToRemove
    }
}

extension UnlinkIdentityInput: Swift.CustomDebugStringConvertible {
    public var debugDescription: Swift.String {
        "UnlinkIdentityInput(identityId: \(Swift.String(describing: identityId)), loginsToRemove: \(Swift.String(describing: loginsToRemove)), logins: [keys: \(Swift.String(describing: logins?.keys)), values: \"CONTENT_REDACTED\"])"}
}

public struct UntagResourceInput: Swift.Sendable {
    /// The Amazon Resource Name (ARN) of the identity pool.
    /// This member is required.
    public var resourceArn: Swift.String?
    /// The keys of the tags to remove from the user pool.
    /// This member is required.
    public var tagKeys: [Swift.String]?

    public init(
        resourceArn: Swift.String? = nil,
        tagKeys: [Swift.String]? = nil
    )
    {
        self.resourceArn = resourceArn
        self.tagKeys = tagKeys
    }
}

public struct UntagResourceOutput: Swift.Sendable {

    public init() { }
}

/// An object representing an Amazon Cognito identity pool.
public struct UpdateIdentityPoolInput: Swift.Sendable {
    /// Enables or disables the Basic (Classic) authentication flow. For more information, see [Identity Pools (Federated Identities) Authentication Flow](https://docs.aws.amazon.com/cognito/latest/developerguide/authentication-flow.html) in the Amazon Cognito Developer Guide.
    public var allowClassicFlow: Swift.Bool?
    /// TRUE if the identity pool supports unauthenticated logins.
    /// This member is required.
    public var allowUnauthenticatedIdentities: Swift.Bool
    /// A list representing an Amazon Cognito user pool and its client ID.
    public var cognitoIdentityProviders: [CognitoIdentityClientTypes.CognitoIdentityProvider]?
    /// The "domain" by which Cognito will refer to your users.
    public var developerProviderName: Swift.String?
    /// An identity pool ID in the format REGION:GUID.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// A string that you provide.
    /// This member is required.
    public var identityPoolName: Swift.String?
    /// The tags that are assigned to the identity pool. A tag is a label that you can apply to identity pools to categorize and manage them in different ways, such as by purpose, owner, environment, or other criteria.
    public var identityPoolTags: [Swift.String: Swift.String]?
    /// The ARNs of the OpenID Connect providers.
    public var openIdConnectProviderARNs: [Swift.String]?
    /// An array of Amazon Resource Names (ARNs) of the SAML provider for your identity pool.
    public var samlProviderARNs: [Swift.String]?
    /// Optional key:value pairs mapping provider names to provider app IDs.
    public var supportedLoginProviders: [Swift.String: Swift.String]?

    public init(
        allowClassicFlow: Swift.Bool? = nil,
        allowUnauthenticatedIdentities: Swift.Bool = false,
        cognitoIdentityProviders: [CognitoIdentityClientTypes.CognitoIdentityProvider]? = nil,
        developerProviderName: Swift.String? = nil,
        identityPoolId: Swift.String? = nil,
        identityPoolName: Swift.String? = nil,
        identityPoolTags: [Swift.String: Swift.String]? = nil,
        openIdConnectProviderARNs: [Swift.String]? = nil,
        samlProviderARNs: [Swift.String]? = nil,
        supportedLoginProviders: [Swift.String: Swift.String]? = nil
    )
    {
        self.allowClassicFlow = allowClassicFlow
        self.allowUnauthenticatedIdentities = allowUnauthenticatedIdentities
        self.cognitoIdentityProviders = cognitoIdentityProviders
        self.developerProviderName = developerProviderName
        self.identityPoolId = identityPoolId
        self.identityPoolName = identityPoolName
        self.identityPoolTags = identityPoolTags
        self.openIdConnectProviderARNs = openIdConnectProviderARNs
        self.samlProviderARNs = samlProviderARNs
        self.supportedLoginProviders = supportedLoginProviders
    }
}

/// An object representing an Amazon Cognito identity pool.
public struct UpdateIdentityPoolOutput: Swift.Sendable {
    /// Enables or disables the Basic (Classic) authentication flow. For more information, see [Identity Pools (Federated Identities) Authentication Flow](https://docs.aws.amazon.com/cognito/latest/developerguide/authentication-flow.html) in the Amazon Cognito Developer Guide.
    public var allowClassicFlow: Swift.Bool?
    /// TRUE if the identity pool supports unauthenticated logins.
    /// This member is required.
    public var allowUnauthenticatedIdentities: Swift.Bool
    /// A list representing an Amazon Cognito user pool and its client ID.
    public var cognitoIdentityProviders: [CognitoIdentityClientTypes.CognitoIdentityProvider]?
    /// The "domain" by which Cognito will refer to your users.
    public var developerProviderName: Swift.String?
    /// An identity pool ID in the format REGION:GUID.
    /// This member is required.
    public var identityPoolId: Swift.String?
    /// A string that you provide.
    /// This member is required.
    public var identityPoolName: Swift.String?
    /// The tags that are assigned to the identity pool. A tag is a label that you can apply to identity pools to categorize and manage them in different ways, such as by purpose, owner, environment, or other criteria.
    public var identityPoolTags: [Swift.String: Swift.String]?
    /// The ARNs of the OpenID Connect providers.
    public var openIdConnectProviderARNs: [Swift.String]?
    /// An array of Amazon Resource Names (ARNs) of the SAML provider for your identity pool.
    public var samlProviderARNs: [Swift.String]?
    /// Optional key:value pairs mapping provider names to provider app IDs.
    public var supportedLoginProviders: [Swift.String: Swift.String]?

    public init(
        allowClassicFlow: Swift.Bool? = nil,
        allowUnauthenticatedIdentities: Swift.Bool = false,
        cognitoIdentityProviders: [CognitoIdentityClientTypes.CognitoIdentityProvider]? = nil,
        developerProviderName: Swift.String? = nil,
        identityPoolId: Swift.String? = nil,
        identityPoolName: Swift.String? = nil,
        identityPoolTags: [Swift.String: Swift.String]? = nil,
        openIdConnectProviderARNs: [Swift.String]? = nil,
        samlProviderARNs: [Swift.String]? = nil,
        supportedLoginProviders: [Swift.String: Swift.String]? = nil
    )
    {
        self.allowClassicFlow = allowClassicFlow
        self.allowUnauthenticatedIdentities = allowUnauthenticatedIdentities
        self.cognitoIdentityProviders = cognitoIdentityProviders
        self.developerProviderName = developerProviderName
        self.identityPoolId = identityPoolId
        self.identityPoolName = identityPoolName
        self.identityPoolTags = identityPoolTags
        self.openIdConnectProviderARNs = openIdConnectProviderARNs
        self.samlProviderARNs = samlProviderARNs
        self.supportedLoginProviders = supportedLoginProviders
    }
}

extension CreateIdentityPoolInput {

    static func urlPathProvider(_ value: CreateIdentityPoolInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteIdentitiesInput {

    static func urlPathProvider(_ value: DeleteIdentitiesInput) -> Swift.String? {
        return "/"
    }
}

extension DeleteIdentityPoolInput {

    static func urlPathProvider(_ value: DeleteIdentityPoolInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeIdentityInput {

    static func urlPathProvider(_ value: DescribeIdentityInput) -> Swift.String? {
        return "/"
    }
}

extension DescribeIdentityPoolInput {

    static func urlPathProvider(_ value: DescribeIdentityPoolInput) -> Swift.String? {
        return "/"
    }
}

extension GetCredentialsForIdentityInput {

    static func urlPathProvider(_ value: GetCredentialsForIdentityInput) -> Swift.String? {
        return "/"
    }
}

extension GetIdInput {

    static func urlPathProvider(_ value: GetIdInput) -> Swift.String? {
        return "/"
    }
}

extension GetIdentityPoolRolesInput {

    static func urlPathProvider(_ value: GetIdentityPoolRolesInput) -> Swift.String? {
        return "/"
    }
}

extension GetOpenIdTokenInput {

    static func urlPathProvider(_ value: GetOpenIdTokenInput) -> Swift.String? {
        return "/"
    }
}

extension GetOpenIdTokenForDeveloperIdentityInput {

    static func urlPathProvider(_ value: GetOpenIdTokenForDeveloperIdentityInput) -> Swift.String? {
        return "/"
    }
}

extension GetPrincipalTagAttributeMapInput {

    static func urlPathProvider(_ value: GetPrincipalTagAttributeMapInput) -> Swift.String? {
        return "/"
    }
}

extension ListIdentitiesInput {

    static func urlPathProvider(_ value: ListIdentitiesInput) -> Swift.String? {
        return "/"
    }
}

extension ListIdentityPoolsInput {

    static func urlPathProvider(_ value: ListIdentityPoolsInput) -> Swift.String? {
        return "/"
    }
}

extension ListTagsForResourceInput {

    static func urlPathProvider(_ value: ListTagsForResourceInput) -> Swift.String? {
        return "/"
    }
}

extension LookupDeveloperIdentityInput {

    static func urlPathProvider(_ value: LookupDeveloperIdentityInput) -> Swift.String? {
        return "/"
    }
}

extension MergeDeveloperIdentitiesInput {

    static func urlPathProvider(_ value: MergeDeveloperIdentitiesInput) -> Swift.String? {
        return "/"
    }
}

extension SetIdentityPoolRolesInput {

    static func urlPathProvider(_ value: SetIdentityPoolRolesInput) -> Swift.String? {
        return "/"
    }
}

extension SetPrincipalTagAttributeMapInput {

    static func urlPathProvider(_ value: SetPrincipalTagAttributeMapInput) -> Swift.String? {
        return "/"
    }
}

extension TagResourceInput {

    static func urlPathProvider(_ value: TagResourceInput) -> Swift.String? {
        return "/"
    }
}

extension UnlinkDeveloperIdentityInput {

    static func urlPathProvider(_ value: UnlinkDeveloperIdentityInput) -> Swift.String? {
        return "/"
    }
}

extension UnlinkIdentityInput {

    static func urlPathProvider(_ value: UnlinkIdentityInput) -> Swift.String? {
        return "/"
    }
}

extension UntagResourceInput {

    static func urlPathProvider(_ value: UntagResourceInput) -> Swift.String? {
        return "/"
    }
}

extension UpdateIdentityPoolInput {

    static func urlPathProvider(_ value: UpdateIdentityPoolInput) -> Swift.String? {
        return "/"
    }
}

extension CreateIdentityPoolInput {

    static func write(value: CreateIdentityPoolInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AllowClassicFlow"].write(value.allowClassicFlow)
        try writer["AllowUnauthenticatedIdentities"].write(value.allowUnauthenticatedIdentities)
        try writer["CognitoIdentityProviders"].writeList(value.cognitoIdentityProviders, memberWritingClosure: CognitoIdentityClientTypes.CognitoIdentityProvider.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["DeveloperProviderName"].write(value.developerProviderName)
        try writer["IdentityPoolName"].write(value.identityPoolName)
        try writer["IdentityPoolTags"].writeMap(value.identityPoolTags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["OpenIdConnectProviderARNs"].writeList(value.openIdConnectProviderARNs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["SamlProviderARNs"].writeList(value.samlProviderARNs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["SupportedLoginProviders"].writeMap(value.supportedLoginProviders, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension DeleteIdentitiesInput {

    static func write(value: DeleteIdentitiesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["IdentityIdsToDelete"].writeList(value.identityIdsToDelete, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension DeleteIdentityPoolInput {

    static func write(value: DeleteIdentityPoolInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["IdentityPoolId"].write(value.identityPoolId)
    }
}

extension DescribeIdentityInput {

    static func write(value: DescribeIdentityInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["IdentityId"].write(value.identityId)
    }
}

extension DescribeIdentityPoolInput {

    static func write(value: DescribeIdentityPoolInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["IdentityPoolId"].write(value.identityPoolId)
    }
}

extension GetCredentialsForIdentityInput {

    static func write(value: GetCredentialsForIdentityInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["CustomRoleArn"].write(value.customRoleArn)
        try writer["IdentityId"].write(value.identityId)
        try writer["Logins"].writeMap(value.logins, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension GetIdInput {

    static func write(value: GetIdInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AccountId"].write(value.accountId)
        try writer["IdentityPoolId"].write(value.identityPoolId)
        try writer["Logins"].writeMap(value.logins, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension GetIdentityPoolRolesInput {

    static func write(value: GetIdentityPoolRolesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["IdentityPoolId"].write(value.identityPoolId)
    }
}

extension GetOpenIdTokenInput {

    static func write(value: GetOpenIdTokenInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["IdentityId"].write(value.identityId)
        try writer["Logins"].writeMap(value.logins, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension GetOpenIdTokenForDeveloperIdentityInput {

    static func write(value: GetOpenIdTokenForDeveloperIdentityInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["IdentityId"].write(value.identityId)
        try writer["IdentityPoolId"].write(value.identityPoolId)
        try writer["Logins"].writeMap(value.logins, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["PrincipalTags"].writeMap(value.principalTags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["TokenDuration"].write(value.tokenDuration)
    }
}

extension GetPrincipalTagAttributeMapInput {

    static func write(value: GetPrincipalTagAttributeMapInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["IdentityPoolId"].write(value.identityPoolId)
        try writer["IdentityProviderName"].write(value.identityProviderName)
    }
}

extension ListIdentitiesInput {

    static func write(value: ListIdentitiesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["HideDisabled"].write(value.hideDisabled)
        try writer["IdentityPoolId"].write(value.identityPoolId)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListIdentityPoolsInput {

    static func write(value: ListIdentityPoolsInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension ListTagsForResourceInput {

    static func write(value: ListTagsForResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceArn"].write(value.resourceArn)
    }
}

extension LookupDeveloperIdentityInput {

    static func write(value: LookupDeveloperIdentityInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DeveloperUserIdentifier"].write(value.developerUserIdentifier)
        try writer["IdentityId"].write(value.identityId)
        try writer["IdentityPoolId"].write(value.identityPoolId)
        try writer["MaxResults"].write(value.maxResults)
        try writer["NextToken"].write(value.nextToken)
    }
}

extension MergeDeveloperIdentitiesInput {

    static func write(value: MergeDeveloperIdentitiesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DestinationUserIdentifier"].write(value.destinationUserIdentifier)
        try writer["DeveloperProviderName"].write(value.developerProviderName)
        try writer["IdentityPoolId"].write(value.identityPoolId)
        try writer["SourceUserIdentifier"].write(value.sourceUserIdentifier)
    }
}

extension SetIdentityPoolRolesInput {

    static func write(value: SetIdentityPoolRolesInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["IdentityPoolId"].write(value.identityPoolId)
        try writer["RoleMappings"].writeMap(value.roleMappings, valueWritingClosure: CognitoIdentityClientTypes.RoleMapping.write(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["Roles"].writeMap(value.roles, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension SetPrincipalTagAttributeMapInput {

    static func write(value: SetPrincipalTagAttributeMapInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["IdentityPoolId"].write(value.identityPoolId)
        try writer["IdentityProviderName"].write(value.identityProviderName)
        try writer["PrincipalTags"].writeMap(value.principalTags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["UseDefaults"].write(value.useDefaults)
    }
}

extension TagResourceInput {

    static func write(value: TagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceArn"].write(value.resourceArn)
        try writer["Tags"].writeMap(value.tags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension UnlinkDeveloperIdentityInput {

    static func write(value: UnlinkDeveloperIdentityInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["DeveloperProviderName"].write(value.developerProviderName)
        try writer["DeveloperUserIdentifier"].write(value.developerUserIdentifier)
        try writer["IdentityId"].write(value.identityId)
        try writer["IdentityPoolId"].write(value.identityPoolId)
    }
}

extension UnlinkIdentityInput {

    static func write(value: UnlinkIdentityInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["IdentityId"].write(value.identityId)
        try writer["Logins"].writeMap(value.logins, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["LoginsToRemove"].writeList(value.loginsToRemove, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UntagResourceInput {

    static func write(value: UntagResourceInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ResourceArn"].write(value.resourceArn)
        try writer["TagKeys"].writeList(value.tagKeys, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
    }
}

extension UpdateIdentityPoolInput {

    static func write(value: UpdateIdentityPoolInput?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AllowClassicFlow"].write(value.allowClassicFlow)
        try writer["AllowUnauthenticatedIdentities"].write(value.allowUnauthenticatedIdentities)
        try writer["CognitoIdentityProviders"].writeList(value.cognitoIdentityProviders, memberWritingClosure: CognitoIdentityClientTypes.CognitoIdentityProvider.write(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["DeveloperProviderName"].write(value.developerProviderName)
        try writer["IdentityPoolId"].write(value.identityPoolId)
        try writer["IdentityPoolName"].write(value.identityPoolName)
        try writer["IdentityPoolTags"].writeMap(value.identityPoolTags, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        try writer["OpenIdConnectProviderARNs"].writeList(value.openIdConnectProviderARNs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["SamlProviderARNs"].writeList(value.samlProviderARNs, memberWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), memberNodeInfo: "member", isFlattened: false)
        try writer["SupportedLoginProviders"].writeMap(value.supportedLoginProviders, valueWritingClosure: SmithyReadWrite.WritingClosures.writeString(value:to:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
    }
}

extension CreateIdentityPoolOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> CreateIdentityPoolOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = CreateIdentityPoolOutput()
        value.allowClassicFlow = try reader["AllowClassicFlow"].readIfPresent()
        value.allowUnauthenticatedIdentities = try reader["AllowUnauthenticatedIdentities"].readIfPresent() ?? false
        value.cognitoIdentityProviders = try reader["CognitoIdentityProviders"].readListIfPresent(memberReadingClosure: CognitoIdentityClientTypes.CognitoIdentityProvider.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.developerProviderName = try reader["DeveloperProviderName"].readIfPresent()
        value.identityPoolId = try reader["IdentityPoolId"].readIfPresent() ?? ""
        value.identityPoolName = try reader["IdentityPoolName"].readIfPresent() ?? ""
        value.identityPoolTags = try reader["IdentityPoolTags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.openIdConnectProviderARNs = try reader["OpenIdConnectProviderARNs"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.samlProviderARNs = try reader["SamlProviderARNs"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.supportedLoginProviders = try reader["SupportedLoginProviders"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension DeleteIdentitiesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteIdentitiesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DeleteIdentitiesOutput()
        value.unprocessedIdentityIds = try reader["UnprocessedIdentityIds"].readListIfPresent(memberReadingClosure: CognitoIdentityClientTypes.UnprocessedIdentityId.read(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DeleteIdentityPoolOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DeleteIdentityPoolOutput {
        return DeleteIdentityPoolOutput()
    }
}

extension DescribeIdentityOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeIdentityOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeIdentityOutput()
        value.creationDate = try reader["CreationDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.identityId = try reader["IdentityId"].readIfPresent()
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.logins = try reader["Logins"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        return value
    }
}

extension DescribeIdentityPoolOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> DescribeIdentityPoolOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = DescribeIdentityPoolOutput()
        value.allowClassicFlow = try reader["AllowClassicFlow"].readIfPresent()
        value.allowUnauthenticatedIdentities = try reader["AllowUnauthenticatedIdentities"].readIfPresent() ?? false
        value.cognitoIdentityProviders = try reader["CognitoIdentityProviders"].readListIfPresent(memberReadingClosure: CognitoIdentityClientTypes.CognitoIdentityProvider.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.developerProviderName = try reader["DeveloperProviderName"].readIfPresent()
        value.identityPoolId = try reader["IdentityPoolId"].readIfPresent() ?? ""
        value.identityPoolName = try reader["IdentityPoolName"].readIfPresent() ?? ""
        value.identityPoolTags = try reader["IdentityPoolTags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.openIdConnectProviderARNs = try reader["OpenIdConnectProviderARNs"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.samlProviderARNs = try reader["SamlProviderARNs"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.supportedLoginProviders = try reader["SupportedLoginProviders"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension GetCredentialsForIdentityOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetCredentialsForIdentityOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetCredentialsForIdentityOutput()
        value.credentials = try reader["Credentials"].readIfPresent(with: CognitoIdentityClientTypes.Credentials.read(from:))
        value.identityId = try reader["IdentityId"].readIfPresent()
        return value
    }
}

extension GetIdOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetIdOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetIdOutput()
        value.identityId = try reader["IdentityId"].readIfPresent()
        return value
    }
}

extension GetIdentityPoolRolesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetIdentityPoolRolesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetIdentityPoolRolesOutput()
        value.identityPoolId = try reader["IdentityPoolId"].readIfPresent()
        value.roleMappings = try reader["RoleMappings"].readMapIfPresent(valueReadingClosure: CognitoIdentityClientTypes.RoleMapping.read(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.roles = try reader["Roles"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension GetOpenIdTokenOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetOpenIdTokenOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetOpenIdTokenOutput()
        value.identityId = try reader["IdentityId"].readIfPresent()
        value.token = try reader["Token"].readIfPresent()
        return value
    }
}

extension GetOpenIdTokenForDeveloperIdentityOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetOpenIdTokenForDeveloperIdentityOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetOpenIdTokenForDeveloperIdentityOutput()
        value.identityId = try reader["IdentityId"].readIfPresent()
        value.token = try reader["Token"].readIfPresent()
        return value
    }
}

extension GetPrincipalTagAttributeMapOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> GetPrincipalTagAttributeMapOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = GetPrincipalTagAttributeMapOutput()
        value.identityPoolId = try reader["IdentityPoolId"].readIfPresent()
        value.identityProviderName = try reader["IdentityProviderName"].readIfPresent()
        value.principalTags = try reader["PrincipalTags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.useDefaults = try reader["UseDefaults"].readIfPresent()
        return value
    }
}

extension ListIdentitiesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListIdentitiesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListIdentitiesOutput()
        value.identities = try reader["Identities"].readListIfPresent(memberReadingClosure: CognitoIdentityClientTypes.IdentityDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.identityPoolId = try reader["IdentityPoolId"].readIfPresent()
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListIdentityPoolsOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListIdentityPoolsOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListIdentityPoolsOutput()
        value.identityPools = try reader["IdentityPools"].readListIfPresent(memberReadingClosure: CognitoIdentityClientTypes.IdentityPoolShortDescription.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension ListTagsForResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> ListTagsForResourceOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = ListTagsForResourceOutput()
        value.tags = try reader["Tags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

extension LookupDeveloperIdentityOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> LookupDeveloperIdentityOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = LookupDeveloperIdentityOutput()
        value.developerUserIdentifierList = try reader["DeveloperUserIdentifierList"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.identityId = try reader["IdentityId"].readIfPresent()
        value.nextToken = try reader["NextToken"].readIfPresent()
        return value
    }
}

extension MergeDeveloperIdentitiesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> MergeDeveloperIdentitiesOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = MergeDeveloperIdentitiesOutput()
        value.identityId = try reader["IdentityId"].readIfPresent()
        return value
    }
}

extension SetIdentityPoolRolesOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SetIdentityPoolRolesOutput {
        return SetIdentityPoolRolesOutput()
    }
}

extension SetPrincipalTagAttributeMapOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> SetPrincipalTagAttributeMapOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = SetPrincipalTagAttributeMapOutput()
        value.identityPoolId = try reader["IdentityPoolId"].readIfPresent()
        value.identityProviderName = try reader["IdentityProviderName"].readIfPresent()
        value.principalTags = try reader["PrincipalTags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.useDefaults = try reader["UseDefaults"].readIfPresent()
        return value
    }
}

extension TagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> TagResourceOutput {
        return TagResourceOutput()
    }
}

extension UnlinkDeveloperIdentityOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UnlinkDeveloperIdentityOutput {
        return UnlinkDeveloperIdentityOutput()
    }
}

extension UnlinkIdentityOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UnlinkIdentityOutput {
        return UnlinkIdentityOutput()
    }
}

extension UntagResourceOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UntagResourceOutput {
        return UntagResourceOutput()
    }
}

extension UpdateIdentityPoolOutput {

    static func httpOutput(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> UpdateIdentityPoolOutput {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let reader = responseReader
        var value = UpdateIdentityPoolOutput()
        value.allowClassicFlow = try reader["AllowClassicFlow"].readIfPresent()
        value.allowUnauthenticatedIdentities = try reader["AllowUnauthenticatedIdentities"].readIfPresent() ?? false
        value.cognitoIdentityProviders = try reader["CognitoIdentityProviders"].readListIfPresent(memberReadingClosure: CognitoIdentityClientTypes.CognitoIdentityProvider.read(from:), memberNodeInfo: "member", isFlattened: false)
        value.developerProviderName = try reader["DeveloperProviderName"].readIfPresent()
        value.identityPoolId = try reader["IdentityPoolId"].readIfPresent() ?? ""
        value.identityPoolName = try reader["IdentityPoolName"].readIfPresent() ?? ""
        value.identityPoolTags = try reader["IdentityPoolTags"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        value.openIdConnectProviderARNs = try reader["OpenIdConnectProviderARNs"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.samlProviderARNs = try reader["SamlProviderARNs"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.supportedLoginProviders = try reader["SupportedLoginProviders"].readMapIfPresent(valueReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), keyNodeInfo: "key", valueNodeInfo: "value", isFlattened: false)
        return value
    }
}

enum CreateIdentityPoolOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceConflictException": return try ResourceConflictException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteIdentitiesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DeleteIdentityPoolOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeIdentityOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum DescribeIdentityPoolOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetCredentialsForIdentityOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ExternalServiceException": return try ExternalServiceException.makeError(baseError: baseError)
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidIdentityPoolConfigurationException": return try InvalidIdentityPoolConfigurationException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceConflictException": return try ResourceConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetIdOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ExternalServiceException": return try ExternalServiceException.makeError(baseError: baseError)
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceConflictException": return try ResourceConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetIdentityPoolRolesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceConflictException": return try ResourceConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetOpenIdTokenOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ExternalServiceException": return try ExternalServiceException.makeError(baseError: baseError)
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceConflictException": return try ResourceConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetOpenIdTokenForDeveloperIdentityOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "DeveloperUserAlreadyRegisteredException": return try DeveloperUserAlreadyRegisteredException.makeError(baseError: baseError)
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceConflictException": return try ResourceConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum GetPrincipalTagAttributeMapOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListIdentitiesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListIdentityPoolsOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum ListTagsForResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum LookupDeveloperIdentityOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceConflictException": return try ResourceConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum MergeDeveloperIdentitiesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceConflictException": return try ResourceConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SetIdentityPoolRolesOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConcurrentModificationException": return try ConcurrentModificationException.makeError(baseError: baseError)
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceConflictException": return try ResourceConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum SetPrincipalTagAttributeMapOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum TagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UnlinkDeveloperIdentityOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceConflictException": return try ResourceConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UnlinkIdentityOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ExternalServiceException": return try ExternalServiceException.makeError(baseError: baseError)
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceConflictException": return try ResourceConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UntagResourceOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

enum UpdateIdentityPoolOutputError {

    static func httpError(from httpResponse: SmithyHTTPAPI.HTTPResponse) async throws -> Swift.Error {
        let data = try await httpResponse.data()
        let responseReader = try SmithyJSON.Reader.from(data: data)
        let baseError = try AWSClientRuntime.AWSJSONError(httpResponse: httpResponse, responseReader: responseReader, noErrorWrapping: false)
        if let error = baseError.customError() { return error }
        switch baseError.code {
            case "ConcurrentModificationException": return try ConcurrentModificationException.makeError(baseError: baseError)
            case "InternalErrorException": return try InternalErrorException.makeError(baseError: baseError)
            case "InvalidParameterException": return try InvalidParameterException.makeError(baseError: baseError)
            case "LimitExceededException": return try LimitExceededException.makeError(baseError: baseError)
            case "NotAuthorizedException": return try NotAuthorizedException.makeError(baseError: baseError)
            case "ResourceConflictException": return try ResourceConflictException.makeError(baseError: baseError)
            case "ResourceNotFoundException": return try ResourceNotFoundException.makeError(baseError: baseError)
            case "TooManyRequestsException": return try TooManyRequestsException.makeError(baseError: baseError)
            default: return try AWSClientRuntime.UnknownAWSHTTPServiceError.makeError(baseError: baseError)
        }
    }
}

extension TooManyRequestsException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> TooManyRequestsException {
        let reader = baseError.errorBodyReader
        var value = TooManyRequestsException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceConflictException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ResourceConflictException {
        let reader = baseError.errorBodyReader
        var value = ResourceConflictException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InternalErrorException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InternalErrorException {
        let reader = baseError.errorBodyReader
        var value = InternalErrorException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidParameterException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidParameterException {
        let reader = baseError.errorBodyReader
        var value = InvalidParameterException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension NotAuthorizedException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> NotAuthorizedException {
        let reader = baseError.errorBodyReader
        var value = NotAuthorizedException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension LimitExceededException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> LimitExceededException {
        let reader = baseError.errorBodyReader
        var value = LimitExceededException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ResourceNotFoundException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ResourceNotFoundException {
        let reader = baseError.errorBodyReader
        var value = ResourceNotFoundException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ExternalServiceException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ExternalServiceException {
        let reader = baseError.errorBodyReader
        var value = ExternalServiceException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension InvalidIdentityPoolConfigurationException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> InvalidIdentityPoolConfigurationException {
        let reader = baseError.errorBodyReader
        var value = InvalidIdentityPoolConfigurationException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension DeveloperUserAlreadyRegisteredException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> DeveloperUserAlreadyRegisteredException {
        let reader = baseError.errorBodyReader
        var value = DeveloperUserAlreadyRegisteredException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension ConcurrentModificationException {

    static func makeError(baseError: AWSClientRuntime.AWSJSONError) throws -> ConcurrentModificationException {
        let reader = baseError.errorBodyReader
        var value = ConcurrentModificationException()
        value.properties.message = try reader["message"].readIfPresent()
        value.httpResponse = baseError.httpResponse
        value.requestID = baseError.requestID
        value.message = baseError.message
        return value
    }
}

extension CognitoIdentityClientTypes.CognitoIdentityProvider {

    static func write(value: CognitoIdentityClientTypes.CognitoIdentityProvider?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["ClientId"].write(value.clientId)
        try writer["ProviderName"].write(value.providerName)
        try writer["ServerSideTokenCheck"].write(value.serverSideTokenCheck)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CognitoIdentityClientTypes.CognitoIdentityProvider {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CognitoIdentityClientTypes.CognitoIdentityProvider()
        value.providerName = try reader["ProviderName"].readIfPresent()
        value.clientId = try reader["ClientId"].readIfPresent()
        value.serverSideTokenCheck = try reader["ServerSideTokenCheck"].readIfPresent()
        return value
    }
}

extension CognitoIdentityClientTypes.UnprocessedIdentityId {

    static func read(from reader: SmithyJSON.Reader) throws -> CognitoIdentityClientTypes.UnprocessedIdentityId {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CognitoIdentityClientTypes.UnprocessedIdentityId()
        value.identityId = try reader["IdentityId"].readIfPresent()
        value.errorCode = try reader["ErrorCode"].readIfPresent()
        return value
    }
}

extension CognitoIdentityClientTypes.Credentials {

    static func read(from reader: SmithyJSON.Reader) throws -> CognitoIdentityClientTypes.Credentials {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CognitoIdentityClientTypes.Credentials()
        value.accessKeyId = try reader["AccessKeyId"].readIfPresent()
        value.secretKey = try reader["SecretKey"].readIfPresent()
        value.sessionToken = try reader["SessionToken"].readIfPresent()
        value.expiration = try reader["Expiration"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension CognitoIdentityClientTypes.RoleMapping {

    static func write(value: CognitoIdentityClientTypes.RoleMapping?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["AmbiguousRoleResolution"].write(value.ambiguousRoleResolution)
        try writer["RulesConfiguration"].write(value.rulesConfiguration, with: CognitoIdentityClientTypes.RulesConfigurationType.write(value:to:))
        try writer["Type"].write(value.type)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CognitoIdentityClientTypes.RoleMapping {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CognitoIdentityClientTypes.RoleMapping()
        value.type = try reader["Type"].readIfPresent() ?? .sdkUnknown("")
        value.ambiguousRoleResolution = try reader["AmbiguousRoleResolution"].readIfPresent()
        value.rulesConfiguration = try reader["RulesConfiguration"].readIfPresent(with: CognitoIdentityClientTypes.RulesConfigurationType.read(from:))
        return value
    }
}

extension CognitoIdentityClientTypes.RulesConfigurationType {

    static func write(value: CognitoIdentityClientTypes.RulesConfigurationType?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Rules"].writeList(value.rules, memberWritingClosure: CognitoIdentityClientTypes.MappingRule.write(value:to:), memberNodeInfo: "member", isFlattened: false)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CognitoIdentityClientTypes.RulesConfigurationType {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CognitoIdentityClientTypes.RulesConfigurationType()
        value.rules = try reader["Rules"].readListIfPresent(memberReadingClosure: CognitoIdentityClientTypes.MappingRule.read(from:), memberNodeInfo: "member", isFlattened: false) ?? []
        return value
    }
}

extension CognitoIdentityClientTypes.MappingRule {

    static func write(value: CognitoIdentityClientTypes.MappingRule?, to writer: SmithyJSON.Writer) throws {
        guard let value else { return }
        try writer["Claim"].write(value.claim)
        try writer["MatchType"].write(value.matchType)
        try writer["RoleARN"].write(value.roleARN)
        try writer["Value"].write(value.value)
    }

    static func read(from reader: SmithyJSON.Reader) throws -> CognitoIdentityClientTypes.MappingRule {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CognitoIdentityClientTypes.MappingRule()
        value.claim = try reader["Claim"].readIfPresent() ?? ""
        value.matchType = try reader["MatchType"].readIfPresent() ?? .sdkUnknown("")
        value.value = try reader["Value"].readIfPresent() ?? ""
        value.roleARN = try reader["RoleARN"].readIfPresent() ?? ""
        return value
    }
}

extension CognitoIdentityClientTypes.IdentityDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> CognitoIdentityClientTypes.IdentityDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CognitoIdentityClientTypes.IdentityDescription()
        value.identityId = try reader["IdentityId"].readIfPresent()
        value.logins = try reader["Logins"].readListIfPresent(memberReadingClosure: SmithyReadWrite.ReadingClosures.readString(from:), memberNodeInfo: "member", isFlattened: false)
        value.creationDate = try reader["CreationDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        value.lastModifiedDate = try reader["LastModifiedDate"].readTimestampIfPresent(format: SmithyTimestamps.TimestampFormat.epochSeconds)
        return value
    }
}

extension CognitoIdentityClientTypes.IdentityPoolShortDescription {

    static func read(from reader: SmithyJSON.Reader) throws -> CognitoIdentityClientTypes.IdentityPoolShortDescription {
        guard reader.hasContent else { throw SmithyReadWrite.ReaderError.requiredValueNotPresent }
        var value = CognitoIdentityClientTypes.IdentityPoolShortDescription()
        value.identityPoolId = try reader["IdentityPoolId"].readIfPresent()
        value.identityPoolName = try reader["IdentityPoolName"].readIfPresent()
        return value
    }
}

public enum CognitoIdentityClientTypes {}
