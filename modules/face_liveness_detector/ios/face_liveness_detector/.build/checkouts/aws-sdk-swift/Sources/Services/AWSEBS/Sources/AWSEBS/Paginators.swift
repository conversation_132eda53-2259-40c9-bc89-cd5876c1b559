//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import Foundation
import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension EBSClient {
    /// Paginate over `[ListChangedBlocksOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListChangedBlocksInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListChangedBlocksOutput`
    public func listChangedBlocksPaginated(input: ListChangedBlocksInput) -> ClientRuntime.PaginatorSequence<ListChangedBlocksInput, ListChangedBlocksOutput> {
        return ClientRuntime.PaginatorSequence<ListChangedBlocksInput, ListChangedBlocksOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listChangedBlocks(input:))
    }
}

extension ListChangedBlocksInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListChangedBlocksInput {
        return ListChangedBlocksInput(
            firstSnapshotId: self.firstSnapshotId,
            maxResults: self.maxResults,
            nextToken: token,
            secondSnapshotId: self.secondSnapshotId,
            startingBlockIndex: self.startingBlockIndex
        )}
}
extension EBSClient {
    /// Paginate over `[ListSnapshotBlocksOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[ListSnapshotBlocksInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `ListSnapshotBlocksOutput`
    public func listSnapshotBlocksPaginated(input: ListSnapshotBlocksInput) -> ClientRuntime.PaginatorSequence<ListSnapshotBlocksInput, ListSnapshotBlocksOutput> {
        return ClientRuntime.PaginatorSequence<ListSnapshotBlocksInput, ListSnapshotBlocksOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.listSnapshotBlocks(input:))
    }
}

extension ListSnapshotBlocksInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> ListSnapshotBlocksInput {
        return ListSnapshotBlocksInput(
            maxResults: self.maxResults,
            nextToken: token,
            snapshotId: self.snapshotId,
            startingBlockIndex: self.startingBlockIndex
        )}
}
