//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import Foundation
import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension DocDBClient {
    /// Paginate over `[DescribeCertificatesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCertificatesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCertificatesOutput`
    public func describeCertificatesPaginated(input: DescribeCertificatesInput) -> ClientRuntime.PaginatorSequence<DescribeCertificatesInput, DescribeCertificatesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCertificatesInput, DescribeCertificatesOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeCertificates(input:))
    }
}

extension DescribeCertificatesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCertificatesInput {
        return DescribeCertificatesInput(
            certificateIdentifier: self.certificateIdentifier,
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeCertificatesInput, OperationStackOutput == DescribeCertificatesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeCertificatesPaginated`
    /// to access the nested member `[DocDBClientTypes.Certificate]`
    /// - Returns: `[DocDBClientTypes.Certificate]`
    public func certificates() async throws -> [DocDBClientTypes.Certificate] {
        return try await self.asyncCompactMap { item in item.certificates }
    }
}
extension DocDBClient {
    /// Paginate over `[DescribeDBClusterParameterGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeDBClusterParameterGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeDBClusterParameterGroupsOutput`
    public func describeDBClusterParameterGroupsPaginated(input: DescribeDBClusterParameterGroupsInput) -> ClientRuntime.PaginatorSequence<DescribeDBClusterParameterGroupsInput, DescribeDBClusterParameterGroupsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeDBClusterParameterGroupsInput, DescribeDBClusterParameterGroupsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeDBClusterParameterGroups(input:))
    }
}

extension DescribeDBClusterParameterGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeDBClusterParameterGroupsInput {
        return DescribeDBClusterParameterGroupsInput(
            dbClusterParameterGroupName: self.dbClusterParameterGroupName,
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeDBClusterParameterGroupsInput, OperationStackOutput == DescribeDBClusterParameterGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeDBClusterParameterGroupsPaginated`
    /// to access the nested member `[DocDBClientTypes.DBClusterParameterGroup]`
    /// - Returns: `[DocDBClientTypes.DBClusterParameterGroup]`
    public func dbClusterParameterGroups() async throws -> [DocDBClientTypes.DBClusterParameterGroup] {
        return try await self.asyncCompactMap { item in item.dbClusterParameterGroups }
    }
}
extension DocDBClient {
    /// Paginate over `[DescribeDBClusterParametersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeDBClusterParametersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeDBClusterParametersOutput`
    public func describeDBClusterParametersPaginated(input: DescribeDBClusterParametersInput) -> ClientRuntime.PaginatorSequence<DescribeDBClusterParametersInput, DescribeDBClusterParametersOutput> {
        return ClientRuntime.PaginatorSequence<DescribeDBClusterParametersInput, DescribeDBClusterParametersOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeDBClusterParameters(input:))
    }
}

extension DescribeDBClusterParametersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeDBClusterParametersInput {
        return DescribeDBClusterParametersInput(
            dbClusterParameterGroupName: self.dbClusterParameterGroupName,
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            source: self.source
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeDBClusterParametersInput, OperationStackOutput == DescribeDBClusterParametersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeDBClusterParametersPaginated`
    /// to access the nested member `[DocDBClientTypes.Parameter]`
    /// - Returns: `[DocDBClientTypes.Parameter]`
    public func parameters() async throws -> [DocDBClientTypes.Parameter] {
        return try await self.asyncCompactMap { item in item.parameters }
    }
}
extension DocDBClient {
    /// Paginate over `[DescribeDBClustersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeDBClustersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeDBClustersOutput`
    public func describeDBClustersPaginated(input: DescribeDBClustersInput) -> ClientRuntime.PaginatorSequence<DescribeDBClustersInput, DescribeDBClustersOutput> {
        return ClientRuntime.PaginatorSequence<DescribeDBClustersInput, DescribeDBClustersOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeDBClusters(input:))
    }
}

extension DescribeDBClustersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeDBClustersInput {
        return DescribeDBClustersInput(
            dbClusterIdentifier: self.dbClusterIdentifier,
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeDBClustersInput, OperationStackOutput == DescribeDBClustersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeDBClustersPaginated`
    /// to access the nested member `[DocDBClientTypes.DBCluster]`
    /// - Returns: `[DocDBClientTypes.DBCluster]`
    public func dbClusters() async throws -> [DocDBClientTypes.DBCluster] {
        return try await self.asyncCompactMap { item in item.dbClusters }
    }
}
extension DocDBClient {
    /// Paginate over `[DescribeDBClusterSnapshotsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeDBClusterSnapshotsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeDBClusterSnapshotsOutput`
    public func describeDBClusterSnapshotsPaginated(input: DescribeDBClusterSnapshotsInput) -> ClientRuntime.PaginatorSequence<DescribeDBClusterSnapshotsInput, DescribeDBClusterSnapshotsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeDBClusterSnapshotsInput, DescribeDBClusterSnapshotsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeDBClusterSnapshots(input:))
    }
}

extension DescribeDBClusterSnapshotsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeDBClusterSnapshotsInput {
        return DescribeDBClusterSnapshotsInput(
            dbClusterIdentifier: self.dbClusterIdentifier,
            dbClusterSnapshotIdentifier: self.dbClusterSnapshotIdentifier,
            filters: self.filters,
            includePublic: self.includePublic,
            includeShared: self.includeShared,
            marker: token,
            maxRecords: self.maxRecords,
            snapshotType: self.snapshotType
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeDBClusterSnapshotsInput, OperationStackOutput == DescribeDBClusterSnapshotsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeDBClusterSnapshotsPaginated`
    /// to access the nested member `[DocDBClientTypes.DBClusterSnapshot]`
    /// - Returns: `[DocDBClientTypes.DBClusterSnapshot]`
    public func dbClusterSnapshots() async throws -> [DocDBClientTypes.DBClusterSnapshot] {
        return try await self.asyncCompactMap { item in item.dbClusterSnapshots }
    }
}
extension DocDBClient {
    /// Paginate over `[DescribeDBEngineVersionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeDBEngineVersionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeDBEngineVersionsOutput`
    public func describeDBEngineVersionsPaginated(input: DescribeDBEngineVersionsInput) -> ClientRuntime.PaginatorSequence<DescribeDBEngineVersionsInput, DescribeDBEngineVersionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeDBEngineVersionsInput, DescribeDBEngineVersionsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeDBEngineVersions(input:))
    }
}

extension DescribeDBEngineVersionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeDBEngineVersionsInput {
        return DescribeDBEngineVersionsInput(
            dbParameterGroupFamily: self.dbParameterGroupFamily,
            defaultOnly: self.defaultOnly,
            engine: self.engine,
            engineVersion: self.engineVersion,
            filters: self.filters,
            listSupportedCharacterSets: self.listSupportedCharacterSets,
            listSupportedTimezones: self.listSupportedTimezones,
            marker: token,
            maxRecords: self.maxRecords
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeDBEngineVersionsInput, OperationStackOutput == DescribeDBEngineVersionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeDBEngineVersionsPaginated`
    /// to access the nested member `[DocDBClientTypes.DBEngineVersion]`
    /// - Returns: `[DocDBClientTypes.DBEngineVersion]`
    public func dbEngineVersions() async throws -> [DocDBClientTypes.DBEngineVersion] {
        return try await self.asyncCompactMap { item in item.dbEngineVersions }
    }
}
extension DocDBClient {
    /// Paginate over `[DescribeDBInstancesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeDBInstancesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeDBInstancesOutput`
    public func describeDBInstancesPaginated(input: DescribeDBInstancesInput) -> ClientRuntime.PaginatorSequence<DescribeDBInstancesInput, DescribeDBInstancesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeDBInstancesInput, DescribeDBInstancesOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeDBInstances(input:))
    }
}

extension DescribeDBInstancesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeDBInstancesInput {
        return DescribeDBInstancesInput(
            dbInstanceIdentifier: self.dbInstanceIdentifier,
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeDBInstancesInput, OperationStackOutput == DescribeDBInstancesOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeDBInstancesPaginated`
    /// to access the nested member `[DocDBClientTypes.DBInstance]`
    /// - Returns: `[DocDBClientTypes.DBInstance]`
    public func dbInstances() async throws -> [DocDBClientTypes.DBInstance] {
        return try await self.asyncCompactMap { item in item.dbInstances }
    }
}
extension DocDBClient {
    /// Paginate over `[DescribeDBSubnetGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeDBSubnetGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeDBSubnetGroupsOutput`
    public func describeDBSubnetGroupsPaginated(input: DescribeDBSubnetGroupsInput) -> ClientRuntime.PaginatorSequence<DescribeDBSubnetGroupsInput, DescribeDBSubnetGroupsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeDBSubnetGroupsInput, DescribeDBSubnetGroupsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeDBSubnetGroups(input:))
    }
}

extension DescribeDBSubnetGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeDBSubnetGroupsInput {
        return DescribeDBSubnetGroupsInput(
            dbSubnetGroupName: self.dbSubnetGroupName,
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeDBSubnetGroupsInput, OperationStackOutput == DescribeDBSubnetGroupsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeDBSubnetGroupsPaginated`
    /// to access the nested member `[DocDBClientTypes.DBSubnetGroup]`
    /// - Returns: `[DocDBClientTypes.DBSubnetGroup]`
    public func dbSubnetGroups() async throws -> [DocDBClientTypes.DBSubnetGroup] {
        return try await self.asyncCompactMap { item in item.dbSubnetGroups }
    }
}
extension DocDBClient {
    /// Paginate over `[DescribeEventsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeEventsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeEventsOutput`
    public func describeEventsPaginated(input: DescribeEventsInput) -> ClientRuntime.PaginatorSequence<DescribeEventsInput, DescribeEventsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeEventsInput, DescribeEventsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeEvents(input:))
    }
}

extension DescribeEventsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeEventsInput {
        return DescribeEventsInput(
            duration: self.duration,
            endTime: self.endTime,
            eventCategories: self.eventCategories,
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            sourceIdentifier: self.sourceIdentifier,
            sourceType: self.sourceType,
            startTime: self.startTime
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeEventsInput, OperationStackOutput == DescribeEventsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeEventsPaginated`
    /// to access the nested member `[DocDBClientTypes.Event]`
    /// - Returns: `[DocDBClientTypes.Event]`
    public func events() async throws -> [DocDBClientTypes.Event] {
        return try await self.asyncCompactMap { item in item.events }
    }
}
extension DocDBClient {
    /// Paginate over `[DescribeEventSubscriptionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeEventSubscriptionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeEventSubscriptionsOutput`
    public func describeEventSubscriptionsPaginated(input: DescribeEventSubscriptionsInput) -> ClientRuntime.PaginatorSequence<DescribeEventSubscriptionsInput, DescribeEventSubscriptionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeEventSubscriptionsInput, DescribeEventSubscriptionsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeEventSubscriptions(input:))
    }
}

extension DescribeEventSubscriptionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeEventSubscriptionsInput {
        return DescribeEventSubscriptionsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            subscriptionName: self.subscriptionName
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeEventSubscriptionsInput, OperationStackOutput == DescribeEventSubscriptionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeEventSubscriptionsPaginated`
    /// to access the nested member `[DocDBClientTypes.EventSubscription]`
    /// - Returns: `[DocDBClientTypes.EventSubscription]`
    public func eventSubscriptionsList() async throws -> [DocDBClientTypes.EventSubscription] {
        return try await self.asyncCompactMap { item in item.eventSubscriptionsList }
    }
}
extension DocDBClient {
    /// Paginate over `[DescribeGlobalClustersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeGlobalClustersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeGlobalClustersOutput`
    public func describeGlobalClustersPaginated(input: DescribeGlobalClustersInput) -> ClientRuntime.PaginatorSequence<DescribeGlobalClustersInput, DescribeGlobalClustersOutput> {
        return ClientRuntime.PaginatorSequence<DescribeGlobalClustersInput, DescribeGlobalClustersOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeGlobalClusters(input:))
    }
}

extension DescribeGlobalClustersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeGlobalClustersInput {
        return DescribeGlobalClustersInput(
            filters: self.filters,
            globalClusterIdentifier: self.globalClusterIdentifier,
            marker: token,
            maxRecords: self.maxRecords
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeGlobalClustersInput, OperationStackOutput == DescribeGlobalClustersOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeGlobalClustersPaginated`
    /// to access the nested member `[DocDBClientTypes.GlobalCluster]`
    /// - Returns: `[DocDBClientTypes.GlobalCluster]`
    public func globalClusters() async throws -> [DocDBClientTypes.GlobalCluster] {
        return try await self.asyncCompactMap { item in item.globalClusters }
    }
}
extension DocDBClient {
    /// Paginate over `[DescribeOrderableDBInstanceOptionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeOrderableDBInstanceOptionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeOrderableDBInstanceOptionsOutput`
    public func describeOrderableDBInstanceOptionsPaginated(input: DescribeOrderableDBInstanceOptionsInput) -> ClientRuntime.PaginatorSequence<DescribeOrderableDBInstanceOptionsInput, DescribeOrderableDBInstanceOptionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeOrderableDBInstanceOptionsInput, DescribeOrderableDBInstanceOptionsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeOrderableDBInstanceOptions(input:))
    }
}

extension DescribeOrderableDBInstanceOptionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeOrderableDBInstanceOptionsInput {
        return DescribeOrderableDBInstanceOptionsInput(
            dbInstanceClass: self.dbInstanceClass,
            engine: self.engine,
            engineVersion: self.engineVersion,
            filters: self.filters,
            licenseModel: self.licenseModel,
            marker: token,
            maxRecords: self.maxRecords,
            vpc: self.vpc
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeOrderableDBInstanceOptionsInput, OperationStackOutput == DescribeOrderableDBInstanceOptionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeOrderableDBInstanceOptionsPaginated`
    /// to access the nested member `[DocDBClientTypes.OrderableDBInstanceOption]`
    /// - Returns: `[DocDBClientTypes.OrderableDBInstanceOption]`
    public func orderableDBInstanceOptions() async throws -> [DocDBClientTypes.OrderableDBInstanceOption] {
        return try await self.asyncCompactMap { item in item.orderableDBInstanceOptions }
    }
}
extension DocDBClient {
    /// Paginate over `[DescribePendingMaintenanceActionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribePendingMaintenanceActionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribePendingMaintenanceActionsOutput`
    public func describePendingMaintenanceActionsPaginated(input: DescribePendingMaintenanceActionsInput) -> ClientRuntime.PaginatorSequence<DescribePendingMaintenanceActionsInput, DescribePendingMaintenanceActionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribePendingMaintenanceActionsInput, DescribePendingMaintenanceActionsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describePendingMaintenanceActions(input:))
    }
}

extension DescribePendingMaintenanceActionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribePendingMaintenanceActionsInput {
        return DescribePendingMaintenanceActionsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            resourceIdentifier: self.resourceIdentifier
        )}
}

extension PaginatorSequence where OperationStackInput == DescribePendingMaintenanceActionsInput, OperationStackOutput == DescribePendingMaintenanceActionsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describePendingMaintenanceActionsPaginated`
    /// to access the nested member `[DocDBClientTypes.ResourcePendingMaintenanceActions]`
    /// - Returns: `[DocDBClientTypes.ResourcePendingMaintenanceActions]`
    public func pendingMaintenanceActions() async throws -> [DocDBClientTypes.ResourcePendingMaintenanceActions] {
        return try await self.asyncCompactMap { item in item.pendingMaintenanceActions }
    }
}
