//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import Foundation
import protocol ClientRuntime.PaginateToken
import struct ClientRuntime.PaginatorSequence

extension DatabaseMigrationClient {
    /// Paginate over `[DescribeApplicableIndividualAssessmentsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeApplicableIndividualAssessmentsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeApplicableIndividualAssessmentsOutput`
    public func describeApplicableIndividualAssessmentsPaginated(input: DescribeApplicableIndividualAssessmentsInput) -> ClientRuntime.PaginatorSequence<DescribeApplicableIndividualAssessmentsInput, DescribeApplicableIndividualAssessmentsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeApplicableIndividualAssessmentsInput, DescribeApplicableIndividualAssessmentsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeApplicableIndividualAssessments(input:))
    }
}

extension DescribeApplicableIndividualAssessmentsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeApplicableIndividualAssessmentsInput {
        return DescribeApplicableIndividualAssessmentsInput(
            marker: token,
            maxRecords: self.maxRecords,
            migrationType: self.migrationType,
            replicationInstanceArn: self.replicationInstanceArn,
            replicationTaskArn: self.replicationTaskArn,
            sourceEngineName: self.sourceEngineName,
            targetEngineName: self.targetEngineName
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeCertificatesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeCertificatesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeCertificatesOutput`
    public func describeCertificatesPaginated(input: DescribeCertificatesInput) -> ClientRuntime.PaginatorSequence<DescribeCertificatesInput, DescribeCertificatesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeCertificatesInput, DescribeCertificatesOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeCertificates(input:))
    }
}

extension DescribeCertificatesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeCertificatesInput {
        return DescribeCertificatesInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeConnectionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeConnectionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeConnectionsOutput`
    public func describeConnectionsPaginated(input: DescribeConnectionsInput) -> ClientRuntime.PaginatorSequence<DescribeConnectionsInput, DescribeConnectionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeConnectionsInput, DescribeConnectionsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeConnections(input:))
    }
}

extension DescribeConnectionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeConnectionsInput {
        return DescribeConnectionsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeDataMigrationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeDataMigrationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeDataMigrationsOutput`
    public func describeDataMigrationsPaginated(input: DescribeDataMigrationsInput) -> ClientRuntime.PaginatorSequence<DescribeDataMigrationsInput, DescribeDataMigrationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeDataMigrationsInput, DescribeDataMigrationsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeDataMigrations(input:))
    }
}

extension DescribeDataMigrationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeDataMigrationsInput {
        return DescribeDataMigrationsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            withoutSettings: self.withoutSettings,
            withoutStatistics: self.withoutStatistics
        )}
}

extension PaginatorSequence where OperationStackInput == DescribeDataMigrationsInput, OperationStackOutput == DescribeDataMigrationsOutput {
    /// This paginator transforms the `AsyncSequence` returned by `describeDataMigrationsPaginated`
    /// to access the nested member `[DatabaseMigrationClientTypes.DataMigration]`
    /// - Returns: `[DatabaseMigrationClientTypes.DataMigration]`
    public func dataMigrations() async throws -> [DatabaseMigrationClientTypes.DataMigration] {
        return try await self.asyncCompactMap { item in item.dataMigrations }
    }
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeDataProvidersOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeDataProvidersInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeDataProvidersOutput`
    public func describeDataProvidersPaginated(input: DescribeDataProvidersInput) -> ClientRuntime.PaginatorSequence<DescribeDataProvidersInput, DescribeDataProvidersOutput> {
        return ClientRuntime.PaginatorSequence<DescribeDataProvidersInput, DescribeDataProvidersOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeDataProviders(input:))
    }
}

extension DescribeDataProvidersInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeDataProvidersInput {
        return DescribeDataProvidersInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeEndpointsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeEndpointsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeEndpointsOutput`
    public func describeEndpointsPaginated(input: DescribeEndpointsInput) -> ClientRuntime.PaginatorSequence<DescribeEndpointsInput, DescribeEndpointsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeEndpointsInput, DescribeEndpointsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeEndpoints(input:))
    }
}

extension DescribeEndpointsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeEndpointsInput {
        return DescribeEndpointsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeEndpointSettingsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeEndpointSettingsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeEndpointSettingsOutput`
    public func describeEndpointSettingsPaginated(input: DescribeEndpointSettingsInput) -> ClientRuntime.PaginatorSequence<DescribeEndpointSettingsInput, DescribeEndpointSettingsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeEndpointSettingsInput, DescribeEndpointSettingsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeEndpointSettings(input:))
    }
}

extension DescribeEndpointSettingsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeEndpointSettingsInput {
        return DescribeEndpointSettingsInput(
            engineName: self.engineName,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeEndpointTypesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeEndpointTypesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeEndpointTypesOutput`
    public func describeEndpointTypesPaginated(input: DescribeEndpointTypesInput) -> ClientRuntime.PaginatorSequence<DescribeEndpointTypesInput, DescribeEndpointTypesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeEndpointTypesInput, DescribeEndpointTypesOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeEndpointTypes(input:))
    }
}

extension DescribeEndpointTypesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeEndpointTypesInput {
        return DescribeEndpointTypesInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeEngineVersionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeEngineVersionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeEngineVersionsOutput`
    public func describeEngineVersionsPaginated(input: DescribeEngineVersionsInput) -> ClientRuntime.PaginatorSequence<DescribeEngineVersionsInput, DescribeEngineVersionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeEngineVersionsInput, DescribeEngineVersionsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeEngineVersions(input:))
    }
}

extension DescribeEngineVersionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeEngineVersionsInput {
        return DescribeEngineVersionsInput(
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeEventsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeEventsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeEventsOutput`
    public func describeEventsPaginated(input: DescribeEventsInput) -> ClientRuntime.PaginatorSequence<DescribeEventsInput, DescribeEventsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeEventsInput, DescribeEventsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeEvents(input:))
    }
}

extension DescribeEventsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeEventsInput {
        return DescribeEventsInput(
            duration: self.duration,
            endTime: self.endTime,
            eventCategories: self.eventCategories,
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            sourceIdentifier: self.sourceIdentifier,
            sourceType: self.sourceType,
            startTime: self.startTime
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeEventSubscriptionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeEventSubscriptionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeEventSubscriptionsOutput`
    public func describeEventSubscriptionsPaginated(input: DescribeEventSubscriptionsInput) -> ClientRuntime.PaginatorSequence<DescribeEventSubscriptionsInput, DescribeEventSubscriptionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeEventSubscriptionsInput, DescribeEventSubscriptionsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeEventSubscriptions(input:))
    }
}

extension DescribeEventSubscriptionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeEventSubscriptionsInput {
        return DescribeEventSubscriptionsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            subscriptionName: self.subscriptionName
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeExtensionPackAssociationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeExtensionPackAssociationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeExtensionPackAssociationsOutput`
    public func describeExtensionPackAssociationsPaginated(input: DescribeExtensionPackAssociationsInput) -> ClientRuntime.PaginatorSequence<DescribeExtensionPackAssociationsInput, DescribeExtensionPackAssociationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeExtensionPackAssociationsInput, DescribeExtensionPackAssociationsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeExtensionPackAssociations(input:))
    }
}

extension DescribeExtensionPackAssociationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeExtensionPackAssociationsInput {
        return DescribeExtensionPackAssociationsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            migrationProjectIdentifier: self.migrationProjectIdentifier
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeFleetAdvisorCollectorsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeFleetAdvisorCollectorsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeFleetAdvisorCollectorsOutput`
    public func describeFleetAdvisorCollectorsPaginated(input: DescribeFleetAdvisorCollectorsInput) -> ClientRuntime.PaginatorSequence<DescribeFleetAdvisorCollectorsInput, DescribeFleetAdvisorCollectorsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeFleetAdvisorCollectorsInput, DescribeFleetAdvisorCollectorsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeFleetAdvisorCollectors(input:))
    }
}

extension DescribeFleetAdvisorCollectorsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeFleetAdvisorCollectorsInput {
        return DescribeFleetAdvisorCollectorsInput(
            filters: self.filters,
            maxRecords: self.maxRecords,
            nextToken: token
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeFleetAdvisorDatabasesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeFleetAdvisorDatabasesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeFleetAdvisorDatabasesOutput`
    public func describeFleetAdvisorDatabasesPaginated(input: DescribeFleetAdvisorDatabasesInput) -> ClientRuntime.PaginatorSequence<DescribeFleetAdvisorDatabasesInput, DescribeFleetAdvisorDatabasesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeFleetAdvisorDatabasesInput, DescribeFleetAdvisorDatabasesOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeFleetAdvisorDatabases(input:))
    }
}

extension DescribeFleetAdvisorDatabasesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeFleetAdvisorDatabasesInput {
        return DescribeFleetAdvisorDatabasesInput(
            filters: self.filters,
            maxRecords: self.maxRecords,
            nextToken: token
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeFleetAdvisorLsaAnalysisOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeFleetAdvisorLsaAnalysisInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeFleetAdvisorLsaAnalysisOutput`
    public func describeFleetAdvisorLsaAnalysisPaginated(input: DescribeFleetAdvisorLsaAnalysisInput) -> ClientRuntime.PaginatorSequence<DescribeFleetAdvisorLsaAnalysisInput, DescribeFleetAdvisorLsaAnalysisOutput> {
        return ClientRuntime.PaginatorSequence<DescribeFleetAdvisorLsaAnalysisInput, DescribeFleetAdvisorLsaAnalysisOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeFleetAdvisorLsaAnalysis(input:))
    }
}

extension DescribeFleetAdvisorLsaAnalysisInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeFleetAdvisorLsaAnalysisInput {
        return DescribeFleetAdvisorLsaAnalysisInput(
            maxRecords: self.maxRecords,
            nextToken: token
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeFleetAdvisorSchemaObjectSummaryOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeFleetAdvisorSchemaObjectSummaryInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeFleetAdvisorSchemaObjectSummaryOutput`
    public func describeFleetAdvisorSchemaObjectSummaryPaginated(input: DescribeFleetAdvisorSchemaObjectSummaryInput) -> ClientRuntime.PaginatorSequence<DescribeFleetAdvisorSchemaObjectSummaryInput, DescribeFleetAdvisorSchemaObjectSummaryOutput> {
        return ClientRuntime.PaginatorSequence<DescribeFleetAdvisorSchemaObjectSummaryInput, DescribeFleetAdvisorSchemaObjectSummaryOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeFleetAdvisorSchemaObjectSummary(input:))
    }
}

extension DescribeFleetAdvisorSchemaObjectSummaryInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeFleetAdvisorSchemaObjectSummaryInput {
        return DescribeFleetAdvisorSchemaObjectSummaryInput(
            filters: self.filters,
            maxRecords: self.maxRecords,
            nextToken: token
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeFleetAdvisorSchemasOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeFleetAdvisorSchemasInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeFleetAdvisorSchemasOutput`
    public func describeFleetAdvisorSchemasPaginated(input: DescribeFleetAdvisorSchemasInput) -> ClientRuntime.PaginatorSequence<DescribeFleetAdvisorSchemasInput, DescribeFleetAdvisorSchemasOutput> {
        return ClientRuntime.PaginatorSequence<DescribeFleetAdvisorSchemasInput, DescribeFleetAdvisorSchemasOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeFleetAdvisorSchemas(input:))
    }
}

extension DescribeFleetAdvisorSchemasInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeFleetAdvisorSchemasInput {
        return DescribeFleetAdvisorSchemasInput(
            filters: self.filters,
            maxRecords: self.maxRecords,
            nextToken: token
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeInstanceProfilesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeInstanceProfilesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeInstanceProfilesOutput`
    public func describeInstanceProfilesPaginated(input: DescribeInstanceProfilesInput) -> ClientRuntime.PaginatorSequence<DescribeInstanceProfilesInput, DescribeInstanceProfilesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeInstanceProfilesInput, DescribeInstanceProfilesOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeInstanceProfiles(input:))
    }
}

extension DescribeInstanceProfilesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeInstanceProfilesInput {
        return DescribeInstanceProfilesInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeMetadataModelAssessmentsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeMetadataModelAssessmentsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeMetadataModelAssessmentsOutput`
    public func describeMetadataModelAssessmentsPaginated(input: DescribeMetadataModelAssessmentsInput) -> ClientRuntime.PaginatorSequence<DescribeMetadataModelAssessmentsInput, DescribeMetadataModelAssessmentsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeMetadataModelAssessmentsInput, DescribeMetadataModelAssessmentsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeMetadataModelAssessments(input:))
    }
}

extension DescribeMetadataModelAssessmentsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeMetadataModelAssessmentsInput {
        return DescribeMetadataModelAssessmentsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            migrationProjectIdentifier: self.migrationProjectIdentifier
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeMetadataModelConversionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeMetadataModelConversionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeMetadataModelConversionsOutput`
    public func describeMetadataModelConversionsPaginated(input: DescribeMetadataModelConversionsInput) -> ClientRuntime.PaginatorSequence<DescribeMetadataModelConversionsInput, DescribeMetadataModelConversionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeMetadataModelConversionsInput, DescribeMetadataModelConversionsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeMetadataModelConversions(input:))
    }
}

extension DescribeMetadataModelConversionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeMetadataModelConversionsInput {
        return DescribeMetadataModelConversionsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            migrationProjectIdentifier: self.migrationProjectIdentifier
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeMetadataModelExportsAsScriptOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeMetadataModelExportsAsScriptInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeMetadataModelExportsAsScriptOutput`
    public func describeMetadataModelExportsAsScriptPaginated(input: DescribeMetadataModelExportsAsScriptInput) -> ClientRuntime.PaginatorSequence<DescribeMetadataModelExportsAsScriptInput, DescribeMetadataModelExportsAsScriptOutput> {
        return ClientRuntime.PaginatorSequence<DescribeMetadataModelExportsAsScriptInput, DescribeMetadataModelExportsAsScriptOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeMetadataModelExportsAsScript(input:))
    }
}

extension DescribeMetadataModelExportsAsScriptInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeMetadataModelExportsAsScriptInput {
        return DescribeMetadataModelExportsAsScriptInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            migrationProjectIdentifier: self.migrationProjectIdentifier
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeMetadataModelExportsToTargetOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeMetadataModelExportsToTargetInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeMetadataModelExportsToTargetOutput`
    public func describeMetadataModelExportsToTargetPaginated(input: DescribeMetadataModelExportsToTargetInput) -> ClientRuntime.PaginatorSequence<DescribeMetadataModelExportsToTargetInput, DescribeMetadataModelExportsToTargetOutput> {
        return ClientRuntime.PaginatorSequence<DescribeMetadataModelExportsToTargetInput, DescribeMetadataModelExportsToTargetOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeMetadataModelExportsToTarget(input:))
    }
}

extension DescribeMetadataModelExportsToTargetInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeMetadataModelExportsToTargetInput {
        return DescribeMetadataModelExportsToTargetInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            migrationProjectIdentifier: self.migrationProjectIdentifier
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeMetadataModelImportsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeMetadataModelImportsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeMetadataModelImportsOutput`
    public func describeMetadataModelImportsPaginated(input: DescribeMetadataModelImportsInput) -> ClientRuntime.PaginatorSequence<DescribeMetadataModelImportsInput, DescribeMetadataModelImportsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeMetadataModelImportsInput, DescribeMetadataModelImportsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeMetadataModelImports(input:))
    }
}

extension DescribeMetadataModelImportsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeMetadataModelImportsInput {
        return DescribeMetadataModelImportsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            migrationProjectIdentifier: self.migrationProjectIdentifier
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeMigrationProjectsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeMigrationProjectsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeMigrationProjectsOutput`
    public func describeMigrationProjectsPaginated(input: DescribeMigrationProjectsInput) -> ClientRuntime.PaginatorSequence<DescribeMigrationProjectsInput, DescribeMigrationProjectsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeMigrationProjectsInput, DescribeMigrationProjectsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeMigrationProjects(input:))
    }
}

extension DescribeMigrationProjectsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeMigrationProjectsInput {
        return DescribeMigrationProjectsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeOrderableReplicationInstancesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeOrderableReplicationInstancesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeOrderableReplicationInstancesOutput`
    public func describeOrderableReplicationInstancesPaginated(input: DescribeOrderableReplicationInstancesInput) -> ClientRuntime.PaginatorSequence<DescribeOrderableReplicationInstancesInput, DescribeOrderableReplicationInstancesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeOrderableReplicationInstancesInput, DescribeOrderableReplicationInstancesOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeOrderableReplicationInstances(input:))
    }
}

extension DescribeOrderableReplicationInstancesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeOrderableReplicationInstancesInput {
        return DescribeOrderableReplicationInstancesInput(
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribePendingMaintenanceActionsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribePendingMaintenanceActionsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribePendingMaintenanceActionsOutput`
    public func describePendingMaintenanceActionsPaginated(input: DescribePendingMaintenanceActionsInput) -> ClientRuntime.PaginatorSequence<DescribePendingMaintenanceActionsInput, DescribePendingMaintenanceActionsOutput> {
        return ClientRuntime.PaginatorSequence<DescribePendingMaintenanceActionsInput, DescribePendingMaintenanceActionsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describePendingMaintenanceActions(input:))
    }
}

extension DescribePendingMaintenanceActionsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribePendingMaintenanceActionsInput {
        return DescribePendingMaintenanceActionsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            replicationInstanceArn: self.replicationInstanceArn
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeRecommendationLimitationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeRecommendationLimitationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeRecommendationLimitationsOutput`
    public func describeRecommendationLimitationsPaginated(input: DescribeRecommendationLimitationsInput) -> ClientRuntime.PaginatorSequence<DescribeRecommendationLimitationsInput, DescribeRecommendationLimitationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeRecommendationLimitationsInput, DescribeRecommendationLimitationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeRecommendationLimitations(input:))
    }
}

extension DescribeRecommendationLimitationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeRecommendationLimitationsInput {
        return DescribeRecommendationLimitationsInput(
            filters: self.filters,
            maxRecords: self.maxRecords,
            nextToken: token
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeRecommendationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeRecommendationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeRecommendationsOutput`
    public func describeRecommendationsPaginated(input: DescribeRecommendationsInput) -> ClientRuntime.PaginatorSequence<DescribeRecommendationsInput, DescribeRecommendationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeRecommendationsInput, DescribeRecommendationsOutput>(input: input, inputKey: \.nextToken, outputKey: \.nextToken, paginationFunction: self.describeRecommendations(input:))
    }
}

extension DescribeRecommendationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeRecommendationsInput {
        return DescribeRecommendationsInput(
            filters: self.filters,
            maxRecords: self.maxRecords,
            nextToken: token
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeReplicationConfigsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReplicationConfigsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReplicationConfigsOutput`
    public func describeReplicationConfigsPaginated(input: DescribeReplicationConfigsInput) -> ClientRuntime.PaginatorSequence<DescribeReplicationConfigsInput, DescribeReplicationConfigsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReplicationConfigsInput, DescribeReplicationConfigsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeReplicationConfigs(input:))
    }
}

extension DescribeReplicationConfigsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReplicationConfigsInput {
        return DescribeReplicationConfigsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeReplicationInstancesOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReplicationInstancesInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReplicationInstancesOutput`
    public func describeReplicationInstancesPaginated(input: DescribeReplicationInstancesInput) -> ClientRuntime.PaginatorSequence<DescribeReplicationInstancesInput, DescribeReplicationInstancesOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReplicationInstancesInput, DescribeReplicationInstancesOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeReplicationInstances(input:))
    }
}

extension DescribeReplicationInstancesInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReplicationInstancesInput {
        return DescribeReplicationInstancesInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeReplicationInstanceTaskLogsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReplicationInstanceTaskLogsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReplicationInstanceTaskLogsOutput`
    public func describeReplicationInstanceTaskLogsPaginated(input: DescribeReplicationInstanceTaskLogsInput) -> ClientRuntime.PaginatorSequence<DescribeReplicationInstanceTaskLogsInput, DescribeReplicationInstanceTaskLogsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReplicationInstanceTaskLogsInput, DescribeReplicationInstanceTaskLogsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeReplicationInstanceTaskLogs(input:))
    }
}

extension DescribeReplicationInstanceTaskLogsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReplicationInstanceTaskLogsInput {
        return DescribeReplicationInstanceTaskLogsInput(
            marker: token,
            maxRecords: self.maxRecords,
            replicationInstanceArn: self.replicationInstanceArn
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeReplicationsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReplicationsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReplicationsOutput`
    public func describeReplicationsPaginated(input: DescribeReplicationsInput) -> ClientRuntime.PaginatorSequence<DescribeReplicationsInput, DescribeReplicationsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReplicationsInput, DescribeReplicationsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeReplications(input:))
    }
}

extension DescribeReplicationsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReplicationsInput {
        return DescribeReplicationsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeReplicationSubnetGroupsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReplicationSubnetGroupsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReplicationSubnetGroupsOutput`
    public func describeReplicationSubnetGroupsPaginated(input: DescribeReplicationSubnetGroupsInput) -> ClientRuntime.PaginatorSequence<DescribeReplicationSubnetGroupsInput, DescribeReplicationSubnetGroupsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReplicationSubnetGroupsInput, DescribeReplicationSubnetGroupsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeReplicationSubnetGroups(input:))
    }
}

extension DescribeReplicationSubnetGroupsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReplicationSubnetGroupsInput {
        return DescribeReplicationSubnetGroupsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeReplicationTableStatisticsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReplicationTableStatisticsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReplicationTableStatisticsOutput`
    public func describeReplicationTableStatisticsPaginated(input: DescribeReplicationTableStatisticsInput) -> ClientRuntime.PaginatorSequence<DescribeReplicationTableStatisticsInput, DescribeReplicationTableStatisticsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReplicationTableStatisticsInput, DescribeReplicationTableStatisticsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeReplicationTableStatistics(input:))
    }
}

extension DescribeReplicationTableStatisticsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReplicationTableStatisticsInput {
        return DescribeReplicationTableStatisticsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            replicationConfigArn: self.replicationConfigArn
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeReplicationTaskAssessmentResultsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReplicationTaskAssessmentResultsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReplicationTaskAssessmentResultsOutput`
    public func describeReplicationTaskAssessmentResultsPaginated(input: DescribeReplicationTaskAssessmentResultsInput) -> ClientRuntime.PaginatorSequence<DescribeReplicationTaskAssessmentResultsInput, DescribeReplicationTaskAssessmentResultsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReplicationTaskAssessmentResultsInput, DescribeReplicationTaskAssessmentResultsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeReplicationTaskAssessmentResults(input:))
    }
}

extension DescribeReplicationTaskAssessmentResultsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReplicationTaskAssessmentResultsInput {
        return DescribeReplicationTaskAssessmentResultsInput(
            marker: token,
            maxRecords: self.maxRecords,
            replicationTaskArn: self.replicationTaskArn
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeReplicationTaskAssessmentRunsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReplicationTaskAssessmentRunsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReplicationTaskAssessmentRunsOutput`
    public func describeReplicationTaskAssessmentRunsPaginated(input: DescribeReplicationTaskAssessmentRunsInput) -> ClientRuntime.PaginatorSequence<DescribeReplicationTaskAssessmentRunsInput, DescribeReplicationTaskAssessmentRunsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReplicationTaskAssessmentRunsInput, DescribeReplicationTaskAssessmentRunsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeReplicationTaskAssessmentRuns(input:))
    }
}

extension DescribeReplicationTaskAssessmentRunsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReplicationTaskAssessmentRunsInput {
        return DescribeReplicationTaskAssessmentRunsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeReplicationTaskIndividualAssessmentsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReplicationTaskIndividualAssessmentsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReplicationTaskIndividualAssessmentsOutput`
    public func describeReplicationTaskIndividualAssessmentsPaginated(input: DescribeReplicationTaskIndividualAssessmentsInput) -> ClientRuntime.PaginatorSequence<DescribeReplicationTaskIndividualAssessmentsInput, DescribeReplicationTaskIndividualAssessmentsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReplicationTaskIndividualAssessmentsInput, DescribeReplicationTaskIndividualAssessmentsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeReplicationTaskIndividualAssessments(input:))
    }
}

extension DescribeReplicationTaskIndividualAssessmentsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReplicationTaskIndividualAssessmentsInput {
        return DescribeReplicationTaskIndividualAssessmentsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeReplicationTasksOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeReplicationTasksInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeReplicationTasksOutput`
    public func describeReplicationTasksPaginated(input: DescribeReplicationTasksInput) -> ClientRuntime.PaginatorSequence<DescribeReplicationTasksInput, DescribeReplicationTasksOutput> {
        return ClientRuntime.PaginatorSequence<DescribeReplicationTasksInput, DescribeReplicationTasksOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeReplicationTasks(input:))
    }
}

extension DescribeReplicationTasksInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeReplicationTasksInput {
        return DescribeReplicationTasksInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            withoutSettings: self.withoutSettings
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeSchemasOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeSchemasInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeSchemasOutput`
    public func describeSchemasPaginated(input: DescribeSchemasInput) -> ClientRuntime.PaginatorSequence<DescribeSchemasInput, DescribeSchemasOutput> {
        return ClientRuntime.PaginatorSequence<DescribeSchemasInput, DescribeSchemasOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeSchemas(input:))
    }
}

extension DescribeSchemasInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeSchemasInput {
        return DescribeSchemasInput(
            endpointArn: self.endpointArn,
            marker: token,
            maxRecords: self.maxRecords
        )}
}
extension DatabaseMigrationClient {
    /// Paginate over `[DescribeTableStatisticsOutput]` results.
    ///
    /// When this operation is called, an `AsyncSequence` is created. AsyncSequences are lazy so no service
    /// calls are made until the sequence is iterated over. This also means there is no guarantee that the request is valid
    /// until then. If there are errors in your request, you will see the failures only after you start iterating.
    /// - Parameters:
    ///     - input: A `[DescribeTableStatisticsInput]` to start pagination
    /// - Returns: An `AsyncSequence` that can iterate over `DescribeTableStatisticsOutput`
    public func describeTableStatisticsPaginated(input: DescribeTableStatisticsInput) -> ClientRuntime.PaginatorSequence<DescribeTableStatisticsInput, DescribeTableStatisticsOutput> {
        return ClientRuntime.PaginatorSequence<DescribeTableStatisticsInput, DescribeTableStatisticsOutput>(input: input, inputKey: \.marker, outputKey: \.marker, paginationFunction: self.describeTableStatistics(input:))
    }
}

extension DescribeTableStatisticsInput: ClientRuntime.PaginateToken {
    public func usingPaginationToken(_ token: Swift.String) -> DescribeTableStatisticsInput {
        return DescribeTableStatisticsInput(
            filters: self.filters,
            marker: token,
            maxRecords: self.maxRecords,
            replicationTaskArn: self.replicationTaskArn
        )}
}
