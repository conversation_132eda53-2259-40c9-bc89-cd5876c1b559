//
// Copyright Amazon.com Inc. or its affiliates.
// All Rights Reserved.
//
// SPDX-License-Identifier: Apache-2.0
//

// Code generated by smithy-swift-codegen. DO NOT EDIT!

import class SmithyWaitersAPI.Waiter
import enum SmithyWaitersAPI.JMESUtils
import protocol ClientRuntime.ServiceError
import struct SmithyWaitersAPI.WaiterConfiguration
import struct SmithyWaitersAPI.WaiterOptions
import struct SmithyWaitersAPI.WaiterOutcome

extension EC2Client {

    static func bundleTaskCompleteWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeBundleTasksInput, DescribeBundleTasksOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeBundleTasksInput, DescribeBundleTasksOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeBundleTasksInput, result: Swift.Result<DescribeBundleTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "BundleTasks[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "complete"
                guard case .success(let output) = result else { return false }
                let bundleTasks = output.bundleTasks
                let projection: [EC2ClientTypes.BundleTaskState]? = bundleTasks?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "complete") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeBundleTasksInput, result: Swift.Result<DescribeBundleTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "BundleTasks[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "failed"
                guard case .success(let output) = result else { return false }
                let bundleTasks = output.bundleTasks
                let projection: [EC2ClientTypes.BundleTaskState]? = bundleTasks?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "failed") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeBundleTasksInput, DescribeBundleTasksOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the BundleTaskComplete event on the describeBundleTasks operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeBundleTasksInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilBundleTaskComplete(options: SmithyWaitersAPI.WaiterOptions, input: DescribeBundleTasksInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeBundleTasksOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.bundleTaskCompleteWaiterConfig(), operation: self.describeBundleTasks(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func conversionTaskCancelledWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeConversionTasksInput, DescribeConversionTasksOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeConversionTasksInput, DescribeConversionTasksOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeConversionTasksInput, result: Swift.Result<DescribeConversionTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ConversionTasks[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "cancelled"
                guard case .success(let output) = result else { return false }
                let conversionTasks = output.conversionTasks
                let projection: [EC2ClientTypes.ConversionTaskState]? = conversionTasks?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "cancelled") } ?? false)
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeConversionTasksInput, DescribeConversionTasksOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ConversionTaskCancelled event on the describeConversionTasks operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeConversionTasksInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilConversionTaskCancelled(options: SmithyWaitersAPI.WaiterOptions, input: DescribeConversionTasksInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeConversionTasksOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.conversionTaskCancelledWaiterConfig(), operation: self.describeConversionTasks(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func conversionTaskCompletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeConversionTasksInput, DescribeConversionTasksOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeConversionTasksInput, DescribeConversionTasksOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeConversionTasksInput, result: Swift.Result<DescribeConversionTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ConversionTasks[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "completed"
                guard case .success(let output) = result else { return false }
                let conversionTasks = output.conversionTasks
                let projection: [EC2ClientTypes.ConversionTaskState]? = conversionTasks?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "completed") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeConversionTasksInput, result: Swift.Result<DescribeConversionTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ConversionTasks[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "cancelled"
                guard case .success(let output) = result else { return false }
                let conversionTasks = output.conversionTasks
                let projection: [EC2ClientTypes.ConversionTaskState]? = conversionTasks?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "cancelled") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeConversionTasksInput, result: Swift.Result<DescribeConversionTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ConversionTasks[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "cancelling"
                guard case .success(let output) = result else { return false }
                let conversionTasks = output.conversionTasks
                let projection: [EC2ClientTypes.ConversionTaskState]? = conversionTasks?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "cancelling") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeConversionTasksInput, DescribeConversionTasksOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ConversionTaskCompleted event on the describeConversionTasks operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeConversionTasksInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilConversionTaskCompleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeConversionTasksInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeConversionTasksOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.conversionTaskCompletedWaiterConfig(), operation: self.describeConversionTasks(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func conversionTaskDeletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeConversionTasksInput, DescribeConversionTasksOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeConversionTasksInput, DescribeConversionTasksOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeConversionTasksInput, result: Swift.Result<DescribeConversionTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ConversionTasks[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "deleted"
                guard case .success(let output) = result else { return false }
                let conversionTasks = output.conversionTasks
                let projection: [EC2ClientTypes.ConversionTaskState]? = conversionTasks?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleted") } ?? false)
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeConversionTasksInput, DescribeConversionTasksOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ConversionTaskDeleted event on the describeConversionTasks operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeConversionTasksInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilConversionTaskDeleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeConversionTasksInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeConversionTasksOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.conversionTaskDeletedWaiterConfig(), operation: self.describeConversionTasks(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func customerGatewayAvailableWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeCustomerGatewaysInput, DescribeCustomerGatewaysOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeCustomerGatewaysInput, DescribeCustomerGatewaysOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeCustomerGatewaysInput, result: Swift.Result<DescribeCustomerGatewaysOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CustomerGateways[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "available"
                guard case .success(let output) = result else { return false }
                let customerGateways = output.customerGateways
                let projection: [Swift.String]? = customerGateways?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "available") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeCustomerGatewaysInput, result: Swift.Result<DescribeCustomerGatewaysOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CustomerGateways[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleted"
                guard case .success(let output) = result else { return false }
                let customerGateways = output.customerGateways
                let projection: [Swift.String]? = customerGateways?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleted") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeCustomerGatewaysInput, result: Swift.Result<DescribeCustomerGatewaysOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "CustomerGateways[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleting"
                guard case .success(let output) = result else { return false }
                let customerGateways = output.customerGateways
                let projection: [Swift.String]? = customerGateways?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleting") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeCustomerGatewaysInput, DescribeCustomerGatewaysOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the CustomerGatewayAvailable event on the describeCustomerGateways operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeCustomerGatewaysInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilCustomerGatewayAvailable(options: SmithyWaitersAPI.WaiterOptions, input: DescribeCustomerGatewaysInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeCustomerGatewaysOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.customerGatewayAvailableWaiterConfig(), operation: self.describeCustomerGateways(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func exportTaskCancelledWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeExportTasksInput, DescribeExportTasksOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeExportTasksInput, DescribeExportTasksOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeExportTasksInput, result: Swift.Result<DescribeExportTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ExportTasks[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "cancelled"
                guard case .success(let output) = result else { return false }
                let exportTasks = output.exportTasks
                let projection: [EC2ClientTypes.ExportTaskState]? = exportTasks?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "cancelled") } ?? false)
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeExportTasksInput, DescribeExportTasksOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ExportTaskCancelled event on the describeExportTasks operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeExportTasksInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilExportTaskCancelled(options: SmithyWaitersAPI.WaiterOptions, input: DescribeExportTasksInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeExportTasksOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.exportTaskCancelledWaiterConfig(), operation: self.describeExportTasks(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func exportTaskCompletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeExportTasksInput, DescribeExportTasksOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeExportTasksInput, DescribeExportTasksOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeExportTasksInput, result: Swift.Result<DescribeExportTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ExportTasks[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "completed"
                guard case .success(let output) = result else { return false }
                let exportTasks = output.exportTasks
                let projection: [EC2ClientTypes.ExportTaskState]? = exportTasks?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "completed") } ?? false)
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeExportTasksInput, DescribeExportTasksOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ExportTaskCompleted event on the describeExportTasks operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeExportTasksInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilExportTaskCompleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeExportTasksInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeExportTasksOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.exportTaskCompletedWaiterConfig(), operation: self.describeExportTasks(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func imageAvailableWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeImagesInput, DescribeImagesOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeImagesInput, DescribeImagesOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeImagesInput, result: Swift.Result<DescribeImagesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Images[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "available"
                guard case .success(let output) = result else { return false }
                let images = output.images
                let projection: [EC2ClientTypes.ImageState]? = images?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "available") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeImagesInput, result: Swift.Result<DescribeImagesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Images[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "failed"
                guard case .success(let output) = result else { return false }
                let images = output.images
                let projection: [EC2ClientTypes.ImageState]? = images?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "failed") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeImagesInput, DescribeImagesOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ImageAvailable event on the describeImages operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeImagesInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilImageAvailable(options: SmithyWaitersAPI.WaiterOptions, input: DescribeImagesInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeImagesOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.imageAvailableWaiterConfig(), operation: self.describeImages(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func imageExistsWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeImagesInput, DescribeImagesOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeImagesInput, DescribeImagesOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeImagesInput, result: Swift.Result<DescribeImagesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "length(Images[]) > `0`"
                // JMESPath comparator: "booleanEquals"
                // JMESPath expected value: "true"
                guard case .success(let output) = result else { return false }
                let images = output.images
                let count = Double(images?.count ?? 0)
                let number = Double(0.0)
                let comparison = SmithyWaitersAPI.JMESUtils.compare(count, >, number)
                return SmithyWaitersAPI.JMESUtils.compare(comparison, ==, true)
            }),
            .init(state: .retry, matcher: { (input: DescribeImagesInput, result: Swift.Result<DescribeImagesOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "InvalidAMIID.NotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeImagesInput, DescribeImagesOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the ImageExists event on the describeImages operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeImagesInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilImageExists(options: SmithyWaitersAPI.WaiterOptions, input: DescribeImagesInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeImagesOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.imageExistsWaiterConfig(), operation: self.describeImages(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func snapshotImportedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeImportSnapshotTasksInput, DescribeImportSnapshotTasksOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeImportSnapshotTasksInput, DescribeImportSnapshotTasksOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeImportSnapshotTasksInput, result: Swift.Result<DescribeImportSnapshotTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ImportSnapshotTasks[].SnapshotTaskDetail.Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "completed"
                guard case .success(let output) = result else { return false }
                let importSnapshotTasks = output.importSnapshotTasks
                let projection: [Swift.String]? = importSnapshotTasks?.compactMap { original in
                    let snapshotTaskDetail = original.snapshotTaskDetail
                    let status = snapshotTaskDetail?.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "completed") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeImportSnapshotTasksInput, result: Swift.Result<DescribeImportSnapshotTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "ImportSnapshotTasks[].SnapshotTaskDetail.Status"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "error"
                guard case .success(let output) = result else { return false }
                let importSnapshotTasks = output.importSnapshotTasks
                let projection: [Swift.String]? = importSnapshotTasks?.compactMap { original in
                    let snapshotTaskDetail = original.snapshotTaskDetail
                    let status = snapshotTaskDetail?.status
                    return status
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "error") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeImportSnapshotTasksInput, DescribeImportSnapshotTasksOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the SnapshotImported event on the describeImportSnapshotTasks operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeImportSnapshotTasksInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilSnapshotImported(options: SmithyWaitersAPI.WaiterOptions, input: DescribeImportSnapshotTasksInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeImportSnapshotTasksOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.snapshotImportedWaiterConfig(), operation: self.describeImportSnapshotTasks(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func instanceExistsWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeInstancesInput, DescribeInstancesOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeInstancesInput, DescribeInstancesOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeInstancesInput, result: Swift.Result<DescribeInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "length(Reservations[]) > `0`"
                // JMESPath comparator: "booleanEquals"
                // JMESPath expected value: "true"
                guard case .success(let output) = result else { return false }
                let reservations = output.reservations
                let count = Double(reservations?.count ?? 0)
                let number = Double(0.0)
                let comparison = SmithyWaitersAPI.JMESUtils.compare(count, >, number)
                return SmithyWaitersAPI.JMESUtils.compare(comparison, ==, true)
            }),
            .init(state: .retry, matcher: { (input: DescribeInstancesInput, result: Swift.Result<DescribeInstancesOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "InvalidInstanceID.NotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeInstancesInput, DescribeInstancesOutput>(acceptors: acceptors, minDelay: 5.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the InstanceExists event on the describeInstances operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeInstancesInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilInstanceExists(options: SmithyWaitersAPI.WaiterOptions, input: DescribeInstancesInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeInstancesOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.instanceExistsWaiterConfig(), operation: self.describeInstances(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func instanceRunningWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeInstancesInput, DescribeInstancesOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeInstancesInput, DescribeInstancesOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeInstancesInput, result: Swift.Result<DescribeInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Reservations[].Instances[].State.Name"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "running"
                guard case .success(let output) = result else { return false }
                let reservations = output.reservations
                let projection: [[EC2ClientTypes.Instance]]? = reservations?.compactMap { original in
                    let instances = original.instances
                    return instances
                }
                let flattened = projection?.flatMap { $0 }
                let projection2: [EC2ClientTypes.InstanceStateName]? = flattened?.compactMap { original in
                    let state = original.state
                    let name = state?.name
                    return name
                }
                return (projection2?.count ?? 0) > 1 && (projection2?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "running") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeInstancesInput, result: Swift.Result<DescribeInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Reservations[].Instances[].State.Name"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "shutting-down"
                guard case .success(let output) = result else { return false }
                let reservations = output.reservations
                let projection: [[EC2ClientTypes.Instance]]? = reservations?.compactMap { original in
                    let instances = original.instances
                    return instances
                }
                let flattened = projection?.flatMap { $0 }
                let projection2: [EC2ClientTypes.InstanceStateName]? = flattened?.compactMap { original in
                    let state = original.state
                    let name = state?.name
                    return name
                }
                return projection2?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "shutting-down") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeInstancesInput, result: Swift.Result<DescribeInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Reservations[].Instances[].State.Name"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "terminated"
                guard case .success(let output) = result else { return false }
                let reservations = output.reservations
                let projection: [[EC2ClientTypes.Instance]]? = reservations?.compactMap { original in
                    let instances = original.instances
                    return instances
                }
                let flattened = projection?.flatMap { $0 }
                let projection2: [EC2ClientTypes.InstanceStateName]? = flattened?.compactMap { original in
                    let state = original.state
                    let name = state?.name
                    return name
                }
                return projection2?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "terminated") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeInstancesInput, result: Swift.Result<DescribeInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Reservations[].Instances[].State.Name"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "stopping"
                guard case .success(let output) = result else { return false }
                let reservations = output.reservations
                let projection: [[EC2ClientTypes.Instance]]? = reservations?.compactMap { original in
                    let instances = original.instances
                    return instances
                }
                let flattened = projection?.flatMap { $0 }
                let projection2: [EC2ClientTypes.InstanceStateName]? = flattened?.compactMap { original in
                    let state = original.state
                    let name = state?.name
                    return name
                }
                return projection2?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "stopping") }) ?? false
            }),
            .init(state: .retry, matcher: { (input: DescribeInstancesInput, result: Swift.Result<DescribeInstancesOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "InvalidInstanceID.NotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeInstancesInput, DescribeInstancesOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the InstanceRunning event on the describeInstances operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeInstancesInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilInstanceRunning(options: SmithyWaitersAPI.WaiterOptions, input: DescribeInstancesInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeInstancesOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.instanceRunningWaiterConfig(), operation: self.describeInstances(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func instanceStoppedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeInstancesInput, DescribeInstancesOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeInstancesInput, DescribeInstancesOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeInstancesInput, result: Swift.Result<DescribeInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Reservations[].Instances[].State.Name"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "stopped"
                guard case .success(let output) = result else { return false }
                let reservations = output.reservations
                let projection: [[EC2ClientTypes.Instance]]? = reservations?.compactMap { original in
                    let instances = original.instances
                    return instances
                }
                let flattened = projection?.flatMap { $0 }
                let projection2: [EC2ClientTypes.InstanceStateName]? = flattened?.compactMap { original in
                    let state = original.state
                    let name = state?.name
                    return name
                }
                return (projection2?.count ?? 0) > 1 && (projection2?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "stopped") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeInstancesInput, result: Swift.Result<DescribeInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Reservations[].Instances[].State.Name"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "pending"
                guard case .success(let output) = result else { return false }
                let reservations = output.reservations
                let projection: [[EC2ClientTypes.Instance]]? = reservations?.compactMap { original in
                    let instances = original.instances
                    return instances
                }
                let flattened = projection?.flatMap { $0 }
                let projection2: [EC2ClientTypes.InstanceStateName]? = flattened?.compactMap { original in
                    let state = original.state
                    let name = state?.name
                    return name
                }
                return projection2?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "pending") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeInstancesInput, result: Swift.Result<DescribeInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Reservations[].Instances[].State.Name"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "terminated"
                guard case .success(let output) = result else { return false }
                let reservations = output.reservations
                let projection: [[EC2ClientTypes.Instance]]? = reservations?.compactMap { original in
                    let instances = original.instances
                    return instances
                }
                let flattened = projection?.flatMap { $0 }
                let projection2: [EC2ClientTypes.InstanceStateName]? = flattened?.compactMap { original in
                    let state = original.state
                    let name = state?.name
                    return name
                }
                return projection2?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "terminated") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeInstancesInput, DescribeInstancesOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the InstanceStopped event on the describeInstances operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeInstancesInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilInstanceStopped(options: SmithyWaitersAPI.WaiterOptions, input: DescribeInstancesInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeInstancesOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.instanceStoppedWaiterConfig(), operation: self.describeInstances(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func instanceTerminatedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeInstancesInput, DescribeInstancesOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeInstancesInput, DescribeInstancesOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeInstancesInput, result: Swift.Result<DescribeInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Reservations[].Instances[].State.Name"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "terminated"
                guard case .success(let output) = result else { return false }
                let reservations = output.reservations
                let projection: [[EC2ClientTypes.Instance]]? = reservations?.compactMap { original in
                    let instances = original.instances
                    return instances
                }
                let flattened = projection?.flatMap { $0 }
                let projection2: [EC2ClientTypes.InstanceStateName]? = flattened?.compactMap { original in
                    let state = original.state
                    let name = state?.name
                    return name
                }
                return (projection2?.count ?? 0) > 1 && (projection2?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "terminated") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeInstancesInput, result: Swift.Result<DescribeInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Reservations[].Instances[].State.Name"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "pending"
                guard case .success(let output) = result else { return false }
                let reservations = output.reservations
                let projection: [[EC2ClientTypes.Instance]]? = reservations?.compactMap { original in
                    let instances = original.instances
                    return instances
                }
                let flattened = projection?.flatMap { $0 }
                let projection2: [EC2ClientTypes.InstanceStateName]? = flattened?.compactMap { original in
                    let state = original.state
                    let name = state?.name
                    return name
                }
                return projection2?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "pending") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeInstancesInput, result: Swift.Result<DescribeInstancesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Reservations[].Instances[].State.Name"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "stopping"
                guard case .success(let output) = result else { return false }
                let reservations = output.reservations
                let projection: [[EC2ClientTypes.Instance]]? = reservations?.compactMap { original in
                    let instances = original.instances
                    return instances
                }
                let flattened = projection?.flatMap { $0 }
                let projection2: [EC2ClientTypes.InstanceStateName]? = flattened?.compactMap { original in
                    let state = original.state
                    let name = state?.name
                    return name
                }
                return projection2?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "stopping") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeInstancesInput, DescribeInstancesOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the InstanceTerminated event on the describeInstances operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeInstancesInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilInstanceTerminated(options: SmithyWaitersAPI.WaiterOptions, input: DescribeInstancesInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeInstancesOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.instanceTerminatedWaiterConfig(), operation: self.describeInstances(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func instanceStatusOkWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeInstanceStatusInput, DescribeInstanceStatusOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeInstanceStatusInput, DescribeInstanceStatusOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeInstanceStatusInput, result: Swift.Result<DescribeInstanceStatusOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "InstanceStatuses[].InstanceStatus.Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "ok"
                guard case .success(let output) = result else { return false }
                let instanceStatuses = output.instanceStatuses
                let projection: [EC2ClientTypes.SummaryStatus]? = instanceStatuses?.compactMap { original in
                    let instanceStatus = original.instanceStatus
                    let status = instanceStatus?.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "ok") } ?? false)
            }),
            .init(state: .retry, matcher: { (input: DescribeInstanceStatusInput, result: Swift.Result<DescribeInstanceStatusOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "InvalidInstanceID.NotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeInstanceStatusInput, DescribeInstanceStatusOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the InstanceStatusOk event on the describeInstanceStatus operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeInstanceStatusInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilInstanceStatusOk(options: SmithyWaitersAPI.WaiterOptions, input: DescribeInstanceStatusInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeInstanceStatusOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.instanceStatusOkWaiterConfig(), operation: self.describeInstanceStatus(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func systemStatusOkWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeInstanceStatusInput, DescribeInstanceStatusOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeInstanceStatusInput, DescribeInstanceStatusOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeInstanceStatusInput, result: Swift.Result<DescribeInstanceStatusOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "InstanceStatuses[].SystemStatus.Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "ok"
                guard case .success(let output) = result else { return false }
                let instanceStatuses = output.instanceStatuses
                let projection: [EC2ClientTypes.SummaryStatus]? = instanceStatuses?.compactMap { original in
                    let systemStatus = original.systemStatus
                    let status = systemStatus?.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "ok") } ?? false)
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeInstanceStatusInput, DescribeInstanceStatusOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the SystemStatusOk event on the describeInstanceStatus operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeInstanceStatusInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilSystemStatusOk(options: SmithyWaitersAPI.WaiterOptions, input: DescribeInstanceStatusInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeInstanceStatusOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.systemStatusOkWaiterConfig(), operation: self.describeInstanceStatus(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func internetGatewayExistsWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeInternetGatewaysInput, DescribeInternetGatewaysOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeInternetGatewaysInput, DescribeInternetGatewaysOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeInternetGatewaysInput, result: Swift.Result<DescribeInternetGatewaysOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "length(InternetGateways[].InternetGatewayId) > `0`"
                // JMESPath comparator: "booleanEquals"
                // JMESPath expected value: "true"
                guard case .success(let output) = result else { return false }
                let internetGateways = output.internetGateways
                let projection: [Swift.String]? = internetGateways?.compactMap { original in
                    let internetGatewayId = original.internetGatewayId
                    return internetGatewayId
                }
                let count = Double(projection?.count ?? 0)
                let number = Double(0.0)
                let comparison = SmithyWaitersAPI.JMESUtils.compare(count, >, number)
                return SmithyWaitersAPI.JMESUtils.compare(comparison, ==, true)
            }),
            .init(state: .retry, matcher: { (input: DescribeInternetGatewaysInput, result: Swift.Result<DescribeInternetGatewaysOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "InvalidInternetGateway.NotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeInternetGatewaysInput, DescribeInternetGatewaysOutput>(acceptors: acceptors, minDelay: 5.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the InternetGatewayExists event on the describeInternetGateways operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeInternetGatewaysInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilInternetGatewayExists(options: SmithyWaitersAPI.WaiterOptions, input: DescribeInternetGatewaysInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeInternetGatewaysOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.internetGatewayExistsWaiterConfig(), operation: self.describeInternetGateways(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func keyPairExistsWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeKeyPairsInput, DescribeKeyPairsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeKeyPairsInput, DescribeKeyPairsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeKeyPairsInput, result: Swift.Result<DescribeKeyPairsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "length(KeyPairs[].KeyName) > `0`"
                // JMESPath comparator: "booleanEquals"
                // JMESPath expected value: "true"
                guard case .success(let output) = result else { return false }
                let keyPairs = output.keyPairs
                let projection: [Swift.String]? = keyPairs?.compactMap { original in
                    let keyName = original.keyName
                    return keyName
                }
                let count = Double(projection?.count ?? 0)
                let number = Double(0.0)
                let comparison = SmithyWaitersAPI.JMESUtils.compare(count, >, number)
                return SmithyWaitersAPI.JMESUtils.compare(comparison, ==, true)
            }),
            .init(state: .retry, matcher: { (input: DescribeKeyPairsInput, result: Swift.Result<DescribeKeyPairsOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "InvalidKeyPair.NotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeKeyPairsInput, DescribeKeyPairsOutput>(acceptors: acceptors, minDelay: 5.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the KeyPairExists event on the describeKeyPairs operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeKeyPairsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilKeyPairExists(options: SmithyWaitersAPI.WaiterOptions, input: DescribeKeyPairsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeKeyPairsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.keyPairExistsWaiterConfig(), operation: self.describeKeyPairs(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func natGatewayAvailableWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeNatGatewaysInput, DescribeNatGatewaysOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeNatGatewaysInput, DescribeNatGatewaysOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeNatGatewaysInput, result: Swift.Result<DescribeNatGatewaysOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "NatGateways[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "available"
                guard case .success(let output) = result else { return false }
                let natGateways = output.natGateways
                let projection: [EC2ClientTypes.NatGatewayState]? = natGateways?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "available") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeNatGatewaysInput, result: Swift.Result<DescribeNatGatewaysOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "NatGateways[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "failed"
                guard case .success(let output) = result else { return false }
                let natGateways = output.natGateways
                let projection: [EC2ClientTypes.NatGatewayState]? = natGateways?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "failed") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeNatGatewaysInput, result: Swift.Result<DescribeNatGatewaysOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "NatGateways[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleting"
                guard case .success(let output) = result else { return false }
                let natGateways = output.natGateways
                let projection: [EC2ClientTypes.NatGatewayState]? = natGateways?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleting") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeNatGatewaysInput, result: Swift.Result<DescribeNatGatewaysOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "NatGateways[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleted"
                guard case .success(let output) = result else { return false }
                let natGateways = output.natGateways
                let projection: [EC2ClientTypes.NatGatewayState]? = natGateways?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleted") }) ?? false
            }),
            .init(state: .retry, matcher: { (input: DescribeNatGatewaysInput, result: Swift.Result<DescribeNatGatewaysOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "NatGatewayNotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeNatGatewaysInput, DescribeNatGatewaysOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the NatGatewayAvailable event on the describeNatGateways operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeNatGatewaysInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilNatGatewayAvailable(options: SmithyWaitersAPI.WaiterOptions, input: DescribeNatGatewaysInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeNatGatewaysOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.natGatewayAvailableWaiterConfig(), operation: self.describeNatGateways(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func natGatewayDeletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeNatGatewaysInput, DescribeNatGatewaysOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeNatGatewaysInput, DescribeNatGatewaysOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeNatGatewaysInput, result: Swift.Result<DescribeNatGatewaysOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "NatGateways[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "deleted"
                guard case .success(let output) = result else { return false }
                let natGateways = output.natGateways
                let projection: [EC2ClientTypes.NatGatewayState]? = natGateways?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleted") } ?? false)
            }),
            .init(state: .success, matcher: { (input: DescribeNatGatewaysInput, result: Swift.Result<DescribeNatGatewaysOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "NatGatewayNotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeNatGatewaysInput, DescribeNatGatewaysOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the NatGatewayDeleted event on the describeNatGateways operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeNatGatewaysInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilNatGatewayDeleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeNatGatewaysInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeNatGatewaysOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.natGatewayDeletedWaiterConfig(), operation: self.describeNatGateways(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func networkInterfaceAvailableWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeNetworkInterfacesInput, DescribeNetworkInterfacesOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeNetworkInterfacesInput, DescribeNetworkInterfacesOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeNetworkInterfacesInput, result: Swift.Result<DescribeNetworkInterfacesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "NetworkInterfaces[].Status"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "available"
                guard case .success(let output) = result else { return false }
                let networkInterfaces = output.networkInterfaces
                let projection: [EC2ClientTypes.NetworkInterfaceStatus]? = networkInterfaces?.compactMap { original in
                    let status = original.status
                    return status
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "available") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeNetworkInterfacesInput, result: Swift.Result<DescribeNetworkInterfacesOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "InvalidNetworkInterfaceID.NotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeNetworkInterfacesInput, DescribeNetworkInterfacesOutput>(acceptors: acceptors, minDelay: 20.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the NetworkInterfaceAvailable event on the describeNetworkInterfaces operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeNetworkInterfacesInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilNetworkInterfaceAvailable(options: SmithyWaitersAPI.WaiterOptions, input: DescribeNetworkInterfacesInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeNetworkInterfacesOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.networkInterfaceAvailableWaiterConfig(), operation: self.describeNetworkInterfaces(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func securityGroupExistsWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeSecurityGroupsInput, DescribeSecurityGroupsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeSecurityGroupsInput, DescribeSecurityGroupsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeSecurityGroupsInput, result: Swift.Result<DescribeSecurityGroupsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "length(SecurityGroups[].GroupId) > `0`"
                // JMESPath comparator: "booleanEquals"
                // JMESPath expected value: "true"
                guard case .success(let output) = result else { return false }
                let securityGroups = output.securityGroups
                let projection: [Swift.String]? = securityGroups?.compactMap { original in
                    let groupId = original.groupId
                    return groupId
                }
                let count = Double(projection?.count ?? 0)
                let number = Double(0.0)
                let comparison = SmithyWaitersAPI.JMESUtils.compare(count, >, number)
                return SmithyWaitersAPI.JMESUtils.compare(comparison, ==, true)
            }),
            .init(state: .retry, matcher: { (input: DescribeSecurityGroupsInput, result: Swift.Result<DescribeSecurityGroupsOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "InvalidGroup.NotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeSecurityGroupsInput, DescribeSecurityGroupsOutput>(acceptors: acceptors, minDelay: 5.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the SecurityGroupExists event on the describeSecurityGroups operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeSecurityGroupsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilSecurityGroupExists(options: SmithyWaitersAPI.WaiterOptions, input: DescribeSecurityGroupsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeSecurityGroupsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.securityGroupExistsWaiterConfig(), operation: self.describeSecurityGroups(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func snapshotCompletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeSnapshotsInput, DescribeSnapshotsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeSnapshotsInput, DescribeSnapshotsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeSnapshotsInput, result: Swift.Result<DescribeSnapshotsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Snapshots[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "completed"
                guard case .success(let output) = result else { return false }
                let snapshots = output.snapshots
                let projection: [EC2ClientTypes.SnapshotState]? = snapshots?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "completed") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeSnapshotsInput, result: Swift.Result<DescribeSnapshotsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Snapshots[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "error"
                guard case .success(let output) = result else { return false }
                let snapshots = output.snapshots
                let projection: [EC2ClientTypes.SnapshotState]? = snapshots?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "error") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeSnapshotsInput, DescribeSnapshotsOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the SnapshotCompleted event on the describeSnapshots operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeSnapshotsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilSnapshotCompleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeSnapshotsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeSnapshotsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.snapshotCompletedWaiterConfig(), operation: self.describeSnapshots(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func spotInstanceRequestFulfilledWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeSpotInstanceRequestsInput, DescribeSpotInstanceRequestsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeSpotInstanceRequestsInput, DescribeSpotInstanceRequestsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeSpotInstanceRequestsInput, result: Swift.Result<DescribeSpotInstanceRequestsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "SpotInstanceRequests[].Status.Code"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "fulfilled"
                guard case .success(let output) = result else { return false }
                let spotInstanceRequests = output.spotInstanceRequests
                let projection: [Swift.String]? = spotInstanceRequests?.compactMap { original in
                    let status = original.status
                    let code = status?.code
                    return code
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "fulfilled") } ?? false)
            }),
            .init(state: .success, matcher: { (input: DescribeSpotInstanceRequestsInput, result: Swift.Result<DescribeSpotInstanceRequestsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "SpotInstanceRequests[].Status.Code"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "request-canceled-and-instance-running"
                guard case .success(let output) = result else { return false }
                let spotInstanceRequests = output.spotInstanceRequests
                let projection: [Swift.String]? = spotInstanceRequests?.compactMap { original in
                    let status = original.status
                    let code = status?.code
                    return code
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "request-canceled-and-instance-running") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeSpotInstanceRequestsInput, result: Swift.Result<DescribeSpotInstanceRequestsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "SpotInstanceRequests[].Status.Code"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "schedule-expired"
                guard case .success(let output) = result else { return false }
                let spotInstanceRequests = output.spotInstanceRequests
                let projection: [Swift.String]? = spotInstanceRequests?.compactMap { original in
                    let status = original.status
                    let code = status?.code
                    return code
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "schedule-expired") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeSpotInstanceRequestsInput, result: Swift.Result<DescribeSpotInstanceRequestsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "SpotInstanceRequests[].Status.Code"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "canceled-before-fulfillment"
                guard case .success(let output) = result else { return false }
                let spotInstanceRequests = output.spotInstanceRequests
                let projection: [Swift.String]? = spotInstanceRequests?.compactMap { original in
                    let status = original.status
                    let code = status?.code
                    return code
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "canceled-before-fulfillment") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeSpotInstanceRequestsInput, result: Swift.Result<DescribeSpotInstanceRequestsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "SpotInstanceRequests[].Status.Code"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "bad-parameters"
                guard case .success(let output) = result else { return false }
                let spotInstanceRequests = output.spotInstanceRequests
                let projection: [Swift.String]? = spotInstanceRequests?.compactMap { original in
                    let status = original.status
                    let code = status?.code
                    return code
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "bad-parameters") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeSpotInstanceRequestsInput, result: Swift.Result<DescribeSpotInstanceRequestsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "SpotInstanceRequests[].Status.Code"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "system-error"
                guard case .success(let output) = result else { return false }
                let spotInstanceRequests = output.spotInstanceRequests
                let projection: [Swift.String]? = spotInstanceRequests?.compactMap { original in
                    let status = original.status
                    let code = status?.code
                    return code
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "system-error") }) ?? false
            }),
            .init(state: .retry, matcher: { (input: DescribeSpotInstanceRequestsInput, result: Swift.Result<DescribeSpotInstanceRequestsOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "InvalidSpotInstanceRequestID.NotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeSpotInstanceRequestsInput, DescribeSpotInstanceRequestsOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the SpotInstanceRequestFulfilled event on the describeSpotInstanceRequests operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeSpotInstanceRequestsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilSpotInstanceRequestFulfilled(options: SmithyWaitersAPI.WaiterOptions, input: DescribeSpotInstanceRequestsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeSpotInstanceRequestsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.spotInstanceRequestFulfilledWaiterConfig(), operation: self.describeSpotInstanceRequests(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func storeImageTaskCompleteWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeStoreImageTasksInput, DescribeStoreImageTasksOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeStoreImageTasksInput, DescribeStoreImageTasksOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeStoreImageTasksInput, result: Swift.Result<DescribeStoreImageTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "StoreImageTaskResults[].StoreTaskState"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "Completed"
                guard case .success(let output) = result else { return false }
                let storeImageTaskResults = output.storeImageTaskResults
                let projection: [Swift.String]? = storeImageTaskResults?.compactMap { original in
                    let storeTaskState = original.storeTaskState
                    return storeTaskState
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "Completed") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeStoreImageTasksInput, result: Swift.Result<DescribeStoreImageTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "StoreImageTaskResults[].StoreTaskState"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "Failed"
                guard case .success(let output) = result else { return false }
                let storeImageTaskResults = output.storeImageTaskResults
                let projection: [Swift.String]? = storeImageTaskResults?.compactMap { original in
                    let storeTaskState = original.storeTaskState
                    return storeTaskState
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "Failed") }) ?? false
            }),
            .init(state: .retry, matcher: { (input: DescribeStoreImageTasksInput, result: Swift.Result<DescribeStoreImageTasksOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "StoreImageTaskResults[].StoreTaskState"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "InProgress"
                guard case .success(let output) = result else { return false }
                let storeImageTaskResults = output.storeImageTaskResults
                let projection: [Swift.String]? = storeImageTaskResults?.compactMap { original in
                    let storeTaskState = original.storeTaskState
                    return storeTaskState
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "InProgress") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeStoreImageTasksInput, DescribeStoreImageTasksOutput>(acceptors: acceptors, minDelay: 5.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the StoreImageTaskComplete event on the describeStoreImageTasks operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeStoreImageTasksInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilStoreImageTaskComplete(options: SmithyWaitersAPI.WaiterOptions, input: DescribeStoreImageTasksInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeStoreImageTasksOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.storeImageTaskCompleteWaiterConfig(), operation: self.describeStoreImageTasks(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func subnetAvailableWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeSubnetsInput, DescribeSubnetsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeSubnetsInput, DescribeSubnetsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeSubnetsInput, result: Swift.Result<DescribeSubnetsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Subnets[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "available"
                guard case .success(let output) = result else { return false }
                let subnets = output.subnets
                let projection: [EC2ClientTypes.SubnetState]? = subnets?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "available") } ?? false)
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeSubnetsInput, DescribeSubnetsOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the SubnetAvailable event on the describeSubnets operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeSubnetsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilSubnetAvailable(options: SmithyWaitersAPI.WaiterOptions, input: DescribeSubnetsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeSubnetsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.subnetAvailableWaiterConfig(), operation: self.describeSubnets(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func volumeAvailableWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeVolumesInput, DescribeVolumesOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeVolumesInput, DescribeVolumesOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeVolumesInput, result: Swift.Result<DescribeVolumesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Volumes[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "available"
                guard case .success(let output) = result else { return false }
                let volumes = output.volumes
                let projection: [EC2ClientTypes.VolumeState]? = volumes?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "available") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeVolumesInput, result: Swift.Result<DescribeVolumesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Volumes[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleted"
                guard case .success(let output) = result else { return false }
                let volumes = output.volumes
                let projection: [EC2ClientTypes.VolumeState]? = volumes?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleted") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeVolumesInput, DescribeVolumesOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the VolumeAvailable event on the describeVolumes operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeVolumesInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilVolumeAvailable(options: SmithyWaitersAPI.WaiterOptions, input: DescribeVolumesInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeVolumesOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.volumeAvailableWaiterConfig(), operation: self.describeVolumes(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func volumeDeletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeVolumesInput, DescribeVolumesOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeVolumesInput, DescribeVolumesOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeVolumesInput, result: Swift.Result<DescribeVolumesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Volumes[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "deleted"
                guard case .success(let output) = result else { return false }
                let volumes = output.volumes
                let projection: [EC2ClientTypes.VolumeState]? = volumes?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleted") } ?? false)
            }),
            .init(state: .success, matcher: { (input: DescribeVolumesInput, result: Swift.Result<DescribeVolumesOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "InvalidVolume.NotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeVolumesInput, DescribeVolumesOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the VolumeDeleted event on the describeVolumes operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeVolumesInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilVolumeDeleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeVolumesInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeVolumesOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.volumeDeletedWaiterConfig(), operation: self.describeVolumes(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func volumeInUseWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeVolumesInput, DescribeVolumesOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeVolumesInput, DescribeVolumesOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeVolumesInput, result: Swift.Result<DescribeVolumesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Volumes[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "in-use"
                guard case .success(let output) = result else { return false }
                let volumes = output.volumes
                let projection: [EC2ClientTypes.VolumeState]? = volumes?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "in-use") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeVolumesInput, result: Swift.Result<DescribeVolumesOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Volumes[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleted"
                guard case .success(let output) = result else { return false }
                let volumes = output.volumes
                let projection: [EC2ClientTypes.VolumeState]? = volumes?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleted") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeVolumesInput, DescribeVolumesOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the VolumeInUse event on the describeVolumes operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeVolumesInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilVolumeInUse(options: SmithyWaitersAPI.WaiterOptions, input: DescribeVolumesInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeVolumesOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.volumeInUseWaiterConfig(), operation: self.describeVolumes(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func vpcPeeringConnectionDeletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeVpcPeeringConnectionsInput, DescribeVpcPeeringConnectionsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeVpcPeeringConnectionsInput, DescribeVpcPeeringConnectionsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeVpcPeeringConnectionsInput, result: Swift.Result<DescribeVpcPeeringConnectionsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "VpcPeeringConnections[].Status.Code"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "deleted"
                guard case .success(let output) = result else { return false }
                let vpcPeeringConnections = output.vpcPeeringConnections
                let projection: [EC2ClientTypes.VpcPeeringConnectionStateReasonCode]? = vpcPeeringConnections?.compactMap { original in
                    let status = original.status
                    let code = status?.code
                    return code
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleted") } ?? false)
            }),
            .init(state: .success, matcher: { (input: DescribeVpcPeeringConnectionsInput, result: Swift.Result<DescribeVpcPeeringConnectionsOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "InvalidVpcPeeringConnectionID.NotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeVpcPeeringConnectionsInput, DescribeVpcPeeringConnectionsOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the VpcPeeringConnectionDeleted event on the describeVpcPeeringConnections operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeVpcPeeringConnectionsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilVpcPeeringConnectionDeleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeVpcPeeringConnectionsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeVpcPeeringConnectionsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.vpcPeeringConnectionDeletedWaiterConfig(), operation: self.describeVpcPeeringConnections(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func vpcPeeringConnectionExistsWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeVpcPeeringConnectionsInput, DescribeVpcPeeringConnectionsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeVpcPeeringConnectionsInput, DescribeVpcPeeringConnectionsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeVpcPeeringConnectionsInput, result: Swift.Result<DescribeVpcPeeringConnectionsOutput, Swift.Error>) -> Bool in
                switch result {
                    case .success: return true
                    case .failure: return false
                }
            }),
            .init(state: .retry, matcher: { (input: DescribeVpcPeeringConnectionsInput, result: Swift.Result<DescribeVpcPeeringConnectionsOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "InvalidVpcPeeringConnectionID.NotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeVpcPeeringConnectionsInput, DescribeVpcPeeringConnectionsOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the VpcPeeringConnectionExists event on the describeVpcPeeringConnections operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeVpcPeeringConnectionsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilVpcPeeringConnectionExists(options: SmithyWaitersAPI.WaiterOptions, input: DescribeVpcPeeringConnectionsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeVpcPeeringConnectionsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.vpcPeeringConnectionExistsWaiterConfig(), operation: self.describeVpcPeeringConnections(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func vpcAvailableWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeVpcsInput, DescribeVpcsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeVpcsInput, DescribeVpcsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeVpcsInput, result: Swift.Result<DescribeVpcsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "Vpcs[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "available"
                guard case .success(let output) = result else { return false }
                let vpcs = output.vpcs
                let projection: [EC2ClientTypes.VpcState]? = vpcs?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "available") } ?? false)
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeVpcsInput, DescribeVpcsOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the VpcAvailable event on the describeVpcs operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeVpcsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilVpcAvailable(options: SmithyWaitersAPI.WaiterOptions, input: DescribeVpcsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeVpcsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.vpcAvailableWaiterConfig(), operation: self.describeVpcs(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func vpcExistsWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeVpcsInput, DescribeVpcsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeVpcsInput, DescribeVpcsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeVpcsInput, result: Swift.Result<DescribeVpcsOutput, Swift.Error>) -> Bool in
                switch result {
                    case .success: return true
                    case .failure: return false
                }
            }),
            .init(state: .retry, matcher: { (input: DescribeVpcsInput, result: Swift.Result<DescribeVpcsOutput, Swift.Error>) -> Bool in
                guard case .failure(let error) = result else { return false }
                return (error as? ClientRuntime.ServiceError)?.typeName == "InvalidVpcID.NotFound"
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeVpcsInput, DescribeVpcsOutput>(acceptors: acceptors, minDelay: 1.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the VpcExists event on the describeVpcs operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeVpcsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilVpcExists(options: SmithyWaitersAPI.WaiterOptions, input: DescribeVpcsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeVpcsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.vpcExistsWaiterConfig(), operation: self.describeVpcs(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func vpnConnectionAvailableWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeVpnConnectionsInput, DescribeVpnConnectionsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeVpnConnectionsInput, DescribeVpnConnectionsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeVpnConnectionsInput, result: Swift.Result<DescribeVpnConnectionsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "VpnConnections[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "available"
                guard case .success(let output) = result else { return false }
                let vpnConnections = output.vpnConnections
                let projection: [EC2ClientTypes.VpnState]? = vpnConnections?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "available") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeVpnConnectionsInput, result: Swift.Result<DescribeVpnConnectionsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "VpnConnections[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleting"
                guard case .success(let output) = result else { return false }
                let vpnConnections = output.vpnConnections
                let projection: [EC2ClientTypes.VpnState]? = vpnConnections?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleting") }) ?? false
            }),
            .init(state: .failure, matcher: { (input: DescribeVpnConnectionsInput, result: Swift.Result<DescribeVpnConnectionsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "VpnConnections[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "deleted"
                guard case .success(let output) = result else { return false }
                let vpnConnections = output.vpnConnections
                let projection: [EC2ClientTypes.VpnState]? = vpnConnections?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleted") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeVpnConnectionsInput, DescribeVpnConnectionsOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the VpnConnectionAvailable event on the describeVpnConnections operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeVpnConnectionsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilVpnConnectionAvailable(options: SmithyWaitersAPI.WaiterOptions, input: DescribeVpnConnectionsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeVpnConnectionsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.vpnConnectionAvailableWaiterConfig(), operation: self.describeVpnConnections(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func vpnConnectionDeletedWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<DescribeVpnConnectionsInput, DescribeVpnConnectionsOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<DescribeVpnConnectionsInput, DescribeVpnConnectionsOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: DescribeVpnConnectionsInput, result: Swift.Result<DescribeVpnConnectionsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "VpnConnections[].State"
                // JMESPath comparator: "allStringEquals"
                // JMESPath expected value: "deleted"
                guard case .success(let output) = result else { return false }
                let vpnConnections = output.vpnConnections
                let projection: [EC2ClientTypes.VpnState]? = vpnConnections?.compactMap { original in
                    let state = original.state
                    return state
                }
                return (projection?.count ?? 0) > 1 && (projection?.allSatisfy { SmithyWaitersAPI.JMESUtils.compare($0, ==, "deleted") } ?? false)
            }),
            .init(state: .failure, matcher: { (input: DescribeVpnConnectionsInput, result: Swift.Result<DescribeVpnConnectionsOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "VpnConnections[].State"
                // JMESPath comparator: "anyStringEquals"
                // JMESPath expected value: "pending"
                guard case .success(let output) = result else { return false }
                let vpnConnections = output.vpnConnections
                let projection: [EC2ClientTypes.VpnState]? = vpnConnections?.compactMap { original in
                    let state = original.state
                    return state
                }
                return projection?.contains(where: { SmithyWaitersAPI.JMESUtils.compare($0, ==, "pending") }) ?? false
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<DescribeVpnConnectionsInput, DescribeVpnConnectionsOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the VpnConnectionDeleted event on the describeVpnConnections operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `DescribeVpnConnectionsInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilVpnConnectionDeleted(options: SmithyWaitersAPI.WaiterOptions, input: DescribeVpnConnectionsInput) async throws -> SmithyWaitersAPI.WaiterOutcome<DescribeVpnConnectionsOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.vpnConnectionDeletedWaiterConfig(), operation: self.describeVpnConnections(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }

    static func passwordDataAvailableWaiterConfig() throws -> SmithyWaitersAPI.WaiterConfiguration<GetPasswordDataInput, GetPasswordDataOutput> {
        let acceptors: [SmithyWaitersAPI.WaiterConfiguration<GetPasswordDataInput, GetPasswordDataOutput>.Acceptor] = [
            .init(state: .success, matcher: { (input: GetPasswordDataInput, result: Swift.Result<GetPasswordDataOutput, Swift.Error>) -> Bool in
                // JMESPath expression: "length(PasswordData) > `0`"
                // JMESPath comparator: "booleanEquals"
                // JMESPath expected value: "true"
                guard case .success(let output) = result else { return false }
                let passwordData = output.passwordData
                let count = Double(passwordData?.count ?? 0)
                let number = Double(0.0)
                let comparison = SmithyWaitersAPI.JMESUtils.compare(count, >, number)
                return SmithyWaitersAPI.JMESUtils.compare(comparison, ==, true)
            }),
        ]
        return try SmithyWaitersAPI.WaiterConfiguration<GetPasswordDataInput, GetPasswordDataOutput>(acceptors: acceptors, minDelay: 15.0, maxDelay: 120.0)
    }

    /// Initiates waiting for the PasswordDataAvailable event on the getPasswordData operation.
    /// The operation will be tried and (if necessary) retried until the wait succeeds, fails, or times out.
    /// Returns a `WaiterOutcome` asynchronously on waiter success, throws an error asynchronously on
    /// waiter failure or timeout.
    /// - Parameters:
    ///   - options: `WaiterOptions` to be used to configure this wait.
    ///   - input: The `GetPasswordDataInput` object to be used as a parameter when performing the operation.
    /// - Returns: A `WaiterOutcome` with the result of the final, successful performance of the operation.
    /// - Throws: `WaiterFailureError` if the waiter fails due to matching an `Acceptor` with state `failure`
    /// or there is an error not handled by any `Acceptor.`
    /// `WaiterTimeoutError` if the waiter times out.
    public func waitUntilPasswordDataAvailable(options: SmithyWaitersAPI.WaiterOptions, input: GetPasswordDataInput) async throws -> SmithyWaitersAPI.WaiterOutcome<GetPasswordDataOutput> {
        let waiter = SmithyWaitersAPI.Waiter(config: try Self.passwordDataAvailableWaiterConfig(), operation: self.getPasswordData(input:))
        return try await waiter.waitUntil(options: options, input: input)
    }
}
